# Creates a named DB user for each nonprod environment
# The user is granted the `app` and `pfml_owner` roles so that they have write and table creation permissions 
# Upon successful completion, the user running the job will receive an email via SES containing
#   - Username (email minus the @domain replacing `.` with `_`) i.e. <EMAIL>'s username is benjamin_d_lake
#   - Password (randomly generated)
#   - List of Nonprod DB endpoints
# The user's email is automatically set from the GitHub context and the username is not customizable

name: Infra Create DB User - Nonprods

on: 
  workflow_dispatch:

defaults:
  run:
    working-directory: ./bin/aws/create_db_user_nonprods
  
permissions:
  id-token: write
  contents: read

jobs:
  create:
    name: Create User in Non-prods
    runs-on: [self-hosted, ecs]

    steps:
      - uses: actions/checkout@v4

      - name: Get User Email
        id: get_email
        uses: octokit/graphql-action@v2.x
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          query: |
            query {
              user(login: "${{ github.actor }}") {
                email
              }
            }
      
      - name: Confirm mass.gov email
        run: |
          if [[ -z "${{ fromJson(steps.get_email.outputs.data).user.email }}" ]]; then
            echo "Your GitHub email needs to be public. Please do so by selecting your mass.gov email in the 'Public email' field under 'Public Profile' in your GitHub settings then try again."
            exit 1
          elif [[ "${{ fromJson(steps.get_email.outputs.data).user.email }}" != *"@mass.gov" ]]; then
            echo "Your mass.gov email must be your primary email in GitHub. Please update your email and try again."
            exit 1
          fi

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: us-east-1
          role-to-assume: arn:aws:iam::498823821309:role/ci-run-deploys-oidc
          role-duration-seconds: 3600
      
      - name: Create User in Nonprod DBs
        run: |
          sudo apt-get install -y libpq-dev python3.10-dev
          pip install boto3 emoji argparse psycopg2
          python3 -u create_db_user_nonprods.py --email ${{ fromJson(steps.get_email.outputs.data).user.email }}

  send-pd-alert:
    name: Send PagerDuty Alert
    needs: [create]
    runs-on: [self-hosted, ecs]
    if: always() && needs.create.result == 'failure'
    steps:
      - uses: actions/checkout@v4

      - name: Send Infra Low Pri PagerDuty Alert
        uses: fjogeleit/http-request-action@v1
        with:
          url: "https://events.pagerduty.com/v2/enqueue"
          method: "POST"
          contentType: "application/json"
          data: >
            {
              "routing_key": "0690c1611f674d01d0b3f2d4c08faed3",
              "event_action": "trigger",
              "payload": {
                "summary": "Create Nonprod DB User failed",
                "severity": "warning",
                "source": "GitHub Actions",
                "custom_details": {
                  "description": "Create Nonprod DB User #${{ github.run_number }}: [workflow run](${{ env.workflow_self_link }})"
                },
                "links": [
                  {
                    "href":"https://github.com/EOLWD/pfml/actions/runs/${{ github.run_id }}",
                    "text": "View GitHub Actions run"
                  }
                ]
              }
            }