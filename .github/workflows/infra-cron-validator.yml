# This workflow checks for AWS cron expressions in specified .tf files.
# It scans entire infra folder, then uses the cron-validate library to ensure each cron meets AWS rules.
# If any cron is invalid, the workflow exits with an error.


name: Validate Cron Expressions

on:
  pull_request:
    types: [opened, edited, reopened, synchronize]
    paths:
      - 'infra/**/*.tf'

jobs:
  validate-cron-expressions:
    runs-on: [self-hosted, ecs]

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20.x'

      - name: Install cron-validate
        run: |
          echo "Installing cron-validate..."
          npm install cron-validate

      - name: Validate AWS Cron Expressions
        shell: bash
        run: |
          set -o pipefail
          echo "Starting validation of AWS Cron expressions..."
          invalid_found=false
          tmpfile=$(mktemp)

          echo "Extracting cron expressions for validation..."
          find infra -type f -name '*.tf' | while read -r file; do
            grep -HnE 'cron\([^)]+\)' "$file" || true
          done |
          while IFS=: read -r fullpath lineno matchline; do
            cronexpr=$(echo "$matchline" | sed -nE 's/.*(cron\([^)]*\)).*/\1/p')
            if [ -n "$cronexpr" ]; then
              echo "$fullpath:$lineno:$cronexpr" >> "$tmpfile"
            fi
          done

          if [ ! -s "$tmpfile" ]; then
            echo "No cron expressions found in second step. Skipping validation."
            exit 0
          fi

          while IFS=: read -r file line match; do
            cron=$(echo "$match" | sed -E 's/cron\(([^)]+)\)/\1/')

            if ! node -e "
              const cronValidate = require('cron-validate').default;
              const cron = '$cron';

              const options = {
                preset: 'aws-cloud-watch',
                override: {
                  useSeconds: false,
                  useYears: true,
                  allowBlankDay: true,
                  mustHaveBlankDayField: true
                }
              };

              const result = cronValidate(cron, options);
              const fields = cron.trim().split(' ');

              if (!result.isValid()) {
                console.error(\`❌ Syntax error: \${cron} in file: \${'$file'} at line: \${'$line'}\`);
                result.getError().forEach(err => {
                  console.error('   →', err);
                });
                process.exit(1);
              }

              console.log(\`✅ Valid AWS Cron expression: \${cron} in file: \${'$file'} at line: \${'$line'}\`);
            "; then
              invalid_found=true
            fi

          done < "$tmpfile"

          if [ "$invalid_found" = true ]; then
            echo "::error::Invalid AWS cron expressions detected."
            exit 1
          else
            echo "✅ All AWS Cron expressions are valid."
          fi
