# Allow or Block traffic to the chosen environment's APIGW WAF

name: Infra Emergency APIGW WAF Update

run-name: ${{ github.workflow }} - ${{ github.event.inputs.action }} - ${{ github.event.inputs.environment }}

on:
  workflow_dispatch:
    inputs:
      environment:
        required: true
        default: "infra-test"
        description: |
          🛑 DO NOT CHANGE THE DROPDOWN ABOVE FOR THE WORKFLOW. KEEP IT ON "main" BRANCH.
          --------------------------------------
          ⬇️ Environment.
        type: choice
        options:
          - infra-test
          - tst2
          - prod
          - training
          - trn2
      action:
        required: true
        description: |
          ⬇️ action.
        type: choice
        options:
          - allow
          - block

permissions:
  id-token: write
  contents: read

jobs:
  pfml-devops-check:
    name: Check for pfml-devops team member
    runs-on: [self-hosted, ecs]
    steps:
      - name: check users
        id: get_teams
        env:
          GITHUB_TOKEN: ${{ secrets.PFML_DEVOPS_TOKEN }}
        uses: octokit/graphql-action@v2.x
        with:
          query: |
            query {
              organization(login: "EOLWD") {
                teams(first: 100, userLogins: ["${{ github.actor }}"]) {
                  totalCount
                  edges {
                    node {
                      name
                    }
                  }
                }
              }
            }
      - name: fail if not a member
        if: |
          contains(steps.get_teams.outputs.data, '"pfml-devops"') == false &&
          contains(steps.get_teams.outputs.data, '"pfml-infra-srs"') == false
        run: |
          echo "Not part of pfml-devops or pfml-infra-srs, cancelling workflow"
          exit 1

  update-apigw-waf-default-rule:
    name: Update APIGW WAF default rule
    needs: pfml-devops-check
    runs-on: [self-hosted, ecs]

    steps:
      - uses: actions/checkout@v4

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-region: us-east-1
          role-duration-seconds: 900
          role-to-assume: arn:aws:iam::498823821309:role/ci-run-deploys-oidc

      - uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - name: install python libraries
        run: pip install boto3 argparse

      - name: ${{ github.event.inputs.action }} APIGW WAF traffic in ${{ github.event.inputs.environment }}
        run: python -u waf.py --env ${{ github.event.inputs.environment }} --${{ github.event.inputs.action }}
        working-directory: ./bin/maintenance-page
