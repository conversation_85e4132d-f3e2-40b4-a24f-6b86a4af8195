Function,REST,URL,User Parameter,Method Name,Variables
wscomposer_request,GET,ReadEmployer,read_employer,,"{param_str_taxId: employer_fein},"
wscomposer_request,POST,webservice,register_api_user,,"{config: EmployeeRegisterService}, xml_body"
customer_api,GET,healthcheck,user_id,health_check,
customer_api,GET,customer/readCustomerDetails,user_id,read_customer_details,
customer_api,POST,customer/updateCustomerDetails,user_id,update_customer_details,"data=customer.json,exclude_none=True"
customer_api,GET,customer/readCustomerContactDetails,user_id,read_customer_contact_details,
customer_api,POST,customer/updateCustomerContactDetails,user_id,update_customer_contact_details,"data=contact_details.json,exclude_none=True"
customer_api,POST,customer/absence/startAbsence,user_id,start_absence,"data=absence_case.json,exclude_none=True"
customer_api,POST,customer/absence/notifications/{notification_case_id}/complete-intake,user_id,complete_intake,
customer_api,GET,customer/absence/absences,user_id,get_absences,
customer_api,GET,customer/absence/absences/{absence_id},user_id,get_absence,
customer_api,GET,customer/claims/{absence_paid_leave_case_id}/benefits,user_id,get_claim_benefit,
customer_api,GET,customer/claims/{absence_paid_leave_case_id}/benefits/{benefit_id}/readDisabilityBenefit,user_id,get_claim_benefit,
group_client_api,GET,groupClient/notifications/{notification_id},employer_user_id,get_notification,
group_client_api,GET,groupClient/absences/absence-period-decisions?{querystring},user_id,get_group_client_absence_period_decisions,
customer_api,GET,customer/absence/absences/{absence_id}/absence-period-decisions,user_id,get_customer_absence_period_decisions,
group_client_api,GET,groupClient/customers/{customer_id}/customer-info,user_id,get_customer_info,
group_client_api,GET,groupClient/customers/{customer_id}/customer-occupations,user_id,get_customer_occupations,
group_client_api,GET,groupClient/cases/{case_id}/outstanding-information,user_id,get_outstanding_information,
group_client_api,POST,groupClient/cases/{case_id}/outstanding-information-received,user_id,update_outstanding_information_as_recieved,"data=outstanding_information.json,exclude_none=True"
group_client_api,GET,groupClient/cases/{absence_id}/eforms,user_id,get_eform_summary,
customer_api,GET,customer/cases/{absence_id}/eForms,user_id,customer_get_eform_summary,
group_client_api,GET,groupClient/cases/{absence_id}/eforms/{eform_id}/readEform,user_id,get_eform,
customer_api,GET,customer/cases/{absence_id}/readEForm/{eform_id},user_id,customer_get_eform,
group_client_api,POST,groupClient/cases/{absence_id}/addEForm/{encoded_eform_type},user_id,create_eform,"data=json.dumps,eform.eformAttributes"
customer_api,POST,customer/cases/{absence_id}/addEForm/{encoded_eform_type},user_id,customer_create_eform,"data=json.dumps,eform.eformAttributes"
customer_api,GET,customer/occupations,user_id,get_customer_occupations_customer_api,
customer_api,GET,customer/cases/{case_id}/occupations,user_id,get_case_occupations,
customer_api,GET,customer/paymentPreferences,user_id,get_payment_preferences,
customer_api,POST,customer/addPaymentPreference,user_id,add_payment_preference,"data=payment_preference.json,exclude_none=True"
customer_api,GET,customer/payment-preferences,user_id,get_customer_payment_preference,
wscomposer_request,POST,webservice,create_overpayment_recovery,,"{config: COMAddOverpaymentActualRecovery}, xml_body"
customer_api,POST,customer/payment-preferences,user_id,create_customer_payment_preference,"data=payment_preference.json,exclude_none=True"
customer_api,POST,customer/payment-preferences/{payment_preference_id}/edit,user_id,update_customer_payment_preference,"data=payment_preference.json,exclude_none=True"
wscomposer_request,POST,webservice,update_occupation,,"{config: OccupationDetailUpdateService}, xml_body"
customer_api,POST,customer/cases/{absence_id}/documents/base64Upload/{document_type},user_id,upload_documents,json=data
customer_api,POST,customer/cases/{absence_id}/documents/upload/{document_type},user_id,upload_document_multipart,"header_content_type=None, files=multipart_data"
group_client_api,GET,groupClient/cases/{absence_id}/documents?_filter=includeChildCases,user_id,group_client_get_documents,header_content_type=header_content_type
group_client_api,GET,groupClient/cases/{absence_id}/managedRequirements,user_id,get_managed_requirements,header_content_type=header_content_type
customer_api,GET,customer/cases/{absence_id}/documents?includeChildCases=True,user_id,get_documents,header_content_type=header_content_type
customer_api,GET,customer/documents?{query_string},user_id,get_documents,header_content_type=header_content_type
group_client_api,GET,groupClient/cases/{absence_id}/documents/{fineos_document_id}/base64Download,user_id,download_document_as_leave_admin,header_content_type=header_content_type
customer_api,GET,customer/cases/{absence_id}/documents/{fineos_document_id}/base64Download,user_id,download_document,header_content_type=header_content_type
customer_api,GET,customer/document/{fineos_document_id}/base64Download,user_id,download_document,header_content_type=header_content_type
customer_api,POST,customer/cases/{absence_id}/documents/{fineos_document_id}/doc-received-for-outstanding-supporting-evidence,user_id,mark_document_as_recieved,
customer_api,GET,customer/occupations/{occupation_id}/week-based-work-pattern,user_id,get_week_based_work_pattern,
customer_api,POST,customer/occupations/{occupation_id}/week-based-work-pattern,user_id,add_week_based_work_pattern,"data=week_based_work_pattern.json,exclude_none=True"
customer_api,POST,customer/occupations/{occupation_id}/week-based-work-pattern/replace,user_id,update_week_based_work_pattern,"data=week_based_work_pattern.json,exclude_none=True"
customer_api,POST,customer/absence/absences/{absence_id}/actual-absence-periods/bulk-create,web_id,submit_intermittent_leave_episode,"data=elements.json,exclude_none=True"
customer_api,GET,customer/absence/absences/{absence_id}/actual-absence-periods,user_id,get_actual_absence_period_resources,
integration_services_api,POST,rest/externalUserProvisioningService/createOrUpdateEmployerViewpointUser,self.wscomposer_user_id,create_or_update_leave_admin,"data=xml_body.encode,utf-8"
wscomposer_request,POST,webservice,create_or_update_employer,,"{config: UpdateOrCreateParty}, xml_body"
customer_api,POST,customer/absence/absences/{absence_id}/leave-periods-change-requests,fineos_web_id,create_or_update_leave_period_change_request,"data=change_request.json,exclude_none=True"
wscomposer_request,POST,webservice,create_service_agreement_for_employer,,"{config: ServiceAgreementService}, xml_body"
wscomposer_request,POST,webservice,send_tax_withholding_preference,,"{config: OptInSITFITService}, xml_body"
wscomposer_request,GET,COMReadPaidLeaveInstruction,read_tax_withholding_preference,,"{param_str_casenumber: absence_id},"
integration_services_soap_api,POST,services/CaseServices,self.soap_user_id,create_appeal_case,"urn:createCase, data=xml_request_body.encode,utf-8"
customer_api,POST,customer/absence/{absence_id}/reflexive-questions,user_id,update_reflexive_questions,"data=additional_information.json,exclude_none=True"
integration_services_api,POST,api/v1/document/uploadAndIndexDocumentToFineosDMS,self.wscomposer_user_id,upload_document_to_dms,"header_content_type=None, files={file: ,file_name, file, application/pd, documentCreationRequest: ,documentCreationRequest, json.dumps,data, application/json}"
activity_services_request,ActivityServiceMethod.CREATE_TASK,xml_body,,,
customer_api,GET,healthcheck,ANONYMOUS_USER,get_is_registered,
integration_services_api,GET,healthcheck,ANONYMOUS_USER,is_integration_services_api_available,
group_client_api,GET,healthcheck,ANONYMOUS_USER,is_group_client_api_available,
