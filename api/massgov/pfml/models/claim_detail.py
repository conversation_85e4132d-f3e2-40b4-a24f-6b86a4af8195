from datetime import date, datetime
from typing import List, Optional
from uuid import UUID

import massgov.pfml.services.claims as claim_service
from massgov.pfml import db
from massgov.pfml.api.models.applications.common import MaskedPaymentPreference
from massgov.pfml.api.models.claims.responses import IntermittentLeaveEpisodeResponse
from massgov.pfml.db.models.absences import <PERSON><PERSON>encePeriod, LkAbsenceStatus
from massgov.pfml.db.models.appeal import Appeal
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.change_request import ChangeRequest
from massgov.pfml.db.models.documents import Document
from massgov.pfml.db.models.employees import (
    <PERSON><PERSON><PERSON>,
    Employee,
    Employer,
    LeaveRequest,
    LkClaimType,
    ManagedRequirement,
    StateLog,
)
from massgov.pfml.db.models.organization_unit import OrganizationUnit


class ClaimDetail:
    """
    A Claim, with additional detail (from table joins, calls to FINEOS, etc.)

    This class should be favored over DetailedClaimResponse internally, for a few reasons:

    1. It includes a reference to the original Claim, with all of the attributes
       and helper methods that go along with it
    2. It doesn't have any restrictions imposed upon it by either the Pydantic models
       (as is the case for DetailedClaimResponse) or SQLAlchemy models (Claim)
    3. Attributes can be loaded dynamically as-needed, instead of at initialization

    Eventually, this class should be used as the primary representation of a ClaimDetail,
    and "DetailedClaimResponse" will be used as it was intended - as the flat JSON response
    for returning claim data to the portal.

    See: https://lwd.atlassian.net/browse/PFMLPB-17734 (Investigate using `ClaimDetail` instead
    of `DetailedClaimResponse` in the API)
    """

    _claim: Claim  # the db-backed Claim and associated data
    _db_session: db.Session

    # additional claim data, loaded dynamically
    _intermittent_leave_episodes: Optional[List[IntermittentLeaveEpisodeResponse]] = None

    def __init__(
        self,
        claim: Claim,
        db_session: db.Session,
    ):
        self._claim = claim
        self._db_session = db_session

    @property
    def absence_periods(self) -> Optional[List[AbsencePeriod]]:
        return self._claim.absence_periods

    @property
    def absence_periods_earliest_start_date(self) -> Optional[date]:
        return self._claim.absence_periods_earliest_start_date

    @property
    def absence_periods_latest_end_date(self) -> Optional[date]:
        return self._claim.absence_periods_latest_end_date

    @property
    def appeal(self) -> Optional[Appeal]:
        return self._claim.appeal

    @property
    def application(self) -> Optional[Application]:
        return self._claim.application

    @property
    def approval_date(self) -> Optional[date]:
        return self._claim.approval_date

    @property
    def approved_intermittent_absence_periods(self) -> Optional[List[AbsencePeriod]]:
        return self._claim.approved_intermittent_absence_periods

    @property
    def authorized_representative_id(self) -> Optional[str]:
        return self._claim.authorized_representative_id

    @property
    def benefit_days(self) -> Optional[int]:
        return self._claim.benefit_days

    @property
    def change_requests(self) -> List[ChangeRequest]:
        return self._claim.change_requests

    @property
    def claim_end_date(self) -> Optional[date]:
        return self._claim.claim_end_date

    @property
    def claim_id(self) -> UUID:
        return self._claim.claim_id

    @property
    def claim_start_date(self) -> Optional[date]:
        return self._claim.claim_start_date

    @property
    def claim_type(self) -> Optional[LkClaimType]:
        return self._claim.claim_type

    @property
    def claim_type_description(self) -> Optional[str]:
        return self._claim.claim_type_description

    @property
    def claim_type_id(self) -> Optional[int]:
        return self._claim.claim_type_id

    @property
    def created_at(self) -> datetime:
        return self._claim.created_at

    @property
    def documents(self) -> List[Document]:
        return self._claim.documents

    @property
    def does_claim_span_benefit_years(self) -> Optional[bool]:
        return self._claim.does_claim_span_benefit_years

    @property
    def earliest_absence_period(self) -> Optional[AbsencePeriod]:
        return self._claim.earliest_absence_period

    @property
    def earliest_follow_up_date(self) -> Optional[date]:
        return self._claim.earliest_follow_up_date

    @property
    def employee(self) -> Optional[Employee]:
        return self._claim.employee

    @property
    def employee_id(self) -> Optional[UUID]:
        return self._claim.employee_id

    @property
    def employee_tax_identifier(self) -> Optional[str]:
        return self._claim.employee_tax_identifier

    @property
    def employer(self) -> Optional[Employer]:
        return self._claim.employer

    @property
    def employer_fein(self) -> Optional[str]:
        return self._claim.employer_fein

    @property
    def employer_id(self) -> Optional[UUID]:
        return self._claim.employer_id

    @property
    def fineos_absence_id(self) -> Optional[str]:
        return self._claim.fineos_absence_id

    @property
    def fineos_absence_status(self) -> Optional[LkAbsenceStatus]:
        return self._claim.fineos_absence_status

    @property
    def fineos_absence_status_id(self) -> Optional[int]:
        return self._claim.fineos_absence_status_id

    @property
    def fineos_notification_id(self) -> Optional[str]:
        return self._claim.fineos_notification_id

    @property
    def has_approval_status(self) -> bool:
        return self._claim.has_approval_status

    @property
    def has_extensions(self) -> bool:
        return self._claim.has_extensions

    @property
    def has_paid_payments_persisted(self) -> Optional[bool]:
        return self._claim.has_paid_payments_persisted

    @property
    def get_is_id_proofed(self) -> bool:
        return self._claim.get_is_id_proofed

    @property
    def masked_payment_preference(self) -> Optional[MaskedPaymentPreference]:
        if (
            self._claim is None
            or self._claim.application is None
            or self._claim.application.payment_preference is None
        ):
            return None
        return MaskedPaymentPreference.from_application_payment_preference(
            self._claim.application.payment_preference
        )

    @property
    def intermittent_leave_episodes(self) -> List[IntermittentLeaveEpisodeResponse]:
        """
        Returns the intermittent leave episodes for this claim.
        These are loaded dynamically.
        If they have been loaded already, we return the loaded values.
        If they have not been loaded already, we fetch them from FINEOS, and persist the result.
        """

        if self._intermittent_leave_episodes is None:
            self._intermittent_leave_episodes = self._get_intermittent_leave_episodes()

        return self._intermittent_leave_episodes

    def _get_intermittent_leave_episodes(self) -> List[IntermittentLeaveEpisodeResponse]:
        if not self.approved_intermittent_absence_periods:
            # No need to fetch IL episodes from FINEOS if there are no approved intermittent absence periods
            return []

        return claim_service.get_intermittent_leave_episodes_from_fineos(
            self._claim, self._db_session
        )

    @property
    def is_id_proofed(self) -> Optional[bool]:
        return self._claim.is_id_proofed

    @property
    def is_reviewable(self) -> bool:
        return self._claim.is_reviewable

    @property
    def latest_follow_up_date(self) -> Optional[date]:
        return self._claim.latest_follow_up_date

    @property
    def leave_requests(self) -> Optional[List[LeaveRequest]]:
        return self._claim.leave_requests

    @property
    def managed_requirements(self) -> Optional[List[ManagedRequirement]]:
        return self._claim.managed_requirements

    @property
    def needs_payment_calculation(self) -> bool:
        return self._claim.needs_payment_calculation

    @property
    def organization_unit(self) -> Optional[OrganizationUnit]:
        return self._claim.organization_unit

    @property
    def organization_unit_id(self) -> Optional[str]:
        return self._claim.organization_unit_id

    @property
    def prior_year(self) -> Optional[date]:
        return self._claim.prior_year

    @property
    def state_logs(self) -> list[StateLog]:
        return self._claim.state_logs

    @property
    def updated_at(self) -> datetime:
        return self._claim.updated_at
