from datetime import date, datetime
from typing import Optional

from pydantic import validator
from sqlalchemy.orm import Session

from massgov.pfml.api.models.appeals.common import AppealRepresentativeOption
from massgov.pfml.api.models.documents.common import ContentType, DocumentType
from massgov.pfml.api.services import fineos_actions
from massgov.pfml.db.lookup_data.documents import DocumentType as DocumentTypeDB
from massgov.pfml.db.models.appeal import Appeal
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.documents import Document
from massgov.pfml.pdf_api.client import AbstractPDFClient
from massgov.pfml.pdf_api.common import PDF_APPEAL_INTAKE_TYPE
from massgov.pfml.pdf_api.models import GeneratePDFRequest
from massgov.pfml.util import logging
from massgov.pfml.util.logging.appeals import get_appeal_log_attributes
from massgov.pfml.util.pydantic import PydanticBaseModelWithSnakeCaseAlias

logger = logging.get_logger(__name__)


class AppealIntakeTemplateFields(PydanticBaseModelWithSnakeCaseAlias):
    """
    Conversion class for taking an appeal in the DB and formatting the data to send to the PDF API.
    These fields should map directly to the AppealIntake.html template in the PDF API codebase.
    """

    fineosAbsenceId: str
    appealEmailAddress: Optional[str]
    appealPhoneNumber: str
    appealReason: Optional[str]
    appealRepresentativeOption: AppealRepresentativeOption
    hasReadNotices: Optional[str]
    reasonForNotReadingNotices: Optional[str]
    needsInterpreter: str
    interpreterLanguageRequested: Optional[str]
    otherIntepreterLanguageRequested: Optional[str]
    originallyDecidedAt: str
    computedIsMoreThanTenDaysPastDecision: bool
    hadPreventingCircumstances: Optional[str]
    originallyDecidedAtReasonForPastDue: Optional[str]
    submittedAt: str

    @validator("needsInterpreter", pre=True)
    def parse_needs_interpreter(cls, v):  # noqa: B902
        return "Yes" if v else "No"

    @validator("hasReadNotices", pre=True)
    def parse_has_read_notices(cls, v):  # noqa: B902
        if v is None:
            return v
        return "Yes" if v else "No"

    @validator("hadPreventingCircumstances", pre=True)
    def parse_had_preventing_circumstances(cls, v):  # noqa: B902
        if v is None:
            return v
        return "Yes" if v else "No"

    @validator("originallyDecidedAt", pre=True)
    def parse_originally_decided_at(cls, v):  # noqa: B902
        if isinstance(v, date):
            return v.strftime("%Y-%m-%d")

        return v

    @validator("submittedAt", pre=True)
    def parse_submitted_at(cls, v):  # noqa: B902
        if isinstance(v, datetime):
            return v.strftime("%Y-%m-%d %H:%M:%S %Z")

        return v

    @classmethod
    def from_orm(cls, appeal: Appeal) -> "AppealIntakeTemplateFields":
        template_fields = super().from_orm(appeal)

        if not appeal.claim.employee:
            raise ValueError("appeal.claim.employee is required")

        email = appeal.claim.employee.email_address
        if not email and appeal.application:
            email = appeal.application.user.email_address
        template_fields.appealEmailAddress = email

        if not template_fields.interpreterLanguageRequested:
            template_fields.interpreterLanguageRequested = (
                appeal.other_interpreter_language_requested
            )
        return template_fields


class GenerateIntakePDFService:
    """
    A service to generate and upload the PDF for an individual appeal.
    """

    def __init__(self, db_session: Session, pdf_client: AbstractPDFClient, appeal: Appeal) -> None:
        self._db_session = db_session
        self._pdf_client = pdf_client
        self._appeal = appeal

        self._content_type = ContentType.pdf
        self._document_type = DocumentType.appeal_form
        self._file_name = "Appeal_Intake"
        self._file_description = "Appeal intake details"

        self._appeal_log_attributes = get_appeal_log_attributes(self._appeal)

    @property
    def application(self) -> Application:
        """
        Type-safe way to grab the appeal's associated application. We expect
        every unprocessed appeal to have an associated application, since the appeal
        was created through the Portal.
        """
        application = self._appeal.application

        if not application:
            raise ValueError("Appeal does not have an associated application.")

        return application

    def log_info(self, message: str) -> None:
        """
        Log utility for logging with appeal attributes.
        """
        return logger.info(message, extra=self._appeal_log_attributes)

    def process_appeal(self) -> None:
        """
        Generate the appeal's intake PDF, upload it to FINEOS, and record it in our database.
        """
        self.log_info("generate_intake_pdf - Generating Appeal Intake PDF")
        fields = AppealIntakeTemplateFields.from_orm(self._appeal).dict()

        pdf_response = self._pdf_client.generate(
            GeneratePDFRequest(
                id="pdf",
                type=PDF_APPEAL_INTAKE_TYPE,
                save=False,
                templateValues=fields,
            )
        )

        assert pdf_response.file
        file_size = len(pdf_response.file)

        self.log_info(f"generate_intake_pdf - Uploading Appeal Intake PDF of size {file_size}")
        fineos_document_id = self.upload_pdf_document(bytes(pdf_response.file))

        self.log_info("generate_intake_pdf - Recording PDF in the PFML database")
        if len(fineos_document_id) > 0:
            self.record_pdf_document(fineos_document_id, file_size)

        self.log_info("generate_intake_pdf - Completed PDF processing")

    def upload_pdf_document(self, pdf_bytes: bytes) -> str:
        """
        Uploads the PDF to FINEOS.
        """
        fineos_document = fineos_actions.upload_document(
            self._appeal.application,  # type: ignore
            self._document_type,
            pdf_bytes,
            f"{self._file_name}.pdf",
            self._content_type,
            self._file_description,
            self._db_session,
            fineos_appeal_id=str(self._appeal.fineos_appeal_id),
            with_multipart=False,
        )
        return str(fineos_document.documentId)

    def record_pdf_document(self, fineos_document_id: str, file_size: int) -> None:
        """
        Records the appeal PDF document in our DB. This marks it as processed so we don't
        try to regenerate the PDF and upload it to FINEOS again.
        """
        document_type_id = DocumentTypeDB.get_id(self._document_type)
        document = Document(
            appeal_id=self._appeal.appeal_id,
            user_id=self.application.user_id,
            fineos_id=fineos_document_id,
            size_bytes=file_size,
            is_stored_in_s3=False,
            name=self._file_name,
            description=self._file_description,
            document_type_id=document_type_id,
            pfml_document_type_id=document_type_id,
        )

        self._db_session.add(document)
        self._db_session.commit()
