import os
import tempfile
from enum import Enum

import massgov.pfml.db as db
import massgov.pfml.util.logging as logging
from massgov.pfml import features
from massgov.pfml.dor.employer_exemptions.employer_exemptions_export_step import (
    EmployerExemptionsExportStep,
)
from massgov.pfml.util.batch.task_runner import TaskRunner
from massgov.pfml.util.bg import background_task
from massgov.pfml.util.datetime import get_now_us_eastern

logger = logging.get_logger(__name__)


class EmployerExemptionsDORExporterTaskRunner(TaskRunner):
    class StepMapping(str, Enum):
        EMPLOYER_EXEMPTIONS_EXPORT_FILE_CREATION = "employer-exemptions-export"

    now = get_now_us_eastern()
    exemptions_file_dir = tempfile.gettempdir()
    exemptions_file_path = os.path.join(
        exemptions_file_dir,
        f"employer_exemptions_export_{now.strftime('%Y%m%d')}.txt",
    )

    def run_steps(self, db_session: db.Session, log_entry_db_session: db.Session) -> None:
        # TODO(PFMLPB-23385): remove enable_employer_exemptions_dor_data_transfer feature flag
        export_to_dor_ff = (
            features.get_config().employer_exemptions.enable_employer_exemptions_dor_data_transfer
        )

        if export_to_dor_ff:
            """Process Nightly Reports"""
            logger.info("Start - Employer Exemptions Export ")
            logger.info(f"Current time: {self.now}")
            if self.is_enabled(self.StepMapping.EMPLOYER_EXEMPTIONS_EXPORT_FILE_CREATION):
                EmployerExemptionsExportStep(
                    db_session=db_session,
                    log_entry_db_session=log_entry_db_session,
                    exemptions_file_reference=self.exemptions_file_path,
                ).run()
            logger.info("Done - Nightly Employer Exemptions Export")
            # TODO (PFMLPB-23385): add code to send file to DOR via MoveIT
            # TODO (PFMLPB-23385): to use the output folder path use os.environ["OUTPUT_FOLDER_PATH"] in the step file
        else:
            logger.info(
                "Employer Exemptions Export to DOR is disabled. " "Skipping the export steps."
            )

    # TODO (PFMLPB-23385): add code to clean up temp dir and file


@background_task("employer-exemptions-export-for-dor")
def main():
    """Entry point for Employer Exemptions Export to DOR task."""
    EmployerExemptionsDORExporterTaskRunner().run()


if __name__ == "__main__":
    main()
