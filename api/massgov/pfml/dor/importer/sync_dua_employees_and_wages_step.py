import bisect
import dataclasses
from datetime import date, datetime
from decimal import Decimal
from enum import auto
from typing import Literal, TypeA<PERSON>s
from uuid import UUID

from sqlalchemy import update
from sqlalchemy.orm import Query
from sqlalchemy.orm.query import Row, RowReturningQuery

import massgov.pfml.dor.importer.dor_file_formats as dor_file_formats
import massgov.pfml.util.logging as logging
from massgov.pfml.db import Session
from massgov.pfml.db.lookup_data.employees import WagesAndContributionsDatasource
from massgov.pfml.db.lookup_data.reference_file_type import ReferenceFileType
from massgov.pfml.db.models.dua import DuaQuarterWagesEmployee, SyncDuaEmployeesAndWagesCheckpoint
from massgov.pfml.db.models.reference_file.reference_file import ReferenceFile
from massgov.pfml.dor.importer.dor_file_formats import ParsedEmployeeWageLine
from massgov.pfml.dor.importer.dor_shared_utils import ImportReport
from massgov.pfml.dor.importer.import_wages import WageImporter
from massgov.pfml.util.batch.step import Step
from massgov.pfml.util.str_enum import StrEnum

DuaQuarterWagesEmployeeId: TypeAlias = int
EmployeeWageLine: TypeAlias = tuple[int, str, str, str, str, str, str, str]

logger = logging.get_logger(__name__)


class SyncDuaEmployeesAndWagesStep(Step):
    """
    Syncs `Employee`s, `WagesAndContributions`, and related entities with
    `DuaQuarterWagesEmployee`s.

    Processes DUA quarter wage records in chronological order of ingestion. May be limited by
    `record_process_limit`.
    """

    INITIAL_CHECKPOINT_ID = -1
    """
    Starting ID of `SyncDuaEmployeesAndWagesCheckpoint` which indicates that no records have been
    processed.
    """

    WAGE_RECORD_BATCH_SIZE = 1_000
    """Limit of `DuaQuarterWagesEmployee` records per batch iteration."""

    class Metrics(StrEnum):
        ENDING_CHECKPOINT = auto()
        """ID of `DuaQuarterWagesEmployee` record step ended processing."""

        STARTING_CHECKPOINT = auto()
        """ID of `DuaQuarterWagesEmployee` record step began processing."""

    def __init__(
        self,
        db_session: Session,
        log_entry_db_session: Session,
        record_process_limit: int | None = None,
        should_add_to_report_queue: bool = False,
    ) -> None:
        super().__init__(db_session, log_entry_db_session, should_add_to_report_queue)
        self._employee_ssn_to_id_map: dict[str, UUID] = {}
        self._processed_record_count = 0
        self._record_process_limit = record_process_limit

    def run_step(self) -> None:
        report = ImportReport()

        try:
            self._import_employees_and_wages(report)
            self._end_report(report, "", "success")
        except Exception as exception:
            message = f"An unexpected exception occurred during import: {exception}"
            logger.error(message)
            self._end_report(report, message, "error")
            raise

    def _create_initial_checkpoint(self) -> None:
        checkpoint = SyncDuaEmployeesAndWagesCheckpoint(
            dua_quarter_wages_employee_id=self.INITIAL_CHECKPOINT_ID
        )
        self.db_session.add(checkpoint)
        self.db_session.commit()

    def _end_report(
        self, report: ImportReport, message: str, status: Literal["error", "success"]
    ) -> None:
        report.end = datetime.now().isoformat()
        report.status = status
        report_dict = dataclasses.asdict(report)
        # The 'message' key is reserved by a logger which is downstream from set_metrics. Passing a
        # 'message' in the metrics results in an error being raised from the logger.
        del report_dict["message"]
        report_dict["note"] = message
        self.set_metrics(report_dict)

    def _get_checkpoint(self) -> DuaQuarterWagesEmployeeId:
        saved_checkpoint = self.db_session.query(
            SyncDuaEmployeesAndWagesCheckpoint.dua_quarter_wages_employee_id
        ).scalar()

        if saved_checkpoint is None:
            self._create_initial_checkpoint()
            return self.INITIAL_CHECKPOINT_ID

        return saved_checkpoint

    def _get_next_batch_size(self) -> int:
        return (
            self.WAGE_RECORD_BATCH_SIZE
            if self._record_process_limit is None
            else min(
                self.WAGE_RECORD_BATCH_SIZE,
                self._record_process_limit - self._processed_record_count,
            )
        )

    def _get_next_reference_file(self) -> ReferenceFile | None:
        return (
            self._query_unprocessed_reference_files()
            .order_by(ReferenceFile.created_at.asc())
            .first()
        )

    def _get_wage_data(
        self, batch: list[DuaQuarterWagesEmployeeId], report: ImportReport
    ) -> list[ParsedEmployeeWageLine]:
        dua_quarter_wages_employees = (
            self._query_wage_data(batch)
            .order_by(DuaQuarterWagesEmployee.dua_quarter_wages_employee_id.asc())
            .all()
        )
        wage_lines_or_nones = map(
            lambda employee_wages: _transform_to_parsed_employee_wage_line(employee_wages, report),
            dua_quarter_wages_employees,
        )
        wage_lines = [
            wage_line_or_none
            for wage_line_or_none in wage_lines_or_nones
            if wage_line_or_none is not None
        ]
        return wage_lines

    def _get_wage_ids_from_reference_file(
        self, reference_file: ReferenceFile
    ) -> list[DuaQuarterWagesEmployeeId]:
        """
        Returns a sorted list of `DuaQuarterWagesEmployee` IDs from `reference_file`.

        The list represents records that have been deduplicated by selecting the first record for
        each unique combination of `employee_ssn`, `employer_fein`, and `year_and_quarter`, with a
        preference for "corrected returns".
        """

        deduplicated_records = (
            self.db_session.query(DuaQuarterWagesEmployee.dua_quarter_wages_employee_id)
            .filter(DuaQuarterWagesEmployee.reference_file == reference_file)
            .distinct(
                DuaQuarterWagesEmployee.employee_ssn,
                DuaQuarterWagesEmployee.employer_fein,
                DuaQuarterWagesEmployee.year_and_quarter,
            )
            .order_by(
                DuaQuarterWagesEmployee.employee_ssn.asc(),
                DuaQuarterWagesEmployee.employer_fein.asc(),
                DuaQuarterWagesEmployee.year_and_quarter.asc(),
                # Sorting "is_corrected_return" in descending order prioritizes the "Y" records over
                # the "N" records. This makes it more likely that the most accurate record is
                # selected for processing when there are duplicates.
                DuaQuarterWagesEmployee.is_corrected_return.desc(),
                DuaQuarterWagesEmployee.dua_quarter_wages_employee_id.asc(),
            )
            .subquery()
        )

        rows = (
            self.db_session.query(deduplicated_records.c.dua_quarter_wages_employee_id)
            .order_by(deduplicated_records.c.dua_quarter_wages_employee_id.asc())
            .all()
        )

        return [row[0] for row in rows]

    def _import_employees_and_wages(self, report: ImportReport) -> None:
        checkpoint = self._get_checkpoint()
        logger.info(f"Importing employees and wages from checkpoint {checkpoint}…")
        self.set_metrics({self.Metrics.STARTING_CHECKPOINT: checkpoint})
        reference_file = self._get_next_reference_file()
        reference_file_wage_ids: list[DuaQuarterWagesEmployeeId] = []
        batch_start = 0

        if reference_file is not None:
            logger.info(f"Processing reference file {reference_file.reference_file_id}…")
            reference_file_wage_ids = self._get_wage_ids_from_reference_file(reference_file)
            batch_start = bisect.bisect_right(reference_file_wage_ids, checkpoint)

        while self._should_continue_processing():
            if reference_file is None:
                logger.info("No unprocessed reference files remaining.")
                break

            batch_size = self._get_next_batch_size()
            batch_end = batch_start + batch_size
            batch = reference_file_wage_ids[batch_start:batch_end]
            self._process_batch(batch, report)
            last_seen_dua_quarter_wages_employee_id = batch[-1] if len(batch) > 0 else None

            if last_seen_dua_quarter_wages_employee_id is None:
                reference_file.processed_import_log_id = self.get_import_log_id()
                logger.info(f"Completed reference file {reference_file.reference_file_id}.")
                reference_file = self._get_next_reference_file()

                if reference_file is not None:
                    logger.info(f"Processing reference file {reference_file.reference_file_id}…")
                    reference_file_wage_ids = self._get_wage_ids_from_reference_file(reference_file)
                    batch_start = 0
            else:
                batch_start = batch_end
                checkpoint = last_seen_dua_quarter_wages_employee_id
                self._save_checkpoint(checkpoint)

        self.set_metrics({self.Metrics.ENDING_CHECKPOINT: checkpoint})
        logger.info("Successfully imported employees and wages.")

    def _run_wage_importer(
        self, wage_data: list[ParsedEmployeeWageLine], report: ImportReport
    ) -> None:
        importer = WageImporter(
            self.db_session,
            wage_data,
            self._employee_ssn_to_id_map,
            report,
            # Employee name changes are not reported for this step. An ephemeral list satisfies this
            # parameter.
            [],
            self.get_import_log_id(),
            WagesAndContributionsDatasource.DUA_REPORTED_WAGES,
        )

        importer.import_employees_and_wage_data(
            record_new_employees=True,
            should_update_existing_employees=False,
            should_update_wages=True,
        )

    def _save_checkpoint(
        self, last_seen_dua_quarter_wages_employee_id: DuaQuarterWagesEmployeeId
    ) -> None:
        save = update(SyncDuaEmployeesAndWagesCheckpoint).values(
            dua_quarter_wages_employee_id=last_seen_dua_quarter_wages_employee_id
        )
        self.db_session.execute(save)
        self.db_session.commit()

    def _should_continue_processing(self) -> bool:
        return (
            self._record_process_limit is None
            or self._processed_record_count < self._record_process_limit
        )

    def _process_batch(self, batch: list[DuaQuarterWagesEmployeeId], report: ImportReport) -> None:
        logger.info("Beginning new batch…")
        wage_data = self._get_wage_data(batch, report)
        self._run_wage_importer(wage_data, report)
        self._processed_record_count += len(batch)
        logger.info(f"Completed batch containing {len(wage_data)} valid wage record(s).")

    def _query_unprocessed_reference_files(self) -> "Query[ReferenceFile]":
        return (
            self.db_session.query(ReferenceFile)
            .filter(ReferenceFile.reference_file_type == ReferenceFileType.DUA_QUARTER_WAGES)
            .filter(ReferenceFile.processed_import_log_id.is_(None))
        )

    def _query_wage_data(
        self, batch: list[DuaQuarterWagesEmployeeId]
    ) -> RowReturningQuery[EmployeeWageLine]:
        """Returns a query for `EmployeeWageLine`s in `batch`."""

        return self.db_session.query(
            DuaQuarterWagesEmployee.dua_quarter_wages_employee_id,
            DuaQuarterWagesEmployee.employer_dua_account_num,
            DuaQuarterWagesEmployee.employee_first_name,
            DuaQuarterWagesEmployee.employee_last_name,
            DuaQuarterWagesEmployee.employee_ssn,
            DuaQuarterWagesEmployee.employer_fein,
            DuaQuarterWagesEmployee.employee_wages,
            DuaQuarterWagesEmployee.year_and_quarter,
        ).filter(DuaQuarterWagesEmployee.dua_quarter_wages_employee_id.in_(batch))


def _transform_to_parsed_employee_wage_line(
    wages: Row[EmployeeWageLine], report: ImportReport
) -> ParsedEmployeeWageLine | None:
    try:
        parsed_employee_wage_line: ParsedEmployeeWageLine = {
            # "B" refers to an employee record. It is set for consistency with other datasources.
            "record_type": "B",
            # "account_key" is intended to be a DFML account key. It is not available in DUA data.
            "account_key": f"dua_import_{wages.employer_dua_account_num}",
            "filing_period": _transform_year_and_quarter_to_filing_period(wages.year_and_quarter),
            "employee_first_name": dor_file_formats.parse_name(wages.employee_first_name),
            "employee_last_name": dor_file_formats.parse_name(wages.employee_last_name),
            "employee_ssn": wages.employee_ssn,
            "independent_contractor": None,
            "opt_in": None,
            "employee_ytd_wages": None,
            "employee_qtr_wages": _transform_employee_wages_to_employee_qtr_wages(
                wages.employee_wages
            ),
            "employee_medical": None,
            "employer_medical": None,
            "employee_family": None,
            "employer_family": None,
            "employer_fein": wages.employer_fein,
        }
        report.parsed_employees_info_count += 1
        return parsed_employee_wage_line
    except Exception as exception:
        logger.warning(
            f"Failed to transform DuaQuarterWagesEmployer to ParsedEmployeeWageLine: {exception}"
        )
        report.parsed_employee_wage_ids_with_exception.append(wages.dua_quarter_wages_employee_id)
        report.parsed_employees_exception_count += 1
        return None


def _transform_employee_wages_to_employee_qtr_wages(employee_wages: str) -> Decimal:
    """Returns a `Decimal` for the employee qtr wages represented by DUA `employee_wages`."""

    dollars = employee_wages[:10]
    cents = employee_wages[10:12]
    return Decimal(f"{dollars}.{cents}")


def _transform_year_and_quarter_to_filing_period(year_and_quarter: str) -> date:
    """Returns a `date` for the filing period represented by DUA `year_and_quarter`."""

    year = int(year_and_quarter[:4])
    quarter = year_and_quarter[4:5]

    match quarter:
        case "1":
            return date(year, 3, 31)
        case "2":
            return date(year, 6, 30)
        case "3":
            return date(year, 9, 30)
        case "4":
            return date(year, 12, 31)
        case _:
            raise ValueError(f'"{year_and_quarter}" ends with invalid quarter "{quarter}"')
