import enum
from typing import List  # Dict,

# import os
# import xml.dom.minidom as minidom
# from datetime import datetime
# from decimal import Decimal
# import massgov.pfml.util.files as file_util
import massgov.pfml.util.logging
from massgov.pfml import db
from massgov.pfml.dor.step import DorStep
from massgov.pfml.dor.util.employer_exemption_dor_export import (
    EmployerExemptionApplication,
    format_employer_exemption_line,
    get_modified_employer_exemptions,
)

# from massgov.pfml.util.datetime import get_now_us_eastern

# from massgov.pfml.db.models.employees import Overpayment, ReferenceFile
# from massgov.pfml.db.lookup_data.reference_file_type import ReferenceFileType
# from massgov.pfml.db.models.ctr.batch_identifier import CtrBatchIdentifier


# from sqlalchemy.orm import joinedload


logger = massgov.pfml.util.logging.get_logger(__name__)


class EmployerExemptionsExportStep(DorStep):
    """
    This step creates the flat file for employer exemptions
    and sends it to DOR via MoveIT.

    """

    class Metrics(str, enum.Enum):
        EXEMPTION_RECORDS_IN_FILE = "exemption_records_in_file"

    exemptions_file_reference: str

    def __init__(
        self,
        db_session: db.Session,
        log_entry_db_session: db.Session,
        exemptions_file_reference: str,
    ):
        self.exemptions_file_reference = exemptions_file_reference
        super().__init__(db_session, log_entry_db_session)

    def run_step(self):
        logger.info("Processing MMARS RE Records")
        exemptions_for_dor = get_modified_employer_exemptions(self.db_session)
        if not exemptions_for_dor:
            logger.info("No exeptions found for DOR")
        else:
            logger.info(f"There are {len(exemptions_for_dor)} Employer Exemptions for DOR")
        self._build_exemptions_file(exemptions_for_dor)
        logger.info("Successfully sent Employer Exemptions file to DOR")

    def _build_exemptions_file(
        self, exemptions_for_dor: List[EmployerExemptionApplication]
    ) -> None:
        """
        Build Employer Exemptions file
        :param exemptions_for_dor: List of Employer Exemption Applications
        :return: None
        """
        # if exemptions_for_dor is empty, create header that says no exemptions and write to file
        # if exemptions_for_dor has data, create file with exemptions with header and calling format_employer_exemption_line for each exemption
        # now = get_now_us_eastern()
        header_line = "H" + str(len(exemptions_for_dor)).rjust(10, "0")
        try:
            # write header and exemptions to file
            with open(self.exemptions_file_reference, "w") as file:
                file.write(header_line + "\n")
                for exemption in exemptions_for_dor:
                    exemption_line = format_employer_exemption_line(self.db_session, exemption)
                    file.write(exemption_line + "\n")
            # file_util.copy_file(self.exemptions_file_reference, f"./employer_exemptions_export_{now.strftime('%Y%m%d')}.txt")

        except Exception as e:
            logger.exception("Unable to create Employers Exemptions file")
            raise e

        # TODO (PFMLPB-23385): Impliment sending the file using move it in a new step file
        # try:
        #     file_util.copy_file(re_file_path, moveit_re_file_path)
        #     logger.info(
        #         "RE File copied to MoveIT", extra={"moveit_re_file_path": moveit_re_file_path}
        #     )

        #     file_util.copy_file(inf_file_path, moveit_inf_re_file_path)
        #     logger.info(
        #         "RE INF File copied to MoveIT",
        #         extra={"moveit_inf_re_file_path": moveit_inf_re_file_path},
        #     )
        # except Exception as e:
        #     logger.exception("Unable to copy VCC file to MoveIT")
        #     raise e

        # extra["re_file_path"] = re_file_path
        # extra["inf_file_path"] = inf_file_path
        # extra["reference_file_id"] = str(reference_file.reference_file_id)
        # logger.info("RE File created successfully", extra=extra)


# try:
#     now = get_now_us_eastern()
#     # create ctr batch identifier record
#     ctr_batch = payments_util.generate_next_batch_identifier(
#         self.db_session, now, MmarsEventType.RE_TRX
#     )
#     re_file_name = ctr_batch.ctr_batch_identifier
#     re_file_path = os.path.join(
#         self.s3_config.pfml_mmars_file_base_location,
#         payments_util.MMARS_Constants.RE_FILE_FOLDER,
#         re_file_name,
#         re_file_name + ".dat",
#     )
#     inf_file_path = os.path.join(
#         self.s3_config.pfml_mmars_file_base_location,
#         payments_util.MMARS_Constants.RE_FILE_FOLDER,
#         re_file_name,
#         re_file_name + ".inf",
#     )
#     moveit_re_file_path = os.path.join(
#         self.s3_config.pfml_mmars_file_base_location,
#         payments_util.MMARS_Constants.MOVEIT_OUTBOUND_FILE_FOLDER,
#         payments_util.MMARS_Constants.MMARS_FILE_NAME.MOVEIT_OUTBOUND_RE_FILE,
#     )
#     moveit_inf_re_file_path = os.path.join(
#         self.s3_config.pfml_mmars_file_base_location,
#         payments_util.MMARS_Constants.MOVEIT_OUTBOUND_FILE_FOLDER,
#         payments_util.MMARS_Constants.MMARS_FILE_NAME.MOVEIT_OUTBOUND_RE_INF_FILE,
#     )
