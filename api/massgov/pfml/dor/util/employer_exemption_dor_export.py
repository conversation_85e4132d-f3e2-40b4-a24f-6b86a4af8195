from typing import List

from massgov.pfml.api.services.insurance_providers import (
    get_insurance_provider_from_db,
    get_insurance_providers_plan_from_db,
)
from massgov.pfml.db import Session
from massgov.pfml.db.lookup_data.employer_exemptions import EmployerExemptionApplicationStatus
from massgov.pfml.db.models.employees import Employer
from massgov.pfml.db.models.employer_exemptions import EmployerExemptionApplication


def get_modified_employer_exemptions(db_session: Session) -> List[EmployerExemptionApplication]:
    """
    Get Modified Employer Exemption Applications from the database
    """
    employer_exemptions = (
        db_session.query(EmployerExemptionApplication)
        .filter(
            EmployerExemptionApplication.employer_exemption_application_status_id
            == EmployerExemptionApplicationStatus.APPROVED.employer_exemption_application_status_id,
            EmployerExemptionApplication.synced_to_dor.is_(False),
        )
        .all()
    )
    return employer_exemptions


def format_employer_exemption_line(
    db_session: Session,
    employer_exemption: EmployerExemptionApplication,
) -> str:
    """
    Format the employer exemption line for export
    """
    employer: Employer = (
        db_session.query(Employer)
        .filter(
            Employer.employer_id == employer_exemption.employer_id,
        )
        .one()
    )
    provider = get_insurance_provider_from_db(
        db_session,
        employer_exemption.insurance_provider_id,
    )
    plan = get_insurance_providers_plan_from_db(
        db_session,
        employer_exemption.insurance_plan_id,
        employer_exemption.insurance_provider_id,
    )

    # DOR uses cease as the type for updated or modified records, and new for new records.
    RECORD_TYPE_CEASE = "C"
    RECORD_TYPE_NEW = "N"

    record_type = RECORD_TYPE_CEASE if employer_exemption.synced_to_dor_at else RECORD_TYPE_NEW

    employer_fein = str(employer.employer_fein).ljust(9, " ")
    id_type = "FEIN"
    commence_date = str(employer_exemption.insurance_plan_effective_at).replace("-", "")
    cease_date = str(employer_exemption.insurance_plan_expires_at).replace("-", "")
    medical_exemption = 1 if employer_exemption.has_medical_exemption else 0
    family_exemption = 1 if employer_exemption.has_family_exemption else 0
    employer_exemption_type = (
        "SelfInsure".ljust(10, " ")
        if employer_exemption.is_self_insured_plan
        else "Private".ljust(10, " ")
    )
    insurance_provider = "".ljust(255, " ")
    if provider is not None:
        insurance_provider = str(provider.insurance_provider_name).ljust(255, " ")
    family_plan_name = "".ljust(100, " ")
    if plan is not None and employer_exemption.has_family_exemption:
        family_plan_name = str(plan.form_name).ljust(100, " ")
    medical_plan_name = "".ljust(100, " ")
    if plan is not None and employer_exemption.has_medical_exemption:
        medical_plan_name = str(plan.form_name).ljust(100, " ")
    workforce_size = (
        str(employer_exemption.average_workforce_count).rjust(10, "0")
        if employer_exemption.average_workforce_count
        else "".rjust(10, "0")
    )

    contact_name = (
        f"{employer_exemption.contact_first_name} {employer_exemption.contact_last_name}".ljust(
            255, " "
        )
    )
    contact_title = str(employer_exemption.contact_title).ljust(100, " ")
    contact_phone = (employer_exemption.contact_phone.phone_number).ljust(30, " ")
    contact_email = str(employer_exemption.contact_email_address).ljust(255, " ")

    return f"{record_type}{employer_fein}{id_type}{commence_date}{cease_date}{medical_exemption}{family_exemption}{employer_exemption_type}{insurance_provider}{family_plan_name}{medical_plan_name}{workforce_size}{contact_name}{contact_title}{contact_phone}{contact_email}"
