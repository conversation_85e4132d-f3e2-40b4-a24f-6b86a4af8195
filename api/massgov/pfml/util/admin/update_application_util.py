import csv
import os

import massgov.pfml.util.files as file_util
import massgov.pfml.util.logging as logging
from massgov.pfml import db
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.employees import Claim, Employee

logger = logging.get_logger(__name__)


class UpdateApplication:
    log_attr: dict
    commit_changes: bool
    file_location: str
    application_id: str
    fineos_absence_id: str
    psd_number: str
    total_records_processed = 0
    total_records_updated = 0
    total_records_skipped = 0
    total_errors = 0

    def __init__(
        self,
        psd_number,
        fineos_absence_id,
        application_id,
        file_location,
        dry_run,
        db_session,
    ):

        self.log_attr = {
            "dry_run": dry_run,
            "psd_ticket_number": psd_number,
            "fineos_absence_id": fineos_absence_id,
            "application_id": application_id,
            "file_location": file_location,
        }

        if dry_run:
            logger.info("***DRY RUN MODE ENABLED***")
        else:
            logger.info("***DRY RUN MODE DISABLED***")

        self.db_session = db.init() if not db_session else db_session

        self.commit_changes = not dry_run

        if file_location:
            self.file_location = file_location
        else:
            self.file_location = ""
            self.application_id = application_id
            self.fineos_absence_id = fineos_absence_id

        self.psd_number = psd_number

    def run(self):

        logger.info("Starting ECS Task: Update Application", extra=self.log_attr)

        if self.file_location:
            csv_path = os.path.join(self.file_location)
            parsed_rows = parse_csv(csv_path)
            logger.info("Total rows parsed, %s", len(parsed_rows))
            self.total_records_processed = len(parsed_rows)

            for i, row in enumerate(parsed_rows):
                try:
                    application_id = row.get("ApplicationID")
                    fineos_absence_case_id = row.get("FineosAbsenceID")
                    self.update_application(i, application_id, fineos_absence_case_id)
                except Exception as e:
                    logger.error(f"Error updating application at index {i}: {e}")
                    self.total_errors += 1
                    continue
        else:
            try:
                self.total_records_processed = 1
                self.update_application(0, self.application_id, self.fineos_absence_id)
            except Exception as e:
                logger.error(f"Error processing Update Application: {e}", exc_info=True)
                self.total_errors = 1

        self.log_attr["total_records_updated"] = self.total_records_updated
        self.log_attr["total_errors"] = self.total_errors
        self.log_attr["total_records_processed"] = self.total_records_processed
        self.log_attr["total_records_skipped"] = self.total_records_skipped
        logger.info("Completed ECS Task: Update Application", extra=self.log_attr)

    def update_application(
        self,
        line_number: int,
        application_id: str,
        fineos_absence_case_id: str,
    ) -> None:
        """
        Updates the application record with the correct tax id where an incorrect tax id was initially used
        """
        try:
            if application_id == "" or fineos_absence_case_id == "":
                logger.error(
                    f"Missing application id or fineos absence case id in row {line_number}"
                )
                self.total_records_skipped += 1
                return

            # Update the application record
            existing_app = (
                self.db_session.query(Application)
                .filter(Application.application_id == application_id)
                .one_or_none()
            )

            if not existing_app:
                logger.info(f"Application {application_id} does not exist in the database")
                self.total_records_skipped += 1
                return
            if existing_app.claim_id is not None:
                logger.info(
                    f"Application {application_id} already has a claim associated with it {existing_app.claim_id}."
                )
                self.total_records_skipped += 1
                return

            existing_taxid = existing_app.tax_identifier_id

            claim = (
                self.db_session.query(Claim)
                .filter(Claim.fineos_absence_id == fineos_absence_case_id)
                .one_or_none()
            )
            if not claim:
                logger.info(
                    f"Claim with fineos absence case id {fineos_absence_case_id} does not exist"
                )
                self.total_records_skipped += 1
                return

            # Check if the claim is already associated with any other application
            # This should not happen, but we check to ensure data integrity
            # and to check for any typos while entering the absence case id.

            claim_associated_with_other_application = (
                self.db_session.query(Application)
                .filter(Application.claim_id == claim.claim_id)
                .filter(Application.application_id != application_id)
                .one_or_none()
            )
            if claim_associated_with_other_application:
                logger.info(
                    f"Claim {claim.claim_id} is already associated with another application {claim_associated_with_other_application.application_id}"
                )
                self.total_records_skipped += 1
                return
            emp = (
                self.db_session.query(Employee)
                .filter(Employee.employee_id == claim.employee_id)
                .one_or_none()
            )

            if not emp:
                logger.info(
                    f"Employee with fineos absence case id {fineos_absence_case_id} does not exist"
                )
                self.total_records_skipped += 1
                return
            if existing_taxid == emp.tax_identifier_id:
                logger.info(
                    f"Application {application_id} already has the correct tax id {self.psd_number}"
                )
                self.total_records_skipped += 1
                return

            self.db_session.query(Application).filter(
                Application.application_id == application_id
            ).update(
                {Application.tax_identifier_id: emp.tax_identifier_id},
            )

            if self.commit_changes:
                self.total_records_updated += 1
                self.db_session.commit()
                logger.info(
                    f"Updated application {application_id} from tax_id {existing_taxid} to tax id {emp.tax_identifier_id}"
                )

            else:
                self.db_session.rollback()
                logger.info(
                    f"DRY RUN: Would have updated application {application_id} from tax_id {existing_taxid} to tax id {emp.tax_identifier_id}"
                )
                self.total_records_skipped += 1

        except Exception as e:
            logger.error(f"Error updating application {application_id}: {e}", exc_info=True)
            self.total_errors += 1


def parse_csv(csv_file_path: str) -> list:

    parsed_csv = csv.DictReader(file_util.open_stream(csv_file_path))
    return list(parsed_csv)
