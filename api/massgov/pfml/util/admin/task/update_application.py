import argparse
import sys
from typing import List

import massgov.pfml.db
from massgov.pfml.util.admin.update_application_util import UpdateApplication
from massgov.pfml.util.bg import background_task


def parse_script_args(args: List[str]) -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description="Updates application records with the correct tax id where an incorrect tax id was initally used"
    )

    parser.add_argument(
        "--psd_number",
        type=str,
        required=True,
        help="(Required) The PSD ticket number (eg 'PSD-4695'). \
            If none provided, put 'None Provided'",
    )

    parser.add_argument(
        "--application_id",
        type=str,
        required=False,
        help="The application id of the application to be updated",
    )

    parser.add_argument(
        "--fineos_absence_id",
        type=str,
        required=False,
        help="The fineos absence case id of the application to be updated",
    )
    parser.add_argument(
        "--file",
        type=str,
        required=False,
        help="The location of the file which contains application information to be updated.",
    )

    parser.add_argument(
        "--dry_run",
        default=True,
        action=argparse.BooleanOptionalAction,
    )

    return parser.parse_args(args)


@background_task("update-application")
def main() -> None:

    args = sys.argv[1:]
    parsed_args = parse_script_args(args)
    db_session = massgov.pfml.db.init()

    psd_number = parsed_args.psd_number.upper()
    application_id = parsed_args.application_id
    fineos_absence_id = parsed_args.fineos_absence_id
    # env = parsed_args.env
    file_location = parsed_args.file

    # dry_run defaults to true unless "false" is explicitly passed in
    dry_run = parsed_args.dry_run
    UpdateApplication(
        psd_number,
        fineos_absence_id,
        application_id,
        file_location,
        dry_run,
        db_session,
    ).run()
