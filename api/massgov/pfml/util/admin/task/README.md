## This is for Adding Employers, Adding/Verifying LAs and removing/deactivating LAs, Updating Application


1)
# Add Employer
This is the ECS task / script for adding employers to PFML dtabase manually. This replaces the current
prcoess: https://lwd.atlassian.net/wiki/spaces/PSD/pages/2339111008/Manual+Add+of+Employer+to+PFML+Prod+Database


## Running the Script
bin/run-ecs-task/run-task.sh performance add-employer FirstName.LastName add-employer --psd_number=7097 --file=s3://massgov-pfml-performance-agency-transfer/employer/Employer.csv --dry_run=True

usage: add-employer --psd_number PSD_NUMBER --file FILELOCATION [--dry_run DRY_RUN]

For info on how to run the script, use the `--help` flag.

a)  The CSV file header rows has to be in the same case as below:
    ER DBA NAME,ER NAME,FEIN,FINEOS CUSTOMER ID,FAMILY EXEMPTION,MEDICAL EXEMPTION,COMMENCE DATE,CEASE DATE,Address

b)  Check to make sure the text data does not contain a ',' in it. Remove any ',' as it will affect the column   values while parsing this csv file.
    Ex: 
        If ER DBA NAME is Test Pharma, Inc -> the Test Pharma is taken as ER DBA NAME and Inc will be assigned to column ER NAME.
        Instead change the name to Test Pharma Inc from Test Pharma, Inc for correct parsing

c) The values for COMMENCE DATE,CEASE DATE are usually defaulted. Add 2 commas for it in the CSV file.
    Refer to Section e)

d) The Address data has to be inside double quotes and address lines one,two,city,state,zipcode has
   to be separated by ','
   Ex:
        "1875 S Grant St,#400,San Mateo,CA,94402"
    If there is no address line 2, add coma in place of it
    Ex:
        "1875 S Grant St,,San Mateo,CA,94402"

e) Sample Employer.csv

ER DBA NAME,ER NAME,FEIN,FINEOS CUSTOMER ID,FAMILY EXEMPTION,MEDICAL EXEMPTION,COMMENCE DATE,CEASE DATE,Address
ABC Systems Inc.,ABC Systems Inc.,364816854,6497264,False,False,,,"3921 Fabian Way,,Palo Alto,CA,94303"
OXO FINANCIAL,OXO FINANCIAL,352240394,6497152,FALSE,FALSE,,,"1875 S Grant St,#400,San Mateo,CA,94402"


### Sample Run
On local machine:
`cd pfml/api`
Create a csv file with employer information and save it in local_s3/agency-transfer/reports/Employer.csv
Change the dry_run to False in api/Makefile if you want to commit changes.
`make add-employer

In `test` environment:
`cd pfml`

`.bin/run-ecs-task/run-task.sh test add-employer FirstName.LastName add-employer --psd_number=7097 --file=s3://massgov-pfml-test-agency-transfer/employer/Employer.csv --dry_run=true

### Reason
Adding employers manually was a multistep process including four steps to add and verify in shadow prod and production apart from opening Smarttronix tickets for sshaow prod creation and for adding employers in production. 

############################################################################################
2)
# Add Leave Admin
This is the ECS task / script for adding/verifying leave admins a to PFML dtabase manually. This replaces the current
prcoess: https://lwd.atlassian.net/wiki/spaces/PSD/pages/1641119989/Manually+Verifying+Leave+Administrators


## Running the Script
## If you are verifying multiple LAs, add them to a CSV file and run the script below.

bin/run-ecs-task/run-task.sh performance add-leave-admin FirstName.LastName add-leave-admin --psd_number=7097 --file=s3://massgov-pfml-performance-agency-transfer/employer/LA.csv --dry_run=True

usage: add-leave-admin --psd_number PSD_NUMBER --file FILELOCATION [--dry_run DRY_RUN]

For info on how to run the script, use the `--help` flag.

a)  The CSV file header rows has to be in the same case as below:
    EMAIL,FINEOS CUSTOMER NUMBER

b) Sample LA.csv

EMAIL,FINEOS CUSTOMER NUMBER
<EMAIL>,6497264
<EMAIL>,,6497152

## If you are verifying single LA, you can pass the email and fineos customer number to the script

bin/run-ecs-task/run-task.sh env_name add-leave-admin FirstName.LastName add-leave-admin --psd_number=7097 --fineos_customer_number=330000037 --email=<EMAIL> --dry_run=True

### Sample Run
On local machine:
`cd pfml/api`
Create a csv file with leave admin information and save it in local_s3/agency-transfer/reports/LA.csv
Change the dry_run to False in api/Makefile if you want to commit changes.
`make add-leave-admin`

In test and higher environment:
`cd pfml`

1) For verifying multiple LAs at once
`.bin/run-ecs-task/run-task.sh env-name add-leave-admin FirstName.LastName add-leave-admin --psd_number=7097 --file=s3://massgov-pfml-test-agency-transfer/employer/LA.csv --dry_run=true

2) For verifying single LA at a time
bin/run-ecs-task/run-task.sh env_name add-leave-admin FirstName.LastName add-leave-admin --psd_number=7097 --fineos_customer_number=330000037 --email=<EMAIL> --dry_run=True

### Reason
Adding/Verifying leave admins manually was a multistep process including four steps to add and verify in shadow prod and production apart from opening Smarttronix tickets for sshaow prod creation and for adding/verifying leave admin in production. 

##############################################################################################################
3)
# Remove Leave Admin
This is the ECS task / script for removing/deactivating leave admins a to PFML dtabase manually. This replaces the current
prcoess: https://lwd.atlassian.net/wiki/spaces/DD/pages/2267119659/SOP+-+Deactivate+Remove+a+Leave+Administrator+from+the+PFML+Database


## Running the Script
## If you are verifying multiple LAs, add them to a CSV file and run the script below.

bin/run-ecs-task/run-task.sh performance remove-leave-admin FirstName.LastName remove-leave-admin --psd_number=7097 --file=s3://massgov-pfml-performance-agency-transfer/employer/LA.csv --dry_run=True

usage: remove-leave-admin --psd_number PSD_NUMBER --file FILELOCATION [--dry_run DRY_RUN]

For info on how to run the script, use the `--help` flag.

a)  The CSV file header rows has to be in the same case as below:
    EMAIL,FINEOS CUSTOMER NUMBER

b) Sample LA.csv

EMAIL,FINEOS CUSTOMER NUMBER
<EMAIL>,6497264
<EMAIL>,,6497152

## If you are verifying single LA, you can pass the email and fineos customer number to the script

bin/run-ecs-task/run-task.sh env_name remove-leave-admin FirstName.LastName remove-leave-admin --psd_number=7097 --fineos_customer_number=330000037 --email=<EMAIL> --dry_run=True

### Sample Run
On local machine:
`cd pfml/api`
Create a csv file with leave admin information and save it in local_s3/agency-transfer/reports/LA.csv
Change the dry_run to False in api/Makefile if you want to commit changes.
`make remove-leave-admin`

In test and higher environment:
`cd pfml`

1) For verifying multiple LAs at once
`.bin/run-ecs-task/run-task.sh env-name remove-leave-admin FirstName.LastName remove-leave-admin --psd_number=7097 --file=s3://massgov-pfml-test-agency-transfer/employer/LA.csv --dry_run=true

2) For verifying single LA at a time
bin/run-ecs-task/run-task.sh env_name remove-leave-admin FirstName.LastName remove-leave-admin --psd_number=7097 --fineos_customer_number=330000037 --email=<EMAIL> --dry_run=True

### Reason
Removing/Deactivating leave admins manually was a multistep process including four steps to add and verify in shadow prod and production apart from opening Smarttronix tickets for sshaow prod creation and for removing/deactivating leave admin in production. 


### Dry Run
By default, all of the above scripts runs in "dry run" mode, and does not commit any changes to Amazon Cognito or the PFML db. To commit these changes, run the script with `--dry_run=false`
The dry run mode logs will let you know if any of the data has failed validations. The data can be fixed like no comma between city and state in Address field or no Address Line2 etc.


### Post run
The ECS task will run successfully skipping any rows that has errors. Please look at the logs to check if any rows were skipped when the imput is a csv file.
Delete the .csv files from S3 once the tasks have run successfully and you have verified the results.

### Local Setup
(See: [Local Integration with AWS](/docs/api/local-integration-with-aws.md))

## Optional Security Metrics
We record optional usage metadata and report them to New Relic. We should make sure not to expose any user PII via this metadata!

These are "optional", but the script will complain if they are not included - we should explicitly specify "None Provided" if we're not able to provide this info.


## Handling Errors
This script is idempotent - so when encountering an error, it is safe to re-run it!
The file can be placed on any S3 folder as long as the correct path is specified when running the task.
If there is any data issue while running the script, that row will be skipped and the next rows will be processed.

### AWS credentials issue
If you see an error with AWS credentials (like below), make sure you have credentials set up to run AWS commands on your local machine! Follow the instructions above in [Local Setup](#local-setup).

`"error configuring S3 Backend: error validating provider credentials: error calling sts:GetCallerIdentity: InvalidClientTokenId: The security token included in the request is invalid"`

### Errors updating Cognito
If running locally, make sure your AWS credentials are up-to-date, and are being supplied to the Docker container correctly.


3)
# Uodating Application
This is the ECS task / script for updating applications with the correct taxid that are in UNF flow.


## Running the Script

 usage:update-application [-h] --psd_number PSD_NUMBER
                          [--application_id APPLICATION_ID]
                          [--fineos_absence_id FINEOS_ABSENCE_ID]
                          [--file FILE] [--dry_run | --no-dry_run]
For info on how to run the script, use the `--help` flag.

Use --no-dry_run to commit changes. Default is --dry_run

If you are running this task for multiple application-fineos absence id combinations, use a csv file.

a)  The CSV file header should have 2 columns with the values as shown below:
    ApplicationID,FineosAbsenceID

b) Sample csv

ApplicationID,FineosAbsenceID
'57bad2a9-c5bb-42e9-9e2a-f9b66caa2ba8','NTN-19000-ABS-01'
'69bad2a9-b61e-429a-2afe-c9b66faa28ac','NTN-23590-ABS-01'

c) Upload the file to S3. Ex: s3://massgov-pfml-'env'-agency-transfer/UNF/application_psd_number.csv
d) Copy AWS credentials to the terminal where you will run this task
e) Change directory to pfml from your home directory
    cd pfml
f) Run the command below replacing the environment, name and s3 location of the file you uploaded in step c)
bin/run-ecs-task/run-task.sh 'env' update-application FirstName.LastName update-application --psd_number=7097 --file=s3://massgov-pfml-'env'-agency-transfer/UNF/application_psd_number.csv --dry_run/--no-dry_run

g) If you want to update a single application, send the values as command line args
    bin/run-ecs-task/run-task.sh 'env' update-application FirstName.LastName update-application --psd_number=7097 --application_id '57bad2a9-c5bb-42e9-9e2a-f9b66caa2ba8' --fineos_absence_id 'NTN-5555-ABS-01' --no-dry_run

### Sample Run
On local machine:
`cd pfml/api`
Create a csv file with the information and save it in local_s3/agency-transfer/unf/test.csv
OR pass the application id and fineos absence id as values. Also pass a dummy psd number.
If you are passing csv file, run:
make update-application --psd_number '12345' --file_location 'local_s3/agency-transfer/unf/test.csv' --no-dry_run

make update-application --psd_number '12345' --application_id '57bad2a9-c5bb-42e9-9e2a-f9b66caa2ba8' --fineos_absence_id 'NTN-5555-ABS-01' --no-dry_run

############################################################################################
