import os
from datetime import date, datetime
from pathlib import Path
from typing import Optional

import dotenv
from botocore.client import BaseClient
from pydantic.validators import bool_validator

import massgov
from massgov.pfml.util.aws_ssm import get_secret_with_history


def get_secret_from_env_with_history(aws_ssm: BaseClient, key: str, depth: int) -> list[str] | None:
    """
    Returns the secret along with its previous versions from SSM up to the given `depth` in reverse
    chronological order.

    The secret may alternatively be set directly as an environment variable, and may be expressed as
    a series of comma separated values to mimic history.

    The current version is `depth` `1`.
    """

    if os.getenv(key) is not None:
        return os.environ[key].split(",")[:depth]

    if os.getenv(f"{key}_SSM_PATH") is not None:
        return get_secret_with_history(aws_ssm, os.environ[f"{key}_SSM_PATH"], depth)

    return None


def get_env_bool(key: str, default: Optional[bool] = None) -> Optional[bool]:
    """Read environment variable value as a boolean.

    Supports most commonly used values for boolean-ness:
    - The integers 0 or 1
    - a str which when converted to lower case is one of '0', 'off', 'f',
      'false', 'n', 'no', '1', 'on', 't', 'true', 'y', 'yes'

    Args:
        key: The environment variable name to lookup a value for.
        default: Value to return if key does not exist or value is empty string.

    Raises:
        TypeError: If value can not be parsed to a boolean.
    """
    str_val = os.getenv(key)

    if str_val is None or str_val.strip() == "":
        return default

    return bool_validator(str_val)


def get_env_date(key: str) -> Optional[date]:
    """
    Read environment variable value for a given key as a date.

    Accepts environment variable strings in the format of "2022-01-01".
    It also accepts dates with no leading zeroes, eg "2022-1-1".

    Raises a ValueError if the string does not match this format, including:
    * 20220101
    * Tuesday, April 20th
    """
    str_val = os.getenv(key)

    if str_val is None or str_val.strip() == "":
        return None

    date_time_val = datetime.strptime(str_val, "%Y-%m-%d")
    return date_time_val.date()


def get_app_environment() -> str:
    """Get the application environment setting

    Defaults to "local".
    """
    return os.getenv("ENVIRONMENT", "local")


def is_app_environment_local(value: str | None = None) -> bool:
    """Check if the application environment is set to 'local'

    If not provided a value to check against as an argument, will fetch the
    current application environment setting itself.
    """
    if value is None:
        value = get_app_environment()

    return value == "local"


def get_config_dir_path() -> Path:
    """Path to the shared backend config/ directory"""
    api_root_path = massgov.get_project_root_dir_path()
    return api_root_path / "config"


def get_env_files(config_dir_path: Path | None = None) -> list[str]:
    """Determine .env file paths

    Based on the value of the ``ENVIRONMENT`` env var, will look for an
    ``<ENVIRONMENT>.env`` file to load in configuration directory. If
    ``ENVIRONMENT`` is not set, will use ``local``.

    Additionally will search up directory structure for a ``.env`` file and
    include it if found.

    Only files that exist at time of function call will be included.

    Args:
        config_dir_path: The directory to search for ``{environment}.env``
            files. If not provided, falls back to ``get_config_dir_path()``.
    """
    environment = get_app_environment()
    files = []

    if not config_dir_path:
        config_dir_path = get_config_dir_path()

    # base configuration file for the current environment
    env_file = config_dir_path / f"{environment}.env"

    if env_file.is_file():
        files.append(os.fspath(env_file))

    # personal overrides, listed after the base config as later .env files will
    # override previous values for matching keys in `read_env_files`
    personal_config = dotenv.find_dotenv()

    # `dotenv.find_dotenv` returns an empty string if it doesn't find file, so
    # only add if non-empty
    if personal_config:
        files.append(personal_config)

    return files


def read_env_files(env_files: list[str]) -> dict[str, Optional[str]]:
    """Read provided .env files in order

    Matching keys in later .env files overide previous values. Files that do not
    exist will be skipped.

    Args:
        env_files: List of file paths to load
    """
    values: dict[str, Optional[str]] = dict()

    for f in env_files:
        values |= dotenv.dotenv_values(dotenv_path=f)

    return values


def set_env_vars(vars: dict[str, Optional[str]], override: bool = False) -> None:
    """Update running environment with provided dictionary

    Keys whose value is ``None`` are not set in the environment.

    Args:
        vars: Key-value environment variable dictionary
        override: If existing env vars should be updated
    """
    for k, v in vars.items():
        if v is None:
            continue

        if k in os.environ and not override:
            continue

        os.environ[k] = v


def load_env_files(env_files: Optional[list[str]] = None, override: bool = False) -> None:
    """Update environment with values from provided .env files

    Args:
        env_files: .env files to load, if not provided will call ``get_env_files``
        override: If existing env vars should be updated
    """
    if env_files is None:
        env_files = get_env_files()

    env_vars = read_env_files(env_files)

    set_env_vars(env_vars, override)
