import argparse
import sys
from typing import List

from massgov.pfml.util.bg import background_task
from massgov.pfml.util.uuids.update_uuid_resolver import UpdateUUIDResolver


def _parse_script_args(args: List[str]) -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description="Updates employer and address UUID for records that were manually inserted into db"
    )

    parser.add_argument(
        "--psd_number",
        type=str,
        required=True,
        help="(Required) The PSD ticket number of the request to disable M<PERSON> (eg 'PSD-4695'). \
            If none provided, put 'None Provided'",
    )

    parser.add_argument(
        "--employer_fein",
        type=str,
        required=True,
        help="(Required) The fein of the employer whose uuid needs to be updated. If none provided, put 'None Provided'",
    )

    parser.add_argument(
        "--dry_run",
        type=str,
        default="True",
        help="(Optional Defaults to 'True'. \
            Set this to 'False' to allow the script to commit changes to the PFML db",
    )

    parser.add_argument(
        "--env",
        type=str,
        default="test",
        help="(Optional Defaults to 'test'. \
            Set this to 'test' to allow updates on all tables except two that are only in prod with new employer id",
    )

    return parser.parse_args(args)


@background_task("update-uuid")
def main() -> None:

    args = sys.argv[1:]
    parsed_args = _parse_script_args(args)

    psd_number = parsed_args.psd_number.upper()
    employer_fein = parsed_args.employer_fein

    # dry_run defaults to true unless "false" is explicitly passed in
    dry_run = not (parsed_args.dry_run.lower() == "false")

    env = parsed_args.env

    update_uuid_resolver = UpdateUUIDResolver(
        psd_number,
        employer_fein,
        dry_run,
        env,
    )
    update_uuid_resolver.run()
