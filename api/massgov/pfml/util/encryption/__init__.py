import subprocess
import tempfile
from abc import ABC, abstractmethod
from queue import Queue
from threading import Thread
from typing import Any, BinaryIO, Callable, Iterable, Iterator, Literal

import boto3
import gnupg

import massgov.pfml.util.logging as logging
from massgov.pfml.util.config import get_secret_from_env_with_history

logger = logging.get_logger(__name__)

aws_ssm = boto3.client("ssm", region_name="us-east-1")


OnDataCallback = Callable[[bytes], Literal[False] | Any]


class Crypt(ABC):
    @abstractmethod
    def encrypt(self, bval: bytes, recipient: str) -> bytes: ...

    @abstractmethod
    def decrypt(self, bval: bytes) -> bytes: ...

    @abstractmethod
    def set_on_data(self, on_data: OnDataCallback) -> None:
        """
        Set `on_data` callback.

        `on_data` is called once for each chunk of decrypted/encrypted data, and then once
        with an empty `bytes` object at EOF.

        If return value of `on_data` `is False` (exactly), then `Crypt` does not collect
        the data internally. With any other return value, `Crypt` collects output and
        concatenates it all together into the final `result.data` field.
        """
        ...

    @abstractmethod
    def set_on_end(self, on_end: Callable) -> None:
        """
        Set `on_end` callback.

        `on_end` is called once at the end of successfully decrypting a stream.
        """
        ...

    @abstractmethod
    def decrypt_stream(self, stream: BinaryIO) -> bytes: ...

    @abstractmethod
    def remove_keys(self) -> None: ...

    def _decrypt_stream_iter_chunks(self, stream: BinaryIO) -> Iterator[bytes]:
        """Decrypt the stream and iteratively yield chunks of bytes."""

        # see https://stackoverflow.com/questions/9968592/turn-functions-with-a-callback-into-python-generators/36946209#36946209
        # https://github.com/vsajip/python-gnupg/issues/246

        chunks_queue = Queue[bytes](maxsize=1)

        def on_data(chunk):
            chunks_queue.put(chunk)
            return False  # tell caller not to buffer (see `set_on_data`)

        exc_queue = Queue[Exception](maxsize=1)

        def do_decryption():
            self.set_on_data(on_data)
            try:
                self.decrypt_stream(stream)
            except Exception as exc:
                exc_queue.put(exc)

        thread = Thread(target=do_decryption)
        thread.start()
        while exc_queue.empty() and (next_chunk := chunks_queue.get()):
            yield next_chunk
        thread.join()
        if not exc_queue.empty():
            raise exc_queue.get()

    def decrypt_stream_iter_lines(self, stream: BinaryIO) -> Iterator[bytes]:
        """
        Decrypt the stream and iteratively yield decoded lines with their line endings,
        without buffering the entire contents into memory.
        """
        incomplete_line: bytes | bytearray | None = None

        for chunk in self._decrypt_stream_iter_chunks(stream):
            if incomplete_line:
                chunk = bytearray(incomplete_line) + bytearray(chunk)
                incomplete_line = None

            *complete_lines, last_line = chunk.splitlines(keepends=True)

            if last_line.endswith(b"\n"):
                complete_lines.append(last_line)
            else:
                incomplete_line = last_line

            yield from complete_lines

        if incomplete_line:
            yield bytes(incomplete_line)


class GpgCrypt(Crypt):
    def __init__(
        self,
        gpg_keys_and_passphrases: Iterable[tuple[str, str]],
        homedir: str | None = None,
        on_data: OnDataCallback | None = None,
        on_end: Callable | None = None,
    ):
        """Set a different gnuhome so keys are not picked up by default in
        shared machine environments (i.e. on AWS)."""
        # if encrypting the same gpg directory used to generate a key needs to be passed in
        gpghome = homedir or tempfile.mkdtemp()
        gpg = gnupg.GPG(gnupghome=gpghome)
        gpg.encoding = "utf-8"
        gpg.on_data = on_data

        unique_fingerprints = set()
        first_passphrase = None
        is_first_key = True

        for key, passphrase in gpg_keys_and_passphrases:
            first_passphrase = first_passphrase or passphrase
            import_result = gpg.import_keys(key_data=key, passphrase=passphrase)

            if import_result.count == 0:
                logger.error("Failed to import GPG decryption key")
                raise ValueError(import_result.stderr)

            for fingerprint in set(import_result.fingerprints):
                unique_fingerprints.add(fingerprint)

                # All passphrases are made to be the same to avoid needing to track or use trial and
                # error to find the correct passphrase to decrypt any given data. The nature of how
                # encrypted PFML data is transmitted, streamed, decrypted, and processed makes other
                # approaches challenging.
                if not is_first_key:
                    _change_passphrase(
                        homedir=gpghome,
                        key_fingerprint=fingerprint,
                        new_passphrase=first_passphrase,
                        old_passphrase=passphrase,
                    )

            is_first_key = False

        self.gpg = gpg
        self.passphrase = first_passphrase
        self.gpg_key_fingerprints = list(unique_fingerprints)
        self.on_end = on_end

    def set_on_data(self, on_data: OnDataCallback) -> None:
        self.gpg.on_data = on_data

    def set_on_end(self, on_end):
        self.on_end = on_end

    def decrypt(self, bval: bytes) -> bytes:
        result = self.gpg.decrypt(bval, passphrase=self.passphrase)

        if not result.ok:
            logger.error("Failed to decrypt")
            raise ValueError(result.stderr)

        return result.data

    def decrypt_stream(self, stream: BinaryIO) -> bytes:
        result = self.gpg.decrypt_file(stream, passphrase=self.passphrase)

        if not result.ok:
            logger.error("Failed to decrypt stream")
            raise ValueError(result.stderr)

        if self.on_end:
            self.on_end()

        return result.data

    def encrypt(self, bval: bytes, recipient: str) -> bytes:
        return self.gpg.encrypt(bval, recipient).data

    def remove_keys(self):
        """Delete keys from the system to avoid any possibility of being viewed by
        other applications in a shared machine environment."""
        logger.info("Deleting GPG keys from system...")
        res = self.gpg.delete_keys(
            fingerprints=self.gpg_key_fingerprints, secret=True, passphrase=self.passphrase
        )
        if res.status != "ok":
            logger.warning("Failed to delete keys")
            logger.warning(res.stderr)


class PassthruCrypt(Crypt):
    """A no-op Crypt implementation which passes data through unchanged."""

    on_data: OnDataCallback | None

    def __init__(self):
        self.on_data = None
        self.on_end = None

    def encrypt(self, bval: bytes, _recipient: str) -> bytes:
        return bval

    def set_on_data(self, on_data: OnDataCallback) -> None:
        self.on_data = on_data

    def set_on_end(self, on_end):
        self.on_end = on_end

    def decrypt(self, bval: bytes) -> bytes:
        return bval

    def decrypt_stream(self, stream: BinaryIO) -> bytes:
        # adapted from https://github.com/vsajip/python-gnupg/blob/0.5.0/gnupg.py#L1039-L1059 in GPG._read_data
        #   called by  https://github.com/vsajip/python-gnupg/blob/0.5.0/gnupg.py#L1075 in GPG._collect_output
        #   called by  https://github.com/vsajip/python-gnupg/blob/0.5.0/gnupg.py#L1128 in GPG._handle_io
        #   called by  https://github.com/vsajip/python-gnupg/blob/0.5.0/gnupg.py#L1777 in GPG.decrypt_file
        chunks = []
        while data := stream.read(1000000):
            if (not self.on_data) or (self.on_data(data) is not False):
                chunks.append(data)
        if self.on_data:
            self.on_data(data)

        if self.on_end:
            self.on_end()

        return b"".join(chunks)

    def remove_keys(self):
        return


def decrypter_factory(decrypt_files: bool) -> Crypt:
    # Initialize the file decrypter
    decrypter: Crypt
    if decrypt_files:
        logger.info("Setting up GPG")
        gpg_decryption_keys = (
            get_secret_from_env_with_history(aws_ssm, "GPG_DECRYPTION_KEY", 2) or []
        )

        if len(gpg_decryption_keys) == 0:
            logger.warning("No GPG decryption keys found!")

        gpg_decryption_passphrases = (
            get_secret_from_env_with_history(aws_ssm, "GPG_DECRYPTION_KEY_PASSPHRASE", 2) or []
        )

        if len(gpg_decryption_passphrases) == 0:
            logger.warning("No GPG decryption passphrases found!")

        key_passphrase_pairs = list(zip(gpg_decryption_keys, gpg_decryption_passphrases))
        decrypter = GpgCrypt(key_passphrase_pairs)
    else:
        logger.info("Skipping GPG decrypter setup")
        decrypter = PassthruCrypt()
    return decrypter


def _change_passphrase(
    homedir: str, key_fingerprint: str, old_passphrase: str, new_passphrase: str
) -> None:
    """
    Changes the passphrase of a GPG encryption key, while still retaining the
    ability to decrypt files that were encrypted prior to the change.

    This is performed in a way that avoids exposing the new and old passphrase.

    Warns but does not raise an exception in the case of failure, as this would
    only affect older data that was not processed before a key rotation. The
    current key with the current passphrase will be used for the vast majority
    of data that the system processes.
    """
    process = subprocess.Popen(
        [
            "gpg",
            "--homedir",
            homedir,
            "--batch",
            "--yes",
            "--pinentry-mode",
            "loopback",
            "--command-fd",
            "0",
            "--edit-key",
            key_fingerprint,
            "passwd",
        ],
        stdin=subprocess.PIPE,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
    )

    stdin_data = f"{old_passphrase}\n{new_passphrase}\n{new_passphrase}\nsave\n"
    _, stderr = process.communicate(input=stdin_data.encode())
    exit_code = process.wait()

    if exit_code != 0:
        masked_stderr = (
            str(stderr)
            .replace(old_passphrase, "*OLD PASSPHRASE*")
            .replace(new_passphrase, "*NEW PASSPHRASE*")
        )
        logger.warning(f"Failed to change encryption passphrase: {masked_stderr}")
