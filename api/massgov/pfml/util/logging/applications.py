from collections import defaultdict
from datetime import datetime
from typing import DefaultDict, Dict, Iterable, List, Optional, Union

import massgov.pfml.util.datetime as datetime_util
from massgov.pfml.api.models.applications.requests import ApplicationRequestBody
from massgov.pfml.api.util.deepgetattr import deepgetattr
from massgov.pfml.db.lookup_data.applications import (
    EmployerBenefitType,
    LeaveReason,
    LeaveReasonQualifier,
    OtherIncomeType,
    PreviousLeaveQualifyingReason,
)
from massgov.pfml.db.lookup_data.employees import LeaveRequestDecision
from massgov.pfml.db.models.applications import (
    Application,
    ContinuousLeavePeriod,
    EmployerBenefit,
    IntermittentLeavePeriod,
    OtherIncome,
    PreviousLeave,
    ReducedScheduleLeavePeriod,
)
from massgov.pfml.fineos.models.customer_api import AbsencePeriod


def get_leave_dates(
    leave_periods: Optional[
        Iterable[Union[ContinuousLeavePeriod, IntermittentLeavePeriod, ReducedScheduleLeavePeriod]]
    ],
    leave_type: str,
) -> Dict[str, str]:
    leave_period_dates = {}
    if leave_periods:
        for i, leave_period in enumerate(leave_periods):
            if leave_period.start_date:
                leave_period_dates[f"application.{leave_type}[{i+1}].start_date"] = (
                    leave_period.start_date.isoformat()
                )
            if leave_period.end_date:
                leave_period_dates[f"application.{leave_type}[{i+1}].end_date"] = (
                    leave_period.end_date.isoformat()
                )

    return leave_period_dates


def _get_leave_period_log_attributes(application: Application) -> Dict[str, str]:
    result = {}
    if application.has_continuous_leave_periods:
        result.update(get_leave_dates(application.continuous_leave_periods, "continuous_leave"))
    if application.has_reduced_schedule_leave_periods:
        result.update(
            get_leave_dates(application.reduced_schedule_leave_periods, "reduced_schedule_leave")
        )
    if application.has_intermittent_leave_periods:
        result.update(get_leave_dates(application.intermittent_leave_periods, "intermittent_leave"))
    return result


def get_application_log_attributes(application: Application) -> Dict[str, Optional[str]]:
    attributes_to_log = [
        "application_id",
        "employee_id",
        "employer_id",
        "has_state_id",
        "has_concurrent_employers",
        "has_continuous_leave_periods",
        "has_employer_benefits",
        "has_future_child_date",
        "has_intermittent_leave_periods",
        "has_mailing_address",
        "has_other_incomes",
        "has_previous_leaves",
        "has_reduced_schedule_leave_periods",
        "has_submitted_payment_preference",
        "hours_worked_per_week",
        "hours_worked_per_week_all_employers",
        "industry_sector",
        "is_withholding_tax",
        "organization_unit_id",
        "language_id",
        "ethnicity",
        "race",
        "race_custom",
        "pregnant_or_recent_birth",
        "created_at",
        "updated_at",
        "completed_time",
        "submitted_time",
        "ready_for_review_time",
        "split_from_application_id",
        "additional_user_not_found_info.employer_name",
        "additional_user_not_found_info.date_of_hire",
        "additional_user_not_found_info.currently_employed",
        "additional_user_not_found_info.date_of_separation",
        "additional_user_not_found_info.recently_acquired_or_merged",
        "additional_user_not_found_info.submitted_time",
        "employer_notified",
        "fields_to_use_from_user_profile",
        "mmg_idv_status_id",
    ]

    timestamp_attributes_to_log = [
        "created_at",
        "updated_at",
        "completed_time",
        "submitted_time",
        "ready_for_review_time",
        "additional_user_not_found_info.submitted_time",
    ]

    result = {}
    for name in attributes_to_log:
        value = deepgetattr(application, name)

        if isinstance(value, list):
            value = sorted(value)

        result[f"application.{name}"] = str(value) if value is not None else None

    if result["application.has_previous_leaves"] is None:
        result["application.has_previous_leaves"] = str(application.has_previous_leaves)

    for name in timestamp_attributes_to_log:
        dt_value: Optional[datetime] = deepgetattr(application, name)
        result[f"application.{name}.timestamp"] = (
            str(dt_value.timestamp()) if dt_value is not None else None
        )

    # Use a different attribute name for fineos_absence_id to avoid using vendor specific names
    result["application.absence_case_id"] = (
        application.claim.fineos_absence_id if application.claim else None
    )
    # Makes it easier to track logs related to specific ID's, as our standard for tracking this ID
    # is 'absence_case_id' and not 'application.absence_case_id'
    result["absence_case_id"] = application.claim.fineos_absence_id if application.claim else None

    # leave_reason and leave_reason_qualifier are objects, so get the underlying string description
    result["application.leave_reason"] = (
        application.leave_reason.leave_reason_description if application.leave_reason else None
    )
    result["application.leave_reason_qualifier"] = (
        application.leave_reason_qualifier.leave_reason_qualifier_description
        if application.leave_reason_qualifier
        else None
    )

    # ethnicity and race are objects, so get the underlying string description
    result["application.ethnicity"] = (
        application.ethnicity.ethnicity_description if application.ethnicity else None
    )
    result["application.race"] = application.race.race_description if application.race else None

    if application.claim is not None and application.claim.employer is not None:
        result["employer.uses_organization_units"] = str(
            application.claim.employer.uses_organization_units
        )

    # for caring leave, log relationship type
    if (
        application.leave_reason_id == LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id
        and application.caring_leave_metadata
        and application.caring_leave_metadata.relationship_to_caregiver
    ):
        result["application.leave_details.caring_leave_metadata.relationship_to_caregiver"] = (
            application.caring_leave_metadata.relationship_to_caregiver.relationship_to_caregiver_description
        )

    result["work_pattern.work_pattern_type"] = (
        application.work_pattern.work_pattern_type.work_pattern_type_description
        if application.work_pattern and application.work_pattern.work_pattern_type
        else None
    )

    if application.industry_sector:
        result["application.industry_sector"] = application.industry_sector.description

    # add leave start_date and end_date for each type of leave period
    result.update(_get_leave_period_log_attributes(application))

    result.update(_get_previous_leaves_log_attributes(application))
    result.update(_get_employer_benefits_log_attributes(application.employer_benefits))
    result.update(_get_other_incomes_log_attributes(application.other_incomes))
    result.update(_get_parental_bonding_attributes(application))

    return result


def _get_parental_bonding_attributes(application: Application) -> Dict[str, str]:
    bonding_attributes = {}
    if _can_get_parental_bonding_attributes(application):
        submit_date = (
            datetime.date(application.submitted_time)
            if application.submitted_time is not None
            else datetime.date(datetime.today())
        )
        bonding_attributes.update(
            {
                "application.bonding_leave_days_before_birth": str(
                    (application.child_birth_date - submit_date).days
                )
            }
        )
    return bonding_attributes


def _can_get_parental_bonding_attributes(application: Application) -> bool:
    if application.leave_reason_id != LeaveReason.CHILD_BONDING.leave_reason_id:
        return False
    if (
        application.leave_reason_qualifier_id
        != LeaveReasonQualifier.NEWBORN.leave_reason_qualifier_id
    ):
        return False
    if application.child_birth_date is None:
        return False
    return True


def get_app_progress_attributes(
    existing_application: Application, application_updates: ApplicationRequestBody
) -> Dict[str, Optional[str]]:
    """Get log attributes for application progress, eg starting or completing a section"""
    attributes: Dict[str, Optional[str]] = {}

    has_started_olb_section = (
        existing_application.has_previous_leaves is None
        and application_updates.has_previous_leaves is not None
    )
    if has_started_olb_section:
        # log when the user first starts the OLB section
        attributes["olb_section_start_time"] = str(datetime_util.utcnow())

    return attributes


def _get_previous_leaves_log_attributes(application: Application) -> Dict[str, str]:
    log_attr = {}

    previous_leaves_any_reason_attr = _get_previous_leaves_log_attributes_helper(
        application.previous_leaves
    )
    log_attr.update(previous_leaves_any_reason_attr)

    # log the total number of previous leaves
    num_previous_leaves = len(application.previous_leaves)
    log_attr["application.num_previous_leaves"] = str(num_previous_leaves)

    return log_attr


def _get_previous_leaves_log_attributes_helper(leaves: Iterable[PreviousLeave]) -> Dict[str, str]:
    result = {}

    reason_values = [
        PreviousLeaveQualifyingReason.PREGNANCY_MATERNITY.previous_leave_qualifying_reason_description,
        PreviousLeaveQualifyingReason.PREGNANCY_HEALTH_CONDITION.previous_leave_qualifying_reason_description,
        PreviousLeaveQualifyingReason.AN_ILLNESS_OR_INJURY.previous_leave_qualifying_reason_description,
        PreviousLeaveQualifyingReason.AN_ILLNESS_OR_INJURY_HOSPITAL.previous_leave_qualifying_reason_description,
        PreviousLeaveQualifyingReason.CARE_FOR_A_FAMILY_MEMBER.previous_leave_qualifying_reason_description,
        PreviousLeaveQualifyingReason.CHILD_BONDING.previous_leave_qualifying_reason_description,
        PreviousLeaveQualifyingReason.MILITARY_CAREGIVER.previous_leave_qualifying_reason_description,
        PreviousLeaveQualifyingReason.MILITARY_EXIGENCY_FAMILY.previous_leave_qualifying_reason_description,
    ]

    reason_counts: DefaultDict[str, int] = defaultdict(int)
    continuous_count = 0

    for leave in leaves:
        if leave.leave_reason:
            reason = leave.leave_reason.previous_leave_qualifying_reason_description
            assert reason
            reason_counts[reason] += 1
        if leave.is_continuous:
            continuous_count += 1
            result["application.num_previous_leave_is_continuous"] = str(continuous_count)

    for reason in reason_values:
        assert reason
        count = reason_counts[reason]
        result[f"application.num_previous_leave_reasons.{reason}"] = str(count)

    return result


def _get_employer_benefits_log_attributes(benefits: Iterable[EmployerBenefit]) -> Dict[str, str]:
    result = {
        "application.num_employer_benefits": str(len(list(benefits))),
        "application.has_full_wage_replacement": "False",
    }

    type_values = [
        EmployerBenefitType.ACCRUED_PAID_LEAVE.employer_benefit_type_description,
        EmployerBenefitType.SHORT_TERM_DISABILITY.employer_benefit_type_description,
        EmployerBenefitType.PERMANENT_DISABILITY_INSURANCE.employer_benefit_type_description,
        EmployerBenefitType.FAMILY_OR_MEDICAL_LEAVE_INSURANCE.employer_benefit_type_description,
        EmployerBenefitType.PAID_TIME_OFF.employer_benefit_type_description,
    ]

    type_counts: DefaultDict[str, int] = defaultdict(int)

    full_wage_replacements = []

    for benefit in benefits:
        if benefit.benefit_type:
            benefit_type = benefit.benefit_type.employer_benefit_type_description
            assert benefit_type
            type_counts[benefit_type] += 1

        full_wage_replacements.append(benefit.is_full_salary_continuous)

    # The application is considered full wage replacement for NR purposes if at least
    # one employer benefit is full wage replacement
    result["application.has_full_wage_replacement"] = str(any(full_wage_replacements))

    for type_value in type_values:
        assert type_value
        count = type_counts[type_value]
        result[f"application.num_employer_benefit_types.{type_value}"] = str(count)

    return result


def _get_other_incomes_log_attributes(incomes: Iterable[OtherIncome]) -> Dict[str, str]:
    result = {"application.num_other_incomes": str(len(list(incomes)))}

    type_values = [
        OtherIncomeType.WORKERS_COMP.other_income_type_description,
        OtherIncomeType.UNEMPLOYMENT.other_income_type_description,
        OtherIncomeType.SSDI.other_income_type_description,
        OtherIncomeType.RETIREMENT_DISABILITY.other_income_type_description,
        OtherIncomeType.JONES_ACT.other_income_type_description,
        OtherIncomeType.RAILROAD_RETIREMENT.other_income_type_description,
        OtherIncomeType.OTHER_EMPLOYER.other_income_type_description,
    ]

    income_counts: DefaultDict[str, int] = defaultdict(int)

    for income in incomes:
        if income.income_type:
            income_type = income.income_type.other_income_type_description
            assert income_type
            income_counts[income_type] += 1

    for type_value in type_values:
        assert type_value
        count = income_counts[type_value]
        result[f"application.num_other_income_types.{type_value}"] = str(count)

    return result


def get_absence_period_log_attributes(
    absence_periods: List[AbsencePeriod], imported_period: Optional[AbsencePeriod]
) -> Dict[str, str]:
    result = {"num_absence_periods": str(len(absence_periods))}
    num_pending = 0
    imported_id = imported_period.id if imported_period else ""
    for index, absence_period in enumerate(absence_periods):
        imported = "imported_" if absence_period.id == imported_id else ""
        result[f"{imported}absence_period_{index}_request_status"] = str(
            absence_period.requestStatus
        )
        result[f"{imported}absence_period_{index}_reason"] = str(absence_period.reason)
        # Count the number of pending leave request decision statuses.
        if (
            absence_period.requestStatus
            == LeaveRequestDecision.PENDING.leave_request_decision_description
        ):
            num_pending += 1
    result["num_pending_leave_request_decision_status"] = str(num_pending)

    return result
