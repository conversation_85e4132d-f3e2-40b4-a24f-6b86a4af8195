#
# Utilities for handling files.
#

import csv
import io
import os
import pathlib
import re
import shutil
import tempfile
import time
from typing import Dict, Iterable, List, Optional, Sequence
from urllib.parse import urlparse

import boto3
import botocore
import ebcdic  # noqa: F401
import paramiko
import smart_open
from botocore.config import Config
from sqlalchemy.exc import SQLAlchemyError
from tenacity import retry
from tenacity.stop import stop_after_attempt
from tenacity.wait import wait_fixed

import massgov.pfml.util.aws.sts as aws_sts
import massgov.pfml.util.logging as logging
from massgov.pfml import db
from massgov.pfml.db.models.reference_file.reference_file import ReferenceFile

logger = logging.get_logger(__name__)

MAX_NUMBER_OF_CHECKS = 3
RETRY_WAIT_TIME = 60 * 5
EBCDIC_ENCODING = "cp1140"  # See https://pypi.org/project/ebcdic/ for further details
FINEOS_BUCKET_PREFIX = "fin-som"


def is_valid_bucket_name(bucket_name: str) -> bool:
    # AWS S3 bucket naming rules:
    # - Must be between 3 and 63 characters long.
    # - Can contain lowercase letters, numbers, dots (.), and hyphens (-).
    # - Must start and end with a lowercase letter or number.
    # - Should not be formatted as an IP address.
    # - Labels separated by dots cannot start with a hyphen or end with a hyphen.
    # - Cannot contain two, adjacent periods, or a period adjacent to a hyphen.
    pattern = r"^(?!.*\.\.)(?!.*\.-)(?!.*-\.)[a-z0-9][a-z0-9.-]{1,61}[a-z0-9]$"
    return re.match(pattern, bucket_name) is not None


def is_s3_path(path):
    return path.startswith("s3://")


def is_sftp_path(path):
    return path.startswith("sftp://")


def get_s3_bucket(path):
    return urlparse(path).hostname


def get_s3_file_key(path):
    return urlparse(path).path[1:]


def get_file_name(path: str) -> str:
    # TODO when the DOR importer is updated to use the file system in upcoming tickets,
    # this function should replace get_file_name() that exists there
    return os.path.basename(path)


def get_directory(path: str) -> str:
    # This handles getting the "directory" of any path (local or S3)
    # Grab everything in the path except the last X characters
    # where X is the length of the file name
    # This preserves the trailing /
    return path[: -len(get_file_name(path))]


def split_s3_url(path):
    parts = urlparse(path)
    bucket_name = parts.netloc
    prefix = parts.path.lstrip("/")
    return (bucket_name, prefix)


# "sftp://<EMAIL>:2222/" -> ("test_user", "example.com", 2222)
def split_sftp_url(path):
    parts = urlparse(path)
    return (parts.username or "", parts.hostname, parts.port or 22)


def get_s3_client(
    bucket_name: str, boto_session: Optional[boto3.Session] = None
) -> botocore.client.BaseClient:
    """Returns the appropriate S3 client for a given bucket"""
    if boto_session:
        return boto_session.client("s3")
    elif bucket_name.startswith(FINEOS_BUCKET_PREFIX):
        # This should get passed in from the method but getting it
        # directly from the environment due to time constraints.
        fineos_boto_session = aws_sts.assume_session(
            role_arn=os.environ["FINEOS_AWS_IAM_ROLE_ARN"],
            external_id=os.environ["FINEOS_AWS_IAM_ROLE_EXTERNAL_ID"],
            role_session_name="payments_copy_file",
            region_name="us-east-1",
        )
        return fineos_boto_session.client("s3")
    else:
        return boto3.client("s3")


def list_folders(folder_path: str, boto_session: Optional[boto3.Session] = None) -> List[str]:
    """List immediate subfolders under folder path.
    Returns a list of subfolders names.
    Args:
        folder_path: path to a folder.
            If S3 this may be a folder under the bucket or just a path to the bucket.
        boto_session: Boto session object to use for S3 access. Only necessary
            if needing to access an S3 bucket with assumed credentials (e.g.,
            cross-account bucket access).
    """
    if is_s3_path(folder_path):
        bucket_name, prefix = split_s3_url(folder_path)

        if prefix and not prefix.endswith("/"):
            prefix = prefix + "/"

        s3 = get_s3_client(bucket_name, boto_session)
        paginator = s3.get_paginator("list_objects_v2")
        pages = paginator.paginate(Bucket=bucket_name, Prefix=prefix, Delimiter="/")

        subfolders = []
        for page in pages:
            folder_details = page.get("CommonPrefixes")
            if folder_details:
                for folder_detail in folder_details:
                    subfolder = folder_detail["Prefix"]
                    start_index = len(prefix)
                    subfolders.append(subfolder[start_index:].strip("/"))

        return subfolders

    # Handle file system
    return [f.name for f in os.scandir(folder_path) if f.is_dir()]


def list_files(
    path: str, recursive: bool = False, boto_session: Optional[boto3.Session] = None
) -> List[str]:
    """List the immediate files under path.

    Args:
        path: Supports s3:// and local paths.
        recursive: If set to True will recursively list all relative key paths under the prefix.
        boto_session: Boto session object to use for S3 access. Only necessary
            if needing to access an S3 bucket with assumed credentials (e.g.,
            cross-account bucket access).
    """
    if is_s3_path(path):
        return list_files_s3(path, recursive, boto_session)

    return list_files_local(path, recursive)


def list_files_s3(
    path: str, recursive: bool = False, boto_session: Optional[boto3.Session] = None
) -> List[str]:
    """List files in the given S3 path."""
    bucket_name, prefix = split_s3_url(path)

    # in order for s3.list_objects to only list the immediate "files" under
    # the given path, the prefix should end in the path delimiter
    if prefix and not prefix.endswith("/"):
        prefix = prefix + "/"

    s3 = get_s3_client(bucket_name, boto_session)

    # When the delimiter is provided, s3 knows to stop listing keys that contain it (starting after the prefix).
    # https://docs.aws.amazon.com/AmazonS3/latest/dev/ListingKeysHierarchy.html
    delimiter = "" if recursive else "/"

    paginator = s3.get_paginator("list_objects_v2")
    pages = paginator.paginate(Bucket=bucket_name, Prefix=prefix, Delimiter=delimiter)

    file_paths = []
    for page in pages:
        object_contents = page.get("Contents")

        if object_contents:
            for object in object_contents:
                if recursive:
                    key = object["Key"]
                    start_index = key.index(prefix) + len(prefix)
                    file_paths.append(key[start_index:])
                else:
                    file_paths.append(get_file_name(object["Key"]))

    return file_paths


def list_files_local(path: str, recursive: bool = False) -> List[str]:
    """List files in the given filesystem path."""
    # Same behaviour as S3: only include files, not directories
    if recursive:
        return sorted(
            str(p.relative_to(path)) for p in pathlib.Path(path).rglob("*") if p.is_file()
        )
    else:
        return sorted(str(p.relative_to(path)) for p in pathlib.Path(path).glob("*") if p.is_file())


def list_files_without_folder(
    path: str, recursive: bool = False, boto_session: Optional[boto3.Session] = None
) -> List[str]:
    files = list_files(path, recursive=recursive, boto_session=boto_session)
    return list(filter(lambda file: file != "", [get_file_name(path) for path in files]))


# Lists all files and directories in the path. Keys in the returned dict are equivalent to a
# simple bash `ls`. Values of the returned dict are the relative path from the current path to the
# contents of that directory.
# Example response: {
#     "a-stellar-testfile.txt": ["a-stellar-testfile.txt"],
#     "a-subfolder": [
#         "a-subfolder/my-file.txt",
#         "a-subfolder/second-file.txt"
#     ]
# }
def list_s3_files_and_directories_by_level(
    path: str, boto_session: Optional[boto3.Session] = None
) -> Dict[str, List[str]]:
    if not is_s3_path(path):
        return {}

    # Add an empty string at the end of the prefix so it always ends in "/".
    bucket_name, prefix = split_s3_url(path)
    prefix = os.path.join(prefix, "")
    s3 = get_s3_client(bucket_name, boto_session)

    contents_by_level: Dict[str, List[str]] = {}
    logger.info(f"retrieving object list from s3, bucket: {bucket_name}, prefix: {prefix}")
    contents = s3.list_objects_v2(Bucket=bucket_name, Prefix=prefix).get("Contents")
    if contents is None:
        return contents_by_level

    for object in contents:
        full_key = object["Key"]

        # Exclude the directory itself from the list of files and directories within the directory.
        if full_key == prefix:
            continue

        relative_path = full_key[len(prefix) :]
        local_component = relative_path.split("/")[0]

        if contents_by_level.get(local_component) is None:
            contents_by_level[local_component] = []

        # Exclude the subdirectories from the list of files and directories returned.
        if not relative_path[-1:] == "/":
            contents_by_level[local_component].append(relative_path)

    return contents_by_level


def copy_file(source, destination, direct_copy=False):
    logger.info(f"Copying file from {source} to {destination}")
    is_source_s3 = is_s3_path(source)
    is_dest_s3 = is_s3_path(destination)

    # This isn't a download or upload method
    # Don't allow "copying" between mismatched locations
    if is_source_s3 != is_dest_s3:
        raise Exception("Cannot download/upload between disk and S3 using this method")

    if is_source_s3:
        start_time = time.monotonic()
        source_bucket, source_path = split_s3_url(source)
        dest_bucket, dest_path = split_s3_url(destination)

        logger.info(
            "Copying file from S3",
            extra={
                "direct_copy": direct_copy,
                "source_bucket": source_bucket,
                "source_path": source_path,
                "dest_bucket": dest_bucket,
                "dest_path": dest_path,
            },
        )

        if direct_copy:
            s3 = boto3.client("s3")
            copy_source = {"Bucket": source_bucket, "Key": source_path}
            # Keeping these log messages for now as we switch other copy_file calls to use direct_copy=True

            response = s3.copy_object(CopySource=copy_source, Bucket=dest_bucket, Key=dest_path)
            end_time = time.monotonic()
            logger.info(
                "Direct copy operation completed",
                extra={"response": response},
            )

        else:

            file_descriptor, tempfile_path = tempfile.mkstemp()

            try:
                # dest_s3 = source_boto3_session.client("s3")
                source_s3 = get_s3_client(source_bucket)
                source_s3.download_file(source_bucket, source_path, tempfile_path)
                file_metadata = source_s3.head_object(Bucket=source_bucket, Key=source_path)

                # dest_s3 = dest_boto3_session.client("s3")
                dest_s3 = get_s3_client(dest_bucket)
                dest_s3.upload_file(
                    tempfile_path,
                    dest_bucket,
                    dest_path,
                    ExtraArgs={
                        "ContentType": file_metadata["ContentType"],
                        "Metadata": file_metadata["Metadata"],
                    },
                )
            finally:
                os.close(file_descriptor)
                os.remove(tempfile_path)

        end_time = time.monotonic()
        logger.info(
            "Copying file from S3 completed",
            extra={
                "direct_copy": direct_copy,
                "source_bucket": source_bucket,
                "source_path": source_path,
                "dest_bucket": dest_bucket,
                "dest_path": dest_path,
                "duration": end_time - start_time,
            },
        )

    else:
        os.makedirs(os.path.dirname(destination), exist_ok=True)
        shutil.copy2(source, destination)


def delete_file(path):
    if is_s3_path(path):
        bucket, s3_path = split_s3_url(path)

        s3 = boto3.client("s3")
        s3.delete_object(Bucket=bucket, Key=s3_path)
    else:
        os.remove(path)


def rename_file(source, destination):
    is_source_s3 = is_s3_path(source)
    is_dest_s3 = is_s3_path(destination)

    # This isn't a download or upload method
    # Don't allow "copying" between mismatched locations
    if is_source_s3 != is_dest_s3:
        raise Exception("Cannot download/upload between disk and S3 using this method")

    if is_source_s3:
        # S3 doesn't have any actual rename process, need to copy and delete the old
        copy_file(source, destination)
        delete_file(source)
    else:
        # This will create any missing intermediary directories
        os.renames(source, destination)


def download_from_s3(source, destination):
    is_source_s3 = is_s3_path(source)
    is_dest_s3 = is_s3_path(destination)

    if not is_source_s3 or is_dest_s3:
        raise Exception("Source must be an S3 URI and destination must not be")

    bucket, path = split_s3_url(source)

    s3 = get_s3_client(bucket)
    s3.download_file(bucket, path, destination)


def upload_to_s3(
    source: str, destination: str, custom_boto3_session: Optional[boto3.Session] = None
) -> None:
    """
    Uploads a local file to an S3 bucket or copies a file from one S3 location to another.

    :param source: The source file path or S3 URI.
    :param destination: The destination S3 URI.
    :param custom_boto3_session: An optional boto3.Session object.
    :raises ValueError: If the destination is not an S3 URI.
    :raises RuntimeError: If the source or destination bucket names are invalid, or if the source file does not exist.
    """
    logger.info("Uploading from %s to %s", source, destination)

    is_source_s3 = is_s3_path(source)
    is_dest_s3 = is_s3_path(destination)

    # Validate destination
    if not is_dest_s3:
        logger.error("Destination is an invalid S3 URI.", extra={"destination": destination})
        raise ValueError("Destination must be an S3 URI")

    dest_bucket_name, dest_prefix = split_s3_url(destination)
    if not is_valid_bucket_name(dest_bucket_name):
        logger.error(
            "Destination S3 bucket name is invalid.", extra={"destination bucket": dest_bucket_name}
        )
        raise RuntimeError(
            f"Invalid bucket name found in upload_to_s3, destination bucket: {dest_bucket_name}"
        )

    if is_source_s3:
        # Source is an S3 URI, perform S3-to-S3 copy
        source_bucket_name, source_prefix = split_s3_url(source)
        if not is_valid_bucket_name(source_bucket_name):
            logger.error(
                "Source S3 bucket name is invalid.", extra={"source S3 bucket": source_bucket_name}
            )
            raise RuntimeError(
                f"Invalid bucket name found in upload_to_s3, source bucket: {source_bucket_name}"
            )
        copy_file(source, destination)
    else:
        # Source is a local file, upload to S3
        if not os.path.isfile(source):
            logger.error(
                "Local source file is invalid.", extra={"local source file": get_file_name(source)}
            )
            raise RuntimeError(
                f"Local source file found in upload_to_s3 is invalid or does not exist: {source}"
            )
        else:
            s3 = get_s3_client(dest_bucket_name, custom_boto3_session)
            s3.upload_file(source, dest_bucket_name, dest_prefix)

    logger.info("Upload operation completed")


def open_stream(path, mode="r", encoding=None):
    if is_s3_path(path):
        so_config = Config(
            max_pool_connections=10,
            connect_timeout=60,
            read_timeout=60,
            retries={"max_attempts": 10},
        )
        so_transport_params = {"client_kwargs": {"config": so_config}}

        return smart_open.open(path, mode, transport_params=so_transport_params, encoding=encoding)
    else:
        return smart_open.open(path, mode, encoding=encoding)


def read_file(path, mode="r", encoding=None):
    return smart_open.open(path, mode, encoding=encoding).read()


def write_file(path, mode="w", encoding=None, use_s3_multipart_upload=True):
    if not is_s3_path(path):
        os.makedirs(os.path.dirname(path), exist_ok=True)
    config = botocore.client.Config(retries={"max_attempts": 10, "mode": "standard"})
    params = {"client_kwargs": {"config": config}, "multipart_upload": use_s3_multipart_upload}
    return smart_open.open(path, mode, encoding=encoding, transport_params=params)


def read_file_lines(path, mode="r", encoding=None):
    stream = smart_open.open(path, mode, encoding=encoding)
    return map(lambda line: line.rstrip(), stream)


def log_attempt_number(retry_state):
    logger.warning(f"Retry {retry_state.attempt_number}: Attempting to connect to MoveIT client")


@retry(
    reraise=True,
    wait=wait_fixed(RETRY_WAIT_TIME),
    stop=stop_after_attempt(MAX_NUMBER_OF_CHECKS),
    after=log_attempt_number,
)
def get_sftp_client(uri: str, ssh_key_password: Optional[str], ssh_key: str) -> paramiko.SFTPClient:
    if not is_sftp_path(uri):
        raise ValueError("uri must be an SFTP URI")

    # Paramiko expects to receive the private key as a file-like object so we write the string
    # to a StringIO object.
    # https://docs.paramiko.org/en/stable/api/keys.html#paramiko.pkey.PKey.from_private_key
    ssh_key_fileobj = io.StringIO(ssh_key)
    pkey = paramiko.rsakey.RSAKey.from_private_key(ssh_key_fileobj, password=ssh_key_password)
    logger.info(f"Connecting to SFTP endpoint {uri}")

    user, host, port = split_sftp_url(uri)
    # As a part of update to paramiko v2.9.0, the RSA-SHA2 algorithms were
    # disabled as our primary SFTP server, MOVEit, supports neither RSA2 nor the
    # `server-sig-algs` protocol extension to negotiate the algorithm to use, so
    # need to basically hard-code it to stay with `ssh-rsa`.
    #
    # Could likely hack in support RSA2 stuff, while still falling back to what
    # MOVEit supports if needed, see: https://lwd.atlassian.net/browse/PFMLPB-9127
    t = paramiko.Transport(
        (host, port), disabled_algorithms={"pubkeys": ["rsa-sha2-256", "rsa-sha2-512"]}
    )
    t.connect(username=user, pkey=pkey)

    client = paramiko.SFTPClient.from_transport(t)
    if not client:
        raise RuntimeError("STFP client unavailable")

    return client


def copy_file_from_s3_to_sftp(source: str, dest: str, sftp: paramiko.SFTPClient) -> None:
    if not is_s3_path(source):
        raise ValueError("source must be an S3 URI")

    # Download file from S3 to a tempfile.
    _handle, tempfile_path = tempfile.mkstemp()
    logger.info(f"Downloading files from {source} to a temporary local directory.")
    download_from_s3(source, tempfile_path)

    # Copy the file from local tempfile to destination.
    logger.info(f"Uploading files to SFTP at {dest}")
    # confirm=False is added as otherwise SFTP will attempt to read
    # the file it just wrote and fail, and the file might not be available yet.
    sftp.put(tempfile_path, dest, confirm=False)


# Copy the file through a local tempfile instead of streaming from SFTP directly to S3 to reduce the
# number of network connections open at any given time (1 at a time instead of 2).
def copy_file_from_sftp_to_s3(source: str, dest: str, sftp: paramiko.SFTPClient) -> None:

    # Download file from SFTP to a tempfile.
    _handle, tempfile_path = tempfile.mkstemp()
    sftp.get(source, tempfile_path)

    upload_file(tempfile_path, dest)


def upload_file(src: str, dest: str, custom_boto3_session: Optional[boto3.Session] = None) -> None:
    if is_s3_path(dest):
        upload_to_s3(src, dest, custom_boto3_session)
    else:
        copy_file(src, dest)


def remove_if_exists(path: str) -> None:
    try:
        os.remove(path)
    except FileNotFoundError:
        pass


def create_csv_from_list(
    data: Iterable[Dict],
    fieldnames: Sequence[str],
    file_name: str,
    folder_path: Optional[str] = None,
) -> pathlib.Path:
    if not folder_path:
        directory = tempfile.mkdtemp()
        csv_filepath = os.path.join(directory, f"{file_name}.csv")
    else:
        csv_filepath = os.path.join(folder_path, f"{file_name}.csv")

    with write_file(csv_filepath) as csv_file:
        writer = csv.DictWriter(csv_file, fieldnames=fieldnames, extrasaction="ignore")

        writer.writeheader()
        for d in data:
            writer.writerow(d)

    return pathlib.Path(csv_filepath)


def move_reference_file(
    db_session: db.Session, ref_file: ReferenceFile, src_dir: str, dest_dir: str
) -> None:
    """Moves a ReferenceFile

    Renames the actual S3 file (copies and deletes) and updates the reference_file.file_location
    """
    if ref_file.file_location is None:
        raise ValueError(f"ReferenceFile {ref_file.reference_file_id} is missing a file_location")

    old_location = ref_file.file_location

    # Verify that the file_locations contains the src directory. Ex: Constants.S3_INBOUND_RECEIVED_DIR
    # This will raise a ValueError if the src directory is not found
    old_location.rindex(src_dir)

    # Replace src directory with the dest directory. Ex: Constants.S3_INBOUND_ERROR_DIR
    new_location = old_location.replace(src_dir, dest_dir)

    # Rename the file
    # This may raise S3-related errors
    rename_file(old_location, new_location)

    # Update reference_file.file_location
    try:
        ref_file.file_location = new_location
        db_session.add(ref_file)
        db_session.commit()
        logger.info(
            "Successfully moved Reference File",
            extra={
                "file_location": ref_file.file_location,
                "src_dir": src_dir,
                "dest_dir": dest_dir,
            },
        )
    except SQLAlchemyError:
        # Rollback the database transaction
        db_session.rollback()
        # Rollback the file move
        rename_file(new_location, old_location)
        # Log the exception
        logger.exception(
            "Unable to move ReferenceFile",
            extra={
                "file_location": ref_file.file_location,
                "src_dir": src_dir,
                "dest_dir": dest_dir,
            },
        )
        raise
