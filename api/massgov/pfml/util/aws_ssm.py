from botocore.client import BaseClient


def get_secret_with_history(client: BaseClient, key: str, depth: int = 1) -> list[str]:
    """
    Returns the secret along with its previous versions up to the given `depth` in reverse
    chronological order.

    The current version is `depth` `1`.
    """

    res = client.get_parameter_history(Name=key, WithDecryption=True)
    sorted_parameters = sorted(
        res["Parameters"], key=lambda parameter: parameter["Version"], reverse=True
    )
    return [parameter["Value"] for parameter in sorted_parameters[:depth]]


def put_secret(client, key, value, overwrite=True, type="SecureString", data_type="text"):
    client.put_parameter(Name=key, Value=value, Type=type, Overwrite=overwrite, DataType=data_type)
