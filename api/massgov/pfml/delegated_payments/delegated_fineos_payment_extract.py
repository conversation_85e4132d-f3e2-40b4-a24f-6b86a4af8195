import enum
import uuid
from datetime import date, datetime
from decimal import Decimal
from typing import Any, Callable, Dict, List, Optional, Tuple, cast

from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import joinedload

import massgov.pfml.api.util.state_log_util as state_log_util
import massgov.pfml.delegated_payments.delegated_payments_util as payments_util
import massgov.pfml.util.logging as logging
from massgov.pfml.db.lookup_data.employees import (
    AddressType,
    BankAccountType,
    PaymentMethod,
    PaymentRelevantParty,
    PaymentTransactionType,
    PrenoteState,
)
from massgov.pfml.db.lookup_data.geo import GeoState
from massgov.pfml.db.lookup_data.payments import (
    ACTIVE_WRITEBACK_RECORD_STATUS,
    FineosWritebackTransactionStatus,
    PaymentEventType,
)
from massgov.pfml.db.lookup_data.reference_file_type import ReferenceFileType
from massgov.pfml.db.lookup_data.state import State
from massgov.pfml.db.models.employees import (
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    Employee,
    EmployeeAddress,
    EmployeePubEftPair,
    Employer,
    ExperianAddressPair,
    LkPaymentRelevantParty,
    LkPaymentTransactionType,
    Overpayment,
    OverpaymentDetails,
    OverpaymentRepayment,
    Payment,
    PaymentDetails,
    PubEft,
    TaxIdentifier,
)
from massgov.pfml.db.models.payments import (
    FineosExtractVbiRequestedAbsence,
    FineosExtractVbiTaskReportDeltaSom,
    FineosExtractVpei,
    FineosExtractVpeiClaimDetails,
    FineosExtractVpeiPaymentDetails,
    FineosExtractVpeiPaymentLine,
    LkFineosWritebackTransactionStatus,
    LkPaymentEventType,
    PaymentLine,
)
from massgov.pfml.db.models.reference_file.overpayment_reference_file import (
    OverpaymentReferenceFile,
)
from massgov.pfml.db.models.reference_file.overpayment_repayment_reference_file import (
    OverpaymentRepaymentReferenceFile,
)
from massgov.pfml.db.models.reference_file.payment_reference_file import PaymentReferenceFile
from massgov.pfml.db.models.reference_file.reference_file import ReferenceFile
from massgov.pfml.delegated_payments.delegated_payments_util import (
    is_prepaid_impact_payments_enabled,
)
from massgov.pfml.delegated_payments.extracts.payment_extract_writeback_util import (
    get_writeback_for_validation_reasons,
)
from massgov.pfml.delegated_payments.issue_resolution.scenarios import (
    FAILED_AUTOMATED_VALIDATION_RESOLUTION,
    get_issue_resolutions_for_validation_reasons,
)
from massgov.pfml.delegated_payments.util.fineos_writeback_util import (
    stage_payment_fineos_writeback,
)
from massgov.pfml.fineos.tasks import get_open_tasks_from_delta
from massgov.pfml.services.payments.issue_resolution import create_issue_resolution
from massgov.pfml.util.batch.step import Step
from massgov.pfml.util.converters.str_to_numeric import str_to_int
from massgov.pfml.util.datetime import (
    datetime_str_to_date,
    datetime_to_isoformat,
    get_now_us_eastern,
)

logger = logging.get_logger(__name__)

# waiting period for pending prenote
PRENOTE_PENDING_WAITING_PERIOD = 5

CANCELLATION_PAYMENT_TRANSACTION_TYPE = "PaymentOut Cancellation"

STATE_TAX_WITHHOLDING_TIN = "SITPAYEE"
FEDERAL_TAX_WITHHOLDING_TIN = "FITAMOUNTPAYEE"
PAYEE_CHILD_SUPPORT_NAME = "Massachusetts Department of Revenue Child Support Enforcement Division"

# There are multiple types of overpayments
OVERPAYMENT_PAYMENT_TRANSACTION_TYPES = [
    PaymentTransactionType.OVERPAYMENT,
    PaymentTransactionType.OVERPAYMENT_ADJUSTMENT,
    PaymentTransactionType.OVERPAYMENT_ADJUSTMENT_CANCELLATION,
]

OVERPAYMENT_REPAYMENT_PAYMENT_TRANSACTION_TYPES = [
    PaymentTransactionType.OVERPAYMENT_RECOVERY,
    PaymentTransactionType.OVERPAYMENT_ACTUAL_RECOVERY,
    PaymentTransactionType.OVERPAYMENT_RECOVERY_CANCELLATION,
    PaymentTransactionType.OVERPAYMENT_RECOVERY_REVERSE,
    PaymentTransactionType.OVERPAYMENT_ACTUAL_RECOVERY_CANCELLATION,
]

PAYMENT_EVENT_TYPES = [
    PaymentEventType.PAYMENT_OUT,
    PaymentEventType.PAYMENT_OUT_CANCELLATIONS,
    PaymentEventType.OVERPAYMENT,
    PaymentEventType.OVERPAYMENT_ADJUSTMENT,
    PaymentEventType.OVERPAYMENT_ADJUSTMENT_CANCELLATION,
    PaymentEventType.OVERPAYMENT_ACTUAL_RECOVERY,
    PaymentEventType.OVERPAYMENT_RECOVERY_CANCELLATION,
    PaymentEventType.OVERPAYMENT_ACTUAL_RECOVERY_CANCELLATION,
    PaymentEventType.OVERPAYMENT_RECOVERY_REVERSE,
]

PAYMENT_OUT_TRANSACTION_TYPE = "PaymentOut"
AUTO_ALT_EVENT_REASON = "Automatic Alternate Payment"
UNKNOWN_REASON = "None"

TAX_IDENTIFICATION_NUMBER = "Tax Identification Number"
TAX_ID = "ID"

# TASKTYPENAME of VBI Task Report Som for other income-related tasks
OTHER_INCOME_TASKTYPENAMES = [
    "Employee Reported Other Income",
    "Escalate Employer Reported Other Income",
    "Escalate employer reported accrued paid leave (PTO)",
    "Employee reported accrued paid leave (PTO)",
    "Employee Reported Other Leave",
]

# TASKTYPENAME of VBI Task Report SOM for fraud related tasks
FRAUD_TASKTYPENAMES = ["Fraud Report Received", "Escalate Employer Reported Fraud"]

SIT_PAYEE = "SIT Payee"  # State Income Tax
FIT_AMOUNT_PAYEE = "FIT Amount Payee"  # Federal Income Tax


class PaymentData:
    """A class for containing any and all payment data. Handles validation and
    pulling values out of the various types

    All values are pulled from the CSV as-is and as strings. Values prefixed with raw_ need
    to be converted from the FINEOS value to one of our DB values (usually a lookup enum)
    """

    validation_container: payments_util.ValidationContainer

    pei_record: FineosExtractVpei
    payment_details: List[FineosExtractVpeiPaymentDetails]
    payment_lines: List[FineosExtractVpeiPaymentLine]
    claim_details: Optional[FineosExtractVpeiClaimDetails]
    requested_absence_record: Optional[FineosExtractVbiRequestedAbsence]

    c_value: str
    i_value: str

    tin: Optional[str] = None
    full_name: Optional[str] = None
    payee_identifier: Optional[str] = None

    absence_case_number: Optional[str] = None
    leave_request_id: Optional[int] = None
    leave_request_decision: Optional[str] = None
    claim_type_id: Optional[int] = None

    payment_method_id: Optional[int] = None
    payment_address: Optional[Address] = None
    pub_eft: Optional[PubEft] = None

    payment_start_period: Optional[date] = None
    payment_end_period: Optional[date] = None
    payment_date: Optional[date] = None
    absence_case_creation_date: Optional[date] = None
    payment_amount: Optional[Decimal] = None

    event_type: Optional[str] = None
    event_reason: Optional[str] = None
    payment_type: Optional[str] = None

    payment_detail_records: List[PaymentDetails]
    payment_line_records: List[PaymentLine]

    payment_transaction_type: LkPaymentTransactionType
    payment_relevant_party: LkPaymentRelevantParty

    is_dor_cse_payee: bool = False

    def increment(self, metric: str) -> None:
        if self.count_incrementer:
            self.count_incrementer(metric)

    def __init__(
        self,
        c_value: str,
        i_value: str,
        pei_record: FineosExtractVpei,
        payment_details: List[FineosExtractVpeiPaymentDetails],
        payment_lines: List[FineosExtractVpeiPaymentLine],
        claim_details: Optional[FineosExtractVpeiClaimDetails],
        requested_absence_record: Optional[FineosExtractVbiRequestedAbsence],
        count_incrementer: Optional[Callable[[str], None]] = None,
    ):
        self.validation_container = payments_util.ValidationContainer(
            str(f"C={c_value},I={i_value}")
        )
        self.c_value = c_value
        self.i_value = i_value
        self.pei_record = pei_record
        self.payment_details = payment_details
        self.payment_lines = payment_lines
        self.claim_details = claim_details
        self.requested_absence_record = requested_absence_record

        self.payment_detail_records = []
        self.payment_line_records = []

        self.count_incrementer = count_incrementer
        self.is_dor_cse_payee = bool(
            self.pei_record and self.pei_record.payeefullname != PAYEE_CHILD_SUPPORT_NAME
        )

        #####################################################################
        # BEGIN - VALIDATION OF PARAMETERS ALWAYS REQUIRED FOR ALL PAYMENTS
        #####################################################################

        # Grab every value we might need out of the datasets
        self.tin = payments_util.validate_db_input(
            "PAYEESOCNUMBE", self.pei_record, self.validation_container, self.is_dor_cse_payee
        )

        self.payee_identifier = payments_util.validate_db_input(
            "PAYEEIDENTIFI", self.pei_record, self.validation_container, True
        )

        self.event_type = payments_util.validate_db_input(
            "EVENTTYPE", self.pei_record, self.validation_container, True
        )

        # Not required as some valid scenarios won't set this (Overpayments)
        self.event_reason = payments_util.validate_db_input(
            "EVENTREASON", self.pei_record, self.validation_container, False
        )

        self.payment_type = payments_util.validate_db_input(
            "PAYMENTTYPE", self.pei_record, self.validation_container, False
        )

        raw_payment_date = payments_util.validate_db_input(
            "PAYMENTDATE",
            self.pei_record,
            self.validation_container,
            True,
            custom_validator_func=payments_util.date_timestamp_validator,
        )
        if raw_payment_date:
            self.payment_date = datetime_str_to_date(raw_payment_date)

        self.payment_amount = self.get_payment_amount(self.pei_record)

        self.payment_relevant_party = self.get_relevant_party()

        self.payment_transaction_type = self.get_payment_transaction_type()

        # Process the payment details records in order to get specific
        # pay-period information for payments.
        if not self.payment_details and self.is_payment_detail_expected():
            self.validation_container.add_validation_issue(
                payments_util.ValidationReason.MISSING_DATASET, "payment_details", "payment_details"
            )

        if self.payment_details:
            self.aggregate_payment_details(self.payment_details)

        # We always expect payment lines, except for zero dollar tax withholdings
        # which don't end up with payment line records for whatever reason
        if not self.payment_lines and not (self.is_tax_withholding() and self.is_zero_dollar()):
            self.validation_container.add_validation_issue(
                payments_util.ValidationReason.MISSING_DATASET, "payment_lines", "payment_lines"
            )

        if self.payment_lines:
            self.aggregate_payment_lines(self.payment_lines)

        #####################################################################
        # BEGIN - VALIDATION OF PARAMETERS ALWAYS REQUIRED FOR PAYMENTS INTENDED FOR PUB
        # - Standard Claimant Payments
        # - Employer Reimbursements to employer
        #####################################################################

        # Find the record in the other datasets.
        if not self.claim_details and self.is_payment_intended_for_pub():
            self.validation_container.add_validation_issue(
                payments_util.ValidationReason.MISSING_DATASET, "claim_details"
            )

        if self.claim_details:
            self.process_claim_details(self.claim_details, self.requested_absence_record)
        elif self.is_payment_intended_for_pub():
            # We require the absence case number, if claim details doesn't exist
            # we want to set the validation issue manually here
            self.validation_container.add_validation_issue(
                payments_util.ValidationReason.MISSING_FIELD, "ABSENCECASENU", "ABSENCECASENU"
            )

        disallowed_lookup_values = [PaymentMethod.DEBIT.payment_method_description]

        # If prepaid impact payments are disabled, we need to skip prepaid card payments
        if not is_prepaid_impact_payments_enabled():
            disallowed_lookup_values.append(PaymentMethod.PREPAID_CARD.payment_method_description)

        # Employer reimbursements are check only
        if self.is_employer_reimbursement_for_pub():
            disallowed_lookup_values.append(PaymentMethod.ACH.payment_method_description)
        raw_payment_method = payments_util.validate_db_input(
            "PAYMENTMETHOD",
            self.pei_record,
            self.validation_container,
            self.is_payment_intended_for_pub(),
            custom_validator_func=payments_util.lookup_validator(
                PaymentMethod,
                disallowed_lookup_values=disallowed_lookup_values,
            ),
        )
        if raw_payment_method:
            self.payment_method_id = PaymentMethod.get_id(raw_payment_method)

        # Process address data and create an address record
        self.process_address_data()

        # Process EFT data and create an EFT record
        self.process_eft_data()

        # We can't use the name from the employee object as that is the
        # claimant, need to get it from the VPEI record
        self.full_name = payments_util.validate_db_input(
            "PAYEEFULLNAME",
            self.pei_record,
            self.validation_container,
            self.is_employer_reimbursement_for_pub(),
        )

    def get_payment_amount(self, pei_record: FineosExtractVpei) -> Optional[Decimal]:
        raw_payment_amount = payments_util.validate_db_input(
            "AMOUNT_MONAMT",
            pei_record,
            self.validation_container,
            True,
            custom_validator_func=payments_util.amount_validator,
        )

        if raw_payment_amount:
            return Decimal(raw_payment_amount)

        # In the unlikely scenario where payment amount isn't set
        # we need to set something as the DB requires this to be set

        # As a sanity check, make certain that missing amount was caught
        # by the earlier validation logic, this if statement shouldn't
        # ever happen. This exists to show we're not ever accidentally
        # setting a payment that is going further in processing.

        if not self.validation_container.has_validation_issues():
            raise Exception("A payment without an amount was found and not caught by validation.")
        return None

    def get_payment_transaction_type(self) -> LkPaymentTransactionType:
        """
        Determine the payment transaction type of the data we have processed.
        This document details the order of precedence in how we handle payments that
        could potentially fall into multiple payment types.
        https://lwd.atlassian.net/wiki/spaces/API/pages/1336901700/Types+of+Payments
        """
        # Cancellations
        if self.event_type == CANCELLATION_PAYMENT_TRANSACTION_TYPE:
            return PaymentTransactionType.CANCELLATION

        # Zero dollar payments overrule all other payment types
        if self.payment_amount == Decimal("0"):
            return PaymentTransactionType.ZERO_DOLLAR

        # Note that Overpayments can be positive or negative amounts
        overpayment_transaction_type = self.get_transaction_type_if_overpayment_or_repayment()
        if overpayment_transaction_type:
            return overpayment_transaction_type

        if (
            self.event_type == PAYMENT_OUT_TRANSACTION_TYPE
            and self.payment_amount
            and self.payment_amount > Decimal("0")
        ):

            if (
                self.payment_relevant_party.payment_relevant_party_id
                == PaymentRelevantParty.DEPARTMENT_OF_REVENUE.payment_relevant_party_id
            ):
                if self.is_dor_cse_payee is False:
                    return PaymentTransactionType.CHILD_SUPPORT_PAYMENT
                else:
                    return PaymentTransactionType.STATE_TAX_WITHHOLDING
            elif (
                self.payment_relevant_party.payment_relevant_party_id
                == PaymentRelevantParty.INTERNAL_REVENUE_SERVICE.payment_relevant_party_id
            ):
                return PaymentTransactionType.FEDERAL_TAX_WITHHOLDING
            elif (
                self.payment_relevant_party.payment_relevant_party_id
                == PaymentRelevantParty.EMPLOYER.payment_relevant_party_id
            ):
                return PaymentTransactionType.EMPLOYER_REIMBURSEMENT
            elif (
                self.payment_relevant_party.payment_relevant_party_id
                == PaymentRelevantParty.CLAIMANT.payment_relevant_party_id
            ):
                return PaymentTransactionType.STANDARD

        self.validation_container.add_validation_issue(
            payments_util.ValidationReason.UNEXPECTED_PAYMENT_TRANSACTION_TYPE,
            f"Unknown payment scenario encountered. Payment Amount: {self.payment_amount}, Event Type: {self.event_type}, Event Reason: {self.event_reason}",
        )
        return PaymentTransactionType.UNKNOWN

    def get_transaction_type_if_overpayment_or_repayment(
        self,
    ) -> Optional[LkPaymentTransactionType]:
        transaction_type = self.get_transaction_type_if_overpayment()

        if transaction_type:
            return transaction_type

        return self.get_transaction_type_if_overpayment_repayment()

    def get_transaction_type_if_overpayment(self) -> Optional[LkPaymentTransactionType]:
        for overpayment_transaction_type in OVERPAYMENT_PAYMENT_TRANSACTION_TYPES:
            if self.event_type == overpayment_transaction_type.payment_transaction_type_description:
                return overpayment_transaction_type
        return None

    def get_transaction_type_if_overpayment_repayment(self) -> Optional[LkPaymentTransactionType]:
        for repayment_transaction_type in OVERPAYMENT_REPAYMENT_PAYMENT_TRANSACTION_TYPES:
            if self.event_type == repayment_transaction_type.payment_transaction_type_description:
                return repayment_transaction_type
        return None

    def get_payment_event_type(self) -> Optional[LkPaymentEventType]:
        for payment_event_type in PAYMENT_EVENT_TYPES:
            if self.event_type == payment_event_type.payment_event_type_description:
                return payment_event_type
        return None

    def get_relevant_party(self) -> LkPaymentRelevantParty:
        """
        Determine the relevant party for the payment. Payment transaction type
        on its own doesn't determine this; for example, a zero dollar payment
        might be issued to a claimant or as part of federal tax withholding.
        """

        if self.tin and self.tin.startswith(STATE_TAX_WITHHOLDING_TIN):
            return PaymentRelevantParty.DEPARTMENT_OF_REVENUE

        if self.tin and self.tin.startswith(FEDERAL_TAX_WITHHOLDING_TIN):
            return PaymentRelevantParty.INTERNAL_REVENUE_SERVICE

        if not self.is_dor_cse_payee:
            return PaymentRelevantParty.DEPARTMENT_OF_REVENUE

        if (
            self.event_reason == AUTO_ALT_EVENT_REASON
            and self.get_payment_event_type()
            and (
                self.payee_identifier == TAX_IDENTIFICATION_NUMBER
                or self.payee_identifier == TAX_ID
            )
        ):
            return PaymentRelevantParty.EMPLOYER

        # this is for Employer Overpayments. Flag will be removed once tested in lower regions
        if payments_util.is_employer_overpayment_enabled():
            # validate_db_input is translating value 'Unknown' mapped to Overpayments to None
            # so we check here for event reason 'None'
            if (
                self.event_reason == UNKNOWN_REASON
                and self.get_payment_event_type()
                and (
                    self.payee_identifier == TAX_IDENTIFICATION_NUMBER
                    or self.payee_identifier == TAX_ID
                )
            ):
                return PaymentRelevantParty.EMPLOYER

        # All other scenarios should be claimants
        return PaymentRelevantParty.CLAIMANT

    def process_claim_details(
        self,
        claim_details: FineosExtractVpeiClaimDetails,
        requested_absence: Optional[FineosExtractVbiRequestedAbsence],
    ) -> None:
        self.absence_case_number = payments_util.validate_db_input(
            "ABSENCECASENU", claim_details, self.validation_container, self.is_standard_payment()
        )

        leave_request_id = payments_util.validate_db_input(
            "LEAVEREQUESTI",
            claim_details,
            self.validation_container,
            self.is_payment_intended_for_pub(),
            custom_validator_func=payments_util.leave_request_id_validator,
        )
        if leave_request_id:
            self.leave_request_id = str_to_int(leave_request_id)

        if requested_absence:

            def leave_request_decision_validator_closure(
                is_adhoc_payment: bool,
            ) -> Callable[[str], Optional[payments_util.ValidationReason]]:
                def leave_request_decision_validator(
                    leave_request_decision: str,
                ) -> Optional[payments_util.ValidationReason]:
                    if leave_request_decision == "In Review":
                        if is_adhoc_payment:
                            return None
                        self.increment(PaymentExtractStep.Metrics.IN_REVIEW_LEAVE_REQUEST_COUNT)

                        return payments_util.ValidationReason.LEAVE_REQUEST_IN_REVIEW
                    if leave_request_decision not in ["Approved", "Fully Adjudicated - Approved"]:
                        self.increment(PaymentExtractStep.Metrics.NOT_APPROVED_LEAVE_REQUEST_COUNT)

                        return payments_util.ValidationReason.INVALID_VALUE
                    return None

                return leave_request_decision_validator

            self.leave_request_decision = payments_util.validate_db_input(
                "LEAVEREQUEST_DECISION",
                requested_absence,
                self.validation_container,
                self.is_payment_intended_for_pub(),
                custom_validator_func=leave_request_decision_validator_closure(
                    self.is_adhoc_payment()
                ),
            )

            # TODO, move try/except into validator func
            claim_type_raw = payments_util.validate_db_input(
                "ABSENCEREASON_COVERAGE",
                requested_absence,
                self.validation_container,
                True,
                custom_validator_func=payments_util.claim_type_validator,
            )
            if claim_type_raw:
                self.claim_type_id = payments_util.get_mapped_claim_type(
                    claim_type_raw
                ).claim_type_id

            raw_absence_case_creation_date = payments_util.validate_db_input(
                "ABSENCE_CASECREATIONDATE",
                requested_absence,
                self.validation_container,
                True,
                custom_validator_func=payments_util.date_timestamp_validator,
            )
            if raw_absence_case_creation_date:
                self.absence_case_creation_date = datetime_str_to_date(
                    raw_absence_case_creation_date
                )

        elif self.is_payment_intended_for_pub():
            self.validation_container.add_validation_issue(
                payments_util.ValidationReason.MISSING_DATASET,
                f"Payment leave request ID not found in requested absence file: {self.leave_request_id}",
            )

    def aggregate_payment_details(
        self, payment_details: List[FineosExtractVpeiPaymentDetails]
    ) -> None:
        """Aggregate payment period dates across all the payment details for this payment.

        Pseudocode:
           payment_start_period = min(payment_detail[1..N].PAYMENTSTARTP)
           payment_end_period = max(payment_detail[1..N].PAYMENTENDP)
        """
        start_periods = []
        end_periods = []
        for payment_detail_row in payment_details:
            payment_details_c_value = payments_util.validate_db_input(
                "C",
                payment_detail_row,
                self.validation_container,
                True,
            )

            payment_details_i_value = payments_util.validate_db_input(
                "I",
                payment_detail_row,
                self.validation_container,
                True,
            )

            row_start_period = payments_util.validate_db_input(
                "PAYMENTSTARTP",
                payment_detail_row,
                self.validation_container,
                True,
                custom_validator_func=payments_util.date_timestamp_validator,
            )
            row_end_period = payments_util.validate_db_input(
                "PAYMENTENDPER",
                payment_detail_row,
                self.validation_container,
                True,
                custom_validator_func=payments_util.date_timestamp_validator,
            )

            # This amount will sum to the amount we pay the claimant
            # across all of the payment periods of a payment
            row_amount_post_tax = payments_util.validate_db_input(
                "BALANCINGAMOU_MONAMT",
                payment_detail_row,
                self.validation_container,
                True,
                custom_validator_func=payments_util.amount_validator,
            )

            # This amount is prior to taxes being taken out and
            # also includes overpayments that have been paid back in some scenarios
            business_net_amount = payments_util.validate_db_input(
                "BUSINESSNETBE_MONAMT",
                payment_detail_row,
                self.validation_container,
                True,
                custom_validator_func=payments_util.amount_validator,
            )

            if row_start_period is not None:
                start_periods.append(row_start_period)
            if row_end_period is not None:
                end_periods.append(row_end_period)

            if all(
                field is not None
                for field in [
                    payment_details_c_value,
                    payment_details_i_value,
                    row_start_period,
                    row_end_period,
                    row_amount_post_tax,
                    business_net_amount,
                ]
            ):
                self.payment_detail_records.append(
                    PaymentDetails(
                        payment_details_id=uuid.uuid4(),
                        vpei_payment_details_id=payment_detail_row.vpei_payment_details_id,
                        payment_details_c_value=payment_details_c_value,
                        payment_details_i_value=payment_details_i_value,
                        period_start_date=datetime_str_to_date(row_start_period),
                        period_end_date=datetime_str_to_date(row_end_period),
                        amount=Decimal(cast(str, row_amount_post_tax)),
                        business_net_amount=Decimal(cast(str, business_net_amount)),
                    )
                )

        if start_periods:
            start_period = min(start_periods)
            self.payment_start_period = datetime_str_to_date(start_period)
        if end_periods:
            end_period = max(end_periods)
            self.payment_end_period = datetime_str_to_date(end_period)

    def aggregate_payment_lines(self, payment_lines: List[FineosExtractVpeiPaymentLine]) -> None:
        """
        Iterate over the list of payment lines and validate
        their values. If valid, create a payment line record
        in the DB.

        Also connects the payment line to the payment detail
        record that it is associated with at the same time.
        """
        # Create a mapping for payment detail C/I value
        # as we'll reference it below
        payment_detail_mapping = {}
        for payment_detail in self.payment_detail_records:
            key = (payment_detail.payment_details_c_value, payment_detail.payment_details_i_value)
            payment_detail_mapping[key] = payment_detail

        for payment_line in payment_lines:
            payment_line_c_value = payments_util.validate_db_input(
                "C",
                payment_line,
                self.validation_container,
                True,
            )

            payment_line_i_value = payments_util.validate_db_input(
                "I",
                payment_line,
                self.validation_container,
                True,
            )

            amount = payments_util.validate_db_input(
                "AMOUNT_MONAMT",
                payment_line,
                self.validation_container,
                True,
                custom_validator_func=payments_util.amount_validator,
            )

            line_type = payments_util.validate_db_input(
                "LINETYPE",
                payment_line,
                self.validation_container,
                True,
            )

            payment_detail_class_id = payments_util.validate_db_input(
                "PAYMENTDETAILCLASSID",
                payment_line,
                self.validation_container,
                self.is_payment_detail_expected(),
            )
            payment_detail_index_id = payments_util.validate_db_input(
                "PAYMENTDETAILINDEXID",
                payment_line,
                self.validation_container,
                self.is_payment_detail_expected(),
            )

            related_payment_detail: Optional[PaymentDetails] = payment_detail_mapping.get(
                (payment_detail_class_id, payment_detail_index_id), None
            )
            if not related_payment_detail and self.is_payment_detail_expected():
                self.validation_container.add_validation_issue(
                    payments_util.ValidationReason.MISSING_DATASET,
                    f"Payment detail with C={payment_detail_class_id},I={payment_detail_index_id} not found for payment line",
                    "payment_detail",
                )

            if all(
                field is not None
                for field in [
                    payment_line_c_value,
                    payment_line_i_value,
                    amount,
                    line_type,
                ]
            ):

                self.payment_line_records.append(
                    PaymentLine(
                        vpei_payment_line_id=payment_line.vpei_payment_line_id,
                        # Payment ID gets set after we create the payment
                        payment_details_id=(
                            related_payment_detail.payment_details_id
                            if related_payment_detail
                            else None
                        ),
                        payment_line_c_value=cast(str, payment_line_c_value),
                        payment_line_i_value=cast(str, payment_line_i_value),
                        amount=Decimal(cast(str, amount)),
                        line_type=cast(str, line_type),
                    )
                )

    def process_address_data(self) -> None:
        # Address values are only required if we are paying by check
        address_required = (
            self.payment_method_id == PaymentMethod.CHECK.payment_method_id
            or (
                is_prepaid_impact_payments_enabled()
                and self.payment_method_id == PaymentMethod.PREPAID_CARD.payment_method_id
            )
        ) and self.is_payment_intended_for_pub()
        address_line_one = payments_util.validate_db_input(
            "PAYMENTADD1", self.pei_record, self.validation_container, address_required
        )
        address_line_two = payments_util.validate_db_input(
            "PAYMENTADD2",
            self.pei_record,
            self.validation_container,
            False,  # Address line two always optional
        )
        address1_len = len(address_line_one) if address_line_one else 0
        address2_len = len(address_line_two) if address_line_two else 0
        if address_required and address1_len + address2_len > 70:
            self.validation_container.add_validation_issue(
                payments_util.ValidationReason.ADDRESS_TOO_LONG,
                "address_one + address_two GT 70",
                "address_line_one + address_line_two",
            )
        city = payments_util.validate_db_input(
            "PAYMENTADD4", self.pei_record, self.validation_container, address_required
        )
        state = payments_util.validate_db_input(
            "PAYMENTADD6",
            self.pei_record,
            self.validation_container,
            address_required,
            custom_validator_func=payments_util.lookup_validator(GeoState),
        )

        zip_code = payments_util.validate_db_input(
            "PAYMENTPOSTCO",
            self.pei_record,
            self.validation_container,
            address_required,
            min_length=5,
            max_length=10,
            custom_validator_func=payments_util.zip_code_validator,
        )

        # Create an address object from the above
        # note we may not add this to the DB if
        # an equivalent already exists, but we'll
        # use this as a lookup tool.
        self.payment_address = Address(
            address_id=uuid.uuid4(),
            address_line_one=address_line_one,
            address_line_two=address_line_two if address_line_two else None,
            city=city,
            geo_state_id=GeoState.get_id(state) if state else None,
            zip_code=zip_code,
            address_type_id=AddressType.MAILING.address_type_id,
        )

    def process_eft_data(self) -> None:
        # These are only required if payment_method is for EFT
        eft_required = (
            self.payment_method_id == PaymentMethod.ACH.payment_method_id
            and self.is_standard_payment()
        )
        routing_nbr = payments_util.validate_db_input(
            "PAYEEBANKSORT",
            self.pei_record,
            self.validation_container,
            eft_required,
            min_length=9,
            max_length=9,
            custom_validator_func=payments_util.routing_number_validator,
        )
        account_nbr = payments_util.validate_db_input(
            "PAYEEACCOUNTN", self.pei_record, self.validation_container, eft_required, max_length=17
        )
        raw_account_type = payments_util.validate_db_input(
            "PAYEEACCOUNTT",
            self.pei_record,
            self.validation_container,
            eft_required,
            custom_validator_func=payments_util.lookup_validator(BankAccountType),
        )

        if all(
            field is not None and len(field) > 0
            for field in [routing_nbr, account_nbr, raw_account_type]
        ):
            self.pub_eft = PubEft(
                pub_eft_id=uuid.uuid4(),
                routing_nbr=cast(str, routing_nbr),
                account_nbr=cast(str, account_nbr),
                bank_account_type_id=BankAccountType.get_id(raw_account_type),
                prenote_state_id=PrenoteState.PENDING_PRE_PUB.prenote_state_id,  # If this is new, we want it to be pending
            )

    def is_adhoc_payment(self) -> bool:
        # In past iteration self.amalgamation_c was used to determine this field,
        # but was switched to payment_type to future proof against fineos
        # changing amalgamation_c field again.
        # See: https://lwd.atlassian.net/browse/API-2235 for more details
        return self.payment_type == "Adhoc"

    def is_standard_payment(self) -> bool:
        # We only want to do specific checks if it is a standard payment
        # There is no need to error a cancellation/overpayment/etc. if the payment
        # is missing EFT or address info that we are never going to use.
        return (
            self.payment_transaction_type.payment_transaction_type_id
            == PaymentTransactionType.STANDARD.payment_transaction_type_id
        )

    def is_employer_reimbursement_for_pub(self) -> bool:
        return (
            self.payment_transaction_type.payment_transaction_type_id
            == PaymentTransactionType.EMPLOYER_REIMBURSEMENT.payment_transaction_type_id
        )

    def is_for_employer(self) -> bool:
        return (
            self.payment_relevant_party.payment_relevant_party_id
            == PaymentRelevantParty.EMPLOYER.payment_relevant_party_id
        )

    def is_zero_dollar(self) -> bool:
        return (
            self.payment_transaction_type.payment_transaction_type_id
            == PaymentTransactionType.ZERO_DOLLAR.payment_transaction_type_id
        )

    def is_tax_withholding(self) -> bool:
        return self.payment_relevant_party.payment_relevant_party_id in [
            PaymentRelevantParty.DEPARTMENT_OF_REVENUE.payment_relevant_party_id,
            PaymentRelevantParty.INTERNAL_REVENUE_SERVICE.payment_relevant_party_id,
        ]

    def is_employee_required(self) -> bool:
        # Employee isn't required and isn't expected for state/federal taxes
        return not self.is_tax_withholding()

    def is_payment_intended_for_pub(self) -> bool:
        # Whether or not the payment needs to go through
        # the core payments logic to PUB which dictates
        # which validations we need to do.
        return self.is_standard_payment() or self.is_employer_reimbursement_for_pub()

    def is_payment_detail_expected(self) -> bool:
        # Overpayment recoveries (of several different types) do not
        # have payment detail records as they don't have pay periods
        return (
            self.payment_transaction_type.payment_transaction_type_id
            not in payments_util.Constants.OVERPAYMENT_TYPES_WITHOUT_PAYMENT_DETAILS_IDS
        )

    def get_traceable_details(self) -> Dict[str, Optional[Any]]:
        # For logging purposes, this returns useful, traceable details
        # about a payment that isn't PII. Recommended usage is as:
        # logger.info("...", extra=payment_data.get_traceable_details())
        return {
            "c_value": self.c_value,
            "i_value": self.i_value,
            "absence_case_id": self.absence_case_number,
            "period_start_date": self.payment_start_period,
            "period_end_date": self.payment_end_period,
            "payment_transaction_type": self.payment_transaction_type.payment_transaction_type_description,
            "payment_relevant_party": self.payment_relevant_party.payment_relevant_party_description,
        }


class PaymentExtractStep(Step):
    """
    https://lwd.atlassian.net/wiki/spaces/API/pages/2365390859/Payment+Extract+Step
    """

    class Metrics(str, enum.Enum):
        EXTRACT_PATH = "extract_path"
        VBI_REQUESTED_ABSENCE_EXTRACT_PATH = "vbi_requested_absence_extract_path"
        ACTIVE_PAYMENT_ERROR_COUNT = "active_payment_error_count"
        APPROVED_PRENOTE_COUNT = "approved_prenote_count"
        CANCELLATION_COUNT = "cancellation_count"
        CLAIM_DETAILS_RECORD_COUNT = "claim_details_record_count"
        CLAIM_NOT_FOUND_COUNT = "claim_not_found_count"
        CLAIMANT_MISMATCH_COUNT = "claimant_mismatch_count"
        EFT_FOUND_COUNT = "eft_found_count"
        EMPLOYEE_MISSING_IN_DB_COUNT = "employee_in_payment_extract_missing_in_db_count"
        EMPLOYEE_FINEOS_NAME_MISSING = "employee_fineos_name_missing"
        EMPLOYER_REIMBURSEMENT_COUNT = "employer_reimbursement_count"
        ERRORED_PAYMENT_COUNT = "errored_payment_count"
        NEW_EFT_COUNT = "new_eft_count"
        NOT_APPROVED_PRENOTE_COUNT = "not_approved_prenote_count"
        NOT_APPROVED_LEAVE_REQUEST_COUNT = "not_approved_leave_request_count"
        IN_REVIEW_LEAVE_REQUEST_COUNT = "in_review_leave_request_count"
        OVERPAYMENT_COUNT = "overpayment_count"
        PAYMENT_DETAILS_RECORD_COUNT = "payment_details_record_count"
        PAYMENT_LINE_RECORD_COUNT = "payment_line_record_count"
        PEI_RECORD_COUNT = "pei_record_count"
        PRENOTE_PAST_WAITING_PERIOD_APPROVED_COUNT = "prenote_past_waiting_period_approved_count"
        PROCESSED_PAYMENT_COUNT = "processed_payment_count"
        REQUESTED_ABSENCE_RECORD_COUNT = "requested_absence_record_count"
        STANDARD_VALID_PAYMENT_COUNT = "standard_valid_payment_count"
        TAX_IDENTIFIER_MISSING_IN_DB_COUNT = "tax_identifier_missing_in_db_count"
        ZERO_DOLLAR_PAYMENT_COUNT = "zero_dollar_payment_count"
        ADHOC_PAYMENT_COUNT = "adhoc_payment_count"
        MULTIPLE_CLAIM_DETAILS_ERROR_COUNT = "multiple_claim_details_error_count"
        FEDERAL_WITHHOLDING_PAYMENT_COUNT = "federal_withholding_payment_count"
        STATE_WITHHOLDING_PAYMENT_COUNT = "state_withholding_payment_count"
        EXEMPT_EMPLOYER_COUNT = "exempt_employer_count"
        OVERPAYMENT_EMPLOYEE_FINEOS_NAME_MISSING = "overpayment_employee_fineos_name_missing"
        OVERPAYMENT_DETAILS_RECORD_COUNT = "overpayment_details_record_count"
        OVERPAYMENT_REPAYMENT_EMPLOYEE_FINEOS_NAME_MISSING = (
            "overpayment_repayment_employee_fineos_name_missing"
        )
        OVERPAYMENT_REPAYMENT_COUNT = "overpayment_repayment_count"
        CHILD_SUPPORT_COUNT = "child_support_count"

    def run_step(self):
        logger.info("Processing payment extract data")
        self.process_records()
        logger.info("Successfully processed payment extract data")

    def is_payment_active_or_currently_being_processed(self, payment: Payment) -> bool:
        """For the given payment, determine if a payment
        is an active payment. This check helps us avoid
        ever double-processing a payment in the event our
        writebacks are not working.

        We get every payment with the same C/I value
        and consider them active if one of the following is true:
        - The payment has an Active writeback status
        - The payment has no writeback status yet
          - This is done in the event we process a new file before
            giving a payment a writeback record. Shouldn't happen
            unless we were unable finish processing extracts for a day.
        """
        # Get all payments associated with C/I value
        # Note that has_active_writeback_issue is set when a payment
        # fails this check. We don't fetch those payments to avoid a case
        # where a payment repeatedly fails because it once failed this check
        other_payments_for_ci_value = (
            self.db_session.query(Payment)
            .filter(
                Payment.fineos_pei_c_value == payment.fineos_pei_c_value,
                Payment.fineos_pei_i_value == payment.fineos_pei_i_value,
                Payment.has_active_writeback_issue.isnot(True),
                Payment.payment_id != payment.payment_id,
            )
            .options(joinedload(Payment.fineos_writeback_details))
            .all()
        )

        # While we iterate over the rest of the versions of the payment
        # we only need to find one issue (regardless of day) for it to
        # be considered active.
        for other_payment in other_payments_for_ci_value:
            writeback_details = other_payment.fineos_writeback_details

            # If the payment doesn't yet have a writeback status
            # then that means it's actively being processed and
            # we should consider it as active. This should be
            # an immensely uncommon scenario that can only happen
            # if we process an extract, but don't give it a writeback
            # before the next extract file is processed.
            if len(writeback_details) == 0:
                extra = payments_util.get_traceable_payment_details(payment)

                extra["active_payment_id"] = other_payment.payment_id
                extra["active_payment_fineos_extract_import_log_id"] = (
                    other_payment.fineos_extract_import_log_id
                )
                logger.error(
                    "Payment received from FINEOS is actively being processed already", extra=extra
                )
                return True

            for writeback_detail in writeback_details:
                if (
                    writeback_detail.transaction_status.writeback_record_status
                    == ACTIVE_WRITEBACK_RECORD_STATUS
                ):
                    extra = payments_util.get_traceable_payment_details(payment)

                    extra["active_writeback_status"] = (
                        writeback_detail.transaction_status.transaction_status_description
                    )
                    extra["active_writeback_sent_at"] = datetime_to_isoformat(
                        writeback_detail.writeback_sent_at
                    )
                    extra["active_writeback_payment_id"] = writeback_detail.payment_id

                    # This isn't unexpected behavior from FINEOS for a few payment in a batch every couple of days
                    # and usually resolves itself.
                    logger.warning(
                        "Payment received from FINEOS already has an active writeback status",
                        extra=extra,
                    )
                    return True

        return False

    def get_employee_and_claim(
        self, payment_data: PaymentData
    ) -> Tuple[Optional[Employee], Optional[Claim]]:

        # Get the TIN, employee and claim associated with the payment to be made
        employee, claim = None, None
        try:
            claim = (
                self.db_session.query(Claim)
                .filter_by(fineos_absence_id=payment_data.absence_case_number)
                .one_or_none()
            )
            # If the employee is required and should be validated, do so
            # Otherwise, we know we aren't going to find an employee, so don't look
            if payment_data.is_employee_required():
                if payment_data.is_for_employer():
                    employee = claim.employee if claim is not None else None
                    if not employee:
                        self.increment(self.Metrics.EMPLOYEE_MISSING_IN_DB_COUNT)
                        payment_data.validation_container.add_validation_issue(
                            payments_util.ValidationReason.MISSING_IN_DB,
                            payment_data.tin,
                            "employee",
                        )
                        employer = (
                            self.db_session.query(Employer)
                            .filter_by(employer_fein=payment_data.tin)
                            .one_or_none()
                        )
                        if not employer:
                            payment_data.validation_container.add_validation_issue(
                                payments_util.ValidationReason.MISSING_IN_DB,
                                payment_data.tin,
                                "employer fein",
                            )
                else:
                    tax_identifier = (
                        self.db_session.query(TaxIdentifier)
                        .filter_by(tax_identifier=payment_data.tin)
                        .one_or_none()
                    )
                    if not tax_identifier:
                        self.increment(self.Metrics.TAX_IDENTIFIER_MISSING_IN_DB_COUNT)
                        payment_data.validation_container.add_validation_issue(
                            payments_util.ValidationReason.MISSING_IN_DB,
                            payment_data.tin,
                            "tax_identifier",
                        )
                    else:
                        employee = (
                            self.db_session.query(Employee)
                            .filter_by(tax_identifier=tax_identifier)
                            .one_or_none()
                        )
                        if not employee:
                            self.increment(self.Metrics.EMPLOYEE_MISSING_IN_DB_COUNT)
                            payment_data.validation_container.add_validation_issue(
                                payments_util.ValidationReason.MISSING_IN_DB,
                                payment_data.tin,
                                "employee",
                            )

        except SQLAlchemyError as e:
            logger.exception(
                "Unexpected error %s with one_or_none when querying for tin/employee/claim",
                type(e),
                extra=payment_data.get_traceable_details(),
            )
            raise

        # If we cannot find the claim, we want to error only for payments that require
        # claim to be set later in processing (claimant, employer reimbursement, and taxes)
        # While we'd like to attach the claim to other payment types it's less of a concern to us.
        if not claim and (
            payment_data.is_payment_intended_for_pub() or payment_data.is_tax_withholding()
        ):
            payment_data.validation_container.add_validation_issue(
                payments_util.ValidationReason.MISSING_IN_DB,
                payment_data.absence_case_number,
                "claim",
            )
            self.increment(self.Metrics.CLAIM_NOT_FOUND_COUNT)
            return None, None

        # Perform various validations on the claim. We require
        # A claim to be ID Proofed
        # A claim to have an attached employer
        # A claim to have a claim type
        # The employee we fetched above to already be connected to the claim
        if claim:
            if not claim.get_is_id_proofed:
                payment_data.validation_container.add_validation_issue(
                    payments_util.ValidationReason.CLAIM_NOT_ID_PROOFED,
                    f"Claim {payment_data.absence_case_number} has not been ID proofed",
                )

            if payment_data.is_payment_intended_for_pub() and not claim.employer_id:
                payment_data.validation_container.add_validation_issue(
                    payments_util.ValidationReason.MISSING_IN_DB,
                    f"Claim {payment_data.absence_case_number} does not have an employer associated with it",
                )

            # If the employee we found does not match what is already attached
            # to the claim, we can't accept the payment.
            if employee and claim.employee_id != employee.employee_id:

                payment_data.validation_container.add_validation_issue(
                    payments_util.ValidationReason.CLAIMANT_MISMATCH,
                    f"Claimant {claim.employee_id} is attached to claim {claim.fineos_absence_id}, but claimant {employee.employee_id} was found.",
                )
                self.increment(self.Metrics.CLAIMANT_MISMATCH_COUNT)
                return None, None

        return employee, claim

    def update_experian_address_pair_fineos_address(
        self, payment_data: PaymentData, employee: Employee
    ) -> Optional[ExperianAddressPair]:
        """Create or update the employee's EFT record

        Returns:
            Optionally returns an ExperianAddressPair if the payment data has no validation errors and is intended for PUB
        """
        # Only update if the employee is using Check for payments
        if payment_data.validation_container.has_validation_issues():
            # We will only update address information if the payment has no issues up to
            # this point in the processing, meaning that required fields are present.
            return None

        if not payment_data.is_payment_intended_for_pub():
            return None

        if not payment_data.payment_address:
            # Shouldn't be possible if it made it past validation
            # but makes mypy happy
            return None

        # Construct an Address from the payment_data
        payment_data_address = payment_data.payment_address

        # If existing_address_pair exists, compare the existing fineos_address with the payment_data address
        #   If they're the same, nothing needs to be done, so we can return the address
        existing_address_pair = payments_util.find_existing_address_pair(
            employee, payment_data_address, self.db_session
        )
        if existing_address_pair:
            return existing_address_pair

        # We need to add the address to the employee.
        # TODO - If FINEOS provides a value that indicates an address
        # has been validated, we would also set the experian_address here.
        # When already verified address is supported, also add a happy path test scenario.
        new_experian_address_pair = ExperianAddressPair(fineos_address=payment_data_address)

        self.db_session.add(payment_data_address)
        self.db_session.add(new_experian_address_pair)

        # We also want to make sure the address is linked in the EmployeeAddress table
        if payment_data.is_standard_payment():
            employee_address = EmployeeAddress(employee=employee, address=payment_data_address)
            self.db_session.add(employee_address)

        return new_experian_address_pair

    def create_payment(
        self, payment_data: PaymentData, claim: Optional[Claim], employee: Optional[Employee]
    ) -> Payment:
        # We always create a new payment record. This may be completely new
        # or a payment might have been created before. We'll check that later.
        logger.info("Creating payment record in DB", extra=payment_data.get_traceable_details())

        # Note that these values may have validation issues and not be set
        # that is fine as it will get moved to an error state at the end of processing

        payment = Payment(
            # IDs for a payment
            payment_id=uuid.uuid4(),
            vpei_id=payment_data.pei_record.vpei_id,
            fineos_pei_c_value=payment_data.c_value,
            fineos_pei_i_value=payment_data.i_value,
            # Claim, absence period, leave info
            claim_type_id=payment_data.claim_type_id,
            fineos_leave_request_id=payment_data.leave_request_id,
            absence_case_creation_date=payment_data.absence_case_creation_date,
            leave_request_decision=payment_data.leave_request_decision,
            # Core payment values
            disb_method_id=payment_data.payment_method_id,
            amount=(
                payment_data.payment_amount
                if payment_data.payment_amount is not None
                else Decimal("0")
            ),
            period_start_date=payment_data.payment_start_period,
            period_end_date=payment_data.payment_end_period,
            payment_date=payment_data.payment_date,
            # Who the payment is for and what type of payment
            payment_relevant_party_id=payment_data.payment_relevant_party.payment_relevant_party_id,
            payment_transaction_type_id=payment_data.payment_transaction_type.payment_transaction_type_id,
            # Tracking info for when we processed the payment
            fineos_extraction_date=get_now_us_eastern().date(),
            fineos_extract_import_log_id=self.get_import_log_id(),
            # For payments that are not paid to the claimant (e.g. - employer reimbursements), we use this name.
            # For claimant payments will use other fields for the claimant name.
            payee_name=payment_data.full_name,
            # This is used later in the post-processing step to filter out
            # adhoc payments from the weekly maximum check and other business rules
            is_adhoc_payment=payment_data.is_adhoc_payment(),
        )
        self.db_session.add(payment)

        # mypy doesn't like these being set above if null, so do here
        if employee:
            payment.employee = employee
        if claim:
            payment.claim = claim

        if payment.is_adhoc_payment:
            self.increment(self.Metrics.ADHOC_PAYMENT_COUNT)

        # If the payment is already being processed or Active,
        # then FINEOS sent us a payment they should not have
        # whether that's a FINEOS issue or writeback issue, we
        # need to error the payment.
        if self.is_payment_active_or_currently_being_processed(payment):
            payment_data.validation_container.add_validation_issue(
                payments_util.ValidationReason.RECEIVED_PAYMENT_CURRENTLY_BEING_PROCESSED,
                "We received a payment that is already being processed, or has been written back to FINEOS as Active already.",
            )
            payment.has_active_writeback_issue = True

            # PFMLPB-18987: Don't count towards monitoring metriv if it's the known issue with FIT Amount Payee and SIT Payee
            if payment.payee_name not in (SIT_PAYEE, FIT_AMOUNT_PAYEE):
                self.increment(self.Metrics.ACTIVE_PAYMENT_ERROR_COUNT)

        for payment_detail in payment_data.payment_detail_records:
            payment_detail.payment = payment
            self.db_session.add(payment_detail)

            extra = payments_util.get_traceable_log_details(payment, payment_detail)
            logger.info("Attached payment detail to payment", extra=extra)

        for payment_line in payment_data.payment_line_records:
            payment_line.payment = payment
            self.db_session.add(payment_line)

            extra = payments_util.get_traceable_log_details(
                payment, payment_line.payment_details, payment_line
            )
            logger.info("Attached payment line to payment", extra=extra)

        return payment

    def create_overpayment(
        self,
        payment_data: PaymentData,
        claim: Optional[Claim],
        employee: Optional[Employee],
        reference_file: ReferenceFile,
    ) -> None:
        """creates or updates an overpayment record."""
        overpayment = self.get_overpayment(payment_data)

        if overpayment:
            self.update_overpayment(payment_data, overpayment, reference_file)

        else:
            self.save_overpayment(payment_data, claim, employee, reference_file)

    def save_overpayment(
        self,
        payment_data: PaymentData,
        claim: Optional[Claim],
        employee: Optional[Employee],
        reference_file: ReferenceFile,
    ) -> None:
        """Creates an overpayment record."""
        logger.info("Creating overpayment record in DB", extra=payment_data.get_traceable_details())
        overpayment = Overpayment(
            overpayment_id=uuid.uuid4(),
            vpei_id=payment_data.pei_record.vpei_id,
            fineos_leave_request_id=payment_data.leave_request_id,
            fineos_pei_c_value=payment_data.c_value,
            fineos_pei_i_value=payment_data.i_value,
            claim_type_id=payment_data.claim_type_id,
            amount=payment_data.payment_amount,
            period_start_date=payment_data.payment_start_period,
            period_end_date=payment_data.payment_end_period,
            overpayment_date=payment_data.payment_date,
            absence_case_creation_date=payment_data.absence_case_creation_date,
            payment_relevant_party_id=payment_data.payment_relevant_party.payment_relevant_party_id,
            payment_transaction_type_id=payment_data.payment_transaction_type.payment_transaction_type_id,
            fineos_extraction_date=get_now_us_eastern().date(),
            fineos_extract_import_log_id=self.get_import_log_id(),
        )

        # mypy doesn't like these being set above if null, so do here
        if claim:
            overpayment.claim = claim
        if employee:
            overpayment.employee = employee

            # Capture the fineos provided employee name for the overpayment
            if (
                employee.fineos_employee_first_name is None
                or employee.fineos_employee_last_name is None
            ):
                self.increment(self.Metrics.OVERPAYMENT_EMPLOYEE_FINEOS_NAME_MISSING)
                payment_data.validation_container.add_validation_issue(
                    payments_util.ValidationReason.MISSING_FINEOS_NAME,
                    f"Missing name from FINEOS on employee {employee.employee_id}",
                )
            else:
                overpayment.fineos_employee_first_name = employee.fineos_employee_first_name
                overpayment.fineos_employee_middle_name = employee.fineos_employee_middle_name
                overpayment.fineos_employee_last_name = employee.fineos_employee_last_name

        if payment_data.event_type:
            overpayment.payment_event_type_id = PaymentEventType.get_id(payment_data.event_type)

        self.db_session.add(overpayment)

        self.increment(
            self.Metrics.OVERPAYMENT_DETAILS_RECORD_COUNT, len(payment_data.payment_detail_records)
        )

        for payment_detail in payment_data.payment_detail_records:
            self.save_overpayment_details(overpayment, payment_detail)

        overpayment_reference_file = OverpaymentReferenceFile(
            overpayment=overpayment, reference_file=reference_file
        )
        self.db_session.add(overpayment_reference_file)

    def get_overpayment(self, payment_data: PaymentData) -> Optional[Overpayment]:
        """Get an overpayment record by PaymentData.c_value and PaymentData.i_value.
        Returns: An existing overpayment record.
        """
        return (
            self.db_session.query(Overpayment)
            .filter(
                Overpayment.fineos_pei_c_value == payment_data.c_value,
                Overpayment.fineos_pei_i_value == payment_data.i_value,
            )
            .first()
        )

    def update_overpayment(
        self, payment_data: PaymentData, overpayment: Overpayment, reference_file: ReferenceFile
    ) -> None:
        """Updates an overpayment record."""
        logger.info("Updating overpayment record in DB", extra=payment_data.get_traceable_details())
        overpayment.vpei_id = payment_data.pei_record.vpei_id
        overpayment.fineos_leave_request_id = payment_data.leave_request_id
        overpayment.fineos_pei_c_value = payment_data.c_value
        overpayment.fineos_pei_i_value = payment_data.i_value
        overpayment.claim_type_id = payment_data.claim_type_id
        overpayment.amount = payment_data.payment_amount
        overpayment.period_start_date = payment_data.payment_start_period
        overpayment.period_end_date = payment_data.payment_end_period
        overpayment.overpayment_date = payment_data.payment_date
        overpayment.absence_case_creation_date = payment_data.absence_case_creation_date
        overpayment.payment_relevant_party_id = (
            payment_data.payment_relevant_party.payment_relevant_party_id
        )

        overpayment.payment_transaction_type_id = (
            payment_data.payment_transaction_type.payment_transaction_type_id
        )

        overpayment.fineos_extraction_date = get_now_us_eastern().date()
        overpayment.fineos_extract_import_log_id = self.get_import_log_id()

        # Update overpayment details
        for overpayment_details in payment_data.payment_detail_records:
            existing_detail = self.get_overpayment_details(overpayment, overpayment_details)

            if existing_detail:
                self.update_overpayment_details(overpayment, overpayment_details, existing_detail)
            else:
                self.save_overpayment_details(overpayment, overpayment_details)

        # Update the existing OverpaymentReferenceFile with the new reference file.
        overpayment_reference_file: Optional[OverpaymentReferenceFile] = (
            self.db_session.query(OverpaymentReferenceFile)
            .filter(OverpaymentReferenceFile.overpayment_id == overpayment.overpayment_id)
            .first()
        )

        if overpayment_reference_file:
            overpayment_reference_file.reference_file_id = reference_file.reference_file_id

    def save_overpayment_details(
        self, overpayment: Overpayment, payment_details: PaymentDetails
    ) -> None:
        """Creates an overpayment details record."""
        overpayment_details = OverpaymentDetails(
            overpayment_details_id=uuid.uuid4(),
            overpayment_details_c_value=payment_details.payment_details_c_value,
            overpayment_details_i_value=payment_details.payment_details_i_value,
            period_start_date=payment_details.period_start_date,
            period_end_date=payment_details.period_end_date,
            amount=payment_details.amount,
            business_net_amount=payment_details.business_net_amount,
            fineos_extraction_date=overpayment.fineos_extraction_date,
            overpayment=overpayment,
        )
        self.db_session.add(overpayment_details)

    def get_overpayment_details(
        self, overpayment: Overpayment, overpayment_details: PaymentDetails
    ) -> Optional[OverpaymentDetails]:
        """Gets an overpayment details record by Overpayment.overpayment_id, PaymentDetails.payment_details_c_value, and PaymentDetails.payment_details_i_value
        Returns: An overpayment details record.
        """
        existing_overpayment_details = (
            self.db_session.query(OverpaymentDetails)
            .filter(
                OverpaymentDetails.overpayment_id == overpayment.overpayment_id,
                OverpaymentDetails.overpayment_details_c_value
                == overpayment_details.payment_details_c_value,
                OverpaymentDetails.overpayment_details_i_value
                == overpayment_details.payment_details_i_value,
            )
            .first()
        )
        return existing_overpayment_details

    def update_overpayment_details(
        self,
        overpayment: Overpayment,
        overpayment_details: PaymentDetails,
        existing_detail: OverpaymentDetails,
    ) -> None:
        """Updates an overpayment details record."""
        existing_detail.period_start_date = overpayment_details.period_start_date
        existing_detail.period_end_date = overpayment_details.period_end_date
        existing_detail.amount = overpayment_details.amount
        existing_detail.business_net_amount = overpayment_details.business_net_amount
        existing_detail.fineos_extraction_date = overpayment.fineos_extraction_date

    def create_overpayment_repayment(
        self, payment_data: PaymentData, employee: Optional[Employee], reference_file: ReferenceFile
    ) -> None:
        """creates or updates an overpayment repayment record."""
        overpayment_repayment = self.get_overpayment_repayment(payment_data)

        if overpayment_repayment:
            self.update_overpayment_repayment(payment_data, overpayment_repayment, reference_file)
        else:
            self.save_overpayment_repayment(payment_data, employee, reference_file)

    def get_overpayment_repayment(
        self, payment_data: PaymentData
    ) -> Optional[OverpaymentRepayment]:
        """Gets an overpayment repayment record by PaymentData.c_value and PaymentData.i_value.
        Returns: An existing overpayment repayment record.
        """
        return (
            self.db_session.query(OverpaymentRepayment)
            .filter(
                OverpaymentRepayment.fineos_pei_c_value == payment_data.c_value,
                OverpaymentRepayment.fineos_pei_i_value == payment_data.i_value,
            )
            .first()
        )

    def save_overpayment_repayment(
        self, payment_data: PaymentData, employee: Optional[Employee], reference_file: ReferenceFile
    ) -> None:
        """Creates an overpayment repayment record."""
        logger.info(
            "Creating an overpayment repayment record in DB",
            extra=payment_data.get_traceable_details(),
        )
        overpayment_repayment = OverpaymentRepayment(
            overpayment_repayment_id=uuid.uuid4(),
            vpei_id=payment_data.pei_record.vpei_id,
            fineos_pei_c_value=payment_data.c_value,
            fineos_pei_i_value=payment_data.i_value,
            overpayment_repayment_date=payment_data.payment_date,
            amount=payment_data.payment_amount,
            # TODO: enable once cwe confirm right table/column should be used from extract files
            # overpayment_recovery_type_id=payment_data.payment_method_id,
            fineos_extraction_date=get_now_us_eastern().date(),
            fineos_extract_import_log_id=self.get_import_log_id(),
        )

        # Note that these values may have validation issues and not be set
        # that is fine as it will get moved to an error state
        if employee:
            overpayment_repayment.employee = employee

            # Capture the fineos provided employee name for the overpayment repayment
            if (
                employee.fineos_employee_first_name is None
                or employee.fineos_employee_last_name is None
            ):
                self.increment(self.Metrics.OVERPAYMENT_REPAYMENT_EMPLOYEE_FINEOS_NAME_MISSING)
                payment_data.validation_container.add_validation_issue(
                    payments_util.ValidationReason.MISSING_FINEOS_NAME,
                    f"Missing name from FINEOS on employee {employee.employee_id}",
                )
            else:
                overpayment_repayment.fineos_employee_first_name = (
                    employee.fineos_employee_first_name
                )
                overpayment_repayment.fineos_employee_middle_name = (
                    employee.fineos_employee_middle_name
                )
                overpayment_repayment.fineos_employee_last_name = employee.fineos_employee_last_name

        if payment_data.event_type:
            overpayment_repayment.payment_event_type_id = PaymentEventType.get_id(
                payment_data.event_type
            )

        self.db_session.add(overpayment_repayment)

        overpayment_repayment_reference_file = OverpaymentRepaymentReferenceFile(
            overpayment_repayment=overpayment_repayment, reference_file=reference_file
        )
        self.db_session.add(overpayment_repayment_reference_file)

    def update_overpayment_repayment(
        self,
        payment_data: PaymentData,
        overpayment_repayment: OverpaymentRepayment,
        reference_file: ReferenceFile,
    ) -> None:
        """Updates an overpayment repayment record."""
        logger.info(
            "Updating overpayment repayment record in DB",
            extra=payment_data.get_traceable_details(),
        )
        overpayment_repayment.vpei_id = payment_data.pei_record.vpei_id
        overpayment_repayment.fineos_pei_c_value = payment_data.c_value
        overpayment_repayment.fineos_pei_i_value = payment_data.i_value
        overpayment_repayment.overpayment_repayment_date = payment_data.payment_date
        overpayment_repayment.amount = payment_data.payment_amount
        # TODO: enable once cwe confirm right table/column should be used from extract files
        # overpayment_repayment.overpayment_recovery_type_id=payment_data.payment_method_id
        overpayment_repayment.fineos_extraction_date = get_now_us_eastern().date()
        overpayment_repayment.fineos_extract_import_log_id = self.get_import_log_id()

        # Update the existing OverpaymentReferenceFile with the new reference file.
        overpayment_repayment_reference_file: Optional[OverpaymentRepaymentReferenceFile] = (
            self.db_session.query(OverpaymentRepaymentReferenceFile)
            .filter(
                OverpaymentRepaymentReferenceFile.overpayment_repayment_id
                == overpayment_repayment.overpayment_repayment_id
            )
            .first()
        )

        if overpayment_repayment_reference_file:
            overpayment_repayment_reference_file.reference_file_id = (
                reference_file.reference_file_id
            )

    def update_eft(self, payment_data: PaymentData, employee: Employee) -> Optional[PubEft]:
        """Create or update the employee's EFT records

        Returns:
            bool: True if the payment_data includes EFT updates; False otherwise
        """

        # Only update if the employee is using ACH for payments
        if payment_data.payment_method_id != PaymentMethod.ACH.payment_method_id:
            # Any existing EFT information is left alone in the event they switch back
            return None

        if payment_data.validation_container.has_validation_issues():
            # We will only update EFT information if the payment has no issues up to
            # this point in the processing, meaning that required fields are present.
            return None

        if not payment_data.is_standard_payment():
            # If it's any non-standard payment (cancellation, overpayment, employer, etc.)
            # There isn't a need to prenote, as it's not going to be paid directly anyways
            # and we don't want to error the payment because it needs to be prenoted.
            return None

        if not payment_data.pub_eft:
            # Shouldn't be possible if it passed validation, but needed
            # to make mypy recognize that it's not null
            return None

        # Need to cast these values as str rather than Optional[str] as we've
        # already validated they're not None for linting
        # Construct an EFT object.
        new_eft = payment_data.pub_eft

        # Retrieve the employee's existing EFT data, if any
        existing_eft = payments_util.find_existing_eft(employee, new_eft)

        # If we found a match, do not need to create anything
        # but do need to add an error to the report if the EFT
        # information is invalid or pending. We can't pay someone
        # unless they have been prenoted
        extra = payment_data.get_traceable_details()
        if existing_eft:
            extra |= payments_util.get_traceable_log_details(
                pub_eft=existing_eft, employee=employee
            )
            self.increment(self.Metrics.EFT_FOUND_COUNT)
            logger.info("Found existing EFT info for claimant associated with payment", extra=extra)

            if PrenoteState.APPROVED.prenote_state_id == existing_eft.prenote_state_id:
                self.increment(self.Metrics.APPROVED_PRENOTE_COUNT)
            elif (
                (PrenoteState.PENDING_WITH_PUB.prenote_state_id == existing_eft.prenote_state_id)
                and existing_eft.prenote_sent_at
                and (get_now_us_eastern() - existing_eft.prenote_sent_at).days
                >= PRENOTE_PENDING_WAITING_PERIOD
            ):
                # Set prenote to approved
                existing_eft.prenote_state_id = PrenoteState.APPROVED.prenote_state_id
                existing_eft.prenote_approved_at = get_now_us_eastern()

                self.increment(self.Metrics.PRENOTE_PAST_WAITING_PERIOD_APPROVED_COUNT)
            else:
                self.increment(self.Metrics.NOT_APPROVED_PRENOTE_COUNT)
                reason = (
                    payments_util.ValidationReason.EFT_PRENOTE_REJECTED
                    if existing_eft.prenote_state_id == PrenoteState.REJECTED.prenote_state_id
                    else payments_util.ValidationReason.EFT_PRENOTE_PENDING
                )
                payment_data.validation_container.add_validation_issue(
                    reason,
                    f"EFT prenote has not been approved, is currently in state [{existing_eft.prenote_state.prenote_state_description}]",
                )

            return existing_eft

        else:
            # This EFT info is new, it needs to be linked to the employee
            # and added to the EFT prenoting flow

            # We will only add it if the EFT info we require is valid and exists
            employee_pub_eft_pair = EmployeePubEftPair(
                employee_id=employee.employee_id, pub_eft_id=new_eft.pub_eft_id
            )

            self.db_session.add(new_eft)
            self.db_session.add(employee_pub_eft_pair)

            extra |= payments_util.get_traceable_log_details(pub_eft=new_eft, employee=employee)
            logger.info(
                "Setting EFT to PENDING_PRE_PUB to begin prenoting process",
                extra=extra,
            )

            # We need to put the payment in an error state if it's not prenoted
            payment_data.validation_container.add_validation_issue(
                payments_util.ValidationReason.EFT_PRENOTE_PENDING,
                "New EFT info found, prenote required",
            )
            self.increment(self.Metrics.NEW_EFT_COUNT)

            return new_eft

    def check_open_tasks_for_claim(self, payment_data: PaymentData, payment: Payment) -> None:
        # Check for specific open tasks associated with the claim.
        if (
            payment_data.is_payment_intended_for_pub()
            and payment_data.absence_case_number is not None
        ):
            # If it is not an adhoc payment and it has certain kinds of open
            # tasks, we reject it and add it to the error report
            if not payment.is_adhoc_payment:
                open_other_income_tasks_delta: List[FineosExtractVbiTaskReportDeltaSom] = (
                    get_open_tasks_from_delta(
                        self.db_session,
                        payment_data.absence_case_number,
                        OTHER_INCOME_TASKTYPENAMES,
                    )
                )
                if len(open_other_income_tasks_delta) > 0:
                    payment_data.validation_container.add_validation_issue(
                        payments_util.ValidationReason.OPEN_OTHER_INCOME_TASKS_DELTA,
                        f"TASKTYPENAMES: {[task.tasktypename for task in open_other_income_tasks_delta]}",
                    )

            # If an absence case has open fraud related tasks, we want to error it.
            # We DO want to include adhoc payments in this check
            open_fraud_tasks_delta = get_open_tasks_from_delta(
                self.db_session, payment_data.absence_case_number, FRAUD_TASKTYPENAMES
            )
            if len(open_fraud_tasks_delta) > 0:
                payment_data.validation_container.add_validation_issue(
                    payments_util.ValidationReason.OPEN_FRAUD_TASKS_DELTA,
                    f"TASKTYPENAMES: {[task.tasktypename for task in open_fraud_tasks_delta]}",
                )

    def add_records_to_db(
        self,
        payment_data: PaymentData,
        employee: Optional[Employee],
        claim: Optional[Claim],
        reference_file: ReferenceFile,
    ) -> Payment:
        start_time = datetime.now()
        # Only update the employee if it exists and there
        # are no validation issues. Employees are used in
        # many contexts, so we want to be careful about modifying
        # them with problematic data.
        payment_eft, address_pair = None, None
        if employee and not payment_data.validation_container.has_validation_issues():
            # Update the mailing address with values from FINEOS
            if payment_data.is_payment_intended_for_pub():
                address_pair = self.update_experian_address_pair_fineos_address(
                    payment_data, employee
                )

            # Update the EFT info with values from FINEOS
            payment_eft = self.update_eft(payment_data, employee)

        if payment_data.get_transaction_type_if_overpayment():
            self.create_overpayment(payment_data, claim, employee, reference_file)

        if payment_data.get_transaction_type_if_overpayment_repayment():
            self.create_overpayment_repayment(payment_data, employee, reference_file)

        # Create the payment record
        payment = self.create_payment(payment_data, claim, employee)

        # Capture the fineos provided employee name for the payment
        if employee:
            if (
                employee.fineos_employee_first_name is None
                or employee.fineos_employee_last_name is None
            ):
                self.increment(self.Metrics.EMPLOYEE_FINEOS_NAME_MISSING)
                payment_data.validation_container.add_validation_issue(
                    payments_util.ValidationReason.MISSING_FINEOS_NAME,
                    f"Missing name from FINEOS on employee {employee.employee_id}",
                )
            else:
                payment.fineos_employee_first_name = employee.fineos_employee_first_name
                payment.fineos_employee_middle_name = employee.fineos_employee_middle_name
                payment.fineos_employee_last_name = employee.fineos_employee_last_name

        # Check whether the employer is exempt from payments
        # Only for standard payments
        if payment_data.is_standard_payment() and payment and claim and claim.employer:
            is_employer_exempt_for_payment = payments_util.is_employer_exempt_for_payment(
                payment, claim, claim.employer
            )
            if is_employer_exempt_for_payment:
                self.increment(self.Metrics.EXEMPT_EMPLOYER_COUNT)
                employer = claim.employer
                message = f"Employer {employer.fineos_employer_id} is exempt for dates {employer.exemption_commence_date} - {employer.exemption_cease_date}"
                payment_data.validation_container.add_validation_issue(
                    payments_util.ValidationReason.EMPLOYER_EXEMPT, message
                )

        self.check_open_tasks_for_claim(payment_data=payment_data, payment=payment)

        # Attach the EFT info used to the payment
        if payment_eft:
            payment.pub_eft = payment_eft

        # Attach the address info used to the payment
        if address_pair:
            payment.experian_address_pair = address_pair

        # Link the payment object to the payment_reference_file
        payment_reference_file = PaymentReferenceFile(
            payment=payment, reference_file=reference_file
        )
        self.db_session.add(payment_reference_file)

        end_time = datetime.now()
        duration = end_time - start_time
        logger.info(
            "Finished add_records_to_db",
            extra={
                "payment_id": payment.payment_id,
                "duration": duration.total_seconds(),
            },
        )

        return payment

    def process_payment_data_record(
        self, payment_data: PaymentData, reference_file: ReferenceFile
    ) -> Payment:
        start_time = datetime.now()
        employee, claim = self.get_employee_and_claim(payment_data)
        end_time = datetime.now()
        duration = end_time - start_time
        logger.info(
            "Finished get_employee_and_claim",
            extra={
                "vpei_id": payment_data.pei_record.vpei_id,
                "employee_id": employee.employee_id if employee else None,
                "claim_id": claim.claim_id if claim else None,
                "duration": duration.total_seconds(),
            },
        )
        payment = self.add_records_to_db(payment_data, employee, claim, reference_file)

        return payment

    def _setup_state_log(self, payment: Payment, payment_data: PaymentData) -> None:
        transaction_status = None
        message = "Success"

        # If it has an active payment issue, we do not want
        # to update the transaction status, the writebacks
        # are probably not working, and we'd prefer the payment
        # keep whatever its original status was.
        if payment.has_active_writeback_issue:
            message = "Active Payment Error - Contact FINEOS"
            end_state = State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_ERROR_REPORT
            self.increment(self.Metrics.ERRORED_PAYMENT_COUNT)

        # https://lwd.atlassian.net/wiki/spaces/API/pages/1336901700/Types+of+Payments
        # Does the payment have validation issues
        # If so, add to that error state
        elif payment_data.validation_container.has_validation_issues():
            message = "Error processing payment record"
            end_state = State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_ERROR_REPORT

            # https://lwd.atlassian.net/wiki/spaces/API/pages/1319272855/Payment+Transaction+Scenarios
            # We want to determine what kind of PEI writeback we'd do if it has errors
            transaction_status = get_writeback_for_validation_reasons(
                payment_data.validation_container
            )
            self.increment(self.Metrics.ERRORED_PAYMENT_COUNT)
            payment_issue_resolution_scenarios = get_issue_resolutions_for_validation_reasons(
                payment_data.validation_container
            )
            for scenario in payment_issue_resolution_scenarios:
                description = None
                if scenario == FAILED_AUTOMATED_VALIDATION_RESOLUTION:
                    description = FAILED_AUTOMATED_VALIDATION_RESOLUTION.get_description_for_issues(
                        payment_data.validation_container.validation_issues
                    )

                create_issue_resolution(
                    payment,
                    scenario,
                    self.get_import_log(),
                    self.db_session,
                    scenario.get_description(description),
                )

        # Employer reimbursements are added to the FINEOS writeback + a report
        elif payments_util.is_payment_transaction_type(
            payment, PaymentTransactionType.CHILD_SUPPORT_PAYMENT
        ):
            end_state = State.CHILD_SUPPORT_READY_FOR_PROCESSING
            self.increment(self.Metrics.CHILD_SUPPORT_COUNT)
        elif payments_util.is_payment_transaction_type(
            payment, PaymentTransactionType.EMPLOYER_REIMBURSEMENT
        ):
            end_state = State.PAYMENT_READY_FOR_ADDRESS_VALIDATION
            self.increment(self.Metrics.EMPLOYER_REIMBURSEMENT_COUNT)

        # Zero dollar payments are added to the FINEOS writeback + a report
        elif payments_util.is_payment_transaction_type(payment, PaymentTransactionType.ZERO_DOLLAR):
            end_state = State.DELEGATED_PAYMENT_PROCESSED_ZERO_PAYMENT
            transaction_status = FineosWritebackTransactionStatus.PROCESSED
            self.increment(self.Metrics.ZERO_DOLLAR_PAYMENT_COUNT)

        # Overpayments are added to to the FINEOS writeback + a report
        elif payments_util.is_payment_transaction_type(
            payment, *OVERPAYMENT_PAYMENT_TRANSACTION_TYPES
        ):
            end_state = State.DELEGATED_PAYMENT_PROCESSED_OVERPAYMENT
            transaction_status = FineosWritebackTransactionStatus.PROCESSED
            self.increment(self.Metrics.OVERPAYMENT_COUNT)

        # Overpayment repayments are added to to the FINEOS writeback + a report
        elif payments_util.is_payment_transaction_type(
            payment, *OVERPAYMENT_REPAYMENT_PAYMENT_TRANSACTION_TYPES
        ):
            end_state = State.DELEGATED_PAYMENT_PROCESSED_OVERPAYMENT
            transaction_status = FineosWritebackTransactionStatus.PROCESSED
            self.increment(self.Metrics.OVERPAYMENT_REPAYMENT_COUNT)

        # Cancellations are added to the FINEOS writeback + a report
        elif payments_util.is_payment_transaction_type(
            payment, PaymentTransactionType.CANCELLATION
        ):
            end_state = State.DELEGATED_PAYMENT_PROCESSED_CANCELLATION
            transaction_status = FineosWritebackTransactionStatus.PROCESSED
            self.increment(self.Metrics.CANCELLATION_COUNT)

        # set status FEDERAL_WITHHOLDING_READY_FOR_PROCESSING
        elif payments_util.is_payment_transaction_type(
            payment, PaymentTransactionType.FEDERAL_TAX_WITHHOLDING
        ):
            end_state = State.FEDERAL_WITHHOLDING_READY_FOR_PROCESSING
            self.increment(self.Metrics.FEDERAL_WITHHOLDING_PAYMENT_COUNT)

        # set status  STATE_WITHHOLDING_READY_FOR_PROCESSING
        elif payments_util.is_payment_transaction_type(
            payment, PaymentTransactionType.STATE_TAX_WITHHOLDING
        ):
            end_state = State.STATE_WITHHOLDING_READY_FOR_PROCESSING
            self.increment(self.Metrics.STATE_WITHHOLDING_PAYMENT_COUNT)

        else:
            end_state = State.PAYMENT_READY_FOR_ADDRESS_VALIDATION
            self.increment(self.Metrics.STANDARD_VALID_PAYMENT_COUNT)

        if transaction_status:
            self._manage_pei_writeback_state(payment, transaction_status, payment_data)

        state_log_util.create_finished_state_log(
            end_state=end_state,
            outcome=state_log_util.build_outcome(message, payment_data.validation_container),
            associated_model=payment,
            db_session=self.db_session,
        )

        extra = payments_util.get_traceable_payment_details(payment, end_state)

        # Keep track of stale payments
        earliest_matching_payment = payments_util.get_earliest_matching_payment(
            self.db_session, payment_data.c_value, payment_data.i_value
        )
        if earliest_matching_payment:
            extra["days_since_payment_first_seen"] = (
                datetime.utcnow().date() - earliest_matching_payment.created_at.date()
            ).days + 1
            extra["day_payment_first_seen"] = str(earliest_matching_payment.created_at.date())

        logger.info(
            "After consuming extracts and performing initial validation, payment added to state",
            extra=extra,
        )
        # For the payments that failed validation, log their reason codes
        # and field names so that we can collect metrics on the most common error types
        for reason, field_name in payment_data.validation_container.get_reasons_with_field_names():
            # Replaced each iteration
            extra["validation_reason"] = str(reason)
            extra["field_name"] = field_name
            logger.info("Payment failed validation", extra=extra)

    def _manage_pei_writeback_state(
        self,
        payment: Payment,
        transaction_status: LkFineosWritebackTransactionStatus,
        payment_data: PaymentData,
    ) -> None:
        """If the payment had any validation issues, we want to writeback to FINEOS
        so that the particular error can be shown in the UI.

        Note that some of these states also mark the payment as
        Active (they only end up in extracts when PendingActive)
        This is deliberate as some payments need to be marked as Active
        so they can be fixed and reissued (an extracted payment can't be modified)
        """

        message = (
            f"Payment queued for PEI writeback {transaction_status.transaction_status_description}"
        )

        stage_payment_fineos_writeback(
            payment=payment,
            writeback_transaction_status=transaction_status,
            db_session=self.db_session,
            import_log_id=self.get_import_log_id(),
        )
        logger.info(message, extra=payment_data.get_traceable_details())

    def get_payment_detail_records_for_ci(
        self, c_value: str, i_value: str, reference_file: ReferenceFile
    ) -> List[FineosExtractVpeiPaymentDetails]:
        # We expect multiple payment detail records
        # joined on the C/I value. Each of these represents
        # a pay period within a payment (although most payments will have exactly 1)
        payment_details_records = (
            self.db_session.query(FineosExtractVpeiPaymentDetails)
            .filter(
                FineosExtractVpeiPaymentDetails.peclassid == c_value,
                FineosExtractVpeiPaymentDetails.peindexid == i_value,
                FineosExtractVpeiPaymentDetails.reference_file_id
                == reference_file.reference_file_id,
            )
            .all()
        )
        self.increment(self.Metrics.PAYMENT_DETAILS_RECORD_COUNT, len(payment_details_records))

        return payment_details_records

    def get_payment_line_records_for_ci(
        self, c_value: str, i_value: str, reference_file: ReferenceFile
    ) -> List[FineosExtractVpeiPaymentLine]:
        # We expect multiple payment line records for each payment
        # joined on the C/I value. Each of these represents
        # a specific amount+type that makes up the payment
        # eg. $500 for base benefit, or -$50 removed for tax
        payment_line_records = (
            self.db_session.query(FineosExtractVpeiPaymentLine)
            .filter(
                FineosExtractVpeiPaymentLine.c_pymnteif_paymentlines == c_value,
                FineosExtractVpeiPaymentLine.i_pymnteif_paymentlines == i_value,
                FineosExtractVpeiPaymentLine.reference_file_id == reference_file.reference_file_id,
            )
            .all()
        )
        self.increment(self.Metrics.PAYMENT_LINE_RECORD_COUNT, len(payment_line_records))

        return payment_line_records

    def get_claim_detail_record_for_ci(
        self, c_value: str, i_value: str, reference_file: ReferenceFile
    ) -> Optional[FineosExtractVpeiClaimDetails]:
        # We expect only one claim details record
        # joined on the C/I value
        claim_details_records = (
            self.db_session.query(FineosExtractVpeiClaimDetails)
            .filter(
                FineosExtractVpeiClaimDetails.peclassid == c_value,
                FineosExtractVpeiClaimDetails.peindexid == i_value,
                FineosExtractVpeiClaimDetails.reference_file_id == reference_file.reference_file_id,
            )
            .all()
        )
        self.increment(self.Metrics.CLAIM_DETAILS_RECORD_COUNT, len(claim_details_records))

        claim_details_record = None
        if len(claim_details_records) > 0:
            claim_details_record = claim_details_records[0]
            # From testing and validation, we shouldn't ever get more than
            # a single claim details record. If we do, log an error, but continue
            if len(claim_details_records) > 1:
                # If you are seeing this error, please investigate and check with FINEOS
                # We should also verify that there is nothing different between the claim details records
                logger.error(
                    "Payment with C/I value %s/%s has multiple claim details records present.",
                    c_value,
                    i_value,
                )
                self.increment(self.Metrics.MULTIPLE_CLAIM_DETAILS_ERROR_COUNT)

        return claim_details_record

    def get_requested_absence_record_for_claim_detail(
        self,
        claim_details_record: Optional[FineosExtractVpeiClaimDetails],
        latest_claimant_extract_reference_file: ReferenceFile,
    ) -> Optional[FineosExtractVbiRequestedAbsence]:
        # To get the requested absence record, we need
        # to use a claim detail record to know the leave request ID.
        requested_absence_record = None
        if claim_details_record:
            leave_request_id = claim_details_record.leaverequesti

            # Realistically, we can get more than one of these
            # as multiple absence periods within a claim can cause
            # multiple records. However, the only fields that differ
            # are the absence period start/end dates (which we do not use)
            # Fetch all of them, we'll filter it to one and log a message
            # just in case it ever matters.
            requested_absence_records = (
                self.db_session.query(FineosExtractVbiRequestedAbsence)
                .filter(
                    FineosExtractVbiRequestedAbsence.leaverequest_id == leave_request_id,
                    FineosExtractVbiRequestedAbsence.reference_file_id
                    == latest_claimant_extract_reference_file.reference_file_id,
                )
                .all()
            )
            self.increment(
                self.Metrics.REQUESTED_ABSENCE_RECORD_COUNT, len(requested_absence_records)
            )
            if len(requested_absence_records) > 0:
                if len(requested_absence_records) > 1:
                    extra = {
                        "leave_request_id": leave_request_id,
                        "vpei_claim_details_id": claim_details_record.vpei_claim_details_id,
                    }
                    logger.warning(
                        "Found more than one requested absence record for claim details",
                        extra=extra,
                    )
                requested_absence_record = requested_absence_records[0]

        return requested_absence_record

    def process_payment_record(
        self,
        raw_payment_record: FineosExtractVpei,
        reference_file: ReferenceFile,
        latest_claimant_extract_reference_file: ReferenceFile,
    ) -> None:
        self.increment(self.Metrics.PROCESSED_PAYMENT_COUNT)

        try:
            c_value, i_value = cast(str, raw_payment_record.c), cast(str, raw_payment_record.i)

            start_time = datetime.now()

            # Fetch the records associated with the payment from other tables
            # We fetch all records even when we expect only one. The PaymentData
            # validation will handle cases where that's unexpected

            payment_details_records = self.get_payment_detail_records_for_ci(
                c_value, i_value, reference_file
            )
            payment_line_records = self.get_payment_line_records_for_ci(
                c_value, i_value, reference_file
            )
            end_time = datetime.now()
            duration = end_time - start_time
            logger.info(
                "finished getting payment details and lines",
                extra={
                    "c_value": c_value,
                    "i_value": i_value,
                    "duration": duration.total_seconds(),
                },
            )
            start_time = datetime.now()
            claim_details_record = self.get_claim_detail_record_for_ci(
                c_value, i_value, reference_file
            )
            requested_absence_record = self.get_requested_absence_record_for_claim_detail(
                claim_details_record, latest_claimant_extract_reference_file
            )
            end_time = datetime.now()
            duration = end_time - start_time
            logger.info(
                "finished getting claim and absence record ",
                extra={
                    "c_value": c_value,
                    "i_value": i_value,
                    "duration": duration.total_seconds(),
                },
            )

            start_time = datetime.now()
            # Construct a payment data object for easier organization of the many params
            payment_data = PaymentData(
                c_value=c_value,
                i_value=i_value,
                pei_record=raw_payment_record,
                payment_details=payment_details_records,
                payment_lines=payment_line_records,
                claim_details=claim_details_record,
                requested_absence_record=requested_absence_record,
                count_incrementer=self.increment,
            )
            end_time = datetime.now()
            duration = end_time - start_time
            logger.info(
                "PaymentData object created",
                extra={
                    "c_value": c_value,
                    "i_value": i_value,
                    "duration": duration.total_seconds(),
                },
            )

            logger.info(
                "Processing extract data for payment record",
                extra=payment_data.get_traceable_details(),
            )

            payment = self.process_payment_data_record(payment_data, reference_file)

            start_time = datetime.now()
            # Create and finish the state log. If there were any issues, this'll set the
            # record to an error state which'll send out a report to address it, otherwise
            # it will move onto the next step in processing
            self._setup_state_log(payment, payment_data)
            end_time = datetime.now()
            duration = end_time - start_time
            logger.info(
                "Finished setup_state_log",
                extra={
                    "c_value": c_value,
                    "i_value": i_value,
                    "duration": duration.total_seconds(),
                },
            )

        except Exception:
            # An exception during processing would indicate
            # either a bug or a scenario that we believe invalidates
            # an entire file and warrants investigating
            logger.exception(
                "An error occurred while processing payment for CI: %s, %s", c_value, i_value
            )
            raise

        return None

    def process_records(self) -> None:
        # Grab the latest payment extract reference file
        reference_file = payments_util.get_latest_reference_file_or_raise(
            self.db_session, ReferenceFileType.FINEOS_PAYMENT_EXTRACT
        )

        if reference_file.processed_import_log_id:
            logger.warning(
                "Already processed the most recent extracts for %s in import run %s",
                reference_file.file_location,
                reference_file.processed_import_log_id,
            )
            return

        self.set_metrics({self.Metrics.EXTRACT_PATH: reference_file.file_location})

        # We also want the latest claimant extract reference
        # file as we'll need it for the requested absence file
        # that we are going to lookup. Don't care if it's processed
        # already as it's just a lookup file
        latest_claimant_extract_reference_file = payments_util.get_latest_reference_file_or_raise(
            self.db_session, ReferenceFileType.FINEOS_CLAIMANT_EXTRACT
        )

        self.set_metrics(
            {
                self.Metrics.VBI_REQUESTED_ABSENCE_EXTRACT_PATH: latest_claimant_extract_reference_file.file_location
            }
        )

        start_time = datetime.now()
        raw_payment_records = (
            self.db_session.query(FineosExtractVpei)
            .filter(FineosExtractVpei.reference_file_id == reference_file.reference_file_id)
            .all()
        )
        end_time = datetime.now()
        duration = end_time - start_time
        logger.info(
            "Fetched payment records from FineosExtractVpei",
            extra={
                "duration": duration.total_seconds(),
            },
        )
        start_time = datetime.now()
        for raw_payment_record in raw_payment_records:
            self.increment(self.Metrics.PEI_RECORD_COUNT)
            self.process_payment_record(
                raw_payment_record,
                reference_file,
                latest_claimant_extract_reference_file,
            )

        end_time = datetime.now()
        duration = end_time - start_time
        logger.info(
            "Processed payment records",
            extra={
                "duration": duration.total_seconds(),
            },
        )
        # Mark the reference file as processed so we don't process
        # the data again if we run before the next FINEOS extracts are generated
        reference_file.processed_import_log_id = self.get_import_log_id()
