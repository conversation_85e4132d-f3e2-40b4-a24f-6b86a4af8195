import enum
from typing import Dict, List, Optional

from pydantic import Field

import massgov.pfml.api.util.state_log_util as state_log_util
import massgov.pfml.delegated_payments.delegated_payments_util as payments_util
import massgov.pfml.util.logging as logging
from massgov.pfml import db
from massgov.pfml.db.lookup_data.employees import PaymentTransactionType
from massgov.pfml.db.lookup_data.state import Flow, State
from massgov.pfml.db.models.employees import Payment, PaymentDetails, StateLog
from massgov.pfml.db.models.import_log import ImportLog
from massgov.pfml.db.models.payments import (
    LinkSplitPayment,
    LkFineosWritebackTransactionStatus,
    PaymentLine,
)
from massgov.pfml.db.models.state import LkState
from massgov.pfml.delegated_payments.util.fineos_writeback_util import (
    get_latest_writeback_for_payment,
    stage_payment_fineos_writeback,
)
from massgov.pfml.util.batch.step import Step
from massgov.pfml.util.pydantic import PydanticBaseSettings

logger = logging.get_logger(__package__)


class Config(PydanticBaseSettings):
    enable_payment_line_matching: bool = Field(
        False, description="Enable payment line matching for delegated payments"
    )


class RelatedPaymentsProcessingStep(Step):
    """
    https://lwd.atlassian.net/wiki/spaces/API/pages/2365947917/Related+Payment+Processing+Step
    """

    class Metrics(str, enum.Enum):
        FEDERAL_WITHHOLDING_RECORD_COUNT = "federal_withholding_record_count"
        STATE_WITHHOLDING_RECORD_COUNT = "state_withholding_record_count"
        EMPLOYER_REIMBURSEMENT_RECORD_COUNT = "employer_reimbursement_record_count"
        STANDARD_PAYMENT_RECORD_COUNT = "standard_payment_record_count"
        CHILD_SUPPORT_RECORD_COUNT = "child_support_record_count"
        ORPHANED_PAYMENT_NO_PARENTS = "orphaned_payment_no_parents"
        ORPHANED_PAYMENT_MULTIPLE_PARENTS = "orphaned_payment_multiple_parents"
        MATCHED_ORPHANED_PAYMENTS_NO_PARENTS = "matched_orphaned_payments_no_parents"
        MATCHED_ORPHANED_PAYMENTS_YESTERDAYS_PARENTS = (
            "matched_orphaned_payments_yesterdays_parents"
        )
        MATCHED_ORPHANED_PAYMENTS_MULTIPLE_PARENTS = "matched_orphaned_payments_multiple_parents"

    # End state when we have multiple primary payments
    multiple_primary_states: Dict[int, LkState] = {
        PaymentTransactionType.EMPLOYER_REIMBURSEMENT.payment_transaction_type_id: State.EMPLOYER_REIMBURSEMENT_ERROR,
        PaymentTransactionType.STATE_TAX_WITHHOLDING.payment_transaction_type_id: State.STATE_WITHHOLDING_ORPHANED_PENDING_AUDIT,
        PaymentTransactionType.FEDERAL_TAX_WITHHOLDING.payment_transaction_type_id: State.FEDERAL_WITHHOLDING_ORPHANED_PENDING_AUDIT,
        PaymentTransactionType.CHILD_SUPPORT_PAYMENT.payment_transaction_type_id: State.CHILD_SUPPORT_ORPHANED_PENDING_AUDIT,
    }

    # End state when we have no primary payments
    primary_not_found_states: Dict[int, LkState] = {
        PaymentTransactionType.EMPLOYER_REIMBURSEMENT.payment_transaction_type_id: State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_AUDIT_REPORT,
        PaymentTransactionType.STATE_TAX_WITHHOLDING.payment_transaction_type_id: State.STATE_WITHHOLDING_ORPHANED_PENDING_AUDIT,
        PaymentTransactionType.FEDERAL_TAX_WITHHOLDING.payment_transaction_type_id: State.FEDERAL_WITHHOLDING_ORPHANED_PENDING_AUDIT,
        PaymentTransactionType.CHILD_SUPPORT_PAYMENT.payment_transaction_type_id: State.CHILD_SUPPORT_ORPHANED_PENDING_AUDIT,
    }

    # End state when we have primary payment in errored state
    primary_in_error_states: Dict[int, LkState] = {
        PaymentTransactionType.EMPLOYER_REIMBURSEMENT.payment_transaction_type_id: State.DELEGATED_PAYMENT_CASCADED_ERROR,
        PaymentTransactionType.STATE_TAX_WITHHOLDING.payment_transaction_type_id: State.STATE_WITHHOLDING_ERROR,
        PaymentTransactionType.FEDERAL_TAX_WITHHOLDING.payment_transaction_type_id: State.FEDERAL_WITHHOLDING_ERROR,
        PaymentTransactionType.CHILD_SUPPORT_PAYMENT.payment_transaction_type_id: State.DELEGATED_PAYMENT_CASCADED_ERROR,
    }

    LIST_OF_RELATED_TRANSACTION_TYPE_IDS = [
        PaymentTransactionType.EMPLOYER_REIMBURSEMENT.payment_transaction_type_id,
    ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.config = Config()

    def run_step(self) -> None:
        """Top-level function that calls all the other functions in this file in order"""
        logger.info("Processing related payment processing step")

        self.sync_primary_to_related_payments()
        self.sync_related_payments_to_primary()

    def sync_primary_to_related_payments(self) -> None:
        logger.info("Processing primary to related payment processing")

        standard_payments: List[Payment] = self._get_standard_payments(self.db_session)

        for payment in standard_payments:
            extra = payments_util.get_traceable_payment_details(payment)
            logger.info("Processing standard payment in related payment processor", extra=extra)
            self.increment(self.Metrics.STANDARD_PAYMENT_RECORD_COUNT)
            if payment.claim is None:
                raise Exception("Claim not found for standard payment id: %s ", payment.payment_id)

            related_payment_records: List[Payment] = (
                self.db_session.query(Payment)
                .filter(Payment.claim_id == payment.claim_id)
                .filter(Payment.period_start_date >= payment.period_start_date)
                .filter(Payment.period_end_date <= payment.period_end_date)
                .filter(
                    Payment.payment_transaction_type_id.in_(
                        self.LIST_OF_RELATED_TRANSACTION_TYPE_IDS
                    )
                )
                .filter(
                    Payment.fineos_extract_import_log_id == payment.fineos_extract_import_log_id
                )
                .all()
            )

            # Otherwise we will have one or more employer reimbursement payments
            # if it is employer reimbursement payment get the state of the payment
            # if the employer reimbursement state is in error state , set the primary payment state to error and create writeback.
            for related_payment in related_payment_records:
                if (
                    related_payment.payment_transaction_type_id
                    != PaymentTransactionType.EMPLOYER_REIMBURSEMENT.payment_transaction_type_id
                ):
                    continue
                related_payment_state_log = state_log_util.get_latest_state_log_in_flow(
                    related_payment, Flow.DELEGATED_PAYMENT, self.db_session
                )
                if related_payment_state_log is None:
                    raise Exception(
                        "State log record not found for the related payment id: %s",
                        related_payment.payment_id,
                    )
                if (
                    related_payment_state_log.end_state_id
                    != State.EMPLOYER_REIMBURSEMENT_READY_FOR_PROCESSING.state_id
                ):
                    end_state = State.DELEGATED_PAYMENT_CASCADED_ERROR

                    transaction_status: Optional[LkFineosWritebackTransactionStatus] = (
                        self._get_payment_writeback_transaction_status(related_payment)
                    )

                    message = (
                        "Employer reimbursement failed validation, need to wait for it to be fixed."
                    )
                    state_log_util.create_finished_state_log(
                        end_state=end_state,
                        outcome=state_log_util.build_outcome(message),
                        associated_model=payment,
                        db_session=self.db_session,
                    )
                    logger.info(
                        "Payment added to state %s",
                        end_state.state_description,
                        extra=extra,
                    )

                    related_payment_log_details = payments_util.get_traceable_payment_details(
                        related_payment
                    )

                    if transaction_status:
                        stage_payment_fineos_writeback(
                            payment=payment,
                            writeback_transaction_status=transaction_status,
                            db_session=self.db_session,
                            import_log_id=self.get_import_log_id(),
                        )
                        logger.info(
                            "Primary payment errored because related payment has %s",
                            transaction_status.transaction_status_description,
                            extra=related_payment_log_details,
                        )
                    else:
                        logger.error(
                            "Writeback details not found for the related payment",
                            extra=related_payment_log_details,
                        )

    def _get_standard_payments(self, db_session: db.Session) -> List[Payment]:
        state_logs = state_log_util.get_all_latest_state_logs_in_end_state(
            associated_class=state_log_util.AssociatedClass.PAYMENT,
            end_state=State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_AUDIT_REPORT,
            db_session=db_session,
        )
        return [state_log.payment for state_log in state_logs if state_log.payment]

    def _previous_extract_query(self, related_payment: Payment) -> db.Query:
        return (
            self.db_session.query(ImportLog.import_log_id)
            .where(
                ImportLog.source == "PaymentExtractStep",
                ImportLog.status == "success",
                ImportLog.import_log_id < related_payment.fineos_extract_import_log_id,
            )
            .order_by(ImportLog.import_log_id.desc())
            .limit(1)
        )

    def _related_standard_payments_query(self, related_payment: Payment) -> db.Query:
        return (
            self.db_session.query(Payment)
            .join(PaymentDetails, PaymentDetails.payment_id == Payment.payment_id)
            .join(PaymentLine, PaymentDetails.payment_details_id == PaymentLine.payment_details_id)
            .where(
                Payment.claim_id == related_payment.claim_id,
                PaymentDetails.period_start_date == related_payment.period_start_date,
                PaymentDetails.period_end_date == related_payment.period_end_date,
                PaymentLine.amount == -related_payment.amount,
                Payment.payment_transaction_type_id
                == PaymentTransactionType.STANDARD.payment_transaction_type_id,
            )
        )

    def _compare_standard_payments(
        self, payment: Payment, old_list: List[Payment], new_list: List[Payment]
    ) -> None:
        """
        Compares two lists of Payment objects (old_list and new_list) and logs differences.
        """
        old_ids = {payment.payment_id for payment in old_list}
        new_ids = {payment.payment_id for payment in new_list}

        if old_ids == new_ids:
            logger.info(
                "payments list matched",
                extra={
                    "count": len(old_ids),
                    "related_payment_id": payment.payment_id,
                    "standard_payments": list(old_ids),
                },
            )
            return

        logger.info(
            "payments list does not match",
            extra={
                "count": len(old_ids),
                "related_payment_id": payment.payment_id,
                "old_ids": list(old_ids),
                "new_ids": list(new_ids),
            },
        )

    def sync_related_payments_to_primary(self) -> None:
        # get employer reimbursement and withholding payment records
        related_payments: List[Payment] = self._get_related_payments()

        if not related_payments:
            logger.info("No related payment records found.")
            return
        for payment in related_payments:

            if payment.claim is None:
                raise Exception("Claim not found for related payment id: %s ", payment.payment_id)

            old_primary_payment_records: List[Payment] = (
                self.db_session.query(Payment)
                .filter(Payment.claim_id == payment.claim_id)
                .filter(Payment.period_start_date <= payment.period_start_date)
                .filter(Payment.period_end_date >= payment.period_end_date)
                .filter(
                    Payment.payment_transaction_type_id
                    == PaymentTransactionType.STANDARD.payment_transaction_type_id
                )
                .filter(
                    Payment.fineos_extract_import_log_id == payment.fineos_extract_import_log_id
                )
                .all()
            )

            todays_primary_payment_records: List[Payment] = (
                self._related_standard_payments_query(payment)
                .where(Payment.fineos_extract_import_log_id == payment.fineos_extract_import_log_id)
                .all()
            )

            self._compare_standard_payments(
                payment, old_primary_payment_records, todays_primary_payment_records
            )

            transaction_type_id = (
                payment.payment_transaction_type_id
                if payment.payment_transaction_type_id is not None
                else 0
            )
            payment_log_details = payments_util.get_traceable_payment_details(payment)

            primary_payment_records: List[Payment] = (
                todays_primary_payment_records
                if self.config.enable_payment_line_matching
                else old_primary_payment_records
            )

            if len(primary_payment_records) > 1:
                self.increment(self.Metrics.ORPHANED_PAYMENT_MULTIPLE_PARENTS)
                if len(todays_primary_payment_records) == 1:
                    self.increment(self.Metrics.MATCHED_ORPHANED_PAYMENTS_MULTIPLE_PARENTS)

                logger.info(
                    "Duplicate primary records exist for related payment %s",
                    payment.claim.fineos_absence_id,
                    extra=payment_log_details,
                )
                # set end state
                end_state = self.multiple_primary_states[transaction_type_id]
                message = "Duplicate records found for the related payment."

                state_log_util.create_finished_state_log(
                    end_state=end_state,
                    outcome=state_log_util.build_outcome(message),
                    associated_model=payment,
                    db_session=self.db_session,
                )
                logger.info(
                    "Payment added to state %s",
                    end_state.state_description,
                    extra=payments_util.get_traceable_payment_details(payment, end_state),
                )
                message = "Duplicate primary payment records found for the related payment record."
                # do we have to do audit
            elif len(primary_payment_records) == 0:
                self.increment(self.Metrics.ORPHANED_PAYMENT_NO_PARENTS)
                if len(todays_primary_payment_records) == 1:
                    self.increment(self.Metrics.MATCHED_ORPHANED_PAYMENTS_NO_PARENTS)
                # check for payments in the previous extract
                yesterdays_primary_payment_records: List[Payment] = (
                    self._related_standard_payments_query(payment)
                    .where(
                        Payment.fineos_extract_import_log_id.in_(
                            self._previous_extract_query(payment).subquery().select()
                        )
                    )
                    .all()
                )
                if len(yesterdays_primary_payment_records) == 1:
                    self.increment(self.Metrics.MATCHED_ORPHANED_PAYMENTS_YESTERDAYS_PARENTS)
                    parent_payment_state_log: Optional[StateLog] = (
                        state_log_util.get_latest_state_log_in_flow(
                            yesterdays_primary_payment_records[0],
                            Flow.DELEGATED_PAYMENT,
                            self.db_session,
                        )
                    )
                    logger.info(
                        "Orphan Payment matched one record",
                        extra={
                            "orphan_payment_id": payment.payment_id,
                            "parent_payment_id": yesterdays_primary_payment_records[0].payment_id,
                            "parent_payment_state.end_state_id": (
                                parent_payment_state_log.end_state_id
                                if parent_payment_state_log
                                else None
                            ),
                            "parent_payment_state.state_description": (
                                parent_payment_state_log.end_state.state_description
                                if parent_payment_state_log and parent_payment_state_log.end_state
                                else None
                            ),
                        },
                    )
                    if (
                        self.config.enable_payment_line_matching
                        and parent_payment_state_log is not None
                    ):
                        if parent_payment_state_log.end_state_id in (
                            State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_ERROR_REPORT.state_id,
                            State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_REJECT_REPORT.state_id,
                            State.PAYMENT_FAILED_ADDRESS_VALIDATION.state_id,
                        ):
                            state_log_util.create_finished_state_log(
                                end_state=State.MAIN_PAYMENT_PREVIOUSLY_REJECTED,
                                outcome=state_log_util.build_outcome(
                                    "Primary payment in error state"
                                ),
                                associated_model=payment,
                                db_session=self.db_session,
                            )
                        else:
                            state_log_util.create_finished_state_log(
                                end_state=State.MAIN_PAYMENT_PREVIOUSLY_PAID,
                                outcome=state_log_util.build_outcome(
                                    "Primary payment in paid state"
                                ),
                                associated_model=payment,
                                db_session=self.db_session,
                            )
                        continue

                elif len(yesterdays_primary_payment_records) > 1:
                    logger.info(
                        "Orphan Payment matched multiple records",
                        extra={
                            "orphan_payment_id": payment.payment_id,
                            "parent_payment_ids": [
                                payment.payment_id for payment in yesterdays_primary_payment_records
                            ],
                        },
                    )
                else:
                    logger.info(
                        "Orphan Payment no match found",
                        extra={
                            "orphan_payment_id": payment.payment_id,
                        },
                    )
                logger.info(
                    "No primary payment record exists for related payment %s",
                    payment.claim.fineos_absence_id,
                    extra=payment_log_details,
                )

                # set correct state
                end_state = self.primary_not_found_states[transaction_type_id]

                message = "No primary payment found for the related payment record."

                state_log_util.create_finished_state_log(
                    end_state=end_state,
                    outcome=state_log_util.build_outcome(message),
                    associated_model=payment,
                    db_session=self.db_session,
                )
                logger.info(
                    "Payment added to state %s",
                    end_state.state_description,
                    extra=payments_util.get_traceable_payment_details(payment, end_state),
                )
            else:
                primary_payment_record = primary_payment_records[0].payment_id
                if primary_payment_record == "":
                    raise Exception(
                        f"Primary payment id not found for related payment id: {payment.payment_id}"
                    )

                link_payment = (
                    self.db_session.query(LinkSplitPayment)
                    .filter(
                        LinkSplitPayment.payment_id == primary_payment_record,
                        LinkSplitPayment.related_payment_id == payment.payment_id,
                    )
                    .one_or_none()
                )
                if not link_payment:
                    link_payment = LinkSplitPayment(
                        payment_id=primary_payment_record, related_payment_id=payment.payment_id
                    )
                    self.db_session.add(link_payment)

                logger.info(
                    "Added related payment to link_payment: Primary payment id %s , Related Payment Id %s",
                    primary_payment_record,
                    payment.payment_id,
                    extra=payment_log_details,
                )

                #  If primary payment has any validation error set related payment state to error
                payment_state_log: Optional[StateLog] = state_log_util.get_latest_state_log_in_flow(
                    primary_payment_records[0], Flow.DELEGATED_PAYMENT, self.db_session
                )
                if payment_state_log is None:
                    raise Exception(
                        "State log record not found for the primary payment id: %s",
                        primary_payment_records[0].payment_id,
                    )
                if (
                    payment_state_log.end_state_id
                    != State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_AUDIT_REPORT.state_id
                ):

                    end_state = self.primary_in_error_states[transaction_type_id]
                    outcome = state_log_util.build_outcome("Primary payment has an error")

                    state_log_util.create_finished_state_log(
                        associated_model=payment,
                        end_state=end_state,
                        outcome=outcome,
                        db_session=self.db_session,
                    )
                    logger.info(
                        "Payment added to state %s",
                        end_state.state_description,
                        extra=payments_util.get_traceable_payment_details(payment, end_state),
                    )
                    # Get the writeback status of the standard payment
                    # Cascade standard writeback status to employer reimbursement payment
                    transaction_status: Optional[LkFineosWritebackTransactionStatus] = (
                        self._get_payment_writeback_transaction_status(primary_payment_records[0])
                    )
                    if transaction_status:
                        message = "Employer reimbursement record error due to an issue with the primary payment."
                        stage_payment_fineos_writeback(
                            payment=payment,
                            writeback_transaction_status=transaction_status,
                            db_session=self.db_session,
                            import_log_id=self.get_import_log_id(),
                        )
                    else:
                        primary_payment_log_details = payments_util.get_traceable_payment_details(
                            primary_payment_records[0]
                        )
                        logger.info(
                            "Writeback details not found for the primary payment",
                            extra=primary_payment_log_details,
                        )

    def _get_payment_writeback_transaction_status(
        self, payment: Payment
    ) -> Optional[LkFineosWritebackTransactionStatus]:
        writeback_details = get_latest_writeback_for_payment(payment, self.db_session)

        if writeback_details is None:
            return None

        return writeback_details.transaction_status

    def _get_related_payments(self) -> List[Payment]:
        """this method appends fedral, state withholding and employer reimbursement payment records"""
        federal_withholding_payments = self._get_payments_for_federal_withholding(self.db_session)
        state_withholding_payments = self._get_payments_for_state_withholding(self.db_session)
        employer_reimbursement_payments = self._get_employer_reimbursement_payment_records(
            self.db_session
        )

        payment_container = []
        for payment in federal_withholding_payments:
            self.increment(self.Metrics.FEDERAL_WITHHOLDING_RECORD_COUNT)
            payment_container.append(payment)

        for payment in state_withholding_payments:
            self.increment(self.Metrics.STATE_WITHHOLDING_RECORD_COUNT)
            payment_container.append(payment)

        for payment in employer_reimbursement_payments:
            self.increment(self.Metrics.EMPLOYER_REIMBURSEMENT_RECORD_COUNT)
            payment_container.append(payment)

        child_support_obligation_payments = self._get_payments_for_child_support_obligation(
            self.db_session
        )
        for payment in child_support_obligation_payments:
            self.increment(self.Metrics.CHILD_SUPPORT_RECORD_COUNT)
            payment_container.append(payment)

        return payment_container

    def _get_employer_reimbursement_payment_records(self, db_session: db.Session) -> List[Payment]:
        state_logs = state_log_util.get_all_latest_state_logs_in_end_state(
            associated_class=state_log_util.AssociatedClass.PAYMENT,
            end_state=State.EMPLOYER_REIMBURSEMENT_READY_FOR_PROCESSING,
            db_session=db_session,
        )
        return [state_log.payment for state_log in state_logs if state_log.payment]

    def _get_payments_for_federal_withholding(self, db_session: db.Session) -> List[Payment]:
        state_logs = state_log_util.get_all_latest_state_logs_in_end_state(
            associated_class=state_log_util.AssociatedClass.PAYMENT,
            end_state=State.FEDERAL_WITHHOLDING_READY_FOR_PROCESSING,
            db_session=db_session,
        )
        return [state_log.payment for state_log in state_logs if state_log.payment]

    def _get_payments_for_state_withholding(self, db_session: db.Session) -> List[Payment]:
        state_logs = state_log_util.get_all_latest_state_logs_in_end_state(
            associated_class=state_log_util.AssociatedClass.PAYMENT,
            end_state=State.STATE_WITHHOLDING_READY_FOR_PROCESSING,
            db_session=db_session,
        )
        return [state_log.payment for state_log in state_logs if state_log.payment]

    def _get_payments_for_child_support_obligation(self, db_session: db.Session) -> List[Payment]:
        state_logs = state_log_util.get_all_latest_state_logs_in_end_state(
            associated_class=state_log_util.AssociatedClass.PAYMENT,
            end_state=State.CHILD_SUPPORT_READY_FOR_PROCESSING,
            db_session=db_session,
        )
        return [state_log.payment for state_log in state_logs if state_log.payment]
