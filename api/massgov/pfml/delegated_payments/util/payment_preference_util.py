from typing import Optional
from uuid import UUID

import massgov.pfml.db as db
import massgov.pfml.util.logging as logging
from massgov.pfml.db.lookup_data.employees import BankAccountType, PaymentMethod
from massgov.pfml.db.models.employees import Employee, TaxIdentifier
from massgov.pfml.db.models.fineos_web_id import FINEOSWebIdExt
from massgov.pfml.fineos.client import AbstractFINEOSClient
from massgov.pfml.fineos.models.customer_api import (
    AccountTypeRequest,
    EditAccountDetailCommand,
    EditPaymentPreferenceCommand,
    PaymentMethodRequest,
    PaymentPreferenceResource,
)

logger = logging.get_logger(__name__)


def get_fineos_web_id(db_session: db.Session, employee_id: UUID) -> Optional[str]:
    fineos_web_id = (
        db_session.query(FINEOSWebIdExt.fineos_web_id)
        .join(
            TaxIdentifier,
            TaxIdentifier.tax_identifier == FINEOSWebIdExt.employee_tax_identifier,
        )
        .join(Employee, Employee.tax_identifier_id == TaxIdentifier.tax_identifier_id)
        .filter(Employee.employee_id == employee_id)
        .first()
    )

    if fineos_web_id:
        return fineos_web_id[0]  # fineos_web_id is a tuple with a single value
    else:
        logger.info(f"No FINEOS Web ID found for employee {employee_id}")
        return None


def find_default_payment_preference(
    fineos_client: AbstractFINEOSClient, fineos_web_id: str
) -> Optional[PaymentPreferenceResource]:
    payment_preferences = fineos_client.get_customer_payment_preference(fineos_web_id)

    if payment_preferences and payment_preferences.elements:
        for payment_preference in payment_preferences.elements:
            if payment_preference.default:
                return payment_preference

    return None


def create_payment_preference_account_details(
    db_session: db.Session, fineos_client: AbstractFINEOSClient, employee: Employee
) -> None:

    employee_name = f"{employee.fineos_employee_first_name} {employee.fineos_employee_last_name}"

    fineos_web_id = get_fineos_web_id(db_session, employee.employee_id)

    if fineos_web_id:
        logger.info(
            f"Updating payment preference for employee {employee.employee_id} in Fineos.  Creating account details."
        )
        fineos_payment_preference = find_default_payment_preference(fineos_client, fineos_web_id)

        if fineos_payment_preference and fineos_payment_preference.id:
            edit_payment_preference = EditPaymentPreferenceCommand(
                paymentMethod=PaymentMethodRequest(
                    name=PaymentMethod.PREPAID_CARD.payment_method_description,
                ),
                accountDetail=EditAccountDetailCommand(
                    accountType=AccountTypeRequest(
                        name=BankAccountType.CHECKING.bank_account_type_description
                    ),
                    accountName=employee_name,
                ),
            )

            try:
                response = fineos_client.update_customer_payment_preference(
                    user_id=fineos_web_id,
                    payment_preference_id=fineos_payment_preference.id,
                    payment_preference=edit_payment_preference,
                )
            except Exception as error:
                logger.warning(
                    f"Failed to update Fineos payment preference for employee {employee.employee_id} in Fineos, error: {error}"
                )

            else:
                if response and response.accountDetail:
                    logger.info(
                        f"Successfully updated Fineos payment preference for employee {employee.employee_id}"
                    )

                else:
                    logger.warning(
                        f"Failed to update Fineos payment preference for employee {employee.employee_id}"
                    )
        else:
            logger.warning(
                f"No default payment preference found for employee {employee.employee_id} in fineos, skipping"
            )
    else:
        logger.warning(f"No FINEOS Web ID found for employee {employee.employee_id}, skipping")
