from typing import Any, Dict, Optional
from uuid import UUID

from sqlalchemy import and_

import massgov.pfml.db as db
import massgov.pfml.util.logging as logging
from massgov.pfml.db.lookup_data.geo import GeoState
from massgov.pfml.db.models.employees import Address, Employee, Employee<PERSON><PERSON><PERSON>, ExperianAddressPair
from massgov.pfml.util.strings import none_if_empty

logger = logging.get_logger(__name__)


def get_experian_address_pair(
    db_session: db.Session, employee: Employee, address: Address
) -> Optional[ExperianAddressPair]:

    # PFML stores blank address lines as empty string.  Other parts of the system may store this as null.
    # We are coverting any nulls to empty strings here to ensure that does not cause a mismatch.
    experian_address_pair = (
        db_session.query(ExperianAddressPair)
        .join(EmployeeAddress, ExperianAddressPair.fineos_address_id == EmployeeAddress.address_id)
        .join(Address, Address.address_id == EmployeeAddress.address_id)
        .filter(
            and_(
                EmployeeAddress.employee_id == employee.employee_id,
                Address.address_line_one == address.address_line_one,
                Address.address_line_two == address.address_line_two,
                Address.city == address.city,
                Address.geo_state_id == address.geo_state_id,
                Address.zip_code == address.zip_code,
            )
        )
        .order_by(ExperianAddressPair.experian_address_id)
        .first()
    )

    return experian_address_pair


def load_address(
    line1: Optional[str],
    line2: Optional[str],
    city: Optional[str],
    state: Optional[str],
    postal: Optional[str],
) -> Address:

    state_id = None

    try:
        state_id = GeoState.get_id(state)
    except KeyError as error:
        message = f"{state} is not a valid state. Error {error}."
        logger.info(message)

    address = Address(
        address_line_one=none_if_empty(line1),
        address_line_two=none_if_empty(line2),
        city=none_if_empty(city),
        geo_state_id=state_id,
        zip_code=none_if_empty(postal),
    )

    return address


def create_experian_address_pair(db_session: db.Session, address: Address) -> ExperianAddressPair:
    experian_address_pair = ExperianAddressPair(
        fineos_address=address,
    )
    db_session.add(experian_address_pair)

    logger.info("Created experian address pair.")

    return experian_address_pair


def create_employee_address(
    db_session: db.Session, employee_id: UUID, address: Address
) -> EmployeeAddress:
    # Create Employee Address
    employee_address = EmployeeAddress(
        employee_id=employee_id,
        address=address,
    )
    db_session.add(employee_address)

    logger.info("Created employee address.")

    return employee_address


def upsert_address(
    db_session: db.Session, employee: Employee, address: Address
) -> ExperianAddressPair:

    extra: Dict[str, Any] = {
        "employee.employee_id": employee.employee_id,
        "employee.fineos_customer_number": employee.fineos_customer_number,
    }

    experian_address_pair = get_experian_address_pair(db_session, employee, address)

    if experian_address_pair:
        extra["experian_address_pair.fineos_address_id"] = experian_address_pair.fineos_address_id
        extra["experian_address_pair.experian_address_id"] = (
            experian_address_pair.experian_address_id
        )
        logger.info("Matching address for employee.", extra=extra)
        if experian_address_pair.experian_address_id is None:
            logger.info("Address is not validated.", extra=extra)

        return experian_address_pair
    else:
        # Create Address
        db_session.add(address)

        # Create Employee Address
        create_employee_address(db_session, employee.employee_id, address)

        # Create Experian Address Pair
        experian_address_pair = create_experian_address_pair(db_session, address)

        extra["created_address"] = True
        extra["created_employee_address"] = True
        extra["created_experian_address_pair"] = True
        extra["experian_address_pair.fineos_address_id"] = experian_address_pair.fineos_address_id
        extra["experian_address_pair.experian_address_id"] = (
            experian_address_pair.experian_address_id
        )
        logger.info("Created address for employee.", extra=extra)
        return experian_address_pair
