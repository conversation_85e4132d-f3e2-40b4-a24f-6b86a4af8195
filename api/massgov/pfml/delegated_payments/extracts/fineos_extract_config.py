from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Callable, Dict, List, Optional, Type, Union

import sqlalchemy

import massgov.pfml.delegated_payments.delegated_config as payments_config
from massgov.pfml.db.lookup_data.reference_file_type import ReferenceFileType
from massgov.pfml.db.models.dor import FineosExtractServiceAgreement
from massgov.pfml.db.models.payments import (
    FineosExtractCancelledPayments,
    FineosExtractEmployeeFeed,
    FineosExtractEmployeeFeedDelta,
    FineosExtractPaymentFullSnapshot,
    FineosExtractReplacedPayments,
    FineosExtractVbi1099DataSom,
    FineosExtractVbiBenefitPeriod,
    FineosExtractVbiDocumentDeltaSom,
    FineosExtractVbiDocumentSom,
    FineosExtractVbiEntitlementPeriodSom,
    FineosExtractVbiLeavePlanRequestedAbsence,
    FineosExtractVbiLeaveSummary,
    FineosExtractVbiOverpaymentAssociatedDuesSom,
    FineosExtractVbiOverpaymentCase,
    FineosExtractVbiOverpaymentsActualRecoverySom,
    FineosExtractVbiOverpaymentsAdjustmentsSom,
    FineosExtractVbiOverpaymentSom,
    FineosExtractVbiOverpaymentsRecoveryPlanSom,
    FineosExtractVbiPaidLeaveCaseInfoSom,
    FineosExtractVbiRequestedAbsence,
    FineosExtractVbiRequestedAbsenceSom,
    FineosExtractVbiTaskReportDeltaSom,
    FineosExtractVbiTaskReportSom,
    FineosExtractVPaidLeaveInstruction,
    FineosExtractVPaidLeaveInstructionSom,
    FineosExtractVpei,
    FineosExtractVpeiClaimDetails,
    FineosExtractVpeiPaymentDetails,
    FineosExtractVpeiPaymentLine,
)
from massgov.pfml.db.models.reference_file.reference_file_type import LkReferenceFileType
from massgov.pfml.fineos.tasks import Constants as TaskStatuses

####################
# Constant values
####################

ExtractTable = Union[
    Type[FineosExtractVpei],
    Type[FineosExtractVpeiClaimDetails],
    Type[FineosExtractVpeiPaymentDetails],
    Type[FineosExtractVpeiPaymentLine],
    Type[FineosExtractVbiRequestedAbsenceSom],
    Type[FineosExtractEmployeeFeed],
    Type[FineosExtractEmployeeFeedDelta],
    Type[FineosExtractVbiRequestedAbsence],
    Type[FineosExtractCancelledPayments],
    Type[FineosExtractPaymentFullSnapshot],
    Type[FineosExtractReplacedPayments],
    Type[FineosExtractVbiLeavePlanRequestedAbsence],
    Type[FineosExtractVPaidLeaveInstruction],
    Type[FineosExtractVbi1099DataSom],
    Type[FineosExtractVbiTaskReportSom],
    Type[FineosExtractVbiTaskReportDeltaSom],
    Type[FineosExtractVbiDocumentSom],
    Type[FineosExtractVbiDocumentDeltaSom],
    Type[FineosExtractVbiOverpaymentsActualRecoverySom],
    Type[FineosExtractVbiOverpaymentsAdjustmentsSom],
    Type[FineosExtractVbiOverpaymentsRecoveryPlanSom],
    Type[FineosExtractVbiOverpaymentAssociatedDuesSom],
    Type[FineosExtractVbiOverpaymentSom],
    Type[FineosExtractVbiOverpaymentCase],
    Type[FineosExtractVbiEntitlementPeriodSom],
    Type[FineosExtractVbiPaidLeaveCaseInfoSom],
    Type[FineosExtractVbiLeaveSummary],
    Type[FineosExtractVPaidLeaveInstructionSom],
    Type[FineosExtractVbiBenefitPeriod],
    Type[FineosExtractServiceAgreement],
]

CANCELLED_OR_REPLACED_EXTRACT_FIELD_NAMES = [
    "C",
    "I",
    "STATUSREASON",
    "GROSSAMOUNT",
    "ADDEDBY",
    "ISSUEDATE",
    "CANCELLATIONDATE",
    "TRANSACTIONSTATUSDATE",
    "TRANSACTIONSTATUS",
    "EXTRACTIONDATE",
    "STOCKNUMBER",
    "CLAIMNUMBER",
    "BENEFITCASENUMBER",
]

# The env var corresponding to the
# path we grab the FINEOS extracts from
FINEOS_DATA_EXPORT_PATH = "FINEOS_DATA_EXPORT_PATH"
FINEOS_ADHOC_DATA_EXPORT_PATH = "FINEOS_ADHOC_DATA_EXPORT_PATH"

# The env var corresponding to the
# path we grab the BI tool extracts from.
# This was added to support the overpayments
# backfill task. It was decided that pulling
# large numbers of files from FINEOS was bad
# form. Instead, the BI tool path provides
# an internal archive of overpayment records.
PFML_BI_TOOL_EXTRACT_PATH = "PFML_BI_TOOL_EXTRACT_PATH"

# The expected format for the dates in the CLOSEDDATE column for the VBI Task Report extract
VBI_TASK_DATE_FORMAT = "%Y-%m-%d %H:%M:%S"

####################
# Config container classes
####################


@dataclass(frozen=True, eq=True)
class FineosExtract:
    """
    Container class representing configuration
    regarding a FINEOS extract file and the
    staging table we will write the contents to.
    """

    file_name: str

    table: ExtractTable = field(compare=False, repr=False)

    # Note field names is simply a list of fields we care about. Extracts will
    # contain many additional fields that we do not use. This is the list of
    # fields that is required to be present, otherwise we throw an exception
    # (see validate_columns_present())
    field_names: List[str] = field(compare=False, repr=False)

    # This allows us to specify fields that need to be mapped to a different field
    # in the staging table. This is useful when the field name in the extract does
    # not match the field name in the staging table.
    # usage is fields_to_be_mapped = {"field_name1": "field_name_in_staging_table", ...}
    fields_to_be_mapped: Dict[str, str] = field(compare=False, repr=False, default_factory=dict)

    # Note known_unconfigured_field_names is a list of fields that we do not care about
    # and do not need to be present in the extract. This is useful for fields that are
    # not used in the extract but are present in the file. These fields will be ignored
    # for the purposes of alerting that they are unconfigured.
    known_unconfigured_field_names: List[str] = field(
        compare=False, repr=False, default_factory=list
    )

    # This allows us to optionally specify filters on fields, in which case we
    # will only process rows that match *all* of these filters.
    # usage is field_filters = {"field_name1": ["list_of", "accepted_values"], ...}
    # or alternatively a function can be passed that will get evaluated against the
    # value for that field
    field_filters: Dict[str, Union[List[str], Callable[[str], bool]]] = field(
        default_factory=lambda: {}, compare=False, repr=False
    )

    primary_key: str = field(compare=False, repr=False, init=False)
    is_primary_key_uuid: bool = field(compare=False, repr=False, init=False)

    def __post_init__(self):
        primary_keys = self.table.__table__.primary_key.columns.keys()  # type: ignore
        if len(primary_keys) != 1:
            raise Exception(
                "FINEOS Extract table %s has more than one primary key %s"
                % (self.table.__tablename__, primary_keys)
            )
        # Have to use this roundabout approach to set primary key
        # as dataclass considers it frozen despite the field having
        # compare=False. Note this is how dataclass sets frozen
        # values in the __init__ function.
        object.__setattr__(self, "primary_key", primary_keys[0])
        # In _parse_raw_extract, we need to know if the primary key is a UUID
        # so we can generate it for COPY. We expect the primary key to be the
        # to be an autoincrementing integer, if it is not a UUID.
        object.__setattr__(
            self,
            "is_primary_key_uuid",
            isinstance(
                self.table.__table__.primary_key.columns.values()[0].type,  # type: ignore
                sqlalchemy.dialects.postgresql.UUID,
            ),
        )


@dataclass
class ExtractConfig:
    """
    Container class for grouping a collection
    of FineosExtract records
    """

    extracts: List[FineosExtract]
    reference_file_type: LkReferenceFileType

    # Environment variable for FINEOS extracts
    source_folder_s3_config_key: str
    # Environment variable for max history of extracts to process (oldest)
    max_history_date_config_key: str
    # Environment variable for min history of extracts to process (newest)
    min_history_date_config_key: Optional[str] = None
    # Used to determine if an extract will be validated within FineosExtractStep.
    requires_all_files_exist: bool = True
    # Flag to bypass header validation allowing the process to proceed even if the CSV file does not contain all the expected columns
    bypass_header_validation: bool = False

    def get_extract_file_names(self) -> List[str]:
        return [extract.file_name for extract in self.extracts]

    def get_fineos_extract_source_folder(self, s3_config: payments_config.PaymentsS3Config) -> str:
        return getattr(s3_config, self.source_folder_s3_config_key.lower())

    def get_fineos_max_history_date_str(self) -> str:
        date_config = payments_config.get_date_config()
        return getattr(date_config, self.max_history_date_config_key.lower())

    def get_fineos_min_history_date_str(self) -> Optional[str]:
        date_config = payments_config.get_date_config()
        return (
            getattr(date_config, self.min_history_date_config_key.lower())
            if self.min_history_date_config_key is not None
            else None
        )


####################
# Extract field filters
####################


def is_closed_within_last_day(date_str):
    # If the task is not closed, it will not have a closed date and should pass
    if date_str == "":
        return True

    # Compare dates rather than datetimes in order to avoid inconsistent behaviors
    # from when the extracts get processed
    yesterday = datetime.now().date() - timedelta(days=1)
    closed_date = datetime.strptime(date_str, VBI_TASK_DATE_FORMAT).date()

    return closed_date >= yesterday


def is_not_empty_string(value: str) -> bool:
    return value.strip() != ""


####################
# Extract configurations
####################
@dataclass
class FineosExtractConstants:
    """
    Constant/container class for defining
    the various FINEOS Extract files that
    we care about. These are grouped below
    for processing in the
    """

    requested_absence_field_names = [
        "NOTIFICATION_CASENUMBER",
        "ABSENCE_CASENUMBER",
        "ABSENCE_CASESTATUS",
        "EMPLOYEE_CUSTOMERNO",
        "ORGUNIT_NAME",
        "EMPLOYER_CUSTOMERNO",
        "LEAVEREQUEST_ID",
        "LEAVEREQUEST_EVIDENCERESULTTYPE",
        "ABSENCEREASON_COVERAGE",
        "ABSENCEPERIOD_CLASSID",
        "ABSENCEPERIOD_INDEXID",
        "ABSENCEPERIOD_START",
        "ABSENCEPERIOD_END",
    ]

    # new field for period id (v24 FINEOS upgrade)
    requested_absence_field_names.append("ABSENCEPERIOD_ID")

    # FINEOS Claimant Extract Files
    VBI_REQUESTED_ABSENCE_SOM = FineosExtract(
        file_name="VBI_REQUESTEDABSENCE_SOM.csv",
        table=FineosExtractVbiRequestedAbsenceSom,
        field_names=requested_absence_field_names,
    )

    EMPLOYEE_FEED = FineosExtract(
        file_name="Employee_feed_SOM.csv",
        table=FineosExtractEmployeeFeed,
        field_names=[
            "C",
            "I",
            "DEFPAYMENTPREF",
            "CUSTOMERNO",
            "NATINSNO",
            "DATEOFBIRTH",
            "PAYMENTMETHOD",
            "SORTCODE",
            "ACCOUNTNO",
            "ACCOUNTTYPE",
            "FIRSTNAMES",
            "INITIALS",
            "LASTNAME",
            "EXTMASSID",
            "EXTOUTOFSTATEID",
            "ADDRESS1",
            "ADDRESS4",
            "ADDRESS6",
            "POSTCODE",
        ],
        known_unconfigured_field_names=[
            "EMPLOYEEID",
            "MASTERPLANID",
            "EXTRACE",
            "EXTETHNICITY",
            "BANKINFO_LASTUPPDATEDATE",
            "LASTUPDATE_USER",
            "BANKINFO_UPDATEUSER_C",
            "BANKINFO_UPDATEUSER_I",
            "INTCODE",
            "AREACODE",
            "TELEPHONENO",
        ],
    )
    EMPLOYEE_FEED_DELTA = FineosExtract(
        file_name="Employee_feed_DELTA_SOM.csv",
        table=FineosExtractEmployeeFeedDelta,
        field_names=EMPLOYEE_FEED.field_names,
        known_unconfigured_field_names=EMPLOYEE_FEED.known_unconfigured_field_names,
    )
    VBI_1099DATA_SOM = FineosExtract(
        file_name="VBI_1099DATA_SOM.csv",
        table=FineosExtractVbi1099DataSom,
        field_names=[
            "FIRSTNAMES",
            "LASTNAME",
            "CUSTOMERNO",
            "PACKEDDATA",
            "DOCUMENTTYPE",
            "C",
            "CREATIONDATE",
            "LASTUPDATEDATE",
        ],
    )
    VPEI = FineosExtract(
        file_name="vpei.csv",
        table=FineosExtractVpei,
        field_names=[
            "C",
            "I",
            "PAYEECUSTOMER",
            "PAYEESOCNUMBE",
            "PAYMENTADD1",
            "PAYMENTADD2",
            "PAYMENTADD4",
            "PAYMENTADD6",
            "PAYMENTPOSTCO",
            "PAYMENTMETHOD",
            "PAYMENTDATE",
            "AMOUNT_MONAMT",
            "PAYEEBANKSORT",
            "PAYEEACCOUNTN",
            "PAYEEACCOUNTT",
            "EVENTTYPE",
            "PAYEEIDENTIFI",
            "PAYEEFULLNAME",
            "EVENTREASON",
            "AMALGAMATIONC",
            "PAYMENTTYPE",
        ],
        known_unconfigured_field_names=[
            "ADMINISTRATIO",
            "SERVICELEVEL",
            "DISBURSEMENTF",
            "PERCENTAGENON",
        ],
    )

    PAYMENT_DETAILS = FineosExtract(
        file_name="vpeipaymentdetails.csv",
        table=FineosExtractVpeiPaymentDetails,
        field_names=[
            "C",
            "I",
            "PECLASSID",
            "PEINDEXID",
            "PAYMENTSTARTP",
            "PAYMENTENDPER",
            "BALANCINGAMOU_MONAMT",
            "BUSINESSNETBE_MONAMT",
        ],
        known_unconfigured_field_names=["PAYPERIODENDD", "PAYPERIODSTAR"],
    )

    PAYMENT_LINE = FineosExtract(
        file_name="vpeipaymentline.csv",
        table=FineosExtractVpeiPaymentLine,
        field_names=[
            "C",
            "I",
            "AMOUNT_MONAMT",
            "LINETYPE",
            "PAYMENTDETAILCLASSID",
            "PAYMENTDETAILINDEXID",
            "C_PYMNTEIF_PAYMENTLINES",
            "I_PYMNTEIF_PAYMENTLINES",
        ],
        known_unconfigured_field_names=["ORIGINALTAXCA"],
    )

    CLAIM_DETAILS = FineosExtract(
        file_name="vpeiclaimdetails.csv",
        table=FineosExtractVpeiClaimDetails,
        field_names=["PECLASSID", "PEINDEXID", "ABSENCECASENU", "LEAVEREQUESTI"],
    )

    # Note this is the one used in the payment extract
    # do not confuse it with the similar _SOM one in the claimant extract
    VBI_REQUESTED_ABSENCE = FineosExtract(
        file_name="VBI_REQUESTEDABSENCE.csv",
        table=FineosExtractVbiRequestedAbsence,
        field_names=[
            "ABSENCEPERIOD_CLASSID",
            "ABSENCEPERIOD_INDEXID",
            "ABSENCEPERIOD_START",
            "ABSENCEPERIOD_END",
            "ABSENCEPERIOD_TYPE",
            "ABSENCEREASON_COVERAGE",
            "ABSENCEREASON_NAME",
            "ABSENCEREASON_QUALIFIER1",
            "ABSENCEREASON_QUALIFIER2",
            "ABSENCE_CASECREATIONDATE",
            "ABSENCE_CASENUMBER",
            "ABSENCE_CASESTATUS",
            "ABSENCE_INTAKESOURCE",
            "EMPLOYEE_CUSTOMERNO",
            "EMPLOYER_CUSTOMERNO",
            "LEAVEREQUEST_DECISION",
            "LEAVEREQUEST_ID",
            "NOTIFICATION_CASENUMBER",
        ],
    )

    PAYMENT_FULL_SNAPSHOT = FineosExtract(
        file_name="Automated-Adhoc-Extract-SOM_PEI_Fullextract.csv",
        table=FineosExtractPaymentFullSnapshot,
        field_names=[
            "AMOUNT_MONAMT",
            "I",
            "EVENTTYPE",
            "EXTRACTIONDAT",
            "PAYMENTDATE",
            "TRANSACTIONST",
        ],
    )

    CANCELLED_PAYMENTS_EXTRACT = FineosExtract(
        file_name="Automated-Adhoc-Extract-SOM_PEI_CancelledRecords.csv",
        table=FineosExtractCancelledPayments,
        field_names=CANCELLED_OR_REPLACED_EXTRACT_FIELD_NAMES,
    )

    REPLACED_PAYMENTS_EXTRACT = FineosExtract(
        file_name="Automated-Adhoc-Extract-SOM_PEI_ReplacedRecords.csv",
        table=FineosExtractReplacedPayments,
        field_names=CANCELLED_OR_REPLACED_EXTRACT_FIELD_NAMES,
    )

    VBI_LEAVE_PLAN_REQUESTED_ABSENCE = FineosExtract(
        file_name="VBI_LEAVEPLANREQUESTEDABSENCE.csv",
        table=FineosExtractVbiLeavePlanRequestedAbsence,
        field_names=[
            "SELECTEDPLAN_CLASSID",
            "SELECTEDPLAN_INDEXID",
            "SELECTEDPLAN_LASTUPDATEDATE",
            "SELECTEDPLAN_ADJUDICAT_RESULT",
            "SELECTEDPLAN_ADJUDICATION_NOTE",
            "SELECTEDPLAN_UPDATEDBYUSERID",
            "LEAVEPLAN_CLASSID",
            "LEAVEPLAN_INDEXID",
            "LEAVEPLAN_DISPLAYREFERENCE",
            "LEAVEPLAN_SHORTNAME",
            "LEAVEPLAN_LONGNAME",
            "LEAVEPLAN_ALIAS",
            "LEAVEPLAN_LEAVEGROUP",
            "LEAVEPLAN_LEAVECATEGORY",
            "LEAVEPLAN_LEAVETYPE",
            "LEAVEPLAN_STATE",
            "LEAVEPLAN_JOBPROTECTION",
            "LEAVEREQUEST_ID",
        ],
    )

    PAID_LEAVE_INSTRUCTION = FineosExtract(
        file_name="vpaidleaveinstruction.csv",
        table=FineosExtractVPaidLeaveInstruction,
        field_names=[
            "C",
            "I",
            "AVERAGEWEEKLYWAGE_MONAMT",
            "C_SELECTEDLEAVEPLAN",
            "I_SELECTEDLEAVEPLAN",
        ],
    )

    VBI_TASKREPORT_SOM = FineosExtract(
        file_name="VBI_TASKREPORT_SOM.csv",
        table=FineosExtractVbiTaskReportSom,
        field_names=[
            "TASKID",
            "TASKTABLEID",
            "CREATIONDATE",
            "STARTDATE",
            "CLOSEDDATE",
            "ONHOLDUNTILDATE",
            "STATUS",
            "TASKTYPENAME",
            "CASETYPE",
            "NOTIFICATIONNUMBER",
            "CASENUMBER",
            "SUBJECTREFERENCE",
        ],
        field_filters={
            "STATUS": [
                TaskStatuses.VBI_TASK_REPORT_STATUS_OPEN,
                TaskStatuses.VBI_TASK_REPORT_STATUS_CLOSED,
            ],
            "CLOSEDDATE": is_closed_within_last_day,
            "TASKTYPENAME": [
                "Employee Reported Other Income",
                "Escalate Employer Reported Other Income",
                "Escalate employer reported accrued paid leave (PTO)",
                "Employee reported accrued paid leave (PTO)",
                "Employee Reported Other Leave",
                "Fraud Report Received",
                "Escalate Employer Reported Fraud",
                "Max Weekly Benefits Exceeded",
                "Agency Reported Additional Income",
                "Exempt Employer",
                "Invalid Payment Waiting Week",
                "Invalid Payment Leave Dates Change",
                "Invalid Payment Pay Adjustment",
                "Invalid Payment Name Mismatch",
                "Invalid Payment Paid Date",
                "Payment Audit Error",
                "Leave Request In Review",
                "Payment Validation Error",
                "Address Validation Error",
                "Bank Processing Error",
                "EFT Account Information Error",
                "Exceeds 26 weeks of total leave",
                "Review and Decision Cancel Time Submitted",
            ],
        },
    )

    VBI_TASKREPORT_DELTA_SOM = FineosExtract(
        file_name="VBI_TASKREPORT_DELTA_SOM.csv",  # TODO: Validate that this is the filename
        table=FineosExtractVbiTaskReportDeltaSom,
        field_names=[
            "TASKID",
            "TASKTABLEID",
            "CREATIONDATE",
            "STARTDATE",
            "CLOSEDDATE",
            "ONHOLDUNTILDATE",
            "STATUS",
            "TASKTYPENAME",
            "CASETYPE",
            "NOTIFICATIONNUMBER",
            "CASENUMBER",
            "SUBJECTREFERENCE",
        ],
        field_filters={
            "STATUS": [
                TaskStatuses.VBI_TASK_REPORT_STATUS_OPEN,
                TaskStatuses.VBI_TASK_REPORT_STATUS_CLOSED,
            ],
            "CLOSEDDATE": is_closed_within_last_day,
            "TASKTYPENAME": [
                "Employee Reported Other Income",
                "Escalate Employer Reported Other Income",
                "Escalate employer reported accrued paid leave (PTO)",
                "Employee reported accrued paid leave (PTO)",
                "Employee Reported Other Leave",
                "Fraud Report Received",
                "Escalate Employer Reported Fraud",
                "Max Weekly Benefits Exceeded",
                "Agency Reported Additional Income",
                "Exempt Employer",
                "Invalid Payment Waiting Week",
                "Invalid Payment Leave Dates Change",
                "Invalid Payment Pay Adjustment",
                "Invalid Payment Name Mismatch",
                "Invalid Payment Paid Date",
                "Payment Audit Error",
                "Leave Request In Review",
                "Payment Validation Error",
                "Address Validation Error",
                "Bank Processing Error",
                "EFT Account Information Error",
                "Exceeds 26 weeks of total leave",
                "Review and Decision Cancel Time Submitted",
            ],
        },
    )

    VBI_DOCUMENT_SOM = FineosExtract(
        file_name="VBI_DOCUMENT_SOM.csv",
        table=FineosExtractVbiDocumentSom,
        field_names=[
            "CASENUMBER",
            "CLASSID",
            "C_OCEMAIL_ATTACHMENTS",
            "C_OCPRTY_DOCUMENTS",
            "CREATEDBYUSERID",
            "CREATIONDATE",
            "DESCRIPTION",
            "DOCUMENTTYPE",
            "INDEXID",
            "I_OCEMAIL_ATTACHMENTS",
            "I_OCPRTY_DOCUMENTS",
            "ISKEYDOCUMENT",
            "LASTUPDATEDATE",
            "PRIVACYTAG",
            "RECEIVEDDATE",
            "STATUS",
            "UPDATEDBYUSERID",
        ],
        known_unconfigured_field_names=["TYPE"],
        field_filters={
            "DOCUMENTTYPE": [
                "Employee Not Found Information",
            ],
            "CASENUMBER": is_not_empty_string,
            "DESCRIPTION": is_not_empty_string,
        },
    )

    VBI_DOCUMENT_DELTA_SOM = FineosExtract(
        file_name="VBI_DOCUMENT_DELTA_SOM.csv",
        table=FineosExtractVbiDocumentDeltaSom,
        field_names=VBI_DOCUMENT_SOM.field_names,
        known_unconfigured_field_names=VBI_DOCUMENT_SOM.known_unconfigured_field_names,
        field_filters=VBI_DOCUMENT_SOM.field_filters,
    )

    # Note that the Fineos extract filename abbreviates 'Entitlement' to 'Entitlemt'
    # We use the fully-spelled-out version in the name of the staging table
    VBI_ENTITLEMTPERIOD_SOM = FineosExtract(
        file_name="VBI_ENTITLEMTPERIOD_SOM.csv",
        table=FineosExtractVbiEntitlementPeriodSom,
        field_names=[
            "PERIODSTARTDATE",
            "PERIODENDDATE",
            "EMPLOYEE_NUMBER",
            "ASSOICATED_LEAVE_PLAN",
            "EMPLOYER_NUMBER",
        ],
    )

    VBI_LEAVESUMMARY: FineosExtract = FineosExtract(
        file_name="VBI_LEAVESUMMARY.csv",
        table=FineosExtractVbiLeaveSummary,
        field_names=[
            "LEAVEREQUEST_CLASSID",
            "LEAVEREQUEST_INDEXID",
            "LEAVEREQUEST_LASTUPDATEDATE",
            "LEAVEREQUEST_ORIGINALREQUEST",
            "LEAVEREQUEST_ID",
            "LEAVEREQUEST_DESCRIPTION",
            "LEAVEREQUEST_DECISION",
            "LEAVEREQUEST_NAME",
            "LEAVEREQUEST_NOTES",
            "LEAVEREQUEST_CHALLENGE_DETAILS",
            "LEAVEREQUEST_CHALLENGE_MADE",
            "LEAVEREQUEST_DIAGNOSIS",
            "LEAVEREQUEST_DI_FIRSTDATE",
            "LEAVEREQUEST_DI_FIRSTDATE_OR",
            "LEAVEREQUEST_EMPLOYER_NOTIFIED",
            "LEAVEREQUEST_ER_NOTIFIEDDATE",
            "LEAVEREQUEST_ER_NOTIFIEDMETHOD",
            "LEAVEREQUEST_APPROVAL_REASON",
            "LEAVEREQUEST_DENIAL_REASON",
            "LEAVEREQUEST_WITHDRAWAL_REASON",
            "LEAVEREQUEST_EVENT_TYPE",
            "LEAVEREQUEST_UPDATEDBYUSERID",
            "NOTIFICATION_CASENUMBER",
            "ABSENCE_CASENUMBER",
            "PRIMARY_RELATIONSHIP_NAME",
            "PRIMARY_RELATIONSHIP_QUAL1",
            "PRIMARY_RELATIONSHIP_QUAL2",
            "PRIMARY_RELATIONSHIP_COVER",
            "SECONDARY_RELATIONSHIP_NAME",
            "SECONDARY_RELATIONSHIP_QUAL1",
            "SECONDARY_RELATIONSHIP_QUAL2",
            "SECONDARY_RELATIONSHIP_COVER",
            "EMPLOYMENT_CLASSID",
            "EMPLOYMENT_INDEXID",
            "HEALTHCARE_PROVIDER_CUSTNO",
        ],
    )

    VBI_BENEFITPERIOD = FineosExtract(
        file_name="VBI_BENEFITPERIOD.csv",
        table=FineosExtractVbiBenefitPeriod,
        field_names=[
            "CLASSID",
            "INDEXID",
            "LASTUPDATEDATE",
            "TOTALPERIODDA",
            "DAYSINPERIOD",
            "PERIODFROMDAT",
            "PERIODTODATE",
            "PERIODTYPE",
            "BENEFITCASENUMBER",
        ],
    )

    VBI_OVERPAYMENTSACTUALRECOVERY_SOM = FineosExtract(
        file_name="VBI_OVERPAYMENTSACTUALRECOVERY_SOM.csv",
        table=FineosExtractVbiOverpaymentsActualRecoverySom,
        field_names=[
            "CLAIMCASENUMBER",
            "BENCASENUMBER",
            "OPCASENUMNER",
            "STATUS",
            "STATUSREASON",
            "DATEADDED",
            "DATEOFRECOVERY",
            "AMOUNTOFRECOVERY",
            "CHECKNAME",
            "CHECKNUMBER",
            "RECOVERYMETHOD",
            "OVERPAYMENTC",
            "OVERPAYMENTI",
            "OVERPAYMENTCASEC",
            "OVERPAYMENTCASEI",
            "C_OCCASE_THECASE",
            "I_OCCASE_THECASE",
            "PEI_C",
            "PEI_I",
        ],
    )

    VBI_OVERPAYMENTSADJUSTMENTS_SOM = FineosExtract(
        file_name="VBI_OVERPAYMENTSADJUSTMENTS_SOM.csv",
        table=FineosExtractVbiOverpaymentsAdjustmentsSom,
        field_names=[
            "CLAIMCASENUMBER",
            "BENCASENUMBER",
            "OPCASENUMNER",
            "ADJUSTMENTNAME",
            "ADJUSTMENTAMOUNT",
            "AGREEMENTDATE",
            "DESCRIPTION",
            "RECEIPTPAYMENTSTATUS",
            "TOCALCULATE",
            "OVERPAYMENTCASEC",
            "OVERPAYMENTCASEI",
        ],
    )

    VBI_OVERPAYMENTSRECOVERYPLAN_SOM = FineosExtract(
        file_name="VBI_OVERPAYMENTSRECOVERYPLAN_SOM.csv",
        table=FineosExtractVbiOverpaymentsRecoveryPlanSom,
        field_names=[
            "CLAIMCASENUMBER",
            "BENCASENUMBER",
            "OPCASENUMNER",
            "AGREEMENTDATE",
            "TYPE",
            "AMOUNTTOSUBMIT",
            "PAYMENTFREQUE",
            "AMTPERFREQ",
            "SUBMITBYDATE",
            "STARTDATE",
            "STOPDATE",
            "ENDDATE",
            "OVERPAYMENTC",
            "OVERPAYMENTI",
            "OVERPAYMENTCASEC",
            "OVERPAYMENTCASEI",
            "C_OCCASE_THECASE",
            "I_OCCASE_THECASE",
            "OSUSER_I",
            "OSUSER",
        ],
    )

    VBI_OVERPAYMENT_ASSOCIATEDDUES_SOM = FineosExtract(
        file_name="VBI_OVERPAYMENT_ASSOCIATEDDUES_SOM.csv",
        table=FineosExtractVbiOverpaymentAssociatedDuesSom,
        field_names=[
            "ABSENCE_CASENUMBER",
            "PAIDLEAVE_CASENUMBER",
            "BENEFIT_CASENUMBER",
            "OVERPAYMENT_CASENUMBER",
            "CUSTOMERNO",
            "PERIOD_STARTDATE",
            "PERIOD_ENDDATE",
            "NAME",
            "AMOUNT_MONAMT",
            "TO_CALCULATE",
        ],
        fields_to_be_mapped={
            "PERIODENDDATE": "PERIOD_ENDDATE",
            "PERIODSTARTDA": "PERIOD_STARTDATE",
            "OPCASENUMNER": "OVERPAYMENT_CASENUMBER",
            "BENCASENUMBER": "BENEFIT_CASENUMBER",
            "CLAIMCASENUMBER": "PAIDLEAVE_CASENUMBER",
        },
    )

    VBI_OVERPAYMENT_SOM = FineosExtract(
        file_name="VBI_OVERPAYMENT_SOM.csv",
        table=FineosExtractVbiOverpaymentSom,
        field_names=[
            "OVERPAYMENTCASE_CLASSID",
            "OVERPAYMENTCASE_INDEXID",
            "NOTIFICATION_NUMBER",
            "ABSENCE_CASENUMBER",
            "ABSENCE_PAIDLEAVE_CASENUMBER",
            "ABSENCE_PAIDLEAVE_BENEFIT",
            "OVERPAYMENT_CASENUMBER",
            "CUSTOMERNO",
            "CREATIONDATE",
            "NET_BENEFIT_OVERPAYMENT_AMOUNT",
            "OVERPAYMENT_ADJUSTMENT_AMOUNT",
            "AGREED_RECOVERY_AMOUNT",
            "RECOVERED_TO_DATE_AMOUNT",
            "OUTSTANDING_AMOUNT",
        ],
        fields_to_be_mapped={
            "CASENUMBER": "OVERPAYMENT_CASENUMBER",
            "DATECREATED": "CREATIONDATE",  # mapping for stage and performance environment
            "DATE_OF_CALCULATION": "CREATIONDATE",  # mapping for stage and performance environment
        },
    )

    VBI_OVERPAYMENTCASE = FineosExtract(
        file_name="VBI_OVERPAYMENTCASE.csv",
        table=FineosExtractVbiOverpaymentCase,
        field_names=[
            "CLASSID",
            "INDEXID",
            "LASTUPDATEDATE",
            "C_PYMNTEIF_PAYMENTEVENTI",
            "I_PYMNTEIF_PAYMENTEVENTI",
            "CASENUMBER",
        ],
    )

    V_PAIDLEAVEINSTRUCTION_SOM = FineosExtract(
        file_name="VPAIDLEAVEINSTRUCTION_SOM.csv",
        table=FineosExtractVPaidLeaveInstructionSom,
        field_names=[
            "CASENUMBER",
            "SITFITOPTIN",
            "C_PAIDLEAVEINSTR",
            "I_PAIDLEAVEINSTR",
            "LASTUPDATEDATE",
            "AVERAGEDAYSWORKED",
            "AVERAGEWEEKLYWAGE_MONAMT",
            "EXTAWWPART2_MONAMT",
            "BENEFITWAITINGPERIOD",
            "C_SELECTEDLEAVEPLAN",
            "I_SELECTEDLEAVEPLAN",
        ],
    )

    VBI_PAIDLEAVECASEINFO_SOM = FineosExtract(
        file_name="VBI_PAIDLEAVECASEINFO_SOM.csv",
        table=FineosExtractVbiPaidLeaveCaseInfoSom,
        field_names=[
            "NOTIFICATION_CASENUMBER",
            "ABSENCECASE_ID",
            "ABSENCE_CASENUMBER",
            "PAIDLEAVE_CASENUMBER",
            "ABSENCE_CASESTATUS",
            "EMPLOYEE_CUSTOMERNO",
            "EMPLOYER_CUSTOMERNO",
            "LEAVEREQUEST_EVIDENCERESULTTYPE",
            "ABSENCEREASON_COVERAGE",
            "ABSENCEPERIOD_START",
            "ABSENCEPERIOD_END",
            "LR_DECREAON",
            "EVIDENCE",
            "LEAVEREQUEST_EVIDENCERECEIPT",
            "LEAVEREQUEST_DENIALREASON",
            "LEAVEREQUEST_ID",
            "MIXEDDECISION",
        ],
    )

    FINEOS_SERVICE_AGREEMENTS = FineosExtract(
        # This file is generated from an ad hoc query to the fineos system
        file_name="FINEOS_SERVICE_AGREEMENTS_ADHOC.csv",
        table=FineosExtractServiceAgreement,
        field_names=[
            "EMPLOYER_CUSTOMERNO",
            "SERVICEAGREEMENT_NO",
            "VERSION",
            "EFFECTIVEDATE",
            "ENDDATE",
            "LEAVEPLAN",
            "VERSION_DATE",
            "LAST_UPDATE_DATE",
            "STATUS",
            "ABSENCE_MANAGEMENT_SERVICING",
        ],
    )


##########################
# Claimant Extract Files
##########################
CLAIMANT_EXTRACT_FILES = [
    FineosExtractConstants.VBI_REQUESTED_ABSENCE_SOM,
    FineosExtractConstants.EMPLOYEE_FEED,
    FineosExtractConstants.EMPLOYEE_FEED_DELTA,
    FineosExtractConstants.VBI_REQUESTED_ABSENCE,
    FineosExtractConstants.VBI_LEAVESUMMARY,
]

CLAIMANT_EXTRACT_FILE_NAMES = [extract_file.file_name for extract_file in CLAIMANT_EXTRACT_FILES]
CLAIMANT_EXTRACT_CONFIG = ExtractConfig(
    extracts=CLAIMANT_EXTRACT_FILES,
    reference_file_type=ReferenceFileType.FINEOS_CLAIMANT_EXTRACT,
    source_folder_s3_config_key=FINEOS_DATA_EXPORT_PATH,
    max_history_date_config_key="FINEOS_CLAIMANT_EXTRACT_MAX_HISTORY_DATE",
)

##########################
# Payment Extract Files
##########################
PAYMENT_EXTRACT_FILES = [
    FineosExtractConstants.VPEI,
    FineosExtractConstants.CLAIM_DETAILS,
    FineosExtractConstants.PAYMENT_DETAILS,
    FineosExtractConstants.PAYMENT_LINE,
]
PAYMENT_EXTRACT_FILE_NAMES = [extract_file.file_name for extract_file in PAYMENT_EXTRACT_FILES]
PAYMENT_EXTRACT_CONFIG = ExtractConfig(
    extracts=PAYMENT_EXTRACT_FILES,
    reference_file_type=ReferenceFileType.FINEOS_PAYMENT_EXTRACT,
    source_folder_s3_config_key=FINEOS_DATA_EXPORT_PATH,
    max_history_date_config_key="FINEOS_PAYMENT_EXTRACT_MAX_HISTORY_DATE",
)

##########################
# Payment Reconciliation Extract Files
##########################
PAYMENT_RECONCILIATION_EXTRACT_FILES = [
    FineosExtractConstants.PAYMENT_FULL_SNAPSHOT,
    FineosExtractConstants.CANCELLED_PAYMENTS_EXTRACT,
    FineosExtractConstants.REPLACED_PAYMENTS_EXTRACT,
]
PAYMENT_RECONCILIATION_EXTRACT_FILE_NAMES = [
    extract_file.file_name for extract_file in PAYMENT_RECONCILIATION_EXTRACT_FILES
]
PAYMENT_RECONCILIATION_EXTRACT_CONFIG = ExtractConfig(
    extracts=PAYMENT_RECONCILIATION_EXTRACT_FILES,
    reference_file_type=ReferenceFileType.FINEOS_PAYMENT_RECONCILIATION_EXTRACT,
    source_folder_s3_config_key=FINEOS_ADHOC_DATA_EXPORT_PATH,
    max_history_date_config_key="FINEOS_PAYMENT_RECONCILIATION_EXTRACT_MAX_HISTORY_DATE",
)

##########################
# IAWW Extract Files
##########################
IAWW_EXTRACT_FILES = [
    FineosExtractConstants.VBI_LEAVE_PLAN_REQUESTED_ABSENCE,
    FineosExtractConstants.PAID_LEAVE_INSTRUCTION,
    FineosExtractConstants.VBI_PAIDLEAVECASEINFO_SOM,
    FineosExtractConstants.V_PAIDLEAVEINSTRUCTION_SOM,
    FineosExtractConstants.VBI_BENEFITPERIOD,
]
IAWW_EXTRACT_FILES_NAMES = [extract_file.file_name for extract_file in IAWW_EXTRACT_FILES]
IAWW_EXTRACT_CONFIG = ExtractConfig(
    extracts=IAWW_EXTRACT_FILES,
    reference_file_type=ReferenceFileType.FINEOS_IAWW_EXTRACT,
    source_folder_s3_config_key=FINEOS_DATA_EXPORT_PATH,
    max_history_date_config_key="FINEOS_IAWW_EXTRACT_MAX_HISTORY_DATE",
)

##########################
# 1099 Extract Files
##########################
REQUEST_1099_EXTRACT_FILES = [FineosExtractConstants.VBI_1099DATA_SOM]

REQUEST_1099_EXTRACT_FILES_NAMES = [
    extract_file.file_name for extract_file in REQUEST_1099_EXTRACT_FILES
]
REQUEST_1099_EXTRACT_CONFIG = ExtractConfig(
    extracts=REQUEST_1099_EXTRACT_FILES,
    reference_file_type=ReferenceFileType.FINEOS_1099_DATA_EXTRACT,
    source_folder_s3_config_key=FINEOS_DATA_EXPORT_PATH,
    max_history_date_config_key="FINEOS_1099_DATA_EXTRACT_MAX_HISTORY_DATE",
)

##########################
# VBI Task Report Extract Files
##########################
VBI_TASKREPORT_SOM_EXTRACT_FILES = [FineosExtractConstants.VBI_TASKREPORT_SOM]
VBI_TASKREPORT_SOM_EXTRACT_FILE_NAMES = [
    extract_file.file_name for extract_file in VBI_TASKREPORT_SOM_EXTRACT_FILES
]
VBI_TASKREPORT_SOM_EXTRACT_CONFIG = ExtractConfig(
    extracts=VBI_TASKREPORT_SOM_EXTRACT_FILES,
    reference_file_type=ReferenceFileType.FINEOS_VBI_TASKREPORT_SOM_EXTRACT,
    source_folder_s3_config_key=FINEOS_DATA_EXPORT_PATH,
    max_history_date_config_key="FINEOS_VBI_TASKREPORT_SOM_EXTRACT_MAX_HISTORY_DATE",
)

VBI_TASKREPORT_DELTA_SOM_EXTRACT_FILES = [FineosExtractConstants.VBI_TASKREPORT_DELTA_SOM]
VBI_TASKREPORT_DELTA_SOM_EXTRACT_FILE_NAMES = [
    extract_file.file_name for extract_file in VBI_TASKREPORT_DELTA_SOM_EXTRACT_FILES
]
VBI_TASKREPORT_DELTA_SOM_EXTRACT_CONFIG = ExtractConfig(
    extracts=VBI_TASKREPORT_DELTA_SOM_EXTRACT_FILES,
    reference_file_type=ReferenceFileType.FINEOS_VBI_TASKREPORT_DELTA_SOM_EXTRACT,
    source_folder_s3_config_key=FINEOS_DATA_EXPORT_PATH,
    max_history_date_config_key="FINEOS_VBI_TASKREPORT_DELTA_SOM_EXTRACT_MAX_HISTORY_DATE",
)


##########################
# VBI Document (Delta) Extract Files
##########################
VBI_DOCUMENT_SOM_EXTRACT_FILES = [
    FineosExtractConstants.VBI_DOCUMENT_SOM,
]
VBI_DOCUMENT_SOM_EXTRACT_FILE_NAMES = [
    extract_file.file_name for extract_file in VBI_DOCUMENT_SOM_EXTRACT_FILES
]
VBI_DOCUMENT_SOM_EXTRACT_CONFIG = ExtractConfig(
    extracts=VBI_DOCUMENT_SOM_EXTRACT_FILES,
    reference_file_type=ReferenceFileType.FINEOS_VBI_DOCUMENT_SOM_EXTRACT,
    source_folder_s3_config_key=FINEOS_DATA_EXPORT_PATH,
    max_history_date_config_key="FINEOS_VBI_DOCUMENT_SOM_EXTRACT_MAX_HISTORY_DATE",
)

VBI_DOCUMENT_DELTA_SOM_EXTRACT_FILES = [
    FineosExtractConstants.VBI_DOCUMENT_DELTA_SOM,
]
VBI_DOCUMENT_DELTA_SOM_EXTRACT_FILE_NAMES = [
    extract_file.file_name for extract_file in VBI_DOCUMENT_DELTA_SOM_EXTRACT_FILES
]
VBI_DOCUMENT_DELTA_SOM_EXTRACT_CONFIG = ExtractConfig(
    extracts=VBI_DOCUMENT_DELTA_SOM_EXTRACT_FILES,
    reference_file_type=ReferenceFileType.FINEOS_VBI_DOCUMENT_DELTA_SOM_EXTRACT,
    source_folder_s3_config_key=FINEOS_DATA_EXPORT_PATH,
    max_history_date_config_key="FINEOS_VBI_DOCUMENT_DELTA_SOM_EXTRACT_MAX_HISTORY_DATE",
)

##########################
# VBI Entitlement Period Extract Files
##########################
VBI_ENTITLEMTPERIOD_SOM_EXTRACT_FILES = [FineosExtractConstants.VBI_ENTITLEMTPERIOD_SOM]
VBI_ENTITLEMTPERIOD_SOM_EXTRACT_FILE_NAMES = [
    extract_file.file_name for extract_file in VBI_ENTITLEMTPERIOD_SOM_EXTRACT_FILES
]
VBI_ENTITLEMTPERIOD_SOM_EXTRACT_CONFIG = ExtractConfig(
    extracts=VBI_ENTITLEMTPERIOD_SOM_EXTRACT_FILES,
    reference_file_type=ReferenceFileType.FINEOS_VBI_ENTITLEMTPERIOD_SOM_EXTRACT,
    source_folder_s3_config_key=FINEOS_DATA_EXPORT_PATH,
    max_history_date_config_key="FINEOS_VBI_ENTITLEMTPERIOD_SOM_EXTRACT_MAX_HISTORY_DATE",
)


##########################
# Overpayment Extract Files
##########################
OVERPAYMENTS_EXTRACT_FILES = [
    FineosExtractConstants.VBI_OVERPAYMENTSACTUALRECOVERY_SOM,
    FineosExtractConstants.VBI_OVERPAYMENT_ASSOCIATEDDUES_SOM,
    FineosExtractConstants.VBI_OVERPAYMENT_SOM,
    FineosExtractConstants.VBI_OVERPAYMENTCASE,
    FineosExtractConstants.VBI_OVERPAYMENTSADJUSTMENTS_SOM,
    FineosExtractConstants.VBI_OVERPAYMENTSRECOVERYPLAN_SOM,
]
OVERPAYMENTS_EXTRACT_FILE_NAMES = [
    extract_file.file_name for extract_file in OVERPAYMENTS_EXTRACT_FILES
]
OVERPAYMENT_EXTRACT_CONFIG = ExtractConfig(
    extracts=OVERPAYMENTS_EXTRACT_FILES,
    reference_file_type=ReferenceFileType.FINEOS_OVERPAYMENT_EXTRACT,
    source_folder_s3_config_key=FINEOS_DATA_EXPORT_PATH,
    max_history_date_config_key="FINEOS_OVERPAYMENT_EXTRACT_MAX_HISTORY_DATE",
    requires_all_files_exist=False,
)

####################################
# Overpayment Backfill Extract Files
####################################
OVERPAYMENTS_BACKFILL_EXTRACT_CONFIG = ExtractConfig(
    extracts=OVERPAYMENTS_EXTRACT_FILES,
    reference_file_type=ReferenceFileType.FINEOS_OVERPAYMENT_EXTRACT,
    source_folder_s3_config_key=PFML_BI_TOOL_EXTRACT_PATH,
    max_history_date_config_key="OVERPAYMENTS_BACKFILL_EXTRACT_MAX_HISTORY_DATE",
    min_history_date_config_key="OVERPAYMENTS_BACKFILL_EXTRACT_MIN_HISTORY_DATE",
    requires_all_files_exist=False,
    bypass_header_validation=True,
)

####################################
# Service Agreement Extract Files
####################################
SERVICE_AGREEMENT_EXTRACT_FILES = [
    FineosExtractConstants.FINEOS_SERVICE_AGREEMENTS,
]
SERVICE_AGREEMENT_EXTRACT_FILE_NAMES = [
    extract_file.file_name for extract_file in SERVICE_AGREEMENT_EXTRACT_FILES
]
SERVICE_AGREEMENT_EXTRACT_CONFIG = ExtractConfig(
    extracts=SERVICE_AGREEMENT_EXTRACT_FILES,
    reference_file_type=ReferenceFileType.FINEOS_SERVICE_AGREEMENT_EXTRACT,
    source_folder_s3_config_key=FINEOS_ADHOC_DATA_EXPORT_PATH,
    max_history_date_config_key="FINEOS_SERVICE_AGREEMENT_EXTRACT_MAX_HISTORY_DATE",
    requires_all_files_exist=False,
)

########################################
# All extract configs
########################################
ALL_FINEOS_EXTRACT_CONFIGS: List[ExtractConfig] = [
    PAYMENT_EXTRACT_CONFIG,
    CLAIMANT_EXTRACT_CONFIG,
    PAYMENT_RECONCILIATION_EXTRACT_CONFIG,
    IAWW_EXTRACT_CONFIG,
    REQUEST_1099_EXTRACT_CONFIG,
    VBI_TASKREPORT_SOM_EXTRACT_CONFIG,
    VBI_TASKREPORT_DELTA_SOM_EXTRACT_CONFIG,
    VBI_DOCUMENT_SOM_EXTRACT_CONFIG,
    OVERPAYMENT_EXTRACT_CONFIG,
]
