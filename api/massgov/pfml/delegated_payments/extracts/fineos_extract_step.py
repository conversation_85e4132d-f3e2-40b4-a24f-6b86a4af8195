import csv
import enum
import os
import tempfile
import time
import uuid
from typing import Any, Dict, List, Optional

from psycopg2.errors import QueryCanceled

import massgov.pfml.delegated_payments.delegated_config as payments_config
import massgov.pfml.delegated_payments.delegated_payments_util as payments_util
import massgov.pfml.delegated_payments.extracts.fineos_extract_util as extract_util
import massgov.pfml.features as features
import massgov.pfml.util.files as file_util
import massgov.pfml.util.logging as logging
from massgov.pfml import db
from massgov.pfml.db.lookup_data.reference_file_type import ReferenceFileType
from massgov.pfml.db.models.reference_file.reference_file import ReferenceFile
from massgov.pfml.delegated_payments.extracts.fineos_extract_config import (
    ExtractConfig,
    FineosExtract,
)
from massgov.pfml.util.batch.step import Step
from massgov.pfml.util.collections.dict import make_keys_lowercase
from massgov.pfml.util.csv import download_and_parse_csv

logger = logging.get_logger(__name__)


class ExtractData:
    """
    Container class used by the FineosExtractStep
    to group the various components of a FINEOS
    Extract, and help group all files that are
    processed together from a given FINEOS batch.
    """

    s3_locations: List[str]
    date_str: str
    extract_config: ExtractConfig
    extract_path_mapping: Dict[str, FineosExtract]

    reference_file: ReferenceFile

    def __init__(
        self,
        s3_locations: List[str],
        date_str: str,
        extract_config: ExtractConfig,
        s3_config: payments_config.PaymentsS3Config,
    ):
        self.s3_locations = s3_locations
        self.date_str = date_str
        self.extract_config = extract_config
        self.extract_path_mapping = {}

        for s3_location in s3_locations:
            for extract in extract_config.extracts:
                if s3_location.endswith(extract.file_name):
                    self.extract_path_mapping[s3_location] = extract

        self.reference_file = ReferenceFile(
            file_location=os.path.join(
                s3_config.pfml_fineos_extract_archive_path,
                payments_util.Constants.S3_INBOUND_RECEIVED_DIR,
                self.date_str,
            ),
            reference_file_type_id=extract_config.reference_file_type.reference_file_type_id,
            reference_file_id=uuid.uuid4(),
        )
        logger.info("Intialized extract data: %s", self.reference_file.file_location)

    def validate_all_files_exist(self) -> None:
        if len(self.extract_config.extracts) != len(self.extract_path_mapping):
            expected_file_names = [extract.file_name for extract in self.extract_config.extracts]
            error_msg = (
                f"Expected to find files {expected_file_names}, but found {self.s3_locations}"
            )
            raise Exception(error_msg)


class FineosExtractStep(Step):
    """
    Step for consuming a group of FINEOS extracts,
    and storing them into their corresponding staging
    tables with a shared reference file + import log ID

    The constructor parameter 'should_backfill' when true
    will process all extract date groups. The
    default behavior (false) is that only the most recent
    extract date group is processed.

    https://lwd.atlassian.net/wiki/spaces/API/pages/2366078980/FINEOS+Extract+Step
    """

    extract_config: ExtractConfig
    s3_config: payments_config.PaymentsS3Config
    active_extract_data: Optional[ExtractData]
    active_extract_data_date_str: Optional[str]

    class Metrics(str, enum.Enum):
        FINEOS_PREFIX = "fineos_prefix"
        ARCHIVE_PATH = "archive_path"
        FILE_TYPE = "file_type"
        RECORDS_PROCESSED_COUNT = "records_processed_count"
        RECORDS_FILTERED_OUT_COUNT = "records_filtered_out_count"
        EXTRACT_DATES = "extract_dates"

    def __init__(
        self,
        db_session: db.Session,
        log_entry_db_session: db.Session,
        extract_config: ExtractConfig,
        s3_config: payments_config.PaymentsS3Config,
        should_backfill: bool = False,
    ) -> None:
        super().__init__(
            db_session=db_session,
            log_entry_db_session=log_entry_db_session,
        )
        self.extract_config = extract_config
        self.active_extract_data = None
        self.active_extract_data_date_str = None
        self.should_backfill = should_backfill
        self.s3_config = s3_config

    def get_import_type(self) -> str:
        """Use the reference file type description for an import log type distinction"""
        return self.extract_config.reference_file_type.reference_file_type_description

    def run_step(self) -> None:
        logger.info(
            "Starting processing of %s files",
            self.extract_config.reference_file_type.reference_file_type_description,
        )
        self.set_metrics(
            {
                self.Metrics.FILE_TYPE: self.extract_config.reference_file_type.reference_file_type_description
            }
        )

        self.modify_extract_fields_based_on_fineos_version()
        self.process_extracts()

        logger.info(
            "Successfully consumed FINEOS extract data for %s",
            self.extract_config.reference_file_type.reference_file_type_description,
        )

    def modify_extract_fields_based_on_fineos_version(self) -> None:
        """
        Modify extract field_names based on the FINEOS version.

        If the FINEOS version is not updated (pre-24.8.1), remove the ABSENCEPERIOD_ID column
        from the AbsencePeriod SOM file configuration (it doesn't exist in the pre-24.8.1
        versions of the SOM extract file)

        Note: conditionally removing the column here because we can't access feature flags in
        fineos_extract.config.py to conditionally add the column there.
        """

        # TODO remove with feature flag (https://lwd.atlassian.net/browse/PFMLPB-22515)
        # Check if FINEOS is running version 24 or later
        fineos_is_running_v24 = features.get_config().fineos.is_running_v24
        if not fineos_is_running_v24:

            # find the extract config specific to AbsencePeriod SOM file
            file_name_specific_to_extract_config = "VBI_REQUESTEDABSENCE_SOM.csv"
            extract_config_to_modify = next(
                (
                    extract
                    for extract in self.extract_config.extracts
                    if extract.file_name == file_name_specific_to_extract_config
                ),
                None,
            )

            # this step runs with different config files, our file may not be involved
            if extract_config_to_modify:

                # Remove the ABSENCEPERIOD_ID column if it exists
                column_to_remove = "ABSENCEPERIOD_ID"
                if column_to_remove in extract_config_to_modify.field_names:
                    extract_config_to_modify.field_names.remove(column_to_remove)
                    logger.info(
                        f"Removed '{column_to_remove}' from {file_name_specific_to_extract_config} extract config"
                    )
                else:
                    logger.warning(
                        f"'{column_to_remove}' not found in {file_name_specific_to_extract_config} extract config"
                    )

    def cleanup_on_failure(self) -> None:
        logger.exception(
            "Error processing %s extract files in date_group: %s",
            self.extract_config.reference_file_type.reference_file_type_description,
            self.active_extract_data_date_str,
            extra={"date_group": self.active_extract_data_date_str},
        )
        if self.active_extract_data:
            # Move the files to the error directory
            # Note we don't create the reference file
            # because if it errors again, we don't want to hit
            # an issue with the reference file location unique key
            self.move_files_from_received_to_out_dir(
                self.active_extract_data,
                payments_util.Constants.S3_INBOUND_ERROR_DIR,
                create_reference_file=False,
            )
            self.active_extract_data = None

    def process_extracts(self) -> None:
        data_by_date = self._move_files_from_fineos_to_received()

        # We want to process these in order to the
        # latest record is created last.
        ordered_dates = sorted(data_by_date.keys())

        if len(ordered_dates) > 0:
            latest_date_str = ordered_dates[-1]
            logger.info("latest date: %s", latest_date_str)

        # date_str: Prefix of the extract files, eg. 2022-01-01-12-00-00
        # s3_file_locations: List of full S3 paths to extract files
        for date_str in ordered_dates:
            s3_file_locations = data_by_date.get(date_str, [])

            logger.info(
                "Processing files in date group: %s", date_str, extra={"date_group": date_str}
            )

            extract_data = ExtractData(
                s3_file_locations, date_str, self.extract_config, self.s3_config
            )
            extra = self._get_extra_for_log(
                reference_file=extract_data.reference_file, extract_data=extract_data
            )
            # We keep track of the active extracts
            # in case we need to clean them up on failure
            self.active_extract_data = extract_data
            self.active_extract_data_date_str = date_str

            if self.should_backfill:
                if self.extract_config.requires_all_files_exist:
                    extract_data.validate_all_files_exist()

                # This will process whatever is available.
                self._download_and_index_data(extract_data)
                self.move_files_from_received_to_out_dir(
                    extract_data, payments_util.Constants.S3_INBOUND_PROCESSED_DIR
                )
                logger.info(
                    "Backfilled extract files for %s now in %s",
                    self.extract_config.reference_file_type.reference_file_type_description,
                    extract_data.reference_file.file_location,
                    extra=extra,
                )
            elif date_str == latest_date_str:
                # We'll only validate all files present for the latest
                # extract that we're going to actually store to the DB
                # Allow some extracts like the overpayments extract to
                # not validate. Some, all, or none of the files may
                # appear within the extract group.
                if self.extract_config.requires_all_files_exist:
                    extract_data.validate_all_files_exist()

                self.set_metrics({self.Metrics.FINEOS_PREFIX: date_str})
                self._download_and_index_data(extract_data)
                self.move_files_from_received_to_out_dir(
                    extract_data, payments_util.Constants.S3_INBOUND_PROCESSED_DIR
                )
                logger.info(
                    "Processed extract files for %s now in %s",
                    self.extract_config.reference_file_type.reference_file_type_description,
                    extract_data.reference_file.file_location,
                    extra=extra,
                )
            else:
                # For records that aren't the latest, we move
                # them to a skipped directory to show that we
                # found them, but we aren't going to process them.
                self.move_files_from_received_to_out_dir(
                    extract_data, payments_util.Constants.S3_INBOUND_SKIPPED_DIR
                )
                logger.info(
                    "Successfully skipped extract files in date group: %s",
                    date_str,
                    extra=extra,
                )

            self.active_extract_data = None
            self.active_extract_data_date_str = None

    def _move_files_from_fineos_to_received(self) -> Dict[str, List[str]]:
        # Grabs all relevant extract files from FINEOS and moves
        # them to the received directory for this step to do processing
        # See the FineosExtractFileFetcher class for full details
        data_by_date = extract_util.FineosExtractFileFetcher(
            self.db_session, self.extract_config, self.s3_config
        ).fetch_files_from_fineos()

        extract_dates = ", ".join(data_by_date.keys())
        logger.info("Dates in /received folder: %s", extract_dates)
        self.set_metrics({self.Metrics.EXTRACT_DATES: extract_dates})

        return data_by_date

    def move_files_from_received_to_out_dir(
        self, extract_data: ExtractData, directory_name: str, create_reference_file: bool = True
    ) -> None:
        """
        Move files from /received to the corresponding skipped/processed/errored
        folder as well as update the reference file
        """
        extra = self._get_extra_for_log(
            reference_file=extract_data.reference_file, extract_data=extract_data
        )

        date_group_folder = extract_util.get_date_group_folder_name(
            extract_data.date_str, self.extract_config.reference_file_type
        )
        # Move each file from the received directory to a specific
        # directory for the given extract type in the skipped/processed/errored folder
        # s3://bucket/path/to/received/2020-01-01-11-30-00-file.csv
        # to
        # s3://bucket/path/to/skipped/2020-01-01-11-30-00-payment-extract/2020-01-01-11-30-00-file.csv
        for file_path, extract in extract_data.extract_path_mapping.items():
            new_path = file_path.replace(
                payments_util.Constants.S3_INBOUND_RECEIVED_DIR,
                f"{directory_name}/{date_group_folder}",
            )

            logger.info(
                "Moving %s file from %s to %s", extract.file_name, file_path, new_path, extra=extra
            )
            file_util.rename_file(file_path, new_path)

        # Update the reference file location from the /received folder to
        # the given extract type folder in the skipped/processed/errored folder
        # s3://bucket/path/to/received/
        # to
        # s3://bucket/path/to/skipped/2020-01-01-11-30-00-payment-extract/
        original_file_location = extract_data.reference_file.file_location
        new_file_location = original_file_location.replace(
            payments_util.Constants.S3_INBOUND_RECEIVED_DIR, directory_name
        ).replace(extract_data.date_str, date_group_folder)

        extra = self._get_extra_for_log(
            reference_file=extract_data.reference_file, extract_data=extract_data
        )
        logger.info(
            "Updated reference file location from %s to %s",
            original_file_location,
            new_file_location,
            extra=extra,
        )
        extract_data.reference_file.file_location = new_file_location
        # We don't create reference files when the process
        # errored to avoid the file_location from being duplicated
        # in the event it errors again
        if create_reference_file:
            self.db_session.add(extract_data.reference_file)

        logger.info("Successfully moved files to %s folder", directory_name, extra=extra)
        self.set_metrics({self.Metrics.ARCHIVE_PATH: new_file_location})

    def _download_and_index_data(self, extract_data: ExtractData) -> None:
        self.db_session.add(extract_data.reference_file)
        self.db_session.flush()  # Need to flush so reference file ID is in the DB

        for file_location, extract in extract_data.extract_path_mapping.items():
            logger.info(
                "Storing extract data from %s to %s with reference_file_id %s and import_log_id %s",
                file_location,
                extract.table.__name__,
                extract_data.reference_file.reference_file_id,
                self.get_import_log_id(),
            )
            with tempfile.TemporaryDirectory() as download_directory:
                self._store_file_to_staging_table(
                    extract_data.reference_file, download_directory, file_location, extract
                )

    def _store_file_to_staging_table(
        self,
        reference_file: ReferenceFile,
        download_directory: str,
        file_location: str,
        extract: FineosExtract,
    ) -> None:
        """
        Processes a FINEOS extract and stores it in its staging table
        """
        extra = self._get_extra_for_log(extract, reference_file)

        updated_records = self._parse_raw_extract(
            reference_file, download_directory, file_location, extract
        )
        if not updated_records:
            logger.warning("No unfiltered rows found for extract file", extra=extra)
            return

        # Now that we've iterated over the records,
        # we want to recreate a CSV with the proper columns
        updated_csv_filename = os.path.join(download_directory, f"updated-{extract.file_name}")
        fieldnames = list(updated_records[0].keys())

        logger.info("Creating temporary CSV file with proper DB columns", extra=extra)
        self._write_csv(updated_csv_filename, fieldnames, updated_records)

        logger.info(
            "Running COPY command to load records into staging table from temporary CSV",
            extra=extra,
        )
        self._load_csv_to_table(extract.table.__tablename__, updated_csv_filename, fieldnames)

    def _parse_raw_extract(
        self,
        reference_file: ReferenceFile,
        download_directory: str,
        file_location: str,
        extract: FineosExtract,
    ) -> List[Dict[str, str]]:
        """
        Parse the raw extract file and return a dictionary of records
        - Validates the columns present in the data
        - Does any applicable filtering for the given records
        - Attaches reference file ID, import log ID, and primary key to record
        """
        extra = self._get_extra_for_log(extract, reference_file, file_location=file_location)
        logger.info("Parsing FINEOS extract file", extra=extra)

        csv_reader = download_and_parse_csv(file_location, download_directory)

        unconfigured_columns = []
        updated_records = []
        filtered_record_count = 0
        for i, record in enumerate(csv_reader):
            lower_key_record = make_keys_lowercase(record)

            if i == 0:
                if not self.extract_config.bypass_header_validation:
                    # Verify that the expected columns are present
                    extract_util.validate_columns_present(lower_key_record, extract)

                # Check if there are any fields that don't have a matching
                # column in the table model
                unconfigured_columns = extract_util.get_unconfigured_fineos_columns(
                    lower_key_record, extract.table
                )
                known_unconfigured_columns: List[str] = [
                    column
                    for column in unconfigured_columns
                    if column.upper() not in extract.known_unconfigured_field_names
                ]
                if len(known_unconfigured_columns) > 0:
                    extra["fields"] = ",".join(known_unconfigured_columns)
                    logger.warning("Unconfigured columns in FINEOS extract.", extra=extra)

            if extract.fields_to_be_mapped:
                lower_key_record = extract_util.map_fields(
                    lower_key_record, extract.fields_to_be_mapped
                )

            if extract_util.matches_all_filters(lower_key_record, extract):
                staging_table_record = extract_util.validate_and_transform_staging_table_record(
                    lower_key_record,
                    extract.table,
                    reference_file,
                    self.get_import_log_id(),
                    # These were already logged when we checked the first record earlier,
                    # so we don't need to log them again.
                    ignore_properties=unconfigured_columns,
                )

                if extract.is_primary_key_uuid:
                    # Need to attach the primary key as the copy command
                    # we use to load doesn't auto-generate it.
                    staging_table_record[extract.primary_key] = uuid.uuid4()

                updated_records.append(staging_table_record)
            else:
                filtered_record_count += 1

        # Update overall and file-specific record counts
        self.increment(self.Metrics.RECORDS_PROCESSED_COUNT, len(updated_records))
        extract_processed_key = f"{extract.file_name}_{self.Metrics.RECORDS_PROCESSED_COUNT}"
        self.increment(extract_processed_key, len(updated_records))
        extra[extract_processed_key] = len(updated_records)

        self.increment(self.Metrics.RECORDS_FILTERED_OUT_COUNT, filtered_record_count)
        extract_filtered_key = f"{extract.file_name}_{self.Metrics.RECORDS_FILTERED_OUT_COUNT}"
        self.increment(extract_filtered_key, filtered_record_count)
        extra[extract_filtered_key] = filtered_record_count

        logger.info("Read in data for extract file %s", file_location, extra=extra)

        return updated_records

    def _write_csv(
        self, csv_filepath: str, fieldnames: List[str], records: List[Dict[str, str]]
    ) -> None:
        with file_util.write_file(csv_filepath, mode="w") as csv_file:
            writer = csv.DictWriter(csv_file, fieldnames=fieldnames, quoting=csv.QUOTE_ALL)
            writer.writeheader()
            writer.writerows(records)

    def _load_csv_to_table(
        self, table_name: str, csv_filepath: str, field_names: List[str]
    ) -> None:

        # Load the data into the DB table using the
        # COPY command to read a CSV file from STDIN (a file pointer)
        # See: https://www.postgresql.org/docs/current/sql-copy.html for postgres docs
        # See: https://www.psycopg.org/docs/cursor.html#cursor.copy_expert for documentation
        #      regarding the copy_expert command that allows us to run the copy command.
        with file_util.open_stream(csv_filepath) as csv_file:
            try:
                start_time = time.monotonic()
                cmd = f"COPY {table_name}({','.join(field_names)}) FROM STDIN with (DELIMITER ',', FORMAT CSV, HEADER TRUE)"
                cursor = self.db_session.connection().connection.cursor()
                cursor.copy_expert(cmd, csv_file)
                row_count = cursor.rowcount
                time_to_copy = time.monotonic() - start_time
                # The freezegun 0.x package had no affect on the time.monotonic call.
                # Post freezegun 1.x package upgrade, the time.monotonic call was affected.
                # That led to a divide by zero error because the start time and end time are the same.
                # This divide by zero error check was added for unit tests that use freezegun and execute this logic.
                rate_per_second = row_count / time_to_copy if time_to_copy else 0
                logger.info(
                    "COPY was successful",
                    extra={
                        "duration_in_seconds": time_to_copy,
                        "table_name": table_name,
                        "csv_filepath": csv_filepath,
                        "rows_added": row_count,
                        "rate_per_second": rate_per_second,
                    },
                )
            except QueryCanceled:
                logger.exception(
                    "The COPY command to load the staging table timed out",
                    extra={"table_name": table_name, "csv_filepath": csv_filepath},
                )
                raise RuntimeError(
                    f"The COPY command to load the staging table timed out for extract: {csv_filepath}"
                )

    def _get_extra_for_log(
        self,
        extract: Optional[FineosExtract] = None,
        reference_file: Optional[ReferenceFile] = None,
        extract_data: Optional[ExtractData] = None,
        **kwargs: Any,
    ) -> Dict[str, Any]:
        extra = {}
        if extract:
            extra["extract_file_name"] = extract.file_name
            extra["extract_table"] = extract.table.__name__
        if reference_file:
            extra["reference_file_location"] = reference_file.file_location
            extra["reference_file_id"] = str(reference_file.reference_file_id)
            extra["reference_file_type"] = (
                ReferenceFileType.get_description(reference_file.reference_file_type_id)  # type: ignore
                if reference_file.reference_file_type_id
                else None
            )
        if extract_data:
            extra["date_group"] = extract_data.date_str

        for k, v in kwargs.items():
            extra[k] = v

        return extra
