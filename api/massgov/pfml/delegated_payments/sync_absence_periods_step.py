import time
from collections import defaultdict
from enum import Enum
from itertools import groupby
from operator import attrgetter
from typing import Any, Dict, List, Optional

from sqlalchemy import and_, cast, exists, literal_column, or_, select
from sqlalchemy.orm import Query
from sqlalchemy.sql.functions import coalesce
from sqlalchemy.types import String

import massgov.pfml.delegated_payments.delegated_payments_util as payments_util
import massgov.pfml.util.logging as logging
from massgov.pfml.db.lookup_data.absences import (
    AbsencePeriodType,
    AbsenceReason,
    AbsenceReasonQualifierOne,
    AbsenceReasonQualifierTwo,
    AbsenceStatus,
)
from massgov.pfml.db.lookup_data.employees import LeaveRequestDecision
from massgov.pfml.db.lookup_data.reference_file_type import ReferenceFileType
from massgov.pfml.db.models.absences import AbsencePeriod
from massgov.pfml.db.models.employees import Claim, LeaveRequest
from massgov.pfml.db.models.payments import (
    FineosExtractVbiRequestedAbsence,
    FineosExtractVbiRequestedAbsenceSom,
)
from massgov.pfml.db.models.reference_file.reference_file import ReferenceFile
from massgov.pfml.delegated_payments.absence_period_container import AbsencePeriodContainer
from massgov.pfml.delegated_payments.claimant_extract_metrics import ClaimantExtractMetrics
from massgov.pfml.delegated_payments.delegated_payments_util import (
    get_latest_reference_file_or_raise,
)
from massgov.pfml.util.batch.step import Step
from massgov.pfml.util.datetime import datetime_str_to_date
from massgov.pfml.util.pydantic import PydanticBaseModel

logger = logging.get_logger(__name__)


class RequestedAbsenceForAbsencePeriod(PydanticBaseModel):
    """
    These fields are used to create and update AbsencePeriods in the PFML database.
    We pull these fields from two FINEOS extracts, RequestedAbsence and
    RequestedAbsenceSom, because the RequestedAbsenceSom is missing some
    claims that are needed for the User Not Found flow.
    """

    class Config:
        arbitrary_types_allowed = True
        from_attributes = True

    # Primary keys for rows in each table
    vbi_requested_absence_som_serial_id: Optional[int]
    vbi_requested_absence_serial_id: Optional[int]

    absenceperiod_classid: str
    absenceperiod_indexid: str
    absenceperiod_id: Optional[str]  # new period id
    absence_casenumber: str
    leaverequest_id: str
    absenceperiod_type: Optional[str]
    absenceperiod_start: str
    absenceperiod_end: str
    absencereason_qualifier1: Optional[str]
    absencereason_qualifier2: Optional[str]
    absencereason_name: Optional[str]
    leaverequest_decision: Optional[str]

    # Fields for sanity-checking claim
    employee_customerno: Optional[str]
    employer_customerno: Optional[str]
    orgunit_name: Optional[str]
    absence_casestatus: Optional[str]

    Claim: Optional[Claim]
    LeaveRequest: Optional[LeaveRequest]
    AbsencePeriod: Optional[AbsencePeriod]


class SyncAbsencePeriodsStep(Step):
    extra: Dict[str, Any] = {}  # used for logging

    class Metrics(str, Enum):
        EXTRACT_PATH = "extract_path"
        ABSENCE_PERIOD_CLASS_ID_OR_INDEX_ID_NOT_FOUND_COUNT = (
            "absence_period_class_id_or_index_id_not_found_count"
        )
        ABSENCE_PERIOD_CLAIM_NOT_FOUND_COUNT = "absence_period_claim_not_found_count"
        ABSENCE_PERIOD_CLAIM_MISMATCH = "absence_period_claim_mismatch"
        CLAIMS_PROCESSED_COUNT = "claims_processed_count"
        ABSENCE_PERIOD_LEAVE_REQUEST_MISMATCH = "absence_period_leave_request_mismatch"
        REQUESTED_ABSENCES_FOR_CLAIM_WARNINGS = "requested_absences_for_claim_warnings"

    def get_requested_absence_iterator(self, reference_file: ReferenceFile) -> Query:
        """
        Retrieves a union of requested absences from both the standard and custom extracts,
        filtered by the given reference_file. It includes relevant details and performs necessary
        joins to ensure completeness, using reference_file_id to match records accurately.
        """

        # Query the 'out of the box' extract of requested absences
        requested_absence_table = self.db_session.query(
            FineosExtractVbiRequestedAbsence.absenceperiod_classid.label("absenceperiod_classid"),
            FineosExtractVbiRequestedAbsence.absenceperiod_indexid.label("absenceperiod_indexid"),
        ).filter(
            FineosExtractVbiRequestedAbsence.reference_file_id == reference_file.reference_file_id,
            FineosExtractVbiRequestedAbsence.absence_casestatus
            == AbsenceStatus.CLOSED.absence_status_description,
            FineosExtractVbiRequestedAbsence.absence_intakesource == "Self-Service",
        )

        # Query the custom extract of requested absences
        requested_absence_som_table = self.db_session.query(
            FineosExtractVbiRequestedAbsenceSom.absenceperiod_classid.label(
                "absenceperiod_classid"
            ),
            FineosExtractVbiRequestedAbsenceSom.absenceperiod_indexid.label(
                "absenceperiod_indexid"
            ),
        ).filter(
            FineosExtractVbiRequestedAbsenceSom.reference_file_id
            == reference_file.reference_file_id
        )

        # Create a union of the two sets of requested absences
        requested_absence_union_table = requested_absence_som_table.union(
            requested_absence_table
        ).subquery()

        # Find all of the requested absence records that need to be imported
        query = (
            (
                self.db_session.query(
                    # Find the original union identifier data
                    requested_absence_union_table.c.absenceperiod_classid.label(
                        "absenceperiod_classid"
                    ),
                    requested_absence_union_table.c.absenceperiod_indexid.label(
                        "absenceperiod_indexid"
                    ),
                    FineosExtractVbiRequestedAbsenceSom.absenceperiod_id.label("absenceperiod_id"),
                    # Get both relevant table identifiers
                    FineosExtractVbiRequestedAbsenceSom.vbi_requested_absence_som_serial_id.label(
                        "vbi_requested_absence_som_serial_id"
                    ),
                    FineosExtractVbiRequestedAbsence.vbi_requested_absence_serial_id.label(
                        "vbi_requested_absence_serial_id"
                    ),
                    # Default to using the data coming from the SOM extract,
                    # and fallback to the data from the 'out of the box' extract,
                    # in the few cases where the SOM extract is missing
                    coalesce(
                        FineosExtractVbiRequestedAbsenceSom.absence_casenumber,
                        FineosExtractVbiRequestedAbsence.absence_casenumber,
                    ).label("absence_casenumber"),
                    coalesce(
                        FineosExtractVbiRequestedAbsenceSom.leaverequest_id,
                        FineosExtractVbiRequestedAbsence.leaverequest_id,
                    ).label("leaverequest_id"),
                    coalesce(
                        FineosExtractVbiRequestedAbsenceSom.absenceperiod_start,
                        FineosExtractVbiRequestedAbsence.absenceperiod_start,
                    ).label("absenceperiod_start"),
                    coalesce(
                        FineosExtractVbiRequestedAbsenceSom.absenceperiod_end,
                        FineosExtractVbiRequestedAbsence.absenceperiod_end,
                    ).label("absenceperiod_end"),
                    # Get the optional fields we can only find on the 'out of the box' extract
                    FineosExtractVbiRequestedAbsence.absenceperiod_type.label("absenceperiod_type"),
                    FineosExtractVbiRequestedAbsence.absencereason_qualifier1.label(
                        "absencereason_qualifier1"
                    ),
                    FineosExtractVbiRequestedAbsence.absencereason_qualifier2.label(
                        "absencereason_qualifier2"
                    ),
                    FineosExtractVbiRequestedAbsence.absencereason_name.label("absencereason_name"),
                    FineosExtractVbiRequestedAbsence.leaverequest_decision.label(
                        "leaverequest_decision"
                    ),
                    coalesce(
                        FineosExtractVbiRequestedAbsenceSom.employee_customerno,
                        FineosExtractVbiRequestedAbsence.employee_customerno,
                    ).label("employee_customerno"),
                    coalesce(
                        FineosExtractVbiRequestedAbsenceSom.employer_customerno,
                        FineosExtractVbiRequestedAbsence.employer_customerno,
                    ).label("employer_customerno"),
                    coalesce(
                        FineosExtractVbiRequestedAbsenceSom.absence_casestatus,
                        FineosExtractVbiRequestedAbsence.absence_casestatus,
                    ).label("absence_casestatus"),
                    FineosExtractVbiRequestedAbsenceSom.orgunit_name.label("orgunit_name"),
                )
                .outerjoin(
                    FineosExtractVbiRequestedAbsenceSom,
                    and_(
                        requested_absence_union_table.c.absenceperiod_classid
                        == FineosExtractVbiRequestedAbsenceSom.absenceperiod_classid,
                        requested_absence_union_table.c.absenceperiod_indexid
                        == FineosExtractVbiRequestedAbsenceSom.absenceperiod_indexid,
                        FineosExtractVbiRequestedAbsenceSom.reference_file_id
                        == reference_file.reference_file_id,
                    ),
                )
                .outerjoin(
                    FineosExtractVbiRequestedAbsence,
                    and_(
                        requested_absence_union_table.c.absenceperiod_classid
                        == FineosExtractVbiRequestedAbsence.absenceperiod_classid,
                        requested_absence_union_table.c.absenceperiod_indexid
                        == FineosExtractVbiRequestedAbsence.absenceperiod_indexid,
                        FineosExtractVbiRequestedAbsence.reference_file_id
                        == reference_file.reference_file_id,
                    ),
                )
            )
            # De-dup the records by class_id and index_id, taking the most recently updated row
            .distinct(
                requested_absence_union_table.c.absenceperiod_classid,
                requested_absence_union_table.c.absenceperiod_indexid,
            ).order_by(
                requested_absence_union_table.c.absenceperiod_classid,
                requested_absence_union_table.c.absenceperiod_indexid,
                "absence_casenumber",
                coalesce(
                    FineosExtractVbiRequestedAbsence.updated_at,
                    FineosExtractVbiRequestedAbsenceSom.updated_at,
                ).desc(),
            )
        )
        return query

    def get_requested_absences_with_related_entities(
        self, reference_file: ReferenceFile, match_on_period_id: bool
    ) -> Query:
        """
        Retrieves requested absences (using reference_file's id as a filter) along with related entities such as LeaveRequest, Claim, and AbsencePeriod
        by performing outer joins on the relevant fields.
        """
        requested_absence = (
            self.get_requested_absence_iterator(reference_file)
            .subquery()
            .alias("requested_absence")
        )
        records = (
            self.db_session.query(
                requested_absence, LeaveRequest, Claim, AbsencePeriod
            ).select_from(requested_absence)
            # Get the matching Claim (if it exists) by joining on absence_casenumber
            .outerjoin(
                Claim,
                Claim.fineos_absence_id == literal_column("requested_absence.absence_casenumber"),
            )
            # Get the matching LeaveRequest(if it exists) by joining on leaverequest_id
            .outerjoin(
                LeaveRequest,
                cast(LeaveRequest.fineos_leave_request_id, String)
                == literal_column("requested_absence.leaverequest_id"),
            )
            # Get existing absence period
            .outerjoin(
                AbsencePeriod,
                # Either we match on period id OR c+i ids, depending on match_on_period_id
                (
                    and_(
                        cast(AbsencePeriod.fineos_absence_period_id, String)
                        == literal_column("requested_absence.absenceperiod_id"),
                        AbsencePeriod.fineos_absence_period_id.isnot(None),
                    )
                    if match_on_period_id
                    else and_(
                        cast(AbsencePeriod.fineos_absence_period_class_id, String)
                        == literal_column("requested_absence.absenceperiod_classid"),
                        cast(AbsencePeriod.fineos_absence_period_index_id, String)
                        == literal_column("requested_absence.absenceperiod_indexid"),
                    )
                ),
            )
        )

        return records

    def get_absence_period_container(
        self,
        class_id: str,
        index_id: str,
        period_id: Optional[str],
        requested_absence: RequestedAbsenceForAbsencePeriod,
    ) -> Optional[AbsencePeriodContainer]:
        validation_container = payments_util.ValidationContainer(f"{class_id}:{index_id}")

        start_date = payments_util.validate_db_input(
            "ABSENCEPERIOD_START", requested_absence, validation_container, True
        )
        end_date = payments_util.validate_db_input(
            "ABSENCEPERIOD_END", requested_absence, validation_container, True
        )
        fineos_leave_request_id = payments_util.validate_db_input(
            "LEAVEREQUEST_ID", requested_absence, validation_container, True
        )

        # Values exclusive to the "additional" requested absence record
        raw_absence_period_type = None
        raw_absence_reason_qualifier_1 = None
        raw_absence_reason_qualifier_2 = None
        raw_absence_reason = None
        raw_leave_request_decision = None

        if requested_absence.vbi_requested_absence_serial_id is None:
            logger.info(
                "Could not find VBI_REQUESTEDABSENCE_SOM record to pair with VBI_REQUESTEDABSENCE data"
            )
            self.increment(ClaimantExtractMetrics.NO_SOM_REQUESTED_ABSENCE_FOUND_COUNT)

        if requested_absence.vbi_requested_absence_som_serial_id is None:
            logger.info(
                "Could not find VBI_REQUESTEDABSENCE record to pair with VBI_REQUESTEDABSENCE_SOM data"
            )
            self.increment(ClaimantExtractMetrics.NO_ADDITIONAL_REQUESTED_ABSENCE_FOUND_COUNT)
        else:
            raw_absence_period_type = payments_util.validate_db_input(
                "ABSENCEPERIOD_TYPE",
                requested_absence,
                validation_container,
                True,
                custom_validator_func=payments_util.lookup_validator(AbsencePeriodType),
            )

            raw_absence_reason_qualifier_1 = payments_util.validate_db_input(
                "ABSENCEREASON_QUALIFIER1",
                requested_absence,
                validation_container,
                True,
                custom_validator_func=payments_util.lookup_validator(AbsenceReasonQualifierOne),
            )

            # 2 isn't required as only ~half of claims have a 2nd qualifier
            raw_absence_reason_qualifier_2 = payments_util.validate_db_input(
                "ABSENCEREASON_QUALIFIER2",
                requested_absence,
                validation_container,
                False,
                custom_validator_func=payments_util.lookup_validator(AbsenceReasonQualifierTwo),
            )

            raw_absence_reason = payments_util.validate_db_input(
                "ABSENCEREASON_NAME",
                requested_absence,
                validation_container,
                True,
                custom_validator_func=payments_util.lookup_validator(AbsenceReason),
            )

            raw_leave_request_decision = payments_util.validate_db_input(
                "LEAVEREQUEST_DECISION",
                requested_absence,
                validation_container,
                True,
                custom_validator_func=payments_util.lookup_validator(LeaveRequestDecision),
            )

        return AbsencePeriodContainer(
            start_date=start_date,
            end_date=end_date,
            class_id=class_id,
            index_id=index_id,
            period_id=period_id,
            is_id_proofed=None,
            leave_request_id=fineos_leave_request_id,
            raw_absence_period_type=raw_absence_period_type,
            raw_absence_reason_qualifier_1=raw_absence_reason_qualifier_1,
            raw_absence_reason_qualifier_2=raw_absence_reason_qualifier_2,
            raw_absence_reason=raw_absence_reason,
            raw_leave_request_decision=raw_leave_request_decision,
        )

    def query_for_absence_period_incase_api_created_it(
        self, class_id: int, index_id: int, period_id: Optional[int]
    ) -> AbsencePeriod | None:
        # PFMLPB-1517-syncabsenceperiodsstep-re-queries-for-absenceperiod-before-creating-it
        # In between the original query for requested absence and this point, an API call
        # may have created the AbsencePeriod. Query here for AbsencePeriod to avoid IntegrityError

        # Placing in a function for easily testing
        return (
            self.db_session.query(AbsencePeriod)
            .filter(
                or_(
                    and_(
                        AbsencePeriod.fineos_absence_period_class_id == class_id,
                        AbsencePeriod.fineos_absence_period_index_id == index_id,
                    ),
                    and_(
                        AbsencePeriod.fineos_absence_period_id == period_id,
                        AbsencePeriod.fineos_absence_period_id.isnot(None),
                    ),
                )
            )
            .one_or_none()
        )

    def create_or_update_absence_period(
        self,
        absence_period_info: AbsencePeriodContainer,
        db_absence_period: Optional[AbsencePeriod],
    ) -> AbsencePeriod | None:
        log_attributes: Dict[str, Any] = {
            **absence_period_info.get_log_extra(),
        }

        # Add / update entry on absence period table
        logger.info("Updating Absence Period Table", extra=log_attributes)

        # check for existing absence period
        if db_absence_period is None:
            db_absence_period = self.query_for_absence_period_incase_api_created_it(
                absence_period_info.class_id,
                absence_period_info.index_id,
                absence_period_info.period_id,
            )

        if db_absence_period is None:
            log_attributes["is_new_absence_period"] = True
            logger.info("Absence period not found, creating it", extra=log_attributes)
            self.increment(ClaimantExtractMetrics.NEW_ABSENCE_PERIOD_COUNT)

            db_absence_period = AbsencePeriod(
                fineos_absence_period_class_id=absence_period_info.class_id,
                fineos_absence_period_index_id=absence_period_info.index_id,
                fineos_absence_period_id=absence_period_info.period_id,
            )
            self.db_session.add(db_absence_period)
        else:
            log_attributes["is_new_absence_period"] = False
            logger.info("Absence period found, updating changed fields", extra=log_attributes)
            self.increment(ClaimantExtractMetrics.FOUND_EXISTING_ABSENCE_PERIOD_COUNT)

        if absence_period_info.leave_request_id is not None:
            if db_absence_period.fineos_leave_request_id is None:
                db_absence_period.fineos_leave_request_id = absence_period_info.leave_request_id
            elif db_absence_period.fineos_leave_request_id != absence_period_info.leave_request_id:
                logger.error(
                    "AbsencePeriod already associated with LeaveRequest with a different fineos_leave_request_id from the absence period information received. Not updating existing AbsencePeriod.",
                    extra={
                        **log_attributes,
                        "existing_fineos_leave_request_id": db_absence_period.fineos_leave_request_id,
                        "mismatching_fineos_leave_request_id": absence_period_info.leave_request_id,
                    },
                )
                self.increment(self.Metrics.ABSENCE_PERIOD_LEAVE_REQUEST_MISMATCH)
                # Removes this absence_period from being updated
                try:
                    self.db_session.expunge(db_absence_period)
                except Exception as e:
                    logger.error("Unexpected error %s while expunging absence period.", type(e))
                return None

        if absence_period_info.start_date is not None:
            db_absence_period.absence_period_start_date = absence_period_info.start_date

        if absence_period_info.end_date is not None:
            db_absence_period.absence_period_end_date = absence_period_info.end_date

        if absence_period_info.raw_absence_period_type is not None:
            updated_absence_period_type_id = AbsencePeriodType.get_id(
                absence_period_info.raw_absence_period_type
            )
            db_absence_period.absence_period_type_id = updated_absence_period_type_id

        if absence_period_info.raw_absence_reason_qualifier_1 is not None:
            db_absence_period.absence_reason_qualifier_one_id = AbsenceReasonQualifierOne.get_id(
                absence_period_info.raw_absence_reason_qualifier_1
            )

        # This field is optional and can be blank, so None/blank are skipped
        if absence_period_info.raw_absence_reason_qualifier_2:
            db_absence_period.absence_reason_qualifier_two_id = AbsenceReasonQualifierTwo.get_id(
                absence_period_info.raw_absence_reason_qualifier_2
            )

        if absence_period_info.raw_absence_reason is not None:
            db_absence_period.absence_reason_id = AbsenceReason.get_id(
                absence_period_info.raw_absence_reason
            )

        if absence_period_info.raw_leave_request_decision is not None:
            updated_leave_request_decision_id = LeaveRequestDecision.get_id(
                absence_period_info.raw_leave_request_decision
            )
            db_absence_period.leave_request_decision_id = updated_leave_request_decision_id

        # if the extract has a period_id, copy it over to db record (along with latest class/index ids)
        if absence_period_info.period_id:
            db_absence_period.fineos_absence_period_id = absence_period_info.period_id
            db_absence_period.fineos_absence_period_class_id = absence_period_info.class_id
            db_absence_period.fineos_absence_period_index_id = absence_period_info.index_id

        return db_absence_period

    def sanity_check_requested_absences_for_claim(
        self, claim: Claim, requested_absences: List[RequestedAbsenceForAbsencePeriod]
    ) -> None:
        # Sanity test that the fields we expect to be the exact same
        # for every requested absence record are in fact the same.
        # If we've encountered a strange scenario, set the value to
        # None so it won't get attached to the claim (this likely indicates a FINEOS issue)
        validation_container = payments_util.ValidationContainer(str(claim.claim_id))
        org_unit_names = []
        employer_customer_numbers = []
        claimant_customer_numbers = []
        absence_case_statuses = []
        logger.info("Starting sanity check to test the fields for claim %s", claim.claim_id)
        log_attributes = {"claim.claim_id": claim.claim_id}

        for requested_absence in requested_absences:
            employer_customer_numbers.append(requested_absence.employer_customerno)
            claimant_customer_numbers.append(requested_absence.employee_customerno)
            absence_case_statuses.append(requested_absence.absence_casestatus)
            org_unit_names.append(requested_absence.orgunit_name)
            logger.info("Requested absence case id : %s", requested_absence)

        unique_org_unit_names = set(
            [name for name in org_unit_names if name is not None and name.strip() != ""]
        )
        if (dupe_number := len(unique_org_unit_names)) > 1:
            validation_container.add_validation_issue(
                payments_util.ValidationReason.UNEXPECTED_RECORD_VARIANCE,
                f"Expected only a single organization unit name for claim, and received {dupe_number}: {org_unit_names}",
            )
            claim.organization_unit = None
            self.increment(
                ClaimantExtractMetrics.MULTIPLE_ORGANIZATION_UNIT_NAMES_FOR_CLAIM_ISSUE_COUNT
            )
            logger.warning(
                "Expected only a single organization unit name for claim, and received %s : %s",
                dupe_number,
                org_unit_names,
                extra=log_attributes,
            )

        if (dupe_number := len(set(employer_customer_numbers))) > 1:
            validation_container.add_validation_issue(
                payments_util.ValidationReason.UNEXPECTED_RECORD_VARIANCE,
                f"Expected only a single employer customer number for claim, and received {dupe_number}: {employer_customer_numbers}",
            )
            claim.employer = None
            self.increment(ClaimantExtractMetrics.MULTIPLE_EMPLOYER_FOR_CLAIM_ISSUE_COUNT)
            logger.warning(
                "Expected only a single employer customer number for claim, and received  %s : %s",
                dupe_number,
                employer_customer_numbers,
                extra=log_attributes,
            )

        if (dupe_number := len(set(claimant_customer_numbers))) > 1:
            validation_container.add_validation_issue(
                payments_util.ValidationReason.UNEXPECTED_RECORD_VARIANCE,
                f"Expected only a single employee customer number for claim, and received {dupe_number}: {claimant_customer_numbers}",
            )
            claim.employee = None
            self.increment(ClaimantExtractMetrics.MULTIPLE_CLAIMANT_FOR_CLAIM_ISSUE_COUNT)
            logger.warning(
                "Expected only a single employee customer number for claim, and received %s : %s",
                dupe_number,
                claimant_customer_numbers,
                extra=log_attributes,
            )

        if (dupe_number := len(set(absence_case_statuses))) > 1:
            validation_container.add_validation_issue(
                payments_util.ValidationReason.UNEXPECTED_RECORD_VARIANCE,
                f"Expected only a single absence case status for claim, and received {dupe_number}: {absence_case_statuses}",
            )
            claim.fineos_absence_status = None
            self.increment(ClaimantExtractMetrics.MULTIPLE_ABSENCE_STATUSES_FOR_CLAIM_ISSUE_COUNT)
            logger.warning(
                "Expected only a single absence case status for claim, and received %s : %s",
                dupe_number,
                absence_case_statuses,
                extra=log_attributes,
            )

        if validation_container.validation_issues:
            combined_message = ""
            issue_details = [
                issue.details
                for issue in validation_container.validation_issues
                if issue.details is not None
            ]
            if issue_details:
                combined_message = f"Absence Case ID: {claim.fineos_absence_id}; Issues: {', '.join(map(str, issue_details))}"
            self.append(self.Metrics.REQUESTED_ABSENCES_FOR_CLAIM_WARNINGS, combined_message)

        logger.info("Completed sanity check for claim %s", claim.claim_id)

    def perform_additional_processing_for_requested_absences_by_claim(
        self,
        records_by_claim_id: Dict[str, List[RequestedAbsenceForAbsencePeriod]],
    ) -> None:
        # There are some fields we want to calculate based on all the requested absences per claim
        for claim_id, records in records_by_claim_id.items():
            if not records:
                # this shouldn't occur
                logger.error(f"requested_absences_by_claim_id for claim_id: {claim_id}")
                continue
            claim = records[0].Claim
            if not claim:
                # this shouldn't occur
                logger.error(
                    f"Claim is None for requested_absences_by_claim_id for claim_id: {claim_id}"
                )
                continue

            try:
                self.update_claim_start_date_and_claim_end_date_for_claims(claim, records)
                self.sanity_check_requested_absences_for_claim(claim, records)
            except Exception as e:
                logger.exception(
                    "Unexpected error %s while processing claim: %s", type(e), claim_id
                )
                self.increment(ClaimantExtractMetrics.CLAIM_UPDATE_EXCEPTION_COUNT)

    def update_claim_start_date_and_claim_end_date_for_claims(
        self,
        claim: Claim,
        requested_absences: List[RequestedAbsenceForAbsencePeriod],
    ) -> None:
        start_dates = [
            requested_absence.absenceperiod_start
            for requested_absence in requested_absences
            if requested_absence.absenceperiod_start
        ]
        end_dates = [
            requested_absence.absenceperiod_end
            for requested_absence in requested_absences
            if requested_absence.absenceperiod_end
        ]

        all_start_end_dates_valid = len(requested_absences) == len(start_dates) == len(end_dates)

        if all_start_end_dates_valid:
            # We don't need to convert to date to do comparisons (min/max)
            # This is because the current string format (Y-M-D...) preserves the chronological sort order
            start_date = datetime_str_to_date(min(start_dates))
            end_date = datetime_str_to_date(max(end_dates))
            if start_date and end_date:
                claim.claim_start_date = start_date
                claim.claim_end_date = end_date

    def check_any_absence_period_has_null_period_null(self):
        query = select(exists().where(AbsencePeriod.fineos_absence_period_id.is_(None)))
        result = self.db_session.execute(query).scalar()
        return bool(result)

    def process_absence_periods(self, reference_file: ReferenceFile) -> None:

        # temporary flag to control whether the sync job
        # finds existing AbsencePeriod records based on class and index ids (old format),
        # or period_id (new format)
        match_on_period_id = True

        # check if there are any AbsencePeriod records in our db that
        # are missing period_id (indicates they are pre-FINEOS-v24.8.1 upgrade and need migration)
        any_absence_periods_have_null_period_id = (
            self.check_any_absence_period_has_null_period_null()
        )
        if any_absence_periods_have_null_period_id:
            # switch to matching on c+i ids (old format),
            # which will ultimately backfill period_id values on the records (if FINEOS v24 flag is enabled)
            # and should prevent this code path again
            match_on_period_id = False

        # Log what matching logic we're using
        log_extras = {
            "any_absence_periods_have_null_period_id": any_absence_periods_have_null_period_id,
            "match_on_period_id": match_on_period_id,
        }
        logger.info(
            "Initial check on state of ids in existing AbsencePeriod data", extra=log_extras
        )

        # Record the start time before iterating over the query results
        start_time = time.time()
        end_time = None

        # create a query that fetches all raw/extracted absence periods referenced in the file
        # (along with all related and existing entities ----- from PFML db????)
        records = self.get_requested_absences_with_related_entities(
            reference_file, match_on_period_id
        )
        records_by_claim_id = defaultdict(list)

        COMMIT_EVERY_N_CLAIMS = 1_000

        # Group the requested absence records by the absence_case_id
        # The groupby() relies on the query's order_by("absence_casenumber")
        # This ordering ensures we process all of the absence periods for a claim
        # together. This in turn prevents a possible deadlock if a different process
        # were to process those absence periods at the same time. See:
        # https://lwd.atlassian.net/wiki/spaces/~771485363/pages/2961047625/SyncAbsencePeriodsStep+deadlock+investigation
        for absence_case_id, absence_period_records in groupby(
            records, attrgetter("absence_casenumber")
        ):

            # Capture the end time of the query here on the first execution of this loop
            if end_time is None:
                end_time = time.time()
                duration = end_time - start_time if end_time else 0

                # Log how long the initial query takes
                log_extras = {"duration (seconds)": duration}
                logger.info("Completed initial query/fetch of AbsencePeriod data", extra=log_extras)

            log_attributes = {"absence_case_id": absence_case_id}
            logger.info("Processing absence periods for absence case", extra=log_attributes)
            self.increment(self.Metrics.CLAIMS_PROCESSED_COUNT)

            for record in absence_period_records:
                self.increment(ClaimantExtractMetrics.REQUESTED_ABSENCE_RECORD_COUNT)

                requested_absence = RequestedAbsenceForAbsencePeriod.from_orm(record)
                class_id = requested_absence.absenceperiod_classid
                index_id = requested_absence.absenceperiod_indexid
                period_id = requested_absence.absenceperiod_id

                log_attributes |= {
                    "absence_period_class_id": class_id,
                    "absence_period_index_id": index_id,
                    "absence_period_period_id": period_id,
                }

                if not (class_id and index_id):
                    logger.warning(
                        "Unable to extract class_id and/or index_id from requested_absence.",
                        extra=log_attributes,
                    )
                    self.increment(self.Metrics.ABSENCE_PERIOD_CLASS_ID_OR_INDEX_ID_NOT_FOUND_COUNT)
                    continue

                if not requested_absence.Claim:
                    logger.error(
                        "Claim not found. Expected Claim to be processed before Absence Periods and exist.",
                        extra=log_attributes,
                    )
                    self.increment(self.Metrics.ABSENCE_PERIOD_CLAIM_NOT_FOUND_COUNT)
                    continue

                absence_period_container = self.get_absence_period_container(
                    class_id,
                    index_id,
                    period_id,
                    requested_absence,
                )

                if not absence_period_container:
                    # Note: get_absence_period_container would log information if none is passed back
                    continue

                absence_period = self.create_or_update_absence_period(
                    absence_period_container, requested_absence.AbsencePeriod
                )

                if not absence_period:
                    continue

                if not absence_period.claim_id:
                    absence_period.claim_id = requested_absence.Claim.claim_id
                elif absence_period.claim_id != requested_absence.Claim.claim_id:
                    logger.error(
                        "AbsencePeriod already associated with Claim with a different absence_case_id from the absence period received. Not updating existing AbsencePeriod.",
                        extra=log_attributes
                        | {
                            "claim.fineos_absence_id": requested_absence.Claim.fineos_absence_id,
                        },
                    )
                    self.increment(self.Metrics.ABSENCE_PERIOD_CLAIM_MISMATCH)
                    # Removes this absence_period from being updated
                    try:
                        self.db_session.expunge(absence_period)
                    except Exception as e:
                        logger.error(
                            "Unexpected error %s while expunging absence period.",
                            type(e),
                            extra=log_attributes,
                        )

                if (
                    requested_absence.LeaveRequest is not None
                    and not absence_period.leave_request_id
                ):
                    absence_period.leave_request_id = (
                        requested_absence.LeaveRequest.leave_request_id
                    )

                records_by_claim_id[str(record.Claim.claim_id)].append(record)

                logger.info(
                    "Done creating or updating absence period",
                    extra=log_attributes | absence_period_container.get_log_extra(),
                )

            # We want to flush the absence periods for each claim to the database as soon
            # as we've processed all of the absence periods for that claim. This ensures
            # the database will lock those records immediately and together, preventing a
            # possible deadlock. See:
            # https://lwd.atlassian.net/wiki/spaces/~771485363/pages/2961047625/SyncAbsencePeriodsStep+deadlock+investigation
            self.db_session.flush()
            logger.info("Flushed absence periods for absence case to database")

            # Finally, every so often, we want to commit the changes to release any locks
            # so that other processes (such as API endpoints that write to this table) aren't
            # blocked. We don't want to commit after every claim, because that adversely affect
            # the performance of this step. Empirically, committing every 1,000 claims in TEST
            # results in the locks being released every 2 to 3 seconds.
            claims_processed = self.get_metric(self.Metrics.CLAIMS_PROCESSED_COUNT)
            if type(claims_processed) == int and claims_processed % COMMIT_EVERY_N_CLAIMS == 0:
                logger.info(
                    "Processed absence periods from %s claims, committing results to database",
                    COMMIT_EVERY_N_CLAIMS,
                )
                self.db_session.commit()

        self.perform_additional_processing_for_requested_absences_by_claim(records_by_claim_id)

    def run_step(self) -> None:
        logger.info("Processing absence periods from FINEOS")

        latest_reference_file = get_latest_reference_file_or_raise(
            self.db_session, ReferenceFileType.FINEOS_CLAIMANT_EXTRACT
        )
        self.set_metrics({self.Metrics.EXTRACT_PATH: latest_reference_file.file_location})

        if latest_reference_file.processed_import_log_id:
            logger.warning(
                "Already processed the most recent extracts for %s in import run %s",
                latest_reference_file.file_location,
                latest_reference_file.processed_import_log_id,
            )
        else:
            self.process_absence_periods(latest_reference_file)
            # Mark the ClaimantExtract group as processed, since this is the last step that uses them
            latest_reference_file.processed_import_log_id = self.get_import_log_id()

        logger.info("Done processing absence periods from FINEOS")
