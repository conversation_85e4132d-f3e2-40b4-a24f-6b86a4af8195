import os
import re
import xml.dom.minidom as minidom
from dataclasses import asdict, dataclass, field
from datetime import date, datetime
from decimal import Decimal, InvalidOperation
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Tuple, Type
from uuid import UUID

from sqlalchemy import func
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import joinedload
from sqlalchemy.sql import extract

import massgov.pfml.api.config as api_config
import massgov.pfml.delegated_payments.delegated_config as payments_config
import massgov.pfml.util.files as file_util
import massgov.pfml.util.logging as logging
from massgov.pfml import db
from massgov.pfml.db.lookup import LookupTable
from massgov.pfml.db.lookup_data.employees import (
    ClaimType,
    PaymentMethod,
    PaymentTransactionType,
    PrenoteState,
)
from massgov.pfml.db.lookup_data.payments import MmarsEventStatusType, MmarsEventType
from massgov.pfml.db.lookup_data.state import State
from massgov.pfml.db.models.absences import AbsencePeriod
from massgov.pfml.db.models.ctr.batch_identifier import CtrBatchIdentifier
from massgov.pfml.db.models.employees import (
    Address,
    Claim,
    Employee,
    EmployeePubEftPair,
    Employer,
    ExperianAddressPair,
    LkClaimType,
    LkPaymentTransactionType,
    Payment,
    PaymentDetails,
    PubEft,
)
from massgov.pfml.db.models.payments import (
    LkMmarsEventType,
    PaymentIssueResolution,
    PaymentLine,
    PaymentLog,
)
from massgov.pfml.db.models.reference_file.payment_reference_file import PaymentReferenceFile
from massgov.pfml.db.models.reference_file.reference_file import ReferenceFile
from massgov.pfml.db.models.reference_file.reference_file_type import LkReferenceFileType
from massgov.pfml.db.models.state import LkState
from massgov.pfml.util.collections.dict import filter_dict
from massgov.pfml.util.compare import compare_attributes
from massgov.pfml.util.converters.str_to_numeric import str_to_int
from massgov.pfml.util.datetime import date_to_isoformat, get_now_us_eastern
from massgov.pfml.util.pydantic.types import MassIdStr
from massgov.pfml.util.routing_number_validation import validate_routing_number
from massgov.pfml.util.strings import none_if_empty

logger = logging.get_logger(__package__)


class LatestReferenceFileNotFound(Exception): ...


class Constants:
    S3_OUTBOUND_READY_DIR = "ready"
    S3_OUTBOUND_SENT_DIR = "sent"
    S3_OUTBOUND_ERROR_DIR = "error"
    S3_INBOUND_RECEIVED_DIR = "received"
    S3_INBOUND_PROCESSED_DIR = "processed"
    S3_INBOUND_SKIPPED_DIR = "skipped"
    S3_INBOUND_ERROR_DIR = "error"

    FILE_NAME_PUB_NACHA = "EOLWD-DFML-NACHA"
    FILE_NAME_IP_FULL_CHECK = "EOLWD-DFML-IP-CHECK"
    FILE_NAME_PUB_POSITIVE_PAY = "EOLWD-DFML-POSITIVE-PAY"
    FILE_NAME_RAW_PUB_POSITIVE_PAY = "PUB-POSITIVE-PAY"
    FILE_NAME_PAYMENT_AUDIT_REPORT = "Payment-Audit-Report"
    FILE_NAME_RAW_PUB_ACH_FILE = "ACD9T136-DFML"
    FILE_NAME_MANUAL_PUB_REJECT = "manual-pub-reject"
    FILE_NAME_RAW_PUB_UNDELIVERABLE_CHECKS = "undeliverable_checks.txt"
    FILE_NAME_PUB_UNDELIVERABLE_CHECKS_BACKUP_IMAGES = "undeliverable_checks_photos"
    FILE_NAME_PUB_UNDELIVERABLE_CHECKS_ADDITIONAL_INFO = "undeliverable_checks_additional"
    FILE_NAME_FE_DENIALS_REPORT = "FE-Denials-Report"

    NACHA_FILE_FORMAT = f"%Y-%m-%d-%H-%M-%S-{FILE_NAME_PUB_NACHA}"

    # States that we wait in while waiting for the reject file
    # If any payments are still in this state when the extract
    # task runs, we'll move them to an error state.
    REJECT_FILE_PENDING_STATES = [
        State.DELEGATED_PAYMENT_PAYMENT_AUDIT_REPORT_SENT,
        State.FEDERAL_WITHHOLDING_RELATED_PENDING_AUDIT,
        State.STATE_WITHHOLDING_RELATED_PENDING_AUDIT,
        State.FEDERAL_WITHHOLDING_ORPHANED_PENDING_AUDIT,
        State.STATE_WITHHOLDING_ORPHANED_PENDING_AUDIT,
        State.EMPLOYER_REIMBURSEMENT_RELATED_PENDING_AUDIT,
        State.CHILD_SUPPORT_RELATED_PENDING_AUDIT,
        State.CHILD_SUPPORT_ORPHANED_PENDING_AUDIT,
    ]

    # These overpayment transaction types don't have payment details
    # which means in a few places we want to explicitly not expect to see payment details.
    OVERPAYMENT_TYPES_WITHOUT_PAYMENT_DETAILS = frozenset(
        [
            PaymentTransactionType.OVERPAYMENT_ACTUAL_RECOVERY,
            PaymentTransactionType.OVERPAYMENT_RECOVERY,
            PaymentTransactionType.OVERPAYMENT_RECOVERY_CANCELLATION,
            PaymentTransactionType.OVERPAYMENT_RECOVERY_REVERSE,
            PaymentTransactionType.OVERPAYMENT_ADJUSTMENT,
            PaymentTransactionType.OVERPAYMENT_ACTUAL_RECOVERY_CANCELLATION,
            PaymentTransactionType.OVERPAYMENT_ADJUSTMENT_CANCELLATION,
        ]
    )
    OVERPAYMENT_TYPES_WITHOUT_PAYMENT_DETAILS_IDS = frozenset(
        [
            overpayment_type.payment_transaction_type_id
            for overpayment_type in OVERPAYMENT_TYPES_WITHOUT_PAYMENT_DETAILS
        ]
    )

    PAYMENT_SENT_STATES = frozenset(
        [
            State.DELEGATED_PAYMENT_PUB_TRANSACTION_CHECK_SENT,
            State.DELEGATED_PAYMENT_PUB_TRANSACTION_EFT_SENT,
        ]
    )
    PAYMENT_SENT_STATE_IDS = frozenset([state.state_id for state in PAYMENT_SENT_STATES])


class MMARS_Constants:
    COMPTROLLER_DEPT_CODE = "EOL"
    COMPTROLLER_UNIT_CODE = "8780"
    BPRO_CD = "PFMO"
    ACTG_FUND_CD = "0631"
    ACTG_RSRC_CD = "6950"
    ACTG_SFUND_CD = "0002"
    MMARS_XML_DOC_ID_TEMPLATE = "INTF{}"
    VCC_BATCH_ID_TEMPLATE = "FML" + "{}VCC{}"  # eg. FML0101VCC24
    FILE_NAME_MMARS_VCC = "EOL{}VCC10"
    # outbound folder from where moveit picks up the files
    MOVEIT_OUTBOUND_FILE_FOLDER = "outbound"
    # inbound folder where moveit drops the files
    MOVEIT_INBOUND_FILE_FOLDER = "inbound"
    MOVEIT_RECEIVED_FILE_FOLDER = "received"
    # folder where outbound files are archived
    VCC_FILE_FOLDER = "VCC"
    RE_FILE_FOLDER = "RE"
    REM_FILE_FOLDER = "REM"
    # folder where inbound files are archived
    VCC_RESPONSE_FILE_FOLDER = "VCC_RESPONSE"
    STATUS_REPONSE_FILE_FOLDER = "STATUS_RESPONSE"
    # Date format of `Payment_Date` field in REPAYMENT_TRANSACTIONS API response from EDM
    PAYMENT_DATE_FORMAT = "%Y-%m-%d"

    @dataclass(frozen=True)
    class VCC_FIELD_LIMITS:
        FRST_NM_MAX_LENGTH: int = 14
        LAST_NM_MAX_LENGTH: int = 30
        STR_1_NM_MAX_LENGTH: int = 75
        TIN_AD_MAX_LENGTH: int = 40  # TIN_AD has max length 40 chars. CTR says ok to truncate
        TIN_CITY_NM_MAX_LENGTH: int = (
            30  # This has a max length of 30 despite it being 60 elsewhere
        )
        TIN_NM_1_MAX_LENGTH: int = 40

    @dataclass(frozen=True)
    class RE_FIELD_LIMITS:
        DOC_IMPORT_MODE_MAX_LENGTH: int = 3
        DOC_VERS_NO_MAX_LENGTH: int = 2
        # etc.

    @dataclass(frozen=True)
    class VEND_ACT_STATUS:
        NA = "0"
        INACTIVE = "1"
        ACTIVE = "2"
        SUSPENDED = "3"
        DISCONTINUED = "4"
        DEBARRED = "5"
        DELETED = "6"

    @dataclass(frozen=True)
    class VEND_APRV_STATUS:
        NA = "0"
        INCOMPLETE = "1"
        REVIEWED = "2"
        COMPLETE = "3"

    vcc_generic_attributes = {
        "DOC_CAT": "VCUST",
        "DOC_TYP": "VCC",
        "DOC_CD": "VCC",
        "DOC_DEPT_CD": COMPTROLLER_DEPT_CODE,
        "DOC_UNIT_CD": COMPTROLLER_UNIT_CODE,
        "DOC_VERS_NO": "1",
    }

    ams_doc_attributes = {"DOC_IMPORT_MODE": "OE"}
    rem_ams_doc_attributes = {"DOC_IMPORT_MODE": "MOD"}

    vcc_doc_vcust_attributes = {
        "DOC_VCUST_LN_NO": "1",
        "ORG_TYP": "1",
        "ORG_CLS": "1",
        "TIN_TYP": "2",
    }

    vcc_doc_ad_attributes = {
        "DOC_VCUST_LN_NO": "1",
        "DFLT_AD_TYP": "TRUE",
        "CNTAC_NO": "PC001",
        "PRIN_CNTAC": "NONE PROVIDED",
        "CNTAC_PH_NO": "NONE PROVIDED",
        "AD_ID": "AD010",
    }

    vcc_doc_ad_attributes = {
        "DOC_VCUST_LN_NO": "1",
        "DFLT_AD_TYP": "TRUE",
        "CNTAC_NO": "PC001",
        "PRIN_CNTAC": "NONE PROVIDED",
        "CNTAC_PH_NO": "NONE PROVIDED",
        "AD_ID": "AD010",
    }

    # We add two of these sections with the only difference being static values
    vcc_doc_ad_attributes_bi = {"AD_TYP": "BI", "DOC_AD_LN_NO": "1"}

    vcc_doc_ad_attributes_pa = {"AD_TYP": "PA", "DOC_AD_LN_NO": "1"}

    vcc_doc_ad_attributes_pr = {"AD_TYP": "PR", "DOC_AD_LN_NO": "2"}

    vcc_doc_1099_attributes = {"DOC_VCUST_LN_NO": "1", "DOC_1099_LN_NO": "1"}

    vcc_doc_bus_attributes = {"DOC_VCUST_LN_NO": "1", "CERT_NO": "DFMLCERTIFIED"}

    vcc_doc_custacc_attributes = {
        "DOC_AD_LN_NO": "1",
        "DOC_CUSTACC_LN_NO": "1",
        "BPRO_CD": BPRO_CD,
        "DEPT_CD": COMPTROLLER_DEPT_CODE,
        "UNIT_CD": "ALL",
        "BILL_TYP": "I",
        "BILL_LOC_CD": "CO",
    }

    # If EFT data is present, we'll add a second vcc_doc_bus section with different static attributes
    vcc_doc_bus_attributes_w9 = {"DOC_BUS_LN_NO": "1", "BUS_TYP": "W9"}

    vcc_doc_bus_attributes_eft = {"DOC_BUS_LN_NO": "2", "BUS_TYP": "EFT"}

    vcc_doc_cert_attributes = {
        "DOC_VCUST_LN_NO": "1",
        "DOC_CERT_LN_NO": "1",
        "VEND_ACT_STA": VEND_ACT_STATUS.INACTIVE,
        "VEND_APRV_STA": VEND_APRV_STATUS.INCOMPLETE,
        "CUST_ACT_STA": VEND_ACT_STATUS.ACTIVE,
        "CUST_APRV_STA": VEND_APRV_STATUS.COMPLETE,
    }

    RE_BATCH_ID_TEMPLATE = "FML" + "{}RE{}"  # eg. FML0101RE24
    REM_BATCH_ID_TEMPLATE = "FML" + "{}REM{}"  # eg. FML0101REM24
    FILE_NAME_MMARS_RE = "EOL{}RE10"
    FILE_NAME_MMARS_REM = "EOL{}REM10"

    re_generic_attributes = {
        "DOC_CAT": "AR",
        "DOC_TYP": "RE",
        "DOC_CD": "RE",
        "DOC_DEPT_CD": COMPTROLLER_DEPT_CODE,
        "DOC_UNIT_CD": COMPTROLLER_UNIT_CODE,
        "DOC_VERS_NO": "1",
    }

    re_doc_acctg_attributes = {
        "DOC_VEND_LN_NO": "1",
        "DEPT_CD": COMPTROLLER_DEPT_CODE,
        "FUND_CD": ACTG_FUND_CD,
        "RSRC_CD": ACTG_RSRC_CD,
        "SFUND_CD": ACTG_SFUND_CD,
    }

    rem_generic_attributes = {
        "DOC_CAT": "AR",
        "DOC_TYP": "RE",
        "DOC_CD": "RE",
        "DOC_DEPT_CD": COMPTROLLER_DEPT_CODE,
        "DOC_UNIT_CD": COMPTROLLER_UNIT_CODE,
    }

    rem_doc_acctg_attributes = {
        "DOC_VEND_LN_NO": "1",
        "DOC_ACTG_LN_NO": "1",
        "DEPT_CD": COMPTROLLER_DEPT_CODE,
        "FUND_CD": ACTG_FUND_CD,
        "RSRC_CD": ACTG_RSRC_CD,
        "SFUND_CD": ACTG_SFUND_CD,
        "REAS_CD": "DATAFIX",
    }

    @dataclass(frozen=True)
    class MMARS_FILE_NAME:
        # Inbound files from mmars
        MOVEIT_INBOUND_FILE = "FML_CTR_NMMARSLOAD"

        # outbound files to mmars
        MOVEIT_OUTBOUND_VCC_FILE = "DFML_VCC.dat"
        MOVEIT_OUTBOUND_VCC_INF_FILE = "DFML_VCC.inf"
        MOVEIT_OUTBOUND_RE_FILE = "DFML_RE.dat"
        MOVEIT_OUTBOUND_RE_INF_FILE = "DFML_RE.inf"
        MOVEIT_OUTBOUND_REM_FILE = "DFML_REM.dat"
        MOVEIT_OUTBOUND_REM_INF_FILE = "DFML_REM.inf"

    @dataclass(frozen=True)
    class MMARS_FILE_SUFFIX:
        # SUFFIX attached to the file name to identify the type of file
        MOVEIT_INBOUND_STATUS = "MMARS_STATUS"
        MOVEIT_INBOUND_VCC_RETURN = "VCC_RETURN"

    @dataclass(frozen=True)
    class MMARS_FILE_IDENTIFIER:
        MOVEIT_INBOUND_STATUS_IDENTIFIER = (
            "AMS_IC_STATUS"  # xml tag to identify the status return file
        )
        MOVEIT_INBOUND_VCC_RETURN_IDENTIFIER = (
            "AMS_DOC_XML_EXPORT_FILE"  # xml tag to identify the VCC return file
        )

    # MMARS event types that are considered non-holdable
    NON_HOLDABLE_MMARS_EVENTS = frozenset(
        [
            MmarsEventStatusType.RE_SUBMITTED.mmars_event_status_type_id,  # RE is submitted to MMARS and waiting for a response
            MmarsEventStatusType.RE_SUCCESS.mmars_event_status_type_id,  # RE is succesfully created in MMARS
            MmarsEventStatusType.REM_SUBMITTED.mmars_event_status_type_id,  # REM is submitted to MMARS and waiting for a response
            MmarsEventStatusType.REM_SUCCESS.mmars_event_status_type_id,  # REM is succesfully created in MMARS
            MmarsEventStatusType.ON_HOLD.mmars_event_status_type_id,  # Already on hold
        ]
    )

    RELEASABLE_MMARS_EVENTS = frozenset(
        [
            MmarsEventStatusType.ON_HOLD.mmars_event_status_type_id,
        ]
    )


class Regexes:
    MONETARY_AMOUNT = (
        r"^\d*\.\d\d$"  # Decimal fields must include 2 digits following the decimal point.
    )
    STATE_ABBREVIATION = r"^[A-Z]{2}$"  # State abbreviations should be exactly 2 uppercase letters.
    COUNTRY_ABBREVIATION = (
        r"^[A-Z]{2}$"  # Country abbreviations should be exactly 2 uppercase letters.
    )
    ZIP_CODE = r"^\d{5}(-\d{4})?$"  # Zip codes must contain 5 digits and may contain +4 identifier.


class ValidationReason(str, Enum):
    MISSING_FIELD = "MissingField"
    MISSING_DATASET = "MissingDataset"
    TOO_MANY_DATASETS = "TooManyDatasets"
    MISSING_IN_DB = "MissingInDB"
    MISSING_FINEOS_NAME = "MissingFineosName"
    FIELD_TOO_SHORT = "FieldTooShort"
    FIELD_TOO_LONG = "FieldTooLong"
    INVALID_LOOKUP_VALUE = "InvalidLookupValue"
    INVALID_VALUE = "InvalidValue"
    INVALID_TYPE = "InvalidType"
    RECEIVED_PAYMENT_CURRENTLY_BEING_PROCESSED = "ReceivedPaymentCurrentlyBeingProcessed"
    UNEXPECTED_PAYMENT_TRANSACTION_TYPE = "UnexpectedPaymentTransactionType"
    EFT_PRENOTE_PENDING = "EFTPending"
    EFT_PRENOTE_REJECTED = "EFTRejected"
    CLAIMANT_MISMATCH = "ClaimantMismatch"
    CLAIM_NOT_ID_PROOFED = "ClaimNotIdProofed"
    ROUTING_NUMBER_FAILS_CHECKSUM = "RoutingNumberFailsChecksum"
    LEAVE_REQUEST_IN_REVIEW = "LeaveRequestInReview"
    UNEXPECTED_RECORD_VARIANCE = "UnexpectedRecordVariance"
    EMPLOYER_EXEMPT = "EmployerExempt"
    OPEN_OTHER_INCOME_TASKS = "OpenOtherIncomeTasks"
    OPEN_OTHER_INCOME_TASKS_DELTA = "OpenOtherIncomeTasksDelta"
    OPEN_FRAUD_TASKS = "OpenFraudTasks"
    OPEN_FRAUD_TASKS_DELTA = "OpenFraudTasksDelta"
    ADDRESS_TOO_LONG = "AddressTooLong"
    PERIOD_END_BEFORE_PERIOD_START = "PeriodEndBeforePeriodStart"
    EMPLOYEE_VCC_RECORD_EXISTS = "EmployeeVCCRecordExists"
    MISSING_EMPLOYEE_ADDRESS = "MissingEmployeeVCCAddress"
    EMPLOYEE_NOT_REGISTERED_IN_MMARS = "EmployeeNotRegisteredInMMARS"


@dataclass(frozen=True, eq=True)
class ValidationIssue:
    reason: ValidationReason
    details: Optional[str] = ""
    field_name: Optional[str] = None

    def to_dict(self):
        output = asdict(self)
        if self.field_name is None:
            del output["field_name"]
        return output


@dataclass
class ValidationContainer:
    # Keeping this simple for now, will likely be expanded in the future.
    record_key: str
    validation_issues: List[ValidationIssue] = field(default_factory=list)

    def add_validation_issue(
        self, reason: ValidationReason, details: Optional[str], field_name: Optional[str] = None
    ) -> None:
        self.validation_issues.append(ValidationIssue(reason, details, field_name))

    def has_validation_issues(self) -> bool:
        return len(self.validation_issues) != 0

    def get_reasons(self) -> List[ValidationReason]:
        return [validation_issue.reason for validation_issue in self.validation_issues]

    def get_reasons_with_field_names(self) -> List[Tuple[ValidationReason, Optional[str]]]:
        return [
            (validation_issue.reason, validation_issue.field_name)
            for validation_issue in self.validation_issues
        ]

    def to_dict(self):
        output = asdict(self)
        output["validation_issues"] = [issue.to_dict() for issue in self.validation_issues]
        return output

    def get_issues_as_string(self) -> str:
        issues = self.get_reasons_with_field_names()
        return ", ".join([f"{reason}: {field_name}" for reason, field_name in issues])


class ValidationIssueException(Exception):
    __slots__ = ["issues", "message"]

    def __init__(self, issues: List[ValidationIssue], message: str):
        self.issues = issues
        self.message = message


def get_date_folder(current_time: Optional[datetime] = None) -> str:
    if not current_time:
        current_time = get_now_us_eastern()

    return current_time.strftime("%Y-%m-%d")


def build_archive_path(
    prefix: str, file_status: str, file_name: str, current_time: Optional[datetime] = None
) -> str:
    """
    Construct the path to a file. In the format: prefix / file_status / current_time as date / file_name
    If no current_time specified, will use get_now_us_eastern() method.
    For example:

    build_archive_path("s3://bucket/path/archive", Constants.S3_INBOUND_RECEIVED_DIR,
      "2021-01-01-12-00-00-example-file.csv", datetime.datetime(2021, 1, 1, 12, 0, 0))
    produces
    "s3://bucket/path/archive/received/2021-01-01/2021-01-01-12-00-00-example-file.csv"

    Parameters
    -----------
    prefix: str
      The beginning of the path, likely based on a s3 path configured by an env var
    file_status: str
      The state the file is in, should be one of constants defined above that start with S3_INBOUND or S3_OUTBOUND
    file_name: str
      name of the file - will not be modified
    current_time: Optional[datetime]
      An optional datetime for use in the path, will be formatted as %Y-%m-%d
    """

    return os.path.join(prefix, file_status, get_date_folder(current_time), file_name)


def lookup_validator(
    lookup_table_clazz: Type[LookupTable], disallowed_lookup_values: Optional[List[str]] = None
) -> Callable[[str], Optional[ValidationReason]]:
    def validator_func(raw_value: str) -> Optional[ValidationReason]:
        # In certain scenarios, a value might be in our lookup table, but not be
        # valid for a particular scenario, this lets you skip those scenarios
        if disallowed_lookup_values and raw_value in disallowed_lookup_values:
            return ValidationReason.INVALID_LOOKUP_VALUE

        # description_to_db_instance is used by the get_id method
        # If the value passed into this method is set as a key in that, it's valid
        if raw_value not in lookup_table_clazz.description_to_db_instance:
            return ValidationReason.INVALID_LOOKUP_VALUE
        return None

    return validator_func


def zip_code_validator(zip_code: str) -> Optional[ValidationReason]:
    if not re.match(Regexes.ZIP_CODE, zip_code):
        return ValidationReason.INVALID_VALUE
    return None


def routing_number_validator(routing_number: str) -> Optional[ValidationReason]:
    if not validate_routing_number(routing_number):
        return ValidationReason.ROUTING_NUMBER_FAILS_CHECKSUM

    return None


def leave_request_id_validator(leave_request_id: str) -> Optional[ValidationReason]:
    parsed_leave_request_id = str_to_int(leave_request_id)
    if parsed_leave_request_id is None:
        return ValidationReason.INVALID_TYPE
    return None


def positive_int_validator(value: str) -> Optional[ValidationReason]:
    parsed_value = str_to_int(value)
    if not parsed_value or parsed_value < 0:
        return ValidationReason.INVALID_TYPE
    return None


def amount_validator(amount_str: str) -> Optional[ValidationReason]:
    try:
        Decimal(amount_str)
    except (InvalidOperation, TypeError):  # Amount is not numeric
        return ValidationReason.INVALID_VALUE
    return None


def mass_id_validator(value: str) -> Optional[ValidationReason]:
    try:
        MassIdStr.validate_type(value)
    except (TypeError, ValueError):
        logger.warning(f"Invalid EXTMASSID {value}")
        return ValidationReason.INVALID_VALUE
    return None


def date_timestamp_validator(value: str) -> Optional[ValidationReason]:
    # We don't return an error, just error if this isn't properly formatted
    # as that would mean we have a very incorrect value
    datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
    return None


def claim_type_validator(value: str) -> Optional[ValidationReason]:
    try:
        get_mapped_claim_type(value)
    except ValueError:
        return ValidationReason.INVALID_VALUE
    return None


def validate_db_input(
    key: str,
    data: Any,
    errors: ValidationContainer,
    required: Optional[bool] = False,
    min_length: Optional[int] = None,
    max_length: Optional[int] = None,
    custom_validator_func: Optional[Callable[[str], Optional[ValidationReason]]] = None,
) -> Optional[str]:
    value = getattr(data, key.lower(), None)
    if value == "Unknown":
        value = None  # Effectively treating "" and "Unknown" the same

    if required and not value:
        errors.add_validation_issue(ValidationReason.MISSING_FIELD, key, field_name=key)
        return None

    validation_issues = []
    # Check the length only if it is defined/not empty
    if value:
        if min_length and len(value) < min_length:
            validation_issues.append(ValidationReason.FIELD_TOO_SHORT)
        if max_length and len(value) > max_length:
            validation_issues.append(ValidationReason.FIELD_TOO_LONG)

        # Also only bother with custom validation if the value exists
        if custom_validator_func:
            reason = custom_validator_func(value)
            if reason:
                validation_issues.append(reason)

    if required:

        for validation_issue in validation_issues:
            # Any non-missing error types add the value to the error details
            # Note that this means these reports will contain PII data
            errors.add_validation_issue(validation_issue, f"{key}: {value}", key)

    # If any of the specific validations hit an error, don't return the value
    # This is true even if the field is not required as we may still use the field.
    if len(validation_issues) > 0:
        return None

    return value


def is_same_address(first: Address, second: Address) -> bool:
    if (
        compare_attributes(first, second, "address_line_one")
        and compare_attributes(first, second, "city")
        and compare_attributes(first, second, "zip_code")
        and compare_attributes(first, second, "geo_state_id")
        and compare_attributes(first, second, "country_id")
        and compare_attributes(first, second, "address_line_two")
    ):
        return True
    else:
        return False


def find_existing_address_pair(
    employee: Optional[Employee], new_address: Address, db_session: db.Session
) -> Optional[ExperianAddressPair]:
    if not employee:
        return None

    subquery = (
        db_session.query(Payment.payment_id)
        .join(Claim)
        .filter(Claim.employee_id == employee.employee_id)
    )
    experian_address_pairs = (
        db_session.query(ExperianAddressPair)
        .join(Payment, Payment.experian_address_pair_id == ExperianAddressPair.fineos_address_id)
        .filter(Payment.payment_id.in_(subquery))
        .all()
    )

    # For each address associated with prior payments for the claimant
    # see if either the address from FINEOS matches or the one returned
    # by Experian matches (in case FINEOS is updated to the more correct one)
    for experian_address_pair in experian_address_pairs:

        existing_fineos_address = experian_address_pair.fineos_address
        existing_experian_address = experian_address_pair.experian_address

        if existing_fineos_address and is_same_address(new_address, existing_fineos_address):
            return experian_address_pair

        if existing_experian_address and is_same_address(new_address, existing_experian_address):
            return experian_address_pair

    return None


def is_same_eft(first: PubEft, second: PubEft) -> bool:
    """Returns true if all EFT fields match"""
    if (
        first.routing_nbr == second.routing_nbr
        and first.account_nbr == second.account_nbr
        and first.bank_account_type_id == second.bank_account_type_id
    ):
        return True
    else:
        return False


def find_existing_eft(employee: Optional[Employee], new_eft: PubEft) -> Optional[PubEft]:
    if not employee:
        return None

    pub_eft_pairs = employee.pub_efts.options(joinedload(EmployeePubEftPair.pub_eft)).all()  # type: ignore

    for pub_eft_pair in pub_eft_pairs:
        if is_same_eft(pub_eft_pair.pub_eft, new_eft):
            return pub_eft_pair.pub_eft

    return None


def move_file_and_update_ref_file(
    db_session: db.Session, destination: str, ref_file: ReferenceFile
) -> None:
    file_util.rename_file(ref_file.file_location, destination)
    ref_file.file_location = destination


def get_mapped_claim_type(claim_type_str: Optional[str]) -> LkClaimType:
    """Given a string from a Vendor Extract, return a LkClaimType

    Raises:
        ValueError: if the string does not match an existing LkClaimType
    """
    if claim_type_str == "Family":
        return ClaimType.FAMILY_LEAVE
    elif claim_type_str == "Employee":
        return ClaimType.MEDICAL_LEAVE
    else:
        raise ValueError("Unknown claim type")


def get_traceable_log_details(
    payment: Optional[Payment] = None,
    payment_details: Optional[PaymentDetails] = None,
    payment_line: Optional[PaymentLine] = None,
    pub_eft: Optional[PubEft] = None,
    employee: Optional[Employee] = None,
) -> Dict[str, Any]:
    # For logging purposes, this returns useful, traceable details
    # about various payment related objects passed in. This is
    # just a combination of the other "get_traceable_x" methods
    # so you don't need to merge them all yourself if wanting multiple.
    #
    # DO NOT PUT PII IN THE RETURN OF THIS METHOD, IT'S MEANT FOR LOGGING
    #
    details: Dict[str, Any] = {}

    if payment:
        details |= get_traceable_payment_details(payment)
    if payment_details:
        details |= get_traceable_payment_period_details(payment_details)
    if payment_line:
        details |= get_traceable_payment_line_details(payment_line)
    if pub_eft:
        details |= get_traceable_pub_eft_details(pub_eft, employee)

    return details


def get_traceable_claim_details(claim: Claim) -> Dict[str, Any]:
    details: Dict[str, Any] = {
        "claim_id": claim.claim_id,
        "absence_case_id": claim.fineos_absence_id,
        "absence_start_date": date_to_isoformat(claim.claim_start_date),
        "absence_end_date": date_to_isoformat(claim.claim_end_date),
        "absence_case_status": (
            claim.fineos_absence_status.absence_status_description
            if claim.fineos_absence_status
            else None
        ),
        "is_id_proofed": claim.get_is_id_proofed,
    }

    employee = claim.employee
    if employee:
        details |= {
            "employee_id": employee.employee_id,
            "fineos_customer_number": employee.fineos_customer_number,
        }

    employer = claim.employer
    if employer:
        details |= {
            "employer_id": employer.employer_id,
            "fineos_employer_id": employer.fineos_employer_id,
        }

    return details


def get_traceable_payment_details(
    payment: Payment, state: Optional[LkState] = None
) -> Dict[str, Any]:
    # For logging purposes, this returns useful, traceable details
    # about a payment and related fields if they exist.
    #
    # DO NOT PUT PII IN THE RETURN OF THIS METHOD, IT'S MEANT FOR LOGGING
    #

    claim = payment.claim
    employee = payment.employee
    employer = payment.claim.employer if payment.claim else None

    details = {
        "payment_id": payment.payment_id,
        "c_value": payment.fineos_pei_c_value,
        "i_value": payment.fineos_pei_i_value,
        "period_start_date": date_to_isoformat(payment.period_start_date),
        "period_end_date": date_to_isoformat(payment.period_end_date),
        "payment_date": date_to_isoformat(payment.payment_date),
        "payment_amount": str(payment.amount),
        "payment_method": (
            payment.disb_method.payment_method_description if payment.disb_method else None
        ),
        "pub_individual_id": payment.pub_individual_id,
        "payment_transaction_type": (
            payment.payment_transaction_type.payment_transaction_type_description
            if payment.payment_transaction_type
            else None
        ),
        "is_adhoc": payment.is_adhoc_payment,
        "fineos_extract_import_log_id": payment.fineos_extract_import_log_id,
        # Leave
        "leave_request_decision": payment.leave_request_decision,
        "claim_type": payment.claim_type.claim_type_description if payment.claim_type else None,
        "fineos_leave_request_id": payment.fineos_leave_request_id,
        # Misc
        "current_state": state.state_description if state else None,
        "relevant_party": (
            payment.payment_relevant_party.payment_relevant_party_description
            if payment.payment_relevant_party
            else None
        ),
    }

    # Add some info from other related models if set
    if claim:
        details |= {
            "claim_id": claim.claim_id,
            "absence_case_id": claim.fineos_absence_id,
        }

        employer = claim.employer
        if employer:
            details |= {
                "employer_id": employer.employer_id,
                "fineos_employer_id": employer.fineos_employer_id,
            }

    if payment.employee:
        employee = payment.employee
        details |= {
            "employee_id": employee.employee_id,
            "fineos_customer_number": employee.fineos_customer_number,
        }

    return details


def get_traceable_payment_period_details(
    payment_detail: PaymentDetails,
) -> Dict[str, Any]:
    # For logging purposes, this returns useful, traceable details
    # about a payment detail.
    #
    # DO NOT PUT PII IN THE RETURN OF THIS METHOD, IT'S MEANT FOR LOGGING
    #

    return {
        "payment_details_id": payment_detail.payment_details_id,
        "payment_details_c_value": payment_detail.payment_details_c_value,
        "payment_details_i_value": payment_detail.payment_details_i_value,
        "payment_details_period_start_date": date_to_isoformat(payment_detail.period_start_date),
        "payment_details_period_end_date": date_to_isoformat(payment_detail.period_end_date),
        "payment_details_balancing_amount": str(payment_detail.amount),
        "payment_details_net_amount": str(payment_detail.business_net_amount),
        "payment_details_payment_id": payment_detail.payment_id,
    }


def get_traceable_payment_line_details(payment_line: PaymentLine) -> Dict[str, Optional[Any]]:
    # For logging purposes, this returns useful, traceable details
    # about a payment line.
    #
    # DO NOT PUT PII IN THE RETURN OF THIS METHOD, IT'S MEANT FOR LOGGING
    #
    return {
        "payment_line_id": payment_line.payment_line_id,
        "payment_line_payment_id": payment_line.payment_id,
        "payment_line_payment_details_id": payment_line.payment_details_id,
        "payment_line_c_value": payment_line.payment_line_c_value,
        "payment_line_i_value": payment_line.payment_line_i_value,
        "payment_line_amount": payment_line.amount,
        "payment_line_type": payment_line.line_type,
    }


def get_traceable_pub_eft_details(
    pub_eft: PubEft,
    employee: Optional[Employee] = None,
    payment: Optional[Payment] = None,
) -> Dict[str, Any]:
    # For logging purposes, this returns useful, traceable details
    # about an EFT record and related fields
    #
    # DO NOT PUT PII IN THE RETURN OF THIS METHOD, IT'S MEANT FOR LOGGING
    #

    details = {}
    if payment:
        details = get_traceable_payment_details(payment)

    details["pub_eft_id"] = pub_eft.pub_eft_id
    details["pub_eft_individual_id"] = pub_eft.pub_individual_id
    details["pub_eft_prenote_state"] = (
        PrenoteState.get_description(pub_eft.prenote_state_id) if pub_eft.prenote_state_id else None
    )
    if employee:
        details["employee_id"] = employee.employee_id
        details["fineos_customer_number"] = employee.fineos_customer_number

    return details


def get_traceable_payment_issue_resolution_details(
    issue_resolution: PaymentIssueResolution,
) -> Dict[str, Any]:

    details = {}
    if issue_resolution.payment:
        details = get_traceable_payment_details(issue_resolution.payment)

    scenario_config = issue_resolution.payment_issue_resolution_scenario_config
    fineos_task = scenario_config.fineos_task_type

    details["issue_resolution_id"] = issue_resolution.payment_issue_resolution_id
    details["issue_resolution_scenario"] = (
        scenario_config.payment_issue_resolution_scenario_config_description
    )
    details["fineos_task_type"] = fineos_task.fineos_task_type_description

    return details


def get_transaction_status_date(payment: Payment) -> date:
    # Check payments that have a check posted date should use
    # that for the transaction status date as that indicates
    # from PUB when the check was actually posted
    if payment.check and payment.check.check_posted_date:
        return payment.check.check_posted_date

    # Otherwise the transaction status date is calculated using the current time.
    return get_now_us_eastern().date()


employee_audit_log_keys = {
    "employee_id",
    "tax_identifier_id",
    "first_name",
    "last_name",
    "date_of_birth",
    "fineos_customer_number",
    "latest_import_log_id",
    "created_at",
    "updated_at",
}
employer_audit_log_keys = {
    "employer_id",
    "employer_fein",
    "employer_name",
    "dor_updated_date",
    "latest_import_log_id",
    "fineos_employer_id",
    "created_at",
    "updated_at",
}


def create_payment_log(
    payment: Payment,
    import_log_id: Optional[int],
    db_session: db.Session,
    additional_details: Optional[Dict[str, Any]] = None,
) -> None:
    """
    Create a log in the DB for information about a payment at a particular point
    in the processing. Automatically adds a snapshot of
    employee/employer/claim/payment check
    """

    snapshot = {}
    # When we refactor claim to be fetched from absence period, change this
    # to be in the above if statement
    claim = payment.claim
    if claim:
        snapshot["claim"] = claim.for_json()

        employee = claim.employee
        if employee:
            employee_json = employee.for_json()
            snapshot["employee"] = filter_dict(employee_json, employee_audit_log_keys)

        employer = claim.employer
        if employer:
            employer_json = employer.for_json()
            snapshot["employer"] = filter_dict(employer_json, employer_audit_log_keys)

    payment_details = payment.payment_details
    if payment_details:
        payment_details_info = []
        for payment_detail in payment_details:
            payment_details_info.append(payment_detail.for_json())
        snapshot["payment_details"] = payment_details_info

    check_details = payment.check
    if check_details:
        snapshot["payment_check"] = check_details.for_json()

    audit_details = {"snapshot": snapshot}
    if additional_details:
        audit_details.update(additional_details)

    payment_log = PaymentLog(payment=payment, import_log_id=import_log_id, details=audit_details)
    db_session.add(payment_log)


def create_success_file(
    start_time: datetime, process_name: str, s3_config: payments_config.PaymentsS3Config
) -> str:
    """
    Create a file that indicates the ECS process was successful. Will
    be put in a folder for the date the processing started, but
    the file will be timestamped with the time it completed.

    s3://bucket/reports/processed/{start_date}/{completion_timestamp}-{process_name}.SUCCESS
    """
    end_time = get_now_us_eastern()
    timestamp_prefix = end_time.strftime("%Y-%m-%d-%H-%M-%S")
    success_file_name = f"{timestamp_prefix}-{process_name}.SUCCESS"

    archive_path = s3_config.pfml_error_reports_archive_path
    output_path = build_archive_path(
        archive_path, Constants.S3_INBOUND_PROCESSED_DIR, success_file_name, current_time=start_time
    )

    # What is the easiest way to create an empty file to upload?
    with file_util.write_file(output_path) as success_file:
        success_file.write("SUCCESS")

    logger.info("Creating success file at %s", output_path)

    return output_path


def is_employer_exempt_for_payment(payment: Payment, claim: Claim, employer: Employer) -> bool:
    # Adhoc payments always skip the exempt employer check
    if payment.is_adhoc_payment:
        return False

    # See if exemptions are even set for the employer
    # + make the linter happy that we're not comparing nulls
    if (
        not employer.exemption_commence_date
        or not employer.exemption_cease_date
        or not claim.claim_start_date
    ):
        return False

    # Check if the employer is exempt for the claim type
    # associated with the payment record
    if (
        payment.claim_type_id == ClaimType.FAMILY_LEAVE.claim_type_id and employer.family_exemption
    ) or (
        payment.claim_type_id == ClaimType.MEDICAL_LEAVE.claim_type_id
        and employer.medical_exemption
    ):
        # Then check if the start of the claim
        # fell within the exempt dates of the employer
        if (
            employer.exemption_commence_date
            <= claim.claim_start_date
            <= employer.exemption_cease_date
        ):
            extra = get_traceable_payment_details(
                payment
            )  # Adds the basics about the employer/claim/payment
            extra["employer_is_exempt_family"] = employer.family_exemption
            extra["employer_is_exempt_medical"] = employer.medical_exemption
            extra["employer_exemption_commence_date"] = employer.exemption_commence_date.isoformat()
            extra["employer_exemption_cease_date"] = employer.exemption_cease_date.isoformat()
            logger.info("Payment failed exempt employer validation check", extra=extra)
            return True

    return False


def use_overpayment_table_for_over_payments_max_weekly_benefit() -> bool:
    return os.environ.get("USE_OVER_PAYMENT_TABLE_FOR_OVER_PAYMENTS_MAX_WEEKLY_BENEFIT", "0") == "1"


def get_earliest_absence_period_for_payment_leave_request(
    db_session: db.Session, payment: Payment
) -> Optional[AbsencePeriod]:
    """
    Get the earliest absence period associated with a payment
    Note that this does not mean the payment is necessarily in
    the absence period. It just means it's the first absence period
    of the paid leave request connected to the payment.

    Claim
        * Paid Leave 1
            * Absence Period A
                * Payment I
                * Payment II
            * Absence Period B
                * Payment III
                * Payment IV
        * Paid Leave 2
            * Absence Period C
                * Payment V
                * Payment VI
            * Absence Period D
                * Payment VII
            * Absence Period E
                * Payment VIII

    For the above example:
    Payments I -> IV would return Absence Period A
    Payments V -> VIII would return Absence Period C

    Nothing would ever return Absence Periods B, D, or E
    """
    return (
        db_session.query(AbsencePeriod)
        .filter(AbsencePeriod.fineos_leave_request_id == payment.fineos_leave_request_id)
        .filter(AbsencePeriod.claim_id == payment.claim_id)
        .order_by(AbsencePeriod.absence_period_start_date.asc())
        .first()
    )


def get_earliest_matching_payment(
    db_session: db.Session, fineos_pei_c_value: str, fineos_pei_i_value: str
) -> Optional[Payment]:
    """
    Get the earliest payment associated with C/I values
    """
    return (
        db_session.query(Payment)
        .filter(
            Payment.fineos_pei_c_value == fineos_pei_c_value,
            Payment.fineos_pei_i_value == fineos_pei_i_value,
        )
        .order_by(Payment.created_at.asc())
        .first()
    )


def get_payment_transaction_type_id(payment: Payment) -> int:
    transaction_type_id = (
        payment.payment_transaction_type_id
        if payment.payment_transaction_type_id is not None
        else 0
    )
    return transaction_type_id


def is_payment_transaction_type(
    payment: Payment, *transaction_types: LkPaymentTransactionType
) -> bool:
    for transaction_type in transaction_types:
        if payment.payment_transaction_type_id == transaction_type.payment_transaction_type_id:
            return True
    return False


def is_employer_overpayment_enabled() -> bool:
    return os.environ.get("ENABLE_EMPLOYER_OVERPAYMENT", "0") == "1"


def get_latest_reference_file_or_raise(
    db_session: db.Session, reference_file_type: LkReferenceFileType
) -> ReferenceFile:
    """Returns the most recent reference file of a given type, or raises an exception if none exist."""

    latest_reference_file = (
        db_session.query(ReferenceFile)
        .filter(ReferenceFile.reference_file_type_id == reference_file_type.reference_file_type_id)
        .order_by(ReferenceFile.created_at.desc())
        .first()
    )

    if not latest_reference_file:
        raise LatestReferenceFileNotFound(
            f"No extracts consumed for {reference_file_type.reference_file_type_description}. This would only happen the first time you run in an environment and have no extracts, make sure FINEOS has created extracts."
        )

    return latest_reference_file


def get_latest_processed_reference_file(
    db_session: db.Session, reference_file_type: LkReferenceFileType
) -> Optional[ReferenceFile]:
    """Returns the most recent processed reference file of a given type, or returns None if none exist."""

    latest_processed_reference_file = (
        db_session.query(ReferenceFile)
        .filter(ReferenceFile.reference_file_type_id == reference_file_type.reference_file_type_id)
        .filter(ReferenceFile.processed_import_log_id.isnot(None))
        .order_by(ReferenceFile.created_at.desc())
        .first()
    )

    return latest_processed_reference_file


def add_payment_reference_file(
    db_session: db.Session,
    payment: Payment,
    payments_in_reference_file: set[UUID],
    reference_file: ReferenceFile,
) -> None:
    """Add a link between the payment and the reference file, and mutates payments_in_reference_file to include the payment ID.
    If the payment ID is already in payments_in_reference_file, does nothing."""

    if payment.payment_id in payments_in_reference_file:
        return
    payments_in_reference_file.add(payment.payment_id)

    payment_reference_file = PaymentReferenceFile(payment=payment, reference_file=reference_file)
    db_session.add(payment_reference_file)


def get_address_pair_for_experian_override(
    db_session: db.Session,
    absence_case_id: str,
) -> Optional[ExperianAddressPair]:
    """
    There are instances where Experian can't validate a USPS address.
    When this happens manual intervention is required to make the Experian address
    the same as the FINEOS address. This function confirms if such a case exists per
    absence case ID.
    """
    address_pair = (
        db_session.query(ExperianAddressPair)
        .select_from(Claim)
        .join(Payment, Payment.claim_id == Claim.claim_id)
        .join(
            ExperianAddressPair,
            Payment.experian_address_pair_id == ExperianAddressPair.fineos_address_id,
        )
        .filter(
            Claim.fineos_absence_id == absence_case_id,
            ExperianAddressPair.experian_address_id.is_(None),
            Payment.disb_method_id == PaymentMethod.CHECK.payment_method_id,
            Payment.payment_transaction_type_id.in_(
                [
                    PaymentTransactionType.STANDARD.payment_transaction_type_id,
                    PaymentTransactionType.EMPLOYER_REIMBURSEMENT.payment_transaction_type_id,
                ]
            ),
        )
        .distinct()
    ).first()

    return address_pair


def override_experian_address(
    db_session: db.Session,
    address_pair: ExperianAddressPair,
) -> None:
    """
    Make the Experian address ID the same as the FINEOS address ID
    only when the Experian address ID is empty.
    """
    if address_pair.experian_address_id is None:
        try:
            address_pair.experian_address_id = address_pair.fineos_address_id
            db_session.add(address_pair)
            db_session.commit()
            logger.info(
                "The Experian address ID was successfully overridden with the FINEOS address ID.",
                extra={
                    "experian_address_id": address_pair.experian_address_id,
                },
            )
        except SQLAlchemyError:
            # Rollback the database transaction
            db_session.rollback()
            # Log the exception
            logger.exception(
                "Failed to set the experian address ID.",
            )
            raise


def is_prepaid_impact_payments_enabled() -> bool:
    return os.environ.get("ENABLE_PREPAID_IMPACT_PAYMENTS", "0") == "1"


def is_sync_payment_preference_enabled() -> bool:
    return os.environ.get("ENABLE_SYNC_PAYMENT_PREFERENCE", "0") == "1"


def add_xml_attributes(element: minidom.Element, attributes: Dict[str, str]) -> None:
    for k, v in attributes.items():
        value = v if v else "null"
        element.setAttribute(k, value)


def get_doc_id(now: datetime, count: int) -> str:
    return MMARS_Constants.MMARS_XML_DOC_ID_TEMPLATE.format(now.strftime("%d%m%Y"), f"{count:04}")


def is_enable_overpayment_vcmt_automation_enabled() -> bool:
    return os.environ.get("ENABLE_OVERPAYMENT_VCMT_AUTOMATION", "0") == "1"


def add_xml_cdata_elements(
    parent: minidom.Element,
    document: minidom.Document,
    elements: Dict[str, Any],
    add_y_attribute: bool = True,
) -> None:
    for key, val in elements.items():
        elem = document.createElement(key)
        if add_y_attribute:
            add_xml_attributes(elem, {"Attribute": "Y"})
        parent.appendChild(elem)

        if val is None:
            cdata = document.createCDATASection("null")
        else:
            # Anything in the CDATA tag is passed directly and markup ignored
            # CTR wants DFML to send all values in as uppercase
            cdata = document.createCDATASection(str(val).upper())
        elem.appendChild(cdata)


def combine_address_lines(address_line_one: Optional[str], address_line_two: Optional[str]) -> str:
    if address_line_one is None:
        raise Exception("combine_address_lines: address_line_one cannot be None")
    combined_address = address_line_one
    if address_line_two:
        combined_address += f" {address_line_two}"
    return combined_address


def has_mailing_address(employee: Employee) -> bool:
    address = employee.claimant_addresses
    if not address:
        return False
    elif not address[0].mailing_address:
        return False
    return True


def get_employee_mailing_address(employee: Employee) -> Optional[Address]:
    """
    Retrieves the employee's mailing address if available. If Experian has
    validated it, returns that address; otherwise defaults to the Fineos address.
    Returns None when no address is found.
    """
    if not employee:
        return None

    if not employee.claimant_addresses:
        return None

    claimant_address = employee.claimant_addresses[0]

    if claimant_address.mailing_address and claimant_address.mailing_address.experian_address:
        return claimant_address.mailing_address.experian_address
    elif claimant_address.mailing_address and claimant_address.mailing_address.fineos_address:
        return claimant_address.mailing_address.fineos_address
    else:
        return None


def get_financial_year(date_obj: date) -> int:
    # need to look at end/start of fiscal year. After June 30th, look forward to next year.
    return date_obj.year if date_obj.month < 7 else date_obj.year + 1


def get_month_period(date_obj: date) -> int:
    # Each month is a period, July is 1, June is 12
    month_number = (date_obj.month - 6) % 12
    return month_number if month_number != 0 else 12


def generate_next_batch_identifier(
    db_session: db.Session, date_obj: datetime, mmars_event: LkMmarsEventType
) -> CtrBatchIdentifier:
    batch_date = date_obj.date()
    year = date_obj.year
    batch_counter = (
        db_session.query(func.max(CtrBatchIdentifier.batch_counter))
        .filter(
            extract("month", CtrBatchIdentifier.batch_date) == batch_date.month,
            extract("day", CtrBatchIdentifier.batch_date) == batch_date.day,
            CtrBatchIdentifier.mmars_event_type_id == mmars_event.mmars_event_type_id,
        )
        .scalar()
    )
    if batch_counter is None:
        batch_counter = 0
    else:
        batch_counter += 1

    ctr_batch_identifier = MMARS_Constants.VCC_BATCH_ID_TEMPLATE.format(
        date_obj.strftime("%m%d"), f"{batch_counter:02}"
    )

    if mmars_event.mmars_event_type_id == MmarsEventType.RE_TRX.mmars_event_type_id:
        ctr_batch_identifier = MMARS_Constants.RE_BATCH_ID_TEMPLATE.format(
            date_obj.strftime("%m%d"), f"{batch_counter:02}"
        )
    elif mmars_event.mmars_event_type_id == MmarsEventType.REM_TRX.mmars_event_type_id:
        ctr_batch_identifier = MMARS_Constants.REM_BATCH_ID_TEMPLATE.format(
            date_obj.strftime("%m%d"), f"{batch_counter:02}"
        )

    ctr_batch_identifier_record = CtrBatchIdentifier(
        ctr_batch_identifier=ctr_batch_identifier,
        year=year,
        batch_date=batch_date,
        batch_counter=batch_counter,
        mmars_event_type_id=mmars_event.mmars_event_type_id,
    )

    return ctr_batch_identifier_record


def get_overpayment_repayment_fetch_days_prior() -> int:
    return api_config.get_config().overpayment_repayment_fetch_days_prior


def is_mock_edm_repayment_response_enabled() -> bool:
    return api_config.get_config().enable_mock_edm_repayment_response


def handle_account_detail(value: str | None) -> str | None:
    return none_if_empty("" if value == "0" else value)
