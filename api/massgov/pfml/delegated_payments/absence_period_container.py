from datetime import date
from typing import Any, Dict, Optional

from massgov.pfml.util.datetime import date_to_isoformat, datetime_str_to_date


class AbsencePeriodContainer:
    class_id: int
    index_id: int
    period_id: Optional[int]
    leave_request_id: Optional[int]
    is_id_proofed: Optional[bool]
    start_date: Optional[date]
    end_date: Optional[date]
    raw_absence_period_type: Optional[str]
    raw_absence_reason_qualifier_1: Optional[str]
    raw_absence_reason_qualifier_2: Optional[str]
    raw_absence_reason: Optional[str]
    raw_leave_request_decision: Optional[str]

    def __init__(
        self,
        start_date: Optional[str],
        end_date: Optional[str],
        class_id: str,
        index_id: str,
        period_id: Optional[str],
        is_id_proofed: Optional[bool],
        leave_request_id: Optional[str],
        raw_absence_period_type: Optional[str],
        raw_absence_reason_qualifier_1: Optional[str],
        raw_absence_reason_qualifier_2: Optional[str],
        raw_absence_reason: Optional[str],
        raw_leave_request_decision: Optional[str],
    ):
        self.start_date = datetime_str_to_date(start_date)
        self.end_date = datetime_str_to_date(end_date)
        self.class_id = int(class_id)
        self.index_id = int(index_id)
        self.period_id = int(period_id) if period_id else None
        self.is_id_proofed = is_id_proofed
        self.leave_request_id = int(leave_request_id) if leave_request_id else None

        self.raw_absence_period_type = raw_absence_period_type
        self.raw_absence_reason_qualifier_1 = raw_absence_reason_qualifier_1
        self.raw_absence_reason_qualifier_2 = raw_absence_reason_qualifier_2
        self.raw_absence_reason = raw_absence_reason
        self.raw_leave_request_decision = raw_leave_request_decision

    def _members(self):
        # _members only contains the values from the SOM version of the
        # file as we want to dedupe records from that file with this,
        # and aren't concerned with the non-SOM file
        return (
            self.start_date,
            self.end_date,
            self.class_id,
            self.index_id,
            self.is_id_proofed,
            self.leave_request_id,
        )

    def __eq__(self, other):
        if not isinstance(other, AbsencePeriodContainer):
            return False

        return self._members() == other._members()

    def __hash__(self):
        return hash(
            (
                self.start_date,
                self.end_date,
                self.class_id,
                self.index_id,
                self.is_id_proofed,
                self.leave_request_id,
            )
        )

    def get_log_extra(self) -> Dict[str, Any]:
        return {
            "absence_period_class_id": self.class_id,
            "absence_period_index_id": self.index_id,
            "absence_period_start_date": date_to_isoformat(self.start_date),
            "absence_period_end_date": date_to_isoformat(self.end_date),
            "fineos_leave_request_id": self.leave_request_id,
            "absence_period_type": self.raw_absence_period_type,
            "leave_request_decision": self.raw_leave_request_decision,
            "absence_reason": self.raw_absence_reason,
            "absence_reason_qualifier_1": self.raw_absence_reason_qualifier_1,
            "absence_reason_qualifier_2": self.raw_absence_reason_qualifier_2,
            "is_id_proofed": self.is_id_proofed,
        }
