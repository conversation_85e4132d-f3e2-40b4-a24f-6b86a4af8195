from typing import Optional, cast

import massgov.pfml.util.logging
from massgov.pfml.db.models.employees import Payment
from massgov.pfml.delegated_payments.audit.audit_writeback_util import (
    CANCEL_TIME_SUBMITTED_MAPPING,
    AuditReportAction,
)
from massgov.pfml.delegated_payments.postprocessing.audit_processor import (
    AuditDetails,
    AuditProcessor,
)
from massgov.pfml.fineos.tasks import get_open_tasks_from_delta
from massgov.pfml.util.batch.step import Step

logger = massgov.pfml.util.logging.get_logger(__name__)


class CancelTimeSubmittedProcessor(AuditProcessor):
    """
    Processor checks if there is an open task tied to the absence case number. The Reject Notes column should show the
    following text `Cancel time submitted` with a Y in a Reject column.

    `Cancel time submitted` should also be a new reject reason so the transaction status of the payment will reflect that.
    """

    def __init__(self, step: Step):
        super().__init__(step, CANCEL_TIME_SUBMITTED_MAPPING)

    def get_audit_report_message(self, payment: Payment) -> Optional[str]:
        return None

    def process(self, payment: Payment) -> Optional[AuditDetails]:
        if not payment.claim:
            return None

        casenumber = payment.claim.fineos_absence_id

        if casenumber:
            cancel_time_submitted = get_open_tasks_from_delta(
                self.db_session,
                casenumber,
                ["Review and Decision Cancel Time Submitted"],
            )

            if cancel_time_submitted:
                return AuditDetails(
                    message="Cancel time submitted",
                    audit_report_reject_notes=self.writeback_mapping.default_reject_notes,
                    audit_report_action=cast(
                        AuditReportAction, self.writeback_mapping.audit_report_action
                    ),
                    audit_report_column=self.writeback_mapping.audit_report_column,
                )

        return None
