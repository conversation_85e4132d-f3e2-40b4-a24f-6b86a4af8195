import massgov.pfml.db as db
import massgov.pfml.delegated_payments.util.payment_preference_util as payment_preference_util
import massgov.pfml.util.logging as logging
from massgov.pfml.db.lookup_data.employees import PaymentMethod
from massgov.pfml.db.lookup_data.payments import PrepaidRegistrationStatus
from massgov.pfml.db.models.payments import ClaimantPrepaidRegistration, PaymentPreference
from massgov.pfml.fineos import create_client
from massgov.pfml.fineos.models.customer_api import (
    EditAccountDetailCommand,
    EditPaymentPreferenceCommand,
    PaymentMethodRequest,
)
from massgov.pfml.util.batch.step import Step

logger = logging.get_logger(__name__)


class UpdateFineosPaymentPreferenceStep(Step):
    def __init__(self, db_session: db.Session, log_entry_db_session: db.Session):
        super().__init__(db_session, log_entry_db_session)

        self.fineos_client = create_client()

    def run_step(self) -> None:
        logger.info("Start -  Sync Prepaid Debit Card Registration step")
        self.update_fineos_payment_preference()
        logger.info("End -  Sync Prepaid Debit Card Registration step")

    def update_fineos_payment_preference(self) -> None:
        registrations = (
            self.db_session.query(ClaimantPrepaidRegistration)
            .filter(
                ClaimantPrepaidRegistration.prepaid_registration_status_id.in_(
                    [
                        PrepaidRegistrationStatus.UPDATE.prepaid_registration_status_id,
                        PrepaidRegistrationStatus.UPDATE_FROM_PRIOR.prepaid_registration_status_id,
                    ]
                )
            )
            .all()
        )

        logger.info(
            f"Found {len(registrations)} prepaid debit card registrations to update in Fineos"
        )

        if registrations:
            for registration in registrations:
                if not registration.account_number or not registration.routing_number:
                    logger.error(
                        f"Prepaid debit card registration for employee {registration.employee_id} is missing account number or routing number"
                    )
                    continue

                payment_preference = (
                    self.db_session.query(PaymentPreference)
                    .filter_by(employee_id=registration.employee_id)
                    .filter_by(default=True)
                    .first()
                )

                if payment_preference:
                    payment_preference.account_number = registration.account_number
                    payment_preference.routing_number = registration.routing_number
                    self.db_session.commit()
                    logger.info(
                        f"Updated account and routing number for employee {registration.employee_id} in payment preference"
                    )
                else:
                    logger.error(
                        f"No payment preference found for employee {registration.employee_id} to update account and routing number"
                    )

                fineos_web_id = payment_preference_util.get_fineos_web_id(
                    self.db_session, registration.employee_id
                )

                if fineos_web_id:
                    logger.info(
                        f"Updating payment preference for employee {registration.employee_id} in Fineos"
                    )
                    fineos_payment_preference = (
                        payment_preference_util.find_default_payment_preference(
                            self.fineos_client, fineos_web_id
                        )
                    )

                    if fineos_payment_preference and fineos_payment_preference.id:
                        edit_payment_preference = EditPaymentPreferenceCommand(
                            paymentMethod=PaymentMethodRequest(
                                name=PaymentMethod.PREPAID_CARD.payment_method_description,
                            ),
                            accountDetail=EditAccountDetailCommand(
                                routingNumber=registration.routing_number,
                                accountNo=registration.account_number,
                            ),
                        )

                        try:
                            response = self.fineos_client.update_customer_payment_preference(
                                user_id=fineos_web_id,
                                payment_preference_id=fineos_payment_preference.id,
                                payment_preference=edit_payment_preference,
                            )
                        except Exception as error:
                            logger.error(
                                f"Failed to update Fineos payment preference for employee {registration.employee_id} in Fineos, error: {error}"
                            )

                            # If payment preference update fails, mark registration as UPDATE_ERROR which means
                            # successfully updated in PFML but failed to update in Fineos
                            registration.prepaid_registration_status_id = (
                                PrepaidRegistrationStatus.UPDATE_ERROR.prepaid_registration_status_id
                            )
                            self.db_session.commit()

                            logger.info(
                                f"Marked the registration for employee {registration.employee_id} as UPDATE_ERROR as Fineos updated failed"
                            )

                            continue
                        else:
                            if (
                                response
                                and response.accountDetail
                                and response.accountDetail.accountNo == registration.account_number
                            ):
                                logger.info(
                                    f"Successfully updated Fineos payment preference for employee {registration.employee_id}"
                                )

                                status = (
                                    PrepaidRegistrationStatus.ACTIVE.prepaid_registration_status_id
                                )
                                if (
                                    registration.prepaid_registration_status_id
                                    == PrepaidRegistrationStatus.UPDATE_FROM_PRIOR.prepaid_registration_status_id
                                ):
                                    status = (
                                        PrepaidRegistrationStatus.INACTIVE.prepaid_registration_status_id
                                    )

                                registration.prepaid_registration_status_id = status
                                self.db_session.commit()

                                logger.info(
                                    f"Successfully updated Registration record for employee {registration.employee_id}"
                                )
                            else:
                                registration.prepaid_registration_status_id = (
                                    PrepaidRegistrationStatus.UPDATE_ERROR.prepaid_registration_status_id
                                )
                                self.db_session.commit()

                                logger.error(
                                    f"Failed to update Fineos payment preference for employee {registration.employee_id}"
                                )
                                logger.info(
                                    f"Marked the registration for employee {registration.employee_id} as UPDATE_ERROR as Fineos updated failed"
                                )
                    else:
                        logger.error(
                            f"No default payment preference found for employee {registration.employee_id} in fineos, skipping"
                        )
                else:
                    logger.error(
                        f"No FINEOS Web ID found for employee {registration.employee_id}, skipping"
                    )
