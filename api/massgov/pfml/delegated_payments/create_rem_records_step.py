import enum
import os
import uuid
import xml.dom.minidom as minidom
from datetime import datetime
from decimal import Decimal
from typing import Dict, List, Optional

from sqlalchemy.orm import joinedload

import massgov.pfml.delegated_payments.delegated_config as payments_config
import massgov.pfml.delegated_payments.delegated_payments_util as payments_util
import massgov.pfml.util.files as file_util
import massgov.pfml.util.logging
from massgov.pfml import db
from massgov.pfml.db.lookup_data.payments import MmarsEventStatusType, MmarsEventType
from massgov.pfml.db.lookup_data.reference_file_type import ReferenceFileType
from massgov.pfml.db.models.ctr.batch_identifier import CtrBatchIdentifier
from massgov.pfml.db.models.employees import Overpayment, ReferenceFile
from massgov.pfml.db.models.payments import <PERSON>marsAuditLog, MmarsEvent, OverpaymentCollection
from massgov.pfml.util.batch.step import Step
from massgov.pfml.util.datetime import get_now_us_eastern

logger = massgov.pfml.util.logging.get_logger(__name__)


class ProcessREMRecordsStep(Step):
    """
    This moves all files from the incoming directories that are populated by
    either Sharepoint or MoveIT to our archive folders for processing.

    Note that this method does not create reference files and leaves that to
    the processes that consume the files.

    https://lwd.atlassian.net/wiki/spaces/API/pages/2365849615/Pickup+Response+Files+Step
    """

    class Metrics(str, enum.Enum):
        TOTAL_REM_RECORDS = "total_rem_records"
        TOTAL_DUPLICATE_REM_RECORDS = "total_duplicate_rem_records"
        TOTAL_REM_RECORDS_PROCESSED = "total_rem_records_processed"
        REM_FILE_PATH = "rem_file_path"
        INF_FILE_PATH = "inf_file_path"

    def __init__(
        self,
        db_session: db.Session,
        log_entry_db_session: db.Session,
        s3_config: payments_config.MmarsS3Config,
    ):
        super().__init__(db_session, log_entry_db_session)
        self.s3_config = s3_config

    def run_step(self):
        logger.info("Processing MMARS REM Records")
        rem_records = self._get_rem_active_records()
        if not rem_records:
            logger.info("No REM Records to process")
            return
        else:
            self.increment(self.Metrics.TOTAL_REM_RECORDS, increment=len(rem_records))
            logger.info(f"Building REM File for {len(rem_records)} RE Records")
            self._build_rem_file(rem_records)
        logger.info("Successfully processed MMARS REM Records")

    def _get_overpayment_collection(
        self, rem_overpayment_id: uuid.UUID
    ) -> List[OverpaymentCollection]:

        rem_overpayment_collections = (
            self.db_session.query(OverpaymentCollection)
            .filter(OverpaymentCollection.overpayment_id == rem_overpayment_id)
            .all()
        )

        return rem_overpayment_collections

    def _get_rem_active_records(self) -> Optional[List[MmarsEvent]]:
        """
        Get RE Records from the database
        """
        re_records = (
            self.db_session.query(MmarsEvent)
            .filter(
                MmarsEvent.mmars_event_type_id == MmarsEventType.REM_TRX.mmars_event_type_id,
                MmarsEvent.mmars_status_type_id
                == MmarsEventStatusType.REM_PENDING.mmars_event_status_type_id,
            )
            .options(
                # only load the `ctr_doc_id` column from overpayment table
                joinedload(MmarsEvent.overpayment).load_only(
                    Overpayment.outstanding_amount, Overpayment.ctr_doc_id
                ),
            )
            .all()
        )

        return re_records

    def _build_rem_file(self, rem_records: List[MmarsEvent]) -> None:
        """
        Build RE Files
        """
        if not rem_records:
            return
        extra = dict()
        rem_total_amount: Decimal = Decimal("0.0")
        try:
            now = get_now_us_eastern()

            # create ctr batch identifier record
            ctr_batch = payments_util.generate_next_batch_identifier(
                self.db_session, now, MmarsEventType.REM_TRX
            )
            rem_file_name = ctr_batch.ctr_batch_identifier
            rem_file_path = os.path.join(
                self.s3_config.pfml_mmars_file_base_location,
                payments_util.MMARS_Constants.REM_FILE_FOLDER,
                rem_file_name,
                rem_file_name + ".dat",
            )
            inf_file_path = os.path.join(
                self.s3_config.pfml_mmars_file_base_location,
                payments_util.MMARS_Constants.REM_FILE_FOLDER,
                rem_file_name,
                rem_file_name + ".inf",
            )
            moveit_rem_file_path = os.path.join(
                self.s3_config.pfml_mmars_file_base_location,
                payments_util.MMARS_Constants.MOVEIT_OUTBOUND_FILE_FOLDER,
                payments_util.MMARS_Constants.MMARS_FILE_NAME.MOVEIT_OUTBOUND_REM_FILE,
            )
            moveit_inf_rem_file_path = os.path.join(
                self.s3_config.pfml_mmars_file_base_location,
                payments_util.MMARS_Constants.MOVEIT_OUTBOUND_FILE_FOLDER,
                payments_util.MMARS_Constants.MMARS_FILE_NAME.MOVEIT_OUTBOUND_REM_INF_FILE,
            )

            # create a reference file for the RE files
            reference_file = ReferenceFile(
                file_location=str(rem_file_path),
                reference_file_type_id=ReferenceFileType.MMARS_REM.reference_file_type_id,
            )
            self.db_session.add(reference_file)

            reference_file.ctr_batch_identifier = ctr_batch

            xml_document = minidom.Document()
            document_root = xml_document.createElement("AMS_DOC_XML_IMPORT_FILE")
            payments_util.add_xml_attributes(document_root, {"VERSION": "1.0"})
            xml_document.appendChild(document_root)
            total_rem_records_processed = 0

            previous_overpayment_id = None
            previous_mmars_status_type_id = None

            rem_document: Optional[minidom.Element] = None

            # sort rem records by overpayment_id so that we can skip duplicate records
            rem_records.sort(key=lambda x: x.overpayment_id)

            for count, rem_record in enumerate(rem_records):
                if not rem_record.overpayment.ctr_doc_id:
                    logger.error(
                        f"ctr_doc_id not found for overpayment_id: {rem_record.overpayment_id}, "
                        f"mmars_event_id: {rem_record.mmars_event_id}"
                    )
                    continue

                # if the overpayment_id is the same as the previous record, it is a duplicate
                if rem_record.overpayment_id == previous_overpayment_id:
                    self.increment(self.Metrics.TOTAL_DUPLICATE_REM_RECORDS)
                    logger.info(
                        f"Skipping REM Record {rem_record.mmars_event_id} as it is a duplicate of the previous record",
                        extra={"overpayment_id": rem_record.overpayment_id},
                    )
                    mmars_audit_log = MmarsAuditLog(
                        mmars_event_id=rem_record.mmars_event_id,
                        mmars_event_type_id=rem_record.mmars_event_type_id,
                        ctr_batch_identifier_id=ctr_batch.ctr_batch_identifier_id,
                        request_data=(
                            rem_document.toprettyxml(indent="   ", encoding="ISO-8859-1").decode(
                                "ISO-8859-1"
                            )
                            if rem_document
                            else None
                        ),
                    )
                    rem_record.mmars_status_type_id = previous_mmars_status_type_id
                    self.db_session.add(mmars_audit_log)

                    # skip duplicate records
                    continue

                ctr_doc_id = rem_record.overpayment.ctr_doc_id

                rem_amount: Decimal = Decimal("0.0")

                rem_overpayment_collections = self._get_overpayment_collection(
                    rem_record.overpayment_id
                )
                rem_overpayment_collection_total_amount = 0
                if rem_record.overpayment.outstanding_amount:
                    # overpayments are stored as negative values but will be sent to MMARS as positive values
                    rem_amount = rem_record.overpayment.outstanding_amount * Decimal("-1.0")

                rem_overpayment_collection_total_amount = sum(
                    rem_overpayment_collection.overpayment_collection_amount
                    for rem_overpayment_collection in rem_overpayment_collections
                )

                if rem_overpayment_collection_total_amount > 0:
                    rem_amount += rem_overpayment_collection_total_amount
                    logger.info(
                        "The overpayment has collections (repayments) associated with it. Total amount of collections are being added rem amount",
                        extra={
                            "overpayment_id": rem_record.overpayment_id,
                            "employee_id": rem_record.employee_id,
                            "mmars_event_id": rem_record.mmars_event_id,
                        },
                    )

                rem_total_amount += rem_amount
                logger.info(
                    f"Processing REM Record {count + 1} of {len(rem_records)}",
                    extra={
                        "employee_id": rem_record.employee_id,
                        "overpayment_case_id": rem_record.overpayment_id,
                    },
                )
                if ctr_doc_id is None:
                    raise ValueError(
                        f"ctr_doc_id cannot be None for overpayment_id: {rem_record.overpayment_id}, "
                        f"mmars_event_id: {rem_record.mmars_event_id}"
                    )

                if (
                    not rem_record.overpayment.claim
                    or not rem_record.overpayment.claim.fineos_absence_id
                ):
                    logger.error(
                        "Fineos Absence ID is missing for the overpayment",
                        extra={
                            "overpayment_id": rem_record.overpayment_id,
                        },
                    )
                    continue
                else:
                    fineos_absence_id = rem_record.overpayment.claim.fineos_absence_id

                rem_document = self._build_individual_rem_record(
                    xml_document, rem_record, rem_amount, now, ctr_doc_id, fineos_absence_id
                )
                document_root.appendChild(rem_document)
                self.increment(self.Metrics.TOTAL_REM_RECORDS_PROCESSED)
                total_rem_records_processed += 1
                mmars_audit_log = MmarsAuditLog(
                    mmars_event_id=rem_record.mmars_event_id,
                    mmars_event_type_id=rem_record.mmars_event_type_id,
                    ctr_batch_identifier_id=ctr_batch.ctr_batch_identifier_id,
                    request_data=rem_document.toprettyxml(
                        indent="   ", encoding="ISO-8859-1"
                    ).decode("ISO-8859-1"),
                )
                rem_record.mmars_status_type_id = (
                    MmarsEventStatusType.REM_SUBMITTED.mmars_event_status_type_id
                )
                previous_overpayment_id = rem_record.overpayment_id
                previous_mmars_status_type_id = rem_record.mmars_status_type_id
                self.db_session.add(mmars_audit_log)

            with file_util.write_file(rem_file_path) as rem_file:
                rem_file.write(
                    xml_document.toprettyxml(indent="   ", encoding="ISO-8859-1").decode(
                        "ISO-8859-1"
                    )
                )
                self.set_metrics({self.Metrics.REM_FILE_PATH: rem_file_path})

        except Exception as e:
            logger.exception("Unable to create RE file")
            raise e

        try:
            inf_dict = self._build_rem_inf(total_rem_records_processed, rem_total_amount, ctr_batch)

            with file_util.write_file(inf_file_path) as inf_file:
                for k, v in inf_dict.items():
                    inf_file.write(f"{k} = {v};\n")
                self.set_metrics({self.Metrics.INF_FILE_PATH: inf_file_path})
        except Exception as e:
            logger.exception("Unable to create RE INF file")
            raise e

        try:
            file_util.copy_file(rem_file_path, moveit_rem_file_path)
            logger.info(
                "REM File copied to MoveIT", extra={"moveit_re_file_path": moveit_rem_file_path}
            )

            file_util.copy_file(inf_file_path, moveit_inf_rem_file_path)
            logger.info(
                "RE INF File copied to MoveIT",
                extra={"moveit_inf_re_file_path": moveit_inf_rem_file_path},
            )
        except Exception as e:
            logger.exception("Unable to copy VCC file to MoveIT")
            raise e

        extra["rem_file_path"] = rem_file_path
        extra["inf_file_path"] = inf_file_path
        extra["reference_file_id"] = str(reference_file.reference_file_id)
        logger.info("REM File created successfully", extra=extra)

    def _build_individual_rem_record(
        self,
        document: minidom.Document,
        rem_record: MmarsEvent,
        rem_line_amount: Decimal,
        now: datetime,
        doc_id: str,
        fineos_absence_id: str,
    ) -> minidom.Element:
        # AMS Document Root element
        financial_year = payments_util.get_financial_year(now)
        per_dc = payments_util.get_month_period(now)
        rem_record.re_amount = rem_line_amount
        rem_record.overpayment.ctr_doc_version_number = (
            rem_record.overpayment.ctr_doc_version_number + 1
        )
        ctr_doc_version_number = str(rem_record.overpayment.ctr_doc_version_number)

        employee = rem_record.employee
        ams_document_attributes = {
            "DOC_ID": doc_id,
            "DOC_VERS_NO": ctr_doc_version_number,
        }
        ams_document_attributes.update(payments_util.MMARS_Constants.rem_ams_doc_attributes.copy())
        ams_document_attributes.update(payments_util.MMARS_Constants.rem_generic_attributes.copy())
        root = document.createElement("AMS_DOCUMENT")
        payments_util.add_xml_attributes(root, ams_document_attributes)

        # REM Document Component: REM_DOC_HDR
        rem_doc_hdr = document.createElement("RE_DOC_HDR")
        payments_util.add_xml_attributes(rem_doc_hdr, {"AMSDataObject": "Y"})
        # Add the individual RE_DOC_HDR values
        rem_doc_hdr_elements = {
            "DOC_ID": doc_id,
            "DOC_NM": rem_record.overpayment.overpayment_casenumber,
            "DOC_VERS_NO": ctr_doc_version_number,
        }
        rem_doc_hdr_elements.update(payments_util.MMARS_Constants.rem_generic_attributes.copy())
        payments_util.add_xml_cdata_elements(rem_doc_hdr, document, rem_doc_hdr_elements)
        root.appendChild(rem_doc_hdr)

        # Add the RE_DOC_VEND
        rem_doc_vend = document.createElement("RE_DOC_VEND")
        payments_util.add_xml_attributes(rem_doc_vend, {"AMSDataObject": "Y"})
        legal_name = f"{employee.first_name} {employee.last_name}"
        # Add the individual RE_DOC_VEND values
        rem_doc_vend_elements = {
            "DOC_ID": doc_id,
            "LGL_NM": legal_name,
            "VEND_CUST_CD": employee.ctr_vendor_customer_code,
            "BPRO_CD": payments_util.MMARS_Constants.BPRO_CD,
            "AD_ID": "AD010",  # hard coded value (to be discussed)
            "AR_DEPT_CD": "EOL",
            "AR_UNIT_CD": "ALL",
            "DOC_VEND_LN_NO": "1",
            "DOC_VERS_NO": ctr_doc_version_number,
        }
        rem_doc_vend_elements.update(payments_util.MMARS_Constants.rem_generic_attributes.copy())
        payments_util.add_xml_cdata_elements(rem_doc_vend, document, rem_doc_vend_elements)
        root.appendChild(rem_doc_vend)

        # Add the RE_DOC_ACTG
        rem_doc_actg = document.createElement("RE_DOC_ACTG")
        payments_util.add_xml_attributes(rem_doc_actg, {"AMSDataObject": "Y"})
        rem_doc_actg_elements = {
            "DOC_ID": doc_id,
            "LN_AM": f"{rem_line_amount:.2f}",
            "BFY": str(financial_year),
            "FY_DC": str(financial_year),
            "PER_DC": str(per_dc),
            "DEPT_CD": payments_util.MMARS_Constants.COMPTROLLER_DEPT_CODE,
            "UNIT_CD": payments_util.MMARS_Constants.COMPTROLLER_UNIT_CODE,
            "ACTG_LN_DSCR": fineos_absence_id,
            "DOC_VERS_NO": ctr_doc_version_number,
        }
        rem_doc_actg_elements.update(payments_util.MMARS_Constants.rem_doc_acctg_attributes.copy())
        rem_doc_actg_elements.update(payments_util.MMARS_Constants.rem_generic_attributes.copy())
        payments_util.add_xml_cdata_elements(rem_doc_actg, document, rem_doc_actg_elements)
        root.appendChild(rem_doc_actg)

        return root

    def _build_rem_inf(
        self,
        rem_records_count: int,
        total_amount: Decimal,
        ctr_batch: CtrBatchIdentifier,
    ) -> Dict[str, str]:
        logger.info("Building RE .INF file for %i employee records", rem_records_count)

        inf_data = {
            "NewMmarsBatchID": ctr_batch.ctr_batch_identifier,
            "NewMmarsBatchDeptCode": payments_util.MMARS_Constants.COMPTROLLER_DEPT_CODE,
            "NewMmarsUnitCode": payments_util.MMARS_Constants.COMPTROLLER_UNIT_CODE,
            "NewMmarsImportDate": ctr_batch.batch_date.strftime("%Y-%m-%d"),
            "NewMmarsTransCode": "RE",
            "NewMmarsTableName": "",
            "NewMmarsTransCount": str(rem_records_count),
            "NewMmarsTransDollarAmount": f"{total_amount:.2f}",
        }
        ctr_batch.inf_data = inf_data
        self.db_session.add(ctr_batch)
        return inf_data
