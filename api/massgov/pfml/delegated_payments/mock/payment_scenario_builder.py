import csv
import os
from dataclasses import dataclass
from datetime import date, timedelta
from typing import Optional, Tuple

import faker

import massgov.pfml.api.util.state_log_util as state_log_util
import massgov.pfml.delegated_payments.delegated_config as payments_config
import massgov.pfml.util.files as file_util
from massgov.pfml import db
from massgov.pfml.db.lookup_data.absences import AbsencePeriodType, AbsenceStatus
from massgov.pfml.db.lookup_data.employees import (
    BankAccountType,
    ClaimType,
    PaymentMethod,
    PrenoteState,
)
from massgov.pfml.db.lookup_data.payments import FineosWritebackTransactionStatus
from massgov.pfml.db.lookup_data.state import Flow, State
from massgov.pfml.db.models.absences import AbsencePeriod, LkAbsencePeriodType, LkAbsenceStatus
from massgov.pfml.db.models.employees import (
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON>loy<PERSON>,
    <PERSON><PERSON>loyer,
    LkBankAccountT<PERSON>,
    Lk<PERSON>laimType,
    LkPaymentMethod,
    LkPrenoteState,
    Payment,
)
from massgov.pfml.db.models.factories import (
    AbsencePeriodFactory,
    BenefitYearFactory,
    ClaimFactory,
    EmployeeFactory,
    EmployeePubEftPairFactory,
    EmployerFactory,
    PubEftFactory,
)
from massgov.pfml.db.models.payments import FineosWritebackDetails
from massgov.pfml.db.models.state import LkState
from massgov.pfml.delegated_payments.audit.delegated_payment_audit_csv import (
    PAYMENT_AUDIT_CSV_HEADERS,
)
from massgov.pfml.delegated_payments.mock.fineos_extract_data import (
    FineosPaymentData,
    create_fineos_claimant_extract_files,
    create_fineos_payment_extract_files,
    create_vbi_document_som_extract_files,
    create_vbi_taskreport_delta_som_extract_files,
    create_vbi_taskreport_som_extract_files,
)
from massgov.pfml.delegated_payments.mock.mock_util import generate_routing_nbr_from_ssn
from massgov.pfml.delegated_payments.mock.scenario_data_generator import MATCH_ADDRESS
from massgov.pfml.util.datetime import get_now_us_eastern

fake = faker.Faker()


def get_current_timestamp_prefix():
    return get_now_us_eastern().strftime("%Y-%m-%d-%H-%M-%S-")


def get_current_date_folder():
    return get_now_us_eastern().strftime("%Y-%m-%d")


def parse_csv(csv_file_path: str) -> tuple[dict[str, str], ...]:
    parsed_csv = csv.DictReader(file_util.open_stream(csv_file_path))
    return tuple(parsed_csv)


def get_audit_report_contents(
    s3_config: payments_config.PaymentsS3Config,
) -> tuple[dict[str, str], ...]:
    payment_audit_report_outbound_folder_path = os.path.join(s3_config.dfml_report_outbound_path)

    audit_report_file_name = "Payment-Audit-Report.csv"

    audit_report_file_path = os.path.join(
        payment_audit_report_outbound_folder_path, audit_report_file_name
    )

    return parse_csv(audit_report_file_path)


def get_pei_writeback_contents(
    s3_config: payments_config.PaymentsS3Config,
) -> tuple[dict[str, str], ...]:
    date_folder = get_current_date_folder()
    timestamp_prefix = get_current_timestamp_prefix()
    writeback_file_path = f"{s3_config.pfml_fineos_writeback_archive_path}sent/{date_folder}/{timestamp_prefix}pei_writeback.csv"
    return parse_csv(writeback_file_path)


def get_writeback_contents_for_payment(payment: Payment) -> Optional[str]:
    "Use the I/C value of the payment to find the row in the writeback file"
    pass


PAYMENT_C_VALUE = "7326"
ABSENCE_PERIOD_C_VALUE = "14449"

CLAIM_TYPE_MAP = {
    ClaimType.FAMILY_LEAVE.claim_type_id: "Family",
    ClaimType.MEDICAL_LEAVE.claim_type_id: "Employee",
}


@dataclass
class PaymentScenarioCsvData:
    claim_data: FineosPaymentData
    payment_data: FineosPaymentData


class PaymentScenario:

    _db_session: db.Session
    _state_after_extract: LkState
    _description: str
    _payment_i_value: str
    _claim: Claim
    _employee: Employee
    _employer: Employer
    _payment_method: LkPaymentMethod
    _bank_account_type: LkBankAccountType

    def __init__(
        self,
        db_session: db.Session,
        state_after_extract: LkState,
        description: str,
        claim: Claim,
        employee: Employee,
        employer: Employer,
        payment_method: LkPaymentMethod,
        bank_account_type: LkBankAccountType,
    ) -> None:
        self._db_session = db_session
        self._state_after_extract = state_after_extract
        self._description = description
        self._payment_i_value = str(fake.unique.random_int())
        self._claim = claim
        self._employee = employee
        self._employer = employer
        self._payment_method = payment_method
        self._bank_account_type = bank_account_type

    def _get_claimant_csv_data(self) -> FineosPaymentData:
        assert self._claim.absence_periods
        absence_period: AbsencePeriod = self._claim.absence_periods[0]
        assert self._employee.tax_identifier
        ssn = self._employee.tax_identifier.tax_identifier
        absence_case_number = self._claim.fineos_absence_id
        payment_method = self._payment_method.payment_method_description
        date_of_birth = "1991-01-01 12:00:00"
        address_1 = ""
        address_2 = ""
        city = ""
        state = ""
        post_code = ""
        routing_nbr = generate_routing_nbr_from_ssn(ssn)
        account_nbr = ssn
        natinsno = ssn
        default_payment_pref = "Y"
        customer_number = ssn
        absence_case_number = absence_case_number
        assert self._claim.fineos_absence_status
        absence_case_status = self._claim.fineos_absence_status.absence_status_description
        absence_case_status_id = self._claim.fineos_absence_status.absence_status_id
        leave_request_evidence = "Satisfied"
        leave_request_start = "2021-01-01 12:00:00"
        leave_request_end = "2021-04-01 12:00:00"
        notification_number = f"NTN-{absence_case_number}"
        fineos_employer_id = self._employer.fineos_employer_id

        fineos_employee_first_name: Optional[str] = self._employee.fineos_employee_first_name
        fineos_employee_middle_name: Optional[str] = self._employee.fineos_employee_middle_name
        fineos_employee_last_name: Optional[str] = self._employee.fineos_employee_last_name

        return FineosPaymentData(
            generate_defaults=True,
            absence_period_c_value=ABSENCE_PERIOD_C_VALUE,
            absence_period_i_value=absence_period.fineos_absence_period_index_id,
            date_of_birth=date_of_birth,
            payment_method=payment_method,
            account_type=self._bank_account_type.bank_account_type_description,
            address_1=address_1,
            address_2=address_2,
            city=city,
            state=state,
            post_code=post_code,
            routing_nbr=routing_nbr,
            account_nbr=account_nbr,
            ssn=natinsno,
            default_payment_pref=default_payment_pref,
            customer_number=customer_number,
            absence_case_number=absence_case_number,
            absence_case_status=absence_case_status,
            absence_case_status_id=absence_case_status_id,
            notification_number=notification_number,
            employer_customer_num=fineos_employer_id,
            fineos_employee_first_name=fineos_employee_first_name,
            fineos_employee_middle_name=fineos_employee_middle_name,
            fineos_employee_last_name=fineos_employee_last_name,
            leave_request_evidence=leave_request_evidence,
            leave_request_start=leave_request_start,
            leave_request_end=leave_request_end,
            leave_request_decision="Approved",
            leave_request_id=absence_period.fineos_leave_request_id,
            include_requested_absence_som=True,
            absence_intake_source="",
        )

    def _get_payment_csv_data(self) -> FineosPaymentData:
        assert self._claim.absence_periods
        absence_period: AbsencePeriod = self._claim.absence_periods[0]
        assert self._employee.tax_identifier
        ssn = self._employee.tax_identifier.tax_identifier.replace("-", "")
        payment_method = self._payment_method.payment_method_description
        payment_date = get_now_us_eastern()
        payment_start_period = payment_date
        payment_end_period = payment_date + timedelta(days=15)
        c_value = PAYMENT_C_VALUE
        i_value = self._payment_i_value
        absence_case_id = self._claim.fineos_absence_id
        is_eft = self._payment_method.payment_method_id == PaymentMethod.ACH.payment_method_id
        routing_nbr = generate_routing_nbr_from_ssn(ssn) if is_eft else ""
        account_nbr = ssn if is_eft else ""
        account_type = self._bank_account_type.bank_account_type_description
        payment_amount = "100.00"
        event_type = "PaymentOut"
        payee_identifier = "Social Security Number"
        event_reason = "Automatic Main Payment"
        amalgamationc = ""
        payment_type = ""
        mock_address = MATCH_ADDRESS
        payee_name = (
            str(self._employee.fineos_employee_first_name)
            + " "
            + str(self._employee.fineos_employee_last_name)
        )
        return FineosPaymentData(
            generate_defaults=True,
            c_value=c_value,
            i_value=i_value,
            include_vpei=True,
            include_claim_details=True,
            include_payment_details=True,
            include_requested_absence=False,
            include_absence_case=False,
            tin=ssn,
            absence_case_number=absence_case_id,
            payment_address_1=mock_address["line_1"],
            payment_address_2=mock_address["line_2"],
            city=mock_address["city"],
            state=mock_address["state"],
            zip_code=mock_address["zip"],
            payment_method=payment_method,
            payment_date=payment_date.strftime("%Y-%m-%d %H:%M:%S"),
            payment_amount=payment_amount,
            routing_nbr=routing_nbr,
            account_nbr=account_nbr,
            account_type=account_type,
            payment_start=payment_start_period.strftime("%Y-%m-%d %H:%M:%S"),
            payment_end=payment_end_period.strftime("%Y-%m-%d %H:%M:%S"),
            event_type=event_type,
            event_reason=event_reason,
            payee_identifier=payee_identifier,
            payee_name=payee_name,
            amalgamationc=amalgamationc,
            payment_type=payment_type,
            absence_period_c_value=ABSENCE_PERIOD_C_VALUE,
            absence_period_i_value=absence_period.fineos_absence_period_index_id,
            leave_request_id=absence_period.fineos_leave_request_id,
            include_requested_absence_som=True,
        )

    def get_csv_data(self) -> PaymentScenarioCsvData:
        claimant_data = self._get_claimant_csv_data()
        payment_data = self._get_payment_csv_data()
        return PaymentScenarioCsvData(claim_data=claimant_data, payment_data=payment_data)

    def _get_audit_row(
        self, s3_config: payments_config.PaymentsS3Config
    ) -> Optional[dict[str, str]]:
        audit_report_contents = get_audit_report_contents(s3_config)
        audit_report_row = None
        for row in audit_report_contents:
            if row["I Value"] == self._payment_i_value:
                audit_report_row = row
                break
        return audit_report_row

    def _get_writeback_row(
        self, s3_config: payments_config.PaymentsS3Config
    ) -> Optional[dict[str, str]]:
        writeback_rows = get_pei_writeback_contents(s3_config)
        writeback_row = None
        for row in writeback_rows:
            if row["pei_I_Value"] == self._payment_i_value:
                writeback_row = row
                break

        return writeback_row

    def _get_writeback_details(self) -> Optional[FineosWritebackDetails]:
        payment = self._get_payment()
        return (
            self._db_session.query(FineosWritebackDetails)
            .filter(FineosWritebackDetails.payment_id == payment.payment_id)
            .one_or_none()
        )

    def _get_expected_audit_row(self) -> dict[Optional[str], str]:
        payment = self._get_payment()
        return {
            PAYMENT_AUDIT_CSV_HEADERS.pfml_payment_id: str(payment.payment_id),
            PAYMENT_AUDIT_CSV_HEADERS.fineos_customer_number: str(
                self._employee.fineos_customer_number
            ),
            PAYMENT_AUDIT_CSV_HEADERS.rejected_by_program_integrity: "",
            PAYMENT_AUDIT_CSV_HEADERS.skipped_by_program_integrity: "",
            PAYMENT_AUDIT_CSV_HEADERS.dor_fineos_name_mismatch_details: "",
            PAYMENT_AUDIT_CSV_HEADERS.dua_additional_income_details: "",
            PAYMENT_AUDIT_CSV_HEADERS.dia_additional_income_details: "",
            PAYMENT_AUDIT_CSV_HEADERS.payment_date_mismatch_details: "",
            PAYMENT_AUDIT_CSV_HEADERS.is_preapproved: "",
            PAYMENT_AUDIT_CSV_HEADERS.preapproval_issues: "There were less than three previous payments",
            PAYMENT_AUDIT_CSV_HEADERS.waiting_week: "",
        }

    def _get_expected_writeback_row(self) -> dict[str, str]:
        return {
            "pei_C_Value": PAYMENT_C_VALUE,
            "pei_I_Value": self._payment_i_value,
            "transactionStatus": FineosWritebackTransactionStatus.PAYMENT_AUDIT_IN_PROGRESS.description,
            "status": "",
            "transStatusDate": get_now_us_eastern().date().strftime("%Y-%m-%d %H:%M:%S"),
        }

    def _get_payment(self) -> Payment:
        payment = (
            self._db_session.query(Payment)
            .filter(Payment.fineos_pei_i_value == self._payment_i_value)
            .one_or_none()
        )

        assert payment, f"No payment was found for scenario: {self._description}"
        return payment

    def verify_after_process_fineos_extracts(
        self, s3_config: payments_config.PaymentsS3Config
    ) -> None:

        latest_state = state_log_util.get_latest_state_log_in_flow(
            self._get_payment(), Flow.DELEGATED_PAYMENT, self._db_session
        )
        assert (
            latest_state and latest_state.end_state
        ), f"Scenario: {self._description} did not have a latest state"
        assert (
            latest_state.end_state_id == self._state_after_extract.state_id
        ), f"Scenario: {self._description} did not have the expected state after processing extracts. Expected:{self._state_after_extract.state_description}, got: {latest_state.end_state.state_description} "
        audit_row = self._get_audit_row(s3_config)
        assert (
            audit_row
        ), f"Payment for scenario: {self._description} was not found in the audit report"
        expected_audit_row = self._get_expected_audit_row()
        for header, expected_value in expected_audit_row.items():
            assert header
            actual_value = audit_row[header]
            assert (
                expected_value == actual_value
            ), f"Audit report contents did not match expections for {self._description}. {header} contained {actual_value}, expected {expected_value}"
        writeback_row = self._get_writeback_row(s3_config)
        assert (
            writeback_row
        ), f"Writeback row was not found in CSV for scenario: {self._description}"
        writeback_detail = self._get_writeback_details()
        assert writeback_detail, f"Writeback detail was not found for scenario: {self._description}"
        expected_writeback_row = self._get_expected_writeback_row()
        for header, expected_value in expected_writeback_row.items():
            actual_value = writeback_row[header]
            assert (
                expected_value == actual_value
            ), f"Writeback row contents did not match expectations for {self._description}. {header} contained {actual_value}, expected {expected_value}"

    def verify_after_pub_process(self):
        pass

    def verify_after_pub_returns(self):
        pass


# Goals
# 1. Increase readability of what data conditions the scenario is setting up
# 2. Remove test assertions that rely on checking counts of certain models
# 3. Make the scenarios responsible for creating and loading CSV files
# 4. Improve the error message when assertions fail
# 5. Make the scenarios responsible for assertions at each stage of the extract process


@dataclass
class PaymentConfiguration:
    bank_account_type: LkBankAccountType
    prenote_state: LkPrenoteState
    payment_method: LkPaymentMethod


@dataclass
class ClaimConfiguration:
    absence_status: LkAbsenceStatus
    absence_period_type: LkAbsencePeriodType
    claim_type: LkClaimType


class PaymentScenarioBuilder:

    _description: str
    _db_session: db.Session
    _payment_configuration: PaymentConfiguration
    _claim_configuration: ClaimConfiguration
    _pub_eft: Optional[Tuple[LkPrenoteState, LkBankAccountType]]
    _is_preapproved: bool
    _expected_after_extract_end_state: LkState

    def set_description(self, description: str) -> "PaymentScenarioBuilder":
        self._description = description
        return self

    def set_payment_configuration(
        self, configuration: PaymentConfiguration
    ) -> "PaymentScenarioBuilder":
        self._payment_configuration = configuration
        return self

    def set_claim_configuration(
        self, configuration: ClaimConfiguration
    ) -> "PaymentScenarioBuilder":
        self._claim_configuration = configuration
        return self

    def set_is_preapproved(self, is_preapproved: bool) -> "PaymentScenarioBuilder":
        self._is_preapproved = is_preapproved
        return self

    def set_expected_after_extract_end_state(self, end_state: LkState) -> "PaymentScenarioBuilder":
        self._expected_after_extract_end_state = end_state
        return self

    def set_expected_end_state_after_pub_process(
        self, end_state: LkState
    ) -> "PaymentScenarioBuilder":
        return self

    def build(self, db_session: db.Session) -> PaymentScenario:
        employer: Employer = EmployerFactory.create()
        employee: Employee = EmployeeFactory.create()
        BenefitYearFactory.create(
            employee=employee, start_date=date(2020, 12, 27), end_date=date(2021, 12, 25)
        )

        assert employee.tax_identifier
        if (
            self._payment_configuration.payment_method.payment_method_id
            == PaymentMethod.ACH.payment_method_id
        ):
            pub_eft = PubEftFactory.create(
                routing_nbr=generate_routing_nbr_from_ssn(employee.tax_identifier.tax_identifier),
                account_nbr=employee.tax_identifier.tax_identifier,
                bank_account_type_id=self._payment_configuration.bank_account_type.bank_account_type_id,
                prenote_state_id=self._payment_configuration.prenote_state.prenote_state_id,
            )
            EmployeePubEftPairFactory.create(employee=employee, pub_eft=pub_eft)

        claim_start_date = date(2021, 5, 1)

        claim_end_date = claim_start_date + timedelta(weeks=26, days=-1)

        claim: Claim = ClaimFactory.create(
            employee=employee,
            employer=employer,
            claim_type_id=ClaimType.FAMILY_LEAVE.claim_type_id,
            fineos_absence_status_id=self._claim_configuration.absence_status.absence_status_id,
            claim_start_date=claim_start_date,
            claim_end_date=claim_end_date,
        )
        AbsencePeriodFactory.create(
            claim=claim,
            absence_period_start_date=claim.claim_start_date,
            absence_period_end_date=claim.claim_end_date,
            absence_period_type_id=self._claim_configuration.absence_period_type.absence_period_type_id,
        )

        return PaymentScenario(
            db_session,
            self._expected_after_extract_end_state,
            self._description,
            claim=claim,
            employee=employee,
            employer=employer,
            payment_method=self._payment_configuration.payment_method,
            bank_account_type=self._payment_configuration.bank_account_type,
        )


HAPPY_PATH_SCENARIO_BUILDER = (
    PaymentScenarioBuilder()
    .set_claim_configuration(
        ClaimConfiguration(
            absence_status=AbsenceStatus.APPROVED,
            absence_period_type=AbsencePeriodType.CONTINUOUS,
            claim_type=ClaimType.FAMILY_LEAVE,
        )
    )
    .set_payment_configuration(
        PaymentConfiguration(BankAccountType.CHECKING, PrenoteState.APPROVED, PaymentMethod.ACH)
    )
    .set_is_preapproved(True)
    .set_expected_after_extract_end_state(State.DELEGATED_PAYMENT_PAYMENT_AUDIT_REPORT_SENT)
)


def generate_csv(
    scenarios: list[PaymentScenario], s3_config: payments_config.PaymentsS3Config
) -> None:
    now = get_now_us_eastern()
    fineos_data_export_path = s3_config.fineos_data_export_path

    fineos_extract_data = [scenario.get_csv_data() for scenario in scenarios]
    create_fineos_claimant_extract_files(
        [data.claim_data for data in fineos_extract_data], fineos_data_export_path, now
    )
    create_fineos_payment_extract_files(
        [data.payment_data for data in fineos_extract_data], fineos_data_export_path, now
    )

    create_vbi_taskreport_som_extract_files([], fineos_data_export_path, now)
    create_vbi_taskreport_delta_som_extract_files([], fineos_data_export_path, now)
    create_vbi_document_som_extract_files([], fineos_data_export_path, now)


def run_pub_extract():
    pass


def run_pub_process():
    pass


def run_pub_returns():
    pass
