import copy
import csv
import io
import os
from collections import OrderedDict
from dataclasses import dataclass
from datetime import date, datetime, timedelta
from typing import Any, Dict, List, Optional, cast

import faker
from sqlalchemy.sql.expression import true

import massgov.pfml.delegated_payments.extracts.fineos_extract_config as fineos_extract_config
import massgov.pfml.features as features
import massgov.pfml.util.files as file_util
import massgov.pfml.util.logging
from massgov.pfml.api.eligibility.benefit_year_dates import calculate_benefit_year_dates
from massgov.pfml.db.lookup_data.absences import AbsenceStatus
from massgov.pfml.db.lookup_data.employees import (
    BankAccountType,
    PaymentMethod,
    PaymentTransactionType,
)
from massgov.pfml.db.models.factories import FineosExtractVbiEntitlementPeriodSomFactory
from massgov.pfml.db.models.payments import (
    FineosExtractVbiEntitlementPeriodSom,
    RequestedAbsenceUnion,
)
from massgov.pfml.delegated_payments.delegated_fineos_payment_extract import (
    PAYEE_CHILD_SUPPORT_NAME,
)
from massgov.pfml.delegated_payments.mock.mock_util import MockData, generate_routing_nbr_from_ssn
from massgov.pfml.delegated_payments.mock.scenario_data_generator import (
    INVALID_ADDRESS,
    LONG_ADDRESS,
    MATCH_ADDRESS,
    NO_MATCH_ADDRESS,
    ScenarioData,
)
from massgov.pfml.fineos.tasks import Constants as TaskStatuses
from massgov.pfml.util.datetime import get_now_us_eastern

logger = massgov.pfml.util.logging.get_logger(__name__)

fake = faker.Faker()
fake.seed_instance(1212)


def random_unique_int(min=1, max=999_999_999):
    return str(fake.unique.random_int(min=min, max=max))


# We may want additional columns here from what we validate
# so these field names extended from the constant values
# This is mainly for new columns we want to implement logic for
# but FINEOS hasn't yet made a change to a particular extract
EMPLOYEE_FEED_FIELD_NAMES = (
    fineos_extract_config.FineosExtractConstants.EMPLOYEE_FEED.field_names
    + [
        "EFFECTIVEFROM",
        "EFFECTIVETO",
    ]
)
REQUESTED_ABSENCE_SOM_FIELD_NAMES = (
    fineos_extract_config.FineosExtractConstants.VBI_REQUESTED_ABSENCE_SOM.field_names
)
# 1099 Files
VBI_1099DATA_SOM_FIELD_NAMES = (
    fineos_extract_config.FineosExtractConstants.VBI_1099DATA_SOM.field_names
)

# Payment files
PEI_FIELD_NAMES = fineos_extract_config.FineosExtractConstants.VPEI.field_names
PEI_PAYMENT_DETAILS_FIELD_NAMES = (
    fineos_extract_config.FineosExtractConstants.PAYMENT_DETAILS.field_names
)
PAYMENT_LINE_FIELD_NAMES = fineos_extract_config.FineosExtractConstants.PAYMENT_LINE.field_names
PEI_CLAIM_DETAILS_FIELD_NAMES = (
    fineos_extract_config.FineosExtractConstants.CLAIM_DETAILS.field_names
)
REQUESTED_ABSENCE_FIELD_NAMES = (
    fineos_extract_config.FineosExtractConstants.VBI_REQUESTED_ABSENCE.field_names
)

# IAWW files
VBI_LEAVE_PLAN_REQUESTED_ABSENCE_FIELD_NAMES = (
    fineos_extract_config.FineosExtractConstants.VBI_LEAVE_PLAN_REQUESTED_ABSENCE.field_names
)
PAID_LEVAVE_INSTRUCTION_FIELD_NAMES = (
    fineos_extract_config.FineosExtractConstants.PAID_LEAVE_INSTRUCTION.field_names
)

VBI_TASKREPORT_SOM_EXTRACT_FIELD_NAMES = (
    fineos_extract_config.FineosExtractConstants.VBI_TASKREPORT_SOM.field_names
)

VBI_DOCUMENT_SOM_EXTRACT_FIELD_NAMES = (
    fineos_extract_config.FineosExtractConstants.VBI_DOCUMENT_SOM.field_names
)

VBI_LEAVESUMMARY_FIELD_NAMES = (
    fineos_extract_config.FineosExtractConstants.VBI_LEAVESUMMARY.field_names
)

V_PAIDLEAVEINSTRUCTION_SOM_FIELD_NAMES = (
    fineos_extract_config.FineosExtractConstants.V_PAIDLEAVEINSTRUCTION_SOM.field_names
)

V_PAIDLEAVEINSTRUCTION_FIELD_NAMES = (
    fineos_extract_config.FineosExtractConstants.PAID_LEAVE_INSTRUCTION.field_names
)

VBI_BENEFITPERIOD_FIELD_NAMES = (
    fineos_extract_config.FineosExtractConstants.VBI_BENEFITPERIOD.field_names
)

VBI_PAIDLEAVECASEINFO_FIELD_NAMES = (
    fineos_extract_config.FineosExtractConstants.VBI_PAIDLEAVECASEINFO_SOM.field_names
)


xml_1099_packed_data = """<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
                <ns2:DataSet xmlns:ns2="http://www.fineos.com/ta/common/external">
                    <EnumObject>
                        <name>ENUM</name>
                        <prompt>Provide the reason for the 1099G reissue</prompt>
                        <value>512512002</value>
                        <radiobutton>false</radiobutton>
                    </EnumObject>
                    <StringObject>
                        <name>SPACE1</name>
                        <prompt></prompt>
                        <value></value>
                        <width>0</width>
                    </StringObject>
                    <StringObject>
                        <name>1099GReissueSHARED</name>
                        <prompt>1099-G Reissue</prompt>
                        <value>1099-G Reissue</value>
                        <width>0</width>
                    </StringObject>
                    <StringObject>
                        <name>ReissueInformation3</name>
                        <prompt>Reissue information</prompt>
                        <value>Reissue will mail a new copy of the 1099-G form to the customer with the new 1099-G form that was created from the payment resolution.</value>
                        <width>250</width>
                    </StringObject>
                    <StringObject>
                    <name>INITIALPAGE_HID</name>
                    <prompt></prompt>
                    <value></value>
                    <width>99999</width>
                    </StringObject>
                    <StringObject>
                        <name>ReissueInformation2</name>
                        <prompt>Reissue Information</prompt>
                        <value>Reissue will mail a new copy of the 1099-G form to the customer using the new address that has been added to the customer record.</value>
                        <width>250</width>
                    </StringObject>
                    <StringObject>
                        <name>ReissueInformation1</name>
                        <prompt>Reissue Information</prompt>
                        <value>Reissue will mail a new copy of the 1099-G form to the customer. The mailing address and payment information will be the same as the previous copy sent.</value>
                        <width>250</width>
                    </StringObject>
                    <StringObject>
                        <name>Confirmation</name>
                        <prompt>Confirmation</prompt>
                        <value>Before submitting this eForm please make sure the address change or payment reconciliation is complete in FINEOS.</value>
                        <width>115</width>
                    </StringObject>
                </ns2:DataSet>"""


class FineosPaymentData(MockData):
    """
    FINEOS Data contains all data we care about for processing a FINEOS extract
    With no parameters, will generate a valid, mostly-random valid standard payment
    Parameters can be overriden by specifying them as kwargs. If you do not want
    random generated values, set generate_defaults to False in the constructor.
    """

    def __init__(
        self,
        generate_defaults=True,
        include_vpei=True,
        include_claim_details=True,
        include_payment_details=True,
        include_payment_lines=True,
        include_requested_absence=True,
        include_employee_feed=True,
        include_employee_feed_delta=True,
        include_absence_case=True,
        include_1099_data=True,
        include_vbi_tasks=False,
        include_vbi_documents=False,
        include_requested_absence_som=True,
        include_vbi_leavesummary=True,
        include_v_paidleaveinstruction_record=True,
        include_v_paidleaveinstruction_som_record=True,
        include_vbi_paidleavecaseinfo_records=True,
        include_vbi_benefitperiod=True,
        include_vbi_leaveplanrequestabsence=True,
        **kwargs,
    ):
        super().__init__(generate_defaults, **kwargs)
        self.include_vpei = include_vpei
        self.include_claim_details = include_claim_details
        self.include_payment_details = include_payment_details
        self.include_payment_lines = include_payment_lines
        self.include_requested_absence = include_requested_absence
        self.include_employee_feed = include_employee_feed
        self.include_employee_feed_delta = include_employee_feed_delta
        self.include_absence_case = include_absence_case
        self.include_1099_data = include_1099_data
        self.include_vbi_tasks = include_vbi_tasks
        self.include_vbi_documents = include_vbi_documents
        self.include_requested_absence_som = include_requested_absence_som
        self.include_vbi_leavesummary = include_vbi_leavesummary
        self.include_v_paidleaveinstruction_record = include_v_paidleaveinstruction_record
        self.include_vbi_paidleavecaseinfo_records = include_vbi_paidleavecaseinfo_records
        self.include_vbi_benefitperiod = include_vbi_benefitperiod
        self.include_vbi_leaveplanrequestabsence = include_vbi_leaveplanrequestabsence
        self.include_v_paidleaveinstruction_som_record = include_v_paidleaveinstruction_som_record
        self.kwargs = kwargs

        # Values used in various places below
        absence_num = random_unique_int()
        self.ssn = self.get_value("ssn", fake.ssn().replace("-", ""))

        # Absence case values
        self.absence_case_number = self.get_value("absence_case_number", f"ABS-{absence_num}")
        self.notification_number = self.get_value("notification_number", f"NTN-{absence_num}")

        self.absence_period_class_id = self.get_value("absence_period_c_value", "14449")
        self.absence_period_index_id = self.get_value("absence_period_i_value", random_unique_int())

        # TODO remove with feature flag (https://lwd.atlassian.net/browse/PFMLPB-22515)
        features.initialize()
        fineos_is_running_v24 = features.get_config().fineos.is_running_v24
        if fineos_is_running_v24:
            self.absence_period_id = self.get_value("absence_period_id", random_unique_int())

        self.claim_type = self.get_value("claim_type", "Family")

        self.absence_case_creation_date = self.get_value(
            "absence_case_creation_date", "2020-12-01 07:00:00"
        )
        self.absence_case_status_id = self.get_value(
            "absence_case_status_id", AbsenceStatus.APPROVED.absence_status_id
        )
        self.absence_case_status = self.get_value(
            "absence_case_status", AbsenceStatus.APPROVED.absence_status_description
        )
        self.absence_intake_source = self.get_value("absence_intake_source", "")

        self.leave_request_id = self.get_value("leave_request_id", random_unique_int())
        self.leave_request_decision = self.get_value("leave_request_decision", "Approved")
        self.leave_request_evidence = self.get_value("leave_request_evidence", "Satisfied")
        self.leave_request_start = self.get_value("leave_request_start", "2021-01-01 12:00:00")
        self.leave_request_end = self.get_value("leave_request_end", "2021-04-01 12:00:00")

        self.employer_customer_num = self.get_value("employer_customer_num", random_unique_int())

        self.absence_period_type = self.get_value("absence_period_type", "Time off period")
        self.absence_reason_qualifier_one = self.get_value(
            "absence_reason_qualifier_one", "Work Related"
        )
        self.absence_reason_qualifier_two = self.get_value(
            "absence_reason_qualifier_two", "Medical Related"
        )
        self.absence_reason = self.get_value(
            "absence_reason", "Serious Health Condition - Employee"
        )

        # Employee values
        self.date_of_birth = self.get_value("date_of_birth", "1980-01-01 12:00:00")
        self.default_payment_pref = self.get_value("default_payment_pref", "Y")
        self.customer_number = self.get_value("customer_number", fake.ssn().replace("-", ""))
        self.tin = self.get_value("tin", self.ssn)

        self.fineos_employee_first_name = self.get_value(
            "fineos_employee_first_name", fake.first_name()
        )
        self.fineos_employee_middle_name = self.get_value("fineos_employee_middle_name", "")
        self.fineos_employee_last_name = self.get_value(
            "fineos_employee_last_name", fake.last_name()
        )

        self.payee_name = self.get_value("payee_name", fake.company())

        self.fineos_address_effective_from = self.get_value(
            "fineos_address_effective_from", "2021-01-01 12:00:00"
        )
        self.fineos_address_effective_to = self.get_value(
            "fineos_address_effective_to", "2022-01-01 12:00:00"
        )
        self.organization_unit_name = self.get_value("organization_unit_name", "")
        self.document_type_1099 = self.get_value("document_type_1099", "1099 Request")
        self.packed_data_1099 = self.get_value("packed_data_1099", xml_1099_packed_data)
        self.creation_date = self.get_value("creation_date", "2022-06-02 12:00:00")
        self.last_update_date = self.get_value("last_update_date", "2022-06-02 12:00:00")
        self.mass_id_number = self.get_value(
            "mass_id_number", f"S{random_unique_int(min=10_000_000, max=99_999_999)}"
        )
        self.out_of_state_id_number = self.get_value(
            "out_of_state_id_number", f"A{random_unique_int(min=100_000_000, max=999_999_999)}"
        )

        # if an Employee was provided, overwrite the values to match the employee
        if employee := self.get_value("employee", None):
            self.employee = employee  # Also make it accessible for easier testing
            self.ssn = employee.tax_identifier.tax_identifier
            self.customer_number = employee.fineos_customer_number
            self.fineos_employee_first_name = employee.fineos_employee_first_name
            self.fineos_employee_middle_name = employee.fineos_employee_middle_name
            self.fineos_employee_last_name = employee.fineos_employee_last_name
            self.date_of_birth = employee.date_of_birth.strftime("%Y-%m-%d %H:%M:%S")
            self.mass_id_number = employee.mass_id_number
            self.out_of_state_id_number = employee.out_of_state_id_number

        # Payment method values (used for payment and employee files)
        self.address_1 = self.get_value(
            "address_1", f"{fake.building_number()} {fake.street_name()} {fake.street_suffix()}"
        )
        self.payment_address_1 = self.get_value("payment_address_1", self.address_1)
        self.address_2 = self.get_value("address_2", "")
        self.payment_address_2 = self.get_value("payment_address_2", "")
        self.city = self.get_value("city", fake.city())
        self.state = self.get_value("state", fake.state_abbr().upper())
        self.zip_code = self.get_value("zip_code", fake.zipcode_plus4())
        self.payment_method = self.get_value("payment_method", "Elec Funds Transfer")

        self.routing_nbr = self.get_value("routing_nbr", generate_routing_nbr_from_ssn(self.ssn))
        self.account_nbr = self.get_value("account_nbr", self.ssn)
        self.account_type = self.get_value("account_type", "Checking")

        # if a PubEft was provided, overwrite the values to match the PubEft
        if pub_eft := self.get_value("pub_eft", None):
            self.routing_nbr = pub_eft.routing_nbr
            self.account_nbr = pub_eft.account_nbr
            self.account_type = BankAccountType.get_description(pub_eft.bank_account_type_id)

        # Payment values
        self.c_value = self.get_value("c_value", "7326")
        self.i_value = self.get_value("i_value", random_unique_int())

        self.payment_details_c_value = self.get_value("payment_details_c_value", "7806")
        self.payment_details_i_value = self.get_value(
            "payment_details_i_value", random_unique_int()
        )

        self.payment_date = self.get_value("payment_date", "2021-01-01 12:00:00")
        self.payment_amount = self.get_value("payment_amount", "100.00")
        self.balancing_amount = self.get_value("balancing_amount", self.payment_amount)
        self.business_net_amount = self.get_value("business_net_amount", self.payment_amount)

        self.event_type = self.get_value("event_type", "PaymentOut")
        self.payee_identifier = self.get_value("payee_identifier", "Social Security Number")
        self.event_reason = self.get_value("event_reason", "Automatic Main Payment")
        self.amalgamationc = self.get_value("amalgamationc", "")  # Default blank
        self.payment_type = self.get_value("payment_type", "")

        self.payment_start_period = self.get_value("payment_start", "2021-01-01 12:00:00")
        self.payment_end_period = self.get_value("payment_end", "2021-01-07 12:00:00")

        self.payment_line_c_value = self.get_value("payment_line_c_value", "7692")
        self.payment_line_i_value = self.get_value("payment_line_i_value", random_unique_int())
        self.payment_line_type = self.get_value("payment_line_type", "Auto Gross Entitlement")
        self.payment_line_amount = self.get_value("payment_line_amount", self.payment_amount)

        # VBI Task Record Som values
        self.task_status = self.get_value("task_status", "")
        self.task_tasktypename = self.get_value("task_tasktypename", "")
        self.task_closeddate = self.get_value("task_closeddate", "")
        self.task_subjectreference = self.get_value("task_subjectreference", "")

        # VBI Document Som values
        self.document_documenttype = self.get_value("document_documenttype", "")
        self.document_indexid = self.get_value("document_indexid", "")
        self.document_lastupdatedate = self.get_value("document_lastupdatedate", "")
        self.document_description = self.get_value("document_description", "")

        # VBI Leave Summary values
        self.leave_request_original_request = self.get_value("leave_request_original_request", "0")
        self.leave_request_description = self.get_value("leave_request_description", "")
        self.leave_request_name = self.get_value("leave_request_name", "")
        self.leave_request_notes = self.get_value("leave_request_notes", "")
        self.leave_request_challenge_details = self.get_value("leave_request_challenge_details", "")
        self.leave_request_challenge_made = self.get_value("leave_request_challenge_made", "0")
        self.leave_request_diagnosis = self.get_value("leave_request_diagnosis", "")
        self.leave_request_start_original = self.get_value("leave_request_start_original", "1")
        self.leave_request_employer_notified = self.get_value(
            "leave_request_employer_notified", "0"
        )
        self.leave_request_employee_notified_date = self.get_value(
            "leave_request_employee_notified_date", ""
        )
        self.leave_request_employee_notified_method = self.get_value(
            "leave_request_employee_notified_method", ""
        )
        self.leave_request_approval_reason = self.get_value("leave_request_approval_reason", "")
        self.leave_request_denial_reason = self.get_value("leave_request_denial_reason", "")
        self.leave_request_withdrawal_reason = self.get_value("leave_request_withdrawal_reason", "")
        self.leave_request_event_type = self.get_value("leave_request_event_type", "")
        self.leave_request_updated_by_user_id = self.get_value(
            "leave_request_updated_by_user_id", ""
        )
        self.primary_relationship_name = self.get_value("primary_relationship_name", "")
        self.primary_relationship_qual1 = self.get_value("primary_relationship_qual1", "")
        self.primary_relationship_qual2 = self.get_value("primary_relationship_qual2", "")
        self.primary_relationship_cover = self.get_value("primary_relationship_cover", "")
        self.secondary_relationship_name = self.get_value("secondary_relationship_name", "")
        self.secondary_relationship_qual1 = self.get_value("secondary_relationship_qual1", "")
        self.secondary_relationship_qual2 = self.get_value("secondary_relationship_qual2", "")
        self.secondary_relationship_cover = self.get_value("secondary_relationship_cover", "")
        self.emploment_classid = self.get_value("employment_classid", random_unique_int())
        self.emploment_indexid = self.get_value("employment_indexid", random_unique_int())
        self.healthcare_provider_custno = self.get_value("healthcare_provider_custno", "")

        self.casenumber = self.get_value("casenumber", "")
        self.sitfitoptin = self.get_value("sitfitoptin", "")
        self.c_paidleaveinstr = self.get_value("c_paidleaveinstr", "")
        self.i_paidleaveinstr = self.get_value("i_paidleaveinstr", "")
        self.lastupdatedate = self.get_value("lastupdatedate", "")
        self.averagedaysworked = self.get_value("averagedaysworked", "")
        self.averageweeklywage_monamt = self.get_value("averageweeklywage_monamt", "")
        self.extawwpart2_monamt = self.get_value("extawwpart2_monamt", "")
        self.benefitwaitingperiod = self.get_value("benefitwaitingperiod", "")
        self.c_selectedleaveplan = self.get_value("c_selectedleaveplan", random_unique_int())
        self.i_selectedleaveplan = self.get_value("i_selectedleaveplan", random_unique_int())

        # VBI Paid Leave Case Info SOM
        self.absencecase_id = self.get_value(
            "absencecase_id", self.notification_number + "-" + self.absence_case_number
        )
        self.paidleave_casenumber = self.get_value("paidleave_casenumber", "")
        self.absence_casestatus = self.absence_case_status
        self.employer_customerno = self.get_value("employer_customerno", "")
        self.leaverequest_evidenceresulttype = self.get_value("leaverequest_evidenceresulttype", "")
        self.absencereason_coverage = self.get_value("absencereason_coverage", "")
        self.absenceperiod_start = self.get_value("absenceperiod_start", "")
        self.absenceperiod_end = self.get_value("absenceperiod_end", "")
        self.lr_decreaon = self.get_value("lr_decreaon", "")
        self.evidence = self.get_value("evidence", "")
        self.leaverequest_evidencereceipt = self.get_value("leaverequest_evidencereceipt", "")
        self.leaverequest_denialreason = self.get_value("leaverequest_denialreason", "")
        self.mixeddecision = self.get_value("mixeddecision", "")

        # VBI Benefit Period values
        self.total_period_days = self.get_value("total_period_days", "")
        self.days_in_period = self.get_value("days_in_period", "")
        self.period_from_date = self.get_value("period_from_date", "")
        self.period_to_date = self.get_value("period_to_date", "")
        self.benefit_period_type = self.get_value("benefit_period_type", "")
        self.benefit_case_number = self.get_value("benefit_case_number", "")

    def get_vpei_record(self):
        vpei_record = OrderedDict()
        if self.include_vpei:
            vpei_record["C"] = self.c_value
            vpei_record["I"] = self.i_value
            vpei_record["PAYEECUSTOMER"] = self.tin
            vpei_record["PAYEESOCNUMBE"] = self.tin
            vpei_record["PAYMENTADD1"] = self.payment_address_1
            vpei_record["PAYMENTADD2"] = self.payment_address_2
            vpei_record["PAYMENTADD4"] = self.city
            vpei_record["PAYMENTADD6"] = self.state
            vpei_record["PAYMENTPOSTCO"] = self.zip_code
            vpei_record["PAYMENTMETHOD"] = self.payment_method
            vpei_record["PAYMENTDATE"] = self.payment_date
            vpei_record["AMOUNT_MONAMT"] = self.payment_amount
            vpei_record["PAYEEBANKSORT"] = self.routing_nbr
            vpei_record["PAYEEACCOUNTN"] = self.account_nbr
            vpei_record["PAYEEACCOUNTT"] = self.account_type
            vpei_record["PAYEEFULLNAME"] = self.payee_name
            vpei_record["EVENTTYPE"] = self.event_type
            vpei_record["PAYEEIDENTIFI"] = self.payee_identifier
            vpei_record["EVENTREASON"] = self.event_reason
            vpei_record["AMALGAMATIONC"] = self.amalgamationc
            vpei_record["PAYMENTTYPE"] = self.payment_type
        return vpei_record

    def get_claim_details_record(self):
        claim_detail_record = OrderedDict()
        if self.include_claim_details:
            claim_detail_record["PECLASSID"] = self.c_value
            claim_detail_record["PEINDEXID"] = self.i_value
            claim_detail_record["ABSENCECASENU"] = self.absence_case_number
            claim_detail_record["LEAVEREQUESTI"] = self.leave_request_id
        return claim_detail_record

    def get_payment_details_record(self):
        payment_detail_record = OrderedDict()
        if self.include_payment_details:
            payment_detail_record["C"] = self.payment_details_c_value
            payment_detail_record["I"] = self.payment_details_i_value

            payment_detail_record["PECLASSID"] = self.c_value
            payment_detail_record["PEINDEXID"] = self.i_value

            payment_detail_record["PAYMENTSTARTP"] = self.payment_start_period
            payment_detail_record["PAYMENTENDPER"] = self.payment_end_period

            payment_detail_record["BALANCINGAMOU_MONAMT"] = self.balancing_amount
            payment_detail_record["BUSINESSNETBE_MONAMT"] = self.business_net_amount

        return payment_detail_record

    def get_payment_line_record(self):
        payment_line_record = OrderedDict()
        if self.include_payment_lines:
            payment_line_record["C"] = self.payment_line_c_value
            payment_line_record["I"] = self.payment_line_i_value

            payment_line_record["AMOUNT_MONAMT"] = self.payment_line_amount
            payment_line_record["LINETYPE"] = self.payment_line_type

            payment_line_record["C_PYMNTEIF_PAYMENTLINES"] = self.c_value
            payment_line_record["I_PYMNTEIF_PAYMENTLINES"] = self.i_value
            payment_line_record["PAYMENTDETAILCLASSID"] = self.payment_details_c_value
            payment_line_record["PAYMENTDETAILINDEXID"] = self.payment_details_i_value

        return payment_line_record

    def get_requested_absence_record(self):
        requested_absence_record = OrderedDict()
        if self.include_requested_absence:
            requested_absence_record["ABSENCEPERIOD_CLASSID"] = self.absence_period_class_id
            requested_absence_record["ABSENCEPERIOD_INDEXID"] = self.absence_period_index_id
            requested_absence_record["LEAVEREQUEST_DECISION"] = self.leave_request_decision
            requested_absence_record["LEAVEREQUEST_ID"] = self.leave_request_id
            requested_absence_record["ABSENCEREASON_COVERAGE"] = self.claim_type
            requested_absence_record["ABSENCE_CASECREATIONDATE"] = self.absence_case_creation_date
            requested_absence_record["ABSENCEPERIOD_TYPE"] = self.absence_period_type
            requested_absence_record["ABSENCEREASON_QUALIFIER1"] = self.absence_reason_qualifier_one
            requested_absence_record["ABSENCEREASON_QUALIFIER2"] = self.absence_reason_qualifier_two
            requested_absence_record["ABSENCEREASON_NAME"] = self.absence_reason
            requested_absence_record["ABSENCE_CASENUMBER"] = self.absence_case_number
            requested_absence_record["NOTIFICATION_CASENUMBER"] = self.notification_number
            requested_absence_record["EMPLOYEE_CUSTOMERNO"] = self.customer_number
            requested_absence_record["EMPLOYER_CUSTOMERNO"] = self.employer_customer_num
            requested_absence_record["ABSENCE_CASESTATUS"] = self.absence_case_status
            requested_absence_record["ABSENCE_INTAKESOURCE"] = self.absence_intake_source
            # TODO: Update this to use absenceperiod_start and absenceperiod_end, update related tests
            requested_absence_record["ABSENCEPERIOD_START"] = self.leave_request_start
            requested_absence_record["ABSENCEPERIOD_END"] = self.leave_request_end
        return requested_absence_record

    def get_employee_feed_record(self):
        employee_feed_record = OrderedDict()
        if self.include_employee_feed:
            employee_feed_record["C"] = self.c_value
            employee_feed_record["I"] = self.i_value
            employee_feed_record["DEFPAYMENTPREF"] = self.default_payment_pref
            employee_feed_record["CUSTOMERNO"] = self.customer_number
            employee_feed_record["NATINSNO"] = self.ssn
            employee_feed_record["DATEOFBIRTH"] = self.date_of_birth
            employee_feed_record["PAYMENTMETHOD"] = self.payment_method
            employee_feed_record["SORTCODE"] = self.routing_nbr
            employee_feed_record["ACCOUNTNO"] = self.account_nbr
            employee_feed_record["ACCOUNTTYPE"] = self.account_type
            employee_feed_record["FIRSTNAMES"] = self.fineos_employee_first_name
            employee_feed_record["INITIALS"] = self.fineos_employee_middle_name
            employee_feed_record["LASTNAME"] = self.fineos_employee_last_name
            employee_feed_record["EFFECTIVEFROM"] = self.fineos_address_effective_from
            employee_feed_record["EFFECTIVETO"] = self.fineos_address_effective_to
            employee_feed_record["EXTMASSID"] = self.mass_id_number
            employee_feed_record["EXTOUTOFSTATEID"] = self.out_of_state_id_number
            employee_feed_record["ADDRESS1"] = self.address_1
            employee_feed_record["ADDRESS4"] = self.city
            employee_feed_record["ADDRESS6"] = self.state
            employee_feed_record["POSTCODE"] = self.zip_code

        return employee_feed_record

    def get_employee_feed_delta_record(self):
        if self.include_employee_feed_delta:
            return self.get_employee_feed_record()

    def get_requested_absence_som_record(self):
        requested_absence_record = OrderedDict()
        if self.include_absence_case:
            requested_absence_record["ABSENCE_CASENUMBER"] = self.absence_case_number
            requested_absence_record["ABSENCEREASON_COVERAGE"] = self.claim_type
            requested_absence_record["NOTIFICATION_CASENUMBER"] = self.notification_number
            requested_absence_record["ABSENCE_CASESTATUS"] = self.absence_case_status
            requested_absence_record["LEAVEREQUEST_EVIDENCERESULTTYPE"] = (
                self.leave_request_evidence
            )
            # TODO: Update this to use absenceperiod_start and absenceperiod_end, update related tests
            requested_absence_record["ABSENCEPERIOD_START"] = self.leave_request_start
            requested_absence_record["ABSENCEPERIOD_END"] = self.leave_request_end
            requested_absence_record["EMPLOYEE_CUSTOMERNO"] = self.customer_number
            requested_absence_record["EMPLOYER_CUSTOMERNO"] = self.employer_customer_num
            requested_absence_record["ABSENCEPERIOD_CLASSID"] = self.absence_period_class_id
            requested_absence_record["ABSENCEPERIOD_INDEXID"] = self.absence_period_index_id
            requested_absence_record["LEAVEREQUEST_ID"] = self.leave_request_id
            requested_absence_record["ORGUNIT_NAME"] = self.organization_unit_name

            # TODO remove with feature flag (https://lwd.atlassian.net/browse/PFMLPB-22515)
            features.initialize()
            fineos_is_running_v24 = features.get_config().fineos.is_running_v24
            if fineos_is_running_v24:
                requested_absence_record["ABSENCEPERIOD_ID"] = self.absence_period_id

        return requested_absence_record

    def get_requested_absence_union(self) -> RequestedAbsenceUnion:
        requested_absence_som = self.get_requested_absence_som_record()
        requested_absence = self.get_requested_absence_record()
        requested_absence_union = OrderedDict()
        requested_absence_union.update(requested_absence)
        requested_absence_union.update(requested_absence_som)
        requested_absence_union_lower = {
            key.lower(): value for key, value in dict(requested_absence_union).items()
        }
        # Code expects that the serial ids would result from this union
        requested_absence_union_lower["vbi_requested_absence_som_serial_id"] = 123
        requested_absence_union_lower["vbi_requested_absence_serial_id"] = 456
        return RequestedAbsenceUnion(**requested_absence_union_lower)

    def get_vbi_1099_data_record(self):
        vbi_1099_data_record = OrderedDict()
        if self.include_1099_data:
            vbi_1099_data_record["C"] = self.c_value
            vbi_1099_data_record["CUSTOMERNO"] = self.customer_number
            vbi_1099_data_record["FIRSTNAMES"] = self.fineos_employee_first_name
            vbi_1099_data_record["LASTNAME"] = self.fineos_employee_last_name
            vbi_1099_data_record["PACKEDDATA"] = self.packed_data_1099
            vbi_1099_data_record["DOCUMENTTYPE"] = self.document_type_1099
            vbi_1099_data_record["CREATIONDATE"] = self.creation_date
            vbi_1099_data_record["LASTUPDATEDATE"] = self.last_update_date

        return vbi_1099_data_record

    def get_vbi_benefitperiod_record(self) -> Dict[str, Any]:
        vbi_benefitperiod_record = OrderedDict()
        if self.include_vbi_benefitperiod:
            vbi_benefitperiod_record["CLASSID"] = self.c_value
            vbi_benefitperiod_record["INDEXID"] = self.i_value
            vbi_benefitperiod_record["LASTUPDATEDATE"] = self.last_update_date
            vbi_benefitperiod_record["TOTALPERIODDA"] = self.total_period_days
            vbi_benefitperiod_record["DAYSINPERIOD"] = self.days_in_period
            vbi_benefitperiod_record["PERIODFROMDAT"] = self.period_from_date
            vbi_benefitperiod_record["PERIODTODATE"] = self.period_to_date
            vbi_benefitperiod_record["PERIODTYPE"] = self.benefit_period_type
            vbi_benefitperiod_record["BENEFITCASENUMBER"] = self.benefit_case_number

        return vbi_benefitperiod_record

    def get_vbi_leavesummary_record(self) -> Dict[str, Any]:
        vbi_leavesummary_record = OrderedDict()
        if self.include_vbi_leavesummary:
            vbi_leavesummary_record["LEAVEREQUEST_CLASSID"] = self.c_value
            vbi_leavesummary_record["LEAVEREQUEST_INDEXID"] = self.i_value
            vbi_leavesummary_record["LEAVEREQUEST_LASTUPDATEDATE"] = self.last_update_date
            vbi_leavesummary_record["LEAVEREQUEST_ORIGINALREQUEST"] = (
                self.leave_request_original_request
            )
            vbi_leavesummary_record["LEAVEREQUEST_ID"] = self.leave_request_id
            vbi_leavesummary_record["LEAVEREQUEST_DESCRIPTION"] = self.leave_request_description
            vbi_leavesummary_record["LEAVEREQUEST_DECISION"] = self.leave_request_decision
            vbi_leavesummary_record["LEAVEREQUEST_NAME"] = self.leave_request_name
            vbi_leavesummary_record["LEAVEREQUEST_NOTES"] = self.leave_request_notes
            vbi_leavesummary_record["LEAVEREQUEST_CHALLENGE_DETAILS"] = (
                self.leave_request_challenge_details
            )
            vbi_leavesummary_record["LEAVEREQUEST_CHALLENGE_MADE"] = (
                self.leave_request_challenge_made
            )
            vbi_leavesummary_record["LEAVEREQUEST_DIAGNOSIS"] = self.leave_request_diagnosis
            vbi_leavesummary_record["LEAVEREQUEST_DI_FIRSTDATE"] = self.leave_request_start
            vbi_leavesummary_record["LEAVEREQUEST_DI_FIRSTDATE_OR"] = (
                self.leave_request_start_original
            )
            vbi_leavesummary_record["LEAVEREQUEST_EMPLOYER_NOTIFIED"] = (
                self.leave_request_employer_notified
            )
            vbi_leavesummary_record["LEAVEREQUEST_ER_NOTIFIEDDATE"] = (
                self.leave_request_employee_notified_date
            )
            vbi_leavesummary_record["LEAVEREQUEST_ER_NOTIFIEDMETHOD"] = (
                self.leave_request_employee_notified_method
            )
            vbi_leavesummary_record["LEAVEREQUEST_APPROVAL_REASON"] = (
                self.leave_request_approval_reason
            )
            vbi_leavesummary_record["LEAVEREQUEST_DENIAL_REASON"] = self.leave_request_denial_reason
            vbi_leavesummary_record["LEAVEREQUEST_WITHDRAWAL_REASON"] = (
                self.leave_request_withdrawal_reason
            )
            vbi_leavesummary_record["LEAVEREQUEST_EVENT_TYPE"] = self.leave_request_event_type
            vbi_leavesummary_record["LEAVEREQUEST_UPDATEDBYUSERID"] = (
                self.leave_request_updated_by_user_id
            )
            vbi_leavesummary_record["NOTIFICATION_CASENUMBER"] = self.notification_number
            vbi_leavesummary_record["ABSENCE_CASENUMBER"] = self.absence_case_number
            vbi_leavesummary_record["PRIMARY_RELATIONSHIP_NAME"] = self.primary_relationship_name
            vbi_leavesummary_record["PRIMARY_RELATIONSHIP_QUAL1"] = self.primary_relationship_qual1
            vbi_leavesummary_record["PRIMARY_RELATIONSHIP_QUAL2"] = self.primary_relationship_qual2
            vbi_leavesummary_record["PRIMARY_RELATIONSHIP_COVER"] = self.primary_relationship_cover
            vbi_leavesummary_record["SECONDARY_RELATIONSHIP_NAME"] = (
                self.secondary_relationship_name
            )
            vbi_leavesummary_record["SECONDARY_RELATIONSHIP_QUAL1"] = (
                self.secondary_relationship_qual1
            )
            vbi_leavesummary_record["SECONDARY_RELATIONSHIP_QUAL2"] = (
                self.secondary_relationship_qual2
            )
            vbi_leavesummary_record["SECONDARY_RELATIONSHIP_COVER"] = (
                self.secondary_relationship_cover
            )
            vbi_leavesummary_record["EMPLOYMENT_CLASSID"] = self.emploment_classid
            vbi_leavesummary_record["EMPLOYMENT_INDEXID"] = self.emploment_indexid
            vbi_leavesummary_record["HEALTHCARE_PROVIDER_CUSTNO"] = self.healthcare_provider_custno

        return vbi_leavesummary_record

    def get_vbi_task_record(self) -> Dict[str, Any]:
        vbi_task_record = OrderedDict()

        if self.include_vbi_tasks:
            vbi_task_record["TASKID"] = str(fake.random_int(1, 999_999_999))
            vbi_task_record["CASENUMBER"] = self.absence_case_number
            vbi_task_record["STATUS"] = self.task_status
            vbi_task_record["TASKTYPENAME"] = self.task_tasktypename
            vbi_task_record["CLOSEDDATE"] = self.task_closeddate
            vbi_task_record["SUBJECTREFERENCE"] = self.task_subjectreference

        return vbi_task_record

    def get_vbi_document_record(self) -> Dict[str, Any]:
        vbi_document_record = OrderedDict()

        if self.include_vbi_documents:
            vbi_document_record["CASENUMBER"] = self.get_value(
                "document_casenumber", self.notification_number
            )
            vbi_document_record["DOCUMENTTYPE"] = self.get_value("document_documenttype", "")
            vbi_document_record["INDEXID"] = self.get_value("document_indexid", "")
            vbi_document_record["LASTUPDATEDATE"] = self.get_value("document_lastupdatedate", "")
            vbi_document_record["DESCRIPTION"] = self.get_value("document_description", "")

        return vbi_document_record

    def get_v_paidleaveinstruction_som_record(self) -> Dict[str, Any]:
        v_paidleavecaseinstruction_som = OrderedDict()

        if self.include_v_paidleaveinstruction_som_record:
            v_paidleavecaseinstruction_som["CASENUMBER"] = self.absence_case_number
            v_paidleavecaseinstruction_som["SITFITOPTIN"] = self.sitfitoptin
            v_paidleavecaseinstruction_som["C_PAIDLEAVEINSTR"] = self.c_paidleaveinstr
            v_paidleavecaseinstruction_som["I_PAIDLEAVEINSTR"] = self.i_paidleaveinstr
            v_paidleavecaseinstruction_som["LASTUPDATEDATE"] = self.lastupdatedate
            v_paidleavecaseinstruction_som["AVERAGEDAYSWORKED"] = self.averagedaysworked
            v_paidleavecaseinstruction_som["AVERAGEWEEKLYWAGE_MONAMT"] = (
                self.averageweeklywage_monamt
            )
            v_paidleavecaseinstruction_som["EXTAWWPART2_MONAMT"] = self.extawwpart2_monamt
            v_paidleavecaseinstruction_som["BENEFITWAITINGPERIOD"] = self.benefitwaitingperiod
            v_paidleavecaseinstruction_som["C_SELECTEDLEAVEPLAN"] = self.c_selectedleaveplan
            v_paidleavecaseinstruction_som["I_SELECTEDLEAVEPLAN"] = self.i_selectedleaveplan

        return v_paidleavecaseinstruction_som

    def get_v_paidleaveinstruction_record(self) -> Dict[str, Any]:
        v_paidleavecaseinstruction = OrderedDict()

        if self.include_v_paidleaveinstruction_record:
            v_paidleavecaseinstruction["C"] = self.c_paidleaveinstr
            v_paidleavecaseinstruction["I"] = self.i_paidleaveinstr
            v_paidleavecaseinstruction["AVERAGEWEEKLYWAGE_MONAMT"] = self.averageweeklywage_monamt
            v_paidleavecaseinstruction["C_SELECTEDLEAVEPLAN"] = self.c_selectedleaveplan
            v_paidleavecaseinstruction["I_SELECTEDLEAVEPLAN"] = self.i_selectedleaveplan

        return v_paidleavecaseinstruction

    def get_vbi_paidleavecaseinfo_record(self) -> Dict[str, Any]:
        vbi_paidleavecaseinfo_record = OrderedDict()

        if self.include_vbi_paidleavecaseinfo_records:
            vbi_paidleavecaseinfo_record["NOTIFICATION_CASENUMBER"] = self.notification_number
            vbi_paidleavecaseinfo_record["ABSENCECASE_ID"] = self.absencecase_id
            vbi_paidleavecaseinfo_record["ABSENCE_CASENUMBER"] = self.absence_case_number
            vbi_paidleavecaseinfo_record["PAIDLEAVE_CASENUMBER"] = self.paidleave_casenumber
            vbi_paidleavecaseinfo_record["ABSENCE_CASESTATUS"] = self.absence_casestatus
            vbi_paidleavecaseinfo_record["EMPLOYEE_CUSTOMERNO"] = self.customer_number
            vbi_paidleavecaseinfo_record["EMPLOYER_CUSTOMERNO"] = self.employer_customerno
            vbi_paidleavecaseinfo_record["LEAVEREQUEST_EVIDENCERESULTTYPE"] = (
                self.leaverequest_evidenceresulttype
            )
            vbi_paidleavecaseinfo_record["ABSENCEREASON_COVERAGE"] = self.absencereason_coverage
            vbi_paidleavecaseinfo_record["ABSENCEPERIOD_START"] = self.absenceperiod_start
            vbi_paidleavecaseinfo_record["ABSENCEPERIOD_END"] = self.absenceperiod_end
            vbi_paidleavecaseinfo_record["LR_DECREAON"] = self.lr_decreaon
            vbi_paidleavecaseinfo_record["EVIDENCE"] = self.evidence
            vbi_paidleavecaseinfo_record["LEAVEREQUEST_EVIDENCERECEIPT"] = (
                self.leaverequest_evidencereceipt
            )
            vbi_paidleavecaseinfo_record["LEAVEREQUEST_DENIALREASON"] = (
                self.leaverequest_denialreason
            )
            vbi_paidleavecaseinfo_record["LEAVEREQUEST_ID"] = self.leave_request_id
            vbi_paidleavecaseinfo_record["MIXEDDECISION"] = self.mixeddecision

        return vbi_paidleavecaseinfo_record

    def get_vbi_leaveplanrequestedabsence_record(self):
        vbi_leaveplanrequestedabsence_record = OrderedDict()

        if self.include_vbi_leaveplanrequestabsence:
            vbi_leaveplanrequestedabsence_record["SELECTEDPLAN_CLASSID"] = self.c_selectedleaveplan
            vbi_leaveplanrequestedabsence_record["SELECTEDPLAN_INDEXID"] = self.i_selectedleaveplan
            vbi_leaveplanrequestedabsence_record["SELECTEDPLAN_LASTUPDATEDATE"] = (
                self.lastupdatedate
            )
            vbi_leaveplanrequestedabsence_record["SELECTEDPLAN_ADJUDICAT_RESULT"] = ""
            vbi_leaveplanrequestedabsence_record["SELECTEDPLAN_ADJUDICATION_NOTE"] = ""
            vbi_leaveplanrequestedabsence_record["SELECTEDPLAN_UPDATEDBYUSERID"] = (
                self.leave_request_updated_by_user_id
            )
            vbi_leaveplanrequestedabsence_record["LEAVEPLAN_CLASSID"] = self.c_value
            vbi_leaveplanrequestedabsence_record["LEAVEPLAN_INDEXID"] = self.i_value
            vbi_leaveplanrequestedabsence_record["LEAVEPLAN_DISPLAYREFERENCE"] = ""
            vbi_leaveplanrequestedabsence_record["LEAVEPLAN_SHORTNAME"] = ""
            vbi_leaveplanrequestedabsence_record["LEAVEPLAN_LONGNAME"] = ""
            vbi_leaveplanrequestedabsence_record["LEAVEPLAN_ALIAS"] = ""
            vbi_leaveplanrequestedabsence_record["LEAVEPLAN_LEAVEGROUP"] = ""
            vbi_leaveplanrequestedabsence_record["LEAVEPLAN_LEAVECATEGORY"] = ""
            vbi_leaveplanrequestedabsence_record["LEAVEPLAN_LEAVETYPE"] = ""
            vbi_leaveplanrequestedabsence_record["LEAVEPLAN_STATE"] = ""
            vbi_leaveplanrequestedabsence_record["LEAVEPLAN_JOBPROTECTION"] = ""
            vbi_leaveplanrequestedabsence_record["LEAVEREQUEST_ID"] = self.leave_request_id

        return vbi_leaveplanrequestedabsence_record


class FineosIAWWData(MockData):
    """
    This contains all data we care about for extracting IAWW data from FINEOS
    With no parameters, it will generate a valid, mostly-random data set
    Parameters can be overriden by specifying them as kwargs
    """

    def __init__(self, **kwargs):
        super().__init__(true, **kwargs)
        self.kwargs = kwargs

        self.c_value = self.get_value("c_value", "7326")
        self.i_value = self.get_value("i_value", random_unique_int())
        self.leaveplan_c_value = self.get_value("leaveplan_c_value", "14437")

        leaveplan_i_value = random_unique_int()
        self.leaveplan_i_value_instruction = self.get_value(
            "leaveplan_i_value_instruction", leaveplan_i_value
        )
        self.leaveplan_i_value_request = self.get_value(
            "leaveplan_i_value_request", leaveplan_i_value
        )
        self.leave_request_id_value = self.get_value("leave_request_id_value", random_unique_int())
        self.aww_value = self.get_value("aww_value", random_unique_int())

    def get_leave_plan_request_absence_record(self):
        leave_plan_request_absence_record = OrderedDict()

        leave_plan_request_absence_record["SELECTEDPLAN_CLASSID"] = self.leaveplan_c_value
        leave_plan_request_absence_record["SELECTEDPLAN_INDEXID"] = self.leaveplan_i_value_request
        leave_plan_request_absence_record["LEAVEREQUEST_ID"] = self.leave_request_id_value

        return leave_plan_request_absence_record

    def get_vpaid_leave_instruction_record(self):
        vpaid_leave_instruction_record = OrderedDict()

        vpaid_leave_instruction_record["C"] = self.c_value
        vpaid_leave_instruction_record["I"] = self.i_value
        vpaid_leave_instruction_record["AVERAGEWEEKLYWAGE_MONAMT"] = self.aww_value
        vpaid_leave_instruction_record["C_SELECTEDLEAVEPLAN"] = self.leaveplan_c_value
        vpaid_leave_instruction_record["I_SELECTEDLEAVEPLAN"] = self.leaveplan_i_value_instruction

        return vpaid_leave_instruction_record


@dataclass
class FineosExportCsvWriter:
    file_name: str
    file_path: str
    file: io.TextIOWrapper
    csv_writer: csv.DictWriter


def _create_file(
    folder_path: str, filename_prefix: str, file_name: str, column_names: List[str]
) -> FineosExportCsvWriter:
    csv_file_path = os.path.join(folder_path, f"{filename_prefix}{file_name}")
    logger.info("writing CSV file %s", csv_file_path)
    csv_file = file_util.write_file(csv_file_path)
    csv_writer = csv.DictWriter(csv_file, fieldnames=column_names)
    csv_writer.writeheader()

    return FineosExportCsvWriter(
        file_name=file_name, file_path=csv_file_path, file=csv_file, csv_writer=csv_writer
    )


def create_fineos_payment_extract_files(
    fineos_payments_dataset: List[FineosPaymentData], folder_path: str, date_of_extract: datetime
) -> None:
    # prefix string
    date_prefix = date_of_extract.strftime("%Y-%m-%d-%H-%M-%S-")

    # create the extract files
    pei_writer = _create_file(
        folder_path,
        date_prefix,
        fineos_extract_config.FineosExtractConstants.VPEI.file_name,
        PEI_FIELD_NAMES,
    )
    pei_payment_details_writer = _create_file(
        folder_path,
        date_prefix,
        fineos_extract_config.FineosExtractConstants.PAYMENT_DETAILS.file_name,
        PEI_PAYMENT_DETAILS_FIELD_NAMES,
    )
    pei_payment_line_writer = _create_file(
        folder_path,
        date_prefix,
        fineos_extract_config.FineosExtractConstants.PAYMENT_LINE.file_name,
        PAYMENT_LINE_FIELD_NAMES,
    )
    pei_claim_details_writer = _create_file(
        folder_path,
        date_prefix,
        fineos_extract_config.FineosExtractConstants.CLAIM_DETAILS.file_name,
        PEI_CLAIM_DETAILS_FIELD_NAMES,
    )

    # write the respective rows
    for fineos_payments_data in fineos_payments_dataset:
        if vpei_record := fineos_payments_data.get_vpei_record():
            pei_writer.csv_writer.writerow(vpei_record)

        pei_payment_details_writer.csv_writer.writerow(
            fineos_payments_data.get_payment_details_record()
        )
        pei_payment_line_writer.csv_writer.writerow(fineos_payments_data.get_payment_line_record())
        pei_claim_details_writer.csv_writer.writerow(
            fineos_payments_data.get_claim_details_record()
        )

    # close the files
    pei_writer.file.close()
    pei_payment_line_writer.file.close()
    pei_payment_details_writer.file.close()
    pei_claim_details_writer.file.close()


def generate_payment_extract_files(
    scenario_dataset: List[ScenarioData],
    folder_path: str,
    date_of_extract: datetime,
    round: int = 1,
) -> None:
    # create the scenario based fineos data for the extract
    fineos_payments_dataset: List[FineosPaymentData] = []

    for scenario_data in scenario_dataset:
        scenario_descriptor = scenario_data.scenario_descriptor

        employee = scenario_data.employee

        prior_payment = scenario_data.payment if round > 1 else None

        if employee.tax_identifier is None:
            raise Exception("Expected employee with tin")

        ssn = employee.tax_identifier.tax_identifier.replace("-", "")
        if scenario_descriptor.employee_in_payment_extract_missing_in_db:
            ssn = "*********"  # SSNs are generated by counting up, so this won't be found

        payment_method = scenario_descriptor.payment_method.payment_method_description

        payment_date = (
            datetime.strptime(scenario_data.payment_date, "%Y-%m-%d %H:%M:%S")
            if scenario_data.payment_date
            else get_now_us_eastern()
        )

        # This'll be a new payment based on different C/I values
        if round > 1 and scenario_descriptor.has_additional_payment_in_period and prior_payment:
            payment_start_period = cast(date, prior_payment.period_start_date)
            payment_end_period = cast(date, prior_payment.period_end_date)
            c_value = scenario_data.additional_payment_c_value
            i_value = scenario_data.additional_payment_i_value
            absence_case_id = scenario_data.additional_payment_absence_case_id
        else:
            payment_start_period = payment_date
            payment_end_period = payment_date + timedelta(days=15)
            c_value = scenario_data.payment_c_value
            i_value = scenario_data.payment_i_value
            absence_case_id = scenario_data.absence_case_id

        is_eft = scenario_descriptor.payment_method == PaymentMethod.ACH
        routing_nbr = generate_routing_nbr_from_ssn(ssn) if is_eft else ""
        account_nbr = ssn if is_eft else ""
        account_type = (
            scenario_descriptor.account_type.bank_account_type_description
            if is_eft and scenario_descriptor.account_type
            else ""
        )

        if scenario_descriptor.payment_close_to_cap:
            # The cap is $850.00
            payment_amount = "800.00"
        elif scenario_descriptor.payment_over_cap:
            payment_amount = "860.00"
        else:
            payment_amount = "100.00"

        if scenario_descriptor.negative_payment_amount:
            payment_amount = "-" + payment_amount

        event_type = "PaymentOut"
        payee_identifier = "Social Security Number"
        event_reason = "Automatic Main Payment"
        amalgamationc = ""
        payment_type = ""

        if scenario_descriptor.is_adhoc_payment:
            payment_type = "Adhoc"

        claim_type = scenario_descriptor.claim_type

        if scenario_descriptor.payment_transaction_type == PaymentTransactionType.ZERO_DOLLAR:
            payment_amount = "0"
        elif scenario_descriptor.payment_transaction_type == PaymentTransactionType.OVERPAYMENT:
            event_type = "Overpayment"
        elif (
            scenario_descriptor.payment_transaction_type
            == PaymentTransactionType.OVERPAYMENT_ADJUSTMENT
        ):
            event_type = "Overpayment Adjustment"
            payment_amount = "50"
        elif (
            scenario_descriptor.payment_transaction_type
            == PaymentTransactionType.OVERPAYMENT_ADJUSTMENT_CANCELLATION
        ):
            event_type = "Overpayment Adjustment Cancellation"
            payment_amount = "0"
        elif (
            scenario_descriptor.payment_transaction_type
            == PaymentTransactionType.OVERPAYMENT_RECOVERY
        ):
            event_type = (
                PaymentTransactionType.OVERPAYMENT_RECOVERY.payment_transaction_type_description
            )
        elif (
            scenario_descriptor.payment_transaction_type
            == PaymentTransactionType.OVERPAYMENT_ACTUAL_RECOVERY
        ):
            event_type = (
                PaymentTransactionType.OVERPAYMENT_ACTUAL_RECOVERY.payment_transaction_type_description
            )
        elif (
            scenario_descriptor.payment_transaction_type
            == PaymentTransactionType.OVERPAYMENT_RECOVERY_CANCELLATION
        ):
            event_type = (
                PaymentTransactionType.OVERPAYMENT_RECOVERY_CANCELLATION.payment_transaction_type_description
            )
        elif (
            scenario_descriptor.payment_transaction_type
            == PaymentTransactionType.OVERPAYMENT_RECOVERY_REVERSE
        ):
            event_type = (
                PaymentTransactionType.OVERPAYMENT_RECOVERY_REVERSE.payment_transaction_type_description
            )
        elif (
            scenario_descriptor.payment_transaction_type
            == PaymentTransactionType.OVERPAYMENT_ACTUAL_RECOVERY_CANCELLATION
        ):
            event_type = (
                PaymentTransactionType.OVERPAYMENT_ACTUAL_RECOVERY_CANCELLATION.payment_transaction_type_description
            )
        elif (
            scenario_descriptor.payment_transaction_type
            == PaymentTransactionType.EMPLOYER_REIMBURSEMENT
        ):
            event_reason = "Automatic Alternate Payment"
            payee_identifier = "Tax Identification Number"
        elif scenario_descriptor.payment_transaction_type == PaymentTransactionType.CANCELLATION:
            event_type = "PaymentOut Cancellation"
        # TODO Unknown

        if scenario_descriptor.fineos_extract_address_valid:
            mock_address = MATCH_ADDRESS
        else:
            if scenario_descriptor.fineos_extract_address_valid_after_fix and round > 1:
                mock_address = MATCH_ADDRESS
            else:
                mock_address = NO_MATCH_ADDRESS

        fix_address = scenario_descriptor.invalid_address_fixed and round > 1
        if scenario_descriptor.invalid_address and (not fix_address):
            mock_address = INVALID_ADDRESS

        if scenario_descriptor.long_address:
            mock_address = LONG_ADDRESS

        # We have one payment record as withholding
        if scenario_descriptor.is_tax_withholding_record_without_primary_payment:
            event_reason = "Automatic Alternate Payment"
            payee_identifier = "ID"
            amalgamationc = "ScheduledAlternate65424"
            ssn = "SITPAYEE123"
            payment_amount = "22.00"
        if scenario_descriptor.is_child_support_record_without_primary_payment:
            event_reason = "Automatic Alternate Payment"
            payee_identifier = "ID"
            payee_name = PAYEE_CHILD_SUPPORT_NAME
            payment_amount = "70.00"

        # Set the payee name
        if (
            scenario_descriptor.payment_transaction_type
            == PaymentTransactionType.EMPLOYER_REIMBURSEMENT
        ):
            payee_name = str(scenario_data.employer.employer_name)
        else:
            payee_name = (
                str(scenario_data.employee.fineos_employee_first_name)
                + " "
                + str(scenario_data.employee.fineos_employee_last_name)
            )

        fineos_payments_data = FineosPaymentData(
            generate_defaults=True,
            c_value=c_value,
            i_value=i_value,
            include_vpei=scenario_descriptor.create_payment,
            include_claim_details=scenario_descriptor.include_claim_details,
            include_payment_details=True,
            include_requested_absence=False,
            include_absence_case=False,
            tin=ssn,
            absence_case_number=absence_case_id,
            payment_address_1=mock_address["line_1"],
            payment_address_2=mock_address["line_2"],
            city=mock_address["city"],
            state=mock_address["state"],
            zip_code=mock_address["zip"],
            payment_method=payment_method,
            payment_date=payment_date.strftime("%Y-%m-%d %H:%M:%S"),
            payment_amount=payment_amount,
            routing_nbr=routing_nbr,
            account_nbr=account_nbr,
            account_type=account_type,
            payment_start=payment_start_period.strftime("%Y-%m-%d %H:%M:%S"),
            payment_end=payment_end_period.strftime("%Y-%m-%d %H:%M:%S"),
            event_type=event_type,
            event_reason=event_reason,
            payee_identifier=payee_identifier,
            payee_name=payee_name,
            amalgamationc=amalgamationc,
            payment_type=payment_type,
            claim_type=claim_type,
            absence_period_c_value=scenario_data.absence_period_c_value,
            absence_period_i_value=scenario_data.absence_period_i_value,
            leave_request_id=scenario_data.leave_request_id,
            include_requested_absence_som=scenario_descriptor.include_requested_absence_som,
        )

        if (
            scenario_descriptor.is_tax_withholding_records_exists
            and scenario_data.tax_withholding_payment_i_values
        ):
            for item in range(2):
                withholding_payment = copy.deepcopy(fineos_payments_data)
                withholding_payment.event_reason = "Automatic Alternate Payment"
                withholding_payment.payee_identifier = "ID"
                withholding_payment.amalgamationc = "ScheduledAlternate65424"

                # always have valid address for withholding payments
                mock_address = MATCH_ADDRESS
                withholding_payment.payment_address_1 = mock_address["line_1"]
                withholding_payment.payment_address_2 = mock_address["line_2"]
                withholding_payment.city = mock_address["city"]
                withholding_payment.state = mock_address["state"]
                withholding_payment.zip_code = mock_address["zip"]

                if item == 0:
                    withholding_payment.tin = "SITPAYEE123"
                    withholding_payment.payment_amount = "22.00"
                    withholding_payment.i_value = scenario_data.tax_withholding_payment_i_values[
                        item
                    ]
                if item == 1:
                    withholding_payment.tin = "FITAMOUNTPAYEE321"
                    withholding_payment.payment_amount = "35.00"
                    withholding_payment.i_value = scenario_data.tax_withholding_payment_i_values[
                        item
                    ]
                fineos_payments_dataset.append(withholding_payment)

        if scenario_descriptor.is_child_support:
            child_support_payment = copy.deepcopy(fineos_payments_data)
            child_support_payment.event_reason = "Automatic Alternate Payment"
            child_support_payment.payee_identifier = "ID"
            child_support_payment.payee_name = PAYEE_CHILD_SUPPORT_NAME
            child_support_payment.payment_amount = "50.00"
            child_support_payment.i_value = scenario_data.child_support_payment_i_values
            fineos_payments_dataset.append(child_support_payment)

        if scenario_descriptor.is_additional_child_support:
            additional_child_support_payment = copy.deepcopy(fineos_payments_data)
            additional_child_support_payment.event_reason = "Automatic Alternate Payment"
            additional_child_support_payment.payee_identifier = "ID"
            additional_child_support_payment.payee_name = PAYEE_CHILD_SUPPORT_NAME
            additional_child_support_payment.payment_amount = "50.00"
            additional_child_support_payment.i_value = (
                scenario_data.additional_child_support_payment_i_values
            )
            fineos_payments_dataset.append(additional_child_support_payment)

        if scenario_descriptor.is_additional_primary_payment:
            additional_primary_payment = copy.deepcopy(fineos_payments_data)
            additional_primary_payment.c_value = scenario_data.additional_payment_c_value
            additional_primary_payment.i_value = scenario_data.additional_payment_i_value
            fineos_payments_dataset.append(additional_primary_payment)

        # TODO Need to refactor
        if (
            scenario_descriptor.is_employer_reimbursement_records_exists
            and scenario_data.employer_reimbursement_payment_i_values
        ):
            for item in range(1):
                employer_reimbursement_payment = copy.deepcopy(fineos_payments_data)
                employer_reimbursement_payment.event_reason = "Automatic Alternate Payment"
                employer_reimbursement_payment.payee_identifier = "Tax Identification Number"
                if scenario_descriptor.is_employer_reimbursement_fineos_extract_address_valid:
                    mock_address = MATCH_ADDRESS
                else:
                    mock_address = NO_MATCH_ADDRESS
                employer_reimbursement_payment.payment_address_1 = mock_address["line_1"]
                employer_reimbursement_payment.payment_address_2 = mock_address["line_2"]
                employer_reimbursement_payment.city = mock_address["city"]
                employer_reimbursement_payment.state = mock_address["state"]
                employer_reimbursement_payment.zip_code = mock_address["zip"]

                if item == 0:
                    employer_reimbursement_payment.payment_amount = "300.00"
                    employer_reimbursement_payment.i_value = (
                        scenario_data.employer_reimbursement_payment_i_values[item]
                    )
                # if item == 1:
                #     employer_reimbursement_payment.payment_amount = "350.00"
                #     employer_reimbursement_payment.i_value = scenario_data.employer_reimbursement_payment_i_values[
                #         item
                #     ]
                fineos_payments_dataset.append(employer_reimbursement_payment)
        fineos_payments_dataset.append(fineos_payments_data)

    # create the files
    create_fineos_payment_extract_files(fineos_payments_dataset, folder_path, date_of_extract)


def create_fineos_claimant_extract_files(
    fineos_claimant_dataset: List[FineosPaymentData], folder_path: str, date_of_extract: datetime
) -> None:
    # prefix string
    date_prefix = date_of_extract.strftime("%Y-%m-%d-%H-%M-%S-")

    # create the extract files
    employee_feed_writer = _create_file(
        folder_path,
        date_prefix,
        fineos_extract_config.FineosExtractConstants.EMPLOYEE_FEED.file_name,
        EMPLOYEE_FEED_FIELD_NAMES,
    )
    employee_feed_delta_writer = _create_file(
        folder_path,
        date_prefix,
        fineos_extract_config.FineosExtractConstants.EMPLOYEE_FEED_DELTA.file_name,
        EMPLOYEE_FEED_FIELD_NAMES,
    )
    requested_absence_som_writer = _create_file(
        folder_path,
        date_prefix,
        fineos_extract_config.FineosExtractConstants.VBI_REQUESTED_ABSENCE_SOM.file_name,
        REQUESTED_ABSENCE_SOM_FIELD_NAMES,
    )
    vbi_1099_data_writer = _create_file(
        folder_path,
        date_prefix,
        fineos_extract_config.FineosExtractConstants.VBI_1099DATA_SOM.file_name,
        VBI_1099DATA_SOM_FIELD_NAMES,
    )

    requested_absence_writer = _create_file(
        folder_path,
        date_prefix,
        fineos_extract_config.FineosExtractConstants.VBI_REQUESTED_ABSENCE.file_name,
        REQUESTED_ABSENCE_FIELD_NAMES,
    )

    vbi_leavesummary = _create_file(
        folder_path,
        date_prefix,
        fineos_extract_config.FineosExtractConstants.VBI_LEAVESUMMARY.file_name,
        VBI_LEAVESUMMARY_FIELD_NAMES,
    )

    v_paidleaveinstruction_som = _create_file(
        folder_path,
        date_prefix,
        fineos_extract_config.FineosExtractConstants.V_PAIDLEAVEINSTRUCTION_SOM.file_name,
        V_PAIDLEAVEINSTRUCTION_SOM_FIELD_NAMES,
    )

    v_paidleaveinstruction = _create_file(
        folder_path,
        date_prefix,
        fineos_extract_config.FineosExtractConstants.PAID_LEAVE_INSTRUCTION.file_name,
        V_PAIDLEAVEINSTRUCTION_FIELD_NAMES,
    )

    vbi_benefitperiod = _create_file(
        folder_path,
        date_prefix,
        fineos_extract_config.FineosExtractConstants.VBI_BENEFITPERIOD.file_name,
        VBI_BENEFITPERIOD_FIELD_NAMES,
    )

    vbi_paidleavecaseinfo = _create_file(
        folder_path,
        date_prefix,
        fineos_extract_config.FineosExtractConstants.VBI_PAIDLEAVECASEINFO_SOM.file_name,
        VBI_PAIDLEAVECASEINFO_FIELD_NAMES,
    )

    vbi_leaveplanrequestedabsence: FineosExportCsvWriter = _create_file(
        folder_path,
        date_prefix,
        fineos_extract_config.FineosExtractConstants.VBI_LEAVE_PLAN_REQUESTED_ABSENCE.file_name,
        VBI_LEAVE_PLAN_REQUESTED_ABSENCE_FIELD_NAMES,
    )

    # write the respective rows
    for fineos_claimant_data in fineos_claimant_dataset:
        employee_feed_writer.csv_writer.writerow(fineos_claimant_data.get_employee_feed_record())
        employee_feed_delta_writer.csv_writer.writerow(
            fineos_claimant_data.get_employee_feed_delta_record()
        )
        if fineos_claimant_data.include_requested_absence_som:
            requested_absence_som_writer.csv_writer.writerow(
                fineos_claimant_data.get_requested_absence_som_record()
            )
        requested_absence_writer.csv_writer.writerow(
            fineos_claimant_data.get_requested_absence_record()
        )
        vbi_1099_data_writer.csv_writer.writerow(fineos_claimant_data.get_vbi_1099_data_record())
        vbi_leavesummary.csv_writer.writerow(fineos_claimant_data.get_vbi_leavesummary_record())
        vbi_paidleavecaseinfo.csv_writer.writerow(
            fineos_claimant_data.get_vbi_paidleavecaseinfo_record()
        )
        v_paidleaveinstruction.csv_writer.writerow(
            fineos_claimant_data.get_v_paidleaveinstruction_record()
        )
        v_paidleaveinstruction_som.csv_writer.writerow(
            fineos_claimant_data.get_v_paidleaveinstruction_som_record()
        )
        vbi_benefitperiod.csv_writer.writerow(fineos_claimant_data.get_vbi_benefitperiod_record())
        vbi_leaveplanrequestedabsence.csv_writer.writerow(
            fineos_claimant_data.get_vbi_leaveplanrequestedabsence_record()
        )

    # close the files
    employee_feed_writer.file.close()
    employee_feed_delta_writer.file.close()
    requested_absence_som_writer.file.close()
    requested_absence_writer.file.close()
    vbi_1099_data_writer.file.close()
    vbi_leavesummary.file.close()
    v_paidleaveinstruction.file.close()
    v_paidleaveinstruction_som.file.close()
    vbi_paidleavecaseinfo.file.close()
    vbi_benefitperiod.file.close()
    vbi_leaveplanrequestedabsence.file.close()


def generate_claimant_data_files(
    scenario_dataset: List[ScenarioData],
    folder_path: str,
    date_of_extract: datetime,
    round: int = 1,
) -> None:
    # create the scenario based fineos data for the extract
    fineos_claimant_dataset: List[FineosPaymentData] = []

    for scenario_data in scenario_dataset:
        scenario_descriptor = scenario_data.scenario_descriptor
        employee = scenario_data.employee
        employer = scenario_data.employer

        if employee.tax_identifier is None:
            raise Exception("Expected employee with tin")

        ssn = employee.tax_identifier.tax_identifier.replace("-", "")
        if scenario_descriptor.claim_extract_employee_identifier_unknown:
            ssn = "UNKNOWNSSN"

        if round > 1 and scenario_descriptor.has_additional_payment_in_period:
            absence_case_number = scenario_data.additional_payment_absence_case_id
        else:
            absence_case_number = scenario_data.absence_case_id

        date_of_birth = "1991-01-01 12:00:00"
        payment_method = scenario_descriptor.payment_method.payment_method_description
        account_type = scenario_descriptor.account_type.bank_account_type_description
        routing_nbr = generate_routing_nbr_from_ssn(ssn)
        account_nbr = ssn
        natinsno = ssn
        default_payment_pref = "Y"
        customer_number = ssn
        absence_case_number = absence_case_number
        absence_case_status = scenario_descriptor.absence_case_status.absence_status_description
        absence_case_status_id = scenario_descriptor.absence_case_status.absence_status_id
        leave_request_evidence = "Satisfied" if scenario_descriptor.is_id_proofed else "Rejected"
        scenario_data.scenario_descriptor.scenario_name
        leave_request_start = scenario_data.leave_request_start
        leave_request_end = scenario_data.leave_request_end
        notification_number = f"NTN-{absence_case_number}"
        fineos_employer_id = employer.fineos_employer_id
        claim_type = scenario_descriptor.claim_type

        fineos_employee_first_name: Optional[str] = employee.fineos_employee_first_name
        fineos_employee_middle_name: Optional[str] = employee.fineos_employee_middle_name
        fineos_employee_last_name: Optional[str] = employee.fineos_employee_last_name

        if scenario_descriptor.dor_fineos_name_mismatch:
            fineos_employee_first_name = "Mismatch"
            fineos_employee_middle_name = "Mismatch"
            fineos_employee_last_name = "Mismatch"

        # Auto generated: leave_request_id
        fineos_claimant_data = FineosPaymentData(
            generate_defaults=True,
            absence_period_c_value=scenario_data.absence_period_c_value,
            absence_period_i_value=scenario_data.absence_period_i_value,
            date_of_birth=date_of_birth,
            payment_method=payment_method,
            account_type=account_type,
            routing_nbr=routing_nbr,
            account_nbr=account_nbr,
            ssn=natinsno,
            default_payment_pref=default_payment_pref,
            customer_number=customer_number,
            absence_case_number=absence_case_number,
            claim_type=claim_type,
            absence_case_status=absence_case_status,
            absence_case_status_id=absence_case_status_id,
            notification_number=notification_number,
            employer_customer_num=fineos_employer_id,
            fineos_employee_first_name=fineos_employee_first_name,
            fineos_employee_middle_name=fineos_employee_middle_name,
            fineos_employee_last_name=fineos_employee_last_name,
            leave_request_evidence=leave_request_evidence,
            leave_request_start=leave_request_start,
            leave_request_end=leave_request_end,
            leave_request_decision=scenario_descriptor.leave_request_decision,
            leave_request_id=scenario_data.leave_request_id,
            include_requested_absence_som=scenario_descriptor.include_requested_absence_som,
            absence_intake_source=scenario_descriptor.absence_intake_source,
        )

        fineos_claimant_dataset.append(fineos_claimant_data)
    # create the files
    create_fineos_claimant_extract_files(fineos_claimant_dataset, folder_path, date_of_extract)


def generate_payment_reconciliation_extract_files(
    folder_path: str, date_prefix: str, row_count: int
) -> Dict[str, List[Dict]]:
    extract_records = {}
    for extract_file in fineos_extract_config.PAYMENT_RECONCILIATION_EXTRACT_FILES:
        csv_handle = _create_file(
            folder_path, date_prefix, extract_file.file_name, extract_file.field_names
        )

        # write the respective rows
        records = []
        for i in range(row_count):
            row = {}
            for field_name in extract_file.field_names:
                row[field_name] = "test"
            row["I"] = str(i)

            csv_handle.csv_writer.writerow(row)
            records.append(row)

        csv_handle.file.close()
        extract_records[extract_file.file_name] = records

    return extract_records


def get_vbi_taskreport_som_extract_records(
    dataset: Optional[List[FineosPaymentData]] = None,
) -> List[Dict[str, Any]]:
    if dataset is None:
        dataset = []

        statuses = [
            TaskStatuses.VBI_TASK_REPORT_STATUS_OPEN,
            TaskStatuses.VBI_TASK_REPORT_STATUS_CLOSED,
            TaskStatuses.VBI_TASK_REPORT_STATUS_CANCELLED,
        ]
        tasktypenames = [
            # This isn't exhaustive - just enough to be able to test that our
            # filter works correctly. These are the ones we want to keep:
            "Document Indexing",
            "Employee Reported Other Income",
            "Employee Reported Other Leave",
            "Employee reported accrued paid leave (PTO)",
            "Escalate Employer Reported Other Income",
            "Escalate employer reported accrued paid leave (PTO)",
            # These are (some) of the ones we want to ignore:
            "Overpayment Mgt New Underpayment Notification",
            "Additional Information Overdue Notification Task",
            "Adjudicate Absence",
        ]

        # Generate all possible permutations of these columns, and fill the rest
        # of the data with random values.
        for status in statuses:
            for tasktypename in tasktypenames:
                # We include closed tasks if they have been closed within the past day
                # so we generate a closeddate within one day for two task types so
                # that we can confirm that functionality
                if status == TaskStatuses.VBI_TASK_REPORT_STATUS_CLOSED:
                    if tasktypename in {"Document Indexing", "Employee Reported Other Income"}:
                        # Closed yesterday
                        closed_date = (datetime.now() - timedelta(days=1)).strftime(
                            fineos_extract_config.VBI_TASK_DATE_FORMAT
                        )
                    elif tasktypename == "Escalate Employer Reported Other Income":
                        # Closed today
                        closed_date = datetime.now().strftime(
                            fineos_extract_config.VBI_TASK_DATE_FORMAT
                        )
                    else:
                        # Closed more than one day and should not get extracted
                        closed_date = (datetime.now() - timedelta(days=2)).strftime(
                            fineos_extract_config.VBI_TASK_DATE_FORMAT
                        )
                else:
                    closed_date = ""
                dataset.append(
                    FineosPaymentData(
                        include_vbi_tasks=True,
                        task_status=status,
                        task_tasktypename=tasktypename,
                        task_closeddate=closed_date,
                        task_subjectreference=(
                            "4990a4b9-d471-441c-8db2-64ede7026be6.pdf"
                            if tasktypename == "Document Indexing"
                            else ""
                        ),
                    )
                )

    records = [data.get_vbi_task_record() for data in dataset]
    return records


def get_vbi_document_som_extract_records(
    dataset: Optional[List[FineosPaymentData]] = None,
) -> List[Dict[str, Any]]:
    if dataset is None:
        dataset = []

        last_update_date = (datetime.now() - timedelta(days=1)).strftime(
            fineos_extract_config.VBI_TASK_DATE_FORMAT
        )
        document_types = [
            # This isn't exhaustive - just enough to be able to test that our
            # filter works correctly.
            "Employee Not Found Information",
            # These are (some) of the ones we want to ignore:
            "Approval Notice",
            "Identification Proof",
        ]

        for indexid, document_type in enumerate(document_types, 1):
            dataset.append(
                FineosPaymentData(
                    include_vbi_documents=True,
                    document_documenttype=document_type,
                    document_indexid=str(indexid),
                    document_lastupdatedate=last_update_date,
                    document_description=(
                        "4990a4b9-d471-441c-8db2-64ede7026be6.pdf"
                        if document_type == "Employee Not Found Information"
                        else ""
                    ),
                    absence_case_number="NTN-1234-ABS-01",
                    notification_number="NTN-1234",
                )
            )

    records = [data.get_vbi_document_record() for data in dataset]
    return records


def get_vbi_document_som_extract_filtered_records(
    records: List[Dict[str, Any]],
) -> List[Dict[str, Any]]:
    filtered_records = []

    for record in records:
        if record["DOCUMENTTYPE"] == "Employee Not Found Information":
            filtered_records.append(record)

    return filtered_records


def get_vbi_taskreport_som_extract_filtered_records(
    records: List[Dict[str, Any]],
) -> List[Dict[str, Any]]:
    filtered_records = []

    for record in records:
        if record["STATUS"] == TaskStatuses.VBI_TASK_REPORT_STATUS_OPEN and record[
            "TASKTYPENAME"
        ] in (
            "Employee Reported Other Income",
            "Employee Reported Other Leave",
            "Employee reported accrued paid leave (PTO)",
            "Escalate Employer Reported Other Income",
            "Escalate employer reported accrued paid leave (PTO)",
        ):
            filtered_records.append(record)

        # This task type is configured to have a CLOSEDDATE within the last day
        # and so is included when we import the extracts
        # https://github.com/EOLWD/pfml/blob/10b9c46f9c4f56c91454805dc0638ae38dcdc0b7/api/massgov/pfml/delegated_payments/mock/fineos_extract_data.py#L1012
        if record["STATUS"] == TaskStatuses.VBI_TASK_REPORT_STATUS_CLOSED and record[
            "TASKTYPENAME"
        ] in [
            "Employee Reported Other Income",
            "Escalate Employer Reported Other Income",
        ]:
            filtered_records.append(record)

    return filtered_records


def create_extract_file(
    records: List[Dict],
    folder_path: str,
    date_of_extract: datetime,
    file_name: str,
    field_names: List[str],
) -> None:
    date_prefix = date_of_extract.strftime("%Y-%m-%d-%H-%M-%S-")

    # create the extract file
    writer = _create_file(
        folder_path,
        date_prefix,
        file_name,
        field_names,
    )

    for record in records:
        writer.csv_writer.writerow(record)

    writer.file.close()


def create_vbi_taskreport_som_extract_files(
    records: List[Dict],
    folder_path: str,
    date_of_extract: datetime,
) -> None:
    create_extract_file(
        records=records,
        folder_path=folder_path,
        date_of_extract=date_of_extract,
        file_name=fineos_extract_config.FineosExtractConstants.VBI_TASKREPORT_SOM.file_name,
        field_names=VBI_TASKREPORT_SOM_EXTRACT_FIELD_NAMES,
    )


def create_vbi_taskreport_delta_som_extract_files(
    records: List[Dict],
    folder_path: str,
    date_of_extract: datetime,
) -> None:
    create_extract_file(
        records=records,
        folder_path=folder_path,
        date_of_extract=date_of_extract,
        file_name=fineos_extract_config.FineosExtractConstants.VBI_TASKREPORT_DELTA_SOM.file_name,
        field_names=fineos_extract_config.FineosExtractConstants.VBI_TASKREPORT_DELTA_SOM.field_names,
    )


def create_vbi_document_som_extract_files(
    records: List[Dict],
    folder_path: str,
    date_of_extract: datetime,
) -> None:
    date_prefix = date_of_extract.strftime("%Y-%m-%d-%H-%M-%S-")

    writers = []
    # create the extract file
    writers.append(
        _create_file(
            folder_path,
            date_prefix,
            fineos_extract_config.FineosExtractConstants.VBI_DOCUMENT_SOM.file_name,
            VBI_DOCUMENT_SOM_EXTRACT_FIELD_NAMES,
        )
    )
    # create the delta extract file
    writers.append(
        _create_file(
            folder_path,
            date_prefix,
            fineos_extract_config.FineosExtractConstants.VBI_DOCUMENT_DELTA_SOM.file_name,
            VBI_DOCUMENT_SOM_EXTRACT_FIELD_NAMES,
        )
    )
    # add the records to the files
    [writer.csv_writer.writerow(record) for writer in writers for record in records]
    # close the files
    for writer in writers:
        writer.file.close()


def create_vbi_entitlemt_period_som_extract_files(
    records: List[Dict],
    folder_path: str,
    date_of_extract: datetime,
) -> None:
    date_prefix = date_of_extract.strftime("%Y-%m-%d-%H-%M-%S-")

    # create the extract file
    writer = _create_file(
        folder_path,
        date_prefix,
        fineos_extract_config.FineosExtractConstants.VBI_ENTITLEMTPERIOD_SOM.file_name,
        fineos_extract_config.FineosExtractConstants.VBI_ENTITLEMTPERIOD_SOM.field_names,
    )

    for record in records:
        writer.csv_writer.writerow(record)

    writer.file.close()


def generate_vbi_taskreport_som_extract_files(
    scenario_dataset: List[ScenarioData],
    folder_path: str,
    date_of_extract: datetime,
) -> None:
    records = []

    for scenario_data in scenario_dataset:
        scenario_descriptor = scenario_data.scenario_descriptor

        if scenario_descriptor.has_open_other_income_tasks:
            record = {
                "STATUS": TaskStatuses.VBI_TASK_REPORT_STATUS_OPEN,
                "CASENUMBER": scenario_data.absence_case_id,
                "TASKTYPENAME": "Employee Reported Other Income",
            }
            records.append(record)

        if scenario_descriptor.has_open_fraud_tasks:
            record = {
                "STATUS": TaskStatuses.VBI_TASK_REPORT_STATUS_OPEN,
                "CASENUMBER": scenario_data.absence_case_id,
                "TASKTYPENAME": "Fraud Report Received",
            }
            records.append(record)

    create_vbi_taskreport_som_extract_files(records, folder_path, date_of_extract)
    # Also create the delta
    create_vbi_taskreport_delta_som_extract_files(records, folder_path, date_of_extract)


def generate_vbi_document_som_extract_files(
    scenario_dataset: List[ScenarioData],
    folder_path: str,
    date_of_extract: datetime,
) -> None:
    records = []

    for scenario_data in scenario_dataset:
        scenario_descriptor = scenario_data.scenario_descriptor

        if scenario_descriptor.has_employee_not_found_info_documents:
            record = {
                "CASENUMBER": scenario_data.fineos_notification_id,
                "DOCUMENTTYPE": "Employee Not Found Information",
            }
            records.append(record)

    create_vbi_document_som_extract_files(records, folder_path, date_of_extract)


def generate_iaww_extract_files(
    dataset: List[FineosIAWWData], folder_path: str, date_prefix: str
) -> Dict[str, List[Dict]]:
    extract_records = {}

    # create the extract files
    leave_plan_requested_absence_writer = _create_file(
        folder_path,
        date_prefix,
        fineos_extract_config.FineosExtractConstants.VBI_LEAVE_PLAN_REQUESTED_ABSENCE.file_name,
        VBI_LEAVE_PLAN_REQUESTED_ABSENCE_FIELD_NAMES,
    )
    paid_leave_instruction_writer = _create_file(
        folder_path,
        date_prefix,
        fineos_extract_config.FineosExtractConstants.PAID_LEAVE_INSTRUCTION.file_name,
        PAID_LEVAVE_INSTRUCTION_FIELD_NAMES,
    )

    vbi_paidleavecaseinfo = _create_file(
        folder_path,
        date_prefix,
        fineos_extract_config.FineosExtractConstants.VBI_PAIDLEAVECASEINFO_SOM.file_name,
        VBI_PAIDLEAVECASEINFO_FIELD_NAMES,
    )

    v_paidleaveinstruction_som = _create_file(
        folder_path,
        date_prefix,
        fineos_extract_config.FineosExtractConstants.V_PAIDLEAVEINSTRUCTION_SOM.file_name,
        V_PAIDLEAVEINSTRUCTION_SOM_FIELD_NAMES,
    )

    vbi_benefitperiod = _create_file(
        folder_path,
        date_prefix,
        fineos_extract_config.FineosExtractConstants.VBI_BENEFITPERIOD.file_name,
        VBI_BENEFITPERIOD_FIELD_NAMES,
    )

    vbi_paidleavecaseinfo.file.close()
    v_paidleaveinstruction_som.file.close()
    vbi_benefitperiod.file.close()

    # write the respective rows
    # we can leave most of the fields empty and just populate the columns we care about
    leave_plan_requested_records = []
    leave_instruction_records = []

    for data in dataset:
        leave_plan_requested_absence_record = data.get_leave_plan_request_absence_record()
        leave_instruction_record = data.get_vpaid_leave_instruction_record()

        row = {}
        row["SELECTEDPLAN_CLASSID"] = leave_plan_requested_absence_record["SELECTEDPLAN_CLASSID"]
        row["SELECTEDPLAN_INDEXID"] = leave_plan_requested_absence_record["SELECTEDPLAN_INDEXID"]
        row["LEAVEREQUEST_ID"] = leave_plan_requested_absence_record["LEAVEREQUEST_ID"]

        leave_plan_requested_absence_writer.csv_writer.writerow(row)
        leave_plan_requested_records.append(row)

        row = {}
        row["C"] = leave_instruction_record["C"]
        row["I"] = leave_instruction_record["I"]
        row["AVERAGEWEEKLYWAGE_MONAMT"] = leave_instruction_record["AVERAGEWEEKLYWAGE_MONAMT"]
        row["C_SELECTEDLEAVEPLAN"] = leave_instruction_record["C_SELECTEDLEAVEPLAN"]
        row["I_SELECTEDLEAVEPLAN"] = leave_instruction_record["I_SELECTEDLEAVEPLAN"]

        paid_leave_instruction_writer.csv_writer.writerow(row)
        leave_instruction_records.append(row)

    leave_plan_requested_absence_writer.file.close()
    extract_records[
        fineos_extract_config.FineosExtractConstants.VBI_LEAVE_PLAN_REQUESTED_ABSENCE.file_name
    ] = leave_plan_requested_records

    paid_leave_instruction_writer.file.close()
    extract_records[
        fineos_extract_config.FineosExtractConstants.PAID_LEAVE_INSTRUCTION.file_name
    ] = leave_instruction_records

    return extract_records


def generate_entitlement_period_csv_record(
    vbi_entitlement_period: FineosExtractVbiEntitlementPeriodSom,
) -> Dict[str, str]:
    return {
        k.upper(): v
        for k, v in vbi_entitlement_period.dict().items()
        if k.upper()
        in fineos_extract_config.FineosExtractConstants.VBI_ENTITLEMTPERIOD_SOM.field_names
    }


def generate_vbi_entitlemt_period_som_extract_files(
    scenario_dataset: List[ScenarioData],
    folder_path: str,
    date_of_extract: datetime,
) -> None:
    records = []

    for scenario_data in scenario_dataset:
        if (
            scenario_data.employee.fineos_customer_number != ""
            and scenario_data.employer.fineos_employer_id is not None
            and scenario_data.claim
        ):
            # Have the entitlement period start randomly sometime in the 120 days preceding
            # the claim
            start_period = (
                scenario_data.claim.claim_start_date
                if scenario_data.claim.claim_start_date
                else date.today()
            ) - timedelta(days=120)
            end_period = scenario_data.claim.claim_start_date
            entitlement_period = calculate_benefit_year_dates(
                fake.date_between(start_date=start_period, end_date=end_period)
            )

            record = generate_entitlement_period_csv_record(
                FineosExtractVbiEntitlementPeriodSomFactory.build(
                    employee_number=scenario_data.employee.fineos_customer_number,
                    periodstartdate=entitlement_period.start_date.strftime("%Y-%m-%d %H:%M:%S"),
                    periodenddate=entitlement_period.end_date.strftime("%Y-%m-%d %H:%M:%S"),
                    employer_number=scenario_data.employer.fineos_employer_id,
                    assoicated_leave_plan="family",
                )
            )
            records.append(record)

    create_vbi_entitlemt_period_som_extract_files(records, folder_path, date_of_extract)
