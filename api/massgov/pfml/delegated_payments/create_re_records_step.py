import enum
import os
import xml.dom.minidom as minidom
from datetime import datetime
from decimal import Decimal
from typing import Dict, List

from sqlalchemy.orm import joinedload

import massgov.pfml.delegated_payments.delegated_config as payments_config
import massgov.pfml.delegated_payments.delegated_payments_util as payments_util
import massgov.pfml.util.files as file_util
import massgov.pfml.util.logging
from massgov.pfml import db
from massgov.pfml.db.lookup_data.payments import MmarsEventStatusType, MmarsEventType
from massgov.pfml.db.lookup_data.reference_file_type import ReferenceFileType
from massgov.pfml.db.models.ctr.batch_identifier import CtrBatchIdentifier
from massgov.pfml.db.models.employees import Overpayment, ReferenceFile
from massgov.pfml.db.models.payments import MmarsAuditLog, MmarsEvent
from massgov.pfml.util.batch.step import Step
from massgov.pfml.util.datetime import get_now_us_eastern

logger = massgov.pfml.util.logging.get_logger(__name__)


class ProcessRERecordsStep(Step):
    """
    This moves all files from the incoming directories that are populated by
    either Sharepoint or MoveIT to our archive folders for processing.

    Note that this method does not create reference files and leaves that to
    the processes that consume the files.

    https://lwd.atlassian.net/wiki/spaces/API/pages/2365849615/Pickup+Response+Files+Step
    """

    class Metrics(str, enum.Enum):
        TOTAL_RE_RECORDS = "total_re_records"
        TOTAL_RE_RECORDS_PROCESSED = "total_re_records_processed"
        TOTAL_RE_RECORDS_SUSPENDED = "total_re_records_suspended"
        RE_FILE_PATH = "re_file_path"
        INF_FILE_PATH = "inf_file_path"

    def __init__(
        self,
        db_session: db.Session,
        log_entry_db_session: db.Session,
        s3_config: payments_config.MmarsS3Config,
    ):
        super().__init__(db_session, log_entry_db_session)
        self.s3_config = s3_config

    def run_step(self):
        logger.info("Processing MMARS RE Records")
        re_records = self._get_re_active_records()
        if not re_records:
            logger.info("No RE Records to process")
            return
        else:
            self.increment(self.Metrics.TOTAL_RE_RECORDS, increment=len(re_records))
            logger.info(f"Building RE File for {len(re_records)} RE Records")
            self._build_re_file(re_records)
        logger.info("Successfully processed MMARS RE Records")

    def _get_re_active_records(self) -> List[MmarsEvent]:
        """
        Get RE Records from the database
        """
        re_records = (
            self.db_session.query(MmarsEvent)
            .filter(
                MmarsEvent.mmars_status_type_id
                == MmarsEventStatusType.RE_PENDING.mmars_event_status_type_id,
            )
            .options(
                # only load the `amount` and `ctr_doc_id` columns from overpayment table
                joinedload(MmarsEvent.overpayment).load_only(
                    Overpayment.outstanding_amount, Overpayment.ctr_doc_id
                ),
            )
            .all()
        )

        return re_records

    def _build_re_file(self, re_records: List[MmarsEvent]) -> None:
        """
        Build RE Files
        """
        if not re_records:
            return
        extra = dict()
        re_total_amount: Decimal = Decimal("0.0")
        try:
            now = get_now_us_eastern()
            # create ctr batch identifier record
            ctr_batch = payments_util.generate_next_batch_identifier(
                self.db_session, now, MmarsEventType.RE_TRX
            )
            re_file_name = ctr_batch.ctr_batch_identifier
            re_file_path = os.path.join(
                self.s3_config.pfml_mmars_file_base_location,
                payments_util.MMARS_Constants.RE_FILE_FOLDER,
                re_file_name,
                re_file_name + ".dat",
            )
            inf_file_path = os.path.join(
                self.s3_config.pfml_mmars_file_base_location,
                payments_util.MMARS_Constants.RE_FILE_FOLDER,
                re_file_name,
                re_file_name + ".inf",
            )
            moveit_re_file_path = os.path.join(
                self.s3_config.pfml_mmars_file_base_location,
                payments_util.MMARS_Constants.MOVEIT_OUTBOUND_FILE_FOLDER,
                payments_util.MMARS_Constants.MMARS_FILE_NAME.MOVEIT_OUTBOUND_RE_FILE,
            )
            moveit_inf_re_file_path = os.path.join(
                self.s3_config.pfml_mmars_file_base_location,
                payments_util.MMARS_Constants.MOVEIT_OUTBOUND_FILE_FOLDER,
                payments_util.MMARS_Constants.MMARS_FILE_NAME.MOVEIT_OUTBOUND_RE_INF_FILE,
            )

            # create a reference file for the RE files
            reference_file = ReferenceFile(
                file_location=str(re_file_path),
                reference_file_type_id=ReferenceFileType.MMARS_RE.reference_file_type_id,
            )
            self.db_session.add(reference_file)

            reference_file.ctr_batch_identifier = ctr_batch

            xml_document = minidom.Document()
            document_root = xml_document.createElement("AMS_DOC_XML_IMPORT_FILE")
            payments_util.add_xml_attributes(document_root, {"VERSION": "1.0"})
            xml_document.appendChild(document_root)
            total_re_records_processed = 0

            for count, re_record in enumerate(re_records):
                re_amount: Decimal = Decimal("0.0")
                if re_record.overpayment.outstanding_amount:
                    # overpayments are stored as negative values but will be sent to MMARS as positive values
                    re_amount = re_record.overpayment.outstanding_amount * Decimal("-1.0")

                if re_amount == 0:
                    logger.warning(
                        f"Overpayment {re_record.overpayment.overpayment_casenumber} has a zero outstanding amount. An Acutal Recovery Offset may be assigned to this overpayment after it is referred",
                        extra={
                            "overpayment_id": re_record.overpayment_id,
                        },
                    )

                    re_record.mmars_status_type_id = (
                        MmarsEventStatusType.RE_SUSPENDED.mmars_event_status_type_id
                    )

                    self.increment(self.Metrics.TOTAL_RE_RECORDS_SUSPENDED)
                    continue

                re_total_amount += re_amount
                logger.info(
                    f"Processing RE Record {count + 1} of {len(re_records)}",
                    extra={
                        "employee_id": re_record.employee_id,
                        "overpayment_case_id": re_record.overpayment_id,
                    },
                )

                # returns the ctr_doc_id if it exists, otherwise generates a new one in overpayment record and returns it
                ctr_doc_id = re_record.overpayment.get_or_generate_unique_id

                if (
                    not re_record.overpayment.claim
                    or not re_record.overpayment.claim.fineos_absence_id
                ):
                    logger.error(
                        "Fineos Absence ID is missing for the overpayment",
                        extra={
                            "overpayment_id": re_record.overpayment_id,
                        },
                    )
                    continue
                else:
                    fineos_absence_id = re_record.overpayment.claim.fineos_absence_id

                re_document = self._build_individual_re_record(
                    xml_document, re_record, re_amount, now, ctr_doc_id, fineos_absence_id
                )
                document_root.appendChild(re_document)
                self.increment(self.Metrics.TOTAL_RE_RECORDS_PROCESSED)
                total_re_records_processed += 1
                mmars_audit_log = MmarsAuditLog(
                    mmars_event_id=re_record.mmars_event_id,
                    mmars_event_type_id=re_record.mmars_event_type_id,
                    ctr_batch_identifier_id=ctr_batch.ctr_batch_identifier_id,
                    request_data=re_document.toprettyxml(
                        indent="   ", encoding="ISO-8859-1"
                    ).decode("ISO-8859-1"),
                )
                re_record.mmars_status_type_id = (
                    MmarsEventStatusType.RE_SUBMITTED.mmars_event_status_type_id
                )
                self.db_session.add(mmars_audit_log)
            self.db_session.commit()

            with file_util.write_file(re_file_path) as re_file:
                re_file.write(
                    xml_document.toprettyxml(indent="   ", encoding="ISO-8859-1").decode(
                        "ISO-8859-1"
                    )
                )
                self.set_metrics({self.Metrics.RE_FILE_PATH: re_file_path})

        except Exception as e:
            logger.exception("Unable to create RE file")
            raise e

        try:
            inf_dict = self._build_re_inf(total_re_records_processed, re_total_amount, ctr_batch)

            with file_util.write_file(inf_file_path) as inf_file:
                for k, v in inf_dict.items():
                    inf_file.write(f"{k} = {v};\n")
                self.set_metrics({self.Metrics.INF_FILE_PATH: inf_file_path})
        except Exception as e:
            logger.exception("Unable to create RE INF file")
            raise e

        try:
            file_util.copy_file(re_file_path, moveit_re_file_path)
            logger.info(
                "RE File copied to MoveIT", extra={"moveit_re_file_path": moveit_re_file_path}
            )

            file_util.copy_file(inf_file_path, moveit_inf_re_file_path)
            logger.info(
                "RE INF File copied to MoveIT",
                extra={"moveit_inf_re_file_path": moveit_inf_re_file_path},
            )
        except Exception as e:
            logger.exception("Unable to copy VCC file to MoveIT")
            raise e

        extra["re_file_path"] = re_file_path
        extra["inf_file_path"] = inf_file_path
        extra["reference_file_id"] = str(reference_file.reference_file_id)
        logger.info("RE File created successfully", extra=extra)

    def _build_individual_re_record(
        self,
        document: minidom.Document,
        re_record: MmarsEvent,
        re_line_amount: Decimal,
        now: datetime,
        doc_id: str,
        fineos_absence_id: str,
    ) -> minidom.Element:
        # AMS Document Root element

        financial_year = payments_util.get_financial_year(now)
        per_dc = payments_util.get_month_period(now)

        re_record.re_amount = re_line_amount

        employee = re_record.employee
        ams_document_attributes = {"DOC_ID": doc_id}
        ams_document_attributes.update(payments_util.MMARS_Constants.ams_doc_attributes.copy())
        ams_document_attributes.update(payments_util.MMARS_Constants.re_generic_attributes.copy())
        root = document.createElement("AMS_DOCUMENT")
        payments_util.add_xml_attributes(root, ams_document_attributes)

        # RE Document Component: RE_DOC_HDR
        re_doc_hdr = document.createElement("RE_DOC_HDR")
        payments_util.add_xml_attributes(re_doc_hdr, {"AMSDataObject": "Y"})
        # Add the individual RE_DOC_HDR values
        re_doc_hdr_elements = {
            "DOC_ID": doc_id,
            "DOC_NM": re_record.overpayment.overpayment_casenumber,
        }
        re_doc_hdr_elements.update(payments_util.MMARS_Constants.re_generic_attributes.copy())
        payments_util.add_xml_cdata_elements(re_doc_hdr, document, re_doc_hdr_elements)
        root.appendChild(re_doc_hdr)

        # Add the RE_DOC_VEND
        re_doc_vend = document.createElement("RE_DOC_VEND")
        payments_util.add_xml_attributes(re_doc_vend, {"AMSDataObject": "Y"})
        legal_name = employee.first_name + " " + employee.last_name
        # Add the individual RE_DOC_VEND values
        re_doc_vend_elements = {
            "DOC_ID": doc_id,
            "LGL_NM": legal_name,
            "VEND_CUST_CD": employee.ctr_vendor_customer_code,
            "BPRO_CD": payments_util.MMARS_Constants.BPRO_CD,
            "AD_ID": "AD010",  # hard coded value (to be discussed)
            "AR_DEPT_CD": "EOL",
            "AR_UNIT_CD": "ALL",
        }
        re_doc_vend_elements.update(payments_util.MMARS_Constants.re_generic_attributes.copy())
        payments_util.add_xml_cdata_elements(re_doc_vend, document, re_doc_vend_elements)
        root.appendChild(re_doc_vend)

        # Add the RE_DOC_ACTG
        re_doc_actg = document.createElement("RE_DOC_ACTG")
        payments_util.add_xml_attributes(re_doc_actg, {"AMSDataObject": "Y"})
        re_doc_actg_elements = {
            "DOC_ID": doc_id,
            "LN_AM": f"{re_line_amount:.2f}",
            "BFY": str(financial_year),
            "FY_DC": str(financial_year),
            "PER_DC": str(per_dc),
            "DEPT_CD": payments_util.MMARS_Constants.COMPTROLLER_DEPT_CODE,
            "UNIT_CD": payments_util.MMARS_Constants.COMPTROLLER_UNIT_CODE,
            "ACTG_LN_DSCR": fineos_absence_id,
        }
        re_doc_actg_elements.update(payments_util.MMARS_Constants.re_doc_acctg_attributes.copy())
        re_doc_actg_elements.update(payments_util.MMARS_Constants.re_generic_attributes.copy())
        payments_util.add_xml_cdata_elements(re_doc_actg, document, re_doc_actg_elements)
        root.appendChild(re_doc_actg)

        return root

    def _build_re_inf(
        self,
        re_records_count: int,
        total_amount: Decimal,
        ctr_batch: CtrBatchIdentifier,
    ) -> Dict[str, str]:
        logger.info("Building RE .INF file for %i employee records", re_records_count)

        inf_data = {
            "NewMmarsBatchID": ctr_batch.ctr_batch_identifier,
            "NewMmarsBatchDeptCode": payments_util.MMARS_Constants.COMPTROLLER_DEPT_CODE,
            "NewMmarsUnitCode": payments_util.MMARS_Constants.COMPTROLLER_UNIT_CODE,
            "NewMmarsImportDate": ctr_batch.batch_date.strftime("%Y-%m-%d"),
            "NewMmarsTransCode": "RE",
            "NewMmarsTableName": "",
            "NewMmarsTransCount": str(re_records_count),
            "NewMmarsTransDollarAmount": f"{total_amount:.2f}",
        }
        ctr_batch.inf_data = inf_data
        self.db_session.add(ctr_batch)
        return inf_data
