import enum

import massgov.pfml.delegated_payments.delegated_payments_util as payments_util
import massgov.pfml.util.logging
from massgov.pfml.db.lookup_data.payments import MmarsEventStatusType
from massgov.pfml.db.models.payments import LkMmarsEventStatusType, MmarsCustomerDetail, MmarsEvent
from massgov.pfml.edm.edm_client import create_client
from massgov.pfml.edm.models.vendor.models import EDMVendorData, EDMVendorRequest
from massgov.pfml.util.batch.step import Step

logger = massgov.pfml.util.logging.get_logger(__name__)


class SyncVCCStep(Step):
    """
    This moves all files from the incoming directories that are populated by
    either Sharepoint or MoveIT to our archive folders for processing.

    Note that this method does not create reference files and leaves that to
    the processes that consume the files.

    https://lwd.atlassian.net/wiki/spaces/API/pages/2365849615/Pickup+Response+Files+Step
    """

    class Metrics(str, enum.Enum):
        TOTAL_VCC_RECORDS = "total_vcc_records"
        TOTAL_VCC_ALREADY_REGISTERED_IN_MMARS = "total_vcc_already_registered_in_mmars"
        TOTAL_VCC_VALID_RECORDS = "total_vcc_valid_records"
        TOTAL_VCC_ISSUE_COUNT = "total_vcc_issue_count"
        TOTAL_VCM_RECORDS_WRITTEN = "total_vcm_records_written"
        TOTAL_VCM_REQUIRED = "total_vcm_required"
        TOTAL_VCM_PENDING = "total_vcm_pending"

    def run_step(self):
        logger.info("Processing MMARS VCC Sync Step")
        self.initialize_metrics()
        self.edm_client = create_client()

        vcc_statuses_to_sync = [
            # newly created VCC records
            MmarsEventStatusType.VCC_PENDING.mmars_event_status_type_id,
            # previously processed VCC records that require a VCM update (e.g., address mismatch)
            MmarsEventStatusType.VCM_REQUIRE.mmars_event_status_type_id,
        ]

        vcc_records = (
            self.db_session.query(MmarsEvent)
            .filter(MmarsEvent.mmars_status_type_id.in_(vcc_statuses_to_sync))
            .all()
        )
        if not vcc_records:
            logger.info("No VCC Records to process")
            return
        else:
            self.increment(self.Metrics.TOTAL_VCC_RECORDS, increment=len(vcc_records))
            for vcc_record in vcc_records:
                validated = self.validate_vcc_record(vcc_record)

                if validated:
                    self.check_for_vcm(vcc_record)

        logger.info("Successfully processed MMARS VCC Sync Step")

    def validate_vcc_record(self, vcc_record: MmarsEvent) -> bool:
        """
        Validate VCC Records
        """

        extra = {"mmars_event_id": vcc_record.mmars_event_id}
        logger.info(f"Validating VCC Record {vcc_record.mmars_event_id}")
        validation_container = payments_util.ValidationContainer(str(vcc_record.mmars_event_id))

        # verify if the employee has a valid address
        if not payments_util.get_employee_mailing_address(vcc_record.employee):
            validation_container.add_validation_issue(
                payments_util.ValidationReason.MISSING_EMPLOYEE_ADDRESS,
                "Employee doesn't have an mailing Address",
            )

        if validation_container.has_validation_issues():
            self.increment(self.Metrics.TOTAL_VCC_ISSUE_COUNT)
            logger.error(
                f"Encountered validation issue while processing vcc record: {validation_container.get_reasons()}",
                extra=extra,
            )
            vcc_record.mmars_status_type_id = (
                MmarsEventStatusType.VCC_ERROR.mmars_event_status_type_id
            )
            return False
        else:
            self.increment(self.Metrics.TOTAL_VCC_VALID_RECORDS)
            return True

    def determine_mmars_status(
        self, edm_data: EDMVendorData, pfml_data: EDMVendorData
    ) -> LkMmarsEventStatusType:
        """
        Determine the MMARS status code by comparing EDM and PFML data
        If data matches, return RE_PENDING
        If the address does not match, return VCM_PENDING
        else return VCM_REQUIRE
        """
        extra = {"VENDOR_CUSTOMER_CODE": edm_data.VENDOR_CUSTOMER_CODE}
        is_vcmt_automation_enabled = payments_util.is_enable_overpayment_vcmt_automation_enabled()

        if edm_data.TIN != pfml_data.TIN:
            logger.info("TIN Mismatch", extra=extra)
            return MmarsEventStatusType.VCM_REQUIRE

        if (edm_data.LAST_NAME.strip().lower()[:5] if edm_data.LAST_NAME else "") != (
            pfml_data.LAST_NAME.strip().lower()[:5] if pfml_data.LAST_NAME else ""
        ):
            logger.info("Last Name Mismatch", extra=extra)
            return MmarsEventStatusType.VCM_REQUIRE

        if edm_data.CUSTOMER_ACTIVE_STATUS != payments_util.MMARS_Constants.VEND_ACT_STATUS.ACTIVE:
            logger.info("EDM data is not active", extra=extra)
            return (
                MmarsEventStatusType.VCM_PENDING
                if is_vcmt_automation_enabled
                else MmarsEventStatusType.VCM_REQUIRE
            )

        if (
            edm_data.CUSTOMER_APPROVAL_STATUS
            != payments_util.MMARS_Constants.VEND_APRV_STATUS.COMPLETE
        ):
            logger.info("EDM data is not approved", extra=extra)
            return (
                MmarsEventStatusType.VCM_PENDING
                if is_vcmt_automation_enabled
                else MmarsEventStatusType.VCM_REQUIRE
            )
        edm_address = f"{edm_data.STREET_1.strip() if edm_data.STREET_1 else ''} {edm_data.STREET_2.strip() if edm_data.STREET_2 else ''}".strip().lower()
        pfml_address = f"{pfml_data.STREET_1.strip() if pfml_data.STREET_1 else ''} {pfml_data.STREET_2.strip() if pfml_data.STREET_2 else ''}".strip().lower()

        if edm_address != pfml_address:
            logger.info("Address Mismatch", extra=extra)
            return (
                MmarsEventStatusType.VCM_PENDING
                if is_vcmt_automation_enabled
                else MmarsEventStatusType.VCM_REQUIRE
            )

        if (edm_data.CITY.strip().lower() if edm_data.CITY else "") != (
            pfml_data.CITY.strip().lower() if pfml_data.CITY else ""
        ):
            logger.info("City Mismatch", extra=extra)
            return (
                MmarsEventStatusType.VCM_PENDING
                if is_vcmt_automation_enabled
                else MmarsEventStatusType.VCM_REQUIRE
            )

        if (edm_data.STATE.strip().lower() if edm_data.STATE else "") != (
            pfml_data.STATE.strip().lower() if pfml_data.STATE else ""
        ):
            logger.info("State Mismatch", extra=extra)
            return (
                MmarsEventStatusType.VCM_PENDING
                if is_vcmt_automation_enabled
                else MmarsEventStatusType.VCM_REQUIRE
            )

        if (edm_data.ZIP_CODE.replace("-", "")[:5] if edm_data.ZIP_CODE else "") != (
            pfml_data.ZIP_CODE.replace("-", "")[:5] if pfml_data.ZIP_CODE else ""
        ):
            logger.info("Zip Code Mismatch", extra=extra)
            return (
                MmarsEventStatusType.VCM_PENDING
                if is_vcmt_automation_enabled
                else MmarsEventStatusType.VCM_REQUIRE
            )

        return MmarsEventStatusType.RE_PENDING

    def check_for_vcm(self, vcc_record: MmarsEvent) -> None:
        """
        Check for VCM
        """
        extra = {
            "mmars_event_id": vcc_record.mmars_event_id,
            "employee_id": vcc_record.employee.employee_id,
            "vcc_code": vcc_record.employee.ctr_vendor_customer_code,
        }
        vcc_code = (
            vcc_record.employee.ctr_vendor_customer_code
            if vcc_record.employee.ctr_vendor_customer_code
            else None
        )
        tin = (
            vcc_record.employee.tax_identifier.tax_identifier
            if vcc_record.employee.tax_identifier
            else None
        )
        vcm_request = EDMVendorRequest(vcc_code=vcc_code, tin=tin)

        edm_vendor_information_response = self.edm_client.get_vendor_information(vcm_request)
        if edm_vendor_information_response.data:
            edm_vendor_data = edm_vendor_information_response.data[0]
        else:
            logger.info("EDM Vendor data not found for VCC Code", extra=extra)
            return

        if edm_vendor_data.VENDOR_CUSTOMER_CODE is None:
            logger.info("EDM Vendor Information not found for VCC Code", extra=extra)
            return
        extra["edm_vendor_data.VENDOR_CUSTOMER_CODE"] = edm_vendor_data.VENDOR_CUSTOMER_CODE
        employee_address = payments_util.get_employee_mailing_address(vcc_record.employee)
        if employee_address:
            employee_address_line_1 = employee_address.address_line_one
            employee_address_line_2 = employee_address.address_line_two
            city = employee_address.city if employee_address.city else ""
            state = (
                employee_address.geo_state.geo_state_description
                if employee_address.geo_state
                else ""
            )
            zip_code = employee_address.zip_code if employee_address.zip_code else ""
        else:
            employee_address_line_1 = ""
            employee_address_line_2 = ""
            city = ""
            state = ""
            zip_code = ""

        pfml_data = EDMVendorData(
            VENDOR_CUSTOMER_CODE=vcc_record.employee.ctr_vendor_customer_code,
            TIN=(
                vcc_record.employee.tax_identifier.tax_identifier
                if vcc_record.employee.tax_identifier
                else None
            ),
            FIRST_NAME=vcc_record.employee.first_name,
            LAST_NAME=vcc_record.employee.last_name,
            LEGAL_NAME=f"{vcc_record.employee.first_name} {vcc_record.employee.last_name}",
            ADDRESS_ID=vcc_record.employee.ctr_billing_address_id,
            STREET_1=employee_address_line_1,
            STREET_2=employee_address_line_2,
            CITY=city,
            STATE=state,
            ZIP_CODE=zip_code,
        )
        mmars_status = self.determine_mmars_status(edm_data=edm_vendor_data, pfml_data=pfml_data)
        vcc_record.mmars_status_type_id = mmars_status.mmars_event_status_type_id

        if mmars_status == MmarsEventStatusType.RE_PENDING:
            logger.info("EDM Vendor Information matched with PFML data", extra=extra)
            self.increment(self.Metrics.TOTAL_VCC_ALREADY_REGISTERED_IN_MMARS)
        else:
            logger.info("EDM Vendor Information not matched with PFML data", extra=extra)
            if mmars_status == MmarsEventStatusType.VCM_PENDING:
                self.increment(self.Metrics.TOTAL_VCM_PENDING)
            elif mmars_status == MmarsEventStatusType.VCM_REQUIRE:
                self.increment(self.Metrics.TOTAL_VCM_REQUIRED)

            # Create VCM record for mismatched data
            vcm_record = MmarsCustomerDetail(
                mmars_event_id=vcc_record.mmars_event_id,
                pfml_data=dict(pfml_data),
                edm_data=dict(edm_vendor_data),
            )
            self.db_session.add(vcm_record)
            self.increment(self.Metrics.TOTAL_VCM_RECORDS_WRITTEN)

        if vcc_record.employee.ctr_vendor_customer_code is None:
            logger.info("Updating vendor customer code for employee", extra=extra)
            vcc_record.employee.ctr_vendor_customer_code = edm_vendor_data.VENDOR_CUSTOMER_CODE
