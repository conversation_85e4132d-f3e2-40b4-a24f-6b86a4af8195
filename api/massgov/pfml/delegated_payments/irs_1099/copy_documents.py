import enum
import os
from typing import List

import massgov.pfml.delegated_payments.delegated_config as payments_config
import massgov.pfml.delegated_payments.irs_1099.pfml_1099_util as pfml_1099_util
import massgov.pfml.util.files as file_util
import massgov.pfml.util.logging
from massgov.pfml.db.models.payments import Pfml1099Batch
from massgov.pfml.util.batch.step import Step

logger = massgov.pfml.util.logging.get_logger(__name__)


class Copy1099DocumentsStep(Step):
    class Metrics(str, enum.Enum):
        DOCUMENTS_DELETED = "documents_deleted"
        DOCUMENTS_COPIED = "documents_copied"
        PFML_1099_BATCH_ID = "pfml_1099_batch_id"
        PRINT_SHOP_PATH = "print_shop_path"
        MERGED_PATH = "merged_path"

    def run_step(self) -> None:
        self._copy_1099_documents()

    def _get_files(self, path: str) -> List[str]:
        result = []
        for file in file_util.list_files(path):
            result.append(os.path.join(path, file))
        return result

    def _copy_1099_documents(self) -> None:
        logger.info("1099 Documents - Copy 1099 Documents Step")

        payment_feature_config = payments_config.get_payment_feature_config()

        print_shop_path = payments_config.get_s3_config().pfml_1099_document_print_shop_path
        self.set_metrics({self.Metrics.PRINT_SHOP_PATH: print_shop_path})

        batch = None

        if payment_feature_config.pfml_1099_batch_id_of_documents_to_copy:
            batch = (
                self.db_session.query(Pfml1099Batch).filter(
                    Pfml1099Batch.pfml_1099_batch_id
                    == payment_feature_config.pfml_1099_batch_id_of_documents_to_copy
                )
            ).one_or_none()
        else:
            batch = pfml_1099_util.get_current_1099_batch(self.db_session)

        if batch is None:
            logger.warning("Batch not found.")
            return

        self.set_metrics({self.Metrics.PFML_1099_BATCH_ID: str(batch.pfml_1099_batch_id)})

        try:

            if payment_feature_config.delete_files_from_print_shop_path:
                files_to_delete = self._get_files(print_shop_path)
                for file_to_delete in files_to_delete:
                    file_util.delete_file(file_to_delete)
                    self.increment(self.Metrics.DOCUMENTS_DELETED)

            merged_batch_folder = f"Batch-{batch.pfml_1099_batch_id}/Merged"
            merged_path = os.path.join(
                payments_config.get_s3_config().pfml_1099_document_archive_path, merged_batch_folder
            )
            self.set_metrics({self.Metrics.MERGED_PATH: merged_path})
            merged_batch_files = self._get_files(merged_path)

            for merged_batch_file in merged_batch_files:
                merged_file_name = file_util.get_file_name(merged_batch_file)
                print_shop_file = os.path.join(print_shop_path, merged_file_name)
                file_util.copy_file(merged_batch_file, print_shop_file, direct_copy=True)
                self.increment(self.Metrics.DOCUMENTS_COPIED)

        except Exception:
            logger.exception(
                "Error copying 1099 documents to S3 print shop path for batch ID: %s",
                batch.pfml_1099_batch_id,
                extra={"batch": batch.pfml_1099_batch_id},
            )
