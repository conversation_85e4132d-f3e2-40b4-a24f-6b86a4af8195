import os
from datetime import date
from decimal import Decimal
from typing import Any, Dict, List, Optional, Tuple
from uuid import UUID

from sqlalchemy import and_, case, cast, func, or_
from sqlalchemy.sql import literal_column
from sqlalchemy.sql.functions import coalesce
from sqlalchemy.sql.sqltypes import Date

import massgov.pfml.api.config as api_config
import massgov.pfml.db as db
import massgov.pfml.util.logging as logging
from massgov.pfml.db.lookup_data.employees import PaymentTransactionType
from massgov.pfml.db.lookup_data.payments import PaymentEventType, WithholdingType
from massgov.pfml.db.lookup_data.state import State
from massgov.pfml.db.models import LkGeoState
from massgov.pfml.db.models.employees import (
    Address,
    Claim,
    ClaimantAddress,
    Employee,
    ExperianAddressPair,
    OverpaymentRepayment,
    Payment,
    StateLog,
    TaxIdentifier,
)
from massgov.pfml.db.models.payments import (
    FineosExtractVpei,
    LkPaymentEventType,
    MmarsPaymentData,
    Pfml1099,
    Pfml1099Batch,
    Pfml1099MMARSPayment,
    Pfml1099Payment,
    Pfml1099Refund,
    Pfml1099Request,
    Pfml1099Withholding,
)
from massgov.pfml.util.datetime import get_now_us_eastern


class Constants:
    CREATED_STATUS = "Created"
    GENERATED_STATUS = "Generated"
    MERGED_STATUS = "Merged"
    COMPLETED_STATUS = "Printed and Mailed"
    REPLACED_STATUS = "Replacement Batch Created: "
    ARCHIVED_STATUS = "Archived: "
    ERRORED_STATUS = "Invalid: "

    FEDERAL_WITHHOLDING_TYPE = "FEDERAL"
    STATE_WITHHOLDING_TYPE = "STATE"


class Corrected1099:
    employee_id: str
    latest_pfml_1099_id: str
    pfml_1099_id: str
    latest_pfml_batch_id: str
    pfml_1099_batch_id: str

    def __init__(
        self,
        employee_id: str,
        latest_pfml_1099_id: str,
        pfml_1099_id: str,
        latest_pfml_batch_id: str,
        pfml_1099_batch_id: str,
    ):
        self.employee_id = employee_id
        self.latest_pfml_1099_id = latest_pfml_1099_id
        self.pfml_1099_id = pfml_1099_id
        self.latest_pfml_batch_id = latest_pfml_batch_id
        self.pfml_1099_batch_id = pfml_1099_batch_id

    def get_traceable_details(self) -> Dict[str, Optional[Any]]:

        return {
            "employee_id": self.employee_id,
            "latest_pfml_1099_id": self.latest_pfml_1099_id,
            "pfml_1099_id": self.pfml_1099_id,
            "latest_pfml_batch_id": self.latest_pfml_batch_id,
            "pfml_1099_batch_id": self.pfml_1099_batch_id,
        }


ACTIVE_STATES = [Constants.CREATED_STATUS, Constants.GENERATED_STATUS, Constants.MERGED_STATUS]

END_STATES = [
    Constants.COMPLETED_STATUS,
    Constants.REPLACED_STATUS,
    Constants.ARCHIVED_STATUS,
    Constants.ERRORED_STATUS,
]

OVERPAYMENT_TYPES_1099 = frozenset(
    [
        PaymentTransactionType.OVERPAYMENT_ACTUAL_RECOVERY,
        PaymentTransactionType.OVERPAYMENT_RECOVERY,
        PaymentTransactionType.OVERPAYMENT_RECOVERY_CANCELLATION,
        PaymentTransactionType.OVERPAYMENT_RECOVERY_REVERSE,
    ]
)

OVERPAYMENT_REPAYMENT_TYPES_1099 = frozenset(
    [
        PaymentEventType.OVERPAYMENT_ACTUAL_RECOVERY,
        PaymentEventType.OVERPAYMENT_RECOVERY,
        PaymentEventType.OVERPAYMENT_RECOVERY_CANCELLATION,
        PaymentEventType.OVERPAYMENT_RECOVERY_REVERSE,
        PaymentEventType.OVERPAYMENT_ACTUAL_RECOVERY_CANCELLATION,
    ]
)
OVERPAYMENT_TYPES_1099_IDS = frozenset(
    [overpayment_type.payment_transaction_type_id for overpayment_type in OVERPAYMENT_TYPES_1099]
)

OVERPAYMENT_REPAYMENT_TYPES_1099_IDS = frozenset(
    [
        overpayment_type.payment_event_type_id
        for overpayment_type in OVERPAYMENT_REPAYMENT_TYPES_1099
    ]
)


logger = logging.get_logger(__package__)


def get_payments(db_session: db.Session, batch: Pfml1099Batch) -> List[Any]:

    year = get_tax_year()

    is_none = None
    payments = []
    if batch.correction_ind:
        # Get all the  payment data for a reprint/correction run
        # Get the list of 1099 requests that remain open
        requests = get_1099_requests(db_session, batch.pfml_1099_batch_id)

        # For each request, copy or requery for payments
        for request in requests:

            if request.correction_ind:
                # Query for updated Payment data for the employee
                check_payments = (
                    db_session.query(
                        StateLog.payment_id, cast(StateLog.ended_at, Date).label("ended_at")
                    )
                    .join(Payment, StateLog.payment_id == Payment.payment_id)
                    .filter(
                        StateLog.end_state_id
                        == State.DELEGATED_PAYMENT_PUB_TRANSACTION_CHECK_SENT.state_id,
                        Payment.payment_transaction_type_id
                        == PaymentTransactionType.STANDARD.payment_transaction_type_id,
                    )
                    .subquery()
                )
                eft_payments = (
                    db_session.query(
                        StateLog.payment_id, cast(StateLog.ended_at, Date).label("ended_at")
                    )
                    .join(Payment, StateLog.payment_id == Payment.payment_id)
                    .filter(
                        StateLog.end_state_id
                        == State.DELEGATED_PAYMENT_PUB_TRANSACTION_EFT_SENT.state_id,
                        Payment.payment_transaction_type_id
                        == PaymentTransactionType.STANDARD.payment_transaction_type_id,
                    )
                    .subquery()
                )
                bank_errors = (
                    db_session.query(
                        StateLog.payment_id,
                        cast(StateLog.ended_at, Date).label("ended_at"),
                        func.rank()
                        .over(
                            order_by=[StateLog.ended_at],
                            partition_by=StateLog.payment_id,
                        )
                        .label("R"),
                    )
                    .join(Payment, StateLog.payment_id == Payment.payment_id)
                    .filter(
                        StateLog.end_state_id == State.DELEGATED_PAYMENT_ERROR_FROM_BANK.state_id,
                        Payment.payment_transaction_type_id
                        == PaymentTransactionType.STANDARD.payment_transaction_type_id,
                    )
                    .subquery()
                )
                child_support_payments = (
                    db_session.query(
                        StateLog.payment_id, cast(StateLog.ended_at, Date).label("ended_at")
                    )
                    .join(Payment, StateLog.payment_id == Payment.payment_id)
                    .filter(
                        StateLog.end_state_id == State.CHILD_SUPPORT_FUNDS_SENT.state_id,
                        Payment.payment_transaction_type_id
                        == PaymentTransactionType.CHILD_SUPPORT_PAYMENT.payment_transaction_type_id,
                    )
                    .subquery()
                )

                pub_payments = (
                    db_session.query(
                        Payment.payment_id.label("payment_id"),
                        Payment.claim_id.label("claim_id"),
                        Employee.employee_id.label("employee_id"),
                        Payment.amount.label("payment_amount"),
                    )
                    .add_columns(
                        case(
                            (check_payments.c.payment_id != is_none, check_payments.c.ended_at),
                            (eft_payments.c.payment_id != is_none, eft_payments.c.ended_at),
                            (
                                child_support_payments.c.payment_id != is_none,
                                child_support_payments.c.ended_at,
                            ),
                        ).label("payment_date"),
                        case((bank_errors.c.payment_id != is_none, bank_errors.c.ended_at)).label(
                            "cancel_date"
                        ),
                    )
                    .join(Claim, Payment.claim_id == Claim.claim_id)
                    .join(Employee, Claim.employee_id == Employee.employee_id)
                    .outerjoin(check_payments, Payment.payment_id == check_payments.c.payment_id)
                    .outerjoin(eft_payments, Payment.payment_id == eft_payments.c.payment_id)
                    .outerjoin(
                        child_support_payments,
                        Payment.payment_id == child_support_payments.c.payment_id,
                    )
                    .outerjoin(bank_errors, Payment.payment_id == bank_errors.c.payment_id)
                    .join(Pfml1099Request, Employee.employee_id == Pfml1099Request.employee_id)
                    .filter(
                        Pfml1099Request.employee_id == request.employee_id,
                        Pfml1099Request.pfml_1099_batch_id == request.pfml_1099_batch_id,
                    )
                    .filter(
                        or_(
                            check_payments.c.payment_id != is_none,
                            eft_payments.c.payment_id != is_none,
                            child_support_payments.c.payment_id != is_none,
                        )
                    )
                    .filter(or_(bank_errors.c.R == is_none, bank_errors.c.R == 1))
                    .filter(
                        or_(
                            case(
                                (
                                    check_payments.c.payment_id != is_none,
                                    func.date_part("YEAR", check_payments.c.ended_at),
                                ),
                                (
                                    eft_payments.c.payment_id != is_none,
                                    func.date_part("YEAR", eft_payments.c.ended_at),
                                ),
                                (
                                    child_support_payments.c.payment_id != is_none,
                                    func.date_part("YEAR", child_support_payments.c.ended_at),
                                ),
                            )
                            == year,
                            case(
                                (
                                    bank_errors.c.payment_id != is_none,
                                    func.date_part("YEAR", bank_errors.c.ended_at),
                                )
                            )
                            == year,
                        )
                    )
                    .all()
                )

                payments.extend(pub_payments)
            else:
                # Copy the Payment data in the last batch for the employee
                last_batch = get_last_1099_batch_for_employee(db_session, request.employee_id)

                if last_batch is not None:
                    pub_payments = (
                        db_session.query(
                            Pfml1099Payment.payment_id.label("payment_id"),
                            Pfml1099Payment.claim_id.label("claim_id"),
                            Pfml1099Payment.employee_id.label("employee_id"),
                            Pfml1099Payment.payment_amount.label("payment_amount"),
                            Pfml1099Payment.payment_date.label("payment_date"),
                            Pfml1099Payment.cancel_date.label("cancel_date"),
                        )
                        .filter(
                            Pfml1099Payment.pfml_1099_batch_id == last_batch.pfml_1099_batch_id,
                            Pfml1099Payment.employee_id == request.employee_id,
                        )
                        .all()
                    )

                    payments.extend(pub_payments)
    else:
        # Get all payment data for the 1099 batch
        # WITH CHECK_PAYMENTS     AS (SELECT PAYMENT_ID, CAST(ENDED_AT AS DATE) FROM STATE_LOG WHERE END_STATE_ID = 137),
        #     EFT_PAYMENTS       AS (SELECT PAYMENT_ID, CAST(ENDED_AT AS DATE) FROM STATE_LOG WHERE END_STATE_ID = 139),
        #     BANK_ERRORS        AS (SELECT PAYMENT_ID, CAST(ENDED_AT AS DATE) FROM STATE_LOG WHERE END_STATE_ID = 182)
        # SELECT CURRENT_TIMESTAMP CREATED_AT,
        #     CURRENT_TIMESTAMP UPDATED_AT,
        #     GEN_RANDOM_UUID() PFML_1099_PAYMENT_ID,
        #     PFML_1099_BATCH_ID,
        #     E.EMPLOYEE_ID EMPLOYEE_ID,
        #     CL.CLAIM_ID CLAIM_ID,
        #     P.PAYMENT_ID PAYMENT_ID,
        #     P.AMOUNT PAYMENT_AMOUNT,
        #     CASE WHEN CP.PAYMENT_ID IS NOT NULL THEN CP.ENDED_AT
        #             WHEN EFT.PAYMENT_ID IS NOT NULL THEN EFT.ENDED_AT
        #             ELSE NULL END PAYMENT_DATE,
        #     CASE WHEN BE.PAYMENT_ID IS NOT NULL THEN BE.ENDED_AT
        #             ELSE NULL END CANCEL_DATE
        # FROM PAYMENT P
        # INNER JOIN CLAIM CL ON P.CLAIM_ID = CL.CLAIM_ID
        # INNER JOIN EMPLOYEE E ON CL.EMPLOYEE_ID = E.EMPLOYEE_ID
        # LEFT OUTER JOIN CHECK_PAYMENTS CP ON P.PAYMENT_ID = CP.PAYMENT_ID
        # LEFT OUTER JOIN EFT_PAYMENTS EFT ON P.PAYMENT_ID = EFT.PAYMENT_ID
        # LEFT OUTER JOIN BANK_ERRORS BE ON P.PAYMENT_ID = BE.PAYMENT_ID
        # WHERE (CP.PAYMENT_ID IS NOT NULL OR EFT.PAYMENT_ID IS NOT NULL)
        # AND (CASE WHEN CP.PAYMENT_ID  IS NOT NULL THEN DATE_PART('YEAR', CP.ENDED_AT)
        #             WHEN EFT.PAYMENT_ID IS NOT NULL THEN DATE_PART('YEAR', EFT.ENDED_AT) END = 2021
        #     OR
        #     CASE WHEN BE.PAYMENT_ID IS NOT NULL THEN DATE_PART('YEAR', BE.ENDED_AT)   END = 2021)
        check_payments = (
            db_session.query(StateLog.payment_id, cast(StateLog.ended_at, Date).label("ended_at"))
            .join(Payment, StateLog.payment_id == Payment.payment_id)
            .filter(
                StateLog.end_state_id
                == State.DELEGATED_PAYMENT_PUB_TRANSACTION_CHECK_SENT.state_id,
                Payment.payment_transaction_type_id
                == PaymentTransactionType.STANDARD.payment_transaction_type_id,
            )
            .subquery()
        )
        eft_payments = (
            db_session.query(StateLog.payment_id, cast(StateLog.ended_at, Date).label("ended_at"))
            .join(Payment, StateLog.payment_id == Payment.payment_id)
            .filter(
                StateLog.end_state_id == State.DELEGATED_PAYMENT_PUB_TRANSACTION_EFT_SENT.state_id,
                Payment.payment_transaction_type_id
                == PaymentTransactionType.STANDARD.payment_transaction_type_id,
            )
            .subquery()
        )
        bank_errors = (
            db_session.query(
                StateLog.payment_id,
                cast(StateLog.ended_at, Date).label("ended_at"),
                func.rank()
                .over(
                    order_by=[StateLog.ended_at],
                    partition_by=StateLog.payment_id,
                )
                .label("R"),
            )
            .join(Payment, StateLog.payment_id == Payment.payment_id)
            .filter(
                StateLog.end_state_id == State.DELEGATED_PAYMENT_ERROR_FROM_BANK.state_id,
                Payment.payment_transaction_type_id
                == PaymentTransactionType.STANDARD.payment_transaction_type_id,
            )
            .subquery()
        )
        child_support_payments = (
            db_session.query(StateLog.payment_id, cast(StateLog.ended_at, Date).label("ended_at"))
            .join(Payment, StateLog.payment_id == Payment.payment_id)
            .filter(
                StateLog.end_state_id == State.CHILD_SUPPORT_FUNDS_SENT.state_id,
                Payment.payment_transaction_type_id
                == PaymentTransactionType.CHILD_SUPPORT_PAYMENT.payment_transaction_type_id,
            )
            .subquery()
        )

        payments = (
            db_session.query(
                Payment.payment_id.label("payment_id"),
                Payment.claim_id.label("claim_id"),
                Employee.employee_id.label("employee_id"),
                Payment.amount.label("payment_amount"),
            )
            .add_columns(
                case(
                    (check_payments.c.payment_id != is_none, check_payments.c.ended_at),
                    (eft_payments.c.payment_id != is_none, eft_payments.c.ended_at),
                    (
                        child_support_payments.c.payment_id != is_none,
                        child_support_payments.c.ended_at,
                    ),
                ).label("payment_date"),
                case((bank_errors.c.payment_id != is_none, bank_errors.c.ended_at)).label(
                    "cancel_date"
                ),
            )
            .join(Claim, Payment.claim_id == Claim.claim_id)
            .join(Employee, Claim.employee_id == Employee.employee_id)
            .outerjoin(check_payments, Payment.payment_id == check_payments.c.payment_id)
            .outerjoin(eft_payments, Payment.payment_id == eft_payments.c.payment_id)
            .outerjoin(
                child_support_payments, Payment.payment_id == child_support_payments.c.payment_id
            )
            .outerjoin(bank_errors, Payment.payment_id == bank_errors.c.payment_id)
            .filter(
                or_(
                    check_payments.c.payment_id != is_none,
                    eft_payments.c.payment_id != is_none,
                    child_support_payments.c.payment_id != is_none,
                )
            )
            .filter(or_(bank_errors.c.R == is_none, bank_errors.c.R == 1))
            .filter(
                or_(
                    case(
                        (
                            check_payments.c.payment_id != is_none,
                            func.date_part("YEAR", check_payments.c.ended_at),
                        ),
                        (
                            eft_payments.c.payment_id != is_none,
                            func.date_part("YEAR", eft_payments.c.ended_at),
                        ),
                        (
                            child_support_payments.c.payment_id != is_none,
                            func.date_part("YEAR", child_support_payments.c.ended_at),
                        ),
                    )
                    == year,
                    case(
                        (
                            bank_errors.c.payment_id != is_none,
                            func.date_part("YEAR", bank_errors.c.ended_at),
                        )
                    )
                    == year,
                )
            )
            .all()
        )

    logger.info("Number of Payments for %s: %s", year, len(payments))

    return payments


def get_mmars_payments(db_session: db.Session, batch: Pfml1099Batch) -> List[Any]:

    year = get_tax_year()

    payments = []
    if batch.correction_ind:
        # Get all the MMARS payment data for a reprint/correction run
        # Get the list of 1099 requests that remain open
        requests = get_1099_requests(db_session, batch.pfml_1099_batch_id)

        # For each request, copy or requery for MMARS payments
        for request in requests:
            if request.correction_ind:
                # Query for updated MMARS Payment data for the employee
                mmars_payments = (
                    db_session.query(
                        MmarsPaymentData.mmars_payment_data_id.label("mmars_payment_id"),
                        MmarsPaymentData.pymt_actg_line_amount.label("payment_amount"),
                        MmarsPaymentData.warrant_select_date.label("payment_date"),
                        Employee.employee_id.label("employee_id"),
                        MmarsPaymentData.vendor_customer_code,
                    )
                    .join(
                        Employee,
                        MmarsPaymentData.vendor_customer_code == Employee.ctr_vendor_customer_code,
                    )
                    .join(Pfml1099Request, Employee.employee_id == Pfml1099Request.employee_id)
                    .filter(
                        MmarsPaymentData.warrant_select_date >= date(year, 1, 1),
                        MmarsPaymentData.warrant_select_date < date(year + 1, 1, 1),
                        Pfml1099Request.employee_id == request.employee_id,
                        Pfml1099Request.pfml_1099_batch_id == request.pfml_1099_batch_id,
                    )
                    .all()
                )

                payments.extend(mmars_payments)
            else:
                # Copy the MMARS Payment data in the last batch for the employee
                last_batch = get_last_1099_batch_for_employee(db_session, request.employee_id)

                if last_batch is not None:
                    mmars_payments = (
                        db_session.query(
                            Pfml1099MMARSPayment.mmars_payment_id.label("mmars_payment_id"),
                            Pfml1099MMARSPayment.payment_amount.label("payment_amount"),
                            Pfml1099MMARSPayment.payment_date.label("payment_date"),
                            Pfml1099MMARSPayment.employee_id.label("employee_id"),
                        )
                        .filter(
                            Pfml1099MMARSPayment.pfml_1099_batch_id
                            == last_batch.pfml_1099_batch_id,
                            Pfml1099MMARSPayment.employee_id == request.employee_id,
                        )
                        .all()
                    )

                    payments.extend(mmars_payments)
    else:
        # Get all MMARS payment data for the 1099 batch
        # SELECT CURRENT_TIMESTAMP CREATED_AT,
        #        CURRENT_TIMESTAMP UPDATED_AT,
        #        GEN_RANDOM_UUID() PFML_1099_MMARS_PAYMENT_ID,
        #        PFML_1099_BATCH_ID,
        #        PYMT_DOC_IDENTIFIER MMARS_PAYMENT_ID,
        #        E.EMPLOYEE_ID EMPLOYEE_ID,
        #        PYMT_ACTG_LINE_AMOUNT PAYMENT_AMOUNT,
        #        CAST(WARRANT_SELECT_DATE AS DATE) PAYMENT_DATE
        # FROM MMARS_PAYMENT_DATA MPD
        # INNER JOIN EMPLOYEE E ON MPD.VENDOR_CUSTOMER_CODE = E.CTR_VENDOR_CUSTOMER_CODE
        # WHERE TO_CHAR(WARRANT_SELECT_DATE, 'YYYY') = '2021'
        payments = (
            db_session.query(
                MmarsPaymentData.mmars_payment_data_id.label("mmars_payment_id"),
                MmarsPaymentData.pymt_actg_line_amount.label("payment_amount"),
                MmarsPaymentData.warrant_select_date.label("payment_date"),
                Employee.employee_id.label("employee_id"),
                MmarsPaymentData.vendor_customer_code,
            )
            .join(
                Employee, MmarsPaymentData.vendor_customer_code == Employee.ctr_vendor_customer_code
            )
            .filter(
                MmarsPaymentData.warrant_select_date >= date(year, 1, 1),
                MmarsPaymentData.warrant_select_date < date(year + 1, 1, 1),
            )
            .all()
        )

    logger.info("Number of MMARS Payments for %s: %s", year, len(payments))

    return payments


def get_overpayment_repayments(db_session: db.Session, batch: Pfml1099Batch) -> List[Any]:
    year = get_tax_year()

    repayments = []
    if batch.correction_ind:
        # Get all the overpayment repayment data for a reprint/correction run
        # Get the list of 1099 requests that remain open
        requests = get_1099_requests(db_session, batch.pfml_1099_batch_id)
        employer_ids_with_correction_ind = list()

        # For each request, copy or requery for overpayment repayments
        for request in requests:
            if request.correction_ind:
                employer_ids_with_correction_ind.append(request.employee_id)
            else:
                # Copy the overpayment repayment data in the last batch for the employee
                last_batch = get_last_1099_batch_for_employee(db_session, request.employee_id)

                if last_batch is not None:
                    overpayment_repayments = (
                        db_session.query(
                            Pfml1099Refund.overpayment_repayment_id.label(
                                "overpayment_repayment_id"
                            ),
                            Pfml1099Refund.refund_amount.label("payment_amount"),
                            Pfml1099Refund.employee_id.label("employee_id"),
                            Pfml1099Refund.refund_date.label("payment_date"),
                        )
                        .filter(
                            Pfml1099Refund.pfml_1099_batch_id == last_batch.pfml_1099_batch_id,
                            Pfml1099Refund.employee_id == request.employee_id,
                        )
                        .all()
                    )

                    repayments.extend(overpayment_repayments)
        if employer_ids_with_correction_ind:
            vpei = db_session.query(
                FineosExtractVpei,
                func.rank()
                .over(
                    order_by=[FineosExtractVpei.fineos_extract_import_log_id.desc()],
                    partition_by=FineosExtractVpei.i,
                )
                .label("R"),
            ).subquery()

            overpayment_repayments = (
                db_session.query(
                    OverpaymentRepayment.overpayment_repayment_id.label("overpayment_repayment_id"),
                    OverpaymentRepayment.amount.label("payment_amount"),
                    Employee.employee_id.label("employee_id"),
                    OverpaymentRepayment.overpayment_repayment_date.label("payment_date"),
                )
                .join(
                    vpei,
                    (OverpaymentRepayment.fineos_pei_c_value == vpei.c.c)
                    & (OverpaymentRepayment.fineos_pei_i_value == vpei.c.i),
                )
                .join(Employee, vpei.c.payeecustomer == Employee.fineos_customer_number)
                .join(
                    LkPaymentEventType,
                    OverpaymentRepayment.payment_event_type_id
                    == LkPaymentEventType.payment_event_type_id,
                )
                .join(Pfml1099Request, Employee.employee_id == Pfml1099Request.employee_id)
                .filter(
                    Pfml1099Request.employee_id.in_(employer_ids_with_correction_ind),
                    Pfml1099Request.pfml_1099_batch_id == batch.pfml_1099_batch_id,
                )
                .filter(
                    OverpaymentRepayment.payment_event_type_id.in_(
                        OVERPAYMENT_REPAYMENT_TYPES_1099_IDS
                    ),
                    func.extract("YEAR", OverpaymentRepayment.overpayment_repayment_date) == year,
                    vpei.c.paymentmethod.notin_(["Inflight Recovery", "Automatic Offset Recovery"]),
                    vpei.c.R == 1,
                )
                .all()
            )
            repayments.extend(overpayment_repayments)
    else:
        # Get all overpayment repayment data for the 1099 batch
        # WITH OVER_PAYMENTS      AS (SELECT PAYMENT_ID, CAST(ENDED_AT AS DATE) FROM STATE_LOG WHERE END_STATE_ID = 125),
        # VPEI               AS (SELECT RANK() OVER (PARTITION BY I ORDER BY FINEOS_EXTRACT_IMPORT_LOG_ID DESC) R, PEI.*
        # FROM FINEOS_EXTRACT_VPEI PEI)
        # SELECT CURRENT_TIMESTAMP CREATED_AT,
        #       CURRENT_TIMESTAMP UPDATED_AT,
        #       GEN_RANDOM_UUID() PFML_1099_REFUND_ID,
        #       XXX PFML_1099_BATCH_ID,
        #       E.EMPLOYEE_ID EMPLOYEE_ID,
        #       NULL CLAIM_ID,
        #       P.PAYMENT_ID PAYMENT_ID,
        #       P.AMOUNT REFUND_AMOUNT
        # FROM OVER_PAYMENTS OP
        # INNER JOIN PAYMENT P ON OP.PAYMENT_ID = P.PAYMENT_ID
        # INNER JOIN VPEI ON P.FINEOS_PEI_C_VALUE = VPEI.C
        # AND P.FINEOS_PEI_I_VALUE = VPEI.I
        # INNER JOIN EMPLOYEE E ON VPEI.PAYEECUSTOMER = E.FINEOS_CUSTOMER_NUMBER
        # INNER JOIN LK_PAYMENT_TRANSACTION_TYPE PTT ON P.PAYMENT_TRANSACTION_TYPE_ID = PTT.PAYMENT_TRANSACTION_TYPE_ID
        # WHERE PTT.PAYMENT_TRANSACTION_TYPE_DESCRIPTION IN ('Overpayment Actual Recovery',
        #                                                   'Overpayment Recovery',
        #                                                   'Overpayment Recovery Reverse',
        #                                                   'Overpayment Recovery Cancellation')
        # AND DATE_PART('YEAR', OP.ENDED_AT) = 2021
        # AND VPEI.PAYMENTMETHOD NOT IN ('Inflight Recovery', 'Automatic Offset Recovery')
        # AND VPEI.R = 1
        # statelog_overpayments = (
        #     db_session.query(StateLog.payment_id, cast(StateLog.ended_at, Date).label("ended_at"))
        #     .filter(StateLog.end_state_id == State.DELEGATED_PAYMENT_PROCESSED_OVERPAYMENT.state_id)
        #     .subquery()
        # )
        vpei = db_session.query(
            FineosExtractVpei,
            func.rank()
            .over(
                order_by=[FineosExtractVpei.fineos_extract_import_log_id.desc()],
                partition_by=FineosExtractVpei.i,
            )
            .label("R"),
        ).subquery()

        overpayment_repayments = (
            db_session.query(
                OverpaymentRepayment.overpayment_repayment_id.label("overpayment_repayment_id"),
                OverpaymentRepayment.amount.label("payment_amount"),
                Employee.employee_id.label("employee_id"),
                OverpaymentRepayment.overpayment_repayment_date.label("payment_date"),
            )
            .join(
                vpei,
                (OverpaymentRepayment.fineos_pei_c_value == vpei.c.c)
                & (OverpaymentRepayment.fineos_pei_i_value == vpei.c.i),
            )
            .join(Employee, vpei.c.payeecustomer == Employee.fineos_customer_number)
            .join(
                LkPaymentEventType,
                OverpaymentRepayment.payment_event_type_id
                == LkPaymentEventType.payment_event_type_id,
            )
            .filter(
                OverpaymentRepayment.payment_event_type_id.in_(
                    OVERPAYMENT_REPAYMENT_TYPES_1099_IDS
                ),
                func.extract("YEAR", OverpaymentRepayment.overpayment_repayment_date) == year,
                vpei.c.paymentmethod.notin_(["Inflight Recovery", "Automatic Offset Recovery"]),
                vpei.c.R == 1,
            )
            .all()
        )
        repayments.extend(overpayment_repayments)

    logger.info("Number of Overpayments for %s: %s", year, len(repayments))

    return repayments


def select_claimant_addresses(db_session):
    is_none = None

    claimant_address = (
        db_session.query(
            Employee.employee_id,
            case(
                (
                    coalesce(ClaimantAddress.mailing_effective_from, "1900-01-01")
                    > coalesce(ClaimantAddress.residential_effective_from, "1900-01-01"),
                    coalesce(ClaimantAddress.mailing_effective_from, "1900-01-01"),
                ),
                else_=coalesce(ClaimantAddress.residential_effective_from, "1900-01-01"),
            ).label("effective_from"),
            case(
                (
                    coalesce(ClaimantAddress.mailing_effective_from, "1900-01-01")
                    > coalesce(ClaimantAddress.residential_effective_from, "1900-01-01"),
                    ClaimantAddress.mailing_address_id,
                ),
                else_=ClaimantAddress.residential_address_id,
            ).label("address_id"),
        )
        .join(ClaimantAddress, ClaimantAddress.employee_id == Employee.employee_id)
        .cte("latest_claimant_address")
    )

    return (
        db_session.query(
            Employee.employee_id.label("employee_id"),
            Employee.tax_identifier_id.label("tax_identifier_id"),
            Employee.fineos_c_number.label("c"),
            Employee.fineos_i_number.label("i"),
            Employee.fineos_employee_first_name.label("first_name"),
            Employee.fineos_employee_last_name.label("last_name"),
            Employee.fineos_customer_number.label("customerno"),
            Address.address_line_one.label("address1"),
            Address.address_line_two.label("address2"),
            Address.city.label("address4"),
            coalesce(LkGeoState.geo_state_description, Address.geo_state_text).label("address6"),
            Address.zip_code.label("postcode"),
            claimant_address.c.effective_from.label("ADDRESS_DATE"),
            literal_column("1").label("R"),
        )
        .join(
            Employee,
            claimant_address.c.employee_id == Employee.employee_id,
        )
        .join(
            ExperianAddressPair,
            ExperianAddressPair.fineos_address_id == claimant_address.c.address_id,
        )
        .join(
            Address,
            Address.address_id
            == case(
                (
                    ExperianAddressPair.experian_address_id != is_none,
                    ExperianAddressPair.experian_address_id,
                ),
                else_=ExperianAddressPair.fineos_address_id,
            ),
        )
        .outerjoin(LkGeoState, Address.geo_state_id == LkGeoState.geo_state_id)
        .cte(name="select_claimant_addresses")
    )


def select_1099(db_session, employees, batch_query, year):
    is_none = None

    payments = (
        db_session.query(
            Pfml1099Payment.employee_id,
            func.sum(Pfml1099Payment.payment_amount).label("GROSS_PAYMENTS"),
        )
        .join(batch_query, batch_query.c.pfml_1099_batch_id == Pfml1099Payment.pfml_1099_batch_id)
        .filter(
            func.extract("YEAR", Pfml1099Payment.payment_date) == year,
            func.extract("YEAR", func.coalesce(Pfml1099Payment.cancel_date, "01-01-1999")) != year,
        )
        .group_by(Pfml1099Payment.employee_id)
        .cte(name="payments")
    )

    credits = (
        db_session.query(
            Pfml1099Payment.employee_id,
            func.sum(Pfml1099Payment.payment_amount).label("OTHER_CREDITS"),
        )
        .join(batch_query, batch_query.c.pfml_1099_batch_id == Pfml1099Payment.pfml_1099_batch_id)
        .filter(
            func.extract("YEAR", Pfml1099Payment.payment_date) != year,
            func.extract("YEAR", func.coalesce(Pfml1099Payment.cancel_date, "01-01-1999")) == year,
        )
        .group_by(Pfml1099Payment.employee_id)
        .cte(name="credits")
    )

    mmars_payments = (
        db_session.query(
            Pfml1099MMARSPayment.employee_id,
            func.sum(Pfml1099MMARSPayment.payment_amount).label("GROSS_PAYMENTS"),
        )
        .join(
            batch_query, batch_query.c.pfml_1099_batch_id == Pfml1099MMARSPayment.pfml_1099_batch_id
        )
        .group_by(Pfml1099MMARSPayment.employee_id)
        .cte(name="mmars_payments")
    )

    overpayment_repayments = (
        db_session.query(
            Pfml1099Refund.employee_id,
            func.sum(Pfml1099Refund.refund_amount).label("OVERPAYMENT_REPAYMENTS"),
        )
        .join(batch_query, batch_query.c.pfml_1099_batch_id == Pfml1099Refund.pfml_1099_batch_id)
        .group_by(Pfml1099Refund.employee_id)
        .cte(name="overpayment_repayments")
    )

    taxes = (
        db_session.query(
            Pfml1099Withholding.employee_id,
            func.sum(
                case(
                    (
                        Pfml1099Withholding.withholding_type == WithholdingType.STATE,
                        Pfml1099Withholding.withholding_amount,
                    ),
                    else_=0,
                )
            ).label("STATE_TAX_WITHHOLDINGS"),
            func.sum(
                case(
                    (
                        Pfml1099Withholding.withholding_type == WithholdingType.FEDERAL,
                        Pfml1099Withholding.withholding_amount,
                    ),
                    else_=0,
                )
            ).label("FEDERAL_TAX_WITHHOLDINGS"),
        )
        .join(
            batch_query, batch_query.c.pfml_1099_batch_id == Pfml1099Withholding.pfml_1099_batch_id
        )
        .group_by(Pfml1099Withholding.employee_id)
        .cte(name="taxes")
    )

    mmars_addresses = (
        db_session.query(
            Pfml1099MMARSPayment.employee_id,
            cast(MmarsPaymentData.scheduled_payment_date, Date).label("PAYMENT_DATE"),
            MmarsPaymentData.address_line_1,
            MmarsPaymentData.address_line_2,
            MmarsPaymentData.city,
            MmarsPaymentData.state,
            MmarsPaymentData.zip_code,
            func.rank()
            .over(
                order_by=[
                    MmarsPaymentData.scheduled_payment_date.desc(),
                    MmarsPaymentData.created_at.desc(),
                    MmarsPaymentData.mmars_payment_data_id.desc(),
                ],
                partition_by=Pfml1099MMARSPayment.employee_id,
            )
            .label("R"),
        )
        .join(
            Pfml1099MMARSPayment,
            MmarsPaymentData.mmars_payment_data_id == Pfml1099MMARSPayment.mmars_payment_id,
        )
        .join(
            batch_query, batch_query.c.pfml_1099_batch_id == Pfml1099MMARSPayment.pfml_1099_batch_id
        )
        .cte(name="mmars_addresses")
    )

    pub_addresses = (
        db_session.query(
            Pfml1099Payment.employee_id,
            Payment.payment_date.label("PAYMENT_DATE"),
            Address.address_line_one,
            Address.address_line_two,
            Address.city,
            LkGeoState.geo_state_description.label("state"),
            Address.zip_code,
            func.rank()
            .over(
                order_by=[
                    Pfml1099Payment.payment_date.desc(),
                    Payment.fineos_extract_import_log_id.desc(),
                    Payment.payment_id.desc(),
                ],
                partition_by=Pfml1099Payment.employee_id,
            )
            .label("R"),
        )
        .join(Payment, Pfml1099Payment.payment_id == Payment.payment_id)
        .join(
            ExperianAddressPair,
            Payment.experian_address_pair_id == ExperianAddressPair.fineos_address_id,
        )
        .join(
            Address,
            Address.address_id
            == case(
                (
                    ExperianAddressPair.experian_address_id != is_none,
                    ExperianAddressPair.experian_address_id,
                ),
                else_=ExperianAddressPair.fineos_address_id,
            ),
        )
        .join(LkGeoState, Address.geo_state_id == LkGeoState.geo_state_id)
        .join(batch_query, batch_query.c.pfml_1099_batch_id == Pfml1099Payment.pfml_1099_batch_id)
        .cte(name="pub_addresses")
    )

    return (
        db_session.query(
            employees.c.employee_id,
            employees.c.tax_identifier_id,
            employees.c.c,
            employees.c.i,
            employees.c.first_name,
            employees.c.last_name,
            employees.c.customerno,
            employees.c.ADDRESS_DATE,
            employees.c.R,
            payments.c.GROSS_PAYMENTS.label("GROSS_PAYMENTS"),
            mmars_payments.c.GROSS_PAYMENTS.label("GROSS_MMARS_PAYMENTS"),
            taxes.c.STATE_TAX_WITHHOLDINGS,
            taxes.c.FEDERAL_TAX_WITHHOLDINGS,
            overpayment_repayments.c.OVERPAYMENT_REPAYMENTS,
            credits.c.OTHER_CREDITS,
            mmars_addresses.c.PAYMENT_DATE.label("MMARS_ADDRESS_DATE"),
            pub_addresses.c.PAYMENT_DATE.label("PUB_ADDRESS_DATE"),
            case(
                (
                    employees.c.ADDRESS_DATE
                    == func.greatest(
                        employees.c.ADDRESS_DATE,
                        mmars_addresses.c.PAYMENT_DATE,
                        pub_addresses.c.PAYMENT_DATE,
                    ),
                    "Using Employee Feed Address",
                ),
                (
                    mmars_addresses.c.PAYMENT_DATE
                    == func.greatest(
                        employees.c.ADDRESS_DATE,
                        mmars_addresses.c.PAYMENT_DATE,
                        pub_addresses.c.PAYMENT_DATE,
                    ),
                    "Using MMARS Payment Address",
                ),
                (
                    pub_addresses.c.PAYMENT_DATE
                    == func.greatest(
                        employees.c.ADDRESS_DATE,
                        mmars_addresses.c.PAYMENT_DATE,
                        pub_addresses.c.PAYMENT_DATE,
                    ),
                    "Using PUB Payment Address",
                ),
            ).label("ADDRESS_SOURCE"),
            case(
                (
                    employees.c.ADDRESS_DATE
                    == func.greatest(
                        employees.c.ADDRESS_DATE,
                        mmars_addresses.c.PAYMENT_DATE,
                        pub_addresses.c.PAYMENT_DATE,
                    ),
                    employees.c.address1,
                ),
                (
                    mmars_addresses.c.PAYMENT_DATE
                    == func.greatest(
                        employees.c.ADDRESS_DATE,
                        mmars_addresses.c.PAYMENT_DATE,
                        pub_addresses.c.PAYMENT_DATE,
                    ),
                    mmars_addresses.c.address_line_1,
                ),
                (
                    pub_addresses.c.PAYMENT_DATE
                    == func.greatest(
                        employees.c.ADDRESS_DATE,
                        mmars_addresses.c.PAYMENT_DATE,
                        pub_addresses.c.PAYMENT_DATE,
                    ),
                    pub_addresses.c.address_line_one,
                ),
            ).label("ADDRESS_LINE_1"),
            case(
                (
                    employees.c.ADDRESS_DATE
                    == func.greatest(
                        employees.c.ADDRESS_DATE,
                        mmars_addresses.c.PAYMENT_DATE,
                        pub_addresses.c.PAYMENT_DATE,
                    ),
                    employees.c.address2,
                ),
                (
                    mmars_addresses.c.PAYMENT_DATE
                    == func.greatest(
                        employees.c.ADDRESS_DATE,
                        mmars_addresses.c.PAYMENT_DATE,
                        pub_addresses.c.PAYMENT_DATE,
                    ),
                    mmars_addresses.c.address_line_2,
                ),
                (
                    pub_addresses.c.PAYMENT_DATE
                    == func.greatest(
                        employees.c.ADDRESS_DATE,
                        mmars_addresses.c.PAYMENT_DATE,
                        pub_addresses.c.PAYMENT_DATE,
                    ),
                    pub_addresses.c.address_line_two,
                ),
            ).label("ADDRESS_LINE_2"),
            case(
                (
                    employees.c.ADDRESS_DATE
                    == func.greatest(
                        employees.c.ADDRESS_DATE,
                        mmars_addresses.c.PAYMENT_DATE,
                        pub_addresses.c.PAYMENT_DATE,
                    ),
                    employees.c.address4,
                ),
                (
                    mmars_addresses.c.PAYMENT_DATE
                    == func.greatest(
                        employees.c.ADDRESS_DATE,
                        mmars_addresses.c.PAYMENT_DATE,
                        pub_addresses.c.PAYMENT_DATE,
                    ),
                    mmars_addresses.c.city,
                ),
                (
                    pub_addresses.c.PAYMENT_DATE
                    == func.greatest(
                        employees.c.ADDRESS_DATE,
                        mmars_addresses.c.PAYMENT_DATE,
                        pub_addresses.c.PAYMENT_DATE,
                    ),
                    pub_addresses.c.city,
                ),
            ).label("CITY"),
            case(
                (
                    employees.c.ADDRESS_DATE
                    == func.greatest(
                        employees.c.ADDRESS_DATE,
                        mmars_addresses.c.PAYMENT_DATE,
                        pub_addresses.c.PAYMENT_DATE,
                    ),
                    employees.c.address6,
                ),
                (
                    mmars_addresses.c.PAYMENT_DATE
                    == func.greatest(
                        employees.c.ADDRESS_DATE,
                        mmars_addresses.c.PAYMENT_DATE,
                        pub_addresses.c.PAYMENT_DATE,
                    ),
                    mmars_addresses.c.state,
                ),
                (
                    pub_addresses.c.PAYMENT_DATE
                    == func.greatest(
                        employees.c.ADDRESS_DATE,
                        mmars_addresses.c.PAYMENT_DATE,
                        pub_addresses.c.PAYMENT_DATE,
                    ),
                    pub_addresses.c.state,
                ),
            ).label("STATE"),
            case(
                (
                    employees.c.ADDRESS_DATE
                    == func.greatest(
                        employees.c.ADDRESS_DATE,
                        mmars_addresses.c.PAYMENT_DATE,
                        pub_addresses.c.PAYMENT_DATE,
                    ),
                    employees.c.postcode,
                ),
                (
                    mmars_addresses.c.PAYMENT_DATE
                    == func.greatest(
                        employees.c.ADDRESS_DATE,
                        mmars_addresses.c.PAYMENT_DATE,
                        pub_addresses.c.PAYMENT_DATE,
                    ),
                    mmars_addresses.c.zip_code,
                ),
                (
                    pub_addresses.c.PAYMENT_DATE
                    == func.greatest(
                        employees.c.ADDRESS_DATE,
                        mmars_addresses.c.PAYMENT_DATE,
                        pub_addresses.c.PAYMENT_DATE,
                    ),
                    pub_addresses.c.zip_code,
                ),
            ).label("ZIP_CODE"),
        )
        .join(Employee, employees.c.customerno == Employee.fineos_customer_number)
        .outerjoin(payments, Employee.employee_id == payments.c.employee_id)
        .outerjoin(credits, Employee.employee_id == credits.c.employee_id)
        .outerjoin(mmars_payments, Employee.employee_id == mmars_payments.c.employee_id)
        .outerjoin(
            overpayment_repayments,
            Employee.employee_id == overpayment_repayments.c.employee_id,
        )
        .outerjoin(taxes, Employee.employee_id == taxes.c.employee_id)
        .outerjoin(
            mmars_addresses,
            (Employee.employee_id == mmars_addresses.c.employee_id) & (mmars_addresses.c.R == 1),
        )
        .outerjoin(
            pub_addresses,
            (Employee.employee_id == pub_addresses.c.employee_id) & (pub_addresses.c.R == 1),
        )
        .filter(
            employees.c.R == 1,
        )
        .subquery()
        .alias("select_1099s")
    )


def select_correction_batch(db_session, request, batch, year):
    batch_query = (
        db_session.query(Pfml1099Batch.pfml_1099_batch_id)
        .where(Pfml1099Batch.pfml_1099_batch_id == batch.pfml_1099_batch_id)
        .cte(name="batch")
    )

    employee_feed = select_claimant_addresses(db_session)
    employee = (
        (
            db_session.query(employee_feed).join(
                Pfml1099Request, employee_feed.c.employee_id == Pfml1099Request.employee_id
            )
        )
        .join(
            batch_query,
            Pfml1099Request.pfml_1099_batch_id == batch_query.c.pfml_1099_batch_id,
        )
        .filter(
            employee_feed.c.employee_id == request.employee_id,
        )
        .cte(name="correction_batch_employees")
    )

    base_1099 = select_1099(db_session, employee, batch_query, year)
    return (
        db_session.query(base_1099, literal_column("TRUE").label("CORRECTION_IND"))
        .join(Pfml1099Request, base_1099.c.employee_id == Pfml1099Request.employee_id)
        .join(
            batch_query,
            Pfml1099Request.pfml_1099_batch_id == batch_query.c.pfml_1099_batch_id,
        )
        .filter(
            base_1099.c.employee_id == request.employee_id,
        )
    )


def select_original_batch(db_session, batch, year):
    batch_query = (
        db_session.query(Pfml1099Batch.pfml_1099_batch_id)
        .where(Pfml1099Batch.pfml_1099_batch_id == batch.pfml_1099_batch_id)
        .cte(name="batch")
    )

    payments_employees = db_session.query(Pfml1099Payment.employee_id.label("EMPLOYEE_ID")).join(
        batch_query, batch_query.c.pfml_1099_batch_id == Pfml1099Payment.pfml_1099_batch_id
    )
    mmars_payments_employees = db_session.query(
        Pfml1099MMARSPayment.employee_id.label("EMPLOYEE_ID")
    ).join(batch_query, batch_query.c.pfml_1099_batch_id == Pfml1099MMARSPayment.pfml_1099_batch_id)
    refunds_employees = db_session.query(Pfml1099Refund.employee_id.label("EMPLOYEE_ID")).join(
        batch_query, batch_query.c.pfml_1099_batch_id == Pfml1099Refund.pfml_1099_batch_id
    )
    withholdings_employees = db_session.query(
        Pfml1099Withholding.employee_id.label("EMPLOYEE_ID")
    ).join(batch_query, batch_query.c.pfml_1099_batch_id == Pfml1099Withholding.pfml_1099_batch_id)

    transactions = payments_employees.union_all(
        mmars_payments_employees, refunds_employees, withholdings_employees
    ).cte(name="transactions")

    employee_feed = select_claimant_addresses(db_session)

    employees = (
        db_session.query(employee_feed)
        .filter(employee_feed.c.employee_id.in_(db_session.query(transactions.c.EMPLOYEE_ID)))
        .cte(name="original_batch_employees")
    )

    base_1099 = select_1099(db_session, employees, batch_query, year)
    return db_session.query(base_1099, literal_column("FALSE").label("CORRECTION_IND"))


def get_1099s(db_session: db.Session, batch: Pfml1099Batch) -> List[Any]:

    year = get_tax_year()

    irs_1099s = []
    if batch.correction_ind:
        # Get all 1099 data for a reprint/correction run
        # Get a list of 1099 requests that remain open
        requests = get_1099_requests(db_session, batch.pfml_1099_batch_id)

        # For each request, copy or requery for 1099 data
        for request in requests:
            if request.correction_ind:
                # Query for updated 1099 data for the employee

                employee_1099 = select_correction_batch(
                    db_session,
                    request,
                    batch,
                    year,
                ).all()
                irs_1099s.extend(employee_1099)
            else:
                # Copy the 1099 data in the last batch for the employee
                last_batch = get_last_1099_batch_for_employee(db_session, request.employee_id)

                if last_batch is not None:
                    employee_1099 = (
                        db_session.query(
                            Pfml1099.employee_id.label("employee_id"),
                            Pfml1099.tax_identifier_id.label("tax_identifier_id"),
                            Pfml1099.c.label("c"),
                            Pfml1099.i.label("i"),
                            Pfml1099.first_name.label("first_name"),
                            Pfml1099.last_name.label("last_name"),
                            Pfml1099.account_number.label("customerno"),
                            literal_column("0").label("GROSS_MMARS_PAYMENTS"),
                            Pfml1099.gross_payments.label("GROSS_PAYMENTS"),
                            Pfml1099.state_tax_withholdings.label("STATE_TAX_WITHHOLDINGS"),
                            Pfml1099.federal_tax_withholdings.label("FEDERAL_TAX_WITHHOLDINGS"),
                            Pfml1099.overpayment_repayments.label("OVERPAYMENT_REPAYMENTS"),
                            Pfml1099.other_credits.label("OTHER_CREDITS"),
                            literal_column("FALSE").label("CORRECTION_IND"),
                            literal_column("'Using Pfml1099 Address'").label("ADDRESS_SOURCE"),
                            Pfml1099.address_line_1.label("ADDRESS_LINE_1"),
                            Pfml1099.address_line_2.label("ADDRESS_LINE_2"),
                            Pfml1099.city.label("CITY"),
                            Pfml1099.state.label("STATE"),
                            Pfml1099.zip.label("ZIP_CODE"),
                        )
                        .filter(
                            Pfml1099.pfml_1099_batch_id == last_batch.pfml_1099_batch_id,
                            Pfml1099.employee_id == request.employee_id,
                        )
                        .all()
                    )

                    irs_1099s.extend(employee_1099)

            # Mark the request as processed by setting the batch id
            request.pfml_1099_batch_id = batch.pfml_1099_batch_id
    else:
        irs_1099s = select_original_batch(db_session, batch, year)
    return irs_1099s


def get_1099_requests(db_session: db.Session, pfml_1099_batch_id: UUID) -> List[Pfml1099Request]:

    requests = (
        db_session.query(Pfml1099Request).filter(
            Pfml1099Request.pfml_1099_batch_id == pfml_1099_batch_id
        )
    ).all()

    logger.info("Number of 1099 Requests: %s", len(requests))

    return requests


def get_current_1099_batch(db_session: db.Session) -> Optional[Pfml1099Batch]:

    year = get_tax_year()

    batches = (
        db_session.query(Pfml1099Batch)
        .filter(Pfml1099Batch.tax_year == year)
        .order_by(Pfml1099Batch.batch_run_date.desc())
    ).all()

    if len(batches) == 0:
        logger.info("No current batch exists")
        return None

    logger.info("Found %s batches in %s", len(batches), year)

    for batch in batches:

        if batch.batch_status in ACTIVE_STATES:
            logger.info(
                "Found an existing batch in state=%s: %s",
                batch.batch_status,
                batch.pfml_1099_batch_id,
                extra={"batch": batch.pfml_1099_batch_id},
            )
            return batch

    return None


def get_last_1099_batch_for_employee(
    db_session: db.Session, employee_id: UUID
) -> Optional[Pfml1099Batch]:

    year = get_tax_year()

    batch = (
        db_session.query(Pfml1099Batch)
        .join(Pfml1099, Pfml1099Batch.pfml_1099_batch_id == Pfml1099.pfml_1099_batch_id)
        .filter(Pfml1099.employee_id == employee_id, Pfml1099Batch.tax_year == year)
        .order_by(Pfml1099Batch.batch_run_date.desc(), Pfml1099Batch.created_at.desc())
        .first()
    )

    if batch is None:
        return None

    return batch


def get_tax_year() -> int:
    return int(os.environ.get("IRS_1099_TAX_YEAR", "0"))


def get_batch_counts(db_session: db.Session) -> Dict[int, int]:
    batches = (
        db_session.query(Pfml1099Batch.tax_year, func.count(Pfml1099Batch.pfml_1099_batch_id))
        .group_by(Pfml1099Batch.tax_year)
        .all()
    )

    batch_counts = {}
    for record in batches:
        year = record[0]
        count = record[1]
        logger.info(
            "Batch year %i has %i entries.", year, count, extra={"tax_year": year, "count": count}
        )
        batch_counts[year] = count

    return batch_counts


def get_payment_counts(db_session: db.Session) -> Dict[str, int]:
    payments = (
        db_session.query(
            Pfml1099Payment.pfml_1099_batch_id, func.count(Pfml1099Payment.pfml_1099_payment_id)
        )
        .group_by(Pfml1099Payment.pfml_1099_batch_id)
        .all()
    )

    payment_counts = {}
    for record in payments:
        batch = record[0]
        count = record[1]
        logger.info(
            "Batch %i has %i payments.", batch, count, extra={"batch": batch, "count": count}
        )
        payment_counts[batch] = count

    return payment_counts


def get_mmars_payment_counts(db_session: db.Session) -> Dict[str, int]:
    payments = (
        db_session.query(
            Pfml1099MMARSPayment.pfml_1099_batch_id,
            func.count(Pfml1099MMARSPayment.pfml_1099_mmars_payment_id),
        )
        .group_by(Pfml1099MMARSPayment.pfml_1099_batch_id)
        .all()
    )

    payment_counts = {}
    for record in payments:
        batch = record[0]
        count = record[1]
        logger.info(
            "Batch %i has %i MMARS payments.", batch, count, extra={"batch": batch, "count": count}
        )
        payment_counts[batch] = count

    return payment_counts


def get_1099_records_to_generate(db_session: db.Session, batchId: str) -> List[Pfml1099]:

    is_none = None

    records = (
        db_session.query(Pfml1099)
        .filter(
            Pfml1099.pfml_1099_batch_id == batchId,
            Pfml1099.sub_batch != is_none,
        )
        .order_by(Pfml1099.pfml_1099_id)
        .all()
    )
    if records is not None:
        logger.info("Number of 1099 Records for batch [%s]: %s", batchId, len(records))

    return records


def get_1099_records_to_assign_sub_batch(db_session: db.Session, batchId: str) -> List[Pfml1099]:

    records = (
        db_session.query(Pfml1099)
        .filter(Pfml1099.pfml_1099_batch_id == batchId)
        .order_by(Pfml1099.pfml_1099_id)
        .all()
    )
    if records is not None:
        logger.info("Number of 1099 Records for batch [%s]: %s", batchId, len(records))

    return records


def get_1099_generated_count(db_session: db.Session, batchId: str) -> int:

    is_none = None

    records = (
        db_session.query(Pfml1099)
        .filter(Pfml1099.pfml_1099_batch_id == batchId, Pfml1099.s3_location != is_none)
        .all()
    )
    if records is not None:
        logger.info("Number of 1099 Records generated prior [%s]: %s", batchId, len(records))

    return len(records)


def get_tax_id(db_session: Any, tax_id_str: str) -> str:

    try:
        tax_id = db_session.get(TaxIdentifier, tax_id_str)
        if tax_id is not None:
            return tax_id.tax_identifier
        else:
            logger.error("There is no tax id for uuid %s", tax_id_str)
            return ""

    except Exception:
        logger.exception("Error accessing 1099 data")
        raise


# TODO (PFMLPB-7961): Update this check to not use the API server configuration
def get_upload_max_files_to_fineos() -> int:
    return api_config.get_config().upload_max_files_to_fineos


# TODO (PFMLPB-7961): Update this check to not use the API server configuration
def get_generate_1099_max_files() -> int:
    return api_config.get_config().generate_1099_max_files


def get_1099_record(db_session: db.Session, status: str, batch_id: str) -> Optional[Pfml1099]:
    """Get a 1099 record based on specific status and order by Created_at Asc"""
    return (
        db_session.query(Pfml1099)
        .order_by(Pfml1099.created_at.asc())
        .filter(Pfml1099.pfml_1099_batch_id == batch_id)
        .filter(Pfml1099.fineos_status == status)
        .first()
    )


def get_offset_1099_records(
    db_session: db.Session, batch_id: str, offset: int = 0, limit: int = 10
) -> List[Pfml1099]:
    """Get a 1099 record based on specific status and order by Created_at Asc"""
    return (
        db_session.query(Pfml1099)
        .filter(Pfml1099.pfml_1099_batch_id == batch_id)
        .order_by(Pfml1099.created_at.asc())
        .offset(offset)
        .limit(limit)
        .all()
    )


# TODO (PFMLPB-7961): Update this check to not use the API server configuration
def is_test_file() -> str:
    if api_config.get_config().enable_1099_testfile_generation:
        return "T"
    else:
        return ""


def is_correction_batch() -> bool:
    return os.environ.get("IRS_1099_CORRECTION_IND", "0") == "1"


def get_upload_1099_doc_batch_start() -> int:
    return int(os.environ.get("UPLOAD_1099_DOC_BATCH_START", 1))


def get_upload_1099_doc_batch_end() -> int:
    return int(os.environ.get("UPLOAD_1099_DOC_BATCH_END", 1))


def is_enable_offset_get_1099() -> bool:
    return os.environ.get("ENABLE_OFFSET_GET_1099", "0") == "1"


def get_1099_records_to_file(db_session: db.Session) -> List[Pfml1099]:

    irs_1099_subquery = (
        db_session.query(
            Pfml1099,
            Pfml1099Batch.batch_run_date,
            func.rank()
            .over(
                order_by=[Pfml1099Batch.batch_run_date.desc(), Pfml1099.created_at.desc()],
                partition_by=Pfml1099.employee_id,
            )
            .label("R"),
        )
        .join(Pfml1099Batch, Pfml1099Batch.pfml_1099_batch_id == Pfml1099.pfml_1099_batch_id)
        .filter(
            and_(
                Pfml1099Batch.tax_year == get_tax_year(),
                or_(
                    Pfml1099.gross_payments > 0,
                    Pfml1099.other_credits > 0,
                    Pfml1099.overpayment_repayments > 0,
                ),
            )
        )
        .subquery()
    )
    irs_1099_records = list(db_session.query(irs_1099_subquery).filter(irs_1099_subquery.c.R == 1))
    logger.info(
        "Filtered records with latest batch run date for each employee is : %s",
        len(irs_1099_records),
    )
    return irs_1099_records


def update_submission_date(db_session: db.Session, tax_data: List[Pfml1099]) -> None:

    for item in tax_data:
        db_session.query(Pfml1099).filter(Pfml1099.pfml_1099_id == item.pfml_1099_id).update(
            {Pfml1099.irs_submission_date: get_now_us_eastern()}
        )


def is_correction_submission() -> bool:
    return os.environ.get("IRS_1099_CORRECTION_IND", "0") == "1"


def get_1099_corrected_records_to_file(db_session: db.Session) -> List[Corrected1099]:

    is_none = None
    is_True = True
    corrected_data_list = []
    latest_irs_submission = (
        db_session.query(
            Pfml1099,
            func.rank()
            .over(order_by=[Pfml1099.created_at.desc()], partition_by=Pfml1099.employee_id)
            .label("R"),
        )
        .filter(
            and_(
                Pfml1099.tax_year == get_tax_year(),
                Pfml1099.irs_submission_date != is_none,
                or_(
                    Pfml1099.gross_payments > 0,
                    Pfml1099.other_credits > 0,
                    Pfml1099.overpayment_repayments > 0,
                ),
            )
        )
        .subquery()
    )
    # logger.info("submission %s", latest_irs_submission)
    latest_irs_generation = (
        db_session.query(
            Pfml1099,
            func.rank()
            .over(order_by=[Pfml1099.created_at.desc()], partition_by=Pfml1099.employee_id)
            .label("R"),
        )
        .filter(and_(Pfml1099.tax_year == get_tax_year(), Pfml1099.correction_ind == is_True))
        .subquery()
    )
    # logger.info("latest_irs_generation %s", latest_irs_generation)
    correction_records = list(
        db_session.query(latest_irs_submission)
        .with_entities(
            latest_irs_generation.c.employee_id,
            latest_irs_generation.c.pfml_1099_id.label("latest_pfml_1099_id"),
            latest_irs_submission.c.pfml_1099_id,
            latest_irs_generation.c.pfml_1099_batch_id.label("latest_pfml_batch_id"),
            latest_irs_submission.c.pfml_1099_batch_id,
        )
        .join(
            latest_irs_generation,
            latest_irs_generation.c.employee_id == latest_irs_submission.c.employee_id,
        )
        .filter(
            and_(
                latest_irs_generation.c.created_at > latest_irs_submission.c.created_at,
                latest_irs_generation.c.R == 1,
                latest_irs_submission.c.R == 1,
            )
        )
    )
    if correction_records is not None:
        logger.info("Corrected query length %s", len(correction_records))
        record_iter = iter(correction_records)
        for record in record_iter:
            corrected_data = Corrected1099(
                record.employee_id,
                record.latest_pfml_1099_id,
                record.pfml_1099_id,
                record.latest_pfml_batch_id,
                record.pfml_1099_batch_id,
            )
            corrected_data_list.append(corrected_data)
        logger.info("Number of corrected records %s", len(corrected_data_list))

    return correction_records


def get_1099_records_to_compare(
    db_session: db.Session, submitted_pfml_1099_id: str, new_pfml_1099_id: str, employee_id: str
) -> Tuple:
    """Get the previously submitted 1099 record to IRS and the newly generated 1099 record"""
    submitted_pfml_record = (
        db_session.query(Pfml1099)
        .order_by(Pfml1099.created_at.asc())
        .filter(Pfml1099.pfml_1099_id == submitted_pfml_1099_id)
        .filter(Pfml1099.employee_id == employee_id)
        .first()
    )
    generated_pfml_record = (
        db_session.query(Pfml1099)
        .order_by(Pfml1099.created_at.asc())
        .filter(Pfml1099.pfml_1099_id == new_pfml_1099_id)
        .filter(Pfml1099.employee_id == employee_id)
        .first()
    )

    return submitted_pfml_record, generated_pfml_record


def get_tax_withholdings(db_session: db.Session, batch: Pfml1099Batch) -> List[Any]:

    year = get_tax_year()

    is_none = None

    tax_withholdings = []
    if batch.correction_ind:

        # Get all the  withholding data for a reprint/correction run
        # Get the list of 1099 requests that remain open
        requests = get_1099_requests(db_session, batch.pfml_1099_batch_id)

        # For each request, copy or requery for withholdings
        for request in requests:
            if request.correction_ind:
                # Query for updated Payment data for the employee

                state_logs = (
                    db_session.query(
                        StateLog.payment_id,
                        cast(StateLog.ended_at, Date).label("ended_at"),
                        StateLog.end_state_id.label("state_id"),
                    )
                    .filter(
                        or_(
                            StateLog.end_state_id == State.FEDERAL_WITHHOLDING_FUNDS_SENT.state_id,
                            StateLog.end_state_id == State.STATE_WITHHOLDING_FUNDS_SENT.state_id,
                        )
                    )
                    .subquery()
                )

                payment_withholdings = (
                    db_session.query(
                        Payment.payment_id.label("payment_id"),
                        Payment.claim_id.label("claim_id"),
                        Employee.employee_id.label("employee_id"),
                        Payment.amount.label("withholding_amount"),
                    )
                    .add_columns(
                        case(
                            (state_logs.c.payment_id != is_none, state_logs.c.ended_at),
                        ).label("withholding_date"),
                        case((state_logs.c.payment_id != is_none, state_logs.c.state_id)).label(
                            "withholding_end_state_id"
                        ),
                    )
                    .join(Claim, Payment.claim_id == Claim.claim_id)
                    .join(Employee, Claim.employee_id == Employee.employee_id)
                    .outerjoin(state_logs, Payment.payment_id == state_logs.c.payment_id)
                    .join(Pfml1099Request, Employee.employee_id == Pfml1099Request.employee_id)
                    .filter(
                        Pfml1099Request.employee_id == request.employee_id,
                        Pfml1099Request.pfml_1099_batch_id == request.pfml_1099_batch_id,
                    )
                    .filter((state_logs.c.payment_id != is_none))
                    .filter(
                        or_(
                            case(
                                (
                                    state_logs.c.payment_id != is_none,
                                    func.date_part("YEAR", state_logs.c.ended_at),
                                ),
                            )
                            == year,
                        )
                    )
                    .all()
                )

                tax_withholdings.extend(payment_withholdings)

            else:

                # Copy the Withholding data in the last batch for the employee
                last_batch = get_last_1099_batch_for_employee(db_session, request.employee_id)

                if last_batch is not None:
                    payment_withholdings = (
                        db_session.query(
                            Pfml1099Withholding.payment_id.label("payment_id"),
                            Pfml1099Withholding.claim_id.label("claim_id"),
                            Pfml1099Withholding.employee_id.label("employee_id"),
                            Pfml1099Withholding.withholding_amount.label("withholding_amount"),
                            Pfml1099Withholding.withholding_date.label("withholding_date"),
                            Pfml1099Withholding.withholding_type_id.label(
                                "withholding_end_state_id"
                            ),
                        )
                        .filter(
                            Pfml1099Withholding.pfml_1099_batch_id == last_batch.pfml_1099_batch_id,
                            Pfml1099Withholding.employee_id == request.employee_id,
                        )
                        .all()
                    )

                    tax_withholdings.extend(payment_withholdings)
    else:
        state_logs = (
            db_session.query(
                StateLog.payment_id,
                cast(StateLog.ended_at, Date).label("ended_at"),
                StateLog.end_state_id.label("state_id"),
            )
            .filter(
                or_(
                    StateLog.end_state_id == State.FEDERAL_WITHHOLDING_FUNDS_SENT.state_id,
                    StateLog.end_state_id == State.STATE_WITHHOLDING_FUNDS_SENT.state_id,
                )
            )
            .subquery()
        )
        tax_withholdings = (
            db_session.query(
                Payment.payment_id.label("payment_id"),
                Payment.claim_id.label("claim_id"),
                Employee.employee_id.label("employee_id"),
                Payment.amount.label("withholding_amount"),
            )
            .add_columns(
                case(
                    (state_logs.c.payment_id != is_none, state_logs.c.ended_at),
                ).label("withholding_date"),
                case((state_logs.c.payment_id != is_none, state_logs.c.state_id)).label(
                    "withholding_end_state_id"
                ),
            )
            .join(Claim, Payment.claim_id == Claim.claim_id)
            .join(Employee, Claim.employee_id == Employee.employee_id)
            .outerjoin(state_logs, Payment.payment_id == state_logs.c.payment_id)
            .filter((state_logs.c.payment_id != is_none))
            .filter(
                or_(
                    case(
                        (
                            state_logs.c.payment_id != is_none,
                            func.date_part("YEAR", state_logs.c.ended_at),
                        ),
                    )
                    == year,
                )
            )
            .all()
        )
    logger.info("Number of Withholdings for %s: %s", year, len(tax_withholdings))

    return tax_withholdings


def get_withholding_type(in_type: int) -> int:

    if in_type == State.STATE_WITHHOLDING_FUNDS_SENT.state_id:
        return WithholdingType.STATE.withholding_type_id
    else:
        return WithholdingType.FEDERAL.withholding_type_id


def format_1099_amount(amount: Decimal) -> str:
    formatted_value = "-{:,.2f}".format(abs(amount)) if amount < 0 else "{:,.2f}".format(amount)
    if formatted_value == "0.00":
        formatted_value = ""
    return formatted_value
