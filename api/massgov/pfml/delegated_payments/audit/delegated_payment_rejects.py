import csv
import enum
import os
from typing import Dict, List, Optional

import massgov.pfml.api.util.state_log_util as state_log_util
import massgov.pfml.delegated_payments.audit.config as payment_report_audit_config
import massgov.pfml.delegated_payments.delegated_config as payments_config
import massgov.pfml.delegated_payments.delegated_payments_util as payments_util
import massgov.pfml.util.files as file_util
import massgov.pfml.util.logging as logging
from massgov.pfml import db
from massgov.pfml.db.lookup_data.employees import PaymentTransactionType
from massgov.pfml.db.lookup_data.payments import FineosWritebackTransactionStatus
from massgov.pfml.db.lookup_data.reference_file_type import ReferenceFileType
from massgov.pfml.db.lookup_data.state import Flow, State
from massgov.pfml.db.models.employees import Payment, StateLog
from massgov.pfml.db.models.payments import LinkSplitPayment, LkFineosWritebackTransactionStatus
from massgov.pfml.db.models.reference_file.reference_file import ReferenceFile
from massgov.pfml.db.models.state import LkState
from massgov.pfml.delegated_payments.audit.audit_writeback_util import get_writeback_for_text
from massgov.pfml.delegated_payments.audit.delegated_payment_audit_csv import (
    AUDIT_CSV_HEADER_TO_COLUMN,
    PaymentAuditCSV,
)
from massgov.pfml.delegated_payments.issue_resolution.scenarios import (
    get_issue_resolutions_for_reject_notes,
)
from massgov.pfml.delegated_payments.util.fineos_writeback_util import (
    create_payment_finished_state_log_with_writeback,
)
from massgov.pfml.services.payments.issue_resolution import create_issue_resolution
from massgov.pfml.util.aws.ses import EmailRecipient, send_templated_email
from massgov.pfml.util.batch.step import Step
from massgov.pfml.util.strings import remove_unicode_replacement_char

logger = logging.get_logger(__name__)


# Payment next states
ACCEPTED_STATE = State.DELEGATED_PAYMENT_VALIDATED
ACCEPTED_OUTCOME = state_log_util.build_outcome("Accepted payment to be added to FINEOS Writeback")

# End States when a payment is rejected or skipped
ERROR_STATES: Dict[int, LkState] = {
    PaymentTransactionType.STANDARD.payment_transaction_type_id: State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_REJECT_REPORT,
    PaymentTransactionType.EMPLOYER_REIMBURSEMENT.payment_transaction_type_id: State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_REJECT_REPORT,
    PaymentTransactionType.STATE_TAX_WITHHOLDING.payment_transaction_type_id: State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_REJECT_REPORT,
    PaymentTransactionType.FEDERAL_TAX_WITHHOLDING.payment_transaction_type_id: State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_REJECT_REPORT,
    PaymentTransactionType.CHILD_SUPPORT_PAYMENT.payment_transaction_type_id: State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_REJECT_REPORT,
}

# End States when a payment is to be paid
PAY_STATES: Dict[int, LkState] = {
    PaymentTransactionType.STANDARD.payment_transaction_type_id: ACCEPTED_STATE,
    PaymentTransactionType.EMPLOYER_REIMBURSEMENT.payment_transaction_type_id: ACCEPTED_STATE,
    PaymentTransactionType.STATE_TAX_WITHHOLDING.payment_transaction_type_id: State.STATE_WITHHOLDING_SEND_FUNDS,
    PaymentTransactionType.FEDERAL_TAX_WITHHOLDING.payment_transaction_type_id: State.FEDERAL_WITHHOLDING_SEND_FUNDS,
    PaymentTransactionType.CHILD_SUPPORT_PAYMENT.payment_transaction_type_id: State.CHILD_SUPPORT_SEND_FUNDS,
}

# End States for related payments when a payment is rejected or skipped
RELATED_ERROR_STATES: Dict[int, LkState] = {
    PaymentTransactionType.EMPLOYER_REIMBURSEMENT.payment_transaction_type_id: State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_REJECT_REPORT,
    PaymentTransactionType.STATE_TAX_WITHHOLDING.payment_transaction_type_id: State.STATE_WITHHOLDING_ERROR,
    PaymentTransactionType.FEDERAL_TAX_WITHHOLDING.payment_transaction_type_id: State.FEDERAL_WITHHOLDING_ERROR,
    PaymentTransactionType.CHILD_SUPPORT_PAYMENT.payment_transaction_type_id: State.CHILD_SUPPORT_ERROR,
}

# End States for related payments when a payment is to be paid
RELATED_PAY_STATES: Dict[int, LkState] = {
    PaymentTransactionType.EMPLOYER_REIMBURSEMENT.payment_transaction_type_id: ACCEPTED_STATE,
    PaymentTransactionType.STATE_TAX_WITHHOLDING.payment_transaction_type_id: State.STATE_WITHHOLDING_SEND_FUNDS,
    PaymentTransactionType.FEDERAL_TAX_WITHHOLDING.payment_transaction_type_id: State.FEDERAL_WITHHOLDING_SEND_FUNDS,
    PaymentTransactionType.CHILD_SUPPORT_PAYMENT.payment_transaction_type_id: State.CHILD_SUPPORT_SEND_FUNDS,
}


class PaymentRejectsException(Exception):
    """An error during Payment Rejects file processing."""


class PaymentRejectsStep(Step):
    """
    https://lwd.atlassian.net/wiki/spaces/API/pages/2365751318/Payment+Rejects+Step
    """

    class Metrics(str, enum.Enum):
        ARCHIVE_PATH = "archive_path"
        ACCEPTED_PAYMENT_COUNT = "accepted_payment_count"
        PARSED_ROWS_COUNT = "parsed_rows_count"
        PAYMENT_STATE_LOG_MISSING_COUNT = "payment_state_log_missing_count"
        PAYMENT_STATE_LOG_NOT_IN_AUDIT_RESPONSE_PENDING_COUNT = (
            "payment_state_log_not_in_audit_response_pending_count"
        )
        REJECTED_PAYMENT_COUNT = "rejected_payment_count"
        SKIPPED_PAYMENT_COUNT = "skipped_payment_count"
        STATE_LOGS_COUNT = "state_logs_count"
        MISSING_REJECT_NOTES = "missing_reject_notes"
        UNKNOWN_REJECT_NOTES = "unknown_reject_notes"

    def __init__(
        self,
        db_session: db.Session,
        log_entry_db_session: db.Session,
        payment_rejects_folder_path: str = "",
    ) -> None:
        """Constructor."""
        super().__init__(db_session, log_entry_db_session)

        self.payment_rejects_folder_path = payment_rejects_folder_path
        if not payment_rejects_folder_path:
            s3_config = payments_config.get_s3_config()
            self.payment_rejects_folder_path = s3_config.pfml_payment_rejects_archive_path
        self.payment_rejects_received_folder_path = os.path.join(
            self.payment_rejects_folder_path, payments_util.Constants.S3_INBOUND_RECEIVED_DIR
        )

    def run_step(self) -> None:
        """Top level function to process payments rejects"""
        logger.info("Start processing payment rejects")

        self.process_rejects_and_send_report()

        logger.info("Done processing payment rejects")

    def cleanup_on_failure(self) -> None:
        self.move_rejects_file_to_error_archive_folder(self.payment_rejects_folder_path)

    def csv_row_to_payment_audit_csv(self, row: Dict[str, str]) -> PaymentAuditCSV:
        """
        Convert a CSV row into a PaymentAuditCSV
        record. This needs to map the nice
        column name we used back to the var.

        For example, "PFML Payment Id" -> "pfml_payment_id"

        Note that validation of the fields we require to
        be sent back to us is done in transition_audit_pending_payment_states
        """
        adjusted_row: Dict[str, Optional[str]] = dict.fromkeys(
            AUDIT_CSV_HEADER_TO_COLUMN.values(), None
        )

        for k, v in row.items():
            key = AUDIT_CSV_HEADER_TO_COLUMN.get(k, None)
            if key is not None:
                adjusted_row[key] = v

        # mypy doesn't realize the dictionary
        # we made has the right param names
        return PaymentAuditCSV(**adjusted_row)  # type: ignore

    def parse_payment_rejects_file(self, file_path: str) -> List[PaymentAuditCSV]:
        with file_util.open_stream(file_path) as csvfile:
            parsed_csv = csv.DictReader(csvfile)

            payment_rejects_rows: List[PaymentAuditCSV] = []

            for row in parsed_csv:
                payment_reject_row = self.csv_row_to_payment_audit_csv(row)
                payment_rejects_rows.append(payment_reject_row)

        return payment_rejects_rows

    def transition_audit_pending_payment_state(
        self,
        payment: Payment,
        is_rejected_payment: bool,
        is_skipped_payment: bool,
        rejected_notes: Optional[str] = None,
    ) -> None:
        payment_state_log: Optional[StateLog] = state_log_util.get_latest_state_log_in_flow(
            payment, Flow.DELEGATED_PAYMENT, self.db_session
        )
        # For whatever reason, the reject notes end up with a weird
        # character in the new line due to the process the PI team uses.
        # Replace that character with a space so our parsing and logging
        # logic works as expected. This char represents an unknown unicode
        # character.
        if rejected_notes:
            rejected_notes = remove_unicode_replacement_char(rejected_notes)

        if payment_state_log is None:
            self.increment(self.Metrics.PAYMENT_STATE_LOG_MISSING_COUNT)
            raise PaymentRejectsException(
                f"No state log found for payment found in audit reject file: {payment.payment_id}"
            )

        if payment_state_log.end_state_id not in [
            State.DELEGATED_PAYMENT_PAYMENT_AUDIT_REPORT_SENT.state_id
        ]:
            self.increment(self.Metrics.PAYMENT_STATE_LOG_NOT_IN_AUDIT_RESPONSE_PENDING_COUNT)
            raise PaymentRejectsException(
                f"Found payment state log not in audit response pending state: {payment_state_log.end_state.state_description if payment_state_log.end_state else None}, payment_id: {payment.payment_id}"
            )

        if is_rejected_payment:
            self.increment(self.Metrics.REJECTED_PAYMENT_COUNT)
            end_state = ERROR_STATES[payments_util.get_payment_transaction_type_id(payment)]
            logger.info(
                "Payment rejected in audit report",
                extra=payments_util.get_traceable_payment_details(payment, end_state),
            )

            writeback_transaction_status = self.convert_reject_notes_to_writeback_status(
                payment, is_rejected=is_rejected_payment, rejected_notes=rejected_notes
            )

            payment_outcome = state_log_util.build_outcome(
                f"Payment rejected with notes: {rejected_notes}"
            )

        elif is_skipped_payment:
            self.increment(self.Metrics.SKIPPED_PAYMENT_COUNT)
            end_state = ERROR_STATES[payments_util.get_payment_transaction_type_id(payment)]
            logger.info(
                "Payment skipped in audit report",
                extra=payments_util.get_traceable_payment_details(payment, end_state),
            )

            writeback_transaction_status = self.convert_reject_notes_to_writeback_status(
                payment, is_rejected=is_rejected_payment, rejected_notes=rejected_notes
            )

            payment_outcome = state_log_util.build_outcome("Payment skipped")

        else:
            self.increment(self.Metrics.ACCEPTED_PAYMENT_COUNT)
            end_state = PAY_STATES[payments_util.get_payment_transaction_type_id(payment)]
            state_log_util.create_finished_state_log(
                payment, end_state, ACCEPTED_OUTCOME, self.db_session
            )
            logger.info(
                "Payment accepted in audit report",
                extra=payments_util.get_traceable_payment_details(payment, ACCEPTED_STATE),
            )

        if is_rejected_payment or is_skipped_payment:
            create_payment_finished_state_log_with_writeback(
                payment=payment,
                payment_end_state=end_state,
                payment_outcome=payment_outcome,
                writeback_transaction_status=writeback_transaction_status,
                db_session=self.db_session,
                import_log_id=self.get_import_log_id(),
            )
            self.create_issue_resolutions_for_manually_added_rejected_notes(
                payment=payment,
                rejected_notes=rejected_notes,
                is_rejected=is_rejected_payment,
            )

        # This payment may be a claimant payment, or another type of payment (e.g. - employer reimbursement, orphaned tax withholdings)
        # We need to set the status and writebacks for all related payments
        related_payments: List[LinkSplitPayment] = (
            self.db_session.query(LinkSplitPayment)
            .filter(LinkSplitPayment.payment_id == payment.payment_id)
            .all()
        )

        if related_payments:
            self.process_related_payments(
                related_payments,
                is_rejected=is_rejected_payment,
                is_skipped=is_skipped_payment,
                rejected_notes=rejected_notes,
            )

    def process_related_payments(
        self,
        related_payments: List[LinkSplitPayment],
        is_skipped: bool,
        is_rejected: bool,
        rejected_notes: Optional[str] = None,
    ) -> None:
        for payment in related_payments:
            related_payment: Payment = payment.related_payment
            if is_rejected:
                end_state = RELATED_ERROR_STATES[
                    payments_util.get_payment_transaction_type_id(related_payment)
                ]

                writeback_transaction_status = self.convert_reject_notes_to_writeback_status(
                    related_payment, is_rejected=is_rejected, rejected_notes=rejected_notes
                )

                payment_outcome = state_log_util.build_outcome("Payment rejected")

            elif is_skipped:
                end_state = RELATED_ERROR_STATES[
                    payments_util.get_payment_transaction_type_id(related_payment)
                ]

                writeback_transaction_status = self.convert_reject_notes_to_writeback_status(
                    related_payment, is_rejected=is_rejected, rejected_notes=rejected_notes
                )

                payment_outcome = state_log_util.build_outcome("Payment skipped")

            else:
                end_state = RELATED_PAY_STATES[
                    payments_util.get_payment_transaction_type_id(related_payment)
                ]
                state_log_util.create_finished_state_log(
                    related_payment, end_state, ACCEPTED_OUTCOME, self.db_session
                )

            if is_rejected or is_skipped:
                create_payment_finished_state_log_with_writeback(
                    payment=related_payment,
                    payment_end_state=end_state,
                    payment_outcome=payment_outcome,
                    writeback_transaction_status=writeback_transaction_status,
                    db_session=self.db_session,
                    import_log_id=self.get_import_log_id(),
                )
                self.create_issue_resolutions_for_manually_added_rejected_notes(
                    payment=related_payment,
                    rejected_notes=rejected_notes,
                    is_rejected=is_rejected,
                )

            logger.info(
                "Related payment moved to state %s based on primary payment",
                end_state.state_description,
                extra=payments_util.get_traceable_payment_details(related_payment, end_state),
            )

    def create_issue_resolutions_for_manually_added_rejected_notes(
        self,
        payment: Payment,
        is_rejected: bool,
        rejected_notes: Optional[str],
    ) -> None:
        """Creates issue resolutions for manually added rejected notes
        These reject notes are manually added by the Payment Integrity team
        when they review the Audit Payment Report generated by PaymentAuditReportStep.

        This function is intended for manually added rejection notes and/or
        tasks that should generate during the PaymentRejectStep.
        """
        extra = payments_util.get_traceable_payment_details(payment)
        extra["reject_notes"] = rejected_notes
        extra["is_rejected"] = is_rejected

        payment_issue_resolution_scenarios = get_issue_resolutions_for_reject_notes(
            rejected_notes, is_rejected
        )

        for scenario in payment_issue_resolution_scenarios:
            extra["payment_issue_resolution_scenario_config_id"] = (
                scenario.config.payment_issue_resolution_scenario_config_id,
            )
            extra["payment_issue_resolution_scenario_config_description"] = (
                scenario.config.payment_issue_resolution_scenario_config_description,
            )

            create_issue_resolution(
                payment=payment,
                scenario=scenario,
                import_log=self.get_import_log(),
                db_session=self.db_session,
                description=scenario.get_description(),
            )
            logger.info("Created issue resolution for payment", extra=extra)

    def convert_reject_notes_to_writeback_status(
        self, payment: Payment, is_rejected: bool, rejected_notes: Optional[str] = None
    ) -> LkFineosWritebackTransactionStatus:
        if is_rejected:
            default_transaction_status = FineosWritebackTransactionStatus.FAILED_MANUAL_VALIDATION
            status_str = "rejected"
        else:
            default_transaction_status = FineosWritebackTransactionStatus.PENDING_PAYMENT_AUDIT
            status_str = "skipped"

        extra = payments_util.get_traceable_payment_details(payment)
        extra["reject_notes"] = rejected_notes
        extra["is_rejected"] = is_rejected

        if rejected_notes is None:
            self.increment(self.Metrics.MISSING_REJECT_NOTES)
            extra["reject_result"] = "Not Set"
            logger.warning(
                "Empty reject note for %s payment: %s",
                status_str,
                payment.payment_id,
                extra=extra,
            )
            writeback_transaction_status = default_transaction_status
        else:
            writeback_for_text = get_writeback_for_text(
                rejected_notes, is_active_writeback_scenario=is_rejected
            )

            if writeback_for_text is None:
                self.increment(self.Metrics.UNKNOWN_REJECT_NOTES)
                extra["reject_result"] = "Cannot convert reject notes"
                logger.warning(
                    "Could not get writeback transaction status from reject notes for %s payment: %s, notes: %s",
                    status_str,
                    payment.payment_id,
                    rejected_notes,
                    extra=extra,
                )
                writeback_transaction_status = default_transaction_status
            else:
                extra["reject_result"] = "Parsed successfully"
                writeback_transaction_status = writeback_for_text

        extra["transaction_status"] = writeback_transaction_status.transaction_status_description
        logger.info(
            "Determined writeback status for payment that was rejected or skipped", extra=extra
        )

        return writeback_transaction_status

    def transition_audit_pending_payment_states(
        self, payment_rejects_rows: List[PaymentAuditCSV]
    ) -> None:
        for payment_rejects_row in payment_rejects_rows:
            if payment_rejects_row.pfml_payment_id is None:
                raise PaymentRejectsException("Missing payment id column in rejects file.")

            if payment_rejects_row.rejected_by_program_integrity is None:
                raise PaymentRejectsException("Missing rejection column in rejects file.")

            if payment_rejects_row.skipped_by_program_integrity is None:
                raise PaymentRejectsException("Missing skip column in rejects file.")

            payment = (
                self.db_session.query(Payment)
                .filter(Payment.payment_id == payment_rejects_row.pfml_payment_id)
                .one_or_none()
            )

            if payment is None:
                raise PaymentRejectsException(
                    f"Could not find payment from rejects file in DB: {payment_rejects_row.pfml_payment_id}"
                )

            logger.info(
                "Processing payment found in audit response file",
                extra=payments_util.get_traceable_payment_details(payment),
            )

            is_rejected_payment = payment_rejects_row.rejected_by_program_integrity == "Y"
            is_skipped_payment = payment_rejects_row.skipped_by_program_integrity == "Y"

            if is_rejected_payment and is_skipped_payment:
                raise PaymentRejectsException(
                    "Unexpected state - rejects row both rejected and skipped."
                )

            rejected_notes = payment_rejects_row.rejected_notes

            self.transition_audit_pending_payment_state(
                payment, is_rejected_payment, is_skipped_payment, rejected_notes
            )

    def get_rejects_files_to_process(self) -> List[str]:
        return file_util.list_files(self.payment_rejects_received_folder_path)

    def process_rejects_and_send_report(self) -> None:
        rejects_files = self.get_rejects_files_to_process()

        if len(rejects_files) == 0:
            raise PaymentRejectsException("No Payment Rejects file found.")

        if len(rejects_files) > 1:
            rejects_files.sort()
            rejects_file_names = ", ".join(rejects_files)
            raise PaymentRejectsException(
                f"Too many Payment Rejects files found: {rejects_file_names}"
            )

        # process the file
        rejects_file_name = rejects_files[0]
        payment_rejects_file_path = os.path.join(
            self.payment_rejects_received_folder_path, rejects_file_name
        )

        logger.info("Start processing Payment Rejects file: %s", payment_rejects_file_path)

        payment_feature_config = payments_config.get_payment_feature_config()
        if not payment_feature_config.enable_payment_audit_report_emails:
            logger.info("Skipping sending Payment Audit File Uploaded notification email")
        else:
            try:
                self.send_payment_audit_report_uploaded_email()
                logger.info("Payment Audit Report Uploaded notification email sent")
            except Exception as error:
                logger.error(
                    "Error sending Payment Audit File Upload notification email", exc_info=error
                )
        # parse the rejects file
        payment_rejects_rows: List[PaymentAuditCSV] = self.parse_payment_rejects_file(
            payment_rejects_file_path,
        )
        parsed_rows_count = len(payment_rejects_rows)
        self.set_metrics({self.Metrics.PARSED_ROWS_COUNT: parsed_rows_count})
        logger.info("Parsed %i payment rejects rows", parsed_rows_count)

        # check if returned rows match expected number in our state log
        state_logs = state_log_util.get_all_latest_state_logs_in_end_state(
            state_log_util.AssociatedClass.PAYMENT,
            State.DELEGATED_PAYMENT_PAYMENT_AUDIT_REPORT_SENT,
            self.db_session,
        )
        state_log_count = len(state_logs)
        self.set_metrics({self.Metrics.STATE_LOGS_COUNT: state_log_count})

        if state_log_count != parsed_rows_count:
            raise PaymentRejectsException(
                f"Unexpected number of parsed Payment Rejects file rows - found: {parsed_rows_count}, expected: {state_log_count}"
            )

        # transition audit pending states
        self.transition_audit_pending_payment_states(payment_rejects_rows)

        # put file in processed folder
        processed_file_path = payments_util.build_archive_path(
            self.payment_rejects_folder_path,
            payments_util.Constants.S3_INBOUND_PROCESSED_DIR,
            rejects_file_name,
        )
        file_util.rename_file(payment_rejects_file_path, processed_file_path)
        logger.info("Payment Rejects file in processed folder: %s", processed_file_path)
        self.set_metrics({self.Metrics.ARCHIVE_PATH: processed_file_path})

        # create reference file
        reference_file = ReferenceFile(
            file_location=processed_file_path,
            reference_file_type_id=ReferenceFileType.DELEGATED_PAYMENT_REJECTS.reference_file_type_id,
        )
        self.db_session.add(reference_file)

        logger.info(
            "Created reference file for Payment Rejects file: %s", reference_file.file_location
        )

        logger.info("Done processing Payment Rejects file: %s", payment_rejects_file_path)

    def send_payment_audit_report_uploaded_email(self):
        email_config = payment_report_audit_config.get_payment_audit_file_upload_email_config()
        sender_email = email_config.pfml_email_address
        recipient_email = EmailRecipient(
            to_addresses=[email_config.payment_audit_report_upload_notification_email_address],
            cc_addresses=[email_config.payment_audit_report_upload_notification_cc_email_address],
        )
        template = "PaymentAuditFileUploaded"
        send_templated_email(
            recipient_email,
            template,
            sender_email,
            email_config.bounce_forwarding_email_address,
            email_config.bounce_forwarding_email_address_arn,
            None,
        )

    def move_rejects_file_to_error_archive_folder(self, payment_rejects_archive_path: str) -> None:
        rejects_files = self.get_rejects_files_to_process()

        for rejects_file_name in rejects_files:
            payment_rejects_file_path = os.path.join(
                self.payment_rejects_received_folder_path, rejects_file_name
            )

            errored_file_path = payments_util.build_archive_path(
                payment_rejects_archive_path,
                payments_util.Constants.S3_INBOUND_ERROR_DIR,
                rejects_file_name,
            )

            file_util.rename_file(payment_rejects_file_path, errored_file_path)
            logger.warning("Payment Rejects file moved to errored folder: %s", errored_file_path)
            self.set_metrics({self.Metrics.ARCHIVE_PATH: errored_file_path})

        logger.info(
            "Done moving Payment Rejects files to error folder: %s", ", ".join(rejects_files)
        )
