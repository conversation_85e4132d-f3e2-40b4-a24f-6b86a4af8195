import enum
import os
from dataclasses import dataclass
from datetime import datetime
from typing import Iterable, List, Optional

import massgov.pfml.delegated_payments.delegated_payments_util as payments_util
import massgov.pfml.util.logging
from massgov.pfml import db
from massgov.pfml.db.lookup_data.employees import ClaimType, PaymentMethod, PaymentTransactionType
from massgov.pfml.db.models.employees import Address, ExperianAddressPair, Payment
from massgov.pfml.delegated_payments.audit.delegated_payment_audit_csv import (
    PAYMENT_AUDIT_CSV_HEADERS,
    PaymentAuditCSV,
    PaymentAuditDetails,
)
from massgov.pfml.delegated_payments.audit.delegated_payment_preapproval_util import (
    get_payment_preapproval_status,
)
from massgov.pfml.delegated_payments.postprocessing.waiting_week_processor import (
    get_payment_in_waiting_week_status,
)
from massgov.pfml.delegated_payments.pub.pub_check import _format_ip_check_memo
from massgov.pfml.delegated_payments.reporting.delegated_abstract_reporting import (
    FileConfig,
    Report,
    ReportGroup,
)
from massgov.pfml.util.datetime import date_to_isoformat, get_now_us_eastern, get_period_in_weeks

logger = massgov.pfml.util.logging.get_logger(__name__)


class PaymentAuditMetrics(str, enum.Enum):
    AUDIT_PATH = "audit_path"
    PAYMENT_COUNT = "payment_count"
    PAYMENT_FETCHED_COUNT = "payment_fetched_count"

    PAYMENT_FLAGGED_BY_AUDIT_PROCESSOR = "payment_flagged_by_audit_processor"

    # DUA reductions processor
    PAYMENT_DUA_REDUCTION_OVERLAP = "payment_dua_reduction_overlap"

    # DIA reductions processor
    PAYMENT_DIA_REDUCTION_OVERLAP = "payment_dia_reduction_overlap"
    PAYMENT_DIA_REDUCTION_ALREADY_APPLIED = "payment_dia_reduction_already_applied"

    # DOR <> FINEOS name mismatch processor
    PAYMENT_DOR_FINEOS_NAME_MISMATCH_COUNT = "payment_dor_fineos_name_mismatch_count"
    PAYMENT_DOR_FINEOS_NAME_SWAPPED_COUNT = "payment_dor_fineos_name_swapped_count"
    PAYMENT_DOR_FINEOS_NAME_PASS_COUNT = "payment_dor_fineos_name_pass_count"

    # Fineos leave duration processor
    PAYMENT_LEAVE_DURATION_THRESHOLD_EXCEEDED_COUNT = (
        "payment_leave_duration_threshold_exceeded_count"
    )
    PAYMENT_LEAVE_DURATION_PASS_COUNT = "payment_leave_duration_pass_count"
    PAYMENT_LEAVE_DURATION_MISSING_BENEFIT_YEAR_COUNT = (
        "payment_leave_duration_missing_benefit_year_count"
    )
    # Payment dates mismatch processor
    PAYMENT_DATE_MISSING_REQUIRED_DATA_COUNT = "payment_date_missing_required_data_count"
    PAYMENT_DATE_MISMATCH_COUNT = "payment_date_mismatch_count"
    PAYMENT_DATE_PASS_COUNT = "payment_date_pass_count"

    # Payment in waiting week processor
    PAYMENT_IN_WAITING_WEEK_COUNT = "payment_in_waiting_week_count"
    PAYMENT_NOT_IN_WAITING_WEEK_COUNT = "payment_not_in_waiting_week_count"


class PaymentAuditRowError(Exception):
    """An error in a row that prevents processing of the payment."""


@dataclass
class PaymentAuditData:
    """Wrapper class to create payment audit report"""

    payment: Payment
    employer_reimbursement_payment: Optional[Payment]
    is_first_time_payment: bool
    previously_errored_payment_count: int
    previously_rejected_payment_count: int
    previously_skipped_payment_count: int
    previously_paid_payment_count: int
    previously_paid_payments_string: Optional[str]
    lifetime_payment_count: int
    gross_payment_amount: str
    net_payment_amount: str
    federal_withholding_amount: str
    state_withholding_amount: str
    child_support_amount: str
    employer_reimbursement_amount: str
    federal_withholding_i_value: str
    state_withholding_i_value: str
    child_support_i_value: str
    employer_reimbursement_i_value: str
    outstanding_overpayments: str
    additional_audit_details: PaymentAuditDetails
    related_parent_payment_status: Optional[str]


def write_audit_report(
    payment_audit_data_set: Iterable[PaymentAuditData],
    output_path: str,
    db_session: db.Session,
    report_name: str,
) -> Optional[str]:
    payment_audit_report_rows: List[PaymentAuditCSV] = []
    for payment_audit_data in payment_audit_data_set:
        payment_audit_report_rows.append(
            build_audit_report_row(payment_audit_data, get_now_us_eastern(), db_session)
        )

    return write_audit_report_rows(payment_audit_report_rows, output_path, db_session, report_name)


def write_audit_report_rows(
    payment_audit_report_rows: Iterable[PaymentAuditCSV],
    output_path: str,
    db_session: db.Session,
    report_name: str,
) -> Optional[str]:
    # Setup the output file
    file_prefix = os.path.join(output_path, payments_util.Constants.S3_OUTBOUND_SENT_DIR)
    file_config = FileConfig(file_prefix=file_prefix)
    report_group = ReportGroup(file_config=file_config)

    report = Report(report_name=report_name, header_record=PAYMENT_AUDIT_CSV_HEADERS)
    report_group.add_report(report)

    for payment_audit_report_row in payment_audit_report_rows:
        report.add_record(payment_audit_report_row)

    reports = report_group.create_and_send_reports()
    if not reports:
        return None  # This will error further up.
    elif len(reports) != 1:
        raise Exception("Audit report generation created %i reports: %s" % (len(reports), reports))

    return reports[0]


def build_audit_report_row(
    payment_audit_data: PaymentAuditData, audit_report_time: datetime, db_session: db.Session
) -> PaymentAuditCSV:
    """Build a single row of the payment audit report file"""

    payment: Payment = payment_audit_data.payment

    if not payment.claim:
        raise ValueError("payment.claim is required")

    claim = payment.claim
    employee = claim.employee if claim else None
    employer_reimbursement: Optional[Payment] = payment_audit_data.employer_reimbursement_payment

    address: Optional[Address] = None
    experian_address_pair: Optional[ExperianAddressPair] = payment.experian_address_pair
    experian_address: Optional[Address] = None
    is_address_verified = "N"

    if experian_address_pair:
        experian_address = experian_address_pair.experian_address
        address = (
            experian_address
            if experian_address is not None
            else experian_address_pair.fineos_address
        )
        is_address_verified = "Y" if experian_address is not None else "N"

    employer_address: Optional[Address] = None
    employer_experian_address_pair: Optional[ExperianAddressPair] = (
        employer_reimbursement.experian_address_pair if employer_reimbursement else None
    )
    employer_experian_address: Optional[Address] = None
    employer_is_address_verified = "N"

    if employer_reimbursement:
        if employer_experian_address_pair:
            employer_experian_address = employer_experian_address_pair.experian_address
            employer_address = (
                employer_experian_address
                if employer_experian_address is not None
                else employer_experian_address_pair.fineos_address
            )
            employer_is_address_verified = "Y" if employer_experian_address is not None else "N"

    employer = claim.employer if claim else None

    check_description = _format_ip_check_memo(payment)

    payment_period_start_date = date_to_isoformat(payment.period_start_date)
    payment_period_end_date = date_to_isoformat(payment.period_end_date)

    payment_period_weeks = None
    if payment.period_start_date and payment.period_end_date:
        payment_period_weeks = get_period_in_weeks(
            payment.period_start_date, payment.period_end_date
        )

    audit_report_details = payment_audit_data.additional_audit_details
    preapproval_status = get_payment_preapproval_status(
        payment, audit_report_details.audit_report_details_list, db_session
    )
    waiting_week_status = get_payment_in_waiting_week_status(payment, db_session)
    # Check if orphaned tax records and if not, set overpayment amount in report
    if (
        payment.payment_transaction_type_id
        == PaymentTransactionType.FEDERAL_TAX_WITHHOLDING.payment_transaction_type_id
        or payment.payment_transaction_type_id
        == PaymentTransactionType.STATE_TAX_WITHHOLDING.payment_transaction_type_id
        or payment.payment_transaction_type_id
        == PaymentTransactionType.CHILD_SUPPORT_PAYMENT.payment_transaction_type_id
    ):
        overpayments = ""

    else:
        overpayments = str(payment_audit_data.outstanding_overpayments)

    payment_audit_row = PaymentAuditCSV(
        pfml_payment_id=str(payment.payment_id),
        leave_type=get_leave_type(payment),
        fineos_customer_number=employee.fineos_customer_number if employee else None,
        first_name=payment.fineos_employee_first_name,
        last_name=payment.fineos_employee_last_name,
        dor_first_name=employee.first_name if employee else None,
        dor_last_name=employee.last_name if employee else None,
        address_line_1=address.address_line_one if address else None,
        address_line_2=address.address_line_two if address else None,
        city=address.city if address else None,
        state=address.geo_state.geo_state_description if address and address.geo_state else None,
        zip=address.zip_code if address else None,
        is_address_verified=is_address_verified,
        employer_id=str(employer.fineos_employer_id) if employer else None,
        employer_payee_name=employer_reimbursement.payee_name if employer_reimbursement else None,
        employer_address_line_1=employer_address.address_line_one if employer_address else None,
        employer_address_line_2=employer_address.address_line_two if employer_address else None,
        employer_city=employer_address.city if employer_address else None,
        employer_state=(
            employer_address.geo_state.geo_state_description
            if employer_address and employer_address.geo_state
            else None
        ),
        employer_zip=employer_address.zip_code if employer_address else None,
        employer_is_address_verified=employer_is_address_verified,
        payment_preference=get_payment_preference(payment),
        scheduled_payment_date=date_to_isoformat(payment.payment_date),
        payment_period_start_date=payment_period_start_date,
        payment_period_end_date=payment_period_end_date,
        payment_period_weeks=str(payment_period_weeks),
        gross_payment_amount=str(payment_audit_data.gross_payment_amount),
        payment_amount=str(payment_audit_data.net_payment_amount),
        federal_withholding_amount=str(payment_audit_data.federal_withholding_amount),
        state_withholding_amount=str(payment_audit_data.state_withholding_amount),
        employer_reimbursement_amount=str(payment_audit_data.employer_reimbursement_amount),
        child_support_amount=str(payment_audit_data.child_support_amount),
        absence_case_number=claim.fineos_absence_id,
        c_value=payment.fineos_pei_c_value,
        i_value=payment.fineos_pei_i_value,
        federal_withholding_i_value=str(payment_audit_data.federal_withholding_i_value),
        state_withholding_i_value=str(payment_audit_data.state_withholding_i_value),
        employer_reimbursement_i_value=str(payment_audit_data.employer_reimbursement_i_value),
        child_support_i_value=str(payment_audit_data.child_support_i_value),
        absence_case_creation_date=date_to_isoformat(payment.absence_case_creation_date),
        absence_start_date=date_to_isoformat(claim.claim_start_date),
        absence_end_date=date_to_isoformat(claim.claim_end_date),
        case_status=(
            claim.fineos_absence_status.absence_status_description
            if claim.fineos_absence_status
            else None
        ),
        leave_request_decision=payment.leave_request_decision,
        check_description=check_description,
        is_first_time_payment=bool_to_str[payment_audit_data.is_first_time_payment],
        previously_errored_payment_count=str(payment_audit_data.previously_errored_payment_count),
        previously_rejected_payment_count=str(payment_audit_data.previously_rejected_payment_count),
        previously_skipped_payment_count=str(payment_audit_data.previously_skipped_payment_count),
        dua_additional_income_details=audit_report_details.dua_additional_income_details,
        dia_additional_income_details=audit_report_details.dia_additional_income_details,
        outstanding_overpayments=overpayments,
        dor_fineos_name_mismatch_details=audit_report_details.dor_fineos_name_mismatch_details,
        rejected_by_program_integrity=bool_to_str[
            audit_report_details.rejected_by_program_integrity
        ],
        skipped_by_program_integrity=bool_to_str[audit_report_details.skipped_by_program_integrity],
        rejected_notes=audit_report_details.rejected_notes,
        previously_paid_payment_count=str(payment_audit_data.previously_paid_payment_count),
        previously_paid_payments=payment_audit_data.previously_paid_payments_string,
        lifetime_payment_count=str(payment_audit_data.lifetime_payment_count),
        payment_date_mismatch_details=audit_report_details.payment_date_mismatch_details,
        is_preapproved=bool_to_str[preapproval_status.is_preapproved()],
        preapproval_issues=preapproval_status.get_preapproval_issue_description(),
        waiting_week=waiting_week_status.value,
        related_parent_payment_status=payment_audit_data.related_parent_payment_status,
    )

    return payment_audit_row


bool_to_str = {None: "", True: "Y", False: ""}


def get_leave_type(payment: Payment) -> Optional[str]:
    claim_type = payment.claim_type
    if not claim_type:
        return ""

    if claim_type.claim_type_id == ClaimType.FAMILY_LEAVE.claim_type_id:
        return "Family"
    elif claim_type.claim_type_id == ClaimType.MEDICAL_LEAVE.claim_type_id:
        return "Medical"

    raise PaymentAuditRowError("Unexpected leave type %s" % claim_type.claim_type_description)


def get_payment_preference(payment: Payment) -> str:
    if payment.disb_method_id == PaymentMethod.ACH.payment_method_id:
        return "ACH"
    elif payment.disb_method_id == PaymentMethod.CHECK.payment_method_id:
        return "Check"
    elif (
        payments_util.is_prepaid_impact_payments_enabled()
        and payment.disb_method_id == PaymentMethod.PREPAID_CARD.payment_method_id
    ):
        return "Prepaid Debit"
    else:
        return ""

    raise PaymentAuditRowError(
        "Unexpected payment preference %s" % payment.disb_method.payment_method_description
    )
