import argparse
import decimal
import random
import uuid
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Tuple

import faker

import massgov.pfml.api.util.state_log_util as state_log_util
import massgov.pfml.db as db
import massgov.pfml.util.logging as logging
from massgov.pfml.db.lookup_data.absences import AbsenceStatus
from massgov.pfml.db.lookup_data.employees import ClaimType, PaymentMethod, State
from massgov.pfml.db.lookup_data.payments import (
    ACTIVE_WRITEBACK_RECORD_STATUS,
    PENDING_ACTIVE_WRITEBACK_RECORD_STATUS,
    FineosWritebackTransactionStatus,
)
from massgov.pfml.db.models.employees import (
    <PERSON>laim,
    Employee,
    ExperianAddressPair,
    LkClaimType,
    LkPaymentMethod,
    LkState,
    Payment,
)
from massgov.pfml.db.models.factories import (
    AbsencePeriodFactory,
    AddressFactory,
    ClaimFactory,
    DuaReductionPaymentFactory,
    EmployeeFactory,
    EmployerFactory,
    ExperianAddressPairFactory,
    FineosExtractVbiTaskReportSomFactory,
)
from massgov.pfml.db.models.payments import LkFineosWritebackTransactionStatus
from massgov.pfml.delegated_payments.audit.audit_writeback_util import DUA_ADDITIONAL_INCOME_MAPPING
from massgov.pfml.delegated_payments.audit.delegated_payment_audit_csv import PaymentAuditDetails
from massgov.pfml.delegated_payments.audit.delegated_payment_audit_util import (
    PaymentAuditCSV,
    PaymentAuditData,
    build_audit_report_row,
    write_audit_report_rows,
)
from massgov.pfml.delegated_payments.mock.delegated_payments_factory import DelegatedPaymentFactory

logger = logging.get_logger(__name__)

fake = faker.Faker()
fake.seed_instance(1212)


# Setup command line generator args

parser = argparse.ArgumentParser(description="Generate fake payments files and data")
parser.add_argument(
    "--folder", type=str, default="payments_files", help="Output folder for generated files"
)


##############################
## Scenario Data Structures ##
##############################


class AuditScenarioName(Enum):
    FAMILY_LEAVE_ACH = "FAMILY_LEAVE_ACH"
    FAMILY_LEAVE_CHECK = "FAMILY_LEAVE_CHECK"
    MEDICAL_LEAVE_ACH = "MEDICAL_LEAVE_ACH"
    MEDICAL_LEAVE_CHECK = "MEDICAL_LEAVE_CHECK"

    SECOND_TIME_PAYMENT = "SECOND_TIME_PAYMENT"
    ERROR_PAYMENT = "ERROR_PAYMENT"
    ERROR_PAYMENT_RESTARTABLE = "ERROR_PAYMENT_RESTARTABLE"
    ADDRESS_VALIDATION_ERROR = "ADDRESS_VALIDATION_ERROR"
    REJECTED_PAYMENT = "REJECTED_PAYMENT"
    REJECTED_PAYMENT_RESTARTABLE = "REJECTED_PAYMENT_RESTARTABLE"
    MULTIPLE_DAYS_IN_ERROR_STATE = "MULTIPLE_DAYS_IN_ERROR_STATE"
    MULTIPLE_DAYS_IN_REJECTED_STATE = "MULTIPLE_DAYS_IN_REJECTED_STATE"
    MIXED_DAYS_IN_ERROR_OR_REJECTED_STATE = "MIXED_DAYS_IN_ERROR_OR_REJECTED_STATE"

    MULTIPLE_SUCCESSFUL_PAST_PAYMENTS = "MULTIPLE_SUCCESSFUL_PAST_PAYMENTS"

    ADDRESS_PAIR_DOES_NOT_EXIST = "ADDRESS_PAIR_DOES_NOT_EXIST"
    ADDRESS_IS_NOT_VERIFIED = "ADDRESS_IS_NOT_VERIFIED"

    AUDIT_REPORT_DETAIL_INFORMATIONAL = "AUDIT_REPORT_DETAIL_INFORMATIONAL"

    PREAPPROVED_PAYMENT = "PREAPPROVED_PAYMENT"

    IN_WAITING_WEEK_EXTENSION = "IN_WAITING_WEEK_EXTENSION"
    CANCEL_TIME_SUBMITTED = "CANCEL_TIME_SUBMITTED"


@dataclass
class StateWritebackConfig:
    state: LkState
    writeback_status: LkFineosWritebackTransactionStatus


@dataclass
class AuditScenarioDescriptor:
    scenario_name: AuditScenarioName
    claim_type: LkClaimType = ClaimType.FAMILY_LEAVE
    payment_method: LkPaymentMethod = PaymentMethod.ACH

    is_first_time_payment: bool = True
    previous_error_states: List[StateWritebackConfig] = field(default_factory=list)
    previous_rejection_states: List[StateWritebackConfig] = field(default_factory=list)

    has_address_pair: bool = True
    is_address_verified: bool = True

    # TODO add when we have a rejected use case
    # audit_report_detail_rejected: bool = False

    audit_report_detail_informational: bool = False

    # This is the default value as the current scenario setup typically
    # only involves creating a single payment
    preapproval_issues: str = "There were less than three previous payments"

    successful_past_payments_count: int = 0


@dataclass
class AuditScenarioData:
    scenario_name: AuditScenarioName
    payment_audit_data: PaymentAuditData


@dataclass
class AuditScenarioNameWithCount:
    name: AuditScenarioName
    count: int


###############
## Scenarios ##
###############

DESCRIPTORS = (
    AuditScenarioDescriptor(
        scenario_name=AuditScenarioName.FAMILY_LEAVE_ACH,
        claim_type=ClaimType.FAMILY_LEAVE,
        payment_method=PaymentMethod.ACH,
    ),
    AuditScenarioDescriptor(
        scenario_name=AuditScenarioName.FAMILY_LEAVE_CHECK,
        claim_type=ClaimType.FAMILY_LEAVE,
        payment_method=PaymentMethod.CHECK,
    ),
    AuditScenarioDescriptor(
        scenario_name=AuditScenarioName.MEDICAL_LEAVE_ACH,
        claim_type=ClaimType.MEDICAL_LEAVE,
        payment_method=PaymentMethod.ACH,
    ),
    AuditScenarioDescriptor(
        scenario_name=AuditScenarioName.MEDICAL_LEAVE_CHECK,
        claim_type=ClaimType.MEDICAL_LEAVE,
        payment_method=PaymentMethod.CHECK,
    ),
    AuditScenarioDescriptor(
        scenario_name=AuditScenarioName.SECOND_TIME_PAYMENT, is_first_time_payment=False
    ),
    AuditScenarioDescriptor(
        scenario_name=AuditScenarioName.ERROR_PAYMENT,
        previous_error_states=[
            StateWritebackConfig(
                State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_ERROR_REPORT,
                FineosWritebackTransactionStatus.FAILED_AUTOMATED_VALIDATION,
            )
        ],
    ),
    AuditScenarioDescriptor(
        scenario_name=AuditScenarioName.ERROR_PAYMENT_RESTARTABLE,
        previous_error_states=[
            StateWritebackConfig(
                State.DEPRECATED_DELEGATED_PAYMENT_ADD_TO_PAYMENT_ERROR_REPORT_RESTARTABLE,
                FineosWritebackTransactionStatus.PENDING_PRENOTE,
            )
        ],
    ),
    AuditScenarioDescriptor(
        scenario_name=AuditScenarioName.ADDRESS_VALIDATION_ERROR,
        previous_error_states=[
            StateWritebackConfig(
                State.PAYMENT_FAILED_ADDRESS_VALIDATION,
                FineosWritebackTransactionStatus.ADDRESS_VALIDATION_ERROR,
            )
        ],
    ),
    AuditScenarioDescriptor(
        scenario_name=AuditScenarioName.REJECTED_PAYMENT,
        previous_rejection_states=[
            StateWritebackConfig(
                State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_REJECT_REPORT,
                FineosWritebackTransactionStatus.FAILED_MANUAL_VALIDATION,
            )
        ],
    ),
    AuditScenarioDescriptor(
        scenario_name=AuditScenarioName.REJECTED_PAYMENT_RESTARTABLE,
        previous_rejection_states=[
            StateWritebackConfig(
                State.DEPRECATED_DELEGATED_PAYMENT_ADD_TO_PAYMENT_REJECT_REPORT_RESTARTABLE,
                FineosWritebackTransactionStatus.PENDING_PAYMENT_AUDIT,
            )
        ],
    ),
    AuditScenarioDescriptor(
        scenario_name=AuditScenarioName.MULTIPLE_DAYS_IN_ERROR_STATE,
        previous_error_states=[
            StateWritebackConfig(
                State.PAYMENT_FAILED_ADDRESS_VALIDATION,
                FineosWritebackTransactionStatus.ADDRESS_VALIDATION_ERROR,
            ),
            StateWritebackConfig(
                State.DEPRECATED_DELEGATED_PAYMENT_ADD_TO_PAYMENT_ERROR_REPORT_RESTARTABLE,
                FineosWritebackTransactionStatus.DATA_ISSUE_IN_SYSTEM,
            ),
            StateWritebackConfig(
                State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_ERROR_REPORT,
                FineosWritebackTransactionStatus.EXEMPT_EMPLOYER,
            ),
        ],
    ),
    AuditScenarioDescriptor(
        scenario_name=AuditScenarioName.MULTIPLE_DAYS_IN_REJECTED_STATE,
        previous_rejection_states=[
            StateWritebackConfig(
                State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_REJECT_REPORT,
                FineosWritebackTransactionStatus.NAME_MISMATCH,
            ),
            StateWritebackConfig(
                State.DEPRECATED_DELEGATED_PAYMENT_ADD_TO_PAYMENT_REJECT_REPORT_RESTARTABLE,
                FineosWritebackTransactionStatus.PENDING_PAYMENT_AUDIT,
            ),
        ],
    ),
    AuditScenarioDescriptor(
        scenario_name=AuditScenarioName.MIXED_DAYS_IN_ERROR_OR_REJECTED_STATE,
        previous_error_states=[
            StateWritebackConfig(
                State.DEPRECATED_DELEGATED_PAYMENT_ADD_TO_PAYMENT_ERROR_REPORT_RESTARTABLE,
                FineosWritebackTransactionStatus.DATA_ISSUE_IN_SYSTEM,
            ),
            StateWritebackConfig(
                State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_ERROR_REPORT,
                FineosWritebackTransactionStatus.EXEMPT_EMPLOYER,
            ),
        ],
        previous_rejection_states=[
            StateWritebackConfig(
                State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_REJECT_REPORT,
                FineosWritebackTransactionStatus.NAME_MISMATCH,
            ),
            StateWritebackConfig(
                State.DEPRECATED_DELEGATED_PAYMENT_ADD_TO_PAYMENT_REJECT_REPORT_RESTARTABLE,
                FineosWritebackTransactionStatus.PENDING_PAYMENT_AUDIT,
            ),
        ],
    ),
    AuditScenarioDescriptor(
        scenario_name=AuditScenarioName.ADDRESS_PAIR_DOES_NOT_EXIST, has_address_pair=False
    ),
    AuditScenarioDescriptor(
        scenario_name=AuditScenarioName.ADDRESS_IS_NOT_VERIFIED, is_address_verified=False
    ),
    AuditScenarioDescriptor(
        scenario_name=AuditScenarioName.AUDIT_REPORT_DETAIL_INFORMATIONAL,
        audit_report_detail_informational=True,
        preapproval_issues="There were less than three previous payments;DUA Additional Income",
    ),
    AuditScenarioDescriptor(
        scenario_name=AuditScenarioName.PREAPPROVED_PAYMENT, preapproval_issues=""
    ),
    AuditScenarioDescriptor(scenario_name=AuditScenarioName.IN_WAITING_WEEK_EXTENSION),
    AuditScenarioDescriptor(
        scenario_name=AuditScenarioName.MULTIPLE_SUCCESSFUL_PAST_PAYMENTS,
        successful_past_payments_count=3,
    ),
    AuditScenarioDescriptor(scenario_name=AuditScenarioName.CANCEL_TIME_SUBMITTED),
)

AUDIT_SCENARIO_DESCRIPTORS: Dict[AuditScenarioName, AuditScenarioDescriptor] = {
    scenario.scenario_name: scenario for scenario in DESCRIPTORS
}

DEFAULT_AUDIT_SCENARIO_DATA_SET = [
    AuditScenarioNameWithCount(scenario_name, 1)
    for scenario_name in AUDIT_SCENARIO_DESCRIPTORS.keys()
]

#######################
## Utility functions ##
#######################


def create_payment_with_end_state(
    c_value: str,
    i_value: str,
    claim: Claim,
    employee: Employee,
    address_pair: Optional[ExperianAddressPair],
    payment_method: LkPaymentMethod,
    end_state: LkState,
    writeback_status: Optional[LkFineosWritebackTransactionStatus],
    db_session: db.Session,
) -> Tuple[Payment, DelegatedPaymentFactory]:
    payment_date = datetime.now().date()
    period_start_date = payment_date - timedelta(days=7)
    period_end_date = payment_date - timedelta(days=1)
    absence_case_creation_date = payment_date - timedelta(days=30)

    payment_amount = round(decimal.Decimal(random.uniform(1, 1000)), 2)

    factory = DelegatedPaymentFactory(
        db_session,
        fineos_pei_c_value=c_value,
        fineos_pei_i_value=i_value,
        employee=employee,
        claim=claim,
        payment_method=payment_method,
        amount=payment_amount,
        payment_date=payment_date,
        period_start_date=period_start_date,
        period_end_date=period_end_date,
        experian_address_pair=address_pair,
    )

    factory.create_payment_detail()
    payment = factory.get_or_create_payment_with_state(end_state)
    payment.absence_case_creation_date = absence_case_creation_date
    payment.leave_request_decision = "Approved"

    factory.get_or_create_payment_with_writeback(writeback_status)

    return (payment, factory)


def _new_ci_value() -> str:
    return str(fake.unique.random_int())


def _is_restartable_state(state_writeback_config: Optional[StateWritebackConfig]) -> bool:
    if state_writeback_config is None:
        return False

    return (
        state_writeback_config.writeback_status.writeback_record_status
        == PENDING_ACTIVE_WRITEBACK_RECORD_STATUS
    )


def _create_previous_payments(
    claim: Claim,
    employee: Employee,
    address_pair: Optional[ExperianAddressPair],
    payment_method: LkPaymentMethod,
    previous_state_writebacks: List[StateWritebackConfig],
    db_session: db.Session,
) -> Tuple[Optional[str], Optional[str]]:

    previous_state_writeback: Optional[StateWritebackConfig] = None
    c_value = _new_ci_value()
    i_value = _new_ci_value()

    for state_writeback in previous_state_writebacks:
        if not _is_restartable_state(previous_state_writeback):
            c_value = _new_ci_value()
            i_value = _new_ci_value()

        create_payment_with_end_state(
            c_value,
            i_value,
            claim,
            employee,
            address_pair,
            payment_method,
            state_writeback.state,
            state_writeback.writeback_status,
            db_session,
        )

        previous_state_writeback = state_writeback

    # return last used ci value if last state was restartable
    if _is_restartable_state(previous_state_writeback):
        return (c_value, i_value)
    else:
        return (None, None)


def generate_scenario_data(
    scenario_descriptor: AuditScenarioDescriptor, db_session: db.Session
) -> AuditScenarioData:
    c_value = _new_ci_value()
    i_value = _new_ci_value()

    mailing_address = AddressFactory.create(
        address_line_one="20 South Ave", city="Burlington", geo_state_id=1, zip_code="01803"
    )

    verified_address = AddressFactory.create(
        address_line_one="20 South Avenue", city="Burlington", geo_state_id=1, zip_code="01803"
    )

    employer = EmployerFactory.create()

    employee = EmployeeFactory.create(fineos_customer_number=str(uuid.uuid4()))

    address_pair: Optional[ExperianAddressPair] = None
    if scenario_descriptor.has_address_pair:
        if scenario_descriptor.is_address_verified:
            address_pair = ExperianAddressPairFactory.create(
                fineos_address=mailing_address, experian_address=verified_address
            )
        else:
            address_pair = ExperianAddressPairFactory.create(fineos_address=mailing_address)

    claim = ClaimFactory.create(
        claim_id=uuid.uuid4(),
        employee=employee,
        employer=employer,
        # TODO:
        # remove once Claim.claim_type_id is hard deleted
        claim_type_id=scenario_descriptor.claim_type.claim_type_id,
        fineos_absence_status_id=AbsenceStatus.APPROVED.absence_status_id,
        claim_start_date=datetime.today().date() - timedelta(days=180),
        claim_end_date=datetime.today().date() - timedelta(days=90),
    )

    if not scenario_descriptor.is_first_time_payment:
        previously_audited_payment, _factory = create_payment_with_end_state(
            c_value,
            i_value,
            claim,
            employee,
            address_pair,
            scenario_descriptor.payment_method,
            State.DELEGATED_PAYMENT_PAYMENT_AUDIT_REPORT_SENT,
            None,
            db_session,
        )
        state_log_util.create_finished_state_log(
            previously_audited_payment,
            State.DELEGATED_PAYMENT_ERROR_FROM_BANK,
            state_log_util.build_outcome("test"),
            db_session,
        )

    # Populate payments and state log for previous error and rejection scenarios
    restartable_c_value = None
    restartable_i_value = None

    if len(scenario_descriptor.previous_error_states) > 0:
        restartable_c_value, restartable_i_value = _create_previous_payments(
            claim,
            employee,
            address_pair,
            scenario_descriptor.payment_method,
            scenario_descriptor.previous_error_states,
            db_session,
        )

    if len(scenario_descriptor.previous_rejection_states) > 0:
        restartable_c_value, restartable_i_value = _create_previous_payments(
            claim,
            employee,
            address_pair,
            scenario_descriptor.payment_method,
            scenario_descriptor.previous_rejection_states,
            db_session,
        )

    for _ in range(scenario_descriptor.successful_past_payments_count):
        DelegatedPaymentFactory(
            db_session,
            employee=employee,
            claim=claim,
        ).get_or_create_payment_with_writeback(FineosWritebackTransactionStatus.PAID)

    # create the current payment staged for audit
    if restartable_c_value and restartable_i_value:
        c_value = restartable_c_value
        i_value = restartable_i_value
    else:
        c_value = _new_ci_value()
        i_value = _new_ci_value()

    payment, factory = create_payment_with_end_state(
        c_value,
        i_value,
        claim,
        employee,
        address_pair,
        scenario_descriptor.payment_method,
        State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_AUDIT_REPORT,
        None,
        db_session,
    )

    if scenario_descriptor.scenario_name == AuditScenarioName.IN_WAITING_WEEK_EXTENSION:
        payment.period_start_date = claim.claim_start_date
        ClaimFactory.create(
            claim_end_date=payment.period_start_date - timedelta(days=1),
            employee=claim.employee,
            employer=claim.employer,
        )

    if scenario_descriptor.scenario_name == AuditScenarioName.CANCEL_TIME_SUBMITTED:
        FineosExtractVbiTaskReportSomFactory.create(
            status="928000",
            tasktypename="Review and Decision Cancel Time Submitted",
        )

    # Add an absence period so we don't trip the date mismatch processor
    AbsencePeriodFactory.create(
        claim=claim,
        absence_period_start_date=payment.period_start_date,
        absence_period_end_date=payment.period_end_date,
        fineos_leave_request_id=payment.fineos_leave_request_id,
    )

    if not scenario_descriptor.preapproval_issues:
        factory.create_payment_history(
            [
                State.DELEGATED_PAYMENT_COMPLETE,
                State.DELEGATED_PAYMENT_COMPLETE,
                State.DELEGATED_PAYMENT_COMPLETE,
            ]
        )

    # create the payment data
    previous_error_count = len(scenario_descriptor.previous_error_states)
    previously_rejected_payment_count = len(
        list(
            filter(
                lambda s: s.writeback_status.writeback_record_status
                == ACTIVE_WRITEBACK_RECORD_STATUS,
                scenario_descriptor.previous_rejection_states,
            )
        )
    )
    previously_skipped_payment_count = len(
        list(
            filter(
                lambda s: s.writeback_status.writeback_record_status
                == PENDING_ACTIVE_WRITEBACK_RECORD_STATUS,
                scenario_descriptor.previous_rejection_states,
            )
        )
    )

    if scenario_descriptor.audit_report_detail_informational:
        # Add audit details for when this uses those directly
        additional_audit_details = PaymentAuditDetails(
            rejected_by_program_integrity=False,
            skipped_by_program_integrity=False,
            rejected_notes=DUA_ADDITIONAL_INCOME_MAPPING.default_reject_notes,
            dua_additional_income_details="Test message",
        )

        # Add DUA details for when it runs the processor
        DuaReductionPaymentFactory.create(
            fineos_customer_number=employee.fineos_customer_number,
            request_week_begin_date=payment.period_start_date,
        )

    else:
        additional_audit_details = PaymentAuditDetails()

    payment_audit_data = PaymentAuditData(
        payment=payment,
        employer_reimbursement_payment=None,
        is_first_time_payment=scenario_descriptor.is_first_time_payment,
        previously_errored_payment_count=previous_error_count,
        previously_rejected_payment_count=previously_rejected_payment_count,
        previously_skipped_payment_count=previously_skipped_payment_count,
        previously_paid_payment_count=0,
        previously_paid_payments_string=None,
        lifetime_payment_count=scenario_descriptor.successful_past_payments_count,
        outstanding_overpayments=str(payment.amount),
        gross_payment_amount="",
        net_payment_amount=str(payment.amount),
        federal_withholding_amount="",
        state_withholding_amount="",
        child_support_amount="",
        employer_reimbursement_amount="",
        federal_withholding_i_value="",
        state_withholding_i_value="",
        child_support_i_value="",
        employer_reimbursement_i_value="",
        additional_audit_details=additional_audit_details,
        related_parent_payment_status=None,
    )

    return AuditScenarioData(
        scenario_name=scenario_descriptor.scenario_name, payment_audit_data=payment_audit_data
    )


def generate_audit_report_dataset(
    data_set_config: List[AuditScenarioNameWithCount], db_session: db.Session
) -> List[AuditScenarioData]:
    scenario_data_set: List[AuditScenarioData] = []

    for scenario_with_count in data_set_config:
        scenario_name = scenario_with_count.name
        scenario_count = scenario_with_count.count
        scenario_descriptor = AUDIT_SCENARIO_DESCRIPTORS[scenario_name]

        for i in range(scenario_count):  # noqa: B007
            scenario_data = generate_scenario_data(scenario_descriptor, db_session)
            scenario_data_set.append(scenario_data)

    return scenario_data_set


def generate_payment_audit_data_set_and_rejects_file(
    config: List[AuditScenarioNameWithCount],
    folder_path: str,
    db_session: db.Session,
    reject_rate: Optional[decimal.Decimal] = None,
    file_name: str = "Payment-Audit-Report-Response",
) -> List[AuditScenarioData]:
    if not reject_rate:
        reject_rate = decimal.Decimal(0.5)
    payment_audit_scenario_data_set: List[AuditScenarioData] = generate_audit_report_dataset(
        config, db_session
    )

    audit_report_time = datetime.now()
    payment_audit_report_rows: List[PaymentAuditCSV] = []

    for payment_audit_scenario_data in payment_audit_scenario_data_set:
        payment_audit_data: PaymentAuditData = payment_audit_scenario_data.payment_audit_data

        audit_report_row = build_audit_report_row(payment_audit_data, audit_report_time, db_session)

        audit_report_row.rejected_by_program_integrity = (
            "Y" if random.random() <= reject_rate else ""
        )
        audit_report_row.skipped_by_program_integrity = (
            ""  # clear this in case it was set by audit report details flow
        )

        payment_audit_report_rows.append(audit_report_row)

        # transition to sent state to simulate the payment audit report step
        state_log_util.create_finished_state_log(
            payment_audit_data.payment,
            State.DELEGATED_PAYMENT_PAYMENT_AUDIT_REPORT_SENT,
            state_log_util.build_outcome("test"),
            db_session,
        )

    db_session.commit()

    write_audit_report_rows(
        payment_audit_report_rows, folder_path, db_session, report_name=file_name
    )

    return payment_audit_scenario_data_set


def generate_payment_rejects_file():
    logging.init(__name__)

    logger.info("Generating payment rejects file.")

    db_session = db.init(sync_lookups=True)
    db.models.factories.db_session = db_session

    args = parser.parse_args()
    folder_path = args.folder

    generate_payment_audit_data_set_and_rejects_file(
        DEFAULT_AUDIT_SCENARIO_DATA_SET, folder_path, db_session
    )

    db_session.commit()

    logger.info("Done generating payment rejects file.")
