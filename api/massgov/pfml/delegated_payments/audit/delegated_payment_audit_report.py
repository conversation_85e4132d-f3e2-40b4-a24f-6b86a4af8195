import decimal
import os
from datetime import date
from enum import Enum
from typing import Iterable, List, Optional, Tuple, cast

from sqlalchemy.orm import joinedload

import massgov.pfml.api.util.state_log_util as state_log_util
import massgov.pfml.delegated_payments.audit.config as payment_report_audit_config
import massgov.pfml.delegated_payments.delegated_config as payments_config
import massgov.pfml.delegated_payments.delegated_payments_util as payments_util
import massgov.pfml.util.files as file_util
import massgov.pfml.util.logging as logging
from massgov.pfml import db
from massgov.pfml.db.lookup_data.employees import (
    LkPaymentTransactionType,
    PaymentTransactionType,
    State,
)
from massgov.pfml.db.lookup_data.payments import (
    ACTIVE_WRITEBACK_RECORD_STATUS,
    PENDING_ACTIVE_WRITEBACK_RECORD_STATUS,
    FineosWritebackTransactionStatus,
)
from massgov.pfml.db.lookup_data.reference_file_type import ReferenceFileType
from massgov.pfml.db.lookup_data.state import Flow
from massgov.pfml.db.models.employees import (
    Employee,
    LkState,
    Overpayment,
    OverpaymentRepayment,
    Payment,
    StateLog,
)
from massgov.pfml.db.models.payments import (
    FineosWritebackDetails,
    LinkSplitPayment,
    MmarsPaymentData,
)
from massgov.pfml.db.models.reference_file.reference_file import ReferenceFile
from massgov.pfml.delegated_payments.audit.delegated_payment_audit_csv import PaymentAuditDetails
from massgov.pfml.delegated_payments.audit.delegated_payment_audit_util import (
    PaymentAuditData,
    PaymentAuditMetrics,
    write_audit_report,
)
from massgov.pfml.delegated_payments.postprocessing.audit_processor import (
    AuditProcessor,
    AuditReportAction,
)
from massgov.pfml.delegated_payments.postprocessing.cancel_time_submitted_processor import (
    CancelTimeSubmittedProcessor,
)
from massgov.pfml.delegated_payments.postprocessing.dia_reductions_processor import (
    DiaReductionsProcessor,
)
from massgov.pfml.delegated_payments.postprocessing.dor_fineos_employee_name_mismatch_processor import (
    DORFineosEmployeeNameMismatchProcessor,
)
from massgov.pfml.delegated_payments.postprocessing.dua_reductions_processor import (
    DuaReductionsProcessor,
)
from massgov.pfml.delegated_payments.postprocessing.payment_date_mismatch_processor import (
    PaymentDateMismatchProcessor,
)
from massgov.pfml.delegated_payments.postprocessing.waiting_week_processor import (
    WaitingWeekProcessor,
)
from massgov.pfml.delegated_payments.util.fineos_writeback_util import (
    create_payment_finished_state_log_with_writeback,
)
from massgov.pfml.util.aws.ses import EmailRecipient, send_templated_email
from massgov.pfml.util.batch.step import Step

logger = logging.get_logger(__name__)

EXCEL_FILE_CELL_MAXIMUM_SIZE = 32_767


class RelatedPaymentStatus(str, Enum):
    MAIN_PAYMENT_PREVIOUSLY_PAID = "Main Payment Previously Paid"
    MAIN_PAYMENT_PREVIOUSLY_REJECTED = "Main Payment Previously Rejected"


class PaymentAuditError(Exception):
    """An error in a row that prevents processing of the payment."""


class PaymentAuditReportStep(Step):
    """
    https://lwd.atlassian.net/wiki/spaces/API/pages/2365751311/Payment+Audit+Report+Step
    """

    Metrics = PaymentAuditMetrics

    audit_processors: List[AuditProcessor]

    def __init__(
        self,
        db_session: db.Session,
        log_entry_db_session: db.Session,
        s3_config: payments_config.PaymentsS3Config,
    ):
        super().__init__(db_session, log_entry_db_session)
        self.s3_config = s3_config
        self.audit_processors = [
            DORFineosEmployeeNameMismatchProcessor(self),
            PaymentDateMismatchProcessor(self),
            WaitingWeekProcessor(self),
            CancelTimeSubmittedProcessor(self),
        ]
        if payments_config.get_payment_feature_config().dia_dua_annotations_enabled:
            self.audit_processors.insert(0, DuaReductionsProcessor(self))

    def run_step(self) -> None:
        self.generate_audit_report()

    def get_payments_for_audit_report(self) -> List[Payment]:
        logger.info("Start fetching payments for audit report")

        state_logs_containers: List[StateLog] = []

        state_logs = state_log_util.get_all_latest_state_logs_in_end_state(
            state_log_util.AssociatedClass.PAYMENT,
            State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_AUDIT_REPORT,
            self.db_session,
        )

        state_logs_containers += state_logs

        federal_withholding_state_logs = state_log_util.get_all_latest_state_logs_in_end_state(
            state_log_util.AssociatedClass.PAYMENT,
            State.FEDERAL_WITHHOLDING_ORPHANED_PENDING_AUDIT,
            self.db_session,
        )

        state_logs_containers += federal_withholding_state_logs

        state_withholding_state_logs = state_log_util.get_all_latest_state_logs_in_end_state(
            state_log_util.AssociatedClass.PAYMENT,
            State.STATE_WITHHOLDING_ORPHANED_PENDING_AUDIT,
            self.db_session,
        )

        state_logs_containers += state_withholding_state_logs

        # Include Orphaned Payments in Audit file
        paid_orphaned_payment_logs = state_log_util.get_all_latest_state_logs_in_end_state(
            state_log_util.AssociatedClass.PAYMENT,
            State.MAIN_PAYMENT_PREVIOUSLY_PAID,
            self.db_session,
        )
        state_logs_containers += paid_orphaned_payment_logs

        rejected_orphaned_payment_logs = state_log_util.get_all_latest_state_logs_in_end_state(
            state_log_util.AssociatedClass.PAYMENT,
            State.MAIN_PAYMENT_PREVIOUSLY_REJECTED,
            self.db_session,
        )
        state_logs_containers += rejected_orphaned_payment_logs

        child_support_state_logs = state_log_util.get_all_latest_state_logs_in_end_state(
            state_log_util.AssociatedClass.PAYMENT,
            State.CHILD_SUPPORT_ORPHANED_PENDING_AUDIT,
            self.db_session,
        )

        state_logs_containers += child_support_state_logs

        state_log_count = len(state_logs_containers)
        self.set_metrics({self.Metrics.PAYMENT_FETCHED_COUNT: state_log_count})

        payments: List[Payment] = []
        for state_log in state_logs_containers:
            payment = state_log.payment

            # Shouldn't happen as they should always have a payment attached
            # but due to our unassociated state log logic, it technically can happen
            # elsewhere in the code and we want to be certain it isn't happening here
            if not payment:
                raise PaymentAuditError(
                    f"A state log was found without a payment in while trying to fetch payments for audit report: {state_log.state_log_id}"
                )

            logger.info(
                "Fetched payment to put into the audit report",
                extra=payments_util.get_traceable_payment_details(payment),
            )

            payments.append(payment)

        logger.info("Done fetching payments for audit report: %i", len(payments))

        return payments

    def set_payments_to_sent_state(self, payments: Iterable[Payment]) -> None:
        logger.info("Start setting payments to sent state")

        for payment in payments:
            logger.info(
                "Adding payment to the audit report",
                extra=payments_util.get_traceable_payment_details(
                    payment, State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_AUDIT_REPORT
                ),
            )

            create_payment_finished_state_log_with_writeback(
                payment=payment,
                payment_end_state=State.DELEGATED_PAYMENT_PAYMENT_AUDIT_REPORT_SENT,
                payment_outcome=state_log_util.build_outcome("Payment Audit Report sent"),
                writeback_transaction_status=FineosWritebackTransactionStatus.PAYMENT_AUDIT_IN_PROGRESS,
                db_session=self.db_session,
            )

            if (
                payment.payment_transaction_type_id
                == PaymentTransactionType.STANDARD.payment_transaction_type_id
            ):
                linked_payments = _get_split_payments(self.db_session, payment)
                for linked_payment in linked_payments:
                    if (
                        linked_payment.payment_transaction_type_id
                        == PaymentTransactionType.STATE_TAX_WITHHOLDING.payment_transaction_type_id
                    ):
                        end_state = State.STATE_WITHHOLDING_RELATED_PENDING_AUDIT
                    elif (
                        linked_payment.payment_transaction_type_id
                        == PaymentTransactionType.FEDERAL_TAX_WITHHOLDING.payment_transaction_type_id
                    ):
                        end_state = State.FEDERAL_WITHHOLDING_RELATED_PENDING_AUDIT
                    elif (
                        linked_payment.payment_transaction_type_id
                        == PaymentTransactionType.CHILD_SUPPORT_PAYMENT.payment_transaction_type_id
                    ):
                        end_state = State.CHILD_SUPPORT_RELATED_PENDING_AUDIT
                    elif (
                        linked_payment.payment_transaction_type_id
                        == PaymentTransactionType.EMPLOYER_REIMBURSEMENT.payment_transaction_type_id
                    ):
                        end_state = State.EMPLOYER_REIMBURSEMENT_RELATED_PENDING_AUDIT
                    outcome = state_log_util.build_outcome("Related Payment Audit report sent")

                    extra = payments_util.get_traceable_payment_details(linked_payment, end_state)
                    extra["primary_payment_id"] = payment.payment_id
                    extra["primary_payment_i_value"] = payment.fineos_pei_i_value
                    logger.info("Updating state log for linked payment", extra=extra)

                    state_log_util.create_finished_state_log(
                        associated_model=linked_payment,
                        end_state=end_state,
                        outcome=outcome,
                        db_session=self.db_session,
                    )

        logger.info("Done setting payments to audit sent state")

    def previously_audit_sent_count(self, payment: Payment) -> int:
        other_claim_payments = _get_other_claim_payments_for_payment(payment)
        previous_states = [State.DELEGATED_PAYMENT_PAYMENT_AUDIT_REPORT_SENT]
        return _get_state_log_count_in_state(other_claim_payments, previous_states, self.db_session)

    def audit_sent_count(self, payments: List[Payment]) -> int:
        states = [State.DELEGATED_PAYMENT_PAYMENT_AUDIT_REPORT_SENT]
        return _get_state_log_count_in_state(payments, states, self.db_session)

    def previously_errored_payment_count(self, payment: Payment) -> int:
        other_claim_payments = _get_other_claim_payments_for_payment(
            payment, same_payment_period=True
        )
        previous_states = [
            State.PAYMENT_FAILED_ADDRESS_VALIDATION,
            State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_ERROR_REPORT,
            State.DEPRECATED_DELEGATED_PAYMENT_ADD_TO_PAYMENT_ERROR_REPORT_RESTARTABLE,
            # We want to keep counting payments that errored in the old restartable state for now
            # consider moving all of these to DELEGATED_PAYMENT_ADD_TO_PAYMENT_ERROR_REPORT
        ]
        return _get_state_log_count_in_state(other_claim_payments, previous_states, self.db_session)

    def previously_rejected_payment_count(self, payment: Payment) -> int:
        other_claim_payments = _get_other_claim_payments_for_payment(
            payment, same_payment_period=True
        )
        previous_states = [
            State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_REJECT_REPORT,
            State.DEPRECATED_DELEGATED_PAYMENT_ADD_TO_PAYMENT_REJECT_REPORT_RESTARTABLE,
        ]
        return _get_state_log_count_in_state(
            other_claim_payments,
            previous_states,
            self.db_session,
            writeback_record_status=ACTIVE_WRITEBACK_RECORD_STATUS,
        )

    def previously_skipped_payment_count(self, payment: Payment) -> int:
        other_claim_payments = _get_other_claim_payments_for_payment(
            payment, same_payment_period=True
        )
        previous_states = [
            State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_REJECT_REPORT,
            State.DEPRECATED_DELEGATED_PAYMENT_ADD_TO_PAYMENT_REJECT_REPORT_RESTARTABLE,
        ]
        return _get_state_log_count_in_state(
            other_claim_payments,
            previous_states,
            self.db_session,
            writeback_record_status=PENDING_ACTIVE_WRITEBACK_RECORD_STATUS,
        )

    def lifetime_payment_count(self, employee: Employee) -> int:
        return self.lifetime_payment_count_mmars(employee) + self.lifetime_payment_count_pub(
            employee
        )

    def lifetime_payment_count_mmars(self, employee: Employee) -> int:
        return (
            self.db_session.query(MmarsPaymentData)
            .filter(MmarsPaymentData.employee_id == employee.employee_id)
            .count()
        )

    def lifetime_payment_count_pub(self, employee: Employee) -> int:
        paid_transaction_status_id = FineosWritebackTransactionStatus.PAID.transaction_status_id
        return (
            self.db_session.query(FineosWritebackDetails)
            .join(Payment, FineosWritebackDetails.payment_id == Payment.payment_id)
            .filter(FineosWritebackDetails.transaction_status_id == paid_transaction_status_id)
            .filter(Payment.employee_id == employee.employee_id)
            .count()
        )

    def previously_paid_payments(
        self, payment: Payment
    ) -> List[Tuple[Payment, Optional[FineosWritebackDetails]]]:
        related_payments = (
            self.db_session.query(Payment)
            .filter(
                Payment.period_start_date == payment.period_start_date,
                Payment.period_end_date == payment.period_end_date,
                Payment.claim_id == payment.claim_id,
                Payment.payment_id != payment.payment_id,
            )
            .options(joinedload(Payment.fineos_writeback_details))
            .all()
        )
        previously_paid_payments = []

        for payment in related_payments:
            writeback_detail = (
                payment.fineos_writeback_details[-1]
                if len(payment.fineos_writeback_details) > 0
                else None
            )
            # In the case writeback_detail is not populated, skip the payment.
            # Filter invalid writeback details in code since we need
            # to look for paid payments that may have errored afterwards.
            if writeback_detail and writeback_detail.transaction_status_id not in [
                FineosWritebackTransactionStatus.PAID.transaction_status_id,
                FineosWritebackTransactionStatus.POSTED.transaction_status_id,
            ]:
                continue

            previously_paid_payments.append((payment, writeback_detail))

        return previously_paid_payments

    def format_previously_paid_payments(
        self, payments: list[Tuple[Payment, Optional[FineosWritebackDetails]]]
    ) -> Optional[str]:
        if len(payments) == 0:
            return None

        output = ""
        for payment, writeback_detail in payments:
            c_val = payment.fineos_pei_c_value
            i_val = payment.fineos_pei_i_value
            amount = payment.amount

            writeback_time = writeback_detail.writeback_sent_at if writeback_detail else "N/A"
            writeback_status = (
                writeback_detail.transaction_status.transaction_status_description
                if writeback_detail
                else "N/A"
            )
            output = output + (
                f"Payment C={c_val}, I={i_val}: "
                f"amount={amount}, transaction_status={writeback_status}, "
                f"writeback_sent_at={writeback_time}\n"
            )

        if len(output) >= EXCEL_FILE_CELL_MAXIMUM_SIZE:
            output = (
                output[: EXCEL_FILE_CELL_MAXIMUM_SIZE - 100]
                + " ALERT: This column has been truncated for size"
            )
            logger.warning(
                "Previous payments in PaymentAuditReport has been truncated. Previous payment count: %i",
                len(payments),
            )

        return output

    def calculate_related_payment_amount(
        self,
        payment: Payment,
        link_payments: List[Payment],
        payment_transaction_type: LkPaymentTransactionType,
    ) -> decimal.Decimal:
        # If the payment itself is of
        # the given type, return its value
        if (
            payment.payment_transaction_type_id
            == payment_transaction_type.payment_transaction_type_id
        ):
            return payment.amount

        payment_amount: decimal.Decimal = decimal.Decimal(0)
        for link_payment in link_payments:
            if (
                link_payment.payment_transaction_type_id
                == payment_transaction_type.payment_transaction_type_id
            ):
                payment_amount += link_payment.amount

        return payment_amount

    def calculate_outstanding_payment_amount(
        self,
        payment: Payment,
    ) -> decimal.Decimal:
        overpayment_balance = decimal.Decimal(0)
        overpayment_amount = decimal.Decimal(0)
        overpayment_repayment_amount = decimal.Decimal(0)

        overpayment_records: List[Overpayment] = (
            self.db_session.query(Overpayment)
            .filter(Overpayment.employee_id == payment.employee_id)
            .all()
        )

        overpayment_repayment_records: List[OverpaymentRepayment] = (
            self.db_session.query(OverpaymentRepayment)
            .filter(OverpaymentRepayment.employee_id == payment.employee_id)
            .all()
        )

        if overpayment_records:
            for overpayment in overpayment_records:
                overpayment_amount += (
                    overpayment.amount if overpayment.amount else decimal.Decimal(0)
                )

        if overpayment_repayment_records:
            for overpayment_repayment in overpayment_repayment_records:
                overpayment_repayment_amount += (
                    overpayment_repayment.amount
                    if overpayment_repayment.amount
                    else decimal.Decimal(0)
                )
            overpayment_balance = overpayment_amount + overpayment_repayment_amount
        else:
            overpayment_balance = overpayment_amount
        return overpayment_balance

    def get_related_payment_i_values(
        self,
        payment: Optional[Payment],
        link_payments: List[Payment],
        payment_transaction_type: LkPaymentTransactionType,
    ) -> str:
        if (
            payment
            and payment.payment_transaction_type_id
            == payment_transaction_type.payment_transaction_type_id
        ):
            return str(payment.fineos_pei_i_value)

        i_values = []
        for link_payment in link_payments:
            if (
                link_payment.payment_transaction_type_id
                == payment_transaction_type.payment_transaction_type_id
            ):
                i_values.append(str(link_payment.fineos_pei_i_value))

        return " ".join(i_values)

    def get_additional_payment_audit_data(self, payment: Payment) -> PaymentAuditDetails:
        audit_report_details_map = {}
        audit_report_details_list = []
        rejected = False
        skipped = False
        program_integrity_notes = []

        extra = payments_util.get_traceable_payment_details(payment)

        for audit_processor in self.audit_processors:
            audit_details = audit_processor.process(payment)
            if not audit_details:
                continue

            audit_report_details_list.append(audit_details)
            if audit_details.audit_report_column:
                audit_report_details_map[audit_details.audit_report_column] = audit_details.message

            note_to_add = audit_details.audit_report_reject_notes
            if audit_details.audit_report_action == AuditReportAction.REJECTED:
                rejected = True
                note_to_add += " (Rejected)"
            elif audit_details.audit_report_action == AuditReportAction.SKIPPED:
                skipped = True
                note_to_add += " (Skipped)"
            # If the audit report action is informational, it won't affect
            # whether it defaults to rejected or skipped and the notes are as-is.
            program_integrity_notes.append(note_to_add)

            audit_processor_name = audit_processor.__class__.__name__
            extra["audit_processor_flagged"] = audit_processor_name
            logger.info("Payment flagged by audit processor %s", audit_processor_name, extra=extra)

        if len(program_integrity_notes) == 0:
            logger.info("Payment passed all audit processor validations", extra=extra)
            return PaymentAuditDetails()

        rejected_notes = ", ".join(program_integrity_notes)

        del extra["audit_processor_flagged"]  # Remove to avoid the metric being confusing
        extra["rejected"] = rejected
        extra["skipped"] = skipped
        extra["reject_notes"] = rejected_notes
        logger.info("Payment will have additional information from audit processors", extra=extra)

        self.increment(self.Metrics.PAYMENT_FLAGGED_BY_AUDIT_PROCESSOR)

        return PaymentAuditDetails(
            rejected_by_program_integrity=rejected,
            skipped_by_program_integrity=(not rejected and skipped),
            rejected_notes=rejected_notes,
            audit_report_details_list=audit_report_details_list,
            **audit_report_details_map,
        )

    def get_related_payment_status(self, payment: Payment) -> Optional[str]:
        latest_state = state_log_util.get_latest_state_log_in_flow(
            payment, Flow.DELEGATED_PAYMENT, self.db_session
        )
        if latest_state is None:
            logger.warning(
                "No latest state found for payment, cannot determine related parent payment status",
                extra={
                    "payment_id": payment.payment_id,
                },
            )
            return None
        elif latest_state.end_state_id == State.MAIN_PAYMENT_PREVIOUSLY_PAID.state_id:
            return RelatedPaymentStatus.MAIN_PAYMENT_PREVIOUSLY_PAID
        elif latest_state.end_state_id == State.MAIN_PAYMENT_PREVIOUSLY_REJECTED.state_id:
            return RelatedPaymentStatus.MAIN_PAYMENT_PREVIOUSLY_REJECTED
        return None

    def build_payment_audit_data_set(
        self, payments: Iterable[Payment]
    ) -> Iterable[PaymentAuditData]:
        logger.info("Start building payment audit data for fetched payments")

        payment_audit_data_set: List[PaymentAuditData] = []

        for payment in payments:
            self.increment(self.Metrics.PAYMENT_COUNT)

            # populate payment audit data by inspecting the currently fetched payment's history
            previously_audit_sent_count = self.previously_audit_sent_count(payment)
            is_first_time_payment = previously_audit_sent_count == 0

            previously_paid_payments = self.previously_paid_payments(payment)

            linked_payments = _get_split_payments(self.db_session, payment)
            federal_withholding_amount: decimal.Decimal = self.calculate_related_payment_amount(
                payment, linked_payments, PaymentTransactionType.FEDERAL_TAX_WITHHOLDING
            )
            state_withholding_amount: decimal.Decimal = self.calculate_related_payment_amount(
                payment, linked_payments, PaymentTransactionType.STATE_TAX_WITHHOLDING
            )

            employer_reimbursement_amount: decimal.Decimal = self.calculate_related_payment_amount(
                payment, linked_payments, PaymentTransactionType.EMPLOYER_REIMBURSEMENT
            )

            child_support_withholding_amount: decimal.Decimal = (
                self.calculate_related_payment_amount(
                    payment, linked_payments, PaymentTransactionType.CHILD_SUPPORT_PAYMENT
                )
            )

            outstanding_overpayments: decimal.Decimal = self.calculate_outstanding_payment_amount(
                payment
            )

            employer_reimbursement: Optional[Payment] = None
            if (
                payment.payment_transaction_type_id
                == PaymentTransactionType.EMPLOYER_REIMBURSEMENT.payment_transaction_type_id
            ):
                employer_reimbursement = payment
            else:
                for linked_payment in linked_payments:
                    if (
                        linked_payment.payment_transaction_type_id
                        == PaymentTransactionType.EMPLOYER_REIMBURSEMENT.payment_transaction_type_id
                    ):
                        employer_reimbursement = linked_payment
                        # Even if we have more than one Employer Reimbursement linked, take the first one and break
                        break

            # Clear the net payment amount for some transaction types (e.g. - Employer Reimbursements, and orphaned Tax Withholdings) in the audit report
            net_payment_amount = payment.amount
            if payment.payment_transaction_type_id in [
                PaymentTransactionType.STATE_TAX_WITHHOLDING.payment_transaction_type_id,
                PaymentTransactionType.FEDERAL_TAX_WITHHOLDING.payment_transaction_type_id,
                PaymentTransactionType.EMPLOYER_REIMBURSEMENT.payment_transaction_type_id,
                PaymentTransactionType.CHILD_SUPPORT_PAYMENT.payment_transaction_type_id,
            ]:
                net_payment_amount = decimal.Decimal(0)

            # Calculate Gross Payment Amount in audit report
            gross_payment_amount: decimal.Decimal = (
                net_payment_amount
                + federal_withholding_amount
                + state_withholding_amount
                + child_support_withholding_amount
                + employer_reimbursement_amount
            )

            payment_audit_data = PaymentAuditData(
                payment=payment,
                employer_reimbursement_payment=employer_reimbursement,
                is_first_time_payment=is_first_time_payment,
                previously_errored_payment_count=self.previously_errored_payment_count(payment),
                previously_rejected_payment_count=self.previously_rejected_payment_count(payment),
                previously_skipped_payment_count=self.previously_skipped_payment_count(payment),
                previously_paid_payment_count=len(previously_paid_payments),
                previously_paid_payments_string=self.format_previously_paid_payments(
                    previously_paid_payments
                ),
                lifetime_payment_count=(
                    self.lifetime_payment_count(payment.employee) if payment.employee else 0
                ),
                gross_payment_amount=str(gross_payment_amount if gross_payment_amount > 0 else ""),
                net_payment_amount=str(net_payment_amount if net_payment_amount > 0 else ""),
                federal_withholding_amount=str(
                    federal_withholding_amount if federal_withholding_amount > 0 else ""
                ),
                state_withholding_amount=str(
                    state_withholding_amount if state_withholding_amount > 0 else ""
                ),
                child_support_amount=str(
                    child_support_withholding_amount if child_support_withholding_amount > 0 else ""
                ),
                employer_reimbursement_amount=str(
                    employer_reimbursement_amount if employer_reimbursement_amount > 0 else ""
                ),
                federal_withholding_i_value=self.get_related_payment_i_values(
                    None, linked_payments, PaymentTransactionType.FEDERAL_TAX_WITHHOLDING
                ),
                state_withholding_i_value=self.get_related_payment_i_values(
                    None, linked_payments, PaymentTransactionType.STATE_TAX_WITHHOLDING
                ),
                employer_reimbursement_i_value=self.get_related_payment_i_values(
                    payment, linked_payments, PaymentTransactionType.EMPLOYER_REIMBURSEMENT
                ),
                child_support_i_value=self.get_related_payment_i_values(
                    None, linked_payments, PaymentTransactionType.CHILD_SUPPORT_PAYMENT
                ),
                outstanding_overpayments=str(
                    outstanding_overpayments
                    if outstanding_overpayments != decimal.Decimal(0)
                    else ""
                ),
                additional_audit_details=self.get_additional_payment_audit_data(payment),
                related_parent_payment_status=self.get_related_payment_status(payment),
            )
            payment_audit_data_set.append(payment_audit_data)

        logger.info(
            "Done building payment audit data for fetched payments: %i", len(payment_audit_data_set)
        )

        return payment_audit_data_set

    def generate_audit_report(self):
        """Top level function to generate and send payment audit report"""

        logger.info("Start generating payment audit report")

        # Fetch payments
        payments: List[Payment] = self.get_payments_for_audit_report()

        if payments_config.get_payment_feature_config().dia_dua_annotations_enabled:
            self.audit_processors.insert(0, DiaReductionsProcessor(self, payments))

        # generate payment audit data
        payment_audit_data_set: Iterable[PaymentAuditData] = self.build_payment_audit_data_set(
            payments
        )

        # write the report to the archive directory
        archive_folder_path = write_audit_report(
            payment_audit_data_set,
            self.s3_config.pfml_error_reports_archive_path,
            self.db_session,
            report_name=payments_util.Constants.FILE_NAME_PAYMENT_AUDIT_REPORT,
        )

        if archive_folder_path is None:
            raise Exception("Payment Audit Report file not written to outbound folder")

        logger.info(
            "Done writing Payment Audit Report file to archive folder: %s", archive_folder_path
        )
        self.set_metrics({self.Metrics.AUDIT_PATH: archive_folder_path})

        # Copy the report to the outgoing folder for program integrity
        outgoing_file_name = f"{payments_util.Constants.FILE_NAME_PAYMENT_AUDIT_REPORT}.csv"
        outbound_path = os.path.join(self.s3_config.dfml_report_outbound_path, outgoing_file_name)
        file_util.copy_file(str(archive_folder_path), str(outbound_path))

        logger.info("Done copying Payment Audit Report file to outbound folder: %s", outbound_path)

        payment_feature_config = payments_config.get_payment_feature_config()

        if not payment_feature_config.enable_payment_audit_report_emails:
            logger.info("Skipping sending Payment Audit File Available notification email")
        else:
            try:
                send_payment_audit_report_available_email()
                logger.info("Payment Audit Report available for download at %s", outbound_path)
            except Exception as error:
                logger.error(
                    "Error sending Payment Audit File Available notification email", exc_info=error
                )

        # create a reference file for the archived report
        reference_file = ReferenceFile(
            file_location=str(archive_folder_path),
            reference_file_type_id=ReferenceFileType.DELEGATED_PAYMENT_AUDIT_REPORT.reference_file_type_id,
        )
        self.db_session.add(reference_file)

        # set fetched payments as sent
        self.set_payments_to_sent_state(payments)

        logger.info("Done generating payment audit report")


def send_payment_audit_report_available_email():
    email_config = payment_report_audit_config.get_payment_audit_file_download_email_config()
    sender_email = email_config.pfml_email_address
    recipient_email = EmailRecipient(
        to_addresses=[email_config.payment_audit_report_available_notification_email_address],
        cc_addresses=[email_config.payment_audit_report_available_notification_cc_email_address],
    )
    template = "PaymentAuditFileAvailable"
    send_templated_email(
        recipient_email,
        template,
        sender_email,
        email_config.bounce_forwarding_email_address,
        email_config.bounce_forwarding_email_address_arn,
        None,
    )


def _get_state_log_count_in_state(
    payments: List[Payment],
    states: List[LkState],
    db_session: db.Session,
    writeback_record_status: Optional[str] = None,
) -> int:
    payment_ids = [p.payment_id for p in payments]
    state_ids = [s.state_id for s in states]

    state_logs = (
        db_session.query(StateLog)
        .filter(StateLog.end_state_id.in_(state_ids), StateLog.payment_id.in_(payment_ids))
        .all()
    )

    # In some cases we want to know if a payment
    # has a writeback status that is Active or PendingActive
    # to know if it was rejected or skipped. Iterate over the
    # payments we grabbed above and check the writebacks on them
    if writeback_record_status is not None:
        payments_with_writeback_record_status = 0
        for state_log in state_logs:
            writeback_details = (
                state_log.payment.fineos_writeback_details if state_log.payment else []
            )

            for writeback_detail in writeback_details:
                if (
                    writeback_detail.transaction_status.writeback_record_status
                    == writeback_record_status
                ):
                    payments_with_writeback_record_status += 1
                    # Break so we only count each payment once in case it has multiple writebacks
                    break

        return payments_with_writeback_record_status

    return len(state_logs)


def _get_other_claim_payments_for_payment(
    payment: Payment, same_payment_period: bool = False
) -> List[Payment]:
    if not payment.claim:
        raise ValueError("payment.claim is required")

    all_claim_payments = payment.claim.payments.all()
    other_claim_payments: List[Payment] = list(
        filter(lambda p: p.payment_id != payment.payment_id, all_claim_payments)
    )

    if same_payment_period:
        payment_date_tuple = _get_date_tuple(payment)
        other_claim_payments = list(
            filter(lambda p: _get_date_tuple(p) == payment_date_tuple, other_claim_payments)
        )

    return other_claim_payments


def _get_split_payments(db_session: db.Session, payment: Payment) -> List[Payment]:
    linked_split_payments: List[Payment] = (
        db_session.query(Payment)
        .join(LinkSplitPayment, Payment.payment_id == LinkSplitPayment.related_payment_id)
        .filter(LinkSplitPayment.payment_id == payment.payment_id)
        .all()
    )
    return linked_split_payments


def _get_date_tuple(payment: Payment) -> Tuple[date, date]:
    return (cast(date, payment.period_start_date), cast(date, payment.period_end_date))
