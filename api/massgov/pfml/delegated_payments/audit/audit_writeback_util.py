from dataclasses import dataclass, field
from enum import Enum
from typing import List, Optional, Set

from massgov.pfml.db.lookup_data.payments import FineosWritebackTransactionStatus
from massgov.pfml.db.models.payments import LkFineosWritebackTransactionStatus


class AuditReportAction(str, Enum):
    """
    Dictates whether the mapping
    causes a payment to set the
    skipped/rejected columns in the
    audit report.
    """

    REJECTED = "REJECTED"
    SKIPPED = "SKIPPED"
    INFORMATIONAL = "INFORMATIONAL"


class RejectNote(str, Enum):
    """
    A non-exhaustive list of reject notes.

    A reject note can be added systematically when we originally generate the file programmatically.
    The audit file then gets manually edited and re-uploaded. In this step, the manually edited audit file
    is being processed, thus these reject reasons can either be manually added
    and thus never be an exhaustive list.
    """

    ALREADY_PAID_FOR_DATES = "Already paid for dates"
    DUA_ADDITIONAL_INCOME = "DUA Additional Income"
    DIA_ADDITIONAL_INCOME = "DIA Additional Income"
    CANCEL_TIME_SUBMITTED = "Cancel time submitted"
    OTHER = "Other"


def is_matching_reject_notes(text: str, valid_reject_notes: set[str]) -> bool:
    """Checks if the given text matches with one of the valid reject notes"""
    return any(reject_note.lower() in text.lower() for reject_note in valid_reject_notes)


@dataclass
class WritebackMapping:
    """
    Container class for helping map
    arbitrary text to a writeback status.

    Default reject notes are what we expect
    in the "reject notes" column to map text
    to a writeback status. If we ourselves populate
    the "reject notes" when sending out an audit line,
    we will use these default reject notes to do so.

    Sometimes we want to configure multiple values
    as the folks creating the audit report might
    have documentation telling them to configure
    it another way.
    """

    default_reject_notes: str
    writeback_status: LkFineosWritebackTransactionStatus

    additional_accepted_reject_notes: List[str] = field(default_factory=list)

    # These two columns indicate the behavior & column
    # for scenarios where we put the records into the audit
    # report. For scenarios where we only expect to see it
    # returned in an audit report, they are not set.
    audit_report_action: Optional[AuditReportAction] = None
    audit_report_column: Optional[str] = None

    valid_reject_notes: Set[str] = field(init=False)

    def __post_init__(self):
        self.valid_reject_notes = set()
        self.valid_reject_notes.add(self.default_reject_notes)
        self.valid_reject_notes.add(self.writeback_status.transaction_status_description)
        self.valid_reject_notes.update(self.additional_accepted_reject_notes)

        # Verify that we only have REJECTED default + active
        # or SKIPPED default + pending active
        # Informational can be either
        if self.audit_report_action:
            if (
                self.audit_report_action == AuditReportAction.REJECTED
                and not self.writeback_status.is_active()
            ):
                raise Exception(
                    "Cannot construct a writeback mapping that defaults to rejected for PendingActive writeback"
                )
            if (
                self.audit_report_action == AuditReportAction.SKIPPED
                and self.writeback_status.is_active()
            ):
                raise Exception(
                    "Cannot construct a writeback mapping that defaults to skipped for Active writeback"
                )


DUA_ADDITIONAL_INCOME_MAPPING = WritebackMapping(
    default_reject_notes=RejectNote.DUA_ADDITIONAL_INCOME,
    writeback_status=FineosWritebackTransactionStatus.DUA_ADDITIONAL_INCOME,
    audit_report_action=AuditReportAction.INFORMATIONAL,
    audit_report_column="dua_additional_income_details",
)
DIA_ADDITIONAL_INCOME_MAPPING = WritebackMapping(
    default_reject_notes=FineosWritebackTransactionStatus.DIA_ADDITIONAL_INCOME.transaction_status_description,
    writeback_status=FineosWritebackTransactionStatus.DIA_ADDITIONAL_INCOME,
    audit_report_action=AuditReportAction.REJECTED,
    audit_report_column="dia_additional_income_details",
)
DOR_FINEOS_NAME_MISMATCH_MAPPING = WritebackMapping(
    default_reject_notes="DOR FINEOS Name Mismatch",
    writeback_status=FineosWritebackTransactionStatus.NAME_MISMATCH,
    additional_accepted_reject_notes=["Name mismatch"],
    audit_report_action=AuditReportAction.INFORMATIONAL,
    audit_report_column="dor_fineos_name_mismatch_details",
)
LEAVE_DATES_CHANGE_MAPPING = WritebackMapping(
    default_reject_notes="Payment Date Mismatch",
    writeback_status=FineosWritebackTransactionStatus.LEAVE_DATES_CHANGE,
    additional_accepted_reject_notes=["Leave Dates Change"],
    audit_report_action=AuditReportAction.REJECTED,
    audit_report_column="payment_date_mismatch_details",
)
SELF_REPORTED_ADDITIONAL_INCOME_MAPPING = WritebackMapping(
    default_reject_notes="Self-Reported Additional Income",
    writeback_status=FineosWritebackTransactionStatus.SELF_REPORTED_ADDITIONAL_INCOME,
)
WAITING_WEEK_MAPPING = WritebackMapping(
    default_reject_notes="Waiting Week",
    writeback_status=FineosWritebackTransactionStatus.WAITING_WEEK,
    audit_report_action=AuditReportAction.INFORMATIONAL,
)
ALREADY_PAID_FOR_DATES_MAPPING = WritebackMapping(
    default_reject_notes=RejectNote.ALREADY_PAID_FOR_DATES,
    writeback_status=FineosWritebackTransactionStatus.ALREADY_PAID_FOR_DATES,
    audit_report_action=AuditReportAction.INFORMATIONAL,
)
EXEMPT_EMPLOYER_MAPPING = WritebackMapping(
    default_reject_notes=FineosWritebackTransactionStatus.EXEMPT_EMPLOYER.transaction_status_description,
    writeback_status=FineosWritebackTransactionStatus.EXEMPT_EMPLOYER,
)
MAX_WEEKLY_EXCEEDED_MAPPING = WritebackMapping(
    default_reject_notes=FineosWritebackTransactionStatus.MAX_WEEKLY_BENEFITS_EXCEEDED.transaction_status_description,
    writeback_status=FineosWritebackTransactionStatus.MAX_WEEKLY_BENEFITS_EXCEEDED,
    additional_accepted_reject_notes=["Weekly benefit amount exceeded"],
    audit_report_action=AuditReportAction.INFORMATIONAL,
)
CANCEL_TIME_SUBMITTED_MAPPING = WritebackMapping(
    default_reject_notes=RejectNote.CANCEL_TIME_SUBMITTED,
    writeback_status=FineosWritebackTransactionStatus.CANCEL_TIME_SUBMITTED,
    audit_report_action=AuditReportAction.REJECTED,
)

WRITEBACK_MAPPINGS = [
    DUA_ADDITIONAL_INCOME_MAPPING,
    DIA_ADDITIONAL_INCOME_MAPPING,
    DOR_FINEOS_NAME_MISMATCH_MAPPING,
    LEAVE_DATES_CHANGE_MAPPING,
    SELF_REPORTED_ADDITIONAL_INCOME_MAPPING,
    WAITING_WEEK_MAPPING,
    ALREADY_PAID_FOR_DATES_MAPPING,
    EXEMPT_EMPLOYER_MAPPING,
    MAX_WEEKLY_EXCEEDED_MAPPING,
    CANCEL_TIME_SUBMITTED_MAPPING,
]


def get_writeback_for_text(
    text: str, is_active_writeback_scenario: bool
) -> Optional[LkFineosWritebackTransactionStatus]:
    # none of the text we look for is
    # particularly short, so shortcut
    # this logic and return None
    if len(text) < 5:
        return None

    for writeback_mapping in WRITEBACK_MAPPINGS:
        # If is_active_writeback_scenario, we want to only potentially grab
        # writebacks where the writeback is "Active", and vice-versa for "PendingActive"
        if is_active_writeback_scenario != writeback_mapping.writeback_status.is_active():
            continue
        if is_matching_reject_notes(text, writeback_mapping.valid_reject_notes):
            return writeback_mapping.writeback_status

    return None
