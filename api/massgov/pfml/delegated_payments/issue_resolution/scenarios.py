from dataclasses import dataclass, field
from typing import Dict, List, Optional, Set

import massgov.pfml.util.logging as logging
from massgov.pfml.db.lookup_data.payments import PaymentIssueResolutionScenarioConfig
from massgov.pfml.db.models.payments import LkPaymentIssueResolutionScenarioConfig
from massgov.pfml.delegated_payments.audit.audit_writeback_util import (
    RejectNote,
    is_matching_reject_notes,
)
from massgov.pfml.delegated_payments.delegated_payments_util import (
    ValidationContainer,
    ValidationIssue,
)
from massgov.pfml.delegated_payments.extracts.payment_extract_writeback_util import (
    EXEMPT_EMPLOYER_WRITEBACK_GROUP,
    FAILED_AUTOMATED_VALIDATION_WRITEBACK_GROUP,
    LEAVE_IN_REVIEW_WRITEBACK_GROUP,
    PRENOTE_ERROR_WRITEBACK_GROUP,
    ValidationReason,
)

logger = logging.get_logger(__name__)


@dataclass
class PaymentIssueResolutionScenario:
    config: LkPaymentIssueResolutionScenarioConfig

    def get_description(self, prepended_text: Optional[str] = None) -> str:
        return f"{prepended_text if prepended_text else ''}\n{self.config.base_description}".strip()


@dataclass
class PaymentExtractResolutionScenario(PaymentIssueResolutionScenario):
    # TODO: https://lwd.atlassian.net/browse/PFMLPB-6651
    validation_reasons: Set[ValidationReason] = field(default_factory=set)

    def get_description_for_issues(self, validation_issues: List[ValidationIssue]) -> str:
        """Build a description string containing any present validation_issues and associated field names."""
        reasons_to_field_names: Dict[str, List[Optional[str]]] = {}

        for issue in validation_issues:
            if issue.reason in self.validation_reasons:
                field_name = issue.field_name if issue.field_name else None
                if issue.reason in reasons_to_field_names:
                    reasons_to_field_names[issue.reason].append(field_name)
                else:
                    reasons_to_field_names[issue.reason] = [field_name]

        descriptions = []
        for reason in sorted(reasons_to_field_names):
            field_names = reasons_to_field_names[reason]
            sorted_field_names = sorted([name for name in field_names if name is not None])

            if len(sorted_field_names) > 0:
                # E.g., "MissingField: field_name_1, field_name_2"
                descriptions.append(reason + ": " + ", ".join(sorted_field_names))
            else:
                descriptions.append(reason)

        return "\n".join(descriptions)


@dataclass
class PaymentRejectStepResolutionScenario(PaymentIssueResolutionScenario):
    """
    Resolution Scenarios that can occur within the PaymentRejectStep.
    These scenarios are meant to be used with manually added reject notes.

    WARNING: using with automated reject reasons may cause duplicated issue resolutions
    if another issue resolution scenario was already applied upstream. For automated,
    reject reasons please use PaymentExtractResolutionScenario or PaymentIssueResolutionScenario.

    WARNING: due to the nature of manually added reject notes in the audit CSV,
    the PaymentRejectStepResolutionScenario can fail to apply due to human error, such as typos.
    Prefer usage of PaymentExtractResolutionScenario or PaymentIssueResolutionScenario when possible.
    """

    reject_notes: set[str]


# Scenarios that result in any one of the validation reasons being found

FAILED_AUTOMATED_VALIDATION_RESOLUTION = PaymentExtractResolutionScenario(
    config=PaymentIssueResolutionScenarioConfig.FAILED_AUTOMATED_VALIDATION_RESOLUTION_CONFIG,
    validation_reasons=FAILED_AUTOMATED_VALIDATION_WRITEBACK_GROUP.reasons,
)
LEAVE_IN_REVIEW_RESOLUTION = PaymentExtractResolutionScenario(
    config=PaymentIssueResolutionScenarioConfig.LEAVE_IN_REVIEW_RESOLUTION_CONFIG,
    validation_reasons=LEAVE_IN_REVIEW_WRITEBACK_GROUP.reasons,
)
PRENOTE_ERROR_RESOLUTION = PaymentExtractResolutionScenario(
    config=PaymentIssueResolutionScenarioConfig.PRENOTE_ERROR_RESOLUTION_CONFIG,
    validation_reasons=PRENOTE_ERROR_WRITEBACK_GROUP.reasons,
)
EXEMPT_EMPLOYER_PAYMENT_EXTRACT_RESOLUTION = PaymentExtractResolutionScenario(
    config=PaymentIssueResolutionScenarioConfig.EXEMPT_EMPLOYER_PAYMENT_EXTRACT_RESOLUTION_CONFIG,
    validation_reasons=EXEMPT_EMPLOYER_WRITEBACK_GROUP.reasons,
)

# Scenarios that are the result of a localized validation step

DOR_FINEOS_NAME_MISMATCH_RESOLUTION = PaymentExtractResolutionScenario(
    config=PaymentIssueResolutionScenarioConfig.DOR_FINEOS_NAME_MISMATCH_RESOLUTION_CONFIG
)
DEPRECATED_LEAVE_DURATION_MAX_EXCEEDED_RESOLUTION = PaymentExtractResolutionScenario(
    config=PaymentIssueResolutionScenarioConfig.DEPRECATED_LEAVE_DURATION_MAX_EXCEEDED_RESOLUTION_CONFIG,
)
LEAVE_DATES_CHANGE_RESOLUTION = PaymentExtractResolutionScenario(
    config=PaymentIssueResolutionScenarioConfig.LEAVE_DATES_CHANGE_RESOLUTION_CONFIG
)
ADDRESS_VALIDATION_RESOLUTION = PaymentExtractResolutionScenario(
    config=PaymentIssueResolutionScenarioConfig.ADDRESS_VALIDATION_RESOLUTION_CONFIG
)
INVALID_WAITING_WEEK_RESOLUTION = PaymentExtractResolutionScenario(
    config=PaymentIssueResolutionScenarioConfig.INVALID_WAITING_WEEK_RESOLUTION_CONFIG
)

# Scenarios that are a result of processing PUB Responses

BANK_PROCESSING_ERROR_RESOLUTION = PaymentIssueResolutionScenario(
    config=PaymentIssueResolutionScenarioConfig.BANK_PROCESSING_ERROR_RESOLUTION_CONFIG
)

# Additional scenarios

UNDER_OR_OVERPAY_ADJUSTMENT = PaymentIssueResolutionScenario(
    config=PaymentIssueResolutionScenarioConfig.UNDER_OR_OVERPAY_ADJUSTMENT_CONFIG
)

# Scenarios that occur from processing PaymentRejectStep
PAYMENT_AUDIT_ERROR_RESOLUTION = PaymentRejectStepResolutionScenario(
    config=PaymentIssueResolutionScenarioConfig.PAYMENT_AUDIT_ERROR_RESOLUTION_CONFIG,
    reject_notes=set([RejectNote.OTHER]),
)
DIA_ADDITIONAL_INCOME_RESOLUTION = PaymentRejectStepResolutionScenario(
    config=PaymentIssueResolutionScenarioConfig.DIA_ADDITIONAL_INCOME_RESOLUTION_CONFIG,
    reject_notes=set([RejectNote.DIA_ADDITIONAL_INCOME]),
)
DUA_ADDITIONAL_INCOME_RESOLUTION = PaymentRejectStepResolutionScenario(
    config=PaymentIssueResolutionScenarioConfig.DUA_ADDITIONAL_INCOME_RESOLUTION_CONFIG,
    reject_notes=set([RejectNote.DUA_ADDITIONAL_INCOME]),
)
INVALID_PAYMENT_DATE_PAID_RESOLUTION = PaymentRejectStepResolutionScenario(
    config=PaymentIssueResolutionScenarioConfig.INVALID_PAYMENT_PAID_DATE_RESOLUTION_CONFIG,
    reject_notes=set([RejectNote.ALREADY_PAID_FOR_DATES]),
)
CANCEL_TIME_SUBMITTED_RESOLUTION = PaymentRejectStepResolutionScenario(
    config=PaymentIssueResolutionScenarioConfig.CANCEL_TIME_SUBMITTED_RESOLUTION_CONFIG,
    reject_notes=set([RejectNote.CANCEL_TIME_SUBMITTED]),
)

# Set issue resolution scenarios that map to validation issues
# that can be encountered during the payment extract step
PAYMENT_ISSUE_RESOLUTION_REJECT_NOTE_SCENARIOS = [
    DIA_ADDITIONAL_INCOME_RESOLUTION,
    DUA_ADDITIONAL_INCOME_RESOLUTION,
    INVALID_PAYMENT_DATE_PAID_RESOLUTION,
    CANCEL_TIME_SUBMITTED_RESOLUTION,
    PAYMENT_AUDIT_ERROR_RESOLUTION,
]


def get_issue_resolutions_for_reject_notes(
    text: str | None, is_rejected: bool
) -> list[PaymentIssueResolutionScenario]:
    scenarios: list[PaymentIssueResolutionScenario] = []
    for reject_notes_scenario in PAYMENT_ISSUE_RESOLUTION_REJECT_NOTE_SCENARIOS:
        if text and is_matching_reject_notes(text, reject_notes_scenario.reject_notes):
            scenarios.append(reject_notes_scenario)
    return scenarios


# Set issue resolution scenarios that map to validation issues
# that can be encountered during the payment extract step
PAYMENT_ISSUE_RESOLUTION_EXTRACT_VALIDATION_SCENARIOS = [
    FAILED_AUTOMATED_VALIDATION_RESOLUTION,
    LEAVE_IN_REVIEW_RESOLUTION,
    PRENOTE_ERROR_RESOLUTION,
    EXEMPT_EMPLOYER_PAYMENT_EXTRACT_RESOLUTION,
]
VALIDATION_REASON_TO_PAYMENT_ISSUE_RESOLUTION_SCENARIO: Dict[
    ValidationReason, PaymentExtractResolutionScenario
] = {}
for scenario in PAYMENT_ISSUE_RESOLUTION_EXTRACT_VALIDATION_SCENARIOS:
    for validation_reason in scenario.validation_reasons:
        VALIDATION_REASON_TO_PAYMENT_ISSUE_RESOLUTION_SCENARIO[validation_reason] = scenario


def get_issue_resolutions_for_validation_reasons(
    validation_container: ValidationContainer,
) -> List[PaymentExtractResolutionScenario]:
    applicable_scenarios: List[PaymentExtractResolutionScenario] = []
    validation_reasons: List[ValidationReason] = validation_container.get_reasons()
    for reason in validation_reasons:
        scenario: PaymentExtractResolutionScenario | None = (
            VALIDATION_REASON_TO_PAYMENT_ISSUE_RESOLUTION_SCENARIO.get(reason, None)
        )
        if scenario is None:
            logger.warning("No scenario has been associated with validation reason: %s", reason)
        elif scenario not in applicable_scenarios:
            applicable_scenarios.append(scenario)

    return applicable_scenarios
