from typing import Optional

from pydantic import Field

import massgov.pfml.util.logging
from massgov.pfml.util.pydantic import PydanticBaseSettings

logger = massgov.pfml.util.logging.get_logger(__name__)

NOT_SET = "ENV_VAR_NOT_SET"


class PaymentFeatureConfig(PydanticBaseSettings):
    pub_payment_starting_check_number: Optional[int] = Field(
        None,
        description="This flag configures the number to start issuing checks from",
    )
    pub_undeliverable_checks_file_processing_is_enabled: bool = Field(
        False,
        description="This flag configures whether or not the pub response task should process the PUB Undeliverable checks file",
    )
    enable_fineos_overpayment_processing: bool = Field(
        False,
        description="This flag configures whether or not the steps to update the overpayment from the FINEOS extact will execute",
    )
    enable_overpayment_adjustment_processing: bool = Field(
        False,
        description="This flag configures whether or not the steps to refer the overpayment adjustment to MMARS will execute",
    )
    enable_1099_print_shop_copying: bool = Field(
        False,
        description="This flag configures whether or not the step to copy 1099 documents to the S3 print shop will execute.",
    )
    delete_files_from_print_shop_path: bool = Field(
        True,
        description="This flag determines if the print shop S3 path will be cleared before files are copied.",
    )
    pfml_1099_batch_id_of_documents_to_copy: Optional[str] = Field(
        None,
        description="This flag is used to copy 1099 documents to the print shop S3 path. If not provided then the latest batch ID will be used.",
    )
    fineos_entitlement_period_process_all_employees: bool = Field(
        False,
        description="When this flag is enabled, the SyncEntitlementPeriods step will process all employees rather than just the ones based on the diff between the current and previous extract",
    )
    dia_dua_annotations_enabled: bool = Field(
        False,
        description="When this flag is enabled, the DIA DUA annotations will be added to the payment report",
    )
    skip_reading_from_fineos_s3_buckets: bool = Field(
        False,
        description="When this flag is enabled, the FineosExtractStep step will not read extracts from the FINEOS S3 buckets",
    )

    pfml_1099_generate_document_sequence_start: Optional[int] = Field(
        None,
        description="Starting sequence number when generating 1099 documents.",
    )

    pfml_1099_generate_document_sequence_end: Optional[int] = Field(
        None,
        description="Ending sequence number when generating 1099 documents.",
    )

    enable_sync_employees_step: bool = Field(False, description="Enable SyncEmployeesStep")

    enable_sync_absence_paid_leave_cases_step: bool = Field(
        False, description="Enable SyncAbsencePaidLeaveCasesStep"
    )

    enable_payment_audit_report_emails: bool = Field(
        False,
        description="Enable Payment Audit Report Emails",
    )

    enable_sync_claims_step: bool = Field(
        False,
        description="Enable Sync Claims steps",
    )

    enable_sync_efts_step: bool = Field(
        False,
        description="Enable Sync EFTs steps",
    )

    enable_sync_leave_requests_step: bool = Field(
        False,
        description="Enable SyncLeaveRequestsStep",
    )

    enable_sync_absence_periods_step: bool = Field(
        False,
        description="Enable Sync Absence Periods steps",
    )

    enable_sync_claimant_address_step: bool = Field(
        False,
        description="This flag configures whether or not the steps to sync the claimant address table are run",
    )
    enable_payment_reject_issue_resolutions_milestone1: bool = Field(
        False,
        description="""
            This flag configures if PaymentRejectIssueResolution is enabled for DUA Additional Income,
            DIA Additional Income, Cancel Time Submitted, Invalid Payment Date and Payment Audit Error
        """,
    )


def get_payment_feature_config():
    return PaymentFeatureConfig()


class PaymentsS3Config(PydanticBaseSettings):
    """Config for Delegated Payments S3 Buckets

    This config is a wrapper around S3 paths (eg. s3://bucket/path/to/folder/).

    Vars prefixed with "fineos" are buckets owned by FINEOS. These env vars
    point to the S3 "folder" that contains the timestamped files & folders.

    Vars prefixed by "pfml" are owned by PFML. Payments uses the PFML agency
    transfer S3 bucket. The payments-related folders within that bucket follow
    this convention:

    Vars prefixed by "pub" are owned by us, but other processes move the files within
    to/from PUB via MoveIt

    Vars prefixed by "dfml" are owned by us, but other processes move the files within
    to/from DFML via sharepoint. These files are shared with either Program Integrity or
    Finance

    /<vendor_or_agency>
      /archive
        /<dataset>
          /received     - Input directory for processing
          /processed    - Successfully processed input file
          /error        - An error occurred with processing
          /skipped      - We received the file, but decided to skip it (extracts skip previous days)
          /sent         - We have sent the file
             <date>
                <filename>
      /inbound          - Files we are receiving, we will move these to a received directory for processing
        <files>
      /outbound         - Files we are sending out, we expect these to be moved/deleted
        <files>

    These env vars are of the format:
    s3://<agency transfer>/<vendor>/archive/<dataset>/
    They do not contain the final state folder (e.g. "received", "processed")

    https://lwd.atlassian.net/wiki/spaces/API/pages/1504280599/PUB+Files+Environment+Configuration
    """

    ## ---- FINEOS-related S3 Bucket envs
    # FINEOS generates data export files for PFML API to pick up
    # This is where FINEOS makes those files available to us
    # Ex: s3://fin-somprod-data-export/PRD/dataexports/
    fineos_data_export_path: str = Field(
        NOT_SET, description="This is where FINEOS makes extract files available to us"
    )

    # FINEOS allows us to generate adhoc query data export files for PFML API to pick up
    # This is where FINEOS makes those adhoc extract files available to us
    # Ex: s3://fin-somprod-data-export/PRD/dataExtracts/AdHocExtract/
    fineos_adhoc_data_export_path: str = Field(
        NOT_SET, description="This is where FINEOS makes adhoc extract files available to us"
    )
    # PFML API generates files for FINEOS to process
    # This is where FINEOS picks up files from us
    # Ex: s3://fin-somprod-data-import/PRD/peiupdate/
    fineos_data_import_path: str = Field(
        NOT_SET, description="This is where FINEOS picks up PEI writeback files from us"
    )
    # PFML API stores a copy of all files that FINEOS generates for us
    # Ex: s3://massgov-pfml-prod-agency-transfer/cps/archive/extracts/
    pfml_fineos_extract_archive_path: str = Field(
        NOT_SET, description="This is where we store the copy of the FINEOS extracts"
    )
    # Path to extracts for the business intelligence tool.
    # The BI tool path contains an archive of FINEOS extracts.
    # Ex: s3://massgov-pfml-test-business-intelligence-tool/fineos/dataexports/
    pfml_bi_tool_extract_path: str = Field(
        NOT_SET, description="This is where we store the BI tool extracts."
    )
    # PFML API stores a copy of all files that we generate for FINEOS
    # Ex: s3://massgov-pfml-prod-agency-transfer/cps/archive/pei-writeback/
    pfml_fineos_writeback_archive_path: str = Field(
        NOT_SET, description="This is where we store the copy of the FINEOS PEI writeback"
    )

    # All outgoing files destined for Sharepoint go in this directory.
    # Ex: s3://massgov-pfml-{env}-reports/dfml-reports/
    dfml_report_outbound_path: str = Field(
        NOT_SET, description="This is where we put files we want picked up and moved to Sharepoint"
    )
    # All incoming files copied from Sharepoint go in this directory.
    # Ex: s3://massgov-pfml-{env}-reports/dfml-responses/
    dfml_response_inbound_path: str = Field(
        NOT_SET, description="This is where files sent to us from Sharepoint are put"
    )

    # All files returned from PUB via MoveIt end up in this directory for our processing.
    # We will copy files from this directory to the below specific inbound paths.
    # Ex: massgov-pfml-{env}-agency-transfer/pub/inbound/
    pub_moveit_inbound_path: str = Field(
        NOT_SET, description="This is where we put files we want picked up and moved to MoveIt"
    )
    # All files destined for PUB that will get there via MoveIt end up in this directory for our processing.
    # Ex: massgov-pfml-{env}-agency-transfer/pub/outbound/
    pub_moveit_outbound_path: str = Field(
        NOT_SET, description="This is where files sent to us from MoveIt are put"
    )

    # ACH outgoing files are archived to this directory when we send them to PUB.
    # ACH return files we receive from PUB will be processed in this directory.
    # Ex: s3://massgov-pfml-prod-agency-transfer/pub/archive/ach/
    pfml_pub_ach_archive_path: str = Field(
        NOT_SET, description="This is where we store the copy of the ACH files"
    )
    # Check outgoing files are archive to this directory when we send them to PUB.
    # Check payment return files we receive from PUB will be processed in this directory.
    # Ex: s3://massgov-pfml-prod-agency-transfer/pub/archive/check/
    pfml_pub_check_archive_path: str = Field(
        NOT_SET, description="This is where we store the copy of the Check files"
    )

    # Where we store the manual PUB reject used to manually fail payments from PUB
    # that are otherwise not returned by the bank due to the particular type of error.
    # s3://massgov-pfml-prod-agency-transfer/pub/archive/manual-reject/
    pfml_manual_pub_reject_archive_path: str = Field(
        NOT_SET, description="This is where we store the copy of the manual PUB reject files"
    )

    # PFML API stores a copy of all files that we generate for reporting
    # Ex: s3://massgov-pfml-prod-agency-transfer/error-reports/archive/
    pfml_error_reports_archive_path: str = Field(
        ..., description="This is where we store a copy of all report files"
    )

    # PFML API stores a copy of the payment reject file we received
    # Ex s3://massgov-pfml-${environment_name}-agency-transfer/audit/archive
    pfml_payment_rejects_archive_path: str = Field(
        NOT_SET, description="This is where we store a copy of the payment reject response file"
    )

    # TODO: Add some description
    pfml_1099_document_archive_path: str = Field(
        NOT_SET, description="This is where we store all 1099 pdf documents"
    )

    pfml_1099_document_print_shop_path: str = Field(
        NOT_SET,
        description="This is where the merged 1099 PDF documents are copied to for print shop access.",
    )

    # Where we store the undeliverable checks files from PUB.
    # All text and PDF artifacts from PUB for undeliverable checks files will be
    # processed in this directory.
    # Ex s3://massgov-pfml-${environment_name}-agency-transfer/pub/undeliverable-checks
    pfml_pub_undeliverable_checks_archive_path: str = Field(
        NOT_SET, description="This is where we store copies of the pub undeliverable checks files"
    )


def get_s3_config() -> PaymentsS3Config:
    return PaymentsS3Config()


class PaymentsDateConfig(PydanticBaseSettings):
    """Config for Payments dates

    PFML API processes the following timestamped files from FINEOS:
    - claimant extracts
    - payment extracts

    Due to some challenges around launch, we need to be able to configure
    how far back in time we look when checking each folder.

    This config is a wrapper around date env vars.

    https://lwd.atlassian.net/wiki/spaces/API/pages/1504280599/PUB+Files+Environment+Configuration
    """

    # PFML API will not process FINEOS claimant data older than this date
    fineos_claimant_extract_max_history_date: str = Field(
        NOT_SET, description="The earliest file we will copy from FINEOS for the claimant extract"
    )
    # PFML API will not process FINEOS payment data older than this date
    fineos_payment_extract_max_history_date: str = Field(
        NOT_SET, description="The earliest file we will copy from FINEOS for the payment extract"
    )
    # PFML API will not process FINEOS payment data older than this date
    fineos_payment_reconciliation_extract_max_history_date: str = Field(
        NOT_SET,
        description="The earliest file we will copy from FINEOS for the payment reconciliation extract",
    )
    # PFML API will not process FINEOS IAWW data older than this date
    fineos_iaww_extract_max_history_date: str = Field(
        NOT_SET, description="The earliest file we will copy from FINEOS for the IAWW extract"
    )

    fineos_1099_data_extract_max_history_date: str = Field(
        NOT_SET, description="The earliest file we will copy from FINEOS for the 1099 extract"
    )

    fineos_vbi_taskreport_som_extract_max_history_date: str = Field(
        NOT_SET,
        description="The earliest file we will copy from FINEOS for the VBI Taskreport Som extract",
    )

    fineos_vbi_taskreport_delta_som_extract_max_history_date: str = Field(
        NOT_SET,
        description="The earliest file we will copy from FINEOS for the VBI Taskreport Delta Som extract",
    )

    fineos_vbi_document_som_extract_max_history_date: str = Field(
        NOT_SET,
        description="The earliest file we will copy from FINEOS for the VBI Document Som extract",
    )

    fineos_vbi_document_delta_som_extract_max_history_date: str = Field(
        NOT_SET,
        description="The earliest file we will copy from FINEOS for the VBI Document Delta Som extract",
    )

    fineos_vbi_entitlemtperiod_som_extract_max_history_date: str = Field(
        NOT_SET,
        description="The earliest file we will copy from FINEOS for the entitlement period extract",
    )

    fineos_overpayment_extract_max_history_date: str = Field(
        NOT_SET,
        description="The earliest file we will copy from FINEOS for the overpayment extract",
    )

    overpayments_backfill_extract_max_history_date: str = Field(
        NOT_SET,
        description="The earliest we will backfill for the overpayment extract.",
    )

    overpayments_backfill_extract_min_history_date: str = Field(
        NOT_SET,
        description="The latest we will backfill for the overpayment extract.",
    )

    fineos_service_agreement_extract_max_history_date: str = Field(
        NOT_SET,
        description="The earliest we will backfill for the fineos extract service agreement.",
    )


def get_date_config() -> PaymentsDateConfig:
    payments_date_config = PaymentsDateConfig()

    logger.info(
        "Constructed payments date config",
        extra={
            "fineos_claimant_extract_max_history_date": payments_date_config.fineos_claimant_extract_max_history_date,
            "fineos_payment_extract_max_history_date": payments_date_config.fineos_payment_extract_max_history_date,
        },
    )
    return payments_date_config


class MmarsS3Config(PydanticBaseSettings):
    """Config for MMARS outbound and Inbound Files

    Outbound Files: Files we are sending to MMARS
        s3://<agency transfer>/ctr/overpayments/outbound/

    Inbound Files: Files we are receiving from MMARS
        s3://<agency transfer>/ctr/overpayments/inbound/

    Archived Outbound Files: Files we have sent to MMARS
        s3://<agency transfer>/ctr/overpayments/VCC
        s3://<agency transfer>/ctr/overpayments/RE
        s3://<agency transfer>/ctr/overpayments/REM

    Archived Inbound Files: Files we have received from MMARS
        s3://<agency transfer>/ctr/overpayments/VCC_STATUS
        s3://<agency transfer>/ctr/overpayments/RE_STATUS
        s3://<agency transfer>/ctr/overpayments/REM_STATUS

    This config has value of the base location. The actual file paths are constructed in the code by appending the file name to the base location.
    """

    # location of the overpayment case files to be sent to MMARS
    pfml_mmars_file_base_location: str = Field(
        NOT_SET, description="Base Location of Inbound and outbound MMARS Files"
    )


def get_mmars_s3_config() -> MmarsS3Config:
    return MmarsS3Config()


class MmarsFeatureConfig(PydanticBaseSettings):

    # location of the overpayment case files to be sent to MMARS
    enable_email_overpayment_referrals_inf_file: str = Field(
        NOT_SET, description="Enable sending of INF on email"
    )


def get_mmars_feature_config() -> MmarsFeatureConfig:
    return MmarsFeatureConfig()
