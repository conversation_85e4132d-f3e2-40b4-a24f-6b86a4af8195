from typing import Optional

import massgov.pfml.db as db
import massgov.pfml.delegated_payments.util.payment_preference_util as payment_preference_util
import massgov.pfml.delegated_payments.util.prepaid_debit_address_util as prepaid_debit_address_util
import massgov.pfml.util.logging as logging
from massgov.pfml.db.lookup_data.employees import BankAccountType, PaymentMethod
from massgov.pfml.db.lookup_data.payments import PrepaidRegistrationStatus
from massgov.pfml.db.models.employees import Employee
from massgov.pfml.db.models.payments import ClaimantPrepaidRegistration, PaymentPreference
from massgov.pfml.delegated_payments.delegated_payments_util import handle_account_detail
from massgov.pfml.fineos import create_client
from massgov.pfml.fineos.models.customer_api import PaymentPreferenceResource
from massgov.pfml.util.batch.step import Step
from massgov.pfml.util.strings import none_if_empty

logger = logging.get_logger(__name__)


class SyncPrepaidDebitPaymentPreferenceWithFineosStep(Step):
    def __init__(self, db_session: db.Session, log_entry_db_session: db.Session):
        super().__init__(db_session, log_entry_db_session)

        self.fineos_client = create_client()

    def run_step(self) -> None:
        logger.info("Start -  Sync Payment Preference With Fineos step")
        self.sync_payment_preference_with_fineos()
        self.db_session.commit()
        logger.info("End -  Sync Payment Preference With Fineos step")

    def upsert_payment_preference(
        self, payment_preference: PaymentPreferenceResource, employee: Employee
    ) -> Optional[PaymentPreference]:
        """
        Check if employee already has a payment preference set.
        If yes and payment preference is the same, do nothing.
        If yes but payment preference is the different, update the existing payment preference.
        If not, create a new payment preference.
        """
        if not payment_preference.paymentMethod:
            logger.error(
                f"Payment preference for employee {employee.employee_id} does not have a payment method"
            )
            return None

        payment_method = payment_preference.paymentMethod.name

        payment_method_id = PaymentMethod.get_id(payment_method)

        if payment_method_id:
            existing_preference = (
                self.db_session.query(PaymentPreference)
                .filter_by(
                    employee_id=employee.employee_id,
                )
                .first()
            )

            if existing_preference:
                existing_preference.default = True
                existing_preference.payment_method_id = payment_method_id

                if (
                    payment_preference.accountDetail
                    and payment_preference.accountDetail.accountType
                ):
                    existing_preference.routing_number = handle_account_detail(
                        payment_preference.accountDetail.routingNumber
                    )
                    existing_preference.account_number = handle_account_detail(
                        payment_preference.accountDetail.accountNo
                    )
                    existing_preference.account_name = none_if_empty(
                        payment_preference.accountDetail.accountName
                    )
                    existing_preference.bank_account_type_id = BankAccountType.get_id(
                        payment_preference.accountDetail.accountType.name
                    )

                logger.info(
                    f"Updated payment preference for employee {employee.employee_id} to {payment_method}"
                )
                return existing_preference
            else:
                if (
                    payment_preference.accountDetail
                    and payment_preference.accountDetail.accountType
                ):
                    fineos_payment_preference = PaymentPreference(
                        payment_method_id=payment_method_id,
                        employee_id=employee.employee_id,
                        routing_number=handle_account_detail(
                            str(payment_preference.accountDetail.routingNumber)
                        ),
                        account_number=handle_account_detail(
                            str(payment_preference.accountDetail.accountNo)
                        ),
                        account_name=none_if_empty(payment_preference.accountDetail.accountName),
                        bank_account_type_id=BankAccountType.get_id(
                            payment_preference.accountDetail.accountType.name
                        ),
                        default=True,
                    )
                    self.db_session.add(fineos_payment_preference)
                    logger.info(
                        f"Created new payment preference for employee {employee.employee_id} as {payment_method}"
                    )
                    return fineos_payment_preference
        else:
            logger.error(
                f"Unknown payment method {payment_method} found for employee {employee.employee_id}"
            )
        return None

    def sync_payment_preference_with_fineos(self) -> None:
        # Generate a list of all pending registrations
        pending_registrations = (
            self.db_session.query(Employee)
            .join(
                ClaimantPrepaidRegistration,
                Employee.employee_id == ClaimantPrepaidRegistration.employee_id,
            )
            .filter(
                ClaimantPrepaidRegistration.prepaid_registration_status_id
                == PrepaidRegistrationStatus.PENDING.prepaid_registration_status_id
            )
            .distinct()
            .all()
        )

        if pending_registrations:
            for employee in pending_registrations:

                fineos_web_id = payment_preference_util.get_fineos_web_id(
                    self.db_session, employee.employee_id
                )

                if fineos_web_id:
                    fineos_payment_preference = (
                        payment_preference_util.find_default_payment_preference(
                            self.fineos_client, fineos_web_id
                        )
                    )
                    if fineos_payment_preference:
                        logger.info(
                            f"Setting payment preference for employee {employee.employee_id}"
                        )
                        payment_preference = self.upsert_payment_preference(
                            fineos_payment_preference, employee
                        )

                        logger.info(f"Setting address record for employee {employee.employee_id}")
                        if payment_preference and fineos_payment_preference.address:
                            address = prepaid_debit_address_util.load_address(
                                fineos_payment_preference.address.addressLine1,
                                fineos_payment_preference.address.addressLine2,
                                fineos_payment_preference.address.addressLine4,
                                fineos_payment_preference.address.addressLine6,
                                fineos_payment_preference.address.postCode,
                            )
                            experian_address_pair = prepaid_debit_address_util.upsert_address(
                                self.db_session, employee, address
                            )
                            if payment_preference:
                                payment_preference.experian_address_pair = experian_address_pair

                    else:
                        logger.info(
                            f"No default payment preferences found for employee {employee.employee_id} in Fineos"
                        )
