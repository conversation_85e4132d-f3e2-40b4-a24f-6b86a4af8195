from datetime import date
from typing import Any, Callable, Dict, List, Optional

import massgov.pfml.delegated_payments.delegated_payments_util as payments_util
import massgov.pfml.util.logging as logging
from massgov.pfml.db.lookup_data.absences import (
    AbsencePeriodType,
    AbsenceReason,
    AbsenceReasonQualifierOne,
    AbsenceReasonQualifierTwo,
    AbsenceStatus,
)
from massgov.pfml.db.lookup_data.employees import (
    BankAccountType,
    LeaveRequestDecision,
    PaymentMethod,
)
from massgov.pfml.db.models.payments import MinimizedEmployeeFeed, RequestedAbsenceUnion
from massgov.pfml.delegated_payments.absence_period_container import AbsencePeriodContainer
from massgov.pfml.delegated_payments.claimant_extract_metrics import ClaimantExtractMetrics
from massgov.pfml.util.datetime import date_to_isoformat, datetime_str_to_date

logger = logging.get_logger(__name__)


class ClaimantData:
    """
    A class for containing any and all claim/claimant data. Handles validation
    and pulling values out of the dictionaries of the files we processed.
    """

    validation_container: payments_util.ValidationContainer

    count_incrementer: Optional[Callable[[str], None]]

    absence_case_id: str
    is_claim_id_proofed: bool = False

    fineos_notification_id: Optional[str] = None
    claim_type_raw: Optional[str] = None
    absence_case_status_id: Optional[int] = None
    absence_start_date: Optional[date] = None
    absence_end_date: Optional[date] = None

    fineos_customer_number: Optional[str] = None
    employer_customer_number: Optional[str] = None
    employee_tax_identifier: Optional[str] = None
    employee_first_name: Optional[str] = None
    employee_middle_name: Optional[str] = None
    employee_last_name: Optional[str] = None
    organization_unit_name: Optional[str] = None
    date_of_birth: Optional[str] = None
    payment_method: Optional[str] = None

    routing_nbr: Optional[str] = None
    account_nbr: Optional[str] = None
    account_type: Optional[str] = None
    should_do_eft_operations: bool = False

    mass_id_number: Optional[str] = None
    out_of_state_id_number: Optional[str] = None

    absence_period_data: List[AbsencePeriodContainer]

    def __init__(
        self,
        absence_case_id: str,
        requested_absences: List[RequestedAbsenceUnion],
        employee_record: Optional[MinimizedEmployeeFeed],
        count_incrementer: Optional[Callable[[str], None]] = None,
    ):
        self.absence_period_data = []
        self.absence_case_id = absence_case_id
        self.validation_container = payments_util.ValidationContainer(self.absence_case_id)

        self.count_incrementer = count_incrementer

        self._process_requested_absences(requested_absences)
        self._process_employee_feed(employee_record)

    def increment(self, metric: str) -> None:
        if self.count_incrementer:
            self.count_incrementer(metric)

    def _process_requested_absences(self, requested_absences: List[RequestedAbsenceUnion]) -> None:
        start_dates: List[str] = []
        end_dates = []

        # Keep track of all the values across all absence periods
        # Used for validation at end of function as we expect these to be the same
        employer_customer_numbers = []
        claimant_customer_numbers = []
        absence_case_statuses = []
        organization_unit_names = []

        # We dedupe absence periods as sometimes the extracts
        # exact duplicates and it causes performance issues
        absence_period_set = set()
        for requested_absence in requested_absences:
            # Add the raw values of a few fields to lists for later validation
            employer_customer_numbers.append(requested_absence.employer_customerno)
            claimant_customer_numbers.append(requested_absence.employee_customerno)
            absence_case_statuses.append(requested_absence.absence_casestatus)
            if requested_absence.orgunit_name:  # skip records with empty org unit name
                organization_unit_names.append(requested_absence.orgunit_name)

            # If any of the requested absence records are ID proofed, then
            # we consider the entire claim valid
            evidence_result_type = requested_absence.leaverequest_evidenceresulttype
            is_absence_period_id_proofed: Optional[bool]

            if evidence_result_type == "Satisfied":
                self.is_claim_id_proofed = True
                is_absence_period_id_proofed = True
            elif evidence_result_type is not None and evidence_result_type.strip() == "":
                is_absence_period_id_proofed = None
            else:
                is_absence_period_id_proofed = False

            start_date = payments_util.validate_db_input(
                "ABSENCEPERIOD_START", requested_absence, self.validation_container, True
            )
            end_date = payments_util.validate_db_input(
                "ABSENCEPERIOD_END", requested_absence, self.validation_container, True
            )
            class_id = payments_util.validate_db_input(
                "ABSENCEPERIOD_CLASSID", requested_absence, self.validation_container, True
            )
            index_id = payments_util.validate_db_input(
                "ABSENCEPERIOD_INDEXID", requested_absence, self.validation_container, True
            )
            fineos_leave_request_id = payments_util.validate_db_input(
                "LEAVEREQUEST_ID", requested_absence, self.validation_container, True
            )

            # Values exclusive to the "additional" requested absence record
            raw_absence_period_type = None
            raw_absence_reason_qualifier_1 = None
            raw_absence_reason_qualifier_2 = None
            raw_absence_reason = None
            raw_leave_request_decision = None

            if requested_absence.vbi_requested_absence_serial_id is None:
                msg = "Could not find VBI_REQUESTEDABSENCE_SOM record to pair with VBI_REQUESTEDABSENCE data, cannot populate orgunit_name or leaverequest_evidenceresulttype, however this is expected in the User Not Found flow"
                logger.info(msg, extra=self.get_traceable_details())
                self.increment(ClaimantExtractMetrics.NO_SOM_REQUESTED_ABSENCE_FOUND_COUNT)

            if requested_absence.vbi_requested_absence_serial_id is None:
                msg = "Could not find VBI_REQUESTEDABSENCE record to pair with VBI_REQUESTEDABSENCE_SOM data, cannot populate all absence period data"
                logger.info(msg, extra=self.get_traceable_details())
                self.validation_container.add_validation_issue(
                    payments_util.ValidationReason.MISSING_DATASET, msg
                )
                self.increment(ClaimantExtractMetrics.NO_ADDITIONAL_REQUESTED_ABSENCE_FOUND_COUNT)
            else:
                raw_absence_period_type = payments_util.validate_db_input(
                    "ABSENCEPERIOD_TYPE",
                    requested_absence,
                    self.validation_container,
                    True,
                    custom_validator_func=payments_util.lookup_validator(AbsencePeriodType),
                )

                raw_absence_reason_qualifier_1 = payments_util.validate_db_input(
                    "ABSENCEREASON_QUALIFIER1",
                    requested_absence,
                    self.validation_container,
                    True,
                    custom_validator_func=payments_util.lookup_validator(AbsenceReasonQualifierOne),
                )

                # 2 isn't required as only ~half of claims have a 2nd qualifier
                raw_absence_reason_qualifier_2 = payments_util.validate_db_input(
                    "ABSENCEREASON_QUALIFIER2",
                    requested_absence,
                    self.validation_container,
                    False,
                    custom_validator_func=payments_util.lookup_validator(AbsenceReasonQualifierTwo),
                )

                raw_absence_reason = payments_util.validate_db_input(
                    "ABSENCEREASON_NAME",
                    requested_absence,
                    self.validation_container,
                    True,
                    custom_validator_func=payments_util.lookup_validator(AbsenceReason),
                )

                raw_leave_request_decision = payments_util.validate_db_input(
                    "LEAVEREQUEST_DECISION",
                    requested_absence,
                    self.validation_container,
                    True,
                    custom_validator_func=payments_util.lookup_validator(LeaveRequestDecision),
                )

            if class_id is None or index_id is None:
                log_attributes = {
                    "absence_period_class_id": requested_absence.absenceperiod_classid,
                    "absence_period_index_id": requested_absence.absenceperiod_indexid,
                }
                logger.warning(
                    "Unable to extract class_id and/or index_id from requested_absence.",
                    extra=log_attributes,
                )
                self.increment(
                    ClaimantExtractMetrics.ABSENCE_PERIOD_CLASS_ID_OR_INDEX_ID_NOT_FOUND_COUNT
                )

                continue

            if start_date:
                start_dates.append(start_date)
            if end_date:
                end_dates.append(end_date)

            absence_period = AbsencePeriodContainer(
                start_date=start_date,
                end_date=end_date,
                class_id=class_id,
                index_id=index_id,
                period_id=None,
                is_id_proofed=is_absence_period_id_proofed,
                leave_request_id=fineos_leave_request_id,
                raw_absence_period_type=raw_absence_period_type,
                raw_absence_reason_qualifier_1=raw_absence_reason_qualifier_1,
                raw_absence_reason_qualifier_2=raw_absence_reason_qualifier_2,
                raw_absence_reason=raw_absence_reason,
                raw_leave_request_decision=raw_leave_request_decision,
            )

            # The absence period data has many duplicate records
            # on values we don't need for our processing. To improve
            # performance, we dedupe to avoid rewriting the same records
            # to the DB hundreds of times potentially.
            if absence_period in absence_period_set and self.count_incrementer:
                self.count_incrementer(ClaimantExtractMetrics.DUPLICATE_ABSENCE_PERIOD_COUNT)
            absence_period_set.add(absence_period)

        self.absence_period_data = list(absence_period_set)
        all_start_end_dates_valid = len(requested_absences) == len(start_dates) == len(end_dates)

        if all_start_end_dates_valid:
            # We don't need to convert to date to do comparisons (min/max)
            # This is because the current string format (Y-M-D...) preserves the chronological sort order
            start_date = min(start_dates)
            self.absence_start_date = datetime_str_to_date(start_date)

            end_date = max(end_dates)
            self.absence_end_date = datetime_str_to_date(end_date)

        else:
            self.increment(ClaimantExtractMetrics.START_DATE_OR_END_DATE_NOT_FOUND_COUNT)

        # Ideally, we would be able to distinguish and separate out the
        # various leave requests that make up a claim, but we don't
        # have this concept in our system at the moment. Until we support
        # that, we're leaving these other fields alone and always choosing the
        # latest one, however incorrect
        # The process_records_to_db method performs a DB query in which we'll need
        # to define the desired order once a decision has been made.
        # TODO: (PFMLPB-8354) Requested Absence order is undefined
        requested_absence = requested_absences[-1]

        # Note this should be identical regardless of absence case
        self.fineos_notification_id = payments_util.validate_db_input(
            "NOTIFICATION_CASENUMBER", requested_absence, self.validation_container, False
        )
        self.claim_type_raw = payments_util.validate_db_input(
            "ABSENCEREASON_COVERAGE", requested_absence, self.validation_container, True
        )

        try:
            self.claim_type_mapped = payments_util.get_mapped_claim_type(self.claim_type_raw)
        except ValueError:
            if self.claim_type_raw:
                self.validation_container.add_validation_issue(
                    payments_util.ValidationReason.INVALID_VALUE,
                    "ABSENCEREASON_COVERAGE",
                    "ABSENCEREASON_COVERAGE",
                )

        raw_absence_case_status = payments_util.validate_db_input(
            "ABSENCE_CASESTATUS",
            requested_absence,
            self.validation_container,
            True,
            custom_validator_func=payments_util.lookup_validator(AbsenceStatus),
        )
        if raw_absence_case_status:
            self.absence_case_status_id = AbsenceStatus.get_id(raw_absence_case_status)

        # Note this should be identical regardless of absence case
        self.fineos_customer_number = payments_util.validate_db_input(
            "EMPLOYEE_CUSTOMERNO", requested_absence, self.validation_container, True
        )

        # Note this should be identical regardless of absence case
        self.employer_customer_number = payments_util.validate_db_input(
            "EMPLOYER_CUSTOMERNO", requested_absence, self.validation_container, True
        )

        self.organization_unit_name = None
        org_unit_names_set = set(organization_unit_names)
        if len(org_unit_names_set) == 1:
            self.organization_unit_name = list(org_unit_names_set)[0]

        # Sanity test that the fields we expect to be the exact same
        # for every requested absence record are in fact the same.
        # If we've encountered a strange scenario, set the value to
        # None so it won't get attached to the claim (this likely indicates a FINEOS issue)
        if (dupe_number := len(set(employer_customer_numbers))) > 1:
            self.validation_container.add_validation_issue(
                payments_util.ValidationReason.UNEXPECTED_RECORD_VARIANCE,
                f"Expected only a single employer customer number for claim, and received {dupe_number}: {employer_customer_numbers}",
            )
            self.employer_customer_number = None
            self.increment(ClaimantExtractMetrics.MULTIPLE_EMPLOYER_FOR_CLAIM_ISSUE_COUNT)

        if (dupe_number := len(set(claimant_customer_numbers))) > 1:
            self.validation_container.add_validation_issue(
                payments_util.ValidationReason.UNEXPECTED_RECORD_VARIANCE,
                f"Expected only a single employee customer number for claim, and received {dupe_number}: {claimant_customer_numbers}",
            )
            self.fineos_customer_number = None
            self.increment(ClaimantExtractMetrics.MULTIPLE_CLAIMANT_FOR_CLAIM_ISSUE_COUNT)

        if (dupe_number := len(set(absence_case_statuses))) > 1:
            self.validation_container.add_validation_issue(
                payments_util.ValidationReason.UNEXPECTED_RECORD_VARIANCE,
                f"Expected only a single absence case status for claim, and received {dupe_number}: {absence_case_statuses}",
            )
            self.absence_case_status_id = None
            self.increment(ClaimantExtractMetrics.MULTIPLE_ABSENCE_STATUSES_FOR_CLAIM_ISSUE_COUNT)

        if (dupe_number := len(org_unit_names_set)) > 1:
            self.validation_container.add_validation_issue(
                payments_util.ValidationReason.UNEXPECTED_RECORD_VARIANCE,
                f"Expected only a single organization unit name for claim, and received {dupe_number}: {organization_unit_names}",
            )
            self.organization_unit_name = None
            self.increment(
                ClaimantExtractMetrics.MULTIPLE_ORGANIZATION_UNIT_NAMES_FOR_CLAIM_ISSUE_COUNT
            )

    def _process_employee_feed(self, employee_feed_record: Optional[MinimizedEmployeeFeed]) -> None:
        # If there isn't a FINEOS Customer Number, we can't lookup the employee record
        if not self.fineos_customer_number:
            return

        # The employee feed data is a list of records associated
        # with the employee feed. There will be a mix of records with
        # DEFPAYMENTPREF set to Y/N. Y indicating that it's the default payment
        # preference. We always prefer the default, but there can be many of each.
        if not employee_feed_record:
            error_msg = (
                f"Employee in VBI_REQUESTEDABSENCE_SOM with absence id {self.absence_case_id} and customer nbr {self.fineos_customer_number} "
                "not found in employee feed file"
            )
            self.validation_container.add_validation_issue(
                payments_util.ValidationReason.MISSING_DATASET, error_msg
            )
            logger.warning("Skipping: %s", error_msg, extra=self.get_traceable_details())
            self.increment(ClaimantExtractMetrics.NO_EMPLOYEE_FEED_RECORDS_FOUND_COUNT)

            # Can't process subsequent records as they pull from employee_feed
            return

        self.employee_tax_identifier = payments_util.validate_db_input(
            "NATINSNO", employee_feed_record, self.validation_container, True
        )

        self.date_of_birth = payments_util.validate_db_input(
            "DATEOFBIRTH", employee_feed_record, self.validation_container, True
        )

        self.employee_first_name = payments_util.validate_db_input(
            "FIRSTNAMES", employee_feed_record, self.validation_container, True
        )

        self.employee_middle_name = payments_util.validate_db_input(
            "INITIALS", employee_feed_record, self.validation_container, False
        )

        self.employee_last_name = payments_util.validate_db_input(
            "LASTNAME", employee_feed_record, self.validation_container, True
        )

        if employee_feed_record.extmassid:
            self.mass_id_number = payments_util.validate_db_input(
                "EXTMASSID",
                employee_feed_record,
                self.validation_container,
                False,
                custom_validator_func=payments_util.mass_id_validator,
            )
            if not self.mass_id_number:
                self.mass_id_number = employee_feed_record.extmassid
        else:
            self.mass_id_number = None

        self.out_of_state_id_number = payments_util.validate_db_input(
            "EXTOUTOFSTATEID", employee_feed_record, self.validation_container, False
        )

        self._process_payment_preferences(employee_feed_record)

    def _process_payment_preferences(self, employee_feed: MinimizedEmployeeFeed) -> None:
        # We only care about the payment preference fields if it is the default payment
        # preference record, otherwise we can't set these fields
        is_default_payment_pref = employee_feed.defpaymentpref == "Y"
        if not is_default_payment_pref:
            message = f"No default payment preference set for FINEOS customer number {self.fineos_customer_number}"
            logger.info(message, extra=self.get_traceable_details())
            self.validation_container.add_validation_issue(
                payments_util.ValidationReason.INVALID_VALUE, message
            )
            self.increment(ClaimantExtractMetrics.NO_DEFAULT_PAYMENT_PREFERENCE_COUNT)
            return

        self.payment_method = payments_util.validate_db_input(
            "PAYMENTMETHOD",
            employee_feed,
            self.validation_container,
            True,
            custom_validator_func=payments_util.lookup_validator(
                PaymentMethod,
                disallowed_lookup_values=[PaymentMethod.DEBIT.payment_method_description],
            ),
        )

        eft_required = self.payment_method == PaymentMethod.ACH.payment_method_description
        if eft_required:
            nbr_of_validation_issues = len(self.validation_container.validation_issues)

            self.routing_nbr = payments_util.validate_db_input(
                "SORTCODE",
                employee_feed,
                self.validation_container,
                eft_required,
                min_length=9,
                max_length=9,
                custom_validator_func=payments_util.routing_number_validator,
            )

            self.account_nbr = payments_util.validate_db_input(
                "ACCOUNTNO", employee_feed, self.validation_container, eft_required, max_length=17
            )

            self.account_type = payments_util.validate_db_input(
                "ACCOUNTTYPE",
                employee_feed,
                self.validation_container,
                eft_required,
                custom_validator_func=payments_util.lookup_validator(BankAccountType),
            )

            if nbr_of_validation_issues == len(self.validation_container.validation_issues):
                self.should_do_eft_operations = True

    def get_traceable_details(
        self, extra: Optional[Dict[str, Optional[Any]]] = None
    ) -> Dict[str, Optional[Any]]:
        return {
            **(extra or {}),
            "absence_case_id": self.absence_case_id,
            "fineos_customer_number": self.fineos_customer_number,
            "absence_start_date": date_to_isoformat(self.absence_start_date),
            "absence_end_date": date_to_isoformat(self.absence_end_date),
            "is_id_proofed": self.is_claim_id_proofed,
        }
