from concurrent.futures import Thr<PERSON><PERSON>ool<PERSON>xecutor
from datetime import datetime
from enum import auto

from sqlalchemy import update

import massgov.pfml.util.datetime as datetime_util
import massgov.pfml.util.logging as logging
from massgov.pfml.db.lookup_data.absences import AbsenceStatus
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.employees import <PERSON><PERSON><PERSON>, Employer, TaxIdentifier
from massgov.pfml.db.models.fineos_web_id import FINEOSWebIdExt
from massgov.pfml.services.documents import DocumentRequirementService
from massgov.pfml.util.batch.step import Step
from massgov.pfml.util.str_enum import StrEnum

logger = logging.get_logger(__name__)


class MarkApplicationsReadyForReviewStep(Step):
    """
    Marks completed applications that have received all required documents in FINEOS as ready for
    review.

    This step seeks to fill a gap where a claimant may bypass the portal by furnishing required
    documents directly to PFML agents, bypassing the portal.

    Uses multithreading to reduce I/O bounds from calling the FINEOS API.
    """

    BATCH_SIZE = 1_000
    """Count of `Application` records yielded in memory at a time."""

    MAX_WORKER_THREADS = 20
    """
    Maximum number of threads for handling asynchronous calls to FINEOS API.

    This step is I/O bound with the FINEOS API.
    """

    class Metrics(StrEnum):
        APPLICATIONS_ERRORED_COUNT = auto()
        """Count of processed Applications that encountered an error."""

        APPLICATIONS_MARKED_READY_FOR_REVIEW_COUNT = auto()
        """Count of Applications that were marked ready for review."""

        APPLICATIONS_PROCESSED_COUNT = auto()
        """Count of Applications that were considered to be marked as ready for review."""

    def cleanup_on_failure(self):
        logger.error(
            f"{self.__class__.__name__} failed. No applications were marked ready for review."
        )

    def run_step(self) -> None:
        logger.info(
            "Marking completed Applications with all required documents received as ready for "
            "review…"
        )

        applications_with_web_ids = self._get_applications_with_fineos_web_ids()

        with ThreadPoolExecutor(
            self.MAX_WORKER_THREADS, "mark_applications_ready_for_review"
        ) as executor:
            results = list(
                executor.map(
                    lambda app_with_web_id: self._process_application(app_with_web_id),
                    applications_with_web_ids.tuples(),
                )
            )

        updates = list(
            {
                "application_id": result[0].application_id,
                "ready_for_review_time": result[1],
                "completed_time": result[0].completed_time,
                "submitted_time": result[0].submitted_time,
                "created_at": result[0].created_at,
                "updated_at": result[1],
            }
            for result in results
            if result[1] is not None
        )

        self.set_metrics(
            {
                self.Metrics.APPLICATIONS_ERRORED_COUNT: sum(
                    1 for result in results if result[2] is False  # This indicates failure.
                ),
                self.Metrics.APPLICATIONS_PROCESSED_COUNT: len(results),
            }
        )

        self.db_session.execute(update(Application), updates)
        self.set_metrics({self.Metrics.APPLICATIONS_MARKED_READY_FOR_REVIEW_COUNT: len(updates)})

        for update_data in updates:
            logger.info(
                "MarkApplicationsReadyForReviewStep - application now ready for review",
                extra={
                    "application.application_id": update_data["application_id"],
                    "application.ready_for_review_time": update_data["ready_for_review_time"],
                    "application.completed_time": update_data["completed_time"],
                    "application.submitted_time": update_data["submitted_time"],
                    "application.created_at": update_data["created_at"],
                    "application.updated_at": update_data["updated_at"],
                },
            )

        logger.info(
            "Successfully marked completed Applications with all required documents received as "
            "ready for review."
        )

    def _get_applications_with_fineos_web_ids(self):
        return (
            self.db_session.query(Application, FINEOSWebIdExt)
            .join(TaxIdentifier)
            .join(Claim)
            .join(Employer)
            .join(
                FINEOSWebIdExt,
                (FINEOSWebIdExt.employee_tax_identifier == TaxIdentifier.tax_identifier)
                & (FINEOSWebIdExt.employer_fein == Employer.employer_fein)
                & (FINEOSWebIdExt.fineos_web_id.isnot(None)),
            )
            .filter(Application.completed_time.isnot(None))
            .filter(Application.ready_for_review_time.is_(None))
            .filter(Claim.fineos_absence_status_id == AbsenceStatus.ADJUDICATION.absence_status_id)
            .yield_per(self.BATCH_SIZE)
        )

    def _process_application(
        self, application_with_web_id: tuple[Application, FINEOSWebIdExt]
    ) -> tuple[Application, datetime | None, bool]:
        """
        Processes an application to determine if it is ready for review.

        Returns the application, and if it is ready for review, the time it was determined to be
        ready for review.
        """

        ready_for_review_time = None

        try:
            application, fineos_web_id_ext = application_with_web_id

            if DocumentRequirementService().are_all_required_documents_received(
                application, fineos_web_id_ext.fineos_web_id
            ):
                ready_for_review_time = datetime_util.utcnow()

            is_success = True
        except Exception as exception:
            is_success = False
            logger.error(
                "An error occurred while processing Application %s: %s",
                application.application_id,
                exception,
            )

        return application, ready_for_review_time, is_success
