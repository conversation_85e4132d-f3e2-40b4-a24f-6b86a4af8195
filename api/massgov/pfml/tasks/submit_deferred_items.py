import massgov.pfml.db as db
from massgov.pfml.util.batch.task_runner import TaskRunner
from massgov.pfml.util.bg import background_task
from massgov.pfml.util.logging import logging

logger = logging.getLogger(__name__)


class DeferredSubmissionItemTaskRunner(TaskRunner):
    """Submit Deferred Submission Items Task Runner"""

    def run_steps(self, db_session: db.Session, log_entry_db_session: db.Session) -> None:
        logger.info("Start - Deferred Submission Item Task Runner")

        logger.info("Hello, world! This is the Deferred Submission Item Task Runner.")

        logger.info("End - Deferred Submission Item Task Runner")


# Kicked off when the sync-fineos-extracts-to-pfml-models task completes successfully
# See: infra/ecs-tasks/template/s3_subscriptions.tf
@background_task("submit-deferred-items")
def main():
    """Run DeferredSubmissionItemTaskRunner"""
    DeferredSubmissionItemTaskRunner().run()
