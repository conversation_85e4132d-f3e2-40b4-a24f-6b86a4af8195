"""
Logic for per-request overrides for feature flags.
"""

import flask
from pydantic.validators import bool_validator

import massgov.pfml.util.logging
from massgov.pfml.features.config import FeaturesConfig
from massgov.pfml.features.logging import log_config_difference

logger = massgov.pfml.util.logging.get_logger(__name__)

FEATURE_FLAG_HEADER_PREFIX = "x-ff-"


def get_config(existing_config: FeaturesConfig, request: flask.Request) -> FeaturesConfig:
    """Get configuration with request overrides set.

    This function does not mutate ``existing_config``, it creates and returns a
    copy.

    Returns:
        A copy of the input configuration with request processing applied.
    """
    new_config = existing_config.copy()

    for header_name_raw, value_raw in request.headers.items():
        # HTTP headers should be treated as case-insensitive, so normalize the
        # value before doing anything with it
        header_name = header_name_raw.casefold()

        if not header_name.startswith(FEATURE_FLAG_HEADER_PREFIX):
            continue

        try:
            value_bool = bool_validator(value_raw)
        except TypeError:
            # theoretically we may want to support non-boolean values at some
            # point (like enums), but just skip for simplicity at the moment
            logger.info(
                "Non-boolean feature flag value. Ignoring.", extra={"header_name": header_name}
            )
            continue

        _process_ff_header(new_config, header_name, value_bool)

    log_config_difference(existing_config, new_config)

    return new_config


def _process_ff_header(config: FeaturesConfig, header_name: str, value: bool) -> None:
    """Run logic to set configuration fields based on header name/value."""

    if header_name == _get_ff_header_name("Disable-Overlapping-Benefit-Year-Claim-Creation"):
        config.application_intake.disable_overlapping_benefit_year_claim_creation = value

    if header_name == _get_ff_header_name("Document-Upload-Optional"):
        config.document_upload_optional.enable_document_upload_optional = value

    if header_name == _get_ff_header_name("Enable-Employer-Exemptions-Portal"):
        config.employer_exemptions.enable_employer_exemptions_portal = value

    # TODO(PFMLPB-23385): remove enable_employer_exemptions_dor_data_transfer feature flag
    if header_name == _get_ff_header_name("Enable-Employer-Exemptions-DOR-Data-Transfer"):
        config.employer_exemptions.enable_employer_exemptions_dor_data_transfer = value

    # TODO(PFMLPB-23588): remove enable_employer_exemptions_doc_storage feature flag
    if header_name == _get_ff_header_name("Enable-Employer-Exemptions-Doc-Storage"):
        config.employer_exemptions.enable_employer_exemptions_doc_storage = value

    if header_name == _get_ff_header_name("Application-Fraud-Check"):
        config.application_intake.enable_application_fraud_check = value

    # TODO PFMLPB-21397 - remove FF enableUniversalProfileIDV https://lwd.atlassian.net/browse/PFMLPB-21397
    if header_name == _get_ff_header_name("Enable-Universal-Profile-IDV"):
        config.universal_profile.enable_universal_profile_idv = value

    # TODO PFMLPB-21400 - remove FF enableMmgIDV https://lwd.atlassian.net/browse/PFMLPB-21400
    if header_name == _get_ff_header_name("Enable-MMG-IDV"):
        config.universal_profile.enable_mmg_idv = value

    # TODO (PFMLPB-22429): Remove SKI applications table flag https://lwd.atlassian.net/browse/PFMLPB-22429
    if header_name == _get_ff_header_name("Enable-SKI-Applications-Table"):
        config.leave_admin.enable_ski_applications_table = value

    # TODO (PFMLPB-22982): Remove SKI applications filter flag https://lwd.atlassian.net/browse/PFMLPB-22982
    if header_name == _get_ff_header_name("Enable-SKI-Applications-Filter"):
        config.leave_admin.enable_ski_applications_filter = value

    # TODO (PFMLPB-22584): Remove SKI wages flag https://lwd.atlassian.net/browse/PFMLPB-22584
    if header_name == _get_ff_header_name("Enable-SKI-Wages"):
        config.leave_admin.enable_ski_wages = value

    # TODO (PFMLPB-24322): Remove Occupation Data Collection feature flag https://lwd.atlassian.net/browse/PFMLPB-24322
    if header_name == _get_ff_header_name("Enable-Occupation-Data-Collection"):
        config.occupation_data_collection.enable_occupation_data_collection = value

    # TODO (PFMLPB-24883): Remove Backend IDV Invalidation feature flag https://lwd.atlassian.net/browse/PFMLPB-24883
    if header_name == _get_ff_header_name("Enable-Backend-Invalidation"):
        config.universal_profile.enable_backend_invalidation = value


def _get_ff_header_name(feature_flag_header_name: str) -> str:
    return f"{FEATURE_FLAG_HEADER_PREFIX}{feature_flag_header_name}".casefold()
