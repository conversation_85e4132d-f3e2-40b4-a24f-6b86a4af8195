from pydantic import Field

from massgov.pfml.util.pydantic import PydanticBaseSettings


class UniversalProfileFeatureConfig(PydanticBaseSettings):
    enable_universal_profile_idv: bool = Field(
        True,
        description="When this feature flag is on, the IDV Universal Profile enhancements are enabled.",
    )

    enable_mmg_idv: bool = Field(
        True,
        description="When this feature flag is on, the MMG IDV Pilot enhancements are enabled.",
    )

    enable_backend_invalidation: bool = Field(
        False,
        description="When this feature flag is on, backend IDV invalidation is enabled.",
    )
