from dataclasses import dataclass, field, fields
from typing import Any

from .application_intake import ApplicationIntakeFeatureConfig
from .document_upload_optional import DocumentUploadOptional
from .employer_exemptions import EmployerExemptionsFeatureConfig
from .fineos import FineosFeatureConfig
from .image_access import ImageAccessFeatureConfig
from .leave_admin import LeaveAdminFeatureConfig
from .mark_applications_ready_for_review import MarkApplicationsReadyForReviewConfig
from .med_to_bonding import MedToBondingFeatureConfig
from .occupation_data_collection import OccupationDataCollectionFeatureConfig
from .re_notification import ReNotificationConfig
from .universal_profile import UniversalProfileFeatureConfig


@dataclass
class FeaturesConfig:
    """
    This class just serves as a databag to collect different settings, broken
    down logically into different attributes.

    Each attribute of this class should be an instance of
    ``PydanticBaseSettings``. This ensures some common behavior, like loading
    environment variables from ``config/local.env`` file for local development.

    Example::

        from massgov.pfml.util.pydantic import PydanticBaseSettings

        class FooFeatureConfig(PydanticBaseSettings):
            enabled: bool = False

            # should try to follow the standard pattern as much as possible, but
            # if needing the environment variable to be a non-standard pattern,
            # can explicitly set the name to look for with `env`, this value
            # ignores the `env_prefix` set on the class
            my_special_config: bool = Field(False, env="MY_SPECIAL_ENV_VAR")

            class Config:
                env_prefix = "FOO_"

    Then included on this class like::

        foo: FooFeatureConfig = field(default_factory=FooFeatureConfig)

    Then in the environment, set as appropriate::

        FOO_ENABLED="true"
        MY_SPECIAL_ENV_VAR="true"

    All the feature config fields should have a default value, that is, they
    should be able to be initialized to a valid, safe configuration without any
    arguments supplied (typically this probably means the "feature" is turned
    off by default).

    See `upstream pydantic docs
    <https://pydantic-docs.helpmanual.io/usage/settings/>`_ for more details on
    functionality of ``Settings``.

    The class generally inherits the regular pydantic behavior with regard to
    various `field types <https://pydantic-docs.helpmanual.io/usage/types/>`_,
    i.e., will perform generally sensible coercion of string-y environment
    variable values into the proper Python types.

    One exception worth noting is that for more complex/collection types, the
    ``Settings`` class will attempt to parse the environment variables as JSON
    (though only for top-level fields).
    """

    fineos: FineosFeatureConfig = field(default_factory=FineosFeatureConfig)
    image_access: ImageAccessFeatureConfig = field(default_factory=ImageAccessFeatureConfig)
    application_intake: ApplicationIntakeFeatureConfig = field(
        default_factory=ApplicationIntakeFeatureConfig
    )
    leave_admin: LeaveAdminFeatureConfig = field(default_factory=LeaveAdminFeatureConfig)
    document_upload_optional: DocumentUploadOptional = field(default_factory=DocumentUploadOptional)
    employer_exemptions: EmployerExemptionsFeatureConfig = field(
        default_factory=EmployerExemptionsFeatureConfig
    )
    universal_profile: UniversalProfileFeatureConfig = field(
        default_factory=UniversalProfileFeatureConfig
    )
    mark_applications_ready_for_review: MarkApplicationsReadyForReviewConfig = field(
        default_factory=MarkApplicationsReadyForReviewConfig
    )
    re_notification: ReNotificationConfig = field(default_factory=ReNotificationConfig)
    med_to_bonding: MedToBondingFeatureConfig = field(default_factory=MedToBondingFeatureConfig)
    occupation_data_collection: OccupationDataCollectionFeatureConfig = field(
        default_factory=OccupationDataCollectionFeatureConfig
    )

    def copy(self):
        kwargs = {f.name: getattr(self, f.name).copy(deep=True) for f in fields(self)}
        return type(self)(**kwargs)

    def dict(self) -> dict[str, Any]:
        return {f.name: getattr(self, f.name).dict() for f in fields(self)}
