from pydantic import Field

from massgov.pfml.util.pydantic import PydanticBaseSettings


class EmployerExemptionsFeatureConfig(PydanticBaseSettings):
    # TODO: remove this feature flag for PFMLPB-17696

    enable_employer_exemptions_portal: bool = Field(
        False,
        description="Controls whether or not the exemption protal is being used",
    )
    # TODO(PFMLPB-23385): remove enable_employer_exemptions_dor_data_transfer feature flag
    enable_employer_exemptions_dor_data_transfer: bool = Field(
        False,
        description="Controls whether or not exemption data is being sent to DOR",
    )
    # TODO(PFMLPB-23588): remove enable_employer_exemptions_doc_storage feature flag
    enable_employer_exemptions_doc_storage: bool = Field(
        False,
        description="Controls whether or not exemption documents are being stored, uploaded, and downloaded via the portal",
    )
