#
# Makefile to build Pydantic models for FINEOS APIs.
#
# This is only needed if the API has changed. The output files should be committed to version control.
#
# Usage:
#   # To build everything:
#     make VERSION=1_2_3 clean
#  	  make VERSION=1_2_3 models
#   # To build one API's models: (may need to be cleaned first)
#     make VERSION=1_2_3 clean
#     make customer_api/spec_1_2_3.py
#     make group_client_api/spec_1_2_3.py

checkversion:
ifndef VERSION
	$(error "VERSION is not set. Please specify a spec version moniker: `VERSION=1_2_3` (needs to match the .json spec file suffix.)"))
endif

MODELS = customer_api/spec_$(VERSION).py group_client_api/spec_$(VERSION).py

models: checkversion $(MODELS)

clean: checkversion
	rm -f $(MODELS) */*.cleaned.json

customer_api.yaml:
	$(error Please fetch https://documentation.fineos.com/support/documentation/ manually)

# Fix some issues in the OpenAPI file.
%.cleaned.json: %.json
	poetry run python preprocess-fineos-models-spec.py $< > $@

%.py: %.cleaned.json
	poetry run datamodel-codegen --field-constraints --base-class "massgov.pfml.util.pydantic.PydanticBaseModelEmptyStrIsNone" --special-field-name-prefix "" --input $< --output $@
	poetry run black $@
	poetry run isort $@
