# just to get the namespace
from datetime import date
from typing import Optional, Union

from pydantic import Field

from massgov.pfml.fineos.common import DOWNLOADABLE_DOC_TYPES

from . import spec_22_5_1 as base  # noqa: F401


# Optional properties
class ManagedRequirementDetails(base.ManagedRequirementDetails):
    creationDate: Optional[date] = Field(  # type: ignore
        ..., description="ISO 8601 date format", example="1999-12-31"
    )
    dateSuppressed: Optional[date] = Field(  # type: ignore
        ..., description="ISO 8601 date format", example="1999-12-31"
    )


class GroupClientDocument(base.GroupClientDocument):
    documentId: Union[int, str] = Field(..., description="The document Id")  # type: ignore

    def is_downloadable_by_leave_admin(self) -> bool:
        return self.name.lower() in DOWNLOADABLE_DOC_TYPES
