from .overrides_22_5_1 import GroupClientDocument, ManagedRequirementDetails  # noqa: F401
from .spec_22_5_1 import (  # noqa: F401
    Absence,
    AbsenceCaseEventEmployeeEmbeddable,
    AbsenceCaseEventNotificationCaseEmbeddable,
    AbsenceCaseEventResource,
    AbsenceCaseEventResources,
    AbsenceDay,
    AbsenceReasonEmbeddable,
    AccommodationCaseResourceGroupClient,
    AccommodationCategoryResponseGroupClient,
    AccommodationEmbeddableGroupClient,
    AccommodationSourceResponseGroupClient,
    AccountDetailEmbeddable,
    AccountDetails,
    AccountTransferInfo,
    AccountTypeRequest,
    AccountTypeResponse,
    ActualAbsencePeriodResource,
    ActualAbsencePeriodResources,
    AdditionalWithholdAmountMoneyRequest,
    AdditionalWithholdAmountMoneyResponse,
    Address,
    AddressEmbeddable,
    AltEmploymentCatRequest,
    AltEmploymentCatResponse,
    AltGroupClientUserListSchema,
    AltRoleSchema,
    AltStatusSchema,
    AmountMoneyRequest,
    AmountMoneyResponse,
    ApiBaseErrorResponse,
    ApiBaseErrorResponseGroupClient,
    ApiError,
    ApiErrorGroupClient,
    ApplicabilityEmbeddable,
    AppliesToStates,
    AUFloorLevelTypesRequest,
    AUFloorLevelTypesResponse,
    AUPostalTypesRequest,
    AUPostalTypesResponse,
    AustralianAddressEmbeddable,
    AUStreetSuffixesRequest,
    AUStreetSuffixesResponse,
    AUTaxCodeDetails,
    Base64EncodedFileData,
    Base64EncodedFileDetails,
    BenefitSummary,
    BulkCreateActualAbsencePeriodCommand,
    BulkCreateCommandCreateActualAbsencePeriodCommand,
    CancellationPeriod,
    CancelLeavePeriodsDetails,
    CaseContactSummary,
    CaseEmbeddable,
    CaseHandler,
    CaseHandlerEmbeddable,
    CaseHandlerEmbeddableGroupClient,
    CaseParticipantsSummary,
    CaseStatusHistory,
    CertificationPeriodDetails,
    ChequeDetailEmbeddable,
    ChequeDetails,
    ChequePaymentInfo,
    ClaimMedicareLevyExemptionResponse,
    ClaimOccupation,
    ClaimSummary,
    ClientDominantSideRequestGroupClient,
    ClientDominantSideResponseGroupClient,
    ClosureReasons,
    ClosureReasonsResponseGroupClient,
    CommunicationPreferenceResource,
    CommunicationPreferences,
    ConditionCategoryRequestGroupClient,
    ConditionCategoryResponseGroupClient,
    ContactContextSwaggerResponse,
    ContactDetails,
    ContactTimePreferenceEmbeddable,
    ContactTimeZoneResponse,
    ContractualEarnings,
    ContractualEarningsList,
    CountryRequest,
    CountryResponse,
    CreateAbsencePeriodTypeRequest,
    CreateAccountDetailCommand,
    CreateActualAbsencePeriodCommand,
    CreateAddressCommand,
    CreateAustralianAddressCommand,
    CreateAUTaxCodeDetailsCommand,
    CreateChequeDetailCommand,
    CreateContractualEarningsCommand,
    CreateEmailAddressCommand,
    CreateNZTaxCodeDetailsCommand,
    CreatePaymentPreferenceCommand,
    CreatePhoneNumberCommand,
    CreateRegionTaxCodeDetailsCommand,
    CreateTaxCodeRecordCommand,
    CreateWebMessageCommand,
    CreateWeekBasedWorkPatternCommand,
    CreateWorkPatternDayCommand,
    CurrentPhaseResponse,
    Customer,
    CustomerAddress,
    CustomerEmbeddable,
    CustomerInfo,
    CustomerOccupation,
    CustomerOccupations,
    CustomerOccupationWeekBasedWorkPattern,
    CustomerResource,
    CustomerResourceGroupClient,
    CustomerResourcesGroupClient,
    DayOfWeekRequest,
    Decision,
    DeliveryTypeRequest,
    DeliveryTypeResponse,
    DependentDeductionsMoneyRequest,
    DependentDeductionsMoneyResponse,
    DiagnosisDetails,
    DisabilityBenefit,
    DisabilityClaim,
    DivisionClassLinkEmbeddable,
    EarningsBasisFrequencyRequest,
    EarningsBasisFrequencyResponse,
    EarningsResponse,
    EarningsTypeRequest,
    EarningsTypeResponse,
    EditAccountDetailCommand,
    EditAdditionalWithholdAmountMoneyRequest,
    EditAddressCommand,
    EditAustralianAddressCommand,
    EditAUTaxCodeDetailsCommand,
    EditChequeDetailCommand,
    EditContractualEarningsCommand,
    EditCustomerInfoCommand,
    EditCustomerOccupationCommand,
    EditDependentDeductionsMoneyRequest,
    EditEmailAddressCommand,
    EditEmploymentTypeRequest,
    EditEmploymentWorkStateRequest,
    EditExtraPayTaxRateRequest,
    EditFilingMaritalStatusRequest,
    EditGroupClientUserCommand,
    EditGroupPolicyMemberDetailCommand,
    EditKiwiSaverStatusRequest,
    EditLevyExemptionRequest,
    EditMaritalRelationshipStatusRequest,
    EditMedicalDetailsCommandGroupClient,
    EditMedicareLevySurchargeRequest,
    EditNonStdDeductionsMoneyRequest,
    EditNZTaxCodeDetailsCommand,
    EditOtherIncomeMoneyRequest,
    EditPaymentPreferenceAddressCommand,
    EditPaymentPreferenceAustralianAddressCommand,
    EditPaymentPreferenceCommand,
    EditPhoneNumberCommand,
    EditPregnancyDetailsCommand,
    EditRegionTaxCodeDetailsCommand,
    EditResidentialStatusRequest,
    EditTaxCodeRecordCommand,
    EditTaxCodeRequest,
    EditTaxOffsetsMoneyRequest,
    EditTaxTypeRequest,
    EditVersionRequest,
    EditWebMessageCommand,
    EditWeekBasedWorkPatternCommand,
    EditWithholdingTaxStatusRequest,
    EditWorkPatternDayCommand,
    EffectivenessResponseGroupClient,
    EForm,
    EFormAttribute,
    EFormSummary,
    EligibilityEmbeddable,
    EmailAddress,
    EmailAddressContactMethodResponse,
    EmailAddressResource,
    Emails,
    EmpLocationCodeRequest,
    EmpLocationCodeResponse,
    Employee,
    EmployeeEmbeddableGroupClient,
    EmployeeLeaveBalance,
    EmployeeOnLeave,
    EmployeePreferredResponseGroupClient,
    EmployeesOnApprovedLeaveRpcResponse,
    EmployerPreferredResponseGroupClient,
    EmploymentCatRequest,
    EmploymentCatResponse,
    EmploymentStatusRequest,
    EmploymentStatusResponse,
    EmploymentTitleRequest,
    EmploymentTitleResponse,
    EmploymentTypeResponse,
    EmploymentWorkStateResponse,
    EndpointPermissionResource,
    EndpointPermissionResources,
    EndPosCodeRequest,
    EndPosCodeResponse,
    EnumCommand,
    EnumDomain,
    EnumDomains,
    EnumInstance,
    EnumInstances,
    EnumInstanceSummary,
    EnumSubset,
    EnumSubsetInfoGroupClient,
    EnumSubsetSummary,
    EpisodeDurationBasisResponse,
    EpisodePeriodDurationBasisRequest,
    ErrorModel,
    ErrorSource,
    ErrorSourceGroupClient,
    ExceptionMeta,
    ExceptionMetaGroupClient,
    ExtendedAddress,
    ExtensionAttribute,
    ExtraPayTaxRateRequest,
    ExtraPayTaxRateResponse,
    FilingMaritalStatusRequest,
    FilingMaritalStatusResponse,
    GenderRequest,
    GenderResponse,
    GroupClientAccommodationCase,
    GroupClientAccommodationDetail,
    GroupClientEditScheduledDayCommand,
    GroupClientUser,
    GroupPolicyClassEmbeddable,
    GroupPolicyDivisionEmbeddable,
    GroupPolicyMemberDetail,
    GroupPolicyMemberDetails,
    HospitalisationDetails,
    HospitalisationDetailsResponse,
    IdentificationNumberTypeRequest,
    IdentificationNumberTypeResponse,
    IncomeSource,
    IncomeSourceResponse,
    JobStrenuousRequest,
    JobStrenuousResponse,
    KiwiSaverStatusRequest,
    KiwiSaverStatusResponse,
    LeaveInfo,
    LeavePlan,
    LeavePlanCategoryDtlResponse,
    LeavePlanCategoryResponse,
    LeavePlanGroupDtlResponse,
    LeavePlanGroupResponse,
    LeavePlanJobProtectionResponse,
    LeavePlanType,
    LeavePlanTypeDtlResponse,
    LeavePlanTypeResponse,
    LeaveRequest,
    LeaveTypeResponse,
    LevyExemptionRequest,
    LifeExpectancyRequestGroupClient,
    LifeExpectancyResponseGroupClient,
    Limitations,
    LimitationsDescriptionResponseGroupClient,
    LinkCommand,
    LinkshdrItem,
    LumpSumBenefit,
    ManagerAcceptedResponse,
    MaritalRelationshipStatusRequest,
    MaritalRelationshipStatusResponse,
    MaritalStatusRequest,
    MaritalStatusResponse,
    MedicalDetailsGroupClient,
    MedicareLevySurchargeRequest,
    MedicareLevySurchargeResponse,
    MemberGroupEmbeddable,
    MemberGroupEmbeddableGroupClient,
    ModelEnum,
    MonthlyBasisEarningAmountsRequest,
    MonthlyBasisEarningAmountsResponse,
    MonthlyEarningsRequest,
    NationalityRequest,
    NationalityResponse,
    NewDiagnosisDetails,
    NewPaymentPreference,
    NominatedPayeeTypeRequest,
    NominatedPayeeTypeResponse,
    NonStdDeductionsMoneyRequest,
    NonStdDeductionsMoneyResponse,
    NotAccommodatedReasonResponseGroupClient,
    NotAccommodatedReasons,
    NotifedByResponse,
    Notification,
    NotificationCase,
    NotificationCaseDetail,
    NotificationCaseEmbeddableGroupClient,
    NotificationCaseForEmployee,
    NotificationCaseSummaryForClaims,
    NotificationClaimSummary,
    NotificationDetails,
    NotificationReason,
    NotificationReasonResponse,
    NotificationReasonsResponse,
    Notifications,
    NZTaxCodeDetails,
    OccupationQualifierEmbeddable,
    OtherIncomeMoneyRequest,
    OtherIncomeMoneyResponse,
    OutstandingInformationData,
    OutstandingInformationItem,
    Participant,
    ParticipantPlan,
    PartyTypeRequest,
    PartyTypeResponse,
    PatternStatusRequest,
    PatternStatusResponse,
    Payment,
    PaymentDayRequest,
    PaymentDayResponse,
    PaymentLine,
    PaymentLineDetails,
    PaymentMethodRequest,
    PaymentMethodResponse,
    PaymentPeriodRequest,
    PaymentPeriodResponse,
    PaymentPreferenceAddressEmbeddable,
    PaymentPreferenceAustralianAddressEmbeddable,
    PaymentPreferenceResource,
    PaymentPreferenceResources,
    PaymentPreferenceResponse,
    Period,
    PeriodDecisions,
    PeriodDecisionsEmployee,
    PermissionGroupResource,
    PermissionGroupResources,
    PersonDetails,
    PhoneNumber,
    PhoneNumberContactMethodResponse,
    PhoneNumberResource,
    PhoneNumbers,
    Policy,
    PreferredContactTimeResponse,
    PregnancyDetails,
    PregnancyRelatedResponseGroupClient,
    QuerySortInfo,
    QuerySortInfoGroupClient,
    ReadDisabilityBenefitResult,
    ReadDisabilityResult,
    ReadLumpSumBenefitResult,
    ReadOccupation,
    ReceivedViaResponse,
    ReevaluateActualAbsencePeriodCommand,
    RegionTaxCodeDetails,
    RegionTaxCodeDetails1,
    ReportedByResponse,
    RequestedEpisodicLeaveDetails,
    ResidentialStatusRequest,
    ResidentialStatusResponse,
    RestrictionsLimitations,
    RpcResponse,
    ScheduledDayResource,
    ScheduledDayResources,
    ServiceAgreementLeavePlan,
    ServiceAgreementLeavePlanSummaries,
    ServiceAgreementLeavePlanSummary,
    SharedEntitlementPlan,
    SingleAccommodationDecisionResponseGroupClient,
    SocialSecurityBenefitResponse,
    SocialSecurityDecisionResponse,
    SortOrderItem,
    SortOrderItemGroupClient,
    StandardHourlyRateMoneyRequest,
    StandardHourlyRateMoneyResponse,
    StartClaimDetails,
    StartClaimSummary,
    StateResponse,
    StatusResponse,
    TaEnumAccommodationTypeGroupClient,
    TaEnumGroupClient,
    TaEnumInstanceSwagger,
    TaEnumInstanceSwaggerGroupClient,
    TaEnumResponseAccommodationTypeGroupClient,
    TaEnumTaEnumTypeGroupClient,
    TaxCodeRecord,
    TaxCodeRecordResources,
    TaxCodeRequest,
    TaxCodeResponse,
    TaxOffsetsMoneyRequest,
    TaxOffsetsMoneyResponse,
    TaxTypeRequest,
    TaxTypeResponse,
    TimeApprovedBasisResponse,
    TimeZoneRequest,
    TimeZoneResponse,
    TitleRequest,
    TitleResponse,
    TypeOfSurgeryRequestGroupClient,
    TypeOfSurgeryResponseGroupClient,
    TypeResponse,
    UserErrorModel,
    ValidationMessageModel,
    VersionRequest,
    VersionResponse,
    WebMessageCaseEmbeddable,
    WebMessageResource,
    WebMessages,
    WeekBasedWorkPatternTypeResponse,
    WeeklyBasisEarningAmountsRequest,
    WeeklyBasisEarningAmountsResponse,
    WeeklyEarningsRequest,
    WithholdingTaxStatusRequest,
    WithholdingTaxStatusResponse,
    WorkPatternDayOfWeekResponse,
    WorkPatternDayResponse,
    WorkPatternTypeRequest,
    WorkPatternTypeResponse,
    WorkWeekStartsRequest,
    WorkWeekStartsResponse,
    WriteDivisionClassLinkCommand,
)
