# generated by datamodel-codegen:
#   filename:  spec.cleaned.json
#   timestamp: 2025-02-24T21:17:27+00:00

from __future__ import annotations

from datetime import date, datetime
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import AnyUrl, Field

from massgov.pfml.util.pydantic import PydanticBaseModelEmptyStrIsNone


class AUFloorLevelTypesRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class AUPostalTypesRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class AUStreetSuffixesRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class Absence(PydanticBaseModelEmptyStrIsNone):
    caseReference: Optional[str] = Field(None, description="Case reference related to absence")
    id: Optional[str] = Field(None, description="Unique identifier for the absence case")


class AbsenceCaseEventEmployeeEmbeddable(PydanticBaseModelEmptyStrIsNone):
    customerNo: Optional[str] = Field(
        None, description="The unique customer number assigned to a customer."
    )
    firstName: Optional[str] = Field(None, description="First name of the employee.")
    id: Optional[str] = Field(None, description="Unique identifier for the employee.")
    initials: Optional[str] = Field(None, description="Middle initials of the employee.")
    lastName: Optional[str] = Field(None, description="Last name of the employee.")


class AbsenceCaseEventNotificationCaseEmbeddable(PydanticBaseModelEmptyStrIsNone):
    caseReference: Optional[str] = Field(None, description="Business identifier for this case.")
    id: Optional[str] = Field(
        None, description="Resource Id of the notification case this absence case belongs."
    )


class AbsenceReasonEmbeddable(PydanticBaseModelEmptyStrIsNone):
    qualifier1: Optional[str] = Field(None, description="The leave request first qualifier.")
    qualifier2: Optional[str] = Field(None, description="The leave request second qualifier.")
    reasonName: Optional[str] = Field(None, description="The leave request reason.")


class AccountTransferInfo(PydanticBaseModelEmptyStrIsNone):
    bankAccountNumber: Optional[str] = Field(
        None, description="The bank account number of an employee"
    )
    bankAccountType: Optional[str] = Field(None, description="The bank account type of an employee")
    bankBranchSortCode: Optional[str] = Field(
        None, description="The employee bank branch sort code"
    )
    bankCode: Optional[str] = Field(None, description="The bank code of an employee")
    bankInstituteName: Optional[str] = Field(
        None, description="The bank institute name of an employee"
    )


class AccountTypeRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class AdditionalWithholdAmountMoneyRequest(PydanticBaseModelEmptyStrIsNone):
    amountMinorUnits: Optional[str] = Field(
        None,
        description="The amount in minor units i.e. without a decimal point. So $100.27 will be 10027",
    )


class AdditionalWithholdAmountMoneyResponse(PydanticBaseModelEmptyStrIsNone):
    amountMinorUnits: Optional[int] = Field(
        None,
        description="The amount in minor units i.e. without a decimal point. So $100.27 will be 10027",
    )
    currency: Optional[str] = Field(None, description="The currency used in the Money amount")
    scale: Optional[int] = Field(None, description="The scale used in the Money amount")


class AltEmploymentCatRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class AmountMoneyRequest(PydanticBaseModelEmptyStrIsNone):
    amountMinorUnits: Optional[str] = Field(
        None,
        description="The amount in minor units i.e. without a decimal point. So $100.27 will be 10027",
    )


class AmountMoneyResponse(PydanticBaseModelEmptyStrIsNone):
    amountMinorUnits: Optional[int] = Field(
        None,
        description="The amount in minor units i.e. without a decimal point. So $100.27 will be 10027",
    )
    currency: Optional[str] = Field(None, description="The currency used in the Money amount")
    scale: Optional[int] = Field(None, description="The scale used in the Money amount")


class AppliesToStates(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = Field(
        None, description="Links to the enum domain and enum instance for the API"
    )
    domainId: Optional[int] = Field(None, description="The <code>domain id</code> of the enum")
    domainName: Optional[str] = Field(None, description="The enum domain name")
    fullId: Optional[int] = Field(None, description="The <code>instance id</code> of the enum")
    name: Optional[str] = Field(None, description="The enum instance name")


class ApplicabilityEmbeddable(PydanticBaseModelEmptyStrIsNone):
    appliesToStates: Optional[AppliesToStates] = Field(
        None,
        description="List of US states which the leave plan applies to. A US state (EnumDomainId=138).",
    )


class Base64EncodedFileData(PydanticBaseModelEmptyStrIsNone):
    base64EncodedFileContents: str = Field(..., description="The base64 encoded file contents.")
    contentType: Optional[str] = Field(None, description="The MIME type of the file.")
    description: Optional[str] = Field(None, description="A description of the file.")
    fileExtension: str = Field(
        ..., description="The extension of the file, without a preceding dot."
    )
    fileName: str = Field(..., description="The name of the file, without the extension.")
    fileSizeInBytes: int = Field(..., description="The size in bytes of the file.")
    managedReqId: Optional[int] = Field(
        None, description="The Managed Requirement ID to be associated with the file."
    )


class Base64EncodedFileDetails(PydanticBaseModelEmptyStrIsNone):
    base64EncodedFileContents: str = Field(..., description="The base64 encoded file contents.")
    contentType: Optional[str] = Field(None, description="The MIME type of the file.")
    description: Optional[str] = Field(None, description="A description of the file.")
    fileExtension: str = Field(
        ..., description="The extension of the file, without a preceding dot."
    )
    fileName: str = Field(..., description="The name of the file, without the extension.")


class CancellationPeriod(PydanticBaseModelEmptyStrIsNone):
    endDate: date = Field(..., description="ISO 8601 date format", example="1999-12-31")
    startDate: date = Field(..., description="ISO 8601 date format", example="1999-12-31")


class CaseHandler(PydanticBaseModelEmptyStrIsNone):
    emailAddress: Optional[str] = Field(None, description="Email address of the case handler.")
    name: Optional[str] = Field(None, description="Full name of the case handler.")
    phoneNumber: Optional[str] = Field(None, description="Phone number of the case handler.")


class CaseHandlerEmbeddable(PydanticBaseModelEmptyStrIsNone):
    emailAddress: Optional[str] = Field(None, description="Email Address")
    id: Optional[str] = Field(None, description="ID used to uniquely identify case handler")
    name: Optional[str] = Field(None, description="Name")
    telephoneNo: Optional[str] = Field(None, description="Telephone")


class CaseHandlerEmbeddableGroupClient(PydanticBaseModelEmptyStrIsNone):
    emailAddress: Optional[str] = Field(None, description="Email address of the case handler.")
    id: Optional[str] = Field(None, description="ID used to uniquely identify case handler.")
    name: Optional[str] = Field(None, description="Full name of the case handler.")
    phoneNumber: Optional[str] = Field(None, description="Phone number of the case handler.")


class CaseParticipantsSummary(PydanticBaseModelEmptyStrIsNone):
    endDate: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    participantId: Optional[int] = Field(None, description="Id of the participant")
    partyName: Optional[str] = Field(
        None,
        description="The name of the party with the participant role on the case",
        max_length=100,
        min_length=0,
    )
    referenceNo: Optional[str] = Field(
        None,
        description="The reference number of the party with the participant role on the case",
        max_length=40,
        min_length=0,
    )
    rolename: Optional[str] = Field(
        None,
        description="The role name assigned to the participant on the case",
        max_length=10,
        min_length=0,
    )
    sourceSystem: Optional[str] = Field(
        None,
        description="The source system of the party with the participant role on the case",
        max_length=100,
        min_length=0,
    )
    startDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )


class CaseStatusHistory(PydanticBaseModelEmptyStrIsNone):
    dateStatusDeparted: Optional[str] = Field(
        None,
        description="Formatted date when benefit's underlying process moved out of a step with this phase.",
    )
    dateStatusEntered: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    status: Optional[str] = Field(
        None, description="The stage in the process that the case was at."
    )


class CertificationPeriodDetails(PydanticBaseModelEmptyStrIsNone):
    consultationDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    duration: Optional[int] = Field(
        None, description="The number of days within the certification period."
    )
    insurerReceivedDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    notes: Optional[str] = Field(None, description="The notes about the certification period.")
    periodFromDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    periodToDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    periodType: Optional[str] = Field(
        None, description="The period type of the certification period."
    )
    status: Optional[str] = Field(None, description="The status of the certification period.")
    statusReason: Optional[str] = Field(
        None, description=" The selected reason for the status of a certification period."
    )
    totalPeriodDays: Optional[int] = Field(
        None,
        description="The total days of a certification period for a specific period type.\r\nThis is calculated based on the event FINEOS.claims.benefits.calcDaysInPeriodEvent",
    )


class ChequeDetailEmbeddable(PydanticBaseModelEmptyStrIsNone):
    nameToPrintOnCheck: Optional[str] = Field(
        None,
        description="This is the name that displays on the payee's check. For example, the payee's name, or if there is a nominated payee, In the Estate of followed by the nominated payee's name.",
    )


class ChequeDetails(PydanticBaseModelEmptyStrIsNone):
    nameToPrintOnCheck: Optional[str] = Field(
        None,
        description="The name that should be printed on the check",
        max_length=100,
        min_length=0,
    )


class ChequePaymentInfo(PydanticBaseModelEmptyStrIsNone):
    chequeNumber: Optional[str] = Field(None, description="The cheque number of an employee")


class ClientDominantSideRequestGroupClient(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class ConditionCategoryRequestGroupClient(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class CountryRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class CreateAbsencePeriodTypeRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class CreateAccountDetailCommand(PydanticBaseModelEmptyStrIsNone):
    accountName: str = Field(..., description="Name of the account holder", max_length=256)
    accountNo: str = Field(
        ..., description="The Account number of the personal account", max_length=256
    )
    accountType: Optional[AccountTypeRequest] = None
    bankCode: Optional[str] = Field(None, description="The Bank Code", max_length=4)
    currency: str = Field(..., description="Currency identifier", max_length=20)
    extensions: Optional[Dict[str, Any]] = None
    routingNumber: str = Field(..., description="Sort code of the Bank", max_length=256)


class CreateAustralianAddressCommand(PydanticBaseModelEmptyStrIsNone):
    buildingName1: Optional[str] = Field(
        None, description="The name of the building.", max_length=30
    )
    buildingName2: Optional[str] = Field(
        None, description="The name of the building continued.", max_length=30
    )
    dpId: Optional[int] = Field(
        None,
        description="Referred to as DPID this is an 8-digit Australian post address identifier which uniquely identifies each delivery point to which Australia Post delivers mail. ",
    )
    extensions: Optional[Dict[str, Any]] = None
    floorLevelNumber: Optional[str] = Field(
        None,
        description="The floorLevelNumber must not be more than {max} characters",
        max_length=2,
    )
    floorLevelType: AUFloorLevelTypesRequest
    lotNumber: Optional[str] = Field(None, description="The lot number.", max_length=6)
    postalNumber: Optional[str] = Field(None, description="The postal number. ", max_length=6)
    postalNumberPrefix: Optional[str] = Field(
        None,
        description="This refers to the non-numeric portion preceding the Postal Number. EXAMPLE : PO Box B20 where B is the prefix. ",
        max_length=3,
    )
    postalNumberSuffix: Optional[str] = Field(
        None,
        description="This refers to the non-numeric portion following the Postal Number. EXAMPLE : PO Box 20A where A is the suffix.",
        max_length=3,
    )
    postalType: AUPostalTypesRequest
    premiseNoSuffix: Optional[str] = Field(
        None, description="The suffix to apply to the number of the premise.", max_length=1
    )
    premiseNoTo: Optional[int] = Field(
        None,
        description="The high number in a range for the premise. This is used when an address consists of a range of numbers as in 8-10 Lower Pembroke Street where the value 10 would be stored in this field. ",
    )
    premiseNoToSuffix: Optional[str] = Field(
        None, description="The suffix to apply to the number to of the premise. ", max_length=1
    )
    streetSuffix: AUStreetSuffixesRequest


class CreateChequeDetailCommand(PydanticBaseModelEmptyStrIsNone):
    nameToPrintOnCheck: Optional[str] = Field(
        None,
        description="This is the name that displays on the payee's check. For example, the payee's name, or if there is a nominated payee, In the Estate of followed by the nominated payee's name.",
        max_length=100,
    )


class CreateEmailAddressCommand(PydanticBaseModelEmptyStrIsNone):
    emailAddress: str = Field(
        ...,
        regex="^[\\w!#$%&'*+/=?`{|}~^-]+(?:\\.[\\w!#$%&'*+/=?`{|}~^-]+)*@(?:[a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,6}$",
    )
    extensions: Optional[Dict[str, Any]] = None


class CreateWebMessageCommand(PydanticBaseModelEmptyStrIsNone):
    narrative: str = Field(..., description="Narrative for WebMessage")
    subject: str = Field(..., description="Subject for WebMessage")


class CustomerEmbeddable(PydanticBaseModelEmptyStrIsNone):
    firstName: Optional[str] = Field(None, description="The First Name of the Customer")
    id: Optional[str] = Field(
        None, description="Resource Id of the case for which we want to retrieve the web messages."
    )
    lastName: Optional[str] = Field(None, description="The Last Name of the Customer")


class DayOfWeekRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class DeliveryTypeRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class DependentDeductionsMoneyRequest(PydanticBaseModelEmptyStrIsNone):
    amountMinorUnits: Optional[str] = Field(
        None,
        description="The amount in minor units i.e. without a decimal point. So $100.27 will be 10027",
    )


class DependentDeductionsMoneyResponse(PydanticBaseModelEmptyStrIsNone):
    amountMinorUnits: Optional[int] = Field(
        None,
        description="The amount in minor units i.e. without a decimal point. So $100.27 will be 10027",
    )
    currency: Optional[str] = Field(None, description="The currency used in the Money amount")
    scale: Optional[int] = Field(None, description="The scale used in the Money amount")


class DiagnosisDetails(PydanticBaseModelEmptyStrIsNone):
    causeGrouping: Optional[str] = Field(
        None,
        description="The name of the medical code group in which this medical code is contained.",
        max_length=500,
        min_length=0,
    )
    description: Optional[str] = Field(
        None, description="A description of the medical code.", max_length=500, min_length=0
    )
    diagnosisDetailsId: Optional[str] = Field(
        None,
        description="Business Entity OID",
        example="PE-00012-**********",
        max_length=10,
        min_length=0,
    )
    firstPrimaryCode: Optional[bool] = Field(
        None,
        description="Indicates whether or not this is the first medical code identified with the primary level indicator.",
    )
    groupType: Optional[str] = Field(
        None, description="Specifies the type of the medical code group."
    )
    levelIndicator: Optional[str] = Field(
        None,
        description="The injury code level e.g. primary or secondary assigned to the injury in the context of the claim.",
    )
    medicalCode: Optional[str] = Field(
        None,
        description="The code ID that specifies a particular diagnosis.",
        max_length=20,
        min_length=0,
    )


class DivisionClassLinkEmbeddable(PydanticBaseModelEmptyStrIsNone):
    classId: Optional[str] = Field(None, description="The ID of the selected Class")
    divisionId: Optional[str] = Field(None, description="The ID of the selected Division")


class EFormSummary(PydanticBaseModelEmptyStrIsNone):
    effectiveDateFrom: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    effectiveDateTo: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    eformId: int = Field(..., description="Unique automatically generated Id of an EForm document.")
    eformType: str = Field(
        ...,
        description="The short business description of the document type.",
        max_length=200,
        min_length=0,
    )
    eformTypeId: Optional[str] = Field(
        None, description="Business Entity OID", example="PE-00012-**********"
    )


class EarningsBasisFrequencyRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class EarningsTypeRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class EditAccountDetailCommand(PydanticBaseModelEmptyStrIsNone):
    accountName: Optional[str] = Field(None, description="Name of the account holder")
    accountNo: Optional[str] = Field(None, description="The Account number of the personal account")
    accountType: Optional[AccountTypeRequest] = None
    bankCode: Optional[str] = Field(None, description="The Bank Code")
    currency: Optional[str] = None
    routingNumber: Optional[str] = Field(None, description="Sort code of the Bank")


class EditAdditionalWithholdAmountMoneyRequest(PydanticBaseModelEmptyStrIsNone):
    amountMinorUnits: Optional[str] = Field(
        None,
        description="The amount in minor units i.e. without a decimal point. So $100.27 will be 10027",
    )


class EditAustralianAddressCommand(PydanticBaseModelEmptyStrIsNone):
    buildingName1: Optional[str] = Field(None, description="The name of the building.")
    buildingName2: Optional[str] = Field(None, description="The name of the building continued.")
    dpId: Optional[int] = Field(
        None,
        description="Referred to as DPID this is an 8-digit Australian post address identifier which uniquely identifies each delivery point to which Australia Post delivers mail. ",
    )
    extensions: Optional[Dict[str, Any]] = None
    floorLevelNumber: Optional[str] = Field(
        None, description="The floorLevelNumber must not be more than {max} characters"
    )
    floorLevelType: Optional[AUFloorLevelTypesRequest] = None
    lotNumber: Optional[str] = Field(None, description="The lot number.")
    postalNumber: Optional[str] = Field(None, description="The postal number. ")
    postalNumberPrefix: Optional[str] = Field(
        None,
        description="This refers to the non-numeric portion preceding the Postal Number. EXAMPLE : PO Box B20 where B is the prefix. ",
    )
    postalNumberSuffix: Optional[str] = Field(
        None,
        description="This refers to the non-numeric portion following the Postal Number. EXAMPLE : PO Box 20A where A is the suffix.",
    )
    postalType: Optional[AUPostalTypesRequest] = None
    premiseNoSuffix: Optional[str] = Field(
        None, description="The suffix to apply to the number of the premise."
    )
    premiseNoTo: Optional[int] = Field(
        None,
        description="The high number in a range for the premise. This is used when an address consists of a range of numbers as in 8-10 Lower Pembroke Street where the value 10 would be stored in this field. ",
    )
    premiseNoToSuffix: Optional[str] = Field(
        None, description="The suffix to apply to the number to of the premise. "
    )
    streetSuffix: Optional[AUStreetSuffixesRequest] = None


class EditChequeDetailCommand(PydanticBaseModelEmptyStrIsNone):
    nameToPrintOnCheck: Optional[str] = Field(
        None,
        description="This is the name that displays on the payee's check. For example, the payee's name, or if there is a nominated payee, In the Estate of followed by the nominated payee's name.",
    )


class EditDependentDeductionsMoneyRequest(PydanticBaseModelEmptyStrIsNone):
    amountMinorUnits: Optional[str] = Field(
        None,
        description="The amount in minor units i.e. without a decimal point. So $100.27 will be 10027",
    )


class EditEmailAddressCommand(PydanticBaseModelEmptyStrIsNone):
    emailAddress: str
    extensions: Optional[Dict[str, Any]] = None


class EditEmploymentTypeRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class EditEmploymentWorkStateRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class EditExtraPayTaxRateRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class EditFilingMaritalStatusRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class EditGroupClientUserCommand(PydanticBaseModelEmptyStrIsNone):
    enabled: Optional[bool] = Field(
        None,
        description="An indicator used to determine if this Group Client User is enabled or not. True = enabled / false = disabled.",
    )


class EditKiwiSaverStatusRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class EditLevyExemptionRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class EditMaritalRelationshipStatusRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class EditMedicareLevySurchargeRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class EditNonStdDeductionsMoneyRequest(PydanticBaseModelEmptyStrIsNone):
    amountMinorUnits: Optional[str] = Field(
        None,
        description="The amount in minor units i.e. without a decimal point. So $100.27 will be 10027",
    )


class EditOtherIncomeMoneyRequest(PydanticBaseModelEmptyStrIsNone):
    amountMinorUnits: Optional[str] = Field(
        None,
        description="The amount in minor units i.e. without a decimal point. So $100.27 will be 10027",
    )


class EditPaymentPreferenceAustralianAddressCommand(PydanticBaseModelEmptyStrIsNone):
    buildingName1: Optional[str] = Field(None, description="The name of the building.")
    buildingName2: Optional[str] = Field(None, description="The name of the building continued.")
    dpId: Optional[int] = Field(
        None,
        description="Referred to as DPID this is an 8-digit Australian post address identifier which uniquely identifies each delivery point to which Australia Post delivers mail. ",
    )
    extensions: Optional[Dict[str, Any]] = None
    floorLevelNumber: Optional[str] = Field(
        None, description="The floorLevelNumber must not be more than {max} characters"
    )
    floorLevelType: Optional[AUFloorLevelTypesRequest] = None
    lotNumber: Optional[str] = Field(None, description="The lot number.")
    postalNumber: Optional[str] = Field(None, description="The postal number. ")
    postalNumberPrefix: Optional[str] = Field(
        None,
        description="This refers to the non-numeric portion preceding the Postal Number. EXAMPLE : PO Box B20 where B is the prefix. ",
    )
    postalNumberSuffix: Optional[str] = Field(
        None,
        description="This refers to the non-numeric portion following the Postal Number. EXAMPLE : PO Box 20A where A is the suffix.",
    )
    postalType: Optional[AUPostalTypesRequest] = None
    premiseNoSuffix: Optional[str] = Field(
        None, description="The suffix to apply to the number of the premise."
    )
    premiseNoTo: Optional[int] = Field(
        None,
        description="The high number in a range for the premise. This is used when an address consists of a range of numbers as in 8-10 Lower Pembroke Street where the value 10 would be stored in this field. ",
    )
    premiseNoToSuffix: Optional[str] = Field(
        None, description="The suffix to apply to the number to of the premise. "
    )
    streetSuffix: Optional[AUStreetSuffixesRequest] = None


class EditPregnancyDetailsCommand(PydanticBaseModelEmptyStrIsNone):
    actualDeliveryDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    deliveryType: Optional[DeliveryTypeRequest] = None
    expectedDeliveryDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    pregnancyComplications: Optional[bool] = Field(
        None, description="Indicates whether or not pregnancy complications occurred."
    )


class EditResidentialStatusRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class EditTaxCodeRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class EditTaxOffsetsMoneyRequest(PydanticBaseModelEmptyStrIsNone):
    amountMinorUnits: Optional[str] = Field(
        None,
        description="The amount in minor units i.e. without a decimal point. So $100.27 will be 10027",
    )


class EditTaxTypeRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class EditVersionRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class EditWebMessageCommand(PydanticBaseModelEmptyStrIsNone):
    readByGroupClient: Optional[bool] = Field(
        None, description="Flag to mark first time open details for webMessage"
    )


class EditWithholdingTaxStatusRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class EditWorkPatternDayCommand(PydanticBaseModelEmptyStrIsNone):
    dayOfWeek: DayOfWeekRequest
    hours: int = Field(
        ...,
        description="The number of whole working hours on this day in the work pattern (0-24)",
        ge=0,
    )
    minutes: int = Field(
        ..., description="The number of minutes on this day in the work pattern (0-59)", ge=0
    )
    weekNumber: int = Field(
        ...,
        description="The number of the week in the pattern within which this day occurs (1, 2, 3 or 4)",
        ge=0,
    )


class EligibilityEmbeddable(PydanticBaseModelEmptyStrIsNone):
    lengthOfService: Optional[str] = Field(None, description="Length of service.")
    minimumWorkHours: Optional[str] = Field(None, description="Minimum work hours.")


class EmpLocationCodeRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class Employee(PydanticBaseModelEmptyStrIsNone):
    id: Optional[str] = Field(None, description="Unique identifier for the resource.")
    name: Optional[str] = Field(None, description="Full name of the employee.")


class EmployeeEmbeddableGroupClient(PydanticBaseModelEmptyStrIsNone):
    id: Optional[str] = Field(None, description="Unique identifier for the employee.")
    name: Optional[str] = Field(None, description="Full name of the employee.")


class EmploymentCatRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class EmploymentStatusRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class EmploymentTitleRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class EndPosCodeRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class EndpointPermissionResource(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = Field(
        None, description="A map of links the user has permission to"
    )
    enabled: Optional[bool] = Field(
        None, description="If the endpoint is enabled or not for that user"
    )
    id: Optional[str] = None
    name: Optional[str] = Field(None, description="Name of the endpoint")


class EndpointPermissionResources(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, Any]] = None
    elements: Optional[List[EndpointPermissionResource]] = None
    hasMoreElements: Optional[bool] = Field(
        None,
        description="<P>If the query has a <code>limit</code> set and the actual number of resources that match the query exceeds the limit this will be true if the number of resources is less than or equal to the limit this will be false.</P><P>Note:If the limit query pattern is not used, this value will not be returned.</P>",
    )
    meta: Optional[Dict[str, Any]] = None
    totalSize: Optional[int] = Field(
        None,
        description="<P>The <code>totalSize</code> is the total number of resources that match the query. This can be greater than the page size in a paginated query if there are more queries.</P><P>Note:This field will not be returned if the query has a <code>limit</code> set for it.</P>",
    )


class EnumCommand(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = None
    name: Optional[str] = None


class EnumInstanceSummary(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = Field(
        None,
        description="Links to related objects (in this case: enumInstance)",
        example="{'enumInstance': ''}",
    )
    dOrder: Optional[int] = None
    endDate: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    fullId: Optional[int] = Field(
        None, description="Enum instance fullId, corresponds to <code>TaEnum.fullId</code>"
    )
    id: Optional[str] = Field(None, description="Enum instance ID")
    name: Optional[str] = Field(None, description="Enum instance name")
    retired: Optional[bool] = Field(
        None,
        description="If <code>true</code> retired Instances only are returned,if <code>false</code>(or not specified) non-retired instances only are returned",
    )
    startDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )


class EnumSubset(PydanticBaseModelEmptyStrIsNone):
    instances: Optional[List[EnumInstanceSummary]] = Field(
        None, description="List of the instances that belongs to this Subset "
    )
    name: Optional[str] = Field(None, description="Enum subset name")


class EnumSubsetInfoGroupClient(PydanticBaseModelEmptyStrIsNone):
    description: Optional[str] = None
    editable: Optional[bool] = None
    name: Optional[str] = None


class EnumSubsetSummary(PydanticBaseModelEmptyStrIsNone):
    name: Optional[str] = Field(None, description="Enum subset name")


class EpisodePeriodDurationBasisRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class ErrorModel(PydanticBaseModelEmptyStrIsNone):
    correlationId: Optional[str] = None
    error: str
    errorDetail: Optional[str] = None
    stacktrace: Optional[str] = None


class ErrorSource(PydanticBaseModelEmptyStrIsNone):
    parameter: Optional[str] = None
    pointer: Optional[str] = None


class ErrorSourceGroupClient(PydanticBaseModelEmptyStrIsNone):
    parameter: Optional[str] = None
    pointer: Optional[str] = None


class ExceptionMeta(PydanticBaseModelEmptyStrIsNone):
    X_Client_ID: Optional[str] = Field(None, alias="X-Client-ID")
    X_Client_Version: Optional[str] = Field(None, alias="X-Client-Version")
    X_Correlation_ID: Optional[str] = Field(None, alias="X-Correlation-ID")
    headers: Optional[Dict[str, str]] = None
    nonDisplayableMessage: Optional[str] = None
    stackTrace: Optional[str] = None


class ExceptionMetaGroupClient(PydanticBaseModelEmptyStrIsNone):
    X_Client_ID: Optional[str] = Field(None, alias="X-Client-ID")
    X_Client_Version: Optional[str] = Field(None, alias="X-Client-Version")
    X_Correlation_ID: Optional[str] = Field(None, alias="X-Correlation-ID")
    headers: Optional[Dict[str, str]] = None
    nonDisplayableMessage: Optional[str] = None
    stackTrace: Optional[str] = None


class ExtraPayTaxRateRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class FilingMaritalStatusRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class GenderRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class GroupClientAccommodationDetail(PydanticBaseModelEmptyStrIsNone):
    acceptedDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    accommodationCategory: Optional[str] = Field(
        None, description="The Category that identifies this association."
    )
    accommodationDescription: Optional[str] = Field(
        None, description="The description of this accommodation."
    )
    accommodationType: Optional[str] = Field(
        None, description="The Type that identifies this accommodation"
    )
    createDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    endDate: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    implementedDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    source: Optional[str] = Field(
        None,
        description="Has the accommodation been resquested by employee or proposed by employer?",
    )


class GroupClientEditScheduledDayCommand(PydanticBaseModelEmptyStrIsNone):
    scheduledTime: Optional[int] = Field(
        None, description="Scheduled time in minutes.", ge=0, le=1440
    )


class GroupPolicyClassEmbeddable(PydanticBaseModelEmptyStrIsNone):
    id: Optional[str] = Field(None, description="ID used to identify the group policy class")
    name: Optional[str] = Field(None, description="The name of the selected group policy class")


class GroupPolicyDivisionEmbeddable(PydanticBaseModelEmptyStrIsNone):
    id: Optional[str] = Field(None, description="ID used to identify the group policy division")
    name: Optional[str] = Field(None, description="The name of the selected group policy division")


class GroupPolicyMemberDetail(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    availablePolicyClasses: Optional[List[GroupPolicyClassEmbeddable]] = Field(
        None,
        description="The ids and names of the classes that are available on the group policy (sorted by name).",
    )
    availablePolicyDivisions: Optional[List[GroupPolicyDivisionEmbeddable]] = Field(
        None,
        description="The ids and names of the divisions that are available on the group policy (sorted by name).",
    )
    divisionClassLinks: Optional[List[DivisionClassLinkEmbeddable]] = Field(
        None,
        description="The list of class and division ids that are linked to the claimant member details for the group policy (sorted by division name and class name).",
    )
    id: Optional[str] = Field(None, description="The claim member details ID.")
    memberNumber: Optional[str] = Field(
        None, description="The Claimant's member number for the group policy"
    )
    policyReference: Optional[str] = Field(None, description="The policy reference")


class GroupPolicyMemberDetails(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, Any]] = None
    elements: Optional[List[GroupPolicyMemberDetail]] = None
    hasMoreElements: Optional[bool] = Field(
        None,
        description="<P>If the query has a <code>limit</code> set and the actual number of resources that match the query exceeds the limit this will be true if the number of resources is less than or equal to the limit this will be false.</P><P>Note:If the limit query pattern is not used, this value will not be returned.</P>",
    )
    meta: Optional[Dict[str, Any]] = None
    totalSize: Optional[int] = Field(
        None,
        description="<P>The <code>totalSize</code> is the total number of resources that match the query. This can be greater than the page size in a paginated query if there are more queries.</P><P>Note:This field will not be returned if the query has a <code>limit</code> set for it.</P>",
    )


class IdentificationNumberTypeRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class JobStrenuousRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class KiwiSaverStatusRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class LeavePlanType(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = None
    leavePlanName: Optional[str] = None


class LeaveRequest(PydanticBaseModelEmptyStrIsNone):
    approvalReason: Optional[str] = Field(None, description="Reason for approval of a request")
    decisionStatus: Optional[str] = Field(None, description="Leave request decision status")
    denialReason: Optional[str] = Field(None, description="Reason for denial of a request")
    id: Optional[str] = Field(
        None, description="Business Entity OID", example="PE-00012-**********"
    )
    qualifier1: Optional[str] = Field(None, description="The leave request first qualifier")
    qualifier2: Optional[str] = Field(None, description="The leave request second qualifier")
    reasonName: Optional[str] = Field(None, description="The leave request reason")


class LevyExemptionRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class LifeExpectancyRequestGroupClient(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class LinkCommand(PydanticBaseModelEmptyStrIsNone):
    id: str
    relName: str
    resource: str


class MaritalRelationshipStatusRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class MaritalStatusRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class MedicareLevySurchargeRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class MemberGroupEmbeddable(PydanticBaseModelEmptyStrIsNone):
    id: Optional[str] = Field(None, description="The Id of the member group")
    name: Optional[str] = Field(
        None,
        description="Represents a way in which the insurable member community may be divided, for example, location or departmental",
    )
    value: Optional[str] = Field(None, description="The name of the member group")


class MemberGroupEmbeddableGroupClient(PydanticBaseModelEmptyStrIsNone):
    id: Optional[str] = Field(None, description="The Id of the member group")
    name: Optional[str] = Field(
        None,
        description="Represents a way in which the insurable member community may be divided, for example, location or departmental",
    )
    value: Optional[str] = Field(None, description="The name of the member group")


class ModelEnum(PydanticBaseModelEmptyStrIsNone):
    domainName: str = Field(..., description="Domain name.", max_length=100, min_length=0)
    instanceValue: str = Field(..., description="Enum instance name.", max_length=100, min_length=0)


class MonthlyBasisEarningAmountsRequest(PydanticBaseModelEmptyStrIsNone):
    bonuses: Optional[Decimal] = Field(None, description="Money amount format", example="1500.50")
    commissions: Optional[Decimal] = Field(
        None, description="Money amount format", example="1500.50"
    )
    earningsAmount: Decimal = Field(..., description="Money amount format", example="1500.50")
    pensionContribution: Optional[Decimal] = Field(
        None, description="Money amount format", example="1500.50"
    )
    shiftPay: Optional[Decimal] = Field(None, description="Money amount format", example="1500.50")


class MonthlyBasisEarningAmountsResponse(PydanticBaseModelEmptyStrIsNone):
    bonuses: Optional[Decimal] = Field(None, description="Money amount format", example="1500.50")
    commissions: Optional[Decimal] = Field(
        None, description="Money amount format", example="1500.50"
    )
    earningsAmount: Decimal = Field(..., description="Money amount format", example="1500.50")
    earningsAmountAsWeekly: Optional[Decimal] = Field(
        None, description="Money amount format", example="1500.50"
    )
    pensionContribution: Optional[Decimal] = Field(
        None, description="Money amount format", example="1500.50"
    )
    shiftPay: Optional[Decimal] = Field(None, description="Money amount format", example="1500.50")
    totalEarnings: Optional[Decimal] = Field(
        None, description="Money amount format", example="1500.50"
    )


class NationalityRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class NewDiagnosisDetails(PydanticBaseModelEmptyStrIsNone):
    levelIndicator: Optional[str] = Field(
        None,
        description="The injury code level e.g. primary or secondary assigned to the injury in the context of the claim.",
    )
    medicalCode: Optional[str] = Field(
        None,
        description="The code ID that specifies a particular diagnosis.",
        max_length=20,
        min_length=0,
    )


class NominatedPayeeTypeRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class NonStdDeductionsMoneyRequest(PydanticBaseModelEmptyStrIsNone):
    amountMinorUnits: Optional[str] = Field(
        None,
        description="The amount in minor units i.e. without a decimal point. So $100.27 will be 10027",
    )


class NonStdDeductionsMoneyResponse(PydanticBaseModelEmptyStrIsNone):
    amountMinorUnits: Optional[int] = Field(
        None,
        description="The amount in minor units i.e. without a decimal point. So $100.27 will be 10027",
    )
    currency: Optional[str] = Field(None, description="The currency used in the Money amount")
    scale: Optional[int] = Field(None, description="The scale used in the Money amount")


class NotificationCase(PydanticBaseModelEmptyStrIsNone):
    caseReference: Optional[str] = Field(None, description="Business identifier for this case.")
    id: Optional[str] = Field(
        None,
        description="Resource Id of the notification case this accommodation case belongs to if one exists.",
    )


class LinkshdrItem(PydanticBaseModelEmptyStrIsNone):
    params: Optional[Dict[str, str]] = None
    rel: Optional[str] = None
    rels: Optional[List[str]] = None
    title: Optional[str] = None
    type: Optional[str] = None
    uri: Optional[AnyUrl] = None
    uriBuilder: Optional[Dict[str, Any]] = None


class NotificationCaseEmbeddableGroupClient(PydanticBaseModelEmptyStrIsNone):
    caseNumber: Optional[str] = Field(None, description="Business identifier for this case.")
    id: Optional[str] = Field(
        None,
        description="Resource Id of the notification case this accommodation case belongs to if one exists.",
    )


class NotificationDetails(PydanticBaseModelEmptyStrIsNone):
    customerNo: str = Field(
        ...,
        description="The unique customer number of the customer for whom the notification is being created. This party will be given the Requester role on the notification created.",
    )
    description: Optional[str] = Field(
        None,
        description="An optional description to capture extra details about the notification. This will be output to an eForm that is associated with the notification.",
    )
    lastWorkingDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    notificationReason: str = Field(
        ...,
        description="The reason for creating the notification, for example, accident or illness. This value must correspond to an instance of the enum domain id 290.",
    )
    notifierFirstName: str = Field(..., description="The first name of the notifier.")
    notifierLastName: str = Field(..., description="The last name of the notifier.")
    notifierPhone: Optional[str] = Field(
        None, description="An optional contact number for the notifier."
    )


class NotificationReason(PydanticBaseModelEmptyStrIsNone):
    pass


class OccupationQualifierEmbeddable(PydanticBaseModelEmptyStrIsNone):
    id: Optional[str] = Field(None, description="Occupation qualifier internal reference.")
    qualifierDescription: Optional[str] = Field(
        None, description="Occupation qualifier description."
    )


class OtherIncomeMoneyRequest(PydanticBaseModelEmptyStrIsNone):
    amountMinorUnits: Optional[str] = Field(
        None,
        description="The amount in minor units i.e. without a decimal point. So $100.27 will be 10027",
    )


class OtherIncomeMoneyResponse(PydanticBaseModelEmptyStrIsNone):
    amountMinorUnits: Optional[int] = Field(
        None,
        description="The amount in minor units i.e. without a decimal point. So $100.27 will be 10027",
    )
    currency: Optional[str] = Field(None, description="The currency used in the Money amount")
    scale: Optional[int] = Field(None, description="The scale used in the Money amount")


class OutstandingInformationData(PydanticBaseModelEmptyStrIsNone):
    documentId: Optional[str] = Field(
        None, description="Document id to link to outstanding requirement. "
    )
    informationType: str = Field(..., description="The type of the outstanding item")


class OutstandingInformationItem(PydanticBaseModelEmptyStrIsNone):
    infoReceived: Optional[bool] = Field(
        None,
        description="Indicates whether any information has been received for the outstanding information item.<BR>For instance, a document could be uploaded and linked to an outstanding requirement, in which case the value will be TRUE, however the outstanding requirement will remain OPEN until a back office user validates that the uploaded document is actually what was required.<BR>This flag will always be FALSE for a evidence result which is either pending or received but not satisfied; it will be TRUE for a received evidence result which is received and satisfied.<BR>If the element represents a combination of outstanding requirements and evidence results, the flag will be FALSE if at least one of these elements returns FALSE.",
    )
    informationType: Optional[str] = Field(
        None,
        description="The type of information item required.The type is intended to be a user-friendly value, that can be exposed on the portal and will mean something to the portal user.",
        max_length=350,
        min_length=0,
    )
    sourcePartyId: Optional[str] = Field(
        None, description="Id of the party that is expected to provide the outstanding item."
    )
    sourcePartyName: Optional[str] = Field(
        None, description="Name of the party that is expected to provide the outstanding item."
    )
    uploadCaseNumber: Optional[str] = Field(
        None,
        description="If the outstanding item is required by multiple cases, the upload Case Number is the id of the lowest case in the hierarchy which is the common parent of all such cases; If the outstanding item is required by one case only, the upload Case Number is the id of that case.Given a caseId, different outstanding items can a have different upload Case Number depending on which case(s) requires which outstanding items.",
        max_length=254,
        min_length=0,
    )


class ParticipantPlan(PydanticBaseModelEmptyStrIsNone):
    approvedTime: Optional[float] = Field(
        None,
        description="Employee total approved leave as per the leave plan calculation for the given period.  The unit of time is provided by the time basis.",
    )
    id: Optional[str] = Field(None, description="A unique identifier for the leave plan.")
    name: Optional[str] = Field(None, description="The name of the leave plan.")
    pendingTime: Optional[float] = Field(
        None,
        description="Employee total pending leave as per the leave plan calculation for the given period.  The unit of time is provided by the time basis.",
    )
    timeBasis: Optional[str] = Field(
        None,
        description="The basis used to record time unit of approved, pending and entitlement. It may be Minutes, Hours, Days, Weeks, Years or Months. This maps to enum TimeUnits(domain id 6757).",
        max_length=10,
        min_length=0,
    )


class PartyTypeRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class PatternStatusRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class PaymentDayRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class PaymentMethodRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class PaymentPeriodRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class PeriodDecisionsEmployee(PydanticBaseModelEmptyStrIsNone):
    customerNo: Optional[str] = Field(None, description="The Customer Id of Employee")
    id: Optional[str] = Field(
        None,
        description="This is deprecated, this is not a usable id field. If wish to use customerNo, use the field customerNo instead",
    )
    name: Optional[str] = Field(None, description="Name of the employee")


class PermissionGroupResource(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = Field(
        None, description="A map of links the user has permission to"
    )
    id: Optional[str] = None
    name: Optional[str] = Field(None, description="Name of the endpoint")


class PermissionGroupResources(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, Any]] = None
    elements: Optional[List[PermissionGroupResource]] = None
    hasMoreElements: Optional[bool] = Field(
        None,
        description="<P>If the query has a <code>limit</code> set and the actual number of resources that match the query exceeds the limit this will be true if the number of resources is less than or equal to the limit this will be false.</P><P>Note:If the limit query pattern is not used, this value will not be returned.</P>",
    )
    meta: Optional[Dict[str, Any]] = None
    totalSize: Optional[int] = Field(
        None,
        description="<P>The <code>totalSize</code> is the total number of resources that match the query. This can be greater than the page size in a paginated query if there are more queries.</P><P>Note:This field will not be returned if the query has a <code>limit</code> set for it.</P>",
    )


class Policy(PydanticBaseModelEmptyStrIsNone):
    referenceNo: str = Field(
        ...,
        description="Unique alphanumeric policy reference from the relevant policy admin system.",
        max_length=50,
        min_length=0,
    )


class ReevaluateActualAbsencePeriodCommand(PydanticBaseModelEmptyStrIsNone):
    pass


class RequestedEpisodicLeaveDetails(PydanticBaseModelEmptyStrIsNone):
    duration: Optional[int] = Field(
        None, description="The duration of each period of absence, for example, 3 (Hours)."
    )
    durationBasis: Optional[str] = Field(
        None,
        description="The unit of the duration of the period of absence, for example, Minutes, Hours, Days. This maps to a sub-set of the Enum TimeUnits (domain id 6757).",
    )
    frequency: Optional[int] = Field(
        None,
        description="How often individual periods of absence are taken, for example, 2 (times)",
    )
    frequencyInterval: Optional[int] = Field(
        None,
        description="The interval of time between the individual periods of absence, for example, 1 (Weeks).",
    )
    frequencyIntervalBasis: Optional[str] = Field(
        None,
        description="The unit of the interval between the periods of absence, for example, Minutes, Hours, Days. This maps to a sub-set of the Enum TimeUnits (domain id 6757)",
    )


class ResidentialStatusRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class RestrictionsLimitations(PydanticBaseModelEmptyStrIsNone):
    restrictionEffectiveDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    restrictions: Optional[str] = Field(
        None,
        description="Specifies the disabilities/restrictions experienced by the injured party as a result of the claim.",
    )


class RpcResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    elements: Optional[List[Dict[str, Any]]] = None
    meta: Optional[Dict[str, Any]] = None


class SharedEntitlementPlan(PydanticBaseModelEmptyStrIsNone):
    approvedTime: Optional[float] = Field(
        None,
        description="Employee total approved leave as per the leave plan calculation for the given period.  The unit of time is provided by the time basis.",
    )
    id: Optional[str] = Field(None, description="A unique identifier for the leave plan.")
    name: Optional[str] = Field(None, description="The name of the leave plan.")
    participantPlans: Optional[List[ParticipantPlan]] = Field(
        None,
        description="A list of leave plans associated with the given shared entitlement tracker leave plan. The leave plan being queried is not included.",
    )
    pendingTime: Optional[float] = Field(
        None,
        description="Employee total pending leave as per the leave plan calculation for the given period.  The unit of time is provided by the time basis.",
    )
    timeBasis: Optional[str] = Field(
        None,
        description="The basis used to record time unit of approved, pending and entitlement. It may be Minutes, Hours, Days, Weeks, Years or Months. This maps to enum TimeUnits(domain id 6757).",
        max_length=10,
        min_length=0,
    )
    timeEntitlement: Optional[float] = Field(
        None,
        description="The time that is available to an employee who has met the eligibility requirements as per the leave plan calculation period.  The unit of time is provided by the time basis.",
    )
    timeWithinPeriod: Optional[int] = Field(
        None,
        description="The time frame within which the employee must avail of the entitled leave. For example, an employee may be entitled to 6 weeks leave within 12 months period.  The unit of time is provided by the time within period basis.",
        le=9999,
    )
    timeWithinPeriodBasis: Optional[str] = Field(
        None,
        description="The basis used to record time unit of time within a period. It may be Minutes, Hours, Days, Weeks or Months. This maps to enum LengthBasis(domain id 6758).",
        max_length=10,
        min_length=0,
    )


class SocialSecurityBenefitResponse(PydanticBaseModelEmptyStrIsNone):
    authorityConsentDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    awardInformationReceivedDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    benefitAmount: Optional[Decimal] = Field(
        None, description="Money amount format", example="1500.50"
    )
    claimStatus: Optional[str] = Field(
        None,
        description="The status of the insureds application with the Social Security Administration.",
    )
    disabilityBenefitsEligibleDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    earlyRetirementBenefitsEligibleDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    insuranceEndDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    reimbursementAgreementReceived: Optional[bool] = Field(
        None, description="Indicates that the reimbursement Agreement has been received."
    )
    reimbursementAgreementSignedDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    socialSecurityBenefitId: Optional[str] = Field(
        None, description="The unique identification number(oid)."
    )


class SocialSecurityDecisionResponse(PydanticBaseModelEmptyStrIsNone):
    applicationDate: Optional[datetime] = Field(
        None, description="ISO 8601 date time format", example="1999-12-31T23:59:59Z"
    )
    approvalType: Optional[str] = Field(
        None, description="The level of decision made for the social security decision"
    )
    decision: Optional[str] = Field(
        None, description="This is the actual decision made for the social security decision"
    )
    decisionDate: Optional[datetime] = Field(
        None, description="ISO 8601 date time format", example="1999-12-31T23:59:59Z"
    )
    socialSecurityDecisionId: str = Field(
        ...,
        description="The unique identifier for the social security",
        max_length=32,
        min_length=0,
    )


class Direction(Enum):
    ASC = "ASC"
    DESC = "DESC"


class SortOrderItem(PydanticBaseModelEmptyStrIsNone):
    columnName: Optional[str] = None
    direction: Optional[Direction] = None


class SortOrderItemGroupClient(PydanticBaseModelEmptyStrIsNone):
    columnName: Optional[str] = None
    direction: Optional[Direction] = None


class StandardHourlyRateMoneyRequest(PydanticBaseModelEmptyStrIsNone):
    amountMinorUnits: Optional[str] = Field(
        None,
        description="The amount in minor units i.e. without a decimal point. So $100.27 will be 10027",
    )


class StandardHourlyRateMoneyResponse(PydanticBaseModelEmptyStrIsNone):
    amountMinorUnits: Optional[int] = Field(
        None,
        description="The amount in minor units i.e. without a decimal point. So $100.27 will be 10027",
    )
    currency: Optional[str] = Field(None, description="The currency used in the Money amount")
    scale: Optional[int] = Field(None, description="The scale used in the Money amount")


class StateResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = Field(
        None, description="Links to the enum domain and enum instance for the API"
    )
    domainId: Optional[int] = Field(None, description="The <code>domain id</code> of the enum")
    domainName: Optional[str] = Field(None, description="The enum domain name")
    fullId: Optional[int] = Field(None, description="The <code>instance id</code> of the enum")
    name: Optional[str] = Field(None, description="The enum instance name")


class TaEnumInstanceSwagger(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[str] = Field(None, description="The <code>instance id</code> of the enum")
    name: Optional[str] = Field(None, description="The enum instance name")


class TaEnumInstanceSwaggerGroupClient(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[str] = Field(None, description="The <code>instance id</code> of the enum")
    name: Optional[str] = Field(None, description="The enum instance name")


class TaEnumTaEnumTypeGroupClient(PydanticBaseModelEmptyStrIsNone):
    active: Optional[bool] = None
    alphaSort: Optional[bool] = None
    defaultFirst: Optional[bool] = None
    domainDescription: Optional[str] = None
    domainId: Optional[int] = None
    domainName: Optional[str] = None
    dorder: Optional[int] = None
    editable: Optional[bool] = None
    endDate: Optional[datetime] = Field(
        None, description="ISO 8601 date time format", example="1999-12-31T23:59:59Z"
    )
    fullId: Optional[int] = None
    id: Optional[int] = None
    name: Optional[str] = None
    nameAttribute: Optional[str] = None
    retired: Optional[bool] = None
    startDate: Optional[datetime] = Field(
        None, description="ISO 8601 date time format", example="1999-12-31T23:59:59Z"
    )
    subsetInfo: Optional[List[EnumSubsetInfoGroupClient]] = None
    subsets: Optional[List[str]] = None


class TaxCodeRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class TaxCodeResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class TaxOffsetsMoneyRequest(PydanticBaseModelEmptyStrIsNone):
    amountMinorUnits: Optional[str] = Field(
        None,
        description="The amount in minor units i.e. without a decimal point. So $100.27 will be 10027",
    )


class TaxOffsetsMoneyResponse(PydanticBaseModelEmptyStrIsNone):
    amountMinorUnits: Optional[int] = Field(
        None,
        description="The amount in minor units i.e. without a decimal point. So $100.27 will be 10027",
    )
    currency: Optional[str] = Field(None, description="The currency used in the Money amount")
    scale: Optional[int] = Field(None, description="The scale used in the Money amount")


class TaxTypeRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class TaxTypeResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class TimeApprovedBasisResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class TimeZoneRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class TimeZoneResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class TitleRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class TitleResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class TypeOfSurgeryRequestGroupClient(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class TypeOfSurgeryResponseGroupClient(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwaggerGroupClient]] = None
    name: str = Field(..., description="The enum instance name")


class TypeResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class UserErrorModel(PydanticBaseModelEmptyStrIsNone):
    error: str


class ValidationMessageModel(PydanticBaseModelEmptyStrIsNone):
    validationMessage: str


class VersionRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class VersionResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class WebMessageCaseEmbeddable(PydanticBaseModelEmptyStrIsNone):
    caseReference: Optional[str] = Field(None, description="Business identifier for this case.")
    caseType: Optional[str] = Field(None, description="Specific type of the case")
    id: Optional[str] = Field(
        None,
        description="Resource Id of the case for which we want to perform action on the web messages.",
    )


class WebMessageResource(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    case: Optional[WebMessageCaseEmbeddable] = None
    contactDateTime: Optional[date] = Field(
        None, description="Timestmp of when the web message was created"
    )
    customer: Optional[CustomerEmbeddable] = None
    id: Optional[str] = Field(None, description="Resource Id of Web Message")
    msgOriginatesFromPortal: Optional[bool] = Field(
        None,
        description="A flag indicating whether the message originates from the customer on the portal or not (in which case the message originating from the insurer)",
    )
    narrative: Optional[str] = Field(None, description="Narrative for WebMessage")
    readByGroupClient: Optional[bool] = Field(
        None,
        description="A flag indicating whether the message has been read or not. All msgs which orginate from Portal (i.e. messages from the customer) will be automatically marked as read when created. All messages which do not originate from the portal (from the insurer to the customer) will be marked as read once the customer has read the message",
    )
    rootCase: Optional[WebMessageCaseEmbeddable] = None
    subject: Optional[str] = Field(None, description="Subject for WebMessage")


class WebMessages(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, Any]] = None
    elements: Optional[List[WebMessageResource]] = None
    hasMoreElements: Optional[bool] = Field(
        None,
        description="<P>If the query has a <code>limit</code> set and the actual number of resources that match the query exceeds the limit this will be true if the number of resources is less than or equal to the limit this will be false.</P><P>Note:If the limit query pattern is not used, this value will not be returned.</P>",
    )
    meta: Optional[Dict[str, Any]] = None
    totalSize: Optional[int] = Field(
        None,
        description="<P>The <code>totalSize</code> is the total number of resources that match the query. This can be greater than the page size in a paginated query if there are more queries.</P><P>Note:This field will not be returned if the query has a <code>limit</code> set for it.</P>",
    )


class WeekBasedWorkPatternTypeResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class WeeklyBasisEarningAmountsRequest(PydanticBaseModelEmptyStrIsNone):
    overtimeHourlyRate: Optional[Decimal] = Field(
        None, description="Money amount format", example="1500.50"
    )
    overtimeHours: Optional[float] = Field(
        None, description="Number of overtime hours in a working week or bi-week."
    )
    shiftAllowance: Optional[Decimal] = Field(
        None, description="Money amount format", example="1500.50"
    )
    shiftBasis: Optional[str] = Field(
        None,
        description="Basis of weekly or bi-weekly shift earnings - either an additional hourly allowance or an additional amount.",
    )
    shiftHours: Optional[float] = Field(
        None, description="Number of shift hours in a working week or bi-week."
    )
    standardHourRate: Optional[Decimal] = Field(
        None, description="Money amount format", example="1500.50"
    )
    standardHours: Optional[float] = Field(
        None, description="The number of hours in a standard working week or bi-week."
    )
    statutoryBasis: Optional[str] = Field(
        None,
        description="Additional weekly or bi-weekly vacation or statutory entitlements type. It may be an amount or percentage of regular hour.",
    )
    statutoryValue: Optional[float] = Field(
        None,
        description="Additional weekly or bi-weekly vacation or statutory entitlement amount or percentage of regular hours.",
    )
    totalAmountOverridden: bool = Field(
        ...,
        description="This attribute indicates if the total weekly or bi-weekly amount has been manually overridden.",
    )
    totalEarnings: Optional[Decimal] = Field(
        None, description="Money amount format", example="1500.50"
    )


class WeeklyBasisEarningAmountsResponse(PydanticBaseModelEmptyStrIsNone):
    overtimeEarnings: Optional[Decimal] = Field(
        None, description="Money amount format", example="1500.50"
    )
    overtimeHourlyRate: Optional[Decimal] = Field(
        None, description="Money amount format", example="1500.50"
    )
    overtimeHours: Optional[float] = Field(
        None, description="Number of overtime hours in a working week or bi-week."
    )
    shiftAllowance: Optional[Decimal] = Field(
        None, description="Money amount format", example="1500.50"
    )
    shiftBasis: Optional[str] = Field(
        None,
        description="Basis of weekly or bi-weekly shift earnings - either an additional hourly allowance or an additional amount.",
    )
    shiftEarnings: Optional[Decimal] = Field(
        None, description="Money amount format", example="1500.50"
    )
    shiftHours: Optional[float] = Field(
        None, description="Number of shift hours in a working week or bi-week."
    )
    standardEarnings: Optional[Decimal] = Field(
        None, description="Money amount format", example="1500.50"
    )
    standardHourRate: Optional[Decimal] = Field(
        None, description="Money amount format", example="1500.50"
    )
    standardHours: Optional[float] = Field(
        None, description="The number of hours in a standard working week or bi-week."
    )
    statutoryBasis: Optional[str] = Field(
        None,
        description="Additional weekly or bi-weekly vacation or statutory entitlements type. It may be an amount or percentage of regular hour.",
    )
    statutoryEarnings: Optional[Decimal] = Field(
        None, description="Money amount format", example="1500.50"
    )
    statutoryValue: Optional[float] = Field(
        None,
        description="Additional weekly or bi-weekly vacation or statutory entitlement amount or percentage of regular hours.",
    )
    totalAmountOverridden: bool = Field(
        ...,
        description="This attribute indicates if the total weekly or bi-weekly amount has been manually overridden.",
    )
    totalEarnings: Optional[Decimal] = Field(
        None, description="Money amount format", example="1500.50"
    )


class WithholdingTaxStatusRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class WithholdingTaxStatusResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class WorkPatternDayOfWeekResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class WorkPatternDayResponse(PydanticBaseModelEmptyStrIsNone):
    dayOfWeek: Optional[WorkPatternDayOfWeekResponse] = None
    hours: Optional[int] = Field(
        None, description="The number of whole working hours on this day in the work pattern (0-24)"
    )
    minutes: Optional[int] = Field(
        None, description="The number of minutes on this day in the work pattern (0-59)"
    )
    weekNumber: Optional[int] = Field(
        None,
        description="The number of the week in the pattern within which this day occurs (1, 2, 3 or 4)",
    )


class WorkPatternTypeRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class WorkPatternTypeResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class WorkWeekStartsRequest(PydanticBaseModelEmptyStrIsNone):
    fullId: Optional[int] = Field(
        None,
        description="The <code>instance id</code> of the enum. If this attribute is specified, the <code>name</code> attribute doesn't need to be.",
    )
    name: Optional[str] = Field(None, description="The enum instance name")


class WorkWeekStartsResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class WriteDivisionClassLinkCommand(PydanticBaseModelEmptyStrIsNone):
    classId: Optional[str] = Field(None, description="The ID of the selected Class")
    divisionId: Optional[str] = Field(None, description="The ID of the selected Division")


class AUFloorLevelTypesResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class AUPostalTypesResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class AUStreetSuffixesResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class AbsenceCaseEventResource(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    adminGroup: Optional[str] = Field(
        None, description="Admin group on the case when the decision is taken."
    )
    employee: Optional[AbsenceCaseEventEmployeeEmbeddable] = None
    eventDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    eventName: Optional[str] = Field(
        None, description="Name of the event/decision taken on the Absence case."
    )
    id: Optional[str] = Field(None, description="Unique id of the absence case event record.")
    notificationCase: Optional[AbsenceCaseEventNotificationCaseEmbeddable] = None
    notificationReason: Optional[NotificationReason] = None


class AbsenceCaseEventResources(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, Any]] = None
    elements: Optional[List[AbsenceCaseEventResource]] = None
    hasMoreElements: Optional[bool] = Field(
        None,
        description="<P>If the query has a <code>limit</code> set and the actual number of resources that match the query exceeds the limit this will be true if the number of resources is less than or equal to the limit this will be false.</P><P>Note:If the limit query pattern is not used, this value will not be returned.</P>",
    )
    meta: Optional[Dict[str, Any]] = None
    totalSize: Optional[int] = Field(
        None,
        description="<P>The <code>totalSize</code> is the total number of resources that match the query. This can be greater than the page size in a paginated query if there are more queries.</P><P>Note:This field will not be returned if the query has a <code>limit</code> set for it.</P>",
    )


class ClosureReasons(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwaggerGroupClient]] = None
    name: str = Field(..., description="The enum instance name")


class Limitations(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwaggerGroupClient]] = None
    name: str = Field(..., description="The enum instance name")


class AccommodationCategoryResponseGroupClient(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwaggerGroupClient]] = None
    name: str = Field(..., description="The enum instance name")


class NotAccommodatedReasons(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwaggerGroupClient]] = None
    name: str = Field(..., description="The enum instance name")


class AccommodationSourceResponseGroupClient(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwaggerGroupClient]] = None
    name: str = Field(..., description="The enum instance name")


class AccountTypeResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class AltEmploymentCatResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class AltRoleSchema(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class AltStatusSchema(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class ApiError(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    code: Optional[str] = None
    detail: Optional[str] = None
    id: Optional[str] = None
    meta: Optional[ExceptionMeta] = None
    source: Optional[ErrorSource] = None
    status: Optional[str] = None
    title: Optional[str] = None


class ApiErrorGroupClient(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    code: Optional[str] = None
    detail: Optional[str] = None
    id: Optional[str] = None
    meta: Optional[ExceptionMetaGroupClient] = None
    source: Optional[ErrorSourceGroupClient] = None
    status: Optional[str] = None
    title: Optional[str] = None


class AustralianAddressEmbeddable(PydanticBaseModelEmptyStrIsNone):
    buildingName1: Optional[str] = Field(None, description="The name of the building.")
    buildingName2: Optional[str] = Field(None, description="The name of the building continued.")
    dpId: Optional[int] = Field(
        None,
        description="Referred to as DPID this is an 8-digit Australian post address identifier which uniquely identifies each delivery point to which Australia Post delivers mail. ",
    )
    extensions: Optional[Dict[str, Any]] = None
    floorLevelNumber: Optional[str] = Field(
        None, description="The floorLevelNumber must not be more than {max} characters"
    )
    floorLevelType: Optional[AUFloorLevelTypesResponse] = None
    lotNumber: Optional[str] = Field(None, description="The lot number.")
    postalNumber: Optional[str] = Field(None, description="The postal number. ")
    postalNumberPrefix: Optional[str] = Field(
        None,
        description="This refers to the non-numeric portion preceding the Postal Number. EXAMPLE : PO Box B20 where B is the prefix. ",
    )
    postalNumberSuffix: Optional[str] = Field(
        None,
        description="This refers to the non-numeric portion following the Postal Number. EXAMPLE : PO Box 20A where A is the suffix.",
    )
    postalType: Optional[AUPostalTypesResponse] = None
    premiseNoSuffix: Optional[str] = Field(
        None, description="The suffix to apply to the number of the premise."
    )
    premiseNoTo: Optional[int] = Field(
        None,
        description="The high number in a range for the premise. This is used when an address consists of a range of numbers as in 8-10 Lower Pembroke Street where the value 10 would be stored in this field. ",
    )
    premiseNoToSuffix: Optional[str] = Field(
        None, description="The suffix to apply to the number to of the premise. "
    )
    streetSuffix: Optional[AUStreetSuffixesResponse] = None


class CancelLeavePeriodsDetails(PydanticBaseModelEmptyStrIsNone):
    additionalNotes: Optional[str] = Field(
        None, description="Additional notes for leave period change request."
    )
    changeRequestPeriods: List[CancellationPeriod] = Field(
        ...,
        description="List of periods for leave period change request.",
        max_items=20,
        min_items=1,
    )
    reason: str = Field(
        ...,
        description="Reason for the leave period change request (EnumDomainId=6870). For example, valid reasons for requesting the removal of one or more leave periods are 'Employee Requested Removal' and 'Created in Error'. Valid reasons for requesting the addition of a leave period are 'Add time for identical Absence Reason', 'Add time for different Absence Reason', 'Add time for unknown Absence Reason'",
    )


class CaseEmbeddable(PydanticBaseModelEmptyStrIsNone):
    caseHandler: Optional[CaseHandlerEmbeddable] = None
    caseNumber: Optional[str] = Field(None, description="Case number")
    caseType: Optional[str] = Field(None, description="Case type")
    id: Optional[str] = Field(None, description="ID used to identify Case")
    status: Optional[str] = Field(None, description="The status of the sub-case.")


class ClaimMedicareLevyExemptionResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class ClientDominantSideResponseGroupClient(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwaggerGroupClient]] = None
    name: str = Field(..., description="The enum instance name")


class ClosureReasonsResponseGroupClient(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwaggerGroupClient]] = None
    name: str = Field(..., description="The enum instance name")


class ConditionCategoryResponseGroupClient(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwaggerGroupClient]] = None
    name: str = Field(..., description="The enum instance name")


class ContactContextSwaggerResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class ContactTimeZoneResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class CountryResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class CreateActualAbsencePeriodCommand(PydanticBaseModelEmptyStrIsNone):
    actualDate: date = Field(..., description="ISO 8601 date format", example="1999-12-31")
    additionalNotes: Optional[str] = Field(
        None,
        description="Enter any additional notes about the reported actual time.",
        max_length=250,
        min_length=0,
    )
    endDateTime: Optional[datetime] = Field(
        None, description="ISO 8601 date time format", example="1999-12-31T23:59:59Z"
    )
    episodePeriodBasis: EpisodePeriodDurationBasisRequest
    episodePeriodDuration: int = Field(
        ..., description="Actual time/duration taken by the employee.", ge=1
    )
    reportedDateTime: Optional[datetime] = Field(
        None, description="ISO 8601 date time format", example="1999-12-31T23:59:59Z"
    )
    startDateTime: Optional[datetime] = Field(
        None, description="ISO 8601 date time format", example="1999-12-31T23:59:59Z"
    )
    timeZone: Optional[TimeZoneRequest] = None
    type: Optional[CreateAbsencePeriodTypeRequest] = None


class CreateAddressCommand(PydanticBaseModelEmptyStrIsNone):
    addressLine1: Optional[str] = Field(
        None, description="First line of an address.", max_length=40
    )
    addressLine2: Optional[str] = Field(
        None, description="Second line of an address.", max_length=40
    )
    addressLine3: Optional[str] = Field(
        None, description="Third line of an address.", max_length=40
    )
    addressLine4: Optional[str] = Field(
        None, description="Fourth line of an address.", max_length=40
    )
    addressLine5: Optional[str] = Field(
        None, description="Fifth line of an address.", max_length=40
    )
    addressLine6: Optional[str] = Field(
        None, description="Sixth line of an address.", max_length=40
    )
    addressLine7: Optional[str] = Field(
        None, description="Seventh line of an address.", max_length=40
    )
    country: CountryRequest
    extensions: Optional[Dict[str, Any]] = None
    extraAustralianFields: Optional[CreateAustralianAddressCommand] = None
    postCode: Optional[str] = Field(None, description="International Postcode.", max_length=40)
    premiseNo: Optional[str] = Field(
        None, description="The premise number i.e. the street number.", max_length=50
    )


class CreateContractualEarningsCommand(PydanticBaseModelEmptyStrIsNone):
    amount: Optional[AmountMoneyRequest] = None
    earningsType: EarningsTypeRequest
    effectiveDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    endDate: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    extensions: Optional[Dict[str, Any]] = None
    frequency: Optional[EarningsBasisFrequencyRequest] = None
    standardHourlyRate: Optional[StandardHourlyRateMoneyRequest] = None
    standardHours: Optional[str] = Field(
        None,
        description="Number of hours in a standard working period. In use for Weekly and Bi-Weekly frequcies only, when TotalAmountOverrride is false",
    )
    totalAmountOverride: bool = Field(
        ...,
        description="For Weekly and Bi-Weekly frequencies, determines whether earnings were specified using Amount (true) or StandardHours and StandardHourlyRate.",
    )


class CreatePaymentPreferenceCommand(PydanticBaseModelEmptyStrIsNone):
    accountDetail: Optional[CreateAccountDetailCommand] = None
    address: Optional[CreateAddressCommand] = None
    bulkPayee: Optional[bool] = Field(
        None,
        description="This is used to indicate a high frequency client. If it is set to true then a negative payment may be created with a view to recovering from another payment, rather than attempting to recover the overpayment.",
    )
    chequeDetail: Optional[CreateChequeDetailCommand] = None
    description: Optional[str] = Field(
        None, description="Payment preference description.", max_length=150
    )
    effectiveFromDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    effectiveToDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    extensions: Optional[Dict[str, Any]] = None
    isDefault: Optional[bool] = None
    nominatedPayeeId: Optional[str] = Field(
        None, description="Unique identifier of nominated payee party."
    )
    nominatedPayeeType: NominatedPayeeTypeRequest
    overridePostalAddress: Optional[bool] = Field(
        None,
        description="This indicator will be used to override the address or use the customer current address. If true then override the address, else use the customer default address.",
    )
    paymentDay: PaymentDayRequest
    paymentMethod: PaymentMethodRequest
    paymentPeriod: PaymentPeriodRequest


class CreatePhoneNumberCommand(PydanticBaseModelEmptyStrIsNone):
    areaCode: Optional[str] = Field(None, max_length=10, min_length=0)
    contactMethod: EnumCommand
    exDirectory: Optional[bool] = None
    extension: Optional[str] = Field(None, max_length=10, min_length=0)
    extensions: Optional[Dict[str, Any]] = None
    intCode: Optional[str] = Field(None, max_length=10, min_length=0)
    telephoneNo: Optional[str] = Field(None, max_length=20, min_length=0)


class CreateWorkPatternDayCommand(PydanticBaseModelEmptyStrIsNone):
    dayOfWeek: DayOfWeekRequest
    hours: int = Field(
        ...,
        description="The number of whole working hours on this day in the work pattern (0-24)",
        ge=0,
    )
    minutes: int = Field(
        ..., description="The number of minutes on this day in the work pattern (0-59)", ge=0
    )
    weekNumber: int = Field(
        ...,
        description="The number of the week in the pattern within which this day occurs (1, 2, 3 or 4)",
        ge=0,
    )


class CurrentPhaseResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class CustomerResource(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    customerNo: Optional[str] = Field(None, description="Customer number")
    dateOfBirth: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    employeeID: Optional[str] = Field(
        None,
        description="The Customer's employee id. This field will be populated if the Customer represents an Employee i.e. a person with one current occupation where the Group Client is listed as the Employer.",
    )
    firstName: Optional[str] = Field(None, description="The First Name of the Customer")
    id: Optional[str] = Field(
        None, description="An id that uniquely identifies the customer record."
    )
    idNumber: Optional[str] = Field(
        None, description="The Customer's id number, could be SSN or national security number"
    )
    jobTitle: Optional[str] = Field(
        None,
        description="The Customer's job title. This field will be populated if the Customer represents an Employee i.e. a person with one current occupation where the Group Client is listed as the Employer.",
    )
    lastName: Optional[str] = Field(None, description="The Last Name of the Customer")
    memberGroups: Optional[List[MemberGroupEmbeddable]] = Field(
        None,
        description="A list of member groups that reflect the customer's place within their employer's structure. If the customer is not linked to member groups then an empty list is returned.",
    )
    organisationUnit: Optional[str] = Field(
        None,
        description="The Customer's organisation unit. This field will be populated if the Customer represents an Employee i.e. a person with one current occupation where the Group Client is listed as the Employer.",
    )
    workSite: Optional[str] = Field(
        None,
        description="The Customer's worksite. This field will be populated if the Customer represents an Employee i.e. a person with one current occupation where the Group Client is listed as the Employer.",
    )


class CustomerResourceGroupClient(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    customerNo: Optional[str] = Field(None, description="Customer number")
    dateOfBirth: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    employeeID: Optional[str] = Field(
        None,
        description="The Customer's employee id. This field will be populated if the Customer represents an Employee i.e. a person with one current occupation where the Group Client is listed as the Employer.",
    )
    firstName: Optional[str] = Field(None, description="The First Name of the Customer")
    id: Optional[str] = Field(
        None, description="An id that uniquely identifies the customer record."
    )
    idNumber: Optional[str] = Field(
        None, description="The Customer's id number, could be SSN or national security number"
    )
    jobTitle: Optional[str] = Field(
        None,
        description="The Customer's job title. This field will be populated if the Customer represents an Employee i.e. a person with one current occupation where the Group Client is listed as the Employer.",
    )
    lastName: Optional[str] = Field(None, description="The Last Name of the Customer")
    memberGroups: Optional[List[MemberGroupEmbeddableGroupClient]] = Field(
        None,
        description="A list of member groups that reflect the customer's place within their employer's structure. If the customer is not linked to member groups then an empty list is returned.",
    )
    organisationUnit: Optional[str] = Field(
        None,
        description="The Customer's organisation unit. This field will be populated if the Customer represents an Employee i.e. a person with one current occupation where the Group Client is listed as the Employer.",
    )
    workSite: Optional[str] = Field(
        None,
        description="The Customer's worksite. This field will be populated if the Customer represents an Employee i.e. a person with one current occupation where the Group Client is listed as the Employer.",
    )


class CustomerResourcesGroupClient(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, Any]] = None
    elements: Optional[List[CustomerResourceGroupClient]] = None
    hasMoreElements: Optional[bool] = Field(
        None,
        description="<P>If the query has a <code>limit</code> set and the actual number of resources that match the query exceeds the limit this will be true if the number of resources is less than or equal to the limit this will be false.</P><P>Note:If the limit query pattern is not used, this value will not be returned.</P>",
    )
    meta: Optional[Dict[str, Any]] = None
    totalSize: Optional[int] = Field(
        None,
        description="<P>The <code>totalSize</code> is the total number of resources that match the query. This can be greater than the page size in a paginated query if there are more queries.</P><P>Note:This field will not be returned if the query has a <code>limit</code> set for it.</P>",
    )


class DeliveryTypeResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class EFormAttribute(PydanticBaseModelEmptyStrIsNone):
    booleanValue: Optional[bool] = Field(
        None,
        description="Boolean value of an EForm attribute, which must be populated if the type attribute is set to boolean.",
    )
    dateValue: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    decimalValue: Optional[float] = Field(
        None,
        description="Decimal value of an EForm attribute, which must be populated if the type attribute is set to decimal.",
    )
    enumValue: Optional[ModelEnum] = None
    integerValue: Optional[int] = Field(
        None,
        description="Integer value of an EForm attribute, which must be populated if the type attribute is set to integer.",
    )
    name: str = Field(..., description="The name of an EForm attibute.")
    stringValue: Optional[str] = Field(
        None,
        description="String value of an EForm attribute, which must be populated if the type attribute is set to string.",
    )


class EarningsBasisFrequencyResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class EarningsTypeResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class EditAddressCommand(PydanticBaseModelEmptyStrIsNone):
    addressLine1: Optional[str] = Field(None, description="First line of an address.")
    addressLine2: Optional[str] = Field(None, description="Second line of an address.")
    addressLine3: Optional[str] = Field(None, description="Third line of an address.")
    addressLine4: Optional[str] = Field(None, description="Fourth line of an address.")
    addressLine5: Optional[str] = Field(None, description="Fifth line of an address.")
    addressLine6: Optional[str] = Field(None, description="Sixth line of an address.")
    addressLine7: Optional[str] = Field(None, description="Seventh line of an address.")
    country: Optional[CountryRequest] = None
    extensions: Optional[Dict[str, Any]] = None
    extraAustralianFields: Optional[EditAustralianAddressCommand] = None
    postCode: Optional[str] = Field(None, description="International Postcode.")
    premiseNo: Optional[str] = None


class EditContractualEarningsCommand(PydanticBaseModelEmptyStrIsNone):
    amount: Optional[AmountMoneyRequest] = None
    earningsType: Optional[EarningsTypeRequest] = None
    effectiveDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    endDate: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    extensions: Optional[Dict[str, Any]] = None
    frequency: Optional[EarningsBasisFrequencyRequest] = None
    standardHourlyRate: Optional[StandardHourlyRateMoneyRequest] = None
    standardHours: Optional[str] = Field(
        None,
        description="Number of hours in a standard working period. In use for Weekly and Bi-Weekly frequcies only, when TotalAmountOverrride is false",
    )
    totalAmountOverride: Optional[bool] = Field(
        None,
        description="For Weekly and Bi-Weekly frequencies, determines whether earnings were specified using Amount (true) or StandardHours and StandardHourlyRate.",
    )


class EditCustomerInfoCommand(PydanticBaseModelEmptyStrIsNone):
    address: Optional[EditAddressCommand] = None
    dateOfBirth: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    extensions: Optional[Dict[str, Any]] = None
    firstName: Optional[str] = Field(None, description="Person's first name")
    gender: Optional[GenderRequest] = None
    idNumber: Optional[str] = Field(
        None,
        description="ID number of the claimant. The ID number could be something like social security number, or tax identificaiton number. They ID type is indicated by the value of the <code>identificationNumberType</code> field.",
    )
    identificationNumberType: Optional[IdentificationNumberTypeRequest] = None
    initials: Optional[str] = Field(None, description="Person's middle initials")
    lastName: Optional[str] = Field(None, description="Person's last name")
    maritalStatus: Optional[MaritalStatusRequest] = None
    nationality: Optional[NationalityRequest] = None
    needsInterpreter: Optional[bool] = Field(
        None, description="Indicates if the customer needs an interpreter"
    )
    partyType: Optional[PartyTypeRequest] = None
    placeOfBirth: Optional[str] = Field(None, description="Place of Birth")
    secondName: Optional[str] = Field(None, description="Person's second name")
    securedClient: Optional[bool] = Field(None, description="Secured client flag")
    staff: Optional[bool] = Field(None, description="Staff flag")
    title: Optional[TitleRequest] = None


class EditCustomerOccupationCommand(PydanticBaseModelEmptyStrIsNone):
    adjustedJobStartDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    altEmploymentCat: Optional[AltEmploymentCatRequest] = None
    cbaValue: Optional[str] = Field(None, description="Collective bargaining agreement code")
    daysWorkedPerWeek: Optional[str] = Field(
        None,
        description="The number of days worked per week, which can be specified with partial days",
    )
    empLocationCode: Optional[EmpLocationCodeRequest] = None
    employeeIdentifier: Optional[str] = Field(
        None, description="Reference identifier of an employee in respect of its employer"
    )
    employmentCat: Optional[EmploymentCatRequest] = None
    employmentStatus: Optional[EmploymentStatusRequest] = None
    employmentTitle: Optional[EmploymentTitleRequest] = None
    employmentType: Optional[EditEmploymentTypeRequest] = None
    employmentWorkState: Optional[EditEmploymentWorkStateRequest] = None
    endPosCode: Optional[EndPosCodeRequest] = None
    endPosReason: Optional[str] = Field(None, description="he reason for job ending")
    extensions: Optional[Dict[str, Any]] = None
    hoursWorkedPerYear: Optional[int] = Field(
        None, description="Number of hours an employee has worked in the past 12 months"
    )
    hrsWorkedPerWeek: Optional[str] = Field(
        None,
        description="The number of hours worked per week, which can be specified using partial hours",
    )
    jobDesc: Optional[str] = Field(None, description="Job description")
    jobEndDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    jobStartDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    jobStrenuous: Optional[JobStrenuousRequest] = None
    jobTitle: Optional[str] = Field(None, description="Job title")
    keyEmployee: Optional[bool] = Field(
        None, description="Is the employee classified as a Key Employee"
    )
    occCodeName: Optional[str] = Field(
        None,
        description="The name(s) associated with the specific Occupation Industry Classification Code id",
    )
    occCodeReference: Optional[str] = Field(
        None, description="Occupation Industry Classification Code id"
    )
    overrideDaysWorkedPerWeek: Optional[bool] = Field(
        None, description="Is override days worked per week true or false?"
    )
    remarks: Optional[str] = Field(None, description="Remarks about the Occupation")
    timeZone: Optional[TimeZoneRequest] = None
    withinFMLACriteria: Optional[bool] = Field(
        None,
        description="Is the employee working within Federal FMLA defined radius i.e. 50 employees within 75 miles",
    )
    workSchDesc: Optional[str] = Field(None, description="Work schedule description")
    workingAtHome: Optional[bool] = Field(None, description="Is the employee working at home")


class EditGroupPolicyMemberDetailCommand(PydanticBaseModelEmptyStrIsNone):
    divisionClassLinks: Optional[List[WriteDivisionClassLinkCommand]] = Field(
        None,
        description="The list of class and division ids that are linked to the claimant member details for the group policy (sorted by division name and class name).",
    )
    memberNumber: Optional[str] = Field(
        None, description="The Claimant's member number for the group policy"
    )


class EditMedicalDetailsCommandGroupClient(PydanticBaseModelEmptyStrIsNone):
    actualFirstSurgeryDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    causeOfInjuryCode: Optional[str] = Field(
        None,
        description="The code ID that specifies the cause of injury. This represents a specific medical code that identifies a particular injury, and is associated with the corresponding claim (using a Code Value Link entity). This must be contained within inverted commas, otherwise the value will be coerced. For example, medical code 10-1 would be coerced into medical code 9.",
    )
    clientDominantSide: Optional[ClientDominantSideRequestGroupClient] = None
    condition: Optional[str] = Field(
        None,
        description="A description of the medical condition of the injured party. Cannot be more than 150 characters.",
    )
    conditionCategory: Optional[ConditionCategoryRequestGroupClient] = None
    contestPreExistingConditionApplies: Optional[bool] = Field(
        None,
        description="Set by the Adjudicator to indicate if the contestable condition applies as the result of the review",
    )
    expectedFirstSurgeryDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    extensions: Optional[Dict[str, Dict[str, Any]]] = Field(
        None,
        description="An object of the extension attributes that contains MedicalDetails (OLClaimMedicalDetails) extension information.",
    )
    facilityId: Optional[str] = Field(
        None,
        description="A specialisation of OCParty that represents an organisation or legal entity.",
    )
    firstDoctorVisitDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    lastReceivedDateMedicalInfoDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    lastRequestedDateMedicalInfoDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    lifeExpectancy: Optional[LifeExpectancyRequestGroupClient] = None
    medicalAuthorisationReceivedDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    outpatient: Optional[date] = Field(None, description="Indicates outpatient.")
    pregnancyIndicator: Optional[bool] = Field(
        None, description="Indicates whether a person is pregnant or not."
    )
    symptomsFirstAppearedDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    treatmentPlan: Optional[str] = Field(
        None,
        description="A description of the treatment plan for the injured party. Cannot be more than 256 characters.",
    )
    typeOfSurgery: Optional[TypeOfSurgeryRequestGroupClient] = None


class EditPaymentPreferenceAddressCommand(PydanticBaseModelEmptyStrIsNone):
    addressLine1: Optional[str] = Field(None, description="First line of an address.")
    addressLine2: Optional[str] = Field(None, description="Second line of an address.")
    addressLine3: Optional[str] = Field(None, description="Third line of an address.")
    addressLine4: Optional[str] = Field(None, description="Fourth line of an address.")
    addressLine5: Optional[str] = Field(None, description="Fifth line of an address.")
    addressLine6: Optional[str] = Field(None, description="Sixth line of an address.")
    addressLine7: Optional[str] = Field(None, description="Seventh line of an address.")
    country: Optional[CountryRequest] = None
    extensions: Optional[Dict[str, Any]] = None
    extraAustralianFields: Optional[EditPaymentPreferenceAustralianAddressCommand] = None
    postCode: Optional[str] = Field(None, description="International Postcode.")
    premiseNo: Optional[str] = None


class EditPaymentPreferenceCommand(PydanticBaseModelEmptyStrIsNone):
    accountDetail: Optional[EditAccountDetailCommand] = None
    address: Optional[EditPaymentPreferenceAddressCommand] = None
    bulkPayee: Optional[bool] = Field(
        None,
        description="This is used to indicate a high frequency client. If it is set to true then a negative payment may be created with a view to recovering from another payment, rather than attempting to recover the overpayment.",
    )
    chequeDetail: Optional[EditChequeDetailCommand] = None
    default: Optional[bool] = Field(None, description="Default payment preference indicator.")
    description: Optional[str] = Field(None, description="Payment preference description.")
    effectiveFromDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    effectiveToDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    isDefault: Optional[bool] = None
    nominatedPayeeId: Optional[str] = Field(
        None, description="Unique identifier of nominated payee party."
    )
    nominatedPayeeType: Optional[NominatedPayeeTypeRequest] = None
    overridePostalAddress: Optional[bool] = Field(
        None,
        description="This indicator will be used to override the address or use the customer current address. If true then override the address, else use the customer default address.",
    )
    paymentDay: Optional[PaymentDayRequest] = None
    paymentMethod: Optional[PaymentMethodRequest] = None
    paymentPeriod: Optional[PaymentPeriodRequest] = None


class EditPhoneNumberCommand(PydanticBaseModelEmptyStrIsNone):
    areaCode: str
    contactMethod: Optional[EnumCommand] = None
    exDirectory: bool
    extension: str
    extensions: Optional[Dict[str, Any]] = None
    intCode: str
    telephoneNo: str


class EditWeekBasedWorkPatternCommand(PydanticBaseModelEmptyStrIsNone):
    patternStartDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    patternStatus: Optional[PatternStatusRequest] = None
    workPatternDays: List[EditWorkPatternDayCommand] = Field(
        ...,
        description="List of days which make up the pattern. There can be 7, 14, 21 or 28 of these.",
    )
    workPatternType: WorkPatternTypeRequest
    workWeekStarts: Optional[WorkWeekStartsRequest] = None


class EffectivenessResponseGroupClient(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwaggerGroupClient]] = None
    name: str = Field(..., description="The enum instance name")


class EmailAddressContactMethodResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class EmailAddressResource(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    emailAddress: Optional[str] = Field(None, description="The email address")
    emailType: Optional[EmailAddressContactMethodResponse] = None
    extensions: Optional[Dict[str, Any]] = None
    id: Optional[str] = Field(None, description="The id of the resource")
    meta: Optional[Dict[str, Any]] = None


class Emails(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, Any]] = None
    elements: Optional[List[EmailAddressResource]] = None
    hasMoreElements: Optional[bool] = Field(
        None,
        description="<P>If the query has a <code>limit</code> set and the actual number of resources that match the query exceeds the limit this will be true if the number of resources is less than or equal to the limit this will be false.</P><P>Note:If the limit query pattern is not used, this value will not be returned.</P>",
    )
    meta: Optional[Dict[str, Any]] = None
    totalSize: Optional[int] = Field(
        None,
        description="<P>The <code>totalSize</code> is the total number of resources that match the query. This can be greater than the page size in a paginated query if there are more queries.</P><P>Note:This field will not be returned if the query has a <code>limit</code> set for it.</P>",
    )


class EmpLocationCodeResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class EmployeeLeaveBalance(PydanticBaseModelEmptyStrIsNone):
    approvedTime: Optional[float] = Field(
        None,
        description="Employee total approved leave as per the leave plan calculation for the given period.  The unit of time is provided by the time basis.",
    )
    availabilityPeriodEndDate: Optional[datetime] = Field(
        None, description="ISO 8601 date time format", example="1999-12-31T23:59:59Z"
    )
    availabilityPeriodStartDate: Optional[datetime] = Field(
        None, description="ISO 8601 date time format", example="1999-12-31T23:59:59Z"
    )
    availableBalance: Optional[float] = Field(
        None,
        description="The projected remaining time entitlement for the employee under the leave plan for the given entitlement period, i.e. Time Entitlement - (Approved Leave + Pending Leave). The consumption of any shared entitlement(s) is not considered by the calculation, only consumption of the time entitlement of the leave plan itself.  The unit of time is provided by the time basis.",
    )
    id: Optional[str] = Field(None, description="A unique identifier for the leave plan.")
    name: Optional[str] = Field(None, description="The name of the leave plan.")
    notificationMessage: Optional[str] = Field(
        None,
        description="The additional information to the employer about the balance being presented and can explain some reasons to be cautious of the value presented here.The following information message returns as per the leave plan type.<UL><LI><B>Standard leave plan:</B> The approximate balance provides an indication of your balance under this leave plan and is for general information purposes only. This balance considers your leave requests within the highlighted months. We recommend that you submit a leave request for an accurate assessment of available time under each leave plan.</LI><LI><B>Shared leave plan:</B>The approximate balance estimates time left under this leave plan. Other leave requests and leave plan entitlements may affect this plan's balance. We recommend that you submit a leave request for an accurate evaluation of available time.</LI><LI><B>Tracking plans:</B>This leave plan tracks time shared between other leave plans and the approved and pending time is totaled from those plans. The approximate balance is a guide only.</LI><LI><B>Leave plan with versions:</B>The approximate balance is an estimate as this leave plan has different versions that may alter the plan's entitlements. These calculations are based on the version that is effective in the selected month. We recommend that you submit a leave request for an accurate evaluation of available time.</LI><LI><B>Leave plans with reasonable timebank:</B> The approximate balance is not available as this leave plan allows for a reasonable amount of time to be taken rather than a set entitlement. The amount of time available is at the employer's discretion.</LI><LI><B>Leave plans that are no longer serviced by a carrier:</B> The approximate balance is not available because this leave plan is no longer serviced by the employer.</LI></UL>",
    )
    pendingTime: Optional[float] = Field(
        None,
        description="Employee total pending leave as per the leave plan calculation for the given period.  The unit of time is provided by the time basis.",
    )
    projectedAvailableTime: Optional[float] = Field(
        None,
        description="The amount of time that is projected to be available to the Employee under the leave plan for the given entitlement period and which reflects the remaining time entitlement of the leave plan and all shared entitlements that it participates in.  The unit of time is provided by the time basis.",
    )
    sharedEntitlementPlans: Optional[List[SharedEntitlementPlan]] = Field(
        None,
        description="A list of shared entitlement tracker leave plans that the leave plan is associated with.",
    )
    timeBasis: Optional[str] = Field(
        None,
        description="The basis used to record time unit of approved, pending and entitlement. It may be Minutes, Hours, Days, Weeks, Years or Months. This maps to enum TimeUnits(domain id 6757).",
        max_length=10,
        min_length=0,
    )
    timeEntitlement: Optional[float] = Field(
        None,
        description="The time that is available to an employee who has met the eligibility requirements as per the leave plan calculation period.  The unit of time is provided by the time basis.",
    )
    timeWithinPeriod: Optional[int] = Field(
        None,
        description="The time frame within which the employee must avail of the entitled leave. For example, an employee may be entitled to 6 weeks leave within 12 months period.  The unit of time is provided by the time within period basis.",
        le=9999,
    )
    timeWithinPeriodBasis: Optional[str] = Field(
        None,
        description="The basis used to record time unit of time within a period. It may be Minutes, Hours, Days, Weeks or Months. This maps to enum LengthBasis(domain id 6758).",
        max_length=10,
        min_length=0,
    )


class EmployeePreferredResponseGroupClient(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwaggerGroupClient]] = None
    name: str = Field(..., description="The enum instance name")


class EmployerPreferredResponseGroupClient(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwaggerGroupClient]] = None
    name: str = Field(..., description="The enum instance name")


class EmploymentCatResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class EmploymentStatusResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class EmploymentTitleResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class EmploymentTypeResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class EmploymentWorkStateResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class EndPosCodeResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class EnumDomain(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = Field(
        None,
        description="Links to related objects (in this case: self and instances)",
        example="{'self': '','instances': ''}",
    )
    alphaSort: Optional[bool] = Field(
        None,
        description="When value is equal to 'true' it indicating that domain instances when presented to user should be sorted alphabetically, 'false' otherwise. This parameter does not influence sort order of instances when returned by this API",
    )
    defaultSort: Optional[bool] = Field(
        None,
        description="When value is equal to 'true' it indicating that default instance should be shown (to user) first in an alphabetically sorted view, 'false' othrwise. This parameter does not influence sort order of instances when returned by this API",
    )
    description: Optional[str] = Field(None, description="Description of this domain")
    editable: Optional[bool] = Field(
        None,
        description="Flag whether this domain is allowed to be edited. Domain can be edited from back office system only - No edit domain endpoint exists.",
    )
    id: Optional[str] = Field(None, description="Enum domain ID (unique in the system)")
    instances: Optional[List[EnumInstanceSummary]] = Field(
        None, description="List of the instances that belongs to this domain "
    )
    name: Optional[str] = Field(None, description="Enum domain name")
    subsets: Optional[List[EnumSubset]] = Field(
        None, description="List of subsets that belongs to this domain"
    )


class EnumDomains(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, Any]] = None
    elements: Optional[List[EnumDomain]] = None
    hasMoreElements: Optional[bool] = Field(
        None,
        description="<P>If the query has a <code>limit</code> set and the actual number of resources that match the query exceeds the limit this will be true if the number of resources is less than or equal to the limit this will be false.</P><P>Note:If the limit query pattern is not used, this value will not be returned.</P>",
    )
    meta: Optional[Dict[str, Any]] = None
    totalSize: Optional[int] = Field(
        None,
        description="<P>The <code>totalSize</code> is the total number of resources that match the query. This can be greater than the page size in a paginated query if there are more queries.</P><P>Note:This field will not be returned if the query has a <code>limit</code> set for it.</P>",
    )


class EnumInstance(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = Field(
        None,
        description="Links to related objects (in this case: SELF and DOMAIN)",
        example="{'self': '','domain': ''}",
    )
    dOrder: Optional[int] = Field(
        None,
        description="Denotes the order the Enum resides within all Enum Instances within Domain",
    )
    domainId: Optional[int] = Field(
        None, description="Enum Domain ID, corresponds to <code>domainId</code>"
    )
    domainName: Optional[str] = Field(
        None, description="Name of domain to which this instance belongs to"
    )
    endDate: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    fullId: Optional[int] = Field(
        None, description="Enum instance fullId, corresponds to <code>TaEnum.fullId</code>"
    )
    id: Optional[str] = Field(None, description="Enum instance ID (unique in the domain)")
    name: Optional[str] = Field(None, description="Enum instance name")
    retired: Optional[bool] = Field(
        None,
        description="If <code>true</code> retired Instances only are returned,if <code>false</code>(or not specified) non-retired instances only are returned",
    )
    startDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    subsets: Optional[List[EnumSubsetSummary]] = Field(
        None, description="List of subsets to which this instance belongs to", unique_items=True
    )


class EnumInstances(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, Any]] = None
    elements: Optional[List[EnumInstance]] = None
    hasMoreElements: Optional[bool] = Field(
        None,
        description="<P>If the query has a <code>limit</code> set and the actual number of resources that match the query exceeds the limit this will be true if the number of resources is less than or equal to the limit this will be false.</P><P>Note:If the limit query pattern is not used, this value will not be returned.</P>",
    )
    meta: Optional[Dict[str, Any]] = None
    totalSize: Optional[int] = Field(
        None,
        description="<P>The <code>totalSize</code> is the total number of resources that match the query. This can be greater than the page size in a paginated query if there are more queries.</P><P>Note:This field will not be returned if the query has a <code>limit</code> set for it.</P>",
    )


class EpisodeDurationBasisResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class ExtensionAttribute(PydanticBaseModelEmptyStrIsNone):
    booleanValue: Optional[bool] = Field(
        None, description="Value of the class extension attribute of a boolean type."
    )
    dateOnlyValue: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    decimalValue: Optional[float] = Field(
        None, description="Value of the class extension attribute of a Decimal type."
    )
    enumValue: Optional[ModelEnum] = None
    integerValue: Optional[int] = Field(
        None, description="Value of the class extension attribute of a string type."
    )
    moneyValue: Optional[str] = Field(
        None, description="Value of the class extension attribute of a Money type."
    )
    name: str = Field(
        ..., description="The name of the attribute which extends standard claim set of attributes."
    )
    stringValue: Optional[str] = Field(
        None, description="Value of the class extension attribute of a string type."
    )


class ExtraPayTaxRateResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class FilingMaritalStatusResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class GenderResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class GroupClientAccommodationCase(PydanticBaseModelEmptyStrIsNone):
    accommodations: Optional[List[GroupClientAccommodationDetail]] = Field(
        None, description="List of accommodations contained in the case."
    )
    caseHandler: Optional[CaseHandler] = None
    caseReference: Optional[str] = Field(None, description="Business identifier for this case.")
    closureReasons: Optional[List[str]] = Field(
        None, description="List of reasons given if the case is not being accommodated."
    )
    decisionDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    employee: Optional[Employee] = None
    id: Optional[str] = Field(None, description="Unique identifier for the resource.")
    limitations: Optional[List[str]] = Field(
        None,
        description="List of limitations the employee has that they require an accommodation in their job.",
    )
    notificationCase: Optional[NotificationCase] = None
    notificationDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    phase: Optional[str] = Field(
        None,
        description="Current phase in the Interactive Accommodation Process required by the Americans With Disabilities Act (ADA) and configured in the system, for example, 'Assessment'.",
    )
    pregnancyRelated: Optional[str] = Field(
        None,
        description="Identifies whether the accommodation being requested is related to an employee's pregnancy.",
    )
    stage: Optional[str] = Field(
        None,
        description="Current stage in the Interactive Accommodation Process and configured in the system, for example, 'Evaluate Accommodation Options'.",
    )


class GroupClientDocument(PydanticBaseModelEmptyStrIsNone):
    caseId: Optional[str] = None
    createdBy: Optional[str] = Field(
        None, description="The User name who created a document.", max_length=60, min_length=0
    )
    dateCreated: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    description: Optional[str] = Field(
        None,
        description="User solicitied description of this document.",
        max_length=4000,
        min_length=0,
    )
    documentId: int = Field(..., description="The document Id")
    effectiveFrom: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    effectiveTo: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    extensionAttributes: Optional[List[ExtensionAttribute]] = Field(
        None,
        description="An array of the extensionAttribute objects which contain document (OCDocumentBase) extension information.",
    )
    fileExtension: Optional[str] = Field(
        None,
        description="The file extension of the document. e.g. .doc for MS Word.",
        max_length=20,
        min_length=0,
    )
    fileName: Optional[str] = Field(
        None,
        description="An external reference to the document. This can be a docid or a path and filename.",
        max_length=400,
        min_length=0,
    )
    isRead: Optional[bool] = None
    name: str = Field(
        ...,
        description="The short business description of the document type",
        max_length=200,
        min_length=0,
    )
    originalFilename: Optional[str] = Field(
        None, description="The original attachment filename.", max_length=300, min_length=0
    )
    privacyTag: Optional[str] = Field(
        None, description="The privacyTag secure action of the document."
    )
    readForMyOrganisation: Optional[bool] = Field(
        None,
        description="The read status of the document by other group client from same Organisation.",
    )
    receivedDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    rootCaseId: Optional[str] = None
    status: Optional[str] = Field(None, description="The status of the document.")
    title: Optional[str] = Field(
        None, description="The title of the document.", max_length=2000, min_length=0
    )
    type: str = Field(
        ...,
        description="The form of the document e.g. image, email, document",
        max_length=100,
        min_length=0,
    )


class GroupClientUser(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    enabled: Optional[bool] = Field(
        None,
        description="An indicator used to determine if this Group Client User is enabled or not. True = enabled / false = disabled.",
    )
    id: Optional[str] = Field(
        None, description="The unique Id used to identify the Group Client User."
    )
    nameOfUser: Optional[str] = Field(None, description="The Group Client Users name.")
    role: Optional[AltRoleSchema] = None
    status: Optional[AltStatusSchema] = None
    userReferenceIdentifier: Optional[str] = Field(
        None,
        description="This is an additional reference id which can be used to identify a Group Client User.",
    )


class HospitalisationDetails(PydanticBaseModelEmptyStrIsNone):
    description: Optional[str] = Field(
        None, description="The description of hospitalisation detail.", max_length=256, min_length=0
    )
    endDate: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    endDateConfirmed: Optional[bool] = Field(
        None, description="The end date confirmation for the hospitalisation period."
    )
    extensionAttributes: Optional[List[ExtensionAttribute]] = None
    hospitalReason: Optional[str] = Field(
        None, description="The hospitalisation reason for the claim period."
    )
    startDate: date = Field(..., description="ISO 8601 date format", example="1999-12-31")


class HospitalisationDetailsResponse(PydanticBaseModelEmptyStrIsNone):
    description: Optional[str] = Field(
        None, description="The description of hospitalisation detail.", max_length=256, min_length=0
    )
    endDate: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    endDateConfirmed: Optional[bool] = Field(
        None, description="The end date confirmation for the hospitalisation period."
    )
    extensionAttributes: Optional[List[ExtensionAttribute]] = None
    facility: Optional[str] = Field(None, description="The facilitator(hospital) name.")
    hospitalReason: Optional[str] = Field(
        None, description="The hospitalisation reason for the claim period."
    )
    hospitalisationDetailId: str = Field(
        ..., description="Business Entity OID", example="PE-00012-**********"
    )
    startDate: date = Field(..., description="ISO 8601 date format", example="1999-12-31")


class IdentificationNumberTypeResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class IncomeSource(PydanticBaseModelEmptyStrIsNone):
    amount: Optional[Decimal] = Field(None, description="Money amount format", example="1500.50")
    endDate: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    extensionAttributes: Optional[List[ExtensionAttribute]] = None
    frequency: Optional[str] = Field(None, description="Frequency of the income.")
    incomeName: str = Field(..., description="Income source name.", max_length=256, min_length=0)
    incomeType: str = Field(..., description="Type of the income source.")
    receivedFrom: Optional[str] = Field(
        None, description="Identify from where the other income source was received."
    )
    startDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )


class IncomeSourceResponse(PydanticBaseModelEmptyStrIsNone):
    amount: Optional[Decimal] = Field(None, description="Money amount format", example="1500.50")
    endDate: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    extensionAttributes: Optional[List[ExtensionAttribute]] = None
    frequency: Optional[str] = Field(None, description="Frequency of the income.")
    incomeName: str = Field(..., description="Income source name.", max_length=256, min_length=0)
    incomeSourceId: Optional[str] = Field(
        None, description="Business Entity OID", example="PE-00012-**********"
    )
    incomeType: str = Field(..., description="Type of the income source.")
    receivedFrom: Optional[str] = Field(
        None, description="Identify from where the other income source was received."
    )
    startDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )


class JobStrenuousResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class KiwiSaverStatusResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class LeavePlan(PydanticBaseModelEmptyStrIsNone):
    adjudicationReason: Optional[str] = Field(
        None, description="Adjudication reason of leave plan period."
    )
    adjudicationStatus: Optional[str] = Field(None, description="Adjudication status of leave plan")
    applicabilityStatus: Optional[str] = Field(
        None, description="Applicability status of leave plan"
    )
    availabilityStatus: Optional[str] = Field(
        None, description="Result of the evaluation of plan availability within a leave request"
    )
    calculationPeriodMethod: Optional[str] = Field(
        None, description="Calculation period method of leave plan period."
    )
    category: Optional[str] = Field(None, description="Leave plan category")
    eligibilityStatus: Optional[str] = Field(None, description="Eligibility status of leave plan")
    evidenceStatus: Optional[str] = Field(
        None, description="Evidence status made on this Selected Leave Plan"
    )
    fixedYearStartDay: Optional[int] = Field(
        None, description="Start day for the specified fixed year"
    )
    fixedYearStartMonth: Optional[str] = Field(None, description="Fixed year start month")
    id: Optional[str] = Field(None, description="The unique identifier for the leave plan")
    leavePlanType: Optional[LeavePlanType] = None
    name: Optional[str] = Field(None, description="Leave plan long name")
    paidLeaveCaseId: Optional[str] = Field(None, description="Paid leave case id")
    shortName: Optional[str] = Field(None, description="Leave plan short name")
    timeBankMethod: Optional[str] = Field(
        None,
        description="The time bank method either from the Service Agreement or from the Leave Plan Availability",
    )
    timeEntitlement: Optional[float] = Field(
        None, description="Number of time units for the time entitlement"
    )
    timeEntitlementBasis: Optional[str] = Field(
        None, description="Units of time used in leave plan availability"
    )
    timeWithinPeriod: Optional[int] = Field(
        None, description="Number of time units for the period duration e.g. Weeks, Days"
    )
    timeWithinPeriodBasis: Optional[str] = Field(
        None, description="Units of time used in calculating the time with in period "
    )


class LeavePlanCategoryDtlResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class LeavePlanCategoryResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class LeavePlanGroupDtlResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class LeavePlanGroupResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class LeavePlanJobProtectionResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class LeavePlanTypeDtlResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class LeavePlanTypeResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class LeaveTypeResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class LifeExpectancyResponseGroupClient(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwaggerGroupClient]] = None
    name: str = Field(..., description="The enum instance name")


class LimitationsDescriptionResponseGroupClient(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwaggerGroupClient]] = None
    name: str = Field(..., description="The enum instance name")


class LumpSumBenefit(PydanticBaseModelEmptyStrIsNone):
    amountType: Optional[str] = Field(
        None,
        description="Indicates the basis upon which the benefit is calculated, examples might be fixed amount basis, salary percentage basis or tiered basis.",
    )
    basisOfPolicyWaitingPeriod: Optional[str] = Field(
        None,
        description="Basis of the period of time that the policy must be in force before the insured qualifies for coverage (i.e. day, week month or year).",
    )
    benefitExpiryDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    benefitIncurredDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    benefitType: Optional[str] = Field(
        None,
        description="Type of a benefit right such as Disability Income - STD, Disability Income LTD, Accelerated Death Benefit or Accidental Death Benefit.",
    )
    brokerAuthorisationFlag: Optional[bool] = Field(
        None,
        description="Flag that indicates if the broker is to be included in the correspondence.",
    )
    effectiveDateForCoverage: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    extensionAttributes: Optional[List[ExtensionAttribute]] = Field(
        None, description="Extra attributes from extension if any."
    )
    flatAmount: Optional[Decimal] = Field(
        None, description="Money amount format", example="1500.50"
    )
    initialNotificationDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    isUnderwritten: Optional[bool] = Field(
        None,
        description="Indicates whether or not the member has been underwritten in order to have additional cover beyond what the group membership offers.",
    )
    maximumBenefitExpiryAge: Optional[int] = Field(
        None, description="Maximum Age to be eligible to receive benefit."
    )
    notificationReceivedDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    overrideClaimIncurredDate: Optional[bool] = Field(
        None,
        description="Indicator which specifies that the benefit level incurred date should be used rather than the claim level incurred date.",
    )
    policyWaitingPeriod: Optional[int] = Field(
        None,
        description="Period of time that the policy must be in force before the insured qualifies for coverage.",
    )
    sourceOfRequest: Optional[str] = Field(
        None,
        description="The type of source that is requesting the benefit, such as claimant, guardian, or medical officer.",
    )
    startDateOfBenefitForClaim: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    sumInsuredAtIncurredDate: Optional[Decimal] = Field(
        None, description="Money amount format", example="1500.50"
    )


class ManagedRequirementDetails(PydanticBaseModelEmptyStrIsNone):
    category: str = Field(
        ...,
        description="The category name of the managed requirement.",
        max_length=50,
        min_length=0,
    )
    classExtensionInformation: Optional[List[ExtensionAttribute]] = Field(
        None,
        description="An array of the extensionAttribute objects which contain managed requirement (OCManagedRequirement) extension information.",
    )
    creationDate: date = Field(..., description="ISO 8601 date format", example="1999-12-31")
    creator: str = Field(..., description="the creator for this managed requirement.")
    dateCompleted: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    dateLastFollowedUp: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    dateRequested: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    dateSuppressed: date = Field(..., description="ISO 8601 date format", example="1999-12-31")
    description: Optional[str] = Field(
        None,
        description="A text description of the outstanding requirement or information.",
        max_length=250,
        min_length=0,
    )
    documentReceived: bool = Field(
        ...,
        description="Indicates that a document has been received for this managed requirement. Returns true if the managed requirement is linked to a document that this user can access,otherwise returns false.",
    )
    followUpDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    managedReqId: int = Field(..., description="the identifier for this managed requirement.")
    notProceedingWithDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    sourceOfInfoPartyName: str = Field(
        ..., description="The source of info party name of the managed requirement."
    )
    status: Optional[str] = Field(
        None,
        description="The current status of this Managed Requirement. ",
        max_length=100,
        min_length=0,
    )
    subjectPartyName: str = Field(
        ..., description="The subject party name of the managed requirement."
    )
    type: str = Field(
        ...,
        description="The name of the type of the managed requirement.",
        max_length=50,
        min_length=0,
    )


class ManagerAcceptedResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class MaritalRelationshipStatusResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class MaritalStatusResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class MedicalDetailsGroupClient(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    actualFirstSurgeryDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    causeOfInjuryCode: Optional[str] = Field(
        None,
        description="The code ID that specifies the cause of injury. This represents a specific medical code that identifies a particular injury, and is associated with the corresponding claim (using a Code Value Link entity). This must be contained within inverted commas, otherwise the value will be coerced. For example, medical code 10-1 would be coerced into medical code 9.",
    )
    clientDominantSide: Optional[ClientDominantSideResponseGroupClient] = None
    condition: Optional[str] = Field(
        None,
        description="A description of the medical condition of the injured party. Cannot be more than 150 characters.",
    )
    conditionCategory: Optional[ConditionCategoryResponseGroupClient] = None
    description: Optional[str] = Field(None, description="Description of the cause of injury code.")
    expectedFirstSurgeryDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    extensions: Optional[Dict[str, Any]] = None
    facilityName: Optional[str] = Field(
        None,
        description="A specialisation of OCParty that represents an organisation or legal entity.",
    )
    firstDoctorVisitDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    id: Optional[str] = Field(None, description="Claim identifier")
    lastReceivedDateMedicalInfoDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    lastRequestedDateMedicalInfoDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    lifeExpectancy: Optional[LifeExpectancyResponseGroupClient] = None
    medicalAuthorisationReceivedDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    meta: Optional[Dict[str, Any]] = None
    outpatient: Optional[bool] = Field(None, description="Indicates outpatient.")
    pregnancyIndicator: Optional[bool] = Field(
        None, description="Indicates whether a person is pregnant or not."
    )
    symptomsFirstAppearedDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    treatmentPlan: Optional[str] = Field(
        None,
        description="A description of the treatment plan for the injured party. Cannot be more than 256 characters.",
    )
    typeOfSurgery: Optional[TypeOfSurgeryResponseGroupClient] = None


class MedicareLevySurchargeResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class MonthlyEarningsRequest(PydanticBaseModelEmptyStrIsNone):
    classExtensionInformation: Optional[List[ExtensionAttribute]] = Field(
        None,
        description="An array of the extensionAttribute objects which contain Earnings extension information.",
    )
    earningsBasis: str = Field(..., description="The earnings frequency.")
    effectiveDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    endDate: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    monthlyBasisEarningAmountsRequest: Optional[MonthlyBasisEarningAmountsRequest] = None
    notes: Optional[str] = Field(
        None, description="Notes relating to the earnings period.", max_length=200, min_length=0
    )
    salaryAmountBasis: Optional[str] = Field(
        None, description="Basis for salary amount being used."
    )


class NationalityResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class NominatedPayeeTypeResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class NotAccommodatedReasonResponseGroupClient(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwaggerGroupClient]] = None
    name: str = Field(..., description="The enum instance name")


class NotifedByResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class NotificationClaimSummary(PydanticBaseModelEmptyStrIsNone):
    caseComplexity: Optional[str] = Field(None, description="Case complexity on Claim")
    claimHandler: Optional[str] = Field(
        None, description="The Person that is handling the claim.", max_length=50, min_length=0
    )
    claimHandlerEmailAddress: Optional[str] = Field(
        None, description="The email address of the claim handler.", max_length=40, min_length=0
    )
    claimHandlerPhoneNo: Optional[str] = Field(
        None, description="The phone number of the claim handler.", max_length=20, min_length=0
    )
    claimId: str = Field(
        ..., description="The claim number of the claim", max_length=256, min_length=0
    )
    claimType: str = Field(
        ...,
        description="The name by which the claim type is referred.",
        max_length=50,
        min_length=0,
    )
    classExtensionInformation: Optional[List[ExtensionAttribute]] = Field(
        None,
        description="An array of the extensionAttribute objects which contain extension information.",
    )
    creationDate: Optional[datetime] = Field(
        None, description="ISO 8601 date time format", example="1999-12-31T23:59:59Z"
    )
    customerName: Optional[str] = Field(
        None, description="The customer name of the claim", max_length=256, min_length=0
    )
    description: Optional[str] = Field(
        None,
        description="Description for this case as entered by the user.",
        max_length=2000,
        min_length=0,
    )
    effectiveDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    notificationDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    policyReferences: Optional[str] = Field(
        None,
        description="The reference(s) to the policy from within the relevant admin system.",
        max_length=50,
        min_length=0,
    )
    stageName: Optional[str] = Field(None, description="The current stage of claim case.")
    status: Optional[str] = Field(
        None,
        description="This is the stage in the process that the case is currently at.",
        max_length=10,
        min_length=0,
    )


class NotificationReasonResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class NotificationReasonsResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class PartyTypeResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class PatternStatusResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class Payment(PydanticBaseModelEmptyStrIsNone):
    accountTransferInfo: Optional[AccountTransferInfo] = None
    benefitCaseNumber: Optional[str] = Field(None, description="The Benefit Case Number.")
    benefitCaseTypeName: Optional[str] = None
    benefitRightTypeName: Optional[str] = None
    chequePaymentInfo: Optional[ChequePaymentInfo] = None
    classExtensionInformation: Optional[List[ExtensionAttribute]] = Field(
        None,
        description="An array of the extensionAttribute objects which contain payments (OLPaymentEventInterface) extension information.",
    )
    dateInterfaceRecordCreated: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    effectiveDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    nominatedPayeeName: Optional[str] = Field(
        None,
        description="The party the payee nominated to receive payment",
        max_length=256,
        min_length=0,
    )
    payeeName: Optional[str] = Field(
        None, description="The party to whom the payment was made", max_length=256, min_length=0
    )
    paymentAddress: Optional[str] = Field(
        None, description="The address the payment was sent to", max_length=256, min_length=0
    )
    paymentAmount: Optional[Decimal] = Field(
        None, description="Money amount format", example="1500.50"
    )
    paymentDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    paymentId: Optional[str] = Field(
        None, description="Business Entity OID", example="PE-00012-**********"
    )
    paymentMethod: Optional[str] = Field(
        None,
        description="The method used to transfer the payment e.g. EFT, Hand Typed Check etc.",
        max_length=256,
        min_length=0,
    )
    paymentType: Optional[str] = Field(
        None, description="The payment type e.g. recurring, adhoc etc", max_length=250, min_length=0
    )
    periodEndDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    periodStartDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    rootCaseNumber: Optional[str] = None


class PaymentDayResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class PaymentLineDetails(PydanticBaseModelEmptyStrIsNone):
    adjustmentCalcDetailClassExtension: Optional[List[ExtensionAttribute]] = Field(
        None,
        description="An array of the extensionAttribute objects which contain OLAdjustmentCalcDetail extension information.",
    )
    adjustmentId: Optional[str] = Field(
        None, description="Business Entity OID", example="PE-00012-**********"
    )
    adjustmentTypeId: Optional[str] = Field(
        None, description="Business Entity OID", example="PE-00012-**********"
    )
    amount: Optional[Decimal] = Field(None, description="Money amount format", example="1500.50")
    calcDetails: Optional[List[str]] = Field(
        None, description="The Calculation Details that explain how each adjustment is calculated. "
    )
    endDate: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    fixedAmountIncreaseInstructionClassExtension: Optional[List[ExtensionAttribute]] = Field(
        None,
        description="An array of the extensionAttribute objects which contain OLFixedAmountIncreaseInstruction extension information.",
    )
    payee: Optional[str] = Field(
        None,
        description="The name of the Payee which the payment adjustment belongs.",
        max_length=100,
        min_length=0,
    )
    startDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )


class PaymentMethodResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class PaymentPeriodResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class PaymentPreferenceAustralianAddressEmbeddable(PydanticBaseModelEmptyStrIsNone):
    buildingName1: Optional[str] = Field(None, description="The name of the building.")
    buildingName2: Optional[str] = Field(None, description="The name of the building continued.")
    dpId: Optional[int] = Field(
        None,
        description="Referred to as DPID this is an 8-digit Australian post address identifier which uniquely identifies each delivery point to which Australia Post delivers mail. ",
    )
    extensions: Optional[Dict[str, Any]] = None
    floorLevelNumber: Optional[str] = Field(
        None, description="The floorLevelNumber must not be more than {max} characters"
    )
    floorLevelType: Optional[AUFloorLevelTypesResponse] = None
    lotNumber: Optional[str] = Field(None, description="The lot number.")
    postalNumber: Optional[str] = Field(None, description="The postal number. ")
    postalNumberPrefix: Optional[str] = Field(
        None,
        description="This refers to the non-numeric portion preceding the Postal Number. EXAMPLE : PO Box B20 where B is the prefix. ",
    )
    postalNumberSuffix: Optional[str] = Field(
        None,
        description="This refers to the non-numeric portion following the Postal Number. EXAMPLE : PO Box 20A where A is the suffix.",
    )
    postalType: Optional[AUPostalTypesResponse] = None
    premiseNoSuffix: Optional[str] = Field(
        None, description="The suffix to apply to the number of the premise."
    )
    premiseNoTo: Optional[int] = Field(
        None,
        description="The high number in a range for the premise. This is used when an address consists of a range of numbers as in 8-10 Lower Pembroke Street where the value 10 would be stored in this field. ",
    )
    premiseNoToSuffix: Optional[str] = Field(
        None, description="The suffix to apply to the number to of the premise. "
    )
    streetSuffix: Optional[AUStreetSuffixesResponse] = None


class Period(PydanticBaseModelEmptyStrIsNone):
    balanceDeduction: Optional[float] = Field(
        None,
        description="The amount of time deducted from the time bank in the specified time entitlement basis e.g. number of Weeks, Days etc.",
    )
    endDate: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    leavePlan: Optional[LeavePlan] = None
    leaveRequest: Optional[LeaveRequest] = None
    parentPeriodReference: Optional[str] = Field(
        None, description="Reference number for the parent period"
    )
    periodReference: Optional[str] = Field(
        None, description="Business Entity OID", example="PE-00012-**********"
    )
    relatedToEpisodic: Optional[bool] = Field(
        None,
        description="True if this period represents a reported actual for an episodic requested period, false otherwise",
    )
    requestedEpisodicLeaveDetails: Optional[RequestedEpisodicLeaveDetails] = None
    startDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    status: Optional[str] = Field(None, description="Status of the leave request")
    timeDecisionReason: Optional[str] = Field(
        None, description="Decision reason associated with the period"
    )
    timeDecisionStatus: Optional[str] = Field(
        None, description="Decision status associated with the period"
    )
    timeDeducted: Optional[str] = Field(
        None,
        description="The amount of time deducted in this period, in the specified time deducted basis e.g. number of Hours, Days",
    )
    timeDeductedBasis: Optional[str] = Field(
        None, description="The time deducted basis in this period e.g. Hours, Days"
    )
    timeRequested: Optional[str] = Field(
        None,
        description="The amount of time requested in this period, in the specified time entitlement basis e.g. number of Weeks, Days",
    )
    type: Optional[str] = Field(None, description="Type of the absence period")


class PersonDetails(PydanticBaseModelEmptyStrIsNone):
    classExtensionInformation: Optional[List[ExtensionAttribute]] = Field(
        None,
        description="An array of the extensionAttribute objects which contain customer (OCPerson) extension information.",
    )
    dateOfBirth: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    gender: Optional[str] = Field(
        None, description="The person's gender.", max_length=100, min_length=0
    )
    maritalStatus: Optional[str] = Field(
        None,
        description="Person's marital status - single / married / etc.",
        max_length=100,
        min_length=0,
    )
    nationality: Optional[str] = Field(
        None, description="Person's nationality.", max_length=100, min_length=0
    )
    needsInterpretor: Optional[bool] = Field(
        None, description="Indicates if the customer needs an interpreter."
    )


class PhoneNumber(PydanticBaseModelEmptyStrIsNone):
    areaCode: Optional[str] = Field(
        None, description="area code value", max_length=20, min_length=0
    )
    classExtensionInformation: Optional[List[ExtensionAttribute]] = Field(
        None,
        description="An array of the extensionAttribute objects which contain phone number (OCPhone) extension information.",
    )
    id: Optional[int] = Field(
        None, description="The id of the contact method (e.g. phone / mobile / emailAddress) ", ge=0
    )
    intCode: Optional[str] = Field(
        None, description="international code value", max_length=10, min_length=0
    )
    phoneNumberType: str = Field(
        ...,
        description="The type of phone number (e.g. landline / mobile)",
        max_length=10,
        min_length=0,
    )
    preferred: Optional[bool] = Field(
        None, description="Specify if it is the first person to try to contact when it is required."
    )
    telephoneNo: Optional[str] = Field(
        None, description="telephone No. value", max_length=10, min_length=0
    )


class PhoneNumberContactMethodResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class PhoneNumberResource(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    areaCode: Optional[str] = Field(None, description="Regional area code")
    contactMethod: Optional[PhoneNumberContactMethodResponse] = None
    exDirectory: Optional[bool] = Field(None, description="True if the number is ex-directory")
    extension: Optional[str] = Field(None, description="Optional extension number for networks")
    extensions: Optional[Dict[str, Any]] = None
    id: Optional[str] = Field(None, description="The id of the resource")
    intCode: Optional[str] = Field(None, description="International area code")
    meta: Optional[Dict[str, Any]] = None
    telephoneNo: Optional[str] = Field(None, description="Local telephone number")


class PhoneNumbers(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, Any]] = None
    elements: Optional[List[PhoneNumberResource]] = None
    hasMoreElements: Optional[bool] = Field(
        None,
        description="<P>If the query has a <code>limit</code> set and the actual number of resources that match the query exceeds the limit this will be true if the number of resources is less than or equal to the limit this will be false.</P><P>Note:If the limit query pattern is not used, this value will not be returned.</P>",
    )
    meta: Optional[Dict[str, Any]] = None
    totalSize: Optional[int] = Field(
        None,
        description="<P>The <code>totalSize</code> is the total number of resources that match the query. This can be greater than the page size in a paginated query if there are more queries.</P><P>Note:This field will not be returned if the query has a <code>limit</code> set for it.</P>",
    )


class PreferredContactTimeResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class PregnancyDetails(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    actualDeliveryDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    deliveryType: Optional[DeliveryTypeResponse] = None
    expectedDeliveryDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    extensions: Optional[Dict[str, Any]] = None
    id: Optional[str] = Field(None, description="Claim Medical Details identifier")
    meta: Optional[Dict[str, Any]] = None
    pregnancyComplications: Optional[bool] = Field(
        None, description="Indicates whether or not pregnancy complications occurred."
    )


class PregnancyRelatedResponseGroupClient(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwaggerGroupClient]] = None
    name: str = Field(..., description="The enum instance name")


class QuerySortInfo(PydanticBaseModelEmptyStrIsNone):
    sortOrderItemsList: Optional[List[SortOrderItem]] = None
    sortStatementsText: Optional[str] = None


class QuerySortInfoGroupClient(PydanticBaseModelEmptyStrIsNone):
    sortOrderItemsList: Optional[List[SortOrderItemGroupClient]] = None
    sortStatementsText: Optional[str] = None


class ReadOccupation(PydanticBaseModelEmptyStrIsNone):
    additionalEmploymentCategory: Optional[str] = Field(
        None, description="The alternate employment category."
    )
    codeId: Optional[str] = Field(None, description="The occupation Code ID.")
    codeName: Optional[str] = Field(
        None, description="The name associated with the specific occupation Code ID."
    )
    dateJobBegan: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    dateJobEnded: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    daysWorkedPerWeek: Optional[float] = Field(
        None, description="The number of days worked per week."
    )
    employeeId: Optional[str] = Field(
        None,
        description="The id of an employee in respect of its employer.",
        max_length=50,
        min_length=0,
    )
    employer: Optional[str] = Field(None, description="The name of the employer.")
    employmentCategory: Optional[str] = Field(None, description="The employment category.")
    employmentLocation: Optional[str] = Field(None, description="The employment location code.")
    employmentStatus: Optional[str] = Field(None, description="The employment status.")
    employmentTitle: Optional[str] = Field(None, description="The employment title.")
    endEmploymentReason: Optional[str] = Field(None, description="The code for occupation ending.")
    endPosReason: Optional[str] = Field(None, description="The reason for job ending.")
    extensionAttributes: Optional[List[ExtensionAttribute]] = Field(
        None, description="The extension attributes."
    )
    hoursWorkedPerWeek: Optional[float] = Field(
        None, description="The number of hours worked per week."
    )
    jobDesc: Optional[str] = Field(None, description="The job description.")
    jobStrenuous: Optional[str] = Field(None, description="The job strenuous.")
    jobTitle: Optional[str] = Field(None, description="The job title.")
    occupationId: Optional[int] = Field(None, description="The occupation ID.")
    overrideDaysWorkedPerWeek: Optional[bool] = Field(
        None, description="Is override days worked per week true or false?"
    )
    primary: Optional[bool] = Field(None, description="Is occupation the primary one?")
    remarks: Optional[str] = Field(None, description="The occupation remarks.")
    selfEmployed: Optional[bool] = Field(
        None, description="Is occupation considered self-employed?"
    )
    workPatternBasis: Optional[str] = Field(None, description="The work pattern basis.")
    workScheduleDescription: Optional[str] = Field(
        None, description="The work schedule description."
    )


class ReceivedViaResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class ReportedByResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class ResidentialStatusResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class ScheduledDayResource(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    date: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    id: Optional[str] = Field(None, description="Id that uniquely identifies the scheduled day.")
    leaveRequestId: Optional[str] = Field(
        None, description="Id that uniquely identifies the leave request of the scheduled day."
    )
    scheduledTime: Optional[int] = Field(None, description="Scheduled time in minutes.")
    workPatternType: Optional[WorkPatternTypeResponse] = None


class ScheduledDayResources(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, Any]] = None
    elements: Optional[List[ScheduledDayResource]] = None
    hasMoreElements: Optional[bool] = Field(
        None,
        description="<P>If the query has a <code>limit</code> set and the actual number of resources that match the query exceeds the limit this will be true if the number of resources is less than or equal to the limit this will be false.</P><P>Note:If the limit query pattern is not used, this value will not be returned.</P>",
    )
    meta: Optional[Dict[str, Any]] = None
    totalSize: Optional[int] = Field(
        None,
        description="<P>The <code>totalSize</code> is the total number of resources that match the query. This can be greater than the page size in a paginated query if there are more queries.</P><P>Note:This field will not be returned if the query has a <code>limit</code> set for it.</P>",
    )


class ServiceAgreementLeavePlan(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    absenceReasons: Optional[List[AbsenceReasonEmbeddable]] = Field(
        None, description="A list of absence reasons associated with the given leave plan."
    )
    alias: Optional[str] = Field(
        None, description="An alias name to alternatively identify the leave plan."
    )
    applicability: Optional[ApplicabilityEmbeddable] = None
    category: Optional[LeavePlanCategoryDtlResponse] = None
    description: Optional[str] = Field(None, description="The description of the leave plan.")
    effectiveFromDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    effectiveToDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    eligibility: Optional[EligibilityEmbeddable] = None
    entitlement: Optional[str] = Field(None, description="The entitlement time.")
    entitlementCalculation: Optional[str] = Field(
        None,
        description='This describes how the entitlement has been calculated. It could be a number followed by a unit of measure or a descriptive text like "Time available as accrued" or hold some more complex description.',
    )
    group: Optional[LeavePlanGroupDtlResponse] = None
    id: Optional[str] = Field(
        None, description="UUID of the leave plan on the active service agreement"
    )
    informationLink: Optional[str] = Field(
        None,
        description="A link to a URL containing relevant online documentation for the leave plan.",
    )
    jobProtection: Optional[LeavePlanJobProtectionResponse] = None
    longName: Optional[str] = Field(None, description="The long name of the leave plan.")
    otherDetails: Optional[List[str]] = Field(
        None,
        description="This is populated by invoking the existing event related to leave plan detail, which returns a comma separated list of string.",
    )
    type: Optional[LeavePlanTypeDtlResponse] = None


class ServiceAgreementLeavePlanSummary(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    alias: Optional[str] = Field(
        None, description="An alias name to alternatively identify the leave plan."
    )
    category: Optional[LeavePlanCategoryResponse] = None
    description: Optional[str] = Field(None, description="The description of the leave plan.")
    effectiveFromDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    effectiveToDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    group: Optional[LeavePlanGroupResponse] = None
    id: Optional[str] = Field(
        None, description="UUID of the leave plan on the active service acreement"
    )
    informationLink: Optional[str] = Field(
        None,
        description="A link to a URL containing relevant online documentation for the leave plan.",
    )
    longName: Optional[str] = Field(None, description="The long name of the leave plan.")
    type: Optional[LeavePlanTypeResponse] = None


class SingleAccommodationDecisionResponseGroupClient(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwaggerGroupClient]] = None
    name: str = Field(..., description="The enum instance name")


class StartClaimSummary(PydanticBaseModelEmptyStrIsNone):
    caseComplexity: Optional[str] = Field(None, description="Case complexity on Claim")
    claimHandler: Optional[str] = Field(
        None, description="The Person that is handling the claim.", max_length=50, min_length=0
    )
    claimHandlerEmailAddress: Optional[str] = Field(
        None, description="The email address of the claim handler.", max_length=40, min_length=0
    )
    claimHandlerPhoneNo: Optional[str] = Field(
        None, description="The phone number of the claim handler.", max_length=20, min_length=0
    )
    claimId: str = Field(
        ..., description="The claim number of the claim", max_length=256, min_length=0
    )
    claimType: str = Field(
        ...,
        description="The name by which the claim type is referred.",
        max_length=50,
        min_length=0,
    )
    classExtensionInformation: Optional[List[ExtensionAttribute]] = Field(
        None,
        description="An array of the extensionAttribute objects which contain extension information.",
    )
    creationDate: Optional[datetime] = Field(
        None, description="ISO 8601 date time format", example="1999-12-31T23:59:59Z"
    )
    customerName: Optional[str] = Field(
        None, description="The customer name of the claim", max_length=256, min_length=0
    )
    description: Optional[str] = Field(
        None,
        description="Description for this case as entered by the user.",
        max_length=2000,
        min_length=0,
    )
    effectiveDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    expectedReturnToWorkDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    notificationCaseId: Optional[str] = Field(
        None,
        description="The notification case number of the claim case if applicable.",
        max_length=256,
        min_length=0,
    )
    notificationDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    policyReferences: Optional[str] = Field(
        None,
        description="The reference(s) to the policy from within the relevant admin system.",
        max_length=50,
        min_length=0,
    )
    stageName: Optional[str] = Field(None, description="The current stage of claim case.")
    status: Optional[str] = Field(
        None,
        description="This is the stage in the process that the case is currently at.",
        max_length=10,
        min_length=0,
    )


class StatusResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Dict[str, str] = Field(
        ..., description="Links to the enum domain and enum instance for the API"
    )
    domainId: int = Field(..., description="The <code>domain id</code> of the enum")
    domainName: str = Field(..., description="The enum domain name")
    fullId: int = Field(..., description="The <code>instance id</code> of the enum")
    instances: Optional[List[TaEnumInstanceSwagger]] = None
    name: str = Field(..., description="The enum instance name")


class TaEnumGroupClient(PydanticBaseModelEmptyStrIsNone):
    active: Optional[bool] = None
    alphaSort: Optional[bool] = None
    defaultFirst: Optional[bool] = None
    domainDescription: Optional[str] = None
    domainId: Optional[int] = None
    domainName: Optional[str] = None
    dorder: Optional[int] = None
    editable: Optional[bool] = None
    endDate: Optional[datetime] = Field(
        None, description="ISO 8601 date time format", example="1999-12-31T23:59:59Z"
    )
    fullId: Optional[int] = None
    id: Optional[int] = None
    name: Optional[str] = None
    nameAttribute: Optional[str] = None
    retired: Optional[bool] = None
    startDate: Optional[datetime] = Field(
        None, description="ISO 8601 date time format", example="1999-12-31T23:59:59Z"
    )
    subsetInfo: Optional[List[EnumSubsetInfoGroupClient]] = None
    subsets: Optional[List[str]] = None
    typedInstances: Optional[List[TaEnumTaEnumTypeGroupClient]] = None


class WeeklyEarningsRequest(PydanticBaseModelEmptyStrIsNone):
    classExtensionInformation: Optional[List[ExtensionAttribute]] = Field(
        None,
        description="An array of the extensionAttribute objects which contain Earnings extension information.",
    )
    earningsBasis: str = Field(..., description="The earnings frequency.")
    effectiveDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    endDate: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    notes: Optional[str] = Field(
        None, description="Notes relating to the earnings period.", max_length=200, min_length=0
    )
    salaryAmountBasis: Optional[str] = Field(
        None, description="Basis for salary amount being used."
    )
    weeklyBasisEarningAmountsRequest: Optional[WeeklyBasisEarningAmountsRequest] = None


class AccountDetailEmbeddable(PydanticBaseModelEmptyStrIsNone):
    accountName: Optional[str] = Field(None, description="Name of the account holder")
    accountNo: Optional[str] = Field(None, description="The Account number of the personal account")
    accountType: Optional[AccountTypeResponse] = None
    bankCode: Optional[str] = Field(None, description="The Bank Code")
    currency: Optional[str] = Field(None, description="Currency identifier")
    extensions: Optional[Dict[str, Any]] = None
    routingNumber: Optional[str] = Field(None, description="Sort code of the Bank")


class AccountDetails(PydanticBaseModelEmptyStrIsNone):
    accountName: str = Field(
        ..., description="Name of the account holder", max_length=256, min_length=0
    )
    accountNo: str = Field(
        ..., description="The Account number of the personal account", max_length=256, min_length=0
    )
    accountType: Optional[str] = Field(
        None, description="Account type of the bank", max_length=256, min_length=0
    )
    bankCode: Optional[str] = Field(None, description="The Bank Code", max_length=4, min_length=0)
    classExtensionInformation: Optional[List[ExtensionAttribute]] = Field(
        None,
        description="An array of the extensionAttribute objects which contain claim extension information.",
    )
    routingNumber: str = Field(
        ..., description="Sort code of the Bank", max_length=256, min_length=0
    )


class ActualAbsencePeriodResource(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    actualDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    additionalBusinessInfo: Optional[Dict[str, str]] = Field(
        None, description="Additional Business Information."
    )
    additionalNotes: Optional[str] = Field(
        None, description="Enter any additional notes about the reported actual time."
    )
    endDateTime: Optional[datetime] = Field(
        None, description="ISO 8601 date time format", example="1999-12-31T23:59:59Z"
    )
    episodePeriodBasis: Optional[EpisodeDurationBasisResponse] = None
    episodePeriodDuration: Optional[int] = Field(
        None, description="Actual time/duration taken by the employee."
    )
    episodicLeaveRequestId: Optional[str] = Field(
        None, description="Id of the related absence period."
    )
    id: Optional[str] = Field(None, description="Unique id of the actual time record.")
    managerAccepted: Optional[ManagerAcceptedResponse] = None
    receivedVia: Optional[ReceivedViaResponse] = None
    reportedBy: Optional[ReportedByResponse] = None
    reportedDateTime: Optional[datetime] = Field(
        None, description="ISO 8601 date time format", example="1999-12-31T23:59:59Z"
    )
    reportingPartyName: Optional[str] = Field(
        None,
        description="The name of party that reported the actual associated to this reporting information.",
    )
    startDateTime: Optional[datetime] = Field(
        None, description="ISO 8601 date time format", example="1999-12-31T23:59:59Z"
    )
    status: Optional[StatusResponse] = None
    timeZone: Optional[TimeZoneResponse] = None
    type: Optional[TypeResponse] = None


class ActualAbsencePeriodResources(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, Any]] = None
    elements: Optional[List[ActualAbsencePeriodResource]] = None
    hasMoreElements: Optional[bool] = Field(
        None,
        description="<P>If the query has a <code>limit</code> set and the actual number of resources that match the query exceeds the limit this will be true if the number of resources is less than or equal to the limit this will be false.</P><P>Note:If the limit query pattern is not used, this value will not be returned.</P>",
    )
    meta: Optional[Dict[str, Any]] = None
    totalSize: Optional[int] = Field(
        None,
        description="<P>The <code>totalSize</code> is the total number of resources that match the query. This can be greater than the page size in a paginated query if there are more queries.</P><P>Note:This field will not be returned if the query has a <code>limit</code> set for it.</P>",
    )


class Address(PydanticBaseModelEmptyStrIsNone):
    addressLine1: Optional[str] = Field(
        None, description="Address Line 1", max_length=40, min_length=0
    )
    addressLine2: Optional[str] = Field(
        None, description="Address Line 2", max_length=40, min_length=0
    )
    addressLine3: Optional[str] = Field(
        None, description="Address Line 3", max_length=40, min_length=0
    )
    addressLine4: Optional[str] = Field(
        None, description="Address Line 4", max_length=40, min_length=0
    )
    addressLine5: Optional[str] = Field(
        None, description="Address Line 5", max_length=40, min_length=0
    )
    addressLine6: Optional[str] = Field(
        None, description="Address Line 6", max_length=40, min_length=0
    )
    addressLine7: Optional[str] = Field(
        None, description="Address Line 7", max_length=40, min_length=0
    )
    classExtensionInformation: Optional[List[ExtensionAttribute]] = Field(
        None,
        description="An array of the extensionAttribute objects which contain claim extension information.",
    )
    country: str = Field(..., description="Country", max_length=100, min_length=0)
    postCode: Optional[str] = Field(
        None, description="International Postcode", max_length=40, min_length=0
    )
    premiseNo: Optional[str] = Field(
        None, description="This is the premise number", max_length=5, min_length=0
    )


class AddressEmbeddable(PydanticBaseModelEmptyStrIsNone):
    addressLine1: Optional[str] = Field(None, description="First line of an address.")
    addressLine2: Optional[str] = Field(None, description="Second line of an address.")
    addressLine3: Optional[str] = Field(None, description="Third line of an address.")
    addressLine4: Optional[str] = Field(None, description="Fourth line of an address.")
    addressLine5: Optional[str] = Field(None, description="Fifth line of an address.")
    addressLine6: Optional[str] = Field(None, description="Sixth line of an address.")
    addressLine7: Optional[str] = Field(None, description="Seventh line of an address.")
    country: Optional[CountryResponse] = None
    extensions: Optional[Dict[str, Any]] = None
    extraAustralianFields: Optional[AustralianAddressEmbeddable] = None
    postCode: Optional[str] = Field(None, description="International Postcode.")
    premiseNo: Optional[str] = Field(None, description="The premise number i.e. the street number.")


class AltGroupClientUserListSchema(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, Any]] = None
    elements: Optional[List[GroupClientUser]] = None
    hasMoreElements: Optional[bool] = Field(
        None,
        description="<P>If the query has a <code>limit</code> set and the actual number of resources that match the query exceeds the limit this will be true if the number of resources is less than or equal to the limit this will be false.</P><P>Note:If the limit query pattern is not used, this value will not be returned.</P>",
    )
    meta: Optional[Dict[str, Any]] = None
    totalSize: Optional[int] = Field(
        None,
        description="<P>The <code>totalSize</code> is the total number of resources that match the query. This can be greater than the page size in a paginated query if there are more queries.</P><P>Note:This field will not be returned if the query has a <code>limit</code> set for it.</P>",
    )


class ApiBaseErrorResponse(PydanticBaseModelEmptyStrIsNone):
    errors: Optional[List[ApiError]] = None


class ApiBaseErrorResponseGroupClient(PydanticBaseModelEmptyStrIsNone):
    errors: Optional[List[ApiErrorGroupClient]] = None


class BenefitSummary(PydanticBaseModelEmptyStrIsNone):
    benefitCaseType: Optional[str] = Field(
        None, description="The name by which the benefit type is referred."
    )
    benefitHandler: Optional[str] = Field(
        None, description="The person that is handling the benefit."
    )
    benefitHandlerEmailAddress: Optional[str] = Field(
        None, description="The email address of the benefit handler."
    )
    benefitHandlerPhoneNo: Optional[str] = Field(
        None, description="The phone number of the benefit handler."
    )
    benefitId: Optional[str] = Field(None, description="The case number of the benefit.")
    benefitIncurredDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    benefitRightCategory: Optional[str] = Field(
        None, description="Information about the category of the benefit right."
    )
    creationDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    customerName: Optional[str] = Field(None, description="The full name of the customer.")
    description: Optional[str] = Field(
        None, description="Description for this case as entered by the user."
    )
    extensionAttributes: Optional[List[ExtensionAttribute]] = Field(
        None, description="Extra attributes from extension if any."
    )
    policyReferences: Optional[str] = Field(
        None, description="The reference(s) to the policy from within the relevant admin system."
    )
    stageName: Optional[str] = Field(None, description="The current stage of benefit claim case.")
    status: Optional[str] = Field(
        None, description="This is the stage in the process that the case is currently at."
    )


class BulkCreateActualAbsencePeriodCommand(PydanticBaseModelEmptyStrIsNone):
    elements: List[CreateActualAbsencePeriodCommand]


class BulkCreateCommandCreateActualAbsencePeriodCommand(PydanticBaseModelEmptyStrIsNone):
    elements: List[CreateActualAbsencePeriodCommand]


class CaseContactSummary(PydanticBaseModelEmptyStrIsNone):
    classAttributes: Optional[List[ExtensionAttribute]] = Field(
        None, description="Optional extension properties"
    )
    contactId: Optional[int] = Field(None, description="The ID of the contact")
    customerName: Optional[str] = Field(None, description="The party with whom contact was made.")
    customerRepresentative: Optional[str] = Field(None, description="The customer representative.")
    date: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    description: Optional[str] = Field(None, description="The contact description")
    direction: Optional[str] = Field(None, description="Whether it is incoming or outgoing contact")
    durationInMinutes: Optional[int] = Field(
        None, description="The duration of the contact in minutes"
    )
    manner: Optional[str] = Field(
        None,
        description="The manner of the person making the contact e.g. Interested, Uninterested etc.",
    )
    methodOfContact: Optional[str] = Field(
        None, description="The method of contact. E.g. Email, Phone etc."
    )
    outcome: Optional[str] = Field(
        None, description="The outcome of the contact e.g. Unknown, Sale, No Sale, No Show etc."
    )
    reason: Optional[str] = Field(None, description="The reason for the contact")


class ClaimOccupation(PydanticBaseModelEmptyStrIsNone):
    additionalEmploymentCategory: Optional[str] = Field(
        None, description="The alternate employment category."
    )
    dateJobBegan: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    dateJobEnded: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    daysWorkedPerWeek: Optional[float] = Field(
        None, description="The number of days worked per week."
    )
    employeeId: Optional[str] = Field(
        None,
        description="The id of an employee in respect of its employer.",
        max_length=50,
        min_length=0,
    )
    employer: Optional[str] = Field(None, description="The name of the employer.")
    employmentCategory: Optional[str] = Field(None, description="The employment category.")
    employmentLocation: Optional[str] = Field(None, description="The employment location code.")
    employmentStatus: Optional[str] = Field(None, description="The employment status.")
    employmentTitle: Optional[str] = Field(None, description="The employment title.")
    endEmploymentReason: Optional[str] = Field(None, description="The code for occupation ending.")
    endPosReason: Optional[str] = Field(None, description="The reason for job ending.")
    extensionAttributes: Optional[List[ExtensionAttribute]] = Field(
        None, description="The extension attributes."
    )
    hoursWorkedPerWeek: Optional[float] = Field(
        None, description="The number of hours worked per week."
    )
    jobDesc: Optional[str] = Field(None, description="The job description.")
    jobStrenuous: Optional[str] = Field(None, description="The job strenuous.")
    jobTitle: Optional[str] = Field(None, description="The job title.")
    primary: Optional[bool] = Field(None, description="Is occupation the primary one?")
    remarks: Optional[str] = Field(None, description="The occupation remarks.")
    selfEmployed: Optional[bool] = Field(
        None, description="Is occupation considered self-employed?"
    )
    workPatternBasis: Optional[str] = Field(None, description="The work pattern basis.")
    workScheduleDescription: Optional[str] = Field(
        None, description="The work schedule description."
    )


class ClaimSummary(PydanticBaseModelEmptyStrIsNone):
    caseComplexity: Optional[str] = Field(None, description="Case complexity on Claim")
    claimHandler: Optional[str] = Field(
        None, description="The Person that is handling the claim.", max_length=50, min_length=0
    )
    claimHandlerEmailAddress: Optional[str] = Field(
        None, description="The email address of the claim handler.", max_length=40, min_length=0
    )
    claimHandlerPhoneNo: Optional[str] = Field(
        None, description="The phone number of the claim handler.", max_length=20, min_length=0
    )
    claimId: str = Field(
        ..., description="The claim number of the claim", max_length=256, min_length=0
    )
    claimType: str = Field(
        ...,
        description="The name by which the claim type is referred.",
        max_length=50,
        min_length=0,
    )
    classExtensionInformation: Optional[List[ExtensionAttribute]] = Field(
        None,
        description="An array of the extensionAttribute objects which contain extension information.",
    )
    creationDate: Optional[datetime] = Field(
        None, description="ISO 8601 date time format", example="1999-12-31T23:59:59Z"
    )
    customerName: Optional[str] = Field(
        None, description="The customer name of the claim", max_length=256, min_length=0
    )
    description: Optional[str] = Field(
        None,
        description="Description for this case as entered by the user.",
        max_length=2000,
        min_length=0,
    )
    effectiveDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    notificationCaseId: Optional[str] = Field(
        None,
        description="The notification case number of the claim case if applicable.",
        max_length=256,
        min_length=0,
    )
    notificationDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    policyReferences: Optional[str] = Field(
        None,
        description="The reference(s) to the policy from within the relevant admin system.",
        max_length=50,
        min_length=0,
    )
    stageName: Optional[str] = Field(None, description="The current stage of claim case.")
    status: Optional[str] = Field(
        None,
        description="This is the stage in the process that the case is currently at.",
        max_length=10,
        min_length=0,
    )


class ContactTimePreferenceEmbeddable(PydanticBaseModelEmptyStrIsNone):
    friday: Optional[bool] = Field(
        None, description="Indicates whether Friday is suitable for contact"
    )
    fromTime: Optional[str] = Field(None, description="Time from which contact is allowed.")
    id: Optional[str] = Field(None, description="Unique identifier of the contact time preference")
    monday: Optional[bool] = Field(
        None, description="Indicates whether Monday is suitable for contact"
    )
    preferredContactTime: Optional[PreferredContactTimeResponse] = None
    saturday: Optional[bool] = Field(
        None, description="Indicates whether Saturday is suitable for contact"
    )
    sunday: Optional[bool] = Field(
        None, description="Indicates whether Sunday is suitable for contact"
    )
    thursday: Optional[bool] = Field(
        None, description="Indicates whether Thursday is suitable for contact"
    )
    timeZone: Optional[ContactTimeZoneResponse] = None
    toTime: Optional[str] = Field(None, description="Time until which contact is allowed.")
    tuesday: Optional[bool] = Field(
        None, description="Indicates whether Tuesday is suitable for contact"
    )
    wednesday: Optional[bool] = Field(
        None, description="Indicates whether Wednesday is suitable for contact"
    )


class ContractualEarnings(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    amount: Optional[AmountMoneyResponse] = None
    earningsType: Optional[EarningsTypeResponse] = None
    effectiveDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    endDate: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    extensions: Optional[Dict[str, Any]] = None
    frequency: Optional[EarningsBasisFrequencyResponse] = None
    id: Optional[str] = None
    standardHourlyRate: Optional[StandardHourlyRateMoneyResponse] = None
    standardHours: Optional[str] = Field(
        None,
        description="Number of hours in a standard working period. In use for Weekly and Bi-Weekly frequcies only, when TotalAmountOverrride is false",
    )
    totalAmountOverride: Optional[bool] = Field(
        None,
        description="For Weekly and Bi-Weekly frequencies, determines whether earnings were specified using Amount (true) or StandardHours and StandardHourlyRate.",
    )


class ContractualEarningsList(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, Any]] = None
    elements: Optional[List[ContractualEarnings]] = None
    hasMoreElements: Optional[bool] = Field(
        None,
        description="<P>If the query has a <code>limit</code> set and the actual number of resources that match the query exceeds the limit this will be true if the number of resources is less than or equal to the limit this will be false.</P><P>Note:If the limit query pattern is not used, this value will not be returned.</P>",
    )
    meta: Optional[Dict[str, Any]] = None
    totalSize: Optional[int] = Field(
        None,
        description="<P>The <code>totalSize</code> is the total number of resources that match the query. This can be greater than the page size in a paginated query if there are more queries.</P><P>Note:This field will not be returned if the query has a <code>limit</code> set for it.</P>",
    )


class CreateWeekBasedWorkPatternCommand(PydanticBaseModelEmptyStrIsNone):
    patternStartDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    patternStatus: Optional[PatternStatusRequest] = None
    workPatternDays: List[CreateWorkPatternDayCommand] = Field(
        ...,
        description="List of days which make up the pattern. There can be 7, 14, 21 or 28 of these.",
    )
    workPatternType: WorkPatternTypeRequest
    workWeekStarts: Optional[WorkWeekStartsRequest] = None


class CustomerInfo(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    address: Optional[AddressEmbeddable] = None
    customerNo: Optional[str] = Field(None, description="Customer number")
    dateOfBirth: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    dateOfDeath: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    extensions: Optional[Dict[str, Any]] = None
    firstName: Optional[str] = Field(None, description="Person's first name")
    gender: Optional[GenderResponse] = None
    id: Optional[str] = None
    idNumber: Optional[str] = Field(
        None,
        description="ID number of the claimant. The ID number could be something like social security number, or tax identificaiton number. They ID type is indicated by the value of the <code>identificationNumberType</code> field.",
    )
    identificationNumberType: Optional[IdentificationNumberTypeResponse] = None
    initials: Optional[str] = Field(None, description="Person's middle initials")
    isDeceased: Optional[bool] = Field(None, description="The person is deceased flag")
    lastName: Optional[str] = Field(None, description="Person's last name")
    maritalStatus: Optional[MaritalStatusResponse] = None
    nationality: Optional[NationalityResponse] = None
    needsInterpreter: Optional[bool] = Field(
        None, description="Indicates if the customer needs an interpreter"
    )
    partyType: Optional[PartyTypeResponse] = None
    placeOfBirth: Optional[str] = Field(None, description="Place of Birth")
    secondName: Optional[str] = Field(None, description="Person's second name")
    securedClient: Optional[bool] = Field(None, description="Secured client flag")
    staff: Optional[bool] = Field(None, description="Staff flag")
    title: Optional[TitleResponse] = None


class CustomerOccupation(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    adjustedJobStartDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    altEmploymentCat: Optional[AltEmploymentCatResponse] = None
    cbaValue: Optional[str] = Field(None, description="Collective bargaining agreement code")
    daysWorkedPerWeek: Optional[str] = Field(
        None,
        description="The number of days worked per week, which can be specified with partial days",
    )
    empLocationCode: Optional[EmpLocationCodeResponse] = None
    employeeIdentifier: Optional[str] = Field(
        None, description="Reference identifier of an employee in respect of its employer"
    )
    employmentCat: Optional[EmploymentCatResponse] = None
    employmentStatus: Optional[EmploymentStatusResponse] = None
    employmentTitle: Optional[EmploymentTitleResponse] = None
    employmentType: Optional[EmploymentTypeResponse] = None
    employmentWorkState: Optional[EmploymentWorkStateResponse] = None
    endPosCode: Optional[EndPosCodeResponse] = None
    endPosReason: Optional[str] = Field(None, description="he reason for job ending")
    extensions: Optional[Dict[str, Any]] = None
    hoursWorkedPerYear: Optional[int] = Field(
        None, description="Number of hours an employee has worked in the past 12 months"
    )
    hrsWorkedPerWeek: Optional[str] = Field(
        None,
        description="The number of hours worked per week, which can be specified using partial hours",
    )
    id: Optional[str] = None
    jobDesc: Optional[str] = Field(None, description="Job description")
    jobEndDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    jobStartDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    jobStrenuous: Optional[JobStrenuousResponse] = None
    jobTitle: Optional[str] = Field(None, description="Job title")
    keyEmployee: Optional[bool] = Field(
        None, description="Is the employee classified as a Key Employee"
    )
    managerName: Optional[str] = Field(None, description="The name of the manager of the employee")
    managerReference: Optional[str] = Field(
        None, description="The customer number that uniquely identifies the manager of the employee"
    )
    memberGroups: Optional[List[MemberGroupEmbeddable]] = Field(
        None,
        description="A list of member groups that reflect the customer's place within their employer's structure. If the customer is not linked to member groups then an empty list is returned.",
    )
    occCodeName: Optional[str] = Field(
        None,
        description="The name(s) associated with the specific Occupation Industry Classification Code id",
    )
    occCodeReference: Optional[str] = Field(
        None, description="Occupation Industry Classification Code id"
    )
    occupationQualifiers: Optional[List[OccupationQualifierEmbeddable]] = Field(
        None,
        description="List of occupation qualifier(s) which have been applied to this employment",
    )
    orgUnitName: Optional[str] = Field(
        None,
        description="The name of the organisation unit within the employer organisation structure - reference only field",
    )
    overrideDaysWorkedPerWeek: Optional[bool] = Field(
        None, description="Is override days worked per week true or false?"
    )
    remarks: Optional[str] = Field(None, description="Remarks about the Occupation")
    timeZone: Optional[TimeZoneResponse] = None
    withinFMLACriteria: Optional[bool] = Field(
        None,
        description="Is the employee working within Federal FMLA defined radius i.e. 50 employees within 75 miles",
    )
    workPatternBasis: Optional[str] = Field(
        None, description="Indicates the type of work pattern in use - reference only field"
    )
    workSchDesc: Optional[str] = Field(None, description="Work schedule description")
    workingAtHome: Optional[bool] = Field(None, description="Is the employee working at home")
    worksiteName: Optional[str] = Field(
        None,
        description="The name of the employer location which the occupation is related to - reference only field",
    )


class CustomerOccupationWeekBasedWorkPattern(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    patternStartDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    patternStatus: Optional[PatternStatusResponse] = None
    workPatternDays: Optional[List[WorkPatternDayResponse]] = Field(
        None,
        description="List of days which make up the pattern. There can be 7, 14, 21 or 28 of these.",
    )
    workPatternType: Optional[WeekBasedWorkPatternTypeResponse] = None
    workWeekStarts: Optional[WorkWeekStartsResponse] = None


class CustomerOccupations(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, Any]] = None
    elements: Optional[List[CustomerOccupation]] = None
    hasMoreElements: Optional[bool] = Field(
        None,
        description="<P>If the query has a <code>limit</code> set and the actual number of resources that match the query exceeds the limit this will be true if the number of resources is less than or equal to the limit this will be false.</P><P>Note:If the limit query pattern is not used, this value will not be returned.</P>",
    )
    meta: Optional[Dict[str, Any]] = None
    totalSize: Optional[int] = Field(
        None,
        description="<P>The <code>totalSize</code> is the total number of resources that match the query. This can be greater than the page size in a paginated query if there are more queries.</P><P>Note:This field will not be returned if the query has a <code>limit</code> set for it.</P>",
    )


class Decision(PydanticBaseModelEmptyStrIsNone):
    absence: Optional[Absence] = None
    employee: Optional[PeriodDecisionsEmployee] = None
    period: Optional[Period] = None


class DisabilityBenefit(PydanticBaseModelEmptyStrIsNone):
    accidentBasisOfMaxBenefitPeriod: Optional[str] = Field(
        None, description="Basis for the maximum period for an accident."
    )
    accidentEliminationPeriod: Optional[int] = Field(
        None, description="Elimination Period units for accident."
    )
    accidentMaxBenefitPeriod: Optional[int] = Field(
        None,
        description="Number of units in the maximum period for an accident. The maximum length of the period to be paid if the customer remains disabled.",
    )
    administrationType: Optional[str] = Field(
        None, description="Specify the AdministrationType for the Funding Arrangement."
    )
    adviceToPayOverride: Optional[str] = Field(
        None,
        description="The advice provided by the insurer with regard to an individual benefit can be overridden by the employer, either regarding the decision (to pay or not to pay) or the calculated amount.",
    )
    amountType: Optional[str] = Field(
        None,
        description="Indicates the basis upon which the benefit is calculated, examples might be fixed amount basis, salary percentage basis or tiered basis.",
    )
    approvedThroughDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    basisOfAccidentEliminationPeriod: Optional[str] = Field(
        None, description="Basis for elimination period for accident."
    )
    basisOfEliminationPeriod: Optional[str] = Field(
        None, description="Basis for elimination period."
    )
    basisOfLateEnrollmentPeriod: Optional[str] = Field(
        None,
        description="Basis of the period after enrolment date when customer has to wait for coverage to become eligible (i.e. day, week month or year).",
    )
    basisOfMaxBenefitPeriod: Optional[str] = Field(
        None,
        description="Basis of the maximum benefit period. The maximum length of the period to be paid if the claimant remains disabled.",
    )
    basisOfMinBenefitPeriod: Optional[str] = Field(
        None,
        description="Basis of the minimum benefit period. The minimum length of the period to be paid if the claimant remains disabled.",
    )
    basisOfMinimumQualifyPeriod: Optional[str] = Field(
        None,
        description="Basis of the period of time policy has to be in force before the minimum benefit provision can be applied (i.e. day, week month or year).",
    )
    basisOfPolicyWaitingPeriod: Optional[str] = Field(
        None,
        description="Basis of the period of time that the policy must be in force before the insured qualifies for coverage (i.e. day, week month or year).",
    )
    benefitEndDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    benefitIncurredDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    benefitStartDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    benefitType: Optional[str] = Field(
        None,
        description="Type of a benefit right such as Disability Income - STD, Disability Income LTD, Accelerated Death Benefit or Accidental Death Benefit.",
    )
    brokerAuthorisationFlag: Optional[bool] = Field(
        None,
        description="Flag that indicates if the broker is to be included in the correspondence.",
    )
    earliestDateForClaimPayment: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    eliminationPeriod: Optional[int] = Field(None, description="The number of Elimination Days.")
    employeeContributionPercentage: Optional[float] = Field(
        None, description="Percentage representing the Employee Contribution Percentage."
    )
    employeeContributionStatus: Optional[str] = Field(
        None,
        description="Field which indicates whether a policy is contributory or non-contributory.",
    )
    employeePremiumTaxation: Optional[str] = Field(
        None, description="The employee portion of the contribution was made pre or post tax."
    )
    employerContributionPercentage: Optional[float] = Field(
        None,
        description="Indicates the percent of the payment that is taxable. Typically used as the Employer contribution.",
    )
    employerPremiumTaxation: Optional[str] = Field(
        None, description="The employer portion of the contribution was made pre tax or gross up."
    )
    expectedResolutionDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    extensionAttributes: Optional[List[ExtensionAttribute]] = Field(
        None,
        description="The array will contain extension attributes from Benefit and Benefit Right objects.",
    )
    frequencyAmount: Optional[Decimal] = Field(
        None, description="Money amount format", example="1500.50"
    )
    hospitalBasisOfEliminationPeriod: Optional[str] = Field(
        None, description="Basis for elimination period for hospitalisation."
    )
    hospitalBasisOfMaxBenefitPeriod: Optional[str] = Field(
        None, description="Basis of the maximum period for hospitalization."
    )
    hospitalEliminationPeriod: Optional[int] = Field(
        None, description="Elimination Period for hospitalisation."
    )
    hospitalMaxBenefitPeriod: Optional[int] = Field(
        None, description="Number of units in the maximum period for hospitalization."
    )
    hospitalizationClauseApplies: Optional[bool] = Field(
        None, description="Indicator which determines whether the Hospitalization clause applies."
    )
    initialNotificationDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    isReimbursement: Optional[bool] = Field(
        None, description="Indicates if the benefit is paid as a reimbursement or not."
    )
    isUnderwritten: Optional[bool] = Field(
        None,
        description="Indicates whether or not the member has been underwritten in order to have additional cover beyond what the group membership offers.",
    )
    lateEnrollmentPeriod: Optional[int] = Field(
        None,
        description="Period after enrolment date when customer has to wait for coverage to become eligible.",
    )
    latestDateForClaimPayment: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    maxBenefitPeriod: Optional[int] = Field(
        None,
        description="Number of units in the maximum benefit period. The maximum length of the period to be paid if the claimant remains disabled",
    )
    minBenefitPeriod: Optional[int] = Field(
        None,
        description="Number of units in the minimum benefit period. The minimum length of the period to be paid if the claimant remains disabled.",
    )
    minimumQualifyPeriod: Optional[int] = Field(
        None,
        description="Period of time policy has to be in force before the minimum benefit provision can be applied.",
    )
    notificationReceivedDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    overrideClaimIncurredDate: Optional[bool] = Field(
        None,
        description="Indicator which specifies that the benefit level incurred date should be used rather than the claim level incurred date.",
    )
    percentTaxable: Optional[float] = Field(
        None, description="The percent of the payment that is taxable."
    )
    percentageNonTaxable: Optional[float] = Field(
        None, description="The Employee Contribution Percentage"
    )
    periodType: Optional[str] = Field(
        None,
        description="The type of Benefit Period used to derive the Approved Through Date. Indicates whether the approval is for a fully certified or partially certified period.",
    )
    policyWaitingPeriod: Optional[int] = Field(
        None,
        description="Period of time that the policy must be in force before the insured qualifies for coverage.",
    )
    serviceLevel: Optional[str] = Field(
        None,
        description='Specify the ServiceLevel for the Funding Arrangement when AdministrationType is "ASO - Advice To Pay"',
    )
    sourceOfRequest: Optional[str] = Field(
        None,
        description="The type of source that is requesting the benefit, such as claimant, guardian, or medical officer.",
    )
    startDateOfBenefitForClaim: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )


class DisabilityClaim(PydanticBaseModelEmptyStrIsNone):
    accidentDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    accidentLocation: Optional[str] = Field(
        None, description="State where Accident Occurred.", max_length=100, min_length=0
    )
    accidentTime: Optional[str] = Field(
        None,
        description="Indicate if the incident occurred in the morning or afternoon",
        max_length=100,
        min_length=0,
    )
    accidentType: Optional[str] = Field(
        None, description="Accident Type<BR><BR>CM<BR> ", max_length=100, min_length=0
    )
    actualPartialReturnToWorkDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    actualReturnToWorkDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    claimAdditionalInfo: Optional[str] = Field(
        None,
        description="Additional incident details relevant to the claim.",
        max_length=4000,
        min_length=0,
    )
    claimIncurredDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    claimType: Optional[str] = Field(
        None,
        description="This field can be used to capture the type of claim. This is a descriptive attribute which can be changed over the life of the claim",
        max_length=10,
        min_length=0,
    )
    classExtensionInformation: Optional[List[ExtensionAttribute]] = Field(
        None,
        description="An array of the extensionAttribute objects which contain claim extension information.",
    )
    dateSymptomsFirstAppeared: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    disabilityDateFromCustomer: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    dismembermentOrLoss: Optional[str] = Field(
        None,
        description="Dismemberment/Loss injuries sustained by the customer.",
        max_length=10,
        min_length=0,
    )
    employerDateLastWorked: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    eventType: Optional[str] = Field(
        None,
        description="The type of event which occurred which led to the disability claim e.g. Accident or Sickness.",
        max_length=10,
        min_length=0,
    )
    expectedReturnToWorkDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    firstDayMissedWork: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    hoursWorked: Optional[float] = Field(
        None, description="Number of hours worked", ge=0.0, le=13.0
    )
    insuredSpouseWorking: Optional[bool] = Field(
        None, description="Indicates if the spouse of the insured is currently working."
    )
    notificationDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    numberOfDependents: Optional[int] = Field(
        None,
        description="The number of dependents for the Customer relevant to this Claim",
        ge=0,
        le=10,
    )
    partialReturnToWorkDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    providerPartialReturnToWorkDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    providerReturnToWorkDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    reasonForClaimEffectiveDateChange: Optional[str] = Field(
        None,
        description="Allows a reason to be recorded for a change to the claim's effective date.",
        max_length=10,
        min_length=0,
    )
    releasedPartialReturnToWorkDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    releasedReturnToWorkDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    returnToWorkIntention: Optional[str] = Field(
        None, description="Indicates the employees return to work plans."
    )
    returnToWorkPartTimeDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    returnToWorkTargetDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    salaryCountNumDays: Optional[int] = Field(
        None,
        description="Salary Continuance Number of Days - Number of days beyond the disability date insured received full pay",
        ge=0,
        le=5,
    )
    source: Optional[str] = Field(
        None,
        description="Describes how the claim was initiated, i.e. on-line, phone call, paper application form, customer portal.",
        max_length=10,
        min_length=0,
    )
    workHistory: Optional[str] = Field(
        None,
        description="This lists the work history of the insured including occupational, job duty as well as when the insured was employed at these jobs.  Used for any/all and LCDD decisions as well as assisting with job  placement strategy development of the vocational consultant.",
        max_length=256,
        min_length=0,
    )
    workRelated: Optional[bool] = Field(
        None, description="Indicates whether the event was work related or not."
    )


class EForm(PydanticBaseModelEmptyStrIsNone):
    eformAttributes: Optional[List[EFormAttribute]] = Field(
        None, description="An array of EForm attributes."
    )
    eformId: int = Field(..., description="Unique automatically generated Id of an EForm document.")
    eformType: Optional[str] = Field(
        None, description="Name of the EForm document type", max_length=200, min_length=0
    )


class EarningsResponse(PydanticBaseModelEmptyStrIsNone):
    classExtensionInformation: Optional[List[ExtensionAttribute]] = Field(
        None,
        description="An array of the extensionAttribute objects which contain Earnings extension information.",
    )
    earningsBasis: str = Field(..., description="The earnings frequency.")
    earningsId: int = Field(
        ..., description="The unique identifier for the earnings record.", ge=0, le=32
    )
    earningsType: str = Field(
        ...,
        description="The type of earnings record, it could refer to pre-disability, partial or salary.",
    )
    effectiveDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    endDate: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    monthlyBasisEarningAmountsResponse: Optional[MonthlyBasisEarningAmountsResponse] = None
    notes: Optional[str] = Field(
        None, description="Notes relating to the earnings period.", max_length=200, min_length=0
    )
    periodInWeeks: int = Field(
        ..., description="Period in weeks between Effective Date and End Date of Earnings record."
    )
    salaryAmountBasis: Optional[str] = Field(
        None, description="Basis for salary amount being used."
    )
    weeklyBasisEarningAmountsResponse: Optional[WeeklyBasisEarningAmountsResponse] = None


class EmailAddress(PydanticBaseModelEmptyStrIsNone):
    classExtensionInformation: Optional[List[ExtensionAttribute]] = Field(
        None,
        description="An array of the extensionAttribute objects which contain email Address extension information.",
    )
    emailAddress: Optional[str] = Field(
        None, description="Customers email address.", max_length=120, min_length=0
    )
    emailAddressType: str = Field(
        ..., description="Identifies the type of mail which is returned (Enum Domain=51)"
    )
    id: Optional[int] = Field(
        None, description="The id of the contact method (e.g. phone / mobile / emailAddress) ", ge=0
    )
    preferred: Optional[bool] = Field(
        None, description="Specify if it is the first person to try to contact when it is required."
    )


class ExtendedAddress(PydanticBaseModelEmptyStrIsNone):
    buildingName1: Optional[str] = Field(
        None, description="The name of the building.", max_length=30, min_length=0
    )
    buildingName2: Optional[str] = Field(
        None, description="The name of the building continued.", max_length=30, min_length=0
    )
    classExtensionInformation: Optional[List[ExtensionAttribute]] = Field(
        None,
        description="An array of the extensionAttribute objects which contain claim extension information.",
    )
    dpid: Optional[float] = Field(
        None, description="DPID - 8 digit Australian post address identifier.", ge=0.0, le=8.0
    )
    floorLevelNumber: Optional[str] = Field(
        None, description="The number of floor level.", max_length=2, min_length=0
    )
    floorLevelType: Optional[str] = Field(
        None, description="Type of floor level description.", max_length=10, min_length=0
    )
    lotNumber: Optional[str] = Field(
        None, description="The lot number.", max_length=6, min_length=0
    )
    postalNumber: Optional[str] = Field(
        None, description="The postal number.", max_length=11, min_length=0
    )
    postalNumberPrefix: Optional[str] = Field(
        None,
        description="This refers to the non-numeric portion preceding the Postal Number.",
        max_length=3,
        min_length=0,
    )
    postalNumberSuffix: Optional[str] = Field(
        None,
        description="This refers to the non-numeric portion following the Postal Number.",
        max_length=3,
        min_length=0,
    )
    postalType: Optional[str] = Field(
        None,
        description="The postal type - PO Box, Roadside Mail Box etc.",
        max_length=10,
        min_length=0,
    )
    premiseNoSuffix: Optional[str] = Field(
        None,
        description="The suffix to apply to the number of the premise.",
        max_length=1,
        min_length=0,
    )
    premiseNoTo: Optional[float] = Field(
        None, description="The high number in a range for the premise.", ge=0.0, le=5.0
    )
    premiseNoToSuffix: Optional[str] = Field(
        None,
        description="The suffix to apply to the number to of the premise.",
        max_length=1,
        min_length=0,
    )
    streetSuffix: Optional[str] = Field(
        None, description="The suffix for the street.", max_length=10, min_length=0
    )


class LeaveInfo(PydanticBaseModelEmptyStrIsNone):
    leaveType: Optional[LeaveTypeResponse] = None
    timeApproved: Optional[str] = Field(
        None,
        description="The amount of time approved in this period, in the specified time entitlement basis e.g. number of Weeks, Days.",
    )
    timeApprovedBasis: Optional[TimeApprovedBasisResponse] = None


class Notification(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    adminGroup: Optional[str] = Field(
        None, description="Administrative group of the notification case."
    )
    caseHandler: Optional[CaseHandlerEmbeddable] = None
    caseNumber: Optional[str] = Field(None, description="The Notification's case number")
    createdDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    customer: Optional[CustomerResource] = None
    expectedRTWDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    id: Optional[str] = Field(None, description="An id to uniquely identify the Notification")
    memberGroups: Optional[List[MemberGroupEmbeddable]] = Field(
        None,
        description="A list of member groups that reflect the customer's place within their employer's structure. If the customer is not linked to member groups then an empty list is returned.",
    )
    notificationDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    notificationReason: Optional[NotificationReasonResponse] = None
    notifiedBy: Optional[NotifedByResponse] = None
    status: Optional[str] = Field(None, description="The status of the Notification")
    subCases: Optional[List[CaseEmbeddable]] = Field(
        None, description="Child cases of the Notification Case"
    )


class NotificationCaseSummaryForClaims(PydanticBaseModelEmptyStrIsNone):
    accidentDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    accidentLocation: Optional[str] = Field(None, description="State where Accident Occurred")
    accidentTime: Optional[str] = Field(
        None, description="Indicates if the incident occurred in the morning or afternoon"
    )
    accidentType: Optional[str] = Field(None, description="Accident Type")
    actualDeliveryDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    actualPartialReturnToWorkDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    actualReturnToWorkDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    claims: Optional[List[NotificationClaimSummary]] = Field(
        None, description="The child cases under this notification case."
    )
    createdDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    dateFirstMissingWork: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    expectedDeliveryDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    expectedRTWDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    multipleConflictingAccidentDates: Optional[bool] = Field(
        None,
        description="Multiple conflicting accident dates found.If true,accidentDate attribute will be empty.",
    )
    notificationCaseId: Optional[str] = Field(
        None, description="The case number for the notification case."
    )
    notificationDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    notificationReason: Optional[str] = Field(
        None,
        description="Notification reason selected during notification case intake.",
        max_length=100,
        min_length=0,
    )
    partialReturnToWorkDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    releasedPartialReturnToWorkDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    releasedReturnToWorkDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    returnToWorkIntention: Optional[str] = Field(
        None,
        description="Indicates the employees return to work plans once they have finished their leave.",
    )
    status: Optional[str] = Field(
        None,
        description="The notification case status his is the current phase of the notification case managing process.",
    )


class Notifications(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, Any]] = None
    elements: Optional[List[Notification]] = None
    hasMoreElements: Optional[bool] = Field(
        None,
        description="<P>If the query has a <code>limit</code> set and the actual number of resources that match the query exceeds the limit this will be true if the number of resources is less than or equal to the limit this will be false.</P><P>Note:If the limit query pattern is not used, this value will not be returned.</P>",
    )
    meta: Optional[Dict[str, Any]] = None
    totalSize: Optional[int] = Field(
        None,
        description="<P>The <code>totalSize</code> is the total number of resources that match the query. This can be greater than the page size in a paginated query if there are more queries.</P><P>Note:This field will not be returned if the query has a <code>limit</code> set for it.</P>",
    )


class Participant(PydanticBaseModelEmptyStrIsNone):
    correspondenceAddress: Optional[Address] = None
    extendedAddress: Optional[ExtendedAddress] = None
    participantSummary: CaseParticipantsSummary
    personDetails: Optional[PersonDetails] = None


class PaymentLine(PydanticBaseModelEmptyStrIsNone):
    amount: Optional[Decimal] = Field(None, description="Money amount format", example="1500.50")
    endDate: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    integrationType: Optional[str] = Field(
        None,
        description="For adjustment PaymentLine this records the IntegrationType which may have been recorded for the adjustment",
        max_length=100,
        min_length=0,
    )
    lineType: Optional[str] = Field(
        None,
        description="The name of the payment adjustment e.g. Gross Benefit, Income Tax, Seat Belt etc.",
        max_length=100,
        min_length=0,
    )
    paymentLinesDetail: Optional[List[PaymentLineDetails]] = Field(
        None, description="The Payment Lines Details belongs to each payment."
    )
    reference: Optional[str] = Field(
        None,
        description="For adjustment PaymentLine this records the Reference which may have been recorded for the adjustment",
        max_length=250,
        min_length=0,
    )
    startDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )


class PaymentPreferenceAddressEmbeddable(PydanticBaseModelEmptyStrIsNone):
    addressLine1: Optional[str] = Field(None, description="First line of an address.")
    addressLine2: Optional[str] = Field(None, description="Second line of an address.")
    addressLine3: Optional[str] = Field(None, description="Third line of an address.")
    addressLine4: Optional[str] = Field(None, description="Fourth line of an address.")
    addressLine5: Optional[str] = Field(None, description="Fifth line of an address.")
    addressLine6: Optional[str] = Field(None, description="Sixth line of an address.")
    addressLine7: Optional[str] = Field(None, description="Seventh line of an address.")
    country: Optional[CountryResponse] = None
    extensions: Optional[Dict[str, Any]] = None
    extraAustralianFields: Optional[PaymentPreferenceAustralianAddressEmbeddable] = None
    postCode: Optional[str] = Field(None, description="International Postcode.")
    premiseNo: Optional[str] = Field(None, description="The premise number i.e. the street number.")


class PaymentPreferenceResource(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    accountDetail: Optional[AccountDetailEmbeddable] = None
    address: Optional[PaymentPreferenceAddressEmbeddable] = None
    bulkPayee: Optional[bool] = Field(
        None,
        description="This is used to indicate a high frequency client. If it is set to true then a negative payment may be created with a view to recovering from another payment, rather than attempting to recover the overpayment.",
    )
    chequeDetail: Optional[ChequeDetailEmbeddable] = None
    default: Optional[bool] = None
    description: Optional[str] = Field(None, description="Payment preference description.")
    effectiveFromDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    effectiveToDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    extensions: Optional[Dict[str, Any]] = None
    id: Optional[str] = None
    identifier: Optional[str] = Field(None, description="Unique identifier.")
    meta: Optional[Dict[str, Any]] = None
    nominatedPayee: Optional[str] = Field(None, description="Name of the nominated payee.")
    nominatedPayeeType: Optional[NominatedPayeeTypeResponse] = None
    paymentDay: Optional[PaymentDayResponse] = None
    paymentMethod: Optional[PaymentMethodResponse] = None
    paymentPeriod: Optional[PaymentPeriodResponse] = None
    stageName: Optional[str] = Field(
        None, description="The current stage of the payment preference."
    )
    status: Optional[CurrentPhaseResponse] = None


class PaymentPreferenceResources(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, Any]] = None
    elements: Optional[List[PaymentPreferenceResource]] = None
    hasMoreElements: Optional[bool] = Field(
        None,
        description="<P>If the query has a <code>limit</code> set and the actual number of resources that match the query exceeds the limit this will be true if the number of resources is less than or equal to the limit this will be false.</P><P>Note:If the limit query pattern is not used, this value will not be returned.</P>",
    )
    meta: Optional[Dict[str, Any]] = None
    totalSize: Optional[int] = Field(
        None,
        description="<P>The <code>totalSize</code> is the total number of resources that match the query. This can be greater than the page size in a paginated query if there are more queries.</P><P>Note:This field will not be returned if the query has a <code>limit</code> set for it.</P>",
    )


class PeriodDecisions(PydanticBaseModelEmptyStrIsNone):
    decisions: Optional[List[Decision]] = Field(
        None, description="List of Period Decisions for the given absence or employee"
    )
    endDate: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    startDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )


class ReadDisabilityBenefitResult(PydanticBaseModelEmptyStrIsNone):
    benefitSummary: Optional[BenefitSummary] = None
    certificationPeriods: Optional[List[CertificationPeriodDetails]] = Field(
        None, description="The certification period details of the disability benefit."
    )
    disabilityBenefit: Optional[DisabilityBenefit] = None


class ReadDisabilityResult(PydanticBaseModelEmptyStrIsNone):
    claimReopened: Optional[bool] = Field(
        None, description="This value is used to inform the group client if the claim is reopened."
    )
    claimSummary: Optional[ClaimSummary] = None
    customerDateOfBirth: Optional[datetime] = Field(
        None, description="ISO 8601 date time format", example="1999-12-31T23:59:59Z"
    )
    disabilityClaim: Optional[DisabilityClaim] = None


class ReadLumpSumBenefitResult(PydanticBaseModelEmptyStrIsNone):
    benefitSummary: Optional[BenefitSummary] = None
    lumpSumBenefit: Optional[LumpSumBenefit] = None


class ServiceAgreementLeavePlanSummaries(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, Any]] = None
    elements: Optional[List[ServiceAgreementLeavePlanSummary]] = None
    hasMoreElements: Optional[bool] = Field(
        None,
        description="<P>If the query has a <code>limit</code> set and the actual number of resources that match the query exceeds the limit this will be true if the number of resources is less than or equal to the limit this will be false.</P><P>Note:If the limit query pattern is not used, this value will not be returned.</P>",
    )
    meta: Optional[Dict[str, Any]] = None
    totalSize: Optional[int] = Field(
        None,
        description="<P>The <code>totalSize</code> is the total number of resources that match the query. This can be greater than the page size in a paginated query if there are more queries.</P><P>Note:This field will not be returned if the query has a <code>limit</code> set for it.</P>",
    )


class TaEnumAccommodationTypeGroupClient(PydanticBaseModelEmptyStrIsNone):
    active: Optional[bool] = None
    alphaSort: Optional[bool] = None
    defaultFirst: Optional[bool] = None
    domainDescription: Optional[str] = None
    domainId: Optional[int] = None
    domainName: Optional[str] = None
    dorder: Optional[int] = None
    editable: Optional[bool] = None
    endDate: Optional[datetime] = Field(
        None, description="ISO 8601 date time format", example="1999-12-31T23:59:59Z"
    )
    fullId: Optional[int] = None
    id: Optional[int] = None
    instances: Optional[List[TaEnumGroupClient]] = None
    name: Optional[str] = None
    nameAttribute: Optional[str] = None
    retired: Optional[bool] = None
    startDate: Optional[datetime] = Field(
        None, description="ISO 8601 date time format", example="1999-12-31T23:59:59Z"
    )
    subsetInfo: Optional[List[EnumSubsetInfoGroupClient]] = None
    subsets: Optional[List[str]] = None
    typedInstances: Optional[List[TaEnumAccommodationTypeGroupClient]] = None


class TaEnumResponseAccommodationTypeGroupClient(PydanticBaseModelEmptyStrIsNone):
    includeAttributes: Optional[List[str]] = None
    taEnum: Optional[TaEnumAccommodationTypeGroupClient] = None


class AbsenceDay(PydanticBaseModelEmptyStrIsNone):
    date: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    leaveApproveDetails: Optional[List[LeaveInfo]] = Field(
        None,
        description="List of hours per leave type within the Notification Case where the employee is approved to leave.",
    )


class AccommodationEmbeddableGroupClient(PydanticBaseModelEmptyStrIsNone):
    acceptedDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    accommodationType: Optional[TaEnumResponseAccommodationTypeGroupClient] = None
    category: Optional[AccommodationCategoryResponseGroupClient] = None
    createDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    decision: Optional[SingleAccommodationDecisionResponseGroupClient] = None
    description: Optional[str] = Field(
        None,
        description="Additional information when category is 'Other Accommodation' and type is 'Other'.",
    )
    effectiveness: Optional[EffectivenessResponseGroupClient] = None
    employeePreferred: Optional[EmployeePreferredResponseGroupClient] = None
    employeePreferredDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    employerPreferred: Optional[EmployerPreferredResponseGroupClient] = None
    endDate: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    implementedDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    notAccommodatedReasons: Optional[NotAccommodatedReasons] = Field(
        None,
        description="List of reasons given if the accommodation is not being accommodated. (Enum Domain = 6858)",
    )
    source: Optional[AccommodationSourceResponseGroupClient] = None


class CommunicationPreferenceResource(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    contactContext: Optional[ContactContextSwaggerResponse] = None
    contactTimePreference: Optional[ContactTimePreferenceEmbeddable] = None
    emailAddresses: Optional[List[EmailAddressResource]] = Field(
        None, description="List of email addresses linked to the preference context"
    )
    id: Optional[str] = Field(
        None,
        description="Id of the communication preference, matching the full ID of the contact context enum",
    )
    meta: Optional[Dict[str, Any]] = None
    phoneNumbers: Optional[List[PhoneNumberResource]] = Field(
        None, description="List of phone numbers linked to the preference context"
    )


class CommunicationPreferences(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, Any]] = None
    elements: Optional[List[CommunicationPreferenceResource]] = None
    hasMoreElements: Optional[bool] = Field(
        None,
        description="<P>If the query has a <code>limit</code> set and the actual number of resources that match the query exceeds the limit this will be true if the number of resources is less than or equal to the limit this will be false.</P><P>Note:If the limit query pattern is not used, this value will not be returned.</P>",
    )
    meta: Optional[Dict[str, Any]] = None
    totalSize: Optional[int] = Field(
        None,
        description="<P>The <code>totalSize</code> is the total number of resources that match the query. This can be greater than the page size in a paginated query if there are more queries.</P><P>Note:This field will not be returned if the query has a <code>limit</code> set for it.</P>",
    )


class ContactDetails(PydanticBaseModelEmptyStrIsNone):
    emailAddresses: Optional[List[EmailAddress]] = Field(
        None,
        description="Return list of email addresses, specifying the type: Email or Work Email.",
        max_items=100,
        min_items=0,
    )
    phoneNumbers: Optional[List[PhoneNumber]] = Field(
        None, description="An array with Customer Phone number details elements."
    )
    preferredContactMethod: Optional[int] = Field(
        None,
        description="Return the Id of the preferred contact method (it corresponds to one of the phones or emails specified in the previous lists and it means the first person to be contacted when it is needed).",
    )


class CustomerAddress(PydanticBaseModelEmptyStrIsNone):
    address: Address
    australianAddress: Optional[ExtendedAddress] = None


class NewPaymentPreference(PydanticBaseModelEmptyStrIsNone):
    accountDetails: Optional[AccountDetails] = None
    chequeDetails: Optional[ChequeDetails] = None
    classExtensionInformation: Optional[List[ExtensionAttribute]] = Field(
        None,
        description="An array of the extensionAttribute objects which contain claim extension information.",
    )
    customerAddress: Optional[CustomerAddress] = None
    description: Optional[str] = Field(None, description="Description of the Payment Preference")
    effectiveFrom: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    effectiveTo: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    isDefault: Optional[bool] = None
    overridePostalAddress: Optional[bool] = Field(
        None,
        description="Indicates that an override address applies for the payment preference. (i.e. sets the paymentAddressType to override. When false the correspondence address for the payee (or nominated payee) applies.",
    )
    paymentMethod: str = Field(
        ...,
        description="The name of the payment method. This is used to determine whether chequeDetails or accountDetails is displayed",
    )


class NotificationCaseDetail(PydanticBaseModelEmptyStrIsNone):
    linkshdr: Optional[List[LinkshdrItem]] = None
    notificationSummaries: Optional[List[NotificationCaseSummaryForClaims]] = None
    totalNumberOfRecords: Optional[int] = None


class NotificationCaseForEmployee(PydanticBaseModelEmptyStrIsNone):
    absenceDays: Optional[List[AbsenceDay]] = Field(
        None,
        description="List of Absence Days on the NotificationCase where the employee is approved to leave.",
    )
    adminGroup: Optional[str] = Field(
        None, description="Administrative group of the notification case"
    )
    caseNumber: Optional[str] = Field(None, description="The Notification's case number")
    id: Optional[str] = Field(None, description="Unique identifier of the notification case")
    notificationReason: Optional[NotificationReasonsResponse] = None


class PaymentPreferenceResponse(PydanticBaseModelEmptyStrIsNone):
    accountDetails: Optional[AccountDetails] = None
    chequeDetails: Optional[ChequeDetails] = None
    classExtensionInformation: Optional[List[ExtensionAttribute]] = Field(
        None,
        description="An array of the extensionAttribute objects which contain claim extension information.",
    )
    customerAddress: Optional[CustomerAddress] = None
    description: Optional[str] = Field(None, description="Description of the Payment Preference")
    effectiveFrom: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    effectiveTo: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    isDefault: Optional[bool] = None
    nominatedPayee: Optional[str] = Field(
        None,
        description="The nominated payee for the payment preference",
        max_length=250,
        min_length=0,
    )
    paymentMethod: str = Field(
        ...,
        description="The name of the payment method. This is used to determine whether chequeDetails or accountDetails is displayed",
    )
    paymentPreferenceId: str = Field(
        ..., description="The unique identifier for payment preference", max_length=32, min_length=0
    )
    stageName: Optional[str] = Field(
        None, description="The current stage of the payment preference"
    )
    status: Optional[str] = Field(None, description="The status of the payment preference")


class AccommodationCaseResourceGroupClient(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    accommodations: Optional[List[AccommodationEmbeddableGroupClient]] = Field(
        None, description="List of accommodations contained in the case."
    )
    caseHandler: Optional[CaseHandlerEmbeddableGroupClient] = None
    caseNumber: Optional[str] = Field(
        None, description="Business identifier for this Accommodation Case."
    )
    closureReasons: Optional[ClosureReasons] = Field(
        None, description="List of reasons given if the case is not being accommodated."
    )
    decisionDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    employee: Optional[EmployeeEmbeddableGroupClient] = None
    id: Optional[str] = Field(None, description="Unique identifier for the Accommodation Case.")
    limitations: Optional[Limitations] = Field(
        None,
        description="List of limitations the employee has that they require an accommodation in their job.",
    )
    notificationCase: Optional[NotificationCaseEmbeddableGroupClient] = None
    notificationDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    phase: Optional[str] = Field(
        None,
        description="Current phase in the Interactive Accommodation Process required by the Americans With Disabilities Act (ADA) and configured in the system, for example, 'Assessment'.",
    )
    pregnancyRelated: Optional[PregnancyRelatedResponseGroupClient] = None
    stage: Optional[str] = Field(
        None,
        description="Current stage in the Interactive Accommodation Process and configured in the system, for example, 'Evaluate Accommodation Options'.",
    )


class Customer(PydanticBaseModelEmptyStrIsNone):
    classExtensionInformation: Optional[List[ExtensionAttribute]] = Field(
        None,
        description="An array of the extensionAttribute objects which contain customer (OCPerson) extension information.",
    )
    customerAddress: Optional[CustomerAddress] = None
    dateOfBirth: date = Field(..., description="ISO 8601 date format", example="1999-12-31")
    firstName: str = Field(..., description="Person's first name.", max_length=50, min_length=0)
    gender: Optional[str] = Field(
        None, description="The person's gender.", max_length=100, min_length=0
    )
    idNumber: Optional[str] = Field(
        None, description="ID number of the claimant.", max_length=10, min_length=0
    )
    identificationNumberType: Optional[str] = Field(
        None,
        description="The type of identification number the party will have.",
        max_length=100,
        min_length=0,
    )
    initals: Optional[str] = None
    initials: Optional[str] = Field(
        None, description="Person's middle initials.", max_length=10, min_length=0
    )
    lastName: str = Field(..., description="Person's last name.", max_length=50, min_length=0)
    maritalStatus: Optional[str] = Field(
        None,
        description="Person's marital status - single / married / etc.",
        max_length=100,
        min_length=0,
    )
    nationality: Optional[str] = Field(
        None, description="Person's nationality.", max_length=100, min_length=0
    )
    needsInterpretor: Optional[bool] = Field(
        None, description="Indicates if the customer needs an interpreter."
    )
    partyType: Optional[str] = None
    placeOfBirth: Optional[str] = Field(
        None, description="Place of birth.", max_length=50, min_length=0
    )
    secondName: Optional[str] = Field(
        None, description="Person's second name.", max_length=50, min_length=0
    )
    securedClient: Optional[bool] = Field(None, description="Secured client flag.")
    staff: Optional[bool] = Field(None, description="Staff flag.")
    title: Optional[str] = Field(
        None, description="Person's chosen title e.g. Mr / Mrs etc.", max_length=100, min_length=0
    )


class EmployeeOnLeave(PydanticBaseModelEmptyStrIsNone):
    customerNo: Optional[str] = Field(
        None, description="The unique customer number assigned to a customer"
    )
    firstName: Optional[str] = Field(None, description="The First Name of the employee")
    id: Optional[str] = Field(None, description="Unique identifier for the employee")
    lastName: Optional[str] = Field(None, description="The Last Name of the employee")
    notificationCases: Optional[List[NotificationCaseForEmployee]] = Field(
        None, description="List of Notification Cases for the given employee within the date range"
    )


class EmployeesOnApprovedLeaveRpcResponse(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    elements: Optional[List[EmployeeOnLeave]] = None
    endDate: Optional[date] = Field(
        None,
        description="The End Date to which we want to retrieve the Employees. Only employees with approved days on or after this date will be returned in the response. ISO 8601 date format",
    )
    meta: Optional[Dict[str, Any]] = None
    startDate: Optional[date] = Field(
        None,
        description="The Start Date from which we want to retrieve the employees. Only employees with approved days on or after this date will be returned in the response. ISO 8601 date format.",
    )


class StartClaimDetails(PydanticBaseModelEmptyStrIsNone):
    claimIncurredDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    customer: Optional[Customer] = None
    customerNumber: Optional[str] = Field(
        None,
        description="The customer number to find an existing party or customer of the claim.",
        max_length=50,
        min_length=0,
    )
    description: Optional[str] = Field(
        None,
        description="Description for this case as entered by the user.",
        max_length=2000,
        min_length=0,
    )
    notificationCaseId: Optional[str] = Field(
        None,
        description="The notification case number of the claim case if applicable.",
        max_length=256,
        min_length=0,
    )
    notificationReason: Optional[str] = Field(
        None,
        description="Notification reason selected during notification case intake.",
        max_length=100,
        min_length=0,
    )
    policies: Optional[List[Policy]] = Field(
        None,
        description="An array of the policy objects with information about policies that will be added to the claim.",
    )


class CreateRegionTaxCodeDetailsCommand(PydanticBaseModelEmptyStrIsNone):
    __root__: Union[CreateAUTaxCodeDetailsCommand, CreateNZTaxCodeDetailsCommand]


class CreateTaxCodeRecordCommand(PydanticBaseModelEmptyStrIsNone):
    additionalWithholdAmount: Optional[AdditionalWithholdAmountMoneyRequest] = None
    dependentDeductions: Optional[DependentDeductionsMoneyRequest] = None
    effectiveDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    endDate: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    extensions: Optional[Dict[str, Any]] = None
    filingMaritalStatus: Optional[FilingMaritalStatusRequest] = None
    nonStdDeductions: Optional[NonStdDeductionsMoneyRequest] = None
    numberExemptions: Optional[int] = Field(
        None, description="The tax exemptions e.g. number of exemptions accumulated."
    )
    otherIncome: Optional[OtherIncomeMoneyRequest] = None
    percentageRate: Optional[str] = Field(
        None,
        description="A percentage rate for tax record. The percentageRate must be of precision 10,2.",
    )
    regionTaxCodeDetails: Optional[CreateRegionTaxCodeDetailsCommand] = None
    taxCode: TaxCodeRequest
    taxType: TaxTypeRequest
    version: VersionRequest


class EditRegionTaxCodeDetailsCommand(PydanticBaseModelEmptyStrIsNone):
    __root__: Union[EditAUTaxCodeDetailsCommand, EditNZTaxCodeDetailsCommand]


class EditTaxCodeRecordCommand(PydanticBaseModelEmptyStrIsNone):
    additionalWithholdAmount: Optional[EditAdditionalWithholdAmountMoneyRequest] = None
    dependentDeductions: Optional[EditDependentDeductionsMoneyRequest] = None
    effectiveDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    endDate: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    extensions: Optional[Dict[str, Any]] = None
    filingMaritalStatus: Optional[EditFilingMaritalStatusRequest] = None
    nonStdDeductions: Optional[EditNonStdDeductionsMoneyRequest] = None
    numberExemptions: Optional[int] = Field(
        None, description="The tax exemptions e.g. number of exemptions accumulated."
    )
    otherIncome: Optional[EditOtherIncomeMoneyRequest] = None
    percentageRate: Optional[str] = Field(
        None,
        description="A percentage rate for tax record. The percentageRate must be of precision 10,2.",
    )
    regionTaxCodeDetails: Optional[EditRegionTaxCodeDetailsCommand] = None
    taxCode: Optional[EditTaxCodeRequest] = None
    taxType: Optional[EditTaxTypeRequest] = None
    version: Optional[EditVersionRequest] = None


class RegionTaxCodeDetails(PydanticBaseModelEmptyStrIsNone):
    __root__: Union[RegionTaxCodeDetails1, RegionTaxCodeDetails2]


class TaxCodeRecord(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, str]] = None
    additionalWithholdAmount: Optional[AdditionalWithholdAmountMoneyResponse] = None
    dependentDeductions: Optional[DependentDeductionsMoneyResponse] = None
    effectiveDate: Optional[date] = Field(
        None, description="ISO 8601 date format", example="1999-12-31"
    )
    endDate: Optional[date] = Field(None, description="ISO 8601 date format", example="1999-12-31")
    extensions: Optional[Dict[str, Any]] = None
    filingMaritalStatus: Optional[FilingMaritalStatusResponse] = None
    id: Optional[str] = Field(None, description="Tax Code Record ID (unique in the system)")
    nonStdDeductions: Optional[NonStdDeductionsMoneyResponse] = None
    numberExemptions: Optional[int] = Field(
        None, description="The tax exemptions e.g. number of exemptions accumulated."
    )
    otherIncome: Optional[OtherIncomeMoneyResponse] = None
    percentageRate: Optional[str] = Field(
        None,
        description="A percentage rate for tax record. The percentageRate must be of precision 10,2.",
    )
    regionTaxCodeDetails: Optional[RegionTaxCodeDetails] = None
    taxCode: Optional[TaxCodeResponse] = None
    taxType: Optional[TaxTypeResponse] = None
    version: Optional[VersionResponse] = None


class TaxCodeRecordResources(PydanticBaseModelEmptyStrIsNone):
    _links: Optional[Dict[str, Any]] = None
    elements: Optional[List[TaxCodeRecord]] = None
    hasMoreElements: Optional[bool] = Field(
        None,
        description="<P>If the query has a <code>limit</code> set and the actual number of resources that match the query exceeds the limit this will be true if the number of resources is less than or equal to the limit this will be false.</P><P>Note:If the limit query pattern is not used, this value will not be returned.</P>",
    )
    meta: Optional[Dict[str, Any]] = None
    totalSize: Optional[int] = Field(
        None,
        description="<P>The <code>totalSize</code> is the total number of resources that match the query. This can be greater than the page size in a paginated query if there are more queries.</P><P>Note:This field will not be returned if the query has a <code>limit</code> set for it.</P>",
    )


class CreateAUTaxCodeDetailsCommand(PydanticBaseModelEmptyStrIsNone):
    claimVaryOffset: Optional[bool] = Field(
        None, description="Indicates if there is a withholding variation."
    )
    combinedIncome: Optional[bool] = Field(
        None, description="Indicates if combinedIncome is greater than a set amount."
    )
    levyExemption: Optional[LevyExemptionRequest] = None
    levyReduction: Optional[bool] = Field(
        None, description="Indicates if claiming medicare levy reduction.."
    )
    maritalRelationshipStatus: Optional[MaritalRelationshipStatusRequest] = None
    medicareLevySurcharge: Optional[MedicareLevySurchargeRequest] = None
    noDependents: Optional[int] = Field(None, description="Number of dependents claimed.")
    qualifyMedicareLevyExemption: Optional[bool] = Field(
        None, description="Indicates if qualifies for medicare levy exemption."
    )
    residentialStatus: Optional[ResidentialStatusRequest] = None
    seniorsPensionersOffset: Optional[bool] = Field(
        None, description="Indicates if Claim or Vary Seniors Pensioners Offset."
    )
    spouse: Optional[bool] = Field(None, description="Indicates if spouse has claimed.")
    studyAndTrainingLoan: Optional[bool] = Field(
        None, description="Indicates if has a study/training loan."
    )
    taxFreeThresholdClaimed: Optional[bool] = Field(
        None, description="Indicates if the tax free threshold claimed."
    )
    taxOffsets: Optional[TaxOffsetsMoneyRequest] = None


class CreateNZTaxCodeDetailsCommand(PydanticBaseModelEmptyStrIsNone):
    extraPayTaxRate: Optional[ExtraPayTaxRateRequest] = None
    irTaxCodeChange: Optional[bool] = Field(
        None,
        description="The tax office may direct the insurer to use a different tax code than the client declared one. This field indicates where the tax office has ordered a tax code change.",
    )
    isGSTRegistered: Optional[bool] = Field(
        None, description="Denotes if a payee is liable for Goods and Services Tax (GST)"
    )
    kiwiSaverStatus: Optional[KiwiSaverStatusRequest] = None
    studLoanThresholdApplies: Optional[bool] = Field(
        None, description="Denotes if a Student Loan Repayment Threshold Applies"
    )
    taxCertNumber: Optional[str] = Field(
        None,
        description="When a payee (client usually) has an individual tax agreement with IRD, i.e. to use a non-standard tax rate, this is the reference number of the tax certificate issued by IRD.",
    )
    taxCodeRateNonStdStudLoan: Optional[str] = Field(
        None, description="Indicates what rate to use for student loan for a non-standard tax code."
    )
    withholdingTaxStatus: Optional[WithholdingTaxStatusRequest] = None
    zeroStudLoanTaxCodeRate: Optional[bool] = Field(
        None,
        description="Indicates if a rate of zero is entered for the TaxCodeRateNonStdStudLoan field that this was intentional.",
    )


class EditAUTaxCodeDetailsCommand(PydanticBaseModelEmptyStrIsNone):
    claimVaryOffset: Optional[bool] = Field(
        None, description="Indicates if there is a withholding variation."
    )
    combinedIncome: Optional[bool] = Field(
        None, description="Indicates if combinedIncome is greater than a set amount."
    )
    levyExemption: Optional[EditLevyExemptionRequest] = None
    levyReduction: Optional[bool] = Field(
        None, description="Indicates if claiming medicare levy reduction.."
    )
    maritalRelationshipStatus: Optional[EditMaritalRelationshipStatusRequest] = None
    medicareLevySurcharge: Optional[EditMedicareLevySurchargeRequest] = None
    noDependents: Optional[int] = Field(None, description="Number of dependents claimed.")
    qualifyMedicareLevyExemption: Optional[bool] = Field(
        None, description="Indicates if qualifies for medicare levy exemption."
    )
    residentialStatus: Optional[EditResidentialStatusRequest] = None
    seniorsPensionersOffset: Optional[bool] = Field(
        None, description="Indicates if Claim or Vary Seniors Pensioners Offset."
    )
    spouse: Optional[bool] = Field(None, description="Indicates if spouse has claimed.")
    studyAndTrainingLoan: Optional[bool] = Field(
        None, description="Indicates if has a study/training loan."
    )
    taxFreeThresholdClaimed: Optional[bool] = Field(
        None, description="Indicates if the tax free threshold claimed."
    )
    taxOffsets: Optional[EditTaxOffsetsMoneyRequest] = None


class EditNZTaxCodeDetailsCommand(PydanticBaseModelEmptyStrIsNone):
    extraPayTaxRate: EditExtraPayTaxRateRequest
    irTaxCodeChange: Optional[bool] = Field(
        None,
        description="The tax office may direct the insurer to use a different tax code than the client declared one. This field indicates where the tax office has ordered a tax code change.",
    )
    isGSTRegistered: Optional[bool] = Field(
        None, description="Denotes if a payee is liable for Goods and Services Tax (GST)"
    )
    kiwiSaverStatus: EditKiwiSaverStatusRequest
    studLoanThresholdApplies: Optional[bool] = Field(
        None, description="Denotes if a Student Loan Repayment Threshold Applies"
    )
    taxCertNumber: Optional[str] = Field(
        None,
        description="When a payee (client usually) has an individual tax agreement with IRD, i.e. to use a non-standard tax rate, this is the reference number of the tax certificate issued by IRD.",
    )
    taxCodeRateNonStdStudLoan: Optional[str] = Field(
        None, description="Indicates what rate to use for student loan for a non-standard tax code."
    )
    withholdingTaxStatus: EditWithholdingTaxStatusRequest
    zeroStudLoanTaxCodeRate: Optional[bool] = Field(
        None,
        description="Indicates if a rate of zero is entered for the TaxCodeRateNonStdStudLoan field that this was intentional.",
    )


class AUTaxCodeDetails(PydanticBaseModelEmptyStrIsNone):
    claimVaryOffset: Optional[bool] = Field(
        None, description="Indicates if there is a withholding variation."
    )
    combinedIncome: Optional[bool] = Field(
        None, description="Indicates if combinedIncome is greater than a set amount."
    )
    levyExemption: Optional[ClaimMedicareLevyExemptionResponse] = None
    levyReduction: Optional[bool] = Field(
        None, description="Indicates if claiming medicare levy reduction.."
    )
    maritalRelationshipStatus: Optional[MaritalRelationshipStatusResponse] = None
    medicareLevySurcharge: Optional[MedicareLevySurchargeResponse] = None
    noDependents: Optional[int] = Field(None, description="Number of dependents claimed.")
    qualifyMedicareLevyExemption: Optional[bool] = Field(
        None, description="Indicates if qualifies for medicare levy exemption."
    )
    residentialStatus: Optional[ResidentialStatusResponse] = None
    seniorsPensionersOffset: Optional[bool] = Field(
        None, description="Indicates if Claim or Vary Seniors Pensioners Offset."
    )
    spouse: Optional[bool] = Field(None, description="Indicates if spouse has claimed.")
    studyAndTrainingLoan: Optional[bool] = Field(
        None, description="Indicates if has a study/training loan."
    )
    taxFreeThresholdClaimed: Optional[bool] = Field(
        None, description="Indicates if the tax free threshold claimed."
    )
    taxOffsets: Optional[TaxOffsetsMoneyResponse] = None


class NZTaxCodeDetails(PydanticBaseModelEmptyStrIsNone):
    extraPayTaxRate: Optional[ExtraPayTaxRateResponse] = None
    irTaxCodeChange: Optional[bool] = Field(
        None,
        description="The tax office may direct the insurer to use a different tax code than the client declared one. This field indicates where the tax office has ordered a tax code change.",
    )
    isGSTRegistered: Optional[bool] = Field(
        None, description="Denotes if a payee is liable for Goods and Services Tax (GST)"
    )
    kiwiSaverStatus: Optional[KiwiSaverStatusResponse] = None
    studLoanThresholdApplies: Optional[bool] = Field(
        None, description="Denotes if a Student Loan Repayment Threshold Applies"
    )
    taxCertNumber: Optional[str] = Field(
        None,
        description="When a payee (client usually) has an individual tax agreement with IRD, i.e. to use a non-standard tax rate, this is the reference number of the tax certificate issued by IRD.",
    )
    taxCodeRateNonStdStudLoan: Optional[str] = Field(
        None, description="Indicates what rate to use for student loan for a non-standard tax code."
    )
    withholdingTaxStatus: Optional[WithholdingTaxStatusResponse] = None
    zeroStudLoanTaxCodeRate: Optional[bool] = Field(
        None,
        description="Indicates if a rate of zero is entered for the TaxCodeRateNonStdStudLoan field that this was intentional.",
    )


class RegionTaxCodeDetails1(AUTaxCodeDetails):
    id: Optional[str] = None


class RegionTaxCodeDetails2(NZTaxCodeDetails):
    id: Optional[str] = None


TaEnumAccommodationTypeGroupClient.update_forward_refs()
CreateRegionTaxCodeDetailsCommand.update_forward_refs()
EditRegionTaxCodeDetailsCommand.update_forward_refs()
RegionTaxCodeDetails.update_forward_refs()
CreateAUTaxCodeDetailsCommand.update_forward_refs()
CreateNZTaxCodeDetailsCommand.update_forward_refs()
EditAUTaxCodeDetailsCommand.update_forward_refs()
EditNZTaxCodeDetailsCommand.update_forward_refs()
AUTaxCodeDetails.update_forward_refs()
NZTaxCodeDetails.update_forward_refs()
RegionTaxCodeDetails1.update_forward_refs()
RegionTaxCodeDetails2.update_forward_refs()
