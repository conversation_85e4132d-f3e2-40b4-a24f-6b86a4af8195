from datetime import datetime
from typing import Dict, List, Optional, Set

import requests

import massgov.pfml.util.json as json
import massgov.pfml.util.logging
import massgov.pfml.util.newrelic.events as newrelic_util
from massgov.pfml.fineos import exception

logger = massgov.pfml.util.logging.get_logger(__name__)


def fineos_document_empty_dates_to_none(response_json: dict) -> dict:
    # Document effectiveFrom and effectiveTo are empty and set to empty strings
    # These fields are not set by the portal. Set to none to avoid validation errors.
    if response_json["effectiveFrom"] == "":
        response_json["effectiveFrom"] = None

    if response_json["effectiveTo"] == "":
        response_json["effectiveTo"] = None

    # Documents uploaded through FINEOS use "dateCreated" instead of "receivedDate"
    if response_json["receivedDate"] == "":
        if "dateCreated" in response_json and response_json["dateCreated"]:
            response_json["receivedDate"] = response_json["dateCreated"]
        # Fineos V24 uses "creationDateTime" instead of "dateCreated"
        elif "creationDateTime" in response_json and response_json["creationDateTime"]:
            response_json["receivedDate"] = response_json["creationDateTime"]
            try:
                dt = datetime.strptime(response_json["creationDateTime"], "%Y-%m-%dT%H:%M:%SZ")
                response_json["receivedDate"] = dt.strftime("%Y-%m-%d")
            except ValueError as error:
                logger.error(f"Error while parsing receivedDate {error}")
                response_json["receivedDate"] = None
        else:
            response_json["receivedDate"] = None

    return response_json


def parse_fineos_document_for_customer(response_json: dict) -> dict:
    response_json = fineos_document_empty_dates_to_none(response_json)

    # documents via /customer/documents endpoint return 'id' in the format '1115-54313'
    if "id" in response_json and response_json["id"] != "":
        response_json["documentId"] = response_json["id"]

    if "type" in response_json and response_json["type"] != "":
        response_json["type"] = response_json["type"]["name"]

    if "status" in response_json and type(response_json["status"]) == dict:
        response_json["status"] = response_json["status"]["name"]

    if "privacyTag" in response_json and type(response_json["privacyTag"]) == dict:
        response_json["privacyTag"] = response_json["privacyTag"]["name"]

    return response_json


def get_errors_meta_data(response: requests.Response) -> Optional[List[Dict]]:
    try:
        errors_data = []
        response_payload_json = response.json()
        if isinstance(response_payload_json, Dict):
            errors = response_payload_json.get("errors", "")
            if errors:
                for error in errors:
                    data = {"id": error.get("id")}
                    data.update(error.get("meta"))
                    errors_data.append(data)
                return errors_data
        return None
    except ValueError:
        return None


def is_expected_failure(err_message: str, expected_failures: Optional[Set[str]]) -> bool:
    if expected_failures:
        lowercased_msg = err_message.lower()

        for expected in expected_failures:
            if expected in lowercased_msg:
                return True

    return False


def log_validation_error(
    err: exception.FINEOSUnprocessableEntity, expected_failures: Optional[Set[str]] = None
) -> None:
    """Parse 422 responses from FINEOS and log individual validation errors.
    422 responses are expected to be in this format:

    [
       { "validationMessage": "error 1" },
       { "validationMessage": "error 2" },
       ...
    ]
    """

    # Try to parse the error as a JSON array.
    # If it isn't JSON, log the full response message.
    try:
        error_json = json.loads(err.message)  # noqa: B306
    except Exception:
        newrelic_util.log_and_capture_exception(err.message)  # noqa: B306
        return

    # Some FINEOS responses are dicts, but 422 errors should be arrays.
    # If it isn't an array, log the full response message.
    if isinstance(error_json, dict):
        newrelic_util.log_and_capture_exception(err.message)  # noqa: B306
        return

    # Log each validation error object. If there's no validationMessage, just log the whole object.
    # If the message matches one of the expected failures, don't log it.
    for error in error_json:
        message = error.get("validationMessage")
        if message is None:
            newrelic_util.log_and_capture_exception(json.dumps(error))
        elif not is_expected_failure(message, expected_failures):
            newrelic_util.log_and_capture_exception(message)
