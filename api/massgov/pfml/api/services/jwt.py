import base64
import json
from abc import ABC, abstractmethod
from typing import Any, Optional

import boto3
import jwt
from botocore.client import BaseClient
from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives import serialization

import massgov.pfml.util.logging
from massgov.pfml.api.services.oauth_server_util import (
    DateTimeEncoder,
    TokenPayload,
    _log_jwt_error,
)

logger = massgov.pfml.util.logging.get_logger(__name__)


class JWTManager(ABC):
    """
    Abstract base class for managing JWT tokens.
    """

    @abstractmethod
    def sign(self, payload: TokenPayload) -> str:
        """
        Sign a JWT with the given payload.
        :param payload: The payload of the token.
        :return: Signed JWT string.
        """
        pass

    @abstractmethod
    def verify(self, token: str, **token_verification_kwargs: Any) -> dict:
        """
        Verify a JWT and return the decoded payload.
        :param token: The JWT string to verify
        :param token_verification_kwargs: Optional verification parameters (aud, sub, iss, jti)
        :return: Decoded payload dictionary
        """
        pass

    @abstractmethod
    def unverified_token_info(self, token: str) -> dict:
        """
        Get unverified token information including header and payload without signature verification.

        :param token: The JWT string to decode
        :return: Decoded payload dictionary
        """
        pass

    @abstractmethod
    def get_public_key(self) -> str:
        """
        Get the public key used to verify the JWT signature.
        :return: Public key in PEM format
        """
        pass


class KMSAsymmetricJWTManager(JWTManager):

    def __init__(
        self,
        key_id: str,  # alias or ARN of the asymmetric key
        signing_algorithm: str = "RSASSA_PKCS1_V1_5_SHA_256",
        jwt_algorithm: str = "RS256",
        kms_client: Optional[BaseClient] = None,
    ):
        if kms_client is None:
            kms_client = boto3.client("kms", region_name="us-east-1")

        self.key_id = key_id
        self.signing_algorithm = signing_algorithm
        self.jwt_algorithm = jwt_algorithm
        self.kms = kms_client

    def encode_segment(self, data, encoder=None):
        json_str = json.dumps(data, cls=encoder)
        return base64.urlsafe_b64encode(json_str.encode()).rstrip(b"=")

    def decode_segment(self, segment):
        padding = "=" * (-len(segment) % 4)
        return base64.urlsafe_b64decode(segment + padding)

    def sign(self, payload: TokenPayload) -> str:
        """
        Sign the JWT payload using the AWS KMS private key.
        Private key is not accessible in console or outside of AWS
        """
        header = {"typ": "JWT", "alg": self.jwt_algorithm}

        # Encode the header and payload using Base64 URL-safe encoding
        segments = [self.encode_segment(header), self.encode_segment(payload, DateTimeEncoder)]
        message = b".".join(segments)

        #  Sign the message using the AWS KMS private key
        try:
            signature = self.kms.sign(
                KeyId=self.key_id,
                Message=message,
                SigningAlgorithm=self.signing_algorithm,
                MessageType="RAW",
            )["Signature"]
        except Exception as e:
            _log_jwt_error(
                logger, f"Failed to sign token with KMS private key: {str(e)}", "kms_signing_error"
            )
            raise JWTSigningError(f"Failed to sign token with KMS private key: {str(e)}") from e

        # Encode the signature using Base64 URL-safe encoding and append to the segments
        # Signature from kms.sign() is already raw bytes that needs just base64url encoding
        segments.append(base64.urlsafe_b64encode(signature).rstrip(b"="))

        # Combine all segments into the final JWT string
        # JWT is header.payload.signature
        return b".".join(segments).decode()

    def get_public_key(self) -> str:
        """
        Fetch and convert the public key from KMS into PEM format.
        """
        response = self.kms.get_public_key(KeyId=self.key_id)
        public_key_der = response["PublicKey"]

        public_key = serialization.load_der_public_key(public_key_der, backend=default_backend())
        public_key_pem = public_key.public_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PublicFormat.SubjectPublicKeyInfo,
        )

        return public_key_pem.decode("utf-8")

    def verify(self, token: str, **token_verification_kwargs: Any) -> dict:
        """
        Verify the JWT token using the public key from AWS KMS.
        Usage: jwt_manager.verify(token, audience='client-id', subject='user123', issuer='auth.example.com')
        """

        try:
            public_key = self.get_public_key()

            # jwt.decode() Claims Verification:
            # Automatic: exp, nbf, iat (timestamp claims)
            # Optional: requires kwargs passed to decode() (aud, sub, iss, jti)
            decoded = jwt.decode(
                token,
                public_key,
                algorithms=[self.jwt_algorithm],  # only ever allow one algorithm for security
                **token_verification_kwargs,
            )

            logger.info("JWT decode of Signature and Claims are valid.")
            return decoded

        except jwt.ExpiredSignatureError as e:
            _log_jwt_error(logger, "Token has expired (exp claim failed)", "expired_token")
            raise JWTVerificationError("Token has expired") from e
        except jwt.ImmatureSignatureError as e:
            _log_jwt_error(logger, "Token not yet valid (nbf claim failed)", "immature_token")
            raise JWTVerificationError("Token not yet valid") from e
        except jwt.InvalidIssuedAtError as e:
            _log_jwt_error(logger, "Invalid iat claim - token issued in future", "invalid_iat")
            raise JWTVerificationError("Invalid issued at time") from e
        except jwt.InvalidAudienceError as e:
            _log_jwt_error(logger, "Invalid audience claim", "invalid_audience")
            raise JWTVerificationError("Invalid token audience") from e
        except jwt.InvalidIssuerError as e:
            _log_jwt_error(logger, "Invalid issuer claim", "invalid_issuer")
            raise JWTVerificationError("Invalid token issuer") from e
        except jwt.InvalidSignatureError as e:
            _log_jwt_error(logger, "Invalid token signature", "invalid_signature")
            raise JWTVerificationError("Invalid token signature") from e
        except jwt.DecodeError as e:
            _log_jwt_error(logger, "Failed to decode malformed token", "decode_error")
            raise JWTVerificationError("Malformed token") from e
        except jwt.MissingRequiredClaimError as e:
            _log_jwt_error(
                logger, f"Missing required claim: {e.claim}", "missing_claim", missing_claim=e.claim
            )
            raise JWTVerificationError(f"Missing required claim: {e.claim}") from e
        except jwt.InvalidTokenError as e:
            _log_jwt_error(logger, "Invalid token", "invalid_token")
            raise JWTVerificationError("Invalid token") from e
        except jwt.PyJWTError as e:
            _log_jwt_error(logger, "JWT validation failed", "jwt_validation_error", error=str(e))
            raise JWTVerificationError(f"JWT validation failed: {str(e)}") from e

    def unverified_token_info(self, token: str) -> dict:
        """
        Get unverified token information including header and payload without signature verification.

        Args:
            token: The JWT string to decode

        Returns:
            dict: Dictionary containing unverified header and payload information
        """
        try:
            payload_data = jwt.decode(token, options={"verify_signature": False})

            logger.info("Retrieved unverified token information")
            return payload_data

        except jwt.DecodeError:
            _log_jwt_error(logger, "Failed to decode malformed token", "decode_error")
            raise JWTVerificationError("Failed to decode malformed token")
        except jwt.InvalidTokenError:
            _log_jwt_error(logger, "Invalid token format", "invalid_token")
            raise JWTVerificationError("Invalid token format")
        except Exception as e:
            _log_jwt_error(logger, "Unexpected error decoding token", "decode_error", error=str(e))
            raise JWTVerificationError(f"Unexpected error decoding token: {str(e)}")


class JWTVerificationError(Exception):
    """Raised when JWT verification fails for any reason"""

    pass


class JWTSigningError(Exception):
    """Raised when JWT verification fails for any reason"""

    pass
