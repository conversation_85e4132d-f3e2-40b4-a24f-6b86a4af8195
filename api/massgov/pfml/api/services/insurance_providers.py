from typing import List

from sqlalchemy.sql.expression import case

import massgov.pfml.util.logging
from massgov.pfml.db import Session
from massgov.pfml.db.models.employer_exemptions import InsurancePlan, InsuranceProvider

logger = massgov.pfml.util.logging.get_logger(__name__)


def get_insurance_providers_from_db(
    db_session: Session, include_deactivated: bool = False
) -> List[InsuranceProvider]:
    """
    Retrieves insurance providers from the database, ordered by name with providers
    lacking addresses appearing last

    Args:
        db_session: SQLAlchemy database session

    Returns:
        List[InsuranceProvider]: List of insurance provider database models
    """
    query = (
        # place deactived providers, if present, at the end of the list
        db_session.query(InsuranceProvider).order_by(
            case(
                (
                    InsuranceProvider.deactivated.is_(False),
                    0,
                ),
                else_=1,
            ),
            InsuranceProvider.insurance_provider_name,
        )
    )

    if not include_deactivated:
        query = query.filter(InsuranceProvider.deactivated.is_(False))

    providers = query.all()

    return providers


def get_insurance_provider_from_db(
    db_session: Session, insurance_provider_id: int | None
) -> InsuranceProvider | None:
    """
    Retrieves a single insurance provider from the database if it exists

    Args:
        db_session: SQLAlchemy database session
        insurance_provider_id: the provider being queried

    Returns:
        InsuranceProvider: insurance provider database model
    """
    if insurance_provider_id is None:
        return None

    provider = (
        db_session.query(InsuranceProvider)
        .filter(InsuranceProvider.insurance_provider_id == insurance_provider_id)
        .one_or_none()
    )

    return provider


def get_insurance_providers_plans_from_db(
    db_session: Session,
    insurance_provider_id: int | None,
    include_deactivated_insurance_plans: bool = False,
) -> List[InsurancePlan] | None:
    """
    Retrieves insurance providers from the database, ordered by name with providers
    lacking addresses appearling last

    Args:
        db_session: SQLAlchemy database session

    Returns:
        List[InsuranceProvider]: List of insurance provider database models
    """
    if insurance_provider_id is None:
        return None

    query = db_session.query(InsurancePlan).filter(
        InsurancePlan.insurance_provider_id == insurance_provider_id,
    )

    if not include_deactivated_insurance_plans:
        query = query.filter(InsurancePlan.deactivated.is_(False))

    plans = query.all()

    return plans


def get_insurance_providers_plan_from_db(
    db_session: Session, insurance_provider_id: int | None, insurance_plan_id: int | None
) -> InsurancePlan | None:
    """
    Retrieves insurance providers from the database, ordered by name with providers
    lacking addresses appearling last

    Args:
        db_session: SQLAlchemy database session

    Returns:
        InsuranceProviderPlan: Single insurance plan for a given insurance provider
    """
    if insurance_provider_id is None or insurance_plan_id is None:
        return None

    plan = (
        db_session.query(InsurancePlan)
        .filter(
            InsurancePlan.insurance_provider_id == insurance_provider_id,
            InsurancePlan.insurance_plan_id == insurance_plan_id,
        )
        .one_or_none()
    )

    return plan
