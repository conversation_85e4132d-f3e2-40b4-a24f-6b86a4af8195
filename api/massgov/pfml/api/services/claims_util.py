from decimal import Decimal
from typing import Any, Optional

from massgov.pfml import db
from massgov.pfml.api.eligibility import eligibility_util
from massgov.pfml.api.eligibility.benefit import calculate_weekly_benefit_amount
from massgov.pfml.api.eligibility.benefit_year_dates import calculate_benefit_year_dates
from massgov.pfml.api.models.claims.responses import ClaimWagesBenefits
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.employees import Claim
from massgov.pfml.db.queries.claims import get_iaww_from_claim
from massgov.pfml.util.logging import get_logger

logger = get_logger(__name__)


def get_weekly_benefit_amount_from_claim(
    db_session: db.Session, claim: Claim
) -> Optional[ClaimWagesBenefits]:
    """
    Retrieves the Individual Average Weekly Wage (IAWW) from the database and calculates
    the Weekly Benefit Amount (WBA) using state metrics.
    """
    claim_id = str(claim.claim_id)
    log_extra = {"claim_id": claim_id}

    try:

        iaww = get_iaww_from_claim(db_session, claim, log_extra)

        if iaww is None:
            logger.warning(
                "No average weekly wage found for claim",
                extra={**log_extra, "iaww": iaww},
            )
            return None

        application = claim.application
        if not application:
            logger.warning(
                "No application found for claim",
                extra=log_extra,
            )
            return None

        wba = get_weekly_benefit_amount_from_application(db_session, iaww, application, log_extra)

        if not wba:
            logger.warning(
                "No weekly benefit amount found for claim",
                extra={**log_extra, "iaww": iaww},
            )
            return None

        return ClaimWagesBenefits(individual_average_weekly_wage=iaww, weekly_benefit_amount=wba)
    except Exception as e:
        logger.exception(
            "Error calculating wages and benefits for claim",
            extra={**log_extra, "error": str(e)},
        )
        return None


def get_weekly_benefit_amount_from_application(
    db_session: db.Session,
    individual_average_weekly_wage: Decimal,
    application: Application,
    log_extra: dict[str, Any],
) -> Decimal | None:
    leave_start_date = application.start_date
    # Get the benefit year using the application start date
    sunday_on_or_before_leave_start_date = calculate_benefit_year_dates(leave_start_date).start_date

    state_metrics = eligibility_util.fetch_state_metric(
        db_session, sunday_on_or_before_leave_start_date
    )
    state_weekly_wage = state_metrics.for_benefits.average_weekly_wage
    max_weekly_benefit_amount = state_metrics.for_benefits.maximum_weekly_benefit_amount

    logger.info(
        "Successfully retrieved state metrics",
        extra={
            **log_extra,
            "state_weekly_wage": state_weekly_wage,
            "max_weekly_benefit_amount": max_weekly_benefit_amount,
        },
    )

    wba = calculate_weekly_benefit_amount(
        individual_average_weekly_wage,
        state_weekly_wage,
        max_weekly_benefit_amount,
    )

    logger.info("Successfully calculated weekly_benefit_amount", extra={**log_extra, "wba": wba})

    return wba
