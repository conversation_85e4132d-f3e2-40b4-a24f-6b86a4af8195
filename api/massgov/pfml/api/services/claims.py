from datetime import date
from typing import List, Optional, Union

import massgov
import massgov.pfml.api.app as app
import massgov.pfml.services.claims as claims_service
import massgov.pfml.util.config as config
from massgov.pfml import db
from massgov.pfml.api.models.applications.common import PaymentScheduleType
from massgov.pfml.api.models.claims.responses import (
    AbsencePaidLeaveCaseResponse,
    ClaimWagesBenefits,
    DetailedClaimResponse,
    IntermittentLeaveEpisodeResponse,
    LeaveRequestResponse,
)
from massgov.pfml.api.models.common import (
    ComputedStartDates,
    get_absence_reason,
    get_computed_start_dates,
    get_earliest_start_date,
)
from massgov.pfml.api.models.IntermittentLeaveEpisodes.common import IntermittentLeaveEpisode
from massgov.pfml.api.services.claims_util import get_weekly_benefit_amount_from_claim
from massgov.pfml.api.services.fineos_actions import submit_intermittent_leave_episode
from massgov.pfml.db.models.absences import AbsencePeriod
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.employees import (
    AbsencePaidLeaveCase,
    Claim,
    Employee,
    LeaveRequest,
    TaxIdentifier,
)
from massgov.pfml.models import ClaimDetail
from massgov.pfml.services.absence_periods import get_absence_period_details
from massgov.pfml.services.claims import calculate_has_paid_payments
from massgov.pfml.services.documents import DocumentRequirementService
from massgov.pfml.util.logging.claims import log_get_claim_metrics

logger = massgov.pfml.util.logging.get_logger(__name__)


def submit_leave_episode(
    claim: Claim,
    fineos_absence_id: str,
    intermittent_leave_episode: IntermittentLeaveEpisode,
    db_session: db.Session,
) -> List[IntermittentLeaveEpisodeResponse]:
    log_attributes = {
        "absence_id": fineos_absence_id,
        "leave_episode_date": intermittent_leave_episode.requested_date,
    }

    try:
        actual_absence_period_reponse_list = submit_intermittent_leave_episode(
            claim, fineos_absence_id, intermittent_leave_episode, db_session
        )
    except Exception as exc:
        logger.error(
            "submit_leave_episode - Failed to submit leave episode",
            extra=log_attributes,
            exc_info=exc,
        )
        raise Exception(
            f"submit_leave_episode - Failed to submit leave episode for absence_id ({fineos_absence_id}): {str(exc)}"
        )

    submitted_episodes = []
    for actual_absence_period in actual_absence_period_reponse_list:
        submitted_episodes.append(IntermittentLeaveEpisodeResponse.from_orm(actual_absence_period))

    return submitted_episodes


def get_claim_detail(claim: Claim, db_session: db.Session) -> DetailedClaimResponse:
    """
    Construct the DetailedClaimResponse for a Claim.
    This detailed response includes extra data that is not stored as part of a Claim in the database,
    for example:
    - calculations like whether or not there are paid payments for the claim
    - information fetched from FINEOS directly, like intermittent leave episodes
    """

    log_attributes = {"absence_case_id": claim.fineos_absence_id}

    # Get the detailed absence periods for this claim.
    # Note that this method also syncs FINEOS data to our database.
    # Many of our tests rely on this to seed info in the test db - as a result,
    # it needs to be called before proceeding with the rest of the function.
    # (We should update our tests to not rely on this syncing so heavily.)
    absence_period_details = get_absence_period_details(claim, db_session)

    # Load both ClaimDetail represenations.
    # We eventually want to deprecate most usages of DetailedClaimResponse, including this one.
    # See: https://lwd.atlassian.net/browse/PFMLPB-17734 (Investigate using `ClaimDetail` instead of `DetailedClaimResponse` in the API)
    claim_detail = ClaimDetail(claim, db_session)
    detailed_claim = DetailedClaimResponse.from_orm(claim)

    detailed_claim.has_paid_payments = calculate_has_paid_payments(claim, db_session)
    detailed_claim.absence_periods = absence_period_details

    detailed_claim.document_requirements = DocumentRequirementService(
        db_session
    ).get_document_requirements_for_claim_via_absence_periods(claim)

    if claim.application:
        detailed_claim.application_id = claim.application.application_id  # type: ignore

    approval_date = claims_service.get_approval_date(claim, db_session)
    detailed_claim.approval_date = approval_date
    detailed_claim.payment_schedule_type = calculate_payment_schedule_type(approval_date)

    detailed_claim.leave_requests = get_leave_requests_for_claim(claim, db_session)

    detailed_claim.payment_preference = claim_detail.masked_payment_preference

    try:
        detailed_claim.intermittent_leave_episodes = claim_detail.intermittent_leave_episodes
    except Exception as error:
        logger.error(
            "claim_detail - Failed to get intermittent leave episodes for claim",
            extra=log_attributes,
            exc_info=error,
        )
        raise error

    log_get_claim_metrics(claim, detailed_claim)

    return detailed_claim


# TODO: skip calculating payment schedule if the claim does not have approval status...
# ... AND it doesn't have payments
def calculate_payment_schedule_type(approval_date: Optional[date]) -> Optional[PaymentScheduleType]:
    """
    Calculate payment schedule type for a claim.
    After the FINEOS 21.3 upgrade, FINEOS will be setting the payment schedule
    based on a standard Sunday->Saturday week, instead of based on the start of the leave.
    Claims approved after the upgrade should be denoted with this new payment schedule.
    """

    fineos_v21_upgrade_date = config.get_env_date("FINEOS_V21_UPGRADE_DATE")

    if not fineos_v21_upgrade_date:
        return PaymentScheduleType.LEAVE_START_BASED

    if approval_date is None:
        return None

    if approval_date > fineos_v21_upgrade_date:
        return PaymentScheduleType.SUNDAY_BASED
    else:
        return PaymentScheduleType.LEAVE_START_BASED


def get_claim_from_db(fineos_absence_id: Optional[str]) -> Optional[Claim]:
    if fineos_absence_id is None:
        return None

    with app.db_session() as db_session:
        claim = (
            db_session.query(Claim)
            .filter(Claim.fineos_absence_id == fineos_absence_id)
            .one_or_none()
        )

    return claim


def get_leave_requests_from_db(claim: Claim, db_session: db.Session) -> List[LeaveRequest]:
    leave_requests = (
        db_session.query(LeaveRequest).filter(LeaveRequest.claim_id == claim.claim_id).all()
    )
    return leave_requests


def get_absence_paid_leave_cases_from_db_claim(
    claim: Claim, db_session: db.Session
) -> List[AbsencePaidLeaveCase]:
    absence_paid_leave_cases = (
        db_session.query(AbsencePaidLeaveCase)
        .filter(AbsencePaidLeaveCase.claim_id == claim.claim_id)
        .all()
    )

    return absence_paid_leave_cases


def get_previous_absence_period_from_claim(
    application_or_claim: Union[Application, Claim], limit_to_claim_employer: bool = True
) -> List[AbsencePeriod]:
    employer = application_or_claim.employer
    employee = application_or_claim.employee
    prior_year_start_date = get_prior_year_start_date(application_or_claim)
    prior_year_end_date = get_prior_year_end_date(application_or_claim)

    previous_absence_periods: List[AbsencePeriod] = []

    if (
        employer is not None
        and employee is not None
        and employee.claims is not None
        and prior_year_start_date is not None
        and prior_year_end_date is not None
    ):
        for previous_claim in employee.claims:
            # check this claim has absence_periods
            if previous_claim.absence_periods is None:
                continue

            # check the claim does not equal current claim
            if previous_claim.claim_id == application_or_claim.claim_id:
                continue

            # check this claim has same employer as current claim
            if limit_to_claim_employer and previous_claim.employer_id != employer.employer_id:
                continue

            # check each absence periods of the claim and append to previous_absence_periods if the
            # period leave decision is approved, pending or in review
            for absence_period in previous_claim.absence_periods:

                # check if absence period has start and end date
                if absence_period.start_date is None or absence_period.end_date is None:
                    continue

                if (
                    absence_period.is_approved
                    or absence_period.in_review
                    or absence_period.is_pending
                ) and (
                    prior_year_start_date <= absence_period.start_date
                    and prior_year_end_date >= absence_period.end_date
                ):
                    previous_absence_periods.append(absence_period)

    return previous_absence_periods


def ensure_employee_relationship_exists(fineos_absence_id: str, tax_identifier: str) -> None:
    """
    Helper to add employee relationship to the claim if it is missing.

    The association could be missing for claims created via the contact center directly in FINEOS
    the notification endpoint fails to set the association, and if the leave admin visits the review page
    prior to the nightly extract.
    """

    with app.db_session() as db_session:

        claim = (
            db_session.query(Claim)
            .filter(Claim.fineos_absence_id == fineos_absence_id)
            .one_or_none()
        )

        if not claim:
            return

        if claim.employee_id:
            return

        try:
            employee = (
                db_session.query(Employee)
                .join(TaxIdentifier)
                .filter(TaxIdentifier.tax_identifier == tax_identifier)
                .one_or_none()
            )
            if employee:
                claim.employee_id = employee.employee_id
        except Exception:
            logger.warning(
                "Failed to update employee relationship for claim",
                extra={"absence_case_id": claim.fineos_absence_id, "claim_id": claim.claim_id},
                exc_info=True,
            )

        db_session.commit()
        return


def get_leave_requests_for_claim(
    claim: Claim, db_session: db.Session
) -> Optional[list[LeaveRequestResponse]]:
    log_attributes = {"absence_case_id": claim.fineos_absence_id}
    try:
        leave_requests = get_leave_requests_from_db(claim, db_session)
    except Exception as error:
        logger.error(
            "Failed to fetch leave requests.",
            extra=log_attributes,
            exc_info=error,
        )

    leave_requests_for_claim = []

    for leave_request in leave_requests:
        leave_request.absence_paid_leave_cases = (
            leave_request.absence_paid_leave_cases
            if leave_request.absence_paid_leave_cases is not None
            else []
        )

        leave_request_detail = LeaveRequestResponse.from_orm(leave_request)

        leave_request_detail.absence_paid_leave_cases = [
            AbsencePaidLeaveCaseResponse.from_orm(absence_paid_leave_case)
            for absence_paid_leave_case in leave_request.absence_paid_leave_cases
        ]
        leave_requests_for_claim.append(leave_request_detail)
    return leave_requests_for_claim


def get_prior_year_end_date(application_or_claim: Union[Application, Claim]) -> Optional[date]:
    if isinstance(application_or_claim, Claim):
        start_date = application_or_claim.absence_periods_earliest_start_date
    elif isinstance(application_or_claim, Application):
        start_date = get_earliest_start_date(application_or_claim)

    return start_date


def get_prior_year_start_date(application_or_claim: Union[Application, Claim]) -> Optional[date]:
    from massgov.pfml.api.models.applications.responses import _get_computed_start_dates

    computed_start_dates = ComputedStartDates(other_reason=None, same_reason=None)
    if isinstance(application_or_claim, Claim):
        absence_reason = get_absence_reason(application_or_claim)
        computed_start_dates = get_computed_start_dates(
            application_or_claim.absence_periods_earliest_start_date,
            absence_reason,
        )
    elif isinstance(application_or_claim, Application):
        computed_start_dates = _get_computed_start_dates(application_or_claim)

    return computed_start_dates.same_reason


def get_iaww_and_weekly_benefit_amount_from_claim(
    db_session: db.Session, claim: Claim
) -> Optional[ClaimWagesBenefits]:
    """
    Gets wage and benefit information for approved claims.
    """

    log_attributes = {
        "absence_case_id": claim.fineos_absence_id,
        "claim_id": claim.claim_id,
        "employee_id": claim.employee_id,
        "employer_id": claim.employer_id,
    }

    # Check if the claim has any approved absence periods
    # to match the portal application status page behavior
    if not claim.approved_absence_periods:
        logger.info(
            "Claim has no approved absence periods",
            extra={**log_attributes},
        )
        return None

    wages_and_benefits = get_weekly_benefit_amount_from_claim(db_session, claim)

    if wages_and_benefits is None:
        logger.info(
            "No wages and benefits found for claim",
            extra={**log_attributes},
        )
        return None

    if wages_and_benefits.weekly_benefit_amount is None:
        logger.warning(
            "No weekly benefit amount found for claim",
            extra={**log_attributes},
        )

    if wages_and_benefits.individual_average_weekly_wage is None:
        logger.warning(
            "No individual average weekly wage found for claim",
            extra={**log_attributes},
        )

    return wages_and_benefits
