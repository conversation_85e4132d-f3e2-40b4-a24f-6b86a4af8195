import abc
from typing import Optional, Union

import massgov.pfml.util.logging as logging
from massgov.pfml.api.services.fineos_actions import register_employee
from massgov.pfml.api.util.claims import parse_notification_id
from massgov.pfml.db.models.employees import Claim
from massgov.pfml.fineos import models

logger = logging.get_logger(__name__)


class CustomerDetailsService(abc.ABC):
    """Abstract service interface for retrieving customer-related details."""

    @abc.abstractmethod
    def get_tax_identifier(self) -> Optional[str]:
        pass

    @abc.abstractmethod
    def get_address(
        self,
    ) -> Optional[Union[models.customer_api.Address, models.group_client_api.AddressEmbeddable]]:
        pass

    @abc.abstractmethod
    def get_customer_info(
        self,
    ) -> Union[models.customer_api.Customer, models.group_client_api.CustomerInfo]:
        pass

    @abc.abstractmethod
    def get_hours_worked_per_week(self) -> Optional[str]:
        pass


class CustomerDetailsFromCustomerApi(CustomerDetailsService):
    """
    Service for grabbing customer details using a database tax ID and customerapi endpoints.
    If the tax identifier cannot be found in the database, we make a call out to the FINEOS
    groupclient notification endpoint to grab it.
    """

    def __init__(self, fineos_client, db_session, absence_id, employer, fineos_employer_user_id):
        self.fineos_client = fineos_client
        self.tax_identifier = None

        db_claim = (
            db_session.query(Claim).filter(Claim.fineos_absence_id == absence_id).one_or_none()
        )

        if db_claim:
            logger.info("Found claim in database, attempting to use DB tax identifier")
            self.tax_identifier = db_claim.employee_tax_identifier

        # It's not guaranteed that the Employee will be set yet for cases created
        # through the contact center. Thankfully, we can fall back and grab the
        # tax identifier from the FINEOS notification endpoint if needed.
        if not self.tax_identifier:
            logger.info(
                "No tax ID found in the database, attempting to call FINEOS notifications endpoint"
            )
            notification_id = parse_notification_id(absence_id)
            notification = fineos_client.get_group_client_notification(
                fineos_employer_user_id, notification_id
            )

            if not notification.customer:
                raise ValueError("Notification does not have an associated customer")

            self.tax_identifier = notification.customer.idNumber

        if not self.tax_identifier:
            raise Exception("Tax identifier was not found for absence case")

        fineos_employer_id = (
            str(db_claim.employer.fineos_employer_id) if db_claim and db_claim.employer else None
        )

        self.fineos_employee_web_id = register_employee(
            fineos_client,
            self.tax_identifier,
            employer.employer_fein,
            db_session,
            fineos_employer_id,
        )

        self.customer_info = fineos_client.read_customer_details(self.fineos_employee_web_id)

    def get_tax_identifier(self) -> Optional[str]:
        return self.tax_identifier

    def get_customer_info(self) -> models.customer_api.Customer:
        return self.customer_info

    def get_hours_worked_per_week(self) -> Optional[str]:
        customer_occupations_from_customerapi = (
            self.fineos_client.get_customer_occupations_customer_api(self.fineos_employee_web_id)
        )

        hours_worked_per_week: Optional[str] = None
        first_occupation_from_list: int = 0

        if customer_occupations_from_customerapi:
            hours_worked_per_week = str(
                customer_occupations_from_customerapi[first_occupation_from_list].hoursWorkedPerWeek
            )
        else:
            logger.info("No customer occupations were returned")

        return hours_worked_per_week

    def get_address(self) -> Optional[models.customer_api.Address]:
        customer_address_obj = self.customer_info.customerAddress
        customer_address = customer_address_obj.address if customer_address_obj else None
        return customer_address


class CustomerDetailsFromGroupClientApi(CustomerDetailsService):
    """
    Service for grabbing customer details using groupclientapi endpoints.
    This avoids calling any customerapi endpoints or retrieving any data from the database.
    """

    def __init__(self, fineos_client, fineos_user_id, absence_period_decisions):
        self.fineos_client = fineos_client

        # We retrieve the Employee from Fineos, rather than through the Claim in our DB,
        # since it's not guaranteed that the Employee will be set yet for cases created
        # through the contact center.
        self.fineos_employee = absence_period_decisions.decisions[0].employee
        self.fineos_user_id = fineos_user_id

        if self.fineos_employee is None or self.fineos_employee.id is None:
            raise ValueError("Absence period is missing an associated employee or ID")

        self.customer_info = fineos_client.get_customer_info(
            fineos_user_id, self.fineos_employee.id
        )

        self.tax_identifier = self.customer_info.idNumber

    def get_tax_identifier(self) -> Optional[str]:
        return self.tax_identifier

    def get_customer_info(self) -> models.group_client_api.CustomerInfo:
        return self.customer_info

    def get_address(self) -> models.group_client_api.AddressEmbeddable:
        return self.customer_info.address

    def get_hours_worked_per_week(self) -> Optional[str]:
        customer_occupations = self.fineos_client.get_customer_occupations(
            self.fineos_user_id, self.fineos_employee.id
        )

        if customer_occupations.elements is None:
            raise ValueError("No customer occupations were returned")

        return customer_occupations.elements[0].hrsWorkedPerWeek
