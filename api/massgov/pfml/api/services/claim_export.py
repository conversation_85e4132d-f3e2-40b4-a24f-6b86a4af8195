import csv
import enum
from collections import defaultdict
from dataclasses import asdict, dataclass
from datetime import date, datetime, timedelta
from decimal import Decimal
from io import StringIO
from typing import Any, Dict, Generator, List, Optional, Set, cast
from uuid import UUID

from sqlalchemy import func, select, union_all
from werkzeug.exceptions import NotFound

import massgov.pfml.api.app as app
import massgov.pfml.util.logging
from massgov.pfml import db
from massgov.pfml.api.eligibility.benefit import calculate_weekly_benefit_amount
from massgov.pfml.api.eligibility.benefit_year_dates import (
    BenefitYearDateRange,
    calculate_benefit_year_dates,
)
from massgov.pfml.api.eligibility.eligibility_util import StateMetrics
from massgov.pfml.api.models.claims.common import remap_absence_period_type
from massgov.pfml.api.models.employers.responses import AbsencePeriodCSVResponse
from massgov.pfml.db.lookup_data.employees import (
    ABSENCE_REASON_ID_CLAIM_TYPE_ID_MAPPING,
    ClaimType,
    LeaveRequestDecision,
)
from massgov.pfml.db.models.applications import (
    ContinuousLeavePeriod,
    IntermittentLeavePeriod,
    ReducedScheduleLeavePeriod,
)
from massgov.pfml.db.models.employees import BenefitYear, BenefitYearContribution
from massgov.pfml.db.models.state_metrics import BenefitsMetrics, UnemploymentMetric
from massgov.pfml.db.queries.leave_admins import (
    absence_periods_by_employer_ids_query,
    get_user_leave_administrators,
)

logger = massgov.pfml.util.logging.get_logger(__name__)


@dataclass
class AbsencePeriodResult:
    claim_id: UUID
    application_id: UUID
    date_of_birth: datetime | None
    absence_case_id: str | None
    employer_name: str | None
    employee_id: UUID
    employee_first_name: str | None
    employee_last_name: str | None
    employer_id: UUID | None
    fein: str | None
    absence_period_start_date: datetime | None
    absence_period_end_date: datetime | None
    absence_period_id: UUID | None
    absence_period_type_description: str | None
    absence_reason_id: int | None
    leave_request_decision_description: str | None
    submitted_time: datetime | None
    completed_time: datetime | None
    org_unit_name: str | None
    la_reviewer_first_name: str | None
    la_reviewer_last_name: str | None
    la_reviewed_date: datetime | None
    follow_up_date: datetime | None
    tax_identifier: str | None

    @classmethod
    def from_row(cls, row):
        """Create an instance from a SQLAlchemy Row."""
        return cls(**row._mapping)


@dataclass
class LeavePeriodResult:
    earliest_date: date
    application_id: UUID

    @classmethod
    def from_row(cls, row):
        """Create an instance from a SQLAlchemy Row."""
        return cls(**row._mapping)


class Metrics(str, enum.Enum):
    ABSENCE_PERIOD_COUNT = "absence_period_count"
    EMPLOYER_COUNT = "employer_count"
    EMPLOYEE_COUNT = "employee_count"
    APPLICATION_COUNT = "application_count"
    EARLIEST_LEAVE_PERIOD_COUNT = "earliest_leave_period_count"
    BENEFITS_YEARS_COUNT = "benefits_years_count"
    STATE_METRICS_COUNT = "state_metrics_count"
    IAWW_COUNT = "iaww_count"
    IAWW_WITH_VALUE_COUNT = "iaww_with_value_count"


class AbsenceMetrics(str, enum.Enum):
    ABSENCE_PERIOD_WITHOUT_REVIEWER_NAME = "absence_period_without_reviewer_name"
    EMPLOYEE_WITHOUT_TAX_IDENTIFIER = "employee_without_tax_identifier"
    ABSENCE_PERIOD_WITHOUT_ID = "absence_period_without_id"
    ABSENCE_PERIOD_WITHOUT_APPLICATION_ID = "absence_period_without_application_id"
    ABSENCE_PERIOD_WITHOUT_EMPLOYEE_ID = "absence_period_without_employee_id"
    ABSENCE_PERIOD_WITHOUT_EMPLOYER_ID = "absence_period_without_employer_id"
    ABSENCE_PERIOD_WITHOUT_LEAVE_START_DATE = "absence_period_without_leave_start_date"
    ABSENCE_PERIOD_WITHOUT_BENEFITS_YEARS_SET = "absence_period_without_benefits_years_set"
    ABSENCE_PERIOD_WITHOUT_BENEFITS_YEARS = "absence_period_without_benefits_years"
    ABSENCE_PERIOD_WITH_REVIEWER_NAME = "absence_period_with_reviewer_name"
    ABSENCE_PERIOD_WITH_IDENTIFIER = "absence_period_with_identifier"
    ABSENCE_PERIOD_WITH_PERIOD_FIELDS = "absence_period_with_period_fields"
    ABSENCE_PERIOD_WITH_APPLICATION_FIELDS = "absence_period_with_application_fields"
    ABSENCE_PERIOD_WITH_REVIEW_FIELDS = "absence_period_with_review_fields"
    ABSENCE_PERIOD_WITH_MONETARY_FIELDS = "absence_period_with_monetary_fields"
    ABSENCE_PERIOD_PROCESSED_COUNT = "absence_period_processed_count"
    ABSENCE_PERIOD_UNAPPROVED_COUNT = "absence_period_unapproved_count"
    ABSENCE_PERIOD_APPROVED_COUNT = "absence_period_approved_count"
    ABSENCE_PERIOD_WITHOUT_IAWW = "absence_period_without_iaww"
    ABSENCE_PERIOD_WITHOUT_WBA = "absence_period_without_wba"
    ABSENCE_PERIOD_WITH_IAWW = "absence_period_with_iaww"
    ABSENCE_PERIOD_WITH_WBA = "absence_period_with_wba"
    ABSENCE_PERIOD_WITHOUT_STATE_METRICS = "absence_period_without_state_metrics"


class ClaimExport:
    """
    Handles the export functionality for claims, including data initialization and processing.

    This class encapsulates the necessary logic to process claim-related data including
    absence periods, employer and employee information, application details, benefits metrics,
    and related mappings. It pulls and organizes database data through various protected methods
    and computes Individual average weekly wage(IAWW) & Weekly benefits amount needed for claim exports.
    Typically used for generating exports such as a CSV for claims based on a specific user and
    their associated claims.
    """

    def __init__(
        self,
        db_session: db.Session,
        user_id: UUID,
        initialize_data: bool = True,
        max_rows: int | None = None,
    ):
        self.db_session: db.Session = db_session
        self.user_id: UUID = user_id
        self.max_rows: int | None = max_rows

        self.absence_periods: List[AbsencePeriodResult] = []
        self.state_metrics: List[StateMetrics] = []

        self.employer_ids: Set[UUID] = set()
        self.employee_ids: Set[UUID] = set()
        self.application_ids: Set[UUID] = set()
        self.benefits_year_ids: Set[UUID] = set()

        self.application_claim_ids_map: Dict[UUID, UUID] = {}
        self.employee_tax_identifiers_map: Dict[UUID, str] = {}
        self.benefits_year_average_weekly_wage_map: Dict[UUID, Decimal | None] = {}
        self.claim_start_date_map: Dict[UUID, Set[date]] = defaultdict(set)
        self.employee_benefits_year_map: Dict[UUID, Set[BenefitYear]] = defaultdict(set)

        self.metrics: Dict[str, int] = {}
        self.log_attributes: Dict[str, Any] = {}

        # TODO: Add comment here
        if initialize_data:
            self.initialize_data()

    def _initialize_export_metrics(self):
        self.log_attributes = {"user_id": str(self.user_id)}
        # Initialize all metrics to 0
        self.metrics = {metric: 0 for metric in AbsenceMetrics} | {metric: 0 for metric in Metrics}

    def initialize_data(self):
        try:
            self._initialize_export_metrics()
            self._set_employer_ids()
            self._set_absence_periods()
            self._set_employee_ids()
            self._set_application_ids()
            self._set_application_claim_ids_map()
            self._set_claim_start_date_map()
            self._set_benefits_metrics()
            self._set_employee_benefits_year_map()
            self._set_benefits_year_average_weekly_wage_map()
        except Exception as error:
            logger.error(
                "Error initializing data for claim export",
                extra={"error": str(error), **self.metrics} | self.log_attributes,
                exc_info=error,
            )
            raise error

    def _set_employer_ids(self):
        # Get all verified and active leave admins associated with the current user
        leave_admins = get_user_leave_administrators(str(self.user_id), self.db_session)
        self.employer_ids = {la.employer_id for la in leave_admins}
        self.metrics[Metrics.EMPLOYER_COUNT] = len(self.employer_ids)
        self.log_attributes = self.log_attributes | {
            "employer_ids": ",".join(map(str, self.employer_ids))
        }

    def _set_absence_periods(self):
        if not self.employer_ids:
            logger.warning(
                "No verified/active leave admins associated with requesting user for CSV export",
                extra=self.metrics | self.log_attributes,
            )

            return

        self.absence_periods = absence_periods_by_employer_ids_query(
            self.db_session, self.employer_ids
        ).all()

        absence_periods_count = len(self.absence_periods)
        self.metrics[Metrics.ABSENCE_PERIOD_COUNT] = absence_periods_count

        if self.max_rows is not None and absence_periods_count > self.max_rows:
            logger.warning(
                f"Too many absence periods ({absence_periods_count}) found for user {self.user_id}. "
                f"Max rows set to {self.max_rows}. ",
                extra=self.metrics | self.log_attributes,
            )
            raise ValueError(
                f"Too many absence periods ({absence_periods_count}) found for user {self.user_id}. "
                f"Max rows set to {self.max_rows}. "
            )

    def _set_employee_ids(self):
        self.employee_ids = {
            absence_period.employee_id
            for absence_period in self.absence_periods
            if absence_period.employee_id
        }
        self.metrics[Metrics.EMPLOYEE_COUNT] = len(self.employee_ids)

    def _set_application_ids(self):
        self.application_ids = {
            absence_period.application_id
            for absence_period in self.absence_periods
            if absence_period.application_id
        }
        self.metrics[Metrics.APPLICATION_COUNT] = len(self.application_ids)

    def _set_application_claim_ids_map(self):
        self.application_claim_ids_map = {
            absence_period.application_id: absence_period.claim_id
            for absence_period in self.absence_periods
            if absence_period.application_id and absence_period.claim_id
        }

    def _get_benefit_year_contributions_from_benefits_year_ids(
        self,
    ) -> List[BenefitYearContribution]:
        benefits_year_contributions = (
            self.db_session.query(
                BenefitYearContribution.benefit_year_id.label("benefit_year_id"),
                BenefitYearContribution.average_weekly_wage.label("average_weekly_wage"),
            )
            .filter(BenefitYearContribution.benefit_year_id.in_(self.benefits_year_ids))
            .all()
        )

        return cast(List[BenefitYearContribution], benefits_year_contributions)

    def _get_benefit_years_from_employee_ids(self) -> List[BenefitYear]:
        """
        Get a list of benefit years based on the employee IDs.
        """
        benefit_years = (
            self.db_session.query(
                BenefitYear.benefit_year_id.label("benefit_year_id"),
                BenefitYear.employee_id.label("employee_id"),
                BenefitYear.start_date.label("start_date"),
                BenefitYear.end_date.label("end_date"),
            )
            .filter(BenefitYear.employee_id.in_(self.employee_ids))
            .all()
        )
        self.metrics[Metrics.BENEFITS_YEARS_COUNT] = len(benefit_years)

        return cast(List[BenefitYear], benefit_years)

    def _get_leave_periods_start_date_from_application_ids(self) -> List[LeavePeriodResult]:
        union_query = union_all(
            select(ContinuousLeavePeriod.start_date, ContinuousLeavePeriod.application_id).filter(
                ContinuousLeavePeriod.application_id.in_(self.application_ids)
            ),
            select(
                ReducedScheduleLeavePeriod.start_date, ReducedScheduleLeavePeriod.application_id
            ).filter(ReducedScheduleLeavePeriod.application_id.in_(self.application_ids)),
            select(
                IntermittentLeavePeriod.start_date, IntermittentLeavePeriod.application_id
            ).filter(IntermittentLeavePeriod.application_id.in_(self.application_ids)),
        ).alias("combined_dates")

        query = select(
            func.min(union_query.c.start_date).label("earliest_date"), union_query.c.application_id
        ).group_by(union_query.c.application_id)

        earliest_leave_periods = self.db_session.execute(query).all()

        self.metrics[Metrics.EARLIEST_LEAVE_PERIOD_COUNT] = len(earliest_leave_periods)

        return cast(List[LeavePeriodResult], earliest_leave_periods)

    def _get_benefits_metrics(self) -> List[BenefitsMetrics]:
        benefits_metrics = (
            self.db_session.query(BenefitsMetrics)
            .order_by(BenefitsMetrics.effective_date.desc())
            .all()
        )
        self.metrics[Metrics.STATE_METRICS_COUNT] = len(benefits_metrics)

        return benefits_metrics

    def _get_iaww(
        self, absence_period_result: AbsencePeriodResult, benefit_year_dates: BenefitYearDateRange
    ) -> Optional[Decimal]:
        benefit_years_set = self.employee_benefits_year_map.get(absence_period_result.employee_id)

        if benefit_years_set is None:
            self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITHOUT_IAWW] += 1
            self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITHOUT_BENEFITS_YEARS_SET] += 1
            return None

        found_benefit_year = [
            benefit_year
            for benefit_year in benefit_years_set
            if benefit_year.start_date <= benefit_year_dates.start_date <= benefit_year.end_date
        ]

        if not found_benefit_year:
            self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITHOUT_IAWW] += 1
            self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITHOUT_BENEFITS_YEARS] += 1
            return None

        benefit_year_id = found_benefit_year[0].benefit_year_id

        average_weekly_wage = self.benefits_year_average_weekly_wage_map.get(benefit_year_id, None)

        if not average_weekly_wage:
            self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITHOUT_IAWW] += 1
            return None

        self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITH_IAWW] += 1

        return average_weekly_wage

    def _get_wba(
        self, benefit_year_dates: BenefitYearDateRange, iaww: Decimal
    ) -> Optional[Decimal]:
        found_state_metrics = [
            state_metric
            for state_metric in self.state_metrics
            if state_metric.for_benefits.effective_date <= benefit_year_dates.start_date
        ]

        if not found_state_metrics:
            self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITHOUT_WBA] += 1
            self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITHOUT_STATE_METRICS] += 1
            return None

        state_weekly_wage = found_state_metrics[0].for_benefits.average_weekly_wage
        max_weekly_wage = found_state_metrics[0].for_benefits.maximum_weekly_benefit_amount

        wba = calculate_weekly_benefit_amount(iaww, state_weekly_wage, max_weekly_wage)

        if not wba:
            self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITHOUT_WBA] += 1
            return None

        self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITH_WBA] += 1

        return wba

    def _set_claim_start_date_map(self):
        earliest_leave_periods = self._get_leave_periods_start_date_from_application_ids()
        for leave_period in earliest_leave_periods:
            leave_period_data = LeavePeriodResult.from_row(leave_period)
            claim_id = self.application_claim_ids_map[leave_period_data.application_id]
            self.claim_start_date_map[claim_id].add(leave_period_data.earliest_date)

    def _set_benefits_metrics(self):
        benefits_metrics = self._get_benefits_metrics()

        for metrics in benefits_metrics:
            self.state_metrics.append(
                StateMetrics(metrics, UnemploymentMetric(datetime.now(), "0"))
            )

    def _set_employee_benefits_year_map(self):
        all_benefit_year_dates = set()

        for _, start_dates in self.claim_start_date_map.items():
            benefit_year_dates = calculate_benefit_year_dates(min(start_dates))
            all_benefit_year_dates.add(benefit_year_dates.start_date)
            all_benefit_year_dates.add(benefit_year_dates.end_date)

        if not all_benefit_year_dates:
            logger.warning(
                "No benefit year dates found for the provided claim start dates",
                extra=self.metrics | self.log_attributes,
            )
            return

        benefits_years = self._get_benefit_years_from_employee_ids()

        for benefit_year in benefits_years:
            self.benefits_year_ids.add(benefit_year.benefit_year_id)
            self.employee_benefits_year_map[benefit_year.employee_id].add(benefit_year)

    def _set_benefits_year_average_weekly_wage_map(self):
        benefits_year_contributions: List[BenefitYearContribution] = (
            self._get_benefit_year_contributions_from_benefits_year_ids()
        )

        self.benefits_year_average_weekly_wage_map = {
            contribution.benefit_year_id: contribution.average_weekly_wage
            for contribution in benefits_year_contributions
        }
        self.metrics[Metrics.IAWW_COUNT] = len(self.benefits_year_average_weekly_wage_map.keys())
        self.metrics[Metrics.IAWW_WITH_VALUE_COUNT] = len(
            [
                iaww
                for iaww in self.benefits_year_average_weekly_wage_map.values()
                if iaww and iaww > 0
            ]
        )

    def _populate_employer_review_complete_by(
        self,
        absence_period_result: AbsencePeriodResult,
        absence_period_response: AbsencePeriodCSVResponse,
    ) -> None:
        if (
            not absence_period_result.la_reviewer_first_name
            or not absence_period_result.la_reviewer_last_name
        ):
            self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITHOUT_REVIEWER_NAME] += 1
            return

        absence_period_response.employer_review_complete_by = (
            f"{absence_period_result.la_reviewer_first_name} {absence_period_result.la_reviewer_last_name}"
        ).strip()

        self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITH_REVIEWER_NAME] += 1

    def _populate_identification_fields(
        self,
        absence_period_result: AbsencePeriodResult,
        absence_period_response: AbsencePeriodCSVResponse,
    ) -> None:

        if not absence_period_result.tax_identifier:
            self.metrics[AbsenceMetrics.EMPLOYEE_WITHOUT_TAX_IDENTIFIER] += 1
            return

        absence_period_response.ssn_last_four = (
            f"***-**-{absence_period_result.tax_identifier[-4:]}"
        )

        self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITH_IDENTIFIER] += 1

    def _populate_absence_period_fields(
        self,
        absence_period_result: AbsencePeriodResult,
        absence_period_response: AbsencePeriodCSVResponse,
    ) -> None:
        claim_type_id = (
            ABSENCE_REASON_ID_CLAIM_TYPE_ID_MAPPING.get(
                absence_period_result.absence_reason_id, None
            )
            if absence_period_result.absence_reason_id
            else None
        )

        if claim_type_id == ClaimType.FAMILY_LEAVE.claim_type_id:
            absence_period_response.absence_reason = ClaimType.FAMILY_LEAVE.claim_type_description
        elif claim_type_id == ClaimType.MEDICAL_LEAVE.claim_type_id:
            absence_period_response.absence_reason = ClaimType.MEDICAL_LEAVE.claim_type_description
        elif claim_type_id == ClaimType.MILITARY_LEAVE.claim_type_id:
            absence_period_response.absence_reason = ClaimType.MILITARY_LEAVE.claim_type_description

        absence_period_response.absence_period_type = remap_absence_period_type(
            absence_period_result.absence_period_type_description
        )

        if not absence_period_result.absence_period_id:
            self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITHOUT_ID] += 1
            return

        if absence_period_result.absence_period_start_date:
            absence_period_response.leave_begin_date = (
                absence_period_result.absence_period_start_date.strftime("%m/%d/%Y")
            )

        if absence_period_result.absence_period_end_date:
            absence_period_response.leave_end_date = (
                absence_period_result.absence_period_end_date.strftime("%m/%d/%Y")
            )

        self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITH_PERIOD_FIELDS] += 1

    def _populate_application_fields(
        self,
        absence_period_result: AbsencePeriodResult,
        absence_period_response: AbsencePeriodCSVResponse,
    ) -> None:
        if not absence_period_result.application_id:
            self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITHOUT_APPLICATION_ID] += 1
            return

        if absence_period_result.date_of_birth:
            absence_period_response.dob = absence_period_result.date_of_birth.strftime("%m/%d/****")

        if absence_period_result.submitted_time:
            absence_period_response.claimant_p1_submission_date = (
                absence_period_result.submitted_time.strftime("%m/%d/%Y")
            )

        if absence_period_result.completed_time:
            absence_period_response.claimant_p3_completion_date = (
                absence_period_result.completed_time.strftime("%m/%d/%Y")
            )

        self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITH_APPLICATION_FIELDS] += 1

    def _populate_review_fields(
        self,
        absence_period_result: AbsencePeriodResult,
        absence_period_response: AbsencePeriodCSVResponse,
    ) -> None:
        if absence_period_result.follow_up_date:
            absence_period_response.employer_review_due_date = (
                absence_period_result.follow_up_date.strftime("%m/%d/%Y")
            )

        if absence_period_result.la_reviewed_date:
            absence_period_response.employer_review_complete_date = (
                absence_period_result.la_reviewed_date.strftime("%m/%d/%Y")
            )

        absence_period_response.decision = absence_period_result.leave_request_decision_description

        dfml_expected_decision_date_delta_days = (
            app.get_app_config().dfml_expected_decision_date_delta_days
        )
        if absence_period_result.la_reviewed_date is not None:
            absence_period_response.dfml_expected_decision_date = (
                absence_period_result.la_reviewed_date
                + timedelta(days=dfml_expected_decision_date_delta_days)
            ).strftime("%m/%d/%Y")

        self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITH_REVIEW_FIELDS] += 1

    def _populate_monetary_fields(
        self,
        absence_period_result: AbsencePeriodResult,
        absence_period_response: AbsencePeriodCSVResponse,
    ) -> None:
        if (
            absence_period_result.leave_request_decision_description
            != LeaveRequestDecision.APPROVED.leave_request_decision_description
        ):
            self.metrics[AbsenceMetrics.ABSENCE_PERIOD_UNAPPROVED_COUNT] += 1
            return

        self.metrics[AbsenceMetrics.ABSENCE_PERIOD_APPROVED_COUNT] += 1

        if not absence_period_result.employee_id:
            self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITHOUT_EMPLOYEE_ID] += 1
            return

        if not absence_period_result.employer_id:
            self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITHOUT_EMPLOYER_ID] += 1
            return

        leave_start_dates = self.claim_start_date_map.get(absence_period_result.claim_id)

        if leave_start_dates is None:
            self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITHOUT_LEAVE_START_DATE] += 1
            return

        benefit_year_dates = calculate_benefit_year_dates(min(leave_start_dates))

        iaww = self._get_iaww(absence_period_result, benefit_year_dates)
        if not iaww:
            return

        absence_period_response.iaww = f"${float(iaww):,.2f}"

        wba = self._get_wba(benefit_year_dates, iaww)
        if not wba:
            return

        absence_period_response.weekly_benefit_amount = f"${float(wba):,.2f}"

        self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITH_MONETARY_FIELDS] += 1

    def _process_absence_period_response(self) -> Generator[Dict[str, str | None], None, None]:
        for absence_period in self.absence_periods:
            try:
                if not absence_period.employee_id or not absence_period.employer_id:
                    logger.error(
                        "Missing employee ID or employer ID in absence period data",
                        extra=self.metrics | self.log_attributes,
                    )
                    raise ValueError("Employee ID or Employer ID is missing")

                absence_period_data: AbsencePeriodResult = AbsencePeriodResult.from_row(
                    absence_period
                )
                absence_period_response: AbsencePeriodCSVResponse = AbsencePeriodCSVResponse(
                    **asdict(absence_period_data)
                )

                self._populate_employer_review_complete_by(absence_period, absence_period_response)
                self._populate_identification_fields(absence_period, absence_period_response)
                self._populate_absence_period_fields(absence_period, absence_period_response)
                self._populate_application_fields(absence_period, absence_period_response)
                self._populate_review_fields(absence_period, absence_period_response)
                self._populate_monetary_fields(absence_period, absence_period_response)

                yield absence_period_response.dict(by_alias=True)

                self.metrics[AbsenceMetrics.ABSENCE_PERIOD_PROCESSED_COUNT] += 1
            except Exception as error:
                logger.error(
                    "Error processing absence period response",
                    extra={
                        "error": str(error),
                        "application_id": absence_period.application_id,
                        "claim_id": absence_period.claim_id,
                        "fineos_absence_id": absence_period.absence_case_id,
                        "employer_id": absence_period.employer_id,
                        "employee_id": absence_period.employee_id,
                        **self.metrics,
                    }
                    | self.log_attributes,
                    exc_info=error,
                )
                raise error

    def create_csv_content(self, output: StringIO) -> None:
        csv_data = self._process_absence_period_response()
        if not csv_data:
            logger.warning(
                "Leave admin requested CSV download but no data available",
                extra=self.metrics | self.log_attributes,
            )
            raise NotFound("No data to format into CSV")

        try:
            headers = [
                field.alias for field in AbsencePeriodCSVResponse.__fields__.values() if field.alias
            ]
            writer = csv.DictWriter(output, fieldnames=headers)
            writer.writeheader()
            writer.writerows(csv_data)

            logger.info("Successfully formatted CSV data", extra=self.metrics | self.log_attributes)

        except Exception as error:
            logger.error(
                "Error generating CSV file",
                extra={"error": str(error), **self.metrics} | self.log_attributes,
                exc_info=error,
            )
            raise error

    def finalize_log(self):
        """
        Finalize the log attributes after processing.
        """
        logger.info(
            "Final log attributes",
            extra={
                "found_identifier_issues": self.metrics[
                    AbsenceMetrics.EMPLOYEE_WITHOUT_TAX_IDENTIFIER
                ]
                > 0,
                "found_wba_issues": self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITHOUT_WBA] > 0,
                "unprocessed_absence_periods_issues": self.metrics[
                    AbsenceMetrics.ABSENCE_PERIOD_PROCESSED_COUNT
                ]
                < self.metrics[Metrics.ABSENCE_PERIOD_COUNT],
                "approved_vs_unapproved_issues": (
                    self.metrics[AbsenceMetrics.ABSENCE_PERIOD_APPROVED_COUNT]
                    + self.metrics[AbsenceMetrics.ABSENCE_PERIOD_UNAPPROVED_COUNT]
                )
                != self.metrics[Metrics.ABSENCE_PERIOD_COUNT],
                "approved_vs_monetary_issues": (
                    self.metrics[AbsenceMetrics.ABSENCE_PERIOD_APPROVED_COUNT]
                    != self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITH_MONETARY_FIELDS]
                ),
                "approved_vs_iaww_issues": (
                    self.metrics[AbsenceMetrics.ABSENCE_PERIOD_APPROVED_COUNT]
                    != self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITH_IAWW]
                ),
                "approved_vs_wba_issues": (
                    self.metrics[AbsenceMetrics.ABSENCE_PERIOD_APPROVED_COUNT]
                    != self.metrics[AbsenceMetrics.ABSENCE_PERIOD_WITH_WBA]
                ),
            },
        )
