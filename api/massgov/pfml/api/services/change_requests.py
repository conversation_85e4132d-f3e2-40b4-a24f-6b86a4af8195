from typing import List

from sqlalchemy.orm.session import Session

import massgov
import massgov.pfml.api.models.change_request.common as api_models
import massgov.pfml.db.lookups as db_lookups
from massgov.pfml.db.lookup_data.deferred_submission_item import DeferredSubmissionStatus
from massgov.pfml.db.models.change_request import ChangeRequest, LkChangeRequestType
from massgov.pfml.db.models.deferred_submission_item import DeferredSubmissionItem
from massgov.pfml.db.models.employees import Claim

logger = massgov.pfml.util.logging.get_logger(__name__)


def defer_change_request(
    change_request: ChangeRequest, db_session: massgov.pfml.db.Session
) -> DeferredSubmissionItem:
    deferred_submission_item: DeferredSubmissionItem = DeferredSubmissionItem(
        change_request_id=change_request.change_request_id,
        deferred_submission_status_id=DeferredSubmissionStatus.PENDING.deferred_submission_status_id,
    )
    db_session.add(deferred_submission_item)
    return deferred_submission_item


def get_change_requests_from_db(claim: Claim, db_session: Session) -> List[ChangeRequest]:

    change_requests = (
        db_session.query(ChangeRequest).filter(ChangeRequest.claim_id == claim.claim_id).all()
    )

    return change_requests


def claim_has_in_progress_requests(claim: Claim, db_session: Session) -> bool:
    """Check whether this claim has any in progress change requests"""
    in_progress_cr = (
        db_session.query(ChangeRequest)
        .filter(
            ChangeRequest.claim_id == claim.claim_id,
            ChangeRequest.submitted_time == None,  # noqa: E711
        )
        .one_or_none()
    )
    return in_progress_cr is not None


def update_change_request_db(
    db_session: Session,
    update_request: api_models.ChangeRequest,
    change_request: ChangeRequest,
) -> ChangeRequest:
    for key in update_request.__fields_set__:
        value = getattr(update_request, key)

        if key == "change_request_type":
            change_request_type = db_lookups.by_value(db_session, LkChangeRequestType, value)
            # Key could possibly be included but not set if a user is trying to click through without selecting a type in the portal flow
            if change_request_type is None:
                continue
            assert isinstance(change_request_type, LkChangeRequestType)
            change_request.change_request_type_id = change_request_type.change_request_type_id
        else:
            setattr(change_request, key, value)

    return change_request
