import json
from datetime import datetime
from typing import Any, Literal, TypedDict

from massgov.pfml.util.logging import Logger

# Moved types to separate file to prevent circular import issues
TokenType = Literal["access_token", "refresh_token"]


class TokenPayload(TypedDict):
    iss: str
    sub: str
    aud: str
    exp: int
    iat: int
    nbf: int
    jti: str
    token_type: TokenType


def _log_jwt_error(
    logger: Logger,
    message: str,
    error_type: str,
    **extra_fields: Any,
) -> None:
    logger.error(message, extra={"error": error_type, "error_type": error_type, **extra_fields})


# Helper class to serialize datetime objects in JSON.
# Ensures datetime claims ('exp', 'iat', 'nbf') are properly serialized in JWT payloads.
class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            # Convert datetime to ISO 8601 string format (e.g., '2024-01-07T14:30:45.123456')
            return obj.isoformat()
        return super().default(obj)  # Fallback to default behavior for other types
