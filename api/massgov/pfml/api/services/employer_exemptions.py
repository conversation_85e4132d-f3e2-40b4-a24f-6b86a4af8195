from datetime import date
from typing import List, Optional
from uuid import UUID

import massgov.pfml.api.models.phones.common as phone_common_io
import massgov.pfml.db.lookups as db_lookups
import massgov.pfml.util.logging
from massgov.pfml.api.models.common import LookupEnum
from massgov.pfml.api.models.employer_exemptions.common import (
    SelfInsuredPlanDetails,
    SelfInsuredPlanQuestions,
)
from massgov.pfml.api.models.employer_exemptions.requests import (
    EmployerExemptionApplicationRequestBody,
    PurchasedPlanDetailsRequestBody,
)
from massgov.pfml.db import Session
from massgov.pfml.db.lookup_data.employer_exemptions import EmployerExemptionApplicationStatus
from massgov.pfml.db.lookup_data.phone import PhoneType
from massgov.pfml.db.models.employer_exemptions import EmployerExemptionApplication, InsurancePlan
from massgov.pfml.db.models.phone import Phone
from massgov.pfml.util.datetime.quarter import Quarter
from massgov.pfml.util.sqlalchemy import get_or_404

logger = massgov.pfml.util.logging.get_logger(__name__)


def employer_exemption_application_is_editable(
    employer_exemption_application: EmployerExemptionApplication,
) -> bool:
    return (
        employer_exemption_application.employer_exemption_application_status_id
        == EmployerExemptionApplicationStatus.DRAFT.employer_exemption_application_status_id
    )


def add_or_update_phone(
    db_session: Session,
    db_column_name: str,
    new_phone: Optional[phone_common_io.Phone],
    employer_exemption_application: EmployerExemptionApplication,
) -> None:
    # to reach this point, the phone number must be a valid phone number
    #   - front-end validation will prevent length/format issues (eg. 123, 202-123-45678)
    #   - back-end validation will prevent invalid phone numbers (eg ************, ************)
    #
    # save all valid phone numbers. (a value of None or "" is considered valid)
    #
    # submit validation checks the value of the stored phone number. a submit
    # validation error will be received if the phone number is empty (eg. None or "")
    if not EmployerExemptionApplication.is_db_column_name(db_column_name):
        raise KeyError(f"Unable to find column '{db_column_name}' in EmployerExemptionApplication.")

    # if the phone_field_name is the fk column name (eg. contact_phone_id),
    # get the related phone object (eg. contact_phone)
    if EmployerExemptionApplication.is_phone_db_model_foreign_key(db_column_name):
        phone = employer_exemption_application.get_related_phone_object(db_column_name)
    else:
        raise KeyError(
            f"'{db_column_name}' does not have a foreign key relationship with the Phone table"
        )

    # If Phone exists, update with what we have, otherwise, create a new Phone
    if not phone:
        phone_object_name = EmployerExemptionApplication.get_related_phone_object_name(
            db_column_name
        )

        if phone_object_name is None:
            raise KeyError(
                f"Unable to find phone_object_name for 'EmployerExemptionApplication.{db_column_name}'"
            )

        setattr(employer_exemption_application, phone_object_name, Phone())
        phone = getattr(employer_exemption_application, phone_object_name)

    # the phone variable will always have a value. ignore the following lint findings:
    #   - error: Item "None" of "Phone | None" has no attribute "phone_type_id"  [union-attr]
    #   - error: Item "None" of "Phone | None" has no attribute "phone_number"  [union-attr]
    phone.phone_type_id = (  # type: ignore[union-attr]
        PhoneType.get_id(new_phone.phone_type) if new_phone and new_phone.phone_type else None
    )
    phone.phone_number = new_phone.e164 if new_phone else None  # type: ignore[union-attr]

    db_session.add(phone)


def is_valid_purchased_private_insurance(
    db_session: Session, employer_exemption_application: EmployerExemptionApplication
) -> bool:

    insurance_info = (
        db_session.query(InsurancePlan)
        .filter(
            employer_exemption_application.insurance_provider_id
            == InsurancePlan.insurance_provider_id,
            employer_exemption_application.insurance_plan_id == InsurancePlan.insurance_plan_id,
            employer_exemption_application.has_family_exemption
            == InsurancePlan.has_family_exemption,
            employer_exemption_application.has_medical_exemption
            == InsurancePlan.has_medical_exemption,
        )
        .one_or_none()
    )

    return insurance_info is not None


def get_system_calculated_employer_exemption_application_status(
    db_session: Session, employer_exemption_application_id: UUID
) -> int:
    existing_employer_exemption_application = get_or_404(
        db_session, EmployerExemptionApplication, employer_exemption_application_id
    )

    # default to approved, status will be overwritten if necessary
    status = EmployerExemptionApplicationStatus.APPROVED.employer_exemption_application_status_id

    # exemption application approve/deny/in-review rules are as follows:
    #   if insurance policy start date not first day of upcoming quarter
    #       set status to In Review
    #
    #   if self-insured private plan:
    #       - auto-deny: if any required screener question is False
    #       - in review: if all required screener question are True
    #       note: self-insured will either be in review or auto-denied; never auto-approved
    #       note: auto-deny can overwrite date-related in review status
    #       note: self-insured questions determine if plan meets PFML requirements
    #
    #       purchased private plans are automatically approved
    if (
        existing_employer_exemption_application.insurance_plan_effective_at
        != Quarter.first_day_of_next_quarter(date.today())
    ):
        status = (
            EmployerExemptionApplicationStatus.IN_REVIEW.employer_exemption_application_status_id
        )

    if existing_employer_exemption_application.is_self_insured_plan:
        status = (
            EmployerExemptionApplicationStatus.IN_REVIEW.employer_exemption_application_status_id
        )
        self_insured_question_columns = set()

        if existing_employer_exemption_application.has_family_exemption:
            for c in EmployerExemptionApplication.get_columns_required_for_self_insured_family():
                self_insured_question_columns.add(c)

        if existing_employer_exemption_application.has_medical_exemption:
            for c in EmployerExemptionApplication.get_columns_required_for_self_insured_medical():
                self_insured_question_columns.add(c)

        for c in self_insured_question_columns:
            if existing_employer_exemption_application.is_auto_deny_value(c):
                status = (
                    EmployerExemptionApplicationStatus.DENIED.employer_exemption_application_status_id
                )
                break

    elif (
        not existing_employer_exemption_application.is_self_insured_plan
        and not is_valid_purchased_private_insurance(
            db_session, existing_employer_exemption_application
        )
    ):
        status = (
            EmployerExemptionApplicationStatus.IN_REVIEW.employer_exemption_application_status_id
        )

    return status


def get_draft_employer_exemption_application(
    db_session: Session, employer_id: UUID
) -> EmployerExemptionApplication | None:
    """
    Checks if a given Employer has an Exemption Application with status set to Draft.

    Args:
        db_session: SQLAlchemy database session
        employer_id: Employer UUID4

    Returns:
        EmployerExemptionApplication | None: An instance of a Draft Employer Exemption Application if it exists or None .
    """

    existing_draft_application = (
        db_session.query(EmployerExemptionApplication)
        .filter(
            EmployerExemptionApplication.employer_id == employer_id,
            EmployerExemptionApplication.employer_exemption_application_status
            == EmployerExemptionApplicationStatus.DRAFT,
        )
        .one_or_none()
    )

    return existing_draft_application


def create_new_employer_exemption_application(
    db_session: Session, employer_exemption_application: EmployerExemptionApplication
) -> EmployerExemptionApplication:
    """
    Persist a new Employer Exemption Application to the Database and return it.

    Args:
        db_session: SQLAlchemy database session
        employer_exemption_application: a partial instance of an Employer Exemption Application

    Returns:
        EmployerExemptionApplication: The instance of an Employer Exemption Application that was just created.
    """
    db_session.add(employer_exemption_application)
    db_session.commit()
    db_session.refresh(employer_exemption_application)

    return employer_exemption_application


def get_all_employer_exemption_applications(
    db_session: Session, employer_id: UUID
) -> List[EmployerExemptionApplication] | None:
    """
    Checks if a given Employer has an Exemption Application with status set to Draft.

    Args:
        db_session: SQLAlchemy database session
        employer_id: Employer UUID4

    Returns:
        EmployerExemptionApplication | None: An instance of a Draft Employer Exemption Application if it exists or None .
    """

    all_applications = (
        db_session.query(EmployerExemptionApplication)
        .filter(EmployerExemptionApplication.employer_id == employer_id)
        .all()
    )

    return all_applications


def update_from_request(
    db_session: Session,
    update_request_body: EmployerExemptionApplicationRequestBody,
    employer_exemption_application: EmployerExemptionApplication,
) -> EmployerExemptionApplication:
    PURCHASED_PLAN_KEY = "purchased_plan"
    SELF_INSURED_KEY = "self_insured"
    SELF_INSURED_QUESITONS_KEY = "questions"

    IGNORE_REQUEST_BODY_FIELDS = [
        "employer_id",  # cannot be updated once created
        "created_by_user_id",  # cannot be updated once created
        "employer_exemption_application_status",  # status is determined by backend logic
        "employer_exemption_application_status_id",  # status is determined by backend logic
        "documents",
    ]

    purchased_plan = None
    self_insured = None
    self_insured_questions = None

    purchased_plan = getattr(update_request_body, PURCHASED_PLAN_KEY)
    self_insured = getattr(update_request_body, SELF_INSURED_KEY)

    if hasattr(self_insured, SELF_INSURED_QUESITONS_KEY):
        self_insured_questions = getattr(self_insured, SELF_INSURED_QUESITONS_KEY)

    for obj in [update_request_body, purchased_plan, self_insured, self_insured_questions]:
        if obj is None:
            continue

        for key in [
            field for field in obj.__fields_set__ if field not in IGNORE_REQUEST_BODY_FIELDS
        ]:
            if isinstance(
                getattr(obj, key),
                (PurchasedPlanDetailsRequestBody, SelfInsuredPlanDetails, SelfInsuredPlanQuestions),
            ):
                # skip keys associated with PurchasedPlanDetailsRequestBody, SelfInsuredPlanDetails,
                # SelfInsuredPlanQuestions types; data is contained in the purchased_plan,
                # self_insured, self_insured_questions objects (eg:
                #     purchased_plan = getattr(body, PURCHASED_PLAN_KEY)
                #     self_insured = getattr(body, SELF_INSURED_KEY)
                # )
                continue

            value = getattr(obj, key)

            if EmployerExemptionApplicationRequestBody.is_phone_object(key):
                db_column_name = (
                    EmployerExemptionApplicationRequestBody.get_mapped_to_db_column_name(key)
                )
                add_or_update_phone(
                    db_session, db_column_name, value, employer_exemption_application
                )
                continue

            if isinstance(value, LookupEnum):
                lookup_model = db_lookups.by_value(db_session, value.get_lookup_model(), value)

                if lookup_model:
                    value = lookup_model

            setattr(employer_exemption_application, key, value)

    columns_to_clear = set()

    if update_request_body.is_self_insured_plan is not None:
        if not update_request_body.is_self_insured_plan:
            columns_to_clear.update(
                EmployerExemptionApplication.get_columns_to_clear_when_is_not_self_insured()
            )
        else:
            columns_to_clear.update(
                EmployerExemptionApplication.get_columns_to_clear_when_is_self_insured()
            )

            if EmployerExemptionApplication.is_family_only_exemption(
                update_request_body.has_family_exemption, update_request_body.has_medical_exemption
            ):
                columns_to_clear.update(
                    EmployerExemptionApplication.get_columns_to_clear_when_is_self_insured_family()
                )
            elif EmployerExemptionApplication.is_medical_only_exemption(
                update_request_body.has_family_exemption, update_request_body.has_medical_exemption
            ):
                columns_to_clear.update(
                    EmployerExemptionApplication.get_columns_to_clear_when_is_self_insured_medical()
                )

    if update_request_body.has_third_party_administrator is False:
        columns_to_clear.update(
            employer_exemption_application.get_columns_to_clear_when_not_third_party_admin()
        )

    for col in columns_to_clear:
        val = getattr(employer_exemption_application, col)
        setattr(employer_exemption_application, col, None)

        if EmployerExemptionApplication.is_phone_db_model_foreign_key(col) and val is not None:
            phone = db_session.query(Phone).filter(Phone.phone_id == val).one()
            db_session.delete(phone)

    db_session.add(employer_exemption_application)
    db_session.commit()
    db_session.refresh(employer_exemption_application)

    return employer_exemption_application
