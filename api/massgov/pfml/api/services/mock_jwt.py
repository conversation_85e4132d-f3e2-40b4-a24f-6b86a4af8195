import base64
import json
import time
from datetime import datetime, timezone

import jwt
import jwt.algorithms
from cryptography.hazmat.primitives.asymmetric import padding

from .jwt import JWTManager
from .oauth_server_util import TokenPayload

MOCK_OAUTH_SERVER_PRIVATE_KEY = {
    "kty": "RSA",
    "kid": "test_key_id",
    "use": "sig",
    "alg": "RS256",
    "n": "iWBm-DQbycUqrPBSD5yk73zxyIr66hBUCyPCShW-btQ-nyBk1E-h4AvtqHpl4Y1aghQDTnn2gLHiRtV_XJtCpK1PEJ3SCqw6wGOEw5bbG7Q88KDvTMUF5k6gzRMHMBTD7lMNPIY-oCuh_Rwvg19hGBD2O6rA2sMHyTB-O2ZwL6M",
    "e": "AQAB",
    "d": "WC8GyisA73teUpcNxjHCem0U86urN5b1rBTvQglFLfWWoST1NIhNm_lsPGsdfTT0tW1NVhHaV3BYlSm06AFKphL1UtHI0z_xS-CnRuqYljyca1YQWhuFETP01c1tVmA4g8iFGUW_VkQ6QgyHiC_kaz_v8skOLLgLoR6KPeo_yPE",
    "p": "o2tSqdoRyCMnzT_CZx1Oq8WCwMo7rWMKFx-wlwaXOoxzqDv0YhjP1t7DqDn5V8yERCVBUP9ZPDIzNmBUQMul7bwIpfs",
    "q": "1zQdXV-7I2VNSUhzRAYvhJAOFvAKiJv8lJc2_66XNGww0g3og_sBPrGwFsO2stVd-rJ1mZWV8D78LHR5",
    "dp": "i8Sa6tKsKrSGsjE6H98dDiTbc_CDogP2-VgNPN5SMa02rki4972o5WmZhiQvcjxlU7NZbeE3fRiiXHt_E_wZan9MFkk",
    "dq": "QRYM74mdgrYHqutTmTY5tuEOsddFiE2NFa-qPagjKQKzvUPhl9EZbkm1VR06K1omw0SoFpxMLc4O3K8Z",
    "qi": "SebQz5QdxAvqGSDUvchSLpxXf0Ry0NhYdBCCMftTwqqVcNjY3GKQ8-YET5Y_dwMmEYM51DCCDolVxBAjbNDlKU7JIjU",
}

MOCK_OAUTH_SERVER_PAYLOAD = {
    "sub": "5a7be3dc-0fe9-42b6-a401-99a36d14208e",
    "iss": "https://paidleave-api.dfml.eol.mass.gov",
    "aud": "test_client_id",
    "exp": int(time.time()) + 300,
    "iat": int(datetime.now(timezone.utc).timestamp()),
    "nbf": int(datetime.now(timezone.utc).timestamp()),
    "jti": "test_jti",
    "token_type": "refresh_token",
}

MOCK_OAUTH_SEVER_HEADER = {"alg": "RS256", "typ": "JWT", "kid": "test_kms_key_id"}


def token_payload(token_type: str) -> dict:
    payload = MOCK_OAUTH_SERVER_PAYLOAD.copy()
    payload["token_type"] = token_type

    return payload


class MockKMSAsymmetricJWTManager(JWTManager):
    def __init__(self, user_id: str, token_type: str = "refresh_token") -> None:
        self.token_type = token_type
        self.user_id = user_id

    def sign(self, payload: TokenPayload) -> str:
        payload["sub"] = self.user_id

        # Mock KMS signing by using the private key directly bypassing AWS KMS
        key = jwt.algorithms.RSAAlgorithm.from_jwk(MOCK_OAUTH_SERVER_PRIVATE_KEY)
        if not isinstance(key, jwt.algorithms.RSAPrivateKey):
            raise ValueError("Private key required for signing")

        # Encode the header and payload using Base64 URL-safe encoding
        header_encoded = (
            base64.urlsafe_b64encode(json.dumps(MOCK_OAUTH_SEVER_HEADER).encode())
            .rstrip(b"=")
            .decode()
        )
        payload_encoded = (
            base64.urlsafe_b64encode(json.dumps(payload).encode()).rstrip(b"=").decode()
        )

        # Sign the message using RS256
        signature = key.sign(
            data=f"{header_encoded}.{payload_encoded}".encode(),
            padding=padding.PKCS1v15(),
            algorithm=jwt.algorithms.hashes.SHA256(),
        )

        signature_encoded = base64.urlsafe_b64encode(signature).rstrip(b"=").decode()

        return f"{header_encoded}.{payload_encoded}.{signature_encoded}"

    def get_public_key(self) -> str:
        return json.dumps(
            {
                "kty": "RSA",
                "kid": "test_kms_key_id",
                "use": "sig",
                "alg": "RS256",
                "n": MOCK_OAUTH_SERVER_PRIVATE_KEY["n"],
                "e": MOCK_OAUTH_SERVER_PRIVATE_KEY["e"],
            }
        )

    def verify(self, token: str, **token_verification_kwargs: dict) -> dict:
        # Split the token into its components
        header_encoded, payload_encoded, signature_encoded = token.split(".")

        # Decode the payload
        payload_padded = payload_encoded + "=" * (-len(payload_encoded) % 4)
        payload_json = base64.urlsafe_b64decode(payload_padded).decode()
        payload = json.loads(payload_json)

        return TokenPayload(**payload)  # type: ignore

    def unverified_token_info(self, token: str) -> dict:
        return self.verify(token)
