import os
import secrets
from datetime import datetime, timedelta, timezone
from typing import Optional

from connexion.lifecycle import ConnexionResponse
from sqlalchemy.orm import Session
from werkzeug.exceptions import ServiceUnavailable, Unauthorized
from werkzeug.security import check_password_hash

import massgov.pfml.api.util.response as response_util
import massgov.pfml.util.logging
from massgov.pfml.api.models.oauth.common import OAuthTokenPayload, OAuthTokenType
from massgov.pfml.api.models.oauth_server.requests import (
    AuthorizationCodeGrantRequest,
    RefreshTokenGrantRequest,
)
from massgov.pfml.api.models.oauth_server.responses import (
    AuthorizationCodeGrantResponse,
    RefreshTokenGrantResponse,
)
from massgov.pfml.api.services.jwt import JWTManager, JWTVerificationError, KMSAsymmetricJWTManager
from massgov.pfml.api.services.oauth_server_util import TokenPayload, TokenType
from massgov.pfml.db.models.employees import User
from massgov.pfml.db.models.oauth_server import OAuthClientCredentials, OAuthServerCode

logger = massgov.pfml.util.logging.get_logger(__name__)


def handle_authorization_code_grant(
    db_session: Session,
    token_request: AuthorizationCodeGrantRequest,
    client_id: str,
    log_extra: Optional[dict] = None,
) -> AuthorizationCodeGrantResponse:
    log_extra = log_extra or {}
    authz_code = token_request.code
    authz_code_record: OAuthServerCode | None = (
        db_session.query(OAuthServerCode)
        .filter(
            OAuthServerCode.authz_code == authz_code,
            OAuthServerCode.expires_at > datetime.now(timezone.utc),
        )
        .one_or_none()
    )

    # Build up log_extra
    log_extra = {
        **log_extra,
        "authz_code": authz_code,
        **(
            {
                "authz_code.oauth_server_code_id": authz_code_record.oauth_server_code_id,
                "authz_code.user_id": authz_code_record.user_id,
                "authz_code.authz_code": authz_code_record.authz_code,
                "authz_code.expires_at": authz_code_record.expires_at,
            }
            if authz_code_record
            else {}
        ),
    }

    if authz_code_record is None:
        logger.error(
            "Cannot find authz_code record in handle_authorization_code_grant()", extra=log_extra
        )
        raise Unauthorized("Invalid or expired authorization code")
    if authz_code_record.expires_at <= datetime.now(timezone.utc):
        logger.error(
            "Authorization code expired in handle_authorization_code_grant. Expiration: {authz_code_record.expires_at}",
            extra=log_extra,
        )
        raise Unauthorized("Invalid or expired authorization code")

    kms_key_id = get_kms_key_id()

    jwt_manager: JWTManager = KMSAsymmetricJWTManager(kms_key_id)

    # generate access/refresh token payloads
    access_token_exp = datetime.now(timezone.utc) + timedelta(minutes=get_access_token_expiration())
    refresh_token_exp = datetime.now(timezone.utc) + timedelta(
        minutes=get_refresh_token_expiration()
    )
    user_id = str(authz_code_record.user.auth_id)
    access_token_payload = generate_token_payload(
        user_id=user_id,
        client_id=client_id,
        expiration=access_token_exp,
        token_type=OAuthTokenType.ACCESS_TOKEN.value,
    )

    refresh_token_payload = generate_token_payload(
        user_id=user_id,
        client_id=client_id,
        expiration=refresh_token_exp,
        token_type=OAuthTokenType.REFRESH_TOKEN.value,
    )

    try:
        access_token = jwt_manager.sign(payload=access_token_payload)
        refresh_token = jwt_manager.sign(payload=refresh_token_payload)
    except Exception as e:
        logger.error("Failed to sign JWT tokens", exc_info=True, extra=log_extra)
        raise ServiceUnavailable("Failed to sign JWT tokens due to an internal error") from e

    logger.info("Successfully signed access and refresh token in handle_authorization_code_grant()")
    token_response = AuthorizationCodeGrantResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        token_type="Bearer",
        expires_in=int((access_token_exp - datetime.now(timezone.utc)).total_seconds()),
    )

    # delete only after successful token creation
    db_session.delete(authz_code_record)
    db_session.commit()
    logger.info(
        "Deleted authz_code record in handle_authorization_code_grant()",
        extra={
            "client_id": client_id,
        },
    )

    return token_response


def _validate_client_credentials(
    db_session: Session,
    client_id: str,
    client_secret: str,
    log_extra: Optional[dict] = None,
) -> OAuthClientCredentials | ConnexionResponse:
    """
    Validates the client credentials by querying the database.
    Returns either valid OAuth client credentials or an error response.
    """
    log_extra = log_extra or {}
    oauth_client = (
        db_session.query(OAuthClientCredentials)
        .filter(OAuthClientCredentials.client_id == client_id)
        .one_or_none()
    )

    if not oauth_client or not oauth_client.client_id:
        logger.error("Invalid client credentials", extra=log_extra)
        return response_util.error_response(
            Unauthorized, "Invalid client credentials", errors=[], data={}
        ).to_api_response()

    # Verify the plaintext client_secret from the request against the stored hash/salted client_secret
    # See https://lwd.atlassian.net/wiki/spaces/DD/pages/4003233807/Live+Chat+-+storing+a+hashed+client_secret
    if not check_password_hash(oauth_client.client_secret, client_secret):
        logger.error("Invalid client secret", extra=log_extra)
        return response_util.error_response(
            Unauthorized, "Invalid client_secret", errors=[], data={}
        ).to_api_response()

    logger.info("Successfully validated client credentials", extra=log_extra)
    return oauth_client


def handle_refresh_token_grant(
    db_session: Session,
    token_request: RefreshTokenGrantRequest,
    client_id: str,
    log_extra: Optional[dict] = None,
) -> RefreshTokenGrantResponse:
    log_extra = log_extra or {}

    # get unverified token payload
    unverified_token_payload = get_unverified_token_payload(token_request.refresh_token)

    # Validate user exists
    user = db_session.query(User).filter(User.auth_id == unverified_token_payload.sub).one_or_none()
    if not user:
        logger.error("Cannot find user specified in refresh token", extra=log_extra)
        raise Unauthorized("User not found")

    log_extra["user_id"] = user.user_id

    # Validate token signature
    verified_token_payload = get_verified_token_payload(
        token_request.refresh_token, client_id, unverified_token_payload.sub
    )

    if verified_token_payload.token_type != OAuthTokenType.REFRESH_TOKEN:
        logger.error("Invalid token type in refresh token grant", extra=log_extra)
        raise Unauthorized("Invalid token type")

    # Validate client ID
    if verified_token_payload.aud != client_id:
        logger.error("Invalid client ID in refresh token audience (aud) claim", extra=log_extra)
        raise Unauthorized("Invalid client ID")

    # Generate access token payload
    access_token_exp = datetime.now(timezone.utc) + timedelta(minutes=get_access_token_expiration())
    access_token_payload = generate_token_payload(
        user_id=verified_token_payload.sub,
        client_id=client_id,
        expiration=access_token_exp,
        token_type=OAuthTokenType.ACCESS_TOKEN.value,
    )

    # Generate new access token
    try:
        jwt_manager: JWTManager = KMSAsymmetricJWTManager(get_kms_key_id())
        access_token = jwt_manager.sign(payload=access_token_payload)
    except Exception as e:
        logger.error("Failed to generate access token", exc_info=True, extra=log_extra)
        raise ServiceUnavailable("Failed to generate access token due to an internal error") from e

    logger.info(
        "Successfully refreshed access token",
        extra=log_extra,
    )

    token_response = RefreshTokenGrantResponse(
        access_token=access_token,
        token_type="Bearer",
        expires_in=int((access_token_exp - datetime.now(timezone.utc)).total_seconds()),
    )

    return token_response


def get_token_issuer() -> str:
    return os.environ.get("API_DOMAIN", "")


def generate_token_payload(
    user_id: str, client_id: str, expiration: datetime, token_type: TokenType
) -> TokenPayload:
    """
    Generate the JWT payload with standard OAuth2 claims

    See RFC 7519 Section 4.1 for standard/registered claim names:
    https://datatracker.ietf.org/doc/html/rfc7519#section-4.1
    """
    now = datetime.now(timezone.utc)

    # iss - Issuer: PFML API
    # sub - Subject: User id
    # aud - Audience: Should be set to client_id (required to be specified when running jwt.decode)
    # exp - Expiration Time (Unix timestamp)
    # nbf - Not Before - can optionally set a token to become valid at a future time (Unix timestamp)
    # iat - Issued At - when the token was created (Unix timestamp)
    # jti - JWT ID: Unique identifier for the token
    # token_type - Distinguish between access_token and refresh_token
    logger.info(f"Generating {token_type} token for user {user_id} and client {client_id}")
    return {
        "iss": get_token_issuer(),
        "sub": str(user_id),
        "aud": client_id,
        "exp": int(expiration.timestamp()),
        "iat": int(now.timestamp()),
        "nbf": int(now.timestamp()),
        "jti": secrets.token_urlsafe(32),
        "token_type": token_type,
    }


def get_unverified_token_payload(token: str) -> OAuthTokenPayload:
    jwt_manager: JWTManager = KMSAsymmetricJWTManager(get_kms_key_id())

    try:
        decoded_payload = jwt_manager.unverified_token_info(token)
        return OAuthTokenPayload(**decoded_payload)
    except JWTVerificationError as e:
        logger.error(
            "Token verification failed for unverified token payload",
            extra={
                "error_type": e.__cause__.__class__.__name__,  # Gets the original JWT error type
                "error_details": str(e.__cause__),  # Gets the original JWT error message
            },
        )
        raise


def get_verified_token_payload(token: str, client_id: str, user_id: str) -> OAuthTokenPayload:
    jwt_manager: JWTManager = KMSAsymmetricJWTManager(get_kms_key_id())

    logger.info(f"Verifying token for user {user_id} and client {client_id}")
    try:
        decoded_payload = jwt_manager.verify(
            token, audience=client_id, issuer=get_token_issuer(), subject=user_id
        )
        return OAuthTokenPayload(**decoded_payload)

    except JWTVerificationError as e:
        logger.error(
            "Token verification failed for verified token payload",
            extra={
                "error_type": e.__cause__.__class__.__name__,  # Gets the original JWT error type
                "error_details": str(e.__cause__),  # Gets the original JWT error message
                "client_id": client_id,
                "user_id": user_id,
            },
        )
        raise


def get_kms_key_id() -> str:
    """
    Retrieve the alias KMS key ID from the environment variable.
    """
    KMS_ASSYMETRIC_KEY_ALIAS = "OAUTH2_KMS_ASYMMETRIC_KEY_ALIAS"

    # TODO (PFMLPB-21177): use env variable KMS alias
    # should be of the form: "alias/<key-name>"
    kms_key_id = os.environ.get(KMS_ASSYMETRIC_KEY_ALIAS)
    if not kms_key_id:
        logger.error(
            f"KMS key not configured for environment variable '{KMS_ASSYMETRIC_KEY_ALIAS}'"
        )
        raise ServiceUnavailable(
            f"KMS key not configured for environment variable '{KMS_ASSYMETRIC_KEY_ALIAS}'"
        )

    return kms_key_id


def get_refresh_token_expiration() -> int:
    return int(os.environ.get("REFRESH_TOKEN_EXPIRATION_MINUTES", 240))


def get_access_token_expiration() -> int:
    return int(os.environ.get("ACCESS_TOKEN_EXPIRATION_MINUTES", 30))


def generate_authz_code() -> str:
    authz_code = secrets.token_urlsafe(32)
    return authz_code
