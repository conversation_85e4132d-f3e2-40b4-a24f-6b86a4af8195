from typing import List, Optional
from uuid import UUID

import flask
import newrelic.agent
from sqlalchemy.orm.exc import MultipleResultsFound
from sqlalchemy.orm.session import Session
from werkzeug.exceptions import BadRequest

import massgov.pfml.api.app as app
import massgov.pfml.api.util.response as response_util
import massgov.pfml.util.logging
from massgov.pfml import db
from massgov.pfml.api.authorization.flask import CREATE, ensure
from massgov.pfml.api.models.notifications.requests import NotificationRequest
from massgov.pfml.api.services.fineos_actions import get_absence_periods_from_claim
from massgov.pfml.api.services.managed_requirements import (
    get_fineos_managed_requirements_from_notification,
)
from massgov.pfml.api.services.service_now_actions import send_notification_to_service_now
from massgov.pfml.api.util.request import parse_request_body
from massgov.pfml.db.models.employees import <PERSON><PERSON><PERSON>, Em<PERSON>loyee, Employer, ManagedRequirement
from massgov.pfml.db.models.notifications import Notification
from massgov.pfml.db.queries.absence_periods import sync_customer_api_absence_periods_to_db
from massgov.pfml.db.queries.managed_requirements import (
    commit_managed_requirements,
    sync_managed_requirements_to_db,
)
from massgov.pfml.fineos.common import FineosRecipientType
from massgov.pfml.fineos.models.group_client_api import ManagedRequirementDetails
from massgov.pfml.util.logging.managed_requirements import log_managed_requirement_issues
from massgov.pfml.util.newrelic.events import record_custom_event

logger = massgov.pfml.util.logging.get_logger(__name__)


def add_notification(db_session: db.Session, notification_request: NotificationRequest) -> None:
    notification = Notification()

    notification.fineos_absence_id = notification_request.absence_case_id

    notification.request_json = notification_request.dict(exclude_none=True)

    notification.trigger = notification_request.trigger

    db_session.add(notification)
    db_session.commit()


def notifications_post(body):
    # Bounce them out if they do not have access
    ensure(CREATE, Notification)

    # Use the pydantic models for validation
    notification_request = parse_request_body(NotificationRequest, body)
    log_attributes = {
        "absence_case_id": notification_request.absence_case_id,
        "notification.absence_case_id": notification_request.absence_case_id,
        "notification.recipient_type": notification_request.recipient_type,
        "notification.source": notification_request.source,
        "notification.trigger": notification_request.trigger,
        "notification_request.claimant_info.customer_id": notification_request.claimant_info.customer_id,
        "notification_request.claimant_info.preferred_language": notification_request.claimant_info.preferred_language,
    }
    for k, v in log_attributes.items():
        newrelic.agent.add_custom_parameter(k, v)

    with app.db_session() as db_session:
        # Persist the notification to the DB
        add_notification(db_session, notification_request)

        # Find or create an associated claim
        claim = (
            db_session.query(Claim)
            .filter(Claim.fineos_absence_id == notification_request.absence_case_id)
            .one_or_none()
        )
        if claim:
            log_attributes = {**log_attributes, "claim_id": str(claim.claim_id)}
            newrelic.agent.add_custom_parameter("claim_id", str(claim.claim_id))

        try:
            employer = (
                db_session.query(Employer)
                .filter(Employer.employer_fein == notification_request.fein.replace("-", ""))
                .one_or_none()
            )

            if employer:
                log_attributes = {**log_attributes, "employer_id": str(employer.employer_id)}
                newrelic.agent.add_custom_parameter("employer_id", employer.employer_id)
        except MultipleResultsFound:
            return _err400_multiple_employer_feins_found(notification_request, log_attributes)

        if employer is None:
            return _err400_employer_fein_not_found(notification_request, log_attributes)

        employee = get_employee_from_fineos_customer_number(
            db_session, log_attributes, notification_request.claimant_info.customer_id
        )

        if employee:
            log_attributes = {**log_attributes, "employee_id": str(employee.employee_id)}
            newrelic.agent.add_custom_parameter("employee_id", employee.employee_id)

            if not bool(employee.date_of_birth) and bool(
                notification_request.claimant_info.date_of_birth
            ):
                employee.date_of_birth = notification_request.claimant_info.date_of_birth
                db_session.add(employee)
                logger.info("Employee %s has a valid date_of_birth.", employee.employee_id)

        if claim is None:
            claim = Claim(
                employer_id=employer.employer_id,
                employee_id=employee.employee_id if employee else None,
                fineos_absence_id=notification_request.absence_case_id,
            )
            db_session.add(claim)
            db_session.commit()

            log_attributes = {**log_attributes, "claim_id": str(claim.claim_id)}
            newrelic.agent.add_custom_parameter("claim_id", str(claim.claim_id))

            logger.info("Created Claim from a Notification", extra=log_attributes)
        else:
            db_changed = False
            if claim.employer_id is None:
                db_changed = True
                claim.employer_id = employer.employer_id
                logger.info("Associated Employer to Claim", extra=log_attributes)
            if claim.employee_id is None and employee is not None:
                db_changed = True
                claim.employee_id = employee.employee_id
                logger.info("Associated Employee to Claim", extra=log_attributes)
            if db_changed:
                db_session.add(claim)
                db_session.commit()

        # Update absence period table
        db_absence_periods = None

        try:
            if not employee or not employee.tax_identifier:
                logger.warning(
                    "Unable to find Employee or Employee has no tax_identifier, can't get absence periods"
                )
            else:
                absence_periods = get_absence_periods_from_claim(claim, db_session)
                db_absence_periods = sync_customer_api_absence_periods_to_db(
                    absence_periods, claim, db_session, log_attributes
                )
        except Exception as error:  # catch all exception handler
            logger.error(
                "Failed to handle update of absence period table.",
                extra=log_attributes,
                exc_info=error,
            )
            newrelic.agent.notice_error(attributes=log_attributes)
            db_session.rollback()  # handle insert errors

        if notification_request.recipient_type == FineosRecipientType.LEAVE_ADMINISTRATOR:
            try:
                fineos_requirements = handle_managed_requirements(
                    notification_request, claim.claim_id, db_session, log_attributes
                )

                updated_db_requirements = (
                    db_session.query(ManagedRequirement)
                    .filter(ManagedRequirement.claim_id == claim.claim_id)
                    .all()
                )

                log_managed_requirement_issues(
                    fineos_requirements, updated_db_requirements, db_absence_periods, log_attributes
                )
            except Exception as error:  # catch all exception handler
                logger.error(
                    "Failed to handle the claim's managed requirements in notification call.",
                    extra=log_attributes,
                    exc_info=error,
                )
                newrelic.agent.notice_error(attributes=log_attributes)
                db_session.rollback()  # handle insert errors

    # Send the request to Service Now
    send_notification_to_service_now(notification_request, employer)

    logger.info("Sent notification", extra=log_attributes)
    return response_util.success_response(
        message="Successfully started notification process.", status_code=201, data={}
    ).to_api_response()


def get_employee_from_fineos_customer_number(
    db_session: db.Session, log_attributes: dict, fineos_customer_number: str
) -> Optional[Employee]:
    """Get employee by fineos_customer_number. Fails without raising an exception since an Employee ID isn't required for sending a notification."""
    try:
        employee = (
            db_session.query(Employee)
            .filter(Employee.fineos_customer_number == fineos_customer_number)
            .one_or_none()
        )
        if employee:
            return employee
        logger.warning(
            "Failed to lookup the specified Employee Fineos Customer Number to associate Claim record",
            extra=log_attributes,
        )
    except MultipleResultsFound:
        # TODO (EMPLOYER-1417): Remove this exception handler once the `fineos_customer_number` field is unique
        logger.exception(
            "Multiple employees found for specified Claimant Fineos Customer Number",
            extra=log_attributes,
        )
    except Exception:
        logger.exception(
            "Unexpected error while searching employees for specified Claimant Fineos Customer Number",
            extra=log_attributes,
        )
    return None


def _err400_employer_fein_not_found(notification_request, log_attributes):
    logger.warning(
        "Failed to lookup the specified FEIN to add Claim record on Notification POST request",
        extra=log_attributes,
    )

    record_custom_event(
        "FineosError",
        {
            **log_attributes,
            "error.class": "FINEOSNotificationInvalidFEIN",
            "error.message": "Failed to lookup the specified FEIN to add Claim record on Notification POST request",
            "absence-id": notification_request.absence_case_id,
            "absence_case_id": (
                notification_request.absence_case_id if flask.has_request_context() else None
            ),
        },
    )

    return response_util.error_response(
        status_code=BadRequest,
        message="Failed to lookup the specified FEIN to add Claim record on Notification POST request",
        errors=[],
        data={},
    ).to_api_response()


def _err400_multiple_employer_feins_found(notification_request, log_attributes):
    logger.exception("Multiple employers found for specified FEIN", extra=log_attributes)

    record_custom_event(
        "FineosError",
        {
            **log_attributes,
            "error.class": "FINEOSNotificationMultipleResults",
            "error.message": "Multiple employers found for specified FEIN",
            "absence-id": notification_request.absence_case_id,
            "absence_case_id": (
                notification_request.absence_case_id if flask.has_request_context() else None
            ),
        },
    )

    return response_util.error_response(
        status_code=BadRequest,
        message="Multiple employers found for specified FEIN",
        errors=[],
        data={},
    ).to_api_response()


def handle_managed_requirements(
    notification: NotificationRequest, claim_id: UUID, db_session: Session, log_attributes: dict
) -> List[ManagedRequirementDetails]:
    fineos_requirements = get_fineos_managed_requirements_from_notification(
        notification, log_attributes
    )

    if len(fineos_requirements) == 0:
        logger.info("No managed requirements returned by Fineos", extra=log_attributes)
        return []

    (_, log_attrs) = sync_managed_requirements_to_db(
        fineos_requirements, claim_id, db_session, log_attributes
    )
    commit_managed_requirements(db_session, log_attrs)
    return fineos_requirements
