import flask
from werkzeug.exceptions import NotFound, ServiceUnavailable

import massgov.pfml.api.app as app
import massgov.pfml.api.util.response as response_util
from massgov.pfml.api.models.insurance_providers.responses import (
    InsuranceProviderDetailsResponse,
    InsuranceProviderResponse,
)
from massgov.pfml.api.services.insurance_providers import (
    get_insurance_provider_from_db,
    get_insurance_providers_from_db,
    get_insurance_providers_plans_from_db,
)


def get_insurance_providers(include_deactivated: bool = False) -> flask.Response:
    # TODO (PFMLPB-19806): removal of feature flag
    enable_employer_exemption_portal = (
        app.get_features_config().employer_exemptions.enable_employer_exemptions_portal
    )
    if not enable_employer_exemption_portal:
        return response_util.error_response(
            ServiceUnavailable, "Exemptions Portal not available in this environment", errors=[]
        ).to_api_response()

    with app.db_session() as db_session:
        # Get raw database models
        insurance_providers = get_insurance_providers_from_db(db_session, include_deactivated)

        # Transform to API response format
        insurance_providers_response = [
            InsuranceProviderResponse.from_orm(provider).dict() for provider in insurance_providers
        ]

    return response_util.success_response(
        message="Successfully retrieved insurance providers",
        data=insurance_providers_response,
        status_code=200,
    ).to_api_response()


def get_insurance_providers_details(
    insurance_provider_id: int, include_deactivated_insurance_plans: bool = False
) -> flask.Response:
    # TODO (PFMLPB-19806): removal of feature flag
    enable_employer_exemption_portal = (
        app.get_features_config().employer_exemptions.enable_employer_exemptions_portal
    )
    if not enable_employer_exemption_portal:
        return response_util.error_response(
            ServiceUnavailable, "Exemptions Portal not available in this environment", errors=[]
        ).to_api_response()

    with app.db_session() as db_session:
        # Get raw database models
        insurance_provider = get_insurance_provider_from_db(db_session, insurance_provider_id)
        insurance_providers_plans = get_insurance_providers_plans_from_db(
            db_session, insurance_provider_id, include_deactivated_insurance_plans
        )
        if not insurance_provider:
            return response_util.error_response(
                NotFound, "Provider not found", errors=[]
            ).to_api_response()

        # Transform to API response format
        insurance_providers_details_response = InsuranceProviderDetailsResponse.custom_from_orm(
            insurance_provider, insurance_providers_plans
        ).dict()

    return response_util.success_response(
        message="Successfully retrieved insurance provider details",
        data=insurance_providers_details_response,
        status_code=200,
    ).to_api_response()
