from typing import Any, Dict

import flask
from connexion.lifecycle import ConnexionResponse
from flask.wrappers import Response
from werkzeug.exceptions import BadRequest, Unauthorized

import massgov.pfml.api.app as app
import massgov.pfml.api.util.response as response_util
import massgov.pfml.util.logging
from massgov.pfml.api.authorization.flask import READ, requires
from massgov.pfml.api.models.oauth_server.requests import (
    AuthorizationCodeGrantRequest,
    RefreshTokenGrantRequest,
)
from massgov.pfml.api.models.oauth_server.responses import (
    AuthorizationCodeGrantResponse,
    OAuthServerMeResponse,
    OAuthServerUser,
    RefreshTokenGrantResponse,
)
from massgov.pfml.api.services.oauth_server import (
    _validate_client_credentials,
    generate_authz_code,
    handle_authorization_code_grant,
    handle_refresh_token_grant,
)
from massgov.pfml.api.util.request import parse_request_body
from massgov.pfml.db.models.oauth_server import OAuthClientCredentials, OAuthServerCode

logger = massgov.pfml.util.logging.get_logger(__name__)

AUTHORIZATION_CODE_GRANT = "authorization_code"
REFRESH_TOKEN_GRANT = "refresh_token"


def token_exchange(body: Dict[str, Any]) -> ConnexionResponse:
    """
    OAuth server token exchange. Exchange either:
       - authorization code for access and refresh token (authorization code grant)
       - refresh token for new access token (refresh token grant)
    """

    # TODO: (PFMLPB-21480) improve input validation on token exchange endpoint
    grant_type = body.get("grant_type")
    logger.info(f"Calling /oauth/token-exchange endpoint with {grant_type}")

    if grant_type == AUTHORIZATION_CODE_GRANT:
        authz_request: AuthorizationCodeGrantRequest = parse_request_body(
            AuthorizationCodeGrantRequest, body
        )
        client_id = authz_request.client_id
        client_secret = authz_request.client_secret
    else:  # grant_type == "refresh_token"
        refresh_request: RefreshTokenGrantRequest = parse_request_body(
            RefreshTokenGrantRequest, body
        )
        client_id = refresh_request.client_id
        client_secret = refresh_request.client_secret

    with app.db_session() as db_session:
        client_creds_validation_result = _validate_client_credentials(
            db_session, client_id, client_secret
        )
        if not isinstance(client_creds_validation_result, OAuthClientCredentials):
            logger.error("Invalid client credentials for /oauth/token-exchange")
            return client_creds_validation_result

        # Handle grant types
        if grant_type == AUTHORIZATION_CODE_GRANT:
            authz_code_token_response: AuthorizationCodeGrantResponse = (
                handle_authorization_code_grant(db_session, authz_request, client_id)
            )

            logger.info("Successfully exchanged authorization code for access and refresh token")

            return flask.jsonify(
                {
                    **authz_code_token_response.dict(),
                    "meta": {
                        "method": "POST",
                        "resource": "/v1/oauth/token-exchange",
                        "message": "Successfully exchanged authorization code for access and refresh token",
                        "grant_type": "authorization_code",
                    },
                }
            )

        else:
            refresh_token_response: RefreshTokenGrantResponse = handle_refresh_token_grant(
                db_session, refresh_request, client_id
            )

            return flask.jsonify(
                {
                    **refresh_token_response.dict(),
                    "meta": {
                        "method": "POST",
                        "resource": "/v1/oauth/token-exchange",
                        "message": "Successfully exchanged refresh token for a new access token",
                        "grant_type": "refresh_token",
                    },
                }
            )


@requires(READ, "EMPLOYER_API")
def get_authorization_code() -> ConnexionResponse:
    current_user = app.current_user()
    if not current_user.user_id:
        logger.error("User not available for Live Chat", extra={"user_id": current_user.user_id})
        return response_util.error_response(
            BadRequest, "User not available for Live Chat", data={}, errors=[]
        ).to_api_response()
    """
    Get the authorization code for the employer
    """
    with app.db_session() as db_session:

        new_employer_authz = OAuthServerCode(
            user_id=current_user.user_id,
            authz_code=generate_authz_code(),
        )
        db_session.add(new_employer_authz)
        db_session.commit()

    logger.info(
        "Successfully retrieved authz code",
        extra={
            "user_id": new_employer_authz.user_id,
            "authz_code": new_employer_authz.authz_code,
            "expires_at": new_employer_authz.expires_at,
        },
    )
    return response_util.success_response(
        message="Successfully retrieved authz code",
        data=new_employer_authz.dict(),
        status_code=200,
    ).to_api_response()


@requires(READ, "EMPLOYER_API")
def me() -> Response:
    current_user = app.current_user()
    employer_eins = [
        leave_admin.employer.employer_fein
        for leave_admin in current_user.user_leave_administrators
        if leave_admin.verified and not leave_admin.deactivated
    ]

    if not employer_eins:
        logger.error(
            "User not associated with any employers", extra={"user_id": current_user.user_id}
        )
        raise Unauthorized("User not associated with any employers")
    joined_eins = ",".join(employer_eins)

    user_response = OAuthServerMeResponse(
        user=OAuthServerUser(
            id=current_user.email_address,
        ),
        firstName=current_user.first_name,
        lastName=current_user.last_name,
        email=current_user.email_address,
        fein=joined_eins,
    )

    return flask.jsonify(user_response.dict())
