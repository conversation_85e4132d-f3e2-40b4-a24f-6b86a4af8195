from typing import Callable, Union

from bouncer.constants import CREATE, EDIT, READ  # noqa: F401 F403
from bouncer.models import RuleList
from flask_bouncer import Bouncer  # noqa: F401

from massgov.pfml.api.authentication.azure import AzureUser
from massgov.pfml.api.authorization.rules import (
    appeals,
    applications,
    azure_user,
    change_requests,
    claims,
    documents,
    employees,
    employer_apis,
    employer_exemptions,
    financial_eligibility,
    idp,
    leave_admins,
    notifications,
    rmv_check,
    users,
)
from massgov.pfml.db.models.employees import User


def create_authorization() -> Callable[[Union[User, AzureUser], RuleList], None]:
    def define_authorization(user: Union[User, AzureUser], they: RuleList) -> None:
        # Admin portal azure authentication/authorization
        if isinstance(user, AzureUser):
            azure_user.administrator(user, they)
        else:
            appeals.rules(user, they)
            applications.rules(user, they)
            change_requests.rules(user, they)
            claims.rules(user, they)
            documents.rules(user, they)
            employer_apis.rules(user, they)
            financial_eligibility.rules(user, they)
            idp.rules(user, they)
            notifications.rules(user, they)
            rmv_check.rules(user, they)
            users.rules(user, they)
            employees.rules(user, they)
            leave_admins.rules(user, they)
            employer_exemptions.rules(user, they)

    return define_authorization
