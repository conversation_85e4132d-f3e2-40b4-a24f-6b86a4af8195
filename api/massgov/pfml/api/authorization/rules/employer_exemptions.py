from bouncer.constants import CREATE, EDIT, READ
from bouncer.models import RuleList

from massgov.pfml.db.models.employees import User
from massgov.pfml.db.models.employer_exemptions import EmployerExemptionApplication


def rules(user: User, they: RuleList) -> None:

    if user.is_employer:
        they.can(
            (CREATE, EDIT, READ),
            EmployerExemptionApplication,
            lambda employer_exemption_application: is_leave_admin_for_employer_exemption_application(
                employer_exemption_application, user
            ),
        )


def is_leave_admin_for_employer_exemption_application(
    employer_exemption_application: EmployerExemptionApplication, user: User
) -> bool:
    for leave_admin in user.user_leave_administrators:
        if leave_admin.employer_id == employer_exemption_application.employer_id:
            return True

    return False
