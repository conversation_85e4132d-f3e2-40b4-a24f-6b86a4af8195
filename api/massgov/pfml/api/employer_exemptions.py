from werkzeug.exceptions import BadRequest, Forbidden, ServiceUnavailable

import massgov.pfml.api.app as app
import massgov.pfml.api.util.response as response_util
import massgov.pfml.util.datetime as datetime_util
import massgov.pfml.util.logging
from massgov.pfml.api.authorization.flask import CREATE, EDIT, READ, ensure, requires
from massgov.pfml.api.models.employer_exemptions.requests import (
    EmployerExemptionApplicationCreateRequestBody,
    EmployerExemptionApplicationRequestBody,
)
from massgov.pfml.api.models.employer_exemptions.responses import (
    EmployerExemptionApplicationResponseBody,
)
from massgov.pfml.api.services.employer_exemptions import (
    create_new_employer_exemption_application,
    employer_exemption_application_is_editable,
    get_all_employer_exemption_applications,
    get_draft_employer_exemption_application,
    get_system_calculated_employer_exemption_application_status,
    update_from_request,
)
from massgov.pfml.api.services.insurance_providers import (
    get_insurance_provider_from_db,
    get_insurance_providers_plan_from_db,
)
from massgov.pfml.api.util.request import parse_request_body
from massgov.pfml.api.validation import employer_exemption_application_rules
from massgov.pfml.api.validation.exceptions import IssueRule, IssueType, ValidationErrorDetail
from massgov.pfml.db.models.employer_exemptions import EmployerExemptionApplication
from massgov.pfml.util.logging.employer_exemptions import (
    get_employer_exemption_application_log_attributes,
)
from massgov.pfml.util.sqlalchemy import get_or_404

logger = massgov.pfml.util.logging.get_logger(__name__)


@requires(READ, "EMPLOYER_API")
def application_get(employer_exemption_application_id):
    with app.db_session() as db_session:
        existing_employer_exemption_application = get_or_404(
            db_session, EmployerExemptionApplication, employer_exemption_application_id
        )

        ensure(READ, existing_employer_exemption_application)

        issues = employer_exemption_application_rules.get_application_submit_issues(
            employer_exemption_application=existing_employer_exemption_application,
            db_session=db_session,
        )

    return response_util.success_response(
        message="Successfully retrieved application",
        data=EmployerExemptionApplicationResponseBody.from_orm(
            existing_employer_exemption_application
        ).dict(),
        warnings=issues,
    ).to_api_response()


@requires(READ, "EMPLOYER_API")
def application_start(body):
    """
    Creates an Employer Exemption Application and returns it

    Args:
        employer_id: A unique identifier UUID for the Employer applying for an Exemption

    Returns:
        EmployerExemptionApplicationResponseBody: A JSON serialization of the newly minted Employer Exemption Application for the give employer.
    """
    # TODO (PFMLPB-19806): removal of feature flag
    enable_employer_exemption_portal = (
        app.get_features_config().employer_exemptions.enable_employer_exemptions_portal
    )
    if not enable_employer_exemption_portal:
        return response_util.error_response(
            ServiceUnavailable, "Exemptions Portal not available in this environment", errors=[]
        ).to_api_response()

    exemption_application_request_body = parse_request_body(
        EmployerExemptionApplicationCreateRequestBody, body
    )
    current_user = app.current_user()
    draft_employer_exemption_application = EmployerExemptionApplication.draft(
        current_user.user_id, exemption_application_request_body.employer_id
    )

    ensure(CREATE, draft_employer_exemption_application)

    with app.db_session() as db_session:
        existing_draft_application: EmployerExemptionApplication | None = (
            get_draft_employer_exemption_application(
                db_session, exemption_application_request_body.employer_id
            )
        )

        if existing_draft_application:
            message = "One draft Employer Exemption Application allowed per Employer"

            return response_util.error_response(
                status_code=BadRequest,
                message=message,
                errors=[
                    ValidationErrorDetail(
                        type=IssueType.duplicate,
                        rule=IssueRule.disallow_multiple_draft_employer_exemption_applications,
                        message=message,
                    )
                ],
                data=EmployerExemptionApplicationResponseBody.from_orm(
                    existing_draft_application
                ).dict(),
            ).to_api_response()

        new_employer_exemption_application = create_new_employer_exemption_application(
            db_session, draft_employer_exemption_application
        )

    log_attributes = get_employer_exemption_application_log_attributes(
        draft_employer_exemption_application
    )

    logger.info("employer_exemptions_applications_start success", extra=log_attributes)

    return response_util.success_response(
        message="Successfully created employer exemption application",
        data=EmployerExemptionApplicationResponseBody.from_orm(
            new_employer_exemption_application
        ).dict(),
        status_code=201,
    ).to_api_response()


@requires(READ, "EMPLOYER_API")
def get_all_employer_exemption_applications_per_exemption_admin():

    # TODO (PFMLPB-19806): removal of feature flag
    enable_employer_exemption_portal = (
        app.get_features_config().employer_exemptions.enable_employer_exemptions_portal
    )
    if not enable_employer_exemption_portal:
        return response_util.error_response(
            ServiceUnavailable,
            "Employer Exemptions Portal not available in this environment",
            errors=[],
        ).to_api_response()

    user = app.current_user()
    all_applications = []
    with app.db_session() as db_session:
        # grabs current user, and gets all the leave admin associated w/ this user
        for employer in user.user_leave_administrators:
            formatted_applications = []
            applications = get_all_employer_exemption_applications(db_session, employer.employer_id)
            # grab all exemption applications (for each of these employer)
            # only 1 in draft mode allowed at a time,
            # but there can be other in the past that can be viewed
            if applications is not None:
                for application in applications:
                    formatted_applications.append(
                        EmployerExemptionApplicationResponseBody.from_orm(application).dict()
                    )

            all_applications.append(
                {
                    "employer_id": employer.employer_id,
                    "employer_exemption_applications": formatted_applications,
                }
            )
    # TODO (PFMLPB-20600): Setup log attributes for Exemptions
    # log_attributes = get_application_log_attributes(application)
    # logger.info("applications_start success", extra=log_attributes)

    return response_util.success_response(
        message="Successfully retrieved all exemption applications",
        data=all_applications,
        status_code=200,
    ).to_api_response()


@requires(EDIT, "EMPLOYER_API")
def application_delete(employer_exemption_application_id):
    with app.db_session() as db_session:
        existing_employer_exemption_application = get_or_404(
            db_session, EmployerExemptionApplication, employer_exemption_application_id
        )

        ensure(EDIT, existing_employer_exemption_application)

        db_session.delete(existing_employer_exemption_application)

    return response_util.success_response(
        message="Successfully deleted exemption application with id "
        + employer_exemption_application_id,
    ).to_api_response()


@requires(READ, "EMPLOYER_API")
def employer_exemption_application_update(employer_exemption_application_id, body):
    with app.db_session() as db_session:
        existing_employer_exemption_application = get_or_404(
            db_session, EmployerExemptionApplication, employer_exemption_application_id
        )

    ensure(EDIT, existing_employer_exemption_application)

    if not employer_exemption_application_is_editable(existing_employer_exemption_application):
        log_attributes = get_employer_exemption_application_log_attributes(
            existing_employer_exemption_application
        )

        logger.info(
            "employer_exemptions_applications_update failure - application already submitted",
            extra=log_attributes,
        )
        message = "Employer Exemption Application {} could not be updated. Application already submitted on {}".format(
            existing_employer_exemption_application.employer_exemption_application_id,
            existing_employer_exemption_application.submitted_at.strftime("%x"),
        )
        return response_util.error_response(
            status_code=Forbidden,
            message=message,
            errors=[
                ValidationErrorDetail(
                    type=IssueType.exists, field="employer_exemption_application", message=message
                )
            ],
            data=EmployerExemptionApplicationResponseBody.from_orm(
                existing_employer_exemption_application
            ).dict(exclude_none=True),
        ).to_api_response()

    exemption_application_request_body = parse_request_body(
        EmployerExemptionApplicationRequestBody, body
    )

    with app.db_session() as db_session:
        # ensure insurance_provider_id and insurance_plan_id are valid values
        # note: this check must occur prior to updating the db
        # failure to check will result in the following error:
        #   'update on table "employer_exemption_application" violates foreign key
        #   constraint "employer_exemption_application_insurance_plan_id_fkey"
        db_insurance_provider = None
        db_insurance_plan = None

        if exemption_application_request_body.purchased_plan:
            db_insurance_provider = get_insurance_provider_from_db(
                db_session,
                exemption_application_request_body.purchased_plan.insurance_provider_id,
            )
            db_insurance_plan = get_insurance_providers_plan_from_db(
                db_session,
                exemption_application_request_body.purchased_plan.insurance_provider_id,
                exemption_application_request_body.purchased_plan.insurance_plan_id,
            )

            # insurance_provider_id was not found in the database. overwrite the request
            # body insurance_provider_id, insurance_plan_id fields to None
            # a valid insurance_plan_id cannot exist if the insurance provider is not
            # found in the db.
            if db_insurance_provider is None:
                exemption_application_request_body.purchased_plan.insurance_provider_id = None
                exemption_application_request_body.purchased_plan.insurance_plan_id = None

            # insurance_plan_id was not found in the database. overwrite the request
            # body insurance_plan_id to None; failure to do so will result in a foreign
            # key violation when calling update_from_request
            if db_insurance_plan is None:
                exemption_application_request_body.purchased_plan.insurance_plan_id = None

        update_from_request(
            db_session,
            exemption_application_request_body,
            existing_employer_exemption_application,
        )

    log_attributes = get_employer_exemption_application_log_attributes(
        existing_employer_exemption_application
    )

    logger.info("employer_exemptions_applications_update success", extra=log_attributes)

    issues = employer_exemption_application_rules.get_application_submit_issues(
        employer_exemption_application=existing_employer_exemption_application,
        db_session=db_session,
    )

    return response_util.success_response(
        message="Employer Exemption Application updated without errors.",
        data=EmployerExemptionApplicationResponseBody.from_orm(
            existing_employer_exemption_application
        ).dict(exclude_none=True),
        warnings=issues,
    ).to_api_response()


@requires(READ, "EMPLOYER_API")
def employer_exemption_application_submit(employer_exemption_application_id):
    current_user = app.current_user()

    with app.db_session() as db_session:
        existing_employer_exemption_application = get_or_404(
            db_session, EmployerExemptionApplication, employer_exemption_application_id
        )

        ensure(EDIT, existing_employer_exemption_application)

        log_attributes = get_employer_exemption_application_log_attributes(
            existing_employer_exemption_application
        )

        if not employer_exemption_application_is_editable(existing_employer_exemption_application):
            logger.info(
                "employer_exemption_application_submit failure - application already submitted",
                extra=log_attributes,
            )
            message = "Employer Exemption Application {} could not be submitted. Application already submitted on {}".format(
                existing_employer_exemption_application.employer_exemption_application_id,
                existing_employer_exemption_application.submitted_at.strftime("%x"),
            )
            return response_util.error_response(
                status_code=Forbidden,
                message=message,
                errors=[
                    ValidationErrorDetail(
                        type=IssueType.exists,
                        field="employer_exemption_application",
                        message=message,
                    )
                ],
                data=EmployerExemptionApplicationResponseBody.from_orm(
                    existing_employer_exemption_application
                ).dict(exclude_none=True),
            ).to_api_response()

        issues = employer_exemption_application_rules.get_application_submit_issues(
            employer_exemption_application=existing_employer_exemption_application,
            db_session=db_session,
        )

        if issues:
            logger.info(
                "employer exemption application failed validation",
                extra=log_attributes,
            )
            return response_util.error_response(
                status_code=BadRequest,
                message="Employer Exemption Application is not valid for submission",
                errors=issues,
                data=EmployerExemptionApplicationResponseBody.from_orm(
                    existing_employer_exemption_application
                ).dict(exclude_none=True),
            ).to_api_response()

        existing_employer_exemption_application.submitted_by_user_id = current_user.user_id
        existing_employer_exemption_application.submitted_at = datetime_util.utcnow()

        existing_employer_exemption_application.employer_exemption_application_status_id = (
            get_system_calculated_employer_exemption_application_status(
                db_session,
                existing_employer_exemption_application.employer_exemption_application_id,
            )
        )
        existing_employer_exemption_application.is_application_status_auto_decided = True

        db_session.add(existing_employer_exemption_application)
        db_session.commit()
        db_session.refresh(existing_employer_exemption_application)
        logger.info("employer_exemption_application_submit success", extra=log_attributes)

    return response_util.success_response(
        message=f"Employer Exemption Application {employer_exemption_application_id} submitted without errors",
        data=EmployerExemptionApplicationResponseBody.from_orm(
            existing_employer_exemption_application
        ).dict(exclude_none=True),
        status_code=201,
    ).to_api_response()
