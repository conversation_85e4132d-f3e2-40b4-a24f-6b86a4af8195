from datetime import datetime
from typing import Dict, Optional
from uuid import UUID

import flask
from connexion.lifecycle import ConnexionResponse
from werkzeug.exceptions import BadRequest, NotFound

import massgov.pfml.api.app as app
import massgov.pfml.api.services.change_requests as change_request_service
import massgov.pfml.api.util.response as response_util
import massgov.pfml.services.documents as documents_service
import massgov.pfml.util.logging
from massgov.pfml.api.authorization.flask import CREATE, EDIT, READ, ensure
from massgov.pfml.api.exceptions import UploadDocumentError
from massgov.pfml.api.models.change_request.common import ChangeRequest
from massgov.pfml.api.models.claims.responses import ChangeRequestResponse
from massgov.pfml.api.models.documents.requests import DocumentRequestBody
from massgov.pfml.api.services.claims import get_claim_from_db
from massgov.pfml.api.services.fineos_actions import (
    submit_change_request as submit_change_request_to_fineos,
)
from massgov.pfml.api.util.request import parse_request_body
from massgov.pfml.api.validation.change_request_rules import get_change_request_issues
from massgov.pfml.db.models.change_request import ChangeRequest as change_request_db_model
from massgov.pfml.db.models.deferred_submission_item import DeferredSubmissionItem
from massgov.pfml.db.models.employees import Claim
from massgov.pfml.util.sqlalchemy import get_or_404

logger = massgov.pfml.util.logging.get_logger(__name__)


def _get_deferred_submission_change_request_log_attributes(
    deferred_submission_item: DeferredSubmissionItem,
) -> Dict[str, Optional[str]]:
    log_attr: Dict[str, Optional[str]] = {
        "deferred_submission_item_id": str(deferred_submission_item.deferred_submission_item_id),
        "deferred_submission_item_created_at": str(deferred_submission_item.created_at),
    }
    if deferred_submission_item.deferred_submission_status_instance is not None:
        log_attr.update(
            {
                "deferred_submission_item_status": deferred_submission_item.deferred_submission_status_instance.description
            }
        )
    return log_attr


def _get_change_request_log_attributes(
    claim: Claim,
    change_request: change_request_db_model,
) -> Dict[str, Optional[str]]:
    log_attr = {
        "absence_case_id": claim.fineos_absence_id,
        "change_request.change_request_id": str(change_request.change_request_id),
        "change_request.start_date": str(change_request.start_date),
        "change_request.end_date": str(change_request.end_date),
    }

    if claim.application is not None and claim.application.leave_reason is not None:
        leave_reason = claim.application.leave_reason.leave_reason_description
        log_attr.update({"claim_leave_reason": leave_reason})

    if change_request.is_medical_to_bonding and change_request.date_of_birth is not None:
        submit_time = (
            change_request.submitted_time
            if change_request.submitted_time is not None
            else datetime.now()
        )
        is_postnatal = change_request.date_of_birth < datetime.date(submit_time)
        log_attr.update({"change_request.is_postnatal": str(is_postnatal)})

    if change_request.change_request_type_instance is not None:
        change_request_type = change_request.type
        log_attr.update({"change_request.change_request_type": change_request_type})

    if change_request.documents_submitted_at is not None:
        documents_submitted_at_time = str(change_request.documents_submitted_at)
        log_attr.update({"change_request.documents_submitted_at": documents_submitted_at_time})

    if change_request.submitted_time is not None:
        change_request_submitted_at_time = str(change_request.submitted_time)
        log_attr.update({"change_request.submitted_time": change_request_submitted_at_time})

    return log_attr


def post_change_request(fineos_absence_id: str, body: dict) -> ConnexionResponse:
    change_request: ChangeRequest = parse_request_body(ChangeRequest, body)

    claim = get_claim_from_db(fineos_absence_id)
    if claim is None:
        logger.warning(
            "post_change_request failed - claim does not exist for given absence ID",
            extra={"absence_case_id": fineos_absence_id},
        )
        error = response_util.error_response(
            status_code=NotFound,
            message="Claim does not exist for given absence ID",
            errors=[],
            data={},
        )
        return error.to_api_response()

    db_change_request = change_request.to_db_model(claim.claim_id)

    ensure(CREATE, db_change_request)

    with app.db_session() as db_session:
        if change_request_service.claim_has_in_progress_requests(claim, db_session):
            error = response_util.error_response(
                status_code=BadRequest,
                message="Multiple in-progress requests are not allowed on a claim",
                errors=[],
                data={},
            )
            logger.error(
                "post_change_request failed - multiple in-progress requests",
                extra={"absence_case_id": fineos_absence_id},
            )
            return error.to_api_response()

        db_session.add(db_change_request)

    issues = get_change_request_issues(db_change_request, claim)

    response_data = ChangeRequestResponse.from_orm(db_change_request)
    logger.info(
        "post_change_request success",
        extra=_get_change_request_log_attributes(claim, db_change_request),
    )
    return response_util.success_response(
        message="Successfully posted change request",
        data=response_data.dict(),
        status_code=201,
        warnings=issues,
    ).to_api_response()


def get_change_requests(fineos_absence_id: str) -> flask.Response:
    claim = get_claim_from_db(fineos_absence_id)

    if claim is None:
        error = response_util.error_response(
            NotFound,
            "Claim does not exist for given absence ID",
            errors=[],
        )
        logger.error(
            "get_change_requests failed - claim does not exist for given absence ID",
            extra={"absence_case_id": fineos_absence_id},
        )
        return error.to_api_response()

    ensure(READ, claim)

    with app.db_session() as db_session:
        change_requests = change_request_service.get_change_requests_from_db(claim, db_session)

    change_requests_dict = []
    for request in change_requests:
        change_request = ChangeRequestResponse(
            change_request_id=request.change_request_id,
            fineos_absence_id=fineos_absence_id,
            change_request_type=request.type,
            start_date=request.start_date,
            end_date=request.end_date,
            submitted_time=request.submitted_time,
            documents_submitted_at=request.documents_submitted_at,
        )
        change_requests_dict.append(change_request.dict())

    logger.info("get_change_requests success", extra={"absence_case_id": fineos_absence_id})
    return response_util.success_response(
        message="Successfully retrieved change requests",
        data={"absence_case_id": fineos_absence_id, "change_requests": change_requests_dict},
        status_code=200,
    ).to_api_response()


def submit_change_request(change_request_id: str) -> flask.Response:
    with app.db_session() as db_session:
        change_request = get_or_404(db_session, change_request_db_model, UUID(change_request_id))

        ensure(EDIT, change_request)

        log_attributes = _get_change_request_log_attributes(change_request.claim, change_request)

        if issues := get_change_request_issues(change_request, change_request.claim):
            error = response_util.error_response(
                status_code=BadRequest,
                message="Invalid change request",
                errors=issues,
                data={},
            )
            validation_error_strings = [issue.message for issue in issues]
            log_attributes["validation_error_list"] = str(validation_error_strings)
            logger.error(
                "submit_change_request failed - issues",
                extra=log_attributes,
            )
            return error.to_api_response()

        if _can_defer_change_request(change_request):
            deferred_submission_item = change_request_service.defer_change_request(
                change_request, db_session
            )
            log_attributes.update(
                _get_deferred_submission_change_request_log_attributes(deferred_submission_item)
            )
            logger.info("submit_change_request deferred", extra=log_attributes)

            return response_util.success_response(
                message="Successfully deferred submission of Change Request to FINEOS",
                data=ChangeRequestResponse.from_orm(change_request).dict(),
                status_code=200,
            ).to_api_response()

        try:
            cr_response = submit_change_request_to_fineos(
                change_request, change_request.claim, db_session
            )
        except Exception as ex:
            logger.error(
                "submit_change_request failed - submit to fineos failed", extra=log_attributes
            )
            raise ex

    response_data = ChangeRequestResponse.from_orm(cr_response)

    logger.info("submit_change_request success", extra=log_attributes)

    return response_util.success_response(
        message="Successfully submitted Change Request to FINEOS",
        data=response_data.dict(),
        status_code=200,
    ).to_api_response()


def _can_defer_change_request(change_request: change_request_db_model) -> bool:
    is_deferred_submission_enabled = (
        app.get_features_config().med_to_bonding.enable_delayed_submission_of_modifications
    )
    return is_deferred_submission_enabled and change_request.is_medical_to_bonding


def delete_change_request(change_request_id: str) -> flask.Response:
    with app.db_session() as db_session:
        change_request = get_or_404(db_session, change_request_db_model, UUID(change_request_id))

        ensure(EDIT, change_request)

        log_attributes = _get_change_request_log_attributes(change_request.claim, change_request)

        if change_request.submitted_time is not None:
            error = response_util.error_response(
                BadRequest,
                "Cannot delete a submitted request",
                data={},
                errors=[],
            )
            logger.error(
                "delete_change_request failed - change request not submitted",
                extra=log_attributes,
            )
            return error.to_api_response()

        db_session.delete(change_request)

    logger.info("delete_change_request success", extra=log_attributes)
    return response_util.success_response(
        message="Successfully deleted change request",
        data={},
        status_code=200,
    ).to_api_response()


def update_change_request(change_request_id: str, body: dict) -> ConnexionResponse:
    update_request: ChangeRequest = parse_request_body(ChangeRequest, body)

    with app.db_session() as db_session:
        change_request = get_or_404(db_session, change_request_db_model, UUID(change_request_id))

        ensure(EDIT, change_request)

        change_request_service.update_change_request_db(db_session, update_request, change_request)

    issues = get_change_request_issues(change_request, change_request.claim)

    response_data = ChangeRequestResponse.from_orm(change_request)

    logger.info(
        "update_change_request success",
        extra=_get_change_request_log_attributes(change_request.claim, change_request),
    )
    return response_util.success_response(
        message="Successfully updated change request",
        data=response_data.dict(),
        status_code=200,
        warnings=issues,
    ).to_api_response()


def upload_document_for_change_request(change_request_id, body, file):
    with app.db_session() as db_session:
        change_request = get_or_404(db_session, change_request_db_model, change_request_id)

    ensure(EDIT, change_request)

    # Parse the document details from the form body
    document_details: DocumentRequestBody = parse_request_body(DocumentRequestBody, body)

    try:
        document_response = documents_service.upload_change_request_document(
            change_request, document_details, file, db_session
        )

    except UploadDocumentError as e:
        return e.to_api_response()

    logger.info(
        "upload_document_for_change_request success",
        extra=_get_change_request_log_attributes(change_request.claim, change_request),
    )

    # Return response
    return response_util.success_response(
        message="Successfully uploaded document", data=document_response.dict(), status_code=200
    ).to_api_response()
