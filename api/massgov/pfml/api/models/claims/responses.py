from datetime import date, datetime, timedelta
from decimal import Decimal
from typing import List, Optional

from pydantic import UUID4

import massgov.pfml.util.logging
from massgov.pfml.api.models.applications.common import MaskedPaymentPreference, PaymentScheduleType
from massgov.pfml.api.models.change_request.common import ChangeRequestType
from massgov.pfml.api.models.claims.common import AbsenceStatus, Address, remap_absence_period_type
from massgov.pfml.api.models.common import (
    ComputedStartDates,
    EmployerBenefit,
    PreviousLeave,
    PreviousPfmlPeriods,
)
from massgov.pfml.api.models.documents.common import DocumentType
from massgov.pfml.api.models.employees.responses import EmployeeBasicResponse
from massgov.pfml.api.models.employers.responses import EmployerResponse
from massgov.pfml.db.models.absences import AbsencePeriod
from massgov.pfml.db.models.change_request import ChangeRequest
from massgov.pfml.db.models.employees import (
    AbsencePaidLeaveCase,
    Claim,
    LeaveRequest,
    ManagedRequirement,
)
from massgov.pfml.fineos.models.customer_api import ActualAbsencePeriodResource
from massgov.pfml.util.pydantic import PydanticBaseModel
from massgov.pfml.util.pydantic.types import (
    FEINFormattedStr,
    MaskedDateStr,
    MaskedTaxIdFormattedStr,
)

logger = massgov.pfml.util.logging.get_logger(__name__)


class ManagedRequirementResponse(PydanticBaseModel):
    follow_up_date: Optional[date]
    responded_at: Optional[date]
    responded_user_first_name: Optional[str]
    responded_user_last_name: Optional[str]
    status: Optional[str]
    category: Optional[str]
    type: Optional[str]
    created_at: Optional[date]
    dfml_decision_date: Optional[date]

    @classmethod
    def from_orm(
        cls,
        managed_requirement: ManagedRequirement,
        dfml_expected_decision_date_delta_days: int = 14,
    ) -> "ManagedRequirementResponse":
        managed_requirement_response = super().from_orm(managed_requirement)
        managed_requirement_response.dfml_decision_date = (
            (
                managed_requirement.responded_at
                + timedelta(days=dfml_expected_decision_date_delta_days)
            ).date()
            if managed_requirement.responded_at
            else None
        )
        if managed_requirement.managed_requirement_status:
            managed_requirement_response.status = (
                managed_requirement.managed_requirement_status.managed_requirement_status_description
            )
        if managed_requirement.managed_requirement_category:
            managed_requirement_response.category = (
                managed_requirement.managed_requirement_category.managed_requirement_category_description
            )
        if managed_requirement.managed_requirement_type:
            managed_requirement_response.type = (
                managed_requirement.managed_requirement_type.managed_requirement_type_description
            )
        if managed_requirement.respondent_user:
            managed_requirement_response.responded_user_first_name = (
                managed_requirement.respondent_user.first_name
            )
            managed_requirement_response.responded_user_last_name = (
                managed_requirement.respondent_user.last_name
            )

        return managed_requirement_response


class AbsencePaidLeaveCaseResponse(PydanticBaseModel):
    absence_paid_leave_case_id: UUID4
    absence_paid_leave_case_number: str
    start_date: date
    end_date: date

    @classmethod
    def from_orm(
        cls,
        absence_paid_leave_case: AbsencePaidLeaveCase,
    ) -> "AbsencePaidLeaveCaseResponse":
        return super().from_orm(absence_paid_leave_case)


class LeaveRequestResponse(PydanticBaseModel):
    leave_request_id: UUID4
    absence_reason_description: Optional[str]
    is_id_proofed: Optional[bool]
    leave_approval_decision_description: Optional[str]
    absence_paid_leave_cases: Optional[list[AbsencePaidLeaveCaseResponse]]

    @classmethod
    def from_orm(cls, leave_request: LeaveRequest) -> "LeaveRequestResponse":
        leave_request_response = super().from_orm(leave_request)

        leave_request_response.absence_reason_description = (
            leave_request.absence_reason.absence_reason_description
        )

        leave_request_response.leave_approval_decision_description = (
            leave_request.leave_approval_decision.leave_request_decision_description
        )

        if leave_request.absence_paid_leave_cases:
            leave_request_response.absence_paid_leave_cases = [
                AbsencePaidLeaveCaseResponse.from_orm(absence_paid_leave_case)
                for absence_paid_leave_case in leave_request.absence_paid_leave_cases
            ]

        return leave_request_response


class EpisodicLeavePeriodResponse(PydanticBaseModel):
    duration: Optional[int]
    duration_basis: Optional[str]
    end_date: Optional[date]
    frequency: Optional[int]
    frequency_interval: Optional[int]
    frequency_interval_basis: Optional[str]
    start_date: Optional[date]
    fineos_absence_period_id: Optional[str]


class AbsencePeriodResponse(PydanticBaseModel):
    """Pydantic Model for absence period returned by the database"""

    fineos_leave_request_id: Optional[int]
    fineos_absence_period_id: Optional[str]
    absence_period_start_date: Optional[date]
    absence_period_end_date: Optional[date]
    modified_start_date: Optional[date]
    modified_end_date: Optional[date]
    reason: Optional[str]
    reason_qualifier_one: Optional[str]
    reason_qualifier_two: Optional[str]
    period_type: Optional[str]
    request_decision: Optional[str]
    document_type_requirements: Optional[List[DocumentType]]
    episodic_leave_period_detail: Optional[EpisodicLeavePeriodResponse]

    @classmethod
    def from_orm(cls, absence_period: AbsencePeriod) -> "AbsencePeriodResponse":
        absence_response = super().from_orm(absence_period)
        if absence_period.absence_period_type:
            absence_response.period_type = remap_absence_period_type(
                absence_period.absence_period_type.absence_period_type_description
            )
        if absence_period.absence_reason:
            absence_response.reason = absence_period.absence_reason.absence_reason_description
        if absence_period.absence_reason_qualifier_one:
            absence_response.reason_qualifier_one = (
                absence_period.absence_reason_qualifier_one.absence_reason_qualifier_one_description
            )
        if absence_period.absence_reason_qualifier_two:
            absence_response.reason_qualifier_two = (
                absence_period.absence_reason_qualifier_two.absence_reason_qualifier_two_description
            )
        if absence_period.leave_request_decision:
            absence_response.request_decision = (
                absence_period.leave_request_decision.leave_request_decision_description
            )
        return absence_response


class AbsencePeriodDetailResponse(AbsencePeriodResponse):
    """Pydantic Model for absence period values for DetailedClaimResponse"""

    approved_start_date: Optional[date]
    approved_end_date: Optional[date]
    is_fully_approved: Optional[bool]

    @classmethod
    def from_absence_period_response(
        cls, absence_period_response: AbsencePeriodResponse
    ) -> "AbsencePeriodDetailResponse":
        return cls(
            fineos_absence_period_id=absence_period_response.fineos_absence_period_id,
            absence_period_start_date=absence_period_response.absence_period_start_date,
            absence_period_end_date=absence_period_response.absence_period_end_date,
            modified_start_date=absence_period_response.modified_start_date,
            modified_end_date=absence_period_response.modified_end_date,
            reason=absence_period_response.reason,
            reason_qualifier_one=absence_period_response.reason_qualifier_one,
            reason_qualifier_two=absence_period_response.reason_qualifier_two,
            period_type=absence_period_response.period_type,
            request_decision=absence_period_response.request_decision,
            document_type_requirements=absence_period_response.document_type_requirements,
            episodic_leave_period_detail=absence_period_response.episodic_leave_period_detail,
        )


class EvidenceDetail(PydanticBaseModel):
    document_name: Optional[str]
    is_document_received: Optional[bool]


class EmployerReview(PydanticBaseModel):
    is_reviewable: bool
    earliest_follow_up_date: Optional[date]
    latest_follow_up_date: Optional[date]


class ClaimResponse(PydanticBaseModel):
    fineos_absence_id: Optional[str]
    employer: Optional[EmployerResponse]
    employee: Optional[EmployeeBasicResponse]
    fineos_notification_id: Optional[str]
    absence_period_start_date: Optional[date]
    absence_period_end_date: Optional[date]
    approval_date: Optional[date]
    claim_start_date: Optional[date]
    claim_end_date: Optional[date]
    claim_status: Optional[AbsenceStatus]
    claim_type_description: Optional[str]
    created_at: Optional[date]
    managed_requirements: Optional[List[ManagedRequirementResponse]]
    absence_periods: Optional[List[AbsencePeriodResponse]] = None
    employer_review: Optional[EmployerReview]
    application_started_at_date: Optional[date]
    application_completed_at_date: Optional[date]
    appeal_filed: Optional[bool] = False

    @classmethod
    def from_orm(cls, claim: Claim) -> "ClaimResponse":
        claim_response = super().from_orm(claim)
        if claim.fineos_absence_status:
            claim_response.claim_status = AbsenceStatus(
                claim.fineos_absence_status.absence_status_description
            )
        claim_response.claim_type_description = claim.claim_type_description
        claim_response.employer_review = EmployerReview(
            is_reviewable=claim.is_reviewable,
            earliest_follow_up_date=claim.earliest_follow_up_date,
            latest_follow_up_date=claim.latest_follow_up_date,
        )
        claim_response.absence_period_start_date = claim.claim_start_date
        claim_response.absence_period_end_date = claim.claim_end_date
        claim_response.approval_date = claim.approval_date
        if claim.application and claim.application.submitted_time:
            claim_response.application_started_at_date = claim.application.submitted_time.date()

        if claim.application and claim.application.completed_time:
            claim_response.application_completed_at_date = claim.application.completed_time.date()
        if claim.appeal:
            claim_response.appeal_filed = True

        return claim_response


class ClaimForPfmlCrmResponse(PydanticBaseModel):
    fineos_absence_id: Optional[str]
    employee: Optional[EmployeeBasicResponse]
    fineos_notification_id: Optional[str]


class DocumentRequirement(PydanticBaseModel):
    document_type: Optional[DocumentType]
    upload_date: Optional[date]


class IntermittentLeaveEpisodeResponse(PydanticBaseModel):
    episode_id: Optional[str]
    episode_leave_request_id: Optional[str]
    date_of_leave: Optional[date]
    duration_in_minutes: Optional[int]
    episode_type: Optional[str]
    status: Optional[str]
    date_reported: Optional[datetime]

    @classmethod
    def from_orm(
        cls, leave_episode_response: ActualAbsencePeriodResource
    ) -> "IntermittentLeaveEpisodeResponse":
        duration_basis = (
            leave_episode_response.episodePeriodBasis.name
            if leave_episode_response.episodePeriodBasis
            else ""
        )
        episode_type = leave_episode_response.type.name if leave_episode_response.type else ""
        status = leave_episode_response.status.name if leave_episode_response.status else ""
        duration_in_minutes = calculate_duration_in_minutes(
            leave_episode_response.episodePeriodDuration, duration_basis
        )

        return cls(
            episode_id=leave_episode_response.id,
            episode_leave_request_id=leave_episode_response.episodicLeaveRequestId,
            date_of_leave=leave_episode_response.actualDate,
            duration_in_minutes=duration_in_minutes,
            episode_type=episode_type,
            status=status,
            date_reported=leave_episode_response.reportedDateTime,
        )


def calculate_duration_in_minutes(
    duration_of_leave: int | None, duration_basis: str | None
) -> int | None:
    if not duration_of_leave:
        return None

    match duration_basis:
        case "Minutes":
            return duration_of_leave
        case "Hours":
            return duration_of_leave * 60
        case "Days":
            return duration_of_leave * 1440
        case _:
            return None


class ClaimPaymentInfo(PydanticBaseModel):
    card_arrival_date: Optional[date]


# This class is intended to show more granular data about a claim that is not shown in the dashboard,
# where ClaimResponse is used. For now the detailed data is absence_periods and outstanding evidence.
class DetailedClaimResponse(PydanticBaseModel):
    application_id: Optional[str]
    fineos_absence_id: Optional[str]
    employer: Optional[EmployerResponse]
    employee: Optional[EmployeeBasicResponse]
    fineos_notification_id: Optional[str]
    claim_status: Optional[AbsenceStatus]
    created_at: Optional[date]
    approval_date: Optional[date]
    absence_periods: Optional[List[AbsencePeriodDetailResponse]]
    managed_requirements: Optional[List[ManagedRequirementResponse]]
    has_paid_payments: Optional[bool]
    payment_schedule_type: Optional[PaymentScheduleType]
    document_requirements: Optional[List[DocumentRequirement]]
    employer_review: Optional[EmployerReview]
    does_claim_span_benefit_years: Optional[bool]
    has_extensions: Optional[bool]
    leave_requests: Optional[List[LeaveRequestResponse]]
    intermittent_leave_episodes: Optional[List[IntermittentLeaveEpisodeResponse]]
    payment_preference: Optional[MaskedPaymentPreference]
    application_started_at_date: Optional[date]
    application_completed_at_date: Optional[date]
    appeal_filed: Optional[bool]
    claim_payment_info: Optional[ClaimPaymentInfo]

    @classmethod
    def from_orm(cls, claim: Claim) -> "DetailedClaimResponse":
        from massgov.pfml.services.claims import (
            get_card_arrival_date,  # This avoids potential for circular imports
        )

        claim_response = super().from_orm(claim)
        if claim.fineos_absence_status:
            claim_response.claim_status = AbsenceStatus(
                claim.fineos_absence_status.absence_status_description
            )
        # Dropping data from DB acquired automatically through the super()_from_orm call.
        # The periods are populated using FINEOS API data.
        claim_response.absence_periods = []
        claim_response.employer_review = EmployerReview(
            is_reviewable=claim.is_reviewable,
            earliest_follow_up_date=claim.earliest_follow_up_date,
            latest_follow_up_date=claim.latest_follow_up_date,
        )
        claim_response.has_paid_payments = claim.has_paid_payments_persisted
        if claim.application and claim.application.submitted_time:
            claim_response.application_started_at_date = claim.application.submitted_time.date()

        if claim.application and claim.application.completed_time:
            claim_response.application_completed_at_date = claim.application.completed_time.date()
        if claim.appeal:
            claim_response.appeal_filed = True
        claim_response.claim_payment_info = ClaimPaymentInfo(
            card_arrival_date=get_card_arrival_date(claim)
        )

        return claim_response


class ClaimWagesBenefits(PydanticBaseModel):
    individual_average_weekly_wage: Optional[Decimal] = None
    weekly_benefit_amount: Optional[Decimal] = None


class ClaimReviewResponse(PydanticBaseModel):
    date_of_birth: Optional[MaskedDateStr]
    employer_benefits: List[EmployerBenefit]
    employer_dba: Optional[str]
    employer_fein: FEINFormattedStr
    employer_id: UUID4
    fineos_absence_id: str
    first_name: Optional[str]
    hours_worked_per_week: Optional[Decimal]
    last_name: Optional[str]
    middle_name: Optional[str]
    previous_leaves: List[PreviousLeave]
    residential_address: Address
    tax_identifier: Optional[MaskedTaxIdFormattedStr]
    absence_periods: List[AbsencePeriodResponse] = []
    managed_requirements: List[ManagedRequirementResponse] = []
    computed_start_dates: Optional[ComputedStartDates]
    approval_date: Optional[date]
    payment_schedule_type: Optional[PaymentScheduleType]
    previous_pfml_leave_periods: Optional[List[PreviousPfmlPeriods]] = []
    wages_and_benefits: Optional[ClaimWagesBenefits] = None


class DocumentResponse(PydanticBaseModel):
    created_at: Optional[date]
    document_type: str
    content_type: Optional[str]
    fineos_document_id: str
    name: Optional[str]
    description: Optional[str]


class ChangeRequestResponse(PydanticBaseModel):
    change_request_id: UUID4
    fineos_absence_id: str
    change_request_type: Optional[ChangeRequestType]
    start_date: Optional[date]
    end_date: Optional[date]
    date_of_birth: Optional[date]
    submitted_time: Optional[datetime]
    documents_submitted_at: Optional[datetime]

    @classmethod
    def from_orm(cls, change_request: ChangeRequest) -> "ChangeRequestResponse":
        if not change_request.claim.fineos_absence_id:
            raise ValueError("Claim is missing fineos_absence_id value")

        change_request_type = (
            change_request.change_request_type_instance.change_request_type_description
            if change_request.change_request_type_instance is not None
            else None
        )

        return cls(
            change_request_id=change_request.change_request_id,
            fineos_absence_id=change_request.claim.fineos_absence_id,
            change_request_type=change_request_type,  # type: ignore
            start_date=change_request.start_date,
            end_date=change_request.end_date,
            date_of_birth=change_request.date_of_birth,
            submitted_time=change_request.submitted_time,
            documents_submitted_at=change_request.documents_submitted_at,
        )
