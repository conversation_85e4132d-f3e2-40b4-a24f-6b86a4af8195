from datetime import date
from decimal import Decimal
from typing import List, Optional

from pydantic import UUID4, Field, validator

import massgov.pfml.util.logging
from massgov.pfml.util.pydantic import PydanticBaseModel
from massgov.pfml.util.pydantic.types import (
    FEINFormattedStr,
    MaskedDateStr,
    MaskedTaxIdFormattedStr,
)

logger = massgov.pfml.util.logging.get_logger(__name__)


class EmployerResponse(PydanticBaseModel):
    employer_dba: Optional[str]
    employer_fein: FEINFormattedStr
    employer_id: UUID4
    has_verification_data: Optional[bool]
    has_verified_leave_admin: Optional[bool]


class LeaveAvailabilityBreakdown(PydanticBaseModel):
    weeks: float = Field(default=0)
    days: float = Field(default=0)
    hours: float = Field(default=0)


class LeaveAvailability(PydanticBaseModel):
    leave_plan_name: Optional[str]
    approved_time: Optional[LeaveAvailabilityBreakdown] = Field(
        default_factory=LeaveAvailabilityBreakdown
    )
    available_balance: Optional[LeaveAvailabilityBreakdown] = Field(
        default_factory=LeaveAvailabilityBreakdown
    )


class LeaveAvailabilityResponse(PydanticBaseModel):
    leave_plan_time: Optional[List[LeaveAvailability]] = Field(default_factory=list)
    overall_time: Optional[LeaveAvailability]
    employer_fein: Optional[FEINFormattedStr]
    tax_identifier: Optional[MaskedTaxIdFormattedStr]
    employee_date_of_birth: Optional[MaskedDateStr]
    employee_first_name: Optional[str]
    employee_last_name: Optional[str]
    benefit_year_start_date: Optional[date]
    benefit_year_end_date: Optional[date]


class AbsencePeriodCSVResponse(PydanticBaseModel):
    """Model for leave admin CSV exports."""

    absence_case_id: Optional[str] = Field("N/A", alias="Application ID")
    employee_first_name: Optional[str] = Field("N/A", alias="First name")
    employee_last_name: Optional[str] = Field("N/A", alias="Last name")
    dob: Optional[str] = Field("N/A", alias="Date of birth")
    ssn_last_four: Optional[str] = Field(None, alias="SSN or ITIN")
    employer_name: Optional[str] = Field("N/A", alias="Organization")
    fein: Optional[str] = Field("N/A", alias="EIN")
    org_unit_name: Optional[str] = Field("N/A", alias="Department")

    absence_reason: Optional[str] = Field("N/A", alias="Leave type")

    leave_begin_date: Optional[str] = Field("N/A", alias="Leave start date")
    leave_end_date: Optional[str] = Field("N/A", alias="Leave end date")
    absence_period_type: Optional[str] = Field("N/A", alias="Leave frequency")
    claimant_p1_submission_date: Optional[str] = Field("N/A", alias="Application started")
    claimant_p3_completion_date: Optional[str] = Field("N/A", alias="Application completed")

    # Employer review due date is +10 business days from the claimant P3 completion date
    employer_review_due_date: Optional[str] = Field("N/A", alias="Employer review due date")
    # LA review complete date is the date the employer review is completed
    employer_review_complete_by: Optional[str] = Field("N/A", alias="Employer review completed by")
    employer_review_complete_date: Optional[str] = Field(
        "N/A", alias="Employer review completion date"
    )
    # Decision is the final decision made by the DFML. It is +14 business days from leave admin completion date
    decision: Optional[str] = Field("N/A", alias="Application status")
    # DFML decision expected date is the date the DFML expects a decision to be made on the absence case
    dfml_expected_decision_date: Optional[str] = Field("N/A", alias="DFML decision expected date")

    weekly_benefit_amount: Optional[Decimal | str] = Field("N/A", alias="Weekly benefit amount")
    iaww: Optional[Decimal | str] = Field("N/A", alias="IAWW")

    @validator("*")
    def check_none(cls, v):  # noqa: B902
        if v is None:
            return "N/A"
        return v

    class Config:
        populate_by_name = True
        allow_population_by_field_name = True
