from datetime import datetime
from typing import Any, Dict, List, Optional, Sequence, Union

from pydantic import UUID4, Field

import massgov.pfml.api.app as app
from massgov.pfml.api.models.phones.common import MaskedPhoneNumber, MaskedPhoneResponse
from massgov.pfml.db.lookup_data.verifications import VerificationType
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.employees import User
from massgov.pfml.db.models.oauth import UserAuthLog
from massgov.pfml.db.queries.leave_admins import get_user_leave_administrators
from massgov.pfml.util.pydantic import PydanticBaseModel
from massgov.pfml.util.pydantic.types import FEINFormattedStr


class RoleResponse(PydanticBaseModel):
    role_id: int
    role_description: str


class ApplicationNamesResponse(PydanticBaseModel):
    first_name: Optional[str]
    middle_name: Optional[str]
    last_name: Optional[str]


class OrganizationUnitResponse(PydanticBaseModel):
    organization_unit_id: UUID4
    name: Optional[str]


class UserEmployerResponse(PydanticBaseModel):
    employer_dba: Optional[str]
    employer_fein: FEINFormattedStr
    employer_id: UUID4
    has_verification_data: bool


class LeaveAdminEmployerResponse(UserEmployerResponse):
    has_verified_leave_admin: bool


class LeaveAdminDataFromUserResponse(PydanticBaseModel):
    first_name: Optional[str]
    last_name: Optional[str]
    phone_number: Optional[MaskedPhoneNumber]
    phone_extension: Optional[str]
    email_address: str


class UserAuthLogResponse(PydanticBaseModel):
    user_id: Optional[UUID4]
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    oauth_operation_id: Optional[int]
    oauth_operation: Optional[str]
    meta_data: Any

    @classmethod
    def from_orm(cls, user_auth_log: UserAuthLog) -> "UserAuthLogResponse":
        return UserAuthLogResponse(
            user_id=user_auth_log.user_id,
            started_at=user_auth_log.started_at,
            completed_at=user_auth_log.completed_at,
            oauth_operation_id=user_auth_log.oauth_operation_id,
            oauth_operation=user_auth_log.oauth_operation.oauth_operation_description,
            meta_data=user_auth_log.meta_data,
        )


class UserLeaveAdminResponse(PydanticBaseModel):
    employer: UserEmployerResponse
    has_fineos_registration: bool
    user_leave_administrator_id: UUID4
    verified: bool

    def normalized_dict(self, email_address: str) -> Dict[str, Any]:
        """Return a flattened response"""
        leave_admin_dict = self.dict()

        return {
            **leave_admin_dict["employer"],
            "has_fineos_registration": leave_admin_dict["has_fineos_registration"],
            "verified": leave_admin_dict["verified"],
            "user_leave_administrator_id": leave_admin_dict["user_leave_administrator_id"],
            # Include the email so the API response for leave admins has a consistent shape
            "email_address": email_address,
        }


class AddedByUserResponse(PydanticBaseModel):
    """Represents the minimal view of a leave admin user who added another leave admin into their organization."""

    email_address: str
    first_name: Optional[str]
    last_name: Optional[str]


class UserLeaveAdminResponseWithUser(PydanticBaseModel):
    employer: LeaveAdminEmployerResponse
    has_fineos_registration: bool
    user: LeaveAdminDataFromUserResponse
    user_leave_administrator_id: UUID4
    verified: bool
    verification_type: Optional[str]
    verified_at: Optional[datetime]
    added_at: Optional[datetime]
    added_by: Optional[AddedByUserResponse]
    organization_units: Optional[list[OrganizationUnitResponse]]

    def normalized_dict(self) -> Dict[str, Any]:
        """Flatten the data model for API responses"""
        leave_admin_dict = self.dict()

        return {
            **leave_admin_dict["employer"],
            **leave_admin_dict["user"],
            "user_leave_administrator_id": leave_admin_dict["user_leave_administrator_id"],
            "verified": leave_admin_dict["verified"],
            "verified_at": leave_admin_dict["verified_at"],
            "verification_type": leave_admin_dict["verification_type"],
            "added_at": leave_admin_dict["added_at"],
            "added_by": leave_admin_dict["added_by"],
            "has_fineos_registration": leave_admin_dict["has_fineos_registration"],
            "organization_units": leave_admin_dict["organization_units"],
        }


class UserLeaveAdminResponseForPending(PydanticBaseModel):
    """
    Model for pending leave admin
    """

    employer: LeaveAdminEmployerResponse = Field(alias="recipient_employer")
    email_address: str = Field(alias="recipient_email")
    user_leave_administrator_action_id: UUID4
    added_at: datetime = Field(alias="created_at")
    added_by: AddedByUserResponse = Field(alias="user")
    verification_type: str = Field(default=VerificationType.ADD.verification_type_description)

    def normalized_dict(self) -> Dict[str, Any]:
        """
        Flatten the data model for API responses, and include default data
        to match the format of non-pending administrators (UserLeaveAdminResponseWithUser).
        """
        default_values: Dict[str, Any] = {
            "first_name": None,
            "last_name": None,
            "phone_extension": None,
            "phone_number": None,
            "has_fineos_registration": False,
            "organization_units": [],
            "verified": False,
            "verified_at": None,
        }

        pending_leave_admin_response_dict = self.dict()
        return {
            **default_values,
            **pending_leave_admin_response_dict["employer"],
            "email_address": pending_leave_admin_response_dict["email_address"],
            "user_leave_administrator_action_id": pending_leave_admin_response_dict[
                "user_leave_administrator_action_id"
            ],
            "verification_type": pending_leave_admin_response_dict["verification_type"],
            "added_at": pending_leave_admin_response_dict["added_at"],
            "added_by": pending_leave_admin_response_dict["added_by"],
        }


UserLeaveAdminSearchResult = Sequence[
    Union[UserLeaveAdminResponseWithUser, UserLeaveAdminResponseForPending]
]


# TODO PFMLPB-23084
class UserResponse(PydanticBaseModel):
    """Response object for a given User result"""

    user_id: UUID4
    auth_id: Optional[str] = Field(alias="auth_id")
    email_address: str
    first_name: Optional[str]
    last_name: Optional[str]
    phone_number: Optional[MaskedPhoneResponse]
    # Optional since it isn't populated at first in from_orm(). After that it
    # should always be a potentially empty list.
    application_names: Optional[List[ApplicationNamesResponse]]
    consented_to_data_sharing: bool
    roles: List[RoleResponse]
    # TODO (PORTAL-154) Stop returning leave admins as part of this endpoint's response
    user_leave_administrators: list
    has_multiple_tax_identifiers: bool
    consented_to_view_tax_documents: Optional[bool]
    language_preference: Optional[str]

    @classmethod
    def from_orm(cls, user: User) -> "UserResponse":
        user_response = super().from_orm(user)
        with app.db_session() as db_session:
            # TODO (PORTAL-154) Stop returning leave admins as part of this endpoint's response
            user_leave_administrators = get_user_leave_administrators(user.user_id, db_session)
            application_names_data = (
                db_session.query(
                    Application.first_name, Application.middle_name, Application.last_name
                )
                .filter(Application.user_id == user.user_id)
                .distinct()
                .limit(5)
                .all()
            )
        user_leave_administrators_data = [
            UserLeaveAdminResponse.from_orm(ula).normalized_dict(user_response.email_address)
            for ula in user_leave_administrators
        ]
        user_response.user_leave_administrators = user_leave_administrators_data
        user_response.application_names = [
            ApplicationNamesResponse(
                first_name=n.first_name, middle_name=n.middle_name, last_name=n.last_name
            )
            for n in application_names_data
        ]
        if user_response.phone_number:
            user_response.phone_number.extension = user.phone_extension

        if user.language:
            user_response.language_preference = user.language.language_description

        return user_response


class AuthURIResponse(PydanticBaseModel):
    auth_uri: str
    claims_challenge: Optional[str]
    code_verifier: str
    nonce: str
    redirect_uri: str
    scope: list
    state: str


class AuthCodeResponse(PydanticBaseModel):
    code: str
    session_state: str
    state: str


class AdminTokenResponse(PydanticBaseModel):
    access_token: str
    refresh_token: str
    id_token: str


class AdminUserResponse(PydanticBaseModel):
    """Response object for a given AzureUser object"""

    sub_id: str
    first_name: Optional[str]
    last_name: Optional[str]
    email_address: str
    groups: List[str]
    permissions: List[str]


class UserDevice(PydanticBaseModel):
    """Response object for a user device"""

    device_key: str
    created_at: datetime
    last_login: datetime
    device_name: str


class CheckAddressValidationOverrideResponse(PydanticBaseModel):
    """Response object for an address override"""

    address_line_one: Optional[str]
    address_line_two: Optional[str]
    city: Optional[str]
    state: Optional[str]
    zip_code: Optional[str]


class UpdatableItemResponse(PydanticBaseModel):
    old_value: Any
    new_value: Any


class UserProfileCheckForUpdatesResponse(PydanticBaseModel):
    profile_updates: dict[str, UpdatableItemResponse]
