from dataclasses import dataclass
from datetime import date
from typing import Optional

from massgov.pfml.util.pydantic import PydanticBaseModel


@dataclass(frozen=True)
class WaitingPeriod(PydanticBaseModel):
    waiting_period_start_date: Optional[date]
    waiting_period_end_date: Optional[date]
    _earliest_approved_start_date: Optional[date]

    @property
    def is_benefit_year_waiting_period(self) -> bool:
        if not self._earliest_approved_start_date:
            # need an approved start date to determine BY waiting period
            return False
        # check if the waiting period is a start of benefit year waiting period.
        # It is a start of leave waiting period if the waiting period start date is
        # the same as the earliest approved leave date, otherwise it is a benefit
        # year start date
        if self._earliest_approved_start_date and self.waiting_period_start_date:
            return self.waiting_period_start_date > self._earliest_approved_start_date
        return False


class WaitingPeriodResponse(PydanticBaseModel):
    waiting_period_start_date: Optional[date]
    waiting_period_end_date: Optional[date]
    is_benefit_year_waiting_period: Optional[bool]

    @classmethod
    def from_orm(cls, waiting_period: WaitingPeriod) -> "WaitingPeriodResponse":
        return cls(
            waiting_period_start_date=waiting_period.waiting_period_start_date,
            waiting_period_end_date=waiting_period.waiting_period_end_date,
            is_benefit_year_waiting_period=waiting_period.is_benefit_year_waiting_period,
        )
