from datetime import date
from typing import Optional
from uuid import UUID

from massgov.pfml.api.models.employer_exemptions.common import (
    EmployerExemptionApplicationStatus,
    ExemptionsApplicationDocuments,
    SelfInsuredPlanDetails,
)
from massgov.pfml.api.models.phones.common import Phone
from massgov.pfml.db.models.employer_exemptions import EmployerExemptionApplication
from massgov.pfml.util.pydantic import PydanticBaseModel


class PurchasedPlanDetailsRequestBody(PydanticBaseModel):

    class Config:
        arbitrary_types_allowed = True
        from_attributes = True
        orm_mode = True

    insurance_provider_id: int | None = None
    insurance_plan_id: int | None = None

    @classmethod
    def from_orm(
        cls, employer_exemption_application: EmployerExemptionApplication
    ) -> "PurchasedPlanDetailsRequestBody":
        if employer_exemption_application is None:
            raise ValueError("Employer Exemption Application cannot be None")

        return cls(
            insurance_provider_id=employer_exemption_application.insurance_provider_id,
            insurance_plan_id=employer_exemption_application.insurance_plan_id,
        )


class EmployerExemptionApplicationCreateRequestBody(PydanticBaseModel):
    employer_id: UUID


class EmployerExemptionApplicationRequestBody(PydanticBaseModel):
    is_legally_acknowledged: Optional[bool] = None
    # Contact details
    contact_first_name: Optional[str] = None
    contact_last_name: Optional[str] = None
    contact_title: Optional[str] = None
    contact_phone: Optional[Phone] = None
    contact_email_address: Optional[str] = None
    has_third_party_administrator: Optional[bool] = None
    tpa_business_name: Optional[str] = None
    tpa_contact_first_name: Optional[str] = None
    tpa_contact_last_name: Optional[str] = None
    tpa_contact_title: Optional[str] = None
    tpa_contact_phone: Optional[Phone] = None
    tpa_contact_email_address: Optional[str] = None
    # Organization Details
    # https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/Exemption+request+questions
    should_workforce_count_include_1099_misc: Optional[bool] = None
    average_workforce_count: Optional[int] = None
    # Insurance Details
    has_family_exemption: Optional[bool] = None
    has_medical_exemption: Optional[bool] = None
    # the 'is_self_insured_plan' determines if purchased plan block is validated or if self_insured block is validated
    is_self_insured_plan: Optional[bool] = None
    insurance_plan_effective_at: Optional[date] = None
    insurance_plan_expires_at: Optional[date] = None
    purchased_plan: Optional[PurchasedPlanDetailsRequestBody] = None
    self_insured: Optional[SelfInsuredPlanDetails] = None
    documents: Optional[ExemptionsApplicationDocuments] = None
    employer_exemption_application_status_id: Optional[int] = 1
    employer_exemption_application_status: Optional[str] = EmployerExemptionApplicationStatus.DRAFT

    @classmethod
    def is_phone_object(cls, field_name):
        return field_name in ["contact_phone", "tpa_contact_phone"]

    @classmethod
    def get_mapped_to_db_column_name(cls, field_name):
        API_NAME_TO_DB_NAME_MAP = {
            "contact_phone": "contact_phone_id",
            "tpa_contact_phone": "tpa_contact_phone_id",
        }

        return API_NAME_TO_DB_NAME_MAP.get(field_name, field_name)
