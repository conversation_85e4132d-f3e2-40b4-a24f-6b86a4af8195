from decimal import Decimal

from massgov.pfml.api.models.common import LookupEnum
from massgov.pfml.db.models.employer_exemptions import EmployerExemptionApplication
from massgov.pfml.util.pydantic import PydanticBaseModel


class EmployerExemptionApplicationStatus(str, LookupEnum):
    DRAFT = "Draft"
    APPROVED = "Approved"
    DENIED = "Denied"
    IN_REVIEW = "In Review"


class SelfInsuredPlanQuestions(PydanticBaseModel):

    class Config:
        arbitrary_types_allowed = True
        from_attributes = True
        orm_mode = True

    does_plan_cover_all_employees: bool | None = None
    does_plan_provide_enough_leave: bool | None = None
    does_plan_provide_enough_medical_leave: bool | None = None
    does_plan_provide_enough_caring_leave: bool | None = None
    does_plan_provide_enough_bonding_leave: bool | None = None
    does_plan_provide_enough_armed_forces_leave: bool | None = None
    does_plan_provide_enough_armed_forces_illness_leave: bool | None = None
    does_plan_pay_enough_benefits: bool | None = None
    does_employer_withhold_premiums: bool | None = None
    are_employer_withholdings_within_allowable_amount: bool | None = None
    does_plan_provide_pfml_job_protection: bool | None = None
    does_plan_provide_return_to_work_benefits: bool | None = None
    does_plan_cover_employee_contribution: bool | None = None
    does_plan_provide_intermittent_caring_leave: bool | None = None
    does_plan_provide_intermittent_bonding_leave: bool | None = None
    does_plan_provide_intermittent_armed_forces_leave: bool | None = None
    does_plan_provide_intermittent_medical_leave: bool | None = None
    does_plan_cover_former_employees: bool | None = None
    does_plan_favor_paid_leave_benefits: bool | None = None


class SelfInsuredPlanDetails(PydanticBaseModel):

    class Config:
        arbitrary_types_allowed = True
        from_attributes = True
        orm_mode = True

    has_obtained_surety_bond: bool | None = None
    surety_company: str | None = None
    surety_bond_amount: Decimal | None = None
    questions: SelfInsuredPlanQuestions | None = None

    @classmethod
    def from_orm(
        cls, employer_exemption_application: EmployerExemptionApplication
    ) -> "SelfInsuredPlanDetails":
        if employer_exemption_application is None:
            raise ValueError("Employer Exemption Application cannot be None")

        self_insured_plan_details_response = super().from_orm(employer_exemption_application)
        self_insured_plan_details_response.questions = SelfInsuredPlanQuestions.from_orm(
            employer_exemption_application
        )

        return self_insured_plan_details_response


# TODO - PFMLPB-20608: Exemptions BE: GET /employer-exemption-applications/{employer_exemption_application_id}/documents
class ExemptionsApplicationDocuments(PydanticBaseModel):
    pass
