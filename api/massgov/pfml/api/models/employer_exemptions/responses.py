from datetime import date, datetime
from typing import Optional
from uuid import UUID

from massgov.pfml.api.models.employer_exemptions.common import (
    EmployerExemptionApplicationStatus,
    ExemptionsApplicationDocuments,
    SelfInsuredPlanDetails,
)
from massgov.pfml.api.models.phones.common import PhoneResponse
from massgov.pfml.db.models.employer_exemptions import EmployerExemptionApplication
from massgov.pfml.util.pydantic import PydanticBaseModel


class PurchasedPlanDetailsResponseBody(PydanticBaseModel):

    class Config:
        arbitrary_types_allowed = True
        from_attributes = True
        orm_mode = True

    insurance_provider_id: int | None = None
    insurance_provider_name: str | None
    insurance_plan_id: int | None = None
    insurance_plan_name: str | None

    @classmethod
    def from_orm(
        cls, employer_exemption_application: EmployerExemptionApplication
    ) -> "PurchasedPlanDetailsResponseBody":
        if employer_exemption_application is None:
            raise ValueError("Employer Exemption Application cannot be None")

        insurance_provider_name = None
        insurance_plan_name = None

        if employer_exemption_application.insurance_provider:
            insurance_provider_name = (
                employer_exemption_application.insurance_provider.insurance_provider_name
            )
            insurance_plan_name = employer_exemption_application.insurance_plan.form_name

        return cls(
            insurance_provider_id=employer_exemption_application.insurance_provider_id,
            insurance_provider_name=insurance_provider_name,
            insurance_plan_id=employer_exemption_application.insurance_plan_id,
            insurance_plan_name=insurance_plan_name,
        )


class EmployerExemptionApplicationResponseBody(PydanticBaseModel):

    class Config:
        arbitrary_types_allowed = True
        from_attributes = True
        orm_mode = True

    created_by_user_id: UUID
    employer_id: UUID
    employer_exemption_application_id: UUID
    employer_exemption_application_status: EmployerExemptionApplicationStatus
    employer_exemption_application_status_id: int
    is_legally_acknowledged: Optional[bool] = None
    # Contact details
    contact_first_name: Optional[str] = None
    contact_last_name: Optional[str] = None
    contact_title: Optional[str] = None
    contact_phone: Optional[PhoneResponse] = None
    contact_email_address: Optional[str] = None
    has_third_party_administrator: Optional[bool] = None
    tpa_business_name: Optional[str] = None
    tpa_contact_first_name: Optional[str] = None
    tpa_contact_last_name: Optional[str] = None
    tpa_contact_title: Optional[str] = None
    tpa_contact_phone: Optional[PhoneResponse] = None
    tpa_contact_email_address: Optional[str] = None
    # Organization Details
    # https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/Exemption+request+questions
    should_workforce_count_include_1099_misc: Optional[bool] = None
    average_workforce_count: Optional[int] = None
    # Insurance Details
    has_family_exemption: Optional[bool] = None
    has_medical_exemption: Optional[bool] = None
    # the 'is_self_insured_plan' determines if purchased plan block is validated or if self_insured block is validated
    is_self_insured_plan: Optional[bool] = None
    insurance_plan_effective_at: Optional[date] = None
    insurance_plan_expires_at: Optional[date] = None
    purchased_plan: Optional[PurchasedPlanDetailsResponseBody] = None
    self_insured: Optional[SelfInsuredPlanDetails] = None
    documents: Optional[ExemptionsApplicationDocuments] = None
    is_application_status_auto_decided: Optional[bool] = None
    submitted_at: datetime | None = None
    submitted_by_user_id: Optional[UUID] = None
    updated_at: datetime | None = None
    created_at: datetime

    @classmethod
    def from_orm(
        cls, employer_exemption_application: EmployerExemptionApplication
    ) -> "EmployerExemptionApplicationResponseBody":
        if employer_exemption_application is None:
            raise ValueError("Employer Exemption Application cannot be None")

        employer_exemption_application_response = super().from_orm(employer_exemption_application)

        employer_exemption_application_response.purchased_plan = (
            PurchasedPlanDetailsResponseBody.from_orm(employer_exemption_application)
        )
        employer_exemption_application_response.self_insured = SelfInsuredPlanDetails.from_orm(
            employer_exemption_application
        )
        if employer_exemption_application.contact_phone is not None:
            employer_exemption_application_response.contact_phone = PhoneResponse.from_orm(
                employer_exemption_application.contact_phone
            )

        return employer_exemption_application_response
