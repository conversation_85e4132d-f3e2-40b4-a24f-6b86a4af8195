from typing import Optional, Union

import phonenumbers
from pydantic import validator

import massgov.pfml.db.models.phone as db_phone_models
import massgov.pfml.util.pydantic.mask as mask
from massgov.pfml.api.models.common import LookupEnum
from massgov.pfml.api.validation.exceptions import (
    IssueType,
    ValidationErrorDetail,
    ValidationException,
)
from massgov.pfml.util.pydantic import PydanticBaseModel

PHONE_MISMATCH_MESSAGE = "E.164 phone number does not match provided phone number"
PHONE_MISMATCH_ERROR = ValidationErrorDetail(
    message=PHONE_MISMATCH_MESSAGE, type=IssueType.invalid_phone_number, field="e164"
)

# Phone I/O Types
"""
These models were originally in ../applications/common.py
To follow the git history prior to its split, see this commit:
https://github.com/EOLWD/pfml/commit/6a901778837e438e14fa7025b186752825c020d4
"""


class InvalidPhoneError(ValueError):
    def __init__(self, message: str):
        self.message = message
        self.type = IssueType.invalid_phone_number
        self.rule = "phone_number_must_be_valid_number"


class PhoneType(str, LookupEnum):
    Cell = "Cell"
    Fax = "Fax"
    Phone = "Phone"


class Phone(PydanticBaseModel):
    # Phone dict coming from front end contains int_code and phone_number separately
    # Values are Optional, deviating from OpenAPI spec to allow for None values in Response
    int_code: Optional[str]
    phone_number: Optional[str]
    phone_type: Optional[PhoneType]
    e164: Optional[str]
    extension: Optional[str]

    @validator("phone_number")
    def check_phone_number(cls, phone_number, values):  # noqa: B902
        # Import here to avoid circular import
        from massgov.pfml.api.util.phone import parse_number

        n = None

        int_code = values.get("int_code")
        if phone_number is None:
            # if phone_number is removed by masking rules, skip validation
            return None

        n = parse_number(phone_number, int_code)

        if n is None or not phonenumbers.is_valid_number(n):
            raise InvalidPhoneError("Phone number must be a valid number")

        return phone_number

    @validator("e164", always=True)
    def populate_e164_if_phone_number(cls, e164_phone_number, values):  # noqa: B902
        # Import here to avoid circular import
        from massgov.pfml.api.util.phone import convert_to_E164

        is_phone_number_provided = "phone_number" in values and values["phone_number"]

        if not e164_phone_number and is_phone_number_provided:
            return convert_to_E164(values.get("phone_number"), values.get("int_code"))

        if e164_phone_number is not None and is_phone_number_provided:
            checked_number = convert_to_E164(values.get("phone_number"), values.get("int_code"))
            if e164_phone_number != checked_number:
                raise ValidationException(
                    errors=[PHONE_MISMATCH_ERROR], message=PHONE_MISMATCH_MESSAGE
                )

        return e164_phone_number

    @validator("e164")
    def check_e164(cls, e164_phone_number):  # noqa: B902
        # because the other validator for "e164" is always=True, this validator
        # seems to be called regardless of if an "e164" value is available
        # (either from the request directly or populate_e164_if_phone_number),
        # so check before doing any validation
        #
        # if/when populate_e164_if_phone_number is removed, shouldn't need to do
        # this check
        if not e164_phone_number:
            return None

        validation_exception = ValidationException(
            errors=[
                ValidationErrorDetail(
                    message="Phone number must be a valid number",
                    type=IssueType.invalid_phone_number,
                    rule="phone_number_must_be_valid_number",
                    field="phone.e164",
                )
            ],
            message="Validation error",
        )

        try:
            n = phonenumbers.parse(e164_phone_number)
        except phonenumbers.NumberParseException:
            raise validation_exception

        if not phonenumbers.is_valid_number(n):
            raise validation_exception

        return e164_phone_number


class MaskedPhoneNumber(str):
    @classmethod
    def __get_validators__(cls):
        yield cls.validate_type

    @classmethod
    def validate_type(cls, val):
        if val is None:
            return None

        return mask.mask_phone(val)


class MaskedPhone(Phone):
    @classmethod
    def from_orm(cls, phone: Union[db_phone_models.Phone, str]) -> "MaskedPhone":
        phone_response = PhoneResponse.from_orm(phone)

        return MaskedPhone(
            int_code=phone_response.int_code,
            phone_number=mask.mask_phone(phone_response.phone_number),
            phone_type=phone_response.phone_type,
        )


class MaskedPhoneResponse(PydanticBaseModel):
    int_code: Optional[str]
    phone_number: Optional[str]
    phone_type: Optional[PhoneType]
    extension: Optional[str]

    @classmethod
    def from_orm(cls, phone: Union[db_phone_models.Phone, str]) -> "MaskedPhoneResponse":
        phone_response = PhoneResponse.from_orm(phone)
        phone_response.phone_number = mask.mask_phone(phone_response.phone_number)

        return MaskedPhoneResponse(
            int_code=phone_response.int_code,
            phone_number=phone_response.phone_number,
            phone_type=phone_response.phone_type,
            extension=phone_response.extension,
        )


# Create this response class to capture phone data without the validators above
class PhoneResponse(PydanticBaseModel):
    int_code: Optional[str]
    phone_number: Optional[str]
    phone_type: Optional[PhoneType]
    e164: Optional[str]
    extension: Optional[str]

    @classmethod
    def from_orm(cls, phone: Union[db_phone_models.Phone, str]) -> "PhoneResponse":
        if isinstance(phone, db_phone_models.Phone):
            phone_response = super().from_orm(phone)
            if phone.phone_type_instance:
                phone_response.phone_type = PhoneType[
                    phone.phone_type_instance.phone_type_description
                ]
        else:
            phone_response = cls(phone_number=phone)

        if phone_response.phone_number:
            parsed_phone_number = phonenumbers.parse(phone_response.phone_number)
            number = str(parsed_phone_number.national_number)

            phone_response.e164 = phonenumbers.format_number(
                parsed_phone_number, phonenumbers.PhoneNumberFormat.E164
            )
            phone_response.phone_number = f"{number[0:3]}-{number[3:6]}-{number[-4:]}"
            phone_response.int_code = str(parsed_phone_number.country_code)

        return phone_response
