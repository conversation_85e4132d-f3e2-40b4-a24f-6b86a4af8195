from massgov.pfml.api.models.common import LookupEnum

"""
These models were originally in api/models/applications/common.py
To follow the git history prior to this split, see this PR:
https://github.com/EOLWD/pfml/pull/8226
"""


class DocumentType(str, LookupEnum):
    passport = "Passport"
    drivers_license_mass = "Driver's License Mass"
    drivers_license_other_state = "Driver's License Other State"
    identification_proof = "Identification Proof"
    state_managed_paid_leave_confirmation = "State managed Paid Leave Confirmation"
    approval_notice = "Approval Notice"
    request_for_more_information = "Request for More Information"
    denial_notice = "Denial Notice"
    certification_form = (
        "Certification Form"  # only used when Portal uploads a certification document
    )
    own_serious_health_condition_form = "Own serious health condition form"
    pregnancy_maternity_form = "Pregnancy/Maternity form"
    child_bonding_evidence_form = "Child bonding evidence form"
    care_for_a_family_member_form = "Care for a family member form"
    military_exigency_form = "Military exigency form"
    withdrawal_notice = "Pending Application Withdrawn"
    appeal_acknowledgment = "Appeal Acknowledgment"
    appeal_form = "Appeal Form"
    maximum_weekly_benefit_change_notice = "Maximum Weekly Benefit Change Notice"
    benefit_amount_change_notice = "Benefit Amount Change Notice"
    leave_allotment_change_notice = "Leave Allotment Change Notice"
    approved_time_cancelled = "Approved Time Cancelled"
    change_request_approved = "Change Request Approved"
    change_request_denied = "Change Request Denied"
    appeals_supporting_documentation = "Appeals Supporting Documentation"
    covered_service_member_identification_proof = "Covered Service Member Identification Proof"
    family_member_active_duty_service_proof = "Family Member Active Duty Service Proof"
    overpayment_notice_partial_balance_recovery = "OP- Partial Recovery and Remaining Bal"
    overpayment_payoff_notice = "Overpayment Payoff Notice"
    overpayment_notice_full_balance_recovery = "OP- Full Balance Recovery"
    overpayment_notice_full_balance_recovery_manual = "OP- Full Balance Recovery - Manual"
    overpayment_notice_full_balance_demand = "Overpayment Notice-Full Balance Demand"
    appeal_hearing_virtual_fillable = "Appeal Hearing Virtual Fillable"
    appeal_rfi = "Appeal RFI"
    appeal_returned_to_adjudication = "Appeal - Returned to Adjudication"
    appeal_approved = "Appeal Approved"
    appeal_dismissed_other = "Appeal Dismissed - Other"
    appeal_dismissed_exempt_employer = "Appeal Dismissed - Exempt Employer"
    modify_decision = "Modify Decision"
    appeal_withdrawn = "Appeal Withdrawn"
    intermittent_time_approved_notice = "Intermittent Time Approved Notice"
    payment_received_updated_overpayment_balance = "Payment Received-Updated Overpayment Balance"
    denial_notice_explanation_of_wages = "Denial Notice Explanation of Wages"
    explanation_of_wages = "Explanation of Wages"
    post_adjudication_report = "Post-Adjudication Report"
    approval_of_application_change = "Approval of Application Change"
    denial_of_application_change = "Denial of Application Change"
    approved_leave_dates_cancelled = "Approved Leave Dates Cancelled"
    denial_of_application = "Denial of Application"
    intermittent_time_reported = "Intermittent Time Reported"
    approval_notice_explanation_of_wages = "Approval Notice Explanation of Wages"
    overpayment_full_demand_er_benefits = "Overpayment Full Demand ER Benefits"
    overpayment_full_demand_intermittent = "Overpayment Full Demand Intermittent"
    overpayment_full_demand_leave_change = "Overpayment Full Demand Leave Change"
    overpayment_full_demand_paid_time_off = "Overpayment Full Demand Paid Time Off"
    overpayment_full_demand_ui = "Overpayment Full Demand UI"
    overpayment_full_demand_workers_comp = "Overpayment Full Demand Workers Comp"
    overpayment_full_recovery_er_benefits = "Overpayment Full Recovery ER Benefits"
    overpayment_full_recovery_intermittent = "Overpayment Full Recovery Intermittent"
    overpayment_full_recovery_leave_change = "Overpayment Full Recovery Leave Change"
    overpayment_full_recovery_paid_time_off = "Overpayment Full Recovery Paid Time Off"
    overpayment_full_recovery_ui = "Overpayment Full Recovery UI"
    overpayment_full_recovery_workers_comp = "Overpayment Full Recovery Workers Comp"
    overpayment_partial_demand_er_benefits = "Overpayment Partial Demand ER Benefits"
    overpayment_partial_demand_intermittent = "Overpayment Partial Demand Intermittent"
    overpayment_partial_leave_change = "Overpayment Partial Leave Change"
    overpayment_partial_paid_time_off = "Overpayment Partial Paid Time Off"
    overpayment_partial_demand_ui = "Overpayment Partial Demand UI"
    overpayment_partial_demand_workers_comp = "Overpayment Partial Demand Workers Comp"
    overpayment_payment_received_new_balance = "Overpayment Payment Received New Balance"
    overpayment_payoff = "Overpayment Payoff"
    dismisssal_for_failure_to_attend_hearing = "Dismissal for Failure to Attend Hearing"
    notice_of_default = "Notice of Default"
    w9_tax_form = "W9 Tax Form"
    eft_change_request = "EFT Change Request"
    notice_of_child_support_withholding = "Notice of Child Support Withholding"
    appeal_postponement_agency = "Appeal Postponement Agency"
    appeal_postponement_approved = "Appeal Postponement Approved"
    appeal_postponement_denied = "Appeal Postponement Denied"
    appeal_reinstatement_denied = "Appeal Reinstatement Denied"
    appeal_reinstatement_granted = "Appeal Reinstatement Granted"
    irs_1099g_tax_form_for_claimants = "1099G Tax Form for Claimants"
    user_not_found_info = "Employee Not Found Information"
    healthcare_provider_form = "Healthcare Provider Form"
    confirmation_of_insurance_form = "Confirmation of Insurance Form"
    self_insurance_declaration_document = "Self-Insurance Declaration Document"
    self_insurance_surety_bond = "Self-Insurance Surety Bond"
    self_insurance_proof_of_benefits = "Self-Insurance Proof of Benefits"
    fmla = "Family and Medical Leave Act Form"


class ContentType(str, LookupEnum):
    pdf = "application/pdf"
    jpeg = "image/jpeg"
    png = "image/png"
    webp = "image/tiff"
    heic = "image/heic"
