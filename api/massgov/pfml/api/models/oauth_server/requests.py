from typing import Literal

from massgov.pfml.util.pydantic import PydanticBaseModel


class OAuthTokenGrantRequest(PydanticBaseModel):
    client_id: str
    client_secret: str


class AuthorizationCodeGrantRequest(OAuthTokenGrantRequest):
    grant_type: Literal["authorization_code"]
    code: str


class RefreshTokenGrantRequest(OAuthTokenGrantRequest):
    grant_type: Literal["refresh_token"]
    refresh_token: str
