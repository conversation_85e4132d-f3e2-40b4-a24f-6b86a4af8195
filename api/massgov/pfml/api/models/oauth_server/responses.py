from typing import Literal

from pydantic import UUID4

from massgov.pfml.util.pydantic import PydanticBaseModel


class OAuthTokenGrantResponse(PydanticBaseModel):
    access_token: str
    token_type: Literal["Bearer"] = "Bearer"
    expires_in: int  # number of seconds until the access token expires


class AuthorizationCodeGrantResponse(OAuthTokenGrantResponse):
    refresh_token: str


class RefreshTokenGrantResponse(OAuthTokenGrantResponse):
    pass


OAuthTokenResponse = AuthorizationCodeGrantResponse | RefreshTokenGrantResponse


class OAuthServerCodeResponse(PydanticBaseModel):
    user_id: UUID4
    authz_code: str
    expires_at: str


class OAuthServerConsumerCustomFields(PydanticBaseModel):
    consumerCustomFieldIdent: str
    other: str


class OAuthServerConsumerContactCustomFields(PydanticBaseModel):
    consumerContactCustomFieldIdent: str
    other: str


class OAuthServerUser(PydanticBaseModel):
    id: str


class OAuthServerMeResponse(PydanticBaseModel):
    user: OAuthServerUser
    firstName: str
    lastName: str
    email: str
    fein: str
