from datetime import date
from decimal import Decimal
from enum import Enum
from typing import List, Optional

from pydantic import UUID4, validator

import massgov.pfml.db.models.applications as db_application_models
import massgov.pfml.db.models.employees as db_employee_models
import massgov.pfml.util.pydantic.mask as mask
from massgov.pfml.api.models.common import (
    AmountFrequency,
    LookupEnum,
    PreviousLeaveQualifyingReason,
)
from massgov.pfml.api.models.notifications.common import NotificationMethod
from massgov.pfml.api.validation.exceptions import (
    IssueType,
    ValidationErrorDetail,
    ValidationException,
)
from massgov.pfml.util.pydantic import PydanticBaseModel
from massgov.pfml.util.pydantic.types import (
    FinancialRoutingNumber,
    MaskedDateStr,
    MaskedFinancialAcctNum,
    MaskedFinancialRoutingNumber,
)

# Applications I/O types


class Occupation(str, LookupEnum):
    sales_clerk = "Sales Clerk"
    administrative = "Administrative"
    engineer = "Engineer"
    health_care = "Health Care"


class EligibilityEmploymentStatus(str, LookupEnum):
    employed = "Employed"
    unemployed = "Unemployed"
    self_employed = "Self-Employed"
    unknown = "Unknown"
    retired = "Retired"


class EmploymentStatus(str, LookupEnum):
    employed = "Employed"
    unemployed = "Unemployed"
    self_employed = "Self-Employed"
    retired = "Retired"
    unknown = "Unknown"


class LeaveReason(str, LookupEnum):
    pregnancy = "Pregnancy/Maternity"
    pregnancy_health_condition = "A health condition during pregnancy"
    child_bonding = "Child Bonding"
    serious_health_condition_employee = "Serious Health Condition - Employee"
    serious_health_condition_hospital_employee = (
        "An illness or injury that required hospitalization"
    )
    caring_leave = "Care for a Family Member"
    military_caregiver = "Military Caregiver"
    military_exigency_family = "Military Exigency Family"

    @classmethod
    def to_previous_leave_qualifying_reason(
        cls, leave_reason: "LeaveReason"
    ) -> PreviousLeaveQualifyingReason:
        if leave_reason == LeaveReason.pregnancy:
            return PreviousLeaveQualifyingReason.PREGNANCY_MATERNITY
        elif leave_reason == LeaveReason.child_bonding:
            return PreviousLeaveQualifyingReason.CHILD_BONDING
        elif leave_reason == LeaveReason.serious_health_condition_employee:
            return PreviousLeaveQualifyingReason.AN_ILLNESS_OR_INJURY
        elif leave_reason == LeaveReason.caring_leave:
            return PreviousLeaveQualifyingReason.CARE_FOR_A_FAMILY_MEMBER
        elif leave_reason == LeaveReason.military_caregiver:
            return PreviousLeaveQualifyingReason.MILITARY_CAREGIVER
        elif leave_reason == LeaveReason.military_exigency_family:
            return PreviousLeaveQualifyingReason.MILITARY_EXIGENCY_FAMILY
        else:
            raise ValueError("unexpected value reason mapping -- was a new value added recently?")


class LeaveReasonQualifier(str, LookupEnum):
    newborn = "Newborn"
    serious_health_condition = "Serious Health Condition"
    # This reason qualifier is not currently being used. See API-389 & PR #1280.
    work_related_accident_injury = "Work Related Accident/Injury"
    adoption = "Adoption"
    foster_care = "Foster Care"
    not_work_related = "Not Work Related"
    sickness = "Sickness"
    postnatal_disability = "Postnatal Disability"
    work_related = "Work Related"
    blood = "Blood"
    blood_stem_cell = "Blood Stem Cell"
    bone_marrow = "Bone Marrow"
    organ = "Organ"
    other = "Other"
    prenatal_care = "Prenatal Care"
    prenatal_disability = "Prenatal Disability"
    pregnancy_related = "Pregnancy Related"
    right_to_leave = "Right to Leave"
    sickness_non_serious_health_condition = "Sickness - Non-Serious Health Condition"
    childcare = "Childcare"
    counseling = "Counseling"
    financial_and_legal_arrangements = "Financial & Legal Arrangements"
    military_events_and_related_activities = "Military Events & Related Activities"
    other_additional_activities = "Other Additional Activities"
    post_deployment_activites_including_bereavement = (
        "Post Deployment Activities - Including Bereavement"
    )
    rest_and_recuperation = "Rest & Recuperation"
    short_notice_deployment = "Short Notice Deployment"
    closure_of_school_childcare = "Closure of School/Childcare"
    quarantine_isolation_non_sick = "Quarantine/Isolation - Not Sick"
    birth_disability = "Birth Disability"
    childcare_and_school_activities = "Childcare and School Activities"
    post_deployment_activities = "Post Deployment Activities"
    parental_care = "Parental Care"


class RelationshipToCaregiver(str, LookupEnum):
    spouse = "Spouse"
    parent = "Parent"
    child = "Child"
    grandparent = "Grandparent"
    grandchild = "Grandchild"
    other_family_member = "Other Family Member"
    service_member = "Service Member"
    inlaw = "Inlaw"
    sibling = "Sibling - Brother/Sister"
    other = "Other"
    employee = "Employee"


class RelationshipQualifier(str, LookupEnum):
    adoptive = "Adoptive"
    biological = "Biological"
    foster = "Foster"
    custodial_parent = "Custodial Parent"
    legal_guardian = "Legal Guardian"
    step_parent = "Step Parent"


class OrganizationUnitSelection(str, Enum):
    not_listed = "not_listed"
    not_selected = "not_selected"


class OrganizationUnit(PydanticBaseModel):
    organization_unit_id: Optional[UUID4]
    employer_id: Optional[UUID4]
    fineos_id: Optional[str]
    name: Optional[str]


class ReducedScheduleLeavePeriods(PydanticBaseModel):
    leave_period_id: Optional[UUID4]
    start_date: Optional[date]
    end_date: Optional[date]
    thursday_off_minutes: Optional[int]
    friday_off_minutes: Optional[int]
    saturday_off_minutes: Optional[int]
    sunday_off_minutes: Optional[int]
    monday_off_minutes: Optional[int]
    tuesday_off_minutes: Optional[int]
    wednesday_off_minutes: Optional[int]


class ContinuousLeavePeriods(PydanticBaseModel):
    leave_period_id: Optional[UUID4]
    start_date: Optional[date]
    end_date: Optional[date]
    last_day_worked: Optional[date]
    expected_return_to_work_date: Optional[date]
    start_date_full_day: Optional[bool]
    start_date_off_hours: Optional[int]
    start_date_off_minutes: Optional[int]
    end_date_full_day: Optional[bool]
    end_date_off_hours: Optional[int]
    end_date_off_minutes: Optional[int]


class FrequencyIntervalBasis(str, Enum):
    days = "Days"
    weeks = "Weeks"
    months = "Months"


class DurationBasis(str, Enum):
    minutes = "Minutes"
    hours = "Hours"
    days = "Days"


class IntermittentLeavePeriods(PydanticBaseModel):
    leave_period_id: Optional[UUID4]
    start_date: Optional[date]
    end_date: Optional[date]
    frequency: Optional[int]
    frequency_interval: Optional[int]
    frequency_interval_basis: Optional[FrequencyIntervalBasis]
    duration: Optional[int]
    duration_basis: Optional[DurationBasis]


class Address(PydanticBaseModel):
    line_1: Optional[str]
    line_2: Optional[str]
    city: Optional[str]
    state: Optional[str]
    zip: Optional[str]


class ApplicationUserNotFoundInfo(PydanticBaseModel):
    currently_employed: Optional[bool]
    date_of_hire: Optional[date]
    date_of_separation: Optional[date]
    employer_name: Optional[str]
    recently_acquired_or_merged: Optional[bool]

    @validator("date_of_separation")
    def date_of_separation_validation(cls, date_of_separation, values):  # noqa: B902
        """The date of separation must be in the past"""
        if not date_of_separation:
            return date_of_separation

        errors = []
        if date_of_separation > date.today():
            errors.append(
                ValidationErrorDetail(
                    message="The date of separation must be in the past",
                    type=IssueType.maximum,
                    field="additional_user_not_found_info.date_of_separation",
                )
            )

        date_of_hire = values.get("date_of_hire")
        if date_of_hire is None or (date_of_hire >= date_of_separation):
            errors.append(
                ValidationErrorDetail(
                    message="The date of separation must after the date of hire",
                    type=IssueType.minimum,
                    field="additional_user_not_found_info.date_of_separation",
                )
            )

        if errors:
            raise ValidationException(
                errors=errors,
                message="Validation error",
                data={},
            )

        return date_of_separation

    @validator("date_of_hire")
    def date_of_hire_in_past(cls, date_of_hire):  # noqa: B902
        """The date of hire must be in the past"""
        if not date_of_hire:
            return date_of_hire

        if date_of_hire > date.today():
            raise ValidationException(
                errors=[
                    ValidationErrorDetail(
                        message="The date of hire must be in the past",
                        type=IssueType.maximum,
                        field="additional_user_not_found_info.date_of_hire",
                    )
                ],
                message="Validation error",
                data={},
            )

        return date_of_hire


class MaskedAddress(Address):
    @classmethod
    def from_orm(cls, address: db_employee_models.Address) -> "MaskedAddress":
        address_response = super().from_orm(address)
        address_response.zip = mask.mask_zip(address.zip_code)
        address_response.state = (
            address.geo_state.geo_state_description if address.geo_state else None
        )
        address_response.line_1 = mask.mask_address(address.address_line_one)
        address_response.line_2 = mask.mask_address(address.address_line_two)

        return address_response


class ApplicantAddress(Address):
    @classmethod
    def from_orm(cls, address: db_employee_models.Address) -> "ApplicantAddress":
        address_response = super().from_orm(address)
        address_response.zip = address.zip_code
        address_response.state = (
            address.geo_state.geo_state_description if address.geo_state else None
        )
        address_response.line_1 = address.address_line_one
        address_response.line_2 = address.address_line_two

        return address_response


class BaseCaringLeaveMetadata(PydanticBaseModel):
    caring_leave_metadata_id: Optional[UUID4]
    family_member_first_name: Optional[str]
    family_member_middle_name: Optional[str]
    family_member_last_name: Optional[str]
    relationship_to_caregiver: Optional[RelationshipToCaregiver]


class CaringLeaveMetadata(BaseCaringLeaveMetadata):
    family_member_date_of_birth: Optional[date]


class MaskedCaringLeaveMetadata(BaseCaringLeaveMetadata):
    family_member_date_of_birth: Optional[MaskedDateStr]


class BaseApplicationLeaveDetails(PydanticBaseModel):
    reason: Optional[LeaveReason]
    reason_qualifier: Optional[LeaveReasonQualifier]
    reduced_schedule_leave_periods: Optional[List[ReducedScheduleLeavePeriods]]
    continuous_leave_periods: Optional[List[ContinuousLeavePeriods]]
    intermittent_leave_periods: Optional[List[IntermittentLeavePeriods]]
    relationship_to_caregiver: Optional[RelationshipToCaregiver]
    relationship_qualifier: Optional[RelationshipQualifier]
    pregnant_or_recent_birth: Optional[bool]
    employer_notified: Optional[bool]
    employer_notification_date: Optional[date]
    employer_notification_method: Optional[NotificationMethod]
    has_future_child_date: Optional[bool]


class MaskedApplicationLeaveDetails(BaseApplicationLeaveDetails):
    child_birth_date: Optional[MaskedDateStr]
    child_placement_date: Optional[MaskedDateStr]
    caring_leave_metadata: Optional[MaskedCaringLeaveMetadata]

    @classmethod
    def from_orm(
        cls, application: db_application_models.Application
    ) -> "MaskedApplicationLeaveDetails":
        leave_details = super().from_orm(application)

        # This model is shared by requests and responses but some fields need to
        # be loaded from different keys in the different situations:
        #
        # - `reason` on request bodies
        # - `leave_reason` on an Application
        #
        # And similarly:
        #
        # - `reason_qualifier` on request bodies
        # - `leave_reason_qualifier` on an Application
        #
        # The alias feature in pydantic applies to every situation data is
        # parsed from a source, therefore can't be used to support reading from
        # two different field names.
        #
        # Since loading from an Application is almost always going to be through
        # the `from_orm` method, we'll do the aliases ourselves for that use
        # case.
        if application.leave_reason is not None:
            leave_details.reason = LeaveReason(application.leave_reason.leave_reason_description)

        if application.leave_reason_qualifier is not None:
            leave_details.reason_qualifier = LeaveReasonQualifier(
                application.leave_reason_qualifier.leave_reason_qualifier_description
            )

        return leave_details


class ApplicationLeaveDetails(BaseApplicationLeaveDetails):
    child_birth_date: Optional[date]
    child_placement_date: Optional[date]
    caring_leave_metadata: Optional[CaringLeaveMetadata]


class PaymentMethod(str, LookupEnum):
    # Item values here map to Fineos Enum domain #2069
    ach = "Elec Funds Transfer"
    check = "Check"
    debit = "Debit"
    prepaid_card = "Prepaid Card"


# Describes the schedule for when payments are issued / renewed
class PaymentScheduleType(str, Enum):
    # Payments renew on the same day of the week that the leave started
    # So if a leave started on Tuesday, payments would be based on Tue - Wed
    LEAVE_START_BASED = "Leave Start-Based"

    # Payments renew on Sunday, so payments are always Sun - Sat
    # (Except partial payments, which go out on the last day of the payment)
    SUNDAY_BASED = "Sunday-Based"


# Used to group information for payment_line types and
# determines which payment_lines to display to claimants based on category
class PaymentLineTypeCategory(str, Enum):
    ADJUSTMENTS = "Adjustments"
    EMPLOYER_REIMBURSEMENTS = "Employer reimbursements"
    GROSS_PAYMENT_AMOUNT = "Gross payment amount"
    NET_PAYMENT_AMOUNT = "Net payment amount"
    OTHER_LEAVE_INCOME_AND_BENEFITS = "Other leave, income and benefits"
    OVERPAYMENT = "Overpayment"
    TAX_WITHHOLDING = "Tax withholding"
    CHILD_SUPPORT = "Child support"


class PaymentLineType(str, Enum):
    ACCRUED_PAID_LEAVE = "Accrued Paid Leave"
    AUTO_GROSS_ENTITLEMENT = "Auto Gross Entitlement"
    DIA = "DIA"
    DUA = "DUA reduction"
    EARNINGS_FROM_ANOTHER_EMPLOYMENT_SELF_EMPLOYMENT = (
        "Earnings from another employment/self-employment"
    )
    EMPLOYER_REIMBURSEMENT = "Employer Reimbursement"
    FAMILY_OR_MEDICAL_LEAVE_INSURANCE = "Family or medical leave insurance"
    FIT_AMOUNT = "FIT Amount"
    GROSS_PAYMENT_AMOUNT = "Gross payment amount"
    JONES_ACT_BENEFITS = "Jones Act benefits"
    MAIN_PAYMENT_LINE = "Main Payment Line"
    CROSS_BENEFIT_MAXIMUM_THRESHOLD_ADJUSTMENT = "Cross Benefit Maximum Threshold Adjustment"
    MAXIMUM_THRESHOLD_ADJUSTMENT = "Maximum Threshold Adjustment"
    NET_PAYMENT_AMOUNT = "Net payment amount"
    OFFSET_RECOVERY = "Offset Recovery"
    STATE_INCOME_TAX = "State Income Tax"
    CHILD_SUPPORT = "Child Support"


class BankAccountType(str, LookupEnum):
    savings = "Savings"
    checking = "Checking"


class WorkPatternType(str, LookupEnum):
    fixed = "Fixed"
    rotating = "Rotating"
    variable = "Variable"


class DayOfWeek(str, LookupEnum):
    sunday = "Sunday"
    monday = "Monday"
    tuesday = "Tuesday"
    wednesday = "Wednesday"
    thursday = "Thursday"
    friday = "Friday"
    saturday = "Saturday"


class BasePaymentPreference(PydanticBaseModel):
    payment_method: Optional[PaymentMethod]
    bank_account_type: Optional[BankAccountType]


class PaymentPreference(BasePaymentPreference):
    account_number: Optional[str]
    routing_number: Optional[FinancialRoutingNumber]


class MaskedPaymentPreference(BasePaymentPreference):
    account_number: Optional[MaskedFinancialAcctNum]
    routing_number: Optional[MaskedFinancialRoutingNumber]

    @classmethod
    def from_application_payment_preference(
        cls, payment_preference: db_application_models.ApplicationPaymentPreference
    ) -> "MaskedPaymentPreference":
        masked_payment_preference = MaskedPaymentPreference.from_orm(payment_preference)
        if payment_preference.payment_method is not None:
            masked_payment_preference.payment_method = PaymentMethod(
                payment_preference.payment_method.payment_method_description
            )
        return masked_payment_preference


class WorkPatternDay(PydanticBaseModel):
    day_of_week: DayOfWeek
    minutes: Optional[int]


class WorkPattern(PydanticBaseModel):
    work_pattern_type: Optional[WorkPatternType]
    work_pattern_days: Optional[List[WorkPatternDay]]


class OtherIncomeType(str, LookupEnum):
    workers_comp = "Workers Compensation"
    unemployment = "Unemployment Insurance"
    ssdi = "SSDI"
    retirement_disability = "Disability benefits under Gov't retirement plan"
    jones_act = "Jones Act benefits"
    railroad_retirement = "Railroad Retirement benefits"
    other_employer = "Earnings from another employment/self-employment"
    unknown = "Unknown"


class OtherIncome(PydanticBaseModel):
    other_income_id: Optional[UUID4]
    income_type: Optional[OtherIncomeType]
    income_start_date: Optional[date]
    income_end_date: Optional[date]
    income_amount_dollars: Optional[Decimal]
    income_amount_frequency: Optional[AmountFrequency]


# Gender I/O Types
class Gender(str, LookupEnum):
    woman = "Woman"
    man = "Man"
    non_binary = "Non-binary"
    not_listed = "Gender not listed"
    no_answer = "Prefer not to answer"


class Ethnicity(str, LookupEnum):
    hispanic_or_latino = "Hispanic or Latino"
    not_hispanic_or_latino = "Not Hispanic or Latino"
    prefer_not_to_answer = "Prefer not to answer"


class Race(str, LookupEnum):
    american_indian_alaska_native = "American Indian/Alaska Native"
    asian_asian_american = "Asian/Asian American"
    black_african_american = "Black/African American"
    middle_eastern_north_african = "Middle Eastern/North African"
    native_hawaiian_other_pacific_islander = "Native Hawaiian/Other Pacific Islander"
    white = "White"
    prefer_not_to_answer = "Prefer not to answer"
    another_race_not_listed_above = "Another race not listed above"
    multiracial = "Multiracial"


class Language(str, LookupEnum):
    english = "English"
    chinese_simplified = "Chinese (simplified)"
    haitian_creole = "Haitian Creole"
    portuguese = "Portuguese"
    spanish = "Spanish"
    vietnamese = "Vietnamese"
    french = "French"
    not_listed = "Language not listed"


class MmgIdvStatus(str, LookupEnum):
    unverified = "Unverified"
    verified = "Verified"


class IndustrySector(str, LookupEnum):
    accommodation_and_food_services = "Accommodation and Food Services"
    administrative_and_support_and_waste_management_remediation_services = (
        "Administrative and Support and Waste Management Remediation Services"
    )
    agriculture_forestry_fishing_and_hunting = "Agriculture, Forestry, Fishing, and Hunting"
    arts_entertainment_and_recreation = "Arts, Entertainment, and Recreation"
    construction = "Construction"
    educational_services = "Educational Services"
    finance_and_insurance = "Finance and Insurance"
    health_care_and_social_assistance = "Health Care and Social Assistance"
    information = "Information"
    management_of_companies_and_enterprises = "Management of Companies and Enterprises"
    manufacturing = "Manufacturing"
    other_services = "Other Services (except Public Administration)"
    professional_scientific_and_technical_services = (
        "Professional, Scientific, and Technical Services"
    )
    public_administration = "Public Administration"
    real_estate_rental_and_leasing = "Real Estate Rental and Leasing"
    retail_trade = "Retail Trade"
    transportation_and_warehousing = "Transportation and Warehousing"
    utilities = "Utilities"
    wholesale_trade = "Wholesale Trade"
