from datetime import date, datetime
from decimal import Decimal
from enum import Enum
from typing import List, Optional

from pydantic import UUID4
from werkzeug.exceptions import BadRequest, ServiceUnavailable

import massgov.pfml.api.app as app
import massgov.pfml.api.util.response as response_util
from massgov.pfml.api.eligibility.handler import BenefitYearsResponse
from massgov.pfml.api.models.applications.common import (
    ApplicantAddress,
    ApplicationUserNotFoundInfo,
    EmploymentStatus,
    Ethnicity,
    Gender,
    IndustrySector,
    Language,
    MaskedApplicationLeaveDetails,
    MaskedPaymentPreference,
    MmgIdvStatus,
    Occupation,
    OrganizationUnit,
    OrganizationUnitSelection,
    OtherIncome,
    Race,
    WorkPattern,
)
from massgov.pfml.api.models.claims.common import AbsenceStatus
from massgov.pfml.api.models.common import (
    ComputedStartDates,
    EmployerBenefit,
    PreviousLeave,
    PreviousPfmlPeriods,
    get_application_earliest_submission_date,
    get_computed_start_dates,
    get_earliest_start_date,
    get_leave_reason,
)
from massgov.pfml.api.models.phones.common import MaskedPhoneResponse
from massgov.pfml.api.services.applications import (
    ApplicationSplit,
    StartEndDates,
    get_application_split,
)
from massgov.pfml.api.services.claims import get_previous_absence_period_from_claim
from massgov.pfml.api.validation.exceptions import IssueType, ValidationErrorDetail
from massgov.pfml.db.models.applications import Application
from massgov.pfml.fineos.exception import FINEOSEntityNotFound, FINEOSFatalUnavailable
from massgov.pfml.util import logging
from massgov.pfml.util.logging.applications import get_application_log_attributes
from massgov.pfml.util.pydantic import PydanticBaseModel
from massgov.pfml.util.pydantic.types import (
    FEINFormattedStr,
    MaskedDateStr,
    MaskedMassIdStr,
    MaskedTaxIdFormattedStr,
)

logger = logging.get_logger(__name__)


class ApplicationStatus(str, Enum):
    Completed = "Completed"
    InManualReview = "In Manual Review"
    ReadyForReview = "Ready for Review"
    Started = "Started"
    Submitted = "Submitted"


class ApplicationSplitResponse(PydanticBaseModel):
    crossed_benefit_year: BenefitYearsResponse
    application_dates_in_benefit_year: StartEndDates
    application_dates_outside_benefit_year: StartEndDates
    application_outside_benefit_year_submittable_on: date

    @classmethod
    def from_orm(cls, application_split: ApplicationSplit) -> "ApplicationSplitResponse":
        return ApplicationSplitResponse(
            crossed_benefit_year=BenefitYearsResponse.from_orm(
                application_split.crossed_benefit_year
            ),
            application_dates_in_benefit_year=application_split.application_dates_in_benefit_year,
            application_dates_outside_benefit_year=application_split.application_dates_outside_benefit_year,
            application_outside_benefit_year_submittable_on=application_split.application_outside_benefit_year_submittable_on,
        )


class ApplicationResponse(PydanticBaseModel):
    application_id: UUID4
    organization_unit_id: Optional[UUID4]
    organization_unit_selection: Optional[OrganizationUnitSelection]
    tax_identifier: Optional[MaskedTaxIdFormattedStr]
    employee_id: Optional[UUID4]
    employer_dba: Optional[str]
    employer_fein: Optional[FEINFormattedStr]
    fineos_absence_id: Optional[str]
    fineos_absence_status: Optional[AbsenceStatus]
    first_name: Optional[str]
    middle_name: Optional[str]
    last_name: Optional[str]
    date_of_birth: Optional[MaskedDateStr]
    gender: Optional[Gender]
    language: Optional[Language]
    ethnicity: Optional[Ethnicity]
    race: Optional[Race]
    race_custom: Optional[str]
    has_continuous_leave_periods: Optional[bool]
    has_intermittent_leave_periods: Optional[bool]
    has_reduced_schedule_leave_periods: Optional[bool]
    has_state_id: Optional[bool]
    has_submitted_payment_preference: Optional[bool]
    mass_id: Optional[MaskedMassIdStr]
    occupation: Optional[Occupation]
    organization_unit: Optional[OrganizationUnit]
    hours_worked_per_week: Optional[Decimal]
    hours_worked_per_week_all_employers: Optional[Decimal]
    employment_status: Optional[EmploymentStatus]
    leave_details: Optional[MaskedApplicationLeaveDetails]
    payment_preference: Optional[MaskedPaymentPreference]
    work_pattern: Optional[WorkPattern]
    updated_time: Optional[datetime]
    status: Optional[ApplicationStatus]
    has_mailing_address: Optional[bool]
    mailing_address: Optional[ApplicantAddress]
    residential_address: Optional[ApplicantAddress]
    has_employer_benefits: Optional[bool]
    additional_user_not_found_info: Optional[ApplicationUserNotFoundInfo]
    employer_benefits: Optional[List[EmployerBenefit]]
    employee_organization_units: List[OrganizationUnit]
    employer_organization_units: List[OrganizationUnit]
    has_other_incomes: Optional[bool]
    other_incomes: Optional[List[OtherIncome]]
    phone: Optional[MaskedPhoneResponse]
    previous_leaves: Optional[List[PreviousLeave]]
    has_previous_leaves: Optional[bool]
    is_withholding_tax: Optional[bool]
    imported_from_fineos_at: Optional[datetime]
    updated_at: datetime
    computed_start_dates: Optional[ComputedStartDates]
    split_from_application_id: Optional[UUID4]
    split_into_application_id: Optional[UUID4]
    computed_earliest_submission_date: Optional[date]
    computed_application_split: Optional[ApplicationSplitResponse]
    computed_leave_details_is_editable: bool = True
    computed_has_passed_manual_review: bool = False
    previous_pfml_leave_periods: Optional[List[PreviousPfmlPeriods]] = []
    fields_to_use_from_user_profile: Optional[List[str]]
    has_concurrent_employers: Optional[bool]
    mmg_idv_status: Optional[MmgIdvStatus]
    industry_sector: Optional[IndustrySector]

    @classmethod
    def from_orm(cls, application: Application) -> "ApplicationResponse":
        application_response = super().from_orm(application)
        if application.mailing_address is not None:
            application_response.mailing_address = ApplicantAddress.from_orm(
                application.mailing_address
            )
        if application.residential_address is not None:
            application_response.residential_address = ApplicantAddress.from_orm(
                application.residential_address
            )
        application_response.leave_details = MaskedApplicationLeaveDetails.from_orm(application)
        if application.payment_preference is not None:
            application_response.payment_preference = (
                MaskedPaymentPreference.from_application_payment_preference(
                    application.payment_preference
                )
            )

        if application.phone is not None:
            application_response.phone = MaskedPhoneResponse.from_orm(application.phone)

        if application.employer is not None:
            application_response.employer_dba = application.employer.employer_dba

        if application.ready_for_review_time:
            application_response.status = ApplicationStatus.ReadyForReview
        elif application.completed_time:
            application_response.status = ApplicationStatus.Completed
        elif application.submitted_time:
            application_response.status = ApplicationStatus.Submitted
        elif (
            application.additional_user_not_found_info
            and application.additional_user_not_found_info.submitted_time
        ):
            application_response.status = ApplicationStatus.InManualReview
        else:
            application_response.status = ApplicationStatus.Started

        if application.claim is not None:
            application_response.fineos_absence_id = application.claim.fineos_absence_id
            application_response.fineos_absence_status = (
                AbsenceStatus(application.claim.fineos_absence_status.absence_status_description)
                if application.claim.fineos_absence_status
                else None
            )
            application_response.computed_leave_details_is_editable = False

        previous_pfml_leave_periods = _get_previous_pfml_leave_periods(application)
        application_response.previous_pfml_leave_periods = previous_pfml_leave_periods

        if application.employee is not None:
            application_response.employee_id = application.employee.employee_id
            application_response.computed_application_split = _get_application_split_response(
                application
            )

        application_response.updated_time = application_response.updated_at
        application_response.computed_start_dates = _get_computed_start_dates(application)
        application_response.computed_earliest_submission_date = (
            get_application_earliest_submission_date(application)
        )

        # True when the application passed through manual review
        # and it is linked to a claim, and it's status is not ApplicationStatus.Completed
        application_response.computed_has_passed_manual_review = (
            application.claim is not None
            and application_response.status == ApplicationStatus.Submitted
            and application.additional_user_not_found_info is not None
            and application.additional_user_not_found_info.submitted_time is not None
        )

        return application_response


def _get_computed_start_dates(application: Application) -> ComputedStartDates:
    earliest_start_date = get_earliest_start_date(application)
    leave_reason = get_leave_reason(application)
    return get_computed_start_dates(earliest_start_date, leave_reason)


def _get_application_split_response(application: Application) -> Optional[ApplicationSplitResponse]:
    with app.db_session() as db_session:
        split = get_application_split(application, db_session)

    if split is None:
        return None
    else:
        return ApplicationSplitResponse.from_orm(split)


def _get_previous_pfml_leave_periods(application: Application) -> List[PreviousPfmlPeriods]:
    previous_absence_periods = get_previous_absence_period_from_claim(
        application, limit_to_claim_employer=False
    )
    previous_pfml_leave_periods = [
        PreviousPfmlPeriods(
            start_date=absence_period.start_date,
            end_date=absence_period.end_date,
        )
        for absence_period in previous_absence_periods
        if absence_period.start_date and absence_period.end_date
    ]
    return previous_pfml_leave_periods


def get_fineos_submit_issues_response(err, existing_application):
    if isinstance(err, FINEOSEntityNotFound):
        logger.error(
            "applications_submit failure - register_employee did not find a match",
            extra=get_application_log_attributes(existing_application),
        )
        return response_util.error_response(
            status_code=BadRequest,
            message="Application {} could not be submitted".format(
                existing_application.application_id
            ),
            errors=[
                ValidationErrorDetail(
                    IssueType.fineos_case_creation_issues, "register_employee did not find a match"
                )
            ],
            data=ApplicationResponse.from_orm(existing_application).dict(exclude_none=True),
        ).to_api_response()

    elif isinstance(err, FINEOSFatalUnavailable):
        logger.error(
            "applications_submit failure - Unexpected error encountered when submitting to the Claims Processing System",
            extra=get_application_log_attributes(existing_application),
        )
        # These errors are usually caught in our error handler and raised with a "fineos_client" issue type.
        # Once the Portal behavior is changed to handle that type, we can remove this special case.
        return response_util.error_response(
            status_code=ServiceUnavailable,
            message="Application {} could not be submitted, try again later".format(
                existing_application.application_id
            ),
            errors=[
                ValidationErrorDetail(
                    IssueType.fineos_case_error,
                    "Unexpected error encountered when submitting to the Claims Processing System",
                )
            ],
            data=ApplicationResponse.from_orm(existing_application).dict(exclude_none=True),
        ).to_api_response()
    else:
        # We don't expect any other errors like 500s. Raise an alarm bell.
        raise err
