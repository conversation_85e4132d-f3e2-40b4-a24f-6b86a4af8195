from typing import List, Optional

from massgov.pfml.api.models.insurance_providers.common import AddressResponse
from massgov.pfml.api.models.phones.common import PhoneResponse
from massgov.pfml.db.models.employer_exemptions import InsurancePlan, InsuranceProvider
from massgov.pfml.util.pydantic import PydanticBaseModel


class InsuranceProviderResponse(PydanticBaseModel):
    """
    Pydantic model for insurance provider API responses.

    Attributes:
        insurance_provider_name: Name of the insurance provider
        insurance_provider_id: Unique identifier for the insurance provider
        phone: Optional phone contact information
        address: Optional address information
    """

    insurance_provider_name: str
    insurance_provider_id: int
    phone: Optional[PhoneResponse] = None
    address: Optional[AddressResponse] = None
    deactivated: bool

    @classmethod
    def from_orm(cls, insurance_provider: InsuranceProvider) -> "InsuranceProviderResponse":
        """
        Creates an InsuranceProviderResponse instance from a database InsuranceProvider model.

        Args:
            insurance_provider: InsuranceProvider database model instance

        Returns:
            InsuranceProviderResponse: Transformed response object

        Raises:
            ValueError: If insurance is None
        """
        if insurance_provider is None:
            raise ValueError("Insurance provider cannot be None")

        return cls(
            insurance_provider_name=insurance_provider.insurance_provider_name,
            insurance_provider_id=insurance_provider.insurance_provider_id,
            phone=(
                PhoneResponse.from_orm(insurance_provider.phone)
                if insurance_provider.phone
                else None
            ),
            address=(
                AddressResponse.from_orm(insurance_provider.address)
                if insurance_provider.address
                else None
            ),
            deactivated=insurance_provider.deactivated,
        )


class InsuranceProviderPlansResponse(PydanticBaseModel):
    """
    Pydantic model for insurance provider plans API responses.

    Attributes:
        insurance_plan_id: Name of the insurance provider
        insurance_provider_id: Unique identifier for the insurance provider
        form_name: Optional phone contact information
        has_family_exemption: Optional address information
        has_medical_exemption: list of plans the provider offers
    """

    insurance_plan_id: int
    insurance_provider_id: int
    form_name: Optional[str] = None
    has_family_exemption: Optional[bool] = None
    has_medical_exemption: Optional[bool] = None
    deactivated: bool

    @classmethod
    def custom_from_orm(
        cls, insurance_plan: InsurancePlan, provider_id: int
    ) -> "InsuranceProviderPlansResponse":
        """
        Creates an InsuranceProviderPlansResponse instance from a database InsurancePlan model.

        Args:
            insurance_plan: InsurancePlan database model instance

        Returns:
            InsuranceProviderPlansResponse: Transformed response object

        Raises:

        """
        return cls(
            insurance_plan_id=insurance_plan.insurance_plan_id,
            insurance_provider_id=provider_id,
            form_name=insurance_plan.form_name,
            has_family_exemption=insurance_plan.has_family_exemption,
            has_medical_exemption=insurance_plan.has_medical_exemption,
            deactivated=insurance_plan.deactivated,
        )


class InsuranceProviderDetailsResponse(PydanticBaseModel):
    """
    Pydantic model for insurance provider details API responses.

    Attributes:
        insurance_provider_name: Name of the insurance provider
        insurance_provider_id: Unique identifier for the insurance provider
        phone: Optional phone contact information
        address: Optional address information
        insurance_plans: list of plans the provider offers
    """

    insurance_provider_name: str
    insurance_provider_id: int
    phone: Optional[PhoneResponse] = None
    address: Optional[AddressResponse] = None
    insurance_plans: Optional[List[InsuranceProviderPlansResponse]]

    @classmethod
    def custom_from_orm(
        cls,
        insurance_provider: InsuranceProvider,
        insurance_plan_list: Optional[List[InsurancePlan]],
    ) -> "InsuranceProviderDetailsResponse":
        """
        Creates an InsuranceProviderDetailsResponse instance from a database InsuranceProvider model and InsurancePlan.

        Args:
            insurance_provider: InsuranceProvider database model instance
            insurance_plan_list: List of InsurancePlan database model instances

        Returns:
            InsuranceProviderDetailsResponse: Transformed response object

        Raises:
            ValueError: If insurance is None
        """
        if insurance_provider is None:
            raise ValueError("Insurance provider cannot be None")

        return cls(
            insurance_provider_name=insurance_provider.insurance_provider_name,
            insurance_provider_id=insurance_provider.insurance_provider_id,
            phone=(
                PhoneResponse.from_orm(insurance_provider.phone)
                if insurance_provider.phone
                else None
            ),
            address=(
                AddressResponse.from_orm(insurance_provider.address)
                if insurance_provider.address
                else None
            ),
            insurance_plans=(
                [
                    InsuranceProviderPlansResponse.custom_from_orm(
                        insurance_plan, insurance_provider.insurance_provider_id
                    )
                    for insurance_plan in insurance_plan_list
                ]
                if insurance_plan_list
                else None
            ),
        )
