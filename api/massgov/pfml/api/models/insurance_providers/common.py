from typing import Optional

from massgov.pfml.api.models.applications.common import Address
from massgov.pfml.db.models.employees import Address as DBAdress


class AddressResponse(Address):
    @classmethod
    def from_orm(cls, address: Optional[DBAdress]) -> "AddressResponse":
        """
        Creates an AddressResponse from the database Address model

        Args:
            address: Optional database Address model instance

        Returns:
            AddressResponse: Transformed address response object
        """
        # create a base response using parent class method
        address_response = super().from_orm(address)

        if address is not None:
            address_response.line_1 = address.address_line_one
            address_response.line_2 = address.address_line_two
            address_response.city = address.city
            address_response.state = address.geo_state_text
            address_response.zip = address.zip_code

        return address_response
