from typing import Optional

from pydantic import UUID4

from massgov.pfml.util.pydantic import PydanticBaseModel


class OverpaymentSearchRequest(PydanticBaseModel):
    fineos_customer_number: str


class OverpaymentReferRequest(PydanticBaseModel):
    overpayment_id: UUID4


class OverpaymentRetryTransactionRequest(PydanticBaseModel):
    mmars_event_id: UUID4


class OverpaymentHoldTransactionRequest(PydanticBaseModel):
    mmars_event_id: UUID4
    reason: Optional[str] = None


class OverpaymentMarkVcmReviewedRequest(PydanticBaseModel):
    employee_id: UUID4
