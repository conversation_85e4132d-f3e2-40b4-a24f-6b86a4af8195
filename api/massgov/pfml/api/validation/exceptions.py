#
# Exceptions to be used to extract relevant validation exception values
#

from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional, Union

from werkzeug.exceptions import HTTPException


class PaymentRequired(HTTPException):
    code = 402
    description = "Payment required"


class ContainsV1AndV2Eforms(HTTPException):
    status_code = HTTPException
    description = "Claim contains both V1 and V2 eforms."


# Partial list of types currently used manually
# This is not a comprehensive list of all IssueRules
class IssueRule(str, Enum):
    # At least one leave period should be present
    min_leave_periods = "min_leave_periods"
    # At least one reduced leave schedule day should have an amount of reduced time
    min_reduced_leave_minutes = "min_reduced_leave_minutes"
    # A rule only applied because a certain condition was met
    conditional = "conditional"
    # Caring leave applications can't start before July
    disallow_caring_leave_before_july = "disallow_caring_leave_before_july"
    # Intermittent leave must be on its own application
    disallow_hybrid_intermittent_leave = "disallow_hybrid_intermittent_leave"
    # Can't submit when earliest leave period is more than 60 days in the future
    disallow_submit_over_60_days_before_start_date = (
        "disallow_submit_over_60_days_before_start_date"
    )
    # Leave Period dates can't overlap
    disallow_overlapping_leave_periods = "disallow_overlapping_leave_periods"
    # Range between earliest leave period start date and the latest leave period end date can’t be 12 months or longer
    disallow_12mo_leave_period = "disallow_12mo_leave_period"
    # Range between continuous leave period start date and end date can’t be 12 months or longer
    disallow_12mo_continuous_leave_period = "disallow_12mo_continuous_leave_period"
    # Range between intermittent leave period start date and end date can’t be 12 months or longer
    disallow_12mo_intermittent_leave_period = "disallow_12mo_intermittent_leave_period"
    # Range between reduced leave period start date and end date can’t be 12 months or longer
    disallow_12mo_reduced_leave_period = "disallow_12mo_reduced_leave_period"
    # Leave periods from other applications cannot overlap
    disallow_overlapping_leave_period_from_other_applications = (
        "disallow_overlapping_leave_period_from_other_applications"
    )
    # Leave periods and previous leaves cannot overlap
    disallow_overlapping_leave_period_with_previous_leave = (
        "disallow_overlapping_leave_period_with_previous_leave"
    )

    disallow_concurrent_leave_end_date_after_pfml_end_date = (
        "disallow_concurrent_leave_end_date_after_pfml_end_date"
    )

    disallow_concurrent_leave_start_date_before_waiting_period_end = (
        "disallow_concurrent_leave_start_date_before_waiting_period_end"
    )

    disallow_employer_benefit_end_date_after_pfml_end_date = (
        "disallow_employer_benefit_end_date_after_pfml_end_date"
    )

    disallow_employer_benefit_start_date_after_pfml_end_date = (
        "disallow_employer_benefit_start_date_after_pfml_end_date"
    )

    disallow_employer_benefit_start_date_before_waiting_period_end = (
        "disallow_employer_benefit_start_date_before_waiting_period_end"
    )

    # Document requirement has already been satisfied in Fineos
    document_requirement_already_satisfied = "document_requirement_already_satisfied"
    # Employer must be notified when employment status is Employed
    require_employer_notified = "require_employer_notified"
    # Partially masked field does not match existing value
    disallow_mismatched_masked_field = "disallow_mismatched_masked_field"
    # Fully masked field present when system contains no data
    disallow_fully_masked_no_existing = "disallow_fully_masked_no_existing"
    # Disallow suspicious attempts for potential fraud cases.
    # Intentionally vague to avoid leaking this is for fraud prevention
    disallow_attempts = "disallow_attempts"
    # Restrict claimants from trying several SSN/FEIN combinations
    max_ssn_fein_update_attempts = "max_ssn_fein_update_attempts"
    # Claimants cannot report over time limits of intermittent leave depending on the episodic_basis
    max_reported_int_episodic_period = "max_reported_int_episodic_period"
    # Reported intermittent leave date cannot be in the future
    reported_intermittent_leave_date_cannot_exceed_today = (
        "reported_intermittent_leave_date_cannot_exceed_today"
    )
    # Employee must have wages from the Employer
    require_employee = "require_employee"
    # Employer record must exist in the API and FINEOS
    require_contributing_employer = "require_contributing_employer"
    # Only one draft Employer Exemption Application allowed per Employer
    disallow_multiple_draft_employer_exemption_applications = (
        "disallow_multiple_draft_employer_exemption_applications"
    )

    def __str__(self):
        return str(self.value)


class IssueType(str, Enum):
    """
    Potential values for the `type` field of a `ValidationErrorDetail`.
    Reuse of existing types when applicable is encouraged.
    """

    # PTO end date is strictly greater than pfml end date
    concurrent_leave_end_date_after_pfml_end_date = "concurrent_leave_end_date_after_pfml_end_date"
    # Data is present but shouldn't be
    conflicting = "conflicting"
    # temporary IssueType for new concurrent leave_alerts
    conflicting_concurrent_leave = "conflicting_concurrent_leave"
    # temporary IssueType for new concurrent leave_alerts
    conflicting_concurrent_leave_v2 = "conflicting_concurrent_leave_v2"
    conflicting_employer_benefit = "conflicting_employer_benefit"
    # Eform versions should be consistent
    contains_v1_and_v2_eforms = "contains_v1_and_v2_eforms"
    # User provided a date that has already been reported
    date_already_reported = "date_already_reported"
    # User provided a date that is not approved
    date_not_approved = "date_not_approved"
    # A matching record already exists
    duplicate = "duplicate"
    # Employer benefit end date is strictly greater than pfml end date
    employer_benefit_end_date_after_pfml_end_date = "employer_benefit_end_date_after_pfml_end_date"
    # Employer benefit start date is strictly greater than pfml end date
    employer_benefit_start_date_after_pfml_end_date = (
        "employer_benefit_start_date_after_pfml_end_date"
    )
    # A record already exists, preventing this data from being used again
    exists = "exists"
    # Number or Date is greater than expected range
    maximum = "maximum"
    # Number or Date is less than the expected range and neither DBA or FEIN are available
    minimum = "minimum"
    # Number or Date is less than the expected range and both DBA & FEIN are available
    minimum_dba = "minimum_dba"
    # Number or Date is less than the expected range and FEIN is available, but DBA is not.
    minimum_ein = "minimum_ein"
    # Data didn't conform to expected pattern
    # OpenAPI validation errors may have this `type`, so using the same term to be consistent.
    pattern = "pattern"
    # Data is insecure or compromised and cannot be used
    insecure = "insecure"
    # Generic issue indicating the data is wrong in some way. For example, it doesn't match our records.
    incorrect = "incorrect"
    # Generic issue indicating the data is wrong in some way, and you are approaching a limit.
    incorrect_close_to_limits = "incorrect_close_to_limits"
    # Generic issue indicating something about the data is invalid. This should
    # only be used when we're unable to provide anything more specific about the issue,
    # for instance when the issue could be a range of things that we're unable to specify.
    invalid = "invalid"
    # TODO (EMPLOYER-1642): Use more generic error
    invalid_phone_number = "invalid_phone_number"
    # Date range is invalid, eg a start date occurs after an end date
    invalid_date_range = "invalid_date_range"
    # TODO (EMPLOYER-1642): Use more generic error
    invalid_year_range = "invalid_year_range"
    # TODO (EMPLOYER-1642): Use more generic error
    invalid_previous_leave_start_date = "invalid_previous_leave_start_date"
    # TODO (EMPLOYER-1642): Use more generic error
    invalid_age = "invalid_age"
    # TODO (EMPLOYER-1642): Use more generic error
    future_birth_date = "future_birth_date"
    # Generic issue for dates that cannot exceed the current date
    future_date = "future_date"
    # Data is missing
    required = "required"
    object_not_found = "object_not_found"
    # TODO (EMPLOYER-1642): Use more generic error
    outstanding_information_request_required = "outstanding_information_request_required"
    # Masked field is not allowed
    invalid_masked_field = "invalid_masked_field"
    # File is too large to be stored
    file_size = "file_size"
    # File mime type is not a supported mime type
    file_type = "file_type"
    # Extension portion is required in the filename, but missing.
    file_name_extension = "file_name_extension"
    # Parsed mime type is different than what file extension indicates
    file_type_mismatch = "file_type_mismatch"
    # Generic error indicating the error originated from Fineos (FINEOSClientBadResponse)
    fineos_client = "fineos_client"
    # A claimant could not be created in FINEOS
    fineos_case_creation_issues = "fineos_case_creation_issues"
    # An unspecified error related to creating/completing the absence case in fineos
    fineos_case_error = "fineos_case_error"
    # Indicates that the requested FINEOS claim has been withdrawn and can no longer be accessed
    fineos_claim_withdrawn = "fineos_claim_withdrawn"
    # If duration of intermittent leave is in hours, hours must be less than a day (24)
    intermittent_duration_hours_maximum = "intermittent_duration_hours_maximum"
    # Total days in an intermittent interval cannot exceed total days in the leave period
    # e.g. You cannot have a leave interval of every 6 months if the start and end
    # date of the leave period is only 2 months
    intermittent_interval_maximum = "intermittent_interval_maximum"
    # Total days absent cannot exceed total days in the interval
    # e.g. You can not request 5 days off 2 times in a week as that would
    # exceed the 7 days in a week
    days_absent_per_intermittent_interval_maximum = "days_absent_per_intermittent_interval_maximum"
    # Employer record must exist in the API
    require_employer = "require_employer"
    # Employer record must exist in the API and FINEOS
    require_contributing_employer = "require_contributing_employer"
    # Data failed a checksum test e.g. Routing number
    checksum = "checksum"
    # Employer can't be verified because there's nothing to verify against
    employer_requires_verification_data = "employer_requires_verification_data"
    # Leave admin user attempting to view data for an organization they don't have access to
    unauthorized_leave_admin = "unauthorized_leave_admin"
    # User is trying to submit a change request without an absence period in a valid state
    must_have_valid_decision_status = "must_have_valid_decision_status"
    # User is trying to add start and end dates to a claim withdrawal
    withdrawal_dates_must_be_null = "withdrawal_dates_must_be_null"
    # User is trying to make an unsupported modification to a claim with non-approved_periods
    only_approved_periods_valid = "only_approved_periods_valid"
    # User is trying to make an unsupported modification to a claim with non-pending_periods or non-in_review_periods
    only_in_review_or_pending_periods_valid = "only_in_review_or_pending_periods_valid"
    # User is trying to make an unsupported modification
    not_medical_to_bonding_claim = "not_medical_to_bonding_claim"
    # User is trying to add a duplicate bonding period
    multiple_med_to_bonding = "multiple_med_to_bonding"
    # User has more paid leave cases than leave requests on the absence case
    more_paid_leave_cases_than_leave_requests = "more_paid_leave_cases_than_leave_requests"
    # User has already submitted a withdrawal
    has_submitted_withdrawal = "has_submitted_withdrawal"
    # Claim is more than the max past its end date to submit a modification
    leave_end_past_max = "leave_end_past_max"
    # Start date is invalid for modification request type
    change_start_date_is_unsupported = "change_start_date_is_unsupported"
    # Modification shouldn't have the same end date as the claim start date (cancel instead)
    change_request_end_date_same_as_start = "change_request_end_date_same_as_start"
    # Modification shouldn't have the same end date as the already approved end date
    change_end_date_same_as_approved_end_date = "change_end_date_same_as_approved_end_date"
    # Too many attempts were made for this endpoint
    too_many_attempts = "too_many_attempts"
    # User is trying to use an expired token
    refresh_auth_token = "refresh_auth_token"
    # Generic issue for email addresses format
    invalid_email_address = "invalid_email_address"
    # Generic error indicating the error originated from our own business logic.
    # This was added when we began enforcing that `type` is an `IssueType`. Avoid using
    # this moving forward.
    # TODO (EMPLOYER-1643): Remove this once the errors the reference it no longer use IssueRule
    pfml = ""
    # Generic value error
    value_error = "value_error"
    # Employee associated with the tax identifier on the claim has an invalid benefit year
    employee_has_invalid_benefit_year = "employee_has_invalid_benefit_year"
    # Range between leave period end date and extension end date can’t be 12 months or longer
    disallow_12mo_extension = "disallow_12mo_extension"

    # User is not authorized to access page, record, etc
    not_authorized_for_access = "not_authorized_for_access"

    # error in connexion custom middleware
    custom_middleware_error = "custom_middleware_error"

    def __str__(self):
        return str(self.value)


@dataclass
class ValidationErrorDetail:
    type: IssueType
    message: str = ""
    rule: Optional[Union[IssueRule, str]] = None
    field: Optional[str] = None
    value: Optional[str] = None  # Do not store PII data here, as it gets logged in some cases
    extra: Optional[Dict[str, str]] = None


class ValidationErrorList:
    def __init__(self):
        self._errors = []

    def add_validation_error(
        self,
        type: IssueType,
        message: str = "",
        rule: Optional[Union[IssueRule, str]] = None,
        field: Optional[str] = None,
        value: Optional[str] = None,
        extra: Optional[Dict[str, str]] = None,
    ) -> None:
        error = ValidationErrorDetail(
            type=type, message=message, rule=rule, field=field, value=value, extra=extra
        )
        self._errors.append(error)

    def add_error_directly(self, error: ValidationErrorDetail) -> None:
        self._errors.append(error)

    def __iadd__(self, additional_errors):
        if isinstance(additional_errors, ValidationErrorDetail):
            self._errors.append(additional_errors)
        elif isinstance(additional_errors, list) and all(
            isinstance(item, ValidationErrorDetail) for item in additional_errors
        ):
            self._errors.extend(additional_errors)
        else:
            raise TypeError("Can only add ValidationErrorDetail or list of ValidationErrorDetail")
        return self

    def __len__(self) -> int:
        return len(self._errors)

    def __iter__(self):
        return iter(self._errors)

    def get_errors(self) -> List[ValidationErrorDetail]:
        return self._errors


class ValidationException(Exception):
    __slots__ = ["errors", "message", "data"]

    def __init__(
        self,
        errors: List[ValidationErrorDetail],
        message: str = "Invalid request",
        data: Optional[Union[dict, List[dict]]] = None,
    ):
        self.errors = errors
        self.message = message
        self.data = data or {}
