from datetime import date
from decimal import Decimal
from typing import Any, Dict, List, Optional

from dateutil.relativedelta import relativedelta

import massgov.pfml.db as db
import massgov.pfml.services.documents as documents_service
import massgov.pfml.util.logging
from massgov.pfml.api.constants.documents import (
    ID_DOC_TYPES,
    SERIOUS_HEALTH_CONDITION_CERTIFICATION_DOC_TYPES,
)
from massgov.pfml.api.util.deepgetattr import deepgetattr
from massgov.pfml.api.validation.application_rules.leave_type import get_leave_periods_issues
from massgov.pfml.api.validation.employment_validator import (
    get_contributing_employer_or_employee_issue,
)
from massgov.pfml.api.validation.exceptions import (
    IssueRule,
    IssueType,
    ValidationErrorDetail,
    ValidationErrorList,
)
from massgov.pfml.db.lookup_data.applications import (
    EmploymentStatus,
    LeaveReason,
    LeaveReasonQualifier,
    MmgIdvStatus,
)
from massgov.pfml.db.lookup_data.employees import PaymentMethod
from massgov.pfml.db.models.applications import Application, WorkPattern
from massgov.pfml.features import FeaturesConfig
from massgov.pfml.util.routing_number_validation import validate_routing_number

from .common import check_required_fields, handle_rename
from .employer_benefits import get_employer_benefits_issues
from .other_incomes import get_other_incomes_issues
from .previous_leaves import get_all_previous_leaves_issues

logger = massgov.pfml.util.logging.get_logger(__name__)


def get_application_submit_issues(
    application: Application,
    db_session: db.Session,
    log_attributes: dict,
    features: FeaturesConfig,
) -> List[ValidationErrorDetail]:
    """Takes in application and outputs any validation issues.
    These issues are either fields that are always required for an application or fields that are conditionally required based on previous input.
    """

    # Only run these validations if the application hasn't already been submitted. This
    # prevents warnings from showing in the response for rules added after the application
    # was submitted, which would cause a Portal user's Checklist to revert back to showing
    # steps as incomplete, and they wouldn't be able to fix this.
    if application.submitted_time:
        return []

    issues = ValidationErrorList()

    issues += get_always_required_issues(application)
    issues += get_leave_periods_issues(application)
    issues += get_conditional_issues(
        application,
        features,
    )
    employer_issue = get_contributing_employer_or_employee_issue(
        db_session,
        application.employer_fein,
        application.tax_identifier,
        log_attributes,
    )
    if employer_issue:
        issues.add_error_directly(employer_issue)

    if features.application_intake.disable_overlapping_benefit_year_claim_creation:
        benefit_year_issue = get_invalid_benefit_year_issue(application)
        if benefit_year_issue:
            extra = {"application.application_id": application.application_id}

            if application.employee:
                extra["employee_id"] = application.employee.employee_id

            logger.info(
                "Application cannot be sumbmitted since the employee has invalid benefit years",
                extra=extra,
            )
            issues.add_error_directly(benefit_year_issue)

    return issues.get_errors()


def get_invalid_benefit_year_issue(application: Application) -> Optional[ValidationErrorDetail]:
    employee = application.employee
    if not employee or not employee.invalid_benefit_years_since:
        return None

    return ValidationErrorDetail(
        type=IssueType.employee_has_invalid_benefit_year,
        message="The employee with matching tax identifier has an invalid benefit year.",
    )


def contains_benefit_year_issue(
    issues: List[ValidationErrorDetail],
) -> Optional[ValidationErrorDetail]:
    for issue in issues:
        if issue.type == IssueType.employee_has_invalid_benefit_year:
            return issue
    return None


def contains_employer_issue(issues: List[ValidationErrorDetail]) -> bool:
    return any(is_employment_issue(issue) for issue in issues)


def is_employment_issue(issue: ValidationErrorDetail) -> bool:
    """
    Returns true if it's an issue with the FEIN or SSN.
    This is used in a few cases to indicate if a user is in the user not found
    flow where additional information is collected.
    """
    return (
        issue.rule == IssueRule.require_employee
        or issue.rule == IssueRule.require_contributing_employer
    )


def get_application_complete_issues(
    application: Application, db_session: db.Session, certificate_document_deferred: bool
) -> List[ValidationErrorDetail]:
    """Takes in an application and outputs any validation issues preventing an application from being completed."""
    issues = ValidationErrorList()
    # Part 1 application exists in FINEOS
    issues += get_fineos_verification_issues(application)

    # Part 2 payment section complete
    issues += get_app_complete_payments_issues(application)
    issues += get_app_is_withholding_tax_issues(application)

    # Part 3 document section complete
    issues += get_documents_issues(application, db_session, certificate_document_deferred)

    return issues.get_errors()


def get_fineos_verification_issues(application):
    # Application must have a case in Fineos in order to be completed. This is equivalent to saying
    # that "Part 1" of the application flow in the Portal has been fulfilled. We don't run the
    # validations in get_application_submit_issues here, because there are scenarios where new
    # rules may be introduced, but those rules don't apply if the application was already "submitted".
    issues = ValidationErrorList()
    if not application.claim or not application.claim.fineos_absence_id:
        issues.add_validation_error(
            type=IssueType.object_not_found,
            message="A case must exist before it can be marked as complete.",
        )
    return issues.get_errors()


def get_address_issues(
    application: Application, address_field_name: str
) -> List[ValidationErrorDetail]:
    issues = ValidationErrorList()
    address_field_db_name_to_api_name_map = {
        f"{address_field_name}.address_line_one": f"{address_field_name}.line_1",
        f"{address_field_name}.city": f"{address_field_name}.city",
        f"{address_field_name}.geo_state_id": f"{address_field_name}.state",
        f"{address_field_name}.zip_code": f"{address_field_name}.zip",
    }

    for field, openapi_field in address_field_db_name_to_api_name_map.items():
        val = deepgetattr(application, field)
        if val is None:
            issues.add_validation_error(
                type=IssueType.required,
                message=f"{openapi_field} is required",
                field=openapi_field,
            )

    return issues.get_errors()


def check_codependent_fields(
    path: str, item: Any, field_a: str, field_b: str, renames: Optional[Dict[str, str]] = None
) -> List[ValidationErrorDetail]:
    """
    Checks that neither or both of the specified fields (field_a and _field_b) are set on item. If only one
    field is set then the returned issues will not be empty.
    """
    issues = ValidationErrorList()

    val_a = getattr(item, field_a)
    val_b = getattr(item, field_b)
    field_a_path = f"{path}.{handle_rename(renames, field_a)}"
    field_b_path = f"{path}.{handle_rename(renames, field_b)}"
    if val_a and not val_b:
        issues.add_validation_error(
            type=IssueType.required,
            rule=IssueRule.conditional,
            message=f"{field_b_path} is required if {field_a_path} is set",
            field=field_b_path,
        )
    elif val_b and not val_a:
        issues.add_validation_error(
            type=IssueType.required,
            rule=IssueRule.conditional,
            message=f"{field_a_path} is required if {field_b_path} is set",
            field=field_a_path,
        )

    return issues.get_errors()


def get_leave_related_issues(application: Application) -> List[ValidationErrorDetail]:
    is_medical_leave_app = application.leave_reason_id in (
        LeaveReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.leave_reason_id,
        LeaveReason.PREGNANCY_MATERNITY.leave_reason_id,
    )

    if application.leave_reason_id == LeaveReason.CHILD_BONDING.leave_reason_id:
        return get_bonding_leave_issues(application)
    elif is_medical_leave_app:
        return get_medical_leave_issues(application)
    elif application.leave_reason_id == LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id:
        return get_caring_leave_issues(application)

    return []


def get_conditional_issues(
    application: Application,
    features: FeaturesConfig,
) -> List[ValidationErrorDetail]:
    issues = ValidationErrorList()
    # TODO (CP-2455): This condition is temporary. It can be removed once we
    # can safely enforce these validation rules across all in-progress claims
    require_other_leaves_fields = not application.submitted_time

    if application.additional_user_not_found_info is not None:
        if not application.additional_user_not_found_info.employer_name:
            issues.add_validation_error(
                type=IssueType.required,
                rule=IssueRule.conditional,
                message="employer_name is required if additional_user_not_found_info is set",
                field="additional_user_not_found_info.employer_name",
            )

        if not application.additional_user_not_found_info.date_of_hire:
            issues.add_validation_error(
                type=IssueType.required,
                rule=IssueRule.conditional,
                message="date_of_hire is required if additional_user_not_found_info is set",
                field="additional_user_not_found_info.date_of_hire",
            )

        if application.additional_user_not_found_info.currently_employed is None:
            issues.add_validation_error(
                type=IssueType.required,
                rule=IssueRule.conditional,
                message="currently_employed is required if additional_user_not_found_info is set",
                field="additional_user_not_found_info.currently_employed",
            )
        elif (
            application.additional_user_not_found_info.currently_employed is False
            and not application.additional_user_not_found_info.date_of_separation
        ):
            issues.add_validation_error(
                type=IssueType.required,
                rule=IssueRule.conditional,
                message="date_of_separation is required if currently_employed is set",
                field="additional_user_not_found_info.date_of_separation",
            )
        issues += get_app_is_withholding_tax_issues(application)
        issues += get_payments_issues(application)

    # Fields involved in Part 1 of the progressive application
    if application.has_state_id and not application.mass_id:
        issues.add_validation_error(
            type=IssueType.required,
            rule=IssueRule.conditional,
            message="mass_id is required if has_mass_id is set",
            field="mass_id",
        )

    if application.residential_address:
        issues += get_address_issues(application, "residential_address")

    if application.mailing_address:
        issues += get_address_issues(application, "mailing_address")
    elif not application.mailing_address and application.has_mailing_address:
        issues.add_validation_error(
            type=IssueType.required,
            rule=IssueRule.conditional,
            message="mailing_address is required if has_mailing_address is set",
            field="mailing_address",
        )
    if application.leave_reason_id is not None:
        issues += get_leave_related_issues(application)

    if features.occupation_data_collection.enable_occupation_data_collection:
        if (
            application.employment_status_id == EmploymentStatus.EMPLOYED.employment_status_id
            and application.industry_sector_id is None
        ):
            issues.add_validation_error(
                type=IssueType.required,
                rule=IssueRule.conditional,
                message="industry_sector is required if employment_status is Employed",
                field="industry_sector",
            )

    if application.employer_notified:
        if not application.employer_notification_date:
            issues.add_validation_error(
                type=IssueType.required,
                rule=IssueRule.conditional,
                message="employer_notification_date is required for leave_details if employer_notified is set",
                field="leave_details.employer_notification_date",
            )
        elif application.employer_notification_date < date.today() - relativedelta(years=2):
            issues.add_validation_error(
                type=IssueType.minimum,
                rule=IssueRule.conditional,
                message="employer_notification_date year must be within the past 2 years",
                field="leave_details.employer_notification_date",
            )
        elif application.employer_notification_date > date.today():
            issues.add_validation_error(
                type=IssueType.maximum,
                rule=IssueRule.conditional,
                message="employer_notification_date must be today or prior",
                field="leave_details.employer_notification_date",
            )

    if application.work_pattern:
        issues += get_work_pattern_issues(application.work_pattern)

    if (
        application.employment_status_id
        and not application.employer_fein
        and (
            application.employment_status_id
            in [
                EmploymentStatus.EMPLOYED.employment_status_id,
                # TODO (CP-1176): Uncomment the below line to require FEIN for unemployed claimants
                # EmploymentStatus.UNEMPLOYED.employment_status_id,
            ]
        )
    ):
        issues.add_validation_error(
            type=IssueType.required,
            rule=IssueRule.conditional,
            # TODO (CP-1176): Update the error message to include Unemployed
            message="employer_fein is required if employment_status is Employed",
            field="employer_fein",
        )

    if application.has_concurrent_employers is None:
        issues.add_validation_error(
            type=IssueType.required,
            rule=IssueRule.conditional,
            message="has_concurrent_employers is required",
            field="has_concurrent_employers",
        )

    if application.has_concurrent_employers is True:
        if application.hours_worked_per_week_all_employers is None:
            issues.add_validation_error(
                message="hours_worked_per_week_all_employers is required",
                type=IssueType.required,
                field="hours_worked_per_week_all_employers",
            )

    if application.hours_worked_per_week_all_employers is not None:
        hours_worked = application.hours_worked_per_week_all_employers
        # there are 168 hours in a week
        one_week_in_hours = Decimal(168)
        if hours_worked > one_week_in_hours:
            issues.add_validation_error(
                message="hours_worked_per_week_all_employers must be 168 or fewer",
                type=IssueType.maximum,
                field="hours_worked_per_week_all_employers",
            )

    # hours_worked_per_week_all_employers may not be less than or equal to hours of the main employer
    if (
        application.has_concurrent_employers
        and application.hours_worked_per_week_all_employers is not None
        and application.hours_worked_per_week is not None
    ):
        if application.hours_worked_per_week_all_employers <= application.hours_worked_per_week:
            # Customize the validation error for hours_worked_per_week_all_employers
            # The issue type depends on whether FEIN, DBA, or both are present along with their validity
            INVALID_DBA = "EMPLOYER NOT FOUND"
            INVALID_FEIN = "000000000"

            has_valid_dba = (
                application.employer
                and application.employer.employer_dba
                and application.employer.employer_dba != INVALID_DBA
            )
            has_valid_fein = (
                application.employer
                and application.employer.employer_fein
                and application.employer.employer_fein != INVALID_FEIN
            )

            # Convert Decimal to string for the extra dict
            hours_worked_per_week = (
                str(application.hours_worked_per_week) if application.hours_worked_per_week else ""
            )

            if has_valid_dba and has_valid_fein:
                issues.add_validation_error(
                    message="hours_worked_per_week_all_employers must be greater than the hours of the main employer on the claim",
                    rule=IssueRule.conditional,
                    field="hours_worked_per_week_all_employers",
                    type=IssueType.minimum_dba,
                    extra={
                        "employer_dba": (
                            application.employer.employer_dba if application.employer else ""
                        ),
                        "employer_fein": (
                            application.employer.employer_fein if application.employer else ""
                        ),
                        "hours_worked_per_week": hours_worked_per_week,
                    },
                )
            elif has_valid_fein:
                issues.add_validation_error(
                    message="hours_worked_per_week_all_employers must be greater than the hours of the main employer on the claim",
                    rule=IssueRule.conditional,
                    field="hours_worked_per_week_all_employers",
                    type=IssueType.minimum_ein,
                    extra={
                        "employer_fein": (
                            application.employer.employer_fein if application.employer else ""
                        ),
                        "hours_worked_per_week": hours_worked_per_week,
                    },
                )
            else:
                issues.add_validation_error(
                    message="hours_worked_per_week_all_employers must be greater than the hours of the main employer on the claim",
                    rule=IssueRule.conditional,
                    field="hours_worked_per_week_all_employers",
                    type=IssueType.minimum,
                    extra={
                        "hours_worked_per_week": hours_worked_per_week,
                    },
                )

    issues += get_employer_benefits_issues(application)
    issues += get_other_incomes_issues(application)
    issues += get_all_previous_leaves_issues(application, features)

    if require_other_leaves_fields:
        required_other_leaves_fields = [
            "has_employer_benefits",
            "has_other_incomes",
        ]

        required_other_leaves_fields.append("has_previous_leaves")

        # TODO (CP-2455): Move these rules into the "always required" set once they can be required
        # on the complete-application endpoint
        for field in required_other_leaves_fields:
            val = deepgetattr(application, field)
            if val is None:
                issues.add_validation_error(
                    type=IssueType.required,
                    message=f"{field} is required",
                    field=field,
                )

    # Fields involved in Part 3 of the progressive application
    # TODO: (API-515) Document and certification validations can be called here

    return issues.get_errors()


def get_medical_leave_issues(application: Application) -> List[ValidationErrorDetail]:
    issues = ValidationErrorList()
    if application.pregnant_or_recent_birth is None:
        issues.add_validation_error(
            type=IssueType.required,
            rule=IssueRule.conditional,
            message="It is required to indicate if there has been a recent pregnancy or birth when medical leave is requested, regardless of if it is related to the leave request",
            field="leave_details.pregnant_or_recent_birth",
        )
    return issues.get_errors()


QUALIFIER_IDS_FOR_BONDING = [
    LeaveReasonQualifier.NEWBORN.leave_reason_qualifier_id,
    LeaveReasonQualifier.ADOPTION.leave_reason_qualifier_id,
    LeaveReasonQualifier.FOSTER_CARE.leave_reason_qualifier_id,
]


def get_bonding_leave_issues(application: Application) -> List[ValidationErrorDetail]:
    issues = ValidationErrorList()

    # This is here for now to consolidate cross-field validation but can be moved to the openapi.yml if this file becomes too unwieldy
    if application.leave_reason_qualifier_id not in QUALIFIER_IDS_FOR_BONDING:
        issues.add_validation_error(
            type=IssueType.required,
            rule=IssueRule.conditional,
            message="Invalid leave reason qualifier for bonding leave type",
            field="leave_details.reason_qualifier",
        )
    if (
        application.leave_reason_qualifier_id
        == LeaveReasonQualifier.NEWBORN.leave_reason_qualifier_id
    ) and not application.child_birth_date:
        issues.add_validation_error(
            type=IssueType.required,
            rule=IssueRule.conditional,
            message="Child birth date is required for newborn bonding leave",
            field="leave_details.child_birth_date",
        )
    if (
        application.leave_reason_qualifier_id
        in [
            LeaveReasonQualifier.ADOPTION.leave_reason_qualifier_id,
            LeaveReasonQualifier.FOSTER_CARE.leave_reason_qualifier_id,
        ]
    ) and not application.child_placement_date:
        issues.add_validation_error(
            type=IssueType.required,
            rule=IssueRule.conditional,
            message="Child placement date is required for foster or adoption bonding leave",
            field="leave_details.child_placement_date",
        )
    return issues.get_errors()


def get_caring_leave_issues(application: Application) -> List[ValidationErrorDetail]:
    if not application.caring_leave_metadata:
        return []

    issues = ValidationErrorList()
    required_fields = [
        "family_member_first_name",
        "family_member_last_name",
        "family_member_date_of_birth",
        "relationship_to_caregiver",
    ]
    issues += check_required_fields(
        "leave_details.caring_leave_metadata", application.caring_leave_metadata, required_fields
    )

    return issues.get_errors()


def get_payments_issues(application: Application) -> List[ValidationErrorDetail]:
    issues = ValidationErrorList()

    if (
        application.payment_preference is None
        or not application.payment_preference.payment_method_id
    ):
        issues.add_validation_error(
            type=IssueType.required,
            message="Payment method is required",
            field="payment_preference.payment_method",
        )
        return issues.get_errors()

    if application.payment_preference.payment_method_id == PaymentMethod.ACH.payment_method_id:
        if not application.payment_preference.account_number:
            issues.add_validation_error(
                type=IssueType.required,
                rule=IssueRule.conditional,
                message="Account number is required for direct deposit",
                field="payment_preference.account_number",
            )
        if not application.payment_preference.routing_number:
            issues.add_validation_error(
                type=IssueType.required,
                rule=IssueRule.conditional,
                message="Routing number is required for direct deposit",
                field="payment_preference.routing_number",
            )
        elif not validate_routing_number(application.payment_preference.routing_number):
            issues.add_validation_error(
                type=IssueType.checksum,
                message="Routing number is invalid",
                field="payment_preference.routing_number",
            )

        if not application.payment_preference.bank_account_type_id:
            issues.add_validation_error(
                type=IssueType.required,
                rule=IssueRule.conditional,
                message="Account type is required for direct deposit",
                field="payment_preference.bank_account_type",
            )

    return issues.get_errors()


# This maps the required field name in the DB to its equivalent in the API
# Because the DB schema and the API schema differ
ALWAYS_REQUIRED_FIELDS_DB_NAME_TO_API_NAME_MAP = {
    "date_of_birth": "date_of_birth",
    "employer_notified": "leave_details.employer_notified",
    "employment_status_id": "employment_status",
    "first_name": "first_name",
    "has_continuous_leave_periods": "has_continuous_leave_periods",
    "has_intermittent_leave_periods": "has_intermittent_leave_periods",
    "has_mailing_address": "has_mailing_address",
    "has_reduced_schedule_leave_periods": "has_reduced_schedule_leave_periods",
    "has_state_id": "has_state_id",
    "hours_worked_per_week": "hours_worked_per_week",
    "last_name": "last_name",
    "leave_reason_id": "leave_details.reason",
    "phone.phone_number": "phone.phone_number",  # TODO (CP-1467): phone_number here includes the int_code from the request, but int_code will eventually be removed
    "phone.phone_type_id": "phone.phone_type",
    "residential_address": "residential_address",
    "tax_identifier": "tax_identifier",
    "work_pattern.work_pattern_type_id": "work_pattern.work_pattern_type",
    "language_id": "language",
}


def get_always_required_issues(application: Application) -> List[ValidationErrorDetail]:
    issues = ValidationErrorList()
    for field, openapi_field in ALWAYS_REQUIRED_FIELDS_DB_NAME_TO_API_NAME_MAP.items():
        val = deepgetattr(application, field)

        if val is None:
            issues.add_validation_error(
                type=IssueType.required,
                message=f"{openapi_field} is required",
                field=openapi_field,
            )

    return issues.get_errors()


def get_work_pattern_issues(work_pattern: WorkPattern) -> List[ValidationErrorDetail]:
    issues = ValidationErrorList()

    minutes_each_day = [day.minutes or 0 for day in work_pattern.work_pattern_days]

    if sum(minutes_each_day) <= 0:
        issues.add_validation_error(
            type=IssueType.minimum,
            field="work_pattern.work_pattern_days",
            message="Total minutes for a work pattern must be greater than 0",
        )
    else:
        # We only check if all minute fields are set when the total minutes are greater than 0
        # which indicates that all fields are empty or set to 0. For that scenario,
        # validating minimum total minutes for the work pattern is all that's needed.
        for i, day in enumerate(work_pattern.work_pattern_days):
            if day.minutes is None:
                issues.add_validation_error(
                    type=IssueType.required,
                    message=f"work_pattern.work_pattern_days[{i}].minutes is required",
                    field=f"work_pattern.work_pattern_days[{i}].minutes",
                )
            elif day.minutes > 24 * 60:
                issues.add_validation_error(
                    type=IssueType.maximum,
                    message="Total minutes in a work pattern week must be less than a day (1440 minutes)",
                    field=f"work_pattern.work_pattern_days[{i}].minutes",
                )

    return issues.get_errors()


def validate_application_state(
    existing_application: Application, db_session: db.Session
) -> List[ValidationErrorDetail]:
    """
    Utility method for validating an application's state in the entire system is valid
    Currently the only check is one to potentially catch fraud where an SSN is being used
    with multiple email addresses.
    """
    issues = ValidationErrorList()

    # We consider an application potentially fraudulent if another application exists that:
    #   Has the same tax identifier
    #   Has a different user
    #   Has been submitted
    potential_fraud_application = (
        db_session.query(Application).filter(
            Application.tax_identifier_id == existing_application.tax_identifier_id,
            Application.application_id != existing_application.application_id,
            Application.user_id != existing_application.user_id,
            Application.submitted_time.isnot(None),
        )
    ).first()

    application_with_claim = (
        db_session.query(Application).filter(
            Application.tax_identifier_id == existing_application.tax_identifier_id,
            Application.application_id != existing_application.application_id,
            Application.user_id == existing_application.user_id,
            Application.claim_id.isnot(None),
        )
    ).first()
    if potential_fraud_application:
        logger.info(
            "Found potential fraud application",
            extra={
                "application.user_id": existing_application.user_id,
                "application.application_id": existing_application.application_id,
                "prior_application.user_id": potential_fraud_application.user_id,
                "prior_application.application_id": potential_fraud_application.application_id,
                "prior_application.absence_case_id": potential_fraud_application.fineos_absence_id,
            },
        )
    if application_with_claim:
        logger.info(
            "Found application with claim. The user already has a valid claim",
            extra={
                "application.application_id": existing_application.application_id,
                "prior_application.application_id": application_with_claim.application_id,
            },
        )

    # This may be a case of fraud if any application was returned.
    # Add an issue, the portal will display information indicating
    # the user should reach out to the contact center for additional assistance.
    if potential_fraud_application and not application_with_claim:
        issues.add_validation_error(
            message="Request by current user not allowed",
            rule=IssueRule.disallow_attempts,
            type=IssueType.pfml,
        )

        logger.warning(
            "Fraud detected. Multiple applications found for specified Tax id",
            extra={
                "application.user_id": existing_application.user_id,
                "application.application_id": existing_application.application_id,
                "prior_application.user_id": potential_fraud_application.user_id,
                "prior_application.application_id": potential_fraud_application.application_id,
                "prior_application.absence_case_id": potential_fraud_application.fineos_absence_id,
            },
        )
    return issues.get_errors()


def get_app_is_withholding_tax_issues(application: Application) -> List[ValidationErrorDetail]:
    """
    Validates that tax withholding preference has been collected.
    Called from application complete endpoint and application submit endpoint
    when collecting additional user not found info
    """
    issues = ValidationErrorList()

    if not isinstance(application.is_withholding_tax, bool):
        issues.add_validation_error(
            type=IssueType.required,
            message="Tax withholding preference is required",
            field="is_withholding_tax",
        )
    return issues.get_errors()


def get_app_complete_payments_issues(application: Application) -> List[ValidationErrorDetail]:
    """Validate payments related selections are complete. Called from application complete endpoint."""
    issues = ValidationErrorList()

    if not application.has_submitted_payment_preference:
        issues.add_validation_error(
            message="Payment preference is required",
            type=IssueType.required,
            field="payment_method",
        )

    return issues.get_errors()


def get_documents_issues(
    application: Application, db_session: db.Session, certificate_document_deferred: bool
) -> List[ValidationErrorDetail]:
    """Takes in an application and outputs any validation issues with upload documents (Part 3)"""
    issues = ValidationErrorList()

    # ID document verification
    has_id_doc = documents_service.has_type_of_fineos_document(
        ID_DOC_TYPES, db_session, application
    )
    # For the IDV pilot, due to constraints of using the existing /rmv-check API to communicate MMG IDV status
    #   MMG Verified applicants that have a State ID do not need to upload an ID document
    #   MMG Verified applicants that do not have a State ID will need to upload an ID document
    #   Applications that are not part of the pilot will have mmg_idv_status_id = NULL
    is_id_doc_required = (
        application.has_state_id is False
        and application.mmg_idv_status_id == MmgIdvStatus.VERIFIED.id
    ) or application.mmg_idv_status_id != MmgIdvStatus.VERIFIED.id
    if not has_id_doc and is_id_doc_required:
        issues.add_validation_error(
            type=IssueType.required, message="An identification document is required"
        )

    # Cert document verification
    # TODO (PFMLPB-19540): update this to require cert doc for all leave reasons if
    #   not certificate_document_deferred (not just SERIOUS_HEALTH_CONDITION_EMPLOYEE)
    #   + update the list of cert doc types we are checking to be broader
    need_cert_doc_validation = (
        not certificate_document_deferred
        and application.leave_reason_id
        == LeaveReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.leave_reason_id
    )

    if need_cert_doc_validation:
        has_cert_doc = documents_service.has_type_of_fineos_document(
            SERIOUS_HEALTH_CONDITION_CERTIFICATION_DOC_TYPES, db_session, application
        )
        if not has_cert_doc:
            issues.add_validation_error(
                type=IssueType.required, message="A certification document is required"
            )

    return issues.get_errors()
