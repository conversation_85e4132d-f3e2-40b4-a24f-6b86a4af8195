from datetime import date
from itertools import chain
from typing import List, Optional, Sequence

import massgov.pfml.util.logging
from massgov.pfml.api.models.applications.common import LeaveReason as LeaveReasonApi
from massgov.pfml.api.models.claims.common import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    EmployerClaimReview,
    EmployerClaimReviewRequestEmployerBenefit,
    EmployerClaimReviewRequestPreviousLeave,
    HoursWorkedPerWeek,
)
from massgov.pfml.api.services.claims import get_previous_absence_period_from_claim
from massgov.pfml.api.validation.exceptions import (
    IssueType,
    ValidationErrorDetail,
    ValidationErrorList,
)
from massgov.pfml.db.models.absences import AbsencePeriod
from massgov.pfml.db.models.employees import Claim

logger = massgov.pfml.util.logging.get_logger(__name__)

# there are 168 hours in a week
MAX_HOURS_WORKED_PER_WEEK = 168


def get_employer_claim_review_issues(
    claim_review: Employer<PERSON>laimReview,
    claim: Optional[Claim] = None,
) -> List[ValidationErrorDetail]:
    """Takes in an employer claim review request and outputs any validation issues."""
    issues: List[ValidationErrorDetail] = []

    issues += get_hours_worked_per_week_issues(claim_review.hours_worked_per_week)
    issues += get_previous_leaves_issues(claim_review.previous_leaves)
    issues += get_employer_benefits_issues(claim_review.employer_benefits)
    issues += get_fraud_check_issues(claim_review.fraud)

    if not claim:
        return issues

    has_amendments = claim_review.has_amendments
    previous_leaves = claim_review.previous_leaves

    issues += get_previous_leaves_overlapping_issues(claim, previous_leaves, has_amendments)

    if has_amendments:
        issues += get_employer_claim_review_duplicate_issues(claim, claim_review)

    return issues


def get_employer_claim_review_duplicate_issues(
    claim: Claim,
    claim_review: EmployerClaimReview,
) -> List[ValidationErrorDetail]:
    """Check for duplicate dates in claims existing previous leaves or employer benefits"""
    return list(
        chain(
            get_previous_leaves_duplicate_issues(claim, claim_review.previous_leaves),
            get_employer_benefits_duplicate_issues(claim, claim_review.employer_benefits),
        )
    )


def get_previous_leaves_duplicate_issues(
    claim: Claim,
    previous_leaves: Sequence[EmployerClaimReviewRequestPreviousLeave],
) -> List[ValidationErrorDetail]:
    issues = ValidationErrorList()

    if claim.application is None or not claim.application.previous_leaves or not previous_leaves:
        return []

    existing_previous_leaves = extract_previous_leaves_field_set(claim)

    for index, leave in enumerate(previous_leaves, 0):
        if (
            previous_leaves[index].employer_changes == EmployerChanges.unchanged
            or previous_leaves[index].employer_changes == EmployerChanges.removed
        ):
            continue

        # We do not expect the leave_reason to be unset
        # This is to satisfy the linters.
        if not leave.leave_reason:
            continue

        leave_start_date = leave.leave_start_date
        leave_end_date = leave.leave_end_date
        leave_reason = leave.leave_reason.value

        if (leave_reason, leave_start_date, leave_end_date) in existing_previous_leaves:
            issues.add_validation_error(
                message="Previous leave date already exists",
                type=IssueType.duplicate,
                field=f"previous_leaves[{index}].leave_start_date",
                extra={
                    "leave_start_date": (leave_start_date.isoformat() if leave_start_date else ""),
                    "leave_end_date": (leave_end_date.isoformat() if leave_end_date else ""),
                    "leave_reason": leave_reason,
                },
            )

    return issues.get_errors()


def get_employer_benefits_duplicate_issues(
    claim: Claim,
    employer_benefits: Sequence[EmployerClaimReviewRequestEmployerBenefit],
) -> List[ValidationErrorDetail]:
    issues = ValidationErrorList()

    if (
        claim.application is None
        or not claim.application.employer_benefits
        or not employer_benefits
    ):
        return []

    existing_employer_benefits = [
        (
            (
                existing_employer_benefit.benefit_type.employer_benefit_type_description
                if existing_employer_benefit.benefit_type
                else ""
            ),
            existing_employer_benefit.benefit_start_date,
            existing_employer_benefit.benefit_end_date,
        )
        for existing_employer_benefit in claim.application.employer_benefits
    ]

    for index, benefit in enumerate(employer_benefits, 0):
        if (
            employer_benefits[index].employer_changes == EmployerChanges.unchanged
            or employer_benefits[index].employer_changes == EmployerChanges.removed
        ):
            continue

        # We do not expect the benefit_type to be unset
        # This is to satisfy the linters.
        if not benefit.benefit_type:
            continue

        benefit_start_date = benefit.benefit_start_date
        benefit_end_date = benefit.benefit_end_date
        benefit_type = benefit.benefit_type.value

        if (benefit_type, benefit_start_date, benefit_end_date) in existing_employer_benefits:
            issues.add_validation_error(
                message="Employer benefit date already exists",
                type=IssueType.duplicate,
                field=f"employer_benefits[{index}].benefit_start_date",
                extra={
                    "benefit_start_date": (
                        benefit_start_date.isoformat() if benefit_start_date else ""
                    ),
                    "benefit_end_date": (benefit_end_date.isoformat() if benefit_end_date else ""),
                    "benefit_type": benefit_type,
                },
            )

    return issues.get_errors()


def get_previous_leaves_overlapping_issues(
    claim: Claim,
    previous_leaves: Sequence[EmployerClaimReviewRequestPreviousLeave],
    has_amendments: bool,
) -> List[ValidationErrorDetail]:
    """
    This function validates the previous leaves from an employer claim review request.

    -   Checks if the previous leaves date is not within the last 52 weeks of claim's earliest start date
    -   Checks if any previous leaves overlap with previously approved PFML leaves for the claimant and employer
    """

    issues = ValidationErrorList()

    if has_amendments is False or not previous_leaves:
        return []

    previous_absence_periods: List[AbsencePeriod] = get_previous_absence_period_from_claim(claim)

    for index, leave in enumerate(previous_leaves, 0):
        if (
            previous_leaves[index].employer_changes == EmployerChanges.unchanged
            or previous_leaves[index].employer_changes == EmployerChanges.removed
        ):
            continue

        leave_start_date = leave.leave_start_date
        leave_end_date = leave.leave_end_date

        if leave_start_date is None or leave_end_date is None:
            continue

        # Checks if the previous leaves date is not within the last 52 weeks of claim's earliest start date
        issues += get_date_range_issues(
            leave_start_date,
            f"previous_leaves[{index}].leave_start_date",
            leave_end_date,
            claim,
        )

        # Check if any previous leave entered overlaps with any previously approved PFML leaves
        for absence_period in previous_absence_periods:
            start_date = absence_period.start_date
            end_date = absence_period.end_date

            if start_date is None or end_date is None:
                continue

            if leave_start_date <= end_date and leave_end_date >= start_date:
                issues.add_validation_error(
                    message=(
                        f"previous_leaves[{index}].leave_start_date dates entered overlaps with leave from "
                        f"{start_date.isoformat()} to "
                        f"{end_date.isoformat()}"
                    ),
                    type=IssueType.conflicting,
                    field=f"previous_leaves[{index}].leave_start_date",
                    extra={
                        "start_date": start_date.isoformat() if start_date else "",
                        "end_date": end_date.isoformat() if end_date else "",
                    },
                )
                # only return one conflicting leave per field
                break

    return issues.get_errors()


def get_hours_worked_per_week_issues(
    hours_worked_per_week: HoursWorkedPerWeek,
) -> List[ValidationErrorDetail]:
    issues = ValidationErrorList()

    hours_worked = hours_worked_per_week.hours_worked

    if hours_worked <= 0:
        issues.add_validation_error(
            message="hours_worked_per_week must be greater than 0",
            type=IssueType.minimum,
            field="hours_worked_per_week",
        )
        return issues.get_errors()

    if hours_worked > MAX_HOURS_WORKED_PER_WEEK:

        issues.add_validation_error(
            message="hours_worked_per_week must be 168 or fewer",
            type=IssueType.maximum,
            field="hours_worked_per_week",
        )
        return issues.get_errors()

    return issues.get_errors()


def get_previous_leaves_issues(
    previous_leaves: Sequence[EmployerClaimReviewRequestPreviousLeave],
) -> List[ValidationErrorDetail]:
    error_list = ValidationErrorList()

    if not previous_leaves:
        return error_list.get_errors()

    for index, previous_leave in enumerate(previous_leaves):
        # FINEOS does not require that leave_start_date or leave_end_date is populated.
        # The existence of one also does not imply the existence of the other.
        if previous_leave.leave_start_date is None:
            continue

        if previous_leave.leave_end_date is None:
            continue

        if previous_leave.leave_start_date > previous_leave.leave_end_date:
            error_list.add_validation_error(
                message="leave_end_date cannot be earlier than leave_start_date",
                type=IssueType.minimum,
                field=f"previous_leaves[{index}].leave_end_date",
            )

    return error_list.get_errors()


def get_employer_benefits_issues(
    employer_benefits: Sequence[EmployerClaimReviewRequestEmployerBenefit],
) -> List[ValidationErrorDetail]:
    error_list = ValidationErrorList()

    if not employer_benefits:
        return error_list.get_errors()

    for index, employer_benefit in enumerate(employer_benefits):
        # FINEOS does not require that benefit_start_date or benefit_end_date is populated.
        # The existence of one also does not imply the existence of the other.
        if employer_benefit.benefit_start_date is None or employer_benefit.benefit_end_date is None:
            continue

        if employer_benefit.benefit_start_date > employer_benefit.benefit_end_date:
            error_list.add_validation_error(
                message="benefit_end_date cannot be earlier than benefit_start_date",
                type=IssueType.minimum,
                field=f"employer_benefits[{index}].benefit_end_date",
            )

    return error_list.get_errors()


def get_fraud_check_issues(fraud: Optional[str]) -> List[ValidationErrorDetail]:
    error_list = ValidationErrorList()

    if fraud is None:
        error_list.add_validation_error(
            type=IssueType.required,
            message="fraud is required",
            field="fraud",
        )

    return error_list.get_errors()


def get_date_range_issues(
    start_date: Optional[date],
    start_date_path: str,
    end_date: Optional[date],
    claim: Claim,
) -> List[ValidationErrorDetail]:
    """
    Checks if a start and end date are valid, if set. start_date is valid if it is greater than or equal to
    minimum_date. end_date is valid if it is greater than or equal to minimum_date and start_date. A date is
    not checked if it isn't set.
    """
    issues = ValidationErrorList()

    if not start_date or not end_date:
        return issues.get_errors()

    if not claim.prior_year or not claim.absence_periods_earliest_start_date:
        return issues.get_errors()

    if start_date < claim.prior_year or end_date < claim.prior_year:
        issues.add_validation_error(
            type=IssueType.minimum,
            message=(
                f"Only report previous leave that occurs between {claim.prior_year.isoformat()} "
                f"and {claim.absence_periods_earliest_start_date.isoformat()}"
            ),
            field=f"{start_date_path}",
            extra={
                "previous_year_start": claim.prior_year.isoformat(),
                "previous_year_end": claim.absence_periods_earliest_start_date.isoformat(),
            },
        )

    return issues.get_errors()


def extract_previous_leaves_field_set(claim: Claim) -> list[tuple[str, date | None, date | None]]:
    previous_leaves: list[tuple[str, date | None, date | None]] = []

    if claim.application is None or not claim.application.previous_leaves:
        return previous_leaves

    for previous_leave in claim.application.previous_leaves:
        leave_start_date = previous_leave.leave_start_date
        leave_end_date = previous_leave.leave_end_date

        # We use the application leave reason as a fallback when previous_leave.leave_reason is null,
        # signifying that it is the same leave reason as the application.
        if previous_leave.leave_reason:
            leave_reason = previous_leave.leave_reason.previous_leave_qualifying_reason_description
        elif claim.application.leave_reason:
            leave_reason = LeaveReasonApi.to_previous_leave_qualifying_reason(
                LeaveReasonApi.validate_type(
                    claim.application.leave_reason.leave_reason_description
                )
            ).value
        else:
            leave_reason = ""

        previous_leaves.append((leave_reason, leave_start_date, leave_end_date))

    return previous_leaves
