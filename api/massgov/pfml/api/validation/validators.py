#
# Custom validation implementations to support custom API response formats
#
import re
from typing import Any, Callable, Optional

import jsonschema
from connexion.json_schema import Draft4RequestValidator, Draft4ResponseValidator
from connexion.utils import is_null
from connexion.validators.form_data import MultiPartFormDataValidator
from connexion.validators.json import JSONRequestBodyValidator, JSONResponseBodyValidator
from connexion.validators.parameter import ParameterValidator
from starlette.requests import Request

import massgov.pfml.util.logging as logging
import massgov.pfml.util.newrelic.events as newrelic_util
from massgov.pfml.api.validation.exceptions import (
    IssueType,
    ValidationErrorDetail,
    ValidationErrorList,
    ValidationException,
)

logger = logging.get_logger(__name__)


# via https://python-jsonschema.readthedocs.io/en/stable/faq/#why-doesn-t-my-schema-s-default-property-set-the-default-on-my-instance
def extend_with_set_default(validator_class):
    validate_properties = validator_class.VALIDATORS["properties"]

    def set_defaults(validator, properties, instance, schema):
        for property, subschema in properties.items():
            if "default" in subschema:
                instance.setdefault(property, subschema["default"])

        for error in validate_properties(validator, properties, instance, schema):
            yield error

    return jsonschema.validators.extend(validator_class, {"properties": set_defaults})


DefaultsEnforcingDraft4RequestValidator = extend_with_set_default(Draft4RequestValidator)
DefaultsEnforcingDraft4ResponseValidator = extend_with_set_default(Draft4ResponseValidator)


def _get_error_value(error: ValidationErrorDetail) -> str | None:
    """
    Extracts the value from a validation error, handling cases where the value is None.

    There should not be PII in the ValidationErrorDetail value, but we're being deliberate about which
    values we extract to avoid surfacing any sensitive information.
    """
    validator = getattr(error, "validator", None)

    if validator in ("type", "enum"):
        return getattr(error, "instance", None)

    # Get date values from format validation errors.
    if validator == "format" and getattr(error, "validator_value", None) in (
        "maskable_date",
        "date",
    ):
        return getattr(error, "instance", None)

    return None


def validate_schema_util(validator_decorator, data, error_message):
    if isinstance(validator_decorator, CustomResponseBodyValidator):
        errors = list(validator_decorator.validator.iter_errors(data))
    else:
        errors = list(validator_decorator._validator.iter_errors(data))

    if errors:
        error_list = ValidationErrorList()
        for error in errors:
            # Fix an error where items in error.path are ints. Convert to strings.
            field_path = list(map(lambda x: str(x), list(error.path)))
            error_value = _get_error_value(error)
            error_list.add_validation_error(
                message=error.message,
                type=error.validator,
                rule=error.validator_value,
                field=".".join(field_path) if field_path else "",
                value=type(error_value).__name__ if error.validator == "type" else error_value,
            )

        invalid_data_payload = data
        if isinstance(data, dict) and "data" in data:
            invalid_data_payload = data["data"]
        raise ValidationException(
            errors=error_list.get_errors(), message=error_message, data=invalid_data_payload
        )


class CustomParameterValidator(ParameterValidator):
    def __init__(self, parameters, uri_parser, strict_validation=False, security_query_params=None):
        super(CustomParameterValidator, self).__init__(
            parameters, uri_parser, strict_validation, security_query_params
        )

    def validate(self, scope):
        request = Request(scope)
        return self.validate_query_parameter_list(request, self.security_query_params)

    def validate_query_parameter_list(self, request, security_params=None):
        # In multipart/form-data requests in the OpenAPI spec requestBody, the payload is sent as formBody elements. Connexion tries to validate these as parameters and will fail since those properties are included as part of the request body. Example: https://swagger.io/docs/specification/describing-request-body/multipart-requests/
        # Below we check if the requestBody is multipart/form-data and skip parameter validation.The validation will be handled by RequestBodyValidator.validate_formdata_parameter_list (https://github.com/zalando/connexion/blob/master/connexion/decorators/validation.py#L125)
        is_multi_part_form = request.headers.get("Content-Type") and request.headers.get(
            "Content-Type"
        ).startswith("multipart/form-data")
        if is_multi_part_form:
            return None

        return ParameterValidator.validate_query_parameter_list(self, request)


class CustomRequestBodyValidator(JSONRequestBodyValidator):
    def __init__(self, *args, **kwargs):
        super(CustomRequestBodyValidator, self).__init__(
            *args, validator=DefaultsEnforcingDraft4RequestValidator, **kwargs
        )

    def _validate(self, body):
        if not is_null(body):
            validate_schema_util(self, body, "Request Validation Error")
            return

        if self._nullable or not self._required:
            return None

        else:
            errors = [
                ValidationErrorDetail(
                    field="", message="Missing request body", type=IssueType.required
                )
            ]

            raise ValidationException(errors=errors, message="Request Validation Error", data=body)


class CustomMultiPartFormDataValidator(MultiPartFormDataValidator):
    def __init__(
        self,
        *,
        schema: dict,
        required: bool = False,
        nullable: bool = False,
        encoding: str,
        strict_validation: bool,
        uri_parser: Any
    ):
        self._uri_parser = uri_parser
        self._schema = schema
        self._required = required
        self._nullable = nullable
        self._encoding = encoding
        self._strict_validation = strict_validation

    def _validate(self, body):
        self._validate_params_strictly(body)

    def _validate_params_strictly(self, data):
        validate_schema_util(self, data, "Request Validation Error")


class CustomResponseBodyValidator(JSONResponseBodyValidator):
    def __init__(self, *args, **kwargs):
        super(CustomResponseBodyValidator, self).__init__(*args, **kwargs)

    def validate_schema(self, data):
        validate_schema_util(self, data, "Response Validation Error")


def log_validation_error(
    validation_exception: ValidationException,
    error: ValidationErrorDetail,
    unexpected_error_check_func: Optional[Callable[[ValidationErrorDetail], bool]] = None,
    only_warn: bool = False,
) -> None:
    # Create a readable message for the individual error.
    # Do not use the error's actual message since it may include PII.
    #
    # Note that the field is modified in the message so array-based fields can be aggregated, e.g.
    #
    #   error.field: data.0.absence_periods.12.reason_qualifier_two
    #   aggregated_field: data.<NUM>.absence_periods.<NUM>.reason_qualifier_two
    #
    aggregated_field = error.field

    if aggregated_field:
        aggregated_field = re.sub(r"\.\d+\.", ".<NUM>.", aggregated_field)

    message = "%s (field: %s, type: %s, rule: %s)" % (
        validation_exception.message,
        aggregated_field,
        error.type,
        error.rule,
    )

    log_attributes = {
        "error.class": "ValidationException",
        "error.type": error.type,
        "error.rule": error.rule,
        "error.field": error.field,
        "error.value": error.value,
    }
    if unexpected_error_check_func and not unexpected_error_check_func(error):
        logger.info(message, extra=log_attributes)
    else:
        # Log explicit errors in the case of unexpected validation errors.
        newrelic_util.log_and_capture_exception(message, extra=log_attributes, only_warn=only_warn)


class CustomResponseValidator(JSONResponseBodyValidator):
    response_validation = False

    @classmethod
    def enable_response_validation(cls):
        cls.response_validation = True

    def validate_helper(self, data):
        v = CustomResponseBodyValidator(
            scope=self._scope, schema=self._schema, encoding=self._encoding
        )
        v.validate_schema(data)

    # def validate_response(self, data, status_code, headers, url):
    def _validate(self, body):
        # Only validate json responses
        if self._get_content_type() != "application/json":
            return True

        if self._scope.get("path").endswith("/status"):
            return True

        try:
            self.validate_helper(body)
        except ValidationException as validation_exception:
            if CustomResponseValidator.response_validation:
                raise validation_exception
            for error in validation_exception.errors:
                # For Response Validation exceptions, we want to log as warnings rather than errors
                log_validation_error(validation_exception, error, only_warn=True)
            return True

    def _get_content_type(self):
        headers = self._scope.get("headers")
        content_type = None

        for header in headers:
            (key, value) = header

            if str(key).find("content-type") > -1 and str(key).startswith("b'"):
                content = re.search(r"b'([^\"]*)'", str(value))
                content_type = content.group(1) if content else None

        return content_type
