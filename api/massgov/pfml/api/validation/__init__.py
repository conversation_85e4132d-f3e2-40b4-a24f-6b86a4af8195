#
# Utility functions to support custom validation handlers on connexion
#
from typing import Union

import botocore.exceptions
import pydantic
import starlette.exceptions
from connexion import <PERSON>laskApp
from connexion.datastructures import MediaTypeDict
from connexion.exceptions import BadRequestProblem, ExtraParameterProblem, ProblemException
from connexion.lifecycle import ConnexionRequest, ConnexionResponse
from connexion.validators.parameter import ParameterValidator
from sqlalchemy.exc import OperationalError
from werkzeug.exceptions import (
    BadRequest,
    Forbidden,
    HTTPException,
    InternalServerError,
    NotFound,
    ServiceUnavailable,
    Unauthorized,
)

import massgov.pfml.api.util.response as response_util
import massgov.pfml.util.logging as logging
from massgov.pfml.api.validation.exceptions import ValidationErrorDetail, ValidationException
from massgov.pfml.api.validation.validators import (
    CustomMultiPartFormDataValidator,
    CustomRequestBodyValidator,
    CustomResponseValidator,
    log_validation_error,
)

logger = logging.get_logger(__name__)

UNEXPECTED_ERROR_TYPES = {"enum", "type"}


def is_unexpected_validation_error(error: ValidationErrorDetail) -> bool:
    return (
        error.type in UNEXPECTED_ERROR_TYPES
        or error.type.startswith("type_error")
        or error.type.startswith("value_error")
    )


def get_metadata_for_response(request: ConnexionRequest) -> response_util.MetaData:
    starlette_request = ConnexionRequest.from_starlette_request(request)
    return response_util.MetaData(
        method=starlette_request.get("method"), resource=starlette_request.get("path")
    )


def db_operational_error_handler(
    request: ConnexionRequest, operational_error: OperationalError
) -> ConnexionResponse:
    log_attr = {"error.class": "sqlalchemy.exc.OperationalError"}
    logger.warning(operational_error.detail, extra=log_attr, exc_info=True)
    return response_util.error_response(
        status_code=ServiceUnavailable,
        message="database service unavailable",
        errors=[],
        meta=get_metadata_for_response(request),
    ).to_api_response()


def validation_request_handler(
    request: ConnexionRequest, validation_exception: ValidationException
) -> ConnexionResponse:
    for error in validation_exception.errors:
        log_validation_error(validation_exception, error, is_unexpected_validation_error)

    return response_util.error_response(
        status_code=BadRequest,
        message=validation_exception.message,
        errors=validation_exception.errors,
        data=validation_exception.data,
        meta=get_metadata_for_response(request),
    ).to_api_response()


def connexion_400_handler(
    request: ConnexionRequest, exception: ProblemException
) -> ConnexionResponse:
    # Ensure that we are still logging 400 info, since
    # we are now ignoring most of these errors in New Relic.
    # _do not log response data_, only the machine-readable errors and
    # messages which should definitely not have sensitive info.
    # Connexion 3.1. upgrade - there is no access to g object in the handler
    # we are executing same code inside common_error_handler function
    # https://github.com/spec-first/connexion/blob/a930303faa9e10603cdd820c497303b2e52d8253/connexion/middleware/exceptions.py#L103

    logger.info(exception.detail, extra={"error.class": exception.type})
    http_exception = HTTPException(description=exception.detail)
    http_exception.code = exception.status_code
    return response_util.error_response(
        status_code=http_exception,
        message=exception.detail,
        errors=[],
        meta=get_metadata_for_response(request),
    ).to_api_response()


def http_exception_handler(
    request: ConnexionRequest, exception: HTTPException
) -> ConnexionResponse:
    if isinstance(exception, starlette.exceptions.HTTPException):
        exception.code = exception.status_code
        exception.description = exception.detail
    return response_util.error_response(
        status_code=exception,
        message=str(exception.description),
        errors=[],
        meta=get_metadata_for_response(request),
    ).to_api_response()


def internal_server_error_handler(
    request: ConnexionRequest, error: InternalServerError
) -> ConnexionResponse:
    exception = error.original_exception if hasattr(error, "original_exception") else error
    starlette_request = ConnexionRequest.from_starlette_request(request)
    logger.exception(
        str(exception),
        extra={"error.class": type(exception).__name__, "path": starlette_request.get("path")},
    )

    # sending always original error 'cause original_exception does not have a 'description' member
    return http_exception_handler(request, error)


def handle_aws_connection_error(
    request: ConnexionRequest,
    error: Union[botocore.exceptions.ConnectionError, botocore.exceptions.HTTPClientError],
) -> ConnexionResponse:
    return response_util.error_response(
        status_code=ServiceUnavailable,
        message="Connection was closed before receiving a valid response from endpoint.",
        errors=[],
        meta=get_metadata_for_response(request),
    ).to_api_response()


def add_error_handlers_to_app(connexion_app: FlaskApp) -> None:
    connexion_app.add_error_handler(ValidationException, validation_request_handler)
    connexion_app.add_error_handler(BadRequestProblem, connexion_400_handler)
    connexion_app.add_error_handler(ExtraParameterProblem, connexion_400_handler)

    # Issues during request parsing are caught and converted to ValidationException
    # by the helper ( parse_request_body() ), so the below handler is for all
    # non-request body Pydantic parsing that occurs throughout the app
    connexion_app.add_error_handler(pydantic.ValidationError, handle_pydantic_validation_error)

    connexion_app.add_error_handler(
        botocore.exceptions.HTTPClientError, handle_aws_connection_error
    )
    connexion_app.add_error_handler(
        botocore.exceptions.ConnectionError, handle_aws_connection_error
    )
    connexion_app.add_error_handler(OperationalError, db_operational_error_handler)
    # These are all handled with the same generic exception handler to make them uniform in structure.
    connexion_app.add_error_handler(NotFound.code, http_exception_handler)
    connexion_app.add_error_handler(HTTPException, http_exception_handler)
    connexion_app.add_error_handler(Forbidden.code, http_exception_handler)
    connexion_app.add_error_handler(Unauthorized.code, http_exception_handler)

    # Override the default internal server error handler to prevent Flask
    # from using logging.error with a generic message. We want to log
    # the original exception.
    #
    # We handle all 500s here but only expect InternalServerError instances,
    # as indicated by the documentation. Calling out InterrnalServerError explicitly
    # here would not override the default internal server error handler.
    #
    connexion_app.add_error_handler(500, internal_server_error_handler)

    # with connexion3.0 update, all the werkzeug HTTPException are handled by connexion as
    # starlette.exceptions.HTTPException, so we need to add handler for starlette exceptions
    connexion_app.add_error_handler(starlette.exceptions.HTTPException, http_exception_handler)


# with custom validators getting error: ERROR    TypeError("'type' object is not subscriptable")
# need to fix custom validators
def get_custom_validator_map(enable_response_validation):
    validator_map = {
        "body": MediaTypeDict(
            {
                "*/*json": CustomRequestBodyValidator,
                "application/x-www-form-urlencoded": CustomMultiPartFormDataValidator,  # FormDataValidator,
                "multipart/form-data": CustomMultiPartFormDataValidator,  # MultiPartFormDataValidator,
            }
        ),
        "response": MediaTypeDict(
            {
                "*/*json": CustomResponseValidator,
                "text/plain": CustomResponseValidator,
            }
        ),
        "parameter": ParameterValidator,
    }

    if enable_response_validation:
        CustomResponseValidator.enable_response_validation()
    return validator_map


def handle_pydantic_validation_error(
    request: ConnexionRequest, exception: pydantic.ValidationError
) -> ConnexionResponse:
    return internal_server_error_handler(
        request, convert_pydantic_error_to_internal_server_error(exception)
    )


# Some pydantic errors aren't of a format we like
pydantic_error_type_map = {"value_error.date": "date"}


def convert_pydantic_error_to_internal_server_error(
    exception: pydantic.ValidationError,
) -> InternalServerError:
    return InternalServerError(description=str(exception), original_exception=exception)


# Note that pydantic errors are reported in New Relic
# before being converted into validation exception responses.
def convert_pydantic_error_to_validation_exception(
    exception: pydantic.ValidationError,
) -> ValidationException:
    errors = []

    for e in exception.errors():
        err_type = e["type"]
        err_ctx = e["ctx"] if "ctx" in e else {}

        if err_type in pydantic_error_type_map:
            err_type = pydantic_error_type_map[err_type]

        # e["loc"] is a tuple of identifying where in the schema the error occurred.
        # if the object is nested, the loc tuple will contain the root element
        # eg. ('__root__', 'contact_phone', 'phone_number'). omit the root element
        # if exists
        err_loc = tuple(v for v in e["loc"] if v != "__root__")
        err_field = err_loc[0]
        err_message = e["msg"]

        errors.append(
            ValidationErrorDetail(
                type=err_ctx["type"] if "type" in err_ctx else err_type,  # type: ignore
                message=(
                    err_ctx["message"]
                    if "message" in err_ctx
                    else f'Error in field: "{err_field}". {err_message.capitalize()}.'
                ),
                rule=err_ctx["rule"] if "rule" in err_ctx else None,
                field=".".join(str(loc) for loc in err_loc),
            )
        )

    return ValidationException(errors=errors, message="Request Validation Error", data={})
