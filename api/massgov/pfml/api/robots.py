from flask import Response

from massgov.pfml.api.authorization.flask import skip_authorization


def init(app):

    # Serve the robots.txt file
    @app.route("/robots.txt")
    @skip_authorization
    def robots():
        # Define the contents of the robots.txt
        robots_txt = """
        # https://www.robotstxt.org/robotstxt.html
        User-agent: *
        Disallow: /
        """

        # Return robots.txt as plain text
        return Response(robots_txt, content_type="text/plain")
