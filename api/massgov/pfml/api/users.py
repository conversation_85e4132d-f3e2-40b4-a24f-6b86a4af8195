import base64
from typing import Any, List, Union
from uuid import UUID

import flask
from connexion.lifecycle import ConnexionResponse
from flask import Response
from werkzeug.exceptions import BadRequest, Forbidden

import massgov.pfml.api.app as app
import massgov.pfml.api.util.response as response_util
import massgov.pfml.util.logging
import massgov.pfml.util.pydantic as pydantic_util
from massgov.pfml.api.authentication import get_bearer_token_from_header
from massgov.pfml.api.authorization.exceptions import NotAuthorizedForAccess
from massgov.pfml.api.authorization.flask import EDIT, READ, ensure
from massgov.pfml.api.models.documents.responses import DocumentResponse
from massgov.pfml.api.models.users.requests import (
    UserConvertEmployerRequest,
    UserProfileCheckForUpdatesRequest,
    UserProfileUpdateRequest,
    UserUpdateRequest,
)
from massgov.pfml.api.models.users.responses import UserProfileCheckForUpdatesResponse, UserResponse
from massgov.pfml.api.services.fineos_actions import download_customer_document
from massgov.pfml.api.services.leave_admins import process_pending_leave_admin_invites
from massgov.pfml.api.services.users import handle_user_patch_fineos_side_effects, update_user
from massgov.pfml.api.util.request import parse_request_body
from massgov.pfml.api.validation.exceptions import IssueType, ValidationErrorDetail
from massgov.pfml.api.validation.user_rules import (
    get_users_convert_employer_issues,
    get_users_patch_issues,
)
from massgov.pfml.db.lookup_data.documents import DocumentType
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.employees import Employer, User
from massgov.pfml.fineos.exception import FINEOSClientError
from massgov.pfml.my_mass_gov import mmg_profile
from massgov.pfml.my_mass_gov.client import get_client as get_mmg_api_client
from massgov.pfml.my_mass_gov.client.models import PhoneResponseDto as MmgPhone
from massgov.pfml.my_mass_gov.client.models.spec import Priority as MmgPhonePriority
from massgov.pfml.my_mass_gov.client.models.spec import ProfileRequestDto
from massgov.pfml.services.documents.get_document_service import GetDocumentService
from massgov.pfml.util.sqlalchemy import get_or_404
from massgov.pfml.util.users import add_leave_admin_and_role

logger = massgov.pfml.util.logging.get_logger(__name__)


def users_get(user_id):
    with app.db_session() as db_session:
        u = get_or_404(db_session, User, user_id)
        data = UserResponse.from_orm(u).dict()
    ensure(READ, u)
    return response_util.success_response(
        message="Successfully retrieved user", data=data
    ).to_api_response()


def users_convert_employer(user_id, body):
    """This endpoint converts the user specified by the user_id to an employer"""
    request_body = parse_request_body(UserConvertEmployerRequest, body)

    with app.db_session() as db_session:
        updated_user = get_or_404(db_session, User, user_id)
        ensure(EDIT, updated_user)

        employer = (
            db_session.query(Employer)
            .filter(Employer.employer_fein == request_body.employer_fein)
            .one_or_none()
        )
        if not employer or not employer.fineos_employer_id:
            logger.info("users_convert failure - Employer not found!")
            return response_util.error_response(
                status_code=BadRequest,
                message="Invalid FEIN",
                errors=[
                    ValidationErrorDetail(
                        type=IssueType.require_employer,
                        message="Invalid FEIN",
                        field="employer_fein",
                    )
                ],
                data={},
            ).to_api_response()

        # verify that we can convert the account
        issues = get_users_convert_employer_issues(updated_user, db_session)
        if issues:
            logger.info(
                "users_convert_employer failure - Couldn't convert user to employer account",
                extra={"employer_id": employer.employer_id},
            )
            return response_util.error_response(
                status_code=BadRequest,
                message="Couldn't convert user to employer account!",
                errors=issues,
                data={},
            ).to_api_response()
        else:
            add_leave_admin_and_role(db_session, updated_user, employer)
            db_session.commit()
            db_session.refresh(updated_user)
            process_pending_leave_admin_invites(db_session, updated_user)
        data = UserResponse.from_orm(updated_user).dict()
    logger.info(
        "users_convert_employer success - account converted",
        extra={"employer_id": employer.employer_id},
    )

    return response_util.success_response(
        message="Successfully converted user", status_code=201, data=data
    ).to_api_response()


def users_current_get():
    """Return the currently authenticated user"""
    current_user = app.current_user()
    ensure(READ, current_user)
    data = UserResponse.from_orm(current_user).dict()
    if current_user.is_employer and current_user.user_leave_administrators:
        has_unverified_org = not all(
            [leave_admin.verified for leave_admin in current_user.user_leave_administrators]
        )
        logger.info(
            "users_current_get - has unverified org",
            extra={"user_id": current_user.user_id, "has_unverified_org": str(has_unverified_org)},
        )

    return response_util.success_response(
        message="Successfully retrieved current user", data=data
    ).to_api_response()


def users_patch(user_id, body):
    """This endpoint modifies the user specified by the user_id"""

    with app.db_session() as db_session:
        user = get_or_404(db_session, User, user_id)

        ensure(EDIT, user)
        issues = get_users_patch_issues(body, user)
        if issues:
            logger.info("users_patch failure - request has invalid fields")
            return response_util.error_response(
                status_code=BadRequest,
                message="Request does not include valid fields.",
                errors=issues,
                data={},
            ).to_api_response()

    request_body = parse_request_body(UserUpdateRequest, body)

    handle_user_patch_fineos_side_effects(user, request_body)

    updated_user = update_user(db_session, user, request_body)
    data = UserResponse.from_orm(updated_user).dict()

    logger.info(
        "user_patch success",
        extra={
            "user_id": user.user_id,
            "is_leave_admin_update": bool(
                updated_user.first_name and updated_user.last_name and updated_user.phone_number
            ),
        },
    )

    return response_util.success_response(
        message="Successfully updated user",
        data=data,
    ).to_api_response()


def get_documents(user_id: UUID) -> Union[List[DocumentResponse], Response]:
    """

    Calls out to the Customer API to retrieve a documents for a user.
    This document is originally fetched via the FINEOS  endpoint.

    GET /users/{user_id}/documents
    """
    with app.db_session() as db_session:
        # Get the referenced user or return 404
        existing_user = get_or_404(db_session, User, user_id)
        ensure(READ, existing_user)

        document_type = flask.request.args.get("document_type")
        if (
            document_type == DocumentType.IRS_1099G_TAX_FORM_FOR_CLAIMANTS.document_type_description
            and existing_user.has_multiple_tax_identifiers
        ):
            logger.error(
                "Get user documents failed - user has multiple tax identifiers",
                extra={"user_id": user_id, "document_type": document_type},
            )
            raise NotAuthorizedForAccess(description="User has multiple tax identifiers")

        api_params = {"name": document_type} if document_type else None
        documents_list = []

        try:
            submitted_application = (
                existing_user.submitted_applications[0]
                if existing_user.submitted_applications
                else None
            )
            if submitted_application is None:
                logger.warning(
                    "User does not have any submitted applications therefore not making FINEOS call to fetch documents.",
                    extra={"user_id": user_id, "document_type": document_type},
                )
            else:
                # Ensure user can access at least one of their submitted application
                ensure(READ, submitted_application)
                documents = GetDocumentService(db_session=db_session).get_user_documents(
                    existing_user, submitted_application, api_params
                )
                for doc in documents:
                    ensure(READ, doc)
                    documents_list.append(doc.dict())

        except FINEOSClientError:
            logger.info(
                "failed to retrieve customer documents",
                extra={"user_id": user_id, "document_type": document_type},
            )
            error = response_util.error_response(
                status_code=BadRequest,
                message="Failed to retrieve documents for id {}".format(user_id),
                errors=[],
                data={},
            )
            return error.to_api_response()

        logger.info(
            "successfully retrieved user documents",
            extra={
                "user_id": user_id,
                "document_type": document_type,
                "document_count": len(documents_list),
            },
        )
        return response_util.success_response(
            message="Successfully retrieved documents", data=documents_list, status_code=200
        ).to_api_response()


def download_document(user_id: UUID, fineos_document_id: str) -> Response:
    """

    Calls out to the Customer API to download a document given a fineos_document_id.
    This document is originally fetched via the FINEOS /customer/documents endpoint.

    GET /users/{user_id}/documents/{fineos_absence_id}
    """
    with app.db_session() as db_session:
        user = get_or_404(db_session, User, user_id)
        ensure(READ, user)

        try:
            document_data = download_customer_document(user, fineos_document_id, db_session)
        except FINEOSClientError:
            logger.info(
                "failed to download customer document",
                extra={"fineos_document_id": fineos_document_id},
            )
            error = response_util.error_response(
                status_code=BadRequest,
                message="Failed to download document for id {}".format(fineos_document_id),
                errors=[],
                data={},
            )
            return error.to_api_response()

        # rules are in api/authorization/rules/documents.py, will throw 403 if unauthorized
        ensure(READ, document_data)

        file_bytes = base64.b64decode(document_data.base64EncodedFileContents.encode("ascii"))
        content_type = document_data.contentType or "application/octet-stream"
        logger.info(
            "successfully downloaded user document",
            extra={
                "user_id": user_id,
                "fineos_document_id": fineos_document_id,
                "document_type": document_data.fileName,
            },
        )

        return ConnexionResponse(
            body=file_bytes,
            content_type=content_type,
            headers={"Content-Disposition": f"attachment; filename={document_data.fileName}"},
        )


def user_profile_check_for_updates(user_id: UUID, body: Any) -> Response:

    try:
        mmg_auth_token = get_bearer_token_from_header()
    except Exception:
        return response_util.error_response(
            status_code=BadRequest,
            errors=[],
            message="Can't read authorization token from request.",
            data={},
        ).to_api_response()

    user_profile_check_for_updates_request = parse_request_body(
        UserProfileCheckForUpdatesRequest, body
    )
    with app.db_session() as db_session:
        u = get_or_404(db_session, User, user_id)
        existing_application = get_or_404(
            db_session, Application, user_profile_check_for_updates_request.from_application
        )

        ensure(READ, u)
        ensure(READ, existing_application)

    if str(existing_application.user_id) != str(user_id):
        return response_util.error_response(
            status_code=Forbidden,
            message="Not authorized",
            errors=[],
            data={},
        ).to_api_response()

    client = get_mmg_api_client()

    enable_universal_profile_idv = (
        app.get_features_config().universal_profile.enable_universal_profile_idv
    )
    existing_profile_response = None
    if enable_universal_profile_idv:
        existing_profile_response = client.get_my_profile(
            mmg_auth_token, include={"addresses", "phones"}
        )
    else:
        existing_profile_response = client.get_my_profile(mmg_auth_token)

    existing_profile = mmg_profile.MmgProfile.from_response(existing_profile_response)
    updatable_fields = mmg_profile.get_updatable_fields(
        existing_application,
        existing_profile,
    )
    data = UserProfileCheckForUpdatesResponse.parse_obj(
        {"profile_updates": updatable_fields.mmg_fields_changing}
    ).dict()

    logger.info(
        "MMG profile fields that could be updated from Application",
        extra={
            "user_id": user_id,
            "application.application_id": existing_application.application_id,
            "mmg_fields": sorted(updatable_fields.mmg_fields_changing.keys()),
        },
    )

    return response_util.success_response(
        message="Successfully checked for updates", data=data
    ).to_api_response()


def patch_user_profile(user_id: UUID, body: Any) -> Response:
    try:
        mmg_auth_token = get_bearer_token_from_header()
    except Exception:
        return response_util.error_response(
            status_code=BadRequest,
            errors=[],
            message="Can't read authorization token from request.",
            data={},
        ).to_api_response()

    user_profile_request = parse_request_body(UserProfileUpdateRequest, body)
    with app.db_session() as db_session:
        u = get_or_404(db_session, User, user_id)
        existing_application = get_or_404(
            db_session, Application, user_profile_request.from_application
        )

        ensure(READ, u)
        ensure(READ, existing_application)

    if str(existing_application.user_id) != str(user_id):
        return response_util.error_response(
            status_code=Forbidden,
            message="Not authorized",
            errors=[],
            data={},
        ).to_api_response()

    client = get_mmg_api_client()
    existing_profile_response = client.get_my_profile(mmg_auth_token)
    existing_profile = mmg_profile.MmgProfile.from_response(existing_profile_response)

    fields = mmg_profile.get_mmg_fields_from_application(existing_application, existing_profile)
    request = ProfileRequestDto()

    for key in user_profile_request.profile_fields_include:
        setattr(request, key, getattr(fields, key))

    logger.info(
        "fields being sent to MMG",
        extra={
            "user_id": user_id,
            "application.application_id": existing_application.application_id,
            "mmg_fields": sorted(user_profile_request.profile_fields_include),
        },
    )

    client.update_my_profile(mmg_auth_token, request)

    return response_util.success_response(
        message="Successfully updated user profile", data={}
    ).to_api_response()


def _check_for_usable_phone(mmg_phones: list[MmgPhone]) -> bool:
    """
    Check if the primary phone number in a user's MMG profile is from a country PFML supports.

    Args:
        mmg_phones: List of phone numbers from MMG profile
        allowed_countries: List of permitted country codes

    Returns:
        bool: True if no primary phones are from disallowed countries
    """

    if not mmg_phones:
        return False

    # Check if the primary phone number is from an allowed country
    # Currently, only US phone numbers are supported.
    # Consider an enum if more countries are supported in the future.
    allowed_countries = ["US"]
    invalid_phones = [
        phone
        for phone in mmg_phones
        if phone.priority == MmgPhonePriority.PRIMARY and phone.countryCode not in allowed_countries
    ]

    if invalid_phones:
        logger.info(
            "Non-US, primary phone number found in the applicant's MMG profile",
            extra={"country_code": invalid_phones[0].countryCode},
        )
        return False

    return True


def get_profile_has_usable_data_for_application(user_id: UUID) -> Response:
    with app.db_session() as db_session:
        u = get_or_404(db_session, User, user_id)
        ensure(READ, u)

    try:
        mmg_auth_token = get_bearer_token_from_header()
    except Exception:
        return response_util.error_response(
            status_code=BadRequest,
            errors=[],
            message="Can't read authorization token from request.",
            data={},
        ).to_api_response()

    client = get_mmg_api_client()

    enable_universal_profile_idv = (
        app.get_features_config().universal_profile.enable_universal_profile_idv
    )
    profile = None
    if enable_universal_profile_idv:
        profile = client.get_my_profile(mmg_auth_token, include={"addresses", "phones"})
    else:
        profile = client.get_my_profile(mmg_auth_token)

    if profile and pydantic_util.has_non_empty_values_set(profile):
        logger.info(
            "MMG profile response is not empty",
            extra={
                "user_id": user_id,
            },
        )
        if pydantic_util.has_non_empty_values_set(
            profile, exclude={"idpUserId", "idpTenantId", "createdAt", "updatedAt"}
        ):
            logger.info(
                "MMG profile is not empty (it has at least some data)",
                extra={
                    "user_id": user_id,
                },
            )
        else:
            logger.info(
                "MMG profile is empty (it has no non-null/empty data)",
                extra={
                    "user_id": user_id,
                },
            )

    usable_fields = list()
    if profile:
        if profile.gender:
            usable_fields.append("gender")

        if profile.raceEthnicity and len(profile.raceEthnicity) > 0:
            usable_fields.append("raceEthnicity")

        if enable_universal_profile_idv:
            if profile.firstname:
                usable_fields.append("firstname")

            if profile.lastname:
                usable_fields.append("lastname")

            if profile.dateOfBirth:
                usable_fields.append("dateOfBirth")

            if profile.addresses and len(profile.addresses) > 0:
                usable_fields.append("addresses")

            if profile.phones and _check_for_usable_phone(
                profile.phones,
            ):
                usable_fields.append("phones")

    logger.info(
        "MMG profile fields that are usable for Application",
        extra={
            "user_id": user_id,
            "mmg_fields": sorted(usable_fields),
        },
    )

    return response_util.success_response(
        message="Successfully retrieved usable data for application",
        data={"usable_fields": usable_fields},
        status_code=200,
    ).to_api_response()
