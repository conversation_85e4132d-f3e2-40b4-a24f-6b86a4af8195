from typing import List
from uuid import UUID

from connexion.lifecycle import ConnexionResponse
from flask import Response
from sqlalchemy.exc import IntegrityError

import massgov.pfml.api.app as app
import massgov.pfml.api.services.appeals as appeals_service
import massgov.pfml.api.util.response as response_util
import massgov.pfml.api.validation.appeal_rules as appeal_rules
import massgov.pfml.services.documents as documents_service
import massgov.pfml.util.logging
from massgov.pfml.api.authorization.flask import CREATE, EDIT, READ, ensure, valid_access_query
from massgov.pfml.api.claims import get_claim_from_db
from massgov.pfml.api.exceptions import ClaimWithdrawn, UploadDocumentError
from massgov.pfml.api.models.appeals.requests import (
    AppealCreateRequest,
    AppealsSearchRequest,
    AppealUpdateRequest,
)
from massgov.pfml.api.models.appeals.responses import AppealResponse
from massgov.pfml.api.models.documents.requests import (
    ConfirmDocumentsRequestBody,
    DocumentRequestBody,
)
from massgov.pfml.api.util.request import parse_request_body
from massgov.pfml.api.validation.exceptions import (
    IssueType,
    ValidationErrorDetail,
    ValidationException,
)
from massgov.pfml.db.models.appeal import Appeal
from massgov.pfml.db.models.employees import Claim
from massgov.pfml.services.documents.get_document_service import GetDocumentService
from massgov.pfml.util.logging.appeals import get_appeal_log_attributes
from massgov.pfml.util.sqlalchemy import get_or_404

logger = massgov.pfml.util.logging.get_logger(__name__)


def appeals_create(body: dict) -> ConnexionResponse:
    """Create a new appeal"""
    appeal_request = parse_request_body(AppealCreateRequest, body)

    logger.info(
        "starting appeal creation",
        extra={
            "absence_case_id": appeal_request.fineos_absence_id,
        },
    )

    claim = get_claim_from_db(appeal_request.fineos_absence_id)
    if claim is None:
        logger.info(
            "appeal creation failure - claim not found",
            extra={"absence_case_id": appeal_request.fineos_absence_id},
        )
        raise ValidationException(
            [
                ValidationErrorDetail(
                    message="An issue occurred while trying to create the appeal.",
                    type=IssueType.incorrect,
                )
            ]
        )

    with app.db_session() as db_session:
        appeal = Appeal(
            claim_id=claim.claim_id,
        )
        ensure(CREATE, appeal)

        try:
            db_session.add(appeal)
            db_session.commit()
        except IntegrityError:
            logger.error("Claim already has an appeal.")
            db_session.rollback()

            raise ValidationException(
                [
                    ValidationErrorDetail(
                        field="claim_id",
                        message="Claim already has an appeal.",
                        type=IssueType.duplicate,
                    )
                ]
            )

        log_attributes = get_appeal_log_attributes(appeal)
        log_attributes["absence_case_id"] = appeal_request.fineos_absence_id

        logger.info(
            "appeals_create success",
            extra=log_attributes,
        )

        return response_util.success_response(
            message="Successfully created appeal",
            status_code=201,
            data=AppealResponse.from_orm(appeal).dict(),
        ).to_api_response()


def appeal_get(appeal_id):
    """Get an appeal by appeal ID"""

    with app.db_session() as db_session:
        existing_appeal = get_or_404(db_session, Appeal, appeal_id)

        ensure(READ, existing_appeal)
        appeal_response = AppealResponse.from_orm(existing_appeal)

        # Only run these validations if the appeal hasn't already been submitted.
        issues = (
            appeal_rules.get_appeal_submit_issues(existing_appeal)
            if not existing_appeal.is_submitted
            else []
        )

    return response_util.success_response(
        message="Successfully retrieved appeal",
        data=appeal_response.dict(),
        warnings=issues,
    ).to_api_response()


def appeals_search(body: dict) -> ConnexionResponse:
    """Search appeals by given attributes"""

    appeal_request = parse_request_body(AppealsSearchRequest, body)
    terms = appeal_request.terms

    with app.db_session() as db_session:
        query = valid_access_query(db_session, READ, Appeal).join(Claim)

        if terms.appeal_id is not None:
            query = query.filter(Appeal.appeal_id == terms.appeal_id)

        if terms.fineos_appeal_id is not None:
            query = query.filter(Appeal.fineos_appeal_id == terms.fineos_appeal_id)

        if terms.fineos_absence_id is not None:
            query = query.filter(Claim.fineos_absence_id == terms.fineos_absence_id)

        if terms.appeal_phone_number is not None:
            query = query.filter(Appeal.appeal_phone_number == terms.appeal_phone_number.e164)

        appeals_response = [AppealResponse.from_orm(appeal).dict() for appeal in query.all()]

    return response_util.success_response(
        message="Successfully retrieved appeals",
        data=appeals_response,
    ).to_api_response()


def appeals_update(appeal_id: UUID, body: dict) -> ConnexionResponse:
    """Update an appeal"""
    with app.db_session() as db_session:
        existing_appeal = get_or_404(db_session, Appeal, appeal_id)

    ensure(EDIT, existing_appeal)

    appeals_service.check_appeal_is_valid_for_update(existing_appeal)
    appeals_service.handle_phone_update(body, existing_appeal)

    appeal_update = parse_request_body(AppealUpdateRequest, body)

    with app.db_session() as db_session:
        appeals_service.update_appeal_from_request(db_session, appeal_update, existing_appeal)

    issues = appeal_rules.get_appeal_submit_issues(existing_appeal)

    log_attributes = get_appeal_log_attributes(existing_appeal)
    logger.info(
        "appeals_update success",
        extra=log_attributes,
    )

    return response_util.success_response(
        message="Appeal updated without errors.",
        data=AppealResponse.from_orm(existing_appeal).dict(exclude_none=True),
        warnings=issues,
    ).to_api_response()


def appeals_complete(appeal_id):
    """Complete a new appeal and submit it to Fineos"""

    with app.db_session() as db_session:
        existing_appeal = get_or_404(db_session, Appeal, appeal_id)

        ensure(EDIT, existing_appeal)

        log_attributes = get_appeal_log_attributes(existing_appeal)
        logger.info(
            "beginning appeals_complete",
            extra=log_attributes,
        )

        appeals_service.check_appeal_is_valid_for_update(existing_appeal)
        issues = appeal_rules.get_appeal_submit_issues(existing_appeal)
        if issues:
            logger.info("appeals_complete failure - appeal failed validation")
            raise ValidationException(issues)

        appeals_service.submit_appeal(db_session, existing_appeal)
        appeals_service.generate_and_upload_pdf(db_session, existing_appeal)

        # will now have a Fineos appeal ID to log
        log_attributes = get_appeal_log_attributes(existing_appeal)
        logger.info(
            "finished appeals_complete",
            extra=log_attributes,
        )
        return response_util.success_response(
            message="Appeal {} submitted without errors".format(existing_appeal.appeal_id),
            data=AppealResponse.from_orm(existing_appeal).dict(exclude_none=True),
            status_code=200,
        ).to_api_response()


def documents_get(appeal_id: UUID) -> Response:
    """Get appeal documents by appeal ID"""

    with app.db_session() as db_session:
        # Get the referenced appeal by id or return 404
        existing_appeal = get_or_404(db_session, Appeal, appeal_id)

        ensure(READ, existing_appeal)

        if existing_appeal.fineos_appeal_id:
            documents = GetDocumentService(db_session).get_documents_from_fineos_for_appeal(
                existing_appeal
            )
            documents_list = [doc.dict() for doc in documents]
        else:
            documents_list = []

        return response_util.success_response(
            message="Successfully retrieved documents", data=documents_list, status_code=200
        ).to_api_response()


def document_upload(appeal_id, body, file):
    """Upload document to an appeal"""

    with app.db_session() as db_session:
        appeal = get_or_404(db_session, Appeal, appeal_id)

    application = appeal.claim.application
    if not application:
        raise Exception("Application with that appeal.claim could not be found")

    ensure(EDIT, appeal)

    # Parse the document details from the form body
    document_details: DocumentRequestBody = parse_request_body(DocumentRequestBody, body)

    try:
        document_response = documents_service.upload_document_to_fineos(
            application, document_details, file, appeal
        )

    except UploadDocumentError as e:
        return e.to_api_response()
    except ClaimWithdrawn as err:
        return err.to_api_response()

    # Return response
    return response_util.success_response(
        message="Successfully uploaded document", data=document_response.dict(), status_code=200
    ).to_api_response()


def confirm_documents(appeal_id: UUID, body: dict) -> ConnexionResponse:
    """
    Creates the 'Appeal Follow Up' task when new documents are uploaded to an appeal case.

    A task is only created in certain conditions to support appeals case examiner workflows:
    1. The appeal is submitted > 24 hours from current time
    2. The most recently uploaded document > 24 hours from current time
        a. We filter out documents that were just uploaded via 'document_upload' via fineos_id

    Due to the parallelization of 'document_upload' we call this endpoint on the frontend
    after all documents are successfully uploaded.
    """
    request: ConfirmDocumentsRequestBody = parse_request_body(ConfirmDocumentsRequestBody, body)

    # We use fineos_document_ids to filter out documents just uploaded through the
    # 'upload_documents' endpoint.
    fineos_document_ids: List[str] = request.fineos_document_ids or []

    with app.db_session() as db_session:
        existing_appeal = get_or_404(db_session, Appeal, appeal_id)

    if not existing_appeal.fineos_appeal_id:
        raise ValueError("fineos_appeal_id is None")

    ensure(EDIT, existing_appeal)

    log_message = appeals_service.create_appeal_task(
        db_session, existing_appeal, fineos_document_ids
    )

    log_attributes = get_appeal_log_attributes(existing_appeal)
    logger.info(
        log_message,
        extra=log_attributes,
    )

    return response_util.success_response(
        message=log_message,
        status_code=201,
        data=AppealResponse.from_orm(existing_appeal).dict(),
    ).to_api_response()
