from datetime import datetime, timezone
from enum import Enum
from typing import Any, List, Optional

from pydantic import Field

import massgov.pfml.db.config as db_config
from massgov.pfml.util.pydantic import PydanticBaseSettings
from massgov.pfml.util.strings import split_str


class RMVAPIBehavior(Enum):
    # Fully mocked endpoint
    MOCK = "fully_mocked"
    # Partially mocked endpoint, will call the RMV API when called with certain records. Mocked responses otherwise.
    PARTIAL_MOCK = "partially_mocked"
    # All requests hit RMV API
    NO_MOCK = "not_mocked"


class AppConfig(PydanticBaseSettings):
    environment: str
    port: int = 1550
    enable_full_error_logs: bool = False
    cors_origins: List[str] = Field(default_factory=list)
    cors_origin_regex: Optional[str] = None
    db: db_config.DbConfig = Field(default_factory=db_config.get_config)
    dev_oauth_key_file: Optional[str] = None
    limit_ssn_fein_max_attempts: int = 5
    rmv_api_behavior: RMVAPIBehavior = RMVAPIBehavior.MOCK
    # True always passes id proofing, False always fails id proofing
    rmv_check_mock_success: bool = True
    enable_application_fraud_check: bool = False
    dashboard_password: Optional[str] = None
    new_plan_proofs_active_at: datetime = Field(
        default_factory=(lambda: datetime(2021, 6, 26, tzinfo=timezone.utc))
    )
    generate_1099_max_files: int = 1000
    # The number of days to look back for overpayment repayment when syncing overpayment collections from the EDM API
    overpayment_repayment_fetch_days_prior: int = 14
    # Enable the EDM repayment response to be mocked based on the overpayment records in the database
    enable_mock_edm_repayment_response: bool = False
    upload_max_files_to_fineos: int = 10
    enable_document_multipart_upload: bool = False
    enable_1099_testfile_generation: bool = Field(False, env="TEST_FILE_GENERATION_1099")
    # Sending emails is enabled by default. It must be disabled explicitly if
    # desired eg for local development
    disable_sending_emails: bool = False
    enable_response_validation: bool = False
    use_overpayment_table_for_over_payments_max_weekly_benefit: bool = Field(
        False, env="USE_OVERPAYMENT_TABLE_FOR_MAX_WEEKLY_BENEFIT"
    )
    channel_switch_unsupported_claims: bool = False
    # The number of trusted proxies between the user and app server. Used to determine
    # the client IP in an originating request.
    trusted_proxies: int = 2
    # Limit the number of leave admin verifications within a period of time.
    enable_verification_limits: bool = False
    claim_status_v2_employer_review: bool = False
    enable_email_change: bool = False
    enable_service_agreement_versions_for_existing_automations: bool = False

    # The number of days a claimant-obligation (crosswalk match) can keep
    # attempting an update to FINEOS before it's marked FAILED (and attempts stop)
    child_support_fineos_attempt_cutoff_days: int = 15
    # Limit CSV export rows for leave admins to avoid request timeout errors
    # Current implementation processes exports synchronously, causing timeouts on large datasets
    # TODO Phase 2: Implement asynchronous CSV processing with email delivery to leave admin
    leave_admin_csv_max_rows: int = Field(50000, env="LEAVE_ADMIN_CSV_MAX_ROWS")
    # The number of days after which the DFML expects a decision to be made on an absence case
    dfml_expected_decision_date_delta_days: int = 14

    class Config:
        @classmethod
        def parse_env_var(cls, field_name: str, raw_val: str) -> Any:
            if field_name == "cors_origins":
                return split_str(raw_val)
            return cls.json_loads(raw_val)  # type: ignore


def get_config() -> AppConfig:
    return AppConfig()
