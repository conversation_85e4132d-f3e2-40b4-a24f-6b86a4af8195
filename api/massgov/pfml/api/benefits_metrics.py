import massgov.pfml.api.app as app
import massgov.pfml.api.util.response as response_util
import massgov.pfml.my_mass_gov.client
import massgov.pfml.util.logging
from massgov.pfml.db.models.state_metrics import BenefitsMetrics
from massgov.pfml.util.datetime import utcnow

logger = massgov.pfml.util.logging.get_logger(__name__)


def get_benefits_metrics():
    """
    Retrieve the 2 active benefits metrics records. Active records are determined by the publish date.
    """
    with app.db_session() as db_session:
        active_benefits_metrics = (
            db_session.query(BenefitsMetrics)
            .filter(
                BenefitsMetrics.publish_date.isnot(None),
                BenefitsMetrics.publish_date <= utcnow(),
            )
            .order_by(BenefitsMetrics.publish_date.desc())
            .all()
        )

    response_data = {
        "max_benefit_amount": active_benefits_metrics[1].maximum_weekly_benefit_amount,
        "max_benefit_year": active_benefits_metrics[1].effective_date.year,
        "new_max_benefit_amount": active_benefits_metrics[0].maximum_weekly_benefit_amount,
        "new_max_benefit_year": active_benefits_metrics[0].effective_date.year,
    }

    return response_util.success_response(
        data=response_data,
        message="Successfully retrieved active benefits metrics",
        status_code=200,
    ).to_api_response()
