from connexion.lifecycle import ConnexionResponse
from werkzeug.exceptions import BadRequest, Forbidden, NotFound

import massgov.pfml.api.app as app
import massgov.pfml.api.util.response as response_util
import massgov.pfml.util.logging
from massgov.pfml.api.authorization.flask import READ, can
from massgov.pfml.api.claims import get_claim_from_db
from massgov.pfml.api.models.common import SearchEnvelope
from massgov.pfml.api.models.waiting_periods.responses import WaitingPeriodResponse
from massgov.pfml.api.util.request import parse_request_body
from massgov.pfml.services.waiting_periods.get_waiting_periods_from_fineos import (
    get_waiting_periods_from_fineos,
)
from massgov.pfml.util.pydantic import PydanticBaseModel

logger = massgov.pfml.util.logging.get_logger(__name__)


class WaitingPeriodsSearchRequestTerms(PydanticBaseModel):
    absence_case_id: str


WaitingPeriodsSearchRequest = SearchEnvelope[WaitingPeriodsSearchRequestTerms]


def get_waiting_periods(body: dict) -> ConnexionResponse:
    request = parse_request_body(WaitingPeriodsSearchRequest, body)
    absence_case_id = request.terms.absence_case_id
    extra = {"absence_case_id": absence_case_id}

    # OpenApi should catch this before we ever get here
    # But mypy complains anyway, so we need this guard clause
    if not absence_case_id:
        raise BadRequest()

    with app.db_session() as db_session:
        claim = get_claim_from_db(absence_case_id)

        if claim is None:
            logger.warning(
                "Claim not in PFML database. Could not retrieve payments for claim", extra=extra
            )
            error = response_util.error_response(
                NotFound,
                "No claim found for absence_case_id. Unable to retrieve waiting periods.",
                errors=[],
            )
            return error.to_api_response()

        if not can(READ, claim):
            logger.warning(
                "get_waiting_periods failure - User does not have access to claim.", extra=extra
            )
            error = response_util.error_response(
                Forbidden, "User does not have access to claim.", errors=[]
            )
            return error.to_api_response()
    waiting_periods = get_waiting_periods_from_fineos(claim, db_session)

    waiting_periods_response = [
        (WaitingPeriodResponse.from_orm(waiting_period).dict() if waiting_period else None)
        for waiting_period in waiting_periods
    ]

    # filter out any null/none values
    filtered_waiting_periods_response = filter_waiting_periods_response(waiting_periods_response)

    return response_util.success_response(
        message="Successfully retrieved waiting periods",
        data={"waiting_periods": filtered_waiting_periods_response},
        status_code=200,
    ).to_api_response()


# filter out null data in the waiting periods response
def filter_waiting_periods_response(waiting_periods):

    # filter out null periods, and filter out null start and end dates, these are incomplete values
    filter_periods = [
        period
        for period in waiting_periods
        if period and period["waiting_period_start_date"] and period["waiting_period_end_date"]
    ]

    return filter_periods
