import datetime
import mimetypes
import urllib
from contextlib import contextmanager
from typing import Any, Callable, Generator

import requests
from connexion.lifecycle import ConnexionResponse
from flask_bouncer import ensure as flask_ensure
from werkzeug.exceptions import BadRequest, Forbidden, NotFound

import massgov.pfml.api.util.response as response_util
import massgov.pfml.services.documents as documents_service
import massgov.pfml.util.logging as logging
from massgov.pfml.api import app
from massgov.pfml.api.authorization.flask import READ
from massgov.pfml.api.models.documents.requests import (
    CertificationDocumentIDPType,
    CertificationDocumentRequestBody,
)
from massgov.pfml.api.services.fineos_actions import upload_document_with_claim
from massgov.pfml.api.util.request import parse_request_body
from massgov.pfml.api.validation.exceptions import ValidationException
from massgov.pfml.db.lookup_data.absences import AbsenceStatus
from massgov.pfml.fineos.transforms.from_fineos.documents import (
    fineos_document_to_document_response,
)
from massgov.pfml.services.claims.get_claim_for_idp import (
    ACTIVE_CLAIM_STATUSES,
    get_claim_for_idp_document_match,
)
from massgov.pfml.util.documents import validate_content_type

logger = logging.get_logger(__name__)
MILLISECOND = datetime.timedelta(milliseconds=1)


class S3DownloadError(RuntimeError):
    """Exception or unexpected response when downloading a file from an S3 bucket."""


@contextmanager
def scoped_logger(level: int = logging.INFO) -> Generator[Callable[..., None], None, None]:
    """Creates a scoped logger that aggregates details within its scope."""
    additional_info: dict[str, Any] = {}

    def log_detail(**kwargs) -> None:  # type: ignore
        """Adds a detail to the scope's log details"""
        additional_info.update(kwargs)

    try:
        yield log_detail

    finally:
        overall_result = (
            "successfully" if additional_info.get("result") == "success" else "in failure"
        )

        logger.log(level, f"IDP workflow completed {overall_result}", extra=additional_info)


def match_cert_doc_to_claim(body: dict) -> ConnexionResponse:
    """Handler for /claims/match-cert-doc-to-claim endpoint

    Takes employee demographic data, searches for a claim associated with an
    employee with that demographic information, downloads a document from a
    presigned S3 url, and uploads the document to the given claim in FINEOS
    """
    with scoped_logger() as detail_logger:
        _log_request_body_details(body, detail_logger)
        try:
            # ensure user is authorized to call this endpoint
            # use flask_ensure instead of custom ensure to
            #   catch exception and log before raising Forbidden()
            flask_ensure(READ, CertificationDocumentRequestBody)
        except Forbidden as e:
            detail_logger(result="user not authorized to call this endpoint", error=str(e))
            raise Forbidden()

        try:
            cert_doc_request: CertificationDocumentRequestBody = parse_request_body(
                CertificationDocumentRequestBody, body
            )
        except ValidationException as e:
            detail_logger(result="request body validation error", error=str(e))
            raise e

        document_data = cert_doc_request.document_data
        with app.db_session() as db_session:
            # get relevant claim
            claim = get_claim_for_idp_document_match(
                document_data=document_data, db_session=db_session, detail_logger=detail_logger
            )
            claim_status = (
                AbsenceStatus.get_description(claim.fineos_absence_status_id)
                if claim.fineos_absence_status_id
                else "None"
            )
            detail_logger(
                claim_ntn=claim.fineos_absence_id,
                is_active_claim=claim.fineos_absence_status_id in ACTIVE_CLAIM_STATUSES,
                claim_status=claim_status,
            )

            # download the relevant document from the s3 bucket
            s3_bucket_url = cert_doc_request.s3_bucket_url
            try:
                response = download_from_s3(s3_bucket_url, detail_logger)
            except S3DownloadError:
                raise NotFound("Failed to download file from S3 bucket")

            # validate file type
            document_name = urllib.parse.urlparse(s3_bucket_url).path.split("/")[-1]
            content_type, _ = mimetypes.guess_type(document_name or "")
            if not content_type:
                message = "File associated with the presigned URL must have the file extension in its name"
                detail_logger(result="missing file extension", error=message)
                raise BadRequest(message)

            try:
                validate_content_type(content_type)
            except ValidationException as e:
                detail_logger(result="invalid file type", error=str(e.errors))
                raise e

            # map IDP document types to certification document type that FINEOS will accept as evidence
            required_cert_doc_type = documents_service.get_doc_type_for_fineos_evidence(
                claim, db_session, detail_logger
            )
            detail_logger(required_cert_doc_type=required_cert_doc_type)
            idp_document_type: CertificationDocumentIDPType = cert_doc_request.document_type

            # upload document to FINEOS for the given claim
            # this does NOT add the document information to PFML DB
            try:
                fineos_document = upload_document_with_claim(
                    claim=claim,
                    document_type=required_cert_doc_type,
                    file_content=response.content,
                    file_name=document_name,
                    content_type=content_type,
                    description=f"IDP Document Type: {idp_document_type}",
                    db_session=db_session,
                    with_multipart=True,
                )
            except Exception as e:
                detail_logger(result="unable to upload doc to FINEOS", error=str(e))
                raise BadRequest(f"Unable to upload document to FINEOS. Details: {str(e)}")

            document_response = fineos_document_to_document_response(
                fineos_document=fineos_document,
                db_session=db_session,
            )
            detail_logger(
                fineos_document_content_type=document_response.content_type,
                fineos_document_id=document_response.fineos_document_id,
                fineos_document_upload_date=document_response.created_at,
            )
            try:
                documents_service.mark_single_document_as_received_for_claim(
                    claim, document_response, db_session
                )
            except Exception as e:
                detail_logger(result="unable to mark doc as evidence in FINEOS", error=str(e))
                raise BadRequest(
                    f"Unable to mark document as evidence received in FINEOS. Details: {str(e)}"
                )

        success_response = response_util.success_response(
            message=f"Successfully uploaded certification document to claim with absence id {claim.fineos_absence_id}",
            data=document_response.dict(),
            status_code=201,
        ).to_api_response()
        detail_logger(result="success")
        return success_response


def download_from_s3(s3_bucket_url: str, detail_logger: Callable) -> requests.Response:
    """Download a file from S3, handling exceptions or unexpected responses."""
    log_extra: dict[str, object] = {"s3.request.url": s3_bucket_url}

    try:
        response = requests.get(url=s3_bucket_url, timeout=5)
    except requests.exceptions.RequestException as ex:
        log_extra |= {"s3.error.class": type(ex).__name__, "s3.error.message": str(ex)}
        logger.error("S3 request failed with exception", extra=log_extra)
        detail_logger(result="failed to download file from S3 bucket", error=str(ex))
        raise S3DownloadError("S3 request failed with exception") from ex

    log_extra |= {
        "s3.response.status_code": response.status_code,
        "s3.response.response_time_ms": int(response.elapsed / MILLISECOND),
        "s3.response.length": len(response.content),
    }
    if response.status_code != 200:
        log_extra |= {
            "s3.response.headers": response.headers,
            "s3.response.text": response.text[:1000],
        }
        detail_logger(result="failed to download file from S3 bucket", error=response.text[:1000])
        logger.error("S3 request failed with non-200 status", extra=log_extra)
        raise S3DownloadError("S3 request failed with non-200 status")

    logger.info("S3 request complete", extra=log_extra)

    return response


def _log_request_body_details(request_body: dict, detail_logger: Callable) -> None:
    document_data: dict = request_body.get("document_data", {})

    def get_first_letter(value_key: str) -> str:
        """extract first letter from dictionary value"""
        return str(document_data.get(value_key, ""))[0] if document_data.get(value_key) else ""

    first_name_initial = get_first_letter("first_name")
    last_name_initial = get_first_letter("last_name")
    dob_first_digit = get_first_letter("date_of_birth")
    ssn_first_digit = get_first_letter("tax_identifier")
    s3_bucket_url = request_body.get("s3_bucket_url")
    document_name = (
        urllib.parse.urlparse(s3_bucket_url).path.split("/")[-1] if s3_bucket_url else None
    )

    demographic_fields_provided = repr(
        [
            key
            for key, value in document_data.items()
            if value and key in ["first_name", "last_name", "date_of_birth", "tax_identifier"]
        ]
    )
    detail_logger(
        document_name=document_name,
        idp_document_type=request_body.get("document_type"),
        provided_ntn=document_data.get("fineos_absence_id"),
        demographic_fields=demographic_fields_provided,
        demographic_id=f"F{first_name_initial}.L{last_name_initial}.B{dob_first_digit}.S{ssn_first_digit}",
    )
