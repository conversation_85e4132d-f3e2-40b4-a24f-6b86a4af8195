# Factories for generating mock data and inserting it into the database.
# This should be used for seeding tables in development and testing.
#

import os
import random
import re
import string
import unittest.mock
import uuid
from datetime import date, datetime, timedelta, timezone
from decimal import Decimal
from typing import Optional, cast

import factory  # this is from the factory_boy package
import faker
import pytz
from sqlalchemy.orm import Session, scoped_session
from werkzeug.security import generate_password_hash

import massgov.pfml.api.models.appeals.common as appeals_common
import massgov.pfml.db as db
import massgov.pfml.db.lookup_data as lookups
import massgov.pfml.db.models.absences as absence_models
import massgov.pfml.db.models.admin as admin_models
import massgov.pfml.db.models.appeal as appeal_models
import massgov.pfml.db.models.applications as application_models
import massgov.pfml.db.models.change_request as change_request_models
import massgov.pfml.db.models.ctr.batch_identifier
import massgov.pfml.db.models.ctr.document_identifier
import massgov.pfml.db.models.documents as document_models
import massgov.pfml.db.models.dua as dua_models
import massgov.pfml.db.models.employees as employee_models
import massgov.pfml.db.models.employer_exemptions as employer_exemptions_models
import massgov.pfml.db.models.oauth as oauth_models
import massgov.pfml.db.models.oauth_server as oauth_server_models
import massgov.pfml.db.models.payments as payment_models
import massgov.pfml.db.models.phone as phone_models
import massgov.pfml.db.models.reference_file.reference_file
import massgov.pfml.db.models.state_metrics as state_metrics_models
import massgov.pfml.db.models.verifications as verification_models
import massgov.pfml.fineos.mock.field
import massgov.pfml.util.datetime as datetime_util
from massgov.pfml.api.authentication.azure import AzureUser
from massgov.pfml.api.util.state_log_util import AssociatedClass
from massgov.pfml.db.lookup_data.dor import FineosServiceAgreementStatus
from massgov.pfml.db.lookup_data.employer_exemptions import EmployerExemptionApplicationStatus
from massgov.pfml.db.lookup_data.language import Language
from massgov.pfml.db.lookup_data.payments import MmarsEventStatusType, MmarsEventType
from massgov.pfml.db.lookup_data.reference_file_type import ReferenceFileType
from massgov.pfml.db.models.dor import (
    ChildSupportObligation,
    ClaimantChildSupportObligation,
    DorCseExtractErrorLog,
    DorCseExtractObligation,
    EmployerDORExemption,
    FineosExtractServiceAgreement,
    FineosServiceAgreement,
)
from massgov.pfml.db.models.financial_eligibility.financial_eligibility_calculation import (
    FinancialEligibilityCalculation,
)
from massgov.pfml.db.models.import_log import ImportLog
from massgov.pfml.db.models.merger_acquisition import MergerAcquisition
from massgov.pfml.db.models.organization_unit import OrganizationUnit
from massgov.pfml.db.models.reference_file.employee_reference_file import EmployeeReferenceFile
from massgov.pfml.db.models.reference_file.payment_reference_file import PaymentReferenceFile
from massgov.pfml.edm.models.vendor.models import EDMVendorData
from massgov.pfml.fineos.models import ServiceAgreementLeavePlans
from massgov.pfml.util.datetime.quarter import Quarter

db_session: Session | None = None

fake = faker.Faker()


def get_db_session() -> Session:
    global db_session

    if os.getenv("DB_FACTORIES_DISABLE_DB_ACCESS", "0") == "1":
        alert_db_session = unittest.mock.MagicMock()
        alert_db_session.add.side_effect = Exception(
            """DB_FACTORIES_DISABLE_DB_ACCESS is set, refusing database action.

            If your tests don't need to cover database behavior, consider
            calling the `build()` method instead of `create()` on the factory to
            not persist the generated model.

            If running tests that actually need data in the DB, pull in the
            `initialize_factories_session` fixture.

            If running factories outside of the tests and you see this, unset
            the DB_FACTORIES_DISABLE_DB_ACCESS env var.
            """
        )

        return alert_db_session

    if db_session is None:
        db_session = db.init()

    return db_session


session = cast(
    Session,
    # first argument to scoped_session doesn't _have_ to be a `sessionmaker`,
    # but types restrict it so, so ignore
    scoped_session(lambda: get_db_session(), scopefunc=lambda: get_db_session()),  # type: ignore[arg-type]
)


class Generators:
    AccountKey = factory.Sequence(lambda n: "%011d" % n)
    EmailAddress = factory.Sequence(lambda n: f"example-{n}+{uuid.uuid4()}@example.com")
    Tin = factory.LazyFunction(lambda: fake.ssn().replace("-", ""))
    Fein = Tin
    Money = factory.LazyFunction(lambda: Decimal(str(round(random.uniform(0, 50000), 2))))
    Now = factory.LazyFunction(lambda: datetime.now(timezone.utc))
    ThisYear = factory.LazyFunction(datetime.now().year)
    # A reproducible datetime that might represent a database creation, modification, or other
    # transaction datetime.
    TransactionDateTime = factory.Faker(
        "date_time_between_dates",
        datetime_start=pytz.UTC.localize(datetime(2020, 1, 1)),
        datetime_end=pytz.UTC.localize(datetime(2022, 1, 1)),
    )
    UtcNow = factory.LazyFunction(datetime_util.utcnow)
    UuidObj = factory.Faker("uuid4", cast_to=None)
    S3Path = factory.LazyFunction(
        lambda: os.path.join(
            "s3://bucket/path/to/",
            "".join(random.choices(string.ascii_letters + string.digits, k=8)) + ".txt",
        )
    )
    CtrDocumentIdentifier = factory.LazyFunction(
        lambda: "INTFDFML" + "".join(random.choices(string.ascii_uppercase + string.digits, k=12))
    )
    FineosAbsenceId = factory.LazyFunction(
        lambda: "NTN-{:02d}-ABS-01".format(fake.unique.random_int(0, 999999))
    )
    FineosNotificationId = factory.LazyFunction(
        lambda: "NTN-{:02d}".format(random.randint(0, 999999))
    )
    FineosAppealId = factory.LazyFunction(
        lambda: "NTN-{:02d}-ABS-01-AP-{:02d}".format(
            random.randint(0, 99999), random.randint(0, 99999)
        )
    )
    VccDocCounter = factory.Sequence(lambda n: n)
    VccDocId = factory.Sequence(
        lambda n: "INTFDFML{}{}".format(datetime.now().strftime("%d%m%Y"), f"{n:04}")
    )
    VccBatchCounter = factory.Sequence(lambda n: n)
    VccBatchId = factory.Sequence(lambda n: "EOL{}VCC{}".format(datetime.now().strftime("%m%d"), n))
    FirstDayOfCurrentYear = factory.LazyFunction(lambda: date(date.today().year, 1, 1))
    LastDayOfCurrentYear = factory.LazyFunction(lambda: date(date.today().year, 12, 31))
    FirstDayOfPreviousYear = factory.LazyFunction(lambda: date(date.today().year - 1, 1, 1))
    LastDayOfPreviousYear = factory.LazyFunction(lambda: date(date.today().year - 1, 12, 31))
    NonZeroDORActivityKeyPy = factory.LazyFunction(
        lambda: int(
            date(date.today().year - 1, date.today().month, date.today().day).strftime("%Y%m%d")
        )
    )
    NonZeroDORActivityKeyCy = factory.LazyFunction(lambda: int(datetime.today().strftime("%Y%m%d")))


class BaseFactory(factory.alchemy.SQLAlchemyModelFactory):
    class Meta:
        abstract = True
        sqlalchemy_session = session
        sqlalchemy_session_persistence = "commit"


class AzureUserFactory(factory.Factory):
    class Meta:
        model = AzureUser

    sub_id = factory.Faker("uuid4")
    first_name = factory.Faker("first_name")
    last_name = factory.Faker("last_name")
    email_address = Generators.EmailAddress
    groups = fake.pylist(3, True)
    permissions = fake.pylist(10, True)


class UserFactory(BaseFactory):
    class Meta:
        model = employee_models.User

    user_id = Generators.UuidObj
    email_address = Generators.EmailAddress
    auth_id = factory.Faker("uuid4")

    @factory.post_generation
    def roles(self, create, extracted, **kwargs):
        if not create:
            return

        if extracted:
            for role in extracted:
                lk_role = lookups.employees.Role.get_instance(db_session, template=role)
                self.roles.append(lk_role)
                get_db_session().commit()


class UserWithNameFactory(BaseFactory):
    class Meta:
        model = employee_models.User

    user_id = Generators.UuidObj
    email_address = Generators.EmailAddress
    auth_id = factory.Faker("uuid4")
    first_name = factory.Faker("first_name")
    last_name = factory.Faker("last_name")

    @factory.post_generation
    def roles(self, create, extracted, **kwargs):
        if not create:
            return

        if extracted:
            for role in extracted:
                lk_role = lookups.employees.Role.get_instance(db_session, template=role)
                self.roles.append(lk_role)
                get_db_session().commit()


class UserAuthLogFactory(BaseFactory):
    class Meta:
        model = oauth_models.UserAuthLog

    user_id = Generators.UuidObj
    oauth_operation_id = 1
    started_at = datetime.now()
    completed_at = datetime.now()


class EmployerOnlyRequiredFactory(BaseFactory):
    class Meta:
        model = employee_models.Employer

    employer_id = Generators.UuidObj
    employer_fein = Generators.Fein
    employer_dba = factory.Faker("company")


class EmployerOnlyDORDataFactory(EmployerOnlyRequiredFactory):
    account_key = Generators.AccountKey
    employer_name = factory.Faker("company")
    family_exemption = factory.Faker("boolean", chance_of_getting_true=0.1)
    medical_exemption = factory.Faker("boolean", chance_of_getting_true=0.1)

    # Address info not implemented

    exemption_commence_date = factory.LazyAttribute(
        lambda e: (
            factory.Faker("date_between_dates", start_date=date(2020, 1, 1), end_date="next year")
            if e.family_exemption or e.medical_exemption
            else None
        )
    )
    exemption_cease_date = factory.LazyAttribute(
        lambda e: (
            factory.Faker(
                "date_between_dates",
                start_date=e.exemption_commence_date,
                end_date=e.exemption_commence_date + timedelta(weeks=52),
            )
            if e.exemption_commence_date
            else None
        )
    )


class EmployerFactory(EmployerOnlyDORDataFactory):
    fineos_employer_id = factory.LazyAttribute(
        lambda e: massgov.pfml.fineos.mock.field.fake_customer_no(e.employer_fein)
    )
    mtc_number = factory.Faker("numerify", text="MTC###########")


class TaxIdentifierFactory(BaseFactory):
    class Meta:
        model = employee_models.TaxIdentifier

    tax_identifier_id = Generators.UuidObj
    tax_identifier = Generators.Tin


class EftFactory(BaseFactory):
    class Meta:
        model = employee_models.EFT

    eft_id = Generators.UuidObj
    account_nbr = "*********"
    routing_nbr = "*********"
    bank_account_type_id = lookups.employees.BankAccountType.CHECKING.bank_account_type_id


class PubEftFactory(BaseFactory):
    class Meta:
        model = employee_models.PubEft

    pub_eft_id = Generators.UuidObj
    routing_nbr = fake.aba()  # "American Bankers Association": a valid bank routing number
    account_nbr = factory.Sequence(lambda n: "%011d" % n)
    bank_account_type_id = lookups.employees.BankAccountType.CHECKING.bank_account_type_id
    prenote_state_id = lookups.employees.PrenoteState.PENDING_PRE_PUB.prenote_state_id
    prenote_response_at = Generators.UtcNow


class EmployeeOnlyDORDataFactory(BaseFactory):
    class Meta:
        model = employee_models.Employee

    employee_id = Generators.UuidObj

    tax_identifier = factory.SubFactory(TaxIdentifierFactory)
    tax_identifier_id = factory.LazyAttribute(lambda t: t.tax_identifier.tax_identifier_id)
    first_name = factory.Faker("first_name")
    last_name = factory.Faker("last_name")


class EmployeeFactory(EmployeeOnlyDORDataFactory):
    middle_name = factory.Faker("first_name")
    other_name = None
    email_address = factory.Faker("email")
    phone_number = "+***********"
    ctr_vendor_customer_code = "VC0001201168"
    gender_id = None
    fineos_employee_first_name = factory.LazyAttribute(lambda e: e.first_name)
    fineos_employee_last_name = factory.LazyAttribute(lambda e: e.last_name)


class EmployeeWithFineosNumberFactory(EmployeeFactory):
    date_of_birth = factory.Faker("date_of_birth", minimum_age=14, maximum_age=100)
    fineos_customer_number = factory.Faker("numerify", text="####")


class EmployeePubEftPairFactory(BaseFactory):
    class Meta:
        model = employee_models.EmployeePubEftPair

    employee = factory.SubFactory(EmployeeWithFineosNumberFactory)
    employee_id = factory.LazyAttribute(lambda e: e.employee.employee_id)

    pub_eft = factory.SubFactory(PubEftFactory)
    pub_eft_id = factory.LazyAttribute(lambda p: p.pub_eft.pub_eft_id)


class CtrBatchIdentifierFactory(BaseFactory):
    class Meta:
        model = massgov.pfml.db.models.ctr.batch_identifier.CtrBatchIdentifier

    ctr_batch_identifier_id = Generators.UuidObj
    ctr_batch_identifier = Generators.VccBatchId
    year = datetime.now().year
    batch_date = datetime.now()
    batch_counter = Generators.VccBatchCounter
    inf_data = {
        "NewMmarsBatchID": "EOL0101GAX11",
        "NewMmarsBatchDeptCode": "EOL",
        "NewMmarsUnitCode": "8780",
        "NewMmarsImportDate": "2020-01-01",
        "NewMmarsTransCode": "GAX",
        "NewMmarsTableName": "",
        "NewMmarsTransCount": "2",
        "NewMmarsTransDollarAmount": "2500.00",
    }


class CtrDocumentIdentifierFactory(BaseFactory):
    class Meta:
        model = massgov.pfml.db.models.ctr.document_identifier.CtrDocumentIdentifier

    ctr_document_identifier_id = Generators.UuidObj
    ctr_document_identifier = Generators.VccDocId
    document_counter = Generators.VccDocCounter
    document_date = Generators.Now


class ReferenceFileFactory(BaseFactory):
    class Meta:
        model = massgov.pfml.db.models.reference_file.reference_file.ReferenceFile

    reference_file_id = Generators.UuidObj
    file_location = Generators.S3Path
    reference_file_type_id = ReferenceFileType.PAYMENT_EXTRACT.reference_file_type_id


class EmployeeReferenceFileFactory(BaseFactory):
    class Meta:
        model = EmployeeReferenceFile

    employee = factory.SubFactory(EmployeeFactory)
    employee_id = factory.LazyAttribute(lambda e: e.employee.employee_id)

    reference_file = factory.SubFactory(ReferenceFileFactory)
    reference_file_id = factory.LazyAttribute(lambda e: e.reference_file.reference_file_id)
    reference_file.reference_file_type_id = ReferenceFileType.VCC.reference_file_type_id

    ctr_document_identifier = factory.SubFactory(CtrDocumentIdentifierFactory)
    ctr_document_identifier_id = factory.LazyAttribute(
        lambda e: e.ctr_document_identifier.ctr_document_identifier_id
    )


class VerificationFactory(BaseFactory):
    class Meta:
        model = verification_models.Verification

    created_at = Generators.UtcNow
    updated_at = Generators.UtcNow
    verification_id = Generators.UuidObj
    verification_type_id = (
        lookups.verifications.VerificationType.VERIFICATION_CODE.verification_type_id
    )
    verification_metadata = factory.Faker("json")


class EmployerQuarterlyContributionFactory(BaseFactory):
    class Meta:
        model = employee_models.EmployerQuarterlyContribution

    employer = factory.SubFactory(EmployerFactory)
    employer_id = factory.LazyAttribute(lambda w: w.employer.employer_id)
    filing_period = datetime.now().strftime("%Y-%m-%d")
    employer_total_pfml_contribution = factory.Faker("random_int", min=1)
    pfm_account_id = factory.Faker("random_int")


class EmployeePushToFineosQueueFactory(BaseFactory):
    class Meta:
        model = employee_models.EmployeePushToFineosQueue

    employee_push_to_fineos_queue_id = Generators.UuidObj
    employee_id = None
    employer_id = None
    action = "UPDATE_NEW_EMPLOYER"
    modified_at = Generators.UtcNow


class EmployerPushToFineosQueueFactory(BaseFactory):
    class Meta:
        model = employee_models.EmployerPushToFineosQueue

    employer_push_to_fineos_queue_id = Generators.UuidObj
    employer_id = None
    action = "INSERT"
    modified_at = Generators.UtcNow
    family_exemption = None
    medical_exemption = None
    exemption_commence_date = None
    exemption_cease_date = None


class WagesAndContributionsFactory(BaseFactory):
    class Meta:
        model = employee_models.WagesAndContributions

    wage_and_contribution_id = Generators.UuidObj
    account_key = Generators.AccountKey
    filing_period = factory.Faker("date_object")
    is_independent_contractor = False
    is_opted_in = False
    employee_ytd_wages = Generators.Money
    employee_qtr_wages = Generators.Money
    employee_med_contribution = Generators.Money
    employer_med_contribution = Generators.Money
    employee_fam_contribution = Generators.Money
    employer_fam_contribution = Generators.Money

    employer = factory.SubFactory(EmployerFactory)
    employer_id = factory.LazyAttribute(lambda w: w.employer.employer_id)

    employee = factory.SubFactory(EmployeeFactory)
    employee_id = factory.LazyAttribute(lambda w: w.employee.employee_id)

    wages_and_contributions_datasource_id = (
        lookups.employees.WagesAndContributionsDatasource.DFML_REPORTED_WAGES.wages_and_contributions_datasource_id
    )


class WagesAndContributionsHistoryFactory(BaseFactory):
    class Meta:
        model = employee_models.WagesAndContributionsHistory

    wages_and_contributions_history_id = Generators.UuidObj
    wage_and_contribution = factory.SubFactory(WagesAndContributionsFactory)

    wage_and_contribution_id = factory.LazyAttribute(
        lambda w: w.wage_and_contribution.wage_and_contribution_id
    )
    is_independent_contractor = factory.LazyAttribute(
        lambda w: w.wage_and_contribution.is_independent_contractor
    )
    is_opted_in = factory.LazyAttribute(lambda w: w.wage_and_contribution.is_opted_in)
    employee_ytd_wages = factory.LazyAttribute(lambda w: w.wage_and_contribution.employee_ytd_wages)
    employee_qtr_wages = factory.LazyAttribute(lambda w: w.wage_and_contribution.employee_qtr_wages)
    employee_med_contribution = factory.LazyAttribute(
        lambda w: w.wage_and_contribution.employee_med_contribution
    )
    employer_med_contribution = factory.LazyAttribute(
        lambda w: w.wage_and_contribution.employer_med_contribution
    )
    employee_fam_contribution = factory.LazyAttribute(
        lambda w: w.wage_and_contribution.employee_fam_contribution
    )
    employer_fam_contribution = factory.LazyAttribute(
        lambda w: w.wage_and_contribution.employer_fam_contribution
    )
    import_log_id = factory.LazyAttribute(lambda w: w.wage_and_contribution.latest_import_log_id)

    wages_and_contributions_datasource_id = factory.LazyAttribute(
        lambda w: w.wage_and_contribution.wages_and_contributions_datasource_id
    )


class OrganizationUnitFactory(BaseFactory):
    class Meta:
        model = OrganizationUnit

    organization_unit_id = Generators.UuidObj

    fineos_id = factory.Faker("numerify", text="PE:#####:##########")
    name = factory.Faker("company")
    employer_id = Generators.UuidObj


class ClaimFactory(BaseFactory):
    class Meta:
        model = employee_models.Claim

    claim_id = Generators.UuidObj

    authorized_representative_id = None
    claim_type_id = None
    benefit_days = 60
    fineos_absence_id = Generators.FineosAbsenceId
    fineos_notification_id = None
    employee = factory.SubFactory(EmployeeFactory)
    employee_id = factory.LazyAttribute(lambda w: w.employee.employee_id if w.employee else None)
    organization_unit = None
    organization_unit_id = factory.LazyAttribute(
        lambda w: w.organization_unit.organization_unit_id if w.organization_unit else None
    )

    # TODO:
    # remove once is_id_proofed and claim_type_id are permanently hard
    # deleted from claim model
    @classmethod
    def create_with_leave_request_and_absence_period(cls, **kwargs):
        """Create an instance of the associated class, with overridden attrs."""
        claim: employee_models.Claim = super().create(**kwargs)

        if claim.is_id_proofed is True:
            LeaveRequestFactory.create(claim_id=claim.claim_id, is_id_proofed=True)

        if claim.claim_type_id is not None:
            claim_type_to_absence_map = {
                lookups.employees.ClaimType.FAMILY_LEAVE.claim_type_id: lookups.employees.AbsenceReason.CARE_OF_A_FAMILY_MEMBER.absence_reason_id,
                lookups.employees.ClaimType.MEDICAL_LEAVE.claim_type_id: lookups.employees.AbsenceReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.absence_reason_id,
            }
            AbsencePeriodFactory.create(
                claim=claim,
                absence_reason_id=claim_type_to_absence_map.get(claim.claim_type_id, None),
                absence_period_start_date=date.today() + timedelta(days=5),
                absence_period_end_date=date.today() + timedelta(days=20),
            )

        return claim

    @classmethod
    def create_with_leave_request_and_absence_period_types(cls, absence_reason_id, **kwargs):
        """Create an instance of the associated class, with overridden attrs."""
        claim: employee_models.Claim = super().create(**kwargs)

        if claim.is_id_proofed is True:
            LeaveRequestFactory.create(claim_id=claim.claim_id, is_id_proofed=True)

        AbsencePeriodFactory.create(
            claim=claim,
            absence_reason_id=absence_reason_id,
            absence_period_start_date=date.today() + timedelta(days=5),
            absence_period_end_date=date.today() + timedelta(days=20),
        )

        return claim

    @classmethod
    def create_claim_with_application_and_appeal(cls, user, **kwargs):
        """Create an instance of the associated class, with overridden attrs."""
        claim: employee_models.Claim = super().create(**kwargs)

        application = ApplicationFactory.create(
            user=user,
            claim=claim,
        )

        appeal = AppealFactory.create(
            claim=claim,
        )

        claim.application = application
        claim.appeal = appeal

        return claim


class AbsencePeriodFactory(BaseFactory):
    class Meta:
        model = absence_models.AbsencePeriod

    absence_period_id = Generators.UuidObj
    claim = factory.SubFactory(ClaimFactory)
    claim_id = factory.LazyAttribute(lambda w: w.claim.claim_id)
    fineos_leave_request_id = factory.Faker("random_int")
    absence_period_start_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 1, 1), date_end=date(2021, 1, 15)
    )
    absence_period_end_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 1, 16), date_end=date(2021, 1, 28)
    )
    leave_request_decision_id = (
        lookups.employees.LeaveRequestDecision.APPROVED.leave_request_decision_id
    )
    absence_period_type_id = 1
    absence_reason_id = 1
    absence_reason_qualifier_one_id = 1
    absence_reason_qualifier_two_id = 1
    created_at = datetime.now()
    updated_at = datetime.now()
    fineos_absence_period_class_id = factory.Faker("random_int")
    fineos_absence_period_index_id = factory.Faker("random_int")


class LeaveRequestFactory(BaseFactory):
    class Meta:
        model = employee_models.LeaveRequest

    leave_request_id = Generators.UuidObj
    fineos_leave_request_id = factory.Sequence(lambda n: n)
    claim_id = factory.LazyAttribute(lambda w: w.claim.claim_id)
    absence_reason_id = 1
    leave_approval_decision_id = 1
    eligibility_decision_id = 1
    is_id_proofed = False


class AbsencePaidLeaveCaseFactory(BaseFactory):
    class Meta:
        model = employee_models.AbsencePaidLeaveCase

    absence_paid_leave_case_id = Generators.UuidObj
    absence_paid_leave_case_number = factory.Faker("numerify", text="PL ABS-######")
    claim = factory.SubFactory(ClaimFactory)
    claim_id = factory.LazyAttribute(lambda w: w.claim.claim_id)
    leave_request_id = Generators.UuidObj
    start_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 1, 1), date_end=date(2021, 1, 15)
    )
    end_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 1, 16), date_end=date(2021, 1, 28)
    )
    average_weekly_wage = factory.Faker("random_int")


class PendingAbsencePeriodFactory(AbsencePeriodFactory):
    leave_request_decision_id = (
        lookups.employees.LeaveRequestDecision.PENDING.leave_request_decision_id
    )


class ManagedRequirementFactory(BaseFactory):
    class Meta:
        model = employee_models.ManagedRequirement

    managed_requirement_id = Generators.UuidObj
    claim = factory.SubFactory(ClaimFactory)
    claim_id = factory.LazyAttribute(lambda w: w.claim.claim_id)
    respondent_user = factory.SubFactory(UserWithNameFactory)
    respondent_user_id = factory.LazyAttribute(lambda w: w.respondent_user.user_id)
    fineos_managed_requirement_id = factory.Sequence(lambda n: n)
    follow_up_date = factory.LazyAttribute(lambda w: w.claim.created_at.date() + timedelta(days=10))
    managed_requirement_status_id = (
        lookups.employees.ManagedRequirementStatus.OPEN.managed_requirement_status_id
    )
    managed_requirement_category_id = (
        lookups.employees.ManagedRequirementCategory.EMPLOYER_CONFIRMATION.managed_requirement_category_id
    )
    managed_requirement_type_id = (
        lookups.employees.ManagedRequirementType.EMPLOYER_CONFIRMATION.managed_requirement_type_id
    )


class OpenManagedRequirementFactory(ManagedRequirementFactory):
    managed_requirement_status_id = (
        lookups.employees.ManagedRequirementStatus.OPEN.managed_requirement_status_id
    )


class FineosExtractVpeiFactory(BaseFactory):
    class Meta:
        model = payment_models.FineosExtractVpei

    c = "7326"
    i = factory.Sequence(lambda n: "%d" % n)
    amount_monamt = Generators.Money


class FineosExtractVbiDocumentDeltaSomFactory(BaseFactory):
    class Meta:
        model = payment_models.FineosExtractVbiDocumentDeltaSom

    casenumber = factory.Sequence(lambda n: f"PL-2022-{n}")
    reference_file = factory.SubFactory(ReferenceFileFactory)
    reference_file_id = factory.LazyAttribute(lambda e: e.reference_file.reference_file_id)
    created_at = factory.LazyFunction(datetime.now)
    updated_at = Generators.UtcNow
    classid = factory.Faker("word")
    createdbyuserid = factory.Faker("user_name")
    creationdate = factory.Faker("date")
    c_ocemail_attachments = factory.Faker("word")
    c_ocprty_documents = factory.Faker("word")
    description = factory.Faker("sentence")
    documenttype = factory.Faker("word")
    indexid = factory.Faker("word")
    iskeydocument = factory.Faker("word")
    i_ocemail_attachments = factory.Faker("word")
    i_ocprty_documents = factory.Faker("word")
    lastupdatedate = factory.Faker("date")
    privacytag = factory.Faker("word")
    receiveddate = factory.Faker("date")
    status = factory.Faker("word")
    updatedbyuserid = factory.Faker("user_name")


class FineosExtractVbiDocumentSomFactory(BaseFactory):
    class Meta:
        model = payment_models.FineosExtractVbiDocumentSom

    casenumber = factory.Sequence(lambda n: f"PL-2022-{n}")
    reference_file = factory.SubFactory(ReferenceFileFactory)
    reference_file_id = factory.LazyAttribute(lambda e: e.reference_file.reference_file_id)
    created_at = factory.LazyFunction(datetime.now)
    updated_at = Generators.UtcNow
    classid = factory.Faker("word")
    createdbyuserid = factory.Faker("user_name")
    creationdate = factory.Faker("date")
    c_ocemail_attachments = factory.Faker("word")
    c_ocprty_documents = factory.Faker("word")
    description = factory.Faker("sentence")
    documenttype = factory.Faker("word")
    indexid = factory.Faker("word")
    iskeydocument = factory.Faker("word")
    i_ocemail_attachments = factory.Faker("word")
    i_ocprty_documents = factory.Faker("word")
    lastupdatedate = factory.Faker("date")
    privacytag = factory.Faker("word")
    receiveddate = factory.Faker("date")
    status = factory.Faker("word")
    updatedbyuserid = factory.Faker("user_name")


class FineosExtractVbiDocumentSomFullRawFactory(BaseFactory):
    class Meta:
        model = payment_models.FineosExtractVbiDocumentFullRaw

    casenumber = factory.Sequence(lambda n: f"PL-2022-{n}")
    reference_file = factory.SubFactory(ReferenceFileFactory)
    reference_file_id = factory.LazyAttribute(lambda e: e.reference_file.reference_file_id)
    created_at = factory.LazyFunction(datetime.now)
    updated_at = Generators.UtcNow
    classid = factory.Faker("word")
    createdbyuserid = factory.Faker("user_name")
    creationdate = factory.Faker("date")
    c_ocemail_attachments = factory.Faker("word")
    c_ocprty_documents = factory.Faker("word")
    description = factory.Faker("sentence")
    documenttype = factory.Faker("word")
    indexid = factory.Faker("word")
    iskeydocument = factory.Faker("word")
    i_ocemail_attachments = factory.Faker("word")
    i_ocprty_documents = factory.Faker("word")
    lastupdatedate = factory.Faker("date")
    privacytag = factory.Faker("word")
    receiveddate = factory.Faker("date")
    status = factory.Faker("word")
    updatedbyuserid = factory.Faker("user_name")


class FineosExtractVbiRequestedAbsenceFactory(BaseFactory):
    class Meta:
        model = payment_models.FineosExtractVbiRequestedAbsence

    vbi_requested_absence_serial_id = factory.Sequence(lambda n: n)
    reference_file = factory.SubFactory(ReferenceFileFactory)
    reference_file_id = factory.LazyAttribute(lambda e: e.reference_file.reference_file_id)
    absence_casenumber = Generators.FineosAbsenceId
    absenceperiod_classid = factory.Faker("random_int")
    absenceperiod_indexid = factory.Faker("random_int")
    absenceperiod_start = factory.Faker(
        "date_between_dates", date_start=date(2021, 1, 1), date_end=date(2021, 1, 15)
    )
    absenceperiod_end = factory.Faker(
        "date_between_dates", date_start=date(2021, 1, 16), date_end=date(2021, 1, 28)
    )
    leaverequest_id = factory.Faker("random_int")


class FineosExtractVbiLeaveSummaryFactory(BaseFactory):
    class Meta:
        model = payment_models.FineosExtractVbiLeaveSummary

    reference_file = factory.SubFactory(ReferenceFileFactory)
    reference_file_id = factory.LazyAttribute(lambda e: e.reference_file.reference_file_id)
    absence_casenumber = Generators.FineosAbsenceId
    leaverequest_classid = factory.Faker("random_int")
    leaverequest_indexid = factory.Faker("random_int")
    leaverequest_id = factory.Faker("random_int")
    notification_casenumber = Generators.FineosNotificationId


class FineosExtractServiceAgreementFactory(BaseFactory):
    class Meta:
        model = FineosExtractServiceAgreement

    service_agreement_id = factory.Faker("random_int")  # BigInt instead of UUID
    last_update_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    employer_customerno = factory.LazyFunction(lambda: str(factory.Faker("random_int")))
    serviceagreement_no = factory.LazyFunction(lambda: "SA-%s" % random.randint(0, 999999))
    effectivedate = date.today().strftime("%Y-%m-%d %H:%M:%S")
    enddate = ""
    leaveplan = factory.LazyFunction(
        lambda: random.choice([leaveplan.value for leaveplan in ServiceAgreementLeavePlans])
    )
    version_date = date.today().strftime("%Y-%m-%d %H:%M:%S")
    status = "Active"
    absence_management_servicing = "Carrier"
    reference_file = factory.SubFactory(
        ReferenceFileFactory,
        reference_file_type_id=ReferenceFileType.FINEOS_SERVICE_AGREEMENT_EXTRACT.reference_file_type_id,
    )
    reference_file_id = factory.LazyAttribute(lambda e: e.reference_file.reference_file_id)

    @classmethod
    def _adjust_kwargs(cls, **kwargs):
        is_fineos_datetime_or_empty(kwargs["effectivedate"], "effectivedate")
        is_fineos_datetime_or_empty(kwargs["enddate"], "enddate")
        is_fineos_datetime_or_empty(kwargs["version_date"], "version_date")
        is_fineos_datetime_or_empty(kwargs["last_update_date"], "last_update_date")
        return kwargs


DATETIME_STRING = re.compile(r"\d\d\d\d-\d\d-\d\d \d\d:\d\d:\d\d")


def is_fineos_datetime_or_empty(s, attr_name):
    if type(s) is not str:
        raise TypeError(f"{attr_name} must be a str")
    if s == "":
        return
    if DATETIME_STRING.fullmatch(s):
        return
    raise ValueError(f"invalid {attr_name} {s!r}: must be YYYY-MM-DD HH:MM:SS or empty")


class FineosExtractVbiRequestedAbsenceSomFactory(BaseFactory):
    class Meta:
        model = payment_models.FineosExtractVbiRequestedAbsenceSom

    vbi_requested_absence_som_serial_id = factory.Sequence(lambda n: n)

    absenceperiod_classid = factory.Faker("random_int")
    absenceperiod_indexid = factory.Faker("random_int")

    employer_customerno = factory.Faker("random_int")
    employee_customerno = factory.Faker("random_int")
    absence_casenumber = Generators.FineosAbsenceId
    notification_casenumber = Generators.FineosNotificationId
    absencereason_coverage = "Employee"

    orgunit_name = "Mass Healthcare"

    reference_file = factory.SubFactory(ReferenceFileFactory)
    reference_file_id = factory.LazyAttribute(lambda e: e.reference_file.reference_file_id)


class FineosExtractVbiTaskReportSomFactory(BaseFactory):
    class Meta:
        model = payment_models.FineosExtractVbiTaskReportSom

    casenumber = Generators.FineosAbsenceId
    taskid = factory.Sequence(lambda n: "%d" % n)

    reference_file = factory.SubFactory(
        ReferenceFileFactory,
        reference_file_type_id=ReferenceFileType.FINEOS_VBI_TASKREPORT_SOM_EXTRACT.reference_file_type_id,
    )
    reference_file_id = factory.LazyAttribute(lambda a: a.reference_file.reference_file_id)


class FineosExtractVbiTaskReportDeltaSomFactory(BaseFactory):
    class Meta:
        model = payment_models.FineosExtractVbiTaskReportDeltaSom

    casenumber = Generators.FineosAbsenceId
    taskid = factory.Sequence(lambda n: "%d" % n)

    reference_file = factory.SubFactory(
        ReferenceFileFactory,
        reference_file_type_id=ReferenceFileType.FINEOS_VBI_TASKREPORT_DELTA_SOM_EXTRACT.reference_file_type_id,
    )
    reference_file_id = factory.LazyAttribute(lambda a: a.reference_file.reference_file_id)


class FineosExtractVpeiPaymentDetailsFactory(BaseFactory):
    class Meta:
        model = payment_models.FineosExtractVpeiPaymentDetails

    c = "7806"
    i = factory.Sequence(lambda n: "%d" % n)
    balancingamou_monamt = Generators.Money


class FineosExtractVpeiClaimDetailsFactory(BaseFactory):
    class Meta:
        model = payment_models.FineosExtractVpeiClaimDetails

    c = "claim-details-c-value"
    i = factory.Sequence(lambda n: n)
    leaverequesti = factory.Sequence(lambda n: n)


class FineosExtractVpeiPaymentLineFactory(BaseFactory):
    class Meta:
        model = payment_models.FineosExtractVpeiPaymentLine

    c = "7692"
    i = factory.Sequence(lambda n: "%d" % n)

    amount_monamt = Generators.Money
    linetype = "Auto Gross Entitlement"


class PaymentFactory(BaseFactory):
    class Meta:
        model = employee_models.Payment

    payment_id = Generators.UuidObj

    amount = Generators.Money
    # Using dates set in Jan 2021 similar to magic dates in other factories, such as
    # IntermittentLeavePeriodFactory and ContinuousLeavePeriodFactory.
    # TODO: We should see if we can convert all dates made by factories to be
    #       more random.
    period_start_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 1, 1), date_end=date(2021, 1, 15)
    )
    period_end_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 1, 16), date_end=date(2021, 1, 28)
    )
    payment_date = factory.LazyAttribute(lambda a: a.period_end_date - timedelta(days=1))
    # Magic number: the C value is the same for all payments, but it doesn't actually
    # matter what number it is, so picking a static number is fine.
    fineos_pei_c_value = "9000"
    # The I value is unique for all payments and should be a string, not an int.
    fineos_pei_i_value = factory.Sequence(lambda n: "%d" % n)

    fineos_leave_request_id = factory.Faker("random_int")

    claim = factory.SubFactory(ClaimFactory)
    claim_id = factory.LazyAttribute(lambda a: a.claim.claim_id if a.claim else None)

    fineos_employee_first_name = factory.Faker("first_name")
    fineos_employee_last_name = factory.Faker("last_name")

    employee = None
    employee_id = factory.LazyAttribute(lambda a: a.employee.employee_id if a.employee else None)

    payee_name = factory.Faker("company")
    payment_transaction_type_id = (
        lookups.employees.PaymentTransactionType.STANDARD.payment_transaction_type_id
    )

    @classmethod
    def with_latest_state_log(
        cls, state_log: Optional[dict] = None, **kwargs: dict
    ) -> employee_models.Payment:

        payment: employee_models.Payment = super().create(**kwargs)

        latest_state = StateLogFactory.create(
            **dict(
                employee=payment.employee,
                payment=payment,
                associated_type=AssociatedClass.PAYMENT.value,
                **(state_log or {}),
            )
        )

        LatestStateLogFactory.create(
            state_log_id=latest_state.state_log_id,
            payment_id=payment.payment_id,
            employee_id=payment.employee_id,
            claim_id=payment.claim_id,
        )

        return payment


class PaymentDetailsFactory(BaseFactory):
    class Meta:
        model = employee_models.PaymentDetails

    payment_details_id = Generators.UuidObj

    payment = factory.SubFactory(PaymentFactory)
    payment_id = factory.LazyAttribute(lambda a: a.payment.payment_id)

    period_start_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 1, 1), date_end=date(2021, 1, 15)
    )
    period_end_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 1, 16), date_end=date(2021, 1, 28)
    )

    payment_details_c_value = "7806"
    payment_details_i_value = factory.Sequence(lambda n: "%d" % n)

    amount = Generators.Money
    business_net_amount = factory.LazyAttribute(lambda a: a.amount)


class PaymentLineFactory(BaseFactory):
    class Meta:
        model = payment_models.PaymentLine

    payment_line_id = Generators.UuidObj

    payment = factory.SubFactory(PaymentFactory)
    payment_id = factory.LazyAttribute(lambda a: a.payment.payment_id)

    payment_details = factory.SubFactory(PaymentDetailsFactory)
    payment_details_id = factory.LazyAttribute(
        lambda a: a.payment_details.payment_details_id if a.payment_details else None
    )

    payment_line_c_value = "7692"
    payment_line_i_value = factory.Sequence(lambda n: "%d" % n)

    amount = Generators.Money
    line_type = "Auto Gross Entitlement"

    vpei_payment_line = factory.SubFactory(FineosExtractVpeiPaymentLineFactory)
    vpei_payment_line_id = factory.LazyAttribute(lambda a: a.vpei_payment_line.vpei_payment_line_id)


class PaymentReferenceFileFactory(BaseFactory):
    class Meta:
        model = PaymentReferenceFile

    payment = factory.SubFactory(PaymentFactory)
    payment_id = factory.LazyAttribute(lambda a: a.payment.payment_id)

    reference_file = factory.SubFactory(ReferenceFileFactory)
    reference_file_id = factory.LazyAttribute(lambda a: a.reference_file.reference_file_id)

    ctr_document_identifier = factory.SubFactory(CtrDocumentIdentifierFactory)
    ctr_document_identifier_id = factory.LazyAttribute(
        lambda a: a.ctr_document_identifier.ctr_document_identifier_id
    )


class FineosExtractVbiEntitlementPeriodSomFactory(BaseFactory):
    class Meta:
        model = payment_models.FineosExtractVbiEntitlementPeriodSom

    vbi_entitlement_period_som_id = Generators.UuidObj
    employee_number = factory.LazyFunction(lambda: str(random.randint(0, 99999)))
    employer_number = factory.LazyFunction(lambda: str(random.randint(0, 99999)))
    assoicated_leave_plan = "family"

    # Random date following the Fineos extract format
    periodstartdate = factory.LazyFunction(
        lambda: fake.date_between(
            start_date=date(2020, 1, 1), end_date=date(2022, 12, 31)
        ).strftime("%Y-%m-%d %H:%M:%S")
    )

    # 52 weeks after periodstartdate
    periodenddate = factory.LazyAttribute(
        lambda a: (
            datetime.strptime(a.periodstartdate, "%Y-%m-%d %H:%M:%S") + timedelta(weeks=52, days=-1)
        ).strftime("%Y-%m-%d %H:%M:%S")
    )


class OverpaymentFactory(BaseFactory):
    class Meta:
        model = employee_models.Overpayment

    overpayment_id = Generators.UuidObj

    claim = factory.SubFactory(ClaimFactory)
    claim_id = factory.LazyAttribute(lambda a: a.claim.claim_id)

    period_start_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 1, 1), date_end=date(2021, 1, 15)
    )
    period_end_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 1, 16), date_end=date(2021, 1, 28)
    )
    overpayment_date = factory.LazyAttribute(lambda a: a.period_end_date - timedelta(days=1))
    amount = Generators.Money
    fineos_pei_c_value = "9000"
    fineos_pei_i_value = factory.Sequence(lambda n: "%d" % n)

    claim_type_id = lookups.employees.ClaimType.FAMILY_LEAVE.claim_type_id
    payment_event_type_id = lookups.payments.PaymentEventType.OVERPAYMENT.payment_event_type_id

    absence_case_creation_date = "2022-12-01 07:00:00"

    leave_request = factory.SubFactory(AbsencePeriodFactory)
    leave_request_id = factory.LazyAttribute(lambda a: a.leave_request.absence_period_id)

    fineos_employee_first_name = factory.Faker("first_name")
    fineos_employee_last_name = factory.Faker("last_name")
    fineos_employee_middle_name = ""

    employee = None
    employee_id = factory.LazyAttribute(lambda a: a.employee.employee_id if a.employee else None)

    fineos_extract_vpei = factory.SubFactory(FineosExtractVpeiFactory)
    vpei_id = factory.LazyAttribute(lambda a: a.fineos_extract_vpei.vpei_id)

    fineos_leave_request_id = factory.Faker("random_int")
    fineos_extraction_date = "2022-12-01 07:00:00"

    fineos_extract_import_log = None
    fineos_extract_import_log_id = factory.LazyAttribute(
        lambda a: (
            a.fineos_extract_import_log.fineos_extract_import_log_id
            if a.fineos_extract_import_log
            else None
        )
    )

    payment_relevant_party_id = (
        lookups.employees.PaymentRelevantParty.DEPARTMENT_OF_REVENUE.payment_relevant_party_id
    )

    payment_transaction_type_id = (
        lookups.employees.PaymentTransactionType.OVERPAYMENT.payment_transaction_type_id
    )


class OverpaymentRepaymentFactory(BaseFactory):
    class Meta:
        model = employee_models.OverpaymentRepayment

    overpayment_repayment_id = Generators.UuidObj
    cancelled_overpayment_repayment_id = Generators.UuidObj
    overpayment_repayment_date = "2022-12-01 07:00:00"
    amount = Generators.Money
    fineos_pei_c_value = "9000"
    fineos_pei_i_value = factory.Sequence(lambda n: "%d" % n)
    payment_event_type_id = (
        lookups.payments.PaymentEventType.OVERPAYMENT_RECOVERY.payment_event_type_id
    )
    overpayment_recovery_type_id = None

    fineos_employee_first_name = factory.Faker("first_name")
    fineos_employee_last_name = factory.Faker("last_name")
    fineos_employee_middle_name = None
    fineos_extraction_date = "2022-12-01 07:00:00"

    employee = None
    employee_id = factory.LazyAttribute(lambda a: a.employee.employee_id if a.employee else None)

    fineos_extract_vpei = factory.SubFactory(FineosExtractVpeiFactory)
    vpei_id = factory.LazyAttribute(lambda a: a.fineos_extract_vpei.vpei_id)
    fineos_extract_import_log = None
    fineos_extract_import_log_id = factory.LazyAttribute(
        lambda a: (
            a.fineos_extract_import_log.fineos_extract_import_log_id
            if a.fineos_extract_import_log
            else None
        )
    )


class OverpaymentAdjustmentFactory(BaseFactory):
    class Meta:
        model = employee_models.OverpaymentAdjustment

    overpayment_adjustment_id = Generators.UuidObj
    overpaymentcase_overpayment = factory.SubFactory(OverpaymentFactory)
    overpaymentcase_overpayment_id = factory.LazyAttribute(
        lambda a: a.overpaymentcase_overpayment.overpayment_id
    )
    adjustment_amount = Generators.Money
    adjustment_agreement_date = factory.Faker("date_object")
    adjustment_description = factory.Faker("sentence")
    overpayment_adjustment_payment_status_id = (
        lookups.payments.OverpaymentAdjustmentPaymentStatus.ACTIVE.overpayment_adjustment_payment_status_id
    )


class PhoneFactory(BaseFactory):
    """A working Cell phone number"""

    class Meta:
        model = phone_models.Phone

    phone_number = "+12404879945"
    phone_type_id = lookups.phone.PhoneType.CELL.phone_type_id


class LeaveReasonFactory(BaseFactory):
    class Meta:
        model = application_models.LkLeaveReason

    leave_reason_id = None
    leave_reason_description = None


class ApplicationFactory(BaseFactory):
    class Meta:
        model = application_models.Application

    application_id = Generators.UuidObj

    requestor = None
    first_name = factory.Faker("first_name")
    last_name = factory.Faker("last_name")
    middle_name = None
    date_of_birth = factory.Faker("date_of_birth", minimum_age=14, maximum_age=100)
    has_state_id = False
    mass_id = None
    has_mailing_address = False
    pregnant_or_recent_birth = False
    child_birth_date = None
    child_placement_date = None
    employer_notified = False
    employer_notification_date = None
    completed_time = None
    submitted_time = None
    is_withholding_tax = None

    # Leave Periods
    has_continuous_leave_periods = False
    has_intermittent_leave_periods = False
    has_reduced_schedule_leave_periods = False

    # Other leaves
    has_employer_benefits = False
    has_other_incomes = False
    has_previous_leaves = False
    has_concurrent_employers = False

    # Relationships
    user = factory.SubFactory(UserFactory)
    user_id = factory.LazyAttribute(lambda a: a.user.user_id)

    employer_fein = Generators.Fein

    tax_identifier = factory.SubFactory(TaxIdentifierFactory)
    tax_identifier_id = factory.LazyAttribute(lambda t: t.tax_identifier.tax_identifier_id)

    payment_preference = None
    payment_preference_id = factory.LazyAttribute(
        lambda t: t.payment_preference.payment_pref_id if t.payment_preference else None
    )

    phone = factory.SubFactory(PhoneFactory)

    # Lookups
    occupation_id = None
    relationship_to_caregiver_id = None
    relationship_qualifier_id = None
    employer_notification_method_id = None
    leave_reason_id = (
        lookups.applications.LeaveReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.leave_reason_id
    )
    split_from_application_id = None
    language_id = Language.ENGLISH.language_id

    created_at = Generators.TransactionDateTime
    updated_at = factory.LazyAttribute(lambda a: a.created_at + timedelta(days=1))


class ApplicationUserNotFoundInfoFactory(BaseFactory):
    class Meta:
        model = application_models.ApplicationUserNotFoundInfo

    # application_id must be passed into the create() call
    currently_employed = factory.Faker("boolean")
    date_of_hire = date(2021, 1, 1)
    date_of_separation = date(2022, 1, 1)
    employer_name = factory.Faker("company")
    recently_acquired_or_merged = factory.Faker("boolean")
    submitted_time = Generators.TransactionDateTime


class AddressFactory(BaseFactory):
    class Meta:
        model = employee_models.Address

    address_type_id = 1
    address_line_one = factory.Faker("street_address")
    city = factory.Faker("city")
    zip_code = factory.Faker("postcode")
    geo_state_id = lookups.geo.GeoState.MA.geo_state_id


class EmployerAddressFactory(BaseFactory):
    class Meta:
        model = employee_models.EmployerAddress

    address = factory.SubFactory(AddressFactory)
    address_id = factory.LazyAttribute(lambda c: c.address.address_id)

    employer = factory.SubFactory(EmployerFactory)
    employer_id = factory.LazyAttribute(lambda w: w.employer.employer_id)


class ExperianAddressPairFactory(BaseFactory):
    class Meta:
        model = employee_models.ExperianAddressPair

    fineos_address = factory.SubFactory(AddressFactory)
    fineos_address_id = factory.LazyAttribute(lambda c: c.fineos_address.address_id)


class PaymentPreferenceFactory(BaseFactory):
    class Meta:
        model = application_models.ApplicationPaymentPreference

    payment_pref_id = Generators.UuidObj
    payment_method_id = lookups.employees.PaymentMethod.ACH.payment_method_id
    account_number = "*********"
    routing_number = "*********"
    bank_account_type_id = lookups.employees.BankAccountType.CHECKING.bank_account_type_id


class ClaimantPrepaidRegistrationFactory(BaseFactory):
    class Meta:
        model = payment_models.ClaimantPrepaidRegistration

    claimant_prepaid_registration_id = Generators.UuidObj

    employee = factory.SubFactory(EmployeeFactory)
    employee_id = factory.LazyAttribute(lambda e: e.employee.employee_id)

    account_number = factory.Sequence(lambda n: "%011d" % n)
    routing_number = fake.aba()  # "American Bankers Association": a valid bank routing number
    prepaid_registration_status_id = (
        lookups.payments.PrepaidRegistrationStatus.ACTIVE.prepaid_registration_status_id
    )


class EmployeePaymentPreferenceFactory(BaseFactory):
    class Meta:
        model = payment_models.PaymentPreference

    payment_preference_id = Generators.UuidObj
    payment_method_id = lookups.employees.PaymentMethod.PREPAID_CARD.payment_method_id
    employee = factory.SubFactory(EmployeeFactory)
    employee_id = factory.LazyAttribute(lambda e: e.employee.employee_id)
    account_number = factory.Sequence(lambda n: "%011d" % n)
    routing_number = fake.aba()  # "American Bankers Association": a valid bank routing number
    account_name = factory.Faker("name")
    bank_account_type_id = lookups.employees.BankAccountType.CHECKING.bank_account_type_id
    bank_customer_id = factory.LazyAttribute(lambda e: e.employee.fineos_customer_number)
    card_id = fake.random_int(min=100000, max=999999)


class ContinuousLeavePeriodFactory(BaseFactory):
    class Meta:
        model = application_models.ContinuousLeavePeriod

    leave_period_id = Generators.UuidObj
    start_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 1, 1), date_end=date(2021, 1, 15)
    )
    end_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 1, 16), date_end=date(2021, 1, 28)
    )
    is_estimated = True
    last_day_worked = factory.Faker("date_object")
    expected_return_to_work_date = factory.Faker("date_object")
    start_date_full_day = None
    start_date_off_hours = None
    start_date_off_minutes = None
    end_date_full_day = None
    end_date_off_hours = None
    end_date_off_minutes = None


class IntermittentLeavePeriodFactory(BaseFactory):
    class Meta:
        model = application_models.IntermittentLeavePeriod

    leave_period_id = Generators.UuidObj
    start_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 2, 1), date_end=date(2021, 2, 15)
    )
    end_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 2, 16), date_end=date(2021, 2, 28)
    )
    frequency = None
    frequency_interval = None
    frequency_interval_basis = None
    duration = None
    duration_basis = None


class ReducedScheduleLeavePeriodFactory(BaseFactory):
    class Meta:
        model = application_models.ReducedScheduleLeavePeriod

    leave_period_id = Generators.UuidObj
    start_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 3, 1), date_end=date(2021, 3, 15)
    )
    end_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 3, 16), date_end=date(2021, 3, 28)
    )
    is_estimated = True
    thursday_off_minutes = 90
    friday_off_minutes = 90
    saturday_off_minutes = 90
    sunday_off_minutes = 90
    monday_off_minutes = 90
    tuesday_off_minutes = 90
    wednesday_off_minutes = 90


class EmployerBenefitFactory(BaseFactory):
    class Meta:
        model = application_models.EmployerBenefit

    # application_id must be passed into the create() call
    benefit_type_id = (
        lookups.applications.EmployerBenefitType.SHORT_TERM_DISABILITY.employer_benefit_type_id
    )
    benefit_amount_dollars = factory.Faker("random_int")
    benefit_amount_frequency_id = lookups.applications.AmountFrequency.PER_MONTH.amount_frequency_id
    benefit_start_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 3, 1), date_end=date(2021, 3, 15)
    )
    benefit_end_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 3, 16), date_end=date(2021, 3, 28)
    )
    is_full_salary_continuous = True


class OtherIncomeFactory(BaseFactory):
    class Meta:
        model = application_models.OtherIncome

    # application_id must be passed into the create() call
    income_type_id = lookups.applications.OtherIncomeType.SSDI.other_income_type_id
    income_amount_dollars = factory.Faker("random_int")
    income_amount_frequency_id = lookups.applications.AmountFrequency.PER_MONTH.amount_frequency_id
    income_start_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 3, 1), date_end=date(2021, 3, 15)
    )
    income_end_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 3, 16), date_end=date(2021, 3, 28)
    )


class PreviousLeaveFactory(BaseFactory):
    class Meta:
        model = application_models.PreviousLeave

    # application_id must be passed into the create() call
    leave_reason_id = (
        lookups.applications.PreviousLeaveQualifyingReason.PREGNANCY_MATERNITY.previous_leave_qualifying_reason_id
    )
    is_for_current_employer = factory.Faker("boolean")
    is_continuous = False
    leave_start_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 3, 1), date_end=date(2021, 3, 15)
    )
    leave_end_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 3, 16), date_end=date(2021, 3, 28)
    )

    worked_per_week_minutes = random.randint(600, 2400)
    leave_minutes = random.randint(600, 2400)
    type = "any_reason"


class BenefitsMetricsFactory(BaseFactory):
    class Meta:
        model = state_metrics_models.BenefitsMetrics

    effective_date = datetime(2019, 10, 1)
    average_weekly_wage = Decimal("1331.66")
    maximum_weekly_benefit_amount = Decimal("1000.00")


class UnemploymentMetricFactory(BaseFactory):
    class Meta:
        model = state_metrics_models.UnemploymentMetric

    effective_date = datetime(2019, 10, 1)
    unemployment_minimum_earnings = Decimal("5000")


class DocumentFactory(BaseFactory):
    class Meta:
        model = document_models.Document

    document_id = Generators.UuidObj

    # TODO: This should point to a User object but that relationship does not exist in the base model.
    #
    # user = factory.SubFactory(UserFactory)
    # user_id = factory.LazyAttribute(lambda a: a.user.user_id)
    user_id = Generators.UuidObj

    # TODO: This should point to an Application object but that relationship does not exist in the base model.
    #
    # application = factory.SubFactory(ApplicationFactory)
    # application_id = factory.LazyAttribute(lambda a: a.application.application_id)
    application_id = Generators.UuidObj

    created_at = Generators.UtcNow
    updated_at = Generators.UtcNow

    # Initialize these type_ids to some random value.
    document_type_id = random.randint(
        1, lookups.documents.DocumentType.STATE_MANAGED_PAID_LEAVE_CONFIRMATION.document_type_id
    )

    # Initialize these type_ids to some random value.
    pfml_document_type_id = random.randint(
        1, lookups.documents.DocumentType.OWN_SERIOUS_HEALTH_CONDITION_FORM.document_type_id
    )

    # These values have no special meaning, just bounds so we get some variation.
    size_bytes = random.randint(1989, 24_072_020)

    is_stored_in_s3 = False
    name = ""
    description = ""


class WorkPatternFixedFactory(BaseFactory):
    """A single week work pattern for someone working a fixed (consistent) schedule"""

    class Meta:
        model = application_models.WorkPattern

    work_pattern_id = Generators.UuidObj
    created_at = Generators.UtcNow

    work_pattern_type_id = lookups.applications.WorkPatternType.FIXED.work_pattern_type_id
    work_pattern_days = factory.LazyAttribute(
        lambda w: [
            application_models.WorkPatternDay(
                work_pattern_id=w.work_pattern_id,
                day_of_week_id=lookups.applications.DayOfWeek.SUNDAY.day_of_week_id,
                minutes=8 * 60 + 15,
            ),
            application_models.WorkPatternDay(
                work_pattern_id=w.work_pattern_id,
                day_of_week_id=lookups.applications.DayOfWeek.MONDAY.day_of_week_id,
                minutes=8 * 60 + 15,
            ),
            application_models.WorkPatternDay(
                work_pattern_id=w.work_pattern_id,
                day_of_week_id=lookups.applications.DayOfWeek.TUESDAY.day_of_week_id,
                minutes=8 * 60 + 15,
            ),
            application_models.WorkPatternDay(
                work_pattern_id=w.work_pattern_id,
                day_of_week_id=lookups.applications.DayOfWeek.WEDNESDAY.day_of_week_id,
                minutes=8 * 60 + 15,
            ),
            application_models.WorkPatternDay(
                work_pattern_id=w.work_pattern_id,
                day_of_week_id=lookups.applications.DayOfWeek.THURSDAY.day_of_week_id,
                minutes=8 * 60 + 15,
            ),
            application_models.WorkPatternDay(
                work_pattern_id=w.work_pattern_id,
                day_of_week_id=lookups.applications.DayOfWeek.FRIDAY.day_of_week_id,
                minutes=8 * 60 + 15,
            ),
            application_models.WorkPatternDay(
                work_pattern_id=w.work_pattern_id,
                day_of_week_id=lookups.applications.DayOfWeek.SATURDAY.day_of_week_id,
                minutes=8 * 60 + 15,
            ),
        ]
    )


class WorkPatternVariableFactory(WorkPatternFixedFactory):
    work_pattern_type_id = lookups.applications.WorkPatternType.VARIABLE.work_pattern_type_id


class WorkPatternRotatingFactory(WorkPatternFixedFactory):
    work_pattern_type_id = lookups.applications.WorkPatternType.ROTATING.work_pattern_type_id
    work_pattern_days = factory.LazyAttribute(
        lambda w: [
            application_models.WorkPatternDay(
                work_pattern_id=w.work_pattern_id, day_of_week_id=i % 7 + 1, minutes=8 * 60
            )
            for i in range(28)
        ]
    )


class DuaReductionPaymentFactory(BaseFactory):
    class Meta:
        model = employee_models.DuaReductionPayment

    fineos_customer_number = factory.Faker("numerify", text="####")
    employer_fein = Generators.Fein
    payment_date = factory.Faker("date_object")

    # Default DUA logic excludes payments where this value is before 12/1/2020.
    # Since the date_object provider generates a random date between 1970 & now,
    # using it would cause almost all generated payments to be excluded.
    request_week_begin_date = factory.Faker("date_this_year")

    gross_payment_amount_cents = random.randint(100, 100000)
    payment_amount_cents = random.randint(100, 100000)
    fraud_indicator = None
    benefit_year_begin_date = factory.Faker("date_object")
    benefit_year_end_date = factory.Faker("date_object")


class DiaReductionPaymentFactory(BaseFactory):
    class Meta:
        model = employee_models.DiaReductionPayment

    fineos_customer_number = factory.Faker("numerify", text="####")
    board_no = factory.Faker("uuid4")
    event_id = factory.Faker("uuid4")
    event_description = "PC"
    eve_created_date = factory.Faker("date_object")
    event_occurrence_date = factory.Faker("date_object")
    award_id = factory.Faker("uuid4")
    award_code = "34"
    award_amount = 1600.00
    award_date = factory.Faker("date_object")
    start_date = factory.Faker("date_object")
    end_date = factory.Faker("date_object")
    weekly_amount = 400.00
    award_created_date = factory.Faker("date_object")
    termination_date = factory.Faker("date_object")


class CaringLeaveMetadataFactory(BaseFactory):
    class Meta:
        model = application_models.CaringLeaveMetadata

    family_member_first_name = factory.Faker("first_name")
    family_member_middle_name = factory.Faker("first_name")
    family_member_last_name = factory.Faker("last_name")
    family_member_date_of_birth = factory.Faker("date_object")


class ImportLogFactory(BaseFactory):
    class Meta:
        model = ImportLog
        sqlalchemy_get_or_create = ("import_log_id",)

    import_log_id = factory.Sequence(lambda n: n + 1000)
    import_type = "Initial"
    report = "{}"
    status = "success"
    start = datetime.now()
    end = datetime.now()


class FineosServiceAgreementFactory(BaseFactory):
    class Meta:
        model = FineosServiceAgreement

    fineos_service_agreement_id = Generators.UuidObj
    employer_id = Generators.UuidObj
    fineos_service_agreement_case_number = factory.Faker("random_int")
    start_date = date.today()
    end_date = None
    version_date = date.today()
    family_exemption = True
    medical_exemption = True
    status_id = factory.LazyFunction(
        lambda: random.choice(
            [status.status_id for status in FineosServiceAgreementStatus.get_all()]
        )
    )
    import_log = factory.SubFactory(ImportLogFactory)


class MmarsPaymentDataFactory(BaseFactory):
    class Meta:
        model = payment_models.MmarsPaymentData

    mmars_payment_data_id = Generators.UuidObj

    budget_fiscal_year = 2021
    fiscal_year = 2021
    fiscal_period = random.randint(1, 12)
    pymt_doc_code = "GAX"
    pymt_doc_department_code = "EOL"
    pymt_doc_unit = "8770"
    pymt_doc_identifier = "GAXMDFMLAAAAJUFG3FJO"
    pymt_doc_version_no = "1"
    pymt_doc_vendor_line_no = "1"
    pymt_doc_comm_line_no = "0"
    pymt_doc_actg_line_no = "1"
    pymt_actg_line_amount = 100.00
    pymt_discount_line_amount = 0
    pymt_penalty_line_amount = 0
    pymt_interest_line_amount = 0
    pymt_backup_withholding_line_amount = 0
    pymt_intercept_amount = 0
    pymt_retainage_line_amount = 0
    pymt_freight_amount = 0
    pymt_default_intercept_fee_amount = 0
    pymt_supplementary_intercept_fee_amount = 0
    pymt_service_from_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 1, 1), date_end=date(2021, 1, 15)
    )
    pymt_service_to_date = factory.LazyAttribute(
        lambda a: a.pymt_service_from_date + timedelta(days=7) if a.pymt_service_from_date else None
    )
    encumbrance_doc_code = "GAE"
    encumbrance_doc_dept = "EOL"
    encumbrance_doc_identifier = "PFMLFAMLFY21********"
    encumbrance_vendor_line_no = "1"
    encumbrance_accounting_line_no = "1"
    disb_doc_code = "AD"
    disb_doc_department_code = "CTR"
    disb_doc_identifier = "DISB0301210000263380"
    disb_doc_version_no = "1"
    disb_vendor_line_no = "1"
    disb_commodity_line_no = "0"
    disb_actg_line_no = "1"
    disb_actg_line_amount = 100.00
    disb_check_amount = 100.00
    disb_discount_line_amount = 0
    disb_penalty_line_amount = 0
    disb_interest_line_amount = 0
    disb_backup_withholding_line_amount = 0
    disb_intercept_amount = 0
    disb_retainage_line_amount = 0
    disb_freight_amount = 0
    disb_default_intercept_fee_amount = 0
    disb_supplementary_intercept_fee_amount = 0
    disb_doc_phase_code = "3"
    disb_doc_function_code = "1"
    actg_line_descr = "REQ 18939"
    check_descr = "Check Description - Factory"
    warrant_no = "M11"
    warrant_select_date = factory.LazyAttribute(
        lambda a: a.pymt_service_to_date + timedelta(days=1) if a.pymt_service_to_date else None
    )
    check_eft_issue_date = factory.LazyAttribute(
        lambda a: a.warrant_select_date + timedelta(days=2) if a.warrant_select_date else None
    )
    bank_account_code = "0"
    check_eft_no = "1234456"
    cleared_date = factory.LazyAttribute(
        lambda a: a.warrant_select_date + timedelta(days=10) if a.warrant_select_date else None
    )
    appropriation = "********"
    appropriation_name = "Family and Medical Leave Benefit Payments"
    object_class = "RR"
    object_class_name = "BENEFIT PROGRAMS"
    object = "R40"
    object_name = "Paid Family Medical Leave"
    income_type = "6"
    income_type_name = "Taxable Grants"
    form_type_indicator = "G"
    form_typ_ind_descr = "1099-G"
    disbursement_frequency = "1"
    disbursement_frequency_name = "Daily"
    payment_lag = "1"
    fund = "631"
    fund_name = "Family and Employment Secuirty Trust Fund"
    fund_category = "5"
    fund_category_name = "Trust and Agency"
    major_program = "DF0632"
    major_program_name = "DFML Benefit Payment"
    program = "DFML2021B"
    program_name = "DEPARTMENT OF FAMILY AND MEDICAL LEAVE COLLECTIONS 2021"
    phase = "K165"
    phase_name = "DEPARTMENT OF FAMILY AND MEDICAL LEAVE BENEFITS 2021"
    activity = "7246"
    activity_name = "Paid Medical Leave"
    vendor_customer_code = "VC0001201168"
    legal_name = "JOHN Q. CLAIMANT"
    address_id = "AD010"
    address_type = "PA"
    address_line_1 = "1 MAIN ST."
    city = "DARTMOUTH"
    state = "MA"
    zip_code = "02747-2722"
    country = "USA"
    vendor_invoice_no = "NTN-241347-ABS-01_14166"
    vendor_invoice_date = warrant_select_date
    scheduled_payment_date = warrant_select_date
    doc_function_code = "1"
    doc_function_code_name = "New"
    doc_phase_code = "3"
    doc_phase_name = "Final"
    government_branch = "10"
    government_branch_name = "EXECUTIVE BRANCH"
    cabinet = "93"
    cabinet_name = "EXECUTIVE OFFICE of LABOR and WORKFORCE DEVELOPMENT"
    department = "EOL"
    department_name = "Executive Office of Labor and Workforce Development"
    division = "1000"
    division_name = "EXECUTIVE OFFICE OF LABOR AND WORKFORCE DEVELOPMENT"
    group_name = "DEPARTMENT OF FAMILY AND MEDICAL LEAVE"
    section_name = "DEPARTMENT OF FAMILY AND MEDICAL LEAVE"
    district = "8700"
    district_name = "DEPARTMENT OF FAMILY AND MEDICAL LEAVE"
    bureau = "8710"
    bureau_name = "DFML ADMINISTRATION"
    unit = "8770"
    unit_name = "PFML Benefit Payments"
    doc_record_date = warrant_select_date
    acceptance_date = warrant_select_date
    doc_created_by = "EOL Interface"
    doc_created_on = warrant_select_date
    doc_last_modified_by = "System Admin"
    doc_last_modified_on = warrant_select_date


class Pfml1099BatchFactory(BaseFactory):
    class Meta:
        model = payment_models.Pfml1099Batch

    pfml_1099_batch_id = Generators.UuidObj

    tax_year = 2021
    batch_run_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 1, 1), date_end=date(2021, 1, 15)
    )
    correction_ind = False
    batch_status = "Created"


class Pfml1099Factory(BaseFactory):
    class Meta:
        model = payment_models.Pfml1099

    pfml_1099_id = Generators.UuidObj

    pfml_1099_batch_id = Generators.UuidObj

    tax_year = 2021
    employee_id = Generators.UuidObj
    tax_identifier_id = Generators.UuidObj
    c = "9999"
    i = "9999"
    first_name = factory.Faker("first_name")
    last_name = factory.Faker("last_name")
    address_line_1 = factory.Faker("street_address")
    address_line_2 = ""
    city = factory.Faker("city")
    state = factory.Faker("state_abbr")
    zip = factory.Faker("postcode")
    gross_payments = Generators.Money
    state_tax_withholdings = Generators.Money
    federal_tax_withholdings = Generators.Money
    other_credits = Generators.Money
    overpayment_repayments = 0.00
    correction_ind = False
    s3_location = None
    fineos_status = "New"


class Pfml1099MMARSPaymentFactory(BaseFactory):
    class Meta:
        model = payment_models.Pfml1099MMARSPayment

    pfml_1099_mmars_payment_id = Generators.UuidObj

    # Pfml1099Batch
    batch = factory.SubFactory(Pfml1099BatchFactory)
    pfml_1099_batch_id = factory.LazyAttribute(lambda a: a.batch.pfml_1099_batch_id)

    # MmarsPaymentData
    mmars_payment = factory.SubFactory(MmarsPaymentDataFactory)
    mmars_payment_id = factory.LazyAttribute(lambda a: a.mmars_payment.mmars_payment_data_id)
    payment_amount = factory.LazyAttribute(lambda a: a.mmars_payment.pymt_actg_line_amount)
    payment_date = factory.LazyAttribute(lambda a: a.mmars_payment.warrant_select_date)

    # Employee
    employee = factory.SubFactory(EmployeeFactory)
    employee_id = factory.LazyAttribute(lambda e: e.employee.employee_id)


class Pfml1099PaymentFactory(BaseFactory):
    class Meta:
        model = payment_models.Pfml1099Payment

    pfml_1099_payment_id = Generators.UuidObj

    pfml_1099_batch_id = Generators.UuidObj

    # Payment
    payment = factory.SubFactory(PaymentFactory)
    payment_id = factory.LazyAttribute(lambda a: a.payment.payment_id)
    payment_amount = factory.LazyAttribute(lambda a: a.payment.amount)
    payment_date = factory.LazyAttribute(lambda a: a.payment.payment_date)

    # Claim
    claim = factory.SubFactory(ClaimFactory)
    claim_id = factory.LazyAttribute(lambda a: a.claim.claim_id)

    # Employee
    employee_id = Generators.UuidObj


class Pfml1099RefundFactory(BaseFactory):
    class Meta:
        model = payment_models.Pfml1099Refund

    pfml_1099_refund_id = Generators.UuidObj

    pfml_1099_batch_id = Generators.UuidObj

    # Payment
    payment = factory.SubFactory(PaymentFactory)
    payment_id = factory.LazyAttribute(lambda a: a.payment.payment_id)
    # Refund amount is a negative value
    refund_amount = factory.LazyAttribute(lambda a: a.payment.amount)
    refund_date = factory.LazyAttribute(lambda a: a.payment.payment_date)

    # Employee
    employee = factory.SubFactory(EmployeeFactory)
    employee_id = factory.LazyAttribute(lambda e: e.employee.employee_id)


class Pfml1099FederalWithholdingFactory(BaseFactory):
    class Meta:
        model = payment_models.Pfml1099Withholding

    pfml_1099_withholding_id = Generators.UuidObj

    pfml_1099_batch_id = Generators.UuidObj

    # Payment
    payment = factory.SubFactory(PaymentFactory)
    payment_id = factory.LazyAttribute(lambda a: a.payment.payment_id)
    withholding_amount = factory.LazyAttribute(lambda a: a.payment.amount)
    withholding_date = factory.LazyAttribute(lambda a: a.payment.payment_date)

    # Claim
    claim = factory.SubFactory(ClaimFactory)
    claim_id = factory.LazyAttribute(lambda a: a.claim.claim_id)

    # Employee
    employee = factory.SubFactory(EmployeeFactory)
    employee_id = factory.LazyAttribute(lambda e: e.employee.employee_id)

    # Withholding Type
    # withholding_type = lookups.payments.WithholdingType.FEDERAL
    withholding_type_id = lookups.payments.WithholdingType.FEDERAL.withholding_type_id


class Pfml1099StateWithholdingFactory(BaseFactory):
    class Meta:
        model = payment_models.Pfml1099Withholding

    pfml_1099_refund_id = Generators.UuidObj

    # Pfml1099Batch
    pfml_1099_batch_id = Generators.UuidObj

    # Payment
    payment = factory.SubFactory(PaymentFactory)
    payment_id = factory.LazyAttribute(lambda a: a.payment.payment_id)
    withholding_amount = factory.LazyAttribute(lambda a: a.payment.amount)
    withholding_date = factory.LazyAttribute(lambda a: a.payment.payment_date)

    # Claim
    claim = factory.SubFactory(ClaimFactory)
    claim_id = factory.LazyAttribute(lambda a: a.claim.claim_id)

    # Employee
    employee = factory.SubFactory(EmployeeFactory)
    employee_id = factory.LazyAttribute(lambda e: e.employee.employee_id)

    # Withholding Type
    withholding_type = lookups.payments.WithholdingType.STATE
    withholding_type_id = withholding_type.withholding_type_id
    organization_unit_id = Generators.UuidObj

    fineos_id = None
    name = factory.Faker("company")
    employer = factory.SubFactory(EmployerFactory)
    employer_id = factory.LazyAttribute(lambda c: c.employer.employer_id)


class DuaEmployeeDemographicsFactory(BaseFactory):
    class Meta:
        model = employee_models.DuaEmployeeDemographics

    dua_employee_demographics_id = Generators.UuidObj

    fineos_customer_number = factory.Faker("numerify", text="####")
    date_of_birth = factory.Faker("date_object")
    gender_code = random.choices(["F", "M", "U", None])
    occupation_code = random.randint(1, 6000)
    occupation_description = None
    employer_fein = Generators.Fein
    employer_reporting_unit_number = random.randint(1, 100000)


class DuaReportingUnitFactory(BaseFactory):
    class Meta:
        model = employee_models.DuaReportingUnit

    dua_reporting_unit_id = Generators.UuidObj
    dua_id = factory.Sequence(lambda n: n)
    dba = None

    employer_id = Generators.UuidObj

    organization_unit = factory.SubFactory(OrganizationUnitFactory)
    organization_unit_id = factory.LazyAttribute(lambda d: d.organization_unit.organization_unit_id)


class EmployeeOccupationFactory(BaseFactory):
    class Meta:
        model = employee_models.EmployeeOccupation

    employee_occupation_id = Generators.UuidObj

    employee = factory.SubFactory(EmployeeFactory)
    employee_id = factory.LazyAttribute(lambda d: d.employee.employee_id)

    employer = factory.SubFactory(EmployerFactory)
    employer_id = factory.LazyAttribute(lambda d: d.employer.employer_id)


class UserLeaveAdministratorFactory(BaseFactory):
    class Meta:
        model = employee_models.UserLeaveAdministrator

    user_leave_administrator_id = Generators.UuidObj
    user = factory.SubFactory(UserFactory, roles=[lookups.employees.Role.EMPLOYER])
    user_id = factory.LazyAttribute(lambda u: u.user.user_id)
    employer = factory.SubFactory(EmployerFactory)
    employer_id = factory.LazyAttribute(lambda u: u.employer.employer_id)
    fineos_web_id = factory.Faker("numerify", text="pfml_leave_admin_#####")
    verification = None


class UserLeaveAdministratorVerifiedFactory(UserLeaveAdministratorFactory):
    verification = factory.SubFactory(VerificationFactory)


class UserLeaveAdministratorActionFactory(BaseFactory):
    class Meta:
        model = employee_models.UserLeaveAdministratorAction

    user_leave_administrator_action_id = Generators.UuidObj
    user = factory.SubFactory(UserFactory, roles=[lookups.employees.Role.EMPLOYER])
    user_id = factory.LazyAttribute(lambda u: u.user.user_id)
    created_at = Generators.UtcNow

    recipient_email = Generators.EmailAddress
    recipient_employer = factory.SubFactory(EmployerFactory)
    recipient_employer_id = factory.LazyAttribute(lambda a: a.recipient_employer.employer_id)
    user_leave_administrator_action_type_id = (
        lookups.employees.UserLeaveAdministratorActionType.ADD.user_leave_administrator_action_type_id
    )


class LinkSplitPaymentFactory(BaseFactory):
    class Meta:
        model = payment_models.LinkSplitPayment

    payment = factory.SubFactory(PaymentFactory)
    payment_id = factory.LazyAttribute(lambda c: c.payment.payment_id)

    related_payment = factory.SubFactory(PaymentFactory)
    related_payment_id = factory.LazyAttribute(lambda c: c.related_payment.payment_id)


class BenefitYearFactory(BaseFactory):
    class Meta:
        model = employee_models.BenefitYear

    employee = factory.SubFactory(EmployeeFactory)
    employee_id = factory.LazyAttribute(lambda w: w.employee.employee_id)

    # If you provide a start_date, the remaining dates are calculated to be valid
    start_date = date(2021, 1, 3)
    end_date = factory.LazyAttribute(lambda x: x.start_date + timedelta(weeks=52, days=-1))
    base_period_start_date = factory.LazyAttribute(
        lambda x: Quarter.from_date(x.start_date).subtract_quarters(4).start_date()
    )
    base_period_end_date = factory.LazyAttribute(
        lambda x: Quarter.from_date(x.start_date).subtract_quarters(1).as_date()
    )

    total_wages = 0


class BenefitYearContributionFactory(BaseFactory):
    class Meta:
        model = employee_models.BenefitYearContribution

    benefit_year_contribution_id = Generators.UuidObj
    benefit_year = factory.SubFactory(BenefitYearFactory)
    benefit_year_id = factory.LazyAttribute(lambda b: b.benefit_year.benefit_year_id)
    average_weekly_wage = Decimal("750.25")


class Pfml1099RequestFactory(BaseFactory):
    class Meta:
        model = payment_models.Pfml1099Request

    pfml_1099_request_id = Generators.UuidObj
    employee_id = Generators.UuidObj
    correction_ind = factory.Faker("boolean")
    pfml_1099_batch_id = Generators.UuidObj


class MergerAcquisitionFactory(BaseFactory):
    class Meta:
        model = MergerAcquisition

    effective_date = date.today()


class ClaimWithApplicationFactory(ClaimFactory):
    employer = factory.SubFactory(EmployerFactory)
    employer_id = factory.LazyAttribute(lambda w: w.employer.employer_id if w.employer else None)
    application = factory.RelatedFactory(
        ApplicationFactory,
        factory_related_name="claim",
    )


class ChangeRequestFactory(BaseFactory):
    class Meta:
        model = change_request_models.ChangeRequest

    change_request_id = Generators.UuidObj
    change_request_type_id = 1
    claim = factory.SubFactory(ClaimWithApplicationFactory)
    claim_id = factory.LazyAttribute(lambda w: w.claim.claim_id)
    start_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 2, 1), date_end=date(2021, 2, 15)
    )
    end_date = factory.Faker(
        "date_between_dates", date_start=date(2021, 2, 16), date_end=date(2021, 2, 28)
    )
    submitted_time = None


class AppealFactory(BaseFactory):
    class Meta:
        model = appeal_models.Appeal

    appeal_id = Generators.UuidObj
    claim = factory.SubFactory(ClaimWithApplicationFactory)
    claim_id = factory.LazyAttribute(lambda a: a.claim.claim_id)
    is_generated_from_extract = False


class SubmittedAppealFactory(AppealFactory):
    appeal_phone_number = "+***********"
    appeal_representative_option_id = lookups.appeal.AppealRepresentativeOption.get_id(
        appeals_common.AppealRepresentativeOption.YES
    )
    appeal_status_id = lookups.appeal.AppealStatus.get_id(
        appeals_common.AppealStatus.CLOSED_CLAIM_DECISION_CHANGED
    )
    fineos_appeal_id = Generators.FineosAppealId
    for_private_insurance = False
    has_read_notices = False
    needs_interpreter = False
    originally_decided_at = factory.Faker("date_object")
    submitted_at = Generators.Now


class PaymentIssueResolutionFactory(BaseFactory):
    class Meta:
        model = payment_models.PaymentIssueResolution

    payment = factory.SubFactory(PaymentFactory)
    import_log = factory.SubFactory(ImportLogFactory)
    fineos_absence_id = Generators.FineosAbsenceId
    task_processed_at = None
    payment_issue_resolution_scenario_config_id = (
        lookups.payments.PaymentIssueResolutionScenarioConfig.FAILED_AUTOMATED_VALIDATION_RESOLUTION_CONFIG.payment_issue_resolution_scenario_config_id
    )
    # This field is deprecated and unsued, but is not nullable so a default needs to get set for the factory
    fineos_task_type_id = (
        lookups.payments.FineosTaskType.ADDRESS_VALIDATION_ERROR.fineos_task_type_id
    )
    task_processing_outcome = None


class LeaveAdminVerificationAttemptFactory(BaseFactory):
    class Meta:
        model = employee_models.LeaveAdminVerificationAttempt

    user = factory.SubFactory(UserFactory)
    employer = factory.SubFactory(EmployerFactory)
    is_successful = False


class DorCseExtractObligationFactory(BaseFactory):
    class Meta:
        model = DorCseExtractObligation

    obligation_id = Generators.UuidObj
    ncp_ssn = factory.Faker("numerify", text="#########")
    ncp_first_name = factory.Faker("first_name")
    ncp_last_name = factory.Faker("last_name")
    ncp_total_obligation = factory.Faker("numerify", text="#######")
    dor_cse_external_id = factory.Faker("numerify", text="###########")


class DorCseExtractErrorLogFactory(BaseFactory):
    class Meta:
        model = DorCseExtractErrorLog

    error_log_id = Generators.UuidObj
    error_log = {"ncp_first_name": "Missing"}


def generate_number_without_leading_zeroes():
    # Generate a random number with 11 digits
    number = random.randint(1000000000, 99999999999)
    return str(number)


class ChildSupportObligationFactory(BaseFactory):
    class Meta:
        model = ChildSupportObligation

    child_support_obligation_id = Generators.UuidObj
    tax_identifier_id = Generators.UuidObj
    first_name = factory.Faker("first_name")
    last_name = factory.Faker("last_name")
    case_open_date = factory.Faker("date_object")
    max_weekly_obligation = factory.Faker("numerify", text="#######")
    effective_from = factory.Faker("date_object")
    dor_cse_external_id = factory.LazyFunction(generate_number_without_leading_zeroes)


class ClaimantChildSupportObligationFactory(BaseFactory):
    class Meta:
        model = ClaimantChildSupportObligation

    tax_identifier_id = Generators.UuidObj
    employee_id = Generators.UuidObj
    case_open_date = factory.Faker("date_object")
    max_weekly_obligation = factory.Faker("numerify", text="#######")
    match_date = factory.Faker("date_object")
    status_type_id = 1
    status_date = factory.Faker("date_object")
    effective_from = factory.Faker("date_object")
    effective_to = factory.Faker("date_object")


class FineosExtractVbiOverpaymentCaseFactory(BaseFactory):
    class Meta:
        model = payment_models.FineosExtractVbiOverpaymentCase

    vbi_overpayment_case_id = Generators.UuidObj
    classid = "7922"
    indexid = factory.Sequence(lambda n: "%d" % n)
    c_pymnteif_paymenteventi = "7326"
    i_pymnteif_paymenteventi = factory.Sequence(lambda n: "%d" % n)
    casenumber = "PL ABS-{}-PL ABS-01-OP{}".format(random.randint(1, 6000), random.randint(1, 6000))

    reference_file = factory.SubFactory(ReferenceFileFactory)
    reference_file_id = factory.LazyAttribute(lambda a: a.reference_file.reference_file_id)


class FineosExtractVbiOverpaymentSomFactory(BaseFactory):
    class Meta:
        model = payment_models.FineosExtractVbiOverpaymentSom

    vbi_overpayment_som_id = Generators.UuidObj
    overpaymentcase_classid = "7922"
    overpaymentcase_indexid = factory.Sequence(lambda n: "%d" % n)
    notification_number = "NTN-{}".format(random.randint(1, 6000))
    absence_casenumber = "{}-ABS-01".format(notification_number)
    absence_paidleave_casenumber = "PL ABS-{}".format(random.randint(1, 6000))
    absence_paidleave_benefit = "{}-PL ABS-01".format(absence_paidleave_casenumber)
    overpayment_casenumber = "{}-OP{}".format(absence_paidleave_benefit, random.randint(1, 6000))
    customerno = "{}".format(random.randint(1, 6000))
    net_benefit_overpayment_amount = str(Generators.Money)
    overpayment_adjustment_amount = str(Generators.Money)
    agreed_recovery_amount = str(Generators.Money)
    recovered_to_date_amount = str(Generators.Money)
    outstanding_amount = str(Generators.Money)

    reference_file = factory.SubFactory(ReferenceFileFactory)
    reference_file_id = factory.LazyAttribute(lambda a: a.reference_file.reference_file_id)


class FineosExtractVbiOverpaymentsAdjustmentsSomFactory(BaseFactory):
    class Meta:
        model = payment_models.FineosExtractVbiOverpaymentsAdjustmentsSom

    vbi_overpayments_adjustments_som_id = Generators.UuidObj
    claimcasenumber = "PL ABS-{}".format(random.randint(1, 6000))
    bencasenumber = "{}-PL ABS-01".format(claimcasenumber)
    opcasenumner = "{}-OP{}".format(bencasenumber, random.randint(1, 6000))
    adjustmentname = "Overpayment Reduction Agreement"
    adjustmentamount = str(Generators.Money)
    agreementdate = factory.LazyFunction(
        lambda: fake.date_between(
            start_date=date(2020, 1, 1), end_date=date(2022, 12, 31)
        ).strftime("%Y-%m-%d %H:%M:%S")
    )
    receiptpaymentstatus = "Active"
    tocalculate = "Business Net Benefit Amount"
    overpaymentcasec = "7922"
    overpaymentcasei = factory.Sequence(lambda n: "%d" % n)

    reference_file = factory.SubFactory(ReferenceFileFactory)
    reference_file_id = factory.LazyAttribute(lambda a: a.reference_file.reference_file_id)


class SubmittableApplicationFactory(ApplicationFactory):
    phone = factory.SubFactory(PhoneFactory)
    residential_address = factory.SubFactory(AddressFactory)
    work_pattern = factory.SubFactory(WorkPatternFixedFactory)
    continuous_leave_periods = [ContinuousLeavePeriodFactory.build()]
    leave_reason_id = lookups.applications.LeaveReason.PREGNANCY_MATERNITY.leave_reason_id
    leave_reason_qualifier_id = (
        lookups.applications.LeaveReasonQualifier.RIGHT_TO_LEAVE.leave_reason_qualifier_id
    )
    employment_status_id = lookups.applications.EmploymentStatus.UNEMPLOYED.employment_status_id
    gender_id = lookups.employees.Gender.NONBINARY.gender_id

    date_of_birth = factory.Faker("date_object")
    first_name = factory.Faker("first_name")
    last_name = factory.Faker("last_name")

    hours_worked_per_week = 40.0
    employer_notification_date = None
    child_birth_date = factory.Faker("date_object")

    employer_notified = False
    has_continuous_leave_periods = True
    has_intermittent_leave_periods = False
    has_mailing_address = False
    has_reduced_schedule_leave_periods = False
    has_state_id = False
    has_employer_benefits = False
    has_other_incomes = False


class FinancialEligibilityCalculationFactory(BaseFactory):
    class Meta:
        model = FinancialEligibilityCalculation

    financial_eligibility_calculation_id = Generators.UuidObj
    fineos_absence_id = Generators.FineosAbsenceId

    # If you provide a benefit_year_start_date, remaining dates will be calculated to match
    benefit_year_start_date = date(2021, 1, 3)
    benefit_year_end_date = factory.LazyAttribute(
        lambda x: x.benefit_year_start_date + timedelta(weeks=52, days=-1)
    )
    base_period_start_date = factory.LazyAttribute(
        lambda x: Quarter.from_date(x.benefit_year_start_date).subtract_quarters(4).start_date()
    )
    base_period_end_date = factory.LazyAttribute(
        lambda x: Quarter.from_date(x.benefit_year_start_date).subtract_quarters(1).as_date()
    )

    employee = factory.SubFactory(EmployeeFactory)
    employee_id = factory.LazyAttribute(lambda e: e.employee.employee_id)

    is_eligible = False
    is_aww_overridden = False


class AdminUserFactory(BaseFactory):
    class Meta:
        model = admin_models.AdminUser

    admin_user_id = Generators.UuidObj
    oauth_id = factory.Faker("uuid4")
    email_address = Generators.EmailAddress
    name = "Fake Admin User"


class AdminAuditLogFactory(BaseFactory):
    class Meta:
        model = admin_models.AdminAuditLog

    admin_user = factory.SubFactory(AdminUserFactory)

    admin_audit_log_id = Generators.UuidObj
    # admin_user_id = Generators.UuidObj
    ticket_num = factory.Faker("numerify", text="TICKET-#####")
    record_type = "user"
    record_id = Generators.UuidObj
    state_before = '{ "email_address": "<EMAIL>" }'
    state_after = '{ "email_address": "<EMAIL>" }'
    created_at = Generators.UtcNow


class FineosExtractEmployeeFeedFactory(BaseFactory):
    class Meta:
        model = payment_models.FineosExtractEmployeeFeed

    c = "1234"
    i = random.randint(1, 999999)
    customerno = random.randint(1, 999999)
    lastupdatedate = Generators.TransactionDateTime
    firstnames = factory.Faker("first_name")
    lastname = factory.Faker("last_name")

    address1 = factory.Faker("street_address")
    address4 = factory.Faker("city")
    address5 = "MA"
    postcode = factory.Faker("postcode")
    country = factory.Faker("country_code")


class FineosExtractEmployeeFeedAggregateFactory(BaseFactory):
    class Meta:
        model = payment_models.FineosExtractEmployeeFeedAggregate

    c = "1234"
    i = random.randint(1, 999999)
    customerno = random.randint(1, 999999)
    lastupdatedate = Generators.TransactionDateTime
    firstnames = factory.Faker("first_name")
    lastname = factory.Faker("last_name")

    address1 = factory.Faker("street_address")
    address4 = factory.Faker("city")
    address5 = "MA"
    postcode = factory.Faker("postcode")
    country = factory.Faker("country_code")


class FineosExtractEmployeeFeedDeltaFactory(FineosExtractEmployeeFeedFactory):
    class Meta:
        model = payment_models.FineosExtractEmployeeFeedDelta


class EmployeeAddressFactory(BaseFactory):
    class Meta:
        model = employee_models.EmployeeAddress

    employee = factory.SubFactory(EmployeeFactory)
    employee_id = factory.LazyAttribute(lambda c: c.employee.employee_id)

    address = factory.SubFactory(AddressFactory)
    address_id = factory.LazyAttribute(lambda c: c.address.address_id)


class ClaimantAddressFactory(BaseFactory):
    class Meta:
        model = employee_models.ClaimantAddress


class MmarsEventFactory(BaseFactory):
    class Meta:
        model = payment_models.MmarsEvent

    overpayment = factory.SubFactory(OverpaymentFactory)
    overpayment_id = factory.LazyAttribute(lambda c: c.overpayment.overpayment_id)

    employee = factory.SubFactory(EmployeeFactory)
    employee_id = factory.LazyAttribute(lambda c: c.employee.employee_id)

    mmars_event_type_id = MmarsEventType.RE_TRX.mmars_event_type_id
    mmars_status_type_id = MmarsEventStatusType.VCC_PENDING.mmars_event_status_type_id


class MmarsAuditLogtFactory(BaseFactory):
    class Meta:
        model = payment_models.MmarsAuditLog

    mmars_event = factory.SubFactory(MmarsEventFactory)
    mmars_event_id = factory.LazyAttribute(lambda c: c.mmars_event.mmars_event_id)

    mmars_event_type_id = MmarsEventType.VCC_TRX.mmars_event_type_id


class EmployerDORExemptionFactory(BaseFactory):
    class Meta:
        model = EmployerDORExemption
        exclude = ["import_log"]

    import_log = factory.SubFactory(ImportLogFactory)
    employer_dor_exemption_id = Generators.UuidObj
    dor_activity_key = 0  # dor_activity_key is always 0 for initial/default exemption
    import_log_id = factory.LazyAttribute(lambda i: i.import_log.import_log_id)
    decision_commence_date = date(9999, 12, 31)
    decision_cease_date = date(9999, 12, 31)
    family_exemption = False
    medical_exemption = False


class NonExemptEmployerFactory(EmployerOnlyRequiredFactory):
    exemption = factory.RelatedFactory(
        EmployerDORExemptionFactory,
        factory_related_name="employer",
    )


class FamilyExemptEmployerFactory(NonExemptEmployerFactory):
    exemption1 = factory.RelatedFactory(
        EmployerDORExemptionFactory,
        factory_related_name="employer",
        dor_activity_key=Generators.NonZeroDORActivityKeyCy,
        decision_commence_date=Generators.FirstDayOfCurrentYear,
        decision_cease_date=Generators.LastDayOfCurrentYear,
        family_exemption=True,
        medical_exemption=False,
    )


class MedicalExemptEmployerFactory(NonExemptEmployerFactory):
    exemption1 = factory.RelatedFactory(
        EmployerDORExemptionFactory,
        factory_related_name="employer",
        dor_activity_key=Generators.NonZeroDORActivityKeyCy,
        decision_commence_date=Generators.FirstDayOfCurrentYear,
        decision_cease_date=Generators.LastDayOfCurrentYear,
        family_exemption=False,
        medical_exemption=True,
    )


class PartiallyExemptEmployerFactory(NonExemptEmployerFactory):
    class Params:
        is_exempt_family = factory.Faker("boolean")

    exemption1 = factory.RelatedFactory(
        EmployerDORExemptionFactory,
        factory_related_name="employer",
        dor_activity_key=Generators.NonZeroDORActivityKeyCy,
        decision_commence_date=Generators.FirstDayOfCurrentYear,
        decision_cease_date=Generators.LastDayOfCurrentYear,
        family_exemption=Params.is_exempt_family,
        medical_exemption=factory.LazyAttribute(lambda o: not o.family_exemption),
    )


class FullyExemptEmployerFactory(NonExemptEmployerFactory):
    exemption1 = factory.RelatedFactory(
        EmployerDORExemptionFactory,
        factory_related_name="employer",
        dor_activity_key=Generators.NonZeroDORActivityKeyCy,
        decision_commence_date=Generators.FirstDayOfCurrentYear,
        decision_cease_date=Generators.LastDayOfCurrentYear,
        family_exemption=True,
        medical_exemption=True,
    )


class FamilyExemptEmployerAmendmentFactory(NonExemptEmployerFactory):
    exemption1 = factory.RelatedFactory(
        EmployerDORExemptionFactory,
        factory_related_name="employer",
        dor_activity_key=Generators.NonZeroDORActivityKeyCy,
        decision_commence_date=Generators.FirstDayOfCurrentYear,
        decision_cease_date=Generators.LastDayOfCurrentYear,
        family_exemption=False,
        medical_exemption=True,
        dor_updated_date=Generators.Now,
    )

    exemption2 = factory.RelatedFactory(
        EmployerDORExemptionFactory,
        factory_related_name="employer",
        dor_activity_key=Generators.NonZeroDORActivityKeyCy,
        decision_commence_date=Generators.FirstDayOfCurrentYear,
        decision_cease_date=Generators.LastDayOfCurrentYear,
        family_exemption=True,
        medical_exemption=False,
        dor_updated_date=Generators.Now,
    )


class MedicalExemptEmployerAmendmentFactory(NonExemptEmployerFactory):
    exemption1 = factory.RelatedFactory(
        EmployerDORExemptionFactory,
        factory_related_name="employer",
        dor_activity_key=Generators.NonZeroDORActivityKeyCy,
        decision_commence_date=Generators.FirstDayOfCurrentYear,
        decision_cease_date=Generators.LastDayOfCurrentYear,
        family_exemption=False,
        medical_exemption=True,
        dor_updated_date=Generators.Now,
    )

    exemption2 = factory.RelatedFactory(
        EmployerDORExemptionFactory,
        factory_related_name="employer",
        dor_activity_key=Generators.NonZeroDORActivityKeyCy,
        decision_commence_date=Generators.FirstDayOfCurrentYear,
        decision_cease_date=Generators.LastDayOfCurrentYear,
        family_exemption=False,
        medical_exemption=True,
        dor_updated_date=Generators.Now,
    )


class PartiallyExemptEmployerAmendmentFactory(NonExemptEmployerFactory):
    class Params:
        is_exempt_family = factory.Faker("boolean")

    exemption1 = factory.RelatedFactory(
        EmployerDORExemptionFactory,
        factory_related_name="employer",
        dor_activity_key=Generators.NonZeroDORActivityKeyCy,
        decision_commence_date=Generators.FirstDayOfCurrentYear,
        decision_cease_date=Generators.LastDayOfCurrentYear,
        family_exemption=Params.is_exempt_family,
        medical_exemption=factory.LazyAttribute(lambda o: not o.family_exemption),
        dor_updated_date=Generators.Now,
    )

    exemption2 = factory.RelatedFactory(
        EmployerDORExemptionFactory,
        factory_related_name="employer",
        dor_activity_key=Generators.NonZeroDORActivityKeyCy,
        decision_commence_date=Generators.FirstDayOfCurrentYear,
        decision_cease_date=Generators.LastDayOfCurrentYear,
        family_exemption=factory.LazyAttribute(lambda o: not o.medical_exemption),
        medical_exemption=Params.is_exempt_family,
        dor_updated_date=Generators.Now,
    )


class FullyExemptEmployerAmendmentFactory(NonExemptEmployerFactory):
    exemption1 = factory.RelatedFactory(
        EmployerDORExemptionFactory,
        factory_related_name="employer",
        dor_activity_key=Generators.NonZeroDORActivityKeyCy,
        decision_commence_date=Generators.FirstDayOfCurrentYear,
        decision_cease_date=Generators.LastDayOfCurrentYear,
        family_exemption=False,
        medical_exemption=True,
        dor_updated_date=Generators.Now,
    )

    exemption2 = factory.RelatedFactory(
        EmployerDORExemptionFactory,
        factory_related_name="employer",
        dor_activity_key=Generators.NonZeroDORActivityKeyCy,
        decision_commence_date=Generators.FirstDayOfCurrentYear,
        decision_cease_date=Generators.LastDayOfCurrentYear,
        family_exemption=True,
        medical_exemption=True,
        dor_updated_date=Generators.Now,
    )


class FamilyExemptPyMedicalExemptCyEmployerFactory(NonExemptEmployerFactory):
    exemption1 = factory.RelatedFactory(
        EmployerDORExemptionFactory,
        factory_related_name="employer",
        dor_activity_key=Generators.NonZeroDORActivityKeyPy,
        decision_commence_date=Generators.FirstDayOfPreviousYear,
        decision_cease_date=Generators.LastDayOfPreviousYear,
        family_exemption=True,
        medical_exemption=False,
        dor_updated_date=Generators.Now,
    )

    exemption2 = factory.RelatedFactory(
        EmployerDORExemptionFactory,
        factory_related_name="employer",
        dor_activity_key=Generators.NonZeroDORActivityKeyCy,
        decision_commence_date=Generators.FirstDayOfCurrentYear,
        decision_cease_date=Generators.LastDayOfCurrentYear,
        family_exemption=False,
        medical_exemption=True,
        dor_updated_date=Generators.Now,
    )


class OverpaymentCollectionFactory(BaseFactory):
    class Meta:
        model = payment_models.OverpaymentCollection

    overpayment_collection_id = Generators.UuidObj
    overpayment = factory.SubFactory(OverpaymentFactory)
    overpayment_id = factory.LazyAttribute(lambda c: c.overpayment.overpayment_id)
    payment_date = factory.Faker("date_object")
    overpayment_collection_amount = Generators.Money
    check_name = factory.Faker("name")
    check_number = factory.Faker("numerify", text="#######")
    vendor_overpayment_collection_id = factory.Faker("bothify", text="INTF################")
    overpayment_collection_status_type_id = (
        lookups.payments.OverpaymentCollectionStatusType.PENDING.overpayment_collection_status_type_id
    )
    overpayment_recovery_type_id = (
        lookups.payments.OverpaymentRecoveryType.CHECK.overpayment_recovery_type_id
    )


class StateLogFactory(BaseFactory):
    class Meta:
        model = employee_models.StateLog

    employee = factory.SubFactory(EmployeeFactory)
    employee_id = factory.LazyAttribute(lambda e: e.employee.employee_id)

    claim = factory.SubFactory(ClaimFactory)
    claim_id = factory.LazyAttribute(lambda c: c.claim.claim_id)

    end_state_id = None

    payment = factory.SubFactory(PaymentFactory)
    payment_id = factory.LazyAttribute(lambda p: p.payment.payment_id)


class LatestStateLogFactory(BaseFactory):
    class Meta:
        model = employee_models.LatestStateLog


class AddressExtractFactory:
    def __init__(
        self,
        state_log_params: Optional[dict] = None,
        claimant_address_params: Optional[dict] = None,
    ) -> None:
        self.employee = EmployeeFactory.create()
        self.claim = ClaimFactory.create(employee=self.employee)
        self.payment = PaymentFactory.create(claim=self.claim)

        params = {
            "employee": self.employee,
            "claim": self.claim,
            "payment": self.payment,
            **(state_log_params or {}),
        }
        self.state_log = StateLogFactory.create(**params)

        params = {
            "employee": self.employee,
            "residential_address": employee_models.ExperianAddressPair(
                fineos_address=AddressFactory()
            ),
            **(claimant_address_params or {}),
        }
        self.claimant_address = ClaimantAddressFactory.create(**params)


class DuaQuarterWagesEmployeeFactory(BaseFactory):
    class Meta:
        model = dua_models.DuaQuarterWagesEmployee

    dua_quarter_wages_employee_id = factory.Sequence(lambda n: n)
    employer_fein = factory.Sequence(lambda n: str(n).rjust(9, "0"))
    employer_dua_account_num = factory.Faker("numerify", text="########")
    unit_location_number = factory.Faker("numerify", text="####")
    year_and_quarter = "20251"
    employee_ssn = factory.Sequence(lambda n: str(n).rjust(9, "0"))
    employee_last_name = factory.Faker("last_name")
    employee_first_name = factory.Faker("first_name")
    employee_middle_initial = "Q"
    employee_wages = factory.Faker("numerify", text="############")
    ma_income_tax_withholding_subject_wages = factory.Faker("numerify", text="############")
    ma_income_tax_withheld_amount = factory.Faker("numerify", text="############")
    hours_worked = "40"
    is_owner_or_officer = "N"
    submitter_dua_username = factory.Faker("numerify", text="##########")
    submitter_fein = factory.Faker("numerify", text="########")
    paymaster_fein = factory.Faker("numerify", text="########")
    is_corrected_return = "N"
    was_employed_month_1 = "Y"
    was_employed_month_2 = "Y"
    was_employed_month_3 = "Y"
    reference_file = factory.SubFactory(ReferenceFileFactory)
    reference_file_id = factory.LazyAttribute(lambda d: d.reference_file.reference_file_id)
    import_log_id = None


class SyncDuaEmployeeAndWagesCheckpointFactory(BaseFactory):
    class Meta:
        model = dua_models.SyncDuaEmployeesAndWagesCheckpoint

    dua_quarter_wages_employee_id = 0


class DuaQuarterWagesEmployerFactory(BaseFactory):
    class Meta:
        model = dua_models.DuaQuarterWagesEmployer

    dua_quarter_wages_employer_id = factory.Sequence(lambda n: n)
    employer_fein = factory.Sequence(lambda n: str(n).rjust(9, "0"))
    employer_name = factory.Faker("company")
    address_line_1 = factory.Faker("street_address")
    address_line_2 = ""
    city = factory.Faker("city")
    state = factory.Faker("state_abbr")
    zip_code = factory.Faker("postcode")
    total_employee_wages = factory.Faker("numerify", text="#########")
    total_ma_income_tax_withholding_subject_wages = factory.Faker("numerify", text="##############")
    total_employees = factory.Faker("numerify", text="#########")
    reference_file = factory.SubFactory(ReferenceFileFactory)
    reference_file_id = factory.LazyAttribute(lambda d: d.reference_file.reference_file_id)
    import_log_id = None


class StatutorilyExcludedEmployerFactory(BaseFactory):
    class Meta:
        model = employee_models.StatutorilyExcludedEmployer

    statutorily_excluded_employer_id = Generators.UuidObj
    employer_fein = factory.Sequence(lambda n: str(n).rjust(9, "0"))
    # Description typically refers to a city or municipality
    description = factory.Faker("city")
    reference_file = factory.SubFactory(
        ReferenceFileFactory,
        reference_file_type_id=ReferenceFileType.STATUTORILY_EXCLUDED_EMPLOYER_FILE.reference_file_type_id,
    )
    reference_file_id = factory.LazyAttribute(lambda d: d.reference_file.reference_file_id)
    import_log_id = None


class EmployerExemptionApplicationFactory(BaseFactory):
    class Meta:
        model = employer_exemptions_models.EmployerExemptionApplication

    class Params:
        FIRST_DAY_OF_NEXT_QUARTER = Quarter.from_date(date.today()).next_quarter().start_date()

    employer_exemption_application_id = Generators.UuidObj

    # TODO - PFMLPB-20608: Exemptions BE: GET /employer-exemption-applications/{employer_exemption_application_id}/documents
    # Replace with the corresponding UserExemptionsAdministrator factory
    user = factory.SubFactory(UserFactory)
    created_by_user_id = factory.LazyAttribute(lambda a: a.user.user_id)

    employer = factory.SubFactory(EmployerOnlyRequiredFactory)
    employer_id = factory.LazyAttribute(lambda e: e.employer.employer_id)

    contact_title = factory.Faker("word")
    contact_first_name = factory.Faker("first_name")
    contact_last_name = factory.Faker("last_name")
    contact_phone = factory.SubFactory(PhoneFactory)
    contact_phone_id = Generators.UuidObj
    contact_email_address = Generators.EmailAddress

    employer_exemption_application_status_id = (
        EmployerExemptionApplicationStatus.DRAFT.employer_exemption_application_status_id
    )

    is_application_status_auto_decided = factory.Faker("boolean")
    should_workforce_count_include_1099_misc = factory.Faker("boolean")

    average_workforce_count = factory.Faker("pyint", min_value=25)
    has_family_exemption: bool | None = factory.Faker("boolean")
    has_medical_exemption: bool | None = factory.Faker("boolean")
    is_self_insured_plan = factory.Faker("boolean")
    insurance_plan_effective_at = Params.FIRST_DAY_OF_NEXT_QUARTER
    insurance_plan_expires_at = Params.FIRST_DAY_OF_NEXT_QUARTER + timedelta(days=365)

    insurance_provider_id: int | None = 1  # factory.Faker("pyint", min_value=1, max_value=25)
    insurance_plan_id: int | None = 1

    has_obtained_surety_bond: bool | None = factory.Faker("boolean")
    surety_company = factory.Faker("company")
    surety_bond_amount = Generators.Money
    has_third_party_administrator = factory.Faker("boolean")

    tpa_business_name = factory.Faker("company")
    tpa_contact_title = factory.Faker("word")
    tpa_contact_first_name = factory.Faker("first_name")
    tpa_contact_last_name = factory.Faker("last_name")
    tpa_contact_phone = factory.SubFactory(PhoneFactory)
    tpa_contact_phone_id = factory.LazyAttribute(lambda a: a.tpa_contact_phone.phone_id)
    tpa_contact_email_address = Generators.EmailAddress

    does_plan_cover_all_employees: bool | None = factory.Faker("boolean")
    does_plan_provide_enough_leave: bool | None = factory.Faker("boolean")
    does_plan_provide_enough_medical_leave: bool | None = factory.Faker("boolean")
    does_plan_provide_enough_caring_leave: bool | None = factory.Faker("boolean")
    does_plan_provide_enough_bonding_leave: bool | None = factory.Faker("boolean")
    does_plan_provide_enough_armed_forces_leave: bool | None = factory.Faker("boolean")
    does_plan_provide_enough_armed_forces_illness_leave: bool | None = factory.Faker("boolean")
    does_plan_pay_enough_benefits: bool | None = factory.Faker("boolean")
    does_employer_withhold_premiums: bool | None = factory.Faker("boolean")
    are_employer_withholdings_within_allowable_amount: bool | None = factory.Faker("boolean")
    does_plan_provide_pfml_job_protection: bool | None = factory.Faker("boolean")
    does_plan_provide_return_to_work_benefits: bool | None = factory.Faker("boolean")
    does_plan_cover_employee_contribution: bool | None = factory.Faker("boolean")
    does_plan_provide_intermittent_caring_leave: bool | None = factory.Faker("boolean")
    does_plan_provide_intermittent_bonding_leave: bool | None = factory.Faker("boolean")
    does_plan_provide_intermittent_armed_forces_leave: bool | None = factory.Faker("boolean")
    does_plan_provide_intermittent_medical_leave: bool | None = factory.Faker("boolean")
    does_plan_cover_former_employees: bool | None = factory.Faker("boolean")
    does_plan_favor_paid_leave_benefits: bool | None = factory.Faker("boolean")
    is_legally_acknowledged: bool = factory.Faker("boolean")
    submitted_at = None


class EmployerExemptionApplicationDraftFactory(BaseFactory):
    class Meta:
        model = employer_exemptions_models.EmployerExemptionApplication

    employer_exemption_application_id = Generators.UuidObj
    user = factory.SubFactory(UserFactory)
    created_by_user_id = factory.LazyAttribute(lambda a: a.user.user_id)
    employer = factory.SubFactory(EmployerOnlyRequiredFactory)
    employer_id = factory.LazyAttribute(lambda e: e.employer.employer_id)
    employer_exemption_application_status_id = (
        EmployerExemptionApplicationStatus.DRAFT.employer_exemption_application_status_id
    )
    is_legally_acknowledged = True


class EmployerExemptionApplicationDraftPrivatePlanFactory(EmployerExemptionApplicationFactory):
    has_family_exemption = True
    has_medical_exemption = True
    is_self_insured_plan = False
    insurance_provider_id = 1
    insurance_plan_id = 1
    is_legally_acknowledged = True
    does_plan_cover_all_employees = None
    does_plan_provide_enough_leave = None
    does_plan_provide_enough_medical_leave = None
    does_plan_provide_enough_caring_leave = None
    does_plan_provide_enough_bonding_leave = None
    does_plan_provide_enough_armed_forces_leave = None
    does_plan_provide_enough_armed_forces_illness_leave = None
    does_plan_pay_enough_benefits = None
    does_employer_withhold_premiums = None
    are_employer_withholdings_within_allowable_amount = None
    does_plan_provide_pfml_job_protection = None
    does_plan_provide_return_to_work_benefits = None
    does_plan_cover_employee_contribution = None
    does_plan_provide_intermittent_caring_leave = None
    does_plan_provide_intermittent_bonding_leave = None
    does_plan_provide_intermittent_armed_forces_leave = None
    does_plan_provide_intermittent_medical_leave = None
    does_plan_cover_former_employees = None
    does_plan_favor_paid_leave_benefits = None


class EmployerExemptionApplicationDraftSelfInsuredFamilyAndMedicalFactory(
    EmployerExemptionApplicationFactory
):
    insurance_provider_id = None
    insurance_plan_id = None
    has_family_exemption = True
    has_medical_exemption = True
    is_self_insured_plan = True
    is_legally_acknowledged = True
    does_plan_cover_all_employees: bool | None = True
    does_plan_provide_enough_leave: bool | None = True
    does_plan_provide_enough_medical_leave: bool | None = True
    does_plan_provide_enough_caring_leave: bool | None = True
    does_plan_provide_enough_bonding_leave: bool | None = True
    does_plan_provide_enough_armed_forces_leave: bool | None = True
    does_plan_provide_enough_armed_forces_illness_leave: bool | None = True
    does_plan_pay_enough_benefits: bool | None = True
    does_employer_withhold_premiums: bool | None = True
    are_employer_withholdings_within_allowable_amount: bool | None = True
    does_plan_provide_pfml_job_protection: bool | None = True
    does_plan_provide_return_to_work_benefits: bool | None = True
    does_plan_cover_employee_contribution: bool | None = True
    does_plan_provide_intermittent_caring_leave: bool | None = True
    does_plan_provide_intermittent_bonding_leave: bool | None = True
    does_plan_provide_intermittent_armed_forces_leave: bool | None = True
    does_plan_provide_intermittent_medical_leave: bool | None = True
    does_plan_cover_former_employees: bool | None = True
    does_plan_favor_paid_leave_benefits: bool | None = True


class EmployerExemptionApplicationDraftSelfInsuredFamilyFactory(
    EmployerExemptionApplicationDraftSelfInsuredFamilyAndMedicalFactory
):

    does_plan_provide_enough_medical_leave = None
    has_family_exemption = True
    has_medical_exemption = False


class EmployerExemptionApplicationDraftSelfInsuredMedicalFactory(
    EmployerExemptionApplicationDraftSelfInsuredFamilyAndMedicalFactory
):

    does_plan_provide_enough_caring_leave = None
    does_plan_provide_enough_bonding_leave = None
    does_plan_provide_enough_armed_forces_leave = None
    does_plan_provide_enough_armed_forces_illness_leave = None
    does_plan_provide_intermittent_caring_leave = None
    does_plan_provide_intermittent_bonding_leave = None
    does_plan_provide_intermittent_armed_forces_leave = None
    has_family_exemption = False
    has_medical_exemption = True


class OAuthServerCodeFactory(BaseFactory):
    class Meta:
        model = oauth_server_models.OAuthServerCode

    authz_code = "test_auth_code"
    user = factory.SubFactory(UserFactory)
    user_id = factory.LazyAttribute(lambda w: w.user.user_id)
    created_at = Generators.UtcNow


class OAuthClientCredentialsFactory(BaseFactory):
    class Meta:
        model = oauth_server_models.OAuthClientCredentials

    oauth_client_credentials_id = Generators.UuidObj
    client_id = "test_client_id"
    client_secret = factory.LazyFunction(lambda: generate_password_hash("test_client_secret"))


class EDMVendorDataFactory(BaseFactory):
    class Meta:
        model = EDMVendorData

    VENDOR_CUSTOMER_CODE = factory.Faker("bothify", text="VCC######")
    TIN = factory.Faker("numerify", text="#########")
    FIRST_NAME = factory.Faker("first_name")
    LAST_NAME = factory.Faker("last_name")
    LEGAL_NAME = factory.LazyAttribute(lambda obj: f"{obj.FIRST_NAME} {obj.LAST_NAME}")
    ADDRESS_ID = factory.Faker("bothify", text="AD######")
    STREET_1 = factory.Faker("street_address")
    STREET_2 = factory.Faker("secondary_address")
    CITY = factory.Faker("city")
    STATE = factory.Faker("state_abbr")
    ZIP_CODE = factory.Faker("postcode")
    CUSTOMER_ACTIVE_STATUS = factory.Faker("random_element", elements=["Active", "Inactive", None])
    CUSTOMER_ACTIVE_STATUS_NAME = factory.Faker(
        "random_element", elements=["Active", "Inactive", "Pending", None]
    )
    CUSTOMER_APPROVAL_STATUS = factory.Faker(
        "random_element", elements=["Approved", "Rejected", None]
    )
    CUSTOMER_APPROVAL_STATUS_NAME = factory.Faker(
        "random_element", elements=["Approved", "Rejected", "Under Review", None]
    )


class MmarsCustomerDetailFactory(BaseFactory):
    class Meta:
        model = payment_models.MmarsCustomerDetail

    mmars_event = factory.SubFactory(
        MmarsEventFactory,
        mmars_event_type_id=MmarsEventType.VCC_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.VCM_REQUIRE.mmars_event_status_type_id,
    )

    mmars_event_id = factory.LazyAttribute(lambda c: c.mmars_event.mmars_event_id)

    pfml_data = factory.LazyFunction(
        lambda: EDMVendorData(
            VENDOR_CUSTOMER_CODE="VCC123456",
            TIN="*********",
            FIRST_NAME="John",
            LAST_NAME="Doe",
            LEGAL_NAME="John Doe LLC",
            ADDRESS_ID="AD123456",
            STREET_1="123 PFML Main St",
            STREET_2="Apt 4B",
            CITY="Boston",
            STATE="MA",
            ZIP_CODE="02118",
            CUSTOMER_ACTIVE_STATUS="Active",
            CUSTOMER_ACTIVE_STATUS_NAME="Active",
            CUSTOMER_APPROVAL_STATUS="Approved",
            CUSTOMER_APPROVAL_STATUS_NAME="Approved",
        ).dict()
    )

    edm_data = factory.LazyFunction(
        lambda: EDMVendorData(
            VENDOR_CUSTOMER_CODE="VCC654321",
            TIN="987654321",
            FIRST_NAME="Jane",
            LAST_NAME="Smith",
            LEGAL_NAME="Jane Smith LLC",
            ADDRESS_ID="AD654321",
            STREET_1="456 EDM Main St",
            STREET_2="Suite 300",
            CITY="Cambridge",
            STATE="MA",
            ZIP_CODE="02139",
            CUSTOMER_ACTIVE_STATUS="Inactive",
            CUSTOMER_ACTIVE_STATUS_NAME="Inactive",
            CUSTOMER_APPROVAL_STATUS="Rejected",
            CUSTOMER_APPROVAL_STATUS_NAME="Rejected",
        ).dict()
    )


class FINEOSWebIdExtFactory(BaseFactory):
    class Meta:
        model = employee_models.FINEOSWebIdExt

    employee_tax_identifier = Generators.Tin
    employer_fein = Generators.Tin
    fineos_web_id = Generators.UuidObj
