from sqlalchemy import Integer, Text
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column as Column

from .base import Base


class LkAddressType(Base):
    __tablename__ = "lk_address_type"
    address_type_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    address_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, address_type_id, address_description):
        self.address_type_id = address_type_id
        self.address_description = address_description
