from datetime import date
from typing import TYPE_CHECKING, Optional
from uuid import UUID

from sqlalchemy import Date, ForeignKey, Integer, Text, UniqueConstraint, and_
from sqlalchemy.dialects.postgresql import UUID as SQL_UUID
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column as Column
from sqlalchemy.orm import relationship

from .base import Base, TimestampMixin, deprecated_column, uuid_gen

if TYPE_CHECKING:
    from .employees import Claim, LeaveRequest, LkLeaveRequestDecision


class AbsencePeriod(Base, TimestampMixin):
    """An absence period of a particular claim"""

    __tablename__ = "absence_period"
    __table_args__ = (
        UniqueConstraint(
            "fineos_absence_period_index_id",
            "fineos_absence_period_class_id",
            name="uix_absence_period_index_id_absence_period_class_id",
        ),
    )

    absence_period_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        primary_key=True,
        default=uuid_gen,
        comment="The internal id for the associated Absence Case record",
    )
    absence_period_start_date = Column(
        Date,
        comment="The start date of the absence period",
    )
    absence_period_end_date = Column(
        Date,
        comment="The end date of the absence period",
    )
    absence_period_type_id = Column(
        Integer,
        ForeignKey("lk_absence_period_type.absence_period_type_id"),
        comment='The id corresponding to the AbsencePeriodType enum, eg 1 ("Time off period")',
    )
    absence_reason_qualifier_one_id = Column(
        Integer,
        ForeignKey("lk_absence_reason_qualifier_one.absence_reason_qualifier_one_id"),
        comment='The id corresponding to the AbsenceReasonQualifierOne enum, eg 1 ("Not Work Related")',
    )
    absence_reason_qualifier_two_id = Column(
        Integer,
        ForeignKey("lk_absence_reason_qualifier_two.absence_reason_qualifier_two_id"),
        comment='The id corresponding to the AbsenceReasonQualifierTwo enum, eg 1 ("Accident / Injury")',
    )
    absence_reason_id = Column(
        Integer,
        ForeignKey("lk_absence_reason.absence_reason_id"),
        comment='The id corresponding to the AbsenceReason enum, eg 1 ("Serious Health Condition - Employee")',
    )
    claim_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("claim.claim_id"),
        index=True,
        nullable=False,
        comment="Internal id for the associated Claim record",
    )
    leave_request_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("leave_request.leave_request_id"),
        comment="ID associated with a leave request",
        index=True,
        nullable=True,
    )
    fineos_absence_period_class_id: Mapped[int] = Column(
        Integer,
        nullable=False,
        index=True,
        comment="Fineos id for the class of absence period",
    )
    fineos_absence_period_index_id: Mapped[int] = Column(
        Integer,
        nullable=False,
        index=True,
        comment="Fineos id for the index of absence period",
    )
    fineos_absence_period_id: Mapped[int] = Column(
        Integer,
        nullable=True,
        index=True,
        unique=True,
        comment="Fineos id for the absence period",
    )
    fineos_leave_request_id = Column(
        Integer,
        index=True,
        comment="Fineos id for the associaated leave request",
    )
    leave_request_decision_id: Mapped[Optional[int]] = deprecated_column(
        Integer,
        ForeignKey("lk_leave_request_decision.leave_request_decision_id"),
        comment='The id corresponding to the LeaveRequestDecision enum, eg 1 ("Pending")',
    )

    # Dates that reflect any modifications or cancelled time
    modified_start_date = Column(
        Date,
        comment="Modified start date for absence period",
    )
    modified_end_date = Column(
        Date,
        comment="Modified end date for absence period",
    )

    claim: Mapped["Claim"] = relationship("Claim")
    leave_request: Mapped[Optional["LeaveRequest"]] = relationship("LeaveRequest")
    absence_period_type: Mapped[Optional["LkAbsencePeriodType"]] = relationship(
        "LkAbsencePeriodType"
    )
    absence_reason: Mapped[Optional["LkAbsenceReason"]] = relationship("LkAbsenceReason")
    absence_reason_qualifier_one: Mapped[Optional["LkAbsenceReasonQualifierOne"]] = relationship(
        "LkAbsenceReasonQualifierOne"
    )
    absence_reason_qualifier_two: Mapped[Optional["LkAbsenceReasonQualifierTwo"]] = relationship(
        "LkAbsenceReasonQualifierTwo"
    )
    leave_request_decision: Mapped[Optional["LkLeaveRequestDecision"]] = relationship(
        "LkLeaveRequestDecision"
    )

    @hybrid_property
    def has_final_decision(self):
        from massgov.pfml.db.lookup_data.employees import LeaveRequestDecision

        # If you update this list, you should probably update the Portal's
        # hasFinalDecision helper too.
        return self.leave_request_decision_id not in [
            LeaveRequestDecision.PENDING.leave_request_decision_id,
            LeaveRequestDecision.IN_REVIEW.leave_request_decision_id,
            LeaveRequestDecision.PROJECTED.leave_request_decision_id,
            None,
        ]

    @has_final_decision.inplace.expression
    @classmethod
    def _has_final_decision_expression(cls):
        from massgov.pfml.db.lookup_data.employees import LeaveRequestDecision

        return and_(
            ~cls.leave_request_decision_id.is_(None),
            ~cls.leave_request_decision_id.in_(
                [
                    LeaveRequestDecision.PENDING.leave_request_decision_id,
                    LeaveRequestDecision.IN_REVIEW.leave_request_decision_id,
                    LeaveRequestDecision.PROJECTED.leave_request_decision_id,
                ]
            ),
        )

    @property
    def start_date(self) -> Optional[date]:
        if self.modified_start_date is not None:
            return self.modified_start_date
        return self.absence_period_start_date

    @property
    def end_date(self) -> Optional[date]:
        if self.modified_end_date is not None:
            return self.modified_end_date
        return self.absence_period_end_date

    @property
    def is_pending(self) -> bool:
        from massgov.pfml.db.lookup_data.employees import LeaveRequestDecision

        # Check if this absence period decision is pending.
        return (
            self.leave_request_decision_id == LeaveRequestDecision.PENDING.leave_request_decision_id
        )

    @property
    def in_review(self) -> bool:
        from massgov.pfml.db.lookup_data.employees import LeaveRequestDecision

        # Check if this absence period decision is in review.
        return (
            self.leave_request_decision_id
            == LeaveRequestDecision.IN_REVIEW.leave_request_decision_id
        )

    @property
    def is_approved(self) -> bool:
        from massgov.pfml.db.lookup_data.employees import LeaveRequestDecision

        # Check if this absence period decision is approved.
        return (
            self.leave_request_decision_id
            == LeaveRequestDecision.APPROVED.leave_request_decision_id
        )

    @property
    def is_intermittent(self) -> bool:
        from massgov.pfml.db.lookup_data.absences import AbsencePeriodType

        # Check if this absence period is for an intermittent leave
        return self.absence_period_type_id == AbsencePeriodType.INTERMITTENT.absence_period_type_id

    @property
    def is_approved_and_intermittent(self) -> bool:
        # Check if this absence period is for an approved intermittent leave
        # e.g. for claimants reporting intermittent leave hours
        return self.is_approved and self.is_intermittent


class LkAbsencePeriodType(Base):
    # Descriptions in this table map to Fineos Enum domain #6843
    __tablename__ = "lk_absence_period_type"
    absence_period_type_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    absence_period_type_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, absence_period_type_id, absence_period_type_description):
        self.absence_period_type_id = absence_period_type_id
        self.absence_period_type_description = absence_period_type_description


class LkAbsenceReason(Base):
    __tablename__ = "lk_absence_reason"
    absence_reason_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    absence_reason_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, absence_reason_id, absence_reason_description):
        self.absence_reason_id = absence_reason_id
        self.absence_reason_description = absence_reason_description


class LkAbsenceReasonQualifierOne(Base):
    __tablename__ = "lk_absence_reason_qualifier_one"
    absence_reason_qualifier_one_id: Mapped[int] = Column(
        Integer, primary_key=True, autoincrement=True
    )
    absence_reason_qualifier_one_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, absence_reason_qualifier_one_id, absence_reason_qualifier_one_description):
        self.absence_reason_qualifier_one_id = absence_reason_qualifier_one_id
        self.absence_reason_qualifier_one_description = absence_reason_qualifier_one_description


class LkAbsenceReasonQualifierTwo(Base):
    __tablename__ = "lk_absence_reason_qualifier_two"
    absence_reason_qualifier_two_id: Mapped[int] = Column(
        Integer, primary_key=True, autoincrement=True
    )
    absence_reason_qualifier_two_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, absence_reason_qualifier_two_id, absence_reason_qualifier_two_description):
        self.absence_reason_qualifier_two_id = absence_reason_qualifier_two_id
        self.absence_reason_qualifier_two_description = absence_reason_qualifier_two_description


class LkAbsenceStatus(Base):
    __tablename__ = "lk_absence_status"
    absence_status_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    absence_status_description: Mapped[str] = Column(Text, nullable=False)
    sort_order: Mapped[int] = Column(Integer, default=0, nullable=False)

    # use to set order when sorting (non alphabetic) by absence status

    def __init__(self, absence_status_id, absence_status_description, sort_order):
        self.absence_status_id = absence_status_id
        self.absence_status_description = absence_status_description
        self.sort_order = sort_order
