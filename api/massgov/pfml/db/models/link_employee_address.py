from typing import TYPE_CHECKING
from uuid import UUID

from sqlalchemy import <PERSON><PERSON><PERSON>
from sqlalchemy.dialects.postgresql import UUID as SQL_UUID
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column as Column
from sqlalchemy.orm import relationship

from .address import Address
from .base import Base, TimestampMixin

if TYPE_CHECKING:
    from massgov.pfml.db.models.employees import Employee


class EmployeeAddress(Base, TimestampMixin):
    """The physical or mailing address of an employee"""

    __tablename__ = "link_employee_address"

    employee_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("employee.employee_id"),
        primary_key=True,
        comment="Internal id of employee associated with employee's address",
    )
    address_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("address.address_id"),
        primary_key=True,
        comment="Internal id of address associated with employee's address",
    )

    employee: Mapped["Employee"] = relationship("Employee", back_populates="employee_addresses")
    address: Mapped["Address"] = relationship("Address", back_populates="employees")
