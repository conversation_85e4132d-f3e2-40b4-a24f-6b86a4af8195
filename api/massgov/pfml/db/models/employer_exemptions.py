import uuid
from dataclasses import dataclass
from enum import Enum
from typing import List, Optional
from uuid import UUID

from sqlalchemy import TIM<PERSON>TA<PERSON>, Boolean
from sqlalchemy import Column as SqlAlchemyColumn
from sqlalchemy import Date, ForeignKey, Integer, Numeric, Text, UniqueConstraint, inspect
from sqlalchemy.dialects.postgresql import UUID as SQL_UUID
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column as Column
from sqlalchemy.orm import relationship

import massgov.pfml.util.logging
from massgov.pfml.api.util.phone import convert_to_E164
from massgov.pfml.db import Session
from massgov.pfml.db.models.employees import Address, Employer, User
from massgov.pfml.db.models.phone import Phone

from .base import Base, TimestampMixin, column_doc, deprecated_column, uuid_gen

logger = massgov.pfml.util.logging.get_logger(__name__)


class LkEmployerExemptionApplicationStatus(Base):
    __tablename__ = "lk_employer_exemption_application_status"
    employer_exemption_application_status_id: Mapped[int] = Column(
        Integer, primary_key=True, autoincrement=True
    )
    employer_exemption_application_status_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(
        self,
        employer_exemption_application_status_id: int,
        employer_exemption_application_status_description: str,
    ):
        self.employer_exemption_application_status_id = employer_exemption_application_status_id
        self.employer_exemption_application_status_description = (
            employer_exemption_application_status_description
        )


# named ColumnIs to avoid name collision with 'from sqlalchemy.orm import mapped_column as Column'
class ColumnIs:
    # used as a dict key in column.info
    class RequiredFor(Enum):
        SUBMIT = "required_for_submit"
        PRESUBMIT_VALIDATION = "required_for_presubmit_validation"

    # used as a dict key in column.info
    class RequiredIf(Enum):
        EXEMPTION_IS_PURCHASED_PRIVATE_PLAN = "required_for_purchased_private_plan"
        EXEMPTION_IS_SELF_INSURED_FAMILY = "required_for_self_insured_family"
        EXEMPTION_IS_SELF_INSURED_MEDICAL = "required_for_self_insured_medical"
        EXEMPTION_HAS_THIRD_PARTY_ADMINISTRATOR = "required_for_third_party_administrator"

    # explicitly define when fields should be set to None on save/submit
    # values can be inferred from RequiredIf class, but choosing to be direct to
    # avoid ambiguity
    class ClearedWhen(Enum):
        EXEMPTION_IS_PURCHASED_PRIVATE_PLAN = "clear_when_purchased_private_plan"
        EXEMPTION_IS_SELF_INSURED_PLAN = "clear_when_self_insured_plan"
        EXEMPTION_IS_SELF_INSURED_FAMILY = "clear_when_self_insured_family"
        EXEMPTION_IS_SELF_INSURED_MEDICAL = "clear_when_self_insured_medical"
        EXEMPTION_DOES_NOT_HAVE_THIRD_PARTY_ADMINISTRATOR = "cleared_when_no_tpa"

    # used as a dict key in column.info
    # if column.info has 'AUTO_DENY_WHEN_VALUE_EQUALS' key and db value equals
    # column.info value, application should be set to denied
    AUTO_DENY_WHEN_VALUE_EQUALS = "auto_deny_when_value_equals"

    # used as a dict key in column.info
    # if column.info has 'MAPPED_TO_API_FIELD_NAME', use value from column.info['MAPPED_TO_API_FIELD_NAME']
    # else use database column name
    MAPPED_TO_API_FIELD_NAME = "mapped_api_name"

    # used as a dict key in column.info. add when column is marked as deprecated
    DEPRECATED = "deprecated"

    # used as a dict key in column.info. add when column should be written to log file
    WRITTEN_TO_LOG_FILE = "written_to_log_file"

    # used as a dict key in column.info. add when column is logged as something
    # other than the column name. value should be of type LogAttribute
    # (eg. ColumnIs.WRITTEN_TO_LOG_FILE_AS: LogAttribute(
    #          attr_name="insurance_provider_name",
    #          value="insurance_provider.insurance_provider_name"
    #       )
    WRITTEN_TO_LOG_FILE_AS = "written_to_log_file_as"


@dataclass
class LogAttribute:
    """Defines custom log attribute for a given database column.
      - attr_name: Attribute name
      - value: Column where value is contained; typically used for
               relationships objects

    Example:
        LogAttribute(
           attr_name="insurance_provider_name",
           value="insurance_provider.insurance_provider_name"
       )
    """

    attr_name: str
    value: str

    def __init__(self, attr_name: str, value: str | None = None):
        self.attr_name = attr_name
        self.value = value if value is not None else attr_name


class EmployerExemptionApplication(Base, TimestampMixin):
    __tablename__ = "employer_exemption_application"

    employer_exemption_application_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        primary_key=True,
        default=uuid_gen,
        comment="Unique primary key id",
        info={
            ColumnIs.RequiredFor.SUBMIT: True,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    created_by_user_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("user.user_id"),
        nullable=False,
        index=True,
        comment="User id associated with employer exemption application",
        info={
            ColumnIs.RequiredFor.SUBMIT: True,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    employer_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("employer.employer_id"),
        nullable=False,
        index=True,
        comment="Internal ID of the employer associated with an exemption application",
        info={
            ColumnIs.RequiredFor.SUBMIT: True,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    contact_title = Column(
        Text,
        comment="Title associated with employer exemption application point of contact",
        info={ColumnIs.RequiredFor.SUBMIT: True, ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: True},
    )
    contact_phone_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey(Phone.phone_id),
        nullable=True,
        comment="Phone id associated with employer exemption application point of contact",
        info={
            ColumnIs.RequiredFor.SUBMIT: True,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: True,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "contact_phone.phone_number",
        },
    )
    contact_email_address = Column(
        Text,
        index=True,
        comment=column_doc(
            "Email address associated with employer exemption application point of contact",
            pii=True,
        ),
        info={ColumnIs.RequiredFor.SUBMIT: True, ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: True},
    )
    employer_exemption_application_status_id = Column(
        Integer,
        ForeignKey(
            "lk_employer_exemption_application_status.employer_exemption_application_status_id"
        ),
        nullable=False,
        comment="Internal id associated with application status",
        info={
            ColumnIs.RequiredFor.SUBMIT: True,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: True,
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
            ColumnIs.WRITTEN_TO_LOG_FILE_AS: LogAttribute(
                attr_name="employer_exemption_application_status",
                value="employer_exemption_application_status.employer_exemption_application_status_description",
            ),
        },
        default=1,  # Tried importing enum but ran into issues; this maps to "DRAFT" status
    )
    is_application_status_auto_decided = Column(
        Boolean,
        nullable=True,
        comment="Denotes if application status was system determined (eg. Auto-approved, Auto-denied)",
        info={
            ColumnIs.RequiredFor.SUBMIT: True,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    should_workforce_count_include_1099_misc = Column(
        Boolean,
        nullable=True,
        comment="Denotes if 1099 miscellaneous employees should be included in employer workforce count",
        info={
            ColumnIs.RequiredFor.SUBMIT: True,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: True,
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    average_workforce_count = Column(
        Integer,
        comment="Average size of employer workforce in Massachusetts",
        info={
            ColumnIs.RequiredFor.SUBMIT: True,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: True,
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    has_family_exemption = Column(
        Boolean,
        comment="Employer has an exemption from Family PFML",
        info={
            ColumnIs.RequiredFor.SUBMIT: True,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: True,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "exemption_type",
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    has_medical_exemption = Column(
        Boolean,
        comment="Employer has an exemption from Medical PFML",
        info={
            ColumnIs.RequiredFor.SUBMIT: True,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: True,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "exemption_type",
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    is_self_insured_plan = Column(
        Boolean,
        nullable=True,
        comment="Employer has self-insured plan; if false, employer plan is purchased",
        info={
            ColumnIs.RequiredFor.SUBMIT: True,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: True,
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    insurance_plan_effective_at = Column(
        Date,
        comment="Insurance policy start date",
        info={
            ColumnIs.RequiredFor.SUBMIT: True,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: True,
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    insurance_plan_expires_at = Column(
        Date,
        comment="Insurance policy renewal date",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: True,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: True,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    insurance_provider_id = Column(
        Integer,
        ForeignKey("insurance_provider.insurance_provider_id"),
        comment="Internal id associated with an insurance provider",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "purchased_plan.insurance_provider_id",
            ColumnIs.ClearedWhen.EXEMPTION_IS_SELF_INSURED_PLAN: True,
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
            ColumnIs.WRITTEN_TO_LOG_FILE_AS: LogAttribute(
                attr_name="insurance_provider_name",
                value="insurance_provider.insurance_provider_name",
            ),
        },
    )
    insurance_plan_id = Column(
        Integer,
        ForeignKey("insurance_plan.insurance_plan_id"),
        comment="Internal id associated with an insurance policy",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "purchased_plan.insurance_plan_id",
            ColumnIs.ClearedWhen.EXEMPTION_IS_SELF_INSURED_PLAN: True,
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
            ColumnIs.WRITTEN_TO_LOG_FILE_AS: LogAttribute(
                attr_name="insurance_plan_name", value="insurance_plan.form_name"
            ),
        },
    )
    has_obtained_surety_bond = Column(
        Boolean,
        nullable=True,
        comment="Denotes if bond has been obtained; only required for self-insured private plans",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: True,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: True,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "self_insured.has_obtained_surety_bond",
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    surety_company = Column(
        Text,
        comment="Name of surety company; only required for self-insured private plans",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: True,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: True,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "self_insured.surety_company",
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    surety_bond_effective_date = deprecated_column(
        Date,
        comment="Surety bond start date",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "self_insured.surety_bond_effective_date",
            ColumnIs.DEPRECATED: True,
        },
    )
    surety_bond_amount = Column(
        Numeric(asdecimal=True),
        comment="Surety bond amount",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: True,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: True,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "self_insured.surety_bond_amount",
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    has_third_party_administrator = Column(
        Boolean,
        nullable=True,
        comment="Denotes if surety bond has a third-party administrator",
        info={
            ColumnIs.RequiredFor.SUBMIT: True,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: True,
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    tpa_business_name = Column(
        Text,
        comment="Third-party administrator name",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_HAS_THIRD_PARTY_ADMINISTRATOR: True,
            ColumnIs.ClearedWhen.EXEMPTION_DOES_NOT_HAVE_THIRD_PARTY_ADMINISTRATOR: True,
        },
    )
    tpa_contact_name: Mapped[Optional[str]] = deprecated_column(
        Text,
        comment=column_doc(
            "The contact name associated with the Employer Exemption application", pii=True
        ),
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.ClearedWhen.EXEMPTION_DOES_NOT_HAVE_THIRD_PARTY_ADMINISTRATOR: True,
            ColumnIs.DEPRECATED: True,
        },
    )
    tpa_phone_number_id: Mapped[Optional[UUID]] = deprecated_column(
        SQL_UUID(as_uuid=True),
        ForeignKey(Phone.phone_id),
        nullable=True,
        comment="Internal id for the associated Third-Party administrator Phone record",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "tpa_contact_phone.phone_number",
            ColumnIs.ClearedWhen.EXEMPTION_DOES_NOT_HAVE_THIRD_PARTY_ADMINISTRATOR: True,
            ColumnIs.DEPRECATED: True,
        },
    )
    tpa_email_address = deprecated_column(
        Text,
        comment=column_doc(
            "The Third-party administrator email address associated with the Employer Exemption application",
            pii=True,
        ),
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.ClearedWhen.EXEMPTION_DOES_NOT_HAVE_THIRD_PARTY_ADMINISTRATOR: True,
            ColumnIs.DEPRECATED: True,
        },
    )
    does_plan_cover_all_employees = Column(
        Boolean,
        nullable=True,
        comment="Denotes if all employees (full-time, part-time, con-call, per diem, permanent, temporary or seasonal) are eligible for benefits",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: True,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: True,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.AUTO_DENY_WHEN_VALUE_EQUALS: False,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "self_insured.questions.does_plan_cover_all_employees",
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    does_plan_provide_enough_leave = Column(
        Boolean,
        nullable=True,
        comment="Denotes if leave plan covers 26 weeks of paid family and medical leave",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: True,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: True,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.AUTO_DENY_WHEN_VALUE_EQUALS: False,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "self_insured.questions.does_plan_provide_enough_leave",
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    does_plan_provide_enough_medical_leave = Column(
        Boolean,
        nullable=True,
        comment="Denotes if plan provides employees up to 20 weeks of paid leave if unable to work due to serious health condition",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: True,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.ClearedWhen.EXEMPTION_IS_SELF_INSURED_FAMILY: True,
            ColumnIs.AUTO_DENY_WHEN_VALUE_EQUALS: False,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "self_insured.questions.does_plan_provide_enough_medical_leave",
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    does_plan_provide_enough_caring_leave = Column(
        Boolean,
        nullable=True,
        comment="Denotes if plan provides at least 12 weeks of paid leave to provide care to a family member with a serious health condition",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: True,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: False,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.ClearedWhen.EXEMPTION_IS_SELF_INSURED_MEDICAL: True,
            ColumnIs.AUTO_DENY_WHEN_VALUE_EQUALS: False,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "self_insured.questions.does_plan_provide_enough_caring_leave",
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    does_plan_provide_enough_childcare_leave = deprecated_column(
        Boolean,
        nullable=True,
        comment="Denotes if plan provides at least 12 weeks of paid leave to care for a child with a serious health condition",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: False,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: False,
            ColumnIs.ClearedWhen.EXEMPTION_IS_SELF_INSURED_MEDICAL: False,
            ColumnIs.AUTO_DENY_WHEN_VALUE_EQUALS: False,
            ColumnIs.WRITTEN_TO_LOG_FILE: False,
            ColumnIs.DEPRECATED: True,
        },
    )
    does_plan_provide_enough_bonding_leave = Column(
        Boolean,
        nullable=True,
        comment="Denotes if plan provides at least 12 weeks of paid leave to bond with a child during the first 12 months after birth, adoption, or foster care placement",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: True,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: False,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.ClearedWhen.EXEMPTION_IS_SELF_INSURED_MEDICAL: True,
            ColumnIs.AUTO_DENY_WHEN_VALUE_EQUALS: False,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "self_insured.questions.does_plan_provide_enough_bonding_leave",
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    does_plan_provide_enough_armed_forces_leave = Column(
        Boolean,
        nullable=True,
        comment="Denotes if plan provides at least 12 weeks of paid leave for a current Armed Forces family member (spouse, child, or parent)",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: True,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: False,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.ClearedWhen.EXEMPTION_IS_SELF_INSURED_MEDICAL: True,
            ColumnIs.AUTO_DENY_WHEN_VALUE_EQUALS: False,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "self_insured.questions.does_plan_provide_enough_armed_forces_leave",
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    does_plan_provide_enough_armed_forces_illness_leave = Column(
        Boolean,
        nullable=True,
        comment="Denotes if plan plan provide a minimum of 26 paid weeks in a benefit year to care for an Armed Forces family member who requires medical care as a result of illness or injury related to the family member's active service",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: True,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: False,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.ClearedWhen.EXEMPTION_IS_SELF_INSURED_MEDICAL: True,
            ColumnIs.AUTO_DENY_WHEN_VALUE_EQUALS: False,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "self_insured.questions.does_plan_provide_enough_armed_forces_illness_leave",
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    does_plan_pay_enough_benefits = Column(
        Boolean,
        nullable=True,
        comment="Denotes if plan pays benefits greater to or equal to the state's plan",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: True,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: True,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.AUTO_DENY_WHEN_VALUE_EQUALS: False,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "self_insured.questions.does_plan_pay_enough_benefits",
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    does_employer_withhold_premiums = Column(
        Boolean,
        nullable=True,
        comment="Denotes if plan withholds premiums or contributions from employee wages",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: True,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: True,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.AUTO_DENY_WHEN_VALUE_EQUALS: False,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "self_insured.questions.does_employer_withhold_premiums",
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    are_employer_withholdings_within_allowable_amount = Column(
        Boolean,
        nullable=True,
        comment="Denotes if plan withholds less than or equal to amount required by state",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: True,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: True,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.AUTO_DENY_WHEN_VALUE_EQUALS: False,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "self_insured.questions.are_employer_withholdings_within_allowable_amount",
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    does_plan_provide_pfml_job_protection = Column(
        Boolean,
        nullable=True,
        comment="Denotes if workplace policy ensures employees are granted job protections under the Paid Family and Medical Leave",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: True,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: True,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.AUTO_DENY_WHEN_VALUE_EQUALS: False,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "self_insured.questions.does_plan_provide_pfml_job_protection",
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    does_plan_provide_return_to_work_benefits = Column(
        Boolean,
        nullable=True,
        comment="Denotes if workplace policy ensures the continuance of employees' existing rights, if any, to vacation time, sick leave, bonuses, advancement, seniority, length-of-service credit or other employment benefits, plans or programs upon their return to employment",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: True,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: True,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.AUTO_DENY_WHEN_VALUE_EQUALS: False,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "self_insured.questions.does_plan_provide_return_to_work_benefits",
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    does_plan_cover_employee_contribution = Column(
        Boolean,
        nullable=True,
        comment="Denotes if workplace policy continues to contribute the employer's portion of employment-related health insurance benefits while on leave",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: True,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: True,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.AUTO_DENY_WHEN_VALUE_EQUALS: False,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "self_insured.questions.does_plan_cover_employee_contribution",
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    does_plan_cover_short_term_disability = deprecated_column(
        Boolean,
        nullable=True,
        comment="Denotes if medical leave plan is covered under short term disability plan",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: False,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: False,
            ColumnIs.ClearedWhen.EXEMPTION_IS_SELF_INSURED_FAMILY: False,
            ColumnIs.AUTO_DENY_WHEN_VALUE_EQUALS: False,
            ColumnIs.WRITTEN_TO_LOG_FILE: False,
            ColumnIs.DEPRECATED: True,
        },
    )
    does_plan_provide_intermittent_caring_leave = Column(
        Boolean,
        nullable=True,
        comment="Denotes if plan allows leave to be taken intermittently or on a reduced leave schedule, if medically necessary, with the weekly benefit amount being prorated",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: True,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: False,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.ClearedWhen.EXEMPTION_IS_SELF_INSURED_MEDICAL: True,
            ColumnIs.AUTO_DENY_WHEN_VALUE_EQUALS: False,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "self_insured.questions.does_plan_provide_intermittent_caring_leave",
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    does_plan_provide_intermittent_bonding_leave = Column(
        Boolean,
        nullable=True,
        comment="Denotes if plan allows for leave to be taken intermittently or on a reduced leave schedule to bond with a child during the first twelve months after the child's birth, adoption, or foster care placement",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: True,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: False,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.ClearedWhen.EXEMPTION_IS_SELF_INSURED_MEDICAL: True,
            ColumnIs.AUTO_DENY_WHEN_VALUE_EQUALS: False,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "self_insured.questions.does_plan_provide_intermittent_bonding_leave",
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    does_plan_provide_intermittent_armed_forces_leave = Column(
        Boolean,
        nullable=True,
        comment="Denotes if plan allows for leave to be taken intermittently or on a reduced leave schedule due to a qualifying exigency arising out of a family member's active duty or impending call to active duty in the Armed Forces",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: True,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: False,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.ClearedWhen.EXEMPTION_IS_SELF_INSURED_MEDICAL: True,
            ColumnIs.AUTO_DENY_WHEN_VALUE_EQUALS: False,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "self_insured.questions.does_plan_provide_intermittent_armed_forces_leave",
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    does_plan_provide_intermittent_medical_leave = Column(
        Boolean,
        nullable=True,
        comment="Denotes if plan allows for leave for an employee's own serious health condition to be taken intermittently or on a reduced leave schedule",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: True,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: True,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.AUTO_DENY_WHEN_VALUE_EQUALS: False,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "self_insured.questions.does_plan_provide_intermittent_medical_leave",
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    does_plan_cover_former_employees = Column(
        Boolean,
        nullable=True,
        comment="Denotes if plan covers unemployed former employees who apply for benefits for family or medical leave up to 26 weeks after separation from employment, or until they obtain other employment",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: True,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: True,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.AUTO_DENY_WHEN_VALUE_EQUALS: False,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "self_insured.questions.does_plan_cover_former_employees",
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    does_plan_favor_paid_leave_benefits = Column(
        Boolean,
        nullable=True,
        comment="Denotes if plan specifically states that all presumptions shall be made in favor of the availability of leave and the payment of leave benefits",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY: True,
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL: True,
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN: True,
            ColumnIs.AUTO_DENY_WHEN_VALUE_EQUALS: False,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "self_insured.questions.does_plan_favor_paid_leave_benefits",
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    submitted_at = Column(
        TIMESTAMP(timezone=True),
        comment="Date and time employer exemption application was submitted",
        info={
            ColumnIs.RequiredFor.SUBMIT: True,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
    )
    is_legally_acknowledged = Column(
        Boolean,
        nullable=False,
        comment="Denotes if employer exemption application has been legally acknowledged",
        info={
            ColumnIs.RequiredFor.SUBMIT: True,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: True,
            ColumnIs.WRITTEN_TO_LOG_FILE: True,
        },
        default=False,
    )
    contact_first_name = Column(
        Text,
        comment=column_doc(
            "Contact first name associated with employer exemption application", pii=True
        ),
        info={ColumnIs.RequiredFor.SUBMIT: True, ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: True},
    )
    contact_last_name = Column(
        Text,
        comment=column_doc(
            "Contact last name associated with employer exemption application", pii=True
        ),
        info={ColumnIs.RequiredFor.SUBMIT: True, ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: True},
    )
    tpa_contact_first_name = Column(
        Text,
        comment=column_doc(
            "Contact first name associated with employer exemption application third party administrator",
            pii=True,
        ),
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_HAS_THIRD_PARTY_ADMINISTRATOR: True,
            ColumnIs.ClearedWhen.EXEMPTION_DOES_NOT_HAVE_THIRD_PARTY_ADMINISTRATOR: True,
        },
    )
    tpa_contact_last_name = Column(
        Text,
        comment=column_doc(
            "Contact last name associated with employer exemption application third party administrator",
            pii=True,
        ),
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_HAS_THIRD_PARTY_ADMINISTRATOR: True,
            ColumnIs.ClearedWhen.EXEMPTION_DOES_NOT_HAVE_THIRD_PARTY_ADMINISTRATOR: True,
        },
    )
    tpa_contact_title = Column(
        Text,
        comment="Title associated with employer exemption application third party administrator",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_HAS_THIRD_PARTY_ADMINISTRATOR: True,
            ColumnIs.ClearedWhen.EXEMPTION_DOES_NOT_HAVE_THIRD_PARTY_ADMINISTRATOR: True,
        },
    )
    tpa_contact_phone_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey(Phone.phone_id, name="employer_exemption_application_tpa_contact_phone_id_fkey"),
        nullable=True,
        comment="Internal id for the associated Third-Party administrator Phone record",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_HAS_THIRD_PARTY_ADMINISTRATOR: True,
            ColumnIs.ClearedWhen.EXEMPTION_DOES_NOT_HAVE_THIRD_PARTY_ADMINISTRATOR: True,
            ColumnIs.MAPPED_TO_API_FIELD_NAME: "tpa_contact_phone.phone_number",
        },
    )
    tpa_contact_email_address = Column(
        Text,
        comment=column_doc(
            "The Third-party administrator email address associated with the Employer Exemption application",
            pii=True,
        ),
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.RequiredIf.EXEMPTION_HAS_THIRD_PARTY_ADMINISTRATOR: True,
            ColumnIs.ClearedWhen.EXEMPTION_DOES_NOT_HAVE_THIRD_PARTY_ADMINISTRATOR: True,
        },
    )
    contact_phone: Mapped["Phone"] = relationship(
        "Phone",
        foreign_keys=[contact_phone_id],
        single_parent=True,
        cascade="all, delete-orphan",
    )
    tpa_contact_phone: Mapped["Phone"] = relationship(
        "Phone",
        foreign_keys=[tpa_contact_phone_id],
        single_parent=True,
        cascade="all, delete-orphan",
    )
    employer: Mapped["Employer"] = relationship("Employer", foreign_keys=employer_id)
    employer_exemption_application_status: Mapped["LkEmployerExemptionApplicationStatus"] = (
        relationship(
            "LkEmployerExemptionApplicationStatus",
            foreign_keys=employer_exemption_application_status_id,
        )
    )
    user: Mapped["User"] = relationship(
        "User",
        foreign_keys=[created_by_user_id],
    )

    submitted_by_user_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("user.user_id", name="employer_exemption_application_submitted_by_user_id_fkey"),
        nullable=True,
        index=True,
        comment="User id associated with user who submitted the employer exemption application",
        info={ColumnIs.RequiredFor.SUBMIT: True, ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False},
    )
    submitted_by_user: Mapped["User"] = relationship(
        "User",
        foreign_keys=[submitted_by_user_id],
    )
    synced_to_dor: Mapped[bool] = Column(
        Boolean,
        nullable=False,
        default=False,
        comment="Denotes if exemption application has been synced to DOR",
    )
    synced_to_dor_at = Column(
        TIMESTAMP(timezone=True),
        nullable=True,
        comment="Date and time exemption application was last synced to DOR",
        info={
            ColumnIs.RequiredFor.SUBMIT: False,
            ColumnIs.RequiredFor.PRESUBMIT_VALIDATION: False,
            ColumnIs.WRITTEN_TO_LOG_FILE: False,
        },
    )

    insurance_provider: Mapped["InsuranceProvider"] = relationship(
        "InsuranceProvider", foreign_keys=insurance_provider_id
    )
    insurance_plan: Mapped["InsurancePlan"] = relationship(
        "InsurancePlan", foreign_keys=insurance_plan_id
    )

    @classmethod
    def draft(cls, created_by_user_id, employer_id):
        from massgov.pfml.db.lookup_data.employer_exemptions import (
            EmployerExemptionApplicationStatus,
        )

        return cls(
            created_by_user_id=created_by_user_id,
            employer_exemption_application_status_id=EmployerExemptionApplicationStatus.DRAFT.employer_exemption_application_status_id,
            employer_id=employer_id,
            is_legally_acknowledged=False,
        )

    @staticmethod
    def _get_relationships_dict() -> dict:
        """Return a dictionary of all mapped relationship objects

        {'contact_phone': <Relationship; contact_phone>,
         'tpa_contact_phone': <Relationship; tpa_contact_phone>,
         'employer': <Relationship; employer>,
         'employer_exemption_application_status': <Relationship; employer_exemption_application_status>,
         'user': <Relationship; user>,
         'submitted_by_user': <Relationship; submitted_by_user>}
        """
        return dict(inspect(EmployerExemptionApplication).relationships.items())

    @staticmethod
    # cannot find a valid type for the relationship parameter; ignore lint issue
    # until a solution is found
    def _get_relationship_foreign_keys(relationship) -> List[SqlAlchemyColumn]:  # type: ignore[no-untyped-def]
        """Returns a list of columns defined for a relationship"""
        # relationship.local_remote_pairs contains a list tuples (local column, remote column)
        # (eg. calling local_remote_pairs on the contact_phone relationship yields,
        # (attributes removed from brevity):
        #
        # [(
        #   Column('contact_phone_id', ForeignKey('phone.phone_id'), table=<employer_exemption_application>,
        #   Column('phone_id', table=<phone>, primary_key=True, nullable=False,
        #  )]
        return [p[0] for p in relationship.local_remote_pairs]

    @staticmethod
    def is_db_column_name(db_column_name: str) -> bool:
        return db_column_name in EmployerExemptionApplication.columns().keys()

    @staticmethod
    def is_deprecated(db_column_name: str) -> bool:
        column = EmployerExemptionApplication.columns().get(db_column_name)

        if column is None:
            raise KeyError(
                f"Unable to find column '{db_column_name}' in EmployerExemptionApplication."
            )

        return ColumnIs.DEPRECATED in column.info and column.info[ColumnIs.DEPRECATED]

    @staticmethod
    def is_phone_db_model_foreign_key(db_column_name: str) -> bool:
        if EmployerExemptionApplication.is_deprecated(db_column_name):
            return False
        else:
            column = dict(EmployerExemptionApplication.columns().items()).get(db_column_name)
            return (
                "phone.phone_id" in [fk.target_fullname for fk in column.foreign_keys]
                if column is not None
                else False
            )

    @staticmethod
    def is_phone_object_name(name: str) -> bool:
        """Returns True if name is defined as a phone relationship in the model

        The following names return true; anything else return False
            contact_phone
            tpa_contact_phone
        """
        # determine if name is defined as a relationship in the model
        # (eg. contact_phone: Mapped["Phone"] = relationship(...))
        relationship = EmployerExemptionApplication._get_relationships_dict().get(name)

        if relationship is not None:
            for column in EmployerExemptionApplication._get_relationship_foreign_keys(relationship):
                if EmployerExemptionApplication.is_phone_db_model_foreign_key(column.name):
                    return True

        return False

    @staticmethod
    def get_related_phone_object_name(db_column_name: str) -> str | None:
        """Returns the name of the associated phone relationship in the model

        The following db_column_name return a value; anything else return None
            contact_phone_id returns contact_phone
            tpa_contact_phone_id returns tpa_contact_phone
        """
        if EmployerExemptionApplication.is_deprecated(
            db_column_name
        ) or not EmployerExemptionApplication.is_phone_db_model_foreign_key(db_column_name):
            return None
        else:
            relationships = EmployerExemptionApplication._get_relationships_dict()

            for object_name, rel in relationships.items():
                for column in EmployerExemptionApplication._get_relationship_foreign_keys(rel):
                    # relationship object has a foreign key mapping to db_column_name
                    # therefore the relationship object name is the associated phone
                    # object name
                    if column.name == db_column_name:
                        return object_name

            # all relationships have been evaluated and no matches were found
            return None

    def get_related_phone_object(self, db_column_name: str) -> Phone | None:
        name = self.get_related_phone_object_name(db_column_name)
        return getattr(self, name) if name is not None else None

    @staticmethod
    def _get_columns_where_info_key_is_true(
        key: ColumnIs.RequiredIf | ColumnIs.ClearedWhen | str,
    ) -> list[SqlAlchemyColumn]:
        return [
            column
            for db_column_name, column in EmployerExemptionApplication.columns().items()
            if not EmployerExemptionApplication.is_deprecated(db_column_name)
            and key in column.info
            and column.info[key]
        ]

    @staticmethod
    def _get_column_names_where_info_key_is_true(
        key: ColumnIs.RequiredIf | ColumnIs.ClearedWhen,
    ) -> list[str]:
        return [
            column.name
            for column in EmployerExemptionApplication._get_columns_where_info_key_is_true(key)
        ]

    @staticmethod
    def get_mapped_to_api_field_name(db_column_name: str) -> str | None:
        if EmployerExemptionApplication.is_deprecated(db_column_name):
            return None
        else:
            db_column_dict = EmployerExemptionApplication.columns()
            column = db_column_dict.get(db_column_name)

            return (
                column.info.get(ColumnIs.MAPPED_TO_API_FIELD_NAME, db_column_name)
                if column is not None
                else None
            )

    @staticmethod
    def get_columns_required_for_self_insured_family():
        return EmployerExemptionApplication._get_column_names_where_info_key_is_true(
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_FAMILY
        )

    @staticmethod
    def get_columns_required_for_self_insured_medical():
        return EmployerExemptionApplication._get_column_names_where_info_key_is_true(
            ColumnIs.RequiredIf.EXEMPTION_IS_SELF_INSURED_MEDICAL
        )

    @staticmethod
    def get_columns_required_for_purchased_private_insured():
        return EmployerExemptionApplication._get_column_names_where_info_key_is_true(
            ColumnIs.RequiredIf.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN
        )

    @staticmethod
    def get_columns_required_for_third_party_administrator():
        return EmployerExemptionApplication._get_column_names_where_info_key_is_true(
            ColumnIs.RequiredIf.EXEMPTION_HAS_THIRD_PARTY_ADMINISTRATOR
        )

    def _get_required_columns(self, required_for_type: ColumnIs.RequiredFor) -> set[str]:
        required = set()

        required_for_self_insured_family = self.get_columns_required_for_self_insured_family()
        required_for_self_insured_medical = self.get_columns_required_for_self_insured_medical()
        required_for_purchased_private_plan = (
            self.get_columns_required_for_purchased_private_insured()
        )
        required_for_third_party_administrator = (
            self.get_columns_required_for_third_party_administrator()
        )

        for column_name, column_attr in EmployerExemptionApplication.columns().items():
            if not column_attr.nullable or column_attr.info[required_for_type]:
                required.add(column_name)

            if self.is_self_insured_plan:
                if self.has_family_exemption and column_name in required_for_self_insured_family:
                    required.add(column_name)

                if self.has_medical_exemption and column_name in required_for_self_insured_medical:
                    required.add(column_name)

            elif self.is_self_insured_plan is not None:
                if column_name in required_for_purchased_private_plan:
                    required.add(column_name)

            if (
                self.has_third_party_administrator
                and column_name in required_for_third_party_administrator
            ):
                required.add(column_name)

        return required

    def get_columns_required_for_submit(self):
        return self._get_required_columns(ColumnIs.RequiredFor.SUBMIT)

    def get_columns_required_for_presubmit_validation(self):
        return self._get_required_columns(ColumnIs.RequiredFor.PRESUBMIT_VALIDATION)

    @staticmethod
    def get_columns_to_clear_when_is_self_insured():
        return EmployerExemptionApplication._get_column_names_where_info_key_is_true(
            ColumnIs.ClearedWhen.EXEMPTION_IS_SELF_INSURED_PLAN
        )

    @staticmethod
    def get_columns_to_clear_when_is_self_insured_family():
        return EmployerExemptionApplication._get_column_names_where_info_key_is_true(
            ColumnIs.ClearedWhen.EXEMPTION_IS_SELF_INSURED_FAMILY
        )

    @staticmethod
    def get_columns_to_clear_when_is_self_insured_medical():
        return EmployerExemptionApplication._get_column_names_where_info_key_is_true(
            ColumnIs.ClearedWhen.EXEMPTION_IS_SELF_INSURED_MEDICAL
        )

    @staticmethod
    def get_columns_to_clear_when_is_not_self_insured():
        return EmployerExemptionApplication._get_column_names_where_info_key_is_true(
            ColumnIs.ClearedWhen.EXEMPTION_IS_PURCHASED_PRIVATE_PLAN
        )

    @staticmethod
    def get_columns_to_clear_when_not_third_party_admin():
        return EmployerExemptionApplication._get_column_names_where_info_key_is_true(
            ColumnIs.ClearedWhen.EXEMPTION_DOES_NOT_HAVE_THIRD_PARTY_ADMINISTRATOR
        )

    @staticmethod
    def get_log_attributes() -> dict[str, str]:
        log_attr: dict[str, str] = {}
        timestamp_mixin_fields = [LogAttribute("created_at"), LogAttribute("updated_at")]

        col = EmployerExemptionApplication._get_columns_where_info_key_is_true(
            ColumnIs.WRITTEN_TO_LOG_FILE
        )

        for c in col:
            info = c.info.get(ColumnIs.WRITTEN_TO_LOG_FILE_AS)
            attr_name = c.name if info is None else info.attr_name
            value = c.name if info is None else info.value
            log_attr[attr_name] = value

        for f in timestamp_mixin_fields:
            log_attr[f.attr_name] = f.value

        return dict(sorted(log_attr.items()))

    @staticmethod
    # named is_family_only_exemption to distinguish static method from instance method
    def is_family_only_exemption(
        # (has_family_exemption and not has_medical_exemption) can evaluate to None
        # (ex. None and True). return False if bool condition evaluates to None
        has_family_exemption: bool | None = None,
        has_medical_exemption: bool | None = None,
    ) -> bool:
        return (has_family_exemption and not has_medical_exemption) or False

    @staticmethod
    # named is_medical_only_exemption to distinguish static method from instance method
    def is_medical_only_exemption(
        has_family_exemption: bool | None = None,
        has_medical_exemption: bool | None = None,
    ) -> bool:
        # (not has_family_exemption and has_medical_exemption) can evaluate to None
        # (ex. True and None). return False if bool condition evaluates to None
        return (not has_family_exemption and has_medical_exemption) or False

    def is_auto_deny_value(self, db_column_name: str) -> bool:
        db_column_dict = self.columns()
        column = db_column_dict.get(db_column_name)

        if column is not None and ColumnIs.AUTO_DENY_WHEN_VALUE_EQUALS in column.info:
            db_value = getattr(self, db_column_name)
            deny_when_value = column.info.get(ColumnIs.AUTO_DENY_WHEN_VALUE_EQUALS)
            return deny_when_value == db_value
        else:
            return False


class InsuranceProvider(Base, TimestampMixin):
    __tablename__ = "insurance_provider"
    insurance_provider_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    insurance_provider_name: Mapped[str] = Column(Text, nullable=False)
    deactivated: Mapped[bool] = Column(Boolean, nullable=False, default=False)
    address_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("address.address_id"),
        nullable=True,
        comment="Internal id for the associated Address record",
    )
    phone_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey(Phone.phone_id),
        nullable=True,
        comment="Internal id for the associated Phone record",
    )

    address: Mapped["Address"] = relationship("Address", foreign_keys=address_id)
    phone: Mapped["Phone"] = relationship("Phone", foreign_keys=phone_id)

    def __init__(
        self,
        insurance_provider_id: int,
        insurance_provider_name: str,
        address_id: Optional[UUID] = None,
        phone_id: Optional[UUID] = None,
        deactivated: bool = False,
    ):
        self.insurance_provider_id = insurance_provider_id
        self.insurance_provider_name = insurance_provider_name
        self.address_id = address_id
        self.phone_id = phone_id
        self.deactivated = deactivated


class InsurancePlan(Base, TimestampMixin):
    __tablename__ = "insurance_plan"
    __table_args__ = (
        UniqueConstraint(
            "insurance_provider_id",
            "form_name",
            name="uix_insurance_plan_insurance_provider_id_form_name",
        ),
    )

    insurance_plan_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    insurance_provider_id = Column(
        Integer, ForeignKey("insurance_provider.insurance_provider_id"), nullable=False
    )
    deactivated: Mapped[bool] = Column(Boolean, nullable=False, default=False)

    form_name: Mapped[str] = Column(
        Text,
        nullable=False,
        comment="Form Id associated with a given insurance plan",
    )
    has_family_exemption = Column(
        Boolean,
        nullable=False,
        comment="Employer has an exemption from Family PFML",
    )
    has_medical_exemption = Column(
        Boolean,
        nullable=False,
        comment="Employer has an exemption from Medical PFML",
    )

    def __init__(
        self,
        insurance_plan_id: int,
        insurance_provider_id: int,
        form_name: str,
        has_family_exemption: bool,
        has_medical_exemption: bool,
        deactivated: bool = False,
    ):
        self.insurance_plan_id = insurance_plan_id
        self.insurance_provider_id = insurance_provider_id
        self.form_name = form_name
        self.has_family_exemption = has_family_exemption
        self.has_medical_exemption = has_medical_exemption
        self.deactivated = deactivated


def sync_insurance_providers(db_session: Session) -> None:
    from massgov.pfml.db.lookup_data.employees import AddressType
    from massgov.pfml.db.lookup_data.geo import Country, GeoState
    from massgov.pfml.db.lookup_data.phone import PhoneType

    class InsurancePlanRef:
        __slots__ = [
            "insurance_plan_id",
            "form_name",
            "has_family_exemption",
            "has_medical_exemption",
            "deactivated",
        ]

        def __init__(
            self,
            insurance_plan_id: int,
            form_name: str,
            has_medical_exemption: bool,
            has_family_exemption: bool,
            deactivated: bool = False,
        ):
            self.insurance_plan_id = insurance_plan_id
            self.form_name = form_name
            self.has_family_exemption = has_family_exemption
            self.has_medical_exemption = has_medical_exemption
            self.deactivated = deactivated

    class InsuranceProviderRef:
        def __init__(
            self,
            insurance_provider_id: int,
            name: str,
            address_line_one: Optional[str],
            city: Optional[str],
            state_abbrev: Optional[str],
            zip_code: Optional[str],
            phone_number: Optional[str],
            country: Optional[str],
            insurance_plan_ref_list: Optional[list[InsurancePlanRef]] = None,
            address_line_two: Optional[str] = None,
            deactivated: bool = False,
        ):
            self.insurance_provider_id = insurance_provider_id
            self.name = name
            self.address_line_one = address_line_one
            self.address_line_two = address_line_two
            self.city = city
            self.state_abbrev = state_abbrev
            self.geo_state_id = GeoState.get_id(state_abbrev) if state_abbrev is not None else None
            self.zip_code = zip_code
            self.country_id = Country.get_id(country) if country is not None else None
            self.phone_number = convert_to_E164(phone_number) if phone_number is not None else None
            self.deactivated = deactivated
            self.insurance_plan_list = (
                [
                    InsurancePlan(
                        insurance_plan_id=plan.insurance_plan_id,
                        insurance_provider_id=insurance_provider_id,
                        form_name=plan.form_name,
                        has_family_exemption=plan.has_family_exemption,
                        has_medical_exemption=plan.has_medical_exemption,
                        deactivated=plan.deactivated,
                    )
                    for plan in insurance_plan_ref_list
                ]
                if insurance_plan_ref_list
                else []
            )

    # define static list of insurance plans sans insurance_provider_id;
    # insurance_provider_id willbe assigned as part of InsuranceProviderRef
    # instantiation
    INSURANCE_PROVIDER_ID_OTHER = 9999
    insurance_plans = {
        "Other": InsurancePlanRef(
            insurance_plan_id=INSURANCE_PROVIDER_ID_OTHER,
            form_name="Other",
            has_family_exemption=True,
            has_medical_exemption=True,
            deactivated=True,
        ),
        "G1500": InsurancePlanRef(
            insurance_plan_id=1,
            form_name="G1500",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "W A MA PFML 0820 P": InsurancePlanRef(
            insurance_plan_id=2,
            form_name="W A MA PFML 0820 P",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "W A MA PFL 0820 P": InsurancePlanRef(
            insurance_plan_id=3,
            form_name="W A MA PFL 0820 P",
            has_family_exemption=True,
            has_medical_exemption=False,
        ),
        "W A MA PML 0820 P": InsurancePlanRef(
            insurance_plan_id=4,
            form_name="W A MA PML 0820 P",
            has_family_exemption=False,
            has_medical_exemption=True,
        ),
        "05 DBL001P 22 05 20": InsurancePlanRef(
            insurance_plan_id=5,
            form_name="05 DBL001P 22 05 20",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "05 DBL003P 22 05 20": InsurancePlanRef(
            insurance_plan_id=6,
            form_name="05 DBL003P 22 05 20",
            has_family_exemption=True,
            has_medical_exemption=False,
        ),
        "05 DBL002P 22 05 20": InsurancePlanRef(
            insurance_plan_id=7,
            form_name="05 DBL002P 22 05 20",
            has_family_exemption=False,
            has_medical_exemption=True,
        ),
        "CSD2100MA": InsurancePlanRef(
            insurance_plan_id=8,
            form_name="CSD2100MA",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "CSD3100MA": InsurancePlanRef(
            insurance_plan_id=9,
            form_name="CSD3100MA",
            has_family_exemption=True,
            has_medical_exemption=False,
        ),
        "CSD1100MA": InsurancePlanRef(
            insurance_plan_id=10,
            form_name="CSD1100MA",
            has_family_exemption=False,
            has_medical_exemption=True,
        ),
        "GP-1-PFML-20-MA": InsurancePlanRef(
            insurance_plan_id=11,
            form_name="GP-1-PFML-20-MA",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "GBD-1852 PFML (2024) (MA) (Rev-1)": InsurancePlanRef(
            insurance_plan_id=12,
            form_name="GBD-1852 PFML (2024) (MA) (Rev-1)",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "MA-PFML-100022": InsurancePlanRef(
            insurance_plan_id=13,
            form_name="MA-PFML-100022",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "MAPFML2-20-STL": InsurancePlanRef(
            insurance_plan_id=14,
            form_name="MAPFML2-20-STL",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "MAPFL2-20-STL": InsurancePlanRef(
            insurance_plan_id=15,
            form_name="MAPFL2-20-STL",
            has_family_exemption=True,
            has_medical_exemption=False,
        ),
        "MAPML2-20-STL": InsurancePlanRef(
            insurance_plan_id=16,
            form_name="MAPML2-20-STL",
            has_family_exemption=False,
            has_medical_exemption=True,
        ),
        "MAPFML2-22-STL": InsurancePlanRef(
            insurance_plan_id=17,
            form_name="MAPFML2-22-STL",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "MAPFL2-22-STL": InsurancePlanRef(
            insurance_plan_id=18,
            form_name="MAPFL2-22-STL",
            has_family_exemption=True,
            has_medical_exemption=False,
        ),
        "MAPML2-22-STL": InsurancePlanRef(
            insurance_plan_id=19,
            form_name="MAPML2-22-STL",
            has_family_exemption=False,
            has_medical_exemption=True,
        ),
        "MAPFML-20-STL": InsurancePlanRef(
            insurance_plan_id=20,
            form_name="MAPFML-20-STL",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "MAPFL-20-STL": InsurancePlanRef(
            insurance_plan_id=21,
            form_name="MAPFL-20-STL",
            has_family_exemption=True,
            has_medical_exemption=False,
        ),
        "MAPML-20-STL": InsurancePlanRef(
            insurance_plan_id=22,
            form_name="MAPML-20-STL",
            has_family_exemption=False,
            has_medical_exemption=True,
        ),
        "MAPFML-22-STL": InsurancePlanRef(
            insurance_plan_id=23,
            form_name="MAPFML-22-STL",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "MAPFL-22-STL": InsurancePlanRef(
            insurance_plan_id=24,
            form_name="MAPFL-22-STL",
            has_family_exemption=True,
            has_medical_exemption=False,
        ),
        "MAPML-22-STL": InsurancePlanRef(
            insurance_plan_id=25,
            form_name="MAPML-22-STL",
            has_family_exemption=False,
            has_medical_exemption=True,
        ),
        "GPNP20-MA-PFML": InsurancePlanRef(
            insurance_plan_id=26,
            form_name="GPNP20-MA-PFML",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "GPNP20-MA-PFL": InsurancePlanRef(
            insurance_plan_id=27,
            form_name="GPNP20-MA-PFL",
            has_family_exemption=True,
            has_medical_exemption=False,
        ),
        "GPNP20-MA-PML": InsurancePlanRef(
            insurance_plan_id=28,
            form_name="GPNP20-MA-PML",
            has_family_exemption=False,
            has_medical_exemption=True,
        ),
        "MOEBP20-MAPFML": InsurancePlanRef(
            insurance_plan_id=29,
            form_name="MOEBP20-MAPFML",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "MOEBP20-MAPFL": InsurancePlanRef(
            insurance_plan_id=30,
            form_name="MOEBP20-MAPFL",
            has_family_exemption=True,
            has_medical_exemption=False,
        ),
        "MOEBP20-MAPML": InsurancePlanRef(
            insurance_plan_id=31,
            form_name="MOEBP20-MAPML",
            has_family_exemption=False,
            has_medical_exemption=True,
        ),
        "GC 458 MA PFML": InsurancePlanRef(
            insurance_plan_id=32,
            form_name="GC 458 MA PFML",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "GC 458 MA PFL": InsurancePlanRef(
            insurance_plan_id=33,
            form_name="GC 458 MA PFL",
            has_family_exemption=True,
            has_medical_exemption=False,
        ),
        "GC 458 MA PML": InsurancePlanRef(
            insurance_plan_id=34,
            form_name="GC 458 MA PML",
            has_family_exemption=False,
            has_medical_exemption=True,
        ),
        "115588 PFML 0520MA": InsurancePlanRef(
            insurance_plan_id=35,
            form_name="115588 PFML 0520MA",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "115588 PFL 0520MA": InsurancePlanRef(
            insurance_plan_id=36,
            form_name="115588 PFL 0520MA",
            has_family_exemption=True,
            has_medical_exemption=False,
        ),
        "115588 PML 0520MA": InsurancePlanRef(
            insurance_plan_id=37,
            form_name="115588 PML 0520MA",
            has_family_exemption=False,
            has_medical_exemption=True,
        ),
        "LRS-9581-1021": InsurancePlanRef(
            insurance_plan_id=38,
            form_name="LRS-9581-1021",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "PFL-100A-2024-MA": InsurancePlanRef(
            insurance_plan_id=39,
            form_name="PFL-100A-2024-MA",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "SPL PFMLP 0820 MA": InsurancePlanRef(
            insurance_plan_id=40,
            form_name="SPL PFMLP 0820 MA",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "SPL PFLP 0820 MA": InsurancePlanRef(
            insurance_plan_id=41,
            form_name="SPL PFLP 0820 MA",
            has_family_exemption=True,
            has_medical_exemption=False,
        ),
        "SPL PMLP 0820 MA": InsurancePlanRef(
            insurance_plan_id=42,
            form_name="SPL PMLP 0820 MA",
            has_family_exemption=False,
            has_medical_exemption=True,
        ),
        "MA0121-PFML-P": InsurancePlanRef(
            insurance_plan_id=43,
            form_name="MA0121-PFML-P",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "20-PFML-GP-01-MA": InsurancePlanRef(
            insurance_plan_id=44,
            form_name="20-PFML-GP-01-MA",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "20-PFL-GP-01-MA": InsurancePlanRef(
            insurance_plan_id=45,
            form_name="20-PFL-GP-01-MA",
            has_family_exemption=True,
            has_medical_exemption=False,
        ),
        "20-PML-GP-01-MA": InsurancePlanRef(
            insurance_plan_id=46,
            form_name="20-PML-GP-01-MA",
            has_family_exemption=False,
            has_medical_exemption=True,
        ),
        "GDC-00400/MA 5/20": InsurancePlanRef(
            insurance_plan_id=47,
            form_name="GDC-00400/MA 5/20",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "********** MA": InsurancePlanRef(
            insurance_plan_id=48,
            form_name="********** MA",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "GPFL2021C MA": InsurancePlanRef(
            insurance_plan_id=49,
            form_name="GPFL2021C MA",
            has_family_exemption=True,
            has_medical_exemption=False,
        ),
        "GPML2021C MA": InsurancePlanRef(
            insurance_plan_id=50,
            form_name="GPML2021C MA",
            has_family_exemption=False,
            has_medical_exemption=True,
        ),
        "UHI-PFML-POL (01/01/2022)": InsurancePlanRef(
            insurance_plan_id=51,
            form_name="UHI-PFML-POL (01/01/2022)",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "UHI-PFL-POL (01/01/2022)": InsurancePlanRef(
            insurance_plan_id=52,
            form_name="UHI-PFL-POL (01/01/2022)",
            has_family_exemption=True,
            has_medical_exemption=False,
        ),
        "UHI-PML-POL (01/01/2022)": InsurancePlanRef(
            insurance_plan_id=53,
            form_name="UHI-PML-POL (01/01/2022)",
            has_family_exemption=False,
            has_medical_exemption=True,
        ),
        "PFML20-1": InsurancePlanRef(
            insurance_plan_id=54,
            form_name="PFML20-1",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "PML20-1": InsurancePlanRef(
            insurance_plan_id=55,
            form_name="PML20-1",
            has_family_exemption=False,
            has_medical_exemption=True,
        ),
        "PFML-POL-MA (4-20)": InsurancePlanRef(
            insurance_plan_id=56,
            form_name="PFML-POL-MA (4-20)",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "PML-POL-MA (4-20)": InsurancePlanRef(
            insurance_plan_id=57,
            form_name="PML-POL-MA (4-20)",
            has_family_exemption=False,
            has_medical_exemption=True,
        ),
        "ZA-MA-PFML-GP-01": InsurancePlanRef(
            insurance_plan_id=58,
            form_name="ZA-MA-PFML-GP-01",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
        "ZA-MA-PFL-GP-03": InsurancePlanRef(
            insurance_plan_id=59,
            form_name="ZA-MA-PFL-GP-03",
            has_family_exemption=True,
            has_medical_exemption=False,
        ),
        "ZA-MA-MLGP-02": InsurancePlanRef(
            insurance_plan_id=60,
            form_name="ZA-MA-MLGP-02",
            has_family_exemption=False,
            has_medical_exemption=True,
        ),
        "SSL-POL-MAPFML-0623": InsurancePlanRef(
            insurance_plan_id=61,
            form_name="SSL-POL-MAPFML-0623",
            has_family_exemption=True,
            has_medical_exemption=True,
        ),
    }

    # define static list list of insurance providers, including references to
    # associated insurance plans defined above

    providers = [
        InsuranceProviderRef(
            insurance_provider_id=INSURANCE_PROVIDER_ID_OTHER,
            name="Other",
            address_line_one=None,
            city=None,
            state_abbrev=None,
            zip_code=None,
            phone_number=None,
            country=None,
            insurance_plan_ref_list=[
                insurance_plans["Other"],
            ],
            deactivated=True,
        ),
        InsuranceProviderRef(
            insurance_provider_id=1,
            name="American Fidelity Assurance Company",
            address_line_one="9000 Cameron Parkway",
            city="Oklahoma City",
            state_abbrev="OK",
            zip_code="73114",
            phone_number="(*************",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["G1500"],
            ],
        ),
        InsuranceProviderRef(
            insurance_provider_id=2,
            name="Anthem Life Insurance Company",
            address_line_one="220 Virginia Avenue Mailpoint IN23A-516",
            city="Indianapolis",
            state_abbrev="IN",
            zip_code="46204",
            phone_number="(*************",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["W A MA PFML 0820 P"],
                insurance_plans["W A MA PFL 0820 P"],
                insurance_plans["W A MA PML 0820 P"],
            ],
        ),
        InsuranceProviderRef(
            insurance_provider_id=3,
            name="Arch Insurance Company",
            address_line_one="Harborside 3 210 Hudson Street Suite 300",
            city="Jersey City",
            state_abbrev="NJ",
            zip_code="07311",
            phone_number="(*************",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["05 DBL001P 22 05 20"],
                insurance_plans["05 DBL003P 22 05 20"],
                insurance_plans["05 DBL002P 22 05 20"],
            ],
        ),
        InsuranceProviderRef(
            insurance_provider_id=4,
            name="Continental American Insurance Company",
            address_line_one="PO Box 427",
            city="Columbia",
            state_abbrev="SC",
            zip_code="29202",
            phone_number="(*************",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["CSD2100MA"],
                insurance_plans["CSD3100MA"],
                insurance_plans["CSD1100MA"],
            ],
        ),
        InsuranceProviderRef(
            insurance_provider_id=5,
            name="The Guardian Life Insurance Company Of America",
            address_line_one="7 Hanover Square",
            city="New York",
            state_abbrev="NY",
            zip_code="10004",
            phone_number="(*************",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["GP-1-PFML-20-MA"],
            ],
        ),
        InsuranceProviderRef(
            insurance_provider_id=6,
            name="Hartford Life And Accident Insurance Company",
            address_line_one="One Hartford Plaza",
            city="Hartford",
            state_abbrev="CT",
            zip_code="06155",
            phone_number="(*************",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["GBD-1852 PFML (2024) (MA) (Rev-1)"],
            ],
        ),
        InsuranceProviderRef(
            insurance_provider_id=7,
            name="Life Insurance Company Of North America",
            address_line_one="1601 Chestnut Street",
            city="Philadelphia",
            state_abbrev="PA",
            zip_code="19192",
            phone_number="(*************",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["MA-PFML-100022"],
            ],
        ),
        InsuranceProviderRef(
            insurance_provider_id=8,
            name="Lincoln Life & Annuity Company Of New York",
            address_line_one="8801 Indian Hills Drive P.O. Box 2616",
            city="Omaha",
            state_abbrev="NE",
            zip_code="68114",
            phone_number="(*************",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["MAPFML2-20-STL"],
                insurance_plans["MAPFL2-20-STL"],
                insurance_plans["MAPML2-20-STL"],
                insurance_plans["MAPFML2-22-STL"],
                insurance_plans["MAPFL2-22-STL"],
                insurance_plans["MAPML2-22-STL"],
            ],
        ),
        InsuranceProviderRef(
            insurance_provider_id=9,
            name="The Lincoln National Life Insurance Company",
            address_line_one="100 Liberty Way Suite 100",
            city="Dover",
            state_abbrev="NH",
            zip_code="03820",
            phone_number="(*************",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["MAPFML-20-STL"],
                insurance_plans["MAPFL-20-STL"],
                insurance_plans["MAPML-20-STL"],
                insurance_plans["MAPFML-22-STL"],
                insurance_plans["MAPFL-22-STL"],
                insurance_plans["MAPML-22-STL"],
            ],
        ),
        InsuranceProviderRef(
            insurance_provider_id=10,
            name="Metropolitan Life Insurance Company",
            address_line_one="200 Park Avenue",
            city="New York",
            state_abbrev="NY",
            zip_code="10166",
            phone_number="(*************",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["GPNP20-MA-PFML"],
                insurance_plans["GPNP20-MA-PFL"],
                insurance_plans["GPNP20-MA-PML"],
            ],
        ),
        InsuranceProviderRef(
            insurance_provider_id=11,
            name="Equitable Financial Life Insurance Company Of America",
            address_line_one="525 Washington Boulevard",
            city="Jersey City",
            state_abbrev="NJ",
            zip_code="07310",
            phone_number="(*************",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["MOEBP20-MAPFML"],
                insurance_plans["MOEBP20-MAPFL"],
                insurance_plans["MOEBP20-MAPML"],
            ],
        ),
        InsuranceProviderRef(
            insurance_provider_id=12,
            name="Principal Life Insurance Company",
            address_line_one="711 High Street",
            city="Des Moines",
            state_abbrev="IA",
            zip_code="50392",
            phone_number="(*************",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["GC 458 MA PFML"],
                insurance_plans["GC 458 MA PFL"],
                insurance_plans["GC 458 MA PML"],
            ],
        ),
        InsuranceProviderRef(
            insurance_provider_id=13,
            name="The Prudential Insurance Company Of America",
            address_line_one="751 Broad Street",
            city="Newark",
            state_abbrev="NJ",
            zip_code="07102",
            phone_number="(*************",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["115588 PFML 0520MA"],
                insurance_plans["115588 PFL 0520MA"],
                insurance_plans["115588 PML 0520MA"],
            ],
        ),
        InsuranceProviderRef(
            insurance_provider_id=14,
            name="Reliance Standard Insurance Company",
            address_line_one="2001 Market Street Suite 1500",
            city="Philadelphia",
            state_abbrev="PA",
            zip_code="19103",
            phone_number="(*************",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["LRS-9581-1021"],
            ],
        ),
        InsuranceProviderRef(
            insurance_provider_id=15,
            name="Renaissance Life & Health Insurance Company Of America",
            address_line_one="225 S. East Street Suite 360",
            city="Indianapolis",
            state_abbrev="IN",
            zip_code="46202",
            phone_number="(*************",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["PFL-100A-2024-MA"],
            ],
        ),
        InsuranceProviderRef(
            insurance_provider_id=16,
            name="Shelterpoint Life Insurance Company",
            address_line_one="1225 Franklin Ave Suite 475",
            city="Garden City",
            state_abbrev="NY",
            zip_code="11530",
            phone_number="(800)365-4999",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["SPL PFMLP 0820 MA"],
                insurance_plans["SPL PFLP 0820 MA"],
                insurance_plans["SPL PMLP 0820 MA"],
            ],
        ),
        InsuranceProviderRef(
            insurance_provider_id=17,
            name="Standard Insurance Company",
            address_line_one="1100 SW 6th Avenue",
            city="Portland",
            state_abbrev="OR",
            zip_code="97204",
            phone_number="(*************",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["MA0121-PFML-P"],
            ],
        ),
        InsuranceProviderRef(
            insurance_provider_id=18,
            name="Sun Life Insurance Company Of Canada",
            address_line_one="175 Addison Road",
            city="Windsor",
            state_abbrev="CT",
            zip_code="06095",
            phone_number="(*************",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["20-PFML-GP-01-MA"],
                insurance_plans["20-PFL-GP-01-MA"],
                insurance_plans["20-PML-GP-01-MA"],
            ],
        ),
        InsuranceProviderRef(
            insurance_provider_id=19,
            name="Symetra Life Insurance Company",
            address_line_one="777 108th Ave NE Suite 1200",
            city="Bellevue",
            state_abbrev="WA",
            zip_code="98004",
            phone_number="(*************",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["GDC-00400/MA 5/20"],
            ],
        ),
        InsuranceProviderRef(
            insurance_provider_id=20,
            name="United Of Omaha Life Insurance Company",
            address_line_one="3300 Mutual of Omaha Plaza",
            city="Omaha",
            state_abbrev="NE",
            zip_code="68175",
            phone_number="(*************",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["********** MA"],
                insurance_plans["GPFL2021C MA"],
                insurance_plans["GPML2021C MA"],
            ],
        ),
        InsuranceProviderRef(
            insurance_provider_id=21,
            name="United Healthcare Insurance Company",
            address_line_one="185 Asylum Street",
            city="Hartford",
            state_abbrev="CT",
            zip_code="06103",
            phone_number="(*************",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["UHI-PFML-POL (01/01/2022)"],
                insurance_plans["UHI-PFL-POL (01/01/2022)"],
                insurance_plans["UHI-PML-POL (01/01/2022)"],
            ],
        ),
        InsuranceProviderRef(
            insurance_provider_id=22,
            name="Unum Insurance Company",
            address_line_one="2211 Congress Street",
            city="Portland",
            state_abbrev="ME",
            zip_code="04122",
            phone_number="(*************",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["PFML20-1"],
                insurance_plans["PML20-1"],
            ],
        ),
        InsuranceProviderRef(
            insurance_provider_id=23,
            name="Usable Life",
            address_line_one="PO Box 1650",
            city="Little Rock",
            state_abbrev="AR",
            zip_code="72203",
            phone_number="(*************",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["PFML-POL-MA (4-20)"],
                insurance_plans["PML-POL-MA (4-20)"],
            ],
        ),
        InsuranceProviderRef(
            insurance_provider_id=24,
            name="Zurich American Life Insurance Company",
            address_line_one="199 Scott Swamp Road",
            city="Farmington",
            state_abbrev="CT",
            zip_code="06032",
            phone_number="(*************",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["ZA-MA-PFML-GP-01"],
                insurance_plans["ZA-MA-PFL-GP-03"],
                insurance_plans["ZA-MA-MLGP-02"],
            ],
        ),
        InsuranceProviderRef(
            insurance_provider_id=25,
            name="Standard Security Life Insurance Company Of New York",
            address_line_one="485 Madison Avenue 14th Floor",
            city="New York",
            state_abbrev="NY",
            zip_code="10022",
            phone_number="(*************",
            country="USA",
            insurance_plan_ref_list=[
                insurance_plans["SSL-POL-MAPFML-0623"],
            ],
        ),
    ]

    for provider in providers:
        address = Address(
            address_line_one=provider.address_line_one,
            address_line_two=provider.address_line_two,
            city=provider.city,
            geo_state_id=provider.geo_state_id,
            geo_state_text=provider.state_abbrev,
            zip_code=provider.zip_code,
            country_id=provider.country_id,
            address_type_id=AddressType.MAILING.address_type_id,
        )

        phone = Phone(
            phone_number=provider.phone_number, phone_type_id=PhoneType.PHONE.phone_type_id
        )

        db_provider = (
            db_session.query(InsuranceProvider)
            .filter(InsuranceProvider.insurance_provider_id == provider.insurance_provider_id)
            .one_or_none()
        )

        if db_provider:
            address.address_id = db_provider.address_id  # type: ignore
            phone.phone_id = db_provider.phone_id
        else:
            # create address/phone uuids for all insurance providers not already in the DB
            address.address_id = uuid.uuid4()
            phone.phone_id = uuid.uuid4()

        if address.address_id is not None:
            db_session.merge(address)

        if phone.phone_id:
            db_session.merge(phone)

        # Flush the address and phone number to the database to obtain the address_id and phone_id
        db_session.flush()

        db_provider = db_session.merge(
            InsuranceProvider(
                insurance_provider_id=provider.insurance_provider_id,
                insurance_provider_name=provider.name,
                address_id=address.address_id,
                phone_id=phone.phone_id,
                deactivated=provider.deactivated,
            )
        )

        # Add all of the providers' plans to the InsurancePlan table
        for plan in provider.insurance_plan_list:
            db_session.merge(plan)
