from uuid import UUID

from sqlalchemy import <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Integer, Text
from sqlalchemy.dialects.postgresql import UUID as SQL_UUID
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column as Column
from sqlalchemy.orm import relationship

from massgov.pfml.db.models.change_request import ChangeRequest

from .base import Base, TableStatus, TimestampMixin, uuid_gen


class LkDeferredSubmissionStatus(Base):
    __tablename__ = "lk_deferred_submission_status"

    deferred_submission_status_id: Mapped[int] = Column(
        Integer, primary_key=True, autoincrement=True
    )
    deferred_submission_status_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, deferred_submission_status_id, deferred_submission_status_description):
        self.deferred_submission_status_id = deferred_submission_status_id
        self.deferred_submission_status_description = deferred_submission_status_description

    @hybrid_property
    def description(self) -> str:
        return self.deferred_submission_status_description


class DeferredSubmissionItem(Base, TimestampMixin):
    __tablename__ = "deferred_submission_item"
    __table_status__ = TableStatus.ACTIVE

    deferred_submission_item_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen
    )
    change_request_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("change_request.change_request_id"),
        index=True,
        nullable=True,
        comment="ID of the change request being deferred if it is a change request",
    )
    deferred_submission_status_id = Column(
        Integer, ForeignKey(LkDeferredSubmissionStatus.deferred_submission_status_id)
    )
    num_submission_attempts = Column(
        Integer, nullable=False, default=0, comment="Number of submission attempts for this item"
    )
    submitted_at = Column(
        TIMESTAMP(timezone=True),
        comment="Timestamp for when the last attempt to submit this item was made",
    )

    deferred_submission_status_instance: Mapped[LkDeferredSubmissionStatus] = relationship(
        LkDeferredSubmissionStatus
    )
    change_request: Mapped[ChangeRequest] = relationship()
