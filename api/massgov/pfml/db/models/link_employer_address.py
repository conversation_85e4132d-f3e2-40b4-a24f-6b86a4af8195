from typing import TYPE_CHECKING
from uuid import UUID

from sqlalchemy import <PERSON><PERSON><PERSON>
from sqlalchemy.dialects.postgresql import UUID as SQL_UUID
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column as Column
from sqlalchemy.orm import relationship

from .address import Address
from .base import Base, TimestampMixin

if TYPE_CHECKING:
    from massgov.pfml.db.models.employees import Employer


class EmployerAddress(Base, TimestampMixin):
    """The physical or mailing address of an employer"""

    __tablename__ = "link_employer_address"

    employer_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("employer.employer_id"),
        primary_key=True,
        unique=True,
        comment="Internal id of employer",
    )
    address_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("address.address_id"),
        primary_key=True,
        unique=True,
        comment="Internal id of address",
    )

    employer: Mapped["Employer"] = relationship("Employer", back_populates="addresses")
    address: Mapped["Address"] = relationship("Address", back_populates="employers")
