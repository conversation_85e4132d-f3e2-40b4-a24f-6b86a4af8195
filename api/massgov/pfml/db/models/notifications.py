"""Grouping for tables related to Notifications"""

#
# A model's ORM representation should always match the database so we can
# properly read and write data. If you make a change, follow the instructions
# in the API README to generate an associated table migration.
#
# Generally, a model factory should be provided in the associated factories.py file.
# This allows us to build mock data and insert them easily in the database for tests
# and seeding.

from sqlalchemy import TIM<PERSON>TAMP, Integer, Text
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.dialects.postgresql import UUID as SQL_UUID
from sqlalchemy.orm import mapped_column as Column

from .base import Base, TimestampMixin, column_doc, uuid_gen


class Notification(Base, TimestampMixin):
    __tablename__ = "notification"
    notification_id = Column(
        SQL_UUID(as_uuid=True),
        primary_key=True,
        default=uuid_gen,
        comment="Internal primary key",
    )
    request_json = Column(
        JSONB,
        nullable=False,
        comment=column_doc(
            "Full notification request sent by FINEOS, including employer FEIN.", pii=True
        ),
    )
    fineos_absence_id = Column(
        Text, index=True, comment="FINEOS absence ID related to the notification"
    )
    last_notified_at = Column(TIMESTAMP(timezone=True), nullable=True)
    trigger = Column(Text, nullable=True)


class LkNotificationMethod(Base):
    __tablename__ = "lk_notification_method"
    notification_method_id = Column(
        Integer, primary_key=True, autoincrement=True, comment="Internal primary key"
    )
    notification_method_description = Column(Text, nullable=False, comment="Notification method")

    def __init__(self, notification_method_id, notification_method_description):
        self.notification_method_id = notification_method_id
        self.notification_method_description = notification_method_description
