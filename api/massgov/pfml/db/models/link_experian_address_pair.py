from typing import Optional
from uuid import UUID

from sqlalchemy import <PERSON><PERSON><PERSON>
from sqlalchemy.dialects.postgresql import UUID as SQL_UUID
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column as Column
from sqlalchemy.orm import relationship

from .address import Address
from .base import Base, TimestampMixin


class ExperianAddressPair(Base, TimestampMixin):
    __tablename__ = "link_experian_address_pair"
    fineos_address_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True), ForeignKey("address.address_id"), primary_key=True, unique=True
    )
    experian_address_id = Column(
        SQL_UUID(as_uuid=True), ForeignKey("address.address_id"), nullable=True, index=True
    )

    fineos_address: Mapped["Address"] = relationship("Address", foreign_keys=fineos_address_id)
    experian_address: Mapped[Optional["Address"]] = relationship(
        "Address", foreign_keys=experian_address_id
    )
