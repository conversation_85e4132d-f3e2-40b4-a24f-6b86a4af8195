from sqlalchemy import <PERSON><PERSON><PERSON><PERSON><PERSON>, Foreign<PERSON>ey, String, Text
from sqlalchemy.dialects.postgresql import UUID as SQL_UUID
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column as Column
from sqlalchemy.orm import relationship

from .base import Base, TimestampMixin, utc_timestamp_expiration_for_live_chat, uuid_gen
from .employees import User


class OAuthServerCode(Base, TimestampMixin):
    __tablename__ = "oauth_server_code"

    oauth_server_code_id = Column(
        SQL_UUID(as_uuid=True),
        primary_key=True,
        default=uuid_gen,
    )
    user_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("user.user_id"),
        default=uuid_gen,
    )
    authz_code = Column(
        Text,
        nullable=False,
    )
    expires_at = Column(
        TIMESTAMP(timezone=True),
        default=utc_timestamp_expiration_for_live_chat,
        nullable=False,
    )
    user: Mapped["User"] = relationship(User)


class OAuthClientCredentials(Base, TimestampMixin):
    __tablename__ = "oauth_client_credentials"

    oauth_client_credentials_id = Column(SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen)
    client_id = Column(String(64), nullable=False, unique=True, index=True)
    client_secret = Column(String(255), nullable=False)
