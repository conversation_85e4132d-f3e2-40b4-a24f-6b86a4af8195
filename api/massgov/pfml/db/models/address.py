from typing import TYPE_CHECKING, Optional
from uuid import UUID

from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, Text
from sqlalchemy.dialects.postgresql import UUID as SQL_UUID
from sqlalchemy.orm import DynamicMapped, Mapped
from sqlalchemy.orm import mapped_column as Column
from sqlalchemy.orm import relationship

from .base import Base, TimestampMixin, column_doc, uuid_gen
from .geo import LkCountry, LkGeoState
from .lk_address_type import LkAddressType

if TYPE_CHECKING:
    from massgov.pfml.db.models.link_employee_address import EmployeeAddress
    from massgov.pfml.db.models.link_employer_address import EmployerAddress
    from massgov.pfml.db.models.link_healthcare_provider_address import HealthCareProviderAddress


class Address(Base, TimestampMixin):
    """A physical or mailing address"""

    __tablename__ = "address"

    address_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        primary_key=True,
        default=uuid_gen,
        comment="Unique primary key id",
    )
    address_type_id = Column(
        Integer,
        ForeignKey("lk_address_type.address_type_id"),
        comment='id corresponding to an AddressType enum, eg 1 ("Home")',
    )
    address_line_one = Column(
        Text,
        comment=column_doc("The address line 1", pii=True),
    )
    address_line_two = Column(
        Text,
        comment=column_doc("The address line 2", pii=True),
    )
    city = Column(
        Text,
        comment="The city of associated address",
    )
    geo_state_id = Column(
        Integer,
        ForeignKey("lk_geo_state.geo_state_id"),
        comment='id corresponding to an GeoState enum, eg 1 ("MA")',
    )
    geo_state_text = Column(
        Text,
        comment="Text name of state associated with address",
    )
    zip_code = Column(
        Text,
        comment="Zip code of associated address",
    )
    country_id = Column(
        Integer,
        ForeignKey("lk_country.country_id"),
        comment='id corresponding to an Country enum, eg 232 ("USA")',
    )

    address_type: Mapped[Optional[LkAddressType]] = relationship(LkAddressType)
    geo_state: Mapped[Optional[LkGeoState]] = relationship(LkGeoState)
    country: Mapped[Optional[LkCountry]] = relationship(LkCountry)
    employees: DynamicMapped["EmployeeAddress"] = relationship(
        "EmployeeAddress", back_populates="address"
    )
    employers: DynamicMapped["EmployerAddress"] = relationship(
        "EmployerAddress", back_populates="address"
    )
    health_care_providers: DynamicMapped["HealthCareProviderAddress"] = relationship(
        "HealthCareProviderAddress", back_populates="address"
    )
