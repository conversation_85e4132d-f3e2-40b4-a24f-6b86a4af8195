from typing import TYPE_CHECKING
from uuid import UUID

from sqlalchemy import T<PERSON><PERSON><PERSON><PERSON>, ForeignKey
from sqlalchemy.dialects.postgresql import UUID as SQL_UUID
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column as Column
from sqlalchemy.orm import relationship

from .base import Base, TimestampMixin, utc_timestamp_gen
from .link_experian_address_pair import ExperianAddressPair

if TYPE_CHECKING:
    from massgov.pfml.db.models.employees import Employee


class ClaimantAddress(Base, TimestampMixin):
    """The residential and mailing address of a claimant"""

    __tablename__ = "link_claimant_address"

    employee_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("employee.employee_id"),
        primary_key=True,
        comment="Internal id of employee associated with employee's address",
    )
    residential_address_id: Mapped[UUID | None] = Column(
        SQL_UUID(as_uuid=True),
        <PERSON><PERSON><PERSON>("link_experian_address_pair.fineos_address_id"),
        comment="Internal id of address associated with employee's residential address",
    )
    residential_effective_from = Column(
        TIMESTAMP(timezone=True),
        default=utc_timestamp_gen,
        comment="effective date of mailing address",
    )
    mailing_address_id: Mapped[UUID | None] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("link_experian_address_pair.fineos_address_id"),
        comment="Internal id of address associated with employee's mailing address",
    )
    mailing_effective_from = Column(
        TIMESTAMP(timezone=True),
        default=utc_timestamp_gen,
        comment="effective date of mailing address",
    )

    employee: Mapped["Employee"] = relationship("Employee", back_populates="claimant_addresses")
    residential_address: Mapped["ExperianAddressPair"] = relationship(
        "ExperianAddressPair", foreign_keys=residential_address_id
    )
    mailing_address: Mapped["ExperianAddressPair"] = relationship(
        "ExperianAddressPair", foreign_keys=mailing_address_id
    )
