from typing import TYPE_CHECKING

from sqlalchemy import Foreign<PERSON>ey
from sqlalchemy.dialects.postgresql import UUID as SQL_UUID
from sqlalchemy.orm import Mapped
from sqlalchemy.orm import mapped_column as Column
from sqlalchemy.orm import relationship

from .address import Address
from .base import Base, TimestampMixin

if TYPE_CHECKING:
    from massgov.pfml.db.models.employees import HealthCareProvider


class HealthCareProviderAddress(Base, TimestampMixin):
    """Physical or mailing address of a healthcare provider"""

    __tablename__ = "link_health_care_provider_address"

    health_care_provider_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("health_care_provider.health_care_provider_id"),
        primary_key=True,
        comment="Internal id of health care provider associated with address",
    )
    address_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("address.address_id"),
        primary_key=True,
        comment="Internal id of address",
    )

    health_care_provider: Mapped["HealthCareProvider"] = relationship(
        "HealthCareProvider", back_populates="addresses"
    )
    address: Mapped["Address"] = relationship("Address", back_populates="health_care_providers")
