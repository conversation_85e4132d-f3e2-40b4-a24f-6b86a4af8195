from decimal import Decimal
from itertools import chain
from typing import TYPE_CHECKING, Dict, List, Optional, Union
from uuid import UUID

from sqlalchemy import TIMESTAMP, <PERSON><PERSON><PERSON>, Date, ForeignKey, Integer, Numeric, Text, case
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.dialects.postgresql import UUID as SQL_UUID
from sqlalchemy.ext.hybrid import hybrid_property
from sqlalchemy.orm import Mapped, backref
from sqlalchemy.orm import mapped_column as Column
from sqlalchemy.orm import relationship

import massgov.pfml.util.datetime as datetime_util
import massgov.pfml.util.logging
from massgov.pfml.db.models.employees import (
    Address,
    <PERSON>laim,
    Employee,
    Employer,
    LkBankAccountType,
    LkGender,
    LkOccupation,
    LkPaymentMethod,
    TaxIdentifier,
    User,
)
from massgov.pfml.db.models.notifications import LkNotificationMethod
from massgov.pfml.db.models.organization_unit import OrganizationUnit
from massgov.pfml.db.models.phone import Phone

from .base import Base, TableStatus, TimestampMixin, column_doc, deprecated_column, uuid_gen
from .language import LkLanguage

if TYPE_CHECKING:
    from massgov.pfml.db.models.appeal import Appeal
    from massgov.pfml.db.models.documents import Document

logger = massgov.pfml.util.logging.get_logger(__name__)


class NoClaimTypeForAbsenceType(Exception):
    pass


class LkEmploymentStatus(Base):
    # Descriptions in this table map to Fineos Enum domain #175
    __tablename__ = "lk_employment_status"
    employment_status_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    employment_status_description: Mapped[str] = Column(Text, nullable=False)
    fineos_label = Column(Text)

    def __init__(self, employment_status_id, employment_status_description, fineos_label):
        self.employment_status_id = employment_status_id
        self.employment_status_description = employment_status_description
        self.fineos_label = fineos_label


class LkLeaveReason(Base):
    __tablename__ = "lk_leave_reason"
    leave_reason_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    leave_reason_description: Mapped[str] = Column(Text, nullable=False)
    _map: Dict[int, int] | None = None

    __allow_unmapped__ = True

    def __init__(self, leave_reason_id, leave_reason_description):
        self.leave_reason_id = leave_reason_id
        self.leave_reason_description = leave_reason_description

    @classmethod
    def generate_map(cls) -> Dict[int, int]:
        from massgov.pfml.db.lookup_data.applications import LeaveReason
        from massgov.pfml.db.lookup_data.employees import ClaimType

        return {
            LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id: ClaimType.FAMILY_LEAVE.claim_type_id,
            LeaveReason.PREGNANCY_MATERNITY.leave_reason_id: ClaimType.MEDICAL_LEAVE.claim_type_id,
            LeaveReason.CHILD_BONDING.leave_reason_id: ClaimType.FAMILY_LEAVE.claim_type_id,
            LeaveReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.leave_reason_id: ClaimType.MEDICAL_LEAVE.claim_type_id,
            LeaveReason.MILITARY_CAREGIVER.leave_reason_id: ClaimType.MILITARY_LEAVE.claim_type_id,
            LeaveReason.MILITARY_EXIGENCY_FAMILY.leave_reason_id: ClaimType.MILITARY_LEAVE.claim_type_id,
        }

    @property
    def absence_to_claim_type(self) -> int:
        if not self._map:
            self._map = self.generate_map()
        if self.leave_reason_id not in self._map:
            raise NoClaimTypeForAbsenceType(f"{self.leave_reason_id} not in the lookup table")
        return self._map[self.leave_reason_id]


class LkLeaveReasonQualifier(Base):
    __tablename__ = "lk_leave_reason_qualifier"
    leave_reason_qualifier_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    leave_reason_qualifier_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, leave_reason_qualifier_id, leave_reason_qualifier_description):
        self.leave_reason_qualifier_id = leave_reason_qualifier_id
        self.leave_reason_qualifier_description = leave_reason_qualifier_description


class LkRelationshipToCaregiver(Base):
    __tablename__ = "lk_relationship_to_caregiver"
    relationship_to_caregiver_id: Mapped[int] = Column(
        Integer, primary_key=True, autoincrement=True
    )
    relationship_to_caregiver_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, relationship_to_caregiver_id, relationship_to_caregiver_description):
        self.relationship_to_caregiver_id = relationship_to_caregiver_id
        self.relationship_to_caregiver_description = relationship_to_caregiver_description


class LkRelationshipQualifier(Base):
    __tablename__ = "lk_relationship_qualifier"
    relationship_qualifier_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    relationship_qualifier_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, relationship_qualifier_id, relationship_qualifier_description):
        self.relationship_qualifier_id = relationship_qualifier_id
        self.relationship_qualifier_description = relationship_qualifier_description


class LkFrequencyOrDuration(Base):
    __tablename__ = "lk_frequency_or_duration"
    frequency_or_duration_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    frequency_or_duration_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, frequency_or_duration_id, frequency_or_duration_description):
        self.frequency_or_duration_id = frequency_or_duration_id
        self.frequency_or_duration_description = frequency_or_duration_description


class LkWorkPatternType(Base):
    # Descriptions in this table map to Fineos Enum domain #277
    __tablename__ = "lk_work_pattern_type"
    work_pattern_type_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    work_pattern_type_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, work_pattern_type_id, work_pattern_type_description):
        self.work_pattern_type_id = work_pattern_type_id
        self.work_pattern_type_description = work_pattern_type_description


class LkDayOfWeek(Base):
    __tablename__ = "lk_day_of_week"
    day_of_week_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    day_of_week_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, day_of_week_id, day_of_week_description):
        self.day_of_week_id = day_of_week_id
        self.day_of_week_description = day_of_week_description


class LkAmountFrequency(Base):
    __tablename__ = "lk_amount_frequency"
    amount_frequency_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    amount_frequency_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, amount_frequency_id, amount_frequency_description):
        self.amount_frequency_id = amount_frequency_id
        self.amount_frequency_description = amount_frequency_description


class LkEmployerBenefitType(Base):
    __tablename__ = "lk_employer_benefit_type"
    employer_benefit_type_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    employer_benefit_type_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, employer_benefit_type_id, employer_benefit_type_description):
        self.employer_benefit_type_id = employer_benefit_type_id
        self.employer_benefit_type_description = employer_benefit_type_description


class LkOtherIncomeType(Base):
    __tablename__ = "lk_other_income_type"
    other_income_type_id: Mapped[int] = Column(Integer, primary_key=True, autoincrement=True)
    other_income_type_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, other_income_type_id, other_income_type_description):
        self.other_income_type_id = other_income_type_id
        self.other_income_type_description = other_income_type_description


class LkPreviousLeaveQualifyingReason(Base):
    __tablename__ = "lk_previous_leave_qualifying_reason"
    previous_leave_qualifying_reason_id: Mapped[int] = Column(
        Integer, primary_key=True, autoincrement=True
    )
    previous_leave_qualifying_reason_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(
        self, previous_leave_qualifying_reason_id, previous_leave_qualifying_reason_description
    ):
        self.previous_leave_qualifying_reason_id = previous_leave_qualifying_reason_id
        self.previous_leave_qualifying_reason_description = (
            previous_leave_qualifying_reason_description
        )


class ApplicationUserNotFoundInfo(Base, TimestampMixin):
    """Information for processing a User Not Found issue on an application"""

    __tablename__ = "application_user_not_found_info"

    application_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("application.application_id"),
        primary_key=True,
        nullable=False,
        comment="Internal id for the associated Application record",
    )
    currently_employed = Column(
        Boolean, comment="Whether or not the claimant is currently employed"
    )
    date_of_hire = Column(Date, comment="The claimant's date of hire at their employer")
    date_of_separation = Column(
        Date, comment="The claimant's date of separation from their employer"
    )
    employer_name = Column(Text, comment="The name of the claimant's employer")
    recently_acquired_or_merged = Column(
        Boolean, comment="Whether or not the claimant's employer was recently acquired or merged"
    )
    submitted_time = Column(
        TIMESTAMP(timezone=True),
        comment="Timestamp when the additional User Not Found info was submitted to FINEOS",
    )

    application: Mapped["Application"] = relationship(
        "Application", back_populates="additional_user_not_found_info"
    )


class LkEthnicity(Base):
    __tablename__ = "lk_ethnicity"
    ethnicity_id: Mapped[int] = Column(Integer, primary_key=True)
    ethnicity_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, ethnicity_id, ethnicity_description):
        self.ethnicity_id = ethnicity_id
        self.ethnicity_description = ethnicity_description

    @property
    def id(self) -> int:
        return self.ethnicity_id


class LkRace(Base):
    __tablename__ = "lk_race"
    race_id: Mapped[int] = Column(Integer, primary_key=True)
    race_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, race_id, race_description):
        self.race_id = race_id
        self.race_description = race_description

    @property
    def id(self) -> int:
        return self.race_id


class LkMmgIdvStatus(Base):
    __tablename__ = "lk_mmg_idv_status"
    mmg_idv_status_id: Mapped[int] = Column(Integer, primary_key=True)
    mmg_idv_status_description: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, mmg_idv_status_id, mmg_idv_status_description):
        self.mmg_idv_status_id = mmg_idv_status_id
        self.mmg_idv_status_description = mmg_idv_status_description

    @property
    def id(self) -> int:
        return self.mmg_idv_status_id


class LkIndustrySector(Base):
    __tablename__ = "lk_industry_sector"
    industry_sector_id: Mapped[int] = Column(Integer, primary_key=True)
    industry_sector_description: Mapped[str] = Column(Text, nullable=False)
    industry_sector_code: Mapped[str] = Column(Text, nullable=False)

    def __init__(self, industry_sector_id, industry_sector_description, industry_sector_code):
        self.industry_sector_id = industry_sector_id
        self.industry_sector_description = industry_sector_description
        self.industry_sector_code = industry_sector_code

    @property
    def id(self) -> int:
        return self.industry_sector_id

    @property
    def description(self) -> str:
        return self.industry_sector_description


class ConcurrentLeave(Base, TimestampMixin):
    """A claimant's employer-sponsored paid leave that is concurrent with the associated application"""

    __tablename__ = "concurrent_leave"
    __table_status__ = TableStatus.OBSOLETE

    concurrent_leave_id: Mapped[UUID] = deprecated_column(
        SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen, comment="Unique primary key id"
    )
    application_id: Mapped[UUID] = deprecated_column(
        SQL_UUID(as_uuid=True),
        ForeignKey("application.application_id"),
        index=True,
        nullable=False,
        comment="Internal id for the associated Application record",
    )
    is_for_current_employer: Mapped[bool | None] = deprecated_column(
        Boolean, comment="Whether or not the concurrent leave is for the current employer"
    )
    leave_start_date: Mapped[Date | None] = deprecated_column(
        Date, comment="The start date of the leave"
    )
    leave_end_date: Mapped[Date | None] = deprecated_column(
        Date, comment="The end date of the leave"
    )


class PreviousLeave(Base, TimestampMixin):
    """A claimant's previous paid leave"""

    # Caution: records of this model get recreated frequently as part of the PATCH /applications/:id endpoint.
    # Only the Application model should hold foreign keys to these records to avoid referenced objects being unexpectedly deleted.
    __tablename__ = "previous_leave"
    previous_leave_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen, comment="Unique primary key id"
    )
    application_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("application.application_id"),
        index=True,
        nullable=False,
        comment="Internal id for the associated Application record",
    )
    leave_start_date = Column(Date, comment="The start date of the leave")
    leave_end_date = Column(Date, comment="The end date of the leave")
    is_for_current_employer = Column(
        Boolean, comment="Whether or not the previous leave was for the current employer"
    )
    is_continuous = Column(
        Boolean,
        comment="Whether or not the claimant was off work from the start to the end of this previous leave",
    )
    leave_reason_id = Column(
        Integer,
        ForeignKey("lk_previous_leave_qualifying_reason.previous_leave_qualifying_reason_id"),
        comment='id corresponding to a LeaveReason enum, eg 1 ("Care for a Family Member")',
    )
    worked_per_week_minutes = Column(
        Integer, comment="The amount of time worked per week, in minutes"
    )
    leave_minutes = Column(Integer, comment="Total amount of leave taken, in minutes")
    type = Column(
        Text,
        comment="The reason for the previous leave; either same as current leave ('same_reason') or another reason ('other_reason')",
    )

    application: Mapped["Application"] = relationship(
        "Application", back_populates="previous_leaves"
    )
    leave_reason: Mapped[Optional[LkPreviousLeaveQualifyingReason]] = relationship(
        LkPreviousLeaveQualifyingReason
    )


class Application(Base, TimestampMixin):
    """An application for Massachusetts Paid Family & Medical Leave"""

    __tablename__ = "application"

    application_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        primary_key=True,
        default=uuid_gen,
        comment="Unique primary key id",
    )
    user_id: Mapped[UUID] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("user.user_id"),
        nullable=False,
        index=True,
        comment="Internal id for the associated User record",
    )
    organization_unit_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("organization_unit.organization_unit_id"),
        nullable=True,
        comment="Internal id for the associated OrganizationUnit record",
    )
    tax_identifier_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("tax_identifier.tax_identifier_id"),
        index=True,
        comment="Internal id for the associated TaxIdentifier record",
    )
    requestor = Column(Integer)
    claim_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("claim.claim_id"),
        nullable=True,
        unique=True,
        comment="Internal id for the associated Claim record",
    )
    has_mailing_address = Column(
        Boolean,
        comment="Whether or not the claimant has a mailing address separate from their residential address",
    )
    mailing_address_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("address.address_id"),
        nullable=True,
        comment="Internal id for the associated mailing Address record",
    )
    residential_address_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("address.address_id"),
        nullable=True,
        comment="Internal id for the associated residential Address record",
    )
    phone_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey(Phone.phone_id),
        nullable=True,
        comment="Internal id for the associated Phone record",
    )
    phone: Mapped[Phone] = relationship(back_populates="application")
    employer_fein = Column(
        Text,
        comment=column_doc(
            "Federal Employer Identification Number (FEIN) of the claimant's employer", pii=True
        ),
    )
    first_name = Column(Text, comment=column_doc("The claimant's first name", pii=True))
    last_name = Column(Text, comment=column_doc("The claimant's last name", pii=True))
    middle_name = Column(Text, comment=column_doc("The claimant's middle name", pii=True))
    date_of_birth = Column(Date, comment=column_doc("The claimant's date of birth", pii=True))
    has_continuous_leave_periods = Column(
        Boolean,
        comment="Whether or not the claimant indicates they have any continuous leave periods to report",
    )
    has_intermittent_leave_periods = Column(
        Boolean,
        comment="Whether or not the claimant indicates they have any intermittent leave periods to report",
    )
    has_reduced_schedule_leave_periods = Column(
        Boolean,
        comment="Whether or not the claimant indicates they have any reduced schedule leave periods to report",
    )
    has_state_id = Column(
        Boolean, comment="Whether or not the claimant has a Massachusetts state-issued ID"
    )
    mass_id = Column(
        Text, comment=column_doc("The claimant's Massachusetts state-issued ID number", pii=True)
    )
    occupation_id = Column(
        Integer,
        ForeignKey("lk_occupation.occupation_id"),
        comment='id corresponding to an Occupation enum, eg 1 ("Health Care")',
    )
    organization_unit_selection = Column(
        Text,
        comment='The claimant\'s reason for not selecting an OrganizationUnit, eg "not_listed"',
    )
    gender_id = Column(
        Integer,
        ForeignKey("lk_gender.gender_id"),
        comment='id corresponding to a Gender enum, eg 1 ("Woman")',
    )
    language_id = Column(
        Integer,
        ForeignKey("lk_language.language_id"),
        nullable=True,
        comment='id corresponding to a Language enum, eg 1 ("English")',
    )
    race_id = Column(
        Integer,
        ForeignKey("lk_race.race_id"),
        comment='id corresponding to a Race enum, eg 2 ("American Indian/Alaska Native")',
    )
    race_custom = Column(Text, comment="Optional description of race")
    ethnicity_id = Column(
        Integer,
        ForeignKey("lk_ethnicity.ethnicity_id"),
        comment='id corresponding to an Ethnicity enum, eg 2 ("Hispanic or Latino")',
    )
    hours_worked_per_week: Mapped[Optional[Decimal]] = Column(
        Numeric, comment="Number of hours worked per week"
    )
    hours_worked_per_week_all_employers: Mapped[Optional[Decimal]] = Column(
        Numeric, comment="Number of hours worked per week across all employers"
    )
    relationship_to_caregiver_id = Column(
        Integer,
        ForeignKey("lk_relationship_to_caregiver.relationship_to_caregiver_id"),
        comment='id corresponding to a RelationshipToCaregiver enum, eg 1 ("Parent")',
    )
    relationship_qualifier_id = Column(
        Integer,
        ForeignKey("lk_relationship_qualifier.relationship_qualifier_id"),
        comment='id corresponding to a RelationshipQualifier enum, eg 1 ("Adopted")',
    )
    pregnant_or_recent_birth = Column(
        Boolean, comment="Whether or not the claimant is pregnant or has had a recent birth"
    )
    child_birth_date = Column(Date, comment=column_doc("The date of birth of the child", pii=True))
    child_placement_date = Column(
        Date,
        comment=column_doc(
            "The date that the child was placed into the home after adoption", pii=True
        ),
    )
    has_future_child_date = Column(
        Boolean,
        comment="Whether or not the birth or placement dates are estimated dates in the future",
    )
    employer_notified = Column(
        Boolean, comment="Whether or not the employer has been notified of the leave"
    )
    employer_notification_date = Column(Date, comment="The date the employer was notified of leave")
    employer_notification_method_id = Column(
        Integer,
        ForeignKey("lk_notification_method.notification_method_id"),
        comment='id corresponding to a NotificationMethod enum, eg 1 ("In Writing")',
    )
    leave_reason_id = Column(
        Integer,
        ForeignKey("lk_leave_reason.leave_reason_id"),
        comment='id corresponding to a LeaveReason enum, eg 1 ("Care for a Family Member")',
    )
    leave_reason_qualifier_id = Column(
        Integer,
        ForeignKey("lk_leave_reason_qualifier.leave_reason_qualifier_id"),
        comment='id corresponding to a LeaveReasonQualifier enum, eg 1 ("Newborn")',
    )
    employment_status_id = Column(
        Integer,
        ForeignKey("lk_employment_status.employment_status_id"),
        comment='id corresponding to an EmploymentStatus enum, eg 1 ("Employed")',
    )
    work_pattern_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("work_pattern.work_pattern_id"),
        comment="Internal id for the associated WorkPattern record",
    )
    payment_preference_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("application_payment_preference.payment_pref_id"),
        comment="Internal id for the associated PaymentPreference record",
    )
    imported_from_fineos_at = Column(
        TIMESTAMP(timezone=True), comment="Date when application was imported from FINEOS"
    )
    ready_for_review_time = Column(
        TIMESTAMP(timezone=True),
        comment="Date when the application was completed, had all required materials, and was ready for review",
    )
    completed_time = Column(
        TIMESTAMP(timezone=True), comment="Date when the application was completed"
    )
    submitted_time = Column(
        TIMESTAMP(timezone=True), comment="Date when the application was submitted to FINEOS"
    )
    has_employer_benefits = Column(
        Boolean,
        comment="Whether or not the claimant will receive employer-sponsored benefits during their leave",
    )
    has_other_incomes = Column(
        Boolean,
        comment="Whether or not the claimant will receive income from other sources during their leave",
    )
    has_submitted_payment_preference = Column(
        Boolean, comment="Whether or not the claimant has submitted their payment preference"
    )
    caring_leave_metadata_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("caring_leave_metadata.caring_leave_metadata_id"),
        comment="Internal id for the associated CaringLeaveMetadata record",
    )
    has_previous_leaves: Mapped[bool | None] = Column(
        Boolean,
        comment="Whether or not the claimant indicates they have any previous leaves to report",
    )
    has_previous_leaves_same_reason: Mapped[bool | None] = deprecated_column(
        Boolean,
        comment="Whether or not the claimant has taken other paid leaves for the same qualifying reason",
    )
    has_previous_leaves_other_reason: Mapped[bool | None] = deprecated_column(
        Boolean,
        comment="Whether or not the claimant has taken other paid leaves for any other reason",
    )
    has_concurrent_leave: Mapped[bool | None] = deprecated_column(
        Boolean,
        comment="Whether or not the claimant will have other employer-sponsored paid time off during their leave",
    )
    has_concurrent_employers = Column(
        Boolean, comment="Whether or not the claimant has concurrent employers", nullable=True
    )
    is_withholding_tax = Column(
        Boolean,
        nullable=True,
        comment="Whether or not the claimant wishes to withhold state and federal taxes from their payments",
    )

    split_from_application_id: Mapped[Optional[UUID]] = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("application.application_id"),
        nullable=True,
        index=True,
        comment="Internal id for the associated Application record that this was split from",
    )

    nbr_of_retries: Mapped[int] = Column(
        Integer,
        nullable=False,
        default=0,
        comment="The number of times the claimant has attempted to change their FEIN or SSN",
    )

    fields_to_use_from_user_profile = Column(
        JSONB,
        nullable=True,
    )

    mmg_idv_status_id = Column(
        Integer,
        ForeignKey("lk_mmg_idv_status.mmg_idv_status_id"),
        comment="ID corresponding to an MMG IDV Status enum",
    )

    industry_sector_id = Column(
        Integer,
        ForeignKey("lk_industry_sector.industry_sector_id"),
        comment="ID corresponding to an industry sector enum",
    )

    user: Mapped["User"] = relationship(User)
    caring_leave_metadata: Mapped[Optional["CaringLeaveMetadata"]] = relationship(
        "CaringLeaveMetadata",
        single_parent=True,
        back_populates="application",
        cascade="all,delete-orphan",
    )
    claim: Mapped[Optional[Claim]] = relationship(Claim, back_populates="application")
    occupation: Mapped[Optional[LkOccupation]] = relationship(LkOccupation)
    organization_unit: Mapped[Optional[OrganizationUnit]] = relationship(OrganizationUnit)
    gender: Mapped[Optional[LkGender]] = relationship(LkGender)
    language: Mapped[Optional[LkLanguage]] = relationship(LkLanguage)
    ethnicity: Mapped[Optional[LkEthnicity]] = relationship(LkEthnicity)
    race: Mapped[Optional[LkRace]] = relationship(LkRace)
    leave_reason: Mapped[Optional[LkLeaveReason]] = relationship(LkLeaveReason)
    leave_reason_qualifier: Mapped[Optional[LkLeaveReasonQualifier]] = relationship(
        LkLeaveReasonQualifier
    )
    employment_status: Mapped[Optional[LkEmploymentStatus]] = relationship(LkEmploymentStatus)
    relationship_to_caregiver: Mapped[Optional[LkRelationshipToCaregiver]] = relationship(
        LkRelationshipToCaregiver
    )
    relationship_qualifier: Mapped[Optional[LkRelationshipQualifier]] = relationship(
        LkRelationshipQualifier
    )
    employer_notification_method: Mapped[Optional[LkNotificationMethod]] = relationship(
        LkNotificationMethod
    )
    tax_identifier: Mapped[Optional[TaxIdentifier]] = relationship(TaxIdentifier)
    mailing_address: Mapped[Optional[Address]] = relationship(
        Address, foreign_keys=[mailing_address_id]
    )
    residential_address: Mapped[Optional[Address]] = relationship(
        Address, foreign_keys=[residential_address_id]
    )
    payment_preference: Mapped[Optional["ApplicationPaymentPreference"]] = relationship(
        "ApplicationPaymentPreference", back_populates="application"
    )
    work_pattern: Mapped[Optional["WorkPattern"]] = relationship(
        "WorkPattern", back_populates="applications", uselist=False
    )
    split_from_application: Mapped[Optional["Application"]] = relationship(
        "Application",
        backref=backref("split_into_application", uselist=False),
        remote_side=[application_id],
    )

    continuous_leave_periods: Mapped[List["ContinuousLeavePeriod"]] = relationship(
        "ContinuousLeavePeriod",
        back_populates="application",
        uselist=True,
        cascade="all, delete-orphan",
    )
    intermittent_leave_periods: Mapped[List["IntermittentLeavePeriod"]] = relationship(
        "IntermittentLeavePeriod",
        back_populates="application",
        uselist=True,
        cascade="all, delete-orphan",
    )
    reduced_schedule_leave_periods: Mapped[List["ReducedScheduleLeavePeriod"]] = relationship(
        "ReducedScheduleLeavePeriod",
        back_populates="application",
        uselist=True,
        cascade="all, delete-orphan",
    )
    employer_benefits: Mapped[List["EmployerBenefit"]] = relationship(
        "EmployerBenefit", back_populates="application", uselist=True
    )
    other_incomes: Mapped[List["OtherIncome"]] = relationship(
        "OtherIncome", back_populates="application", uselist=True
    )
    previous_leaves: Mapped[List["PreviousLeave"]] = relationship(
        "PreviousLeave", back_populates="application", uselist=True
    )
    additional_user_not_found_info: Mapped["ApplicationUserNotFoundInfo"] = relationship(
        "ApplicationUserNotFoundInfo", back_populates="application", uselist=False
    )
    documents: Mapped[list["Document"]] = relationship(back_populates="application")

    employee: Mapped[Optional[Employee]] = relationship(
        Employee, secondary=TaxIdentifier.__table__, uselist=False, viewonly=True
    )
    employer: Mapped[Optional[Employer]] = relationship(
        Employer,
        primaryjoin="foreign(Application.employer_fein) == remote(Employer.employer_fein)",
        uselist=False,
        viewonly=True,
    )

    appeal: Mapped[Optional["Appeal"]] = relationship(
        "Appeal",
        back_populates="application",
        secondary=Claim.__table__,
        uselist=False,
        viewonly=True,
    )

    mmg_idv_status: Mapped[Optional[LkMmgIdvStatus]] = relationship(LkMmgIdvStatus)

    industry_sector: Mapped[Optional[LkIndustrySector]] = relationship(LkIndustrySector)

    @property
    def employee_organization_units(self) -> list[OrganizationUnit]:
        if not self.employee or not self.employer:
            return []
        units = self.employee.get_organization_units(self.employer)
        logger.info(
            "Application found Employee's organization units",
            extra={
                "employer.employer_id": self.employer.employer_id,
                "employee.organization_unit_ids": ",".join(
                    str(r.organization_unit_id) for r in units
                ),
            },
        )
        return units

    @property
    def employer_organization_units(self) -> list[OrganizationUnit]:
        if not self.employer:
            return []
        units = self.employer.organization_units
        logger.info(
            "Application found Employer's organization units",
            extra={
                "employer.employer_id": self.employer.employer_id,
                "employer.organization_unit_ids": ",".join(
                    str(r.organization_unit_id) for r in units
                ),
            },
        )
        return units

    @property
    def all_leave_periods(
        self,
    ) -> list[
        Union["ContinuousLeavePeriod", "ReducedScheduleLeavePeriod", "IntermittentLeavePeriod"]
    ]:
        leave_periods = list(
            chain(
                self.continuous_leave_periods,
                self.intermittent_leave_periods,
                self.reduced_schedule_leave_periods,
            )
        )
        return leave_periods  # type: ignore

    @property
    def split_into_application_id(self):
        return self.split_into_application.application_id if self.split_into_application else None  # type: ignore

    @property
    def fineos_absence_id(self) -> Optional[str]:
        if not self.claim:
            return None

        return self.claim.fineos_absence_id

    @property
    def start_date(self):
        if not self.all_leave_periods:
            return None
        return min([lp.start_date for lp in self.all_leave_periods if lp.start_date])

    @property
    def end_date(self):
        if not self.all_leave_periods:
            return None
        return max([lp.end_date for lp in self.all_leave_periods if lp.end_date])

    @property
    def employer_fein_for_fineos(self) -> Optional[str]:
        if self.claim and self.claim.employer and self.claim.employer.is_dummy:
            return self.claim.employer.employer_fein

        return self.employer_fein

    @property
    def employee_id(self) -> Optional[UUID]:
        if not self.employee:
            return None
        return self.employee.employee_id

    @property
    def employer_id(self) -> Optional[UUID]:
        if not self.employer:
            return None
        return self.employer.employer_id

    def mark_complete(self, completed_time: Optional[datetime_util.datetime] = None) -> None:
        """
        Shared function to standardize how we mark applications "completed".
        Note that Application does not have a status.
        ApplicationResponse has a status, and this is set to "Completed" by the from_orm method based on application.completed_time.
        """
        if not completed_time:
            completed_time = datetime_util.utcnow()
        self.completed_time = completed_time


class CaringLeaveMetadata(Base, TimestampMixin):
    """Caring leave information for an application"""

    __tablename__ = "caring_leave_metadata"
    caring_leave_metadata_id = Column(
        SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen, comment="Unique primary key id"
    )
    family_member_first_name = Column(
        Text, comment=column_doc("The first name of the family member being cared for", pii=True)
    )
    family_member_last_name = Column(
        Text, comment=column_doc("The last name of the family member being cared for", pii=True)
    )
    family_member_middle_name = Column(
        Text, comment=column_doc("The middle name of the family member being cared for", pii=True)
    )
    family_member_date_of_birth = Column(
        Date, comment=column_doc("The date of birth of the family member being cared for", pii=True)
    )
    relationship_to_caregiver_id = Column(
        Integer,
        ForeignKey("lk_relationship_to_caregiver.relationship_to_caregiver_id"),
        comment='id corresponding to a RelationshipToCaregiver enum, eg 1 ("Parent")',
    )

    relationship_to_caregiver: Mapped[Optional[LkRelationshipToCaregiver]] = relationship(
        LkRelationshipToCaregiver
    )
    application: Mapped[Optional[Application]] = relationship(
        "Application", back_populates="caring_leave_metadata", uselist=False
    )


class ApplicationPaymentPreference(Base, TimestampMixin):
    """Payment preference information for an application"""

    __tablename__ = "application_payment_preference"
    payment_pref_id = Column(
        SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen, comment="Unique primary key id"
    )
    payment_method_id = Column(
        Integer,
        ForeignKey("lk_payment_method.payment_method_id"),
        comment='id corresponding to a PaymentMethod enum, eg 2 ("Check")',
    )
    account_number = Column(Text, comment=column_doc("The bank account number", pii=True))
    routing_number = Column(Text, comment=column_doc("The bank routing number", pii=True))
    bank_account_type_id = Column(
        Integer,
        ForeignKey("lk_bank_account_type.bank_account_type_id"),
        nullable=True,
        comment='id corresponding to a BankAccountType enum, eg 2 ("Checking")',
    )

    application: Mapped[Optional[Application]] = relationship(
        "Application", back_populates="payment_preference", uselist=False
    )
    payment_method: Mapped[Optional[LkPaymentMethod]] = relationship(LkPaymentMethod)
    bank_account_type: Mapped[Optional[LkBankAccountType]] = relationship(LkBankAccountType)


class ContinuousLeavePeriod(Base, TimestampMixin):
    """A request for continuous leave"""

    __tablename__ = "continuous_leave_period"
    leave_period_id = Column(
        SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen, comment="Unique primary key id"
    )
    application_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("application.application_id"),
        index=True,
        comment="Internal id for the associated Application record",
    )
    start_date = Column(Date, comment="The start date of this leave period")
    end_date = Column(Date, comment="The end date of this leave period")
    is_estimated = Column(Boolean, default=True, nullable=False)
    last_day_worked = Column(Date, comment="THe last date on which the claimant worked")
    expected_return_to_work_date = Column(
        Date, comment="The date on which the claimant expects to return to work"
    )
    start_date_full_day = Column(Boolean)
    start_date_off_hours = Column(Integer)
    start_date_off_minutes = Column(Integer)
    end_date_full_day = Column(Boolean)
    end_date_off_hours = Column(Integer)
    end_date_off_minutes = Column(Integer)

    application: Mapped[Optional[Application]] = relationship(
        Application, back_populates="continuous_leave_periods"
    )


class IntermittentLeavePeriod(Base, TimestampMixin):
    """A request for intermittent leave"""

    __tablename__ = "intermittent_leave_period"
    leave_period_id = Column(
        SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen, comment="Unique primary key id"
    )
    application_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("application.application_id"),
        index=True,
        comment="Internal id for the associated Application record",
    )
    start_date = Column(Date, comment="The start date of this leave period")
    end_date = Column(Date, comment="The end date of this leave period")
    frequency = Column(Integer, comment="Estimated number of absences per interval")
    frequency_interval = Column(
        Integer, comment="Number of measurement periods in the interval used to calculate frequency"
    )  # typically 1, but we use 6 if the user selects "irregularly over 6 months"
    frequency_interval_basis = Column(
        Text, comment="Units of the measurement period; Days, Weeks, or Months"
    )
    duration = Column(
        Integer,
        comment="The duration of time that an absence typically lasts, in units according to duration_basis",
    )
    duration_basis = Column(
        Text,
        comment="Unit of measurement of the duration of a typical absence; Minutes, Hours, or Days",
    )

    application: Mapped[Optional[Application]] = relationship(
        Application, back_populates="intermittent_leave_periods"
    )


class ReducedScheduleLeavePeriod(Base, TimestampMixin):
    """A request for reduced schedule leave"""

    __tablename__ = "reduced_schedule_leave_period"
    leave_period_id = Column(
        SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen, comment="Unique primary key id"
    )
    application_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("application.application_id"),
        index=True,
        comment="Internal id for the associated Application record",
    )
    start_date = Column(Date, comment="The start date of this leave period")
    end_date = Column(Date, comment="The end date of this leave period")
    is_estimated = Column(Boolean, default=True, nullable=False)
    sunday_off_minutes = Column(
        Integer, comment="The amount of time the claimant has requested off on Sunday, in minutes"
    )
    monday_off_minutes = Column(
        Integer, comment="The amount of time the claimant has requested off on Monday, in minutes"
    )
    tuesday_off_minutes = Column(
        Integer, comment="The amount of time the claimant has requested off on Tuesday, in minutes"
    )
    wednesday_off_minutes = Column(
        Integer,
        comment="The amount of time the claimant has requested off on Wednesday, in minutes",
    )
    thursday_off_minutes = Column(
        Integer,
        comment="The amount of time the claimant has requested off on Thursday, in minutes",
    )
    friday_off_minutes = Column(
        Integer, comment="The amount of time the claimant has requested off on Friday, in minutes"
    )
    saturday_off_minutes = Column(
        Integer,
        comment="The amount of time the claimant has requested off on Saturday, in minutes",
    )

    application: Mapped[Optional[Application]] = relationship(
        Application, back_populates="reduced_schedule_leave_periods"
    )


class EmployerBenefit(Base, TimestampMixin):
    """An employer-sponsored benefit that a claimant will receive during their leave"""

    # Caution: records of this model get recreated frequently as part of the PATCH /applications/:id endpoint.
    # Only the Application model should hold foreign keys to these records to avoid referenced objects being unexpectedly deleted.
    __tablename__ = "employer_benefit"
    employer_benefit_id = Column(
        SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen, comment="Unique primary key id"
    )
    application_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("application.application_id"),
        index=True,
        nullable=False,
        comment="Internal id for the associated Application record",
    )
    # What is the first day of leave from work that this benefit will pay you for?"
    benefit_start_date = Column(Date, comment="The first day of leave that this benefit will apply")
    benefit_end_date = Column(Date, comment="The last day of leave that this benefit will apply")
    benefit_type_id = Column(
        Integer,
        ForeignKey("lk_employer_benefit_type.employer_benefit_type_id"),
        comment='id corresponding to an EmployerBenefitType enum, eg 1 ("Accrued paid leave")',
    )
    benefit_amount_dollars: Mapped[Optional[Decimal]] = Column(
        Numeric(asdecimal=True), comment="The amount of the benefit, in dollars"
    )
    benefit_amount_frequency_id = Column(
        Integer,
        ForeignKey("lk_amount_frequency.amount_frequency_id"),
        comment='id corresponding to an AmountFrequency enum, eg 1 ("Per Day")',
    )
    is_full_salary_continuous = Column(
        Boolean, comment="Whether or not this benefit totals the claimant's regular pay"
    )

    application: Mapped[Application] = relationship(Application, back_populates="employer_benefits")
    benefit_type: Mapped[Optional[LkEmployerBenefitType]] = relationship(LkEmployerBenefitType)
    benefit_amount_frequency: Mapped[Optional[LkAmountFrequency]] = relationship(LkAmountFrequency)


class OtherIncome(Base, TimestampMixin):
    """Other income that a claimant will receive during their leave"""

    # Caution: records of this model get recreated frequently as part of the PATCH /applications/:id endpoint.
    # Only the Application model should hold foreign keys to these records to avoid referenced objects being unexpectedly deleted.
    __tablename__ = "other_income"
    other_income_id = Column(
        SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen, comment="Unique primary key id"
    )
    application_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("application.application_id"),
        index=True,
        nullable=False,
        comment="Internal id for the associated Application record",
    )
    income_start_date = Column(Date, comment="The first day of leave that this income will pay for")
    income_end_date = Column(Date, comment="The last day of leave that this income will pay for")
    income_type_id = Column(
        Integer,
        ForeignKey("lk_other_income_type.other_income_type_id"),
        comment='id corresponding to an OtherIncomeType enum, eg 1 ("Workers Compensation")',
    )
    income_amount_dollars: Mapped[Optional[Decimal]] = Column(
        Numeric(asdecimal=True), comment="The amount of the other income, in dollars"
    )
    income_amount_frequency_id = Column(
        Integer,
        ForeignKey("lk_amount_frequency.amount_frequency_id"),
        comment='id corresponding to an AmountFrequency enum, eg 1 ("Per Day")',
    )

    application: Mapped[Application] = relationship(Application, back_populates="other_incomes")
    income_type: Mapped[Optional[LkOtherIncomeType]] = relationship(LkOtherIncomeType)
    income_amount_frequency: Mapped[Optional[LkAmountFrequency]] = relationship(LkAmountFrequency)


class WorkPattern(Base, TimestampMixin):
    """A typical work schedule for a claimant for a week"""

    __tablename__ = "work_pattern"
    work_pattern_id = Column(
        SQL_UUID(as_uuid=True), primary_key=True, default=uuid_gen, comment="Unique primary key id"
    )
    work_pattern_type_id = Column(
        Integer,
        ForeignKey("lk_work_pattern_type.work_pattern_type_id"),
        comment='id corresponding to a WorkPatternType enum, eg 0 ("Fixed")',
    )

    applications: Mapped[List[Application]] = relationship(
        "Application", back_populates="work_pattern", uselist=True
    )
    work_pattern_type: Mapped[LkWorkPatternType] = relationship(LkWorkPatternType)
    work_pattern_days: Mapped[List["WorkPatternDay"]] = relationship(
        "WorkPatternDay",
        back_populates="work_pattern",
        uselist=True,
        order_by="asc(WorkPatternDay.sort_order)",
    )


class WorkPatternDay(Base, TimestampMixin):
    """A typical work schedule for a claimant for a given day"""

    __tablename__ = "work_pattern_day"
    work_pattern_id = Column(
        SQL_UUID(as_uuid=True),
        ForeignKey("work_pattern.work_pattern_id"),
        primary_key=True,
        comment="Internal id for the associated WorkPattern record",
    )
    day_of_week_id = Column(
        Integer,
        ForeignKey("lk_day_of_week.day_of_week_id"),
        primary_key=True,
        comment='id corresponding to a DayOfWeek enum, eg 1 ("Monday")',
    )
    minutes = Column(Integer, comment="Amount of time typically worked on this day, in minutes")

    work_pattern: Mapped[WorkPattern] = relationship(
        WorkPattern, back_populates="work_pattern_days"
    )
    day_of_week: Mapped[LkDayOfWeek] = relationship(LkDayOfWeek)

    @hybrid_property
    def sort_order(self):
        """Set sort order of Sunday to 0"""
        day_of_week_is_sunday = self.day_of_week_id == 7
        return 0 if day_of_week_is_sunday else self.day_of_week_id

    @sort_order.inplace.expression
    @classmethod
    def _sort_order_expression(cls):
        """Set sort order of Sunday to 0"""
        day_of_week_is_sunday = cls.day_of_week_id == 7
        return case((day_of_week_is_sunday, 0), else_=cls.day_of_week_id)
