# Convenience script for running alembic migration commands through a pyscript
# rather than the command line. This allows poetry to package and alias it for
# running on the production docker image from any directory.
import itertools
import os
import pathlib
import tempfile
import time
from datetime import datetime
from typing import List

import sqlalchemy
import sqlalchemy.engine
from alembic import command, script
from alembic.config import Config
from alembic.operations.ops import MigrationScript
from alembic.runtime import migration

import massgov.pfml.util.logging
from massgov.pfml.db.config import get_email_config, get_s3_config
from massgov.pfml.util.bg import background_task

logger = massgov.pfml.util.logging.get_logger(__name__)
alembic_cfg = Config(os.path.join(os.path.dirname(__file__), "./alembic.ini"))

# Override the script_location to be absolute based on this file's directory.
alembic_cfg.set_main_option("script_location", os.path.dirname(__file__))

executed_statements: List[str] = []


@background_task("db-migrate-up")
def up(revision="head"):
    try:
        enable_query_logging()
        command.upgrade(alembic_cfg, revision)
    except Exception:
        raise
    finally:
        # If there's an exception we still want to notify the EDM team of any migrations that were applied.
        try:
            enable_notify_edm_team_of_pfml_schema_changes = (
                get_s3_config().enable_notify_edm_team_of_pfml_schema_changes
            )
            logger.info(
                "Checking if EDM team will be notified of schema changes.",
                extra={
                    "enable_notify_edm_team_of_pfml_schema_changes": enable_notify_edm_team_of_pfml_schema_changes
                },
            )
            if enable_notify_edm_team_of_pfml_schema_changes:
                _upload_executed_statements_to_s3_and_send_email(executed_statements)
        except Exception as ex:
            # Failing to notify the EDM team shouldn't cause the job to fail.
            logger.error(
                "Failed to notify EDM team of PFML database schema changes.",
                exc_info=ex,
            )


@background_task("db-migrate-down")
def down(revision="-1"):
    try:
        enable_query_logging()
        command.downgrade(alembic_cfg, revision)
    except Exception:
        raise
    finally:
        # If there's an exception we still want to notify the EDM team of any migrations that were applied.
        try:
            enable_notify_edm_team_of_pfml_schema_changes = (
                get_s3_config().enable_notify_edm_team_of_pfml_schema_changes
            )
            logger.info(
                "Checking if EDM team will be notified of schema changes.",
                extra={
                    "enable_notify_edm_team_of_pfml_schema_changes": enable_notify_edm_team_of_pfml_schema_changes
                },
            )
            if enable_notify_edm_team_of_pfml_schema_changes:
                _upload_executed_statements_to_s3_and_send_email(executed_statements)
        except Exception as ex:
            # Failing to notify the EDM team shouldn't cause the job to fail.
            logger.error(
                "Failed to notify EDM team of PFML database schema changes.",
                exc_info=ex,
            )


@background_task("db-migrate-downall")
def downall(revision="base"):
    enable_query_logging()
    command.downgrade(alembic_cfg, revision)


def enable_query_logging():
    """Log each migration query as it happens along with timing.

    Based on the example at https://docs.sqlalchemy.org/en/13/faq/performance.html#query-profiling
    """

    @sqlalchemy.event.listens_for(sqlalchemy.engine.Engine, "before_cursor_execute")
    def before_execute(conn, _cursor, statement, _parameters, _context, _executemany):
        conn.info.setdefault("query_start_time", []).append(time.monotonic())
        logger.info("before execute", extra={"migrate.sql": statement.strip()})

    @sqlalchemy.event.listens_for(sqlalchemy.engine.Engine, "after_cursor_execute")
    def after_execute(conn, _cursor, statement, _parameters, _context, _executemany):
        total = int(1000 * (time.monotonic() - conn.info["query_start_time"].pop(-1)))
        logger.info(
            "after execute", extra={"migrate.sql": statement.strip(), "migrate.time_ms": total}
        )
        executed_statements.append(statement.strip())


def have_all_migrations_run(db_engine: sqlalchemy.engine.Engine) -> None:
    directory = script.ScriptDirectory.from_config(alembic_cfg)
    with db_engine.begin() as connection:
        context = migration.MigrationContext.configure(connection)
        current_heads = set(context.get_current_heads())
        expected_heads = set(directory.get_heads())

        logger.info(
            "The current migration head is at {} and Alembic is expecting {}".format(
                current_heads, expected_heads
            )
        )

        # Only throw _if_ it's been migrated and doesn't match expectations.
        # Otherwise, don't bother with this - most likely running in a testing environment.
        if current_heads != expected_heads:
            raise Exception(
                "The database schema is not in sync with the migrations. Please verify that the "
                "migrations have been run up to {}; currently at {}".format(
                    expected_heads, current_heads
                )
            )


def check_model_parity() -> None:
    revisions: List[MigrationScript] = []

    def process_revision_directives(context, revision, directives):
        nonlocal revisions
        revisions = list(directives)
        # Prevent actually generating a migration
        directives[:] = []

    command.revision(
        config=alembic_cfg,
        autogenerate=True,
        process_revision_directives=process_revision_directives,
    )
    diff = list(
        itertools.chain.from_iterable(
            op.as_diffs() for script in revisions for op in script.upgrade_ops_list
        )
    )

    message = (
        "The application models are not in sync with the migrations. You should generate "
        "a new automigration or update your local migration file. "
        "If there are unexpected errors you may need to merge main into your branch."
    )

    if diff:
        for line in diff:
            print("::error title=Missing migration::Missing migration:", line)

        logger.error(message, extra={"issues": str(diff)})
        raise Exception(message)


def _upload_executed_statements_to_s3_and_send_email(executed_statements: List[str]) -> None:
    import massgov.pfml.util.files as file_util

    logger.info("Starting process to notify EDM team of schema changes.")
    # This file will go to the EDM team.
    add_column_statements = [
        statement for statement in executed_statements if "add column" in statement.lower()
    ]
    drop_column_statements = [
        statement for statement in executed_statements if "drop column" in statement.lower()
    ]
    create_table_statements = [
        statement
        for statement in executed_statements
        if statement.lower().startswith("create table")
    ]
    drop_table_statements = [
        statement for statement in executed_statements if statement.lower().startswith("drop table")
    ]
    comment_on_column_statements = [
        statement
        for statement in executed_statements
        if statement.lower().startswith("comment on column") and "pii:true" in statement.lower()
    ]

    filtered_statements = (
        add_column_statements
        + drop_column_statements
        + create_table_statements
        + drop_table_statements
        + comment_on_column_statements
    )

    if len(filtered_statements) > 0:
        temp_directory = pathlib.Path(tempfile.mkdtemp())

        file_name = f"PFML_DB_Schema_Changes_{datetime.now().strftime('%Y%m%d%H%M')}.txt"

        logger.info(
            "Creating temporary file containing schema changes.", extra={"file_name": file_name}
        )
        file = _create_file_containing_executed_statements(
            temp_directory, file_name, filtered_statements
        )

        s3_config = get_s3_config()
        s3_dest = os.path.join(s3_config.pfml_schema_changes_directory, file_name)

        logger.info("Writing file containing schema changes to S3.", extra={"s3_dest": s3_dest})
        file_util.upload_to_s3(str(file), s3_dest)

        _send_email_to_edm_team(file)


def _create_file_containing_executed_statements(
    temp_directory: pathlib.Path, file_name: str, statements: list[str]
) -> pathlib.Path:
    report_file_path = temp_directory / file_name
    with open(report_file_path, "w") as f:
        for statement in statements:
            f.write(statement + "\n")

    return report_file_path


def _send_email_to_edm_team(file: pathlib.Path) -> None:
    from massgov.pfml.util.aws.ses import EmailRecipient, send_email

    email_config = get_email_config()

    sender = email_config.pfml_email_address
    recipient = email_config.dfml_db_migration_email_address
    cc_recipient = email_config.dfml_db_migration_cc_email_address
    email_recipient = EmailRecipient(to_addresses=[recipient], cc_addresses=[cc_recipient])
    bounce_forwarding_email_address_arn = email_config.bounce_forwarding_email_address_arn

    logger.info(
        "Sending email to EDM team.", extra={"recipient": recipient, "cc_recipient": cc_recipient}
    )
    send_email(
        recipient=email_recipient,
        subject="PFML DB Schema Changes",
        body_text="PFML DB schema changes are attached.",
        sender=sender,
        bounce_forwarding_email_address_arn=bounce_forwarding_email_address_arn,
        attachments=[file],
    )
    logger.info("Sent email to EDM team.")
