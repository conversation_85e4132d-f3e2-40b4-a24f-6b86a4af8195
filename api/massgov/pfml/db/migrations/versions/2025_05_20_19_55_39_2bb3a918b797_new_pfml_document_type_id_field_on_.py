"""new pfml_document_type_id field on documents

Revision ID: 2bb3a918b797
Revises: 6d50bfe1e6b5
Create Date: 2025-05-20 19:55:39.378544

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "2bb3a918b797"
down_revision = "6d50bfe1e6b5"


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column("document", sa.Column("pfml_document_type_id", sa.Integer(), nullable=True))
    op.create_foreign_key(
        "document_pfml_document_type_id_fkey",
        "document",
        "lk_document_type",
        ["pfml_document_type_id"],
        ["document_type_id"],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("document_pfml_document_type_id_fkey", "document", type_="foreignkey")
    op.drop_column("document", "pfml_document_type_id")
    # ### end Alembic commands ###
