"""add new optional columns last_notified_at and trigger to notification table

Revision ID: ef20016d47c0
Revises: 2cbe830f5239
Create Date: 2025-06-04 15:01:35.900337

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "ef20016d47c0"
down_revision = "2cbe830f5239"


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "notification", sa.Column("last_notified_at", sa.TIMESTAMP(timezone=True), nullable=True)
    )
    op.add_column("notification", sa.Column("trigger", sa.Text(), nullable=True))
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("notification", "trigger")
    op.drop_column("notification", "last_notified_at")
    # ### end Alembic commands ###
