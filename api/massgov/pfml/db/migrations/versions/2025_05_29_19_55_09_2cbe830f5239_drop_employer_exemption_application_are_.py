"""Drop employer_exemption_application.are_employer_contributions_required column

Revision ID: 2cbe830f5239
Revises: f7af9e6ff8a2
Create Date: 2025-05-29 19:55:09.917841

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "2cbe830f5239"
down_revision = "f7af9e6ff8a2"


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("employer_exemption_application", "are_employer_contributions_required")
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "employer_exemption_application",
        sa.Column(
            "are_employer_contributions_required",
            sa.BOOLEAN(),
            autoincrement=False,
            nullable=True,
            comment="Denotes if an employer is required to contribute to Paid Family Medical Leave",
        ),
    )
    # ### end Alembic commands ###
