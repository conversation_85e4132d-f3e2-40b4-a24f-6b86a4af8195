"""Create deferred_submission_item and lk_deferred_submission_status tables

Revision ID: f7af9e6ff8a2
Revises: 2bb3a918b797
Create Date: 2025-05-28 20:01:04.541114

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "f7af9e6ff8a2"
down_revision = "2bb3a918b797"


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "lk_deferred_submission_status",
        sa.Column(
            "deferred_submission_status_id", sa.Integer(), autoincrement=True, nullable=False
        ),
        sa.Column("deferred_submission_status_description", sa.Text(), nullable=False),
        sa.<PERSON>KeyConstraint("deferred_submission_status_id"),
    )
    op.create_table(
        "deferred_submission_item",
        sa.Column("deferred_submission_item_id", sa.UUID(), nullable=False),
        sa.Column(
            "change_request_id",
            sa.UUID(),
            nullable=True,
            comment="ID of the change request being deferred if it is a change request",
        ),
        sa.Column("deferred_submission_status_id", sa.Integer(), nullable=True),
        sa.Column(
            "num_submission_attempts",
            sa.Integer(),
            nullable=False,
            default=0,
            comment="Number of submission attempts for this item",
        ),
        sa.Column(
            "submitted_at",
            sa.TIMESTAMP(timezone=True),
            nullable=True,
            comment="Timestamp for when the last attempt to submit this item was made",
        ),
        sa.Column(
            "created_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.Column(
            "updated_at",
            sa.TIMESTAMP(timezone=True),
            server_default=sa.text("now()"),
            nullable=False,
        ),
        sa.ForeignKeyConstraint(
            ["change_request_id"],
            ["change_request.change_request_id"],
        ),
        sa.ForeignKeyConstraint(
            ["deferred_submission_status_id"],
            ["lk_deferred_submission_status.deferred_submission_status_id"],
        ),
        sa.PrimaryKeyConstraint("deferred_submission_item_id"),
    )
    op.create_index(
        op.f("ix_deferred_submission_item_change_request_id"),
        "deferred_submission_item",
        ["change_request_id"],
        unique=False,
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(
        op.f("ix_deferred_submission_item_change_request_id"), table_name="deferred_submission_item"
    )
    op.drop_table("deferred_submission_item")
    op.drop_table("lk_deferred_submission_status")
    # ### end Alembic commands ###
