"""Create LkIndustrySector lookup table

Revision ID: 6d50bfe1e6b5
Revises: 7af793f01fd2
Create Date: 2025-05-16 15:48:25.183485

"""

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision = "6d50bfe1e6b5"
down_revision = "7af793f01fd2"


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "lk_industry_sector",
        sa.Column("industry_sector_id", sa.Integer(), nullable=False),
        sa.Column("industry_sector_description", sa.Text(), nullable=False),
        sa.Column("industry_sector_code", sa.Text(), nullable=False),
        sa.<PERSON>KeyConstraint("industry_sector_id"),
    )
    op.add_column(
        "application",
        sa.Column(
            "industry_sector_id",
            sa.Integer(),
            nullable=True,
            comment="ID corresponding to an industry sector enum",
        ),
    )
    op.create_foreign_key(
        "application_industry_sector_fkey",
        "application",
        "lk_industry_sector",
        ["industry_sector_id"],
        ["industry_sector_id"],
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint("application_industry_sector_fkey", "application", type_="foreignkey")
    op.drop_column("application", "industry_sector_id")
    op.drop_table("lk_industry_sector")
    # ### end Alembic commands ###
