from datetime import datetime

from sqlalchemy.dialects.postgresql import insert
from sqlalchemy.orm import Session
from sqlalchemy.sql import literal_column, select

import massgov.pfml.util.logging as logging

logger = logging.get_logger(__name__)


def combine_fineos_documents(db_session: Session) -> None:
    # Local imports to avoid circular dependencies
    from massgov.pfml.db.models.payments import (
        FineosExtractVbiDocumentDeltaSom,
        FineosExtractVbiDocumentFullRaw,
    )

    logger.info(
        "Start - Adding FineosExtractVbiDocumentDeltaSom to FineosExtractVbiDocumentFullRaw table"
    )
    start_time = datetime.now()

    select_stmt = select(
        FineosExtractVbiDocumentDeltaSom.casenumber,
        FineosExtractVbiDocumentDeltaSom.classid,
        FineosExtractVbiDocumentDeltaSom.createdbyuserid,
        FineosExtractVbiDocumentDeltaSom.creationdate,
        FineosExtractVbiDocumentDeltaSom.c_ocemail_attachments,
        FineosExtractVbiDocumentDeltaSom.c_ocprty_documents,
        FineosExtractVbiDocumentDeltaSom.description,
        FineosExtractVbiDocumentDeltaSom.documenttype,
        FineosExtractVbiDocumentDeltaSom.indexid,
        FineosExtractVbiDocumentDeltaSom.iskeydocument,
        FineosExtractVbiDocumentDeltaSom.i_ocemail_attachments,
        FineosExtractVbiDocumentDeltaSom.i_ocprty_documents,
        FineosExtractVbiDocumentDeltaSom.lastupdatedate,
        FineosExtractVbiDocumentDeltaSom.privacytag,
        FineosExtractVbiDocumentDeltaSom.receiveddate,
        FineosExtractVbiDocumentDeltaSom.status,
        FineosExtractVbiDocumentDeltaSom.updatedbyuserid,
        FineosExtractVbiDocumentDeltaSom.reference_file_id,
        FineosExtractVbiDocumentDeltaSom.fineos_extract_import_log_id,
        literal_column("'FineosExtractVbiDocumentDeltaSom'").label("source_file"),
        literal_column("CURRENT_TIMESTAMP").label("created_at"),
        literal_column("CURRENT_TIMESTAMP").label("updated_at"),
    )

    insert_stmt = (
        insert(FineosExtractVbiDocumentFullRaw)
        .from_select(
            [
                "casenumber",
                "classid",
                "createdbyuserid",
                "creationdate",
                "c_ocemail_attachments",
                "c_ocprty_documents",
                "description",
                "documenttype",
                "indexid",
                "iskeydocument",
                "i_ocemail_attachments",
                "i_ocprty_documents",
                "lastupdatedate",
                "privacytag",
                "receiveddate",
                "status",
                "updatedbyuserid",
                "reference_file_id",
                "fineos_extract_import_log_id",
                "source_file",
                "created_at",
                "updated_at",
            ],
            select_stmt,
        )
        .on_conflict_do_nothing(
            index_elements=[FineosExtractVbiDocumentFullRaw.vbi_document_som_id]
        )
    )

    db_session.execute(insert_stmt)
    db_session.commit()

    end_time = datetime.now()
    duration = end_time - start_time
    logger.info(
        f"Querying FineosExtractVbiDocumentDeltaSom to FineosExtractVbiDocumentFullRaw executed in {duration.total_seconds()} seconds"
    )

    logger.info(
        "End - Adding FineosExtractVbiDocumentDeltaSom to FineosExtractVbiDocumentFullRaw table"
    )
