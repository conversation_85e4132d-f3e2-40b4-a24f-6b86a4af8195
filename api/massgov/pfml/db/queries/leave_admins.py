from typing import List, Set, Union
from uuid import UUID

from pydantic import UUID4
from sqlalchemy import and_, func
from sqlalchemy.orm import Query, contains_eager

from massgov.pfml.db import Session
from massgov.pfml.db.lookup_data.employees import ManagedRequirementType
from massgov.pfml.db.models.absences import <PERSON><PERSON>encePeriod, LkAbsencePeriodType, LkAbsenceReason
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.employees import (
    <PERSON><PERSON><PERSON>,
    Employee,
    Employer,
    EmployerQuarterlyContribution,
    LkLeaveRequestDecision,
    ManagedRequirement,
    User,
    UserLeaveAdministrator,
)
from massgov.pfml.db.models.organization_unit import OrganizationUnit
from massgov.pfml.db.models.verifications import Verification


def get_user_leave_administrators(
    user_id: Union[str, UUID4], db: Session
) -> List[UserLeaveAdministrator]:
    """
    Get User Leave Administrators and load required relationship data in place
    for better performance when users are a leave admin for multiple employers
    """
    return (
        db.query(UserLeaveAdministrator)
        .join(Employer)
        .join(EmployerQuarterlyContribution, isouter=True)
        .join(Verification, isouter=True)
        .options(
            contains_eager(UserLeaveAdministrator.employer).contains_eager(
                Employer.employer_quarterly_contributions
            )
        )
        .filter(
            UserLeaveAdministrator.user_id == user_id,
            UserLeaveAdministrator.deactivated.isnot(True),
        )
        .all()
    )


def absence_periods_by_employer_ids_query(db: Session, employer_ids: Set[UUID | UUID4]) -> Query:
    from massgov.pfml.db.models.employees import TaxIdentifier

    """
    Query retrieves absence period information along with related employee, employer, application, and claim data,
    includes a subquery to fetch the most recent managed requirement
    """
    # Subquery to get the most recent managed requirement per claim
    recent_managed_requirement = (
        db.query(
            ManagedRequirement.claim_id,
            ManagedRequirement.responded_at.label("la_reviewed_date"),
            ManagedRequirement.follow_up_date.label("follow_up_date"),
            User.first_name.label("la_reviewer_first_name"),
            User.last_name.label("la_reviewer_last_name"),
            func.row_number()
            .over(
                partition_by=ManagedRequirement.claim_id,
                order_by=ManagedRequirement.responded_at.desc().nulls_last(),
            )
            .label("rn"),
        )
        .outerjoin(User, ManagedRequirement.respondent_user_id == User.user_id)
        .filter(
            and_(
                ManagedRequirement.managed_requirement_type_id
                == ManagedRequirementType.EMPLOYER_CONFIRMATION.managed_requirement_type_id,
                ManagedRequirement.respondent_user_id.isnot(None),
            )
        )
        .subquery("recent_managed_requirement")
    )

    # Filter subquery to only get the most recent record
    recent_managed_requirement_filtered = (
        db.query(recent_managed_requirement)
        .filter(recent_managed_requirement.c.rn == 1)
        .subquery("recent_mr_filtered")
    )

    query = (
        db.query(
            Claim.claim_id.label("claim_id"),
            Application.application_id.label("application_id"),
            Application.date_of_birth.label("date_of_birth"),
            Application.submitted_time.label("submitted_time"),
            Application.completed_time.label("completed_time"),
            Claim.fineos_absence_id.label("absence_case_id"),
            Employer.employer_dba.label("employer_name"),
            Employer.employer_fein.label("fein"),
            Employer.employer_id.label("employer_id"),
            Employee.employee_id.label("employee_id"),
            Employee.first_name.label("employee_first_name"),
            Employee.last_name.label("employee_last_name"),
            AbsencePeriod.absence_period_start_date.label("absence_period_start_date"),
            AbsencePeriod.absence_period_end_date.label("absence_period_end_date"),
            AbsencePeriod.absence_period_id.label("absence_period_id"),
            LkAbsencePeriodType.absence_period_type_description.label(
                "absence_period_type_description"
            ),
            LkAbsenceReason.absence_reason_id.label("absence_reason_id"),
            LkLeaveRequestDecision.leave_request_decision_description.label(
                "leave_request_decision_description"
            ),
            OrganizationUnit.name.label("org_unit_name"),
            TaxIdentifier.tax_identifier.label("tax_identifier"),
            recent_managed_requirement_filtered.c.la_reviewer_first_name,
            recent_managed_requirement_filtered.c.la_reviewer_last_name,
            recent_managed_requirement_filtered.c.la_reviewed_date,
            recent_managed_requirement_filtered.c.follow_up_date,
        )
        .filter(Claim.employer_id.in_(employer_ids))
        .join(Application, Claim.claim_id == Application.claim_id)
        .join(Employer, Claim.employer_id == Employer.employer_id)
        .join(Employee, Claim.employee_id == Employee.employee_id)
        .join(AbsencePeriod, Claim.claim_id == AbsencePeriod.claim_id)
        .join(TaxIdentifier, TaxIdentifier.tax_identifier_id == Employee.tax_identifier_id)
        .join(
            LkAbsencePeriodType,
            AbsencePeriod.absence_period_type_id == LkAbsencePeriodType.absence_period_type_id,
        )
        .join(LkAbsenceReason, AbsencePeriod.absence_reason_id == LkAbsenceReason.absence_reason_id)
        .join(
            LkLeaveRequestDecision,
            AbsencePeriod.leave_request_decision_id
            == LkLeaveRequestDecision.leave_request_decision_id,
        )
        .outerjoin(
            OrganizationUnit, Claim.organization_unit_id == OrganizationUnit.organization_unit_id
        )
        .outerjoin(
            recent_managed_requirement_filtered,
            recent_managed_requirement_filtered.c.claim_id == Claim.claim_id,
        )
    )

    return query
