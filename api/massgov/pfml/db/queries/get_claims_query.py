import re
from enum import Enum
from typing import Any, Callable, Optional, Set, Type, TypeVar, cast
from uuid import UUID

from sqlalchemy import ColumnExpressionArgument, and_, asc, desc, func, or_, select
from sqlalchemy.sql.elements import UnaryExpression
from sqlalchemy.sql.roles import From<PERSON>lauseRole, JoinTargetRole
from sqlalchemy.sql.selectable import Subquery

from massgov.pfml import db
from massgov.pfml.api.util.paginate.paginator import (
    NewPaginator,
    OrderDirection,
    Page,
    PaginationAPIContext,
)
from massgov.pfml.db.lookup_data.employees import ManagedRequirementType
from massgov.pfml.db.models.absences import AbsencePeriod
from massgov.pfml.db.models.base import Base
from massgov.pfml.db.models.employees import (
    Claim,
    Employee,
    LkLeaveRequestDecision,
    ManagedRequirement,
)
from massgov.pfml.util.logging import get_logger

logger = get_logger(__package__)


class ToDoFilterOption(Enum):
    REVIEW_DUE = "review_due"
    NO_ACTION = "no_action"


# Wrapper for the DB layer of the `get_claims` endpoint
# Create a query for filtering and ordering Claim results
# The "get" methods are idempotent, the rest will change the query and affect the results
class GetClaimsQuery:
    joined: Set[JoinTargetRole | FromClauseRole | Type[Base]]

    def __init__(self, db_session: db.Session):
        self.session = db_session
        self.stmt = select(Claim)
        self.joined = set()

    # prevents duplicate joining of a table
    def join(
        self,
        model: JoinTargetRole | FromClauseRole | Type[Base],
        isouter: bool = False,
        join_filter: Optional[Any] = None,
    ) -> None:
        if model in self.joined:
            return
        self.joined.add(model)
        if join_filter is not None:  # use join_filter when query filter would not work
            self.stmt = self.stmt.join(model, join_filter, isouter=isouter)
        else:
            self.stmt = self.stmt.join(model, isouter=isouter)

    def add_employers_filter(self, employer_ids: Set[UUID]) -> None:
        self.stmt = self.stmt.where(Claim.employer_id.in_(employer_ids))

    def add_employees_filter(self, employee_ids: Set[UUID]) -> None:
        self.stmt = self.stmt.where(Claim.employee_id.in_(employee_ids))

    # TODO (PFMLPB-22982): Remove legacy SKI application filter functionality
    def add_is_reviewable_filter(self, is_reviewable: bool) -> None:
        """
        Filters claims by checking if they are reviewable or not.

        A claim is reviewable if it has an associated open requirement (earliest_follow_up_date isn't None)
        AND the claim has at least one reviewable (non-final) absence period request decision
        """
        if is_reviewable:
            self.stmt = self.stmt.where(
                Claim.earliest_follow_up_date.isnot(None),
                Claim.absence_periods.any(~AbsencePeriod.has_final_decision),  # type: ignore
            )
        else:
            self.stmt = self.stmt.where(
                or_(
                    Claim.earliest_follow_up_date.is_(None),
                    ~Claim.absence_periods.any(~AbsencePeriod.has_final_decision),  # type: ignore
                )
            )

    def add_to_do_filter(self, to_do: Set[str]) -> None:
        """
        Filter claims by checking if review_due or no_action is in to_do
        If no options are selected, no additional filtering is applied.
        """
        # If no options are selected, don't apply any filtering
        if not to_do:
            logger.info("No to_do filters selected, skipping to_do filtering")
            return None

        conditions = []

        if ToDoFilterOption.REVIEW_DUE.value in to_do:
            conditions.append(
                and_(
                    Claim.earliest_follow_up_date.isnot(None),
                    Claim.absence_periods.any(~AbsencePeriod.has_final_decision),  # type: ignore
                )
            )

        if ToDoFilterOption.NO_ACTION.value in to_do:
            # Opposite of review_due - claims that don't need review
            conditions.append(
                or_(
                    Claim.earliest_follow_up_date.is_(None),
                    ~Claim.absence_periods.any(~AbsencePeriod.has_final_decision),  # type: ignore
                )
            )

        if conditions:
            logger.info(f"Applying conditions with OR logic to claims filter: {', '.join(to_do)}")
            self.stmt = self.stmt.where(or_(*conditions))

    # TODO (PFMLPB-22982): Upon SKI Filter removal, rename to:
    #      add_application_status_filter(self, application_status)
    def add_request_decision_filter(self, request_decisions: Set[LkLeaveRequestDecision]) -> None:
        """
        Add a filter to the claims query to include only claims where at least one associated
        absence period has a leave request decision matching the provided decision(s).
        """
        filter = Claim.absence_periods.any(  # type: ignore
            AbsencePeriod.leave_request_decision_id.in_(
                {d.leave_request_decision_id for d in request_decisions}
            )
        )
        self.stmt = self.stmt.where(filter)

    def employee_search_sub_query(self) -> Subquery:
        return self.session.query(
            Employee,
            func.concat(Employee.first_name, " ", Employee.last_name).label("first_last"),
            func.concat(Employee.last_name, " ", Employee.first_name).label("last_first"),
            func.concat(
                Employee.first_name, " ", Employee.middle_name, " ", Employee.last_name
            ).label("full_name"),
            func.concat(
                Employee.fineos_employee_first_name, " ", Employee.fineos_employee_last_name
            ).label("first_last_fineos"),
            func.concat(
                Employee.fineos_employee_last_name, " ", Employee.fineos_employee_first_name
            ).label("last_first_fineos"),
            func.concat(
                Employee.fineos_employee_first_name,
                " ",
                Employee.fineos_employee_middle_name,
                " ",
                Employee.fineos_employee_last_name,
            ).label("full_name_fineos"),
        ).subquery()

    def format_search_string(self, search_string: str) -> str:
        return re.sub(r"\s+", " ", search_string).strip()

    def add_search_filter(self, search_string: str) -> None:
        # use outer join to return claims with missing relationship data
        self.join(Claim.employee, isouter=True)

        search_string = self.format_search_string(search_string)
        ntn_fastpath_regex = re.compile(r"^NTN-\d+", re.IGNORECASE)
        # If there is a space, we presume we have a full name:
        if " " in search_string:
            search_sub_query = self.employee_search_sub_query()
            self.join(search_sub_query, isouter=True)
            filters = and_(
                search_sub_query.c.employee_id == Claim.employee_id,
                or_(
                    search_sub_query.c.first_last.ilike(f"%{search_string}%"),
                    search_sub_query.c.last_first.ilike(f"%{search_string}%"),
                    search_sub_query.c.full_name.ilike(f"%{search_string}%"),
                    search_sub_query.c.first_last_fineos.ilike(f"%{search_string}%"),
                    search_sub_query.c.last_first_fineos.ilike(f"%{search_string}%"),
                    search_sub_query.c.full_name_fineos.ilike(f"%{search_string}%"),
                ),
            )
        # Without a space, we have either a single name or an absence ID.
        # If we have an obvious absence ID, do a fast search on that:
        elif ntn_fastpath_regex.match(search_string):
            filters = or_(Claim.fineos_absence_id.ilike(f"{search_string}%"))
        # Otherwise, do a more general search on name and absence ID:
        else:
            filters = or_(
                Claim.fineos_absence_id.ilike(f"%{search_string}%"),
                or_(
                    Employee.first_name.ilike(f"%{search_string}%"),
                    Employee.middle_name.ilike(f"%{search_string}%"),
                    Employee.last_name.ilike(f"%{search_string}%"),
                    Employee.fineos_employee_first_name.ilike(f"%{search_string}%"),
                    Employee.fineos_employee_middle_name.ilike(f"%{search_string}%"),
                    Employee.fineos_employee_last_name.ilike(f"%{search_string}%"),
                ),
            )
        self.stmt = self.stmt.where(filters)

    def add_managed_requirements_filter(self) -> None:
        filters = [
            ManagedRequirement.claim_id == Claim.claim_id,
            ManagedRequirement.managed_requirement_type_id
            == ManagedRequirementType.EMPLOYER_CONFIRMATION.managed_requirement_type_id,
        ]
        # use outer join to return claims without managed_requirements (one to many)
        self.join(ManagedRequirement, isouter=True, join_filter=and_(*filters))

    def add_order_by(self, context: PaginationAPIContext, is_reviewable: Optional[bool]) -> None:
        is_asc = context.order_direction == OrderDirection.asc.value
        sort_fn = asc_null_first if is_asc else desc_null_last

        self.stmt = self.stmt.distinct()

        if context.order_key is Claim.employee:
            self.add_order_by_employee(sort_fn)

        elif context.order_by == "latest_follow_up_date":
            self.add_order_by_follow_up_date(is_asc, is_reviewable)

        elif context.order_by in Claim.__table__.columns:
            self.add_order_by_column(is_asc, context)

    def add_order_by_follow_up_date(self, is_asc: bool, is_reviewable: Optional[bool]) -> None:
        """
        For order_direction=ascending (user selects "Oldest"),
        return non-open requirements first, then open,
        all in ascending order.

        For order_direction=descending (user selects "Newest"),
        return open requirements first (sorted by those that need attention first),
        then all the non-open claims in desc order.

        More details in test_get_claims_with_order_by_follow_up_date_desc,
        test_get_claims_with_order_by_follow_up_date_asc, and
        test_get_claims_with_order_by_follow_up_date_asc_and_is_reviewable_yes
        """
        order_keys: list[Any] = []
        columns = []
        if is_asc:
            if is_reviewable:
                # Only sort by one key, otherwise a subset (those with multiple managed requirements)
                # get returned first (non-open), followed by all the rest (open requirements).
                # When is_reviewable==True, all claims will have `earliest_follow_up_date`.
                columns = [Claim.earliest_follow_up_date.label("earliest")]
                order_keys = [asc("earliest")]
            else:
                columns = [
                    Claim.latest_follow_up_date.label("latest"),
                    Claim.earliest_follow_up_date.label("earliest"),
                ]
                order_keys = [asc("latest"), asc("earliest")]
        else:
            columns = [
                Claim.earliest_follow_up_date.label("earliest"),
                Claim.latest_follow_up_date.label("latest"),
            ]
            order_keys = [asc("earliest"), desc("latest").nulls_last()]

        self.stmt = self.stmt.add_columns(*columns).order_by(*order_keys)

    def add_order_by_employee(self, sort_fn: Callable) -> None:
        columns = [
            Employee.last_name,
            Employee.first_name,
            Employee.middle_name,
        ]
        order_keys = list(map(sort_fn, columns))
        # use outer join to return claims with missing relationship data
        self.join(Claim.employee, isouter=True)
        self.stmt = self.stmt.add_columns(*columns).order_by(*order_keys)

    def add_order_by_column(self, is_asc: bool, context: PaginationAPIContext) -> None:
        order_key = context.order_key.asc() if is_asc else context.order_key.desc()
        self.stmt = self.stmt.add_columns(context.order_key).order_by(order_key)

    def get_paginated_results(self, context: PaginationAPIContext) -> Page[Claim]:
        paginator = NewPaginator(self.stmt, self.session, page_size=context.page_size)  # type: ignore

        # TODO: add argument to NewPagintor to add post-processing/column selection for page_at?
        # would be nice to have it inferred from NewPaginator type param actually.
        page_results = paginator.page_at_results(context.page_offset)
        # The type for `columns()` doesn't yet include a model itself as supported, but functionaly it is
        # https://github.com/sqlalchemy/sqlalchemy/blob/c6a280658c1b969d8efb3896764f641d150a75d4/lib/sqlalchemy/engine/result.py#L2041
        page_values = cast(list[Claim], page_results.columns(Claim).scalars().all()) if page_results else []  # type: ignore[arg-type]

        return Page(
            values=page_values,
            size=context.page_size,
            offset=context.page_offset,
            total_records=paginator.total_records,
            total_pages=paginator.total_pages,
        )


_T = TypeVar("_T")


def asc_null_first(column: ColumnExpressionArgument[_T]) -> UnaryExpression[_T]:
    return asc(column).nulls_first()


def desc_null_last(column: ColumnExpressionArgument[_T]) -> UnaryExpression[_T]:
    return desc(column).nulls_last()
