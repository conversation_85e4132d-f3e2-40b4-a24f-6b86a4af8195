from datetime import date
from decimal import Decimal
from typing import Any

from pydantic import UUID4
from sqlalchemy import select

import massgov.pfml.util.logging
from massgov.pfml.api.eligibility.benefit_year import get_benefit_year_for_leave_start_date
from massgov.pfml.db import Session
from massgov.pfml.db.models.employees import BenefitYearContribution, Claim

logger = massgov.pfml.util.logging.get_logger(__name__)


def get_claim_based_on_absence_id(db_session: Session, fineos_absence_id: str) -> Claim | None:
    """Returns a claim based on the given FINEOS Absence Case ID (NTN)"""

    claim_query = select(Claim).filter(Claim.fineos_absence_id == fineos_absence_id)
    claim = db_session.execute(claim_query).one_or_none()
    if claim:
        return claim[0]
    return None


def get_iaww_from_claim(
    db_session: Session, claim: Claim, log_extra: dict[str, Any]
) -> Decimal | None:
    """
    Retrieves the Individual Average Weekly Wage (IAWW) from the benefit_year_contribution table
    for a given claim using the benefit_year_id, employee_id, and employer_id
    """
    employee_id = _get_employee_id_from_claim(claim, log_extra)
    if not employee_id:
        return None

    employer_id = _get_employer_id_from_claim(claim, log_extra)
    if not employer_id:
        return None

    leave_start_date = _get_application_start_date_from_claim(claim, log_extra)
    if not leave_start_date:
        return None

    log_extra = {
        **log_extra,
        "employee_id": employee_id,
        "employer_id": employer_id,
        "leave_start_date": leave_start_date,
    }

    found_benefit_year = get_benefit_year_for_leave_start_date(
        db_session, employee_id, leave_start_date
    )

    if not found_benefit_year:
        logger.warning("No benefit year found for employee and leave start date", extra=log_extra)
        return None

    benefit_year_id = found_benefit_year.benefit_year_id
    log_extra["benefit_year_id"] = benefit_year_id

    query = (
        select(BenefitYearContribution.average_weekly_wage)
        .filter(
            BenefitYearContribution.benefit_year_id == benefit_year_id,
            BenefitYearContribution.employee_id == employee_id,
            BenefitYearContribution.employer_id == employer_id,
        )
        .limit(1)
    )

    result = db_session.execute(query).scalar_one_or_none()
    if result is None:
        logger.warning("No IAWW found for the provided claim", extra=log_extra)

    return result


def _get_employee_id_from_claim(claim: Claim, log_extra: dict[str, Any]) -> UUID4 | None:
    """
    Extract employee_id from a claim with proper error handling and logging
    """
    employee_id = claim.employee_id
    if not employee_id:
        logger.error("No employee_id found in claim", extra=log_extra)
        return None
    return employee_id


def _get_employer_id_from_claim(claim: Claim, log_extra: dict[str, Any]) -> UUID4 | None:
    """
    Extract employer_id from a claim with proper error handling and logging
    """
    employer_id = claim.employer_id
    if not employer_id:
        logger.error("No employer_id found in claim", extra=log_extra)
        return None
    return employer_id


def _get_application_start_date_from_claim(claim: Claim, log_extra: dict[str, Any]) -> date | None:
    """
    Extract application start date from a claim with proper error handling and logging
    """
    if not claim.application:
        logger.warning("No application found in claim", extra=log_extra)
        return None

    leave_start_date = claim.application.start_date
    if not leave_start_date:
        logger.warning("No start_date found in claim application", extra=log_extra)
        return None

    return leave_start_date
