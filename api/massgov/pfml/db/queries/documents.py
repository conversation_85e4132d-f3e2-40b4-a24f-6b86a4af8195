import massgov.pfml.db as db
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.documents import Document, LkDocumentType


def get_first_fineos_document_in_types(
    db_session: db.Session, application: Application, doc_types: list[LkDocumentType]
) -> Document | None:
    """
    Retrieves the first `Document` with a fineos document type (document_type_id) matching any of
    `doc_types` in no specific order.

    Returns `None` if no matching `Document` is found.
    """

    doc_type_identifiers = [doc_type.document_type_id for doc_type in doc_types]

    # only checks document_type_id which is the document type uploaded to FINEOS and a FINEOS-recognized type
    return (
        db_session.query(Document)
        .filter(
            Document.application_id == application.application_id,
            Document.document_type_id.in_(doc_type_identifiers),
        )
        .first()
    )
