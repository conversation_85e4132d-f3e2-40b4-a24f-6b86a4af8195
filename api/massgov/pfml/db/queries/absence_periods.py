from datetime import date, <PERSON><PERSON><PERSON>
from operator import and_, or_
from typing import Dict, List, Optional, Tuple, Union
from uuid import UUID

from sqlalchemy.orm.session import Session

import massgov
import massgov.pfml.util.newrelic.events as newrelic_util
from massgov.pfml.api.models.claims.common import remap_absence_period_type
from massgov.pfml.api.models.claims.responses import (
    AbsencePeriodResponse,
    EpisodicLeavePeriodResponse,
)
from massgov.pfml.api.validation.exceptions import (
    IssueType,
    ValidationErrorDetail,
    ValidationException,
)
from massgov.pfml.db.lookup_data.absences import (
    AbsencePeriodType,
    AbsenceReason,
    AbsenceReasonQualifierOne,
    AbsenceReasonQualifierTwo,
)
from massgov.pfml.db.lookup_data.employees import LeaveRequestDecision
from massgov.pfml.db.models.absences import AbsencePeriod
from massgov.pfml.db.models.employees import Claim
from massgov.pfml.db.models.employees import LeaveRequest as dbLeaveRequest
from massgov.pfml.fineos.models.customer_api import AbsencePeriod as FineosAbsencePeriod
from massgov.pfml.fineos.models.customer_api import AbsencePeriodDecision
from massgov.pfml.fineos.models.group_client_api import (
    LeaveRequest,
    Period,
    RequestedEpisodicLeaveDetails,
)
from massgov.pfml.util.logging.absence_periods import get_absence_period_log_attributes

logger = massgov.pfml.util.logging.get_logger(__name__)

# This is a "magic" identifier that does not correspond to any real Fineos value.
# It is used to differentiate unique period ids that don't have a class ID from
# compound period ids comprised of the class ID and index ID
# Period index IDs must be unique in the scope of its class ID.
# This is the class ID to scope those to.
FINEOS_ABSENCE_PERIOD_ID_CLASS_ID = 99999


def split_fineos_absence_period_id(id: str) -> Tuple[int, int, Optional[int]]:
    """
    Conditionally splits a FINEOS absence period id into its parts.
    Returns a tuple with class_id, index_id, and period_id

    IDs come in 1 of 2 forms based on Fineos version:
    - Prefixed compound id: "PL-12345-67890"
        - Class and index ids are both in the ID and will be returned as numbers
        - Returned period_id will be None
    - Unique period id: "12345"
        - This is a unique identifier and will be returned as period_id (number)
        - The old class_id and index_id are still required, so we set them:
        -   class_id: a fabricated class id to differentiate it from the other format
        -   index_id: copy of period_id

    Anything else is considered invalid and will raise a validation error.
    """

    period_id_parts = id.split("-")

    class_id = None
    index_id = None
    period_id = None

    # This is the expected behavior for Fineos versions >= v24
    # (introduced in a version since v22.5.1)
    # example: "12345" -> ["12345"]
    if len(period_id_parts) == 1:

        raw_period_id = period_id_parts[0]

        # make sure this is a valid period id
        if not raw_period_id.isdecimal():
            message = "Non-decimal Fineos absence period ID provided"
            validation_error = ValidationErrorDetail(
                message=message, type=IssueType.fineos_client, field="id"
            )
            raise ValidationException(errors=[validation_error], message=message, data={})

        # set the period id
        period_id = int(raw_period_id)

        # client_id and index_id can't be null in the db
        # so we set them for now (but these values shouldn't be used)
        class_id = FINEOS_ABSENCE_PERIOD_ID_CLASS_ID
        index_id = period_id

        return class_id, index_id, period_id

    # This is the expected behavior for Fineos versions <= 22.5.1
    # example: "PL-12345-67890" -> ["PL", "12345", "67890"]
    if len(period_id_parts) >= 3:

        raw_class_id = period_id_parts[1]
        raw_index_id = period_id_parts[2]

        # make sure these are both valid ids
        if not (raw_class_id.isdecimal() and raw_index_id.isdecimal()):
            message = "Invalid fineos absence period unique identifier (id/periodReference)"
            validation_error = ValidationErrorDetail(
                message=message, type=IssueType.fineos_client, field="id/periodReference"
            )
            raise ValidationException(errors=[validation_error], message=message, data={})

        # set the class_id and index_id
        # (leave the new period_id blank)
        class_id = int(raw_class_id)
        index_id = int(raw_index_id)

        return class_id, index_id, period_id

    # id had unexpected number of parts
    message = "Invalid fineos absence period unique identifier (id/periodReference)"
    validation_error = ValidationErrorDetail(
        message=message, type=IssueType.fineos_client, field="id/periodReference"
    )
    raise ValidationException(errors=[validation_error], message=message, data={})


def get_absence_period_by_claim_id_and_fineos_ids(
    db_session: Session,
    claim_id: UUID,
    class_id: int,
    index_id: int,
    period_id: Optional[int] = None,
) -> Optional[AbsencePeriod]:
    return (
        db_session.query(AbsencePeriod)
        .filter(
            AbsencePeriod.claim_id == claim_id,
            or_(
                and_(
                    AbsencePeriod.fineos_absence_period_class_id == class_id,
                    AbsencePeriod.fineos_absence_period_index_id == index_id,
                ),
                (
                    AbsencePeriod.fineos_absence_period_id == period_id
                    if period_id is not None
                    else False
                ),
            ),
        )
        .one_or_none()
    )


# TODO (PFMLPB-21791): Add tests
def find_db_absence_period_for_fineos_absence_period(
    fineos_absence_period: FineosAbsencePeriod, db_absence_periods: List[AbsencePeriod]
) -> Optional[AbsencePeriod]:
    """
    For a FINEOS absence period, find the corresponding db absence period object.

    This is determined by looking at the id of the FINEOS absence period. This id is persisted as
    the "class_id" and "index_id" in our database. We have to reference both of those values to determine
    the unique absence period that matches a given FINEOS absence period.

    Note that if we didn't have access to the list of db absence periods, we could fetch the
    appropriate record from the database with `get_absence_period_by_claim_id_and_fineos_ids`.
    """

    if fineos_absence_period.id is None:
        # unable to find a db absence period if there's no id
        return None

    (class_id, index_id, period_id) = split_fineos_absence_period_id(fineos_absence_period.id)

    for absence_period in db_absence_periods:
        if (
            absence_period.fineos_absence_period_class_id == class_id
            and absence_period.fineos_absence_period_index_id == index_id
        ) or (period_id and absence_period.fineos_absence_period_id == period_id):
            # return the first match - there should never be multiple matches
            return absence_period

    # none found
    return None


def get_employee_absence_periods(
    db_session: Session,
    employee_id: UUID,
    fineos_absence_status_ids: Optional[List[int]] = None,
    absence_period_type_id: Optional[int] = None,
) -> List[AbsencePeriod]:
    filters = [Claim.employee_id == employee_id]
    if fineos_absence_status_ids is not None and len(fineos_absence_status_ids) > 0:
        filters.append(Claim.fineos_absence_status_id.in_(fineos_absence_status_ids))

    if absence_period_type_id is not None:
        filters.append(AbsencePeriod.absence_period_type_id == absence_period_type_id)

    return (
        db_session.query(AbsencePeriod)
        .join(Claim)
        .filter(*filters)
        .order_by(AbsencePeriod.absence_period_start_date)
        .all()
    )


def get_employee_absence_periods_for_leave_request(
    db_session: Session, employee_id: UUID, fineos_leave_request_id: int
) -> List[AbsencePeriod]:
    absence_periods: List[AbsencePeriod] = (
        db_session.query(AbsencePeriod)
        .join(Claim)
        .filter(
            Claim.employee_id == employee_id,
            AbsencePeriod.fineos_leave_request_id == fineos_leave_request_id,
        )
        .order_by(AbsencePeriod.absence_period_start_date)
        .all()
    )
    return absence_periods


def create_absence_period_from_fineos_ids_and_claim_id(
    claim_id: UUID, class_id: int, index_id: int, period_id: Optional[int]
) -> AbsencePeriod:
    absence_period = AbsencePeriod()

    absence_period.claim_id = claim_id
    absence_period.fineos_absence_period_class_id = class_id
    absence_period.fineos_absence_period_index_id = index_id

    if period_id is not None:
        absence_period.fineos_absence_period_id = period_id

    return absence_period


def parse_fineos_period_leave_request(
    db_absence_period: AbsencePeriod,
    leave_request: LeaveRequest,
    db_leave_request: Optional[dbLeaveRequest] = None,
) -> Tuple[AbsencePeriod, Optional[dbLeaveRequest]]:
    if leave_request.qualifier1:
        db_absence_period.absence_reason_qualifier_one_id = AbsenceReasonQualifierOne.get_id(
            leave_request.qualifier1
        )
    if leave_request.qualifier2:
        db_absence_period.absence_reason_qualifier_two_id = AbsenceReasonQualifierTwo.get_id(
            leave_request.qualifier2
        )

    db_absence_period.absence_reason_id = AbsenceReason.get_id(leave_request.reasonName)
    db_absence_period.leave_request_decision_id = LeaveRequestDecision.get_id(
        leave_request.decisionStatus
    )

    if db_leave_request:
        db_leave_request.absence_reason_id = AbsenceReason.get_id(leave_request.reasonName)
        db_leave_request.leave_approval_decision_id = LeaveRequestDecision.get_id(
            leave_request.decisionStatus
        )

    return (db_absence_period, db_leave_request)


def upsert_absence_period_from_fineos_period(
    db_session: Session,
    claim_id: UUID,
    fineos_period: Union[Period, FineosAbsencePeriod],
    log_attributes: Dict,
    absence_period_decisions: Optional[List[AbsencePeriodDecision]] = None,
) -> Optional[AbsencePeriod]:
    """
    Update or Insert Fineos Period from the Group Client API
    or Fineos Absence Period from the Customer Client API
    """
    fineos_period = convert_customer_api_period_to_group_client_period(fineos_period)

    if fineos_period.leaveRequest is None:
        logger.error("Failed to extract leave request from fineos period.", extra=log_attributes)
        return None

    if fineos_period.periodReference:
        class_id, index_id, period_id = split_fineos_absence_period_id(
            fineos_period.periodReference
        )
    else:
        logger.error("Failed to extract class and index id.", extra=log_attributes)
        return None

    db_absence_period = get_absence_period_by_claim_id_and_fineos_ids(
        db_session, claim_id, class_id, index_id, period_id
    )
    if db_absence_period is None:
        db_absence_period = create_absence_period_from_fineos_ids_and_claim_id(
            claim_id, class_id, index_id, period_id
        )

    if absence_period_decisions and fineos_period.periodReference:
        modified_start_date, modified_end_date = calculate_modified_period_dates(
            fineos_period, absence_period_decisions
        )
        db_absence_period.modified_start_date = modified_start_date
        db_absence_period.modified_end_date = modified_end_date

    db_absence_period.absence_period_start_date = fineos_period.startDate
    db_absence_period.absence_period_end_date = fineos_period.endDate
    db_absence_period.absence_period_type_id = AbsencePeriodType.get_id(fineos_period.type)
    db_leave_request: Optional[dbLeaveRequest] = (
        db_session.query(dbLeaveRequest)
        .filter(
            dbLeaveRequest.claim_id == claim_id,
            dbLeaveRequest.fineos_leave_request_id == db_absence_period.fineos_leave_request_id,
        )
        .one_or_none()
    )

    if db_leave_request:
        # Update the leave request along with the absence period
        db_absence_period, db_leave_request = parse_fineos_period_leave_request(
            db_absence_period, fineos_period.leaveRequest, db_leave_request
        )
        logger.info(
            "Updating Absence Period and Leave Request Tables",
            extra={
                **log_attributes,
                **get_absence_period_log_attributes(db_absence_period),
            },
        )
        db_session.add(db_absence_period)
        db_session.add(db_leave_request)
    else:
        db_absence_period = parse_fineos_period_leave_request(
            db_absence_period, fineos_period.leaveRequest
        )[0]
        logger.info(
            "Updating Absence Period Table",
            extra={
                **log_attributes,
                **get_absence_period_log_attributes(db_absence_period),
            },
        )
        db_session.add(db_absence_period)

    return db_absence_period


def convert_customer_api_period_to_group_client_period(
    fineos_absence_period: Union[Period, FineosAbsencePeriod],
) -> Period:
    """
    convert from Customer Client API model to Group Client API model
    """
    if isinstance(fineos_absence_period, Period):
        return fineos_absence_period
    leave_request = LeaveRequest(
        qualifier1=fineos_absence_period.reasonQualifier1,
        qualifier2=fineos_absence_period.reasonQualifier2,
        reasonName=fineos_absence_period.reason,
        decisionStatus=fineos_absence_period.requestStatus,
    )

    requested_episodic_leave_details = None

    # If any value in the episodic leave detail is NULL then the information is incomplete from FINEOS and we do
    # not want to set the requested episodic leave details
    if (
        fineos_absence_period.episodicLeavePeriodDetail is not None
        and fineos_absence_period.episodicLeavePeriodDetail.duration is not None
        and fineos_absence_period.episodicLeavePeriodDetail.durationBasis is not None
        and fineos_absence_period.episodicLeavePeriodDetail.frequency is not None
        and fineos_absence_period.episodicLeavePeriodDetail.frequencyInterval is not None
        and fineos_absence_period.episodicLeavePeriodDetail.frequencyIntervalBasis is not None
    ):
        requested_episodic_leave_details = RequestedEpisodicLeaveDetails(
            duration=fineos_absence_period.episodicLeavePeriodDetail.duration,
            durationBasis=fineos_absence_period.episodicLeavePeriodDetail.durationBasis,
            frequency=fineos_absence_period.episodicLeavePeriodDetail.frequency,
            frequencyInterval=fineos_absence_period.episodicLeavePeriodDetail.frequencyInterval,
            frequencyIntervalBasis=fineos_absence_period.episodicLeavePeriodDetail.frequencyIntervalBasis,
        )

    return Period(
        periodReference=fineos_absence_period.id,
        startDate=fineos_absence_period.startDate,
        endDate=fineos_absence_period.endDate,
        type=fineos_absence_period.absenceType,
        leaveRequest=leave_request,
        requestedEpisodicLeaveDetails=requested_episodic_leave_details,
    )


def reformat_parent_period_id(parent_period_id: Optional[str]) -> Optional[str]:
    """reformat parent id to be in same format as period id to look for equality. For example, 'AP:1000:1001' to 'AP-1000-1001'. Quirk of FINEOS API."""
    return parent_period_id.replace(":", "-") if parent_period_id else None


def get_known_decisions(
    absence_period_id: str, absence_period_decisions: List[AbsencePeriodDecision]
) -> List[AbsencePeriodDecision]:
    """get any decisions with "Known" status that match the period id"""
    return [
        absence_period_decision
        for absence_period_decision in absence_period_decisions
        if (
            absence_period_decision.absencePeriodStatus == "Known"
            and absence_period_decision.periodId == absence_period_id
        )
    ]


def get_cancelled_decisions(
    absence_period_id: str, absence_period_decisions: List[AbsencePeriodDecision]
) -> List[AbsencePeriodDecision]:
    """get any decisions with "Cancelled" status that match the period id"""
    return [
        absence_period_decision
        for absence_period_decision in absence_period_decisions
        if (
            absence_period_decision.absencePeriodStatus == "Cancelled"
            and reformat_parent_period_id(absence_period_decision.parentPeriodId)
            == absence_period_id
        )
    ]


def calculate_modified_period_dates(
    absence_period: Period, absence_period_decisions: List[AbsencePeriodDecision]
) -> Tuple[Optional[date], Optional[date]]:
    """Look for any decisions matching the current absence period id and use those
    to set modified start date and end date. Could be None if no decisions match."""

    # Cannot match decisions to a period without an id
    if not absence_period.periodReference:
        return None, None

    # Skip these calculations on periods that have been cancelled, denied, or withdrawn,
    # since all the time is functionally removed and it will cause issues with display
    if absence_period.leaveRequest and absence_period.leaveRequest.decisionStatus in [
        "Denied",
        "Cancelled",
        "Withdrawn",
    ]:
        return None, None

    absence_period_id = absence_period.periodReference

    known_decisions = get_known_decisions(absence_period_id, absence_period_decisions)

    if len(known_decisions) == 0:
        return None, None

    start_date = min(
        [
            known_decision.startDate
            for known_decision in known_decisions
            if known_decision.startDate is not None
        ]
    )
    end_date = max(
        [
            known_decision.endDate
            for known_decision in known_decisions
            if known_decision.endDate is not None
        ]
    )

    cancelled_decisions = get_cancelled_decisions(absence_period_id, absence_period_decisions)

    # if there are cancelled decisions, look for the earliest start date
    # for a cancelled period and use that as the new end date
    if len(cancelled_decisions) > 0:
        start_cancelled_time = min(
            [
                cancelled_decision.startDate
                for cancelled_decision in cancelled_decisions
                if cancelled_decision.startDate is not None
            ]
        )

        if start_cancelled_time is not None:
            end_date = start_cancelled_time - timedelta(days=1)

    return start_date, end_date


def convert_fineos_absence_period_to_claim_response_absence_period(
    period: Union[Period, FineosAbsencePeriod],
    log_attributes: Dict,
    absence_period_decisions: Optional[List[AbsencePeriodDecision]] = None,
) -> AbsencePeriodResponse:

    fineos_period = convert_customer_api_period_to_group_client_period(period)
    absence_period = AbsencePeriodResponse()

    if absence_period_decisions and fineos_period.periodReference:
        try:
            modified_start_date, modified_end_date = calculate_modified_period_dates(
                fineos_period, absence_period_decisions
            )
            absence_period.modified_start_date = modified_start_date
            absence_period.modified_end_date = modified_end_date
        except Exception as error:
            logger.error(
                "Failed to calculate modified dates.",
                extra=log_attributes,
                exc_info=error,
            )

    absence_period.absence_period_start_date = fineos_period.startDate
    absence_period.absence_period_end_date = fineos_period.endDate

    absence_period.period_type = remap_absence_period_type(fineos_period.type)
    if fineos_period.leaveRequest is None:
        newrelic_util.log_and_capture_exception(
            "Failed to extract leave request from fineos period.", extra=log_attributes
        )
        return absence_period
    leave_request = fineos_period.leaveRequest
    absence_period.reason = leave_request.reasonName
    absence_period.reason_qualifier_one = leave_request.qualifier1
    absence_period.reason_qualifier_two = leave_request.qualifier2
    absence_period.request_decision = leave_request.decisionStatus
    absence_period.fineos_absence_period_id = fineos_period.periodReference

    absence_period.episodic_leave_period_detail = get_episodic_leave_details_from_fineos_period(
        fineos_period
    )

    return absence_period


def get_episodic_leave_details_from_fineos_period(
    fineos_period: Period,
) -> EpisodicLeavePeriodResponse | None:

    episodic_leave_details = None

    if fineos_period.requestedEpisodicLeaveDetails is not None:
        episodic_leave_details = EpisodicLeavePeriodResponse()
        episodic_leave_details.fineos_absence_period_id = fineos_period.periodReference
        episodic_leave_details.duration = fineos_period.requestedEpisodicLeaveDetails.duration
        episodic_leave_details.duration_basis = (
            fineos_period.requestedEpisodicLeaveDetails.durationBasis
        )
        episodic_leave_details.frequency = fineos_period.requestedEpisodicLeaveDetails.frequency
        episodic_leave_details.frequency_interval = (
            fineos_period.requestedEpisodicLeaveDetails.frequencyInterval
        )
        episodic_leave_details.frequency_interval_basis = (
            fineos_period.requestedEpisodicLeaveDetails.frequencyIntervalBasis
        )
        episodic_leave_details.start_date = fineos_period.startDate
        episodic_leave_details.end_date = fineos_period.endDate

    return episodic_leave_details


def sync_customer_api_absence_periods_to_db(
    absence_periods: List[FineosAbsencePeriod],
    claim: Claim,
    db_session: Session,
    log_attributes: Dict,
    absence_period_decisions: Optional[List[AbsencePeriodDecision]] = None,
) -> List[AbsencePeriod]:
    db_absence_periods: List[AbsencePeriod] = []
    # add/update absence period table
    try:
        for absence_period in absence_periods:
            db_absence_period = upsert_absence_period_from_fineos_period(
                db_session, claim.claim_id, absence_period, log_attributes, absence_period_decisions
            )
            if db_absence_period is not None:
                db_absence_periods.append(db_absence_period)
        if len(absence_periods) > 0:
            # also sync start and end date to the data coming from fineos
            absence_start_dates = [ap.startDate for ap in absence_periods if ap.startDate]

            claim.claim_start_date = min(absence_start_dates)

            absence_end_dates = [ap.endDate for ap in absence_periods if ap.endDate]

            claim.claim_end_date = max(absence_end_dates)

    except Exception as error:
        logger.exception("Failed while populating AbsencePeriod Table", extra={**log_attributes})
        raise error
    # only commit if there were no errors
    db_session.commit()
    return db_absence_periods
