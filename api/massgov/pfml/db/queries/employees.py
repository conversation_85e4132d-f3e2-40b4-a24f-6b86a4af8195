import re
from datetime import date

from sqlalchemy import and_, func, or_, select
from sqlalchemy.orm import joinedload

import massgov.pfml.util.logging
from massgov.pfml.db import Session
from massgov.pfml.db.models.employees import <PERSON><PERSON>m, Employee, TaxIdentifier
from massgov.pfml.util.sql_utils import escape_like

logger = massgov.pfml.util.logging.get_logger(__name__)


def get_employees_by_name_dob_ssn(
    db_session: Session,
    first_name: str | None = None,
    last_name: str | None = None,
    date_of_birth: date | None = None,
    last_4_digits_ssn: str | None = None,
) -> list[Employee]:
    """
    Returns list of employees matching on some combo of first name, last name, date of birth, and last 4 digits of SSN

    If both DOB and last 4 digits of SSN provided, match only on those two. Otherwise, match on whatever fields are
    provided.
    """
    employee_query = (
        select(Employee)
        .outerjoin(TaxIdentifier)
        .options(joinedload(Employee.claims).options(joinedload(Claim.employer)))
    )

    if date_of_birth and last_4_digits_ssn:
        # if both DOB & SSN provided, match only on those
        employee_query = employee_query.filter(
            and_(
                Employee.date_of_birth == date_of_birth,
                TaxIdentifier.tax_identifier_last4 == last_4_digits_ssn,
            )
        )

    else:
        # any other fields provided must match
        if first_name:
            # strip first name of a middle initial, if applicable
            middle_initial_regex = r"\s+\w\.?$"
            lower_first_name = escape_like(first_name.lower())
            lower_first_name_stripped = re.sub(middle_initial_regex, "", lower_first_name)

            employee_query = employee_query.filter(
                or_(
                    func.REGEXP_REPLACE(func.lower(Employee.first_name), middle_initial_regex, "")
                    == lower_first_name_stripped,
                    func.REGEXP_REPLACE(
                        func.lower(Employee.fineos_employee_first_name),
                        middle_initial_regex,
                        "",
                    )
                    == lower_first_name_stripped,
                ),
            )

        if last_name:
            lower_last_name = escape_like(last_name.lower())

            employee_query = employee_query.filter(
                or_(
                    func.lower(Employee.last_name) == lower_last_name,
                    func.lower(Employee.fineos_employee_last_name) == lower_last_name,
                ),
            )

        if date_of_birth:
            employee_query = employee_query.filter(Employee.date_of_birth == date_of_birth)

        if last_4_digits_ssn:
            employee_query = employee_query.filter(
                TaxIdentifier.tax_identifier_last4 == last_4_digits_ssn
            )

    matching_employees = db_session.execute(employee_query).unique().scalars().all()
    return list(matching_employees)
