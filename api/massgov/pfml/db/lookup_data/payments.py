from enum import Enum

from massgov.pfml.db.lookup import LookupTable
from massgov.pfml.db.models.payments import (
    ACTIVE_WRITEBACK_RECORD_STATUS,
    PENDING_ACTIVE_WRITEBACK_RECORD_STATUS,
    LkFineosTaskType,
    LkFineosWritebackTransactionStatus,
    LkMmarsEventActionType,
    LkMmarsEventStatusType,
    LkMmarsEventType,
    LkMmarsPhaseCodeType,
    LkMmarsStatusCodeType,
    LkOverpaymentAdjustmentPaymentStatus,
    LkOverpaymentCollectionStatusType,
    LkOverpaymentRecoveryType,
    LkPaymentAuditReportType,
    LkPaymentEventType,
    LkPaymentIssueResolutionScenarioConfig,
    LkPaymentType,
    LkPrepaidRegistrationStatus,
    LkWithholdingType,
)
from massgov.pfml.delegated_payments.delegated_config import get_payment_feature_config

payment_feature_config = get_payment_feature_config()


class FineosTaskType(LookupTable):
    model = LkFineosTaskType
    column_names = (
        "fineos_task_type_id",
        "fineos_task_type_description",
    )

    MAX_WEEKLY_BENEFITS_EXCEEDED = LkFineosTaskType(1, "Max Weekly Benefits Exceeded")

    AGENCY_REPORTED_ADDITIONAL_INCOME = LkFineosTaskType(2, "Agency Reported Additional Income")

    EXEMPT_EMPLOYER = LkFineosTaskType(3, "Exempt Employer")

    INVALID_PAYMENT_WAITING_WEEK = LkFineosTaskType(4, "Invalid Payment Waiting Week")

    INVALID_PAYMENT_LEAVE_DATES_CHANGE = LkFineosTaskType(5, "Invalid Payment Leave Dates Change")

    INVALID_PAYMENT_PAY_ADJUSTMENT = LkFineosTaskType(6, "Invalid Payment Pay Adjustment")

    INVALID_PAYMENT_NAME_MISMATCH = LkFineosTaskType(7, "Invalid Payment Name Mismatch")

    INVALID_PAYMENT_PAID_DATE = LkFineosTaskType(8, "Invalid Payment Paid Date")

    PAYMENT_AUDIT_ERROR = LkFineosTaskType(9, "Payment Audit Error")

    LEAVE_REQUEST_IN_REVIEW = LkFineosTaskType(10, "Leave Request In Review")

    PAYMENT_VALIDATION_ERROR = LkFineosTaskType(11, "Payment Validation Error")

    ADDRESS_VALIDATION_ERROR = LkFineosTaskType(12, "Address Validation Error")

    BANK_PROCESSING_ERROR = LkFineosTaskType(13, "Bank Processing Error")

    PRENOTE_ERROR = LkFineosTaskType(14, "EFT Account Information Error")

    EXCEEDS_26_WEEKS_OF_TOTAL_LEAVE = LkFineosTaskType(15, "Exceeds 26 weeks of total leave")

    APPEAL_FOLLOW_UP = LkFineosTaskType(16, "Appeal Follow Up")

    APPLY_CHILD_SUPPORT_REDUCTION = LkFineosTaskType(17, "Apply Child Support Reduction")


class PaymentIssueResolutionScenarioConfig(LookupTable):
    model = LkPaymentIssueResolutionScenarioConfig
    column_names = (
        "payment_issue_resolution_scenario_config_id",
        "payment_issue_resolution_scenario_config_description",
        "fineos_task_type_id",
        "is_enabled",
        "min_days_closed_before_reopen",
        "min_days_open_before_reopen",
        "base_description",
    )

    FAILED_AUTOMATED_VALIDATION_RESOLUTION_CONFIG = LkPaymentIssueResolutionScenarioConfig(
        1,
        "FAILED_AUTOMATED_VALIDATION_RESOLUTION",
        FineosTaskType.PAYMENT_VALIDATION_ERROR.fineos_task_type_id,
        "Please see the SOP for task completion details: https://massgov.sharepoint.com/:w:/r/sites/EOL-DFML-Teams/Shared%20Documents/DFML/Operations/Program%20Integrity/Payment%20Reconciliation/SOPs/162%20SOP_%20Payment%20Validation%20Error%2010.6.21.docx",
        is_enabled=True,
    )

    LEAVE_IN_REVIEW_RESOLUTION_CONFIG = LkPaymentIssueResolutionScenarioConfig(
        2,
        "LEAVE_IN_REVIEW_RESOLUTION",
        FineosTaskType.LEAVE_REQUEST_IN_REVIEW.fineos_task_type_id,
        'This Leave Request needs a decision before payments can be made. It is currently "In Review"',
    )

    PRENOTE_ERROR_RESOLUTION_CONFIG = LkPaymentIssueResolutionScenarioConfig(
        3,
        "PRENOTE_ERROR_RESOLUTION",
        FineosTaskType.PRENOTE_ERROR.fineos_task_type_id,
        "Please see the SOP for task completion details: https://massgov.sharepoint.com/:w:/r/sites/EOL-DFML-Teams/Shared%20Documents/DFML/Operations/Program%20Integrity/Payment%20Reconciliation/SOPs/160%20SOP_%20EFT%20Rejected%20Updated%2010.12.21.docx",
        is_enabled=True,
    )

    EXEMPT_EMPLOYER_PAYMENT_EXTRACT_RESOLUTION_CONFIG = LkPaymentIssueResolutionScenarioConfig(
        4,
        "EXEMPT_EMPLOYER_PAYMENT_EXTRACT_RESOLUTION",
        FineosTaskType.EXEMPT_EMPLOYER.fineos_task_type_id,
        "Payment has been flagged for an employer exemption from PFML program during pay period.\nPlease see the SOP for task completion details: https://massgov.sharepoint.com/:w:/r/sites/EOL-DFML-Teams/Shared%20Documents/DFML/Operations/Program%20Integrity/Payment%20Reconciliation/SOPs/172%20SOP_%20Exempt%20Employer%2010.14.21.docx",
        is_enabled=True,
    )

    DOR_FINEOS_NAME_MISMATCH_RESOLUTION_CONFIG = LkPaymentIssueResolutionScenarioConfig(
        5,
        "DOR_FINEOS_NAME_MISMATCH_RESOLUTION",
        FineosTaskType.INVALID_PAYMENT_NAME_MISMATCH.fineos_task_type_id,
        "Please see the SOP for task completion details: https://massgov.sharepoint.com/:w:/r/sites/EOL-DFML-Teams/Shared%20Documents/DFML/Operations/Program%20Integrity/Payment%20Reconciliation/SOPs/164%20SOP_%20Name%20Mismatch%2010.7.21.docx",
    )

    DEPRECATED_LEAVE_DURATION_MAX_EXCEEDED_RESOLUTION_CONFIG = LkPaymentIssueResolutionScenarioConfig(
        6,
        "LEAVE_DURATION_MAX_EXCEEDED_RESOLUTION",
        FineosTaskType.EXCEEDS_26_WEEKS_OF_TOTAL_LEAVE.fineos_task_type_id,
        "Before this claim can be paid, this claimant's leave dates need to be adjusted to ensure that more than 26 total weeks are not taken within their benefit year.",
        is_enabled=False,
    )

    LEAVE_DATES_CHANGE_RESOLUTION_CONFIG = LkPaymentIssueResolutionScenarioConfig(
        7,
        "LEAVE_DATES_CHANGE_RESOLUTION",
        FineosTaskType.INVALID_PAYMENT_LEAVE_DATES_CHANGE.fineos_task_type_id,
        "Please see the SOP for task completion details: https://massgov.sharepoint.com/:w:/r/sites/EOL-DFML-Teams/Shared%20Documents/DFML/Operations/Program%20Integrity/Payment%20Reconciliation/SOPs/169%20SOP_%20Invalid%20Payment%20Leave%20Date%2010.9.21.docx",
    )

    DEPRECATED_MAX_WEEKLY_EXCEEDED_RESOLUTION = LkPaymentIssueResolutionScenarioConfig(
        # Deprecated since Feb 2023
        8,
        "MAX_WEEKLY_EXCEEDED_RESOLUTION",
        FineosTaskType.MAX_WEEKLY_BENEFITS_EXCEEDED.fineos_task_type_id,
        "Please see the SOP for task completion details: https://massgov.sharepoint.com/:w:/r/sites/EOL-DFML-Teams/Shared%20Documents/DFML/Operations/Program%20Integrity/Payment%20Reconciliation/SOPs/161%20-%20SOP%20-%20How%20to%20Process%20Aggregate%20850%20Exceeded%20-%20final.docx",
    )

    BANK_PROCESSING_ERROR_RESOLUTION_CONFIG = LkPaymentIssueResolutionScenarioConfig(
        9,
        "BANK_PROCESSING_ERROR_RESOLUTION",
        FineosTaskType.BANK_PROCESSING_ERROR.fineos_task_type_id,
        "Please see the SOP for task completion details: https://massgov.sharepoint.com/:w:/r/sites/EOL-DFML-Teams/Shared%20Documents/DFML/Operations/Program%20Integrity/Payment%20Reconciliation/SOPs/163%20SOP_%20Bank%20Processing%20Error%20Updated%2010.12.21.docx",
        is_enabled=True,
    )

    UNDER_OR_OVERPAY_ADJUSTMENT_CONFIG = LkPaymentIssueResolutionScenarioConfig(
        10,
        "UNDER_OR_OVERPAY_ADJUSTMENT",
        FineosTaskType.INVALID_PAYMENT_PAY_ADJUSTMENT.fineos_task_type_id,
        "Please see the SOP for task completion details: https://massgov.sharepoint.com/:w:/r/sites/EOL-DFML-Teams/Shared%20Documents/DFML/Operations/Program%20Integrity/Payment%20Reconciliation/SOPs/174%20SOP_%20Invalid%20Payment%20Adjustment.docx",
    )

    ADDRESS_VALIDATION_RESOLUTION_CONFIG = LkPaymentIssueResolutionScenarioConfig(
        11,
        "ADDRESS_VALIDATION_RESOLUTION",
        FineosTaskType.ADDRESS_VALIDATION_ERROR.fineos_task_type_id,
        "Please see the SOP for task completion details: https://massgov.sharepoint.com/:w:/r/sites/EOL-DFML-Teams/Shared%20Documents/DFML/Operations/Program%20Integrity/Payment%20Reconciliation/SOPs/165%20SOP_%20Address%20Validation%20Error%2010.7.21.docx",
        is_enabled=True,
    )

    INVALID_WAITING_WEEK_RESOLUTION_CONFIG = LkPaymentIssueResolutionScenarioConfig(
        12,
        "INVALID_WAITING_WEEK_RESOLUTION",
        FineosTaskType.INVALID_PAYMENT_WAITING_WEEK.fineos_task_type_id,
        "Please see the SOP for task completion details: https://massgov.sharepoint.com/:w:/r/sites/EOL-DFML-Teams/Shared%20Documents/DFML/Operations/Program%20Integrity/Payment%20Reconciliation/SOPs/168%20SOP_%20Invalid%20Payment%20Waiting%20Week%2010.9.21.docx",
        is_enabled=True,
    )

    INVALID_PAYMENT_PAID_DATE_RESOLUTION_CONFIG = LkPaymentIssueResolutionScenarioConfig(
        13,
        "INVALID_PAYMENT_PAID_DATE",
        FineosTaskType.INVALID_PAYMENT_PAID_DATE.fineos_task_type_id,
        "Payments previously issued for pay period. Review payments in Absence Paid Leave Case and Associated Dues in Overpayment Case for potential in flight recovery.",
        is_enabled=payment_feature_config.enable_payment_reject_issue_resolutions_milestone1,
    )

    PAYMENT_AUDIT_ERROR_RESOLUTION_CONFIG = LkPaymentIssueResolutionScenarioConfig(
        14,
        "PAYMENT_AUDIT_ERROR",
        FineosTaskType.PAYMENT_AUDIT_ERROR.fineos_task_type_id,
        "Cancel rejected payment. Review case notes prior to reissuing payment.",
        is_enabled=payment_feature_config.enable_payment_reject_issue_resolutions_milestone1,
    )

    DUA_ADDITIONAL_INCOME_RESOLUTION_CONFIG = LkPaymentIssueResolutionScenarioConfig(
        15,
        "DUA_ADDITIONAL_INCOME",
        FineosTaskType.AGENCY_REPORTED_ADDITIONAL_INCOME.fineos_task_type_id,
        "Payment rejected for match with other agency benefit. Review transaction status for paused payment and follow SOP 158.",
        is_enabled=payment_feature_config.enable_payment_reject_issue_resolutions_milestone1,
    )

    CANCEL_TIME_SUBMITTED_RESOLUTION_CONFIG = LkPaymentIssueResolutionScenarioConfig(
        16,
        "CANCEL_TIME_SUBMITTED",
        FineosTaskType.PAYMENT_AUDIT_ERROR.fineos_task_type_id,
        "Open cancellation request during payment generation. Cancel rejected payment and follow SOP 080 to process cancellation request.",
        is_enabled=payment_feature_config.enable_payment_reject_issue_resolutions_milestone1,
    )

    DIA_ADDITIONAL_INCOME_RESOLUTION_CONFIG = LkPaymentIssueResolutionScenarioConfig(
        17,
        "DIA_ADDITIONAL_INCOME",
        FineosTaskType.AGENCY_REPORTED_ADDITIONAL_INCOME.fineos_task_type_id,
        "Payment rejected for match with other agency benefit. Review transaction status for paused payment and follow SOP 158.",
        is_enabled=payment_feature_config.enable_payment_reject_issue_resolutions_milestone1,
    )


class FineosWritebackTransactionStatus(LookupTable):
    model = LkFineosWritebackTransactionStatus
    column_names = (
        "transaction_status_id",
        "transaction_status_description",
        "writeback_record_status",
    )

    FAILED_AUTOMATED_VALIDATION = LkFineosWritebackTransactionStatus(
        2, "Payment Validation Error", ACTIVE_WRITEBACK_RECORD_STATUS
    )
    PRENOTE_ERROR = LkFineosWritebackTransactionStatus(
        3, "EFT Account Information Error", ACTIVE_WRITEBACK_RECORD_STATUS
    )
    PENDING_PRENOTE = LkFineosWritebackTransactionStatus(
        4, "EFT Pending Bank Validation", PENDING_ACTIVE_WRITEBACK_RECORD_STATUS
    )
    DATA_ISSUE_IN_SYSTEM = LkFineosWritebackTransactionStatus(
        5, "Payment System Error", PENDING_ACTIVE_WRITEBACK_RECORD_STATUS
    )
    DEPRECATED_TOTAL_BENEFITS_OVER_CAP = LkFineosWritebackTransactionStatus(
        6, "Max Weekly Benefits Exceeded", ACTIVE_WRITEBACK_RECORD_STATUS
    )  # Duplicate of 18 - MAX_WEEKLY_BENEFITS_EXCEEDED, use that instead
    ADDRESS_VALIDATION_ERROR = LkFineosWritebackTransactionStatus(
        7, "Address Validation Error", ACTIVE_WRITEBACK_RECORD_STATUS
    )
    PENDING_PAYMENT_AUDIT = LkFineosWritebackTransactionStatus(
        8, "Pending Payment Audit", PENDING_ACTIVE_WRITEBACK_RECORD_STATUS
    )
    BANK_PROCESSING_ERROR = LkFineosWritebackTransactionStatus(
        9, "Bank Processing Error", ACTIVE_WRITEBACK_RECORD_STATUS
    )
    PROCESSED = LkFineosWritebackTransactionStatus(10, "Processed", ACTIVE_WRITEBACK_RECORD_STATUS)
    PAID = LkFineosWritebackTransactionStatus(11, "Paid", ACTIVE_WRITEBACK_RECORD_STATUS)
    POSTED = LkFineosWritebackTransactionStatus(12, "Posted", ACTIVE_WRITEBACK_RECORD_STATUS)
    LEAVE_IN_REVIEW = LkFineosWritebackTransactionStatus(
        13, "Leave Plan In Review", PENDING_ACTIVE_WRITEBACK_RECORD_STATUS
    )

    # == Payment Rejection Statuses
    FAILED_MANUAL_VALIDATION = LkFineosWritebackTransactionStatus(
        1, "Payment Audit Error", ACTIVE_WRITEBACK_RECORD_STATUS
    )  # Default rejection status

    DUA_ADDITIONAL_INCOME = LkFineosWritebackTransactionStatus(
        14, "DUA Additional Income", ACTIVE_WRITEBACK_RECORD_STATUS
    )

    DIA_ADDITIONAL_INCOME = LkFineosWritebackTransactionStatus(
        15, "DIA Additional Income", ACTIVE_WRITEBACK_RECORD_STATUS
    )

    SELF_REPORTED_ADDITIONAL_INCOME = LkFineosWritebackTransactionStatus(
        16, "SelfReported Additional Income", ACTIVE_WRITEBACK_RECORD_STATUS
    )

    EXEMPT_EMPLOYER = LkFineosWritebackTransactionStatus(
        17, "Exempt Employer", ACTIVE_WRITEBACK_RECORD_STATUS
    )

    MAX_WEEKLY_BENEFITS_EXCEEDED = LkFineosWritebackTransactionStatus(
        18, "Max Weekly Benefits Exceeded", ACTIVE_WRITEBACK_RECORD_STATUS
    )

    WAITING_WEEK = LkFineosWritebackTransactionStatus(
        19, "InvalidPayment WaitingWeek", ACTIVE_WRITEBACK_RECORD_STATUS
    )

    ALREADY_PAID_FOR_DATES = LkFineosWritebackTransactionStatus(
        20, "InvalidPayment PaidDate", ACTIVE_WRITEBACK_RECORD_STATUS
    )

    LEAVE_DATES_CHANGE = LkFineosWritebackTransactionStatus(
        21, "InvalidPayment LeaveDateChange", ACTIVE_WRITEBACK_RECORD_STATUS
    )

    UNDER_OR_OVERPAY_ADJUSTMENT = LkFineosWritebackTransactionStatus(
        22, "InvalidPayment PayAdjustment", ACTIVE_WRITEBACK_RECORD_STATUS
    )

    NAME_MISMATCH = LkFineosWritebackTransactionStatus(
        23, "InvalidPayment NameMismatch", ACTIVE_WRITEBACK_RECORD_STATUS
    )

    DEPRECATED_WITHHOLDING_ERROR = LkFineosWritebackTransactionStatus(
        24, "PrimaryPayment ProcessingErr", ACTIVE_WRITEBACK_RECORD_STATUS
    )

    PAYMENT_AUDIT_IN_PROGRESS = LkFineosWritebackTransactionStatus(
        25, "Payment Audit In Progress", PENDING_ACTIVE_WRITEBACK_RECORD_STATUS
    )

    VOID_CHECK = LkFineosWritebackTransactionStatus(
        26, "PUB Check Voided", ACTIVE_WRITEBACK_RECORD_STATUS
    )

    STOP_CHECK = LkFineosWritebackTransactionStatus(
        27, "PUB Check Undeliverable", ACTIVE_WRITEBACK_RECORD_STATUS
    )

    STALE_CHECK = LkFineosWritebackTransactionStatus(
        28, "PUB Check Stale", ACTIVE_WRITEBACK_RECORD_STATUS
    )

    DEPRECATED_LEAVE_DURATION_MAX_EXCEEDED = LkFineosWritebackTransactionStatus(
        29, "Max Leave Duration Exceeded", ACTIVE_WRITEBACK_RECORD_STATUS
    )

    INVALID_ROUTING_NUMBER = LkFineosWritebackTransactionStatus(
        30, "Invalid Routing Number", ACTIVE_WRITEBACK_RECORD_STATUS
    )

    PUB_PAYMENT_RETURNED = LkFineosWritebackTransactionStatus(
        31, "PUB Payment Returned", ACTIVE_WRITEBACK_RECORD_STATUS
    )

    CANCEL_TIME_SUBMITTED = LkFineosWritebackTransactionStatus(
        32, "Cancel time submitted", ACTIVE_WRITEBACK_RECORD_STATUS
    )

    PREPAID_DEBIT_ERROR = LkFineosWritebackTransactionStatus(
        33, "Prepaid Debit Error", ACTIVE_WRITEBACK_RECORD_STATUS
    )

    PREPAID_DEBIT_PENDING = LkFineosWritebackTransactionStatus(
        34, "Prepaid Debit Pending", PENDING_ACTIVE_WRITEBACK_RECORD_STATUS
    )


class DeprecatedAuditReportAction(str, Enum):
    REJECTED = "REJECTED"
    SKIPPED = "SKIPPED"
    INFORMATIONAL = "INFORMATIONAL"


# TODO - deprecate
class PaymentAuditReportType(LookupTable):
    model = LkPaymentAuditReportType
    column_names = (
        "payment_audit_report_type_id",
        "payment_audit_report_type_description",
        "payment_audit_report_action",
        "payment_audit_report_column",
    )

    DEPRECATED_MAX_WEEKLY_BENEFITS = LkPaymentAuditReportType(
        1, "Deprecated - Max Weekly Benefits", DeprecatedAuditReportAction.REJECTED, None
    )
    DEPRECATED_DUA_DIA_REDUCTION = LkPaymentAuditReportType(
        2,
        "Deprecated - DUA DIA Reduction (Deprecated)",
        DeprecatedAuditReportAction.INFORMATIONAL,
        None,
    )
    DEPRECATED_LEAVE_PLAN_IN_REVIEW = LkPaymentAuditReportType(
        3, "Deprecated - Leave Plan In Review", DeprecatedAuditReportAction.SKIPPED, None
    )
    DOR_FINEOS_NAME_MISMATCH = LkPaymentAuditReportType(
        4,
        "DOR FINEOS Name Mismatch",
        DeprecatedAuditReportAction.INFORMATIONAL,
        "dor_fineos_name_mismatch_details",
    )
    DUA_ADDITIONAL_INCOME = LkPaymentAuditReportType(
        5,
        "DUA Additional Income",
        DeprecatedAuditReportAction.INFORMATIONAL,
        "dua_additional_income_details",
    )
    DIA_ADDITIONAL_INCOME = LkPaymentAuditReportType(
        6,
        "DIA Additional Income",
        DeprecatedAuditReportAction.INFORMATIONAL,
        "dia_additional_income_details",
    )
    PAYMENT_DATE_MISMATCH = LkPaymentAuditReportType(
        7,
        "Payment Date Mismatch",
        DeprecatedAuditReportAction.REJECTED,
        "payment_date_mismatch_details",
    )
    DEPRECATED_EXCEEDS_26_WEEKS_TOTAL_LEAVE = LkPaymentAuditReportType(
        8,
        "Deprecated - Exceeds 26 weeks of total leave",
        DeprecatedAuditReportAction.INFORMATIONAL,
        "exceeds_26_weeks_total_leave_details",
    )
    WAITING_WEEK = LkPaymentAuditReportType(
        9, "Waiting Week", DeprecatedAuditReportAction.INFORMATIONAL, None
    )


class WithholdingType(LookupTable):
    model = LkWithholdingType
    column_names = ("withholding_type_id", "withholding_type_description")

    FEDERAL = LkWithholdingType(1, "Federal Tax")
    STATE = LkWithholdingType(2, "State Tax")


class PaymentType(LookupTable):
    model = LkPaymentType
    column_names = ("payment_type_id", "payment_type_description")

    CLAIMANT_CHECK = LkPaymentType(1, "Claimant Check")
    CLAIMANT_ACH = LkPaymentType(2, "Claimant ACH")
    ZERO_DOLLAR = LkPaymentType(3, "Zero Dollar")
    EMPLOYER_REIMBURSEMENT = LkPaymentType(4, "Employer Reimbursement")
    FEDERAL_TAX_WITHHOLDING = LkPaymentType(5, "Federal Tax Withholding")
    STATE_TAX_WITHHOLDING = LkPaymentType(6, "State Tax Withholding")
    LEGACY_MMARS = LkPaymentType(7, "Legacy MMARS")
    CHILD_SUPPORT_WITHHOLDING = LkPaymentType(8, "Child Support Withholding")


class PaymentEventType(LookupTable):
    model = LkPaymentEventType
    column_names = ("payment_event_type_id", "payment_event_type_description")

    PAYMENT_OUT = LkPaymentEventType(1, "PaymentOut")
    PAYMENT_OUT_CANCELLATIONS = LkPaymentEventType(2, "PaymentOut Cancellation")
    OVERPAYMENT = LkPaymentEventType(3, "Overpayment")
    OVERPAYMENT_ADJUSTMENT = LkPaymentEventType(4, "Overpayment Adjustment")
    OVERPAYMENT_ADJUSTMENT_CANCELLATION = LkPaymentEventType(
        5, "Overpayment Adjustment Cancellation"
    )
    OVERPAYMENT_ACTUAL_RECOVERY = LkPaymentEventType(6, "Overpayment Actual Recovery")
    OVERPAYMENT_RECOVERY_CANCELLATION = LkPaymentEventType(7, "Overpayment Recovery Cancellation")
    OVERPAYMENT_ACTUAL_RECOVERY_CANCELLATION = LkPaymentEventType(
        8, "Overpayment Actual Recovery Cancellation"
    )
    OVERPAYMENT_RECOVERY_REVERSE = LkPaymentEventType(9, "Overpayment Recovery Reverse")
    OVERPAYMENT_RECOVERY = LkPaymentEventType(10, "Overpayment Recovery")


class OverpaymentRecoveryType(LookupTable):
    model = LkOverpaymentRecoveryType
    column_names = ("overpayment_recovery_type_id", "overpayment_recovery_type_description")

    CHECK = LkOverpaymentRecoveryType(1, "Check")
    INVOICE_RECOVERY = LkOverpaymentRecoveryType(2, "Invoice Recovery")
    AUTOMATIC_OFFSET_RECOVERY = LkOverpaymentRecoveryType(3, "Automatic Offset Recovery")
    INFLIGHT_RECOVERY = LkOverpaymentRecoveryType(4, "Inflight Recovery")
    EFT = LkOverpaymentRecoveryType(5, "EFT")
    CREDIT_CARD = LkOverpaymentRecoveryType(6, "Credit Card")


class OverpaymentAdjustmentPaymentStatus(LookupTable):
    model = LkOverpaymentAdjustmentPaymentStatus
    column_names = (
        "overpayment_adjustment_payment_status_id",
        "overpayment_adjustment_payment_status_description",
    )

    ACTIVE = LkOverpaymentAdjustmentPaymentStatus(1, "Active")
    PENDING_ACTIVE = LkOverpaymentAdjustmentPaymentStatus(2, "PendingActive")
    CANCELLED = LkOverpaymentAdjustmentPaymentStatus(3, "Cancelled")
    OTHER = LkOverpaymentAdjustmentPaymentStatus(99, "Other")


class PrepaidRegistrationStatus(LookupTable):
    model = LkPrepaidRegistrationStatus
    column_names = ("prepaid_registration_status_id", "prepaid_registration_status_description")

    PENDING = LkPrepaidRegistrationStatus(1, "Pending")
    UPDATE = LkPrepaidRegistrationStatus(2, "Update")
    ACTIVE = LkPrepaidRegistrationStatus(3, "Active")
    INACTIVE = LkPrepaidRegistrationStatus(4, "Inactive")
    ERROR = LkPrepaidRegistrationStatus(5, "Error")
    UPDATE_ERROR = LkPrepaidRegistrationStatus(6, "Update Error")
    UPDATE_FROM_PRIOR = LkPrepaidRegistrationStatus(7, "Update From Prior")


class MmarsEventType(LookupTable):
    model = LkMmarsEventType
    column_names = ("mmars_event_type_id", "mmars_event_type_description")

    VCC_TRX = LkMmarsEventType(0, "VCC Event")
    RE_TRX = LkMmarsEventType(1, "Reimburse Event")
    REM_TRX = LkMmarsEventType(2, "Reimburse Modify Event")


class MmarsEventStatusType(LookupTable):
    model = LkMmarsEventStatusType
    column_names = ("mmars_event_status_type_id", "mmars_event_status_type_description")

    VCC_PENDING = LkMmarsEventStatusType(1, "VCC Pending")  # status for VCC files pending in MMARS
    VCC_ERROR = LkMmarsEventStatusType(
        2, "VCC Error"
    )  # status for VCC error in creating th VCC files
    VCC_SUBMITTED = LkMmarsEventStatusType(
        3, "VCC Submitted"
    )  # status for VCC files submitted to MMARS
    VCC_FAILED = LkMmarsEventStatusType(4, "VCC Failed")  # status for VCC files failed in MMARS
    RE_PENDING = LkMmarsEventStatusType(
        5, "RE Pending"
    )  # status for VCC processed in MMARS but RE files pending in MMARS
    RE_ERROR = LkMmarsEventStatusType(6, "RE Error")  # status for RE error in creating the RE files
    RE_SUBMITTED = LkMmarsEventStatusType(
        7, "RE Submitted"
    )  # status for RE files submitted to MMARS
    RE_FAILED = LkMmarsEventStatusType(8, "RE Failed")  # status for RE files failed in MMARS
    RE_SUCCESS = LkMmarsEventStatusType(
        9, "RE Success"
    )  # status for RE files processed successfully in MMARS
    REM_PENDING = LkMmarsEventStatusType(
        10, "REM Pending"
    )  # status for VCC processed in MMARS but RE files pending in MMARS
    REM_ERROR = LkMmarsEventStatusType(
        11, "REM Error"
    )  # status for REM error in creating the REM files
    REM_SUBMITTED = LkMmarsEventStatusType(
        12, "REM Submitted"
    )  # status for REM files submitted to MMARS
    REM_FAILED = LkMmarsEventStatusType(13, "REM Failed")  # status for REM files failed in MMARS
    REM_SUCCESS = LkMmarsEventStatusType(
        14, "REM Success"
    )  # status for REM files processed successfully in MMARS
    VCM_REQUIRE = LkMmarsEventStatusType(
        15, "VCM Require"
    )  # status for REM files processed successfully in MMARS
    RE_SUSPENDED = LkMmarsEventStatusType(
        16, "RE Suspended"
    )  # status for RE files that have been permanently paused and are not to be processed further
    ON_HOLD = LkMmarsEventStatusType(
        17, "On Hold"
    )  # status for transactions that are put an hold by Admin Portal user, which can be removed by Admin Portal user
    VCM_PENDING = LkMmarsEventStatusType(18, "VCM Pending")  # status for VCM files pending in MMARS
    VCM_SUBMITTED = LkMmarsEventStatusType(
        19, "VCM Submitted"
    )  # status for VCM files submitted to MMARS
    VCM_FAILED = LkMmarsEventStatusType(20, "VCM Failed")  # status for VCM files failed in MMARS


class OverpaymentCollectionStatusType(LookupTable):
    model = LkOverpaymentCollectionStatusType
    column_names = (
        "overpayment_collection_status_type_id",
        "overpayment_collection_status_type_description",
    )

    PENDING = LkOverpaymentCollectionStatusType(1, "Pending")
    SUCCESS = LkOverpaymentCollectionStatusType(2, "Success")
    ERROR = LkOverpaymentCollectionStatusType(3, "Error")


class MmarsEventActionType(LookupTable):
    """Defines actions for MMARS events to log their status."""

    model = LkMmarsEventActionType
    column_names = ("mmars_event_action_type_id", "mmars_event_action_type_description")

    REFERRED = LkMmarsEventActionType(1, "Referred")
    HELD = LkMmarsEventActionType(
        2, "Held"
    )  # Put a hold on mmars_event to prevent further processing
    RELEASED = LkMmarsEventActionType(3, "Released")  # Released referral after a hold
    RETRIED = LkMmarsEventActionType(
        4, "Retried"
    )  # Retried referring after a mmars_event got into error or failure state
    VCM_REVIEWED = LkMmarsEventActionType(
        5, "VCM Reviewed"
    )  # Reviewed VCM record and marked as reviewed


class MmarsPhaseCodeType(LookupTable):
    """Defines phase code received in MMARS response files."""

    model = LkMmarsPhaseCodeType
    column_names = ("mmars_phase_code_type_id", "mmars_phase_code_type_description")

    NO_PHASE = LkMmarsPhaseCodeType(0, "No Phase")
    DRAFT = LkMmarsPhaseCodeType(1, "Draft")
    PENDING = LkMmarsPhaseCodeType(2, "Pending")
    FINAL = LkMmarsPhaseCodeType(3, "Final")
    HISTORICAL = LkMmarsPhaseCodeType(5, "Historical (Final)")
    CONFLICT_DRAFT = LkMmarsPhaseCodeType(6, "Conflict Draft")
    TEMPLATE = LkMmarsPhaseCodeType(7, "Template")


class MmarsStatusCodeType(LookupTable):
    """Defines phase code received in MMARS response files."""

    model = LkMmarsStatusCodeType
    column_names = ("mmars_status_code_type_id", "mmars_status_code_type_description")

    HELD = LkMmarsStatusCodeType(1, "Held")
    READY = LkMmarsStatusCodeType(2, "Ready")
    REJECTED = LkMmarsStatusCodeType(3, "Rejected")
    SUBMITTED = LkMmarsStatusCodeType(4, "Submitted")
