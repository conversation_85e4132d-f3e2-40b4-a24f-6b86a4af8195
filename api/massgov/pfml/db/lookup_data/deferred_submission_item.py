from massgov.pfml.db.lookup import LookupTable
from massgov.pfml.db.models.deferred_submission_item import LkDeferredSubmissionStatus


class DeferredSubmissionStatus(LookupTable):
    model = LkDeferredSubmissionStatus
    column_names = ("deferred_submission_status_id", "deferred_submission_status_description")

    PENDING = LkDeferredSubmissionStatus(1, "Pending")
    SUBMITTED = LkDeferredSubmissionStatus(2, "Submitted")
    ERROR = LkDeferredSubmissionStatus(3, "Error")
    CANCELED = LkDeferredSubmissionStatus(4, "Canceled")
