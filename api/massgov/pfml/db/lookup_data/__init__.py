#
# Models representing data in lookup tables (the tables themselves are defined elsewhere)
#

import importlib
import inspect
import pkgutil
import time
from types import ModuleType

import massgov.pfml.util.logging
from massgov.pfml.db import Session
from massgov.pfml.db.lookup import LookupTable

from . import (  # noqa: F401
    absences,
    appeal,
    applications,
    azure,
    change_request,
    deferred_submission_item,
    documents,
    dor,
    employees,
    flags,
    geo,
    industry_codes,
    language,
    notifications,
    payments,
    phone,
    pub_error,
    reference_file_type,
    state,
    verifications,
)

logger = massgov.pfml.util.logging.get_logger(__name__)


def sync_data(db_session: Session) -> None:
    """Synchronize all lookup data"""
    start_time = time.monotonic()

    sync_lookup_tables(db_session)
    sync_non_lookup_static_tables(db_session)

    logger.info("sync took %.2fs", time.monotonic() - start_time)


def sync_lookup_tables(db_session: Session) -> None:
    """Synchronize data in all lookup tables in package"""
    modules = import_submodules(__name__)

    for full_name, module in modules.items():
        logger.info(f"Syncing module {full_name}")
        sync_module_lookup_tables(db_session, module)
        db_session.commit()


def sync_module_lookup_tables(db_session: Session, module: ModuleType) -> None:
    """Synchronize all lookup tables in given module"""
    lookups = inspect.getmembers(
        module, lambda v: inspect.isclass(v) and issubclass(v, LookupTable)
    )

    for lookup_class_name, lookup_class in lookups:
        # only include lookups from the module in question, i.e., ignore any
        # imports
        if lookup_class.__module__ != module.__name__:
            continue

        logger.info(f"Syncing {lookup_class_name}")
        lookup_class.sync_to_database(db_session)


def import_submodules(package: str | ModuleType, recursive: bool = True) -> dict[str, ModuleType]:
    """Import all submodules of a module, recursively, including subpackages"""
    if isinstance(package, str):
        package = importlib.import_module(package)

    results = {}
    for _loader, name, is_pkg in pkgutil.walk_packages(package.__path__):
        full_name = package.__name__ + "." + name
        try:
            results[full_name] = importlib.import_module(full_name)
        except ModuleNotFoundError:
            continue

        if recursive and is_pkg:
            results.update(import_submodules(full_name))

    return results


def sync_non_lookup_static_tables(db_session: Session) -> None:
    logger.info("Syncing Azure Permissions")
    azure.sync_azure_permissions(db_session)
    db_session.commit()

    logger.info("Syncing Holidays")
    import massgov.pfml.db.models.holiday as holiday

    holiday.sync_holidays(db_session)
    db_session.commit()

    logger.info("Syncing State Metrics")
    import massgov.pfml.db.models.state_metrics as state_metrics

    state_metrics.sync_state_metrics(db_session)
    db_session.commit()

    logger.info("Syncing Insurance Providers")
    import massgov.pfml.db.models.employer_exemptions as employer_exemptions

    employer_exemptions.sync_insurance_providers(db_session)
    db_session.commit()
