from massgov.pfml.db.lookup import LookupTable
from massgov.pfml.db.models.employer_exemptions import LkEmployerExemptionApplicationStatus


class EmployerExemptionApplicationStatus(LookupTable):
    model = LkEmployerExemptionApplicationStatus
    column_names = (
        "employer_exemption_application_status_id",
        "employer_exemption_application_status_description",
    )

    DRAFT = LkEmployerExemptionApplicationStatus(1, "Draft")
    APPROVED = LkEmployerExemptionApplicationStatus(2, "Approved")
    DENIED = LkEmployerExemptionApplicationStatus(3, "Denied")
    IN_REVIEW = LkEmployerExemptionApplicationStatus(4, "In Review")
