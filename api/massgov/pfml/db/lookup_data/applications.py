from massgov.pfml.db.lookup import LookupTable
from massgov.pfml.db.models.applications import (
    LkAmountFrequency,
    LkDayOfWeek,
    LkEmployerBenefitType,
    LkEmploymentStatus,
    LkEthnicity,
    LkFrequencyOrDuration,
    LkIndustrySector,
    LkLeaveReason,
    LkLeaveReasonQualifier,
    LkMmgIdvStatus,
    LkOtherIncomeType,
    LkPreviousLeaveQualifyingReason,
    LkRace,
    LkRelationshipQualifier,
    LkRelationshipToCaregiver,
    LkWorkPatternType,
)


class WorkPatternType(LookupTable):
    # Item descriptions here map to Fineos Enum domain #277
    model = LkWorkPatternType
    column_names = ("work_pattern_type_id", "work_pattern_type_description")

    FIXED = LkWorkPatternType(0, "Fixed")
    ROTATING = LkWorkPatternType(1, "Rotating")
    VARIABLE = LkWorkPatternType(2, "Variable")


class DayOfWeek(LookupTable):
    model = LkDayOfWeek
    column_names = ("day_of_week_id", "day_of_week_description")

    MONDAY = LkDayOfWeek(1, "Monday")
    TUESDAY = LkDayOfWeek(2, "Tuesday")
    WEDNESDAY = LkDayOfWeek(3, "Wednesday")
    THURSDAY = LkDayOfWeek(4, "Thursday")
    FRIDAY = LkDayOfWeek(5, "Friday")
    SATURDAY = LkDayOfWeek(6, "Saturday")
    SUNDAY = LkDayOfWeek(7, "Sunday")


class LeaveReason(LookupTable):
    model = LkLeaveReason
    column_names = ("leave_reason_id", "leave_reason_description")

    CARE_FOR_A_FAMILY_MEMBER = LkLeaveReason(1, "Care for a Family Member")
    PREGNANCY_MATERNITY = LkLeaveReason(2, "Pregnancy/Maternity")
    CHILD_BONDING = LkLeaveReason(3, "Child Bonding")
    SERIOUS_HEALTH_CONDITION_EMPLOYEE = LkLeaveReason(4, "Serious Health Condition - Employee")
    MILITARY_CAREGIVER = LkLeaveReason(5, "Military Caregiver")
    MILITARY_EXIGENCY_FAMILY = LkLeaveReason(6, "Military Exigency Family")


class LeaveReasonQualifier(LookupTable):
    model = LkLeaveReasonQualifier
    column_names = ("leave_reason_qualifier_id", "leave_reason_qualifier_description")

    NEWBORN = LkLeaveReasonQualifier(1, "Newborn")
    SERIOUS_HEALTH_CONDITION = LkLeaveReasonQualifier(2, "Serious Health Condition")
    # This reason qualifier is not currently being used. See API-389 & PR #1280.
    WORK_RELATED_ACCIDENT_INJURY = LkLeaveReasonQualifier(3, "Work Related Accident/Injury")
    ADOPTION = LkLeaveReasonQualifier(4, "Adoption")
    FOSTER_CARE = LkLeaveReasonQualifier(5, "Foster Care")
    NOT_WORK_RELATED = LkLeaveReasonQualifier(6, "Not Work Related")
    SICKNESS = LkLeaveReasonQualifier(7, "Sickness")
    POSTNATAL_DISABILITY = LkLeaveReasonQualifier(8, "Postnatal Disability")

    WORK_RELATED = LkLeaveReasonQualifier(9, "Work Related")
    BLOOD = LkLeaveReasonQualifier(10, "Blood")
    BLOOD_STEM_CELL = LkLeaveReasonQualifier(11, "Blood Stem Cell")
    BONE_MARROW = LkLeaveReasonQualifier(12, "Bone Marrow")
    ORGAN = LkLeaveReasonQualifier(13, "Organ")
    OTHER = LkLeaveReasonQualifier(14, "Other")
    PRENATAL_CARE = LkLeaveReasonQualifier(15, "Prenatal Care")
    PRENATAL_DISABILITY = LkLeaveReasonQualifier(16, "Prenatal Disability")
    PREGNANCY_RELATED = LkLeaveReasonQualifier(17, "Pregnancy Related")
    RIGHT_TO_LEAVE = LkLeaveReasonQualifier(18, "Right to Leave")
    SICKNESS_NON_SERIOUS_HEALTH_CONDITION = LkLeaveReasonQualifier(
        19, "Sickness - Non-Serious Health Condition"
    )
    CHILDCARE = LkLeaveReasonQualifier(20, "Childcare")
    COUNSELING = LkLeaveReasonQualifier(21, "Counseling")
    FINANCIAL_AND_LEGAL_ARRANGEMENTS = LkLeaveReasonQualifier(22, "Financial & Legal Arrangements")
    MILITARY_EVENTS_AND_RELATED_ACTIVITIES = LkLeaveReasonQualifier(
        23, "Military Events & Related Activities"
    )
    OTHER_ADDITIONAL_ACTIVITIES = LkLeaveReasonQualifier(24, "Other Additional Activities")
    POST_DEPLOYMENT_ACTIVITES_INCLUDING_BEREAVEMENT = LkLeaveReasonQualifier(
        25, "Post Deployment Activities - Including Bereavement"
    )
    REST_AND_RECUPERATION = LkLeaveReasonQualifier(26, "Rest & Recuperation")
    SHORT_NOTICE_DEPLOYMENT = LkLeaveReasonQualifier(27, "Short Notice Deployment")
    CLOSURE_OF_SCHOOL_CHILDCARE = LkLeaveReasonQualifier(28, "Closure of School/Childcare")
    QUARANTINE_ISOLATION_NON_SICK = LkLeaveReasonQualifier(29, "Quarantine/Isolation - Not Sick")
    BIRTH_DISABILITY = LkLeaveReasonQualifier(30, "Birth Disability")
    CHILDCARE_AND_SCHOOL_ACTIVITIES = LkLeaveReasonQualifier(31, "Childcare and School Activities")
    POST_DEPLOYMENT_ACTIVITIES = LkLeaveReasonQualifier(32, "Post Deployment Activities")
    PARENTAL_CARE = LkLeaveReasonQualifier(33, "Parental Care")


class RelationshipToCaregiver(LookupTable):
    model = LkRelationshipToCaregiver
    column_names = ("relationship_to_caregiver_id", "relationship_to_caregiver_description")

    PARENT = LkRelationshipToCaregiver(1, "Parent")
    CHILD = LkRelationshipToCaregiver(2, "Child")
    GRANDPARENT = LkRelationshipToCaregiver(3, "Grandparent")
    GRANDCHILD = LkRelationshipToCaregiver(4, "Grandchild")
    OTHER_FAMILY_MEMBER = LkRelationshipToCaregiver(5, "Other Family Member")
    SERVICE_MEMBER = LkRelationshipToCaregiver(6, "Service Member")
    INLAW = LkRelationshipToCaregiver(7, "Inlaw")
    SIBLING = LkRelationshipToCaregiver(8, "Sibling - Brother/Sister")
    OTHER = LkRelationshipToCaregiver(9, "Other")
    EMPLOYEE = LkRelationshipToCaregiver(10, "Employee")
    SPOUSE = LkRelationshipToCaregiver(11, "Spouse")


class RelationshipQualifier(LookupTable):
    model = LkRelationshipQualifier
    column_names = ("relationship_qualifier_id", "relationship_qualifier_description")

    ADOPTED = LkRelationshipQualifier(1, "Adopted")
    BIOLOGICAL = LkRelationshipQualifier(2, "Biological")
    FOSTER = LkRelationshipQualifier(3, "Foster")
    CUSTODIAL_PARENT = LkRelationshipQualifier(4, "Custodial Parent")
    LEGAL_GAURDIAN = LkRelationshipQualifier(5, "Legal Guardian")
    STEP_PARENT = LkRelationshipQualifier(6, "Step Parent")
    LEGALLY_MARRIED = LkRelationshipQualifier(7, "Legally Married")
    UNDISCLOSED = LkRelationshipQualifier(8, "Undisclosed")
    PARENT_IN_LAW = LkRelationshipQualifier(9, "Parent-In-Law")


class Ethnicity(LookupTable):
    model = LkEthnicity
    column_names = ("ethnicity_id", "ethnicity_description")

    PREFER_NOT_TO_ANSWER = LkEthnicity(1, "Prefer not to answer")
    HISPANIC_OR_LATINO = LkEthnicity(2, "Hispanic or Latino")
    NOT_HISPANIC_OR_LATINO = LkEthnicity(3, "Not Hispanic or Latino")


class Race(LookupTable):
    model = LkRace
    column_names = ("race_id", "race_description")

    PREFER_NOT_TO_ANSWER = LkRace(1, "Prefer not to answer")
    AMERICAN_INDIAN_ALASKA_NATIVE = LkRace(2, "American Indian/Alaska Native")
    ASIAN_ASIAN_AMERICAN = LkRace(3, "Asian/Asian American")
    BLACK_AFRICAN_AMERICAN = LkRace(4, "Black/African American")
    NATIVE_HAWAIIAN_OTHER_PACIFIC_ISLANDER = LkRace(5, "Native Hawaiian/Other Pacific Islander")
    WHITE = LkRace(6, "White")
    ANOTHER_RACE_NOT_LISTED_ABOVE = LkRace(7, "Another race not listed above")
    MULTIRACIAL = LkRace(8, "Multiracial")
    MIDDLE_EASTERN_NORTH_AFRICAN = LkRace(9, "Middle Eastern/North African")


class FrequencyOrDuration(LookupTable):
    model = LkFrequencyOrDuration
    column_names = ("frequency_or_duration_id", "frequency_or_duration_description")

    DAYS = LkFrequencyOrDuration(1, "Days")
    WEEKS = LkFrequencyOrDuration(2, "Weeks")
    MONTHS = LkFrequencyOrDuration(3, "Months")
    MINUTES = LkFrequencyOrDuration(4, "Minutes")
    HOURS = LkFrequencyOrDuration(5, "Hours")


class EmploymentStatus(LookupTable):
    model = LkEmploymentStatus
    column_names = ("employment_status_id", "employment_status_description", "fineos_label")

    EMPLOYED = LkEmploymentStatus(1, "Employed", "Active")
    UNEMPLOYED = LkEmploymentStatus(2, "Unemployed", "Terminated")
    SELF_EMPLOYED = LkEmploymentStatus(3, "Self-Employed", "Self-Employed")
    RETIRED = LkEmploymentStatus(4, "Retired", "Retired")
    UNKNOWN = LkEmploymentStatus(5, "Unknown", "Unknown")


class AmountFrequency(LookupTable):
    model = LkAmountFrequency
    column_names = ("amount_frequency_id", "amount_frequency_description")

    PER_DAY = LkAmountFrequency(1, "Per Day")
    PER_WEEK = LkAmountFrequency(2, "Per Week")
    PER_MONTH = LkAmountFrequency(3, "Per Month")
    ALL_AT_ONCE = LkAmountFrequency(4, "In Total")
    UNKNOWN = LkAmountFrequency(5, "Unknown")


class EmployerBenefitType(LookupTable):
    model = LkEmployerBenefitType
    column_names = ("employer_benefit_type_id", "employer_benefit_type_description")

    ACCRUED_PAID_LEAVE = LkEmployerBenefitType(1, "Accrued paid leave")
    SHORT_TERM_DISABILITY = LkEmployerBenefitType(2, "Short-term disability insurance")
    PERMANENT_DISABILITY_INSURANCE = LkEmployerBenefitType(3, "Permanent disability insurance")
    FAMILY_OR_MEDICAL_LEAVE_INSURANCE = LkEmployerBenefitType(
        4, "Family or medical leave insurance"
    )
    UNKNOWN = LkEmployerBenefitType(5, "Unknown")
    NOT_SURE = LkEmployerBenefitType(6, "I'm not sure")
    PAID_TIME_OFF = LkEmployerBenefitType(7, "Paid time off")


class OtherIncomeType(LookupTable):
    model = LkOtherIncomeType
    column_names = ("other_income_type_id", "other_income_type_description")

    WORKERS_COMP = LkOtherIncomeType(1, "Workers Compensation")
    UNEMPLOYMENT = LkOtherIncomeType(2, "Unemployment Insurance")
    SSDI = LkOtherIncomeType(3, "SSDI")
    RETIREMENT_DISABILITY = LkOtherIncomeType(4, "Disability benefits under Gov't retirement plan")
    JONES_ACT = LkOtherIncomeType(5, "Jones Act benefits")
    RAILROAD_RETIREMENT = LkOtherIncomeType(6, "Railroad Retirement benefits")
    OTHER_EMPLOYER = LkOtherIncomeType(7, "Earnings from another employment/self-employment")
    UNKNOWN = LkOtherIncomeType(8, "Unknown")


# TODO (CP-1554): investigate whether this model can be merged with LeaveReason
class PreviousLeaveQualifyingReason(LookupTable):
    model = LkPreviousLeaveQualifyingReason
    column_names = (
        "previous_leave_qualifying_reason_id",
        "previous_leave_qualifying_reason_description",
    )

    PREGNANCY_MATERNITY = LkPreviousLeaveQualifyingReason(1, "Pregnancy")
    AN_ILLNESS_OR_INJURY = LkPreviousLeaveQualifyingReason(2, "An illness or injury")
    CARE_FOR_A_FAMILY_MEMBER = LkPreviousLeaveQualifyingReason(
        3, "Caring for a family member with a serious health condition"
    )
    CHILD_BONDING = LkPreviousLeaveQualifyingReason(
        4, "Bonding with my child after birth or placement"
    )
    MILITARY_CAREGIVER = LkPreviousLeaveQualifyingReason(
        5, "Caring for a family member who serves in the armed forces"
    )
    MILITARY_EXIGENCY_FAMILY = LkPreviousLeaveQualifyingReason(
        6, "Managing family affairs while a family member is on active duty in the armed forces"
    )

    # separate choice on the portal,
    # should be mapped to pregnancy (the "Pregnancy" choice) before sending to fineos
    PREGNANCY_HEALTH_CONDITION = LkPreviousLeaveQualifyingReason(
        7, "A health condition during pregnancy"
    )

    # separate choice on the portal,
    # should be mapped to medical (the "An illness or injury" choice) before sending to fineos
    AN_ILLNESS_OR_INJURY_HOSPITAL = LkPreviousLeaveQualifyingReason(
        8, "An illness or injury that required hospitalization"
    )


class MmgIdvStatus(LookupTable):
    model = LkMmgIdvStatus
    column_names = ("mmg_idv_status_id", "mmg_idv_status_description")

    UNVERIFIED = LkMmgIdvStatus(1, "Unverified")
    VERIFIED = LkMmgIdvStatus(2, "Verified")


class IndustrySector(LookupTable):
    model = LkIndustrySector
    column_names = ("industry_sector_id", "industry_sector_description", "industry_sector_code")

    ACCOMMODATION_AND_FOOD_SERVICES = LkIndustrySector(1, "Accommodation and Food Services", "72")
    ADMINISTRATIVE_AND_SUPPORT_WASTE_MANAGEMENT_REMEDIATION_SERVICES = LkIndustrySector(
        2, "Administrative and Support and Waste Management Remediation Services", "56"
    )
    AGRICULTURE_FORESTRY_FISHING_AND_HUNTING = LkIndustrySector(
        3, "Agriculture, Forestry, Fishing, and Hunting", "11"
    )
    ARTS_ENTERTAINMENT_AND_RECREATION = LkIndustrySector(
        4, "Arts, Entertainment, and Recreation", "71"
    )
    CONSTRUCTION = LkIndustrySector(5, "Construction", "23")
    EDUCATIONAL_SERVICES = LkIndustrySector(6, "Educational Services", "61")
    FINANCE_AND_INSURANCE = LkIndustrySector(7, "Finance and Insurance", "52")
    HEALTH_CARE_AND_SOCIAL_ASSISTANCE = LkIndustrySector(
        8, "Health Care and Social Assistance", "62"
    )
    INFORMATION = LkIndustrySector(9, "Information", "51")
    MANAGEMENT_OF_COMPANIES_AND_ENTERPRISES = LkIndustrySector(
        10, "Management of Companies and Enterprises", "55"
    )
    MANUFACTURING = LkIndustrySector(11, "Manufacturing", "31 - 33")
    OTHER_SERVICES_EXCEPT_PUBLIC_ADMINISTRATION = LkIndustrySector(
        12, "Other Services (except Public Administration)", "81"
    )
    PUBLIC_ADMINISTRATION = LkIndustrySector(13, "Public Administration", "92")
    PROFESSIONAL_SCIENTIFIC_AND_TECHNICAL_SERVICES = LkIndustrySector(
        14, "Professional, Scientific, and Technical Services", "54"
    )
    REAL_ESTATE_RENTAL_AND_LEASING = LkIndustrySector(15, "Real Estate Rental and Leasing", "53")
    RETAIL_TRADE = LkIndustrySector(16, "Retail Trade", "44 - 45")
    TRANSPORTATION_AND_WAREHOUSING = LkIndustrySector(
        17, "Transportation and Warehousing", "48 - 49"
    )
    UTILITIES = LkIndustrySector(18, "Utilities", "22")
    WHOLESALE_TRADE = LkIndustrySector(19, "Wholesale Trade", "42")
