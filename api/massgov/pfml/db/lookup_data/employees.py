from massgov.pfml.db.lookup import LookupTable
from massgov.pfml.db.models import LkAddressType
from massgov.pfml.db.models.employees import (
    LkBankAccountType,
    LkClaimType,
    LkEducationLevel,
    LkEligibilityDecision,
    LkGender,
    LkLeaveRequestDecision,
    LkManagedRequirementCategory,
    LkManagedRequirementStatus,
    LkManagedRequirementType,
    LkMaritalStatus,
    LkOccupation,
    LkPaymentCheckStatus,
    LkPaymentMethod,
    LkPaymentRelevantParty,
    LkPaymentTransactionType,
    LkPrenoteState,
    LkRole,
    LkTitle,
    LkUserLeaveAdministratorActionType,
    LkWagesAndContributionsDatasource,
)

from .absences import AbsenceReason
from .state import State


class AddressType(LookupTable):
    model = LkAddressType
    column_names = ("address_type_id", "address_description")

    HOME = LkAddressType(1, "Home")
    BUSINESS = LkAddressType(2, "Business")
    MAILING = LkAddressType(3, "Mailing")
    RESIDENTIAL = LkAddressType(4, "Residential")


class ClaimType(LookupTable):
    model = LkClaimType
    column_names = ("claim_type_id", "claim_type_description")

    FAMILY_LEAVE = LkClaimType(1, "Family Leave")
    MEDICAL_LEAVE = LkClaimType(2, "Medical Leave")
    MILITARY_LEAVE = LkClaimType(3, "Military Leave")


ABSENCE_REASON_ID_CLAIM_TYPE_ID_MAPPING: dict[int, int] = {
    AbsenceReason.BEREAVEMENT.absence_reason_id: ClaimType.FAMILY_LEAVE.claim_type_id,
    AbsenceReason.CHILD_BONDING.absence_reason_id: ClaimType.FAMILY_LEAVE.claim_type_id,
    AbsenceReason.EDUCATIONAL_ACTIVITY_FAMILY.absence_reason_id: ClaimType.FAMILY_LEAVE.claim_type_id,
    AbsenceReason.MEDICAL_DONATION_FAMILY.absence_reason_id: ClaimType.FAMILY_LEAVE.claim_type_id,
    AbsenceReason.MILITARY_CAREGIVER.absence_reason_id: ClaimType.FAMILY_LEAVE.claim_type_id,
    AbsenceReason.MILITARY_EXIGENCY_FAMILY.absence_reason_id: ClaimType.FAMILY_LEAVE.claim_type_id,
    AbsenceReason.PREVENTATIVE_CARE_FAMILY_MEMBER.absence_reason_id: ClaimType.FAMILY_LEAVE.claim_type_id,
    AbsenceReason.PUBLIC_HEALTH_EMERGENCY_FAMILY.absence_reason_id: ClaimType.FAMILY_LEAVE.claim_type_id,
    AbsenceReason.MEDICAL_DONATION_EMPLOYEE.absence_reason_id: ClaimType.MEDICAL_LEAVE.claim_type_id,
    AbsenceReason.MILITARY_EMPLOYEE.absence_reason_id: ClaimType.MEDICAL_LEAVE.claim_type_id,
    AbsenceReason.PERSONAL_EMPLOYEE.absence_reason_id: ClaimType.MEDICAL_LEAVE.claim_type_id,
    AbsenceReason.PREGNANCY_MATERNITY.absence_reason_id: ClaimType.MEDICAL_LEAVE.claim_type_id,
    AbsenceReason.PREVENTATIVE_CARE_EMPLOYEE.absence_reason_id: ClaimType.MEDICAL_LEAVE.claim_type_id,
    AbsenceReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.absence_reason_id: ClaimType.MEDICAL_LEAVE.claim_type_id,
    AbsenceReason.SICKENESS_NON_SERIOUS_HEALTH_CONDITION_EMPLOYEE.absence_reason_id: ClaimType.MEDICAL_LEAVE.claim_type_id,
    AbsenceReason.CARE_OF_A_FAMILY_MEMBER.absence_reason_id: ClaimType.FAMILY_LEAVE.claim_type_id,
}


class ManagedRequirementStatus(LookupTable):
    model = LkManagedRequirementStatus
    column_names = ("managed_requirement_status_id", "managed_requirement_status_description")

    OPEN = LkManagedRequirementStatus(1, "Open")
    COMPLETE = LkManagedRequirementStatus(2, "Complete")
    SUPPRESSED = LkManagedRequirementStatus(3, "Suppressed")


class ManagedRequirementCategory(LookupTable):
    model = LkManagedRequirementCategory
    column_names = ("managed_requirement_category_id", "managed_requirement_category_description")

    EMPLOYER_CONFIRMATION = LkManagedRequirementCategory(1, "Employer Confirmation")


class ManagedRequirementType(LookupTable):
    model = LkManagedRequirementType
    column_names = ("managed_requirement_type_id", "managed_requirement_type_description")

    EMPLOYER_CONFIRMATION = LkManagedRequirementType(1, "Employer Confirmation of Leave Data")


class MaritalStatus(LookupTable):
    model = LkMaritalStatus
    column_names = ("marital_status_id", "marital_status_description")

    SINGLE = LkMaritalStatus(1, "Single")
    MARRIED = LkMaritalStatus(2, "Married")
    DIVORCED = LkMaritalStatus(3, "Divorced")
    WIDOWED = LkMaritalStatus(4, "Widowed")


class Gender(LookupTable):
    model = LkGender
    column_names = ("gender_id", "gender_description", "fineos_gender_description")
    WOMAN = LkGender(1, "Woman", "Female")
    MAN = LkGender(2, "Man", "Male")
    NONBINARY = LkGender(3, "Non-binary", "Neutral")
    NOT_LISTED = LkGender(4, "Gender not listed", "Unknown")
    NO_ANSWER = LkGender(5, "Prefer not to answer", "Not Provided")


class Occupation(LookupTable):
    model = LkOccupation
    column_names = ("occupation_id", "occupation_description")

    HEALTH_CARE = LkOccupation(1, "Health Care")
    SALES_CLERK = LkOccupation(2, "Sales Clerk")
    ADMINISTRATIVE = LkOccupation(3, "Administrative")
    ENGINEER = LkOccupation(4, "Engineer")


class EducationLevel(LookupTable):
    model = LkEducationLevel
    column_names = ("education_level_id", "education_level_description")


class Role(LookupTable):
    model = LkRole
    column_names = ("role_id", "role_description")

    USER = LkRole(1, "User")
    FINEOS = LkRole(2, "Fineos")
    EMPLOYER = LkRole(3, "Employer")
    PFML_CRM = LkRole(4, "PFML_CRM")
    PFML_IMAGE_ACCESS = LkRole(5, "PFML_IMAGE_ACCESS")
    IDP = LkRole(6, "IDP")


class PaymentMethod(LookupTable):
    model = LkPaymentMethod
    column_names = ("payment_method_id", "payment_method_description")

    ACH = LkPaymentMethod(1, "Elec Funds Transfer")
    CHECK = LkPaymentMethod(2, "Check")
    DEBIT = LkPaymentMethod(3, "Debit")
    PREPAID_CARD = LkPaymentMethod(4, "Prepaid Card")


class BankAccountType(LookupTable):
    model = LkBankAccountType
    column_names = ("bank_account_type_id", "bank_account_type_description")

    SAVINGS = LkBankAccountType(1, "Savings")
    CHECKING = LkBankAccountType(2, "Checking")


class PrenoteState(LookupTable):
    model = LkPrenoteState
    column_names = ("prenote_state_id", "prenote_state_description")

    PENDING_PRE_PUB = LkPrenoteState(1, "Pending - Needs to be sent to PUB")
    PENDING_WITH_PUB = LkPrenoteState(2, "Pending - Was sent to PUB")
    APPROVED = LkPrenoteState(3, "Approved")
    REJECTED = LkPrenoteState(4, "Rejected")


class SharedPaymentConstants:
    """
    A class to hold Payment-Specific constants relevant
    to more than one part of the application.
    Definining constants here allows them to be shared
    throughout the application without creating circular dependencies
    """

    # States that indicate we have sent a payment to PUB
    # and it has not yet errored.
    PAID_STATES = frozenset(
        [
            State.DELEGATED_PAYMENT_PUB_TRANSACTION_CHECK_SENT,
            State.DELEGATED_PAYMENT_PUB_TRANSACTION_EFT_SENT,
            State.DELEGATED_PAYMENT_COMPLETE,
            State.DELEGATED_PAYMENT_COMPLETE_WITH_CHANGE_NOTIFICATION,
        ]
    )
    PAID_STATE_IDS = frozenset([state.state_id for state in PAID_STATES])


class PaymentRelevantParty(LookupTable):
    model = LkPaymentRelevantParty
    column_names = ("payment_relevant_party_id", "payment_relevant_party_description")

    UNKNOWN = LkPaymentRelevantParty(1, "Unknown")
    CLAIMANT = LkPaymentRelevantParty(2, "Claimant")
    DEPARTMENT_OF_REVENUE = LkPaymentRelevantParty(3, "Department of Revenue")
    INTERNAL_REVENUE_SERVICE = LkPaymentRelevantParty(4, "Internal Revenue Service")
    EMPLOYER = LkPaymentRelevantParty(5, "Employer")


class PaymentTransactionType(LookupTable):
    model = LkPaymentTransactionType
    column_names = ("payment_transaction_type_id", "payment_transaction_type_description")

    STANDARD = LkPaymentTransactionType(1, "Standard")
    ZERO_DOLLAR = LkPaymentTransactionType(2, "Zero Dollar")
    OVERPAYMENT = LkPaymentTransactionType(3, "Overpayment")
    CANCELLATION = LkPaymentTransactionType(4, "Cancellation")
    UNKNOWN = LkPaymentTransactionType(5, "Unknown")
    EMPLOYER_REIMBURSEMENT = LkPaymentTransactionType(6, "Employer Reimbursement")
    OVERPAYMENT_ACTUAL_RECOVERY = LkPaymentTransactionType(7, "Overpayment Actual Recovery")
    OVERPAYMENT_RECOVERY = LkPaymentTransactionType(8, "Overpayment Recovery")
    OVERPAYMENT_ADJUSTMENT = LkPaymentTransactionType(9, "Overpayment Adjustment")
    OVERPAYMENT_RECOVERY_REVERSE = LkPaymentTransactionType(10, "Overpayment Recovery Reverse")
    OVERPAYMENT_RECOVERY_CANCELLATION = LkPaymentTransactionType(
        11, "Overpayment Recovery Cancellation"
    )
    FEDERAL_TAX_WITHHOLDING = LkPaymentTransactionType(12, "Federal Tax Withholding")
    STATE_TAX_WITHHOLDING = LkPaymentTransactionType(13, "State Tax Withholding")
    STANDARD_LEGACY_MMARS = LkPaymentTransactionType(14, "Standard Legacy MMARS")

    OVERPAYMENT_ACTUAL_RECOVERY_CANCELLATION = LkPaymentTransactionType(
        15, "Overpayment Actual Recovery Cancellation"
    )
    OVERPAYMENT_ADJUSTMENT_CANCELLATION = LkPaymentTransactionType(
        16, "Overpayment Adjustment Cancellation"
    )
    CHILD_SUPPORT_PAYMENT = LkPaymentTransactionType(17, "Child Support Payment")


class PaymentCheckStatus(LookupTable):
    model = LkPaymentCheckStatus
    column_names = ("payment_check_status_id", "payment_check_status_description")

    PAID = LkPaymentCheckStatus(1, "Paid")
    OUTSTANDING = LkPaymentCheckStatus(2, "Outstanding")
    FUTURE = LkPaymentCheckStatus(3, "Future")
    VOID = LkPaymentCheckStatus(4, "Void")
    STALE = LkPaymentCheckStatus(5, "Stale")
    STOP = LkPaymentCheckStatus(6, "Stop")


class Title(LookupTable):
    model = LkTitle
    column_names = ("title_id", "title_description")

    UNKNOWN = LkTitle(1, "Unknown")
    MR = LkTitle(2, "Mr")
    MRS = LkTitle(3, "Mrs")
    MISS = LkTitle(4, "Miss")
    MS = LkTitle(5, "Ms")
    DR = LkTitle(6, "Dr")
    MADAM = LkTitle(7, "Madam")
    SIR = LkTitle(8, "Sir")


class EligibilityDecision(LookupTable):
    model = LkEligibilityDecision
    column_names = ("eligibility_decision_id", "eligibility_decision_description")

    CLAIMANT_WAGES_UNDER_MINIMUM = LkEligibilityDecision(1, "claimant wages under minimum")
    FINANCIALLY_ELIGIBLE = LkEligibilityDecision(2, "financially eligible")
    CLAIMANT_WAGES_FAILED_30X_RULE = LkEligibilityDecision(3, "claimant wages failed 30x rule")
    OPT_IN_QUARTERLY_CONTRIBUTIONS_NOT_MET = LkEligibilityDecision(
        4, "opt-in quarterly contributions not met"
    )
    UNKNOWN = LkEligibilityDecision(5, "unknown")


class LeaveRequestDecision(LookupTable):
    # Item descriptions here map to Fineos Enum domain #6821
    model = LkLeaveRequestDecision
    column_names = ("leave_request_decision_id", "leave_request_decision_description")

    PENDING = LkLeaveRequestDecision(1, "Pending")
    IN_REVIEW = LkLeaveRequestDecision(2, "In Review")
    APPROVED = LkLeaveRequestDecision(3, "Approved")
    DENIED = LkLeaveRequestDecision(4, "Denied")
    CANCELLED = LkLeaveRequestDecision(5, "Cancelled")
    WITHDRAWN = LkLeaveRequestDecision(6, "Withdrawn")
    PROJECTED = LkLeaveRequestDecision(7, "Projected")
    VOIDED = LkLeaveRequestDecision(8, "Voided")
    UNKNOWN = LkLeaveRequestDecision(9, "Unknown")


class UserLeaveAdministratorActionType(LookupTable):
    model = LkUserLeaveAdministratorActionType
    column_names = (
        "user_leave_administrator_action_type_id",
        "user_leave_administrator_action_type_description",
    )

    ADD = LkUserLeaveAdministratorActionType(1, "Add")
    DEACTIVATE = LkUserLeaveAdministratorActionType(2, "Deactivate")


class WagesAndContributionsDatasource(LookupTable):
    model = LkWagesAndContributionsDatasource
    column_names = (
        "wages_and_contributions_datasource_id",
        "wages_and_contributions_datasource_description",
    )

    DFML_REPORTED_WAGES = LkWagesAndContributionsDatasource(1, "DFML reported wages")
    EXEMPTION_FILED = LkWagesAndContributionsDatasource(2, "Exemption filed")
    DUA_REPORTED_WAGES = LkWagesAndContributionsDatasource(3, "DUA reported wages")
    PENDING_FILING = LkWagesAndContributionsDatasource(4, "Pending filing")
    UNKNOWN = LkWagesAndContributionsDatasource(5, "Unknown")
