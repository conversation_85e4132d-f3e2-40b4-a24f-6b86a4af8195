import csv
import io
import os
import pathlib
import re
import tempfile
from datetime import date, datetime, timedelta
from decimal import Decimal
from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy import Nullable, Row, and_, not_, or_

import massgov.pfml.api.util.state_log_util as state_log_util
import massgov.pfml.db as db
import massgov.pfml.util.batch.log as batch_log
import massgov.pfml.util.files as file_util
import massgov.pfml.util.logging as logging
from massgov.pfml.db.lookup_data.absences import AbsenceStatus
from massgov.pfml.db.lookup_data.employees import State
from massgov.pfml.db.lookup_data.reference_file_type import ReferenceFileType
from massgov.pfml.db.models.employees import Claim, DuaReductionPayment, Employee, Employer
from massgov.pfml.db.models.reference_file.dua_reduction_payment_reference_file import (
    DuaReductionPaymentReferenceFile,
)
from massgov.pfml.db.models.reference_file.reference_file import ReferenceFile
from massgov.pfml.delegated_payments.delegated_payments_util import move_file_and_update_ref_file
from massgov.pfml.reductions.common import (
    HISTORICAL_ABSENCE_CASE_PREFIX,
    AgencyLoadResult,
    get_claimants_for_outbound,
)
from massgov.pfml.reductions.config import get_moveit_config, get_s3_config
from massgov.pfml.util.datetime import get_now_us_eastern, utcnow
from massgov.pfml.util.files import create_csv_from_list, upload_to_s3, write_file
from massgov.pfml.util.sftp_s3_transfer import (
    SftpS3TransferConfig,
    copy_from_sftp_to_s3_and_archive_files,
    copy_to_sftp_and_archive_s3_files,
)

logger = logging.get_logger(__name__)

DuaReductionPaymentAndClaim = Row[tuple[DuaReductionPayment, Optional[Claim], Optional[Employer]]]


class Metrics:
    PENDING_DUA_PAYMENT_FILES_COUNT = "pending_dua_payment_files_count"
    SUCCESSFUL_DUA_PAYMENT_FILES_COUNT = "successful_dua_payment_files_count"
    ERRORED_DUA_PAYMENT_FILES_COUNT = "errored_dua_payment_files_count"
    NEW_DUA_PAYMENT_ROW_COUNT = "new_dua_payment_row_count"
    TOTAL_DUA_PAYMENT_ROW_COUNT = "total_dua_payment_row_count"
    CLAIMANTS_SENT_TO_DUA_COUNT = "claimants_sent_to_dua_count"
    IMPORT_LOG_STATUS = "import_log_status"
    DUA_PAYMENT_FILES_DOWNLOADED_COUNT = "dua_payment_files_downloaded_count"
    REPORT_DUA_PAYMENTS_TO_DFML_ROW_COUNT = "report_dua_payments_to_dfml_row_count"
    REPORT_DUA_PAYMENTS_TO_DFML_CONSOLIDATED_ROW_COUNT = (
        "report_dua_payments_to_dfml_consolidated_row_count"
    )
    SKIPPED_EXISTING_DUA_PAYMENT_ROW_COUNT = "skipped_existing_dua_payment_row_count"
    OUTPUT_FILE = "output_file"


class Constants:

    CLAIMANT_LIST_FILENAME_PREFIX = "DFML_CLAIMANTS_FOR_DUA_"
    CLAIMANT_LIST_FILENAME_TIME_FORMAT = "%Y%m%d%H%M"
    # The "DFML Report" is the "payment list" file. Some things will refer to
    # "payment list" others will use "payment report" or just "report", they are
    # the all the same thing, means these files.
    PAYMENT_LIST_FILENAME_PREFIX = "DUA_DFML_"
    PAYMENT_LIST_FILENAME_TIME_FORMAT = "%Y%m%d%H%M"
    PAYMENT_REPORT_TIME_FORMAT = "%m/%d/%Y"

    # Originally we sent DUA one row per absence case and this CASE_ID field
    # held the absence case id.
    #
    # But we switched to sending one row per claimant (as they may have multiple
    # cases over time), so the field has been repurposed to hold the customer
    # number to avoid DUA needing to change anything on their end.
    CASE_ID_FIELD = "CASE_ID"

    # We changed the primary key from being by absence case to being by customer
    # number. This is the first column in the DUA report.
    CUSTOMER_ID_FIELD = "CUSTOMER_ID"
    EMPR_FEIN_FIELD = "EMPR_FEIN"
    WARRANT_DT_OUTBOUND_DFML_REPORT_FIELD = "PAYMENT_DATE"
    RQST_WK_DT_OUTBOUND_DFML_REPORT_FIELD = "BENEFIT_WEEK_START_DATE"
    NUMBER_OF_CONSOLIDATED_WEEKS_FIELD = "NUMBER_OF_WEEKS"
    WBA_ADDITIONS_OUTBOUND_DFML_REPORT_FIELD = "GROSS_PAYMENT_AMOUNT"
    PAID_AM_OUTBOUND_DFML_REPORT_FIELD = "NET_PAYMENT_AMOUNT"
    FRAUD_IND_FIELD = "FRAUD_IND"
    BYB_DT_FIELD = "BYB_DT"
    BYE_DT_FIELD = "BYE_DT"
    DATE_PAYMENT_ADDED_TO_REPORT_FIELD = "DATE_PAYMENT_ADDED_TO_REPORT"
    EMPLOYER_CUSTOMER_NUMBER = "EMPLOYER_CUSTOMER_NUMBER"
    BENEFIT_START_DATE_FIELD = "START_DATE"
    SSN_FIELD = "SSN"
    WARRANT_DT_FIELD = "WARRANT_DT"
    RQST_WK_DT_FIELD = "RQST_WK_DT"
    WBA_ADDITIONS_FIELD = "WBA_ADDITIONS"
    PAID_AM_FIELD = "PAID_AM"
    ABSENCE_CASE_ID_FIELD = "ABSENCE_CASE_ID"
    ABSENCE_CASE_STATUS_FIELD = "ABSENCE_CASE_STATUS"
    ABSENCE_CASE_PERIOD_START_FIELD = "ABSENCE_PERIOD_START_DATE"
    ABSENCE_CASE_PERIOD_END_FIELD = "ABSENCE_PERIOD_END_DATE"

    CLAIMANT_LIST_FIELDS = [CASE_ID_FIELD, SSN_FIELD, BENEFIT_START_DATE_FIELD]

    DFML_REPORT_CSV_COLUMN_TO_TABLE_DATA_FIELD_MAP = {
        CUSTOMER_ID_FIELD: "fineos_customer_number",
        WARRANT_DT_OUTBOUND_DFML_REPORT_FIELD: "payment_date",
        RQST_WK_DT_OUTBOUND_DFML_REPORT_FIELD: "request_week_begin_date",
        NUMBER_OF_CONSOLIDATED_WEEKS_FIELD: "number_of_weeks",
        WBA_ADDITIONS_OUTBOUND_DFML_REPORT_FIELD: "gross_payment_amount_cents",
        PAID_AM_OUTBOUND_DFML_REPORT_FIELD: "payment_amount_cents",
        FRAUD_IND_FIELD: "fraud_indicator",
        BYB_DT_FIELD: "benefit_year_begin_date",
        BYE_DT_FIELD: "benefit_year_end_date",
        DATE_PAYMENT_ADDED_TO_REPORT_FIELD: "created_at",
        ABSENCE_CASE_ID_FIELD: "claim.fineos_absence_id",
        ABSENCE_CASE_PERIOD_START_FIELD: "claim.claim_start_date",
        ABSENCE_CASE_PERIOD_END_FIELD: "claim.claim_end_date",
        ABSENCE_CASE_STATUS_FIELD: "claim.fineos_absence_status.absence_status_description",
        EMPLOYER_CUSTOMER_NUMBER: "employer.fineos_employer_id",
    }

    DUA_PAYMENT_CSV_COLUMN_TO_TABLE_DATA_FIELD_MAP = {
        CASE_ID_FIELD: "fineos_customer_number",
        EMPR_FEIN_FIELD: "employer_fein",
        WARRANT_DT_FIELD: "payment_date",
        RQST_WK_DT_FIELD: "request_week_begin_date",
        WBA_ADDITIONS_FIELD: "gross_payment_amount_cents",
        PAID_AM_FIELD: "payment_amount_cents",
        FRAUD_IND_FIELD: "fraud_indicator",
        BYB_DT_FIELD: "benefit_year_begin_date",
        BYE_DT_FIELD: "benefit_year_end_date",
    }

    EXCLUDE_PAYMENTS_BEFORE_DATE = date(2020, 12, 1)


def copy_claimant_list_to_moveit(db_session: db.Session) -> None:
    s3_config = get_s3_config()
    moveit_config = get_moveit_config()

    transfer_config = SftpS3TransferConfig(
        s3_bucket_uri=s3_config.s3_bucket_uri,
        source_dir=s3_config.s3_dua_outbound_directory_path,
        archive_dir=s3_config.s3_dua_archive_directory_path,
        dest_dir=moveit_config.moveit_dua_outbound_path,
        sftp_uri=moveit_config.moveit_sftp_uri,
        ssh_key_password=moveit_config.moveit_ssh_key_password,
        ssh_key=moveit_config.moveit_ssh_key,
    )

    copied_reference_files = copy_to_sftp_and_archive_s3_files(transfer_config, db_session)
    for ref_file in copied_reference_files:
        state_log_util.create_finished_state_log(
            associated_model=ref_file,
            end_state=State.DUA_CLAIMANT_LIST_SUBMITTED,
            outcome=state_log_util.build_outcome("Sent list of claimants to DUA via MoveIt"),
            db_session=db_session,
        )

    # Commit the StateLogs we created to the database.
    db_session.commit()


def _format_claimants_for_dua_claimant_list(
    claimants: List[Employee],
) -> Tuple[List[Dict[str, str]], int]:
    claimants_info = []

    failed_claimants: int = 0

    for employee in claimants:
        info: Dict[str, str] = {}
        try:
            fineos_customer_number = employee.fineos_customer_number
            tax_id = employee.tax_identifier

            if not (fineos_customer_number and tax_id):
                logger.warning(
                    "Employee missing required information. Skipping.",
                    extra={
                        "employee_id": employee.employee_id,
                        "has_fineos_customer_number": bool(fineos_customer_number),
                        "has_tax_id": bool(tax_id),
                    },
                )
                continue

            info[Constants.CASE_ID_FIELD] = fineos_customer_number
            info[Constants.SSN_FIELD] = tax_id.tax_identifier.replace("-", "")
            info[Constants.BENEFIT_START_DATE_FIELD] = (
                datetime.today() - timedelta(days=100)
            ).strftime("%Y%m%d")
        except Exception as error:
            failed_claimants += 1
            logger.warning(
                f"DUA Claimant export failed for employee {employee.employee_id} with error {error}"
            )
        else:
            claimants_info.append(info)

    return claimants_info, failed_claimants


def get_failed_claimants_threshold_pct() -> int:
    return int(os.environ.get("FAILED_CLAIMANTS_THRESHOLD_PCT", 5))


def _get_claimants_info_csv_path(
    claimants: List[Dict], failed_claimants: int
) -> Tuple[pathlib.Path, int]:
    file_name = Constants.CLAIMANT_LIST_FILENAME_PREFIX + get_now_us_eastern().strftime(
        Constants.CLAIMANT_LIST_FILENAME_TIME_FORMAT
    )

    directory = tempfile.mkdtemp()
    csv_filepath = os.path.join(directory, f"{file_name}.csv")

    with write_file(csv_filepath) as csv_file:
        writer = csv.DictWriter(
            csv_file, fieldnames=Constants.CLAIMANT_LIST_FIELDS, extrasaction="ignore"
        )
        writer.writeheader()
        for data in claimants:
            try:
                writer.writerow(data)
            except Exception as error:
                failed_claimants += 1
                logger.warning(
                    f"DUA Claimant export failed for fineos customer number {data[Constants.CASE_ID_FIELD]} with error {error}"
                )

    return pathlib.Path(csv_filepath), failed_claimants


def validate_failed_claimants(
    claimants: int, failed_claimants: int, log_entry: batch_log.LogEntry
) -> None:
    failed_claimants_threshold_pct = get_failed_claimants_threshold_pct()
    failed_pct = 100 * float(failed_claimants) / float(claimants)
    if failed_pct > failed_claimants_threshold_pct:
        error = f"DUA's failed records count is greater than threshold value {failed_claimants_threshold_pct}%"
        logger.error(error)
        raise ValueError(error)
    else:
        logger.warning(
            f"DUA encountered partial failure less than threshold value {failed_claimants_threshold_pct}%"
        )
        log_entry.set_metrics({Metrics.IMPORT_LOG_STATUS: "warning"})


def create_list_of_claimants(db_session: db.Session, log_entry: batch_log.LogEntry) -> None:
    config = get_s3_config()

    claimants = get_claimants_for_outbound(db_session)

    dua_claimant_info, failed_claimants = _format_claimants_for_dua_claimant_list(claimants)

    claimant_info_path, failed_claimants = _get_claimants_info_csv_path(
        dua_claimant_info, failed_claimants
    )

    if failed_claimants > 0:
        validate_failed_claimants(len(claimants), failed_claimants, log_entry)

    log_entry.set_metrics({Metrics.CLAIMANTS_SENT_TO_DUA_COUNT: len(dua_claimant_info)})

    s3_dest = os.path.join(
        config.s3_bucket_uri, config.s3_dua_outbound_directory_path, claimant_info_path.name
    )
    file_util.upload_to_s3(str(claimant_info_path), s3_dest)
    log_entry.set_metrics({Metrics.OUTPUT_FILE: s3_dest})
    # Update ReferenceFile and StateLog Tables
    ref_file = ReferenceFile(
        file_location=s3_dest,
        reference_file_type_id=ReferenceFileType.DUA_CLAIMANT_LIST.reference_file_type_id,
    )
    db_session.add(ref_file)
    # commit ref_file to db
    db_session.commit()

    # Update StateLog Tables
    state_log_util.create_finished_state_log(
        associated_model=ref_file,
        end_state=State.DUA_CLAIMANT_LIST_CREATED,
        outcome=state_log_util.build_outcome("Created claimant list for DUA"),
        db_session=db_session,
    )

    # commit StateLog to db
    db_session.commit()


def load_new_dua_payments(
    db_session: db.Session, log_entry: batch_log.LogEntry, file_path: Optional[str] = None
) -> AgencyLoadResult:
    s3_config = get_s3_config()
    pending_dir = os.path.join(s3_config.s3_bucket_uri, s3_config.s3_dua_pending_directory_path)
    archive_dir = os.path.join(s3_config.s3_bucket_uri, s3_config.s3_dua_archive_directory_path)
    error_dir = os.path.join(s3_config.s3_bucket_uri, s3_config.s3_dfml_error_directory_path)
    move_files = True
    ref_files = []
    result = AgencyLoadResult()
    if file_path:
        ref_file = ReferenceFile(
            file_location=file_path,
            reference_file_type_id=ReferenceFileType.DUA_PAYMENT_LIST.reference_file_type_id,
        )
        db_session.add(ref_file)
        ref_files.append(ref_file)
        if not file_util.is_s3_path(file_path):
            move_files = False

    else:
        ref_files = _get_pending_dua_payment_reference_files(pending_dir, db_session)

    for ref_file in ref_files:
        log_entry.increment(Metrics.PENDING_DUA_PAYMENT_FILES_COUNT)
        result.found_pending_files = True

        try:
            new_row_count, total_row_count, skipped_row_count = (
                _load_dua_payment_from_reference_file(ref_file, archive_dir, db_session, move_files)
            )
            log_entry.increment(Metrics.SUCCESSFUL_DUA_PAYMENT_FILES_COUNT)
            log_entry.increment(Metrics.NEW_DUA_PAYMENT_ROW_COUNT, new_row_count)
            log_entry.increment(Metrics.TOTAL_DUA_PAYMENT_ROW_COUNT, total_row_count)
            log_entry.increment(Metrics.SKIPPED_EXISTING_DUA_PAYMENT_ROW_COUNT, skipped_row_count)

        except Exception:
            if move_files:
                # Move to error directory and update ReferenceFile.
                filename = os.path.basename(ref_file.file_location)
                dest_path = os.path.join(error_dir, filename)
                move_file_and_update_ref_file(db_session, dest_path, ref_file)

                # transition to an error state
                state_log_util.create_finished_state_log(
                    associated_model=ref_file,
                    end_state=State.DUA_PAYMENT_LIST_ERROR_SAVE_TO_DB,
                    outcome=state_log_util.build_outcome(
                        "Error loading DUA payment file into database"
                    ),
                    db_session=db_session,
                )
                db_session.commit()

            log_entry.increment(Metrics.ERRORED_DUA_PAYMENT_FILES_COUNT)

            # Log exceptions but continue attempting to load other payment files into the database.
            logger.exception(
                "Failed to load new DUA payments to database from file",
                extra={
                    "file_location": ref_file.file_location,
                    "reference_file_id": ref_file.reference_file_id,
                },
            )
    return result


def _load_dua_payment_from_reference_file(
    ref_file: ReferenceFile, archive_directory: str, db_session: db.Session, move_files: bool
) -> Tuple[int, int, int]:
    new_row_count = 0
    total_row_count = 0

    # Load to database.
    with file_util.open_stream(ref_file.file_location) as f:
        new_row_count, total_row_count, skipped_row_count = _load_new_rows_from_file(f, db_session)

    # Move to archive directory and update ReferenceFile.
    if move_files:
        filename = os.path.basename(ref_file.file_location)
        dest_path = os.path.join(archive_directory, filename)
        file_util.rename_file(ref_file.file_location, dest_path)
        ref_file.file_location = dest_path
        db_session.commit()

        # Create StateLog entry.
        state_log_util.create_finished_state_log(
            associated_model=ref_file,
            end_state=State.DUA_PAYMENT_LIST_SAVED_TO_DB,
            outcome=state_log_util.build_outcome("Loaded DUA payment file into database"),
            db_session=db_session,
        )
    db_session.commit()

    return new_row_count, total_row_count, skipped_row_count


def _get_pending_dua_payment_reference_files(
    pending_directory: str, db_session: db.Session
) -> List[ReferenceFile]:
    # Add a trailing % so that we match anything within the directory.
    return (
        db_session.query(ReferenceFile)
        .filter(
            ReferenceFile.reference_file_type_id
            == ReferenceFileType.DUA_PAYMENT_LIST.reference_file_type_id,
            ReferenceFile.file_location.like(pending_directory + "%"),
        )
        .all()
    )


def _convert_dict_with_csv_keys_to_db_keys(csv_data: Dict[str, Any]) -> Dict[str, Any]:
    # Load empty strings as null values.
    return {
        Constants.DUA_PAYMENT_CSV_COLUMN_TO_TABLE_DATA_FIELD_MAP[k]: None if v == "" else v
        for k, v in csv_data.items()
    }


def _get_matching_dua_reduction_payments(
    db_data: Dict[str, Any], db_session: db.Session
) -> List[DuaReductionPayment]:
    # https://stackoverflow.com/questions/7604967/sqlalchemy-build-query-filter-dynamically-from-dict
    query = db_session.query(DuaReductionPayment)
    for attr, value in db_data.items():
        # Empty fields are read as empty strings. Convert those values to nulls for the database.
        if value == "":
            value = None

        query = query.filter(getattr(DuaReductionPayment, attr) == value)

    return query.all()


def _load_new_rows_from_file(file: io.StringIO, db_session: db.Session) -> Tuple[int, int, int]:
    new_row_count = 0
    total_row_count = 0
    skipped_row_count = 0

    for row in csv.DictReader(file):
        total_row_count += 1
        db_data = _convert_dict_with_csv_keys_to_db_keys(row)
        if len(_get_matching_dua_reduction_payments(db_data, db_session)) == 0:
            dua_reduction_payment = DuaReductionPayment(**db_data)
            db_session.add(dua_reduction_payment)

            new_row_count += 1
        else:
            logger.info(
                "DUA payment already exists in database",
                extra={"dua_payment": db_data},
            )
            skipped_row_count += 1

    return new_row_count, total_row_count, skipped_row_count

    # Commit these changes to the database after we've updated the ReferenceFile's file_location
    # in the calling code.


def download_payment_list_from_moveit(db_session: db.Session, log_entry: batch_log.LogEntry) -> int:
    s3_config = get_s3_config()
    moveit_config = get_moveit_config()

    transfer_config = SftpS3TransferConfig(
        s3_bucket_uri=s3_config.s3_bucket_uri,
        source_dir=moveit_config.moveit_dua_inbound_path,
        archive_dir=moveit_config.moveit_dua_archive_path,
        dest_dir=s3_config.s3_dua_pending_directory_path,
        sftp_uri=moveit_config.moveit_sftp_uri,
        ssh_key_password=moveit_config.moveit_ssh_key_password,
        ssh_key=moveit_config.moveit_ssh_key,
        regex_filter=re.compile(r"DUA_DFML_\d+.csv"),
    )

    copied_reference_files = copy_from_sftp_to_s3_and_archive_files(transfer_config, db_session)
    for ref_file in copied_reference_files:
        ref_file.reference_file_type_id = ReferenceFileType.DUA_PAYMENT_LIST.reference_file_type_id
        state_log_util.create_finished_state_log(
            associated_model=ref_file,
            end_state=State.DUA_PAYMENT_LIST_SAVED_TO_S3,
            outcome=state_log_util.build_outcome("Saved DUA payment list to S3"),
            db_session=db_session,
        )

    # Commit the ReferenceFile changes and StateLogs we created to the database.
    db_session.commit()

    if len(copied_reference_files) == 0:
        logger.info("No new payment files were detected in the SFTP server.")
    else:
        logger.info(
            "New payment files were detected in the SFTP server.",
            extra={"reference_file_count": len(copied_reference_files)},
        )

    log_entry.set_metrics({Metrics.DUA_PAYMENT_FILES_DOWNLOADED_COUNT: len(copied_reference_files)})
    return len(copied_reference_files)


def _convert_cent_to_dollars(cent: Optional[int] = 0) -> Decimal:
    cent_str = f"{cent or 0 :02}"
    dollar = cent_str[:-2] + "." + cent_str[-2:]
    return Decimal(dollar)


def _get_non_submitted_reduction_payments(
    db_session: db.Session,
) -> List[DuaReductionPaymentAndClaim]:
    """
    Include only payment records that meet the following criteria:
    - Created within the last 90 days
    - Benefit dates are within period since PFML has been active (after EXCLUDE_PAYMENTS_BEFORE_DATE)
    - For an associated claim
        - It does not represent a historical absence case
        - The absence period dates contain the DUA benefit dates
    """
    ninety_days_ago = utcnow().date() - timedelta(days=90)
    return (
        db_session.query(DuaReductionPayment, Nullable(Claim), Nullable(Employer))
        .outerjoin(
            Employee, DuaReductionPayment.fineos_customer_number == Employee.fineos_customer_number
        )
        .outerjoin(Claim, Claim.employee_id == Employee.employee_id)
        .outerjoin(Employer, Employer.employer_id == Claim.employer_id)
        .filter(DuaReductionPayment.created_at >= ninety_days_ago)
        .filter(
            DuaReductionPayment.request_week_begin_date >= Constants.EXCLUDE_PAYMENTS_BEFORE_DATE
        )
        .filter(
            or_(
                Claim.claim_id.is_(None),
                and_(
                    not_(Claim.fineos_absence_id.startswith(HISTORICAL_ABSENCE_CASE_PREFIX)),
                    DuaReductionPayment.request_week_begin_date.between(
                        Claim.claim_start_date, Claim.claim_end_date
                    ),
                ),
            )
        )
        .order_by(
            DuaReductionPayment.fineos_customer_number,
            Claim.fineos_absence_id,
            DuaReductionPayment.request_week_begin_date,
        )
        .all()
    )


def _format_date_for_report(raw_date: Optional[date]) -> str:
    if raw_date is None:
        return ""

    return raw_date.strftime(Constants.PAYMENT_REPORT_TIME_FORMAT)


def _format_reduction_payments_for_report(
    reduction_payments: List[DuaReductionPaymentAndClaim],
) -> List[Dict]:
    if len(reduction_payments) == 0:
        return [
            {
                field: "NO NEW PAYMENTS"
                for field in Constants.DFML_REPORT_CSV_COLUMN_TO_TABLE_DATA_FIELD_MAP.keys()
            }
        ]

    report_entries: List[Dict] = []

    for payment, claim, employer in reduction_payments:
        entry = {
            Constants.CUSTOMER_ID_FIELD: payment.fineos_customer_number,
            Constants.WARRANT_DT_OUTBOUND_DFML_REPORT_FIELD: _format_date_for_report(
                payment.payment_date
            ),
            Constants.RQST_WK_DT_OUTBOUND_DFML_REPORT_FIELD: _format_date_for_report(
                payment.request_week_begin_date
            ),
            Constants.NUMBER_OF_CONSOLIDATED_WEEKS_FIELD: 1,
            Constants.WBA_ADDITIONS_OUTBOUND_DFML_REPORT_FIELD: _convert_cent_to_dollars(
                payment.gross_payment_amount_cents
            ),
            Constants.PAID_AM_OUTBOUND_DFML_REPORT_FIELD: _convert_cent_to_dollars(
                payment.payment_amount_cents
            ),
            Constants.FRAUD_IND_FIELD: payment.fraud_indicator,
            Constants.BYB_DT_FIELD: _format_date_for_report(payment.benefit_year_begin_date),
            Constants.BYE_DT_FIELD: _format_date_for_report(payment.benefit_year_end_date),
            Constants.DATE_PAYMENT_ADDED_TO_REPORT_FIELD: _format_date_for_report(
                payment.created_at
            ),
        }

        if claim is not None:
            entry.update(
                {
                    Constants.ABSENCE_CASE_ID_FIELD: claim.fineos_absence_id,
                    Constants.ABSENCE_CASE_STATUS_FIELD: (
                        AbsenceStatus.get_description(claim.fineos_absence_status_id)
                        if claim.fineos_absence_status_id
                        else None
                    ),
                    Constants.ABSENCE_CASE_PERIOD_START_FIELD: _format_date_for_report(
                        claim.claim_start_date
                    ),
                    Constants.ABSENCE_CASE_PERIOD_END_FIELD: _format_date_for_report(
                        claim.claim_end_date
                    ),
                }
            )

        if employer is not None:
            entry.update({Constants.EMPLOYER_CUSTOMER_NUMBER: employer.fineos_employer_id})

        prior_entry = report_entries[-1] if len(report_entries) > 0 else None
        if prior_entry and _should_consolidate(prior_entry, entry):
            prior_entry[Constants.NUMBER_OF_CONSOLIDATED_WEEKS_FIELD] += 1
        else:
            report_entries.append(entry)

    return report_entries


def _should_consolidate(prior_entry: Dict, entry: Dict) -> bool:
    """
    Returns True if entry is for the next benefit week after prior_entry, and
    CUSTOMER_ID, GROSS_PAYMENT_AMOUNT, NET_PAYMENT_AMOUNT, FRAUD_IND, and
    ABSENCE_CASE_ID are all identical.
    """
    customer_id_matches = (
        prior_entry[Constants.CUSTOMER_ID_FIELD] == entry[Constants.CUSTOMER_ID_FIELD]
    )

    prior_entry_benefit_week_formatted = prior_entry[
        Constants.RQST_WK_DT_OUTBOUND_DFML_REPORT_FIELD
    ]
    prior_entry_benefit_week = datetime.strptime(
        prior_entry_benefit_week_formatted, Constants.PAYMENT_REPORT_TIME_FORMAT
    )
    # E.g., if we've already consolidated 2 weeks, we want to look 2 weeks ahead
    weeks_ahead = prior_entry[Constants.NUMBER_OF_CONSOLIDATED_WEEKS_FIELD]
    next_benefit_week = prior_entry_benefit_week + timedelta(weeks=weeks_ahead)
    next_benefit_week_formatted = _format_date_for_report(next_benefit_week)
    weeks_match = (
        entry[Constants.RQST_WK_DT_OUTBOUND_DFML_REPORT_FIELD] == next_benefit_week_formatted
    )

    payment_fields_match = (
        prior_entry[Constants.WBA_ADDITIONS_OUTBOUND_DFML_REPORT_FIELD]
        == entry[Constants.WBA_ADDITIONS_OUTBOUND_DFML_REPORT_FIELD]
        and prior_entry[Constants.PAID_AM_OUTBOUND_DFML_REPORT_FIELD]
        == entry[Constants.PAID_AM_OUTBOUND_DFML_REPORT_FIELD]
        and prior_entry[Constants.FRAUD_IND_FIELD] == entry[Constants.FRAUD_IND_FIELD]
    )

    # Neither entry has a claim OR both entries have a claim and the claim IDs are the same
    claims_match = (
        Constants.ABSENCE_CASE_ID_FIELD not in prior_entry
        and Constants.ABSENCE_CASE_ID_FIELD not in entry
    ) or (
        Constants.ABSENCE_CASE_ID_FIELD in prior_entry
        and Constants.ABSENCE_CASE_ID_FIELD in entry
        and prior_entry[Constants.ABSENCE_CASE_ID_FIELD] == entry[Constants.ABSENCE_CASE_ID_FIELD]
    )

    return customer_id_matches and weeks_match and payment_fields_match and claims_match


def _get_new_dua_payments_to_dfml_report_csv_path(
    reduction_payments_info: List[Dict],
) -> pathlib.Path:
    file_name = Constants.PAYMENT_LIST_FILENAME_PREFIX + get_now_us_eastern().strftime(
        Constants.PAYMENT_LIST_FILENAME_TIME_FORMAT
    )
    return create_csv_from_list(
        reduction_payments_info,
        list(Constants.DFML_REPORT_CSV_COLUMN_TO_TABLE_DATA_FIELD_MAP.keys()),
        file_name,
    )


def create_report_new_dua_payments_to_dfml(
    db_session: db.Session, log_entry: batch_log.LogEntry
) -> None:
    config = get_s3_config()

    # get non-submitted payments
    non_submitted_payments = _get_non_submitted_reduction_payments(db_session)

    # get reduction payment report info
    reduction_payment_report_info = _format_reduction_payments_for_report(non_submitted_payments)

    # get csv path for reduction report
    reduction_report_csv_path = _get_new_dua_payments_to_dfml_report_csv_path(
        reduction_payment_report_info
    )

    log_entry.set_metrics(
        {Metrics.REPORT_DUA_PAYMENTS_TO_DFML_ROW_COUNT: len(non_submitted_payments)}
    )

    log_entry.set_metrics(
        {
            Metrics.REPORT_DUA_PAYMENTS_TO_DFML_CONSOLIDATED_ROW_COUNT: len(
                reduction_payment_report_info
            )
        }
    )

    # Upload info to s3
    s3_dest = os.path.join(
        config.s3_bucket_uri, config.s3_dfml_outbound_directory_path, reduction_report_csv_path.name
    )
    upload_to_s3(str(reduction_report_csv_path), s3_dest)

    # Create ReferenceFile
    ref_file = ReferenceFile(
        file_location=s3_dest,
        reference_file_type_id=ReferenceFileType.DUA_REDUCTION_REPORT_FOR_DFML.reference_file_type_id,
    )
    db_session.add(ref_file)
    db_session.commit()

    unique_reduction_payments = {
        p.dua_reduction_payment_id: p for p, _, _ in non_submitted_payments
    }.values()

    # Create objects that link DuaReductionPayments to the ReferenceFile.
    for unique_reduction_payment in unique_reduction_payments:
        link_obj = DuaReductionPaymentReferenceFile(
            dua_reduction_payment=unique_reduction_payment, reference_file=ref_file
        )
        db_session.add(link_obj)

    # Update StateLog Tables
    state_log_util.create_finished_state_log(
        associated_model=ref_file,
        end_state=State.DUA_REPORT_FOR_DFML_CREATED,
        outcome=state_log_util.build_outcome("Created payments DFML report for DUA"),
        db_session=db_session,
    )

    # commit StateLog to db
    db_session.commit()
