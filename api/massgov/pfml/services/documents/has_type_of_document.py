import massgov.pfml.db as db
import massgov.pfml.db.queries.documents as document_queries
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.documents import LkDocumentType
from massgov.pfml.services.documents.get_document_service import GetDocumentService


def has_type_of_fineos_document(
    doc_types: list[LkDocumentType], db_session: db.Session, application: Application
) -> bool:
    """Helper to retrieve if an application has a particular type of document associated with it. Looks
    only at the document type related to FINEOS document types and not PFML document types"""

    has_doc = (
        document_queries.get_first_fineos_document_in_types(db_session, application, doc_types)
        is not None
    )

    if has_doc:
        return True

    # Check if a document has been uploaded directly to FINEOS and is not in the PFML database.
    fineos_documents = GetDocumentService(db_session).get_documents_from_fineos_for_application(
        application
    )

    doc_type_descriptions = [doc_type.document_type_description.lower() for doc_type in doc_types]

    return any(
        doc.document_type.lower() in doc_type_descriptions
        for doc in fineos_documents
        if doc.document_type is not None
    )
