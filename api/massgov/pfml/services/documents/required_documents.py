from typing import cast

import massgov.pfml.db as db
import massgov.pfml.fineos
from massgov.pfml.api.constants.documents import ABSENCE_REASON_TO_DOCUMENT_TYPE_MAPPING
from massgov.pfml.api.models.claims.responses import DocumentRequirement
from massgov.pfml.api.models.documents.common import DocumentType
from massgov.pfml.api.models.documents.responses import DocumentResponse
from massgov.pfml.api.services import fineos_actions
from massgov.pfml.db.lookup_data.absences import AbsenceReason
from massgov.pfml.db.lookup_data.applications import LeaveReason
from massgov.pfml.db.models.absences import AbsencePeriod
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.employees import Claim
from massgov.pfml.services.documents.get_document_service import GetDocumentService


class DocumentRequirementService:
    def __init__(self, db_session=None, fineos=None):
        self._db_session = db_session
        self._fineos = fineos
        self.get_document_service = GetDocumentService(db_session=db_session)

    @property
    def db_session(self):
        if self._db_session is None:
            self._db_session = db.init(sync_lookups=True)
        return self._db_session

    @property
    def fineos(self):
        if self._fineos is None:
            self._fineos = massgov.pfml.fineos.create_client()
        return self._fineos

    def are_all_required_documents_received(
        self, application: Application, fineos_web_id: str | None = None
    ) -> bool:
        # An application without a claim is not in a state to have documents.
        if application.claim is None:
            return False

        document_types = self.get_unreceived_required_document_types(application, fineos_web_id)
        return len(document_types) == 0

    def get_unreceived_required_document_types(
        self, application: Application, fineos_web_id: str | None = None
    ) -> list[DocumentType]:
        if application.claim is None:
            raise ValueError(
                "Cannot get unreceived required document types for application "
                f"{application.application_id}. The application does not have a claim."
            )

        if not fineos_web_id:
            fineos_web_id = fineos_actions.get_or_register_employee_fineos_web_id(
                self.fineos, application, self.db_session
            )

        fineos_absence_id = application.fineos_absence_id
        outstanding_evidence = self.fineos.get_outstanding_evidence(
            fineos_web_id, fineos_absence_id
        )

        required_document_types = self.get_required_document_types_for_application(application)

        unreceived_evidence = [
            cast(DocumentType, evidence.name)
            for evidence in outstanding_evidence
            if fineos_absence_id == evidence.uploadCaseNumber
            and not evidence.docReceived
            and evidence.name is not None
            and evidence.name in required_document_types
        ]

        return unreceived_evidence

    def get_document_requirements_for_claim_via_absence_periods(
        self, claim: Claim
    ) -> list[DocumentRequirement]:
        """Get document requirements for a claim based on its absence periods.

        Claim object must have related absence periods loaded
        """

        if not claim.absence_periods:
            raise ValueError(
                "Cannot get document requirements for claim "
                f"{claim.claim_id}. The claim must have associated absence periods."
            )

        document_type_requirements = self.get_required_document_types_for_absence_periods(
            claim.absence_periods
        )
        if not document_type_requirements:
            return []

        docs_uploaded_to_fineos = self.get_document_service.get_documents_from_fineos_for_claim(
            claim
        )

        return self.determine_document_requirements(
            document_type_requirements, docs_uploaded_to_fineos
        )

    def get_document_requirements_for_application(
        self, application: Application
    ) -> list[DocumentRequirement]:

        document_type_requirements = self.get_required_document_types_for_application(application)
        if not document_type_requirements:
            return []

        docs_uploaded_to_fineos = (
            self.get_document_service.get_documents_from_fineos_for_application(application)
        )

        return self.determine_document_requirements(
            document_type_requirements, docs_uploaded_to_fineos
        )

    @staticmethod
    def determine_document_requirements(
        document_type_requirements: list[DocumentType],
        uploaded_documents: list[DocumentResponse],
    ) -> list[DocumentRequirement]:
        """Based on list of documents and document type requirements, return list of DocumentRequirements"""
        document_requirements: list[DocumentRequirement] = []

        for document_type in document_type_requirements:
            document_upload_dates = [
                doc.created_at
                for doc in uploaded_documents
                if doc.document_type == document_type and doc.created_at is not None
            ]
            upload_date = max(document_upload_dates) if document_upload_dates else None
            document_requirements.append(
                DocumentRequirement(document_type=document_type, upload_date=upload_date)
            )

        # sort to get deterministic ordering
        document_requirements.sort(key=lambda x: x.document_type if x.document_type else "")

        return document_requirements

    def get_required_document_types_for_application(
        self, application: Application
    ) -> list[DocumentType]:
        """
        Returns a list of all document types required for the given application based on leave reason
        """
        try:
            leave_reason_description = LeaveReason.get_description(application.leave_reason_id)
        except KeyError:
            leave_reason_description = None

        return self.get_required_document_types_by_absence_reason(leave_reason_description)

    def get_required_document_types_for_absence_periods(
        self,
        absence_periods: list[AbsencePeriod],
    ) -> list[DocumentType]:
        """
        Returns a list of all document types required for given absence periods based on absence reason
        """
        required_document_type_list = set()
        for absence_period in absence_periods:
            if absence_period.absence_reason:
                # absence_reason relationship object is loaded
                absence_reason_description = (
                    absence_period.absence_reason.absence_reason_description
                )
            else:
                absence_reason_description = AbsenceReason.get_description(
                    absence_period.absence_reason_id
                )

            if not absence_reason_description:
                continue

            absence_period_required_docs = self.get_required_document_types_by_absence_reason(
                absence_reason_description
            )

            required_document_type_list.update(absence_period_required_docs)

        return list(required_document_type_list)

    def get_required_document_types_by_absence_reason(
        self,
        absence_reason: str | None,
    ) -> list[DocumentType]:
        """
        Returns a list of all document types required for a given absence period based on absence reason
        """
        if not absence_reason:
            return []

        absence_period_required_docs = ABSENCE_REASON_TO_DOCUMENT_TYPE_MAPPING.get(
            absence_reason, []
        )

        return absence_period_required_docs
