from typing import Iterable

from massgov.pfml import db, fineos
from massgov.pfml.api.models.documents.responses import DocumentResponse
from massgov.pfml.db.models.appeal import Appeal
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.employees import User
from massgov.pfml.fineos.models.customer_api import Document
from massgov.pfml.fineos.transforms.from_fineos.documents import (
    fineos_document_to_document_response,
)


class BaseDocumentService:
    def __init__(self, db_session=None, fineos=None):
        self._db_session = db_session
        self._fineos = fineos

    @property
    def db_session(self):
        if self._db_session is None:
            self._db_session = db.init(sync_lookups=True)
        return self._db_session

    @property
    def fineos(self):
        if self._fineos is None:
            self._fineos = fineos.create_client()
        return self._fineos

    def transform_and_filter_documents(
        self,
        fineos_documents: Iterable[Document],
        application: Application | None = None,
        appeal: Appeal | None = None,
        user: User | None = None,
    ) -> list[DocumentResponse]:
        """Transforms FINEOS documents into DocumentResponse objects and filters out unsupported file types"""
        return [
            fineos_document_to_document_response(
                fineos_document, self.db_session, application=application, appeal=appeal, user=user
            )
            for fineos_document in fineos_documents
            if fineos_document.fileExtension not in [".doc", ".docx"]
        ]
