from functools import singledispatchmethod

from massgov.pfml.api.models.documents.responses import DocumentResponse
from massgov.pfml.api.services.fineos_actions import (
    get_or_register_employee_fineos_web_id,
    register_employee_with_claim,
)
from massgov.pfml.db.models.appeal import Appeal
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.documents import Document
from massgov.pfml.db.models.employees import Claim, User
from massgov.pfml.util.logging import get_logger

from .base_document_service import BaseDocumentService
from .errors import NoApplicationFound, NoClaimFound

logger = get_logger(__name__)


class GetDocumentService(BaseDocumentService):
    def __init__(self, db_session=None):
        super().__init__(db_session=db_session)

    def get_document_by_id(
        self, fineos_document_id: str, application: Application
    ) -> None | Document | DocumentResponse:
        # Check whether document metadata exists in the database
        document = (
            self.db_session.query(Document)
            .filter(Document.fineos_id == fineos_document_id)
            .one_or_none()
        )

        if document is None:
            # If not found in the database, retrieve the document using the service method
            # This will return a DocumentResponse rather than a Document
            documents = self.get_documents_from_fineos_for_application(application)
            for d in documents:
                if d.fineos_document_id == fineos_document_id:
                    return d
            logger.warning("No document found for ID %s", fineos_document_id)

        return document

    @singledispatchmethod
    def validate_document(self, document):
        """Default validation for unsupported document types."""
        raise TypeError(f"Unsupported document type for validation: {type(document)}")

    @validate_document.register
    def _(self, application: Application) -> str:
        """Validates application for required fields"""
        if not application.claim:
            raise NoClaimFound("No claim found. No documents to retrieve.")
        fineos_absence_id = application.fineos_absence_id
        if fineos_absence_id is None:
            # application must have Absence Case ID (NTN)
            raise ValueError("Missing absence case id")
        return fineos_absence_id

    @validate_document.register
    def _(self, appeal: Appeal) -> tuple[str, Application]:
        """Validates appeals for required fields"""
        fineos_appeal_id = appeal.fineos_appeal_id
        if fineos_appeal_id is None:
            # appeal must have Appeal ID (NTN)
            raise ValueError("Missing appeal id")

        application = (
            self.db_session.query(Application)
            .filter(Application.claim_id == appeal.claim_id)
            .one_or_none()
        )
        if not application:
            # appeal must have an associated application
            raise NoApplicationFound(
                "Application with the given appeal's claim_id could not be found"
            )

        return fineos_appeal_id, application

    @validate_document.register
    def _(self, claim: Claim) -> str:
        """Validates claims for required fields"""
        fineos_absence_id = claim.fineos_absence_id
        if fineos_absence_id is None:
            # claim must have Absence Case ID (NTN)
            raise ValueError("Missing absence case id")
        return fineos_absence_id

    def get_documents_from_fineos_for_application(
        self, application: Application
    ) -> list[DocumentResponse]:
        """Get documents from FINEOS for given application."""
        try:
            fineos_absence_id = self.validate_document(application)

            fineos_web_id = get_or_register_employee_fineos_web_id(
                self.fineos, application, self.db_session
            )
            logger.info(f"Retrieving documents for absence case ID {fineos_absence_id}")
            fineos_documents = self.fineos.get_documents(fineos_web_id, fineos_absence_id)

            return self.transform_and_filter_documents(
                fineos_documents=fineos_documents, application=application
            )
        except NoClaimFound:
            logger.warning("No claim found for application %s", application)
            return []
        except Exception:
            logger.exception(f"Error retrieving documents for application {application}")
            raise

    def get_documents_from_fineos_for_appeal(self, appeal: Appeal) -> list[DocumentResponse]:
        """Get documents from FINEOS for given appeal. Appeal must have an associated application"""
        try:
            fineos_appeal_id, application = self.validate_document(appeal)

            fineos_web_id = get_or_register_employee_fineos_web_id(
                self.fineos, application, self.db_session
            )
            logger.info(f"Retrieving documents for appeal case {fineos_appeal_id}")
            fineos_documents = self.fineos.get_documents(fineos_web_id, fineos_appeal_id)

            return self.transform_and_filter_documents(
                fineos_documents=fineos_documents, application=application, appeal=appeal
            )
        except Exception:
            logger.exception(f"Error retrieving documents for appeal {appeal}")
            raise

    def get_documents_from_fineos_for_claim(self, claim: Claim) -> list[DocumentResponse]:
        """Retrieves documents from FINEOS for a claim"""
        try:
            fineos_absence_id = self.validate_document(claim)

            fineos_web_id = register_employee_with_claim(self.fineos, self.db_session, claim)
            logger.info(f"Retrieving documents for absence case ID {fineos_absence_id}")
            fineos_documents = self.fineos.get_documents(fineos_web_id, fineos_absence_id)

            return self.transform_and_filter_documents(fineos_documents=fineos_documents)
        except Exception:
            logger.exception(f"Error retrieving documents for claim {claim}")
            raise

    def get_user_documents(
        self,
        user: User,
        application: Application,
        api_params: dict | None = None,
    ) -> list[DocumentResponse]:
        fineos_web_id = get_or_register_employee_fineos_web_id(
            self.fineos, application, self.db_session
        )
        if fineos_web_id is None:
            raise ValueError("Missing fineos_web_id")
        logger.info("Retrieving documents for user_id {user.user_id}")
        fineos_documents = self.fineos.get_documents(fineos_web_id, None, api_params)

        return self.transform_and_filter_documents(fineos_documents=fineos_documents, user=user)
