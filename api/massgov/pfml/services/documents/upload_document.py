import tempfile
from datetime import datetime, timezone
from typing import Any, Dict, Optional

import newrelic.agent
import puremagic
from puremagic import PureError
from werkzeug.datastructures import FileStorage
from werkzeug.exceptions import NotFound

import massgov.pfml.api.app as app
import massgov.pfml.services.documents as documents_service
import massgov.pfml.util.logging
import massgov.pfml.util.pdf as pdf_util
from massgov.pfml import db
from massgov.pfml.api.authorization.flask import CREATE, EDIT, ensure
from massgov.pfml.api.constants.documents import ID_DOC_TYPES, LEAVE_REASON_TO_DOCUMENT_TYPE_MAPPING
from massgov.pfml.api.exceptions import ClaimWithdrawn, UploadDocumentError
from massgov.pfml.api.models.documents.common import ContentType as AllowedContentTypes
from massgov.pfml.api.models.documents.common import DocumentType as IoDocumentTypes
from massgov.pfml.api.models.documents.requests import DocumentRequestBody
from massgov.pfml.api.models.documents.responses import DocumentResponse
from massgov.pfml.api.services.fineos_actions import upload_document
from massgov.pfml.api.validation.exceptions import (
    IssueType,
    ValidationErrorDetail,
    ValidationException,
)
from massgov.pfml.db.lookup_data.documents import DocumentType
from massgov.pfml.db.models.appeal import Appeal
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.change_request import ChangeRequest
from massgov.pfml.db.models.documents import Document
from massgov.pfml.fineos.exception import FINEOSForbidden
from massgov.pfml.services.documents import DocumentRequirementService
from massgov.pfml.util.documents import validate_content_type
from massgov.pfml.util.logging.appeals import get_appeal_log_attributes
from massgov.pfml.util.logging.applications import get_application_log_attributes

logger = massgov.pfml.util.logging.get_logger(__name__)

FINEOS_UPLOAD_SIZE_CONSTRAINT = 4500000  # bytes
FILE_TOO_LARGE_MSG = "File is too large."
FILE_SIZE_VALIDATION_ERROR = ValidationErrorDetail(
    message=FILE_TOO_LARGE_MSG, type=IssueType.file_size, field="file"
)


def upload_change_request_document(
    change_request: ChangeRequest,
    document_details: DocumentRequestBody,
    file: FileStorage,
    db_session: db.Session,
) -> DocumentResponse:
    application = change_request.claim.application

    if application is None:
        raise NotFound(
            description="Could not find associated application for change request with ID {}".format(
                change_request.change_request_id
            )
        )

    # TODO: validate the documents

    document_response = upload_document_to_fineos(application, document_details, file)

    # mark that documents have been successfully submitted to FINEOS
    change_request.documents_submitted_at = datetime.now(timezone.utc)
    db_session.commit()

    return document_response


def upload_document_to_fineos(
    application: Application,
    document_details: DocumentRequestBody,
    file: FileStorage,
    appeal: Optional[Appeal] = None,
) -> DocumentResponse:
    if appeal:
        if not appeal.fineos_appeal_id:
            validation_error = ValidationErrorDetail(
                message="Appeal does not have a FINEOS ID.",
                type=IssueType.object_not_found,
                field="appeal",
            )
            raise UploadDocumentError(
                error=validation_error,
                message=validation_error.message,
                data=document_details.dict(),
            )
        log_attributes: Dict[str, Any] = get_appeal_log_attributes(appeal)
    else:
        log_attributes = get_application_log_attributes(application)

    with app.db_session() as db_session:
        # Check if user can edit application
        ensure(EDIT, application)

        if not application.leave_reason:
            raise ValueError("application.leave_reason is required")

        # To upload documents, an application needs to have a corresponding claim in FINEOS
        claim = application.claim
        if not claim:
            logger.warning(
                "document_upload - No claim for application",
                extra=log_attributes,
            )

        # Check if user can create a document associated with this application before making any API calls or
        # persisting the document to the database.
        document = Document()
        document.user_id = application.user_id
        ensure(CREATE, document)

        # Validate the file name and type
        content_type = ""
        try:
            if document_details.name:
                validate_file_name_for_fineos_upload(document_details.name)
            validate_file_name_for_fineos_upload(file.filename)
            content_type = get_valid_content_type(file)
        except ValidationException as exception:
            error = exception.errors[0]
            raise UploadDocumentError(
                error=error, message=error.message, data=document_details.dict()
            )

        # Get additional file meta data
        file.seek(0)
        file_content = file.read()
        file_size = len(file_content)

        file_name = document_details.name or file.filename

        file_description = ""
        if document_details.description:
            file_description = document_details.description

        try:
            # If the file is a PDF larger than the upload size constraint,
            # attempt to compress the PDF and update file meta data.
            # A size constraint of 10MB is still enforced by the API gateway,
            # so the API should not expect to receive anything above this size
            if (
                content_type == AllowedContentTypes.pdf.value
                and file_size > FINEOS_UPLOAD_SIZE_CONSTRAINT
            ):
                # tempfile.SpooledTemporaryFile writes the compressed file in-memory
                with tempfile.SpooledTemporaryFile(mode="wb+") as compressed_file:
                    file_size = pdf_util.compress_pdf(file, compressed_file)  # type: ignore
                    file_name = f"Compressed_{file_name}"

                    compressed_file.seek(0)
                    file_content = compressed_file.read()

            # Validate file size, regardless of processing
            validate_file_size_for_fineos_upload(file_size)

        except pdf_util.PDFSizeError:
            logger.warning("document_upload - file too large", extra=log_attributes, exc_info=True)
            raise UploadDocumentError(
                error=FILE_SIZE_VALIDATION_ERROR,
                message="File validation error.",
                data=document_details.dict(),
            )
        except pdf_util.PDFCompressionError:
            logger.warning(
                "document_upload - file too large and failed to compress",
                extra=log_attributes,
                exc_info=True,
            )
            newrelic.agent.notice_error(attributes={"document_id": document.document_id})

            # Compression errors are displayed as PDF Size errors to the claimant
            raise UploadDocumentError(
                error=FILE_SIZE_VALIDATION_ERROR,
                message="File validation error - file too large and failed to compress.",
                data=document_details.dict(),
            )

        document_type = document_details.document_type.value
        pfml_document_type: str | None = document_type
        fineos_document_type: str | IoDocumentTypes = document_type
        if document_type == IoDocumentTypes.certification_form:
            # Passed in doc type generic certification_form means PFML document type is unknown
            pfml_document_type = None
            # Map uploaded cert doc type to one that FINEOS will accept as evidence for given leave reason
            fineos_document_type = LEAVE_REASON_TO_DOCUMENT_TYPE_MAPPING[
                application.leave_reason.leave_reason_description
            ]

        if not appeal and fineos_document_type not in [
            doc_type.document_type_description for doc_type in ID_DOC_TYPES
        ]:
            # Check for existing STATE_MANAGED_PAID_LEAVE_CONFIRMATION documents, and reuse the doc type if there are docs
            # Because existing claims where only part 1 has been submitted should continue using old doc type, submitted_time
            # rather than existing docs should be examined

            if has_previous_state_managed_paid_leave(application, db_session) or (
                application.submitted_time
                and application.submitted_time < app.get_app_config().new_plan_proofs_active_at
            ):
                fineos_document_type = IoDocumentTypes.state_managed_paid_leave_confirmation

        log_attributes.update(
            {
                "document.file_size": file_size,
                "document.content_type": content_type,
                "document.document_type": fineos_document_type,
                "document.pfml_document_type": pfml_document_type,
            }
        )

        # Upload document to FINEOS
        try:
            upload_doc_type = fineos_document_type
            if app.get_features_config().fineos.is_running_v24:
                if fineos_document_type == IoDocumentTypes.pregnancy_maternity_form.value:
                    upload_doc_type = "Pregnancy and Maternity form"
            fineos_document = upload_document(
                application,
                upload_doc_type,
                file_content,
                file_name,  # type: ignore
                content_type,
                file_description,
                db_session,
                str(appeal.fineos_appeal_id) if appeal else None,
                with_multipart=app.get_app_config().enable_document_multipart_upload,
            ).dict()
            logger.info(
                "document_upload - document uploaded to claims processing system",
                extra=log_attributes,
            )
        except Exception as err:
            logger.warning(
                "document_upload failure - failure uploading document to claims processing system",
                extra=log_attributes,
                exc_info=True,
            )

            if isinstance(err, FINEOSForbidden):
                # Through testing, we've identified Fineos returning 403 errors when cases are Withdrawn:
                # https://lwd.atlassian.net/browse/PSD-2530
                raise ClaimWithdrawn()

            # Bubble any other issues up to the API error handlers
            raise

        # Insert a document metadata row
        if appeal:
            document.appeal_id = appeal.appeal_id
        else:
            document.application_id = application.application_id
        # doc type uploaded to FINEOS
        document.document_type_id = DocumentType.get_id(fineos_document_type)
        # doc type uploaded by claimant
        document.pfml_document_type_id = (
            DocumentType.get_id(pfml_document_type) if pfml_document_type else None
        )
        document.size_bytes = file_size
        document.fineos_id = fineos_document["documentId"]
        document.is_stored_in_s3 = False
        document.name = file_name  # type: ignore
        document.description = file_description

        db_session.add(document)

        document_response = DocumentResponse.from_orm(document=document, content_type=content_type)

        try:
            if not appeal:
                documents_service.mark_single_document_as_received_for_application(
                    application, document_response, db_session
                )
                logger.info("document_upload - evidence marked as received", extra=log_attributes)
        except Exception as err:
            logger.warning(
                "document_upload failure - failure marking evidence as received",
                extra=log_attributes,
                exc_info=err,
            )
            raise

        # Note that we expect the DB session to rollback here due to the raised exception,
        # so the document is not saved and the claimant has the opportunity to try again.
        # This behaviour will create multiple documents in FINEOS but will ensure that
        # the evidence can eventually be marked as received without manual intervention.
        db_session.commit()

        # save created_at based on when document added to DB
        document_response.created_at = document.created_at

        mark_applications_ready_for_review_is_enabled = (
            app.get_features_config().mark_applications_ready_for_review.enable_mark_applications_ready_for_review
        )

        # Mark application as ready for review if this is the last required document.
        if (
            mark_applications_ready_for_review_is_enabled
            and application.completed_time
            and application.ready_for_review_time is None
            and DocumentRequirementService(db_session).are_all_required_documents_received(
                application
            )
        ):
            application.ready_for_review_time = document.created_at
            log_attributes["application.ready_for_review_time"] = str(document.created_at)
            log_attributes["application.ready_for_review_time.timestamp"] = str(
                document.created_at.timestamp()
            )
            logger.info(
                "document_upload - application now ready for review",
                extra=log_attributes,
            )

        logger.info("document_upload success", extra=log_attributes)
        return document_response


# We need custom validation here since we get the content type from the uploaded file
def get_valid_content_type(file):
    """Use pure magic library to identify file type, use file mimetype as backup"""
    try:
        validate_content_type(file.mimetype)
        content_type = puremagic.from_stream(file.stream, mime=True, filename=file.filename)
        if content_type != file.mimetype:
            message = "Detected content type and mime type do not match. Detected: {}, mimeType: {}".format(
                content_type, file.mimetype
            )
            logger.warning(message)
            validation_error = ValidationErrorDetail(
                message=message,
                type=IssueType.file_type_mismatch,
                rule="Detected content type and mime type do not match.",
                field="file",
            )
            raise ValidationException(errors=[validation_error], message=message, data={})

        return content_type
    except (ValueError, PureError):
        # return the validated mime type in cases where pure magic can not detect the type
        return file.mimetype


def validate_file_name_for_fineos_upload(file_name):
    """Validate the file name has an extension"""
    extension_index = file_name.rfind(".")
    if extension_index < 1:
        logger.warning("Missing extension on file name.")
        message = "Missing extension on file name: {}".format(file_name)
        validation_error = ValidationErrorDetail(
            message=message,
            type=IssueType.file_name_extension,
            rule="File name extension required.",
            field="file",
        )
        raise ValidationException(errors=[validation_error], message=message, data={})


def validate_file_size_for_fineos_upload(file_size_bytes: int) -> None:
    """Validate the file size is below the known upload size constraint for files in FINEOS."""
    if file_size_bytes > FINEOS_UPLOAD_SIZE_CONSTRAINT:
        raise ValidationException(
            errors=[FILE_SIZE_VALIDATION_ERROR], message=FILE_TOO_LARGE_MSG, data={}
        )


def has_previous_state_managed_paid_leave(existing_application, db_session):
    # For now, if there are documents previously submitted for the application with the
    # STATE_MANAGED_PAID_LEAVE_CONFIRMATION document type, that document type must also
    # be used for subsequent documents uploaded to the application. If not, the document type
    # from the request should be used instead.
    existing_documents_with_old_doc_type = (
        db_session.query(Document)
        .filter(Document.application_id == existing_application.application_id)
        .filter(
            Document.document_type_id
            == DocumentType.STATE_MANAGED_PAID_LEAVE_CONFIRMATION.document_type_id
        )
    ).all()

    if len(existing_documents_with_old_doc_type) > 0:
        return True

    return False
