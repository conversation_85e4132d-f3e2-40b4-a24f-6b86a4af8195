from datetime import date, timedelta
from typing import List, Optional

from werkzeug.exceptions import NotFound

import massgov.pfml.db as db
import massgov.pfml.util.logging
from massgov.pfml.api.models.waiting_periods.responses import WaitingPeriod
from massgov.pfml.api.services import fineos_actions
from massgov.pfml.api.services.claims import get_absence_paid_leave_cases_from_db_claim
from massgov.pfml.db.models.employees import Claim
from massgov.pfml.fineos.models.customer_api import ReadDisabilityBenefitResult

logger = massgov.pfml.util.logging.get_logger(__name__)


def get_waiting_periods_from_fineos(
    claim: Claim, db_session: db.Session
) -> List[Optional[WaitingPeriod]]:
    log_attributes = {"claim": claim.claim_id, "fineos_absence_id": claim.fineos_absence_id}

    application = claim.application
    if not application:
        raise NotFound(description="No application is associated with the given claim")

    absence_paid_leave_cases = get_absence_paid_leave_cases_from_db_claim(claim, db_session)

    if not absence_paid_leave_cases:
        logger.warning(
            "No absence paid leave cases are associated with the given claim", extra=log_attributes
        )
        return []

    absence_paid_leave_case_numbers = [
        aplc.absence_paid_leave_case_number for aplc in absence_paid_leave_cases
    ]

    # returns a dict of absence paid leave cases and their associated benefit_ids
    benefit_ids_by_aplc_number = {}

    # returns a list of dicts with start and end dates for waiting_periods
    waiting_periods = []

    fineos = massgov.pfml.fineos.create_client()

    web_id = fineos_actions.register_employee_with_claim(fineos, db_session, claim)

    for absence_paid_leave_case_number in absence_paid_leave_case_numbers:
        # returns a list of benefit summaries
        claim_benefit_details = fineos_actions.get_claim_benefits(
            claim=claim,
            absence_paid_leave_case_number=absence_paid_leave_case_number,
            web_id=web_id,
            fineos=fineos,
        )

        # We only want to know about benefits that have been approved
        approved_benefit_detail_item = next(
            (
                benefit_detail_item
                for benefit_detail_item in claim_benefit_details
                if benefit_detail_item.stageName == "Approved"
            ),
            None,
        )
        # We only need a single benefit_id to make the call to read disability benefits
        benefit_id = (
            approved_benefit_detail_item.benefitId if approved_benefit_detail_item else None
        )
        benefit_ids_by_aplc_number[absence_paid_leave_case_number] = benefit_id

    if not benefit_ids_by_aplc_number:
        logger.info("No benefit_ids are associated with the given claim", extra=log_attributes)

    for absence_paid_leave_case_number, benefit_id in benefit_ids_by_aplc_number.items():
        if benefit_id is not None:
            disability_benefit_details = fineos_actions.get_disability_benefit_details(
                absence_paid_leave_case_number=absence_paid_leave_case_number,
                benefit_id=benefit_id,
                web_id=web_id,
                fineos=fineos,
            )
            if disability_benefit_details:
                waiting_periods.append(
                    parse_waiting_period(
                        disability_benefit_details, claim.earliest_approved_start_date
                    )
                )
    return waiting_periods


def parse_waiting_period(
    disability_benefit_details: ReadDisabilityBenefitResult,
    earliest_approved_start_date: Optional[date],
) -> Optional[WaitingPeriod]:
    if not disability_benefit_details.disabilityBenefit:
        # no benefit found - unable to determine waiting period
        return None

    benefit_incurred_date = disability_benefit_details.disabilityBenefit.benefitIncurredDate
    benefit_start_date = disability_benefit_details.disabilityBenefit.benefitStartDate

    if benefit_incurred_date == benefit_start_date:
        # in extensions the benefit incurred date and benefit start date are the same
        # in other words there is no waiting period
        return None

    # start and end should be 7 days, inclusive of both days
    # subtract 1 from the benefit start date to get the waiting period end date
    waiting_period_start_date = benefit_incurred_date
    waiting_period_end_date = (
        benefit_start_date - timedelta(days=1) if benefit_start_date is not None else None
    )

    return WaitingPeriod(
        waiting_period_start_date=waiting_period_start_date,
        waiting_period_end_date=waiting_period_end_date,
        _earliest_approved_start_date=earliest_approved_start_date,
    )
