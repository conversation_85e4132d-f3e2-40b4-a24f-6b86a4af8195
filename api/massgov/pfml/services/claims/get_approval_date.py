from datetime import date
from typing import List, Optional

import massgov
from massgov.pfml import db
from massgov.pfml.api.models.documents.responses import DocumentResponse
from massgov.pfml.db.models.employees import Claim
from massgov.pfml.services.documents.get_document_service import GetDocumentService

logger = massgov.pfml.util.logging.get_logger(__name__)


def get_approval_date(claim: Claim, db_session: db.Session) -> Optional[date]:
    """
    Get approval date for a claim.
    Calculated based on the date of the earliest approval document for this claim.
    """
    # TODO (PORTAL-2435): Only calculate approval date for claims with an approval status

    # If we've already calculated the approval date, return it from the claim
    if claim.approval_date:
        return claim.approval_date

    approval_docs = _get_approval_documents(claim, db_session)

    if len(approval_docs) == 0:
        logger.warning(
            "Unable to get approval date - no approval docs found",
            extra={
                "absence_id": claim.fineos_absence_id,
                "absence_case_id": claim.fineos_absence_id,
            },
        )
        return None

    approval_dates = [doc.created_at for doc in approval_docs]
    approval_dates.sort()
    earliest_approval_date = approval_dates[0]

    # If we successfully calculated an approval date, persist it to the claim
    claim.approval_date = earliest_approval_date

    return earliest_approval_date


def _get_approval_documents(claim: Claim, db_session: db.Session) -> List[DocumentResponse]:
    docs = GetDocumentService(db_session=db_session).get_documents_from_fineos_for_claim(claim)
    approval_docs = [doc for doc in docs if doc.document_type == "Approval Notice"]

    log_attr = {
        "absence_id": claim.fineos_absence_id,
        "absence_case_id": claim.fineos_absence_id,
        "fineos_absence_status": claim.fineos_absence_status,
        "has_approval_status": claim.has_approval_status,
        "num_approval_documents": len(approval_docs),
    }
    logger.info("Retrieved approval documents for claim", extra=log_attr)

    return approval_docs
