from collections import defaultdict
from typing import Any, Callable, Dict, Optional
from uuid import UUID

from massgov.pfml import db
from massgov.pfml.db.models.absences import AbsencePeriod
from massgov.pfml.db.models.employees import AbsencePaidLeaveCase, BenefitYear, Claim, LeaveRequest
from massgov.pfml.delegated_payments.claimant_extract_metrics import ClaimantExtractMetrics
from massgov.pfml.util import logging
from massgov.pfml.util.datetime import is_date_contained, is_range_contained

logger = logging.get_logger(__name__)


class SynchronizedAbsenceCaseData:
    claim: Claim
    leave_requests: list[LeaveRequest]
    absence_periods: list[AbsencePeriod]
    absence_paid_leave_cases: list[AbsencePaidLeaveCase]
    benefit_years: list[BenefitYear]

    absence_periods_by_fineos_leave_request_id: dict[int, list[AbsencePeriod]] = defaultdict(list)
    absence_paid_leave_cases_by_leave_request_id: dict[UUID, list[AbsencePaidLeaveCase]] = (
        defaultdict(list)
    )
    leave_requests_by_fineos_leave_request_id: dict[int, LeaveRequest] = {}
    leave_requests_by_leave_request_id: dict[UUID, LeaveRequest] = {}

    def __init__(self, fineos_absence_id: str, db_session: db.Session):
        self.db_session = db_session

        self.claim = (
            self.db_session.query(Claim).filter(Claim.fineos_absence_id == fineos_absence_id).one()
        )
        self.leave_requests = (
            self.db_session.query(LeaveRequest)
            .filter(LeaveRequest.claim_id == self.claim.claim_id)
            .all()
        )
        self.absence_periods = (
            self.db_session.query(AbsencePeriod)
            .filter(AbsencePeriod.claim_id == self.claim.claim_id)
            .order_by(AbsencePeriod.absence_period_start_date)
            .all()
        )
        self.absence_paid_leave_cases = (
            self.db_session.query(AbsencePaidLeaveCase)
            .filter(AbsencePaidLeaveCase.claim_id == self.claim.claim_id)
            .order_by(AbsencePaidLeaveCase.start_date)
            .all()
        )
        self.benefit_years = (
            self.db_session.query(BenefitYear)
            .filter(BenefitYear.employee_id == self.claim.employee_id)
            .order_by(BenefitYear.start_date)
            .all()
        )
        for leave_request in self.leave_requests:
            self.leave_requests_by_fineos_leave_request_id[
                leave_request.fineos_leave_request_id
            ] = leave_request
            self.leave_requests_by_leave_request_id[leave_request.leave_request_id] = leave_request

        for absence_period in self.absence_periods:
            self.absence_periods_by_fineos_leave_request_id[
                absence_period.fineos_leave_request_id
            ].append(absence_period)

        for absence_paid_leave_case in self.absence_paid_leave_cases:
            self.absence_paid_leave_cases_by_leave_request_id[
                absence_paid_leave_case.leave_request_id
            ].append(absence_paid_leave_case)

    def get_traceable_info(self, extra: Optional[Dict[str, Optional[Any]]] = None) -> dict:
        return {
            **(extra or {}),
            "claim_id": self.claim.claim_id,
            "fineos_absence_id": self.claim.fineos_absence_id,
            "employee_id": self.claim.employee_id,
            "employer_id": self.claim.employer_id,
            "claim_absence_period_start_date": (
                self.claim.claim_start_date.isoformat() if self.claim.claim_start_date else None
            ),
            "claim_absence_period_end_date": (
                self.claim.claim_end_date.isoformat() if self.claim.claim_end_date else None
            ),
            "leave_requests_count": len(self.leave_requests),
            "absence_periods_count": len(self.absence_periods),
            "absence_paid_leave_cases_count": len(self.absence_paid_leave_cases),
            "benefit_years_count": len(self.benefit_years),
        }

    def get_traceable_leave_request_info(
        self, leave_request: LeaveRequest, extra: Optional[Dict[str, Optional[Any]]] = None
    ) -> dict:
        return self.get_traceable_info(
            {
                **(extra or {}),
                "leave_request_id": leave_request.leave_request_id,
                "fineos_leave_request_id": leave_request.fineos_leave_request_id,
            }
        )

    def get_traceable_absence_paid_leave_case_info(
        self,
        absence_paid_leave_case: AbsencePaidLeaveCase,
        extra: Optional[Dict[str, Optional[Any]]] = None,
    ) -> dict:
        return self.get_traceable_info(
            {
                **(extra or {}),
                "absence_paid_leave_case_id": absence_paid_leave_case.absence_paid_leave_case_id,
                "absence_paid_leave_case_number": absence_paid_leave_case.absence_paid_leave_case_number,
                "leave_request_id": absence_paid_leave_case.leave_request_id,
                "absence_paid_leave_case_start_date": absence_paid_leave_case.start_date.isoformat(),
                "absence_paid_leave_case_end_date": absence_paid_leave_case.end_date.isoformat(),
            }
        )


class ValidateAbsenceCaseSync:
    count_incrementer: Optional[Callable[[str], None]]
    db_session: db.Session

    Metrics = ClaimantExtractMetrics

    def __init__(
        self,
        synchronized_data: SynchronizedAbsenceCaseData,
        count_incrementer: Optional[Callable[[str], None]] = None,
    ):
        self.data = synchronized_data
        self.count_incrementer = count_incrementer

    def increment(self, metric: str) -> None:
        if self.count_incrementer:
            self.count_incrementer(metric)

    def validate(self) -> None:
        self.has_at_least_one_absence_period_per_leave_request()
        self.has_as_many_absence_types_as_absence_periods_per_leave_request()
        self.has_at_least_one_paid_leave_case_per_leave_request()
        self.has_valid_dates_for_each_absence_paid_leave_case()
        self.should_span_benefit_years_for_each_leave_request_with_multiple_absence_paid_leave_cases()

        self.has_one_leave_request_per_absence_reason()

    def has_at_least_one_absence_period_per_leave_request(self) -> bool:
        passed = True
        for leave_request in self.data.leave_requests:
            if (
                leave_request.fineos_leave_request_id
                not in self.data.absence_periods_by_fineos_leave_request_id
            ):
                logger.warning(
                    "Leave request is missing a matching absence period",
                    extra=self.data.get_traceable_leave_request_info(leave_request),
                )
                self.increment(ClaimantExtractMetrics.LEAVE_REQUEST_MISSING_ABSENCE_PERIODS_COUNT)
                passed = False

        for fineos_leave_request_id in self.data.absence_periods_by_fineos_leave_request_id:
            if fineos_leave_request_id not in self.data.leave_requests_by_fineos_leave_request_id:
                logger.warning(
                    "Fineos leave request id from absence periods does not match any leave request",
                    extra=self.data.get_traceable_info(
                        {"fineos_leave_request_id": fineos_leave_request_id}
                    ),
                )
                self.increment(
                    ClaimantExtractMetrics.FINEOS_LEAVE_REQUEST_ID_MISSING_LEAVE_REQUEST_COUNT
                )
                passed = False

        return passed

    def has_as_many_absence_types_as_absence_periods_per_leave_request(self) -> bool:
        passed = True
        for leave_request in self.data.leave_requests:
            absence_periods_x = self.data.absence_periods_by_fineos_leave_request_id[
                leave_request.fineos_leave_request_id
            ]
            absence_types = {
                absence_period.absence_period_type_id for absence_period in absence_periods_x
            }

            if len(absence_types) != len(absence_periods_x):
                logger.warning(
                    "Leave request has unequeal amount of absence types to absence periods",
                    extra=self.data.get_traceable_leave_request_info(
                        leave_request,
                        {
                            "absence_periods_count": len(absence_periods_x),
                            "absence_types_count": len(absence_types),
                            "absence_types": str(absence_types),
                        },
                    ),
                )
                self.increment(
                    ClaimantExtractMetrics.LEAVE_REQUEST_INVALID_ABSENCE_TYPES_TO_ABSENCE_PERIODS_COUNT
                )
                passed = False

        return passed

    def has_at_least_one_paid_leave_case_per_leave_request(self) -> bool:
        passed = True
        for leave_request in self.data.leave_requests:
            if (
                leave_request.leave_request_id
                not in self.data.absence_paid_leave_cases_by_leave_request_id
            ):
                logger.warning(
                    "Leave request is missing a matching absence paid leave case",
                    extra=self.data.get_traceable_leave_request_info(leave_request),
                )
                self.increment(
                    ClaimantExtractMetrics.LEAVE_REQUEST_MISSING_ABSENCE_PAID_LEAVE_CASES_COUNT
                )
                passed = False

        for leave_request_id in self.data.absence_paid_leave_cases_by_leave_request_id:
            if leave_request_id not in self.data.leave_requests_by_leave_request_id:
                logger.warning(
                    "Leave request id from absence paid leave cases does not match any leave request",
                    extra=self.data.get_traceable_info({"leave_request_id": leave_request_id}),
                )
                self.increment(
                    ClaimantExtractMetrics.ABSENCE_PAID_LEAVE_CASE_LEAVE_REQUEST_ID_MISSING_LEAVE_REQUEST_COUNT
                )
                passed = False

        return passed

    def has_valid_dates_for_each_absence_paid_leave_case(self) -> bool:
        passed = True

        if not self.data.claim.claim_start_date or not self.data.claim.claim_end_date:
            # Existing metric START_DATE_OR_END_DATE_NOT_FOUND_COUNT
            logger.warning(
                "Unable to validate absence paid leave case dates as claim dates are missing",
                extra=self.data.get_traceable_info(),
            )
            return False

        for absence_paid_leave_case in self.data.absence_paid_leave_cases:
            if not is_range_contained(
                (
                    self.data.claim.claim_start_date,
                    self.data.claim.claim_end_date,
                ),
                (absence_paid_leave_case.start_date, absence_paid_leave_case.end_date),
            ):
                logger.warning(
                    "Absence paid leave case dates are not contained in leave request dates",
                    extra=self.data.get_traceable_absence_paid_leave_case_info(
                        absence_paid_leave_case
                    ),
                )
                self.increment(ClaimantExtractMetrics.ABSENCE_PAID_LEAVE_CASE_INVALID_DATES_COUNT)
                passed = False

        return passed

    def should_span_benefit_years_for_each_leave_request_with_multiple_absence_paid_leave_cases(
        self,
    ):
        passed = True

        if not self.data.claim.claim_start_date or not self.data.claim.claim_end_date:
            # Existing metric START_DATE_OR_END_DATE_NOT_FOUND_COUNT
            logger.warning(
                "Unable to validate absence paid leave case dates align with benefit years as claim dates are missing",
                extra=self.data.get_traceable_info(),
            )
            return False

        start_benefit_years = [
            benefit_year
            for benefit_year in self.data.benefit_years
            if is_date_contained(
                (benefit_year.start_date, benefit_year.end_date),
                self.data.claim.claim_start_date,
            )
        ]
        end_benefit_years = [
            benefit_year
            for benefit_year in self.data.benefit_years
            if is_date_contained(
                (benefit_year.start_date, benefit_year.end_date),
                self.data.claim.claim_end_date,
            )
        ]

        # Verifying that the leave request can only start in one benefit year and end in one benefit year
        # without failing validation
        if len(start_benefit_years) != 1 or len(end_benefit_years) != 1:
            logger.info(
                "Unexpected number of benefit years found for leave request dates",
                extra=self.data.get_traceable_info(
                    {
                        "start_benefit_years_count": len(start_benefit_years),
                        "end_benefit_years_count": len(end_benefit_years),
                        "start_benefit_years": str(x.benefit_year_id for x in start_benefit_years),
                        "end_benefit_years": str(x.benefit_year_id for x in end_benefit_years),
                    }
                ),
            )
            self.increment(
                ClaimantExtractMetrics.UNEXPECTED_NUMBER_OF_BENEFIT_YEARS_FOR_ABSENCE_PERIOD_DATES_COUNT
            )

        spans_benefit_years = start_benefit_years != end_benefit_years

        for leave_request in self.data.leave_requests:
            absence_paid_leave_cases_x = self.data.absence_paid_leave_cases_by_leave_request_id[
                leave_request.leave_request_id
            ]

            # If there is only one absence paid leave case, then it is fine
            if len(absence_paid_leave_cases_x) == 1:
                continue

            # Construct log extra details
            log_extra: Dict[str, Optional[Any]] = self.data.get_traceable_leave_request_info(
                leave_request,
                {
                    "spans_benefit_years": spans_benefit_years,
                    "absence_paid_leave_cases_count": len(absence_paid_leave_cases_x),
                },
            )
            for i, absence_paid_leave_case in enumerate(absence_paid_leave_cases_x):
                log_extra |= {
                    f"absence_paid_leave_case_{i}_leave_request_id": absence_paid_leave_case.leave_request_id,
                    f"absence_paid_leave_case_{i}_start_date": absence_paid_leave_case.start_date,
                    f"absence_paid_leave_case_{i}_end_date": absence_paid_leave_case.end_date,
                }

            # There should never be more than two absence paid leave cases
            if len(absence_paid_leave_cases_x) > 2:
                logger.warning(
                    "Leave request has more than two absence paid leave cases", extra=log_extra
                )
                self.increment(
                    ClaimantExtractMetrics.LEAVE_REQUEST_SPLIT_IN_MORE_THAN_TWO_PAID_LEAVE_CASES
                )
                passed = False
                continue

            # If there are two absence paid leave cases, they should span benefit years
            if not spans_benefit_years:
                logger.warning(
                    "Leave request has two absence paid leave cases but does not span benefit years",
                    extra=log_extra,
                )
                self.increment(
                    ClaimantExtractMetrics.LEAVE_REQUEST_SPLIT_IN_TWO_PAID_LEAVE_CASES_WITHOUT_SPANNING_BENEFIT_YEARS_COUNT
                )
                passed = False
                continue

            # Verify the absence paid leave case split dates without failing validation
            if absence_paid_leave_cases_x[0].end_date != start_benefit_years[0].end_date:
                logger.info(
                    "Absence paid leave case doesnt end on the starting benefit year's end date",
                    extra=log_extra,
                )
                self.increment(
                    ClaimantExtractMetrics.FIRST_ABSENCE_PAID_LEAVE_CASE_FROM_SPLIT_LEAVE_REQUEST_ENDS_ON_UNEXPECTED_DATE_COUNT
                )
            if absence_paid_leave_cases_x[1].start_date != end_benefit_years[0].start_date:
                logger.info(
                    "Absence paid leave case doesnt start on the ending benefit year's start date",
                    extra=log_extra,
                )
                self.increment(
                    ClaimantExtractMetrics.SECOND_ABSENCE_PAID_LEAVE_CASE_FROM_SPLIT_LEAVE_REQUEST_STARTS_ON_UNEXPECTED_DATE_COUNT
                )

        return passed

    def has_one_leave_request_per_absence_reason(self):
        passed = True

        absence_reasons: set[int] = {
            leave_request.absence_reason_id for leave_request in self.data.leave_requests
        }

        if len(absence_reasons) != len(self.data.leave_requests):
            logger.warning(
                "Absence reasons not unique for leave requests",
                extra=self.data.get_traceable_info(
                    {
                        "absence_reasons": str(absence_reasons),
                        "absence_reasons_count": len(absence_reasons),
                    }
                ),
            )
            self.increment(ClaimantExtractMetrics.ABSENCE_REASONS_TO_LEAVE_REQUESTS_INVALID_COUNT)
            passed = False

        return passed
