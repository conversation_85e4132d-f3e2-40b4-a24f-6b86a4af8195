from datetime import date
from typing import List, Optional

import massgov
from massgov.pfml.db.models.absences import AbsencePeriod
from massgov.pfml.db.models.employees import AbsencePaidLeaveCase, LeaveRequest
from massgov.pfml.util.pydantic import PydanticBaseModel

logger = massgov.pfml.util.logging.get_logger(__name__)


class DateRange(PydanticBaseModel):
    start_date: date
    end_date: date


def determine_approved_dates(absence_period: AbsencePeriod) -> Optional[DateRange]:
    """
    Determine approved dates for an AbsencePeriod.

    Typically, an AbsencePeriod is approved or denied in its entirety.
    (Technically the associated "LeaveRequest" is what is approved or denied.)

    However, when an AbsencePeriod spans benefit years, things get complicated.

    In that case, the portion of the AbsencePeriod in the current benefit year is approved,
    and the remaining portion may or may not be approved, based on other factors.
    (Such as if the portion after the benefit year is >60 days in the future.)

    We can determine that _actual_ approved dates for an AbsencePeriod by referring to the
    associated AbsencePaidLeaveCase(s).
    The AbsencePaidLeaveCase contains only the approved dates for which a claimant will
    be getting payments.

    We can have the following scenarios:
    1. One paid leave case, which fully covers the absence period. (Typical)
    2. Two paid leave cases, which combined fully cover the absence period.
       (The AbsencePeriod spans benefit years, and both are approved)
    3. One paid leave case, which partially covers the absence period.
       (Partial approval / mixed decision)
    """

    associated_leave_request: Optional[LeaveRequest] = absence_period.leave_request
    if not associated_leave_request:
        # no way to determine approved dates without a leave request
        return None

    associated_paid_leave_cases: Optional[List[AbsencePaidLeaveCase]] = (
        associated_leave_request.absence_paid_leave_cases
    )
    if not associated_paid_leave_cases:
        # no way to determine approved dates without an absence_paid_leave_case
        return None

    if not absence_period.start_date:
        # no way to determine approved dates without absence period dates
        return None

    # we make the assumption that if the absence_period is approved, then the start
    # is always approved
    approved_start_date = absence_period.start_date

    # get the latest end_date from the paid leave cases as the outer bounds
    # of what has been approved
    approved_end_date = _get_latest_end_date(associated_paid_leave_cases)

    return DateRange(
        start_date=approved_start_date,
        end_date=approved_end_date,
    )


def _get_latest_end_date(paid_leave_cases: List[AbsencePaidLeaveCase]) -> date:
    end_dates = [x.end_date for x in paid_leave_cases]
    end_dates.sort(reverse=True)  # sort with latest dates first

    return end_dates[0]
