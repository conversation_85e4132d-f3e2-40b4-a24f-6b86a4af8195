import json
from datetime import date
from typing import Any, List, Optional

from pydantic import Field, root_validator, validator

import massgov.pfml.util.logging
from massgov.pfml.util.pydantic import PydanticBaseModelEmptyStrIsNone

logger = massgov.pfml.util.logging.get_logger(__name__)


class EDMVendorRequest(PydanticBaseModelEmptyStrIsNone):
    vcc_code: Optional[str] = Field(None, description="VCC Code")
    tin: Optional[str] = Field(None, description="TIN Number")

    @root_validator(pre=True)
    def validate_atleast_one_field(cls, values):  # noqa: B902
        vcc_code, tin = values.get("vcc_code"), values.get("tin")
        if not vcc_code and not tin:
            raise ValueError("At least one of 'vcc_code' or 'tin' must be populated.")
        return values


class EDMVendorResultSetMetaData(PydanticBaseModelEmptyStrIsNone):
    numRows: int
    format: str
    partitionInfo: Optional[List[Any]] = Field(default=[])
    rowType: Optional[List[Any]] = Field(default=[])


class EDMVendorData(PydanticBaseModelEmptyStrIsNone):
    VENDOR_CUSTOMER_CODE: Optional[str]
    TIN: Optional[str]
    FIRST_NAME: Optional[str]
    LAST_NAME: Optional[str]
    LEGAL_NAME: Optional[str]
    ADDRESS_ID: Optional[str]
    STREET_1: Optional[str]
    STREET_2: Optional[str]
    CITY: Optional[str]
    STATE: Optional[str]
    ZIP_CODE: Optional[str]
    CUSTOMER_ACTIVE_STATUS: Optional[str]
    CUSTOMER_ACTIVE_STATUS_NAME: Optional[str]
    CUSTOMER_APPROVAL_STATUS: Optional[str]
    CUSTOMER_APPROVAL_STATUS_NAME: Optional[str]


class EDMResponse(PydanticBaseModelEmptyStrIsNone):
    resultSetMetaData: Optional[EDMVendorResultSetMetaData]
    code: Optional[int]
    statementStatusUrl: Optional[str]
    requestId: Optional[str]
    sqlState: Optional[str]
    statementHandle: Optional[str]
    message: Optional[str]
    createdOn: Optional[int]


class EDMVendorResponse(EDMResponse):
    data: List[EDMVendorData]

    @validator("data", pre=True)
    def parse_data(cls, value: List[List[str]]) -> List[EDMVendorData]:  # noqa: B902
        """Parses the JSON strings in the data field into a list of dictionaries."""
        parsed_data = []
        for row in value:
            if row and isinstance(row[0], str):
                try:
                    d = json.loads(row[0])
                    d_obj = EDMVendorData.parse_obj(d)
                    parsed_data.append(d_obj)
                except json.JSONDecodeError:
                    extra = {
                        "requestId": cls.requestId,
                        "statementHandle": cls.statementHandle,
                        "code": cls.code,
                        "sqlState": cls.sqlState,
                        "message": cls.message,
                        "createdOn": cls.createdOn,
                    }
                    logger.error("Failed to parse JSON data", extra=extra)
        return parsed_data


class EDMRepaymentRequest(PydanticBaseModelEmptyStrIsNone):
    start_timestamp: date
    end_timestamp: date

    def format_date(self, dt: Optional[date]) -> Optional[str]:
        if dt:
            return dt.strftime("%Y-%m-%d")
        return None


class EDMRepaymentData(PydanticBaseModelEmptyStrIsNone):
    DOC_IDENTIFIER: Optional[str]
    REF_DOC_IDENTIFIER: Optional[str]
    VENDOR_CUSTOMER_CODE: Optional[str]
    LEGAL_NAME: Optional[str]
    LINE_AMOUNT: Optional[str]
    PAYMENT_DATE: Optional[str]
    DATE_TO_WAREHOUSE: Optional[str]
    ACCEPTANCE_DATE: Optional[str]
    EXTENDED_DOC_DESCR: Optional[str]
    CHECK_EFT_NO: Optional[str]


class EDMRepaymentResponse(EDMResponse):
    data: List[EDMRepaymentData]

    @validator("data", pre=True)
    def parse_data(cls, value: List[List[str]]) -> List[EDMRepaymentData]:  # noqa: B902
        """Parses the JSON strings in the data field into a list of dictionaries."""
        parsed_data = []
        for row in value:
            if row and isinstance(row[0], str):
                try:
                    d = json.loads(row[0])
                    d_obj = EDMRepaymentData.parse_obj(d)
                    parsed_data.append(d_obj)
                except json.JSONDecodeError:
                    extra = {
                        "requestId": cls.requestId,
                        "statementHandle": cls.statementHandle,
                        "code": cls.code,
                        "sqlState": cls.sqlState,
                        "message": cls.message,
                        "createdOn": cls.createdOn,
                    }
                    logger.error("Failed to parse JSON data", extra=extra)
        return parsed_data
