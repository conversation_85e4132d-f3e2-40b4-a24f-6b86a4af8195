#
# EDM client - mock implementation.
#
# This implementation is intended for use in local development or in test cases. It may also be
# used in deployed environments when needed.
#

from datetime import datetime, timedelta, timezone
from typing import Optional

import faker
from sqlalchemy import and_
from sqlalchemy.orm import joinedload

import massgov.pfml.util.logging
import massgov.pfml.util.logging.wrapper
from massgov.pfml import db
from massgov.pfml.db.models.employees import Employee, Overpayment
from massgov.pfml.delegated_payments import delegated_payments_util
from massgov.pfml.delegated_payments.process_mmars_response_file import (
    MmarsEvent,
    MmarsEventStatusType,
)
from massgov.pfml.edm.models.vendor.models import EDMRepaymentData

from . import client, models

logger = massgov.pfml.util.logging.get_logger(__name__)

mock_vendor_data_json = {
    "resultSetMetaData": {
        "numRows": 1,
        "format": "jsonv2",
        "partitionInfo": [{"rowCount": 1, "uncompressedSize": 830}],
        "rowType": [
            {
                "name": "OBJECT_CONSTRUCT(*)",
                "database": "",
                "schema": "",
                "table": "",
                "nullable": True,
                "byteLength": None,
                "length": None,
                "type": "object",
                "scale": None,
                "precision": None,
                "collation": None,
            }
        ],
    },
    "data": [
        [
            '{\n  "ADDRESS_ID": "AD010",\n  "CITY": "BOSTON",\n  "CUSTOMER_ACTIVE_STATUS": "2",\n  "CUSTOMER_ACTIVE_STATUS_NAME": "Active",\n  "CUSTOMER_APPROVAL_STATUS": "3",\n  "CUSTOMER_APPROVAL_STATUS_NAME": "Complete",\n  "FIRST_NAME": "FN017",\n  "LAST_NAME": "LN017",\n  "LEGAL_NAME": "FN017 LN017",\n  "STATE": "MA",\n  "STREET_1": "50 STANIFORD ST",\n  "TIN": "123000017",\n  "VENDOR_CUSTOMER_CODE": "VC0000100017",\n  "ZIP_CODE": "02114"\n}'
        ]
    ],
    "code": "090001",
    "statementStatusUrl": "/api/v2/statements/01ba3c53-030c-3684-0000-a4d701d2866e?requestId=5a056c6d-bb2c-4a7c-84dc-587932b5f860",
    "requestId": "5a056c6d-bb2c-4a7c-84dc-587932b5f860",
    "sqlState": "00000",
    "statementHandle": "01ba3c53-030c-3684-0000-a4d701d2866e",
    "message": "Statement executed successfully.",
    "createdOn": 1738941351642,
}

mock_repayment_data_json = {
    "resultSetMetaData": {
        "numRows": 1,
        "format": "jsonv2",
        "partitionInfo": [{"rowCount": 1, "uncompressedSize": 1117}],
        "rowType": [
            {
                "name": "OBJECT_CONSTRUCT(*)",
                "database": "",
                "schema": "",
                "table": "",
                "nullable": True,
                "byteLength": None,
                "length": None,
                "type": "object",
                "scale": None,
                "precision": None,
                "collation": None,
            }
        ],
    },
    "data": [
        [
            '{\n  "ACCEPTANCE_DATE": "2024-05-23 00:00:00.000",\n  "CHECK_EFT_NO": "005120",\n  "DATE_TO_WAREHOUSE": "2024-05-24 05:35:23.603",\n  "DOC_IDENTIFIER": "05232400000000343650",\n  "LEGAL_NAME": "KITCHENS OF THE BAY STATE INC",\n  "LINE_AMOUNT": 2150,\n  "PAYMENT_DATE": "2024-05-23 00:00:00.000",\n  "REF_DOC_IDENTIFIER": "05152400000000018254",\n  "VENDOR_CUSTOMER_CODE": "VC0001508361"\n}'
        ]
    ],
    "code": "090001",
    "statementStatusUrl": "/api/v2/statements/01ba44f0-020c-38bf-0000-a4d701d5640e?requestId=ea49faab-70b2-41d5-81db-f85429cea0ae",
    "requestId": "ea49faab-70b2-41d5-81db-f85429cea0ae",
    "sqlState": "00000",
    "statementHandle": "01ba44f0-020c-38bf-0000-a4d701d5640e",
    "message": "Statement executed successfully.",
    "createdOn": 1739073614097,
}

sample_extended_doc_descr = [
    "NCOURT    0BD507B4                      1000000000006888683 EPayACH",  # Payment with OverpaymentRecoveryType.EFT
    "NCOURT     9C6584E6                       1000000000006886335 EPayMC",  # Payment with OverpaymentRecoveryType.CREDIT_CARD
    "411605                                   1000000000006880656 Lockbox",  # Payment with OverpaymentRecoveryType.CHECK
]


def generate_mock_repayment_response_from_db(
    db_session: db.Session, edm_repayment_request: models.vendor.EDMRepaymentRequest
) -> models.vendor.EDMRepaymentResponse:
    _faker = faker.Faker()

    # fetch all succesfully submitted MMARS events for the given date range
    submitted_mmars_events = (
        db_session.query(MmarsEvent)
        .filter(
            and_(
                MmarsEvent.mmars_status_type_id
                == MmarsEventStatusType.RE_SUCCESS.mmars_event_status_type_id,
                MmarsEvent.updated_at >= edm_repayment_request.start_timestamp,
                MmarsEvent.updated_at <= edm_repayment_request.end_timestamp,
            )
        )
        .options(
            # only load the `amount` and `ctr_doc_id` columns from overpayment table
            joinedload(MmarsEvent.overpayment).load_only(
                Overpayment.outstanding_amount, Overpayment.ctr_doc_id
            ),
            joinedload(MmarsEvent.overpayment)
            .joinedload(Overpayment.employee)
            .load_only(Employee.first_name, Employee.last_name),
        )
        .order_by(MmarsEvent.updated_at)
        .all()
    )

    edm_repayment_detail_list: list[EDMRepaymentData] = []

    # If no submitted MMARS events found, mimic the response where EDM sends an record with all values as None
    if not submitted_mmars_events:
        edm_repayment_detail_list.append(EDMRepaymentData())
    else:
        for i, submitted_mmars_event in enumerate(submitted_mmars_events):
            edm_repayment_detail = EDMRepaymentData()
            edm_repayment_detail.LINE_AMOUNT = str(
                submitted_mmars_event.overpayment.outstanding_amount
            )
            # In production, DOC_IDENTIFIER is a unique identifier that MMARS assigns.
            # Assigning `mmars_event_id` in Mock will prevent duplicate records collection creation
            edm_repayment_detail.DOC_IDENTIFIER = str(submitted_mmars_event.mmars_event_id)

            # Assign one day after the updated_at day. If it exceeds today, then make it today.
            next_day = (submitted_mmars_event.updated_at + timedelta(days=1)).date()
            edm_repayment_detail.PAYMENT_DATE = min(
                next_day, datetime.now(timezone.utc).date()
            ).strftime(delegated_payments_util.MMARS_Constants.PAYMENT_DATE_FORMAT)

            edm_repayment_detail.REF_DOC_IDENTIFIER = submitted_mmars_event.overpayment.ctr_doc_id

            # Assign EXTENDED_DOC_DESCR using modulo operator to cycle through sample_extended_doc_descr
            edm_repayment_detail.EXTENDED_DOC_DESCR = sample_extended_doc_descr[
                i % len(sample_extended_doc_descr)
            ]

            if not edm_repayment_detail.EXTENDED_DOC_DESCR.endswith("EPayMC"):
                edm_repayment_detail.CHECK_EFT_NO = f"{_faker.random_number(digits=8)}"

            if submitted_mmars_event.overpayment.employee:
                edm_repayment_detail.LEGAL_NAME = f"{submitted_mmars_event.overpayment.employee.first_name} {submitted_mmars_event.overpayment.employee.last_name}"
            else:
                edm_repayment_detail.LEGAL_NAME = "Unknown"

            edm_repayment_detail_list.append(edm_repayment_detail)

    logger.info(f"Generating mock repayment response from db for {edm_repayment_request}")

    payment_response = models.vendor.EDMRepaymentResponse(data=[])
    payment_response.data = edm_repayment_detail_list

    return payment_response


class MockEDMClient(client.AbstractEDMClient):

    def __init__(self, db_session: Optional[db.Session] = None):

        # passing db_session to the create_client method will start creating mock repayment response data
        # by fetching data from the database rather than providing a static mock response.
        # this will allow us to test the sync_overpayment_collection_step with real data from the database
        self.db_session = db_session
        logger.info("MockEDMClient initialized with db_session")

    def get_vendor_information(
        self, edm_vendor_request: models.vendor.EDMVendorRequest
    ) -> models.vendor.EDMVendorResponse:
        logger.info(f"MockEDMClient.get_vendor_information called with {edm_vendor_request}")
        mock_response = models.vendor.EDMVendorResponse.parse_obj(mock_vendor_data_json)
        if mock_response.data:
            if edm_vendor_request.vcc_code:
                mock_response.data[0].VENDOR_CUSTOMER_CODE = edm_vendor_request.vcc_code
            if edm_vendor_request.tin:
                mock_response.data[0].TIN = edm_vendor_request.tin
        return mock_response

    def get_repayment_information(
        self, edm_repayment_request: models.vendor.EDMRepaymentRequest
    ) -> models.vendor.EDMRepaymentResponse:
        logger.info(f"MockEDMClient.get_repayment_information called with {edm_repayment_request}")

        if self.db_session:
            mock_response = generate_mock_repayment_response_from_db(
                self.db_session, edm_repayment_request
            )
        else:
            mock_response = models.vendor.EDMRepaymentResponse.parse_obj(mock_repayment_data_json)
        return mock_response


massgov.pfml.util.logging.wrapper.log_all_method_calls(MockEDMClient, logger)
