#
# EDM client.
#
import datetime
import os
import re
import urllib
from typing import Any, Mapping, Optional

import oauthlib.oauth2
import requests
import requests_oauthlib

import massgov.pfml.util.logging
import massgov.pfml.util.logging.wrapper
from massgov.pfml import db
from massgov.pfml.delegated_payments import delegated_payments_util
from massgov.pfml.util.newrelic.events import record_custom_event

from . import client, exception, mock_client, models

logger = massgov.pfml.util.logging.get_logger(__name__)
MILLISECOND = datetime.timedelta(milliseconds=1)
RE_NUMBER = re.compile(r"[0-9]{2,}")


class EDMClientConfig(massgov.pfml.util.pydantic.PydanticBaseSettings):
    api_base_url: Optional[str]
    oauth2_url: Optional[str]
    oauth2_client_id: Optional[str]
    oauth2_client_secret: Optional[str]
    oauth2_scope: Optional[str]
    code: Optional[str]
    refresh_token: Optional[str]

    class Config:
        env_prefix = "EDM_CLIENT_"


def create_client(
    config: Optional[EDMClientConfig] = None, db_session: Optional[db.Session] = None
) -> client.AbstractEDMClient:
    """Factory to create the right type of client object for the given configuration."""
    if config is None:
        config = EDMClientConfig()

    # db_session should only be passed for testing purposes by creating a mock data from database
    # rather than providing a static mock response
    if db_session:
        if os.getenv("ENVIRONMENT") == "prod":
            raise Exception(
                "db_session provided in production environment. "
                "db_session should only be used for Mock EDM client in testing environments."
            )

        mock_edm_repayment_response_enabled = (
            delegated_payments_util.is_mock_edm_repayment_response_enabled()
        )
        if mock_edm_repayment_response_enabled:
            logger.warning("using mock EDM client")
            return mock_client.MockEDMClient(db_session)
        else:
            raise Exception(
                "db_session provided without ENABLE_MOCK_EDM_REPAYMENT_RESPONSE set to true. "
                "db_session should only be used for Mock EDM client in testing environments."
            )

    if config.api_base_url:
        backend = oauthlib.oauth2.BackendApplicationClient(client_id=config.oauth2_client_id)
        oauth_session = requests_oauthlib.OAuth2Session(client=backend)

        return EDMClient(
            edm_api_base_url=config.api_base_url,
            oauth_session=oauth_session,
            oauth2_url=config.oauth2_url,
            client_id=config.oauth2_client_id,
            client_secret=config.oauth2_client_secret,
            code=config.code,
            refresh_token=config.refresh_token,
        )
    else:
        if os.getenv("ENVIRONMENT") == "local":
            logger.warning("using mock EDM client")
            return mock_client.MockEDMClient()
        else:
            # The mock client should only ever be used for tests and local development of the API
            # It should always result in the an exception if used in any other environment
            raise Exception(
                "Mock EDM client should only get used in tests or for local development"
            )


class EDMClient(client.AbstractEDMClient):
    """EDM API client."""

    edm_api_base_url: str
    oauth_session: Any

    def __init__(
        self,
        edm_api_base_url,
        oauth_session,
        oauth2_url,
        client_id,
        client_secret,
        code,
        refresh_token,
    ):
        self.edm_api_base_url = edm_api_base_url
        self.oauth_session = oauth_session
        self.oauth2_url = oauth2_url
        self.client_id = client_id
        self.client_secret = client_secret
        self.code = code
        self.refresh_token = refresh_token
        self._init_oauth_session()

    def __repr__(self):
        return "<EDMClient %s>" % urllib.parse.urlparse(self.edm_api_base_url).hostname

    def _init_oauth_session(self):
        """Set up an OAuth session and get a token."""
        try:
            token = self.oauth_session.refresh_token(
                self.oauth2_url,
                refresh_token=self.refresh_token,
                client_id=self.client_id,
                client_secret=self.client_secret,
                code=self.code,
            )
        except (
            oauthlib.oauth2.OAuth2Error,
            requests.exceptions.RequestException,
            requests.exceptions.Timeout,
        ) as ex:
            self._handle_client_side_exception("POST", self.oauth2_url, ex, "init_oauth_session")

        logger.info(
            "POST %s => type %s, expires %is",
            self.oauth2_url,
            token["token_type"],
            token["expires_in"],
        )

    def _handle_client_side_exception(
        self, method: str, url: str, ex: Exception, method_name: str
    ) -> None:
        # Make sure New Relic records errors from EDM, even if the API does not ultimately
        # return an error.
        record_custom_event(
            "EdmError",
            {
                "edm.error.class": type(ex).__name__,
                "edm.error.message": str(ex),
                "edm.request.method": method,
                "edm.request.uri": url,
            },
        )

        if isinstance(
            ex,
            (
                requests.exceptions.Timeout,
                oauthlib.oauth2.TemporarilyUnavailableError,
                requests.exceptions.ConnectionError,
            ),
        ):
            logger.warning("%s %s => %r", method, url, ex)
            raise exception.EDMFatalUnavailable(method_name=method_name, cause=ex)
        else:
            logger.exception("%s %s => %r", method, url, ex)
            raise exception.EDMFatalClientSideError(method_name=method_name, cause=ex)

    def _request(
        self,
        method: str,
        url: str,
        method_name: str,
        headers: Mapping[str, Optional[str]],
        body: Optional[dict[str, Optional[str]]] = None,
        **args: Any,
    ) -> requests.Response:
        """Make a request and handle errors."""
        log_extra: dict[str, Any] = {
            "edm.request.method": method,
            "edm.request.url": url,
            "edm.request.url_rule": RE_NUMBER.sub("%", url),
        }
        if logger.isEnabledFor(massgov.pfml.util.logging.DEBUG):
            log_extra_debug: dict[str, Any] = {
                "edm.request.headers": headers,
            }
            logger.debug("%s %s start", method, url, extra=log_extra | log_extra_debug)

        request_timeout = 10

        try:
            try:
                response = self.oauth_session.request(
                    method, url, timeout=(6.1, request_timeout), headers=headers, json=body, **args
                )
            except oauthlib.oauth2.TokenExpiredError:
                logger.info("token expired, starting new OAuth session")
                self._init_oauth_session()
                response = self.oauth_session.request(
                    method, url, timeout=(6.1, request_timeout), headers=headers, json=body, **args
                )
        except (requests.exceptions.RequestException, requests.exceptions.Timeout) as ex:
            self._handle_client_side_exception(method, url, ex, method_name)

        log_extra |= {
            "edm.response.status_code": response.status_code,
            "edm.response.response_time_ms": int(response.elapsed / MILLISECOND),
            "edm.response.length": len(response.content),
        }
        not_ok = (
            response.status_code != requests.codes.ok
            and response.status_code != requests.codes.created
        )

        if not_ok:

            log_extra |= {
                "edm.response.headers": response.headers,
                "edm.response.text": response.text,
            }
            # EDM returned an error. Record it in New Relic before raising the exception.

            base_edm_error_payload = {
                "edm.error.class": "EDMClientBadResponse",
                "edm.error.message": response.text,
                "edm.response.status": response.status_code,
                "edm.request.method": method,
                "edm.request.uri": url,
                "edm.request.response_millis": response.elapsed / MILLISECOND,
            }

            record_custom_event(
                "EdmError",
                base_edm_error_payload,
            )

            err: exception.EDMClientError

            if (
                response.status_code
                in (requests.codes.SERVICE_UNAVAILABLE, requests.codes.GATEWAY_TIMEOUT)
                or "ESOCKETTIMEDOUT" in response.text
            ):
                # The service is unavailable for some reason. Log a warning. There should be a
                # percentage-based alarm for when there are too many of these.
                err = exception.EDMFatalUnavailable(
                    response_status=response.status_code,
                    message=response.text,
                    method_name=method_name,
                )
                log_fn = logger.warning
            elif response.status_code == requests.codes.NOT_FOUND:
                err = exception.EDMNotFound(
                    method_name, requests.codes.ok, response.status_code, message=response.text
                )
                log_fn = logger.warning
            elif response.status_code == requests.codes.FORBIDDEN:
                err = exception.EDMForbidden(
                    method_name, requests.codes.ok, response.status_code, message=response.text
                )
                log_fn = logger.warning
            else:
                # We should never see anything other than these. Log an error if we do. These include issues
                # like 400 BAD REQUEST (misformatted request), and 500 INTERNAL SERVER ERROR.

                message = response.text
                err = exception.EDMFatalResponseError(
                    response_status=response.status_code,
                    message=message,
                    method_name=method_name,
                )
                log_fn = logger.error

            log_fn(
                "%s %s => %s (%ims)",
                method,
                url,
                response.status_code,
                response.elapsed / MILLISECOND,
                extra=log_extra,
                exc_info=err,
            )
            raise err

        logger.info(
            "%s %s => %s (%ims)",
            method,
            url,
            response.status_code,
            response.elapsed / MILLISECOND,
            extra=log_extra,
        )
        return response

    def _vendor_api(
        self,
        method: str,
        url: str,
        method_name: str,
        body_statement: str,
        header_content_type: Optional[str] = "application/json",
        **args: Any,
    ) -> requests.Response:
        """Make a request to the Account Protection API."""
        content_type_header = {"Content-Type": header_content_type} if header_content_type else {}
        headers = dict({}, **content_type_header)
        body: dict[str, Any] = dict()
        body["statement"] = body_statement
        body["timeout"] = "60"
        body["resultSetMetaData"] = {"format": "json"}

        response = self._request(method, url, method_name, headers, body, **args)
        return response

    def get_vendor_information(
        self, edm_vendor_request: models.vendor.EDMVendorRequest
    ) -> models.vendor.EDMVendorResponse:
        logger.info(f"EDMClient.get_vendor_information called with {edm_vendor_request}")
        if edm_vendor_request.vcc_code:
            where_clause = f"WHERE VENDOR_CUSTOMER_CODE = '{edm_vendor_request.vcc_code}'"
        elif edm_vendor_request.tin:
            where_clause = f"WHERE TIN = '{edm_vendor_request.tin}'"
        else:
            raise ValueError("At least one of 'vcc_code' or 'tin' must be populated.")

        # snowflake query to pass in edm endpoint
        columns = models.vendor.EDMVendorData().dict().keys()
        object_contsruct = [f"'{col}', {col}" for col in columns]
        # snowflake query created "select OBJECT_CONSTRUCT('column_name', column_name) from VW_VCCDATA where VENDOR_CUSTOMER_CODE = 'vcc_code'" # nosec
        body_statement = f"SELECT OBJECT_CONSTRUCT({', '.join(object_contsruct)}) FROM VW_VCCDATA {where_clause}"  # nosec
        response = self._vendor_api(
            "POST", self.edm_api_base_url, "get_vendor_information", body_statement
        )
        json = response.json()
        vendor_response = models.vendor.EDMVendorResponse.parse_obj(json)
        return vendor_response

    def get_repayment_information(
        self, edm_repayment_request: models.vendor.EDMRepaymentRequest
    ) -> models.vendor.EDMRepaymentResponse:
        logger.info(f"EDMClient.get_repayment_information called with {edm_repayment_request}")
        params = dict()
        params["startTimestamp"] = edm_repayment_request.format_date(
            edm_repayment_request.start_timestamp
        )
        params["endTimestamp"] = edm_repayment_request.format_date(
            edm_repayment_request.end_timestamp
        )

        # snowflake query to pass in edm endpoint
        columns = models.vendor.EDMRepaymentData().dict().keys()
        object_contsruct = [f"'{col}', {col}" for col in columns]
        # snowflake query created "select OBJECT_CONSTRUCT('column_name', column_name) from VW_REPAYMENT_TRANSACTIONS where DATE_TO_WAREHOUSE >= 'startTimestamp' AND DATE_TO_WAREHOUSE <= 'endTimestamp'" # nosec
        body_statement = f"select OBJECT_CONSTRUCT({', '.join(object_contsruct)}) from VW_REPAYMENT_TRANSACTIONS WHERE DATE_TO_WAREHOUSE >= '{params['startTimestamp']}' AND DATE_TO_WAREHOUSE <= '{params['endTimestamp']}'"  # nosec
        response = self._vendor_api(
            "POST", self.edm_api_base_url, "get_repayment_information", body_statement
        )
        json = response.json()
        repayment_response = models.vendor.EDMRepaymentResponse.parse_obj(json)
        return repayment_response
