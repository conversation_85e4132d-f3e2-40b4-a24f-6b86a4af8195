#
# EDM client - abstract base class.
#

import abc

from . import models


class AbstractEDMClient(abc.ABC):
    """Abstract base class for a EDM API client."""

    @abc.abstractmethod
    def get_vendor_information(
        self, edm_vendor_request: models.vendor.EDMVendorRequest
    ) -> models.vendor.EDMVendorResponse:
        """EDM call to get vendor information"""
        pass

    @abc.abstractmethod
    def get_repayment_information(
        self, edm_repayment_request: models.vendor.EDMRepaymentRequest
    ) -> models.vendor.EDMRepaymentResponse:
        """EDM call to get repayment information"""
        pass
