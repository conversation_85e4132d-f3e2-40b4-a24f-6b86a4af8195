openapi: 3.0.3
info:
  title: Paid Leave API
  description: An API for managing leave within Massachusetts Paid Family and Medical Leave.
  version: "2022-11-17"
  contact:
    name: "EOL DL DFML PFML SERVICE DESK"
    url: "https://mass.gov/PaidLeave"
    email: "<EMAIL>"

servers:
  - url: /v1
    description: Development server

security:
  - jwt: [] # JWT authentication option

paths:
  /status:
    get:
      security:
        - {} # no authentication option
      tags:
        - Test Endpoints
      summary: Get the API status
      operationId: massgov.pfml.api.status.status_get
      responses:
        "200":
          description: A successful request returns a 200 status code and a user object.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SuccessfulResponse"
        "503":
          $ref: "#/components/responses/ServiceUnavailableError"

  /flags:
    get:
      security:
        - {} # no authentication option
      tags:
        - Feature Flags
      summary: Get feature flags
      operationId: massgov.pfml.api.flags.flags_get
      responses:
        "200":
          description: A successful request returns a 200 status code and list of flags.
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/Flag"
        "503":
          $ref: "#/components/responses/ServiceUnavailableError"
  /flags/{name}:
    get:
      security:
        - {} # no authentication option
      tags:
        - Feature Flags
      summary: Get a feature flag
      operationId: massgov.pfml.api.flags.flag_get
      parameters:
        - name: name
          in: path
          schema:
            type: string
          description: Name of the flag to get
          required: true
      responses:
        "200":
          description: A successful request returns a 200 status code and one flag.
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/Flag"
        "503":
          $ref: "#/components/responses/ServiceUnavailableError"

  /roles:
    delete:
      tags:
        - Roles
        - Users
      summary: Remove a role from a user
      operationId: massgov.pfml.api.roles.roles_users_delete
      responses:
        "200":
          description: Role deleted without issue
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SuccessfulResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "404":
          $ref: "#/components/responses/NotFound"
      requestBody:
        $ref: "#/components/requestBodies/RoleUserDeleteRequest"

  /users/{user_id}/convert-employer:
    post:
      tags:
        - Users
      summary: Convert a User account to an employer role
      operationId: massgov.pfml.api.users.users_convert_employer
      parameters:
        - name: user_id
          in: path
          schema:
            type: string
          description: ID of User to update
          required: true
      responses:
        "201":
          description: User converted without errors
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "404":
          $ref: "#/components/responses/NotFound"
      requestBody:
        $ref: "#/components/requestBodies/EmployerAddFeinRequestBody"

  /users/current:
    get:
      tags:
        - Users
      summary: >
        Retrieve the User account corresponding to the currently authenticated
        user
      operationId: massgov.pfml.api.users.users_current_get
      responses:
        "200":
          description: A successful request returns a 200 status code and a user object.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserResponse"

  /users/{user_id}:
    get:
      tags:
        - Users
      summary: Retrieve a User account
      operationId: massgov.pfml.api.users.users_get
      parameters:
        - name: user_id
          in: path
          schema:
            type: string
            format: uuid
          description: ID of User to update
          required: true
      responses:
        "200":
          description: A successful request returns a 200 status code and a user object.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserResponse"
        "404":
          $ref: "#/components/responses/NotFound"
    patch:
      tags:
        - Users
      summary: Update a User account
      operationId: massgov.pfml.api.users.users_patch
      parameters:
        - name: user_id
          in: path
          schema:
            type: string
          description: ID of User to update
          required: true
      responses:
        "200":
          description: A successful request returns a 200 status code and a user object.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserResponse"
        "404":
          $ref: "#/components/responses/NotFound"
      requestBody:
        $ref: "#/components/requestBodies/UserUpdateRequest"

  /users/{user_id}/documents:
    get:
      summary: List of customer documents retrieved from FINEOS for user.
      operationId: massgov.pfml.api.users.get_documents
      tags:
        - Users
      parameters:
        - name: user_id
          in: path
          schema:
            type: string
            example: "009fa369-291b-403f-a85a-15e938c26f2f"
          description: The id of the user we're retrieving documents for
          required: true
        - name: document_type
          in: query
          schema:
            $ref: "#/components/schemas/DocumentType"
          description: The filtered list of one document type
          required: false
      responses:
        "200":
          description: A successful request returns a 200 status code and a list of relevant tax documents.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        type: array
                        items:
                          $ref: "#/components/schemas/DocumentResponse"
        "400":
          description: An unsuccessful request returns a 400 status code if one or more errors occurred.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "403":
          description: An unsuccessful request returns a 403 status code if there are auth failures.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /users/{user_id}/documents/{fineos_document_id}:
    get:
      summary: Download a document by fineos_document_id.
      operationId: massgov.pfml.api.users.download_document
      tags:
        - Users
      parameters:
        - name: user_id
          in: path
          schema:
            type: string
            format: uuid
          description: ID of User to download document for
          required: true
        - name: fineos_document_id
          in: path
          schema:
            type: string
            example: "13453-124"
          description: The fineos document id
          required: true
      responses:
        "200":
          $ref: "#/components/responses/StreamContentDownload"
        "400":
          description: An unsuccessful request returns a 400 status code if one or more errors occurred.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "403":
          description: An unsuccessful request returns a 403 status code if there are auth failures.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /employees/{employee_id}:
    get:
      security:
        - jwt: []
        - oauth2: []
          agent-id: []
      tags:
        - Employees
      summary: Retrieve an Employee record
      operationId: massgov.pfml.api.employees.employees_get
      parameters:
        - name: employee_id
          in: path
          schema:
            type: string
          description: ID of Employee to retrieve
          required: true
      responses:
        "200":
          description: A successful request returns a 200 status code and an employee object.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/EmployeeResponse"
        "404":
          description: An unsuccessful request returns a 404 status code for employees that are not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EmployeeErrorResponse"

  /employees/search:
    post:
      security:
        - jwt: []
        - oauth2: []
          agent-id: []
      tags:
        - Employees
      summary: Lookup Employees
      operationId: massgov.pfml.api.employees.employees_search
      responses:
        "200":
          description: A successful response returns a 200 status code and an employee object.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/EmployeesResponse"
        "404":
          $ref: "#/components/responses/NotFound"
      requestBody:
        $ref: "#/components/requestBodies/EmployeeSearchRequest"

  /employers/add:
    post:
      tags:
        - Employers
      summary: Add an FEIN to the logged in Leave Administrator
      operationId: massgov.pfml.api.employers.employer_add_fein
      requestBody:
        $ref: "#/components/requestBodies/EmployerAddFeinRequestBody"
      responses:
        "201":
          description: FEIN added without errors
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/EmployerResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "402":
          description: No verification data for FEIN
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/ErrorResponse"
        "409":
          description: Duplicate employer for user
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/ErrorResponse"
        "503":
          description: Error while adding FEIN
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/ErrorResponse"

  /employers/withholding/{employer_id}:
    get:
      summary: Retrieves the last withholding date for the FEIN specified
      operationId: massgov.pfml.api.employers.employer_get_most_recent_withholding_dates
      tags:
        - Employers
      parameters:
        - name: employer_id
          in: path
          schema:
            type: string
            format: uuid
            example: "009fa369-291b-403f-a85a-15e938c26f2f"
          description: "ID of the employer"
          required: true
      responses:
        "200":
          description: A successful request returns a 200 status code.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/WithholdingResponse"
        "400":
          description: An unsuccessful request returns a 400 status code if one or more errors occurred.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /employers/{employer_id}/employees/{employee_id}/leave-allotment:
    get:
      security:
        - jwt: []
        - oauth2: []
          agent-id: []
      tags:
        - Employers
      summary: Retrieve an Employee leave allotment record
      operationId: massgov.pfml.api.employers.get_leave_allotment
      parameters:
        - name: employee_id
          in: path
          schema:
            type: string
          description: ID of Employee leave allotment to retrieve
          required: true
        - name: employer_id
          in: path
          schema:
            type: string
          description: ID of Employee associated Employer
          required: true
      responses:
        "200":
          description: A successful request returns a 200 status code and a leave_availability_response object.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/EmployeeLeaveAllotmentResponse"
        "404":
          description: An unsuccessful request returns a 404 status code if no employee or employer are found for the requested id.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "500":
          description: Internal server error

  /claims/{fineos_absence_id}:
    get:
      summary: Retrieve a claim for a specified absence ID
      operationId: massgov.pfml.api.claims.get_claim
      tags:
        - Claims
      parameters:
        - name: fineos_absence_id
          in: path
          schema:
            type: string
            example: "NTN-00-ABS-00"
          description: "FINEOS absence ID of the claim data to be retrieved"
          required: true
      responses:
        "200":
          description: The claim information.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/DetailedClaimResponse"
        "400":
          description: An unsuccessful request returns a 400 status code if one or more errors occurred.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /claims:
    get:
      deprecated: true
      security:
        - jwt: []
        - oauth2: []
          agent-id: []
      summary: Retrieve claims
      description: |
        Deprecated. For new integrations use `POST /claims/search`.
      operationId: massgov.pfml.api.claims.get_claims
      tags:
        - Claims
      parameters:
        - $ref: "#/components/parameters/pageSize"
        - $ref: "#/components/parameters/pageOffset"
        - $ref: "#/components/parameters/orderBy"
        - $ref: "#/components/parameters/orderDirection"
        - name: employer_id
          in: query
          schema:
            type: array
            items:
              type: string
              format: uuid
          description: ID(s) of Employers to query claims for
          required: false
          explode: false
        - name: employee_id
          in: query
          schema:
            type: array
            items:
              type: string
              format: uuid
          description: ID(s) of Employee to query claims for
          required: false
          explode: false
        - name: search
          in: query
          schema:
            type: string
          description: >
            A search string used to filter the request that,
            when provided, is used in a fuzzy search against the claims'
            Absence Case IDs, in addition to the employees' first, middle, and last names.
            When the string is empty, it is ignored.
          required: false
        - name: is_reviewable
          in: query
          schema:
            type: string
            enum: ["yes", "no"]
          description: A string value used to check whether or not to filter claims where a review is requested for a leave admin.
        - name: request_decision
          in: query
          schema:
            type: string
            enum: ["approved", "denied", "withdrawn", "pending", "cancelled"]
          description: >
            request decision used in filtering, request_decision is a status associated with an absence_period
          required: false
        - name: to_do
          in: query
          schema:
            type: array
            items:
              type: string
              enum: ["review_due", "no_action"]
          description: Filter claims by to-do status, replacing the previous "is_reviewable" filter
          required: false
        - name: application_status
          in: query
          schema:
            type: array
            items:
              type: string
              enum: ["approved", "denied", "withdrawn", "pending", "cancelled"]
          description: Filter claims by application status, replacing the previous "request_decision" filter
          required: false
      responses:
        "200":
          description: The claim information.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/ClaimsResponse"
        "400":
          description: An unsuccessful request returns a 400 status code if one or more errors occurred.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /claims/search:
    post:
      security:
        - jwt: []
        - oauth2: []
          agent-id: []
      summary: Retrieve claims
      operationId: massgov.pfml.api.claims.retrieve_claims
      tags:
        - Claims
      requestBody:
        $ref: "#/components/requestBodies/ClaimSearchRequest"
      responses:
        "200":
          description: The claim information.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/ClaimsResponse"
        "400":
          description: An unsuccessful request returns a 400 status code if one or more errors occurred.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /claims/match-cert-doc-to-claim:
    post:
      summary: Search claims by claimant info to match certification document to a claim
      operationId: massgov.pfml.api.documents.match_cert_doc_to_claim
      tags:
        - Documents
        - Claims
      requestBody:
        $ref: "#/components/requestBodies/CertificationDocumentRequestBody"
      responses:
        "201":
          description: A successful request returns a 201
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                example:
                  meta:
                    method: "POST"
                    resource: "/notifications"
                  status_code: 201
                  message: "success"
        "404":
          $ref: "#/components/responses/NotFound"
        "400":
          description: An unsuccessful request returns a 400 status code if one or more errors occurred.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /change-request:
    post:
      summary: Create a change request record
      operationId: massgov.pfml.api.change_requests.post_change_request
      tags:
        - ChangeRequest
      parameters:
        - name: fineos_absence_id
          in: query
          schema:
            type: string
            example: "NTN-00-ABS-00"
          required: true
      requestBody:
        $ref: "#/components/requestBodies/ChangeRequest"
      responses:
        "201":
          description: A successful request returns a 201
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/ChangeRequestResponse"
        "404":
          $ref: "#/components/responses/NotFound"
        "400":
          description: An unsuccessful request returns a 400 status code if one or more errors occurred.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

    get:
      summary: Get list of change requests for a claim
      operationId: massgov.pfml.api.change_requests.get_change_requests
      tags:
        - ChangeRequest
      parameters:
        - name: fineos_absence_id
          in: query
          schema:
            type: string
            example: "NTN-00-ABS-00"
          description: "FINEOS absence ID of the claim associated with change request data to be retrieved"
          required: false
      responses:
        "200":
          description: The change requests for this absence case.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        type: object
                        properties:
                          absence_case_id:
                            type: string
                            example: "NTN-111-ABS-01"
                            nullable: false
                          change_requests:
                            $ref: "#/components/schemas/ChangeRequestsResponse"

  /report-intermittent-leave-episode:
    post:
      security:
        - jwt: []
      summary: Create a leave episode record and submit request to FINEOS
      operationId: massgov.pfml.api.submit_leave_episode.submit_leave_episode
      tags:
        - Claims
      parameters:
        - name: absence_case_id
          in: query
          required: true
          schema:
            type: string
            example: "NTN-00-ABS-00"
      requestBody:
        $ref: "#/components/requestBodies/IntermittentLeaveEpisodeRequest"
      responses:
        "200":
          description: Intermittent leave reported successfully
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/IntermittentLeaveEpisodesResponse"
        "400":
          $ref: "#/components/responses/BadRequest"

  /change-request/{change_request_id}:
    delete:
      summary: Delete an in-progress ChangeRequest
      operationId: massgov.pfml.api.change_requests.delete_change_request
      tags:
        - ChangeRequest
      parameters:
        - name: change_request_id
          in: path
          schema:
            type: string
            format: uuid
            example: 5f91c12b-4d49-4eb0-b5d9-7fa0ce13eb32
          description: ID of the change request to delete
          required: true
      responses:
        "200":
          description: Change Request successfully deleted
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SuccessfulResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "404":
          $ref: "#/components/responses/NotFound"
    patch:
      summary: Update an in-progress ChangeRequest
      operationId: massgov.pfml.api.change_requests.update_change_request
      tags:
        - ChangeRequest
      parameters:
        - name: change_request_id
          in: path
          schema:
            type: string
            format: uuid
            example: 5f91c12b-4d49-4eb0-b5d9-7fa0ce13eb32
          description: ID of the change request to update
          required: true
      requestBody:
        $ref: "#/components/requestBodies/ChangeRequest"
      responses:
        "200":
          description: Change Request successfully updated
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/ChangeRequestResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "404":
          $ref: "#/components/responses/NotFound"

  /change-request/{change_request_id}/submit:
    post:
      summary: Submit a completed change request to FINEOS
      operationId: massgov.pfml.api.change_requests.submit_change_request
      tags:
        - ChangeRequest
      parameters:
        - name: change_request_id
          in: path
          schema:
            type: string
            format: uuid
          description: ID of Change Request to submit
          required: true
      responses:
        "200":
          description: A successful request returns a 200
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/ChangeRequestResponse"
        "404":
          $ref: "#/components/responses/NotFound"
        "400":
          description: An unsuccessful request returns a 400 status code if one or more errors occurred.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /change-request/{change_request_id}/documents:
    post:
      summary: Upload Document
      operationId: massgov.pfml.api.change_requests.upload_document_for_change_request
      tags:
        - ChangeRequest
        - Documents
      parameters:
        - name: change_request_id
          in: path
          schema:
            type: string
            format: uuid
            example: 5f91c12b-4d49-4eb0-b5d9-7fa0ce13eb32
          description: ID of Change Request to submit documents for
          required: true
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: "#/components/schemas/DocumentUploadRequest"
            encoding:
              file:
                contentType: application/pdf, image/jpeg, image/png, image/tiff, image/heic
      responses:
        "200":
          description: A successful request returns a 200 status code.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/DocumentResponse"
        "400":
          description: An unsuccessful request returns a 400 status code if one or more errors occurred.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /payments:
    get:
      summary: Retrieve payments with status for a specified absence ID
      operationId: massgov.pfml.api.payments.get_payments
      tags:
        - Claims
      parameters:
        - name: absence_case_id
          in: query
          schema:
            type: string
            example: "NTN-00-ABS-00"
          description: "FINEOS absence ID of the claim associated with payment data to be retrieved"
          required: true
      responses:
        "200":
          description: The payments for this absence case.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/PaymentsResponse"
  /waiting-periods/search:
    post:
      security:
        - jwt: []
      summary: Retrieve waiting periods for a specified absence ID
      operationId: massgov.pfml.api.waiting_periods.get_waiting_periods
      requestBody:
        $ref: "#/components/requestBodies/WaitingPeriodRequestBody"
      tags:
        - Claims
      responses:
        "200":
          description: The waiting periods for this absence case.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/WaitingPeriodsResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "404":
          $ref: "#/components/responses/NotFound"

  /employers/claims/{fineos_absence_id}/documents/{fineos_document_id}:
    get:
      summary: Retrieve a FINEOS documents for a specified absence ID and document ID
      operationId: massgov.pfml.api.employer_claim.employer_document_download
      tags:
        - Claims
      parameters:
        - name: fineos_absence_id
          in: path
          schema:
            type: string
            example: "NTN-00-ABS-00"
          description: "FINEOS absence ID of the claim data to be retrieved"
          required: true
        - name: fineos_document_id
          in: path
          schema:
            type: string
            example: "1111"
          description: "FINEOS document ID pertaining to the claim that will be retrieved"
          required: true
      responses:
        "200":
          $ref: "#/components/responses/StreamContentDownload"
        "400":
          description: An unsuccessful request returns a 400 status code if one or more errors occurred.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /employers/claims/{fineos_absence_id}/documents:
    get:
      summary: Retrieve a list of FINEOS documents for a specified absence ID
      operationId: massgov.pfml.api.employer_claim.employer_get_claim_documents
      tags:
        - Claims
      parameters:
        - name: fineos_absence_id
          in: path
          schema:
            type: string
            example: "NTN-00-ABS-00"
          description: "FINEOS absence ID of the claim data to be retrieved"
          required: true
      responses:
        "200":
          description: A successful request returns a 200 status code.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        type: array
                        items:
                          $ref: "#/components/schemas/ClaimDocumentResponse"
        "400":
          description: An unsuccessful request returns a 400 status code if one or more errors occurred.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /employers/claims/{fineos_absence_id}/review:
    get:
      summary: Retrieve FINEOS claim review data for a specified absence ID
      operationId: massgov.pfml.api.employer_claim.employer_get_claim_review
      tags:
        - Claims
      parameters:
        - name: fineos_absence_id
          in: path
          schema:
            type: string
            example: "NTN-00-ABS-00"
          description: "FINEOS absence ID of the claim data to be retrieved"
          required: true
      responses:
        "200":
          description: A successful request returns a 200 status code.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/ClaimReviewResponse"
        "400":
          description: An unsuccessful request returns a 400 status code if one or more errors occurred.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          $ref: "#/components/responses/NotFound"
    patch:
      tags:
        - Claims
      summary: Save review claim from leave admin
      operationId: massgov.pfml.api.employer_claim.employer_update_claim_review
      parameters:
        - name: fineos_absence_id
          in: path
          schema:
            type: string
            example: "NTN-00-ABS-00"
          description: ID of absence to update
          required: true
      responses:
        "200":
          description: A successful request returns a 200 status code and a claim object.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/UpdateClaimReviewResponse"
        "404":
          $ref: "#/components/responses/NotFound"
      requestBody:
        $ref: "#/components/requestBodies/EmployerClaimRequestBody"

  /employers/claims/{fineos_absence_id}/validate:
    post:
      tags:
        - Claims
      summary: Validate review claim from leave admin
      operationId: massgov.pfml.api.employer_claim.employer_validate_claim_review
      parameters:
        - name: fineos_absence_id
          in: path
          schema:
            type: string
            example: "NTN-00-ABS-00"
          description: ID of absence to update
          required: true
      responses:
        "200":
          description: A successful request returns a 200 status code.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/UpdateClaimReviewResponse"
        "404":
          $ref: "#/components/responses/NotFound"
      requestBody:
        $ref: "#/components/requestBodies/EmployerClaimRequestBody"

  /application-imports:
    post:
      tags:
        - Application Import
      summary: >
        Creates a new application in the PFML database from a FINEOS application that was created through the
        contact center.
      operationId: massgov.pfml.api.application_imports.create_application_import
      responses:
        "201":
          $ref: "#/components/responses/ApplicationImportedResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "404":
          description: Application with requested absence_case_id was not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "503":
          description: Application could not be completed in Claims Processing System.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/ErrorResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/ApplicationResponse"
      requestBody:
        $ref: "#/components/requestBodies/ApplicationImportRequestBody"

  /applications:
    post:
      tags:
        - Applications
      summary: Create an Application
      operationId: massgov.pfml.api.applications.applications_start
      responses:
        "200":
          $ref: "#/components/responses/ApplicationCreatedResponse"
        "400":
          $ref: "#/components/responses/BadRequest"

    get:
      tags:
        - Applications
      summary: Retrieve all Applications for the specified user
      operationId: massgov.pfml.api.applications.applications_get
      parameters:
        - $ref: "#/components/parameters/pageOffset"
        - $ref: "#/components/parameters/orderBy"
        - $ref: "#/components/parameters/pageSize"
        - $ref: "#/components/parameters/orderDirection"
      responses:
        "200":
          $ref: "#/components/responses/ApplicationSearchResponse"

  /applications/{application_id}:
    get:
      tags:
        - Applications
      summary: Retrieve an Application identified by the application id
      operationId: massgov.pfml.api.applications.application_get
      parameters:
        - name: application_id
          in: path
          schema:
            type: string
            format: uuid
          description: ID of Application to retrieve
          required: true
      responses:
        "200":
          $ref: "#/components/responses/ApplicationResponse"
        "404":
          $ref: "#/components/responses/NotFound"

    patch:
      tags:
        - Applications
      summary: Update an Application
      description: >
        Supports partial update of an Application record.

        Note that array property updates are not partial, after processing the
        PATCH, the Application state will contain only the values of the array
        in the request.

        The behavior generally follows RFC 7396.

        It is expected clients will call this endpoint multiple times and with
        incomplete data as individual pieces of information is collected from
        the user. The API will review the data provided and return any pertinent
        warnings or errors.
      operationId: massgov.pfml.api.applications.applications_update
      parameters:
        - name: application_id
          in: path
          schema:
            type: string
            format: uuid
          description: ID of Application to update
          required: true
      responses:
        "200":
          description: Application updated without errors.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/ApplicationResponse"
        "400":
          description: An unsuccessful request returns a 400 status code if one or more errors occured, described in the error response.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
      requestBody:
        $ref: "#/components/requestBodies/ApplicationRequestBody"

  /applications/{application_id}/submit-application:
    post:
      tags:
        - Applications
      summary: >
        Submit the first part of the application to the Claims Processing System.
      operationId: massgov.pfml.api.applications.applications_submit
      parameters:
        - name: application_id
          in: path
          schema:
            type: string
            format: uuid
          description: ID of Application to submit
          required: true
      responses:
        "201":
          description: Application submitted without errors.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/ApplicationResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "503":
          description: Application could not be submitted to Claims Processing System.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/ErrorResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/ApplicationResponse"

  /applications/{application_id}/complete-application:
    post:
      tags:
        - Applications
      summary: >
        Complete intake of an application in the Claims Processing System.
      operationId: massgov.pfml.api.applications.applications_complete
      parameters:
        - name: application_id
          in: path
          schema:
            type: string
            format: uuid
          description: ID of Application to submit
          required: true
      requestBody:
        $ref: "#/components/requestBodies/ApplicationCompleteRequestBody"

      responses:
        "201":
          description: Application completed without errors.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/ApplicationResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "503":
          description: Application could not be completed in Claims Processing System.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/ErrorResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/ApplicationResponse"

  /applications/{application_id}/documents/{document_id}:
    get:
      summary: Download an application (case) document by id.
      operationId: massgov.pfml.api.applications.document_download
      tags:
        - Applications
      parameters:
        - name: application_id
          in: path
          schema:
            type: string
            format: uuid
            example: 5f91c12b-4d49-4eb0-b5d9-7fa0ce13eb32
          description: ID of Application the document is associated with
          required: true
        - name: document_id
          in: path
          schema:
            type: string
            example: "4074"
          description: The fineos document id
          required: true
      responses:
        "200":
          $ref: "#/components/responses/StreamContentDownload"
        "400":
          description: An unsuccessful request returns a 400 status code if one or more errors occurred.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /applications/{application_id}/documents:
    get:
      summary: Get list of documents for a case
      operationId: massgov.pfml.api.applications.documents_get
      tags:
        - Applications
        - Documents
      parameters:
        - name: application_id
          in: path
          schema:
            type: string
            format: uuid
            example: 5f91c12b-4d49-4eb0-b5d9-7fa0ce13eb32
          description: ID of Application to update
          required: true
      responses:
        "200":
          description: A successful request returns a 200 status code.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        type: array
                        items:
                          $ref: "#/components/schemas/DocumentResponse"
        "400":
          description: An unsuccessful request returns a 400 status code if one or more errors occurred.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
    post:
      summary: Upload Document
      operationId: massgov.pfml.api.applications.document_upload
      tags:
        - Applications
        - Documents
      parameters:
        - name: application_id
          in: path
          schema:
            type: string
            format: uuid
            example: 5f91c12b-4d49-4eb0-b5d9-7fa0ce13eb32
          description: ID of Application to update
          required: true
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: "#/components/schemas/DocumentUploadRequest"
            encoding:
              file:
                contentType: application/pdf, image/jpeg, image/png, image/tiff, image/heic
      responses:
        "200":
          description: A successful request returns a 200 status code.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/DocumentResponse"
        "400":
          description: An unsuccessful request returns a 400 status code if one or more errors occurred.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /applications/{application_id}/submit-payment-preference:
    post:
      summary: Submit Payment Preference
      operationId: massgov.pfml.api.applications.payment_preference_submit
      tags:
        - Applications
        - PaymentPreference
      parameters:
        - name: application_id
          in: path
          schema:
            type: string
            format: uuid
            example: 5f91c12b-4d49-4eb0-b5d9-7fa0ce13eb32
          description: ID of Application to update
          required: true
      requestBody:
        $ref: "#/components/requestBodies/PaymentPreferenceRequestBody"
      responses:
        "201":
          description: Successful payment submission returns a 201 status code.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/ApplicationResponse"
        "400":
          description: Invalid payment preference when attempting to submit to FINEOS.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: Application with requested application_id was not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "503":
          description: Payment preference failed to save.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /applications/{application_id}/get-customer-payment-preference:
    get:
      summary: Get Customer Payment Preferences. This should replace the paymentPreferences which is deprecated.
      operationId: massgov.pfml.api.applications.customer_payment_preference_get
      tags:
        - Applications
        - PaymentPreference
      parameters:
        - name: application_id
          in: path
          schema:
            type: string
            format: uuid
            example: 5f91c12b-4d49-4eb0-b5d9-7fa0ce13eb32
          description: ID of Application to update
          required: true
      responses:
        "201":
          description: Successful payment submission returns a 201 status code.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/ApplicationResponse"
        "400":
          description: Invalid payment preference when attempting to submit to FINEOS.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: Application with requested application_id was not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "503":
          description: Payment preference failed to save.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /applications/{application_id}/submit-customer-payment-preference:
    post:
      summary: Submit Customer Payment Preference. This should replace the submit-payment-preference which is deprecated.
      operationId: massgov.pfml.api.applications.customer_payment_preference_submit
      tags:
        - Applications
        - PaymentPreference
      parameters:
        - name: application_id
          in: path
          schema:
            type: string
            format: uuid
            example: 5f91c12b-4d49-4eb0-b5d9-7fa0ce13eb32
          description: ID of Application to update
          required: true
      requestBody:
        $ref: "#/components/requestBodies/PaymentPreferenceRequestBody"
      responses:
        "201":
          description: Successful payment submission returns a 201 status code.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/ApplicationResponse"
        "400":
          description: Invalid payment preference when attempting to submit to FINEOS.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: Application with requested application_id was not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "503":
          description: Payment preference failed to save.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /applications/{application_id}/update-customer-payment-preference:
    patch:
      summary: Update Customer Payment Preference.
      operationId: massgov.pfml.api.applications.customer_payment_preference_update
      tags:
        - Applications
        - PaymentPreference
      parameters:
        - name: application_id
          in: path
          schema:
            type: string
            format: uuid
            example: 5f91c12b-4d49-4eb0-b5d9-7fa0ce13eb32
          description: ID of Application to update
          required: true
      requestBody:
        $ref: "#/components/requestBodies/PaymentPreferenceRequestBody"
      responses:
        "200":
          description: Successful payment update returns a 201 status code.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/ApplicationResponse"
        "400":
          description: Invalid payment preference when attempting to submit to FINEOS.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: Application with requested application_id was not found.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "503":
          description: Payment preference failed to save.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /applications/{application_id}/submit-tax-withholding-preference:
    post:
      summary: Submit Tax Withholding Preference
      operationId: massgov.pfml.api.applications.submit_tax_withholding_preference
      tags:
        - Applications
        - TaxPreference
      parameters:
        - name: application_id
          in: path
          schema:
            type: string
            format: uuid
            example: 5f91c12b-4d49-4eb0-b5d9-7fa0ce13eb32
          description: ID of the application to update
          required: true
      requestBody:
        $ref: "#/components/requestBodies/TaxWithholdingPreferenceRequestBody"
      responses:
        "201":
          description: Successful submission of tax withholding preference returns a 201
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/ApplicationResponse"
        "400":
          description: Error when attempting to submit tax withholding preference to FINEOS
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: Application with requested application_id was not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "503":
          description: Tax withholding preference failed to save.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /appeals:
    post:
      tags:
        - Appeals
      summary: Create an appeal
      operationId: massgov.pfml.api.appeals.appeals_create
      responses:
        "201":
          description: Appeal created.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/AppealResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
      requestBody:
        $ref: "#/components/requestBodies/AppealCreateRequest"

  /appeals/{appeal_id}:
    get:
      tags:
        - Appeals
      summary: Retrieve an appeal identified by the appeal id
      operationId: massgov.pfml.api.appeals.appeal_get
      parameters:
        - name: appeal_id
          in: path
          schema:
            type: string
            format: uuid
          description: ID of appeal to retrieve
          required: true
      responses:
        "200":
          description: Appeal found.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/AppealResponse"
        "404":
          $ref: "#/components/responses/NotFound"

    patch:
      tags:
        - Appeals
      summary: Update an appeal
      description: >
        Supports partial update of an Appeal record.

        Note that array property updates are not partial, after processing the
        PATCH, the Appeal state will contain only the values of the array
        in the request.

        The behavior generally follows RFC 7396.

        It is expected clients will call this endpoint multiple times and with
        incomplete data as individual pieces of information is collected from
        the user. The API will review the data provided and return any pertinent
        warnings or errors.
      operationId: massgov.pfml.api.appeals.appeals_update
      parameters:
        - name: appeal_id
          in: path
          schema:
            type: string
            format: uuid
          description: ID of Appeal to update
          required: true
      responses:
        "200":
          description: Appeal updated without errors.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/AppealResponse"
        "400":
          description: An unsuccessful request returns a 400 status code if one or more errors occured, described in the error response.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
      requestBody:
        $ref: "#/components/requestBodies/AppealUpdateRequest"

  /appeals/{appeal_id}/complete:
    post:
      tags:
        - Appeals
      summary: Complete creation of an appeal in FINEOS
      operationId: massgov.pfml.api.appeals.appeals_complete
      parameters:
        - name: appeal_id
          in: path
          schema:
            type: string
            format: uuid
          description: ID of Appeal to submit
          required: true
      responses:
        "200":
          description: Appeal creation completed without errors.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/AppealResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "503":
          description: Appeal could not be completed.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/ErrorResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/AppealResponse"

  /appeals/search:
    post:
      tags:
        - Appeals
      security:
        - jwt: []
      summary: Retrieve appeals
      operationId: massgov.pfml.api.appeals.appeals_search
      requestBody:
        $ref: "#/components/requestBodies/AppealsSearchRequest"

      responses:
        "200":
          description: The set of appeals found.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/AppealsResponse"
        "400":
          description: An unsuccessful request returns a 400 status code if one or more errors occurred.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /appeals/{appeal_id}/documents:
    get:
      summary: Get a list of documents for an appeal case.
      operationId: massgov.pfml.api.appeals.documents_get
      tags:
        - Appeals
        - Documents
      parameters:
        - name: appeal_id
          description: ID of Appeal case
          in: path
          schema:
            type: string
            format: uuid
            example: 5f91c12b-4d49-4eb0-b5d9-7fa0ce13eb32
          required: true
      responses:
        "200":
          description: A successful request returns a 200 status code.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        type: array
                        items:
                          $ref: "#/components/schemas/DocumentResponse"
                example:
                  meta:
                    method: "GET"
                    resource: "/appeals/7c1bdaf8-51b4-4a1b-a665-463450d75c95/documents"
                  status_code: 200
                  message: "success"
                  data:
                    - user_id: "fabd09ed-0ea9-4091-843e-2e37d2c61482"
                      appeal_id: "fdc23456-c8fa-48bf-89a1-5c7c6a3af164"
                      created_at: "1970-06-01"
                      document_type: "Appeal Form"
                      content_type: "application/pdf"
                      fineos_document_id: "1234"
                      name: "Appeal Form"
                      description: "Appeal Form"
        "400":
          description: An unsuccessful request returns a 400 status code if one or more errors occurred.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

    post:
      summary: Upload an Appeal Document
      operationId: massgov.pfml.api.appeals.document_upload
      tags:
        - Appeals
        - Documents
      parameters:
        - name: appeal_id
          in: path
          schema:
            type: string
            format: uuid
            example: 5f91c12b-4d49-4eb0-b5d9-7fa0ce13eb32
          description: ID of the appeal case
          required: true
      requestBody:
        content:
          multipart/form-data:
            schema:
              $ref: "#/components/schemas/DocumentUploadRequest"
            encoding:
              file:
                contentType: application/pdf, image/jpeg, image/png, image/tiff, image/heic
      responses:
        "200":
          description: A successful request returns a 200 status code.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/DocumentResponse"
                example:
                  meta:
                    method: "POST"
                    resource: "/appeals/7c1bdaf8-51b4-4a1b-a665-463450d75c95/documents"
                  status_code: 200
                  message: "success"
                  data:
                    user_id: "fabd09ed-0ea9-4091-843e-2e37d2c61482"
                    appeal_id: "fdc23456-c8fa-48bf-89a1-5c7c6a3af164"
                    created_at: "1970-06-01"
                    document_type: "Appeal Form"
                    content_type: "application/pdf"
                    fineos_document_id: "1234"
                    name: "Appeal Form"
                    description: "Appeal Form"
        "400":
          description: An unsuccessful request returns a 400 status code if one or more errors occurred.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /appeals/{appeal_id}/confirm-documents:
    post:
      summary: Confirms the uploaded appeal documents for review
      operationId: massgov.pfml.api.appeals.confirm_documents
      tags:
        - Appeals
      parameters:
        - name: appeal_id
          in: path
          schema:
            type: string
            format: uuid
            example: 5f91c12b-4d49-4eb0-b5d9-7fa0ce13eb32
          description: ID of the appeal case
          required: true
      requestBody:
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/ConfirmDocumentsRequest"
      responses:
        "200":
          description: A successful request returns a 200 status code.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/AppealResponse"
                example:
                  meta:
                    method: "POST"
                    resource: "/appeals/7c1bdaf8-51b4-4a1b-a665-463450d75c95/confirm-documents"
                  status_code: 200
                  message: "success"
                  data:
                    appeal_id: "7c1bdaf8-51b4-4a1b-a665-463450d75c95"
                    fineos_appeal_id: "NTN-01-ABS-01-AP-01"
                    fineos_absence_id: "NTN-01-ABS-01"
                    appeal_phone_number:
                      {
                        "int_code": "1",
                        "phone_number": "***-***-2345",
                        "phone_type": "Phone",
                      }
                    appeal_reason: "Missed deadline"
                    for_private_insurance: true
                    needs_interpreter: false
                    appeal_representative_option: "Yes"
                    appeal_status: "Closed - Claim Decision Changed"
                    originally_decided_at: "2020-06-26"
                    originally_decided_at_reason_for_past_due: "Was out of town"
                    computed_is_more_than_ten_days_past_decision: false
                    is_generated_from_extract: false
                    submitted_at: "2020-06-26T01:02:03.967736+00:00"
                    created_at: "2020-06-26T01:02:03.967736+00:00"
                    updated_at: "2020-06-26T01:02:03.967736+00:00"
        "400":
          description: An unsuccessful request returns a 400 status code if one or more errors occurred.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /financial-eligibility:
    post:
      security:
        - oauth2: []
      tags:
        - Financial Eligibility
      summary: Retrieve financial eligibility by SSN/ITIN, FEIN, leave start date, application submitted date and employment status.
      operationId: massgov.pfml.api.eligibility.handler.eligibility_post
      responses:
        "200":
          description: a successful response returns a 200 status code a financial_eligibility object.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/EligibilityResponse"
                example:
                  meta:
                    method: "POST"
                    resource: "/financial-eligibility"
                  status_code: 200
                  message: "success"
                  data:
                    financially_eligible: true
                    description: "Financially eligible"
                    total_wages: 55000
                    state_average_weekly_wage: 1431
                    unemployment_minimum: 5100
                    employer_average_weekly_wage: 1500
        "400":
          $ref: "#/components/responses/BadRequest"
      requestBody:
        $ref: "#/components/requestBodies/EligibilityRequest"

  /financial-eligibility/search:
    post:
      security:
        - oauth2: []
      tags:
        - Financial Eligibility
      summary: Search endpoint for retrieving financial eligibility calculations
      operationId: massgov.pfml.api.eligibility.handler.eligibility_search
      responses:
        "200":
          description: a successful response returns a 200 status code and the found financial eligibility calculation
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        type: array
                        items:
                          $ref: "#/components/schemas/FinancialEligibilityCalculationResponse"
      requestBody:
        $ref: "#/components/requestBodies/FinancialEligibilityCalculationSearchRequest"

  /rmv-check:
    post:
      security:
        - oauth2: []
      tags:
        - ID Proofing
      summary: Perform lookup and data matching for information on RMV-issued IDs
      operationId: massgov.pfml.api.rmv_check.rmv_check_post
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/RMVCheckRequest"
      responses:
        "200":
          description: Summary of the check
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/RMVCheckResponse"
                example:
                  meta:
                    method: "POST"
                    resource: "/rmv-check"
                  status_code: 200
                  message: "success"
                  data:
                    verified: true
                    description: Verification check passed.
        "400":
          $ref: "#/components/responses/BadRequest"
        default:
          $ref: "#/components/responses/UnexpectedError"

  /notifications:
    post:
      security:
        - oauth2: []
      tags:
        - Notifications
      summary: >
        Send a notification that a document is available for a claimant to either the claimant or leave administrator.
      operationId: massgov.pfml.api.notifications.notifications_post
      responses:
        "201":
          description: a successful response returns a 201 status code and asynchronously starts the notification process.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                example:
                  meta:
                    method: "POST"
                    resource: "/notifications"
                  status_code: 201
                  message: "success"
        "400":
          $ref: "#/components/responses/BadRequest"
        "503":
          description: todo
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
      requestBody:
        $ref: "#/components/requestBodies/NotificationRequest"

  /employers/verifications:
    post:
      tags:
        - Verifications
      summary: >
        Check to see if user should be verified and create verification record
      operationId: massgov.pfml.api.verifications.verifications
      responses:
        "201":
          description: Checks verification data, and if valid, creates a verification record for a leave admin
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
      requestBody:
        $ref: "#/components/requestBodies/VerificationRequest"

  /benefit-years/search:
    post:
      security:
        - jwt: []
      tags:
        - Benefit Years
      summary: >
        Returns all benefit years for the employee_id in the request; if no employee_id is present
        returns all benefit years for the current user.
      operationId: massgov.pfml.api.eligibility.handler.benefit_years_search
      responses:
        "200":
          description: A successful response returns a 200 status code and the benefit years
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        type: array
                        items:
                          $ref: "#/components/schemas/BenefitYearResponse"
                example:
                  meta:
                    method: "POST"
                    resource: "/benefit-years/search"
                  status_code: 200
                  message: "success"
                  data:
                    - benefit_year_end_date: "2020-12-31"
                      benefit_year_start_date: "2020-01-01"
                      employee_id: "2a340cf8-6d2a-4f82-a075-73588d003f8f"
                      current_benefit_year: true
                    - benefit_year_end_date: "2019-12-31"
                      benefit_year_start_date: "2019-01-01"
                      employee_id: "2a340cf8-6d2a-4f82-a075-73588d003f8f"
                      current_benefit_year: false
      requestBody:
        $ref: "#/components/requestBodies/BenefitYearsSearchRequest"

  /holidays/search:
    post:
      security:
        - {} # no authentication option
      tags:
        - Holidays
      summary: Returns any holidays in a date range
      operationId: massgov.pfml.api.holidays.holidays_search
      requestBody:
        $ref: "#/components/requestBodies/HolidaysSearchRequest"
      responses:
        "200":
          description: Successfully returning holidays in range
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/HolidaysSearchResponse"
                example:
                  meta:
                    method: "POST"
                    resource: "/holidays/search"
                  status_code: 200
                  message: "success"
                  data:
                    - name: "New Year's Day"
                      date: "2022-01-01"
                    - name: "Christmas Day"
                      date: "2021-12-25"
        "400":
          $ref: "#/components/responses/BadRequest"

  /admin/authorize:
    get:
      security:
        - {} # no authentication required
      tags:
        - Admin
      summary: Returns azure ad authentication url to initiate user auth code flow
      operationId: massgov.pfml.api.admin.admin_authorization_url
      responses:
        "200":
          description: Initiated authentication code flow
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AuthURIResponse"

  /admin/token:
    post:
      security:
        - {} # no authentication required
      tags:
        - Admin
      summary: Trade an authentication code for an access token
      operationId: massgov.pfml.api.admin.admin_token
      requestBody:
        $ref: "#/components/requestBodies/AdminTokenRequest"
      responses:
        "200":
          $ref: "#/components/responses/AdminTokenResponse"
        "401":
          $ref: "#/components/responses/BadRequest"
        "404":
          $ref: "#/components/responses/NotFound"

  /admin/login:
    get:
      security:
        - jwt: []
      tags:
        - Admin
      summary: Login as admin user
      operationId: massgov.pfml.api.admin.admin_login
      responses:
        "200":
          description: Successfully logged in as admin account!
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminUserResponse"
        "400":
          $ref: "#/components/responses/Unauthorized"
        "401":
          $ref: "#/components/responses/BadRequest"
        "404":
          $ref: "#/components/responses/NotFound"

  /admin/logout:
    get:
      security:
        - {} # no authentication required
      tags:
        - Admin
      summary: Logout admin user
      operationId: massgov.pfml.api.admin.admin_logout
      responses:
        "200":
          description: Retrieved logout url
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/AdminLogoutResponse"

  /admin/users:
    get:
      tags:
        - Admin
        - Users
      summary: Retrieve all user accounts
      operationId: massgov.pfml.api.admin.admin_users_get
      parameters:
        - $ref: "#/components/parameters/pageSize"
        - $ref: "#/components/parameters/pageOffset"
        - name: email_address
          in: query
          schema:
            type: string
          description: A partial email address, will be used as a wildcard.
          required: false
      responses:
        "200":
          description: A successful request returns a 200 status code and user objects.
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/UserResponse"
        "401":
          $ref: "#/components/responses/BadRequest"
        "404":
          $ref: "#/components/responses/NotFound"

  /admin/flags/{name}:
    patch:
      tags:
        - Admin
        - Feature Flags
      summary: Update a feature flag
      operationId: massgov.pfml.api.admin.admin_flags_patch
      parameters:
        - name: name
          in: path
          schema:
            type: string
          description: Name of flag to update
          required: true
      responses:
        "200":
          description: A successful request returns a 200 status code and a user object.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SuccessfulResponse"
        "404":
          $ref: "#/components/responses/NotFound"
      requestBody:
        $ref: "#/components/requestBodies/FlagUpdateRequest"

  /admin/flag-logs/{name}:
    get:
      tags:
        - Admin
        - Feature Flags
      summary: Get logs for a feature flag
      operationId: massgov.pfml.api.admin.admin_get_flag_logs
      parameters:
        - name: name
          in: path
          schema:
            type: string
          description: Name of the flag to get
          required: true
      responses:
        "200":
          description: A successful request returns a 200 status code and list of flag logs.
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/FlagWithLog"
        "503":
          $ref: "#/components/responses/ServiceUnavailableError"

  /admin/check-address-validation-override/{absence_case_id}:
    get:
      tags:
        - Address Validation Override
      summary: Check if an NTN number can use the FINEOS address as the verified mailing address.
      operationId: massgov.pfml.api.admin.check_address_validation_override_get
      parameters:
        - name: absence_case_id
          in: path
          schema:
            type: string
          description: Absence case ID e.g. NTN number.
          required: true
      responses:
        "200":
          description: A successful request returns a 200 status code and a response object.
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/CheckAddressValidationOverrideResponse"
        "404":
          $ref: "#/components/responses/NotFound"

  /admin/confirm-address-validation-override/{absence_case_id}:
    patch:
      tags:
        - Address Validation Override
      summary: Set the FINEOS address as the verified mailing address.
      operationId: massgov.pfml.api.admin.confirm_address_validation_override_patch
      parameters:
        - name: absence_case_id
          in: path
          schema:
            type: string
          description: Absence case ID e.g. NTN number.
          required: true
      responses:
        "200":
          description: A successful request returns a 200 status code.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SuccessfulResponse"
        "404":
          $ref: "#/components/responses/NotFound"

  /admin/smoke-test-fineos-customer-api:
    get:
      tags:
        - FINEOS API Smoke Test
      summary: Used to confirm the FINEOS Customer API is available.
      operationId: massgov.pfml.api.admin.smoke_test_fineos_customer_api_get
      responses:
        "200":
          description: A successful request returns a 200 status code.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SuccessfulResponse"
        "404":
          $ref: "#/components/responses/NotFound"

  /admin/smoke-test-fineos-integration-services-api:
    get:
      tags:
        - FINEOS API Smoke Test
      summary: Used to confirm the FINEOS Integration Services API is available.
      operationId: massgov.pfml.api.admin.smoke_test_fineos_integration_services_api_get
      responses:
        "200":
          description: A successful request returns a 200 status code.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SuccessfulResponse"
        "404":
          $ref: "#/components/responses/NotFound"

  /admin/smoke-test-fineos-group-client-api:
    get:
      tags:
        - FINEOS API Smoke Test
      summary: Used to confirm the FINEOS Group Client API is available.
      operationId: massgov.pfml.api.admin.smoke_test_fineos_group_client_api_get
      responses:
        "200":
          description: A successful request returns a 200 status code.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SuccessfulResponse"
        "404":
          $ref: "#/components/responses/NotFound"

  /admin/validate-address:
    get:
      tags:
        - Admin
        - Feature Flags
      parameters:
        - name: address
          in: query
          schema:
            type: string
          description: An address to validate.
          required: true
      summary: Validate/Suggest Address
      operationId: massgov.pfml.api.admin.validate_address
      responses:
        "200":
          description: A successful request returns a 200 status code and list of address.
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/ExperianReturn"
        "503":
          $ref: "#/components/responses/ServiceUnavailableError"

  /admin/overpayment/vcm-report:
    get:
      tags:
        - Admin
      summary: Used to display the overpayment VCM report
      operationId: massgov.pfml.api.admin.overpayment_vcm_report
      responses:
        "200":
          description: A successful request returns a 200 status code.
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/VCMComparisonResponse"
        "404":
          $ref: "#/components/responses/NotFound"
  /admin/overpayment/mark-vcm-reviewed:
    post:
      tags:
        - Admin
      summary: Marks VCM Required mmars event as reviewed (VCC Pending)
      operationId: massgov.pfml.api.admin.mark_overpayment_vcm_reviewed
      requestBody:
        $ref: "#/components/requestBodies/OverpaymentMarkVcmReviewedRequest"
      responses:
        "200":
          description: Successfully marked a vcm required mmars event as reviewed
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SuccessfulResponse"
        "503":
          $ref: "#/components/responses/ServiceUnavailableError"
  /admin/overpaymentsearch:
    post:
      tags:
        - Admin
      summary: search for overpayments
      operationId: massgov.pfml.api.admin.overpayment_search
      requestBody:
        $ref: "#/components/requestBodies/OverpaymentSearchRequest"
      responses:
        "200":
          description: A successful request returns a 200 status code and overpayment information
          content:
            application/json:
              schema:
                properties:
                  data:
                    items:
                      $ref: "#/components/schemas/OverpaymentSearchResponse"
        "503":
          $ref: "#/components/responses/ServiceUnavailableError"
  /admin/overpayment/refer:
    post:
      tags:
        - Admin
      summary: Refer an overpayment
      operationId: massgov.pfml.api.admin.refer_overpayment
      requestBody:
        $ref: "#/components/requestBodies/ReferOverpaymentRequest"
      responses:
        "200":
          description: Successfully referred overpayment
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OverpaymentCaseResponse"
        "400":
          description: Bad request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "401":
          description: Unauthorized
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: Overpayment not found
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "503":
          $ref: "#/components/responses/ServiceUnavailableError"
  /admin/overpayment/retry-transaction:
    post:
      tags:
        - Admin
      summary: Retry referring an overpayment
      operationId: massgov.pfml.api.admin.retry_overpayment_transaction
      requestBody:
        $ref: "#/components/requestBodies/RetryOverpaymentTransactionRequest"
      responses:
        "200":
          description: Successfully re-referred an overpayment
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MmarsEventResponse"
        "503":
          $ref: "#/components/responses/ServiceUnavailableError"
  /admin/overpayment/hold-transaction:
    post:
      tags:
        - Admin
      summary: Hold an overpayment transaction
      operationId: massgov.pfml.api.admin.hold_overpayment_transaction
      requestBody:
        $ref: "#/components/requestBodies/HoldOverpaymentTransactionRequest"
      responses:
        "200":
          description: Successfully hold an overpayment transaction
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MmarsEventResponse"
        "503":
          $ref: "#/components/responses/ServiceUnavailableError"
  /admin/overpayment/release-transaction:
    post:
      tags:
        - Admin
      summary: Releases the hold from an overpayment transaction
      operationId: massgov.pfml.api.admin.release_overpayment_transaction
      requestBody:
        $ref: "#/components/requestBodies/HoldOverpaymentTransactionRequest"
      responses:
        "200":
          description: Successfully relased an overpayment transaction
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/MmarsEventResponse"
        "503":
          $ref: "#/components/responses/ServiceUnavailableError"
  /admin/omnisearch/users:
    post:
      tags:
        - Admin
      summary: search for users
      operationId: massgov.pfml.api.admin.omni_search_users
      requestBody:
        $ref: "#/components/requestBodies/OmniSearchRequest"
      responses:
        "200":
          description: A successful request returns a 200 status code and list of users.
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/UserResponse"
        "503":
          $ref: "#/components/responses/ServiceUnavailableError"

  /admin/omnisearch/users/{user_id}:
    get:
      tags:
        - Admin
      parameters:
        - name: user_id
          in: path
          schema:
            type: string
          description: A user_id to get user detail.
          required: true
      summary: Get user account details
      operationId: massgov.pfml.api.admin.omni_search_get_user_detail
      responses:
        "200":
          description: A successful request returns a 200 status code and list of users.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserResponse"
        "503":
          $ref: "#/components/responses/ServiceUnavailableError"

  /admin/omnisearch/users/{user_id}/auth-log:
    get:
      tags:
        - Admin
      parameters:
        - name: user_id
          in: path
          schema:
            type: string
          description: A user_id to get user detail.
          required: true
      summary: User Oauth Logs Detail
      operationId: massgov.pfml.api.admin.omni_search_get_user_auth_logs
      responses:
        "200":
          description: A successful request returns a 200 status code and list of users.
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/UserAuthLogResponse"
        "503":
          $ref: "#/components/responses/ServiceUnavailableError"

  /admin/users/{user_id}:
    patch:
      tags:
        - Admin
      summary: Update a User account
      operationId: massgov.pfml.api.admin.admin_users_patch
      parameters:
        - name: user_id
          in: path
          schema:
            type: string
          description: ID of User to update
          required: true
      responses:
        "200":
          description: A successful request returns a 200 status code and a user object.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserResponse"
        "404":
          $ref: "#/components/responses/NotFound"
      requestBody:
        $ref: "#/components/requestBodies/AdminUserUpdateRequest"

  /admin/users/{user_id}/roles/{role_id}:
    post:
      tags:
        - Admin
      parameters:
        - name: user_id
          in: path
          schema:
            type: string
          description: The id of the user to modify.
          required: true
        - name: role_id
          in: path
          schema:
            type: string
          description: The id of the role to add to the user.
          required: true
      summary: Add a role for a user
      operationId: massgov.pfml.api.admin.users_role_add
      responses:
        "400":
          $ref: "#/components/responses/BadRequest"
        "404":
          $ref: "#/components/responses/NotFound"
        "200":
          description: Role added without issue.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SuccessfulResponse"
      requestBody:
        $ref: "#/components/requestBodies/AdminUpdateBaseRequest"
    delete:
      tags:
        - Admin
      parameters:
        - name: user_id
          in: path
          schema:
            type: string
          description: The id of the user to modify.
          required: true
        - name: role_id
          in: path
          schema:
            type: string
          description: The id of the role to delete from the user.
          required: true
      summary: Remove a role from a user
      operationId: massgov.pfml.api.admin.users_role_delete
      responses:
        "200":
          description: Role deleted without issue
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SuccessfulResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "404":
          $ref: "#/components/responses/NotFound"
      requestBody:
        $ref: "#/components/requestBodies/AdminUpdateBaseRequest"

  /admin/omnisearch/auth-logs:
    post:
      tags:
        - Admin
      summary: Search user auth logs
      operationId: massgov.pfml.api.admin.omni_search_user_auth_logs
      requestBody:
        $ref: "#/components/requestBodies/OmniSearchRequest"
      responses:
        "200":
          description: A successful request returns a 200 status code and list of users.
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/UserAuthLogResponse"
        "503":
          $ref: "#/components/responses/ServiceUnavailableError"

  /admin/audit-logs:
    get:
      tags:
        - Admin
      parameters:
        - name: filter_id
          in: query
          schema:
            type: string
          description: The id of the record the logs relate to or admin_user_id that created them.
          required: false
      summary: Get audit logs.
      operationId: massgov.pfml.api.admin.audit_logs
      responses:
        "400":
          $ref: "#/components/responses/BadRequest"
        "404":
          $ref: "#/components/responses/NotFound"
        "200":
          description: Zero or more logs returned for filter criteria.
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/AuditLogResponse"

  /leave-admins/search:
    post:
      tags:
        - Leave Admins
      summary: >
        Returns leave administrators filtered based on key passed into request body
      operationId: massgov.pfml.api.leave_admins.leave_admins_search
      responses:
        "200":
          description: A successful request returns a 200 status code and user objects.
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/UserLeaveAdminResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
      requestBody:
        $ref: "#/components/requestBodies/UserLeaveAdminRequest"

  /leave-admins/add:
    post:
      tags:
        - Leave Admins
      summary: >
        Add Leave Administrator to employer
      operationId: massgov.pfml.api.leave_admins.leave_admins_add
      responses:
        "201":
          description: A successful request returns a 201 status code.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SuccessfulResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
      requestBody:
        $ref: "#/components/requestBodies/UserLeaveAdminAddRequest"

  /leave-admins/download-csv:
    get:
      summary: Download leave admin application and employee data as CSV
      operationId: massgov.pfml.api.leave_admins.download_csv
      responses:
        "200":
          description: Employer application CSV file download
          content:
            text/csv:
              schema:
                type: string
                format: binary
        "403":
          description: Forbidden - User does not have permission to access this data
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "404":
          description: No application data found for the given employer
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "429":
          description: Too Many Requests - Rate limit exceeded
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Internal Server Error - Something went wrong while processing the request
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /leave-admins/{user_leave_administrator_id}/deactivate:
    post:
      tags:
        - Leave Admins
      summary: >
        Deactivates a partciular leave administrator
      operationId: massgov.pfml.api.leave_admins.deactivate_leave_admin
      parameters:
        - name: user_leave_administrator_id
          in: path
          schema:
            type: string
            format: uuid
          description: ID of user leave administrator to deactivate
          required: true
      responses:
        "200":
          description: Leave administrator was marked as deactivated.
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/UserLeaveAdminResponse"
        "400":
          $ref: "#/components/responses/BadRequest"

  /oauth/start-flow/{operation}/{user_type}/{language}:
    get:
      security:
        - {} # no authentication required
      tags:
        - OAUTH
      summary: Returns lmg authentication url to initiate user auth code flow
      parameters:
        - name: operation
          in: path
          schema:
            type: string
          required: true
        - name: user_type
          in: path
          schema:
            type: string
          required: true
        - name: language
          in: path
          schema:
            type: string
          required: true
        - name: origin
          in: query
          schema:
            type: string
          required: false
      operationId: massgov.pfml.api.oauth.lmg_authorization_url
      responses:
        "200":
          description: Initiated LMG authentication code flow
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/LMGAuthURIResponse"

  /oauth/token:
    post:
      security:
        - {} # no authentication required
      tags:
        - OAUTH
      summary: Trade an authentication code for an access token
      operationId: massgov.pfml.api.oauth.get_lmg_token
      requestBody:
        $ref: "#/components/requestBodies/LMGTokenRequest"
      responses:
        "200":
          $ref: "#/components/responses/LMGTokenResponse"
        "401":
          $ref: "#/components/responses/BadRequest"
        "404":
          $ref: "#/components/responses/NotFound"

  /oauth/token-exchange:
    post:
      security:
        - {}
      summary: Exchange authorization code or refresh token for access and refresh tokens
      description: |
        OAuth server token exchange. Exchange either:
        - authorization code for access and refresh token (authorization code grant)
        - refresh token for new access token (refresh token grant)
      operationId: massgov.pfml.api.oauth_server.token_exchange
      tags:
        - OAuthServer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              oneOf:
                - $ref: "#/components/schemas/AuthorizationCodeGrantRequest"
                - $ref: "#/components/schemas/RefreshTokenGrantRequest"
      responses:
        "200":
          description: Successful token exchange
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OAuthTokenResponse"
        "400":
          description: Invalid request parameters
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "401":
          description: Invalid client credentials or invalid/expired authorization code
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
        "500":
          description: Server error
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"

  /oauth/authorize:
    get:
      tags:
        - OAuthServer
      summary: Returns authorization code for live chat
      operationId: massgov.pfml.api.oauth_server.get_authorization_code
      responses:
        "200":
          description: Initiated authorization code
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OAuthServerCodeResponse"
        "404":
          $ref: "#/components/responses/NotFound"

  /oauth/me:
    get:
      security:
        - oauth-server: []
      tags:
        - OAuthServer
      summary: Get user information
      operationId: massgov.pfml.api.oauth_server.me
      responses:
        "200":
          description: User information
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/OauthMeResponse"
        "401":
          $ref: "#/components/responses/Unauthorized"

  /users/{user_id}/profile:
    patch:
      summary: Endpoint for saving data back to a user's MMG profile.
      operationId: massgov.pfml.api.users.patch_user_profile
      tags:
        - Users
      parameters:
        - name: user_id
          in: path
          schema:
            type: string
            format: uuid
          description: ID of User to patch
          required: true
      requestBody:
        $ref: "#/components/requestBodies/UserProfileUpdateRequest"
      responses:
        "200":
          description: A successful request returns a 200 status code.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SuccessfulResponse"
        "404":
          $ref: "#/components/responses/NotFound"

  /users/{user_id}/profile/check-for-updates:
    post:
      summary: Provides the logic determining if there are updates that could be made to the user's MMG profile.
      operationId: massgov.pfml.api.users.user_profile_check_for_updates
      tags:
        - Users
      parameters:
        - name: user_id
          in: path
          schema:
            type: string
            format: uuid
          description: ID of User to check for usable data for application
          required: true
      requestBody:
        $ref: "#/components/requestBodies/UserProfileCheckForUpdatesRequest"
      responses:
        "200":
          description: A successful request returns a 200 status code and a response object.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserProfileCheckForUpdatesResponse"
        "404":
          $ref: "#/components/responses/NotFound"

  /users/{user_id}/profile/has-usable-data-for-application:
    get:
      summary: Check if user has usable data for application
      operationId: massgov.pfml.api.users.get_profile_has_usable_data_for_application
      tags:
        - Users
      parameters:
        - name: user_id
          in: path
          schema:
            type: string
            format: uuid
          description: ID of User to check for usable data for application
          required: true
      responses:
        "200":
          description: A successful request returns a 200 status code and a response object.
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/HasUsableDataForApplicationResponse"
        "404":
          $ref: "#/components/responses/NotFound"

  /address/search:
    get:
      tags:
        - Experian Address Validation
      summary: Address search using Experian Singleline REST API
      operationId: massgov.pfml.experian.address_validate_rest.experian.address_search
      description: Search for addresses using the Experian Singleline Address REST API.
      parameters:
        - in: query
          name: address
          schema:
            type: string
          required: true
          description: The address line to search for. E.g., "11111 Kyle Street Unit 101, Boston, MA"
      responses:
        "200":
          description: Successful response with address details
          content:
            application/json:
              schema:
                type: object
                # Define the schema for the response here
        "400":
          description: Bad request
        "500":
          description: Internal server error

  /address/format:
    get:
      tags:
        - Experian Address Validation
      summary: Address formatting using Experian Singleline REST API
      operationId: massgov.pfml.experian.address_validate_rest.experian.address_format
      description: Format address into Address Line 1..3, City, STate, Zip, Country
      parameters:
        - in: query
          name: global_address_key
          schema:
            type: string
          required: true
          description: Global address key found during address search using Experian Singleline REST API
      responses:
        "200":
          description: Successful response with address details
          content:
            application/json:
              schema:
                type: object
                # Define the schema for the response here
        "400":
          description: Bad request
        "500":
          description: Internal server error

  /benefits-metrics:
    get:
      tags:
        - Benefits Metrics
      summary: Retrieve the benefits metrics
      operationId: massgov.pfml.api.benefits_metrics.get_benefits_metrics
      responses:
        "200":
          description: A successful request returns a 200 status code and a response object.
          content:
            application/json:
              schema:
                allOf:
                  - $ref: "#/components/schemas/SuccessfulResponse"
                  - properties:
                      data:
                        $ref: "#/components/schemas/BenefitsMetricsResponse"

  /insurance-providers:
    get:
      summary: Retrieve all insurance providers
      operationId: massgov.pfml.api.insurance_providers.get_insurance_providers
      parameters:
        - in: query
          name: include_deactivated
          schema:
            type: boolean
          description: Denotes if deactivated insurance providers should be returned
      tags:
        - Insurance Providers
      responses:
        "200":
          description: The insurance providers.
          content:
            application/json:
              schema:
                properties:
                  data:
                    type: array
                    items:
                      $ref: "#/components/schemas/InsuranceProviderResponse"
        "500":
          description: Internal server error

  /insurance-providers/{insurance_provider_id}:
    get:
      summary: Retrieve details for insurance provider's id provided
      operationId: massgov.pfml.api.insurance_providers.get_insurance_providers_details
      tags:
        - Insurance Providers
      parameters:
        - name: insurance_provider_id
          in: path
          schema:
            type: string
          description: ID of insurance provider that detail is wanted for
          required: true
        - name: include_deactivated_insurance_plans
          in: query
          schema:
            type: boolean
          description: Denotes if deactivated insurance provider plans should be returned
      responses:
        "200":
          description: The insurance providers.
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/InsuranceProvidersDetailsResponse"
        "500":
          description: Internal server error

  /employer-exemption-applications:
    get:
      summary: retrieves all employer exemption applications for a user
      operationId: massgov.pfml.api.employer_exemptions.get_all_employer_exemption_applications_per_exemption_admin
      tags:
        - Employer Exemptions
      parameters:
        - $ref: "#/components/parameters/pageSize"
        - $ref: "#/components/parameters/pageOffset"
        - $ref: "#/components/parameters/orderBy"
        - $ref: "#/components/parameters/orderDirection"
      responses:
        "200":
          description: all employer exemption applications.
          content:
            application/json:
              schema:
                properties:
                  data:
                    $ref: "#/components/schemas/AllEmployerExemptionsApplicationResponse"
        "500":
          description: Internal server error
    post:
      tags:
        - Employer Exemptions
      summary: Create an Employer Exemption Application
      operationId: massgov.pfml.api.employer_exemptions.application_start
      requestBody:
        $ref: "#/components/requestBodies/EmployerExemptionApplicationCreateRequestBody"
      responses:
        "200":
          $ref: "#/components/responses/EmployerExemptionApplicationCreatedResponseBody"
        "400":
          $ref: "#/components/responses/BadRequest"
        "500":
          description: Internal server error

  /employer-exemption-applications/{employer_exemption_application_id}:
    get:
      tags:
        - Employer Exemptions
      summary: Retrieve an Employer Exemption Application identified by the employer exemption application id
      operationId: massgov.pfml.api.employer_exemptions.application_get
      parameters:
        - name: employer_exemption_application_id
          in: path
          schema:
            type: string
            format: uuid
          description: ID of Employer Exemption Application to retrieve
          required: true
      responses:
        "200":
          $ref: "#/components/responses/EmployerExemptionApplicationGetResponseBody"
        "404":
          $ref: "#/components/responses/NotFound"
    delete:
      tags:
        - Employer Exemptions
      summary: Delete an Employer Exemption Application identified by the employer exemption application id
      operationId: massgov.pfml.api.employer_exemptions.application_delete
      parameters:
        - name: employer_exemption_application_id
          in: path
          schema:
            type: string
            format: uuid
          description: ID of Employer Exemption Application to delete
          required: true
      responses:
        "200":
          description: Exemption application deleted without issue
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SuccessfulResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "404":
          $ref: "#/components/responses/NotFound"
    patch:
      summary: Update an Employer Exemption Application.
      description: >
        Supports partial update of an Employer Exemption Application record.

        Note that array property updates are not partial, after processing the
        PATCH, the Application state will contain only the values of the array
        in the request.

        The behavior generally follows RFC 7396.

        It is expected clients will call this endpoint multiple times and with
        incomplete data as individual pieces of information is collected from
        the user. The API will review the data provided and return any pertinent
        warnings or errors.
      operationId: massgov.pfml.api.employer_exemptions.employer_exemption_application_update
      tags:
        - Employer Exemptions
      parameters:
        - name: employer_exemption_application_id
          in: path
          schema:
            type: string
            format: uuid
          description: ID of Employer Exemption Application to update
          required: true
      responses:
        "200":
          $ref: "#/components/responses/EmployerExemptionApplicationUpdatedResponseBody"
        "400":
          description: An unsuccessful request returns a 400 status code if one or more errors occured, described in the error response.
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ErrorResponse"
      requestBody:
        $ref: "#/components/requestBodies/EmployerExemptionApplicationRequestBody"

  /employer-exemption-applications/{employer_exemption_application_id}/submit-application:
    post:
      tags:
        - Employer Exemptions
      summary: >
        Submit an Employer Exemption Application
      operationId: massgov.pfml.api.employer_exemptions.employer_exemption_application_submit
      parameters:
        - name: employer_exemption_application_id
          in: path
          schema:
            type: string
            format: uuid
          description: ID of Employer Exemption Application to submit
          required: true
      responses:
        "201":
          $ref: "#/components/responses/EmployerExemptionApplicationSubmittedResponseBody"
        "400":
          $ref: "#/components/responses/BadRequest"

tags:
  - name: Test Endpoints
  - name: Roles
  - name: Users
  - name: Employees
  - name: Employers
  - name: Claims
  - name: Application Import
  - name: Applications
  - name: Appeals
  - name: Financial Eligibility
  - name: ID Proofing
  - name: Experian Address Validation
  - name: Notifications
  - name: Feature Flags
  - name: Admin
  - name: Benefit Years
  - name: Holidays
  - name: ChangeRequest
  - name: Documents
  - name: TaxPreference
  - name: PaymentPreference
  - name: Verifications
  - name: Leave Admins
  - name: Fraud Protection
  - name: Address Validation Override
  - name: FINEOS API Smoke Test
  - name: OAUTH
  - name: OAuthServer
  - name: Benefits Metrics
  - name: Insurance Providers
  - name: Employer Exemptions

components:
  securitySchemes:
    jwt:
      type: http
      scheme: bearer
      bearerFormat: JWT
      x-bearerInfoFunc: massgov.pfml.api.authentication.decode_jwt
    oauth-server:
      type: http
      scheme: bearer
      bearerFormat: JWT
      x-bearerInfoFunc: massgov.pfml.api.authentication.decode_oauth_server_jwt
      description: |
        Valid JWT requirement for OAuth server implementation.
    oauth2:
      type: oauth2
      x-tokenInfoFunc: massgov.pfml.api.authentication.decode_jwt
      flows:
        clientCredentials:
          tokenUrl: /api/v1/oauth2/token
          scopes: {}
    agent-id:
      type: apiKey
      in: header
      name: Mass-PFML-Agent-ID
      x-apikeyInfoFunc: massgov.pfml.api.authentication.validate_agent_id
      description: |
        Some clients are required to provide a non-empty value for this header in order to authenticate.

        The value should be a stable, unique identifier for the entity/actor/agent driving the interaction with the Paid Leave API.

        The typical case will be when the client is another system, making calls to the Paid Leave API on behalf of a user in that other system, the header value should then be some identifier for that individual user (not the client).
  parameters:
    pageSize:
      name: "page_size"
      in: query
      required: false
      schema:
        type: integer
        format: int32
        minimum: 1
        default: 25
        maximum: 1000
    pageOffset:
      name: "page_offset"
      in: query
      required: false
      schema:
        type: integer
        format: int32
        minimum: 1
        default: 1
    orderBy:
      name: "order_by"
      in: query
      required: false
      schema:
        type: string
        default: "created_at"
        enum: ["created_at", "employee", "latest_follow_up_date"]
    orderDirection:
      name: "order_direction"
      in: query
      required: false
      schema:
        type: string
        default: "descending"
        enum: ["ascending", "descending"]

  requestBodies:
    AdminTokenRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/AdminTokenRequest"
      required: true
    AdminUpdateBaseRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/AdminUpdateBaseRequest"
      required: true
    UserProfileUpdateRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/UserProfileUpdateRequest"
    UserProfileCheckForUpdatesRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/UserProfileCheckForUpdatesRequest"
    AdminUserUpdateRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/AdminUserUpdateRequest"
      required: true
    RoleUserDeleteRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/RoleUserDeleteRequest"
      required: true
    UserUpdateRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/UserUpdateRequest"
      required: true
    ClaimSearchRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ClaimSearchRequest"
    EmployeeSearchRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/EmployeeSearchRequest"
      required: true
    EmployerClaimRequestBody:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/EmployerClaimRequestBody"
      required: true
    ApplicationRequestBody:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ApplicationRequestBody"
      required: true
    ApplicationCompleteRequestBody:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ApplicationCompleteRequestBody"
      required: false
    ApplicationImportRequestBody:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ApplicationImportRequestBody"
    EligibilityRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/EligibilityRequest"
      required: true
    FlagUpdateRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/Flag"
      required: true
    OmniSearchRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/OmniSearchRequest"
      required: true
    ReferOverpaymentRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ReferOverpaymentRequest"
    RetryOverpaymentTransactionRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/RetryOverpaymentTransactionRequest"
      required: true
    HoldOverpaymentTransactionRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/HoldOverpaymentTransactionRequest"
      required: true
    OverpaymentMarkVcmReviewedRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/OverpaymentMarkVcmReviewedRequest"
      required: true
    OverpaymentSearchRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/OverpaymentSearchRequest"
      required: true
    NotificationRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/NotificationRequest"
      required: true
    VerificationRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/VerificationRequest"
      required: true
    PaymentPreferenceRequestBody:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/PaymentPreferenceRequestBody"
      required: true
    TaxWithholdingPreferenceRequestBody:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/TaxWithholdingPreferenceRequestBody"
    EmployerAddFeinRequestBody:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/EmployerAddFeinRequestBody"
    BenefitYearsSearchRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/BenefitYearsSearchRequest"
    FinancialEligibilityCalculationSearchRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/FinancialEligibilityCalculationSearchRequest"
    ChangeRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ChangeRequest"
    HolidaysSearchRequest:
      content:
        application/json:
          schema:
            type: object
            properties:
              terms:
                type: object
                properties:
                  start_date:
                    $ref: "#/components/schemas/Date"
                    required: true
                  end_date:
                    $ref: "#/components/schemas/Date"
                    required: true
            required: ["terms"]
          example:
            terms:
              start_date: "2021-12-25"
              end_date: "2022-01-02"
    WaitingPeriodRequestBody:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/WaitingPeriodsSearchRequest"
    AppealCreateRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/AppealCreateRequest"
    AppealUpdateRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/AppealUpdateRequest"
    AppealsSearchRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/AppealsSearchRequest"
    IntermittentLeaveEpisodeRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/IntermittentLeaveEpisodeRequest"
      required: true
    UserLeaveAdminRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/UserLeaveAdminRequest"
    UserLeaveAdminAddRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/UserLeaveAdminAddRequest"
    LMGTokenRequest:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/LMGTokenRequest"
    CertificationDocumentRequestBody:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/CertificationDocumentRequest"
    EmployerExemptionApplicationCreateRequestBody:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/EmployerExemptionApplicationCreateRequestBody"
    EmployerExemptionApplicationRequestBody:
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/EmployerExemptionApplicationRequestBody"
  responses:
    AdminTokenResponse:
      description: User has successfully logged into microsoft account
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/AdminTokenResponse"
    LMGTokenResponse:
      description: User has successfully logged into microsoft account
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/LMGTokenResponse"
    ApplicationResponse:
      description: Application found
      content:
        application/json:
          schema:
            allOf:
              - $ref: "#/components/schemas/SuccessfulResponse"
              - properties:
                  data:
                    $ref: "#/components/schemas/ApplicationResponse"
    ApplicationCreatedResponse:
      description: A new application successfully created
      content:
        application/json:
          schema:
            allOf:
              - $ref: "#/components/schemas/SuccessfulResponse"
              - properties:
                  data:
                    $ref: "#/components/schemas/ApplicationResponse"
    ApplicationImportedResponse:
      description: Application successfully imported
      content:
        application/json:
          schema:
            allOf:
              - $ref: "#/components/schemas/SuccessfulResponse"
              - properties:
                  data:
                    $ref: "#/components/schemas/ApplicationResponse"
    ApplicationSearchResponse:
      description: Search successful
      content:
        application/json:
          schema:
            allOf:
              - $ref: "#/components/schemas/SuccessfulResponse"
              - properties:
                  data:
                    $ref: "#/components/schemas/ApplicationSearchResults"
    NotFound:
      description: The specified resource was not found
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
    Unauthorized:
      description: Unauthorized
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
    BadRequest:
      description: There was a problem with your request
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
    ServiceUnavailableError:
      description: The service is not available
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
    UnexpectedError:
      description: Unexpected error
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
    StreamContentDownload:
      description: Streamed Content
      content:
        application/pdf:
          schema:
            type: string
            format: binary
        image/jpeg:
          schema:
            type: string
            format: binary
        image/jpeg,image/pjpeg:
          schema:
            type: string
            format: binary
        image/png:
          schema:
            type: string
            format: binary
        application/octet-stream:
          schema:
            type: string
            format: binary
    EmployerExemptionApplicationCreatedResponseBody:
      description: A new Employer Exemption Application successfully created
      content:
        application/json:
          schema:
            allOf:
              - $ref: "#/components/schemas/SuccessfulResponse"
              - properties:
                  data:
                    $ref: "#/components/schemas/EmployerExemptionApplicationResponseBody"
    EmployerExemptionApplicationUpdatedResponseBody:
      description: Employer Exemption Application updated without errors.
      content:
        application/json:
          schema:
            allOf:
              - $ref: "#/components/schemas/SuccessfulResponse"
              - properties:
                  data:
                    $ref: "#/components/schemas/EmployerExemptionApplicationResponseBody"
    EmployerExemptionApplicationSubmittedResponseBody:
      description: Employer Exemption Application submitted without errors.
      content:
        application/json:
          schema:
            allOf:
              - $ref: "#/components/schemas/SuccessfulResponse"
              - properties:
                  data:
                    $ref: "#/components/schemas/EmployerExemptionApplicationResponseBody"
    EmployerExemptionApplicationGetResponseBody:
      description: Employer Exemption Application found
      content:
        application/json:
          schema:
            allOf:
              - $ref: "#/components/schemas/SuccessfulResponse"
              - properties:
                  data:
                    $ref: "#/components/schemas/EmployerExemptionApplicationResponseBody"
  schemas:
    AuthURIResponse:
      type: object
      properties:
        auth_uri:
          type: string
        claims_challenge:
          type: string
          nullable: true
        code_verifier:
          type: string
        nonce:
          type: string
        redirect_uri:
          type: string
        scope:
          type: array
          items:
            type: string
        state:
          type: string

    AdminLogoutResponse:
      type: object
      properties:
        logout_uri:
          type: string

    AuthCodeResponse:
      type: object
      properties:
        code:
          type: string
        session_state:
          type: string
        state:
          type: string

    AdminTokenRequest:
      type: object
      properties:
        auth_uri_res:
          $ref: "#/components/schemas/AuthURIResponse"
        auth_code_res:
          $ref: "#/components/schemas/AuthCodeResponse"

    AdminTokenResponse:
      type: object
      properties:
        access_token:
          type: string
        refresh_token:
          type: string
        id_token:
          type: string

    AdminUpdateBaseRequest:
      type: object
      additionalProperties: false
      properties:
        ticket_num:
          type: string
          nullable: false
          example: "TICKET-1234"

    UserProfileUpdateRequest:
      type: object
      additionalProperties: false
      properties:
        from_application:
          type: string
          format: uuid
          example: "2a340cf8-6d2a-4f82-a075-73588d003f8f"
        profile_fields_include:
          type: array
          items:
            type: string

    UserProfileCheckForUpdatesRequest:
      type: object
      additionalProperties: false
      properties:
        from_application:
          type: string
          format: uuid
          example: "2a340cf8-6d2a-4f82-a075-73588d003f8f"

    AdminUserUpdateRequest:
      type: object
      additionalProperties: false
      properties:
        auth_id:
          type: string
          nullable: false
          example: "00000000-0000-0000-0000-000000000000"
        email_address:
          type: string
          nullable: false
          example: "<EMAIL>"
        ticket_num:
          type: string
          nullable: false
          example: "TICKET-1234"

    AuditLogResponse:
      type: object
      properties:
        admin_audit_log_id:
          type: string
          format: uuid
          example: "00000000-0000-0000-0000-000000000000"
        admin_user_id:
          type: string
          format: uuid
          example: "00000000-0000-0000-0000-000000000000"
        ticket_num:
          type: string
          example: "TICKET-1234"
        record_type:
          type: string
          example: "user,role"
        record_id:
          type: string
          format: uuid
          example: "00000000-0000-0000-0000-000000000000"
        state_before:
          type: string
        state_after:
          type: string
        created_at:
          type: string
          format: date-time

    LMGTokenResponse:
      type: object
      properties:
        outcomes:
          type: array
          items:
            type: string
        token_data:
          $ref: "#/components/schemas/OAuthTokenData"

    OAuthTokenData:
      type: object
      properties:
        id_token:
          type: string
        access_token:
          type: string
        id_token_expires_in:
          type: string
        not_before:
          type: string
        profile_info:
          type: string
        scope:
          type: string
        token_type:
          type: string

    AuthorizationCodeGrantRequest:
      type: object
      required:
        - grant_type
        - client_id
        - client_secret
        - code
      properties:
        grant_type:
          type: string
          enum: ["authorization_code"]
          description: OAuth 2.0 Grant Type for authorization code flow
        client_id:
          type: string
          description: OAuth client identifier
        client_secret:
          type: string
          description: OAuth client secret
        code:
          type: string
          description: Authorization code obtained from the `/oauth/authorize` endpoint

    RefreshTokenGrantRequest:
      type: object
      required:
        - grant_type
        - client_id
        - client_secret
        - refresh_token
      properties:
        grant_type:
          type: string
          enum: ["refresh_token"]
          description: OAuth 2.0 Grant Type for refresh token flow
        client_id:
          type: string
          description: OAuth client identifier
        client_secret:
          type: string
          description: OAuth client secret
        refresh_token:
          type: string
          description: Refresh token from a previous token exchange response

    AuthorizationCodeGrantResponse:
      type: object
      required:
        - access_token
        - refresh_token
        - token_type
        - expires_in
        - meta
      properties:
        access_token:
          type: string
          description: JWT access token
        refresh_token:
          type: string
          description: JWT refresh token for obtaining new access tokens
        token_type:
          type: string
          enum: ["Bearer"]
          default: "Bearer"
          description: Token type (always "Bearer")
        expires_in:
          type: integer
          description: Number of seconds until the access token expires
        meta:
          type: object
          description: Additional metadata
          properties:
            method:
              type: string
              description: HTTP method used
            resource:
              type: string
              description: API endpoint
            message:
              type: string
              description: Response message of the action performed
            grant_type:
              type: string
              enum: ["authorization_code"]
              description: Grant type (always "authorization_code")

    RefreshTokenGrantResponse:
      type: object
      required:
        - access_token
        - token_type
        - expires_in
        - meta
      properties:
        access_token:
          type: string
          description: JWT access token
        token_type:
          type: string
          enum: ["Bearer"]
          default: "Bearer"
          description: Token type (always "Bearer")
        expires_in:
          type: integer
          description: Number of seconds until the access token expires
        meta:
          type: object
          description: Additional metadata
          properties:
            method:
              type: string
              description: HTTP method used
            resource:
              type: string
              description: API endpoint
            message:
              type: string
              description: Response message of the action performed
            grant_type:
              type: string
              enum: ["refresh_token"]
              description: Grant type (always "refresh_token")

    OAuthTokenResponse:
      oneOf:
        - $ref: "#/components/schemas/AuthorizationCodeGrantResponse"
        - $ref: "#/components/schemas/RefreshTokenGrantResponse"

    SuccessfulResponse:
      type: object
      properties:
        status_code:
          type: integer
        message:
          type: string
        meta:
          $ref: "#/components/schemas/Meta"
        data:
          # TODO: Connexion 3.1. upgrade - oneOf is causing issues
          # https://github.com/spec-first/connexion/issues/1720
          # oneOf:
          #   - type: array
          #   - type: object
          nullable: true
        warnings:
          type: array
          items:
            $ref: "#/components/schemas/ValidationErrorDetail"
      required: ["status_code"]
      additionalProperties: false

    LMGAuthURIResponse:
      type: object
      properties:
        auth_uri:
          type: string
        code_verifier:
          type: string
          nullable: true
        nonce:
          type: string
          nullable: true
        redirect_uri:
          type: string
        token_endpoint:
          type: string
        scope:
          type: array
          items:
            type: string
        state:
          type: string
        flow_id:
          type: string
        end_session_url:
          type: string
        user_type:
          type: string
        operation:
          type: string

    LMGAuthCodeResponse:
      type: object
      properties:
        code:
          type: string
        state:
          type: string

    LMGTokenRequest:
      type: object
      properties:
        auth_uri_res:
          $ref: "#/components/schemas/LMGAuthURIResponse"
        auth_code_res:
          $ref: "#/components/schemas/LMGAuthCodeResponse"

    OAuthServerCodeResponse:
      type: object
      properties:
        user_id:
          type: string
          format: uuid
          example: "009fa369-291b-403f-a85a-15e938c26f2f"
        authz_code:
          type: string
        expires_at:
          type: string
          format: date-time

    OAuthServerUser:
      type: object
      properties:
        email:
          type: string
          format: email
          example: "<EMAIL>"
        first_name:
          type: string
          example: "Jane"
        last_name:
          type: string
          example: "Doe"

    OAuthServerMeResponse:
      type: object
      properties:
        user:
          $ref: "#/components/schemas/OAuthServerUser"
        user_leave_administrators:
          type: string
          example: "419904348, 419904349"

    ErrorResponse:
      type: object
      properties:
        status_code:
          type: integer
        message:
          type: string
        meta:
          $ref: "#/components/schemas/Meta"
        data:
          oneOf:
            - type: array
            - type: object
        warnings:
          type: array
          items:
            $ref: "#/components/schemas/ValidationErrorDetail"
        errors:
          type: array
          items:
            $ref: "#/components/schemas/ValidationErrorDetail"
      required: ["status_code", "errors"]
      additionalProperties: false

    Meta:
      type: object
      properties:
        resource:
          type: string
        method:
          type: string
        query:
          type: object
        paging:
          type: object
          properties:
            page_offset:
              type: integer
            page_size:
              type: integer
            total_records:
              type: integer
            total_pages:
              type: integer
            order_by:
              type: string
            order_direction:
              type: string
      required: ["resource", "method"]
      additionalProperties: false

    ValidationErrorDetail:
      type: object
      properties:
        type:
          example: "required"
          type: string
        message:
          example: "leave_details.reason is required."
          description: "Somewhat user-friendly description of the error. May contain PII."
          type: string
        rule:
          description: >
            Usage varies depending on the source of the validation error. OpenAPI validation
            errors will set this to the expected value behavior (i.e the max length, or pattern).
          oneOf:
            - type: string
            - type: number
            - type: integer
            - type: boolean
            - type: array
            - type: object
          nullable: true
        field:
          description: "Field path, when the error is associated with a specific field."
          example: "leave_details.reason"
          nullable: true
          type: string
        extra:
          type: object
          additionalProperties:
            type: string

    Address:
      type: object
      properties:
        city:
          type: string
          nullable: true
          maxLength: 40 # FINEOS requirement
          example: "Springfield"
        line_1:
          type: string
          nullable: true
          maxLength: 40 # FINEOS requirement
          example: "742 Evergreen Terrace"
        line_2:
          type: string
          nullable: true
          maxLength: 40 # FINEOS requirement
          example: ""
        state:
          type: string
          nullable: true
          maxLength: 40 # FINEOS requirement
          example: "MA"
        zip:
          type: string
          nullable: true
          pattern: '^[0-9]{5}((?:-[0-9]{4})?|(?:-\*{4})?)$'
          example: "12345"

    Flag:
      type: object
      properties:
        start:
          type: string
          nullable: true
          format: date-time
          example: "2020-06-26T01:02:03.967736+00:00"
        end:
          type: string
          nullable: true
          format: date-time
          example: "2020-06-26T01:02:03.967736+00:00"
        name:
          type: string
          example: "maintenance"
          readOnly: true
        options:
          type: object
          nullable: true
          example:
            page_routes:
              - "/applications/*"
              - "/employers/*"
              - "/employer-exemptions/*"
              - "/insurance-providers/*"
        enabled:
          type: boolean
          example: false

    FlagWithLog:
      allOf:
        - $ref: "#/components/schemas/Flag"
        - type: object
          properties:
            first_name:
              type: string
              example: "John"
              readOnly: true
            last_name:
              type: string
              example: "Doe"
              readOnly: true
            updated_at:
              type: string
              nullable: false
              format: date-time
              example: "2020-06-26T01:02:03.967736+00:00"
              readOnly: true

    ExperianReturn:
      type: object
      properties:
        verify_level:
          type: string
          nullable: false
          format: str
          example: "StreetPartial"
        picklist:
          type: array
          nullable: true
          example: ["742 Evergreen Terrace"]
        valid_address:
          type: array
          nullable: true
          example:
            [
              {
                "Address Line 1": "742 Evergreen Terrace",
                "Address Line 2": null,
                "City": "Manchester",
                "State": "CT",
                "Zip+4": "06042-8721",
              },
            ]

    UserResponse:
      type: object
      properties:
        user_id:
          type: string
          example: "009fa369-291b-403f-a85a-15e938c26f2f"
        auth_id:
          type: string
          format: uuid
          example: "00000000-291b-403f-a85a-15e938c26f2f"
        first_name:
          type: string
          example: "Jane"
          nullable: true
        last_name:
          type: string
          example: "Doe"
          nullable: true
        email_address:
          type: string
          format: email
          example: "<EMAIL>"
        application_names:
          type: array
          items:
            type: object
            additionalProperties: false
            properties:
              first_name:
                type: string
                example: "Jane"
                nullable: true
              middle_name:
                type: string
                example: "Emily"
                nullable: true
              last_name:
                type: string
                example: "Doe"
                nullable: true
        consented_to_data_sharing:
          type: boolean
          example: false
        roles:
          type: array
          items:
            $ref: "#/components/schemas/RoleResponse"
        user_leave_administrators:
          type: array
          items:
            $ref: "#/components/schemas/UserLeaveAdminResponse"
        has_multiple_tax_identifiers:
          type: boolean
          example: false
        consented_to_view_tax_documents:
          type: boolean
          nullable: true
          example: false
        language_preference:
          $ref: "#/components/schemas/Language"
          nullable: true

    OauthMeUserResponse:
      type: object
      properties:
        id:
          type: string
          format: email
          example: "<EMAIL>"
        firstName:
          type: string
          example: "Jane"
        lastName:
          type: string
          example: "Doe"

    OAuthServerConsumerCustomFields:
      nullable: true
      type: object
      properties:
        consumerCustomFieldIdent:
          type: string
          example: "*********, *********"
        other:
          type: string
          example: "userLeaveAdministrators"

    OAuthServerConsumerContactCustomFields:
      nullable: true
      type: object
      properties:
        consumerContactCustomFields:
          type: string
          example: "<EMAIL>"
        other:
          type: string
          example: "emailAddress"

    OauthMeResponse:
      type: object
      properties:
        user:
          $ref: "#/components/schemas/OauthMeUserResponse"
        consumerCustomFields:
          $ref: "#/components/schemas/OAuthServerConsumerCustomFields"
        consumerContactCustomFields:
          $ref: "#/components/schemas/OAuthServerConsumerContactCustomFields"

    VCMComparisonResponse:
      type: object
      properties:
        employee_id:
          type: string
          nullable: true
          example: "009fa369-291b-403f-a85a-15e938c26f2f"
        fineos_customer_number:
          type: string
          description: The Fineos customer number associated with the comparison.
        customer_active_status_name:
          type: string
          nullable: true
          description: The active status name of the customer in EDM.
        customer_approval_status_name:
          type: string
          nullable: true
          description: The approval status name of the customer in EDM.
        pfml:
          $ref: "#/components/schemas/OverpaymentCustomerDetailsResponse"
          description: The PFML data for the comparison.
        edm:
          $ref: "#/components/schemas/OverpaymentCustomerDetailsResponse"
          description: The EDM data for the comparison.
    OverpaymentCustomerDetailsResponse:
      type: object
      properties:
        vendor_customer_code:
          type: string
          nullable: true
          description: The vendor customer code.
        first_name:
          type: string
          nullable: true
          description: The first name of the customer.
        last_name:
          type: string
          nullable: true
          description: The last name of the customer.
        legal_name:
          type: string
          nullable: true
          description: The legal name of the customer.
        address:
          $ref: "#/components/schemas/Address"
          description: The address of the customer.

    OverpaymentSearchResponse:
      type: object
      properties:
        employee_firstname:
          type: string
        employee_lastname:
          type: string
        fineos_customer_number:
          type: string
        overpayment_cases:
          type: array
          items:
            $ref: "#/components/schemas/OverpaymentCaseResponse"
        employee_address:
          $ref: "#/components/schemas/Address"
        prevent_referral:
          type: boolean
        referral_prevention_reason:
          type: string
    OverpaymentCaseResponse:
      type: object
      properties:
        overpayment_id:
          type: string
          format: uuid
        overpayment_casenumber:
          type: string
        amount:
          type: number
        recovered_to_date_amount:
          type: number
        outstanding_amount:
          type: number
        overpayment_case_creation_date:
          type: string
          format: date
        overpayment_adjustment_amount:
          type: number
        agreed_recovery_amount:
          type: number
        period_start_date:
          type: string
          format: date
        period_end_date:
          type: string
          format: date
        adjustment_description:
          type: string
        referral_date:
          type: string
          format: date
        referral_status:
          type: string
        mmars_events:
          type: array
          items:
            $ref: "#/components/schemas/MmarsEventResponse"
    MmarsEventResponse:
      type: object
      properties:
        mmars_event_id:
          type: string
          format: uuid
        mmars_event_type_id:
          type: integer
        mmars_status_type_id:
          type: integer
        is_holdable:
          type: boolean
        is_releasable:
          type: boolean
        created_at:
          type: string
          format: date
        updated_at:
          type: string
          format: date
    OmniSearchRequest:
      type: object
      properties:
        search:
          type: string
          example: "Jane"

    OverpaymentSearchRequest:
      type: object
      properties:
        fineos_customer_number:
          type: string
          example: "1111111"
    ReferOverpaymentRequest:
      type: object
      properties:
        overpayment_id:
          type: string
          format: uuid
          example: "c058ca18-1b14-4eba-bb49-0a9775730d77"
      required: ["overpayment_id"]
    RetryOverpaymentTransactionRequest:
      type: object
      properties:
        mmars_event_id:
          type: string
          format: uuid
          example: "d4922e69-ca13-4ac3-80ca-de10087859d3"
      required: ["mmars_event_id"]
    HoldOverpaymentTransactionRequest:
      type: object
      properties:
        mmars_event_id:
          type: string
          format: uuid
          example: "d4922e69-ca13-4ac3-80ca-de10087859d3"
        reason:
          type: string
          example: "Further investigation required"
      required: ["mmars_event_id"]
    OverpaymentMarkVcmReviewedRequest:
      type: object
      properties:
        employee_id:
          type: string
          format: uuid
          example: "d4922e69-ca13-4ac3-80ca-de10087859d3"
      required: ["employee_id"]
    UserAuthLogResponse:
      type: object
      nullable: true
      properties:
        user_id:
          type: string
          example: "009fa369-291b-403f-a85a-15e938c26f2f"
          nullable: true
        started_at:
          type: string
          example: "2023-05-17"
        completed_at:
          type: string
          example: "2023-05-17"
        auth_conversion_status_id:
          type: number
          example: 2
        oauth_operation:
          type: string
          example: Authenticate
        meta_data:
          type: object
          nullable: true
          properties:
            outcomes:
              type: array
              items:
                type: string

    UserProfileCheckForUpdatesResponse:
      type: object
      properties:
        profile_updates:
          type: object
          additionalProperties: false
          properties:
            gender:
              $ref: "#/components/schemas/UpdatableItemResponse"
            raceEthnicity:
              $ref: "#/components/schemas/UpdatableItemResponse"

    AdminUserResponse:
      type: object
      properties:
        sub_id:
          type: string
          example: "QfbsFWS4NTwEUFQKUCmwipIyJiZOxxKBLGSQC0tNFAB"
        first_name:
          type: string
          example: "Jane"
        last_name:
          type: string
          example: "Doe"
        email_address:
          type: string
          format: email
          example: "<EMAIL>"
        groups:
          type: array
          items:
            type: string
        permissions:
          type: array
          items:
            type: string

    RoleResponse:
      type: object
      additionalProperties: false
      properties:
        role_id:
          type: integer
        role_description:
          type: string

    UserLeaveAdminResponse:
      type: object
      additionalProperties: false
      properties:
        email_address:
          type: string
          format: email
        employer_id:
          type: string
          format: uuid
          example: "009fa369-291b-403f-a85a-15e938c26f2f"
        employer_fein:
          type: string
          example: "41-9904348"
        employer_dba:
          type: string
          example: "The Super Store"
          nullable: true
          description: "Employer's Doing Business As (DBA) name. This is the name under which the employer operates and can be different from the legal name."
        has_fineos_registration:
          type: boolean
        has_verification_data:
          type: boolean
        has_verified_leave_admin:
          type: boolean
          nullable: true
        verified:
          type: boolean
        verification_type:
          type: string
          nullable: true
        verified_at:
          type: string
          format: date-time
          nullable: true
        added_at:
          type: string
          format: date-time
          nullable: true
        added_by:
          $ref: "#/components/schemas/AddedByUserResponse"
          nullable: true
        first_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true
        phone_number:
          $ref: "#/components/schemas/MaskedPhoneNumber"
          nullable: true
        phone_extension:
          type: string
          nullable: true
        organization_units:
          type: array
          nullable: true
        user_leave_administrator_id:
          type: string
          format: uuid
          example: "009fa369-291b-403f-a85a-15e938c26f2f"
          nullable: true
        user_leave_administrator_action_id:
          type: string
          format: uuid
          example: "009fa369-291b-403f-a85a-15e938c26f2f"
          nullable: true

    UpdatableItemResponse:
      type: object
      additionalProperties: false
      properties:
        old_value:
          $ref: "#/components/schemas/AnyValue"
        new_value:
          type: object
          additionalProperties: true

    AddedByUserResponse:
      type: object
      additionalProperties: false
      nullable: true
      properties:
        email_address:
          type: string
        first_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true

    RoleUserDeleteRequest:
      type: object
      additionalProperties: false
      properties:
        role:
          type: object
          additionalProperties: false
          properties:
            role_description:
              type: string
          required: ["role_description"]
        user_id:
          type: string
          format: uuid
          example: "fabd09ed-0ea9-4091-843e-2e37d2c61482"
      required: ["role", "user_id"]

    UserUpdateRequest:
      type: object
      additionalProperties: false
      properties:
        consented_to_data_sharing:
          type: boolean
          nullable: false
          example: true
        consented_to_view_tax_documents:
          type: boolean
          nullable: false
          example: true
        first_name:
          type: string
          nullable: true
        last_name:
          type: string
          nullable: true
        phone:
          $ref: "#/components/schemas/Phone"
        language_preference:
          $ref: "#/components/schemas/Language"
          nullable: true

    ClaimSearchRequest:
      type: object
      properties:
        terms:
          $ref: "#/components/schemas/ClaimSearchRequestTerms"
        order:
          $ref: "#/components/schemas/ClaimSearchRequestOrder"
        paging:
          $ref: "#/components/schemas/SearchRequestPaging"
      required: ["terms"]

    ClaimSearchRequestOrder:
      type: object
      properties:
        by:
          type: string
          default: "created_at"
          enum:
            - "created_at"
            - "employee"
            - "latest_follow_up_date"
        direction:
          type: string
          example: "ascending"
          default: "descending"
          enum:
            - ascending
            - descending

    EmployeeSearchRequest:
      type: object
      properties:
        terms:
          $ref: "#/components/schemas/EmployeeSearchRequestTerms"
        order:
          $ref: "#/components/schemas/EmployeeSearchRequestOrder"
        paging:
          $ref: "#/components/schemas/SearchRequestPaging"
      required: ["terms"]

    EmployeeErrorResponse:
      type: object

    EmployeeResponse:
      type: object
      properties:
        employee_id:
          type: string
          format: uuid
          example: "009fa369-291b-403f-a85a-15e938c26a7b"
        first_name:
          type: string
          example: "Jane"
        middle_name:
          type: string
          nullable: true
          example: "Alice"
        other_name:
          type: string
          nullable: true
          example: "Marie"
        email_address:
          type: string
          format: email
          nullable: true
          example: "<EMAIL>"
        last_name:
          type: string
          example: "Doe"
        phone_numbers:
          type: array
          items:
            nullable: true
            $ref: "#/components/schemas/Phone"
        tax_identifier_last4:
          type: string
          nullable: true
          example: "1234"
        tax_identifier:
          $ref: "#/components/schemas/SsnItin"
          nullable: true
        fineos_customer_number:
          type: string
          nullable: true
          example: "1111111"
        mass_id_number:
          nullable: true
          $ref: "#/components/schemas/MassId"
        out_of_state_id_number:
          type: string
          nullable: true
        date_of_birth:
          nullable: true
          $ref: "#/components/schemas/MaskedDate"
        created_at:
          format: date
      required: ["employee_id"]

    EmployeeBasicResponse:
      type: object
      properties:
        employee_id:
          type: string
          format: uuid
          example: "009fa369-291b-403f-a85a-15e938c26a7b"
        fineos_customer_number:
          type: string
          nullable: true
          example: "1111111"
        first_name:
          type: string
          example: "Jane"
        middle_name:
          type: string
          nullable: true
          example: "Alice"
        last_name:
          type: string
          example: "Doe"
        other_name:
          type: string
          nullable: true
          example: "Marie"
      required: ["employee_id"]

    EmployeeLeaveAllotmentResponse:
      type: object
      properties:
        leave_plan_time:
          $ref: "#/components/schemas/EmployeeLeaveAvailability"
        overall_time:
          type: array
          items:
            $ref: "#/components/schemas/EmployeeLeaveAvailability"
        employer_fein:
          type: string
          example: "41-9904348"
        tax_identifier:
          $ref: "#/components/schemas/MaskedSsnItin"
        employee_date_of_birth:
          $ref: "#/components/schemas/MaskedDate"
        benefit_year_start_date:
          $ref: "#/components/schemas/Date"
        benefit_year_end_date:
          $ref: "#/components/schemas/Date"

    EmployeeLeaveAvailability:
      type: object
      properties:
        leave_plan_name:
          type: string
          example: "MA PFML - Employee"
        approved_time:
          $ref: "#/components/schemas/EmployeeLeaveAvailabilityBreakdown"
        available_balance:
          $ref: "#/components/schemas/EmployeeLeaveAvailabilityBreakdown"

    EmployeeLeaveAvailabilityBreakdown:
      type: object
      properties:
        weeks:
          type: number
          example: 12
        days:
          type: number
          example: 5
        hours:
          type: number
          example: 13

    EmployerClaimRequestBody:
      type: object
      properties:
        employer_benefits:
          type: array
          items:
            $ref: "#/components/schemas/EmployerClaimReviewRequestEmployerBenefit"
          maxItems: 10
        previous_leaves:
          type: array
          items:
            $ref: "#/components/schemas/EmployerClaimReviewRequestPreviousLeave"
          maxItems: 16
        has_amendments:
          type: boolean
        hours_worked_per_week:
          $ref: "#/components/schemas/HoursWorkedPerWeek"
        employer_decision:
          type: string
          enum:
            - Approve
            - Deny
            - Requires More Information
        fraud:
          type: string
          enum:
            - "Yes"
            - "No"
        comment:
          type: string
          maxLength: 9999
          nullable: true
        leave_reason:
          type: string
          nullable: true
          example: Care for a Family Member
        believe_relationship_accurate:
          type: string
          nullable: true
          enum:
            - "Yes"
            - "No"
            - "Unknown"
        relationship_inaccurate_reason:
          type: string
          nullable: true
          maxLength: 9999
          example: Their reported info doesn't match my records
      additionalProperties: false
      required:
        - employer_benefits
        - previous_leaves
        - hours_worked_per_week

    BenefitYearResponse:
      type: object
      properties:
        employee_id:
          type: string
          format: uuid
          example: "2a340cf8-6d2a-4f82-a075-73588d003f8f"
        benefit_year_start_date:
          $ref: "#/components/schemas/Date"
        benefit_year_end_date:
          $ref: "#/components/schemas/Date"
        current_benefit_year:
          type: boolean
          example: true
        invalid_benefit_years_since:
          $ref: "#/components/schemas/Date"
          nullable: true

    FinancialEligibilityCalculationResponse:
      type: object
      properties:
        benefit_year_start_date:
          $ref: "#/components/schemas/Date"
        benefit_year_end_date:
          $ref: "#/components/schemas/Date"
        base_period_start_date:
          $ref: "#/components/schemas/Date"
        base_period_end_date:
          $ref: "#/components/schemas/Date"
        is_aww_overridden:
          type: boolean
        is_missing_wage_data:
          type: boolean
        is_eligible:
          type: boolean
        employers:
          type: array
          items:
            $ref: "#/components/schemas/FinancialEligibilityCalculationEmployer"
        quarterly_wages:
          type: array
          items:
            $ref: "#/components/schemas/FinancialEligibilityCalculationQuarterWage"
        average_weekly_wage:
          type: number
        total_wages:
          type: number

    FinancialEligibilityCalculationEmployer:
      type: object
      properties:
        employer_id:
          type: string
          format: uuid
        employer_fein:
          type: string
        employer_dba:
          type: string
          example: "The Super Store"
          nullable: true
          description: "Employer's Doing Business As (DBA) name. This is the name under which the employer operates and can be different from the legal name."
        total_wages:
          type: number

    FinancialEligibilityCalculationQuarterWageEmployer:
      type: object
      properties:
        employer_id:
          type: string
          format: uuid
        quarter_wage:
          type: number
          nullable: true

    FinancialEligibilityCalculationQuarterWage:
      type: object
      properties:
        start_date:
          $ref: "#/components/schemas/Date"
        end_date:
          $ref: "#/components/schemas/Date"
        total_wages:
          type: number
        employers:
          type: array
          items:
            $ref: "#/components/schemas/FinancialEligibilityCalculationQuarterWageEmployer"

    UpdateClaimReviewResponse:
      type: object
      properties:
        claim_id:
          type: string
      additionalProperties: false
      required:
        - claim_id

    ClaimReviewResponse:
      type: object
      properties:
        date_of_birth:
          $ref: "#/components/schemas/MaskedDate"
        employer_benefits:
          type: array
          items:
            $ref: "#/components/schemas/EmployerBenefit"
        employer_dba:
          type: string
          nullable: true
        employer_id:
          type: string
          format: uuid
        employer_fein:
          $ref: "#/components/schemas/Fein"
        fineos_absence_id:
          type: string
          example: "NTN-111-ABS-01"
        first_name:
          type: string
        hours_worked_per_week:
          type: number
          nullable: true
        last_name:
          type: string
        middle_name:
          type: string
          nullable: true
        previous_leaves:
          type: array
          items:
            $ref: "#/components/schemas/PreviousLeave"
        residential_address:
          $ref: "#/components/schemas/Address"
        tax_identifier:
          $ref: "#/components/schemas/MaskedSsnItin"
        absence_periods:
          type: array
          items:
            $ref: "#/components/schemas/AbsencePeriodResponse"
        previous_pfml_leave_periods:
          type: array
          items:
            $ref: "#/components/schemas/PreviousPfmlPeriods"
        managed_requirements:
          type: array
          items:
            $ref: "#/components/schemas/ManagedRequirementResponse"
        computed_start_dates:
          $ref: "#/components/schemas/ComputedStartDates"
        payment_schedule_type:
          type: string
          enum:
            - Leave Start-Based
            - Sunday-Based
          nullable: true
          example: "Sunday-Based"
        approval_date:
          type: string
          format: date
          nullable: true
          example: "2022-06-19"
          description: "Date that this claim was approved."
        wages_and_benefits:
          $ref: "#/components/schemas/ClaimWagesBenefits"
      additionalProperties: false
      required:
        - employer_benefits
        - employer_dba
        - employer_fein
        - employer_id
        - fineos_absence_id
        - previous_leaves
        - residential_address
        - absence_periods

    ClaimWagesBenefits:
      type: object
      nullable: true
      properties:
        individual_average_weekly_wage:
          type: number
          nullable: true
          example: 1294.00
        weekly_benefit_amount:
          type: number
          nullable: true
          example: 921.37

    PreviousLeave:
      type: object
      properties:
        previous_leave_id:
          type: string
          format: uuid
          example: "2a340cf8-6d2a-4f82-a075-73588d003f8f"
          nullable: true
        is_for_current_employer:
          type: boolean
          nullable: true
          example: true
        is_continuous:
          type: boolean
          nullable: true
          example: true
        leave_end_date:
          $ref: "#/components/schemas/Date"
          nullable: true
        leave_start_date:
          $ref: "#/components/schemas/Date"
          nullable: true
        leave_reason:
          type: string
          nullable: true
          enum:
            - Pregnancy
            - A health condition during pregnancy
            - Caring for a family member with a serious health condition
            - Bonding with my child after birth or placement
            - Caring for a family member who serves in the armed forces
            - Managing family affairs while a family member is on active duty in the armed forces
            - Unknown
            - An illness or injury
            - An illness or injury that required hospitalization
        worked_per_week_minutes:
          type: integer
          nullable: true
          minimum: 0
        leave_minutes:
          type: integer
          nullable: true
          minimum: 0
        type:
          type: string
          nullable: true
          enum:
            - any_reason
            - other_reason
            - same_reason

    EmployerClaimReviewRequestPreviousLeave:
      allOf:
        - $ref: "#/components/schemas/PreviousLeave"
        - type: object
          properties:
            employer_changes:
              $ref: "#/components/schemas/EmployerChanges"

    ApplicationUserNotFoundInfo:
      type: object
      properties:
        currently_employed:
          type: boolean
          nullable: true
        date_of_hire:
          $ref: "#/components/schemas/Date"
          nullable: true
        date_of_separation:
          $ref: "#/components/schemas/Date"
          nullable: true
        employer_name:
          type: string
          maxLength: 256
          nullable: true
        recently_acquired_or_merged:
          type: boolean
          nullable: true

    ApplicationRequestBody:
      allOf:
        - type: object
          properties:
            organization_unit_selection:
              type: string
              nullable: true
              enum:
                - not_listed
                - not_selected
            organization_unit_id:
              type: string
              format: uuid
              example: "2f0f58a0-fcad-465b-b474-ee6c961cd6e3"
              nullable: true
            tax_identifier:
              $ref: "#/components/schemas/SsnItin"
              nullable: true
            employer_fein:
              $ref: "#/components/schemas/Fein"
              nullable: true
            hours_worked_per_week:
              type: number
              nullable: true
              example: 40.5
            hours_worked_per_week_all_employers:
              type: number
              nullable: true
              example: 40.5
              description: "Total time worked per week across all employers, expressed as a decimal number of hours. Minutes are expressed as fractions of an hour, so 40 hours and 30 minutes would be 40.5 hours"
            first_name:
              type: string
              nullable: true
              maxLength: 50
              example: "John"
            middle_name:
              type: string
              nullable: true
              maxLength: 50
              example: "Jake"
            last_name:
              type: string
              nullable: true
              maxLength: 50
              example: "Doe"
            date_of_birth:
              $ref: "#/components/schemas/DateOrMaskedDate"
              nullable: true
            has_mailing_address:
              type: boolean
              nullable: true
              example: false
            mailing_address:
              $ref: "#/components/schemas/Address"
              nullable: true
            residential_address:
              $ref: "#/components/schemas/Address"
              nullable: true
            has_continuous_leave_periods:
              type: boolean
              nullable: true
              example: true
            has_employer_benefits:
              type: boolean
              nullable: true
              example: true
            has_intermittent_leave_periods:
              type: boolean
              nullable: true
              example: true
            has_other_incomes:
              type: boolean
              nullable: true
              example: true
            has_reduced_schedule_leave_periods:
              type: boolean
              nullable: true
              example: true
            is_residential_address_validated:
              type: boolean
              nullable: true
              example: true
            is_mailing_address_validated:
              type: boolean
              nullable: true
              example: true
            has_state_id:
              type: boolean
              nullable: true
              example: true
            mass_id:
              $ref: "#/components/schemas/MassId"
              nullable: true
            employment_status:
              type: string
              nullable: true
              enum:
                - Employed
                - Unemployed
                - Self-Employed
                - Retired
                - Unknown
              example: "Employed"
            occupation:
              type: string
              nullable: true
              enum:
                - Sales Clerk
                - Administrative
                - Engineer
                - Health Care
              example: "Sales Clerk"
            gender:
              type: string
              nullable: true
              enum:
                - Woman
                - Man
                - Non-binary
                - Gender not listed
                - Prefer not to answer
            language:
              $ref: "#/components/schemas/Language"
            race:
              type: string
              nullable: true
              enum:
                - American Indian/Alaska Native
                - Asian/Asian American
                - Black/African American
                - Middle Eastern/North African
                - Native Hawaiian/Other Pacific Islander
                - White
                - Prefer not to answer
                - Another race not listed above
                - Multiracial
            race_custom:
              type: string
              example: "Black/Asian"
              nullable: true
            ethnicity:
              type: string
              nullable: true
              enum:
                - Prefer not to answer
                - Hispanic or Latino
                - Not Hispanic or Latino
            leave_details:
              $ref: "#/components/schemas/ApplicationLeaveDetails"
            work_pattern:
              $ref: "#/components/schemas/WorkPattern"
              nullable: true
            employer_benefits:
              nullable: true
              type: array
              items:
                $ref: "#/components/schemas/EmployerBenefit"
              maxItems: 6
            other_incomes:
              nullable: true
              type: array
              items:
                $ref: "#/components/schemas/OtherIncome"
              maxItems: 6
            phone:
              $ref: "#/components/schemas/Phone"
            has_previous_leaves:
              type: boolean
              nullable: true
              example: true
            previous_leaves:
              nullable: true
              description: |
                The list of all previous leaves.
              type: array
              items:
                $ref: "#/components/schemas/PreviousLeave"
              maxItems: 6
            additional_user_not_found_info:
              $ref: "#/components/schemas/ApplicationUserNotFoundInfo"
              nullable: true
            fields_to_use_from_user_profile:
              nullable: true
              type: array
            has_concurrent_employers:
              type: boolean
              nullable: true
              description: "Whether or not a claimant is working for multiple employers."
            mmg_idv_status:
              type: string
              nullable: true
              enum:
                - Unverified
                - Verified
            industry_sector:
              $ref: "#/components/schemas/IndustrySector"
              nullable: true
        - type: object
          properties:
            tax_identifier:
              nullable: true
            employer_fein:
              nullable: true
            date_of_birth:
              nullable: true
            mass_id:
              nullable: true

    ApplicationImportRequestBody:
      type: object
      properties:
        absence_case_id:
          type: string
          example: "NTN-111-ABS-01"
          nullable: true
        tax_identifier:
          $ref: "#/components/schemas/SsnItin"
          nullable: true

    ApplicationCompleteRequestBody:
      type: object
      properties:
        certificate_document_deferred:
          type: boolean
          default: false
          nullable: false
          description: Document uploading has been deferred

    ApplicationResponse:
      type: object
      properties:
        organization_unit_id:
          type: string
          format: uuid
          example: "2f0f58a0-fcad-465b-b474-ee6c961cd6e3"
          nullable: true
        application_id:
          type: string
          format: uuid
          example: "2a340cf8-6d2a-4f82-a075-73588d003f8f"
        fineos_absence_id:
          type: string
          example: "NTN-111-ABS-01"
          nullable: true
        fineos_absence_status:
          type: string
          example: "Adjudication"
          nullable: true
        tax_identifier:
          $ref: "#/components/schemas/MaskedSsnItin"
          nullable: true
        employee_id:
          type: string
          format: uuid
          example: "2a340cf8-6d2a-4f82-a075-73588d003f8f"
          nullable: true
        employer_id:
          type: string
          format: uuid
          example: "2f0f58a0-fcad-465b-b474-ee6c961cd6e3"
          nullable: true
        employer_dba:
          type: string
          example: "The Super Store"
          nullable: true
          description: "Employer's Doing Business As (DBA) name. This is the name under which the employer operates and can be different from the legal name."
        employer_fein:
          type: string
          nullable: true
          example: "41-9904348"
        first_name:
          type: string
          example: "John"
          nullable: true
        middle_name:
          type: string
          example: "Jake"
          nullable: true
        last_name:
          type: string
          example: "Doe"
          nullable: true
        date_of_birth:
          nullable: true
          $ref: "#/components/schemas/MaskedDate"
        has_continuous_leave_periods:
          type: boolean
          nullable: true
          example: true
        has_employer_benefits:
          type: boolean
          nullable: true
          example: true
        has_intermittent_leave_periods:
          type: boolean
          nullable: true
          example: true
        has_reduced_schedule_leave_periods:
          type: boolean
          nullable: true
          example: true
        has_other_incomes:
          type: boolean
          nullable: true
          example: true
        has_submitted_payment_preference:
          type: boolean
          nullable: true
          example: true
        has_state_id:
          type: boolean
          nullable: true
          example: true
        has_mailing_address:
          type: boolean
          nullable: true
          example: false
        mailing_address:
          $ref: "#/components/schemas/Address"
          nullable: true
        residential_address:
          $ref: "#/components/schemas/Address"
          nullable: true
        mass_id:
          type: string
          nullable: true
          example: "*********"
        employment_status:
          type: string
          enum:
            - Employed
            - Unemployed
            - Self-Employed
            - Retired
            - Unknown
          example: "Employed"
          nullable: true
        occupation:
          type: string
          enum:
            - Sales Clerk
            - Administrative
            - Engineer
            - Health Care
          example: "Sales Clerk"
          nullable: true
        employee_organization_units:
          type: array
          items:
            $ref: "#/components/schemas/OrganizationUnit"
        employer_organization_units:
          type: array
          items:
            $ref: "#/components/schemas/OrganizationUnit"
        organization_unit:
          nullable: true
          $ref: "#/components/schemas/OrganizationUnit"
        organization_unit_selection:
          type: string
          nullable: true
          enum:
            - not_listed
            - not_selected
        gender:
          type: string
          nullable: true
          enum:
            - Woman
            - Man
            - Non-binary
            - Gender not listed
            - Prefer not to answer
        language:
          $ref: "#/components/schemas/Language"
        race:
          type: string
          nullable: true
          enum:
            - American Indian/Alaska Native
            - Asian/Asian American
            - Black/African American
            - Middle Eastern/North African
            - Native Hawaiian/Other Pacific Islander
            - White
            - Prefer not to answer
            - Another race not listed above
            - Multiracial
        race_custom:
          type: string
          example: "Black/Asian"
          nullable: true
        ethnicity:
          type: string
          nullable: true
          enum:
            - Prefer not to answer
            - Hispanic or Latino
            - Not Hispanic or Latino
        hours_worked_per_week:
          type: number
          nullable: true
          example: 40.5
        hours_worked_per_week_all_employers:
          type: number
          nullable: true
          example: 40.5
          description: "Total time worked per week across all employers, expressed as a decimal number of hours. Minutes are expressed as fractions of an hour, so 40 hours and 30 minutes would be 40.5 hours"
        leave_details:
          $ref: "#/components/schemas/ApplicationLeaveDetails"
          nullable: true
        payment_preference:
          type: object
          items:
            $ref: "#/components/schemas/PaymentPreference"
          nullable: true
        work_pattern:
          $ref: "#/components/schemas/WorkPattern"
          nullable: true
        employer_benefits:
          type: array
          items:
            $ref: "#/components/schemas/EmployerBenefit"
          nullable: true
        other_incomes:
          type: array
          items:
            $ref: "#/components/schemas/OtherIncome"
          nullable: true
        imported_from_fineos_at:
          type: string
          format: date-time
          nullable: true
          example: "2020-06-26T01:02:03.967736+00:00"
        updated_at:
          type: string
          format: date-time
          example: "2020-06-26T01:02:03.967736+00:00"
        updated_time:
          type: string
          format: date-time
          description: |
            This field is deprecated. Please use `updated_at`.
          example: "2020-06-26T01:02:03.967736+00:00"
        status:
          type: string
          description: |
            The current application status.

            * `Started` - is started and in progress.
            * `In Manual Review` - is successfully stored in the claims processing system as a pdf indexing task.
            * `Submitted` - is successfully stored in the claims processing system as a claim.
            * `Completed` - is marked complete and has been submitted by the user.
            * `ReadyforReview` - marked complete, submitted by user, and all required documents uploaded
          enum:
            - Started
            - In Manual Review
            - Submitted
            - Completed
            - Ready for Review
          example: "Started"
        phone:
          $ref: "#/components/schemas/MaskedPhone"
          nullable: true
        has_previous_leaves:
          type: boolean
          nullable: true
        previous_pfml_leave_periods:
          type: array
          items:
            $ref: "#/components/schemas/PreviousPfmlPeriods"
        previous_leaves:
          description: |
            The list of all previous leaves.
          nullable: true
          type: array
          items:
            $ref: "#/components/schemas/PreviousLeave"
        is_withholding_tax:
          type: boolean
          nullable: true
        computed_start_dates:
          $ref: "#/components/schemas/ComputedStartDates"
        split_from_application_id:
          type: string
          format: uuid
          example: "2f0f58a0-fcad-465b-b474-ee6c961cd6e3"
          nullable: true
        split_into_application_id:
          type: string
          format: uuid
          example: "2f0f58a0-fcad-465b-b474-ee6c961cd6e3"
          nullable: true
        computed_earliest_submission_date:
          type: string
          format: date
          example: "2022-07-04"
          nullable: true
        computed_application_split:
          $ref: "#/components/schemas/ComputedApplicationSplit"
          nullable: true
        computed_leave_details_is_editable:
          type: boolean
        computed_has_passed_manual_review:
          type: boolean
        fields_to_use_from_user_profile:
          nullable: true
          type: array
        has_concurrent_employers:
          type: boolean
          nullable: true
          description: "Whether or not a claimant is working for multiple employers."
        mmg_idv_status:
          type: string
          description: |
            The applicant's MyMassGov (MMG) Identity Verification (IDV) status.
            * `null` - The applicant has not started IDV, or the identity confirmation step is incomplete.
            * `Unverified` - MMG was unable to verify the applicant's, or an applicant's document verification is incomplete.
            * `Verified` - MMG has verified the applicant's identity.
          nullable: true
          enum:
            - Unverified
            - Verified
        industry_sector:
          $ref: "#/components/schemas/IndustrySector"
          nullable: true

    ComputedStartDates:
      type: object
      properties:
        other_reason:
          type: string
          format: date
          example: "2021-01-01"
          nullable: true
        same_reason:
          type: string
          format: date
          example: "2021-01-01"
          nullable: true

    ComputedApplicationSplit:
      type: object
      properties:
        crossed_benefit_year:
          $ref: "#/components/schemas/BenefitYearResponse"
        application_dates_in_benefit_year:
          $ref: "#/components/schemas/StartEndDates"
        application_dates_outside_benefit_year:
          $ref: "#/components/schemas/StartEndDates"
        application_outside_benefit_year_submittable_on:
          type: string
          format: date
          example: "2020-11-02"

    StartEndDates:
      type: object
      properties:
        start_date:
          type: string
          format: date
          example: "2021-01-01"
        end_date:
          type: string
          format: date
          example: "2021-01-12"

    AppealCreateRequest:
      type: object
      properties:
        fineos_absence_id:
          type: string
          example: "NTN-01-ABS-01"
        appeal_reason:
          type: string
          example: "Missed deadline"
          nullable: true

    AppealUpdateRequest:
      type: object
      properties:
        appeal_phone_number:
          $ref: "#/components/schemas/Phone"
          nullable: true
        appeal_reason:
          type: string
          example: "Missed deadline"
          nullable: true
        for_private_insurance:
          type: boolean
          example: false
          nullable: true
        needs_interpreter:
          type: boolean
          example: false
          nullable: true
        interpreter_language_requested:
          type: string
          example: "Spanish"
          nullable: true
        appeal_representative_option:
          type: string
          nullable: true
          example: "Yes"
          enum:
            - "Yes"
            - "No"
            - Unknown
        originally_decided_at:
          type: string
          format: date
          example: "2020-06-26"
          nullable: true
        originally_decided_at_reason_for_past_due:
          type: string
          example: "Was out of town"
          nullable: true

    AppealsSearchTerms:
      type: object
      properties:
        appeal_id:
          type: string
          format: uuid
          example: "7c1bdaf8-51b4-4a1b-a665-463450d75c95"
          nullable: true
        fineos_appeal_id:
          type: string
          example: "NTN-01-ABS-01-AP-01"
          nullable: true
        fineos_absence_id:
          type: string
          example: "NTN-01-ABS-01"
          nullable: true
        appeal_phone_number:
          $ref: "#/components/schemas/Phone"
          nullable: true
    WaitingPeriodsSearchRequest:
      type: object
      properties:
        terms:
          type: object
          properties:
            absence_case_id:
              type: string

    AppealsSearchRequest:
      type: object
      properties:
        terms:
          $ref: "#/components/schemas/AppealsSearchTerms"
          nullable: true

    AppealResponse:
      type: object
      properties:
        appeal_id:
          type: string
          format: uuid
          example: "7c1bdaf8-51b4-4a1b-a665-463450d75c95"
        fineos_appeal_id:
          type: string
          example: "NTN-01-ABS-01-AP-01"
          nullable: true
        fineos_absence_id:
          type: string
          example: "NTN-01-ABS-01"
        appeal_phone_number:
          $ref: "#/components/schemas/MaskedPhone"
          nullable: true
        appeal_reason:
          type: string
          example: "Missed deadline"
          nullable: true
        for_private_insurance:
          type: boolean
          example: false
          nullable: true
        had_preventing_circumstances:
          type: boolean
          example: false
          nullable: true
        has_read_notices:
          type: boolean
          example: false
          nullable: true
        needs_interpreter:
          type: boolean
          example: false
          nullable: true
        interpreter_language_requested:
          type: string
          example: "Spanish"
          nullable: true
        appeal_representative_option:
          type: string
          nullable: true
          example: "Yes"
          enum:
            - "Yes"
            - "No"
            - Unknown
        appeal_status:
          type: string
          nullable: true
          example: "Open - Conduct Hearing"
          enum:
            - "Closed - Claim Decision Changed"
            - "Closed - Claim Decision Affirmed"
            - "Trigger Appeal Started Notification"
            - "Open - Conduct Hearing"
            - "Open - Schedule Hearing"
            - "Add Employer Participant Role"
            - "Open - Reconsideration"
        originally_decided_at:
          type: string
          format: date
          example: "2020-06-26"
          nullable: true
        originally_decided_at_reason_for_past_due:
          type: string
          example: "Was out of town"
          nullable: true
        other_interpreter_language_requested:
          type: string
          example: "French"
          nullable: true
        reason_for_not_reading_notices:
          type: string
          example: "Didn't want to read them"
          nullable: true
        computed_is_more_than_ten_days_past_decision:
          type: boolean
          example: true
          nullable: true
        is_generated_from_extract:
          type: boolean
          example: false
        submitted_at:
          type: string
          format: date-time
          example: "2020-06-26T01:02:03.967736+00:00"
          nullable: true
        created_at:
          type: string
          format: date-time
          example: "2020-06-26T01:02:03.967736+00:00"
        updated_at:
          type: string
          format: date-time
          example: "2020-06-26T01:02:03.967736+00:00"

    AppealsResponse:
      type: array
      items:
        $ref: "#/components/schemas/AppealResponse"

    DocumentUploadRequest:
      type: object
      properties:
        document_type:
          type: string
          enum:
            - Passport
            - Driver's License Mass
            - Driver's License Other State
            - Identification Proof
            - State managed Paid Leave Confirmation
            - Approval Notice
            - Request for more Information
            - Denial Notice
            - Own serious health condition form
            - Pregnancy/Maternity form
            - Child bonding evidence form
            - Care for a family member form
            - Military exigency form
            - Pending Application Withdrawn
            - Appeal Acknowledgment
            - Maximum Weekly Benefit Change Notice
            - Benefit Amount Change Notice
            - Leave Allotment Change Notice
            - Approved Time Cancelled
            - Change Request Approved
            - Change Request Denied
            - Certification Form # Only used when Claimant Portal uploads certification docs
            - Appeals Supporting Documentation
            - Covered Service Member Identification Proof
            - Family Member Active Duty Service Proof
            - Denial Notice Explanation of Wages
            - Explanation of Wages
            - Approval of Application Change
            - Denial of Application Change
            - Approved Leave Dates Cancelled
            - Denial of Application
            - Approval Notice Explanation of Wages
            - "Family and Medical Leave Act Form"

          example: "Passport"
        name:
          type: string
          example: "passport.png"
        description:
          type: string
          example: "Jane Smith Passport"
        file:
          type: string
          format: binary
          nullable: false
      required:
        - document_type
        - file
      additionalProperties: false

    ConfirmDocumentsRequest:
      type: object
      description: The FINEOS document ID(s) that are filtered out of db query in endpoint
      properties:
        fineos_document_ids:
          type: array
          nullable: true
          items:
            type: string
          example: ["12345", "56789"]

    WithholdingResponse:
      type: object
      properties:
        filing_period:
          format: date
          example: "1970-06-01"

    ManagedRequirementResponse:
      type: object
      properties:
        follow_up_date:
          format: date
          example: "1970-06-01"
        responded_at:
          format: date
          example: "1970-06-01"
        responded_user_first_name:
          type: string
          example: "John"
        responded_user_last_name:
          type: string
          example: "Doe"
        status:
          type: string
          nullable: true
        category:
          type: string
          nullable: true
        type:
          type: string
          nullable: true
        dfml_decision_date:
          format: date
          example: "1970-06-01"
        created_at:
          format: date
    AbsencePeriodResponse:
      type: object
      properties:
        fineos_absence_period_id:
          type: string
          nullable: true
        absence_period_start_date:
          type: string
          format: date
          example: "2021-06-01"
        absence_period_end_date:
          type: string
          format: date
          example: "2021-06-08"
        modified_start_date:
          type: string
          format: date
          example: "2021-06-01"
          description: "Start date of the absence period reflecting any modifications made"
          nullable: true
        modified_end_date:
          type: string
          format: date
          example: "2021-06-08"
          description: "End date of the absence period reflecting any modifications made"
          nullable: true
        episodic_leave_period_detail:
          $ref: "#/components/schemas/EpisodicLeavePeriodDetail"
          nullable: true
        reason:
          type: string
          enum:
            - Care for a Family Member
            - Pregnancy/Maternity
            - Child Bonding
            - Serious Health Condition - Employee
            - Sickness - Non-Serious Health Condition - Employee
            - Military Caregiver
            - Military Exigency Family
            - Medical Donation - Employee
            - Preventative Care - Employee
            - Military - Employee
            - Personal - Employee
          example: "Child Bonding"
          nullable: true
        reason_qualifier_one:
          $ref: "#/components/schemas/ReasonQualifierOne"
          nullable: true
        reason_qualifier_two:
          type: string
          nullable: true
        period_type:
          type: string
          enum:
            - Continuous
            - Intermittent
            - Reduced Schedule
          example: "Continuous"
          nullable: true
        request_decision:
          type: string
          enum:
            - Pending
            - In Review
            - Approved
            - Denied
            - Cancelled
            - Withdrawn
            - Projected
            - Voided
          example: "Approved"
          nullable: true
        fineos_leave_request_id:
          type: integer
          example: 1234
          nullable: true
        document_type_requirements:
          type: array
          items:
            $ref: "#/components/schemas/DocumentType"
          nullable: true

    AbsencePeriodDetailResponse:
      type: object
      description:
        Response model for absence period properties used in DetailedClaimResponse.
        Includes properties from AbsencePeriodResponse and additional properties that should only be returned
        as part of DetailedClaimResponse.
      properties:
        fineos_absence_period_id:
          type: string
          nullable: true
        absence_period_start_date:
          type: string
          format: date
          example: "2021-06-01"
        absence_period_end_date:
          type: string
          format: date
          example: "2021-06-08"
        approved_start_date:
          type: string
          description:
            The approved start date from the corresponding Leave Request and
            Absence Paid Leave cases. Used to determine if AbsencePeriod is partially approved.
          format: date
          example: "2021-06-01"
          nullable: true
        approved_end_date:
          type: string
          description:
            The approved end date from the corresponding Leave Request and
            Absence Paid Leave cases. Used to determine if AbsencePeriod is partially approved.
          format: date
          example: "2021-06-08"
          nullable: true
        is_fully_approved:
          type: boolean
          description: Whether or not this absence period is fully approved.
          example: false
          nullable: true
        modified_start_date:
          type: string
          format: date
          example: "2021-06-01"
          description: "Start date of the absence period reflecting any modifications made"
          nullable: true
        modified_end_date:
          type: string
          format: date
          example: "2021-06-08"
          description: "End date of the absence period reflecting any modifications made"
          nullable: true
        episodic_leave_period_detail:
          $ref: "#/components/schemas/EpisodicLeavePeriodDetail"
          nullable: true
        reason:
          type: string
          enum:
            - Care for a Family Member
            - Pregnancy/Maternity
            - Child Bonding
            - Serious Health Condition - Employee
            - Sickness - Non-Serious Health Condition - Employee
            - Military Caregiver
            - Military Exigency Family
            - Medical Donation - Employee
            - Preventative Care - Employee
            - Military - Employee
            - Personal - Employee
          example: "Child Bonding"
          nullable: true
        reason_qualifier_one:
          $ref: "#/components/schemas/ReasonQualifierOne"
          nullable: true
        reason_qualifier_two:
          type: string
          nullable: true
        period_type:
          type: string
          enum:
            - Continuous
            - Intermittent
            - Reduced Schedule
          example: "Continuous"
          nullable: true
        request_decision:
          type: string
          enum:
            - Pending
            - In Review
            - Approved
            - Denied
            - Cancelled
            - Withdrawn
            - Projected
            - Voided
          example: "Approved"
          nullable: true
        fineos_leave_request_id:
          type: integer
          example: 1234
          nullable: true
        document_type_requirements:
          type: array
          items:
            $ref: "#/components/schemas/DocumentType"
          nullable: true

    EpisodicLeavePeriodDetail:
      type: object
      properties:
        fineos_absence_period_id:
          type: string
          nullable: true
        duration:
          type: integer
        duration_basis:
          type: string
          enum:
            - Minutes
            - Hours
            - Days
          example: "Hours"
        end_date:
          type: string
          format: date
        frequency:
          type: integer
        frequency_interval:
          type: integer
        frequency_interval_basis:
          type: string
          enum:
            - Days
            - Weeks
            - Months
          example: "Weeks"
        start_date:
          type: string
          format: date

    LeaveRequestResponse:
      type: object
      properties:
        absence_paid_leave_cases:
          type: array
          nullable: true
          items:
            $ref: "#/components/schemas/AbsencePaidLeaveCaseResponse"
        leave_request_id:
          type: string
          format: uuid
        absence_reason:
          type: string
        is_id_proofed:
          type: boolean
        leave_approval_decision:
          type: string
    AbsencePaidLeaveCaseResponse:
      type: object
      properties:
        absence_paid_leave_case_id:
          type: string
          format: uuid
        absence_paid_leave_case_number:
          type: string
        start_date:
          type: string
          format: date
        end_date:
          type: string
          format: date

    PreviousPfmlPeriods:
      type: object
      properties:
        start_date:
          format: date
          example: "1970-06-01"
        end_date:
          format: date
          example: "1970-06-01"

    DetailedClaimResponse:
      type: object
      properties:
        employer:
          $ref: "#/components/schemas/EmployerResponse"
          nullable: true
        employee:
          $ref: "#/components/schemas/EmployeeBasicResponse"
          nullable: true
        application_id:
          type: string
          format: uuid
          example: "fdc23456-c8fa-48bf-89a1-5c7c6a3af164"
          nullable: true
        fineos_absence_id:
          type: string
          example: "NTN-01-ABS-01"
          nullable: true
        fineos_notification_id:
          type: string
          example: "NTN-01"
          nullable: true
        created_at:
          type: string
          format: date
          nullable: true
        approval_date:
          type: string
          format: date
          nullable: true
          example: "2022-06-19"
          description: "Date that this claim was approved."
        does_claim_span_benefit_years:
          type: boolean
          example: false
        absence_periods:
          type: array
          items:
            $ref: "#/components/schemas/AbsencePeriodDetailResponse"
          nullable: true
        employer_review:
          $ref: "#/components/schemas/EmployerReview"
        has_paid_payments:
          type: boolean
          example: false
        payment_schedule_type:
          type: string
          enum:
            - Leave Start-Based
            - Sunday-Based
          nullable: true
          example: "Sunday-Based"
        document_requirements:
          type: array
          nullable: true
          items:
            type: object
            properties:
              document_type:
                $ref: "#/components/schemas/DocumentType"
              upload_date:
                description: The most recent upload date for this type of document
                type: string
                format: date
                example: "1970-06-01"
                nullable: true
        has_extensions:
          type: boolean
          nullable: true
        leave_requests:
          type: array
          nullable: true
          items:
            $ref: "#/components/schemas/LeaveRequestResponse"
        intermittent_leave_episodes:
          type: array
          items:
            $ref: "#/components/schemas/IntermittentLeaveEpisodeResponse"
          nullable: true
        payment_preference:
          type: object
          items:
            $ref: "#/components/schemas/PaymentPreference"
          nullable: true
        application_started_at_date:
          type: string
          format: date
          nullable: true
        application_completed_at_date:
          type: string
          format: date
          nullable: true
        appeal_filed:
          type: boolean
          nullable: true
        claim_payment_info:
          $ref: "#/components/schemas/ClaimPaymentInfo"

    ClaimPaymentInfo:
      type: object
      nullable: true
      description: Payment information for a claim
      properties:
        card_arrival_date:
          type: string
          format: date
          nullable: true
          description: The expected arrival date of a prepaid card, if that is the payment method for this claim.

    ClaimResponse:
      type: object
      properties:
        employer:
          nullable: true
          $ref: "#/components/schemas/EmployerResponse"
        employee:
          nullable: true
          $ref: "#/components/schemas/EmployeeBasicResponse"
        fineos_absence_id:
          type: string
          example: "NTN-01-ABS-01"
          nullable: true
        fineos_notification_id:
          type: string
          example: "NTN-01"
          nullable: true
        absence_period_start_date:
          type: string
          format: date
          example: "1970-06-01"
          nullable: true
          deprecated: true
          description: |
            This field is deprecated. Please use `claim_end_date`.
        absence_period_end_date:
          type: string
          format: date
          example: "1970-06-01"
          nullable: true
          deprecated: true
          description: |
            This field is deprecated. Please use `claim_end_date`.
        claim_start_date:
          type: string
          format: date
          example: "1970-06-01"
          nullable: true
        claim_end_date:
          type: string
          format: date
          example: "1970-06-01"
          nullable: true
        claim_status:
          $ref: "#/components/schemas/AbsenceCaseStatus"
          nullable: true
        claim_type_description:
          type: string
          example: "Medical Leave"
          nullable: true
        created_at:
          type: string
          format: date
          nullable: true
        managed_requirements:
          type: array
          nullable: true
          items:
            $ref: "#/components/schemas/ManagedRequirementResponse"
        absence_periods:
          type: array
          nullable: true
          items:
            $ref: "#/components/schemas/AbsencePeriodResponse"
        application_started_at_date:
          type: string
          format: date
          nullable: true
        application_completed_at_date:
          type: string
          format: date
          nullable: true
        appeal_filed:
          type: boolean
          nullable: true
        approval_date:
          type: string
          format: date
          nullable: true
          example: "2022-06-19"
          description: "Date that this claim was approved."

    ClaimsResponse:
      type: array
      items:
        $ref: "#/components/schemas/ClaimResponse"

    EmployeesResponse:
      type: array
      items:
        $ref: "#/components/schemas/EmployeeResponse"

    HolidaysSearchResponse:
      type: array
      items:
        type: object
        properties:
          name:
            type: string
            example: "New Year's Day"
          date:
            $ref: "#/components/schemas/Date"

    PaymentsResponse:
      type: object
      properties:
        absence_case_id:
          type: string
          example: "NTN-01-ABS-01"
        payments:
          type: array
          items:
            $ref: "#/components/schemas/PaymentResponse"

    PaymentResponse:
      type: object
      properties:
        payment_id:
          type: string
          format: uuid
          nullable: true
        period_start_date:
          format: date
          example: "1970-06-01"
        period_end_date:
          format: date
          example: "1970-06-01"
        amount:
          type: number
          nullable: true
          example: 750.25
        sent_to_bank_date:
          format: date
          example: "1970-06-01"
          nullable: true
          type: string
        payment_method:
          type: string
          enum:
            - Elec Funds Transfer
            - Check
            - Prepaid Card
          nullable: true
          example: "Elec Funds Transfer"
        expected_send_date_start:
          format: date
          type: string
          nullable: true
          example: "1970-06-01"
        expected_send_date_end:
          format: date
          type: string
          nullable: true
          example: "1970-06-01"
        cancellation_date:
          format: date
          type: string
          nullable: true
          example: "2021-01-01"
        status:
          type: string
          enum:
            - Sent to bank
            - Pending
            - Cancelled
            - Delayed
          example: "Delayed"
        writeback_transaction_status:
          type: string
          nullable: true
          example: "Address Validation Error"
        transaction_date:
          format: date
          type: string
          nullable: true
          example: "2021-01-01"
        transaction_date_could_change:
          type: boolean
          example: false
        num_days_to_process_delay:
          type: integer
          nullable: true
          example: 5
          description: Number of days for delayed transactions to appear from Processed to Delayed
        payment_details:
          type: array
          items:
            $ref: "#/components/schemas/PaymentDetailsResponse"

    WaitingPeriodsResponse:
      type: object
      properties:
        waiting_periods:
          type: array
          items:
            $ref: "#/components/schemas/WaitingPeriod"

    WaitingPeriod:
      type: object
      properties:
        waiting_period_start_date:
          $ref: "#/components/schemas/Date"
          nullable: true
        waiting_period_end_date:
          $ref: "#/components/schemas/Date"
          nullable: true
        is_benefit_year_waiting_period:
          type: boolean
          nullable: true
          description: "is the waiting period for the start of a new benefit year"

    PaymentDetailsResponse:
      type: object
      properties:
        payment_detail_id:
          type: string
          format: uuid
          example: "fabd09ed-0ea9-4091-843e-2e37d2c61482"
        period_start_date:
          type: string
          format: date
          example: "2022-05-01"
          nullable: true
        period_end_date:
          type: string
          format: date
          example: "2022-06-01"
          nullable: true
        amount:
          type: number
          example: 750.25
          description: "Amount after reductions and split payments are taken out."
        business_net_amount:
          type: number
          example: 750.25
          description: "Amount after reductions are taken out, but prior to split payments. Used in max weekly benefits logic."
        payment_lines:
          type: array
          items:
            $ref: "#/components/schemas/PaymentLineResponse"

    UserLeaveAdminRequest:
      type: object
      properties:
        user_id:
          type: string
          format: uuid
          nullable: true
        employer_id:
          type: string
          format: uuid
          nullable: true
    UserLeaveAdminAddRequest:
      type: object
      properties:
        email_address:
          type: string
          format: email
          example: "<EMAIL>"
          minLength: 3
        employer_id:
          type: string
          format: uuid
    PaymentLineResponse:
      type: object
      properties:
        payment_line_id:
          type: string
          format: uuid
          example: "fabd09ed-0ea9-4091-843e-2e37d2c61482"
        amount:
          type: number
          example: 750.25
        line_type:
          type: string
          example: "Auto Gross Entitlement"
          description: "Category of the line, e.g., Auto Gross Entitlement, State Income Tax, etc. See: https://lwd.atlassian.net/wiki/spaces/DD/pages/2249818349/Tech+Spec+-+Consume+PEI+Line+Items"
        line_type_category:
          type: string
          enum:
            - "Other leave, income and benefits"
            - "Adjustments"
            - "Gross payment amount"
            - "Tax withholding"
            - "Net payment amount"
            - "Overpayment"
            - "Employer reimbursements"
            - "Child support"
          example: "Gross payment amount"
          description: "Category of line type, e.g. Gross payment amount, Net payment amount, Tax witholding, etc. See: https://lwd.atlassian.net/wiki/spaces/DD/pages/2301984773/PFMLPB-3268+Content+for+payment+breakdowns#Payment-line-items"

    ChangeRequestsResponse:
      type: array
      items:
        $ref: "#/components/schemas/ChangeRequestResponse"

    EmployerResponse:
      type: object
      properties:
        employer_id:
          type: string
          format: uuid
          example: "fabd09ed-0ea9-4091-843e-2e37d2c61482"
        employer_fein:
          type: string
          example: "12-3456789"
        employer_dba:
          type: string
          example: "The Super Store"
          nullable: true
          description: "Employer's Doing Business As (DBA) name. This is the name under which the employer operates and can be different from the legal name."
        has_verification_data:
          type: boolean
          nullable: true
        has_verified_leave_admin:
          type: boolean
          nullable: true

    ClaimDocumentResponse:
      type: object
      properties:
        created_at:
          format: date
          type: string
          example: "1970-06-01"
          nullable: true
        document_type:
          type: string
          enum:
            - State managed Paid Leave Confirmation
            - Approval Notice
            - Request for more Information
            - Denial Notice
            - Employer Response Additional Documentation
            - Own serious health condition form
            - Pregnancy/Maternity form
            - Child bonding evidence form
            - Care for a family member form
            - Military exigency form
            - Pending Application Withdrawn
            - Appeal Acknowledgment
            - Maximum Weekly Benefit Change Notice
            - Benefit Amount Change Notice
            - Leave Allotment Change Notice
            - Approved Time Cancelled
            - Change Request Approved
            - Change Request Denied
            - Covered Service Member Identification Proof
            - Family Member Active Duty Service Proof
            - Overpayment Payoff Notice
            - Overpayment Notice-Full Balance Demand
            - OP- Partial Recovery and Remaining Bal
            - OP- Full Balance Recovery
            - OP- Full Balance Recovery - Manual
            - Appeal Hearing Virtual Fillable
            - Appeal RFI
            - Appeal - Returned to Adjudication
            - Appeal Approved
            - Appeal Dismissed - Other
            - Appeal Dismissed - Exempt Employer
            - Modify Decision
            - Appeal Withdrawn
            - Intermittent Time Approved Notice
            - Payment Received-Updated Overpayment Balance
            - Denial Notice Explanation of Wages
            - Explanation of Wages
            - Post-Adjudication Report
            - Approval of Application Change
            - Denial of Application Change
            - Approved Leave Dates Cancelled
            - Denial of Application
            - Intermittent Time Reported
            - Approval Notice Explanation of Wages
            - Overpayment Full Demand ER Benefits
            - Overpayment Full Demand Intermittent
            - Overpayment Full Demand Leave Change
            - Overpayment Full Demand Paid Time Off
            - Overpayment Full Demand UI
            - Overpayment Full Demand Workers Comp
            - Overpayment Full Recovery ER Benefits
            - Overpayment Full Recovery Intermittent
            - Overpayment Full Recovery Leave Change
            - Overpayment Full Recovery Paid Time Off
            - Overpayment Full Recovery UI
            - Overpayment Full Recovery Workers Comp
            - Overpayment Partial Demand ER Benefits
            - Overpayment Partial Demand Intermittent
            - Overpayment Partial Leave Change
            - Overpayment Partial Paid Time Off
            - Overpayment Partial Demand UI
            - Overpayment Partial Demand Workers Comp
            - Overpayment Payment Received New Balance
            - Overpayment Payoff
            - Dismissal for Failure to Attend Hearing
            - Notice of Default
            - W9 Tax Form
            - EFT Change Request
            - Notice of Child Support Withholding
          example: "Denial Notice"
        content_type:
          type: string
          example: "image/png"
          nullable: true
        fineos_document_id:
          type: string
          example: "1234"
        name:
          type: string
          example: "passport.png"
          nullable: true
        description:
          type: string
          example: "Jane Smith Passport"
          nullable: true
      required:
        - created_at
        - document_type
        - content_type
        - fineos_document_id
        - name
        - description
      additionalProperties: false

    CertificationDocumentData:
      type: object
      properties:
        first_name:
          type: string
          nullable: true
          example: "Jane"
        last_name:
          type: string
          nullable: true
          example: "Doe"
        middle_name:
          type: string
          nullable: true
          example: "S"
        other_name:
          type: string
          nullable: true
          example: "Janey"
        tax_identifier:
          type: string
          nullable: true
          example: "*********"
        date_of_birth:
          type: string
          format: date
          nullable: true
          example: "1970-06-01"
        fineos_absence_id:
          type: string
          nullable: true
          example: "NTN-9999-ABS-01"

    CertificationDocumentRequest:
      type: object
      properties:
        s3_bucket_url:
          type: string
          minLength: 1
          pattern: ^https?:\/\/[^\s]+$
        document_type:
          type: string
          enum:
            - DFML_FAMILY_SERIOUS_HEALTH_COND
            - DFML_YOUR_SERIOUS_HEALTH_COND
            - HPF_DIGITAL_CSHC
            - WH_380_F
            - WH_380_E
          example: "WH_380_F"
        document_data:
          $ref: "#/components/schemas/CertificationDocumentData"

    DocumentResponse:
      type: object
      properties:
        user_id:
          type: string
          format: uuid
          example: "fabd09ed-0ea9-4091-843e-2e37d2c61482"
        application_id:
          type: string
          format: uuid
          example: "fdc23456-c8fa-48bf-89a1-5c7c6a3af164"
          nullable: true
        appeal_id:
          type: string
          format: uuid
          example: "fdc23456-c8fa-48bf-89a1-5c7c6a3af164"
          nullable: true
        created_at:
          format: date
          example: "1970-06-01"
        document_type:
          $ref: "#/components/schemas/DocumentType"
          nullable: true
        content_type:
          type: string
          example: "image/png"
          nullable: true
        fineos_document_id:
          type: string
          example: "1234"
        name:
          type: string
          example: "passport.png"
        description:
          type: string
          example: "Jane Smith Passport"
        is_legal_notice:
          type: boolean
      required:
        - user_id
        - created_at
        - document_type
        - fineos_document_id
        - name
        - description
        - is_legal_notice
      additionalProperties: false

    PaymentPreferenceRequestBody:
      type: object
      properties:
        payment_preference:
          nullable: false
          $ref: "#/components/schemas/PaymentPreference"

    TaxWithholdingPreferenceRequestBody:
      type: object
      properties:
        is_withholding_tax:
          type: boolean
          nullable: true

    EmployerAddFeinRequestBody:
      type: object
      properties:
        employer_fein:
          $ref: "#/components/schemas/Fein"
          nullable: true

    EligibilityRequest:
      type: object
      properties:
        tax_identifier:
          nullable: false
          required: true
          $ref: "#/components/schemas/SsnItin"
        employer_fein:
          nullable: false
          $ref: "#/components/schemas/Fein"
        leave_start_date:
          nullable: false
          $ref: "#/components/schemas/Date"
        application_submitted_date:
          nullable: false
          $ref: "#/components/schemas/Date"
        employment_status:
          type: string
          enum:
            - Employed
            - Unemployed
            - Self-Employed
            - Unknown
            - Retired
          example: "Employed"
          nullable: false
        entitlement_period_start_date:
          nullable: false
          $ref: "#/components/schemas/Date"
        absence_case_number:
          nullable: false
          type: string
      required:
        - tax_identifier
        - employer_fein
        - leave_start_date
        - application_submitted_date
        - employment_status
        - entitlement_period_start_date
        - absence_case_number

    EligibilityResponse:
      type: object
      properties:
        financially_eligible:
          type: boolean
          example: false
        description:
          type: string
          example: "Claimant wages under minimum"
        total_wages:
          type: number
          example: 75000
          nullable: true
        state_average_weekly_wage:
          type: number
          example: 1431
          nullable: true
        unemployment_minimum:
          type: number
          example: 5100
          nullable: true
        employer_average_weekly_wage:
          type: number
          example: 1500
          nullable: true
      required:
        - financially_eligible
        - description

    ApplicationLeaveDetails:
      type: object
      description:
        Remove existing leave periods by including the key of the relevant type of leave period
        and an empty array or null value.
      properties:
        reason:
          type: string
          nullable: true
          enum:
            - Pregnancy/Maternity
            - Child Bonding
            - Serious Health Condition - Employee
            - Care for a Family Member
            - Military Caregiver
            - Military Exigency Family
          example: "Child Bonding"
        reason_qualifier:
          $ref: "#/components/schemas/ReasonQualifierOne"
          nullable: true
        reduced_schedule_leave_periods:
          type: array
          nullable: true
          items:
            $ref: "#/components/schemas/ReducedScheduleLeavePeriods"
        continuous_leave_periods:
          type: array
          nullable: true
          items:
            $ref: "#/components/schemas/ContinuousLeavePeriods"
        intermittent_leave_periods:
          type: array
          nullable: true
          items:
            $ref: "#/components/schemas/IntermittentLeavePeriods"
        caring_leave_metadata:
          nullable: true
          $ref: "#/components/schemas/CaringLeaveMetadata"
        pregnant_or_recent_birth:
          type: boolean
          nullable: true
          example: true
        child_birth_date:
          nullable: true
          $ref: "#/components/schemas/DateOrMaskedDate"
        child_placement_date:
          nullable: true
          $ref: "#/components/schemas/DateOrMaskedDate"
        has_future_child_date:
          type: boolean
          nullable: true
          example: true
        employer_notified:
          type: boolean
          nullable: true
          example: true
        employer_notification_date:
          nullable: true
          $ref: "#/components/schemas/Date"
        employer_notification_method:
          type: string
          nullable: true
          enum:
            - In Writing
            - In Person
            - By Telephone
            - Other
          example: "In Writing"

    ReasonQualifierOne:
      type: string
      example: "Newborn"
      enum:
        - Not Work Related
        - Work Related
        - Blood
        - Blood Stem Cell
        - Bone Marrow
        - Organ
        - Other
        - Postnatal Disability
        - Prenatal Care
        - Prenatal Disability
        - Adoption
        - Foster Care
        - Newborn
        - Pregnancy Related
        - Right to Leave
        - Serious Health Condition
        - Sickness - Non-Serious Health Condition
        - Childcare
        - Counseling
        - Financial & Legal Arrangements
        - Military Events & Related Activities
        - Other Additional Activities
        - Post Deployment Activities - Including Bereavement
        - Rest & Recuperation
        - Short Notice Deployment
        - Closure of School/Childcare
        - Quarantine/Isolation - Not Sick
        - Birth Disability
        - Childcare and School Activities
        - Parental Care
        - Post Deployment Activities

    ReducedScheduleLeavePeriods:
      type: object
      properties:
        leave_period_id:
          type: string
          format: uuid
          nullable: true
        start_date:
          nullable: true
          $ref: "#/components/schemas/Date"
        end_date:
          nullable: true
          $ref: "#/components/schemas/Date"
        thursday_off_minutes:
          type: integer
          minimum: 0
          nullable: true
          example: 90
        friday_off_minutes:
          type: integer
          minimum: 0
          nullable: true
          example: 90
        saturday_off_minutes:
          type: integer
          minimum: 0
          nullable: true
          example: 90
        sunday_off_minutes:
          type: integer
          minimum: 0
          nullable: true
          example: 90
        monday_off_minutes:
          type: integer
          minimum: 0
          nullable: true
          example: 90
        tuesday_off_minutes:
          type: integer
          minimum: 0
          nullable: true
          example: 90
        wednesday_off_minutes:
          type: integer
          minimum: 0
          nullable: true
          example: 90

    ContinuousLeavePeriods:
      type: object
      properties:
        leave_period_id:
          type: string
          format: uuid
          nullable: true
        start_date:
          nullable: true
          $ref: "#/components/schemas/Date"
        end_date:
          nullable: true
          $ref: "#/components/schemas/Date"
        last_day_worked:
          nullable: true
          $ref: "#/components/schemas/Date"
        expected_return_to_work_date:
          nullable: true
          $ref: "#/components/schemas/Date"
        start_date_full_day:
          type: boolean
          nullable: true
          example: true
        start_date_off_hours:
          type: integer
          nullable: true
          example: 0
        start_date_off_minutes:
          type: integer
          nullable: true
          example: 0
        end_date_off_hours:
          type: integer
          nullable: true
          example: 0
        end_date_off_minutes:
          type: integer
          nullable: true
          example: 0
        end_date_full_day:
          type: boolean
          nullable: true
          example: true

    IntermittentLeaveEpisodeRequest:
      type: object
      properties:
        requested_date:
          description: Date of leave episode in YYYY-MM-DD
          $ref: "#/components/schemas/Date"
        episodic_period:
          description: Amount of time taken for the episode, units of time here are based on the episodic_period_basis
          type: integer
          example: 4
          minimum: 1
        episodic_period_basis:
          type: string
          description: The unit of time for the episodic_period.
          enum:
            - Days
            - Hours
            - Minutes
          example: "Days"
      required:
        - requested_date
        - episodic_period
        - episodic_period_basis

    IntermittentLeavePeriods:
      type: object
      properties:
        leave_period_id:
          type: string
          format: uuid
          nullable: true
        start_date:
          nullable: true
          $ref: "#/components/schemas/Date"
        end_date:
          nullable: true
          $ref: "#/components/schemas/Date"
        frequency:
          type: integer
          nullable: true
          example: 4
        frequency_interval:
          type: integer
          nullable: true
          example: 2
        frequency_interval_basis:
          type: string
          nullable: true
          enum:
            - Days
            - Weeks
            - Months
          example: "Weeks"
        duration:
          type: integer
          nullable: true
          example: 2
        duration_basis:
          type: string
          nullable: true
          enum:
            - Minutes
            - Hours
            - Days
          example: "Days"

    PaymentPreference:
      type: object
      properties:
        payment_method:
          type: string
          nullable: true
          enum:
            - Elec Funds Transfer
            - Check
            - Prepaid Card
          example: "Elec Funds Transfer"
        account_number:
          type: string
          nullable: true
          minLength: 6
          maxLength: 17
          example: "1234567"
        routing_number:
          nullable: true
          $ref: "#/components/schemas/RoutingNbr"
        bank_account_type:
          type: string
          nullable: true
          enum:
            - Checking
            - Savings
          example: "Checking"

    WorkPattern:
      type: object
      properties:
        work_pattern_type:
          type: string
          enum:
            - Fixed
            - Rotating
            - Variable
          nullable: true
        work_pattern_days:
          type: array
          maxItems: 7
          items:
            $ref: "#/components/schemas/WorkPatternDay"
          nullable: true

    WorkPatternDay:
      type: object
      properties:
        day_of_week:
          $ref: "#/components/schemas/DayOfWeek"
        week_number:
          type: integer
          minimum: 1
          maximum: 4
        minutes:
          type: integer
          minimum: 0
          nullable: true

    EmployerBenefit:
      type: object
      properties:
        employer_benefit_id:
          type: string
          format: uuid
          nullable: true
        benefit_start_date:
          $ref: "#/components/schemas/Date"
          nullable: true
        benefit_end_date:
          $ref: "#/components/schemas/Date"
          nullable: true
        benefit_amount_dollars:
          type: number
          nullable: true
          example: 400
        benefit_amount_frequency:
          type: string
          nullable: true
          enum:
            - Per Day
            - Per Week
            - Per Month
            - In Total
            - Unknown
          example: "Per Month"
        benefit_type:
          type: string
          nullable: true
          enum:
            - Accrued paid leave
            - Short-term disability insurance
            - Permanent disability insurance
            - Family or medical leave insurance
            - Unknown
            - I'm not sure
            - Paid time off
        is_full_salary_continuous:
          type: boolean
          nullable: true

    EmployerClaimReviewRequestEmployerBenefit:
      allOf:
        - $ref: "#/components/schemas/EmployerBenefit"
        - type: object
          properties:
            employer_changes:
              $ref: "#/components/schemas/EmployerChanges"

    OtherIncome:
      type: object
      properties:
        other_income_id:
          type: string
          format: uuid
          nullable: true
        income_start_date:
          $ref: "#/components/schemas/Date"
          nullable: true
        income_end_date:
          $ref: "#/components/schemas/Date"
          nullable: true
        income_amount_dollars:
          type: number
          nullable: true
          example: 700
        income_amount_frequency:
          type: string
          nullable: true
          enum:
            - Per Day
            - Per Week
            - Per Month
            - In Total
            - Unknown
          example: "Per Month"
        income_type:
          type: string
          nullable: true
          enum:
            - Workers Compensation
            - Unemployment Insurance
            - SSDI
            - Disability benefits under Gov't retirement plan
            - Jones Act benefits
            - Railroad Retirement benefits
            - Earnings from another employment/self-employment
            - Unknown
          example: "SSDI"

    ApplicationSearchResults:
      type: array
      items:
        $ref: "#/components/schemas/ApplicationResponse"

    RMVCheckRequest:
      allOf:
        - type: object
          additionalProperties: false
          properties:
            absence_case_id:
              title: Absence Case ID
              description: FINEOS ID for the case the check is being performed for.
              type: string
            date_of_birth:
              $ref: "#/components/schemas/Date"
            first_name:
              title: First Name
              description:
                This must match _exactly_ with what is on record with the RMV to
                be verified. This might be relevant because the RMV uses a
                restricted character set of `[a-zA-Z \-']` for names.
              type: string
              example: Jane
            last_name:
              title: Last Name
              description:
                This must match _exactly_ with what is on record with the RMV to
                be verified. This might be relevant because the RMV uses a
                restricted character set of `[a-zA-Z \-']` for names.
              type: string
              example: Doe
            mass_id_number:
              $ref: "#/components/schemas/MassId"
            residential_address_city:
              title: Residential Address - City
              type: string
              example: Boston
            residential_address_line_1:
              title: Residential Address - Line 1
              type: string
              example: 123 Main St.
            residential_address_line_2:
              title: Residential Address - Line 2
              type: string
              nullable: true
              example: Apt. 123
            residential_address_zip_code:
              title: Residential Address - Zip Code
              description:
                Accepts ZIP and ZIP+4 formats. Only first five characters are
                relevant for check.
              type: string
              pattern: ^\d{5}(-\d{4})?$
              example: "12345"
            ssn_last_4:
              title: SSN Last 4
              description: Last four characters of a Social Security Number.
              type: string
              pattern: ^\d{4}$
              example: "9999"
          required:
            - first_name
            - last_name
            - date_of_birth
            - ssn_last_4
            - mass_id_number
            - residential_address_line_1
            - residential_address_city
            - residential_address_zip_code
            - absence_case_id
        - type: object
          properties:
            mass_id_number:
              title: Massachusetts-issued ID number
              description: The ID number of a Mass ID or Massachusetts driver's license.

    RMVCheckResponse:
      type: object
      properties:
        verified:
          title: Verified
          description: Indicates if all data points were successfully verified.
          type: boolean
        description:
          title: Description
          description: Human-readable description of the verification result.
          type: string
      required:
        - verified
        - description

    NotificationRecipient:
      type: object
      properties:
        first_name:
          type: string
          example: "Jane"
          description: "Required if and only if recipient_type is Claimant"
        last_name:
          type: string
          example: "Doe"
          description: "Required if and only if recipient_type is Claimant"
        full_name:
          type: string
          example: "Jane Doe"
          description: "Required if and only if recipient_type is Leave Administrator"
        contact_id:
          type: string
          example: "11"
          description: "The contact ID of the recipient, only required if recipient type is a Leave Adminstrator"
        email_address:
          type: string
          format: email
          example: "<EMAIL>"
          nullable: false
          minLength: 1

    NotificationClaimant:
      type: object
      properties:
        first_name:
          type: string
          example: "John"
          nullable: false
          minLength: 1
        last_name:
          type: string
          example: "Smith"
          nullable: false
          minLength: 1
        date_of_birth:
          $ref: "#/components/schemas/Date"
          nullable: false
        customer_id:
          type: string
          example: "1234"
          nullable: false
        preferred_language:
          $ref: "#/components/schemas/NotificationLanguage"
          nullable: true

    NotificationRequest:
      type: object
      properties:
        absence_case_id:
          title: Absence Case ID
          description: FINEOS ID for the case the notification is related to.
          type: string
          example: NTN-111-ABS-01
          minLength: 1
        fein:
          type: string
          example: "41-9904348"
        organization_name:
          type: string
          example: "Wayne Enterprises"
        document_type:
          title: Document Type
          description: The type of document that is available to view.
          type: string
          example: Legal Notice
        trigger:
          title: Trigger
          description: The claim state that triggered the notification.
          type: string
          example: claim.approved
        source:
          title: Source
          description: The source of the claim application. Can be either Self-Service or Call Center.
          type: string
          example: Self-Service
          enum:
            - Self-Service
            - Call Center
        recipient_type:
          title: Recipient Type
          description: Who is receiving the notification/what type of person is in the recipients list. Can be either Claimant or Leave Administrator.
          type: string
          example: Leave Administrator
          enum:
            - Claimant
            - Leave Administrator
        recipients:
          type: array
          description: Who to send the notification to.
          items:
            $ref: "#/components/schemas/NotificationRecipient"
        claimant_info:
          $ref: "#/components/schemas/NotificationClaimant"

      required:
        - absence_case_id
        - trigger
        - source
        - recipient_type
        - recipients
        - claimant_info
        - fein
        - organization_name

    VerificationRequest:
      type: object
      properties:
        employer_id:
          type: string
          format: uuid
          example: 5f91c12b-4d49-4eb0-b5d9-7fa0ce13eb32
        verification_type:
          type: string
          enum:
            - PFML Withholding
            - MTC Number
          example: PFML Withholding
          nullable: true
          default: PFML Withholding
        withholding_amount:
          type: number
          example: 100
          nullable: true
          description: "Required if verification_type is PFML Withholding"
        withholding_quarter:
          type: string
          format: date
          example: "1970-06-01"
          nullable: true
          description: "Required if verification_type is PFML Withholding"
        mtc_number:
          type: string
          example: MTC*********0
          nullable: true
          description: "Required if verification_type is MTC Number"
      required:
        - employer_id

    OrganizationUnit:
      type: object
      properties:
        organization_unit_id:
          type: string
          format: uuid
          example: "2f0f58a0-fcad-465b-b474-ee6c961cd6e3"
        name:
          type: string
          example: "Sales"
      required:
        - organization_unit_id
        - name

    SsnItin:
      type: string
      example: "***********"
      pattern: '^[\d|\*]{3}-[\d|\*]{2}-\d{4}$'

    MaskedSsnItin:
      type: string
      example: "***-**-1234"
      pattern: '^\*{3}-\*{2}-\d{4}$'

    NonMaskedSsnItin:
      type: string
      example: "***********"
      pattern: '^\d{3}-\d{2}-\d{4}$'

    Fein:
      type: string
      example: "00-0000000"
      pattern: '^\d{2}-\d{7}$'

    MassId:
      type: string
      example: "S99988801"
      pattern: '^(\d{9}|S(\d{8}|A\d{7})|(\*{9}))$'

    RoutingNbr:
      type: string
      example: "*********"
      pattern: '^((0[0-9])|(1[0-2])|(2[1-9])|(3[0-2])|(6[1-9])|(7[0-2])|80)([0-9]{7})$|(\*{9})$'

    AnyValue:
      nullable: true
      description: Can be any value - string, number, boolean, array or object

    Date:
      type: string
      example: "1970-01-01"
      format: date

    MaskedDate:
      type: string
      example: "****-01-01"
      pattern: '\*\*\*\*-[0-9]{2}-[0-9]{2}'

    DateOrMaskedDate:
      type: string
      example: "1970-01-01"
      format: maskable_date

    DayOfWeek:
      type: string
      enum:
        - Sunday
        - Monday
        - Tuesday
        - Wednesday
        - Thursday
        - Friday
        - Saturday

    Phone:
      type: object
      properties:
        int_code:
          type: string
          example: "1"
          pattern: "^[0-9]{1,3}"
          nullable: true
        phone_number:
          type: string
          example: "************"
          pattern: '^([0-9]|\*){3}\-([0-9]|\*){3}\-[0-9]{4}$'
          nullable: true
        e164:
          type: string
          example: "+12247052345"
          pattern: '^\+?[1-9]\d{1,14}$'
          nullable: true
        phone_type:
          type: string
          nullable: true
          enum:
            - Cell
            - Fax
            - Phone
        extension:
          type: string
          example: "123"
          nullable: true
          pattern: "^[0-9]+$"

    MaskedPhoneNumber:
      type: string
      example: "***-***-1234"
      pattern: '^\*{3}\-\*{3}\-[0-9]{4}$'

    MaskedPhone:
      type: object
      properties:
        int_code:
          type: string
          example: "1"
          pattern: "^[0-9]{1,3}"
          nullable: true
        phone_number:
          $ref: "#/components/schemas/MaskedPhoneNumber"
          nullable: true
        phone_type:
          type: string
          enum:
            - Cell
            - Fax
            - Phone
          nullable: true

    CaringLeaveMetadata:
      type: object
      properties:
        family_member_first_name:
          type: string
          example: "Jane"
          nullable: true
        family_member_middle_name:
          type: string
          example: "Alice"
          nullable: true
        family_member_last_name:
          type: string
          example: "Doe"
          nullable: true
        family_member_date_of_birth:
          $ref: "#/components/schemas/DateOrMaskedDate"
          nullable: true
        relationship_to_caregiver:
          type: string
          nullable: true
          enum:
            - Parent
            - Child
            - Grandparent
            - Grandchild
            - Other Family Member
            - Service Member
            - Inlaw
            - Sibling - Brother/Sister
            - Other
            - Spouse

    BenefitYearsSearchTerms:
      type: object
      properties:
        employee_id:
          type: string
          example: "2a340cf8-6d2a-4f82-a075-73588d003f8f"
          format: uuid
        current:
          type: boolean
          example: false
        end_date_within:
          type: array
          items:
            type: string
            format: date
          minItems: 2
          maxItems: 2
          example: ["2020-12-12", "2021-01-10"]
        fineos_absence_id:
          type: string
          example: "NTN-00-ABS-00"

    FinancialEligibilityCalculationSearchTerms:
      type: object
      properties:
        fineos_absence_id:
          type: string
          example: "NTN-00-ABS-00"

    BenefitYearsSearchRequest:
      type: object
      properties:
        terms:
          $ref: "#/components/schemas/BenefitYearsSearchTerms"
          nullable: true
        order:
          $ref: "#/components/schemas/SearchRequestOrder"
        paging:
          $ref: "#/components/schemas/SearchRequestPaging"

    FinancialEligibilityCalculationSearchRequest:
      type: object
      properties:
        terms:
          $ref: "#/components/schemas/FinancialEligibilityCalculationSearchTerms"
          nullable: true
        order:
          $ref: "#/components/schemas/SearchRequestOrder"
        paging:
          $ref: "#/components/schemas/SearchRequestPaging"

    HoursWorkedPerWeek:
      type: object
      properties:
        hours_worked:
          type: number
        employer_changes:
          $ref: "#/components/schemas/EmployerChanges"
      required:
        - hours_worked
        - employer_changes

    ClaimSearchRequestTerms:
      type: object
      properties:
        employer_id:
          oneOf:
            - type: string
            - type: array
              items:
                type: string
                format: uuid
          description: |
            Employer IDs to query claims for.

            Can provide a single string, optionally as comma separated list of
            IDs, or an array strings. The latter is preferred.
        employee_id:
          oneOf:
            - type: string
            - type: array
              items:
                type: string
                format: uuid
          description: |
            Employee IDs to query claims for.

            Can provide a single string, optionally as comma separated list of
            IDs, or an array of strings. The latter is preferred.
        search:
          type: string
          description: |
            A search string used to filter the request that, when provided, is
            used in a fuzzy search against the claims' Absence Case IDs, in
            addition to the employees' first, middle, and last names.

            When the string is empty, it is ignored.
        is_reviewable:
          oneOf:
            - type: boolean
            - type: string
              enum: ["yes", "no"]
          description: |
            Whether or not to filter claims where a review is requested for a
            leave admin.
        request_decision:
          type: string
          enum: ["approved", "denied", "withdrawn", "pending", "cancelled"]
          description: |
            Request decision used in filtering, which is a status associated
            with an Absence Period.

    EmployeeSearchRequestTerms:
      type: object
      properties:
        first_name:
          type: string
          example: "Jane"
          description: |
            If `first_name` is present, `last_name` must be present as well.

            This will be a case-insensitive, fuzzy match against first names on
            record.
          nullable: true
          minLength: 1
        last_name:
          type: string
          example: "Doe"
          description: |
            If `last_name` is present, `first_name` must be present as well.

            This will be a case-insensitive, fuzzy match against last names on
            record.
          nullable: true
          minLength: 1
        email_address:
          type: string
          format: email
          example: "<EMAIL>"
          nullable: true
          minLength: 3
        phone_number:
          type: string
          example: "+12247052345"
          description: |
            A phone number in E.164 format.
          pattern: '^\+?[1-9]\d{1,14}$'
          nullable: true
        tax_identifier:
          $ref: "#/components/schemas/NonMaskedSsnItin"
          nullable: true
        fineos_customer_number:
          type: string
          example: "111111"
          nullable: true

    EmployeeSearchRequestOrder:
      type: object
      properties:
        by:
          type: string
          default: "created_at"
          enum:
            - created_at
            - first_name
            - last_name
            - email_address
        direction:
          type: string
          example: "ascending"
          default: "descending"
          enum:
            - ascending
            - descending

    SearchRequestOrder:
      type: object
      properties:
        by:
          type: string
          default: "created_at"
          enum: ["created_at"]
        direction:
          type: string
          example: "ascending"
          default: "descending"
          enum:
            - ascending
            - descending

    SearchRequestPaging:
      type: object
      properties:
        offset:
          type: integer
          example: 1
          default: 1
        size:
          type: integer
          default: 25
          example: 10
          maximum: 1000

    ChangeRequest:
      type: object
      properties:
        change_request_type:
          type: string
          enum:
            - Modification
            - Withdrawal
            - Medical To Bonding Transition
            - Cancellation
            - Extension
          nullable: true
        start_date:
          $ref: "#/components/schemas/Date"
          nullable: true
        end_date:
          $ref: "#/components/schemas/Date"
          nullable: true
        date_of_birth:
          $ref: "#/components/schemas/Date"
          nullable: true

    HasUsableDataForApplicationResponse:
      type: object
      properties:
        usable_fields:
          type: array
          items:
            type: string

    ChangeRequestResponse:
      type: object
      properties:
        change_request_id:
          type: string
        fineos_absence_id:
          type: string
        change_request_type:
          type: string
          enum:
            - Modification
            - Withdrawal
            - Medical To Bonding Transition
            - Cancellation
            - Extension
          nullable: true
        start_date:
          $ref: "#/components/schemas/Date"
          nullable: true
        end_date:
          $ref: "#/components/schemas/Date"
          nullable: true
        date_of_birth:
          $ref: "#/components/schemas/Date"
          nullable: true
        submitted_time:
          type: string
          format: date-time
          example: "2020-06-26T01:02:03.967736+00:00"
          nullable: true
        documents_submitted_at:
          type: string
          format: date-time
          example: "2020-06-26T01:02:03.967736+00:00"
          nullable: true

    AbsenceCaseStatus:
      description: Absence case status
      type: string
      enum:
        [
          "Adjudication",
          "Approved",
          "Closed",
          "Completed",
          "Declined",
          "In Review",
          "Intake In Progress",
        ]

    DocumentType:
      description: Document types
      type: string
      enum:
        - Approval Notice
        - Denial Notice
        - Passport
        - Driver's License Mass
        - Driver's License Other State
        - Identification Proof
        - Absence Certification
        - State managed Paid Leave Confirmation
        - Own serious health condition form
        - Pregnancy/Maternity form
        - Child bonding evidence form
        - Care for a family member form
        - Military exigency form
        - Pending Application Withdrawn
        - Appeal Acknowledgment
        - Appeal Form
        - Appeal Notice - Claim Decision Affirmed
        - Appeal Notice - Claim Decision Changed
        - Appeals Supporting Documentation
        - Hearing Scheduled Notice
        - Maximum Weekly Benefit Change Notice
        - Benefit Amount Change Notice
        - Leave Allotment Change Notice
        - Approved Time Cancelled
        - Change Notice
        - Change Request Approved
        - Change Request Denied
        - Received Correspondence
        - Leave Request Review
        - Request for more Information
        - Additional Evidence Information eForm
        - Other Case Documentation
        - Other Income - current version
        - Other Leaves - current version
        - Inbound Payment Preference Modification Form
        - Outbound Payment Preference Modification Form
        - Returned Payment
        - Employer Response Additional Documentation
        - Employer Reimbursement Approval Notice
        - Employer Reimbursement Denial Notice
        - " Employer Reimbursement Formstack"
        - Overpayment Payoff Notice
        - Covered Service Member Identification Proof
        - Family Member Active Duty Service Proof
        - Pre-Review Summary Report
        - Payment Received-Updated Overpayment Balance
        - Reimbursement Request Form
        - Payment Plan Agreement
        - 1099G Tax Form for Claimants
        - Direct Deposit/EFT form - GD
        - Bounced Payment
        - Overpayment Notice-Full Balance Demand
        - OP- Partial Recovery and Remaining Bal
        - OP- Full Balance Recovery
        - OP- Full Balance Recovery - Manual
        - Appeal Hearing Virtual Fillable
        - Appeal RFI
        - Appeal - Returned to Adjudication
        - Appeal Approved
        - Appeal Dismissed - Other
        - Appeal Dismissed - Exempt Employer
        - Modify Decision
        - Appeal Withdrawn
        - Intermittent Time Approved Notice
        - Denial Notice Explanation of Wages
        - Explanation of Wages
        - Post-Adjudication Report
        - Healthcare Provider Form
        - Approval of Application Change
        - Denial of Application Change
        - Approved Leave Dates Cancelled
        - Denial of Application
        - Intermittent Time Reported
        - Approval Notice Explanation of Wages
        - Overpayment Full Demand ER Benefits
        - Overpayment Full Demand Intermittent
        - Overpayment Full Demand Leave Change
        - Overpayment Full Demand Paid Time Off
        - Overpayment Full Demand UI
        - Overpayment Full Demand Workers Comp
        - Overpayment Full Recovery ER Benefits
        - Overpayment Full Recovery Intermittent
        - Overpayment Full Recovery Leave Change
        - Overpayment Full Recovery Paid Time Off
        - Overpayment Full Recovery UI
        - Overpayment Full Recovery Workers Comp
        - Overpayment Partial Demand ER Benefits
        - Overpayment Partial Demand Intermittent
        - Overpayment Partial Leave Change
        - Overpayment Partial Paid Time Off
        - Overpayment Partial Demand UI
        - Overpayment Partial Demand Workers Comp
        - Overpayment Payment Received New Balance
        - Overpayment Payoff
        - Dismissal for Failure to Attend Hearing
        - Notice of Default
        - W9 Tax Form
        - EFT Change Request
        - Notice of Child Support Withholding
        - Appeal Postponement Agency
        - Appeal Postponement Approved
        - Appeal Postponement Denied
        - Appeal Reinstatement Denied
        - Appeal Reinstatement Granted
        - Confirmation of Insurance Form
        - Self-Insurance Declaration Document
        - Self-Insurance Surety Bond
        - Self-Insurance Proof of Benefits
        - "Family and Medical Leave Act Form"
      example: "Passport"

    EmployerReview:
      description: Outstanding evidence for employer review
      type: object
      properties:
        is_reviewable:
          type: boolean
          example: false
        latest_follow_up_date:
          type: string
          format: date
          example: "1970-06-01"
          nullable: true
        earliest_follow_up_date:
          type: string
          format: date
          example: "1970-06-01"
          nullable: true

    EmployerChanges:
      description: Employer change type for claim review
      type: string
      enum:
        - Added
        - Amended
        - Removed
        - Unchanged
      example: "Added"

    Language:
      description: User language preference for application
      type: string
      nullable: true
      enum:
        - English
        - Chinese (simplified)
        - Haitian Creole
        - Portuguese
        - Spanish
        - Vietnamese
        - French
        - Language not listed
      example: "English"

    NotificationLanguage:
      description: User language preference for notifications
      type: string
      nullable: true
      enum:
        - English
        - Chinese
        - Haitian Creole
        - Portuguese
        - Spanish
        - Vietnamese
        - French
      example: "English"

    CheckAddressValidationOverrideResponse:
      type: object
      properties:
        address_line_one:
          title: Address Line One
          description: Address line one to use as the verified mailing address.
          type: string
        address_line_two:
          title: Address Line Two
          description: Address line two to use as the verified mailing address.
          type: string
        city:
          title: City
          description: City to use as the verified mailing address.
          type: string
        state:
          title: State
          description: State to use as the verified mailing address.
          type: string
        zip_code:
          title: Zip Code
          description: Zip code to use as the verified mailing address.
          type: string

    IntermittentLeaveEpisodesResponse:
      type: object
      properties:
        leave_episodes:
          type: array
          items:
            $ref: "#/components/schemas/IntermittentLeaveEpisodeResponse"

    IntermittentLeaveEpisodeResponse:
      type: object
      nullable: true
      properties:
        episode_id:
          type: string
          nullable: true
          description: the FINEOS unique ID given to the episode reported
        episode_leave_request_id:
          type: string
          nullable: true
          description: the FINEOS leave request ID of the episode reported
        date_of_leave:
          format: date
          type: string
          nullable: true
          example: "2021-01-01"
          description: the date the reported episode of leave was taken
        duration_in_minutes:
          type: integer
          nullable: true
          description: Actual time/duration taken by the employee, in minutes
        episode_type:
          description: The type of actual period
          type: string
          nullable: true
          example: Office Visit
        status:
          type: string
          nullable: true
          description: the FINEOS approval status for the leave episode, e.g. Pending, In Review, Denied, Approved, Certified
        date_reported:
          type: string
          nullable: true
          format: date-time
          description: the date the user submitted the episode of leave

    BenefitsMetricsResponse:
      type: object
      properties:
        max_benefit_amount:
          type: number
          example: 1170.64
        max_benefit_year:
          type: integer
          example: 2025
        new_max_benefit_amount:
          type: number
          example: 1149.9
        new_max_benefit_year:
          type: integer
          example: 2024

    InsuranceProviderResponse:
      type: object
      properties:
        insurance_provider_id:
          type: integer
          example: 1
          nullable: false
        insurance_provider_name:
          type: string
          nullable: false
        address:
          $ref: "#/components/schemas/Address"
          nullable: true
        phone:
          $ref: "#/components/schemas/Phone"
          nullable: true
        deactivated:
          type: boolean
          nullable: false

    InsuranceProvidersDetailsResponse:
      type: object
      properties:
        insurance_provider_id:
          type: integer
          example: 1
          nullable: false
        insurance_provider_name:
          type: string
          nullable: false
        address:
          $ref: "#/components/schemas/Address"
        phone:
          $ref: "#/components/schemas/Phone"
        insurance_plans:
          type: array
          items:
            $ref: "#/components/schemas/InsuranceProviderPlans"

    InsuranceProviderPlans:
      type: object
      properties:
        insurance_plan_id:
          type: integer
          nullable: false
        insurance_provider_id:
          type: integer
        form_name:
          type: string
        has_family_exemption:
          type: boolean
        has_medical_exemption:
          type: boolean
        deactivated:
          type: boolean
          nullable: false

    EmployerExemptionApplicationPurchasedPlanDetailsRequest:
      type: object
      properties:
        insurance_provider_id:
          type: integer
          nullable: true
        insurance_plan_id:
          type: integer
          nullable: true

    EmployerExemptionApplicationPurchasedPlanDetailsResponse:
      type: object
      properties:
        insurance_provider_id:
          type: integer
          nullable: true
        insurance_provider_name:
          type: string
          nullable: true
        insurance_plan_id:
          type: integer
          nullable: true
        form_name:
          type: string
          nullable: true

    EmployerExemptionApplicationSelfInsuredPlanDetailsQuestions:
      type: object
      properties:
        does_plan_cover_all_employees:
          type: boolean
          nullable: true
        does_plan_provide_enough_leave:
          type: boolean
          nullable: true
        does_plan_provide_enough_medical_leave:
          type: boolean
          nullable: true
        does_plan_provide_enough_caring_leave:
          type: boolean
          nullable: true
        does_plan_provide_enough_bonding_leave:
          type: boolean
          nullable: true
        does_plan_provide_enough_armed_forces_leave:
          type: boolean
          nullable: true
        does_plan_provide_enough_armed_forces_illness_leave:
          type: boolean
          nullable: true
        does_plan_pay_enough_benefits:
          type: boolean
          nullable: true
        does_employer_withhold_premiums:
          type: boolean
          nullable: true
        are_employer_withholdings_within_allowable_amount:
          type: boolean
          nullable: true
        does_plan_provide_pfml_job_protection:
          type: boolean
          nullable: true
        does_plan_provide_return_to_work_benefits:
          type: boolean
          nullable: true
        does_plan_cover_employee_contribution:
          type: boolean
          nullable: true
        does_plan_provide_intermittent_caring_leave:
          type: boolean
          nullable: true
        does_plan_provide_intermittent_bonding_leave:
          type: boolean
          nullable: true
        does_plan_provide_intermittent_armed_forces_leave:
          type: boolean
          nullable: true
        does_plan_provide_intermittent_medical_leave:
          type: boolean
          nullable: true
        does_plan_cover_former_employees:
          type: boolean
          nullable: true
        does_plan_favor_paid_leave_benefits:
          type: boolean
          nullable: true

    EmployerExemptionApplicationSelfInsuredPlanDetails:
      type: object
      properties:
        has_obtained_surety_bond:
          type: boolean
          nullable: true
        surety_company:
          type: string
          nullable: true
        surety_bond_amount:
          type: number
          example: 1234.56
          nullable: true
        questions:
          $ref: "#/components/schemas/EmployerExemptionApplicationSelfInsuredPlanDetailsQuestions"
          nullable: true

    EmployerExemptionApplicationCreateRequestBody:
      type: object
      properties:
        employer_id:
          type: string
          format: uuid
          nullable: false

    EmployerExemptionApplicationRequestBody:
      type: object
      properties:
        is_legally_acknowledged:
          type: boolean
          nullable: true
        contact_first_name:
          type: string
          nullable: true
          maxLength: 50
        contact_last_name:
          type: string
          nullable: true
          maxLength: 50
        contact_title:
          type: string
          nullable: true
          maxLength: 50
        contact_phone:
          $ref: "#/components/schemas/Phone"
          nullable: true
        contact_email_address:
          type: string
          format: email
          example: "<EMAIL>"
          nullable: true
        has_third_party_administrator:
          type: boolean
          nullable: true
        tpa_business_name:
          type: string
          nullable: true
          maxLength: 256
        tpa_contact_first_name:
          type: string
          nullable: true
          maxLength: 50
        tpa_contact_last_name:
          type: string
          nullable: true
          maxLength: 50
        tpa_contact_title:
          type: string
          nullable: true
          maxLength: 50
        tpa_contact_phone:
          $ref: "#/components/schemas/Phone"
          nullable: true
        tpa_contact_email_address:
          type: string
          format: email
          example: "<EMAIL>"
          nullable: true
        should_workforce_count_include_1099_misc:
          type: boolean
          nullable: true
        average_workforce_count:
          type: integer
          example: 1
          default: 0
          nullable: true
        has_family_exemption:
          type: boolean
          nullable: true
        has_medical_exemption:
          type: boolean
          nullable: true
        is_self_insured_plan:
          type: boolean
          nullable: true
        insurance_plan_effective_at:
          $ref: "#/components/schemas/Date"
          nullable: true
        insurance_plan_expires_at:
          $ref: "#/components/schemas/Date"
          nullable: true
        purchased_plan:
          $ref: "#/components/schemas/EmployerExemptionApplicationPurchasedPlanDetailsRequest"
          nullable: true
        self_insured:
          $ref: "#/components/schemas/EmployerExemptionApplicationSelfInsuredPlanDetails"
          nullable: true
        documents:
          # TODO - PFMLPB-20608: Exemptions BE: GET /employer-exemption-applications/{employer_exemption_application_id}/documents
          type: string
          default: ""
          nullable: true
        employer_exemption_application_status_id:
          # TODO - PFMLPB-20600: Remove status id from application request
          type: integer
          default: 1
          nullable: true
        employer_exemption_application_status:
          type: string
          enum:
            - Draft
            - Approved
            - Denied
            - In Review
          nullable: true

    EmployerExemptionApplicationResponseBody:
      type: object
      properties:
        employer_exemption_application_id:
          type: string
          format: uuid
          nullable: false
        employer_id:
          type: string
          format: uuid
          nullable: false
        created_by_user_id:
          type: string
          format: uuid
          nullable: false
        is_legally_acknowledged:
          type: boolean
          nullable: false
        contact_first_name:
          type: string
          nullable: true
        contact_last_name:
          type: string
          nullable: true
        contact_title:
          type: string
          nullable: true
        contact_phone:
          $ref: "#/components/schemas/Phone"
          nullable: true
        contact_email_address:
          type: string
          format: email
          example: "<EMAIL>"
          nullable: true
        should_workforce_count_include_1099_misc:
          type: boolean
          nullable: true
        average_workforce_count:
          type: integer
          example: 1
          default: 0
          nullable: true
        has_family_exemption:
          type: boolean
          nullable: true
        has_medical_exemption:
          type: boolean
          nullable: true
        is_self_insured_plan:
          type: boolean
          nullable: true
        insurance_plan_effective_at:
          $ref: "#/components/schemas/Date"
          nullable: true
        insurance_plan_expires_at:
          $ref: "#/components/schemas/Date"
          nullable: true
        purchased_plan:
          $ref: "#/components/schemas/EmployerExemptionApplicationPurchasedPlanDetailsResponse"
          nullable: true
        self_insured:
          $ref: "#/components/schemas/EmployerExemptionApplicationSelfInsuredPlanDetails"
          nullable: true
        documents:
          # TODO - PFMLPB-20608: Exemptions BE: GET /employer-exemption-applications/{employer_exemption_application_id}/documents
          type: string
          default: ""
          nullable: true
        employer_exemption_application_status_id:
          type: integer
          default: 1
        employer_exemption_application_status:
          type: string
          enum:
            - Draft
            - Approved
            - Denied
            - In Review
        is_application_status_auto_decided:
          type: boolean
          nullable: true
        submitted_at:
          type: string
          format: date-time
          example: "2020-06-26T01:02:03.967736+00:00"
          nullable: true
        submitted_by_user_id:
          type: string
          format: uuid
          nullable: true

    AllEmployerExemptionsApplicationResponse:
      type: array
      items:
        type: object
        properties:
          employer_id:
            type: string
            format: uuid
          employer_exemption_applications:
            type: array
            items:
              $ref: "#/components/schemas/EmployerExemptionApplicationResponseBody"

    IndustrySector:
      type: string
      description: Occupation data collection industry
      enum:
        - "Accommodation and Food Services"
        - "Administrative and Support and Waste Management Remediation Services"
        - "Agriculture, Forestry, Fishing, and Hunting"
        - "Arts, Entertainment, and Recreation"
        - "Construction"
        - "Educational Services"
        - "Finance and Insurance"
        - "Health Care and Social Assistance"
        - "Information"
        - "Management of Companies and Enterprises"
        - "Manufacturing"
        - "Other Services (except Public Administration)"
        - "Professional, Scientific, and Technical Services"
        - "Public Administration"
        - "Real Estate Rental and Leasing"
        - "Retail Trade"
        - "Transportation and Warehousing"
        - "Utilities"
        - "Wholesale Trade"
