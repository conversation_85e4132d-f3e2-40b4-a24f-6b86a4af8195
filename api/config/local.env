#
# Default configuration for local development.
#

# General configuration
ENVIRONMENT=local
LOGGING_LEVEL=massgov.pfml.fineos.fineos_client=DEBUG,massgov.pfml.servicenow.client=DEBUG,newrelic.api.transaction=DEBUG,newrelic.core.custom_event=DEBUG

# Database configuration
DB_HOST=localhost
# DB_PORT=5432
DB_NAME=pfml
DB_ADMIN_USERNAME=pfml
DB_ADMIN_PASSWORD=secret123
DB_USERNAME=pfml_api
DB_PASSWORD=secret123
DB_NESSUS_PASSWORD=nessussecret123

# General API server configuration
CORS_ORIGINS=http://localhost:3000,http://localhost:3001
PORTAL_BASE_URL=https://paidleave.mass.gov

#Local S3 configuration
S3_BUCKET="local_s3"
# include request/response body in API-server error logs
ENABLE_FULL_ERROR_LOGS=1

# by default point to the local set of keys, these are used to generate dev bearer tokens
DEV_OAUTH_KEY_FILE=file://./jwks.json

DASHBOARD_PASSWORD=secret123

# Enable this variable to configure calls to the RMV API
RMV_API_BEHAVIOR=fully_mocked
# Enable this variable to configure /rmv-check
RMV_CHECK_MOCK_SUCCESS=1

# Disable this variable and set the following three variables to use a real Service Now API instead of a mock:
ENABLE_MOCK_SERVICE_NOW_CLIENT=1
# SERVICE_NOW_BASE_URL=...
# SERVICE_NOW_USERNAME=...
# SERVICE_NOW_PASSWORD=...

# Misc. feature flags
ENABLE_APPLICATION_FRAUD_CHECK=0
ENABLE_DOCUMENT_MULTIPART_UPLOAD=0
CHANNEL_SWITCH_UNSUPPORTED_CLAIMS=1
ENABLE_VERIFICATION_LIMITS=0
CLAIM_STATUS_V2_EMPLOYER_REVIEW=1
ENABLE_EMAIL_CHANGE=1
ENABLE_RESPONSE_VALIDATION=1
ENABLE_2025_STATE_METRICS=1
# Limit ssn/fein attempts
LIMIT_SSN_FEIN_MAX_ATTEMPTS=5
ENABLE_OFFSET_GET_1099=1
UPLOAD_1099_DOC_BATCH_START=1
UPLOAD_1099_DOC_BATCH_END=10
EMPLOYER_DOR_ROW_POPULATION=1
# To enable/disable system messages translation
ENABLE_SYSTEM_MESSAGE_TRANSLATION=0

# DOR-FINEOS ETL
FOLDER_PATH=dor_mock
PROCESSED_FOLDER=dor/processed/
DOR_EMPLOYEE_INTEGRITY_FILE_PATH_PREFIX=local_s3/reports
FINEOS_FOLDER_PATH=local_s3/fineos_mock
fineos_aws_iam_role_arn=arn
fineos_aws_iam_role_external_id=1
fineos_is_running_v24=0
# Enable this variable after initial full load
# ELIGIBILITY_FEED_MODE=updates

# Disable AWS credentials by default.
# To enable connection to AWS, follow the instructions in docker-compose.override.yml.
# AWS_PROFILE=eolwd-pfml-nonprod-admins-498823821309

# Disable AWS SES functionality locally so we don't need to authenticate with AWS SES.
# To test AWS SES locally, set this to "false" and follow the instructions for
# configuring AWS credentials in docker-compose.override.yml.
DISABLE_SENDING_EMAILS=1
PFML_EMAIL_ADDRESS=<EMAIL>
BOUNCE_FORWARDING_EMAIL_ADDRESS=<EMAIL>
BOUNCE_FORWARDING_EMAIL_ADDRESS_ARN=arn:aws:ses:us-east-1:498823821309:identity/<EMAIL>

# FEATURES_FILE_PATH=file:///app/features.yaml
PDF_API_HOST=http://localhost:5000
GENERATE_1099_MAX_FILES=1000
OVERPAYMENT_REPAYMENT_FETCH_DAYS_PRIOR = 14
ENABLE_MOCK_EDM_REPAYMENT_RESPONSE = 0
UPLOAD_MAX_FILES_TO_FINEOS=10
TEST_FILE_GENERATION_1099=0
IRS_1099_CORRECTION_IND=0
IRS_1099_TAX_YEAR=2024
ENABLE_1099_PRINT_SHOP_COPYING=1
# PFML_1099_BATCH_ID_OF_DOCUMENTS_TO_COPY=b4136e46-8b71-41d8-9cef-556173a0887c
DELETE_FILES_FROM_PRINT_SHOP_PATH=1
# SWITCH TO QUERY OVERPAYMENTS TABLE FOR OVERPAYMENTS
USE_OVERPAYMENT_TABLE_FOR_MAX_WEEKLY_BENEFIT=0
TRUSTED_PROXIES=0
ENABLE_EMPLOYER_OVERPAYMENT=1
ENABLE_CHILD_SUPPORT_AUTOMATION=0
ENABLE_SERVICE_AGREEMENT_VERSIONS_FOR_EXISTING_AUTOMATIONS=1
FINEOS_SERVICE_AGREEMENT_IMPORT_DB_POPULATION=1
ENABLE_PREPAID_IMPACT_PAYMENTS=1
ENABLE_PROCESS_OVERPAYMENT_REFERRALS=1
ENABLE_PROCESS_MMARS_RESPONSE_FILE=1
ENABLE_PROCESS_OVERPAYMENT_COLLECTIONS=1
ENABLE_SYNC_PAYMENT_PREFERENCE=1

# CHILD SUPPORT ENFORCEMENT / REDUCTIONS
CHILD_SUPPORT_FINEOS_ATTEMPT_CUTOFF_DAYS=15

# FINEOS API
# To enable API calls (instead of the mock), uncomment the line below and fill in the client secret
# FINEOS_CLIENT_CUSTOMER_API_URL=https://idt2-api.masspfml.fineos.com/customerapi/
FINEOS_CLIENT_GROUP_CLIENT_API_URL=https://idt2-api.masspfml.fineos.com/groupclientapi/
FINEOS_CLIENT_INTEGRATION_SERVICES_API_URL=https://idt2-api.masspfml.fineos.com/integration-services/
FINEOS_CLIENT_WSCOMPOSER_API_URL=https://idt2-api.masspfml.fineos.com/integration-services/wscomposer/
FINEOS_CLIENT_OAUTH2_URL=https://idt2-api.masspfml.fineos.com/oauth2/token
FINEOS_CLIENT_OAUTH2_CLIENT_ID=6qhtu25ofmscnnf2l38emr9fo4
FINEOS_CLIENT_OAUTH2_CLIENT_SECRET=replace_me_with_client_secret
FINEOS_CLIENT_SOAP_USER_ID=CONTENT
FINEOS_CLIENT_SOAP_PASSWORD=replace_me_with_soap_password

# FINEOS import (inbound)
FINEOS_DATA_EXPORT_PATH=local_s3/fin-data-export/LCL/dataexports

# FINEOS export (outbound)
FINEOS_DATA_IMPORT_PATH=local_s3/fin-data-import/LCL/peiupdate

# PUB payments
PFML_FINEOS_WRITEBACK_ARCHIVE_PATH=local_s3/agency-transfer/cps/pei-writeback
PFML_FINEOS_EXTRACT_ARCHIVE_PATH=local_s3/agency-transfer/cps/extracts
DFML_REPORT_OUTBOUND_PATH=local_s3/reports/dfml-reports
DFML_RESPONSE_INBOUND_PATH=local_s3/reports/dfml-responses
PUB_MOVEIT_INBOUND_PATH=local_s3/agency-transfer/pub/inbound
PUB_MOVEIT_OUTBOUND_PATH=local_s3/agency-transfer/pub/outbound
PFML_PUB_ACH_ARCHIVE_PATH=local_s3/agency-transfer/pub/ach
PFML_PUB_CHECK_ARCHIVE_PATH=local_s3/agency-transfer/pub/check
PFML_PUB_UNDELIVERABLE_CHECKS_ARCHIVE_PATH=local_s3/agency-transfer/pub/undeliverable-checks
PFML_MANUAL_PUB_REJECT_ARCHIVE_PATH=local_s3/agency-transfer/pub/manual-reject
PFML_ERROR_REPORTS_ARCHIVE_PATH=local_s3/agency-transfer/reports
PFML_PAYMENT_REJECTS_ARCHIVE_PATH=local_s3/agency-transfer/audit
PFML_1099_DOCUMENT_ARCHIVE_PATH=local_s3/1099
PFML_1099_DOCUMENT_PRINT_SHOP_PATH=local_s3/agency-transfer/printshop/1099
DFML_FINES_AND_REPAYMENTS_PATH=local_s3/agency-transfer/bofa/dfml-fines-and-repayment
PFML_MMARS_FILE_BASE_LOCATION= local_s3/agency-transfer/ctr/overpayments

# PAYMENTS_GAX_BIEVNT_EMAIL=
# PAYMENTS_DFML_BUSINESS_OPERATIONS_EMAIL=
AGENCY_REDUCTIONS_EMAIL_ADDRESS=<EMAIL>
DFML_PROJECT_MANAGER_EMAIL_ADDRESS=

# Uncomment and set these in order to run the FINEOS and CTR ECS tasks locally with real S3
# You'll also need to follow the instructions in docker-compose.override.yml to configure AWS credentials.
#
# FINEOS_DATA_EXPORT_PATH=s3://massgov-pfml-test-agency-transfer/chouinard-test/payments/fake-fineos-export/
# FINEOS_DATA_IMPORT_PATH=s3://massgov-pfml-test-agency-transfer/chouinard-test/payments/fake-fineos-import/
# PFML_CTR_INBOUND_PATH=s3://massgov-pfml-test-agency-transfer/chouinard-test/payments/ctr/inbound
# PFML_CTR_OUTBOUND_PATH=s3://massgov-pfml-test-agency-transfer/chouinard-test/payments/ctr/outbound
# PFML_FINEOS_INBOUND_PATH=s3://massgov-pfml-test-agency-transfer/chouinard-test/payments/cps/inbound
# PFML_FINEOS_OUTBOUND_PATH=s3://massgov-pfml-test-agency-transfer/chouinard-test/payments/cps/outbound
# PFML_ERROR_REPORTS_PATH=s3://massgov-pfml-test-agency-transfer/chouinard-test/payments/error-reports/outbound
# NEW_PLAN_PROOFS_ACTIVE_AT=2021-06-26 00:00:00+00:00

# Old payments
PFML_FINEOS_INBOUND_PATH=payments_files/cps/inbound/
PFML_FINEOS_OUTBOUND_PATH=payments_files/cps/outbound/
PFML_ERROR_REPORTS_PATH=payments_files/error-reports/outbound

# Earliest FINEOS extract files processed
FINEOS_CLAIMANT_EXTRACT_MAX_HISTORY_DATE=2021-06-12
FINEOS_PAYMENT_EXTRACT_MAX_HISTORY_DATE=2021-06-12
FINEOS_IAWW_EXTRACT_MAX_HISTORY_DATE=2021-11-15
FINEOS_1099_DATA_EXTRACT_MAX_HISTORY_DATE=2021-01-02
FINEOS_VBI_TASKREPORT_SOM_EXTRACT_MAX_HISTORY_DATE=2022-03-11
FINEOS_VBI_TASKREPORT_DELTA_SOM_EXTRACT_MAX_HISTORY_DATE=2024-07-16
FINEOS_VBI_DOCUMENT_SOM_EXTRACT_MAX_HISTORY_DATE=2022-03-01
FINEOS_OVERPAYMENT_EXTRACT_MAX_HISTORY_DATE=2022-06-01
FINEOS_VBI_ENTITLEMTPERIOD_SOM_EXTRACT_MAX_HISTORY_DATE=2022-10-28

PFML_BI_TOOL_EXTRACT_PATH=local_s3/business-intelligence-tool/fineos/dataexports

OVERPAYMENTS_BACKFILL_EXTRACT_MAX_HISTORY_DATE=2021-01-01
OVERPAYMENTS_BACKFILL_EXTRACT_MIN_HISTORY_DATE=2021-03-01

# Experian API
EXPERIAN_AUTH_TOKEN=replace_me_with_auth_token

# Audit / Rejects report
PAYMENT_AUDIT_REPORT_OUTBOUND_FOLDER_PATH=payments_files/audit/outbound/
PAYMENT_AUDIT_REPORT_SENT_FOLDER_PATH=payments_files/audit/sent/

# EDM API
# EDM_CLIENT_API_BASE_URL=https://coma-lwd-dev.privatelink.snowflakecomputing.com/api/v2/statements
EDM_CLIENT_OAUTH2_URL=https://coma-lwd-dev.privatelink.snowflakecomputing.com/oauth/token-request
EDM_CLIENT_OAUTH2_CLIENT_ID=1h421NhbL80gpQjsPgZcg7
EDM_CLIENT_OAUTH2_CLIENT_SECRET=replace_with_client_secret
EDM_CLIENT_CODE=replace_with_client_code
EDM_CLIENT_REFRESH_TOKEN=replace_with_refresh_token

# Azure SSO used for the Admin Portal
AZURE_AD_CLIENT_ID=ecc75e15-cd60-4e28-b62f-d1bf80e05d4d
AZURE_AD_TENANT_ID=3e861d16-48b7-4a0e-9806-8c04d81b7b2a
AZURE_AD_AUTHORITY_DOMAIN=login.microsoftonline.com
ADMIN_PORTAL_BASE_URL=http://localhost:3001
AZURE_AD_PARENT_GROUP=TSS-SG-PFML_ADMIN_PORTAL_NON_PROD
AZURE_AD_CLIENT_SECRET=replace_with_client_secret


# DUA data (non-reductions) processing
DUA_TRANSFER_BASE_PATH=local_s3/agency_transfer
MOVEIT_SFTP_URI=sftp://foo@localhost:2222
SFTP_URI=sftp://foo@localhost:2222
MOVEIT_DUA_INBOUND_PATH=upload/DFML/DUA/Inbound
MOVEIT_DUA_OUTBOUND_PATH=upload/DFML/DUA/Outbound
MOVEIT_DUA_ARCHIVE_PATH=upload/DFML/DUA/Archive
# contents of `local_sftp/ssh_api_rsa_key` collapsed into one line, since not
# all dotenv tools support direct multi-line strings/variables
MOVEIT_SSH_KEY="-----BEGIN OPENSSH PRIVATE KEY-----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\nRLqEcTCjXZypAAAADm1hc3MtcGZtbC1zZnRwAQIDBA==\n-----END OPENSSH PRIVATE KEY-----"
SFTP_SSH_KEY="-----BEGIN OPENSSH PRIVATE KEY-----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\nRLqEcTCjXZypAAAADm1hc3MtcGZtbC1zZnRwAQIDBA==\n-----END OPENSSH PRIVATE KEY-----"
SFTP_SSH_KEY_PASSWORD=foo

# New Relic Api
NR_API_HOST="https://api.newrelic.com/graphql"
NR_API_KEY=replace_with_api_key
NR_API_ACCOUNT_ID = 2837112
NR_DASHBOARD_ARCHIVE_PATH=local_s3/dashboards

SQLALCHEMY_WARN_20=1

ENABLE_FINEOS_API_LOGGING_TO_DB=1

#LMG Personal
LMG_PERSONAL_BASE_URL=https://personal.login.test.tss.mass.gov/0f521299-8041-4527-b1d0-5ebebacc91cc
LMG_PERSONAL_APPLICATION_ID=b1d56125-75b5-417a-b416-52b45b971825
LMG_PERSONAL_POLICY_SISU=B2C_1A_CITIZEN_SISU
LMG_PERSONAL_POLICY_CHANGE_EMAIL=B2C_1A_CITIZEN_CHANGEEMAIL
LMG_PERSONAL_POLICY_CHANGE_MFA=B2C_1A_CITIZEN_CHANGEMFA
LMG_PERSONAL_POLICY_CHANGE_NAME=B2C_1A_CITIZEN_CHANGENAME
LMG_PERSONAL_POLICY_CHANGE_PW=B2C_1A_CITIZEN_CHANGEPASSWORD
LMG_PERSONAL_OAUTH_CLIENT_SECRET=replace_with_client_secret
# https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/#Non-production

#LMG Business
LMG_BUSINESS_BASE_URL=https://admin.login.test.tss.mass.gov/ed96e6cd-0036-4043-bb11-3900ecd6c71c
LMG_BUSINESS_APPLICATION_ID=8f24d0e5-e309-4857-982c-fd7ddf1957b7
LMG_BUSINESS_POLICY_SISU=B2C_1A_PARTNER_SISU
LMG_BUSINESS_POLICY_CHANGE_EMAIL=B2C_1A_PARTNER_CHANGEEMAIL
LMG_BUSINESS_POLICY_CHANGE_MFA=B2C_1A_PARTNER_CHANGEMFA
LMG_BUSINESS_POLICY_CHANGE_NAME=B2C_1A_PARTNER_CHANGENAME
LMG_BUSINESS_POLICY_CHANGE_PW=B2C_1A_PARTNER_CHANGEPASSWORD
LMG_BUSINESS_OAUTH_CLIENT_SECRET=replace_with_client_secret
# https://lwd.atlassian.net/wiki/spaces/DD/pages/**********/#Non-production

MYMASSGOV_API_PERSONAL_APPLICATION_ID_URI=https://TestMassGovB2C.onmicrosoft.com/cc7f9e56-2b0e-4836-8cf5-1f7522620038
MYMASSGOV_API_BUSINESS_APPLICATION_ID_URI=https://PartnerTestMassGovB2C.onmicrosoft.com/bbce28f8-bd8e-4902-b22b-f324da2531bf
MY_MASS_GOV_API_CLIENT_BASE_URL=https://api.my.test-next.tss.mass.gov

ENABLE_SYNC_LEAVE_REQUESTS_STEP=1
ENABLE_SYNC_ABSENCE_PERIODS_STEP=1

# USBANK API
USBANK_CLIENT_OAUTH2_URL=https://stage-apip.prepaidgateway.com/oauth/oauth20/token
USBANK_CLIENT_OAUTH2_CLIENT_ID=ZIibVvtwkGo526nuHEGm6t5GPoLXNN2H
USBANK_CLIENT_OAUTH2_CLIENT_SECRET=replace_with_client_secret # SSM key usbank_oauth2_client_secret

CPS_ERROR_REPORTS_RECEIVED_S3_PATH=local_s3/cps-errors/received/
CPS_ERROR_REPORTS_PROCESSED_S3_PATH=local_s3/cps-errors/processed/