#!/usr/bin/env python3
#
# Create a leave request and associated objects for an approved Claim
#
# Run:
#   make create-leave-request-for-approved-claim \
#     --args="--fineos_absence_id='<fineos_absence_id>'"
#
# Arguments:
#   - (required) `fineos_absence_id`: You must pass in a FINEOS absence ID that exists in
#     your local database; otherwise, this script doesn't do anything helpful for you
#   - (optional) `fineos_leave_request_id`: Pass in the actual FINEOS leave request ID if
#     you want to mock exactly what happens in an AWS environment; otherwise, a random
#     ID will be generated by default
#   - (optional) `leave_request_absence_reason`: Pass in the leave request reason. By
#     default, this uses "Serious Health Condition - Employee"
#
# Run example:
#   make create-leave-request-for-approved-claim args="
#     --fineos_absence_id='NTN-544309-ABS-01' \
#     --fineos_leave_request_id='103684' \
#     --leave_request_absence_reason='Serious Health Condition - Employee'"
#
# Assumptions (aka you should only use this if ALL of the following are true):
# - You have created and submitted an application in the Portal
# - You are doing local development and cannot run the nightly batch job
#   that would normally handle this behavior
#
# This script does the following:
# - if a Benefit Year does not already exist for this Employee, creates a Benefit Year
#   that matches the approved Claim's start date
# - creates a Leave Request for the Claim
# - updates the Absence Period to link to the Leave Request
# - updates the Claim to set `claim.fineos_absence_status_id` to approved
#
# In an AWS environment (e.g. production, stage, etc), these changes would normally be
# taken care of by the nightly pub-payments-process-fineos background/batch job. For local
# development, this script updates the PFML API database as though the batch job has run.
#
# TODO: There is the possibility of drift between what pub-payments-process-fineos
# background/batch job does and what this script does. See PFMLPB-15647.
#
from decimal import Decimal
from random import randint, uniform

import click

import massgov.pfml.db
from massgov.pfml.api.eligibility.benefit_year_dates import calculate_benefit_year_dates
from massgov.pfml.db.lookup_data.absences import AbsenceReason, AbsenceStatus
from massgov.pfml.db.lookup_data.employees import EligibilityDecision, LeaveRequestDecision
from massgov.pfml.db.models.absences import AbsencePeriod
from massgov.pfml.db.models.employees import BenefitYear, Claim, LeaveRequest

db_session = massgov.pfml.db.init(sync_lookups=True)


@click.command()
@click.option(
    "--fineos_absence_id",
    prompt="The Claim's fineos_absence_id",
    help="FINEOS Absence Case ID for the Claim (e.g. NTN-12345-ABS-01)",
)
@click.option(
    "--fineos_leave_request_id",
    help="FINEOS Leave Request ID for the claim (e.g. 111111)",
    default=randint(100000, 999999),
)
@click.option(
    "--leave_request_absence_reason",
    help="The Absence Reason for the claim; must match the AbsenceReason LookupTable (e.g. 'Serious Health Condition - Employee')",
    default="Serious Health Condition - Employee",
)
def main(
    fineos_absence_id: str,
    fineos_leave_request_id: int,
    leave_request_absence_reason: str,
) -> None:
    click.secho(
        f"Arguments: fineos_absence_id={fineos_absence_id}, fineos_leave_request_id={fineos_leave_request_id}, leave_request_absence_reason={leave_request_absence_reason}",
        fg="cyan",
    )

    # Validate assumptions. If any assumptions do not pass, exit
    try:
        # Only proceed if the Absence Reason is valid
        absence_reason = AbsenceReason.get_id(leave_request_absence_reason)

        # Only proceed if exactly 1 Claim exists
        claim = db_session.query(Claim).filter(Claim.fineos_absence_id == fineos_absence_id).one()

        # Only proceed if exactly 1 Absence Period exists for that Claim.
        #
        # Note: this is a limitation of this script and means that you can’t yet use this
        # scripts in scenarios where a Claim (or extension or both) has more than one
        # Absence Period (e.g. a continuous Absence Period and a reduced Absence Period).
        #
        # TODO: this should probably be changed to get all Absence Periods associated with
        # this Claim that don't yet have `fineos_leave_request_id` and `leave_request_id`
        # set.
        absence_period = (
            db_session.query(AbsencePeriod)
            .filter(AbsencePeriod.claim_id == claim.claim_id)
            .order_by(AbsencePeriod.absence_period_end_date)
            .one()
        )

        # Only proceed if there are no existing Leave Requests
        existing_leave_requests = (
            db_session.query(LeaveRequest).filter(LeaveRequest.claim_id == claim.claim_id).all()
        )
        if len(existing_leave_requests) > 0:
            raise Exception("Has existing leave requests")

        # Only proceed if the Claim has a start date
        if claim.claim_start_date is None:
            raise Exception("Claim is missing a start date")

        # Only proceed if the Claim has an associated Employee
        if claim.employee_id is None:
            raise Exception("Claim is missing an employee")

    except Exception as e:
        click.secho(f"Error: {str(e)}", fg="red")
        return

    # If no Benefit Year exists for the Employee associated with the Claim, create one.
    existing_benefit_year = (
        db_session.query(BenefitYear)
        .filter(BenefitYear.employee_id == claim.employee_id)
        .one_or_none()
    )
    if existing_benefit_year is None:
        benefit_year_dates = calculate_benefit_year_dates(claim.claim_start_date)
        benefit_year = BenefitYear(
            **benefit_year_dates.dict(),
            employee_id=claim.employee_id,
            # Choose a floor that is anecdotally shown to have been approved
            total_wages=Decimal(round(uniform(30000, 50000), 2)),
        )
        db_session.add(benefit_year)
        click.secho(
            f"Creating Benefit Year starting on {claim.claim_start_date} for Employee {claim.employee_id}",
            fg="cyan",
        )

    # Create a Leave Request for the Claim
    # Get the fineos_leave_request_id and absence_reason from args
    # Everything else is hard-coded based on assumptions around successful adjudication of the claim
    leave_request = LeaveRequest(
        claim_id=claim.claim_id,
        fineos_leave_request_id=fineos_leave_request_id,
        absence_reason_id=absence_reason,
        leave_approval_decision_id=LeaveRequestDecision.APPROVED.leave_request_decision_id,
        eligibility_decision_id=EligibilityDecision.FINANCIALLY_ELIGIBLE.eligibility_decision_id,
        is_id_proofed=True,
    )
    db_session.add(leave_request)
    click.secho(
        f"Creating Leave Request for Claim {claim.claim_id}",
        fg="cyan",
    )

    # Update the Claim's fineos_absence_status_id
    claim.fineos_absence_status_id = AbsenceStatus.APPROVED.absence_status_id
    click.secho(
        f"Updating Claim {claim.claim_id}: setting fineos_absence_status_id to APPROVED",
        fg="cyan",
    )

    # Commit the changes to the database
    db_session.commit()

    # Update the Absence Periods with the associated Leave Requests
    absence_period.fineos_leave_request_id = fineos_leave_request_id
    absence_period.leave_request_id = leave_request.leave_request_id
    db_session.add(absence_period)
    click.secho(
        f"Updating Absence Period {absence_period.absence_period_id}: associating with Leave Request",
        fg="cyan",
    )
    # Commit again to the database
    db_session.commit()

    click.secho(
        "Done!",
        fg="green",
    )


if __name__ == "__main__":
    main()
