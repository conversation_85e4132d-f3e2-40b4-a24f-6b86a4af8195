#!/usr/bin/env python3
#
# Create a leave request and associated objects for an extension
#
# Run:
#   make create-leave-request-for-extension \
#     --args="--fineos_absence_id='<fineos_absence_id>'"
#
# Arguments:
#   - (required) `fineos_absence_id`: You must pass in a FINEOS absence ID that exists in
#     your local database; otherwise, this script doesn't do anything helpful for you
#   - (optional) `fineos_leave_request_id`: Pass in the actual FINEOS leave request ID if
#     you want to mock exactly what happens in an AWS environment; otherwise, a random
#     ID will be generated by default
#   - (optional) `leave_request_absence_reason`: Pass in the leave request reason. By
#     default, this uses "Serious Health Condition - Employee"
#
# Run example:
#   make create-leave-request-for-extension args="
#     --fineos_absence_id='NTN-544309-ABS-01' \
#     --fineos_leave_request_id='103684' \
#     --leave_request_absence_reason='Serious Health Condition - Employee'"
#
# Assumptions (aka you should only use this if ALL of the following are true):
# - You have created and submitted an application in the Portal
# - You have run `create-leave-request-for-approved-claim.py` already
# - (Optional) You have created a change request for the extension in the Portal
#   - If a change request exists, this process mirrors what happens if the Claimant makes
#     a change request in the Portal. If the change request does not exist, this process
#     mirrors what happens if the extension is created directly in in FINEOS by the
#     Contact Center
# - You have run the "Add Time" task for the extension in FINEOS
# - You are doing local development and cannot run the nightly batch job that would
#   normally handle this behavior
#
# This script does the following:
# - creates a Leave Request for the extension
# - if the Absence Period for the extension doesn't exist, creates one
# - updates the Absence Period to link to the Leave Request
# - updates the Claim to set `claim.fineos_absence_status_id` to adjudication
# - if there is no Change Request for the extension, the script will interactively prompt
#   for an end date for the extension
#
# In an AWS environment (e.g. production, stage, etc), these changes would normally be
# taken care of by the nightly pub-payments-process-fineos background/batch job. For local
# development, this script updates the PFML API database as though the batch job has run.
#
# TODO: There is the possibility of drift between what pub-payments-process-fineos
# background/batch job does and what this script does. See PFMLPB-15647.
#
# Note: This script also technically works for medical to bonding transitions, even though
# they aren't technically "extensions".
#
from datetime import timedelta
from random import randint

import click

import massgov.pfml.db
from massgov.pfml.db.lookup_data.absences import AbsencePeriodType, AbsenceReason, AbsenceStatus
from massgov.pfml.db.lookup_data.employees import EligibilityDecision, LeaveRequestDecision
from massgov.pfml.db.models.absences import AbsencePeriod
from massgov.pfml.db.models.change_request import ChangeRequest
from massgov.pfml.db.models.employees import Claim, LeaveRequest

db_session = massgov.pfml.db.init(sync_lookups=True)


@click.command()
@click.option(
    "--fineos_absence_id",
    prompt="The Claim's fineos_absence_id",
    help="FINEOS Absence Case ID for the Claim to add an extension to (e.g. NTN-12345-ABS-01)",
)
@click.option(
    "--fineos_leave_request_id",
    help="FINEOS Leave Request ID for the extension (e.g. 111111)",
    default=randint(100000, 999999),
)
@click.option(
    "--leave_request_absence_reason",
    help="The Absence Reason for the extension; must match the AbsenceReason LookupTable (e.g. 'Serious Health Condition - Employee')",
    default="Serious Health Condition - Employee",
)
def main(
    fineos_absence_id: str,
    fineos_leave_request_id: int,
    leave_request_absence_reason: str,
) -> None:
    click.secho(
        f"Arguments: fineos_absence_id={fineos_absence_id}, fineos_leave_request_id={fineos_leave_request_id}, leave_request_absence_reason={leave_request_absence_reason}",
        fg="cyan",
    )

    # Validate assumptions. If any assumptions do not pass, exit
    try:
        # Only proceed if the Absence Reason is valid
        absence_reason = AbsenceReason.get_id(leave_request_absence_reason)

        # Only proceed if exactly 1 Claim exists
        claim = db_session.query(Claim).filter(Claim.fineos_absence_id == fineos_absence_id).one()

        # Only proceed if the Claim has at least 1 Absence Period (aka the one associated
        # with the original Claim)
        absence_periods = (
            db_session.query(AbsencePeriod)
            .filter(AbsencePeriod.claim_id == claim.claim_id)
            .order_by(AbsencePeriod.absence_period_end_date)
            .all()
        )
        if len(absence_periods) == 0:
            raise Exception("Claim has no Absence Periods")

        # Only proceed if the Claim has a start date
        if claim.claim_start_date is None:
            raise Exception("Claim is missing a start date")

        # Only proceed if the Claim has an associated Employee
        if claim.employee_id is None:
            raise Exception("Claim is missing an employee")

    except Exception as e:
        click.secho(f"Error: {str(e)}", fg="red")
        return

    # Create a Leave Request for the Claim
    # Get the fineos_leave_request_id and absence_reason from args
    # Everything else is hard-coded based on assumptions around successful adjudication of the claim
    leave_request = LeaveRequest(
        claim_id=claim.claim_id,
        fineos_leave_request_id=fineos_leave_request_id,
        absence_reason_id=absence_reason,
        leave_approval_decision_id=LeaveRequestDecision.PENDING.leave_request_decision_id,
        eligibility_decision_id=EligibilityDecision.UNKNOWN.eligibility_decision_id,
        is_id_proofed=False,
    )
    db_session.add(leave_request)
    click.secho(
        f"Creating Leave Request for an extension on Claim {claim.claim_id}",
        fg="cyan",
    )

    # Update the Claim's fineos_absence_status_id
    # For an extension that has been opened, but not yet approved, this gets set to ADJUDICATION
    claim.fineos_absence_status_id = AbsenceStatus.ADJUDICATION.absence_status_id
    click.secho(
        f"Updating {claim.claim_id}: setting fineos_absence_status_id to ADJUDICATION",
        fg="cyan",
    )

    # Commit the changes to the database
    db_session.commit()

    # If multiple Absence Periods are associated with the Claim,
    # then we'll modify the one with the latest end date (aka the last one)
    if len(absence_periods) > 1:
        last_absence_period = absence_periods[-1]
        # Update the Absence Periods with the associated Leave Requests
        last_absence_period.fineos_leave_request_id = fineos_leave_request_id
        last_absence_period.leave_request_id = leave_request.leave_request_id
        db_session.add(last_absence_period)
        click.secho(
            f"Updating Absence Period {last_absence_period.absence_period_id}: associating with Leave Request",
            fg="cyan",
        )

    # If only 1 Absence Period is associated with the Claim,
    # then we'll create a new one for the extension
    else:
        # For the absence_period_start_date, we'll use the day after the previous absence
        # period's absence_period_end_date
        if absence_periods[-1].absence_period_end_date is None:
            click.secho("Error: the Absence Period for the Claim has no end date", fg="red")
            return
        else:
            start_date = absence_periods[-1].absence_period_end_date + timedelta(days=1)

        # For the absence_period_end_date:
        # - if there is an existing change request, use the change request end date
        # - if not, ask the operator
        existing_change_requests = (
            db_session.query(ChangeRequest).filter(ChangeRequest.claim_id == claim.claim_id).all()
        )
        if len(existing_change_requests) > 0:
            if existing_change_requests[-1].end_date is None:
                click.secho("Error: the Change Request for the extension has no end date", fg="red")
                return
            end_date = existing_change_requests[-1].end_date
        else:
            end_datetime = click.prompt(
                "Extension end date (YYYY-MM-DD)", type=click.DateTime(formats=["%Y-%m-%d"])
            )
            end_date = end_datetime.date()

        # Most of the values should match exactly what the nightly batch job generates, except:
        # - we generate random values for the FINEOS class and index IDs
        # - we assume the absence_period_type_id is fixed
        absence_period = AbsencePeriod(
            claim_id=claim.claim_id,
            fineos_leave_request_id=fineos_leave_request_id,
            absence_period_start_date=start_date,
            absence_period_end_date=end_date,
            leave_request_decision_id=LeaveRequestDecision.PENDING.leave_request_decision_id,
            fineos_absence_period_class_id=randint(1, 20000),
            fineos_absence_period_index_id=randint(1, 20000),
            absence_period_type_id=AbsencePeriodType.TIME_OFF_PERIOD.absence_period_type_id,
            absence_reason_id=absence_reason,
            modified_start_date=start_date,
            modified_end_date=end_date,
            leave_request_id=leave_request.leave_request_id,
        )
        db_session.add(absence_period)
        click.secho(
            f"Creating Absence Period for an extension on Claim {claim.claim_id}",
            fg="cyan",
        )

    # Commit again to the database
    db_session.commit()

    click.secho(
        "Done!",
        fg="green",
    )


if __name__ == "__main__":
    main()
