#!/usr/bin/env python3

import base64
import pprint
import urllib.parse

import click

import massgov.pfml.api.authentication as auth
import massgov.pfml.api.authentication.lmg as lmg
from massgov.pfml.api.models.oauth.responses import (
    LMGAuthCodeResponse,
    LMGAuthURIResponse,
    OAuthFlowState,
)

TENANT_VALUES = ["personal", "business"]


@click.command()
@click.option(
    "--tenant",
    "-t",
    help="Which MMG tenant to login to",
    required=True,
    type=click.Choice(TENANT_VALUES),
    default=TENANT_VALUES[0],
)
@click.option(
    "--scopes",
    "-s",
    help="Which OAuth scopes to request on token",
    multiple=True,
    default=[],
)
@click.option("-i", "--id-token", is_flag=True, help="Print the ID Token")
@click.option("-a", "--access-token", is_flag=True, help="Print the Access Token")
@click.option("-v", "--verbose", is_flag=True, help="Enables verbose mode")
@click.pass_context
def main(
    ctx: click.Context,
    tenant: str,
    scopes: tuple[str],
    id_token: bool,
    access_token: bool,
    verbose: bool = False,
) -> None:
    operation = "authenticate"
    language = "en"
    auth_code_scopes = ["openid"]
    token_scopes = auth_code_scopes + list(scopes)

    auth.configure_lmg()
    lmg_config = auth.lmg_configs.get_lmg_config(operation, tenant)  # type: ignore[union-attr]

    if verbose:
        click.echo("Using MMG SSO config:")
        click.echo(pprint.pformat(lmg_config.__dict__))
        click.echo()

    flow_state = OAuthFlowState(
        user_type=tenant,
    )

    auth_params = lmg.create_authorization_url(
        lmg_config=lmg_config,
        scopes=auth_code_scopes,
        flow_state=flow_state,
        lang=language,
    )

    click.secho(
        "You should probably start a new incognito/private browsing session for this flow, where you can interrupt the redirect.",
        bg="yellow",
    )
    click.echo()
    click.echo(f"Visit url: {auth_params.uri}")
    click.echo()

    auth_response_url = click.prompt("Copy-paste the oauth-return url")
    click.echo()
    auth_response = urllib.parse.parse_qs(urllib.parse.urlparse(auth_response_url).query)

    auth_response_clean = singularize_auth_response(auth_response)

    if verbose:
        click.echo()
        click.echo("Auth code response")
        click.echo(pprint.pformat(auth_response_clean))

        click.echo(pprint.pformat({k: d64(v) for k, v in auth_response_clean.items()}))
        click.echo()

    lmg_auth_code = LMGAuthCodeResponse.parse_obj(auth_response_clean)
    lmg_auth_flow_config = LMGAuthURIResponse(
        auth_uri=auth_params.uri,
        code_verifier=auth_params.code_verifier,
        nonce=None,
        redirect_uri=lmg_config.redirect_uri,
        token_endpoint=lmg_config.token_endpoint,
        scope=token_scopes,
        state=auth_params.state,
        operation=operation,
        user_type=tenant,
        flow_id="",
        end_session_url="",
    )

    token_response = lmg.fetch_token(lmg_config, lmg_auth_flow_config, lmg_auth_code, token_scopes)

    if verbose:
        click.echo(pprint.pformat(token_response.dict()))
        click.echo()

    if id_token:
        click.secho("ID token", bg="green")
        click.echo(token_response.id_token)
        click.echo()

    if access_token:
        click.secho("Access token", bg="green")
        click.echo(token_response.access_token)
        click.echo()

    if not id_token or not access_token:
        click.secho("Token", bg="green")
        if token_response.access_token:
            click.echo(token_response.access_token)
        else:
            click.echo(token_response.id_token)


def singularize_auth_response(d: dict) -> dict:
    return {k: v[0] if len(v) == 1 else v for k, v in d.items()}


def d64(s: str) -> bytes:
    return base64.urlsafe_b64decode(s + "===")


if __name__ == "__main__":
    main()
