#!/usr/bin/env python3
#
# Generates benefit years data for a set of employees
#
# Run via `make create-benefit-years`.
# View options: `make create-benefit-years args="--help"`
#
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List

import click

import massgov.pfml.db
from massgov.pfml.api.eligibility.benefit_year_dates import (
    BenefitYearDateRange,
    calculate_benefit_year_dates,
)
from massgov.pfml.db import Session
from massgov.pfml.db.models.employees import BenefitYear, Employee
from massgov.pfml.db.models.factories import (
    ApplicationFactory,
    BenefitYearFactory,
    EmployeeFactory,
    UserFactory,
)

db_session = massgov.pfml.db.init()
# Ensuring the db queries and the factories are using the same db session
massgov.pfml.db.models.factories.db_session = db_session


def create_benefit_year_for_employee(
    benefit_year_dates: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    employee: Employee,
    db_session: Session,
    previous: bool = False,
) -> None:
    # We do not want to generate new benefit years if this employee has
    # existing benefit years that overlap the dates of the benefit years that would be generated
    existing_benefit_years = (
        db_session.query(BenefitYear)
        .filter((BenefitYear.employee_id == employee.employee_id))
        .all()
    )
    for by in existing_benefit_years:
        by_delta = abs((by.start_date - benefit_year_dates.start_date).days)
        if by_delta < 364:
            click.secho(
                f"Employee {employee.employee_id} already has a benefit year that overlaps with the{' previous' if previous else ''} benefit year that would be generated. Will not generate benefit years.",
                fg="red",
            )
            quit()
    BenefitYearFactory.create(
        **benefit_year_dates.dict(), employee=employee, total_wages=Decimal(1000)
    )
    click.secho(
        f"Created{' previous' if previous else ''} benefit year for employee {employee.employee_id}",
        fg="cyan",
    )


def get_or_create_employees(
    employee_id: str, number_of_employees: int, db_session: Session
) -> List[Employee]:
    employees: List[Employee] = []
    if employee_id:
        try:
            employee = db_session.query(Employee).filter(Employee.employee_id == employee_id).one()
            click.secho(f"Found employee {employee.employee_id}", fg="green")
            employees.append(employee)
        except Exception as e:
            click.secho(
                f"Did not find employee {employee_id}. Will not generate benefit years.",
                fg="red",
            )
            click.secho(f"Error: {str(e)}", fg="red")
            quit()
    else:
        for _i in range(number_of_employees):
            employee = EmployeeFactory.create()
            employees.append(employee)
            click.secho(f"Created employee {employee.employee_id}", fg="cyan")
    return employees


def generate(
    db_session: Session,
    number_of_employees: int,
    claim_start_date: datetime,
    create_previous_benefit_year: bool,
    employee_id: str = "",
) -> None:

    if employee_id and number_of_employees > 1:
        click.secho(
            "When generating benefit years for existing employees, please run the script for one employee at a time. Will not generate benefit years.",
            fg="red",
        )
        quit()

    user = UserFactory.create(consented_to_data_sharing=True)
    benefit_year_dates = calculate_benefit_year_dates(claim_start_date)
    previous_benefit_year_dates = calculate_benefit_year_dates(
        benefit_year_dates.start_date - timedelta(weeks=52)
    )

    employees = get_or_create_employees(employee_id, number_of_employees, db_session)
    for employee in employees:
        # An application is required as this is the how we determine which employees
        # a particular user can see benefit years from
        # User -> Application -> TaxIdentifier -> Employee
        ApplicationFactory.create(user=user, tax_identifier=employee.tax_identifier)

        create_benefit_year_for_employee(benefit_year_dates, employee, db_session)

        if create_previous_benefit_year:
            create_benefit_year_for_employee(
                previous_benefit_year_dates, employee, db_session, True
            )

    click.secho(
        f"You can create a JWT for this user by running `make jwt auth_id={user.auth_id}`",
        fg="green",
    )


# In order for the test suite to work properly
# the function(s) being tested in the tests must NOT be decorated with these @click decorators.
# Therefore the business logic was pulled out into the above generate() function.
@click.command(
    help='Creates sample benefit year data for local API testing.\n\nAll arguments are optional.\n\nExample: make create-benefit-years args="--create_previous_benefit_year=false --claim_start_date="2026-06-21""'
)
@click.option(
    "--number_of_employees",
    default=1,
    help="The number of unique employees that should be generated and have benefit years generated for them",
)
@click.option(
    "--claim_start_date",
    type=click.DateTime(formats=["%Y-%m-%d"]),
    default=str(datetime.today().strftime("%Y-%m-%d")),
    help="The date that the claim would start.",
)
@click.option(
    "--create_previous_benefit_year",
    type=click.BOOL,
    default=True,
    help="Create a benefit year that predates claim_start_date",
)
@click.option(
    "--employee_id",
    type=click.UUID,
    help="The employee_id of one existing employee you'd like to create benefit years for",
)
def main(
    number_of_employees: int,
    claim_start_date: datetime,
    create_previous_benefit_year: bool,
    employee_id: str = "",
) -> None:
    return generate(
        db_session, number_of_employees, claim_start_date, create_previous_benefit_year, employee_id
    )


if __name__ == "__main__":
    main()
