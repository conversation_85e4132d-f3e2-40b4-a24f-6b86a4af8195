#
# Tests for massgov.pfml.fineos.fineos_client.
#

import datetime
import os
import pathlib
import re
import time
import unittest.mock
import xml.etree.ElementTree
from pathlib import Path

import defusedxml.ElementTree
import pytest
import requests

import massgov.pfml.fineos.models
import massgov.pfml.util.json as json
from massgov.pfml.api.models.documents.common import DocumentType
from massgov.pfml.db.models.fineos_api_log import FINEOSApiLog
from massgov.pfml.fineos.exception import (
    FINEOSClientError,
    FINEOSFatalResponseError,
    FINEOSServiceError,
)
from massgov.pfml.fineos.fineos_client import FINEOSClient
from massgov.pfml.fineos.mock_client import (
    mock_absence_period_response,
    mock_absence_reason_resource,
    mock_benefit_details,
    mock_disability_benefits,
    mock_document,
    mock_document_for_customer,
    mock_document_for_group_client,
    mock_document_metas_for_customer,
    mock_document_metas_for_group_client,
    mock_leave_availability_response,
    mock_leave_plans_response,
    mock_requested_absence_period_resource_response,
    mock_tax_withholding_leave_request,
    mock_tax_withholding_preference_read_response,
)
from massgov.pfml.fineos.models.customer_api import (
    AbsenceDetails,
    AbsencePeriodDecisions,
    AbsenceReasonResource,
    ActualAbsencePeriodResources,
    Base64EncodedFileData,
    BenefitSummary,
    ChangeRequestPeriod,
    CreateAbsencePeriodTypeRequest,
    CreateActualAbsencePeriodCommand,
    CreateActualAbsencePeriodCommandElements,
    CreateLeavePeriodsChangeRequestCommand,
    EpisodePeriodDurationBasisRequest,
    ReadDisabilityBenefitResult,
    ReasonRequest,
    RequestedAbsencePeriodResource,
)
from massgov.pfml.fineos.models.group_client_api import EForm, EFormAttribute, ModelEnum
from massgov.pfml.fineos.models.wscomposer import OCOrganisation
from tests.helpers.logging import assert_log_contains

TEST_FOLDER = pathlib.Path(__file__).parent
XML_ERROR_FILE_PATH = TEST_FOLDER / "expected_xml" / "xml_error.txt"
with open(XML_ERROR_FILE_PATH) as f:
    xml_error_text = f.read()

ws_update_response = """<?xml version='1.0' encoding='utf-8'?>
<p:WSUpdateResponse xmlns:p="http://www.fineos.com/wscomposer/UpdateOrCreateParty"
                    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <oid-list>
    <oid>PE:11528:0000124502</oid>
  </oid-list>
  <additional-data-set>
    <additional-data>
      <name>SERVICE_RESPONSE_CODE</name>
      <value>200</value>
    </additional-data>
    <additional-data>
      <name>CUSTOMER_NUMBER</name>
      <value>5157438</value>
    </additional-data>
  </additional-data-set>
</p:WSUpdateResponse>"""

ws_create_overpayment_recovery_response = """<?xml version='1.0' encoding='utf-8'?>
<p:WSUpdateResponse xmlns:p="http://www.fineos.com/wscomposer/COMAddOverpaymentActualRecovery" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <oid-list>
        <oid>PE:07329:0000000001</oid>
    </oid-list>
    <additional-data-set>
        <additional-data>
            <name>CheckName</name>
            <value>John Doe</value>
        </additional-data>
        <additional-data>
            <name>RecoveryMethod</name>
            <value>Check</value>
        </additional-data>
        <additional-data>
            <name>DateOfRecovery</name>
            <value>2024-10-11</value>
        </additional-data>
        <additional-data>
            <name>OverPaymentCaseNumber</name>
            <value>PL ABS-00001-PL ABS-01-OP00001</value>
        </additional-data>
        <additional-data>
            <name>AmountOfRecovery</name>
            <value>100</value>
        </additional-data>
        <additional-data>
            <name>CheckNumber</name>
            <value>00001</value>
        </additional-data>
        <additional-data>
            <name>ValidationMessages</name>
            <value></value>
        </additional-data>
    </additional-data-set>
</p:WSUpdateResponse>
"""

service_agreement_no_revisions_success_response = """<?xml version='1.0' encoding='utf-8'?>
<p:WSUpdateResponse xmlns:p="http://www.fineos.com/wscomposer/ServiceAgreementService" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <oid-list/>
    <additional-data-set>
        <additional-data>
            <name>CustomerNumber</name>
            <value>5229784</value>
        </additional-data>
        <additional-data>
            <name>ServiceAgreementCaseNumber</name>
            <value>SA-1234</value>
        </additional-data>
        <additional-data>
            <name>UnlinkAllExistingLeavePlans</name>
            <value>True</value>
        </additional-data>
        <additional-data>
            <name>ActivityMessages</name>
            <value>Created Service Agreement (SA). Case Number: SA-558162, Stage Name: Pending, Phase: Pending. Linking leave plan found using short name 'MA PFML - Family' to the service agreement. LeavePlan DisplayReference: F5627 LongName: MA PFML - Family. Service agreement details have been set. Attempting to progress case forward from stage 'Pending' to 'Review'. Case Number: SA-558162, Stage Name: Pending, Phase: Pending. After attempting to progress case foward from stage 'Pending' to 'Review'. Steps : [Pending, Review], Case Number: SA-558162, Stage Name: Review, Phase: Pending. Attempting to progress case forward from stage 'Review' to 'Active'. Case Number: SA-558162, Stage Name: Review, Phase: Pending. After attempting to progress case foward from stage 'Review' to 'Active'. Steps : [Review, Active], Case Number: SA-558162, Stage Name: Active, Phase: Decided. Service agreement has been moved to 'Active' stage. Created Basic Master Plan: 558163</value>
        </additional-data>
    </additional-data-set>
</p:WSUpdateResponse>"""

service_agreement_both_case_numbers_success_response = """<?xml version='1.0' encoding='utf-8'?>
<p:WSUpdateResponse xmlns:p="http://www.fineos.com/wscomposer/ServiceAgreementService" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <oid-list/>
    <additional-data-set>
        <additional-data>
            <name>CustomerNumber</name>
            <value>5229784</value>
        </additional-data>
        <additional-data>
            <name>ServiceAgreementCaseNumber</name>
            <value>SA-1234</value>
        </additional-data>
        <additional-data>
            <name>ServiceAgreementRevisionCaseNumber</name>
            <value>SA-1234-SAR-01</value>
        </additional-data>
        <additional-data>
            <name>UnlinkAllExistingLeavePlans</name>
            <value>True</value>
        </additional-data>
        <additional-data>
            <name>ActivityMessages</name>
            <value>Created Service Agreement (SA). Case Number: SA-558162, Stage Name: Pending, Phase: Pending. Linking leave plan found using short name 'MA PFML - Family' to the service agreement. LeavePlan DisplayReference: F5627 LongName: MA PFML - Family. Service agreement details have been set. Attempting to progress case forward from stage 'Pending' to 'Review'. Case Number: SA-558162, Stage Name: Pending, Phase: Pending. After attempting to progress case foward from stage 'Pending' to 'Review'. Steps : [Pending, Review], Case Number: SA-558162, Stage Name: Review, Phase: Pending. Attempting to progress case forward from stage 'Review' to 'Active'. Case Number: SA-558162, Stage Name: Review, Phase: Pending. After attempting to progress case foward from stage 'Review' to 'Active'. Steps : [Review, Active], Case Number: SA-558162, Stage Name: Active, Phase: Decided. Service agreement has been moved to 'Active' stage. Created Basic Master Plan: 558163</value>
        </additional-data>
    </additional-data-set>
</p:WSUpdateResponse>"""

service_agreement_success_response = """<?xml version='1.0' encoding='utf-8'?>
<p:WSUpdateResponse xmlns:p="http://www.fineos.com/wscomposer/ServiceAgreementService" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <oid-list/>
    <additional-data-set>
        <additional-data>
            <name>CustomerNumber</name>
            <value>5229784</value>
        </additional-data>
        <additional-data>
            <name>ServiceAgreementRevisionCaseNumber</name>
            <value>SA-1234-SAR-01</value>
        </additional-data>
        <additional-data>
            <name>UnlinkAllExistingLeavePlans</name>
            <value>True</value>
        </additional-data>
        <additional-data>
            <name>ActivityMessages</name>
            <value>Created Service Agreement (SA). Case Number: SA-558162, Stage Name: Pending, Phase: Pending. Linking leave plan found using short name 'MA PFML - Family' to the service agreement. LeavePlan DisplayReference: F5627 LongName: MA PFML - Family. Service agreement details have been set. Attempting to progress case forward from stage 'Pending' to 'Review'. Case Number: SA-558162, Stage Name: Pending, Phase: Pending. After attempting to progress case foward from stage 'Pending' to 'Review'. Steps : [Pending, Review], Case Number: SA-558162, Stage Name: Review, Phase: Pending. Attempting to progress case forward from stage 'Review' to 'Active'. Case Number: SA-558162, Stage Name: Review, Phase: Pending. After attempting to progress case foward from stage 'Review' to 'Active'. Steps : [Review, Active], Case Number: SA-558162, Stage Name: Active, Phase: Decided. Service agreement has been moved to 'Active' stage. Created Basic Master Plan: 558163</value>
        </additional-data>
    </additional-data-set>
</p:WSUpdateResponse>"""

service_agreement_missing_customer_number_response = """<?xml version='1.0' encoding='utf-8'?>
<p:WSUpdateResponse xmlns:p="http://www.fineos.com/wscomposer/ServiceAgreementService" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <oid-list/>
    <additional-data-set>
        <additional-data>
            <name>UnlinkAllExistingLeavePlans</name>
            <value>True</value>
        </additional-data>
    </additional-data-set>
</p:WSUpdateResponse>"""

service_agreement_missing_both_case_numbers_response = """<?xml version='1.0' encoding='utf-8'?>
<p:WSUpdateResponse xmlns:p="http://www.fineos.com/wscomposer/ServiceAgreementService" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <oid-list/>
    <additional-data-set>
        <additional-data>
            <name>CustomerNumber</name>
            <value>5229784</value>
        </additional-data>
        <additional-data>
            <name>UnlinkAllExistingLeavePlans</name>
            <value>True</value>
        </additional-data>
        <additional-data>
            <name>ActivityMessages</name>
            <value>Created Service Agreement (SA). Case Number: SA-558162, Stage Name: Pending, Phase: Pending. Linking leave plan found using short name 'MA PFML - Family' to the service agreement. LeavePlan DisplayReference: F5627 LongName: MA PFML - Family. Service agreement details have been set. Attempting to progress case forward from stage 'Pending' to 'Review'. Case Number: SA-558162, Stage Name: Pending, Phase: Pending. After attempting to progress case foward from stage 'Pending' to 'Review'. Steps : [Pending, Review], Case Number: SA-558162, Stage Name: Review, Phase: Pending. Attempting to progress case forward from stage 'Review' to 'Active'. Case Number: SA-558162, Stage Name: Review, Phase: Pending. After attempting to progress case foward from stage 'Review' to 'Active'. Steps : [Review, Active], Case Number: SA-558162, Stage Name: Active, Phase: Decided. Service agreement has been moved to 'Active' stage. Created Basic Master Plan: 558163</value>
        </additional-data>
    </additional-data-set>
</p:WSUpdateResponse>"""

service_agreement_service_error_response = """<?xml version='1.0' encoding='utf-8'?>
<p:WSUpdateResponse xmlns:p="http://www.fineos.com/wscomposer/ServiceAgreementService" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <oid-list/>
    <additional-data-set>
        <additional-data>
            <name>CustomerNumber</name>
            <value>5229784</value>
        </additional-data>
        <additional-data>
            <name>UnlinkAllExistingLeavePlans</name>
            <value>True</value>
        </additional-data>
        <additional-data>
            <name>ServiceErrors</name>
            <value>java.lang.NullPointerException</value>
        </additional-data>
    </additional-data-set>
</p:WSUpdateResponse>"""

tax_withholding_success_response = """<?xml version='1.0' encoding='utf-8'?>
<p:WSUpdateResponse xmlns:p="http://www.fineos.com/wscomposer/OptInSITFITService" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <oid-list/>
    <additional-data-set>
        <additional-data>
            <name>AbsenceCaseNumber</name>
            <value>NTN-250774-ABS-01</value>
        </additional-data>
        <additional-data>
            <name>FlagValue</name>
            <value>True</value>
        </additional-data>
    </additional-data-set>
</p:WSUpdateResponse>"""

tax_withholding_error_response = """<?xml version='1.0' encoding='utf-8'?>
<p:WSUpdateResponse xmlns:p="http://www.fineos.com/wscomposer/OptInSITFITService" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
    <oid-list/>
    <additional-data-set>
        <additional-data>
            <name>AbsenceCaseNumber</name>
            <value>NTN-250774-ABS-01</value>
        </additional-data>
        <additional-data>
            <name>FlagValue</name>
            <value>True</value>
        </additional-data>
        <additional-data>
            <name>ServiceErrors</name>
            <value>There is no case that corresponds to NTN-250774-ABS-01</value>
        </additional-data>
    </additional-data-set>
</p:WSUpdateResponse>"""

get_eform_response = json.dumps(
    {
        "eformType": "Other Income - current version",
        "eformId": 6245,
        "eformAttributes": [
            {
                "name": "V2OtherIncomeNonEmployerBenefitWRT1",
                "enumValue": {
                    "domainName": "WageReplacementType2",
                    "instanceValue": "Workers Compensation",
                },
            },
            {"name": "V2Spacer1", "stringValue": ""},
            {
                "name": "V2ReceiveWageReplacement7",
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "Yes"},
            },
            {"name": "Spacer11", "stringValue": ""},
            {
                "name": "V2ReceiveWageReplacement8",
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "No"},
            },
            {
                "name": "V2SalaryContinuation1",
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "No"},
            },
            {"name": "V2Spacer3", "stringValue": ""},
            {"name": "V2Spacer2", "stringValue": ""},
            {
                "name": "V2SalaryContinuation2",
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "Yes"},
            },
            {"name": "V2Spacer5", "stringValue": ""},
            {
                "name": "V2ReceiveWageReplacement3",
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "Please Select"},
            },
            {"name": "V2Spacer4", "stringValue": ""},
            {"name": "V2Spacer7", "stringValue": ""},
            {"name": "V2Spacer6", "stringValue": ""},
            {"name": "V2Header1", "stringValue": "Employer-Sponsored Benefits"},
            {"name": "V2Spacer9", "stringValue": ""},
            {"name": "V2Header2", "stringValue": "Income from Other Sources"},
            {"name": "V2Spacer8", "stringValue": ""},
            {
                "name": "V2ReceiveWageReplacement1",
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "Yes"},
            },
            {
                "name": "V2ReceiveWageReplacement2",
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "Yes"},
            },
            {
                "name": "V2Examples7",
                "stringValue": "Workers Compensation, Unemployment Insurance, Social Security Disability Insurance, Disability benefits under a governmental retirement plan such as STRS or PERS, Jones Act benefits, Railroad Retirement benefit, Earnings from another employer or through self-employment",
            },
            {"name": "V2OtherIncomeNonEmployerBenefitStartDate1", "dateValue": "2021-05-04"},
            {
                "name": "V2WRT1",
                "enumValue": {
                    "domainName": "WageReplacementType",
                    "instanceValue": "Permanent disability insurance",
                },
            },
            {
                "name": "V2WRT2",
                "enumValue": {
                    "domainName": "WageReplacementType",
                    "instanceValue": "Temporary disability insurance (Long- or Short-term)",
                },
            },
            {
                "name": "V2Frequency2",
                "enumValue": {
                    "domainName": "FrequencyEforms",
                    "instanceValue": "One time / Lump sum",
                },
            },
            {
                "name": "V2Frequency1",
                "enumValue": {"domainName": "FrequencyEforms", "instanceValue": "Per week"},
            },
            {"name": "V2OtherIncomeNonEmployerBenefitEndDate1", "dateValue": "2021-05-05"},
            {"name": "V2StartDate1", "dateValue": "2021-05-04"},
            {"name": "V2EndDate1", "dateValue": "2021-05-28"},
            {"name": "V2Amount1", "decimalValue": 40},
            {"name": "V2EndDate2", "dateValue": "2021-05-28"},
            {"name": "V2Amount2", "decimalValue": 150},
            {"name": "V2StartDate2", "dateValue": "2021-05-10"},
            {"name": "V2OtherIncomeNonEmployerBenefitAmount1", "decimalValue": 75},
            {"name": "V2Spacer10", "stringValue": ""},
            {
                "name": "V2OtherIncomeNonEmployerBenefitFrequency1",
                "enumValue": {"domainName": "FrequencyEforms", "instanceValue": "Per month"},
            },
        ],
    }
)


def xml_equal(actual_data, expected_name):
    actual_xml = xml.etree.ElementTree.canonicalize(xml_data=actual_data)
    expected_xml = xml.etree.ElementTree.canonicalize(
        from_file=TEST_FOLDER / "expected_xml" / expected_name
    )
    assert actual_xml == expected_xml
    return True


class MockOAuth2Session(requests.Session):
    """A session that mocks out OAuth2Session methods but is otherwise a real `requests.Session`.

    This helps tests use as much of the real `requests` library code as possible, which makes it
    possible to test for bugs that only appear in when real HTTP requests are happening.
    """

    @staticmethod
    def fetch_token(token_url, client_id, client_secret, timeout):
        return {
            "access_token": "1234abcd",
            "token_type": "Bearer",
            "expires_in": 3600,
            "expires_at": time.time() + 3600,
        }


@pytest.fixture
def fineos_client(httpserver):
    """FINEOSClient configured to call pytest-httpserver and injected with a mock oauth_session."""
    client = FINEOSClient(
        customer_api_url=httpserver.url_for("/customerapi/"),
        group_client_api_url=httpserver.url_for("/groupclientapi/"),
        integration_services_api_url=httpserver.url_for("/integration-services/"),
        wscomposer_url=httpserver.url_for("/wscomposer/"),
        wscomposer_user_id="USER",
        oauth2_url=httpserver.url_for("/oauth2/token"),
        client_id="1234567890abcdefghij",
        client_secret="abcdefghijklmnopqrstuvwxyz",
        oauth_session=MockOAuth2Session(),
        soap_user_id="USER",
        soap_password="put_password_here",
    )
    return client


class TestGetAbsence:
    @pytest.fixture
    def absence(self):
        return """
        {
            "absenceId": "NTN-100-ABS-01",
            "creationDate": "2021-09-01T17:32:53Z",
            "lastUpdatedDate": "2021-09-02T20:09:49Z",
            "status": "Adjudication",
            "notifiedBy": "Jane Doe",
            "notificationDate": "2021-09-01",
            "absencePeriods": [
                {
                "id": "PL-14449-**********",
                "reason": "Serious Health Condition - Employee",
                "reasonQualifier1": "Not Work Related",
                "reasonQualifier2": "Sickness",
                "startDate": "2021-10-05",
                "endDate": "2021-10-06",
                "expectedReturnToWorkDate": "",
                "status": "Known",
                "requestStatus": "Pending",
                "absenceType": "Continuous"
                }
            ],
            "absenceDays": [
                {
                "date": "2021-10-05",
                "timeRequested": "8.00",
                "timeRequestedBasis": "Hours",
                "timeDeducted": "08:00",
                "timeDeductedBasis": "Hours",
                "decision": ""
                }
            ],
            "reportedTimeOff": [
                {
                "startDate": "2021-10-05",
                "endDate": "2021-10-06",
                "startDateFullDay": true,
                "startDateOffHours": 0,
                "startDateOffMinutes": 0,
                "endDateOffHours": 0,
                "endDateOffMinutes": 0,
                "endDateFullDay": true,
                "decision": "Pending"
                }
            ],
            "reportedReducedSchedule": [],
            "selectedLeavePlans": [
                {
                "longName": "MA PFML - Employee",
                "category": "Paid",
                "applicability": "Applicable",
                "eligibility": "Met",
                "decision": "Pending Certification"
                }
            ],
            "financialCaseIds": []
        }
            """

    def test_get_absence(self, httpserver, fineos_client, absence):
        httpserver.expect_request(
            "/customerapi/customer/absence/absences/NTN-100-ABS-01",
            method="GET",
            headers={"userid": "FINEOS_WEB_ID", "Content-Type": "application/json"},
        ).respond_with_data(absence, content_type="application/json")
        response = fineos_client.get_absence("FINEOS_WEB_ID", "NTN-100-ABS-01")

        assert type(response) == AbsenceDetails

    def test_get_absence_finV24(self, httpserver, fineos_client, absence):

        # use the same response fixture but change the requestStatus for the v24 variation
        absence_finv24 = absence.replace(
            '"requestStatus": "Pending"', '"requestStatus": "Fully Adjudicated - Approved"'
        )

        httpserver.expect_request(
            "/customerapi/customer/absence/absences/NTN-100-ABS-01",
            method="GET",
            headers={"userid": "FINEOS_WEB_ID", "Content-Type": "application/json"},
        ).respond_with_data(absence_finv24, content_type="application/json")

        response: massgov.pfml.fineos.models.customer_api.AbsenceDetails = (
            fineos_client.get_absence("FINEOS_WEB_ID", "NTN-100-ABS-01")
        )

        assert response.absencePeriods[0].requestStatus == "Approved"


class TestGetLeaveAvailability:
    @pytest.fixture
    def leave_availability(self):
        return json.dumps(mock_leave_availability_response())

    def test_get_leave_availability(self, httpserver, fineos_client, leave_availability):
        httpserver.expect_request(
            "/customerapi/customer/absence/absences/leave-plans/mock_leave_plan_id/leave-availability",
            method="GET",
            headers={"userid": "FINEOS_WEB_ID", "Content-Type": "application/json"},
        ).respond_with_data(leave_availability, content_type="application/json")

        response = fineos_client.get_leave_availability("FINEOS_WEB_ID", "mock_leave_plan_id")

        assert response.id == "mock_leave_plan_id"
        assert response.name == "MA PFML - Employee"
        assert response.approvedTime == 5
        assert response.pendingTime == 2
        assert response.timeBasis == "Hours"
        assert response.timeEntitlement == 20
        assert response.timeWithinPeriod == 52
        assert response.timeWithinPeriodBasis == "Weeks"
        assert response.availableBalance == 10
        assert response.projectedAvailableTime == 12
        assert response.availabilityPeriodStartDate == datetime.date(2024, 1, 1)
        assert response.availabilityPeriodEndDate == datetime.date(2024, 12, 31)
        assert response.notificationMessage == "Standard leave plan message"


class TestGetCustomerAbsencePeriodDecisions:
    @pytest.fixture
    def absence_period_decisions(self):
        return """
        {
            "startDate": "2022-06-01",
            "endDate": "2022-08-01",
            "absencePeriodDecisions": [
                {
                   "periodId": "PL-14449-0000307420",
                   "parentPeriodId": "",
                   "startDate": "2022-06-01",
                   "endDate": "2022-07-01",
                   "absencePeriodStatus": "Known",
                   "absencePeriodType": "Time off period",
                   "absenceCaseId": "NTN-234373-ABS-01",
                   "leavePlanId": "abdc368f-ace6-4d6a-b697-f1016fe8a314",
                   "leavePlanCategory": "Paid",
                   "leavePlanName": "MA PFML - Employee",
                   "leavePlanShortName": "MA PFML - Employee",
                   "timeBankMethod": "Length / Duration",
                   "availabilityPeriodMethod": "Rolling Forward - Sunday",
                   "timeWithinPeriod": 52,
                   "timeWithinPeriodBasis": "Weeks",
                   "fixedYearStartDay": 0,
                   "fixedYearStartMonth": "Please Select",
                   "timeEntitlement": 20,
                   "timeEntitlementBasis": "Weeks",
                   "reasonName": "Serious Health Condition - Employee",
                   "qualifier1": "Not Work Related",
                   "qualifier2": "Sickness",
                   "applicabilityStatus": "Applicable",
                   "eligibilityStatus": "Met",
                   "adjudicationStatus": "Accepted",
                   "evidenceStatus": "Satisfied",
                   "availabilityStatus": "Approved",
                   "leaveRequestId": "PL-14432-**********",
                   "decisionStatus": "Approved",
                   "approvalReason": "Fully Approved",
                   "denialReason": "Please Select",
                   "timeDecisionStatus": "Approved",
                   "timeDecisionReason": "Leave Request Approved",
                   "balanceDeduction": 4.43125,
                   "actualForRequestedEpisodic": false,
                   "timeRequested": "177:15",
                   "timeDeducted": "177:15",
                   "timeDeductedBasis": "Hours"
                }
            ]
        }
        """

    def test_get(self, httpserver, fineos_client, absence_period_decisions):
        httpserver.expect_request(
            "/customerapi/customer/absence/absences/NTN-100-ABS-01/absence-period-decisions",
            method="GET",
            headers={"userid": "FINEOS_WEB_ID", "Content-Type": "application/json"},
        ).respond_with_data(absence_period_decisions, content_type="application/json")
        response = fineos_client.get_customer_absence_period_decisions(
            "FINEOS_WEB_ID", "NTN-100-ABS-01"
        )

        assert type(response) == AbsencePeriodDecisions

    def test_get_finV24(self, httpserver, fineos_client, absence_period_decisions):

        # use the same response fixture but change the requestStatus for the v24 variation
        absence_period_decisions_finV24 = absence_period_decisions.replace(
            '"decisionStatus": "Approved"', '"decisionStatus": "Fully Adjudicated - Approved"'
        )

        httpserver.expect_request(
            "/customerapi/customer/absence/absences/NTN-100-ABS-01/absence-period-decisions",
            method="GET",
            headers={"userid": "FINEOS_WEB_ID", "Content-Type": "application/json"},
        ).respond_with_data(absence_period_decisions_finV24, content_type="application/json")
        response = fineos_client.get_customer_absence_period_decisions(
            "FINEOS_WEB_ID", "NTN-100-ABS-01"
        )

        assert response.absencePeriodDecisions[0].decisionStatus == "Approved"


class TestAddCustomerAbsencePeriods:
    @pytest.fixture
    def requested_absence_period(self):
        return {
            "actualDate": "2023-08-22",
            "additionalNotes": "api",
            "episodePeriodBasis": {"name": "Minutes"},
            "episodePeriodDuration": 205,
            "type": {"name": "Incapacity"},
        }

    def test_post(self, httpserver, fineos_client, requested_absence_period):
        absence_case = "NTN-527629-ABS-01"

        absence_period_response = mock_absence_period_response()
        httpserver.expect_request(
            f"/customerapi/customer/absence/absences/{absence_case}/actual-absence-periods/bulk-create",
        ).respond_with_data(
            json.dumps(absence_period_response),
            status=200,
            content_type="application/json",
        )

        element = CreateActualAbsencePeriodCommand(
            actualDate=requested_absence_period["actualDate"],
            episodePeriodBasis=EpisodePeriodDurationBasisRequest(
                name=requested_absence_period["episodePeriodBasis"]["name"]
            ),
            episodePeriodDuration=requested_absence_period["episodePeriodDuration"],
            type=CreateAbsencePeriodTypeRequest(name=requested_absence_period["type"]["name"]),
        )

        elements = CreateActualAbsencePeriodCommandElements(elements=[element])
        reported_absence_period = fineos_client.submit_intermittent_leave_episode(
            "FINEOS_WEB_ID", absence_case, elements
        )

        assert (
            reported_absence_period[0].actualDate
            == absence_period_response["elements"][0]["actualDate"]
        )


class TestGetActualAbsencePeriodResources:
    absence_id = "NTN-527629-ABS-01"

    def test_success(self, httpserver, fineos_client):
        mock_response = mock_absence_period_response()
        httpserver.expect_request(
            f"/customerapi/customer/absence/absences/{self.absence_id}/actual-absence-periods",
        ).respond_with_data(
            json.dumps(mock_response),
            status=200,
            content_type="application/json",
        )

        actual_absence_period_resources: ActualAbsencePeriodResources = (
            fineos_client.get_actual_absence_period_resources("FINEOS_WEB_ID", self.absence_id)
        )

        actual_absence_period = actual_absence_period_resources.elements[0]

        # assert that object is parsed correctly
        assert actual_absence_period.actualDate == datetime.date(2023, 8, 22)
        assert actual_absence_period.episodePeriodDuration == 205
        assert actual_absence_period.episodePeriodBasis.name == "Minutes"


def test_get_claim_benefits(
    httpserver,
    fineos_client,
):
    benefit_details_data = mock_benefit_details()
    httpserver.expect_request(
        "/customerapi/customer/claims/PL ABS-124790/benefits",
        method="GET",
        headers={"userid": "FINEOS_WEB_ID", "Content-Type": "application/json"},
    ).respond_with_data(
        json.dumps(benefit_details_data), status=200, content_type="application/json"
    )
    response = fineos_client.read_claim_benefit("FINEOS_WEB_ID", "PL ABS-124790")
    assert type(response[0]) == BenefitSummary


def test_get_disability_benefits(httpserver, fineos_client):
    disability_benefit_data = mock_disability_benefits()
    httpserver.expect_request(
        "/customerapi/customer/claims/PL ABS-124790/benefits/PL ABS-486934-PL ABS-01/readDisabilityBenefit",
        method="GET",
        headers={"userid": "FINEOS_WEB_ID", "Content-Type": "application/json"},
    ).respond_with_data(
        json.dumps(disability_benefit_data), status=200, content_type="application/json"
    )
    response = fineos_client.read_disability_benefit(
        "FINEOS_WEB_ID", "PL ABS-124790", "PL ABS-486934-PL ABS-01"
    )
    assert type(response) == ReadDisabilityBenefitResult


def test_constructor(httpserver, fineos_client):
    assert fineos_client.customer_api_url == httpserver.url_for("/customerapi/")
    assert fineos_client.group_client_api_url == httpserver.url_for("/groupclientapi/")
    assert fineos_client.integration_services_api_url == httpserver.url_for(
        "/integration-services/"
    )
    assert fineos_client.wscomposer_url == httpserver.url_for("/wscomposer/")
    assert fineos_client.oauth2_url == httpserver.url_for("/oauth2/token")
    assert fineos_client.oauth_session is not None


class TestCreateOrUpdateEmployer:

    def test_unicode_name(self, httpserver, fineos_client):
        httpserver.expect_ordered_request(
            "/wscomposer/webservice",
            query_string={"config": "UpdateOrCreateParty", "userid": "USER"},
            method="POST",
            headers={"Content-Type": "application/xml; charset=utf-8"},
        ).respond_with_data(ws_update_response.encode("utf-8"), content_type="application/xml")

        employer_request_body = massgov.pfml.fineos.models.CreateOrUpdateEmployer(
            fineos_customer_nbr="08eedafc-c591-4988-a099-b35b3e2b704f",
            employer_fein="100000050",
            employer_legal_name="Test “Two” Č Corp",
            employer_dba="“Two” Č",
        )
        fineos_customer_nbr, fineos_employer_id = fineos_client.create_or_update_employer(
            employer_request_body
        )

        assert len(httpserver.log) == 1
        tree = defusedxml.ElementTree.fromstring(httpserver.log[0][0].data)
        assert (
            tree.find(
                "./update-data/PartyIntegrationDTO/organisation/OCOrganisation/DoingBusinessAs"
            ).text
            == "“Two” Č"
        )
        assert (
            tree.find(
                "./update-data/PartyIntegrationDTO/organisation/OCOrganisation/LegalBusinessName"
            ).text
            == "Test “Two” Č Corp"
        )
        assert (
            tree.find("./update-data/PartyIntegrationDTO/organisation/OCOrganisation/Name").text
            == "Test “Two” Č Corp"
        )
        assert fineos_customer_nbr == "08eedafc-c591-4988-a099-b35b3e2b704f"
        assert fineos_employer_id == 5157438

    def test_success(self, httpserver, fineos_client):
        httpserver.expect_ordered_request(
            "/wscomposer/webservice",
            query_string={"config": "UpdateOrCreateParty", "userid": "USER"},
            method="POST",
            headers={"Content-Type": "application/xml; charset=utf-8"},
        ).respond_with_data(ws_update_response.encode("utf-8"), content_type="application/xml")

        employer_request_body = massgov.pfml.fineos.models.CreateOrUpdateEmployer(
            fineos_customer_nbr="08eedafc-c591-4988-a099-b35b3e2b704f",
            employer_fein="100000050",
            employer_legal_name="Test One Corp",
            employer_dba="Test One",
        )
        fineos_customer_nbr, fineos_employer_id = fineos_client.create_or_update_employer(
            employer_request_body
        )

        assert len(httpserver.log) == 1
        assert xml_equal(httpserver.log[0][0].data, "create_or_update_employer.request.xml")
        assert fineos_customer_nbr == "08eedafc-c591-4988-a099-b35b3e2b704f"
        assert fineos_employer_id == 5157438


def test_create_service_agreement_for_employer_with_both_service_agreement_numbers_success(
    caplog_info, httpserver, fineos_client: FINEOSClient
):
    httpserver.expect_ordered_request(
        "/wscomposer/webservice",
        query_string={"config": "ServiceAgreementService", "userid": "USER"},
        method="POST",
        headers={"Content-Type": "application/xml; charset=utf-8"},
    ).respond_with_data(
        service_agreement_both_case_numbers_success_response.encode("utf-8"),
        content_type="application/xml",
    )

    service_agreement_inputs = massgov.pfml.fineos.models.CreateOrUpdateServiceAgreement(
        unlink_leave_plans=True,
    )

    fineos_revision_case_number = fineos_client.create_service_agreement_for_employer(
        5229784, service_agreement_inputs
    )

    assert_log_contains(
        caplog_info,
        "Service agreement response info",
        {
            "fineos_employer_id": 5229784,
            "fineos_case_number_field_name": "ServiceAgreementRevisionCaseNumber",
            "fineos_case_number_field_value": "SA-1234-SAR-01",
        },
    )

    assert fineos_revision_case_number == "SA-1234-SAR-01"


def test_create_service_agreement_for_employer_without_revisions_success(
    caplog_info, httpserver, fineos_client: FINEOSClient
):
    httpserver.expect_ordered_request(
        "/wscomposer/webservice",
        query_string={"config": "ServiceAgreementService", "userid": "USER"},
        method="POST",
        headers={"Content-Type": "application/xml; charset=utf-8"},
    ).respond_with_data(
        service_agreement_no_revisions_success_response.encode("utf-8"),
        content_type="application/xml",
    )

    service_agreement_inputs = massgov.pfml.fineos.models.CreateOrUpdateServiceAgreement(
        unlink_leave_plans=True,
    )

    fineos_revision_case_number = fineos_client.create_service_agreement_for_employer(
        5229784, service_agreement_inputs
    )

    assert_log_contains(
        caplog_info,
        "Service agreement response info",
        {
            "fineos_employer_id": 5229784,
            "fineos_case_number_field_name": "ServiceAgreementCaseNumber",
            "fineos_case_number_field_value": "SA-1234",
        },
    )

    assert fineos_revision_case_number == "SA-1234"


def test_create_service_agreement_for_employer_success(
    caplog_info, httpserver, fineos_client: FINEOSClient
):
    httpserver.expect_ordered_request(
        "/wscomposer/webservice",
        query_string={"config": "ServiceAgreementService", "userid": "USER"},
        method="POST",
        headers={"Content-Type": "application/xml; charset=utf-8"},
    ).respond_with_data(
        service_agreement_success_response.encode("utf-8"), content_type="application/xml"
    )

    service_agreement_inputs = massgov.pfml.fineos.models.CreateOrUpdateServiceAgreement(
        unlink_leave_plans=True,
    )

    fineos_revision_case_number = fineos_client.create_service_agreement_for_employer(
        5229784, service_agreement_inputs
    )

    assert_log_contains(
        caplog_info,
        "Service agreement response info",
        {
            "fineos_employer_id": 5229784,
            "fineos_case_number_field_name": "ServiceAgreementRevisionCaseNumber",
            "fineos_case_number_field_value": "SA-1234-SAR-01",
        },
    )

    assert fineos_revision_case_number == "SA-1234-SAR-01"


def test_create_service_agreement_for_employer_no_case_number_in_response(
    httpserver, fineos_client: FINEOSClient
):
    httpserver.expect_ordered_request(
        "/wscomposer/webservice",
        query_string={"config": "ServiceAgreementService", "userid": "USER"},
        method="POST",
        headers={"Content-Type": "application/xml; charset=utf-8"},
    ).respond_with_data(
        service_agreement_missing_both_case_numbers_response.encode("utf-8"),
        content_type="application/xml",
    )

    service_agreement_inputs = massgov.pfml.fineos.models.CreateOrUpdateServiceAgreement(
        unlink_leave_plans=True,
    )

    with pytest.raises(FINEOSFatalResponseError):
        fineos_client.create_service_agreement_for_employer(5229784, service_agreement_inputs)


def test_create_service_agreement_for_employer_no_customer_number_in_response(
    httpserver, fineos_client: FINEOSClient
):
    httpserver.expect_ordered_request(
        "/wscomposer/webservice",
        query_string={"config": "ServiceAgreementService", "userid": "USER"},
        method="POST",
        headers={"Content-Type": "application/xml; charset=utf-8"},
    ).respond_with_data(
        service_agreement_missing_customer_number_response.encode("utf-8"),
        content_type="application/xml",
    )

    service_agreement_inputs = massgov.pfml.fineos.models.CreateOrUpdateServiceAgreement(
        unlink_leave_plans=True,
    )

    with pytest.raises(FINEOSFatalResponseError):
        fineos_client.create_service_agreement_for_employer(5229784, service_agreement_inputs)


def test_create_service_agreement_for_employer_service_error(
    httpserver, fineos_client: FINEOSClient
):
    httpserver.expect_ordered_request(
        "/wscomposer/webservice",
        query_string={"config": "ServiceAgreementService", "userid": "USER"},
        method="POST",
        headers={"Content-Type": "application/xml; charset=utf-8"},
    ).respond_with_data(
        service_agreement_service_error_response.encode("utf-8"),
        content_type="application/xml",
    )

    service_agreement_inputs = massgov.pfml.fineos.models.CreateOrUpdateServiceAgreement(
        unlink_leave_plans=True,
    )

    with pytest.raises(
        FINEOSServiceError,
        match=r"\(create_service_agreement_for_employer\) java\.lang\.NullPointerException",
    ):
        fineos_client.create_service_agreement_for_employer(5229784, service_agreement_inputs)


def test_get_eform(httpserver, fineos_client):

    httpserver.expect_ordered_request(
        "/groupclientapi/groupClient/cases/NTN-100-ABS-01/eforms/3333/readEform",
        method="GET",
        headers={"userid": "FINEOS_WEB_ID", "Content-Type": "application/json"},
    ).respond_with_data(get_eform_response, content_type="application/json")

    eform = fineos_client.get_eform("FINEOS_WEB_ID", "NTN-100-ABS-01", 3333)
    assert eform == EForm(
        eformType="Other Income - current version",
        eformId=6245,
        eformAttributes=[
            EFormAttribute(
                name="V2OtherIncomeNonEmployerBenefitWRT1",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue=None,
                enumValue=ModelEnum(
                    domainName="WageReplacementType2", instanceValue="Workers Compensation"
                ),
            ),
            EFormAttribute(
                name="V2Spacer1",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue="",
                enumValue=None,
            ),
            EFormAttribute(
                name="V2ReceiveWageReplacement7",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue=None,
                enumValue=ModelEnum(domainName="PleaseSelectYesNo", instanceValue="Yes"),
            ),
            EFormAttribute(
                name="Spacer11",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue="",
                enumValue=None,
            ),
            EFormAttribute(
                name="V2ReceiveWageReplacement8",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue=None,
                enumValue=ModelEnum(domainName="PleaseSelectYesNo", instanceValue="No"),
            ),
            EFormAttribute(
                name="V2SalaryContinuation1",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue=None,
                enumValue=ModelEnum(domainName="PleaseSelectYesNo", instanceValue="No"),
            ),
            EFormAttribute(
                name="V2Spacer3",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue="",
                enumValue=None,
            ),
            EFormAttribute(
                name="V2Spacer2",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue="",
                enumValue=None,
            ),
            EFormAttribute(
                name="V2SalaryContinuation2",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue=None,
                enumValue=ModelEnum(domainName="PleaseSelectYesNo", instanceValue="Yes"),
            ),
            EFormAttribute(
                name="V2Spacer5",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue="",
                enumValue=None,
            ),
            EFormAttribute(
                name="V2ReceiveWageReplacement3",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue=None,
                enumValue=ModelEnum(domainName="PleaseSelectYesNo", instanceValue="Please Select"),
            ),
            EFormAttribute(
                name="V2Spacer4",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue="",
                enumValue=None,
            ),
            EFormAttribute(
                name="V2Spacer7",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue="",
                enumValue=None,
            ),
            EFormAttribute(
                name="V2Spacer6",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue="",
                enumValue=None,
            ),
            EFormAttribute(
                name="V2Header1",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue="Employer-Sponsored Benefits",
                enumValue=None,
            ),
            EFormAttribute(
                name="V2Spacer9",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue="",
                enumValue=None,
            ),
            EFormAttribute(
                name="V2Header2",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue="Income from Other Sources",
                enumValue=None,
            ),
            EFormAttribute(
                name="V2Spacer8",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue="",
                enumValue=None,
            ),
            EFormAttribute(
                name="V2ReceiveWageReplacement1",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue=None,
                enumValue=ModelEnum(domainName="PleaseSelectYesNo", instanceValue="Yes"),
            ),
            EFormAttribute(
                name="V2ReceiveWageReplacement2",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue=None,
                enumValue=ModelEnum(domainName="PleaseSelectYesNo", instanceValue="Yes"),
            ),
            EFormAttribute(
                name="V2Examples7",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue="Workers Compensation, Unemployment Insurance, Social Security Disability Insurance, Disability benefits under a governmental retirement plan such as STRS or PERS, Jones Act benefits, Railroad Retirement benefit, Earnings from another employer or through self-employment",
                enumValue=None,
            ),
            EFormAttribute(
                name="V2OtherIncomeNonEmployerBenefitStartDate1",
                booleanValue=None,
                dateValue=datetime.date(2021, 5, 4),
                decimalValue=None,
                integerValue=None,
                stringValue=None,
                enumValue=None,
            ),
            EFormAttribute(
                name="V2WRT1",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue=None,
                enumValue=ModelEnum(
                    domainName="WageReplacementType", instanceValue="Permanent disability insurance"
                ),
            ),
            EFormAttribute(
                name="V2WRT2",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue=None,
                enumValue=ModelEnum(
                    domainName="WageReplacementType",
                    instanceValue="Temporary disability insurance (Long- or Short-term)",
                ),
            ),
            EFormAttribute(
                name="V2Frequency2",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue=None,
                enumValue=ModelEnum(
                    domainName="FrequencyEforms", instanceValue="One time / Lump sum"
                ),
            ),
            EFormAttribute(
                name="V2Frequency1",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue=None,
                enumValue=ModelEnum(domainName="FrequencyEforms", instanceValue="Per week"),
            ),
            EFormAttribute(
                name="V2OtherIncomeNonEmployerBenefitEndDate1",
                booleanValue=None,
                dateValue=datetime.date(2021, 5, 5),
                decimalValue=None,
                integerValue=None,
                stringValue=None,
                enumValue=None,
            ),
            EFormAttribute(
                name="V2StartDate1",
                booleanValue=None,
                dateValue=datetime.date(2021, 5, 4),
                decimalValue=None,
                integerValue=None,
                stringValue=None,
                enumValue=None,
            ),
            EFormAttribute(
                name="V2EndDate1",
                booleanValue=None,
                dateValue=datetime.date(2021, 5, 28),
                decimalValue=None,
                integerValue=None,
                stringValue=None,
                enumValue=None,
            ),
            EFormAttribute(
                name="V2Amount1",
                booleanValue=None,
                dateValue=None,
                decimalValue=40.0,
                integerValue=None,
                stringValue=None,
                enumValue=None,
            ),
            EFormAttribute(
                name="V2EndDate2",
                booleanValue=None,
                dateValue=datetime.date(2021, 5, 28),
                decimalValue=None,
                integerValue=None,
                stringValue=None,
                enumValue=None,
            ),
            EFormAttribute(
                name="V2Amount2",
                booleanValue=None,
                dateValue=None,
                decimalValue=150.0,
                integerValue=None,
                stringValue=None,
                enumValue=None,
            ),
            EFormAttribute(
                name="V2StartDate2",
                booleanValue=None,
                dateValue=datetime.date(2021, 5, 10),
                decimalValue=None,
                integerValue=None,
                stringValue=None,
                enumValue=None,
            ),
            EFormAttribute(
                name="V2OtherIncomeNonEmployerBenefitAmount1",
                booleanValue=None,
                dateValue=None,
                decimalValue=75.0,
                integerValue=None,
                stringValue=None,
                enumValue=None,
            ),
            EFormAttribute(
                name="V2Spacer10",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue="",
                enumValue=None,
            ),
            EFormAttribute(
                name="V2OtherIncomeNonEmployerBenefitFrequency1",
                booleanValue=None,
                dateValue=None,
                decimalValue=None,
                integerValue=None,
                stringValue=None,
                enumValue=ModelEnum(domainName="FrequencyEforms", instanceValue="Per month"),
            ),
        ],
    )


def test_get_group_client_absence_period_decisions_with_error(caplog, httpserver, fineos_client):

    httpserver.expect_ordered_request(
        "/groupclientapi/groupClient/absences/absence-period-decisions?absenceId=NTN-251-ABS-01",
        method="GET",
        headers={"userid": "FINEOS_WEB_ID", "Content-Type": "application/json"},
    ).respond_with_data('{"message": "Not found"', status=404, content_type="application/json")

    with pytest.raises(FINEOSClientError):
        fineos_client.get_group_client_absence_period_decisions("FINEOS_WEB_ID", "NTN-251-ABS-01")
    assert "FINEOS Client Exception: get_group_client_absence_period_decisions" in caplog.text


def test_get_fineos_error_method_name(httpserver, fineos_client):
    httpserver.expect_ordered_request(
        "/groupclientapi/customers/123456789/customer-info",
        method="GET",
        headers={"userid": "FINEOS_WEB_ID", "Content-Type": "application/json"},
    ).respond_with_data(
        '{"message": "read_customer_details_error"}', status=400, content_type="application/json"
    )
    # capture exception and double check message
    with pytest.raises(
        Exception, match=re.escape("(get_customer_info) FINEOSFatalResponseError: 500")
    ):
        fineos_client.get_customer_info("FINEOS_WEB_ID", "123456789")


def test_get_fineos_error_meta_data_with_non_json():
    response = requests.Response()
    response._content = "not_json".encode("utf-8")

    error_meta_data = massgov.pfml.fineos.fineos_client.get_errors_meta_data(response)
    assert error_meta_data is None


def test_get_fineos_error_meta_data_with_json_list():
    response = requests.Response()
    response._content = '[{"ha":"ha"}]'.encode("utf-8")

    error_meta_data = massgov.pfml.fineos.fineos_client.get_errors_meta_data(response)
    assert error_meta_data is None


def test_get_fineos_error_meta_data_with_string():
    response = requests.Response()
    response._content = '{"errors":[{"id": "123", "meta":{"X-Correlation-ID":"038a2865"}}]}'.encode(
        "utf-8"
    )

    error_meta_data = massgov.pfml.fineos.fineos_client.get_errors_meta_data(response)
    assert error_meta_data == [{"id": "123", "X-Correlation-ID": "038a2865"}]


def test_get_fineos_error_meta_data_with_int():
    response = requests.Response()
    content = '{"errors":[{"id": "456", "meta":{"X-Correlation-ID":"038a2865"}}, {"id": "789", "meta":{"X-Correlation-ID":"2c20edb52b7b"}}]}'.encode(
        "utf-8"
    )
    response._content = content

    error_meta_data = massgov.pfml.fineos.fineos_client.get_errors_meta_data(response)
    assert error_meta_data == [
        {"id": "456", "X-Correlation-ID": "038a2865"},
        {"id": "789", "X-Correlation-ID": "2c20edb52b7b"},
    ]


def test_customer_api_document_upload_multipart_success(httpserver, fineos_client):
    case_id = "NTN-464041-ABS-01"
    document_type = DocumentType.identification_proof
    description = "test document"
    filename = "test.pdf"
    content_type = "application/pdf"
    file_content = b""

    document_response = mock_document(case_id, document_type, filename, description)

    httpserver.expect_request(
        f"/customerapi/customer/cases/{case_id}/documents/upload/{document_type}", method="POST"
    ).respond_with_data(json.dumps(document_response), status=200, content_type="application/json")

    document = fineos_client.upload_document_multipart(
        "FINEOS_WEB_ID", case_id, document_type, file_content, filename, content_type, description
    )
    assert document.documentId == document_response["documentId"]
    assert document.caseId == document_response["caseId"]


def test_customer_api_document_upload_multipart_success_pregnancy(httpserver, fineos_client):
    case_id = "NTN-464041-ABS-01"
    document_type = DocumentType.pregnancy_maternity_form
    description = "test document"
    filename = "test.pdf"
    content_type = "application/pdf"
    file_content = b""

    document_response = mock_document(case_id, document_type, filename, description)

    httpserver.expect_request(
        f"/customerapi/customer/cases/{case_id}/documents/upload/{document_type}", method="POST"
    ).respond_with_data(json.dumps(document_response), status=200, content_type="application/json")

    document = fineos_client.upload_document_multipart(
        "FINEOS_WEB_ID", case_id, document_type, file_content, filename, content_type, description
    )

    assert document.documentId == document_response["documentId"]
    assert document.caseId == document_response["caseId"]


def test_send_tax_withholding_preference_success(httpserver, fineos_client: FINEOSClient):
    httpserver.expect_ordered_request(
        "/wscomposer/webservice",
        query_string={"config": "OptInSITFITService", "userid": "USER"},
        method="POST",
        headers={"Content-Type": "application/xml; charset=utf-8"},
    ).respond_with_data(
        tax_withholding_success_response.encode("utf-8"),
        content_type="application/xml",
    )

    fineos_client.send_tax_withholding_preference("NTN-250774-ABS-01", True)


def test_send_tax_withholding_preference_service_error(httpserver, fineos_client: FINEOSClient):
    httpserver.expect_ordered_request(
        "/wscomposer/webservice",
        query_string={"config": "OptInSITFITService", "userid": "USER"},
        method="POST",
        headers={"Content-Type": "application/xml; charset=utf-8"},
    ).respond_with_data(
        tax_withholding_error_response.encode("utf-8"),
        content_type="application/xml",
    )

    with pytest.raises(
        FINEOSServiceError,
        match=r"\(send_tax_withholding_preference\) There is no case that corresponds "
        "to NTN-250774-ABS-01",
    ):
        fineos_client.send_tax_withholding_preference("NTN-250774-ABS-01", True)


def test_soap_api_create_case_with_error(httpserver, fineos_client):
    case_id = "NTN-not-in-system-ABS-01"

    httpserver.expect_request(
        "/integration-services/services/CaseServices",
        method="POST",
        headers={"userid": "USER", "Content-Type": "text/xml", "SOAPAction": "urn:createCase"},
    ).respond_with_data(json.dumps(xml_error_text), status=500, content_type="application/json")
    # capture exception and check message
    with pytest.raises(
        Exception,
        match=re.escape(
            "(create_appeal_case) FINEOSFatalResponseError: 500: A case with Case Number NTN-not-in-system-ABS-01 was not found."
        ),
    ):
        fineos_client.create_appeal_case(case_id)


def test__parse_xml_response(fineos_client):
    # Contents below include a line break between "Line 1" and "Line 2" in the <soapenv>
    response_text = """--MIMEBoundary_c37989542b16afe03f204fa3cdb6e0f6e2e9b3a0bd8589cf
Content-Type: application/xop+xml; charset=utf-8; type="text/xml"
Content-Transfer-Encoding: binary
Content-ID: <<EMAIL>>

<?xml version='1.0' encoding='utf-8'?><soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/">Line 1
Line 2</soapenv:Envelope>--MIMEBoundary_c37989542b16afe03f204fa3cdb6e0f6e2e9b3a0bd8589cf--"""
    parsed_xml = fineos_client._parse_xml_response(response_text, "NTN-00-ABS-00")
    assert (
        parsed_xml.text
        == """Line 1
Line 2"""
    )


class TestGetLeavePlans:
    @pytest.fixture
    def leave_plans_request_response(self):
        return json.dumps(mock_leave_plans_response())

    def test_success(self, httpserver, fineos_client, leave_plans_request_response):
        httpserver.expect_request(
            "/customerapi/customer/absence/agreement/leavePlans",
            method="GET",
            headers={"userid": "FINEOS_WEB_ID", "Content-Type": "application/json"},
        ).respond_with_data(leave_plans_request_response, content_type="application/json")

        leave_plans = fineos_client.get_leave_plans("FINEOS_WEB_ID")
        assert len(leave_plans) == 3
        assert {leave_plan.id for leave_plan in leave_plans} == {
            "abdc368f-ace6-4d6a-b697-123456789031",
            "f25383ea-81ba-4d59-b5dd-12345678900e",
            "2994cbe7-3c1c-4a57-91af-123456789034",
        }
        assert {leave_plan.name for leave_plan in leave_plans} == {
            "MA PFML - Employee",
            "MA PFML - Family",
            "MA PFML - Military Caregiver",
        }


class TestCreateOrUpdateLeavePeriodChangeRequest:
    @pytest.fixture
    def change_request(self):
        return CreateLeavePeriodsChangeRequestCommand(
            reason=ReasonRequest(name="Employee Request"),
            changeRequestPeriods=[
                ChangeRequestPeriod(
                    endDate=datetime.date(2022, 2, 15), startDate=datetime.date(2022, 2, 14)
                )
            ],
            additionalNotes="Withdrawal",
        )

    @pytest.fixture
    def change_request_response(self):
        return {
            "additionalNotes": "Withdrawal",
            "changeRequestPeriods": [{"endDate": "2022-2-15", "startDate": "2022-2-14"}],
            "id": "string",
            "reason": {
                "_links": {"property1": "string", "property2": "string"},
                "domainId": 0,
                "domainName": "string",
                "fullId": 0,
                "instances": [{"fullId": "string", "name": "string"}],
                "name": "Employee Request",
            },
            "requestDate": "2022-2-14",
        }

    def test_success(self, httpserver, fineos_client, change_request, change_request_response):
        absence_id = "NTN-123456-ABS-01"
        httpserver.expect_request(
            f"/customerapi/customer/absence/absences/{absence_id}/leave-periods-change-requests",
            method="POST",
        ).respond_with_data(
            json.dumps(change_request_response), status=201, content_type="application/json"
        )

        response = fineos_client.create_or_update_leave_period_change_request(
            "web_id", absence_id, change_request
        )

        reason = response.reason
        assert reason.name == "Employee Request"

        assert response.additionalNotes == "Withdrawal"

        period = response.changeRequestPeriods[0]
        assert period.startDate == datetime.date(2022, 2, 14)
        assert period.endDate == datetime.date(2022, 2, 15)


class TestGetDocuments:
    class TestGetDocumentsForAbsenceId:
        def test_success(self, httpserver, fineos_client):
            absence_id = "NTN-123456-ABS-01"
            documents = [mock_document(absence_id)]
            httpserver.expect_request(
                f"/customerapi/customer/cases/{absence_id}/documents", method="GET"
            ).respond_with_data(json.dumps(documents), status=200, content_type="application/json")

            response = fineos_client.get_documents("FINEOS_WEB_ID", absence_id)

            assert len(response) == 1

        def test_403(self, httpserver, fineos_client):
            absence_id = "NTN-123456-ABS-01"
            httpserver.expect_request(
                f"/customerapi/customer/cases/{absence_id}/documents", method="GET"
            ).respond_with_data("", status=403, content_type="application/json")

            documents = fineos_client.get_documents("FINEOS_WEB_ID", absence_id)
            assert len(documents) == 0

    class TestGetDocumentsWithoutAbsenceId:
        def test_success(self, httpserver, fineos_client, fineos_pre_v24_feature_config):
            document_response = json.dumps({"elements": [mock_document_for_customer()]})
            httpserver.expect_request(
                "/customerapi/customer/documents", method="GET"
            ).respond_with_data(document_response, status=200, content_type="application/json")

            response = fineos_client.get_documents("FINEOS_WEB_ID")

            assert len(response) == 1

        def test_403(self, httpserver, fineos_client, fineos_pre_v24_feature_config):
            httpserver.expect_request(
                "/customerapi/customer/documents", method="GET"
            ).respond_with_data("", status=403, content_type="application/json")

            documents = fineos_client.get_documents("FINEOS_WEB_ID")
            assert len(documents) == 0

        def test_with_params(self, httpserver, fineos_client, fineos_pre_v24_feature_config):
            document_one = mock_document_for_customer()
            document_two = mock_document_for_customer()

            document_response = json.dumps({"elements": [document_one, document_two]})

            httpserver.expect_request(
                "/customerapi/customer/documents", method="GET"
            ).respond_with_data(document_response, status=200, content_type="application/json")

            params = {"name": 1, "sort": "asc"}
            documents = fineos_client.get_documents("FINEOS_WEB_ID", None, params)
            assert len(documents) == 2

    class TestGetDocumentMetasWithoutAbsenceId:
        def test_success(self, httpserver, fineos_client, fineos_v24_feature_config):
            document_response = json.dumps({"elements": [mock_document_for_customer()]})
            httpserver.expect_request(
                "/customerapi/customer/document-metas", method="GET"
            ).respond_with_data(document_response, status=200, content_type="application/json")

            response = fineos_client.get_documents("FINEOS_WEB_ID")
            assert len(response) == 1

        def test_403(self, httpserver, fineos_client, fineos_v24_feature_config):
            httpserver.expect_request(
                "/customerapi/customer/document-metas", method="GET"
            ).respond_with_data("", status=403, content_type="application/json")

            documents = fineos_client.get_documents("FINEOS_WEB_ID")
            assert len(documents) == 0

        def test_with_params(self, httpserver, fineos_client, fineos_v24_feature_config):
            document_one = mock_document_metas_for_customer()
            document_two = mock_document_metas_for_customer()

            document_response = json.dumps({"elements": [document_one, document_two]})

            httpserver.expect_request(
                "/customerapi/customer/document-metas", method="GET"
            ).respond_with_data(document_response, status=200, content_type="application/json")

            params = {"name": 1, "sort": "name"}
            documents = fineos_client.get_documents("FINEOS_WEB_ID", None, params)
            assert len(documents) == 2

        def test_documents_get_date_created_with_invalid_date(
            self, httpserver, fineos_client, fineos_v24_feature_config, caplog
        ):
            mocked_document = mock_document_metas_for_customer()
            mocked_document["receivedDate"] = ""
            mocked_document["creationDateTime"] = "2023-10-01T00:00:00.000"

            document_response = json.dumps({"elements": [mocked_document]})

            httpserver.expect_request(
                "/customerapi/customer/document-metas", method="GET"
            ).respond_with_data(document_response, status=200, content_type="application/json")

            with caplog.at_level("ERROR"):
                response = fineos_client.get_documents("FINEOS_WEB_ID")

            assert len(response) == 1
            assert any("Error while parsing receivedDate" in message for message in caplog.messages)

    class TestGroupClientGetDocumentWithoutAbsenceId:
        def test_success(self, httpserver, fineos_client, fineos_pre_v24_feature_config):
            """
            Fineos v24 feature flag is disable.
            The documents API endpoint should return expected response.
            """
            document_response = json.dumps(mock_document_for_group_client())

            httpserver.expect_ordered_request(
                "/groupclientapi/groupClient/cases/NTN-001-001/documents",
                method="GET",
                query_string={"_filter": "includeChildCases"},
            ).respond_with_data(document_response, status=200, content_type="application/json")

            response = fineos_client.group_client_get_documents("FINEOS_WEB_ID", "NTN-001-001")

            assert len(response) == 1

        def test_failure(self, httpserver, fineos_client, fineos_v24_feature_config):
            """
            Fineos v24 feature flag is enable.
            The documents API endpoint should fail as it cannot parse the response.
            """
            document_response = json.dumps(mock_document_for_group_client())

            httpserver.expect_ordered_request(
                "/groupclientapi/groupClient/cases/NTN-001-001/documents",
                method="GET",
                query_string={"_filter": "includeChildCases"},
            ).respond_with_data(document_response, status=200, content_type="application/json")

            with pytest.raises(FINEOSFatalResponseError) as exc_info:
                fineos_client.group_client_get_documents("FINEOS_WEB_ID", "NTN-001-001")

            assert "No handler found" in str(exc_info.value)

    class TestGroupClientGetDocumentMetasWithoutAbsenceId:
        def test_success(self, httpserver, fineos_client, fineos_v24_feature_config):
            document_response = json.dumps(mock_document_metas_for_group_client())

            httpserver.expect_ordered_request(
                "/groupclientapi/groupClient/cases/NTN-001-001/document-metas",
                method="GET",
                query_string={"_subcaseDocuments": "true"},
            ).respond_with_data(document_response, status=200, content_type="application/json")

            response = fineos_client.group_client_get_documents("FINEOS_WEB_ID", "NTN-001-001")

            assert len(response) == 1

        def test_with_multiple_documents(
            self, httpserver, fineos_client, fineos_v24_feature_config
        ):
            document = mock_document_metas_for_group_client()

            # Adding another document to the response
            document["elements"].append(document["elements"][0])

            document_response = json.dumps(document)

            httpserver.expect_ordered_request(
                "/groupclientapi/groupClient/cases/NTN-001-001/document-metas",
                method="GET",
                query_string={"_subcaseDocuments": "true"},
            ).respond_with_data(document_response, status=200, content_type="application/json")

            response = fineos_client.group_client_get_documents("FINEOS_WEB_ID", "NTN-001-001")

            assert len(response) == 2


class TestDownloadDocument:
    @pytest.fixture
    def document_data(self):
        return Base64EncodedFileData(
            fileName="test.pdf",
            fileExtension="pdf",
            base64EncodedFileContents="Zm9v",  # decodes to "foo"
            contentType="application/pdf",
            description=None,
            fileSizeInBytes=0,
            managedReqId=None,
        )

    def test_success_with_absence_id(self, httpserver, fineos_client, document_data):
        absence_id = "NTN-123456-ABS-01"
        fineos_document_id = "foo"
        httpserver.expect_request(
            f"/customerapi/customer/cases/{absence_id}/documents/{fineos_document_id}/base64Download",
            method="GET",
        ).respond_with_data(json.dumps(document_data), status=200, content_type="application/json")

        response = fineos_client.download_document("web_id", fineos_document_id, absence_id)

        assert response.fileName == "test.pdf"

    def test_success_without_absence_id(self, httpserver, fineos_client, document_data):
        fineos_document_id = "foo"
        httpserver.expect_request(
            f"/customerapi/customer/document/{fineos_document_id}/base64Download",
            method="GET",
        ).respond_with_data(json.dumps(document_data), status=200, content_type="application/json")

        response = fineos_client.download_document("web_id", fineos_document_id, None)

        assert response.fileName == "test.pdf"


class TestGetTaxWithholdingPreference:
    @pytest.fixture
    def mock_paid_leave_instruction_response(self):
        path = Path(__file__).parent
        with open(os.path.join(path, "assets", "COMReadPaidLeaveInstruction.Response.xml")) as f:
            return f.read()

    def test_parsing(self, httpserver, fineos_client, mock_paid_leave_instruction_response):
        # Needs to match the IDs from the XML file
        absence_id = "NTN-38781-ABS-01"

        httpserver.expect_request(
            "/wscomposer/COMReadPaidLeaveInstruction",
            query_string={
                "userid": "USER",
                "param_str_casenumber": absence_id,
            },
            method="GET",
            headers={"Content-Type": "application/xml; charset=utf-8"},
        ).respond_with_data(
            mock_paid_leave_instruction_response, status=201, content_type="application/xml"
        )

        preference = fineos_client.read_tax_withholding_preference(absence_id)
        assert preference is True

    def test_missing_case(self, httpserver, fineos_client, mock_paid_leave_instruction_response):
        # Should not match the NTN from the XML file
        absence_id = "NTN-123456-ABS-01"

        httpserver.expect_request(
            "/wscomposer/COMReadPaidLeaveInstruction",
            query_string={
                "userid": "USER",
                "param_str_casenumber": absence_id,
            },
            method="GET",
            headers={"Content-Type": "application/xml; charset=utf-8"},
        ).respond_with_data(
            mock_paid_leave_instruction_response, status=201, content_type="application/xml"
        )

        with pytest.raises(ValueError, match="Could not find case data"):
            fineos_client.read_tax_withholding_preference(absence_id)

    def test_missing_leave_requests(self, fineos_client):
        response = mock_tax_withholding_preference_read_response(leave_requests=[])
        case = fineos_client._find_matching_case(response, "NTN-38781-ABS-01")

        with pytest.raises(ValueError, match="Could not find leave request from FINEOS response"):
            fineos_client._find_tax_withholding_value(case)

    def test_multiple_leave_requests_all_true(self, fineos_client):
        response = mock_tax_withholding_preference_read_response(
            leave_requests=[
                mock_tax_withholding_leave_request(True),
                mock_tax_withholding_leave_request(True),
            ]
        )
        case = fineos_client._find_matching_case(response, "NTN-38781-ABS-01")
        value = fineos_client._find_tax_withholding_value(case)
        assert value is True

    def test_multiple_leave_requests_all_false(self, fineos_client, caplog):
        response = mock_tax_withholding_preference_read_response(
            leave_requests=[
                mock_tax_withholding_leave_request(False),
                mock_tax_withholding_leave_request(False),
            ]
        )
        case = fineos_client._find_matching_case(response, "NTN-38781-ABS-01")
        value = fineos_client._find_tax_withholding_value(case)
        assert value is False

    def test_multiple_leave_requests_different_values(self, fineos_client):
        response = mock_tax_withholding_preference_read_response(
            leave_requests=[
                mock_tax_withholding_leave_request(False),
                mock_tax_withholding_leave_request(True),
            ]
        )
        case = fineos_client._find_matching_case(response, "NTN-38781-ABS-01")

        with pytest.raises(
            ValueError, match="Multiple tax withholding values found for leave request"
        ):
            fineos_client._find_tax_withholding_value(case)


class TestLogRequest:
    def test_log_request_info(self, httpserver, fineos_client, caplog_info):
        request_method = "GET"
        response_code = 200
        absence_id = "NTN-123456-ABS-01"
        documents = [mock_document(absence_id)]
        expected_url = httpserver.url_for(
            "/customerapi/customer/cases/NTN-123456-ABS-01/documents?includeChildCases=True"
        )

        httpserver.expect_request(
            f"/customerapi/customer/cases/{absence_id}/documents", method=request_method
        ).respond_with_data(
            json.dumps(documents), status=response_code, content_type="application/json"
        )

        fineos_client.get_documents("FINEOS_WEB_ID", absence_id)

        actual_response_time = caplog_info.records[-1].FINEOSResponseTime

        expected_log_message = "FINEOS request complete"
        expected_log_data = {
            "FINEOSMethod": request_method,
            "FINEOSResponseCode": response_code,
            "FINEOSUrl": expected_url,
            "FINEOSResponseTime": actual_response_time,
            "fineos.request.method": request_method,
            "fineos.request.url": expected_url,
            "fineos.request.url_rule": "http://localhost:%/customerapi/customer/cases/NTN-%-ABS-%/documents?includeChildCases=True",
            "fineos.response.status_code": response_code,
            "fineos.response.response_time_ms": actual_response_time,
        }

        assert_log_contains(caplog_info, expected_log_message, expected_log_data)

    def test_log_request_warning(self, httpserver, fineos_client, caplog_warning):
        request_method = "GET"
        response_code = 403  # 400-499 codes generate warning message
        absence_id = "NTN-123456-ABS-01"
        documents = [mock_document(absence_id)]
        expected_url = httpserver.url_for(
            "/customerapi/customer/cases/NTN-123456-ABS-01/documents?includeChildCases=True"
        )

        httpserver.expect_request(
            f"/customerapi/customer/cases/{absence_id}/documents", method=request_method
        ).respond_with_data(
            json.dumps(documents), status=response_code, content_type="application/json"
        )

        fineos_client.get_documents("FINEOS_WEB_ID", absence_id)

        actual_response_time = caplog_warning.records[0].FINEOSResponseTime
        expected_log_message = "FINEOS request complete"

        expected_log_data = {
            "FINEOSMethod": request_method,
            "FINEOSResponseCode": response_code,
            "FINEOSUrl": expected_url,
            "FINEOSResponseTime": actual_response_time,
            "fineos.request.method": request_method,
            "fineos.request.url": expected_url,
            "fineos.request.url_rule": "http://localhost:%/customerapi/customer/cases/NTN-%-ABS-%/documents?includeChildCases=True",
            "fineos.response.status_code": response_code,
            "fineos.response.response_time_ms": actual_response_time,
        }

        assert_log_contains(caplog_warning, expected_log_message, expected_log_data)

    def test_log_request_error(self, httpserver, fineos_client, caplog_error):
        request_method = "GET"
        response_code = 500  # 404, 413, 500 generate errors
        absence_id = "NTN-123456-ABS-01"
        documents = [mock_document(absence_id)]
        expected_url = httpserver.url_for(
            "/customerapi/customer/cases/NTN-123456-ABS-01/documents?includeChildCases=True"
        )

        httpserver.expect_request(
            f"/customerapi/customer/cases/{absence_id}/documents", method=request_method
        ).respond_with_data(
            json.dumps(documents), status=response_code, content_type="application/json"
        )

        with pytest.raises(FINEOSFatalResponseError):
            fineos_client.get_documents("FINEOS_WEB_ID", absence_id)

        actual_response_time = caplog_error.records[-1].FINEOSResponseTime
        expected_log_message = "FINEOS request complete"

        expected_log_data = {
            "FINEOSMethod": request_method,
            "FINEOSResponseCode": response_code,
            "FINEOSUrl": expected_url,
            "FINEOSResponseTime": actual_response_time,
            "fineos.request.method": request_method,
            "fineos.request.url": expected_url,
            "fineos.request.url_rule": "http://localhost:%/customerapi/customer/cases/NTN-%-ABS-%/documents?includeChildCases=True",
            "fineos.response.status_code": response_code,
            "fineos.response.response_time_ms": actual_response_time,
        }

        assert_log_contains(caplog_error, expected_log_message, expected_log_data)


class TestFineosApiLogging:

    def test_fineos_api_logging_enabled(
        self, httpserver, fineos_client, test_db_session, monkeypatch
    ):
        monkeypatch.setenv("ENABLE_FINEOS_API_LOGGING_TO_DB", "1")

        request_method = "GET"
        response_code = 200
        absence_id = "NTN-123456-ABS-01"
        documents = [mock_document(absence_id)]
        expected_url = httpserver.url_for(
            "/customerapi/customer/cases/NTN-123456-ABS-01/documents?includeChildCases=True"
        )

        httpserver.expect_request(
            f"/customerapi/customer/cases/{absence_id}/documents", method=request_method
        ).respond_with_data(
            json.dumps(documents), status=response_code, content_type="application/json"
        )

        fineos_client.get_documents("FINEOS_WEB_ID", absence_id)

        fineos_api_logs = (
            FINEOSClient.db_session_raw.query(FINEOSApiLog).filter(
                FINEOSApiLog.request_method == request_method
                and FINEOSApiLog.request_url == expected_url
            )
        ).all()

        assert len(fineos_api_logs) > 1


class TestFineosOverpaymentRecovery:
    def test_create_overpayment_recovery_succesful(self, httpserver, fineos_client):
        httpserver.expect_ordered_request(
            "/wscomposer/webservice",
            query_string={"config": "COMAddOverpaymentActualRecovery", "userid": "USER"},
            method="POST",
            headers={"Content-Type": "application/xml; charset=utf-8"},
        ).respond_with_data(
            ws_create_overpayment_recovery_response.encode("utf-8"), content_type="application/xml"
        )

        create_overpayment_recovery_request_body = massgov.pfml.fineos.models.OverpaymentRecovery(
            overpayment_case_number="PL ABS-00001-PL ABS-01-OP00001",
            amount_of_recovery=100.0,
            date_of_recovery=datetime.date(2024, 10, 11),
            recovery_method=massgov.pfml.fineos.models.OverpaymentRecoveryMethod.CHECK,
            check_name="John Doe",
            check_number="00001",
        )

        oid = fineos_client.create_overpayment_recovery(create_overpayment_recovery_request_body)

        assert oid == "PE:07329:0000000001"
        assert len(httpserver.log) == 1

    def test_create_overpayment_recovery_invalid_case_number(self, httpserver, fineos_client):

        invalid_overpayment_case_number = "PL ABS-99999-PL ABS-01-OP99999"
        # Define the expected XML error response
        ws_error_response = f"""
            <?xml version="1.0" encoding="UTF-8"?>
            <ErrorDetails>
                <faultcode>com.fineos.ta.ws.composer.sql.WSComposerQueryParamsException</faultcode>
                <faultstring>The OverPaymentCaseNumber {invalid_overpayment_case_number} does not exist or is invalid</faultstring>
                <detail></detail>
            </ErrorDetails>
            """

        # Set up the HTTP server to expect the request and respond with the error XML
        httpserver.expect_ordered_request(
            "/wscomposer/webservice",
            query_string={"config": "COMAddOverpaymentActualRecovery", "userid": "USER"},
            method="POST",
            headers={"Content-Type": "application/xml; charset=utf-8"},
        ).respond_with_data(
            ws_error_response.strip().encode("utf-8"), content_type="application/xml", status=400
        )

        # Create the request body with an invalid overpayment_case_number
        create_overpayment_recovery_request_body = massgov.pfml.fineos.models.OverpaymentRecovery(
            overpayment_case_number="PL ABS-99999-PL ABS-01-OP99999",  # Invalid case number
            amount_of_recovery=100.0,
            date_of_recovery=datetime.date(2024, 10, 11),
            recovery_method=massgov.pfml.fineos.models.OverpaymentRecoveryMethod.CHECK,
            check_name="John Doe",
            check_number="00001",
        )

        # Call the method and expect an exception
        with pytest.raises(massgov.pfml.fineos.exception.FINEOSFatalResponseError) as excinfo:
            fineos_client.create_overpayment_recovery(create_overpayment_recovery_request_body)

        # Assert the expected error message
        assert (
            "The OverPaymentCaseNumber PL ABS-99999-PL ABS-01-OP99999 does not exist or is invalid"
            in str(excinfo.value)
        )
        assert len(httpserver.log) == 1

    def test_create_overpayment_recovery_missing_check_details(self, httpserver, fineos_client):
        # Define the expected XML error response for missing check name
        ws_error_response_missing_check_name = """
        <?xml version="1.0" encoding="UTF-8"?>
        <ErrorDetails>
            <faultcode>com.fineos.ta.ws.composer.sql.WSComposerQueryParamsException</faultcode>
            <faultstring>Recovery Method is Check - please enter a check name</faultstring>
            <detail></detail>
        </ErrorDetails>
        """

        # Define the expected XML error response for missing check number
        ws_error_response_missing_check_number = """
        <?xml version="1.0" encoding="UTF-8"?>
        <ErrorDetails>
            <faultcode>com.fineos.ta.ws.composer.sql.WSComposerQueryParamsException</faultcode>
            <faultstring>Recovery Method is Check - please enter a check number</faultstring>
            <detail></detail>
        </ErrorDetails>
        """

        # Set up the HTTP server to expect the request and respond with the error XML for missing check name
        httpserver.expect_ordered_request(
            "/wscomposer/webservice",
            query_string={"config": "COMAddOverpaymentActualRecovery", "userid": "USER"},
            method="POST",
            headers={"Content-Type": "application/xml; charset=utf-8"},
        ).respond_with_data(
            ws_error_response_missing_check_name.strip().encode("utf-8"),
            content_type="application/xml",
            status=400,
        )

        # Create the request body with missing check name
        create_overpayment_recovery_request_body_missing_check_name = (
            massgov.pfml.fineos.models.OverpaymentRecovery(
                overpayment_case_number="PL ABS-00001-PL ABS-01-OP00001",
                amount_of_recovery=100.0,
                date_of_recovery="2024-10-11",
                recovery_method=massgov.pfml.fineos.models.OverpaymentRecoveryMethod.CHECK,
                check_name=None,  # Missing check name
                check_number="00001",
            )
        )

        # Call the method and expect an exception for missing check name
        with pytest.raises(massgov.pfml.fineos.exception.FINEOSFatalResponseError) as excinfo:
            fineos_client.create_overpayment_recovery(
                create_overpayment_recovery_request_body_missing_check_name
            )

        # Assert the expected error message for missing check name
        assert "Recovery Method is Check - please enter a check name" in str(excinfo.value)

        # Set up the HTTP server to expect the request and respond with the error XML for missing check number
        httpserver.expect_ordered_request(
            "/wscomposer/webservice",
            query_string={"config": "COMAddOverpaymentActualRecovery", "userid": "USER"},
            method="POST",
            headers={"Content-Type": "application/xml; charset=utf-8"},
        ).respond_with_data(
            ws_error_response_missing_check_number.strip().encode("utf-8"),
            content_type="application/xml",
            status=400,
        )

        # Create the request body with missing check number
        create_overpayment_recovery_request_body_missing_check_number = (
            massgov.pfml.fineos.models.OverpaymentRecovery(
                overpayment_case_number="PL ABS-00001-PL ABS-01-OP00001",
                amount_of_recovery=100.0,
                date_of_recovery="2024-10-11",
                recovery_method=massgov.pfml.fineos.models.OverpaymentRecoveryMethod.CHECK,
                check_name="John Doe",
                check_number=None,  # Missing check number
            )
        )

        # Call the method and expect an exception for missing check number
        with pytest.raises(massgov.pfml.fineos.exception.FINEOSFatalResponseError) as excinfo:
            fineos_client.create_overpayment_recovery(
                create_overpayment_recovery_request_body_missing_check_number
            )

        # Assert the expected error message for missing check number
        assert "Recovery Method is Check - please enter a check number" in str(excinfo.value)

        # Verify that the HTTP server received the expected requests
        assert len(httpserver.log) == 2


class TestGetAbsenceReason:
    def test_success(self, httpserver, fineos_client):
        absence_reason_id = "12345-1"
        mock_response = mock_absence_reason_resource(absence_reason_id)
        httpserver.expect_request(
            f"/customerapi/customer/absence/absence-reasons/{absence_reason_id}",
        ).respond_with_data(json.dumps(mock_response), status=200, content_type="application/json")

        absence_reason_resource: AbsenceReasonResource = fineos_client.get_absence_reason(
            "FINEOS_WEB_ID", absence_reason_id
        )

        # assert that object is parsed correctly
        assert type(absence_reason_resource) == AbsenceReasonResource
        assert absence_reason_resource.id == "12345-1"
        assert absence_reason_resource.reasonName == "Mock reason name"
        assert absence_reason_resource.reasonQualifier1 == "Mock qualifier 1"
        assert absence_reason_resource.reasonQualifier2 == "Mock qualifier 2"
        assert absence_reason_resource.validTransitions == ["14412-9", "14412-10", "14412-19"]

    def test_with_error(self, httpserver, fineos_client):
        absence_reason_id = "12345-1"
        httpserver.expect_request(
            f"/customerapi/customer/absence/absence-reasons/{absence_reason_id}",
        ).respond_with_data(
            '{"message": "Internal Server Error"', status=500, content_type="application/json"
        )

        with pytest.raises(FINEOSClientError, match="FINEOSFatalResponseError: 500"):
            fineos_client.get_absence_reason("FINEOS_WEB_ID", absence_reason_id)


class TestAddAbsencePeriodToAbsenceCase:
    def test_success(self, httpserver, fineos_client):
        absence_id = "NTN-527629-ABS-01"
        mock_absence_period = unittest.mock.MagicMock()
        mock_absence_period.json.return_value = "{}"
        mock_response = mock_requested_absence_period_resource_response()
        httpserver.expect_request(
            f"/customerapi/customer/absence/absences/{absence_id}/requested-absence-periods",
        ).respond_with_data(
            json.dumps(mock_response),
            status=200,
            content_type="application/json",
        )

        requested_absence_period_resource_response: RequestedAbsencePeriodResource = (
            fineos_client.add_absence_period_to_absence_case(
                "FINEOS_WEB_ID", absence_id, mock_absence_period
            )
        )

        # assert that object is parsed correctly
        assert type(requested_absence_period_resource_response) == RequestedAbsencePeriodResource
        assert requested_absence_period_resource_response.startDate == datetime.date(1999, 12, 31)
        assert requested_absence_period_resource_response.endDate == datetime.date(1999, 12, 31)
        assert requested_absence_period_resource_response.status.name == "string"
        assert requested_absence_period_resource_response.type.fullId == 0
        assert requested_absence_period_resource_response.details.__root__.startDateAllDay is True
        assert requested_absence_period_resource_response.details.__root__.startHours == 0
        assert (
            requested_absence_period_resource_response.details.__root__.lastDayWorked
            == datetime.date(1999, 12, 31)
        )

    def test_with_error(self, httpserver, fineos_client, caplog):
        absence_id = "NTN-527629-ABS-01"
        mock_absence_period = unittest.mock.MagicMock()
        mock_absence_period.json.return_value = "{}"
        httpserver.expect_request(
            f"/customerapi/customer/absence/absences/{absence_id}/requested-absence-periods",
        ).respond_with_data(
            '{"message": "Internal Server Error"}', status=500, content_type="application/json"
        )

        with pytest.raises(FINEOSFatalResponseError):
            fineos_client.add_absence_period_to_absence_case(
                "FINEOS_WEB_ID", absence_id, mock_absence_period
            )


class TestUpdateRequestedAbsencePeriod:
    def test_success(self, httpserver, fineos_client):
        absence_id = "NTN-30687-ABS-01"
        requested_absence_period_id = "112818"
        mock_edit_absence_period_command = unittest.mock.MagicMock()
        mock_edit_absence_period_command.json.return_value = "{}"
        mock_response = mock_requested_absence_period_resource_response()
        httpserver.expect_request(
            f"/customerapi/customer/absence/absences/{absence_id}/requested-absence-periods/{requested_absence_period_id}/edit",
            method="POST",
        ).respond_with_data(
            json.dumps(mock_response),
            status=200,
            content_type="application/json",
        )

        requested_absence_period_resource_response: RequestedAbsencePeriodResource = (
            fineos_client.update_requested_absence_period(
                "FINEOS_WEB_ID",
                absence_id,
                requested_absence_period_id,
                mock_edit_absence_period_command,
            )
        )

        # assert that response object is parsed correctly
        assert type(requested_absence_period_resource_response) == RequestedAbsencePeriodResource
        assert requested_absence_period_resource_response.startDate == datetime.date(1999, 12, 31)
        assert requested_absence_period_resource_response.endDate == datetime.date(1999, 12, 31)
        assert requested_absence_period_resource_response.status.name == "string"
        assert requested_absence_period_resource_response.type.fullId == 0
        assert requested_absence_period_resource_response.details.__root__.startDateAllDay is True
        assert requested_absence_period_resource_response.details.__root__.startHours == 0
        assert (
            requested_absence_period_resource_response.details.__root__.lastDayWorked
            == datetime.date(1999, 12, 31)
        )

    def test_with_error(self, httpserver, fineos_client, caplog):
        absence_id = "NTN-30687-ABS-01"
        requested_absence_period_id = "112818"
        mock_absence_period = unittest.mock.MagicMock()
        mock_absence_period.json.return_value = "{}"
        httpserver.expect_request(
            f"/customerapi/customer/absence/absences/{absence_id}/requested-absence-periods/{requested_absence_period_id}/edit",
            method="POST",
        ).respond_with_data(
            '{"message": "Internal Server Error"}', status=500, content_type="application/json"
        )

        with pytest.raises(FINEOSFatalResponseError):
            fineos_client.update_requested_absence_period(
                "FINEOS_WEB_ID", absence_id, requested_absence_period_id, mock_absence_period
            )


class TestReadFineosEmployer:
    @pytest.fixture
    def mock_read_employer_response(self):
        path = pathlib.Path(__file__).parent
        with open(os.path.join(path, "assets", "ReadEmployer.Response.xml")) as f:
            return f.read()

    @pytest.fixture
    def get_parsed_employer_resource(self, httpserver, fineos_client, mock_read_employer_response):
        # Needs to match the IDs from the XML file
        employer_fein = "*********"

        httpserver.expect_request(
            "/wscomposer/ReadEmployer",
            query_string={
                "userid": "USER",
                "param_str_taxId": employer_fein,
            },
            method="GET",
            headers={"Content-Type": "application/xml; charset=utf-8"},
        ).respond_with_data(mock_read_employer_response, status=200, content_type="application/xml")

        employer_resource = fineos_client.read_employer(employer_fein)
        return employer_resource

    def test_parsing_success(self, get_parsed_employer_resource):
        # assert that object is parsed correctly
        assert type(get_parsed_employer_resource) == OCOrganisation
        assert get_parsed_employer_resource.OCOrganisation[0].OID == "PE:11528:0000001256"
        assert (
            get_parsed_employer_resource.is_valid_oid(
                get_parsed_employer_resource.OCOrganisation[0].OID
            )
            is True
        )
        assert get_parsed_employer_resource.is_valid_oid("123USER:456") is False
        assert get_parsed_employer_resource.is_valid_oid("") is False
        assert get_parsed_employer_resource.parse_oid("") == ("", "11528", "")
        assert get_parsed_employer_resource.oid_parts == ("PE", "11528", "0000001256")
        assert get_parsed_employer_resource.get_class_id() == "11528"
        assert get_parsed_employer_resource.get_index_id() == "0000001256"


@pytest.mark.parametrize(
    "key,expected_msg,error_msg",
    (
        (
            "nonDisplayableMessage",
            "A document of type <doc type> is not required for the case provided <case number>.",
            """{"errors" : [ {
                "id" : "df787ee1-5b4f-43b0-a35d-e67c4af0dcaf",
                "status" : "400",
                "meta" : {
                "nonDisplayableMessage" : "A document of type <doc type> is not required for the case provided <case number>.",
                }
            } ]""",
        ),
        ("test", "", '"detail" : "Request could not be processed, please try again later."'),
        ("test", "", {}),
        ("test", "", []),
        ("test", "", None),
        ("test", "", ""),
        (None, "", None),
        ("", "", ""),
    ),
)
def test_internal_error(fineos_client, key, expected_msg, error_msg):
    msg = fineos_client._get_internal_error(key, error_msg)
    assert expected_msg == msg
