import uuid
from datetime import datetime
from unittest import mock

import pytest

import massgov.pfml.services.documents as documents_service
from massgov.pfml.api.models.documents.common import DocumentType as DocumentTypeEnum
from massgov.pfml.api.models.documents.responses import DocumentResponse
from massgov.pfml.db.models.documents import Document
from massgov.pfml.db.models.factories import ClaimFactory, EmployerFactory
from massgov.pfml.fineos.exception import FINEOSClientError, FINEOSFatalResponseError


@pytest.fixture
def mock_db_document():
    mock_db_document = mock.MagicMock(spec=Document)
    mock_db_document.document_type_instance = None
    mock_db_document.document_id = 1
    mock_db_document.user_id = uuid.uuid4()
    mock_db_document.application_id = uuid.uuid4()
    mock_db_document.appeal_id = uuid.uuid4()
    mock_db_document.document_type_id = 9
    mock_db_document.pfml_document_type_id = 9
    mock_db_document.fineos_id = 3131
    mock_db_document.size_bytes = 47029
    mock_db_document.is_stored_in_s3 = True
    mock_db_document.name = "Mock Passport"
    mock_db_document.description = "Page 1"
    mock_db_document.created_at = datetime.now()
    return mock_db_document


@pytest.fixture
def claim(employer):
    employer = EmployerFactory.build()
    return ClaimFactory.build(employer_id=employer.employer_id, fineos_absence_id="NTN-111-111")


ALREADY_SATISFIED_ERROR = FINEOSFatalResponseError(
    "upload_document",
    200,
    400,
    """{"errors" : [ {
                "id" : "df787ee1-5b4f-43b0-a35d-e67c4af0dcaf",
                "status" : "400",
                "code" : "",
                "title" : "Invalid",
                "detail" : "Request could not be processed, please try again later.",
                "meta" : {
                "nonDisplayableMessage" : "A document of type <doc type> is not required for the case provided <case number>.",
                "X-Client-ID" : "Unknown",
                "X-Correlation-ID" : "c23a5bee-36d2-4467-b1b9-dc7347671709",
                "X-Client-Version" : "Unknown"
                },
                "_links" : {
                "origin" : "http://api.server.context/customerapi/api/v5.0/customer/cases/<case number>/documents/3011/doc-received-for-outstanding-supporting-evidence"
                }
            } ]""",
)

ERROR = FINEOSClientError()


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.mark_document_as_received")
def test_mark_single_document_as_received_for_claim_already_satisfied(
    mock_mark_fineos_document, mock_db_document, test_db_session, claim
):

    # GIVEN
    fineos_web_id = "pfml_api_468df93c-cb2d-424e-9690-f61cc65506bb"
    mock_mark_fineos_document.side_effect = ALREADY_SATISFIED_ERROR
    document_response = DocumentResponse.from_orm(mock_db_document)

    # WHEN
    documents_service.mark_single_document_as_received_for_claim(
        claim=claim,
        document_response=document_response,
        db_session=test_db_session,
        fineos_web_id=fineos_web_id,
    )

    # THEN
    mock_mark_fineos_document.assert_called_with(
        user_id=str(fineos_web_id),
        absence_id=str(claim.fineos_absence_id),
        fineos_document_id=document_response.fineos_document_id,
    )


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.mark_document_as_received")
def test_mark_single_document_as_received_for_claim_error(
    mock_mark_fineos_document, mock_db_document, test_db_session, claim
):

    # GIVEN
    fineos_web_id = "pfml_api_468df93c-cb2d-424e-9690-f61cc65506bb"
    mock_mark_fineos_document.side_effect = ERROR
    document_response = DocumentResponse.from_orm(mock_db_document)

    # WHEN
    with pytest.raises(FINEOSClientError):
        documents_service.mark_single_document_as_received_for_claim(
            claim=claim,
            document_response=document_response,
            db_session=test_db_session,
            fineos_web_id=fineos_web_id,
        )

    # THEN
    mock_mark_fineos_document.assert_called_with(
        user_id=str(fineos_web_id),
        absence_id=str(claim.fineos_absence_id),
        fineos_document_id=document_response.fineos_document_id,
    )


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.mark_document_as_received")
def test_mark_single_document_as_received_for_claim_irrelevant_doc(
    mock_mark_fineos_document, mock_db_document, test_db_session, claim
):

    # GIVEN
    fineos_web_id = "pfml_api_468df93c-cb2d-424e-9690-f61cc65506bb"
    mock_mark_fineos_document.side_effect = ERROR
    document_response = DocumentResponse.from_orm(mock_db_document)
    document_response.document_type = DocumentTypeEnum.passport

    # WHEN
    documents_service.mark_single_document_as_received_for_claim(
        claim=claim,
        document_response=document_response,
        db_session=test_db_session,
        fineos_web_id=fineos_web_id,
    )

    # THEN
    mock_mark_fineos_document.assert_not_called()
