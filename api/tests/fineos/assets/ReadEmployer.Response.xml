<?xml version='1.0' encoding='utf-8'?>
<p:ReadEmployerResponse xmlns:p="http://www.fineos.com/wscomposer/ReadEmployer" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <OCOrganisation>
    <OID>PE:11528:0000001256</OID>
    <BOEVersion>1</BOEVersion>
    <LastUpdateDate>2024-09-04T12:55:58</LastUpdateDate>
    <UserUpdatedBy>CONTENT</UserUpdatedBy>
    <C_CORRESP_PRIVHOLDER>0</C_CORRESP_PRIVHOLDER>
    <C_OSGROUP_OWP>0</C_OSGROUP_OWP>
    <CorrespNeedsTranslation>false</CorrespNeedsTranslation>
    <CulturalConsiderations></CulturalConsiderations>
    <CustomerNo>78566</CustomerNo>
    <Disabled>false</Disabled>
    <ExcludePartyFromSearch>false</ExcludePartyFromSearch>
    <FailedLogonAttempts>0</FailedLogonAttempts>
    <GroupClient>false</GroupClient>
    <I_CORRESP_PRIVHOLDER>0</I_CORRESP_PRIVHOLDER>
    <I_OSGROUP_OWP>0</I_OSGROUP_OWP>
    <IdentificationNumberType>
      <InstanceDomainAndFullId>
        <InstanceName>Tax Identification Number</InstanceName>
        <DomainName>Identification Number Type</DomainName>
        <FullId>8736002</FullId>
      </InstanceDomainAndFullId>
    </IdentificationNumberType>
    <LastSuccessfulLogon xsi:nil="true" />
    <NotificationIssued>false</NotificationIssued>
    <PartyType>
      <InstanceDomainAndFullId>
        <InstanceName>Employer</InstanceName>
        <DomainName>Party Type</DomainName>
        <FullId>3290008</FullId>
      </InstanceDomainAndFullId>
    </PartyType>
    <Password></Password>
    <Position1>0</Position1>
    <Position2>0</Position2>
    <PronouncedAs></PronouncedAs>
    <ReferenceGloballyUnique>false</ReferenceGloballyUnique>
    <ReferenceNo></ReferenceNo>
    <SecuredClient>false</SecuredClient>
    <SelfServiceEnabled>false</SelfServiceEnabled>
    <SourceSystem>
      <InstanceDomainAndFullId>
        <InstanceName>Internal</InstanceName>
        <DomainName>PartySourceSystem</DomainName>
        <FullId>8032000</FullId>
      </InstanceDomainAndFullId>
    </SourceSystem>
    <SuppressMktg>false</SuppressMktg>
    <TenureStart xsi:nil="true" />
    <UnVerified>false</UnVerified>
    <NextPartyNumber></NextPartyNumber>
    <PartyDetailsUpdated>false</PartyDetailsUpdated>
    <TransientNameAsString></TransientNameAsString>
    <AccountingDate xsi:nil="true" />
    <BusinessType>
      <InstanceDomainAndFullId>
        <InstanceName>Unknown</InstanceName>
        <DomainName>Organisation Business Type</DomainName>
        <FullId>3328000</FullId>
      </InstanceDomainAndFullId>
    </BusinessType>
    <Category>
      <InstanceDomainAndFullId>
        <InstanceName>Unknown</InstanceName>
        <DomainName>Organisation Category</DomainName>
        <FullId>8576000</FullId>
      </InstanceDomainAndFullId>
    </Category>
    <CompanyNumber></CompanyNumber>
    <CorporateTaxDistrict></CorporateTaxDistrict>
    <CorporateTaxNumber>*********</CorporateTaxNumber>
    <DateBusinessCommenced xsi:nil="true" />
    <DoingBusinessAs>Duran-Wells</DoingBusinessAs>
    <EndOfTrading xsi:nil="true" />
    <EOTReasonCode>
      <InstanceDomainAndFullId>
        <InstanceName>Unknown</InstanceName>
        <DomainName>EndOfTradingReasonCode</DomainName>
        <FullId>6592000</FullId>
      </InstanceDomainAndFullId>
    </EOTReasonCode>
    <EOTReasonInd>false</EOTReasonInd>
    <FinancialYearEnd xsi:nil="true" />
    <LegalBusinessName>Test Company 474</LegalBusinessName>
    <LegalStatus>
      <InstanceDomainAndFullId>
        <InstanceName>Unknown</InstanceName>
        <DomainName>Legal Status</DomainName>
        <FullId>1408000</FullId>
      </InstanceDomainAndFullId>
    </LegalStatus>
    <Name>Test Company 474</Name>
    <PayeTaxDistrict></PayeTaxDistrict>
    <PayeTaxNumber></PayeTaxNumber>
    <RegisteredNumber></RegisteredNumber>
    <ShortName>Test Com</ShortName>
    <UpperName>TEST COMPANY 474</UpperName>
    <UpperRegisteredNumber></UpperRegisteredNumber>
    <UpperShortName>TEST COM</UpperShortName>
    <VatNumber></VatNumber>
    <organisationUnits />
  </OCOrganisation>
</p:ReadEmployerResponse>
