import csv
import os
from datetime import datetime
from pathlib import Path
from typing import List, Optional

import pytest

import massgov.pfml.delegated_payments.delegated_config as payments_config
from massgov.pfml.db.lookup_data.dor import FineosServiceAgreementStatus
from massgov.pfml.db.models.dor import FineosExtractServiceAgreement, FineosServiceAgreement
from massgov.pfml.db.models.factories import EmployerFactory
from massgov.pfml.delegated_payments.extracts.fineos_extract_config import (
    SERVICE_AGREEMENT_EXTRACT_CONFIG,
    FineosExtractConstants,
)
from massgov.pfml.fineos.import_fineos_service_agreements import FineosServiceAgreementTaskRunner
from massgov.pfml.fineos.models import ServiceAgreementLeavePlans
from massgov.pfml.fineos.tool.bucket_tool_step import BucketToolStep

MOCK_EMPLOYER_ID = "123"


def generate_csv_file(
    directory: str,
    file_name: str,
    field_names: List[str],
    rows: List[Optional[List[Optional[str]]]],
) -> None:
    """
    Create a CSV file with the given file name, write the column names and append the rows of data.
    A new CSV file is generated on each run the filename is time dependent. See line 42 and 43

    :param directory: The temporary location to store the generated CSV
    :param file_name: The name of the CSV file
    :param field_names: The column titles of the CSV file
    :param rows: A list of rows to persist to the CSV file
    """

    os.makedirs(directory, exist_ok=True)
    # set date_str here so that each mock_run has a different reference file name
    date_str = datetime.now().strftime("%Y-%m-%d-%H-%m-%S")
    filepath = Path.joinpath(Path(directory), f"{date_str}-{file_name}")

    with open(filepath, "w", newline="") as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(field_names)
        writer.writerows(rows)


def get_sample_service_agreement_row() -> List:
    return [
        MOCK_EMPLOYER_ID,
        "SA-456",
        "1",
        "2020-01-01 00:00:00",
        "",
        ServiceAgreementLeavePlans.EMPLOYEE.value,
        "2020-01-01",
        "2020-01-01",
        FineosServiceAgreementStatus.PENDING.status_description,
        "",
    ]


def generate_service_agreement_csv(directory: str) -> None:
    """
    Creates a test Service Agreement CSV file in the given directory
    The `generate_csv_file` function generates a different CSV on each call given that filepath is dependent on current time.
    """
    file_name: str = FineosExtractConstants.FINEOS_SERVICE_AGREEMENTS.file_name
    field_names: List = FineosExtractConstants.FINEOS_SERVICE_AGREEMENTS.field_names
    rows: List[Optional[List[Optional[str]]]] = [get_sample_service_agreement_row()]

    generate_csv_file(directory, file_name, field_names, rows)


def setup_mock_bucket_tool_step(monkeypatch, directory_map):
    """
    Sets up a mock for BucketToolStep.run to generate CSV files in specified directories.
    """

    def mock_run(self):
        for directory, generate_csv_funcs in directory_map.items():
            for func in generate_csv_funcs:
                func(directory)

    monkeypatch.setattr(BucketToolStep, "run", mock_run)


# needed to initialize the feature config before code being tested checks feature flags
@pytest.fixture(autouse=True)
def use_initialize_feature_config(initialize_feature_config):
    pass


@pytest.fixture
def mock_bucket_tool_step(monkeypatch, mock_payments_s3_config):
    """
    Mocks what Fineos Systems would do after reading the config file copied
    by BucketToolStep within FineosServiceAgreementTaskRunner

    Fineos System would read the SQL in the config and write the result of the
    query to the filename specified in the config with a date prefix.
    """
    directory: str = SERVICE_AGREEMENT_EXTRACT_CONFIG.get_fineos_extract_source_folder(
        mock_payments_s3_config
    )

    setup_mock_bucket_tool_step(monkeypatch, {directory: [generate_service_agreement_csv]})
    yield


@pytest.fixture
def mock_payments_s3_config(tmp_path):
    """Mock S3 configuration using test specific temporary directory, isolated from any other test."""
    return payments_config.PaymentsS3Config(
        fineos_data_export_path=f"{tmp_path}/fin-data-export/LCL/dataexports/",
        fineos_adhoc_data_export_path=f"{tmp_path}/fin-data-export/LCL/dataExtracts/AdHocExtract/",
        fineos_data_import_path=f"{tmp_path}/fin-data-import/LCL/peiupdate/",
        pfml_fineos_extract_archive_path=f"{tmp_path}/agency-transfer/cps/archive/extracts/",
        pfml_error_reports_archive_path=f"{tmp_path}/agency-transfer/error-reports/archive/",
    )


def set_fineos_service_agreement_env_vars(monkeypatch):
    current_date_str = datetime.now().strftime("%Y-%m-%d")
    monkeypatch.setenv("FINEOS_SERVICE_AGREEMENT_EXTRACT_MAX_HISTORY_DATE", current_date_str)


def create_mock_employer(session, employer_id):
    employer = EmployerFactory(fineos_employer_id=employer_id)
    session.add(employer)
    session.commit()
    return employer


@pytest.mark.flaky(max_runs=3, min_passes=1)
@pytest.mark.timeout(30)  # Use a timeout to limit WaitForFineosExtractStep
def test_import_fineos_service_agreement_e2e(
    local_initialize_factories_session,
    local_test_db_session,
    mock_payments_s3_config,
    mock_bucket_tool_step,
    monkeypatch,
):
    set_fineos_service_agreement_env_vars(monkeypatch)
    employer = create_mock_employer(local_test_db_session, MOCK_EMPLOYER_ID)

    FineosServiceAgreementTaskRunner(s3_config=mock_payments_s3_config).run()  # create

    raw_service_agreements = local_test_db_session.query(FineosExtractServiceAgreement).all()
    assert len(raw_service_agreements) == 1
    assert raw_service_agreements[0].employer_customerno == MOCK_EMPLOYER_ID
    assert raw_service_agreements[0].effectivedate == "2020-01-01 00:00:00"
    assert raw_service_agreements[0].leaveplan == ServiceAgreementLeavePlans.EMPLOYEE.value
    assert (
        raw_service_agreements[0].status == FineosServiceAgreementStatus.PENDING.status_description
    )

    service_agreements = local_test_db_session.query(FineosServiceAgreement).all()
    assert len(service_agreements) == 1
    assert service_agreements[0].employer == employer
    assert (
        service_agreements[0].fineos_service_agreement_case_number
        == raw_service_agreements[0].serviceagreement_no
    )
    assert service_agreements[0].start_date.isoformat() == "2020-01-01"
    assert service_agreements[0].family_exemption is True
    assert service_agreements[0].medical_exemption is False
    assert service_agreements[0].status_id == FineosServiceAgreementStatus.PENDING.status_id
    assert service_agreements[0].import_log_id is not None

    original_updated_at = service_agreements[0].updated_at

    # Running again should not update the service agreement since it would not have different data
    # FYI this would however create a new CSV that would get processed,
    # because of how mock_bucket_tool_step is configured
    FineosServiceAgreementTaskRunner(s3_config=mock_payments_s3_config).run()  # update

    raw_service_agreements = (
        local_test_db_session.query(FineosExtractServiceAgreement)
        .order_by(FineosExtractServiceAgreement.created_at)
        .all()
    )
    # should have a new raw extract service agreement
    assert len(raw_service_agreements) == 2
    for raw_sa in raw_service_agreements:
        assert raw_sa.employer_customerno == MOCK_EMPLOYER_ID
        assert raw_sa.effectivedate == "2020-01-01 00:00:00"
        assert raw_sa.leaveplan == ServiceAgreementLeavePlans.EMPLOYEE.value
        assert raw_sa.status == FineosServiceAgreementStatus.PENDING.status_description
    # their reference files should differ
    assert (
        raw_service_agreements[0].reference_file_id != raw_service_agreements[1].reference_file_id
    )

    service_agreements = local_test_db_session.query(FineosServiceAgreement).all()
    # should not have created a new service agreement
    assert len(service_agreements) == 1
    assert service_agreements[0].employer == employer
    assert (
        service_agreements[0].fineos_service_agreement_case_number
        == raw_service_agreements[0].serviceagreement_no
    )
    assert service_agreements[0].start_date.isoformat() == "2020-01-01"
    assert service_agreements[0].family_exemption is True
    assert service_agreements[0].medical_exemption is False
    assert service_agreements[0].status_id == FineosServiceAgreementStatus.PENDING.status_id
    assert service_agreements[0].import_log_id is not None
    # should not have updated the existing service agreement since all the data was the same
    assert service_agreements[0].updated_at == service_agreements[0].created_at
    assert service_agreements[0].updated_at == original_updated_at
