import random
from typing import Type

import pytest

from massgov.pfml import features
from massgov.pfml.dor.step import DorStep
from massgov.pfml.dor.task.employer_exemptions_to_dor_export import (
    EmployerExemptionsDORExporterTaskRunner,
)
from massgov.pfml.util.batch.process_files_in_path_step import ProcessFilesInPathStep
from massgov.pfml.util.batch.task_runner import TaskRunner

TASK_RUNNERS: list[Type[TaskRunner]] = [
    EmployerExemptionsDORExporterTaskRunner,
]


def task_id_func(task_runner_cls):
    # function to make the test pretty print the
    # class name rather than a random memory value
    return task_runner_cls.__name__


def param_id_func(init_params):
    return str(init_params)


@pytest.mark.parametrize(
    "init_params",
    [["--steps", "ALL"], None],  # Show that "--steps ALL" and no params behave the same
    ids=param_id_func,
)
@pytest.mark.parametrize("task_runner_cls", TASK_RUNNERS, ids=task_id_func)
def test_employer_exemptions_to_dor_task_runner_init_all(
    test_db_session, monkeypatch, task_runner_cls, init_params
):
    features.initialize()
    features.get_config().employer_exemptions.enable_employer_exemptions_dor_data_transfer = True
    task_runner = task_runner_cls(init_params)

    shuffled_tasks = list(task_runner_cls.StepMapping)
    random.shuffle(shuffled_tasks)
    shuffled_init_params = ["--steps"].extend(shuffled_tasks)
    shuffled_task_runner = task_runner_cls(shuffled_init_params)

    all_except_steps = [step.value for step in task_runner.AllExceptStepMapping]

    # Verify the configured steps are enabled
    for item in task_runner.StepMapping:
        if item.value in all_except_steps:
            assert task_runner.step_config[item.value] is False
        else:
            assert task_runner.step_config[item.value] is True

    steps_run = []

    def mock_run(self):
        steps_run.append(self.__class__.__name__)

        # There are a few steps derived from this
        # class that run multiple times, once for each
        # file they find. Need to make sure they "find"
        # no more files.
        if isinstance(self, ProcessFilesInPathStep):
            self.more_files_to_process = False

    monkeypatch.setattr(DorStep, "run", mock_run)
    monkeypatch.setenv("EMPLOYER_EXEMPTIONS_EXPORT_FILE_CREATION", True)

    task_runner.run_steps(test_db_session, test_db_session)
    steps_run_all = steps_run
    # Run the shuffled version of the task
    steps_run = []
    shuffled_task_runner.run_steps(test_db_session, test_db_session)
    steps_run_when_shuffled = steps_run

    # Verify that every step ran once
    assert len(task_runner.StepMapping) - len(task_runner.AllExceptStepMapping) == len(
        steps_run_all
    )

    # Show that the order the steps are passed in does not matter
    assert steps_run_all == steps_run_when_shuffled
