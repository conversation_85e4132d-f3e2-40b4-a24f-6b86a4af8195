#
# Tests for massgov.pfml.dor.importer.import_dor.
#
import copy
import decimal
import pathlib
import tempfile
from datetime import date, datetime, timezone
from typing import List
from unittest.mock import patch

import boto3
import botocore
import faker
import pytest
from sqlalchemy import not_
from sqlalchemy.exc import SQLAlchemyError

import massgov.pfml.dor.importer.import_dor as import_dor
import massgov.pfml.dor.importer.lib.dor_persistence_util as dor_persistence_util
import massgov.pfml.dor.importer.paths
import massgov.pfml.dor.mock.generate as generator
import massgov.pfml.util.batch.log
import massgov.pfml.util.logging
from massgov.pfml.db import Session
from massgov.pfml.db.lookup_data.employees import AddressType, WagesAndContributionsDatasource
from massgov.pfml.db.lookup_data.geo import Country, GeoState
from massgov.pfml.db.models.dor import EmployerDORExemption
from massgov.pfml.db.models.employees import (
    Address,
    Employee,
    EmployeePushToFineosQueue,
    Employer,
    EmployerPushToFineosQueue,
    EmployerQuarterlyContribution,
    WagesAndContributions,
    WagesAndContributionsHistory,
    WagesAndContributionsUnused,
)
from massgov.pfml.db.models.employer_exemptions import InsuranceProvider
from massgov.pfml.db.models.factories import (
    EmployeeFactory,
    EmployerFactory,
    EmployerQuarterlyContributionFactory,
    WagesAndContributionsFactory,
)
from massgov.pfml.dor.importer import dor_shared_utils
from massgov.pfml.dor.importer.dor_shared_utils import (
    PROCESSED_FOLDER,
    RECEIVED_FOLDER,
    Constants,
    generate_and_upload_dor_employee_integrity_file,
    get_discreet_db_exception_message,
    move_file_to_processed,
)
from massgov.pfml.dor.importer.employers.employer_importer import import_employers
from massgov.pfml.dor.importer.import_wages import WageImporter
from massgov.pfml.dor.util.process_dor_import import DORImport, ProcessDORImport
from massgov.pfml.util.batch.log import create_log_entry
from massgov.pfml.util.datetime import get_now_us_eastern
from massgov.pfml.util.encryption import GpgCrypt, PassthruCrypt

from . import dor_test_data as test_data

decrypter = PassthruCrypt()
employee_file = "DORDFML_20200519120622"
employer_file = "DORDFMLEMP_20200519120622"

fake = faker.Faker()
TEST_FOLDER = pathlib.Path(__file__).parent

EMPTY_SSN_TO_EMPLOYEE_ID_MAP = {}


@pytest.fixture(autouse=True)
def enable_dor_row_population(monkeypatch):
    monkeypatch.setenv("EMPLOYER_DOR_ROW_POPULATION", "1")


@pytest.fixture
def test_fs_path(tmp_path):
    employer_quarter_line = test_data.get_employer_quarter_line()
    employee_quarter_line = test_data.get_employee_quarter_line()
    content1 = "{}\n{}".format(employer_quarter_line, employee_quarter_line)

    content2 = test_data.get_employer_info_line()

    test_folder = tmp_path / "test_folder"
    test_folder.mkdir()
    test_file = test_folder / employee_file
    test_file.write_text(content1)
    test_file2 = test_folder / employer_file
    test_file2.write_text(content2)

    return test_folder


@pytest.fixture
def test_fs_path_bad_fein(tmp_path):
    employer_quarter_line = test_data.get_employer_quarter_line()
    employee_quarter_line = test_data.get_employee_quarter_line()
    content1 = "{}\n{}".format(employer_quarter_line, employee_quarter_line)

    content2 = test_data.get_employer_info_line_bad_fein()

    test_folder = tmp_path / "test_folder"
    test_folder.mkdir()
    test_file = test_folder / employee_file
    test_file.write_text(content1)
    test_file2 = test_folder / employer_file
    test_file2.write_text(content2)

    return test_folder


@pytest.fixture
def test_fs_path_with_activity_key(tmp_path):
    employer_quarter_line = test_data.get_employer_quarter_line()
    employee_quarter_line = test_data.get_employee_quarter_line()
    content1 = "{}\n{}".format(employer_quarter_line, employee_quarter_line)

    content2 = test_data.get_employer_info_line_with_activity_key()

    test_folder = tmp_path / "test_folder"
    test_folder.mkdir()
    test_file = test_folder / employee_file
    test_file.write_text(content1)
    test_file2 = test_folder / employer_file
    test_file2.write_text(content2)

    return test_folder


@pytest.fixture
def test_fs_path_with_mtc_number(tmp_path):
    employer_quarter_line = test_data.get_employer_quarter_line()
    employee_quarter_line = test_data.get_employee_quarter_line()
    content1 = "{}\n{}".format(employer_quarter_line, employee_quarter_line)

    content2 = test_data.get_employer_info_line_with_mtc_number()

    test_folder = tmp_path / "test_folder"
    test_folder.mkdir()
    test_file = test_folder / employee_file
    test_file.write_text(content1)
    test_file2 = test_folder / employer_file
    test_file2.write_text(content2)

    return test_folder


@pytest.fixture
def dor_employer_lookups(test_db_session):

    # setup employer expected lookup values
    GeoState.sync_to_database(test_db_session)
    state = GeoState.get_instance(test_db_session, template=GeoState.MA)

    Country.sync_to_database(test_db_session)
    country = Country.get_instance(test_db_session, template=Country.USA)

    return (state, country)


@pytest.fixture
def mock_file_util():
    with patch("massgov.pfml.dor.importer.dor_shared_utils.file_util") as mock:
        yield mock


@pytest.fixture
def mock_dor_config():
    with patch("massgov.pfml.dor.importer.dor_shared_utils.dor_config") as mock:
        yield mock


@pytest.fixture
def mock_send_email():
    with patch(
        "massgov.pfml.dor.importer.dor_shared_utils.send_employee_integrity_report_email"
    ) as mock:
        yield mock


def test_employer_import(test_db_session, dor_employer_lookups):
    # perform import
    employer_payload = test_data.get_new_employer()
    employers = [employer_payload]
    report, report_log_entry = get_new_import_report(test_db_session)
    assert report_log_entry.import_log_id is not None

    import_employers(test_db_session, employers, report, report_log_entry.import_log_id)

    # confirm expected columns are persisted
    persisted_employer = (
        test_db_session.query(Employer)
        .filter(Employer.account_key == employer_payload["account_key"])
        .one_or_none()
    )
    employer_id = persisted_employer.employer_id

    assert persisted_employer is not None

    validate_employer_persistence(
        employer_payload, persisted_employer, report_log_entry.import_log_id
    )

    persisted_employer_address = dor_persistence_util.get_employer_address(
        test_db_session, employer_id
    )
    assert persisted_employer_address is not None

    persisted_address = dor_persistence_util.get_address(
        test_db_session, persisted_employer_address.address_id
    )
    assert persisted_address is not None

    state, country = dor_employer_lookups
    validate_employer_address_persistence(
        employer_payload, persisted_address, AddressType.BUSINESS, state, country
    )

    assert report.created_employers_count == 1
    assert report.updated_employers_count == 0
    assert report.unmodified_employers_count == 0

    # Verify Logs are correct
    employer_insert_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "INSERT")
        .all()
    )
    assert len(employer_insert_logs) == 1
    assert employer_insert_logs[0].employer_id == employer_id
    # ------
    employer_update_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employer_update_logs) == 0

    # Verify Dor Exemptions were NOT synced sicne data is missing
    # `id` and `activity key`
    dor_exemptions: List[EmployerDORExemption] = test_db_session.query(EmployerDORExemption).all()
    assert len(dor_exemptions) == 0


@pytest.mark.parametrize("ff_value", ["0", None])
def test_employer_import_with_activity_key_feature_flag_off(
    test_db_session, dor_employer_lookups, ff_value, monkeypatch
):
    monkeypatch.setenv("EMPLOYER_DOR_ROW_POPULATION", ff_value)
    # perform import
    employer_payload = test_data.get_new_employer_with_activity_key()
    employers = [employer_payload]
    report, report_log_entry = get_new_import_report(test_db_session)
    assert report_log_entry.import_log_id is not None

    import_employers(test_db_session, employers, report, report_log_entry.import_log_id)

    # confirm expected columns are persisted
    persisted_employer = (
        test_db_session.query(Employer)
        .filter(Employer.account_key == employer_payload["account_key"])
        .one_or_none()
    )
    employer_id = persisted_employer.employer_id

    assert persisted_employer is not None

    validate_employer_persistence(
        employer_payload, persisted_employer, report_log_entry.import_log_id
    )

    persisted_employer_address = dor_persistence_util.get_employer_address(
        test_db_session, employer_id
    )
    assert persisted_employer_address is not None

    persisted_address = dor_persistence_util.get_address(
        test_db_session, persisted_employer_address.address_id
    )
    assert persisted_address is not None

    state, country = dor_employer_lookups
    validate_employer_address_persistence(
        employer_payload, persisted_address, AddressType.BUSINESS, state, country
    )

    assert report.created_employers_count == 1
    assert report.updated_employers_count == 0
    assert report.unmodified_employers_count == 0

    # Verify Logs are correct
    employer_insert_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "INSERT")
        .all()
    )
    assert len(employer_insert_logs) == 1
    assert employer_insert_logs[0].employer_id == employer_id
    # ------
    employer_update_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employer_update_logs) == 0

    # Verify Dor Exemptions were synced
    dor_exemptions: List[EmployerDORExemption] = test_db_session.query(EmployerDORExemption).all()
    assert len(dor_exemptions) == 0


def test_employer_import_with_activity_key(test_db_session, dor_employer_lookups):
    # perform import
    employer_payload = test_data.get_new_employer_with_activity_key()
    employers = [employer_payload]
    report, report_log_entry = get_new_import_report(test_db_session)
    assert report_log_entry.import_log_id is not None

    import_employers(test_db_session, employers, report, report_log_entry.import_log_id)

    # confirm expected columns are persisted
    persisted_employer = (
        test_db_session.query(Employer)
        .filter(Employer.account_key == employer_payload["account_key"])
        .one_or_none()
    )
    employer_id = persisted_employer.employer_id

    assert persisted_employer is not None

    validate_employer_persistence(
        employer_payload, persisted_employer, report_log_entry.import_log_id
    )

    persisted_employer_address = dor_persistence_util.get_employer_address(
        test_db_session, employer_id
    )
    assert persisted_employer_address is not None

    persisted_address = dor_persistence_util.get_address(
        test_db_session, persisted_employer_address.address_id
    )
    assert persisted_address is not None

    state, country = dor_employer_lookups
    validate_employer_address_persistence(
        employer_payload, persisted_address, AddressType.BUSINESS, state, country
    )

    assert report.created_employers_count == 1
    assert report.updated_employers_count == 0
    assert report.unmodified_employers_count == 0

    # Verify Logs are correct
    employer_insert_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "INSERT")
        .all()
    )
    assert len(employer_insert_logs) == 1
    assert employer_insert_logs[0].employer_id == employer_id
    # ------
    employer_update_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employer_update_logs) == 0

    # Verify Dor Exemptions were synced
    dor_exemptions: List[EmployerDORExemption] = test_db_session.query(EmployerDORExemption).all()
    assert len(dor_exemptions) == 1
    assert dor_exemptions[0].employer_id == employer_id
    assert dor_exemptions[0].dor_activity_key == employer_payload["activity_key"]
    assert dor_exemptions[0].decision_cease_date == employer_payload["exemption_cease_date"]
    assert dor_exemptions[0].decision_commence_date == employer_payload["exemption_commence_date"]
    assert dor_exemptions[0].medical_exemption == employer_payload["medical_exemption"]
    assert dor_exemptions[0].family_exemption == employer_payload["family_exemption"]

    assert report.new_exemption_activities == 1
    assert report.updated_exemption_activities == 0
    assert report.unchanged_exemption_activities == 0


def test_employer_import_with_mtc_number(test_db_session):
    employer_payload = test_data.get_new_employer_with_mtc_number()
    employers = [employer_payload]
    report, report_log_entry = get_new_import_report(test_db_session)
    assert report_log_entry.import_log_id is not None

    import_employers(test_db_session, employers, report, report_log_entry.import_log_id)

    assert report.created_employers_count == 1
    assert report.updated_employers_count == 0
    assert report.unmodified_employers_count == 0

    assert report.new_exemption_activities == 1
    assert report.updated_exemption_activities == 0
    assert report.unchanged_exemption_activities == 0

    assert report.employer_with_mtc_number == 1


def test_employer_import_creates_employer_dor_exemption_for_activity_update(test_db_session):
    report, report_log_entry = get_new_import_report(test_db_session)

    # create employer dependency
    employer_payload = test_data.get_new_employer_with_activity_key()
    account_key = employer_payload["account_key"]
    employers = [employer_payload]
    import_employers(test_db_session, employers, report, report_log_entry.import_log_id)

    persisted_employer = (
        test_db_session.query(Employer).filter(Employer.account_key == account_key).one_or_none()
    )
    employer_id = persisted_employer.employer_id

    # Verify Employer Logs are correct
    employer_insert_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "INSERT")
        .all()
    )
    assert len(employer_insert_logs) == 1
    assert employer_insert_logs[0].employer_id == employer_id
    # ------
    employer_update_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employer_update_logs) == 0

    # create extra exemptions
    dor_exemptions = [
        EmployerDORExemption(
            employer_id=employer_id,
            dor_activity_key=1,
            import_log_id=create_log_entry(
                test_db_session, "Employer load", "UPDATES"
            ).import_log_id,
            decision_commence_date=date(2021, 2, 9),
            decision_cease_date=date(2022, 2, 9),
            family_exemption=True,
            medical_exemption=False,
            dor_updated_date=datetime(2021, 1, 1, 12),
        ),
        EmployerDORExemption(
            employer_id=employer_id,
            dor_activity_key=2,
            import_log_id=create_log_entry(
                test_db_session, "Employer load", "UPDATES"
            ).import_log_id,
            decision_commence_date=employer_payload["exemption_commence_date"],
            decision_cease_date=employer_payload["exemption_cease_date"],
            family_exemption=employer_payload["family_exemption"],
            medical_exemption=employer_payload["medical_exemption"],
            dor_updated_date=datetime(2021, 1, 15, 12),
        ),
        EmployerDORExemption(
            employer_id=employer_id,
            dor_activity_key=2,
            import_log_id=create_log_entry(
                test_db_session, "Employer load", "UPDATES"
            ).import_log_id,
            decision_commence_date=date(2021, 3, 9),
            decision_cease_date=date(2022, 2, 9),
            family_exemption=False,
            medical_exemption=False,
            dor_updated_date=datetime(2021, 1, 15, 12),
        ),
    ]
    test_db_session.add_all(dor_exemptions)

    employer_payload = test_data.get_new_employer_with_activity_key()
    employer_payload["activity_key"] = 2
    account_key = employer_payload["account_key"]
    employers = [employer_payload]
    report, report_log_entry = get_new_import_report(test_db_session)
    import_employers(test_db_session, employers, report, report_log_entry.import_log_id)

    # Verify Dor Exemptions were synced
    assert report.new_exemption_activities == 0
    assert report.updated_exemption_activities == 1
    assert report.unchanged_exemption_activities == 0

    dor_exemptions: List[EmployerDORExemption] = test_db_session.query(EmployerDORExemption).all()
    # 1 from first employer import
    # 3 manually created
    # 1 from employer update
    assert len(dor_exemptions) == 5


def test_employer_import_creates_employer_dor_exemption_for_identical_data_and_different_activity(
    test_db_session,
):
    report, report_log_entry = get_new_import_report(test_db_session)

    # create employer dependency
    employer_payload = test_data.get_new_employer_with_activity_key()
    account_key = employer_payload["account_key"]
    employers = [employer_payload]
    import_employers(test_db_session, employers, report, report_log_entry.import_log_id)

    persisted_employer = (
        test_db_session.query(Employer).filter(Employer.account_key == account_key).one_or_none()
    )
    employer_id = persisted_employer.employer_id

    # Verify Employer Logs are correct
    employer_insert_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "INSERT")
        .all()
    )
    assert len(employer_insert_logs) == 1
    assert employer_insert_logs[0].employer_id == employer_id
    # ------
    employer_update_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employer_update_logs) == 0

    # create extra exemptions
    dor_exemptions = [
        EmployerDORExemption(
            dor_activity_key=1,
            import_log_id=create_log_entry(
                test_db_session, "Employer load", "UPDATES"
            ).import_log_id,
            decision_commence_date=date(2021, 2, 9),
            decision_cease_date=date(2022, 2, 9),
            family_exemption=True,
            medical_exemption=False,
            dor_updated_date=datetime(2021, 1, 1, 12),
            employer_id=employer_id,
        ),
        EmployerDORExemption(
            employer_id=employer_id,
            dor_activity_key=2,
            import_log_id=create_log_entry(
                test_db_session, "Employer load", "UPDATES"
            ).import_log_id,
            decision_commence_date=employer_payload["exemption_commence_date"],
            decision_cease_date=employer_payload["exemption_cease_date"],
            family_exemption=employer_payload["family_exemption"],
            medical_exemption=employer_payload["medical_exemption"],
            dor_updated_date=datetime(2021, 1, 15, 12),
        ),
    ]
    test_db_session.add_all(dor_exemptions)

    employer_payload = test_data.get_new_employer_with_activity_key()
    employer_payload["activity_key"] = 3
    account_key = employer_payload["account_key"]
    employers = [employer_payload]
    report, report_log_entry = get_new_import_report(test_db_session)
    import_employers(test_db_session, employers, report, report_log_entry.import_log_id)

    # Verify Dor Exemptions were synced
    assert report.new_exemption_activities == 1
    assert report.updated_exemption_activities == 0
    assert report.unchanged_exemption_activities == 0

    dor_exemptions: List[EmployerDORExemption] = test_db_session.query(EmployerDORExemption).all()
    # 1 from first employer import
    # 2 manually created
    # 1 from employer update
    assert len(dor_exemptions) == 4


def test_employer_import_does_not_create_employer_dor_exemption_for_identical_activity(
    test_db_session,
):
    report, report_log_entry = get_new_import_report(test_db_session)

    # create employer dependency
    employer_payload = test_data.get_new_employer_with_activity_key()
    account_key = employer_payload["account_key"]
    employers = [employer_payload]
    import_employers(test_db_session, employers, report, report_log_entry.import_log_id)

    persisted_employer = (
        test_db_session.query(Employer).filter(Employer.account_key == account_key).one_or_none()
    )
    employer_id = persisted_employer.employer_id

    # Verify Employer Logs are correct
    employer_insert_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "INSERT")
        .all()
    )
    assert len(employer_insert_logs) == 1
    assert employer_insert_logs[0].employer_id == employer_id
    # ------
    employer_update_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employer_update_logs) == 0

    # create extra exemptions
    dor_exemptions = [
        EmployerDORExemption(
            dor_activity_key=1,
            import_log_id=create_log_entry(
                test_db_session, "Employer load", "UPDATES"
            ).import_log_id,
            decision_commence_date=date(2021, 2, 9),
            decision_cease_date=date(2022, 2, 9),
            family_exemption=True,
            medical_exemption=False,
            dor_updated_date=datetime(2021, 1, 1, 12),
            employer_id=employer_id,
        ),
        EmployerDORExemption(
            employer_id=employer_id,
            dor_activity_key=2,
            import_log_id=create_log_entry(
                test_db_session, "Employer load", "UPDATES"
            ).import_log_id,
            decision_commence_date=employer_payload["exemption_commence_date"],
            decision_cease_date=employer_payload["exemption_cease_date"],
            family_exemption=employer_payload["family_exemption"],
            medical_exemption=employer_payload["medical_exemption"],
            dor_updated_date=datetime(2021, 1, 15, 12),
        ),
    ]
    test_db_session.add_all(dor_exemptions)

    employer_payload = test_data.get_new_employer_with_activity_key()
    employer_payload["activity_key"] = 2
    account_key = employer_payload["account_key"]
    employers = [employer_payload]
    report, report_log_entry = get_new_import_report(test_db_session)
    import_employers(test_db_session, employers, report, report_log_entry.import_log_id)

    # Verify Dor Exemptions were synced
    assert report.new_exemption_activities == 0
    assert report.updated_exemption_activities == 0
    assert report.unchanged_exemption_activities == 1

    dor_exemptions: List[EmployerDORExemption] = test_db_session.query(EmployerDORExemption).all()
    # 1 from first employer import
    # 2 manually created
    assert len(dor_exemptions) == 3


def test_employer_import_creates_employer_dor_exemption_for_identical_activity_key_in_same_import(
    test_db_session,
):
    report, report_log_entry = get_new_import_report(test_db_session)

    # create employer dependency
    employer_payload = test_data.get_new_employer_with_activity_key()
    account_key = employer_payload["account_key"]
    employers = [employer_payload]
    import_employers(test_db_session, employers, report, report_log_entry.import_log_id)

    persisted_employer = (
        test_db_session.query(Employer).filter(Employer.account_key == account_key).one_or_none()
    )
    employer_id = persisted_employer.employer_id

    # Verify Employer Logs are correct
    employer_insert_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "INSERT")
        .all()
    )
    assert len(employer_insert_logs) == 1
    assert employer_insert_logs[0].employer_id == employer_id
    # ------
    employer_update_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employer_update_logs) == 0

    # create extra exemptions
    dor_exemptions = [
        EmployerDORExemption(
            dor_activity_key=1,
            import_log_id=create_log_entry(
                test_db_session, "Employer load", "UPDATES"
            ).import_log_id,
            decision_commence_date=date(2021, 2, 9),
            decision_cease_date=date(2022, 2, 9),
            family_exemption=True,
            medical_exemption=False,
            dor_updated_date=datetime(2021, 1, 1, 12),
            employer_id=employer_id,
        ),
        EmployerDORExemption(
            employer_id=employer_id,
            dor_activity_key=2,
            import_log_id=create_log_entry(
                test_db_session, "Employer load", "UPDATES"
            ).import_log_id,
            decision_commence_date=employer_payload["exemption_commence_date"],
            decision_cease_date=employer_payload["exemption_cease_date"],
            family_exemption=employer_payload["family_exemption"],
            medical_exemption=employer_payload["medical_exemption"],
            dor_updated_date=datetime(2021, 1, 15, 12),
        ),
    ]
    test_db_session.add_all(dor_exemptions)

    employer_payload = test_data.get_new_employer_with_activity_key()
    employer_payload["activity_key"] = 3
    account_key = employer_payload["account_key"]
    employer_payload_copy = employer_payload.copy()
    employers = [employer_payload, employer_payload_copy]  # identical
    report, report_log_entry = get_new_import_report(test_db_session)
    import_employers(test_db_session, employers, report, report_log_entry.import_log_id)

    # Verify Dor Exemptions were synced
    assert report.new_exemption_activities == 1
    assert report.updated_exemption_activities == 0
    assert report.unchanged_exemption_activities == 0
    assert report.duplicate_exemptions == 1

    dor_exemptions: List[EmployerDORExemption] = test_db_session.query(EmployerDORExemption).all()
    # 3 from first employer import
    # 1 manually created
    assert len(dor_exemptions) == 4


def test_employer_update_empty_dor_updated_date(test_db_session, dor_employer_lookups):
    # perform initial import
    new_employer_payload = test_data.get_new_employer()
    report, report_log_entry = get_new_import_report(test_db_session)

    import_employers(
        test_db_session, [new_employer_payload], report, report_log_entry.import_log_id
    )

    persisted_employer = (
        test_db_session.query(Employer)
        .filter(Employer.account_key == new_employer_payload["account_key"])
        .one_or_none()
    )

    # set dor_updated_date to None to create the error scenario
    persisted_employer.dor_updated_date = None
    test_db_session.commit()
    test_db_session.refresh(persisted_employer)

    assert persisted_employer.dor_updated_date is None

    import_employers(
        test_db_session, [new_employer_payload], report, report_log_entry.import_log_id
    )

    persisted_employer = (
        test_db_session.query(Employer)
        .filter(Employer.account_key == new_employer_payload["account_key"])
        .one_or_none()
    )

    # Check that the dor_updated_date has been set (update occurred)
    assert persisted_employer.dor_updated_date is not None


def test_mtc_update(test_db_session, dor_employer_lookups):
    employer_payload = test_data.new_employer_with_activity_key
    report, report_log_entry = get_new_import_report(test_db_session)
    assert report_log_entry.import_log_id is not None

    import_employers(test_db_session, [employer_payload], report, report_log_entry.import_log_id)

    # confirm expected columns are persisted
    persisted_employer = (
        test_db_session.query(Employer)
        .filter(Employer.account_key == employer_payload["account_key"])
        .one_or_none()
    )

    assert persisted_employer is not None

    assert report.created_employers_count == 1
    assert report.updated_employers_count == 0
    assert report.unmodified_employers_count == 0
    assert persisted_employer.mtc_number is None

    # perform import_employer with mtc_number to check if it updates the mtc_number
    employer_payload = test_data.new_employer_with_activity_key
    employer_payload["mtc_number"] = "PFM01********9"
    report, report_log_entry = get_new_import_report(test_db_session)

    import_employers(test_db_session, [employer_payload], report, report_log_entry.import_log_id)
    assert report_log_entry.import_log_id is not None

    # confirm expected columns are persisted
    persisted_employer = (
        test_db_session.query(Employer)
        .filter(Employer.account_key == employer_payload["account_key"])
        .one_or_none()
    )

    assert report.created_employers_count == 0
    assert report.updated_employers_count == 1
    assert report.unmodified_employers_count == 0
    assert persisted_employer.mtc_number == employer_payload["mtc_number"]


def test_import_dor_should_create_employer_when_duplicate_mtc_already_exists(
    test_db_session, dor_employer_lookups, initialize_factories_session
):
    EmployerFactory.create(mtc_number="PFM10000000001")

    # This employer should get created with MTC number
    employer_payload_1 = {
        **test_data.get_new_employer_with_activity_key(),
        "mtc_number": "PFM10000000001",
        "account_key": "***********",
        "fein": "*********",
    }

    report, report_log_entry = get_new_import_report(test_db_session)
    assert report_log_entry.import_log_id is not None

    import_employers(test_db_session, [employer_payload_1], report, report_log_entry.import_log_id)

    # confirm expected columns are persisted
    persisted_employer = (
        test_db_session.query(Employer)
        .filter(Employer.account_key == employer_payload_1["account_key"])
        .one_or_none()
    )

    assert persisted_employer is not None

    assert report.created_employers_count == 1
    assert report.updated_employers_count == 0
    assert report.unmodified_employers_count == 0
    assert report.employer_with_mtc_number == 1
    assert report.employers_with_unavailable_mtc_number_count == 1
    assert persisted_employer.mtc_number is None


def test_import_dor_should_update_employer_when_duplicate_mtc_already_exists(
    test_db_session, dor_employer_lookups, initialize_factories_session
):

    # This employer should get created with MTC number
    employer_payload_1 = {
        **test_data.get_new_employer_with_activity_key(),
        "mtc_number": "PFM10000000001",
        "account_key": "***********",
        "fein": "*********",
    }

    # This employer should get created without MTC number, we will try to update it with incorrect MTC later
    employer_payload_2 = {
        **test_data.get_new_employer_with_activity_key(),
        "mtc_number": None,
        "account_key": "***********",
        "fein": "*********",
    }

    report, report_log_entry = get_new_import_report(test_db_session)
    assert report_log_entry.import_log_id is not None

    import_employers(
        test_db_session,
        [employer_payload_1, employer_payload_2],
        report,
        report_log_entry.import_log_id,
    )

    # confirm expected columns are persisted
    persisted_employer_1 = (
        test_db_session.query(Employer)
        .filter(Employer.account_key == employer_payload_1["account_key"])
        .one_or_none()
    )
    persisted_employer_2 = (
        test_db_session.query(Employer)
        .filter(Employer.account_key == employer_payload_2["account_key"])
        .one_or_none()
    )

    assert persisted_employer_1 is not None
    assert persisted_employer_2 is not None

    assert report.created_employers_count == 2
    assert report.updated_employers_count == 0
    assert report.unmodified_employers_count == 0
    assert report.employer_with_mtc_number == 1
    assert report.employers_with_unavailable_mtc_number_count == 0

    assert persisted_employer_1.mtc_number is not None
    assert persisted_employer_2.mtc_number is None

    # Try to update the employer that was just created, setting their MTC number to MTC on the other employer

    employer_payload_2 = {
        **test_data.get_updated_employer(),
        "account_key": "***********",
        "fein": "*********",
        "mtc_number": persisted_employer_1.mtc_number,
    }

    report, report_log_entry = get_new_import_report(test_db_session)
    assert report_log_entry.import_log_id is not None

    import_employers(test_db_session, [employer_payload_2], report, report_log_entry.import_log_id)

    # confirm expected columns are persisted
    persisted_employer_2 = (
        test_db_session.query(Employer)
        .filter(Employer.account_key == employer_payload_2["account_key"])
        .one_or_none()
    )

    assert persisted_employer_2 is not None

    assert report.created_employers_count == 0
    assert report.updated_employers_count == 1
    assert report.unmodified_employers_count == 0
    assert report.employer_with_mtc_number == 1
    assert report.employers_with_unavailable_mtc_number_count == 1
    assert persisted_employer_2.mtc_number is None


def test_import_dor_should_create_employers_when_duplicate_mtc_in_dor_file(
    test_db_session, dor_employer_lookups
):
    # This employer should get created with MTC number
    employer_payload_1 = {
        **test_data.get_new_employer_with_activity_key(),
        "mtc_number": "PFM10000000001",
        "account_key": "***********",
        "fein": "*********",
    }
    # This employer should get created without MTC number
    employer_payload_2 = {
        **test_data.get_new_employer_with_activity_key(),
        "mtc_number": "PFM10000000001",
        "account_key": "***********",
        "fein": "*********",
    }

    report, report_log_entry = get_new_import_report(test_db_session)
    assert report_log_entry.import_log_id is not None

    import_employers(
        test_db_session,
        [employer_payload_1, employer_payload_2],
        report,
        report_log_entry.import_log_id,
    )

    # confirm expected columns are persisted
    persisted_employer = (
        test_db_session.query(Employer)
        .filter(Employer.account_key == employer_payload_1["account_key"])
        .one_or_none()
    )
    persisted_employer_2 = (
        test_db_session.query(Employer)
        .filter(Employer.account_key == employer_payload_2["account_key"])
        .one_or_none()
    )

    assert persisted_employer is not None
    assert persisted_employer_2 is not None

    assert report.created_employers_count == 2
    assert report.updated_employers_count == 0
    assert report.unmodified_employers_count == 0
    assert report.employer_with_mtc_number == 2
    assert report.employers_with_unavailable_mtc_number_count == 1
    assert persisted_employer.mtc_number == employer_payload_1["mtc_number"]
    assert persisted_employer_2.mtc_number is None


def test_mtc_mismatch(test_db_session, dor_employer_lookups, caplog):
    caplog.set_level(massgov.pfml.util.logging.INFO)
    employer_payload = test_data.new_employer_with_activity_key
    employer_payload["mtc_number"] = "PFM01********9"
    report, report_log_entry = get_new_import_report(test_db_session)

    import_employers(test_db_session, [employer_payload], report, report_log_entry.import_log_id)

    # confirm expected columns are persisted
    persisted_employer = (
        test_db_session.query(Employer)
        .filter(Employer.account_key == employer_payload["account_key"])
        .one_or_none()
    )

    assert persisted_employer is not None

    assert report.created_employers_count == 1
    assert report.updated_employers_count == 0
    assert report.unmodified_employers_count == 0
    assert persisted_employer.mtc_number == "PFM01********9"

    # perform import_employer with mtc_number to check that it does not update the mtc_number because of the mismatch
    employer_payload = test_data.new_employer_with_activity_key
    employer_payload["mtc_number"] = "PFM98765432110"
    report, report_log_entry = get_new_import_report(test_db_session)

    import_employers(test_db_session, [employer_payload], report, report_log_entry.import_log_id)

    # Verify the log output
    expected_log_message = "Employer MTC number mismatch"
    assert expected_log_message in caplog.text
    log_record = next(record for record in caplog.records if record.message == expected_log_message)
    assert log_record.account_key == employer_payload["account_key"]
    assert log_record.employer_mtc_number == "PFM01********9"
    assert log_record.dor_mtc_number == "PFM98765432110"

    # confirm expected columns are persisted
    persisted_employer = (
        test_db_session.query(Employer)
        .filter(Employer.account_key == employer_payload["account_key"])
        .one_or_none()
    )

    assert report.created_employers_count == 0
    assert report.updated_employers_count == 0
    assert report.unmodified_employers_count == 1
    assert persisted_employer.mtc_number == "PFM01********9"


def test_employer_update(test_db_session, dor_employer_lookups):
    # perform initial import
    new_employer_payload = test_data.get_new_employer()
    report, report_log_entry = get_new_import_report(test_db_session)

    import_employers(
        test_db_session, [new_employer_payload], report, report_log_entry.import_log_id
    )
    persisted_employer = (
        test_db_session.query(Employer)
        .filter(Employer.account_key == new_employer_payload["account_key"])
        .one_or_none()
    )
    employer_id = persisted_employer.employer_id

    assert report.created_employers_count == 1
    assert report.updated_employers_count == 0

    # Verify Logs are correct (1)
    employer_insert_logs1: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "INSERT")
        .all()
    )
    employer_insert_logs_ids1 = [x.employer_push_to_fineos_queue_id for x in employer_insert_logs1]
    assert len(employer_insert_logs1) == 1
    assert employer_insert_logs1[0].employer_id == employer_id
    # ------
    employer_update_logs1: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employer_update_logs1) == 0
    # ------------

    # confirm unchanged update date will be skipped
    report2, report_log_entry2 = get_new_import_report(test_db_session)
    updated_employer_payload_to_skip = test_data.get_updated_employer_except_update_date()
    import_employers(
        test_db_session,
        [updated_employer_payload_to_skip],
        report2,
        report_log_entry2.import_log_id,
    )
    existing_employer = test_db_session.get(Employer, employer_id)

    with pytest.raises(AssertionError):
        validate_employer_persistence(
            updated_employer_payload_to_skip, existing_employer, report_log_entry.import_log_id
        )

    assert report2.updated_employers_count == 0
    assert report2.unmodified_employers_count == 1
    assert existing_employer.latest_import_log_id == report_log_entry.import_log_id

    # Verify Logs are correct (2)
    employer_insert_logs2: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(
            EmployerPushToFineosQueue.action == "INSERT",
            not_(
                EmployerPushToFineosQueue.employer_push_to_fineos_queue_id.in_(
                    employer_insert_logs_ids1
                )
            ),
        )
        .all()
    )
    assert len(employer_insert_logs2) == 0
    # ------
    employer_update_logs2: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employer_update_logs2) == 0
    # ------------

    # confirm expected columns are now updated
    report3, report_log_entry3 = get_new_import_report(test_db_session)
    updated_employer_payload = test_data.get_updated_employer()
    import_employers(
        test_db_session, [updated_employer_payload], report3, report_log_entry3.import_log_id
    )
    persisted_employer = test_db_session.get(Employer, employer_id)
    assert persisted_employer is not None

    validate_employer_persistence(
        updated_employer_payload, persisted_employer, report_log_entry3.import_log_id
    )

    persisted_employer_address = dor_persistence_util.get_employer_address(
        test_db_session, employer_id
    )
    assert persisted_employer_address is not None

    persisted_address = dor_persistence_util.get_address(
        test_db_session, persisted_employer_address.address_id
    )
    assert persisted_address is not None

    state, country = dor_employer_lookups
    validate_employer_address_persistence(
        updated_employer_payload, persisted_address, AddressType.BUSINESS, state, country
    )

    assert report3.updated_employers_count == 1

    # Verify Logs are correct (3)
    employer_insert_logs3: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(
            EmployerPushToFineosQueue.action == "INSERT",
            not_(
                EmployerPushToFineosQueue.employer_push_to_fineos_queue_id.in_(
                    employer_insert_logs_ids1
                )
            ),
        )
        .all()
    )
    assert len(employer_insert_logs3) == 0
    # ------
    employer_update_logs3: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employer_update_logs3) == 1
    assert employer_update_logs3[0].employer_id == persisted_employer.employer_id
    assert employer_update_logs3[0].family_exemption == new_employer_payload["family_exemption"]
    assert employer_update_logs3[0].medical_exemption == new_employer_payload["medical_exemption"]
    assert (
        employer_update_logs3[0].exemption_commence_date
        == new_employer_payload["exemption_commence_date"]
    )
    assert (
        employer_update_logs3[0].exemption_cease_date
        == new_employer_payload["exemption_cease_date"]
    )
    # ------------


def test_employer_create_and_update_in_same_run(test_db_session):
    new_employer_payload = test_data.get_new_employer()
    updated_employer_payload = test_data.get_updated_employer()
    report, report_log_entry = get_new_import_report(test_db_session)

    import_employers(
        test_db_session,
        [new_employer_payload, updated_employer_payload],
        report,
        report_log_entry.import_log_id,
    )
    persisted_employer = (
        test_db_session.query(Employer)
        .filter(Employer.account_key == new_employer_payload["account_key"])
        .one_or_none()
    )
    employer_id = persisted_employer.employer_id

    assert report.created_employers_count == 1
    assert report.updated_employers_count == 1
    assert report.unmodified_employers_count == 0

    # confirm expected columns are now updated
    persisted_employer = test_db_session.get(Employer, employer_id)
    assert persisted_employer is not None

    validate_employer_persistence(
        updated_employer_payload, persisted_employer, report_log_entry.import_log_id
    )

    # Verify Logs are correct
    employer_insert_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "INSERT")
        .all()
    )
    assert len(employer_insert_logs) == 1
    assert employer_insert_logs[0].employer_id == persisted_employer.employer_id

    # ------
    employer_update_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employer_update_logs) == 1
    assert employer_update_logs[0].employer_id == persisted_employer.employer_id

    assert employer_update_logs[0].family_exemption == new_employer_payload["family_exemption"]
    assert employer_update_logs[0].medical_exemption == new_employer_payload["medical_exemption"]
    assert (
        employer_update_logs[0].exemption_commence_date
        == new_employer_payload["exemption_commence_date"]
    )
    assert (
        employer_update_logs[0].exemption_cease_date == new_employer_payload["exemption_cease_date"]
    )
    # ------------


def test_employer_address(test_db_session):
    employer_international_address = test_data.get_employer_international_address()
    employer_invalid_country = test_data.get_employer_invalid_country()

    report, report_log_entry = get_new_import_report(test_db_session)

    import_employers(
        test_db_session,
        [employer_international_address, employer_invalid_country],
        report,
        report_log_entry.import_log_id,
    )

    assert report.created_employers_count == 2

    invalid_country_employer = (
        test_db_session.query(Employer)
        .filter(Employer.account_key == employer_invalid_country["account_key"])
        .one()
    )
    assert invalid_country_employer.addresses[0].address.country_id is None

    # confirm expected columns are now updated
    valid_country_employer = (
        test_db_session.query(Employer)
        .filter(Employer.account_key == employer_international_address["account_key"])
        .one_or_none()
    )
    persisted_address = valid_country_employer.addresses[0].address

    assert persisted_address.geo_state_id is None
    assert (
        persisted_address.geo_state_text == employer_international_address["employer_address_state"]
    )
    assert persisted_address.country_id == Country.get_id(
        employer_international_address["employer_address_country"]
    )

    # Verify Logs are correct

    employer_insert_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "INSERT")
        .all()
    )
    assert len(employer_insert_logs) == 2

    found_employer_1 = next(
        x for x in employer_insert_logs if x.employer_id == invalid_country_employer.employer_id
    )
    assert found_employer_1

    found_employer_2 = next(
        x for x in employer_insert_logs if x.employer_id == valid_country_employer.employer_id
    )
    assert found_employer_2

    # ------
    employer_update_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employer_update_logs) == 0
    # ------------


def test_employer_invalid_fein(test_db_session):
    employer_data_invalid_type = copy.deepcopy(test_data.new_employer)
    employer_data_invalid_length = copy.deepcopy(test_data.new_employer)
    employer_data_invalid_type["fein"] = "abcdefghi"
    employer_data_invalid_type["account_key"] = "***********"
    employer_data_invalid_length["fein"] = "********"

    before_employer_count = test_db_session.query(Employer).count()

    report, report_log_entry = get_new_import_report(test_db_session)
    import_employers(
        test_db_session,
        [employer_data_invalid_type, employer_data_invalid_length],
        report,
        report_log_entry.import_log_id,
    )

    assert report.created_employers_count == 0
    assert len(report.invalid_employer_feins_by_account_key) == 2
    assert employer_data_invalid_type["account_key"] in report.invalid_employer_feins_by_account_key
    assert (
        employer_data_invalid_length["account_key"] in report.invalid_employer_feins_by_account_key
    )
    after_employer_count = test_db_session.query(Employer).count()
    assert before_employer_count == after_employer_count

    # Verify Logs are correct
    employer_insert_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "INSERT")
        .all()
    )
    assert len(employer_insert_logs) == 0
    # ------
    employer_update_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employer_update_logs) == 0
    # ------------


def test_record_employees_with_new_employers_and_log_update_new_employers(test_db_session):
    # Employee generate helper
    def generate_employee_and_wage_item(id, employer):
        employee = next(generator.generate_single_employee(id, [employer]))
        # convert quarter to date
        employee["filing_period"] = employee["filing_period"].as_date()
        return employee

    # Create two employers
    employer1 = generator.generate_single_employer(1)
    employer2 = generator.generate_single_employer(2)
    employers = [employer1, employer2]
    for employer in employers:
        employer["family_exemption"] = employer["medical_exemption"] = False
        employer["exemption_commence_date"] = generator.NO_EXEMPTION_DATE
        employer["exemption_cease_date"] = generator.NO_EXEMPTION_DATE

    report, report_log_entry = get_new_import_report(test_db_session)
    import_employers(test_db_session, employers, report, report_log_entry.import_log_id)

    created_employers = test_db_session.query(Employer).all()
    assert len(created_employers) == 2

    # Verify Employer Logs are correct (1)
    employer_insert_logs1: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "INSERT")
        .all()
    )
    assert len(employer_insert_logs1) == 2

    found_employer_1 = next(
        x for x in employer_insert_logs1 if x.employer_id == created_employers[0].employer_id
    )
    assert found_employer_1

    found_employer_2 = next(
        x for x in employer_insert_logs1 if x.employer_id == created_employers[1].employer_id
    )
    assert found_employer_2

    # ------
    employer_update_logs1: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employer_update_logs1) == 0
    # ------------

    # Create two employees
    employee1 = generate_employee_and_wage_item(1, employer1)
    employee2 = generate_employee_and_wage_item(2, employer2)

    employee_ssns_to_id_created_in_current_import_run = {}

    report1, report_log_entry1 = get_new_import_report(test_db_session)
    wage_import1 = WageImporter(
        test_db_session,
        [employee1, employee2],
        employee_ssns_to_id_created_in_current_import_run,
        report1,
        list(),
        report_log_entry1.import_log_id,
        WagesAndContributionsDatasource.DFML_REPORTED_WAGES,
    )
    wage_import1.import_employees_and_wage_data(record_new_employees=True, should_update_wages=True)

    assert report1.created_employees_count == 2
    assert report1.logged_employees_for_new_employer == 2

    created_employees = test_db_session.query(Employee).all()
    assert len(created_employees) == 2

    created_wages = test_db_session.query(WagesAndContributions).all()
    assert len(created_wages) == 2

    # Verify Employee Logs are correct (1)
    employee_insert_logs1: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "INSERT")
        .all()
    )
    employee_insert_log_ids1 = [x.employee_push_to_fineos_queue_id for x in employee_insert_logs1]

    assert len(employee_insert_logs1) == 2

    found_employee_1 = next(
        x for x in employee_insert_logs1 if x.employee_id == created_employees[0].employee_id
    )
    assert found_employee_1

    found_employee_2 = next(
        x for x in employee_insert_logs1 if x.employee_id == created_employees[1].employee_id
    )
    assert found_employee_2
    # ------
    employee_update_logs1: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employee_update_logs1) == 0
    # ------
    employee_employer_logs1: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE_NEW_EMPLOYER")
        .all()
    )
    assert len(employee_employer_logs1) == report1.created_employees_count
    # ------------

    # Simulate a wage entry for an existing employee with a new employer
    employee2_employer1 = generate_employee_and_wage_item(2, employer1)
    employee2_employer1_second_entry = generate_employee_and_wage_item(2, employer1)
    employee3 = generate_employee_and_wage_item(3, employer1)
    employee_ssns_to_id_created_in_current_import_run = {}

    report2, report_log_entry2 = get_new_import_report(test_db_session)
    wage_import2 = WageImporter(
        test_db_session,
        [employee1, employee2, employee2_employer1, employee2_employer1_second_entry, employee3],
        employee_ssns_to_id_created_in_current_import_run,
        report2,
        list(),
        report_log_entry2.import_log_id,
        WagesAndContributionsDatasource.DFML_REPORTED_WAGES,
    )
    wage_import2.import_employees_and_wage_data(record_new_employees=True, should_update_wages=True)

    assert report2.unmodified_employees_count == 2
    assert report2.created_employees_count == 1
    assert report2.logged_employees_for_new_employer == 2

    employee_with_new_employer = dor_persistence_util.get_employees_by_ssn(
        test_db_session, [employee2_employer1["employee_ssn"]]
    )[0]

    created_employee = dor_persistence_util.get_employees_by_ssn(
        test_db_session, [employee3["employee_ssn"]]
    )[0]

    # Verify Employee Logs are correct (2)
    employee_insert_logs2: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(
            EmployeePushToFineosQueue.action == "INSERT",
            not_(
                EmployeePushToFineosQueue.employee_push_to_fineos_queue_id.in_(
                    employee_insert_log_ids1
                )
            ),
        )
        .all()
    )
    assert len(employee_insert_logs2) == 1
    assert employee_insert_logs2[0].employee_id == created_employee.employee_id
    # ------
    employee_update_logs2: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employee_update_logs2) == 0
    # ------
    employee_employer_logs2: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE_NEW_EMPLOYER")
        .all()
    )
    assert len(employee_employer_logs2) == 4
    assert employee_employer_logs2[0].employer_id == found_employer_1.employer_id
    assert employee_employer_logs2[0].employee_id == found_employee_1.employee_id

    assert employee_employer_logs2[1].employer_id == created_employers[1].employer_id
    assert employee_employer_logs2[1].employee_id == employee_with_new_employer.employee_id

    assert employee_employer_logs2[2].employer_id == created_employers[0].employer_id
    assert employee_employer_logs2[2].employee_id == employee_with_new_employer.employee_id

    assert employee_employer_logs2[3].employer_id == created_employers[0].employer_id
    assert employee_employer_logs2[3].employee_id == created_employee.employee_id
    # ------------


def test_generate_and_upload_dor_employee_integrity_file(mock_file_util, mock_dor_config):
    timestamp_prefix = get_now_us_eastern().strftime("%Y-%m-%d-%H-%M-%S")
    dor_employee_integrity_date_stamped_file_name = (
        f"{timestamp_prefix}-{Constants.EMPLOYEE_INTEGRITY_FILE_NAME}"
    )

    dor_employee_integrity_list = [
        {
            "EmployeeSSN": test_data.get_new_employee_wage_data()["employee_ssn"],
            "DifferenceMeasure": fake.random_number(digits=2) / 100.0,
            "CurrentPfmlFirstNameValue": test_data.get_new_employee_wage_data()[
                "employee_first_name"
            ],
            "CurrentPfmlLastNameValue": test_data.get_new_employee_wage_data()[
                "employee_last_name"
            ],
            "NewFirstNameValue": test_data.get_updated_employee_wage_data()["employee_first_name"],
            "NewLastNameValue": test_data.get_updated_employee_wage_data()["employee_last_name"],
        }
    ]

    timestamp_prefix = get_now_us_eastern().strftime("%Y-%m-%d-%H-%M-%S")
    dor_employee_integrity_date_stamped_file_name = (
        f"{timestamp_prefix}-{Constants.EMPLOYEE_INTEGRITY_FILE_NAME}"
    )

    generate_and_upload_dor_employee_integrity_file(dor_employee_integrity_list)

    mock_file_util.create_csv_from_list.assert_called_once_with(
        dor_employee_integrity_list,
        Constants.EMPLOYEE_INTEGRITY_FILE_FIELDS,
        dor_employee_integrity_date_stamped_file_name,
    )
    mock_dor_config.build_archive_path.assert_called_once_with(
        mock_dor_config.get_import_s3_config().DOR_EMPLOYEE_INTEGRITY_FILE_PATH_PREFIX,
        Constants.EMPLOYEE_INTEGRITY_FOLDER_PATH,
        f"{dor_employee_integrity_date_stamped_file_name}.csv",
    )
    mock_file_util.upload_file.assert_called_once_with(
        str(mock_file_util.create_csv_from_list.return_value),
        mock_dor_config.build_archive_path.return_value,
    )


def test_send_employee_integrity_report_email(mock_send_email, mock_s3_bucket):
    prefix = "s3://" + mock_s3_bucket
    dor_report_path = prefix + Constants.EMPLOYEE_INTEGRITY_FOLDER_PATH
    assert mock_send_email.called_once_with(dor_report_path)


def get_new_import_report(test_db_session):
    report = dor_shared_utils.ImportReport()
    report_log_entry = massgov.pfml.util.batch.log.create_log_entry(
        test_db_session, "DOR", "Initial", source_context=__name__, report=report
    )

    return report, report_log_entry


def test_employee_wage_data_create(
    test_db_session, dor_employer_lookups, initialize_factories_session
):
    # create empty report
    report, report_log_entry = get_new_import_report(test_db_session)

    # create employer dependency
    employer_payload = test_data.get_new_employer()
    account_key = employer_payload["account_key"]
    employers = [employer_payload]
    import_employers(test_db_session, employers, report, report_log_entry.import_log_id)

    persisted_employer = (
        test_db_session.query(Employer).filter(Employer.account_key == account_key).one_or_none()
    )
    employer_id = persisted_employer.employer_id

    # Verify Employer Logs are correct
    employer_insert_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "INSERT")
        .all()
    )
    assert len(employer_insert_logs) == 1
    assert employer_insert_logs[0].employer_id == employer_id
    # ------
    employer_update_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employer_update_logs) == 0
    # ------------

    # perform employee and wage import
    employee_wage_data_payload = test_data.get_new_employee_wage_data()

    employee_id_by_ssn = {}
    wage_import = WageImporter(
        test_db_session,
        [employee_wage_data_payload],
        employee_id_by_ssn,
        report,
        list(),
        report_log_entry.import_log_id,
        WagesAndContributionsDatasource.DFML_REPORTED_WAGES,
    )
    wage_import.import_employees_and_wage_data(record_new_employees=True, should_update_wages=True)

    employee_id = employee_id_by_ssn[employee_wage_data_payload["employee_ssn"]]
    persisted_employee = test_db_session.get(Employee, employee_id)

    assert persisted_employee is not None
    validate_employee_persistence(
        employee_wage_data_payload, persisted_employee, report_log_entry.import_log_id
    )

    persisted_wage_info = (
        dor_persistence_util.get_wages_and_contributions_by_employee_id_and_filling_period(
            test_db_session, employee_id, employer_id, employee_wage_data_payload["filing_period"]
        )
    )

    assert persisted_wage_info is not None
    validate_wage_persistence(
        employee_wage_data_payload, persisted_wage_info, report_log_entry.import_log_id
    )

    assert report.created_employees_count == 1
    assert report.updated_employees_count == 0

    assert report.created_wages_and_contributions_count == 1
    assert report.updated_wages_and_contributions_count == 0

    # Verify Employee Logs are correct
    employee_insert_logs: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "INSERT")
        .all()
    )
    assert len(employee_insert_logs) == 1
    assert employee_insert_logs[0].employee_id == employee_id
    # ------
    employee_update_logs: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employee_update_logs) == 0
    # ------
    employee_employer_logs: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE_NEW_EMPLOYER")
        .all()
    )
    assert employee_employer_logs[0].employee_id == employee_id
    assert employee_employer_logs[0].employer_id == employer_id
    assert len(employee_employer_logs) == 1

    # test one_or_none count
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create()
    WagesAndContributionsFactory.create(
        employee=employee, employer=employer, filing_period=date(2020, 9, 30)
    )
    WagesAndContributionsFactory.create(
        employee=employee, employer=employer, filing_period=date(2020, 9, 30)
    )
    dor_persistence_util.get_wages_and_contributions_by_employee_id_and_filling_period(
        test_db_session,
        employee.employee_id,
        employer.employer_id,
        date(2020, 9, 30),
        report.exception_count,
    )
    assert report.exception_count["MultipleResultsFound"] == 1
    # ------------


def test_duplicate_employer_fein(test_db_session, initialize_factories_session):
    # create empty report
    report, report_log_entry = get_new_import_report(test_db_session)
    EmployerFactory.create(employer_fein="*********", fineos_employer_id="123")
    EmployerFactory.create(employer_fein="*********", fineos_employer_id="124")
    test_db_session.commit()

    dor_persistence_util.get_employer_by_fein(test_db_session, "*********", report.exception_count)

    assert report.exception_count["MultipleResultsFound"] == 1


def test_employee_wage_data_update(test_db_session, dor_employer_lookups, monkeypatch):
    # create empty report
    report, report_log_entry = get_new_import_report(test_db_session)

    # create employer dependency
    employer_payload = test_data.get_new_employer()
    account_key = employer_payload["account_key"]
    employers = [employer_payload]
    import_employers(test_db_session, employers, report, report_log_entry.import_log_id)

    persisted_employer = (
        test_db_session.query(Employer).filter(Employer.account_key == account_key).one_or_none()
    )
    employer_id = persisted_employer.employer_id

    # Verify Employer Logs are correct
    employer_insert_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "INSERT")
        .all()
    )
    assert len(employer_insert_logs) == 1
    assert employer_insert_logs[0].employer_id == employer_id
    # ------
    employer_update_logs: List[EmployerPushToFineosQueue] = (
        test_db_session.query(EmployerPushToFineosQueue)
        .filter(EmployerPushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employer_update_logs) == 0
    # ------------

    # perform initial employee and wage import
    employee_wage_data_payload = test_data.get_new_employee_wage_data()

    employee_id_by_ssn = {}
    wage_import1 = WageImporter(
        test_db_session,
        [employee_wage_data_payload],
        employee_id_by_ssn,
        report,
        list(),
        report_log_entry.import_log_id,
        WagesAndContributionsDatasource.DFML_REPORTED_WAGES,
    )
    wage_import1.import_employees_and_wage_data(record_new_employees=True, should_update_wages=True)
    employee_id = employee_id_by_ssn[employee_wage_data_payload["employee_ssn"]]

    assert report.created_employees_count == 1
    assert report.created_wages_and_contributions_count == 1
    assert report.updated_employees_count == 0
    assert report.unmodified_employees_count == 0
    assert report.updated_wages_and_contributions_count == 0

    # Verify a matching WagesAndContributionsHistory record exists only if enabled
    assert test_db_session.query(WagesAndContributionsHistory).count() == 1

    # Verify Employee Logs are correct (1)
    employee_insert_logs1: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "INSERT")
        .all()
    )
    employee_insert_log_ids1 = [x.employee_push_to_fineos_queue_id for x in employee_insert_logs1]
    assert len(employee_insert_logs1) == 1
    assert employee_insert_logs1[0].employee_id == employee_id
    # ------
    employee_update_logs1: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employee_update_logs1) == 0
    # ------
    employee_employer_logs1: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE_NEW_EMPLOYER")
        .all()
    )
    employee_employer_log_ids1 = [
        x.employee_push_to_fineos_queue_id for x in employee_employer_logs1
    ]
    assert len(employee_employer_logs1) == 1
    assert employee_employer_logs1[0].employee_id == employee_id
    assert employee_employer_logs1[0].employer_id == employer_id
    # ------------

    # confirm that existing employee info is not updated when there is no change
    report2, report_log_entry2 = get_new_import_report(test_db_session)

    wage_import2 = WageImporter(
        test_db_session,
        [employee_wage_data_payload],
        EMPTY_SSN_TO_EMPLOYEE_ID_MAP,
        report2,
        list(),
        report_log_entry2.import_log_id,
        WagesAndContributionsDatasource.DFML_REPORTED_WAGES,
    )
    wage_import2.import_employees_and_wage_data(record_new_employees=True, should_update_wages=True)

    persisted_employee = test_db_session.get(Employee, employee_id)
    assert persisted_employee is not None

    validate_employee_persistence(
        employee_wage_data_payload, persisted_employee, report_log_entry.import_log_id
    )

    assert report2.created_employees_count == 0
    assert report2.created_wages_and_contributions_count == 0
    assert report2.updated_employees_count == 0
    assert report2.unmodified_employees_count == 1
    assert report2.updated_wages_and_contributions_count == 0
    assert report2.unmodified_wages_and_contributions_count == 1

    # Verify Employee Logs are correct (2)
    employee_insert_logs2: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(
            EmployeePushToFineosQueue.action == "INSERT",
            not_(
                EmployeePushToFineosQueue.employee_push_to_fineos_queue_id.in_(
                    employee_insert_log_ids1
                )
            ),
        )
        .all()
    )
    assert len(employee_insert_logs2) == 0
    # ------
    employee_update_logs2: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employee_update_logs2) == 0
    # ------
    employee_employer_logs2: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(
            EmployeePushToFineosQueue.action == "UPDATE_NEW_EMPLOYER",
            not_(
                EmployeePushToFineosQueue.employee_push_to_fineos_queue_id.in_(
                    employee_employer_log_ids1
                )
            ),
        )
        .all()
    )
    assert len(employee_employer_logs2) == 0
    # ------------

    # confirm updates are persisted
    report3, report_log_entry3 = get_new_import_report(test_db_session)
    updated_employee_wage_data_payload = test_data.get_updated_employee_wage_data()
    wage_import3 = WageImporter(
        test_db_session,
        [updated_employee_wage_data_payload],
        EMPTY_SSN_TO_EMPLOYEE_ID_MAP,
        report3,
        list(),
        report_log_entry3.import_log_id,
        WagesAndContributionsDatasource.DFML_REPORTED_WAGES,
    )
    wage_import3.import_employees_and_wage_data(record_new_employees=True, should_update_wages=True)

    persisted_employee = test_db_session.get(Employee, employee_id)

    assert persisted_employee is not None
    validate_employee_persistence(
        updated_employee_wage_data_payload, persisted_employee, report_log_entry3.import_log_id
    )

    persisted_wage_info = (
        dor_persistence_util.get_wages_and_contributions_by_employee_id_and_filling_period(
            test_db_session,
            employee_id,
            employer_id,
            updated_employee_wage_data_payload["filing_period"],
        )
    )

    assert persisted_wage_info is not None
    validate_wage_persistence(
        updated_employee_wage_data_payload, persisted_wage_info, report_log_entry3.import_log_id
    )

    assert report3.created_employees_count == 0
    assert report3.created_wages_and_contributions_count == 0
    assert report3.updated_employees_count == 1
    assert report3.unmodified_employees_count == 0
    assert report3.updated_wages_and_contributions_count == 1
    assert report3.unmodified_wages_and_contributions_count == 0

    # Verify WagesAndContributionsHistory has been persisted
    wage_history_records = (
        test_db_session.query(WagesAndContributionsHistory)
        .order_by(WagesAndContributionsHistory.created_at.asc())
        .all()
    )
    assert len(wage_history_records) == 2

    assert wage_history_records[0].wage_and_contribution == persisted_wage_info

    # Validate that the data captured is the previous data
    validate_wage_history_persistence(
        employee_wage_data_payload, wage_history_records[0], report_log_entry.import_log_id
    )

    # Verify Employee Logs are correct (3)
    employee_insert_logs3: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(
            EmployeePushToFineosQueue.action == "INSERT",
            not_(
                EmployeePushToFineosQueue.employee_push_to_fineos_queue_id.in_(
                    employee_insert_log_ids1
                )
            ),
        )
        .all()
    )
    assert len(employee_insert_logs3) == 0
    # ------
    employee_update_logs3: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(EmployeePushToFineosQueue.action == "UPDATE")
        .all()
    )
    assert len(employee_update_logs3) == 1
    assert employee_update_logs3[0].employee_id == employee_id
    # ------

    employee_employer_logs3: List[EmployeePushToFineosQueue] = (
        test_db_session.query(EmployeePushToFineosQueue)
        .filter(
            EmployeePushToFineosQueue.action == "UPDATE_NEW_EMPLOYER",
            not_(
                EmployeePushToFineosQueue.employee_push_to_fineos_queue_id.in_(
                    employee_employer_log_ids1
                )
            ),
        )
        .all()
    )

    assert len(employee_employer_logs3) == 0
    # ------------


def test_employee_name_change(
    test_db_session, employee_factory, claim_factory, employee_wage_factory
):
    employee_1 = employee_factory()
    claim_factory(employee=employee_1)

    wage_data_1 = employee_wage_factory({"employee_first_name": "jabroni"}, employee=employee_1)

    report_1, report_log_entry_1 = get_new_import_report(test_db_session)

    employee_id_by_ssn = {}
    wage_import_1 = WageImporter(
        test_db_session,
        [wage_data_1],
        employee_id_by_ssn,
        report_1,
        list(),
        report_log_entry_1.import_log_id,
        WagesAndContributionsDatasource.DFML_REPORTED_WAGES,
    )
    wage_import_1.import_employees_and_wage_data(
        record_new_employees=True, should_update_wages=True
    )

    # This account has a name change and a claim, but name updates are disabled so they should not show up on the name change report
    assert len(wage_import_1.employee_report_name_changes) == 1
    assert wage_import_1.employee_report_name_changes[0]["NewFirstNameValue"] == "jabroni"

    employee_2 = employee_factory()
    claim_factory(employee=employee_2)

    wage_data_2 = employee_wage_factory({"employee_first_name": "jabroni"}, employee=employee_2)

    report_2, report_log_entry_2 = get_new_import_report(test_db_session)

    employee_id_by_ssn = {}
    wage_import_2 = WageImporter(
        test_db_session,
        [wage_data_2],
        employee_id_by_ssn,
        report_2,
        list(),
        report_log_entry_2.import_log_id,
        WagesAndContributionsDatasource.DFML_REPORTED_WAGES,
    )
    wage_import_2.import_employees_and_wage_data(
        record_new_employees=True, should_update_wages=True, should_update_existing_employees=False
    )

    # This account has a name change and a claim, so they should show up on the name change report
    assert len(wage_import_2.employee_report_name_changes) == 0


def test_employee_name_change_no_claim(test_db_session, employee_factory, employee_wage_factory):
    employee = employee_factory()

    wage_data = employee_wage_factory({"employee_first_name": "jabroni"}, employee=employee)

    report, report_log_entry = get_new_import_report(test_db_session)

    employee_id_by_ssn = {}
    wage_import = WageImporter(
        test_db_session,
        [wage_data],
        employee_id_by_ssn,
        report,
        list(),
        report_log_entry.import_log_id,
        WagesAndContributionsDatasource.DFML_REPORTED_WAGES,
    )
    wage_import.import_employees_and_wage_data(record_new_employees=True, should_update_wages=True)

    # No name changes should be reported since there are no claims for this employee
    assert len(wage_import.employee_report_name_changes) == 0


# == Validation Helpers ==


def validate_employee_persistence(employee_wage_payload, employee_row, import_log_id):
    assert employee_row.tax_identifier.tax_identifier == employee_wage_payload["employee_ssn"]
    assert employee_row.first_name == employee_wage_payload["employee_first_name"]
    assert employee_row.last_name == employee_wage_payload["employee_last_name"]
    assert employee_row.latest_import_log_id == import_log_id


def validate_wage_persistence(employee_wage_payload, wage_row, import_log_id):
    assert wage_row.is_independent_contractor == employee_wage_payload["independent_contractor"]
    assert wage_row.is_opted_in == employee_wage_payload["opt_in"]
    assert wage_row.employee_ytd_wages == employee_wage_payload["employee_ytd_wages"]
    assert wage_row.employee_qtr_wages == employee_wage_payload["employee_qtr_wages"]
    assert wage_row.employee_med_contribution == employee_wage_payload["employee_medical"]
    assert wage_row.employer_med_contribution == employee_wage_payload["employer_medical"]
    assert wage_row.employee_fam_contribution == employee_wage_payload["employee_family"]
    assert wage_row.employer_fam_contribution == employee_wage_payload["employer_family"]
    assert wage_row.latest_import_log_id == import_log_id


def validate_wage_history_persistence(employee_wage_payload, wage_row, import_log_id):
    assert wage_row.is_independent_contractor == employee_wage_payload["independent_contractor"]
    assert wage_row.is_opted_in == employee_wage_payload["opt_in"]
    assert wage_row.employee_ytd_wages == employee_wage_payload["employee_ytd_wages"]
    assert wage_row.employee_qtr_wages == employee_wage_payload["employee_qtr_wages"]
    assert wage_row.employee_med_contribution == employee_wage_payload["employee_medical"]
    assert wage_row.employer_med_contribution == employee_wage_payload["employer_medical"]
    assert wage_row.employee_fam_contribution == employee_wage_payload["employee_family"]
    assert wage_row.employer_fam_contribution == employee_wage_payload["employer_family"]
    assert wage_row.import_log_id == import_log_id


def validate_employer_persistence(employer_payload, employer_row, import_log_id):
    assert employer_row.employer_fein == employer_payload["fein"]
    assert employer_row.employer_name == employer_payload["employer_name"]
    assert employer_row.family_exemption == employer_payload["family_exemption"]
    assert employer_row.medical_exemption == employer_payload["medical_exemption"]
    assert employer_row.exemption_commence_date == employer_payload["exemption_commence_date"]
    assert employer_row.exemption_cease_date == employer_payload["exemption_cease_date"]
    assert employer_row.dor_updated_date == employer_payload["updated_date"]
    assert employer_row.latest_import_log_id == import_log_id


def validate_employer_address_persistence(
    employer_payload, address_row, business_address_type, state, country
):
    assert address_row.address_type_id == business_address_type.address_type_id
    assert address_row.address_line_one == employer_payload["employer_address_street"]
    assert address_row.city == employer_payload["employer_address_city"]
    assert address_row.geo_state_id == state.geo_state_id
    assert address_row.zip_code == employer_payload["employer_address_zip"]
    assert address_row.country_id == country.country_id


def test_parse_employer_file(test_fs_path):
    employer_info = test_data.get_new_employer()
    employer_file_path = "{}/{}".format(str(test_fs_path), employer_file)

    dor_import = DORImport(
        employer_file_path=employer_file_path,
        employee_file_path=employer_file_path,
        decrypter=decrypter,
    )
    process_dor_import = ProcessDORImport(dor_import)

    employers_info = process_dor_import.parse_employer_file()
    del employer_info["mtc_number"]
    assert employers_info[0] == employer_info


def test_parse_employer_file_with_bad_fein(test_fs_path_bad_fein, test_db_session):
    employer_info = test_data.get_new_employer()
    employer_file_path = "{}/{}".format(str(test_fs_path_bad_fein), employer_file)

    dor_import = DORImport(
        employer_file_path=employer_file_path,
        employee_file_path=employer_file_path,
        decrypter=decrypter,
    )
    process_dor_import = ProcessDORImport(dor_import)

    employers_info = process_dor_import.parse_employer_file()
    import_employers(
        test_db_session,
        employers_info,
        dor_import.report,
        dor_import.report_log_entry.import_log_id,
    )
    del employer_info["mtc_number"]
    assert dor_import.report.skipped_employers_count == 1
    assert dor_import.report.skipped_employers[0]["fein"] == "14467405"


def test_parse_employer_file_with_activity_key(test_fs_path_with_activity_key):
    employer_info = test_data.get_new_employer_with_activity_key("PFM10000000001")
    employer_file_path = "{}/{}".format(str(test_fs_path_with_activity_key), employer_file)

    dor_import = DORImport(
        employer_file_path=employer_file_path,
        employee_file_path=employer_file_path,
        decrypter=decrypter,
    )
    process_dor_import = ProcessDORImport(dor_import)

    employers_info = process_dor_import.parse_employer_file()

    assert employers_info[0] == employer_info


def test_parse_employer_file_with_mtc_number(test_fs_path_with_mtc_number):
    employer_info = test_data.get_new_employer_with_mtc_number()
    employer_file_path = "{}/{}".format(str(test_fs_path_with_mtc_number), employer_file)

    dor_import = DORImport(
        employer_file_path=employer_file_path,
        employee_file_path=employer_file_path,
        decrypter=decrypter,
    )
    process_dor_import = ProcessDORImport(dor_import)

    employers_info = process_dor_import.parse_employer_file()
    assert employers_info[0] == employer_info


## == full import ==


@pytest.mark.timeout(60)
def test_e2e_parse_and_persist(test_db_session, dor_employer_lookups, mock_send_email):
    # generate files for import
    employer_count = 100

    employer_file_path = get_temp_file_path()
    employee_file_path = get_temp_file_path()

    employer_file = open(employer_file_path, "w")
    employee_file = open(employee_file_path, "w")

    generator.generate(employer_count, employer_file, employee_file)
    employer_file.close()
    employee_file.close()

    employer_lines = open(employer_file_path, "r").readlines()
    assert len(employer_lines) >= employer_count  # Each employer may have multiple lines

    employee_lines = open(employee_file_path, "r").readlines()
    employee_a_lines = tuple(filter(lambda s: s.startswith("A"), employee_lines))
    employee_b_lines = tuple(filter(lambda s: s.startswith("B"), employee_lines))

    # Test scenario where already created tax ID will pass.
    dor_persistence_util.create_tax_id(test_db_session, "*********")

    assert len(employee_a_lines) == employer_count * 4
    wages_contributions_count = len(employee_b_lines)
    assert wages_contributions_count >= employer_count

    # import
    import_batches = [
        massgov.pfml.dor.importer.paths.ImportBatch(
            upload_date="20200805", employer_file=employer_file_path, employee_file=""
        ),
        massgov.pfml.dor.importer.paths.ImportBatch(
            upload_date="20200805", employer_file="", employee_file=employee_file_path
        ),
    ]

    reports = import_dor.process_import_batches(
        import_batches=import_batches, decrypt_files=False, optional_db_session=test_db_session
    )

    report_one = reports[0]
    assert report_one.created_employers_count == employer_count

    report_two = reports[1]
    assert report_two.created_employees_count >= employer_count
    assert report_two.created_wages_and_contributions_count == wages_contributions_count

    assert report_two.created_employer_quarters_count == len(employee_a_lines)
    mock_send_email.assert_called_once()


@pytest.mark.timeout(60)
def test_e2e_parse_and_persist_empty_dba_city(
    test_db_session, dor_employer_lookups, mock_send_email
):
    # generate files for import
    employer_count = 100

    employer_file_path = get_temp_file_path()
    employee_file_path = get_temp_file_path()

    employer_file = open(employer_file_path, "w")
    employee_file = open(employee_file_path, "w")

    generator.generate(
        employer_count, employer_file, employee_file, set_empty_dba=True, set_empty_city=True
    )
    employer_file.close()
    employee_file.close()

    employer_lines = open(employer_file_path, "r").readlines()
    assert len(employer_lines) >= employer_count  # Each employer may have multiple lines

    employee_lines = open(employee_file_path, "r").readlines()
    employee_a_lines = tuple(filter(lambda s: s.startswith("A"), employee_lines))
    employee_b_lines = tuple(filter(lambda s: s.startswith("B"), employee_lines))

    # Test scenario where already created tax ID will pass.
    dor_persistence_util.create_tax_id(test_db_session, "*********")

    assert len(employee_a_lines) == employer_count * 4
    wages_contributions_count = len(employee_b_lines)
    assert wages_contributions_count >= employer_count

    # import
    import_batches = [
        massgov.pfml.dor.importer.paths.ImportBatch(
            upload_date="20200805", employer_file=employer_file_path, employee_file=""
        ),
        massgov.pfml.dor.importer.paths.ImportBatch(
            upload_date="20200805", employer_file="", employee_file=employee_file_path
        ),
    ]

    reports = import_dor.process_import_batches(
        import_batches=import_batches, decrypt_files=False, optional_db_session=test_db_session
    )

    report_one = reports[0]
    assert report_one.created_employers_count == employer_count

    report_two = reports[1]
    assert report_two.created_employees_count >= employer_count
    assert report_two.created_wages_and_contributions_count == wages_contributions_count

    assert report_two.created_employer_quarters_count == len(employee_a_lines)

    # all employers came in as "" dba, check to make sure the first one is None (they all will be)
    employer_in_db = test_db_session.query(Employer).first()
    assert employer_in_db.employer_dba is None

    first_address_in_db = (
        test_db_session.query(Address)
        .outerjoin(InsuranceProvider)
        .filter(InsuranceProvider.address_id == None)  # noqa: E711
        .first()
    )
    assert first_address_in_db.city is None

    # make sure every address was created
    address_count = (
        test_db_session.query(Address)
        .outerjoin(InsuranceProvider)
        .filter(InsuranceProvider.address_id == None)  # noqa: E711
        .count()
    )
    assert address_count == employer_count


@pytest.mark.timeout(25)
def test_decryption(monkeypatch, test_db_session, dor_employer_lookups):
    monkeypatch.setenv("DECRYPT", "true")

    decryption_key = open(TEST_FOLDER / "encryption" / "test_private.key").read()
    passphrase = "bb8d58fa-d781-11ea-87d0-0242ac130003"

    decrypter = GpgCrypt([[decryption_key, passphrase]])

    employer_file_path = TEST_FOLDER / "encryption" / "DORDFMLEMP_20201118193421"
    employee_file_path = TEST_FOLDER / "encryption" / "DORDFML_20201118193421"

    report = import_dor.process_daily_import(
        employer_file_path=str(employer_file_path),
        employee_file_path=str(employee_file_path),
        employee_report_name_changes=list(),
        decrypter=decrypter,
        db_session=test_db_session,
    )

    employer_count = 100
    employee_count = employer_count * generator.EMPLOYER_TO_EMPLOYEE_RATIO

    assert report.created_employers_count == employer_count
    assert report.created_employees_count == employee_count
    assert report.created_wages_and_contributions_count >= employee_count


def test_importing_wages_for_existing_employer_without_account_key_and_mixed_records(
    initialize_factories_session, test_db_session
):
    # This test case is designed to capture a scenario where an employer was manually added
    # to the system without an account key, and wage records were later received from DOR with
    # an account key that maps to the employer FEIN. "A" records may appear after related "B"
    # records.
    decrypter = PassthruCrypt()

    employer_file_path = TEST_FOLDER / "fixtures" / "dor_wage_files" / "DORDFMLEMP_a_after_b"
    employee_file_path = TEST_FOLDER / "fixtures" / "dor_wage_files" / "DORDFML_a_after_b"

    EmployerFactory.create(account_key=None, employer_fein="*********")

    report = import_dor.process_daily_import(
        employer_file_path=str(employer_file_path),
        employee_file_path=str(employee_file_path),
        employee_report_name_changes=list(),
        decrypter=decrypter,
        db_session=test_db_session,
    )

    assert report.created_employers_count == 0
    assert report.created_employees_count == 2
    assert report.created_wages_and_contributions_count == 2


def test_importing_wages_bad_ssn(initialize_factories_session, test_db_session):

    decrypter = PassthruCrypt()

    employer_file_path = TEST_FOLDER / "fixtures" / "dor_wage_files" / "DORDFMLEMP_a_after_b"
    employee_file_path = TEST_FOLDER / "fixtures" / "dor_wage_files" / "DORDFML_a_after_b_bad_ssn"

    EmployerFactory.create(account_key=None, employer_fein="*********")

    report = import_dor.process_daily_import(
        employer_file_path=str(employer_file_path),
        employee_file_path=str(employee_file_path),
        employee_report_name_changes=list(),
        decrypter=decrypter,
        db_session=test_db_session,
    )

    assert report.created_employers_count == 0
    assert report.created_employees_count == 1
    assert report.invalid_employee_lines_count == 1
    assert report.created_wages_and_contributions_count == 1


def test_get_discreet_db_exception_message():
    original_exception_message = 'sqlalchemy.exc.InvalidRequestError: This Session\'s transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (psycopg2.errors.UniqueViolation) duplicate key value violates unique constraint "tax_identifier_tax_identifier_key"\nDETAIL:  Key (tax_identifier)=(********9) already exists.'
    expected_discreet_message = 'DB Exception: sqlalchemy.exc.InvalidRequestError: This Session\'s transaction has been rolled back due to a previous exception during flush. To begin a new transaction with this Session, first issue Session.rollback(). Original exception was: (psycopg2.errors.UniqueViolation) duplicate key value violates unique constraint "tax_identifier_tax_identifier_key"\n, exception type: SQLAlchemyError'
    exception = SQLAlchemyError(original_exception_message)
    discreet_message = get_discreet_db_exception_message(exception)
    assert "********9" not in discreet_message
    assert expected_discreet_message == discreet_message


def test_move_file_to_processed(mock_s3_bucket):
    file_name = "test.txt"

    key = "{}{}".format(RECEIVED_FOLDER, file_name)
    moved_key = "{}{}".format(PROCESSED_FOLDER, file_name)
    full_path = "s3://{}/{}".format(mock_s3_bucket, key)

    s3 = boto3.client("s3")
    s3.put_object(Bucket=mock_s3_bucket, Key=key, Body="line 1 text\nline 2 text\nline 3 text")

    should_exist_1 = s3.head_object(Bucket=mock_s3_bucket, Key=key)
    assert should_exist_1 is not None

    move_file_to_processed(PROCESSED_FOLDER, full_path, s3)

    try:
        s3.head_object(Bucket=mock_s3_bucket, Key=key)
        raise AssertionError("This file should have been deleted.")
    except botocore.exceptions.ClientError:
        """No Op"""

    should_exist_1 = s3.head_object(Bucket=mock_s3_bucket, Key=moved_key)
    assert should_exist_1 is not None


def get_temp_file_path():
    handle, path = tempfile.mkstemp()
    return path


employer_quarterly_info_list = [
    # This employer / filing period combination is repeated below.
    {
        "record_type": "A",
        "account_key": "44*********",
        "filing_period": date(2020, 3, 31),
        "employer_name": "Boone PLC",
        "employer_fein": "*********",
        "amended_flag": False,
        "pfm_account_id": "*********",
        "total_pfml_contribution": decimal.Decimal("46723.66"),
        "received_date": date(2020, 4, 7),
        "updated_date": datetime(2020, 9, 17, 23, 0, tzinfo=timezone.utc),
    },
    {
        "record_type": "A",
        "account_key": "44*********",
        "filing_period": date(2019, 6, 30),
        "employer_name": "Gould, Brown & Miller",
        "employer_fein": "*********",
        "amended_flag": False,
        "pfm_account_id": "*********",
        "total_pfml_contribution": decimal.Decimal("57034.87"),
        "received_date": date(2019, 7, 18),
        "updated_date": datetime(2020, 10, 12, 23, 0, tzinfo=timezone.utc),
    },
    # Repeat of 1st record above.
    {
        "record_type": "A",
        "account_key": "44*********",
        "filing_period": date(2020, 3, 31),
        "employer_name": "Boone PLC",
        "employer_fein": "*********",
        "amended_flag": False,
        "pfm_account_id": "*********",
        "total_pfml_contribution": decimal.Decimal("46723.66"),
        "received_date": date(2020, 4, 7),
        "updated_date": datetime(2020, 9, 17, 23, 0, tzinfo=timezone.utc),
    },
    {
        "record_type": "A",
        "account_key": "44*********",
        "filing_period": date(2019, 6, 30),
        "employer_name": "Stephens LLC",
        "employer_fein": "*********",
        "amended_flag": True,
        "pfm_account_id": "*********",
        "total_pfml_contribution": decimal.Decimal("15493.23"),
        "received_date": date(2019, 7, 28),
        "updated_date": datetime(2020, 10, 5, 23, 0, tzinfo=timezone.utc),
    },
]


def test_import_employer_pfml_contributions_repeated_record(test_db_session, employers):
    report, log_entry = get_new_import_report(test_db_session)

    import_dor.import_employer_pfml_contributions(
        test_db_session, employer_quarterly_info_list, report, log_entry.import_log_id
    )

    def row(index, period, total, account, received, updated, log_id=log_entry.import_log_id):
        return (
            employers[index].employer_id,
            date.fromisoformat(period),
            decimal.Decimal(total),
            account,
            datetime.fromisoformat(f"{received} 00:00+00:00"),
            datetime.fromisoformat(f"{updated}+00:00"),
            log_id,
        )

    rows = (
        test_db_session.query(
            EmployerQuarterlyContribution.employer_id,
            EmployerQuarterlyContribution.filing_period,
            EmployerQuarterlyContribution.employer_total_pfml_contribution,
            EmployerQuarterlyContribution.pfm_account_id,
            EmployerQuarterlyContribution.dor_received_date,
            EmployerQuarterlyContribution.dor_updated_date,
            EmployerQuarterlyContribution.latest_import_log_id,
        )
        .order_by(EmployerQuarterlyContribution.dor_received_date)
        .all()
    )
    assert rows == [
        row(1, "2019-06-30", "57034.87", "*********", "2019-07-18", "2020-10-12 23:00"),
        row(2, "2019-06-30", "15493.23", "*********", "2019-07-28", "2020-10-05 23:00"),
        row(0, "2020-03-31", "46723.66", "*********", "2020-04-07", "2020-09-17 23:00"),
    ]


def test_import_employer_pfml_contributions_with_updates(test_db_session, employers):
    EmployerQuarterlyContributionFactory.create(
        employer=employers[1],
        filing_period=date(2019, 6, 30),
        employer_total_pfml_contribution=decimal.Decimal("1000.00"),
        dor_received_date=date(2019, 6, 10),
    )
    EmployerQuarterlyContributionFactory.create(
        employer=employers[0],
        filing_period=date(2020, 3, 31),
        employer_total_pfml_contribution=decimal.Decimal("2000.00"),
        dor_received_date=date(2020, 3, 20),
    )
    EmployerQuarterlyContributionFactory.create(
        employer=employers[0],
        filing_period=date(2019, 9, 30),
        employer_total_pfml_contribution=decimal.Decimal("3000.00"),
        dor_received_date=date(2019, 9, 30),
        dor_updated_date=datetime(2019, 8, 30, 3, 30, tzinfo=timezone.utc),
        pfm_account_id="300000",
    )

    report, log_entry = get_new_import_report(test_db_session)

    import_dor.import_employer_pfml_contributions(
        test_db_session, employer_quarterly_info_list, report, log_entry.import_log_id
    )

    def row(index, period, total, account, received, updated, log_id=log_entry.import_log_id):
        return (
            employers[index].employer_id,
            date.fromisoformat(period),
            decimal.Decimal(total),
            account,
            datetime.fromisoformat(f"{received} 00:00+00:00"),
            datetime.fromisoformat(f"{updated}+00:00"),
            log_id,
        )

    rows = (
        test_db_session.query(
            EmployerQuarterlyContribution.employer_id,
            EmployerQuarterlyContribution.filing_period,
            EmployerQuarterlyContribution.employer_total_pfml_contribution,
            EmployerQuarterlyContribution.pfm_account_id,
            EmployerQuarterlyContribution.dor_received_date,
            EmployerQuarterlyContribution.dor_updated_date,
            EmployerQuarterlyContribution.latest_import_log_id,
        )
        .order_by(EmployerQuarterlyContribution.dor_received_date)
        .all()
    )
    assert rows == [
        row(1, "2019-06-30", "57034.87", "*********", "2019-07-18", "2020-10-12 23:00"),
        row(2, "2019-06-30", "15493.23", "*********", "2019-07-28", "2020-10-05 23:00"),
        row(0, "2019-09-30", "3000.00", "300000", "2019-09-30", "2019-08-30 03:30", None),
        row(0, "2020-03-31", "46723.66", "*********", "2020-04-07", "2020-09-17 23:00"),
    ]


def test_import_employees_and_wage_data_uses_dfml_reported_wages_datasource(
    test_db_session: Session,
):
    employer = generator.generate_single_employer(1)
    employer["exemption_cease_date"] = generator.NO_EXEMPTION_DATE
    employer["exemption_commence_date"] = generator.NO_EXEMPTION_DATE
    employer["family_exemption"] = False
    employer["medical_exemption"] = False
    employer_import_report, employer_import_log = get_new_import_report(test_db_session)
    import_employers(
        test_db_session, [employer], employer_import_report, employer_import_log.import_log_id
    )
    employee = next(generator.generate_single_employee(1, [employer]))
    employee["filing_period"] = employee["filing_period"].as_date()
    import_report, import_log = get_new_import_report(test_db_session)
    wage_import = WageImporter(
        test_db_session,
        [employee],
        {},
        import_report,
        [],
        import_log.import_log_id,
        WagesAndContributionsDatasource.DFML_REPORTED_WAGES,
    )

    wage_import.import_employees_and_wage_data()

    wages_and_contributions = test_db_session.query(WagesAndContributions).all()
    assert len(wages_and_contributions) == 1

    for wages in wages_and_contributions:
        assert (
            wages.datasource.wages_and_contributions_datasource_id
            == WagesAndContributionsDatasource.DFML_REPORTED_WAGES.wages_and_contributions_datasource_id
        )

    wages_and_contributions_histories = test_db_session.query(WagesAndContributionsHistory).all()
    assert len(wages_and_contributions_histories) == 1

    for history in wages_and_contributions_histories:
        assert (
            history.datasource.wages_and_contributions_datasource_id
            == WagesAndContributionsDatasource.DFML_REPORTED_WAGES.wages_and_contributions_datasource_id
        )


def test_import_employees_and_wage_data_overwrites_dua_wages_and_contributions(
    initialize_factories_session, test_db_session: Session
):
    filing_period = date(2024, 12, 31)

    existing_boolean = None
    existing_datasource = WagesAndContributionsDatasource.DUA_REPORTED_WAGES
    existing_wage_amount = None
    existing_qtr_wage_amount = decimal.Decimal("1.00")
    existing_employee, existing_employer, existing_wages_and_contributions = (
        test_data.generate_existing_entities_for_wage_and_contributions_testing(
            existing_boolean,
            existing_datasource,
            filing_period,
            existing_wage_amount,
            existing_qtr_wage_amount,
        )
    )

    new_boolean = True
    new_wage_amount = decimal.Decimal("2.00")
    new_wage_data = test_data.generate_new_wage_data_for_wages_and_contributions_testing(
        new_boolean, existing_employee, existing_employer, filing_period, new_wage_amount
    )
    new_datasource = WagesAndContributionsDatasource.DFML_REPORTED_WAGES

    import_report, import_log = get_new_import_report(test_db_session)

    wage_import = WageImporter(
        test_db_session,
        [new_wage_data],
        {},
        import_report,
        [],
        import_log.import_log_id,
        new_datasource,
    )

    wage_import.import_employees_and_wage_data(record_new_employees=True, should_update_wages=True)

    # No values have changed.
    wages_and_contributions_records = test_db_session.query(WagesAndContributions).all()
    assert len(wages_and_contributions_records) == 1
    wages_and_contributions = wages_and_contributions_records[0]
    assert (
        wages_and_contributions.wages_and_contributions_datasource_id
        == new_datasource.wages_and_contributions_datasource_id
    )
    assert wages_and_contributions.employee_fam_contribution == new_wage_amount
    assert wages_and_contributions.employee_med_contribution == new_wage_amount
    assert wages_and_contributions.employee_qtr_wages == new_wage_amount
    assert wages_and_contributions.employee_ytd_wages == new_wage_amount
    assert wages_and_contributions.employer_fam_contribution == new_wage_amount
    assert wages_and_contributions.employer_med_contribution == new_wage_amount
    assert wages_and_contributions.filing_period == filing_period
    assert wages_and_contributions.is_independent_contractor == new_boolean
    assert wages_and_contributions.is_opted_in == new_boolean

    wages_and_contributions_history_records = test_db_session.query(
        WagesAndContributionsHistory
    ).all()
    assert len(wages_and_contributions_history_records) == 2

    # The existing history has not changed.
    old_wages_and_contributions_history = wages_and_contributions_history_records[0]
    assert (
        old_wages_and_contributions_history.wages_and_contributions_datasource_id
        == existing_datasource.wages_and_contributions_datasource_id
    )
    assert old_wages_and_contributions_history.employee_fam_contribution == existing_wage_amount
    assert old_wages_and_contributions_history.employee_med_contribution == existing_wage_amount
    assert old_wages_and_contributions_history.employee_qtr_wages == existing_qtr_wage_amount
    assert old_wages_and_contributions_history.employee_ytd_wages == existing_wage_amount
    assert old_wages_and_contributions_history.employer_fam_contribution == existing_wage_amount
    assert old_wages_and_contributions_history.employer_med_contribution == existing_wage_amount
    assert old_wages_and_contributions_history.is_independent_contractor == existing_boolean
    assert old_wages_and_contributions_history.is_opted_in == existing_boolean

    # A new history has been created.
    new_wages_and_contributions_history = wages_and_contributions_history_records[1]
    assert (
        new_wages_and_contributions_history.wages_and_contributions_datasource_id
        == new_datasource.wages_and_contributions_datasource_id
    )
    assert new_wages_and_contributions_history.employee_fam_contribution == new_wage_amount
    assert new_wages_and_contributions_history.employee_med_contribution == new_wage_amount
    assert new_wages_and_contributions_history.employee_qtr_wages == new_wage_amount
    assert new_wages_and_contributions_history.employee_ytd_wages == new_wage_amount
    assert new_wages_and_contributions_history.employer_fam_contribution == new_wage_amount
    assert new_wages_and_contributions_history.employer_med_contribution == new_wage_amount
    assert new_wages_and_contributions_history.is_independent_contractor == new_boolean
    assert new_wages_and_contributions_history.is_opted_in == new_boolean

    # A difference in quarterly wages was detected, and an update was made.
    assert import_report.differing_qtr_wages_count == 1
    assert import_report.unmodified_wages_and_contributions_count == 0
    assert import_report.updated_wages_and_contributions_count == 1

    # No WagesAndContributionsUnused are created
    wages_and_contributions_unused_records = test_db_session.query(
        WagesAndContributionsUnused
    ).all()
    assert len(wages_and_contributions_unused_records) == 0


def test_import_employees_and_wage_data_overwrites_dfml_wages_and_contributions(
    initialize_factories_session, test_db_session: Session
):
    filing_period = date(2024, 12, 31)

    existing_boolean = False
    existing_datasource = WagesAndContributionsDatasource.DFML_REPORTED_WAGES
    existing_wage_amount = decimal.Decimal("1.00")
    existing_employee, existing_employer = (
        test_data.generate_existing_entities_for_wage_and_contributions_testing(
            existing_boolean,
            existing_datasource,
            filing_period,
            existing_wage_amount,
            existing_wage_amount,
        )[:2]
    )

    new_boolean = True
    new_wage_amount = decimal.Decimal("2.00")
    new_wage_data = test_data.generate_new_wage_data_for_wages_and_contributions_testing(
        new_boolean, existing_employee, existing_employer, filing_period, new_wage_amount
    )
    new_datasource = WagesAndContributionsDatasource.DFML_REPORTED_WAGES

    import_report, import_log = get_new_import_report(test_db_session)

    wage_import = WageImporter(
        test_db_session,
        [new_wage_data],
        {},
        import_report,
        [],
        import_log.import_log_id,
        new_datasource,
    )

    wage_import.import_employees_and_wage_data(record_new_employees=True, should_update_wages=True)

    # Wages and contributions have been updated.
    wages_and_contributions_records = test_db_session.query(WagesAndContributions).all()
    assert len(wages_and_contributions_records) == 1
    wages_and_contributions = wages_and_contributions_records[0]
    assert (
        wages_and_contributions.wages_and_contributions_datasource_id
        == new_datasource.wages_and_contributions_datasource_id
    )
    assert wages_and_contributions.employee_fam_contribution == new_wage_amount
    assert wages_and_contributions.employee_med_contribution == new_wage_amount
    assert wages_and_contributions.employee_qtr_wages == new_wage_amount
    assert wages_and_contributions.employee_ytd_wages == new_wage_amount
    assert wages_and_contributions.employer_fam_contribution == new_wage_amount
    assert wages_and_contributions.employer_med_contribution == new_wage_amount
    assert wages_and_contributions.filing_period == filing_period
    assert wages_and_contributions.is_independent_contractor == new_boolean
    assert wages_and_contributions.is_opted_in == new_boolean

    wages_and_contributions_history_records = test_db_session.query(
        WagesAndContributionsHistory
    ).all()
    assert len(wages_and_contributions_history_records) == 2

    # The existing history has not changed.
    old_wages_and_contributions_history = wages_and_contributions_history_records[0]
    assert (
        old_wages_and_contributions_history.wages_and_contributions_datasource_id
        == existing_datasource.wages_and_contributions_datasource_id
    )
    assert old_wages_and_contributions_history.employee_fam_contribution == existing_wage_amount
    assert old_wages_and_contributions_history.employee_med_contribution == existing_wage_amount
    assert old_wages_and_contributions_history.employee_qtr_wages == existing_wage_amount
    assert old_wages_and_contributions_history.employee_ytd_wages == existing_wage_amount
    assert old_wages_and_contributions_history.employer_fam_contribution == existing_wage_amount
    assert old_wages_and_contributions_history.employer_med_contribution == existing_wage_amount
    assert old_wages_and_contributions_history.is_independent_contractor == existing_boolean
    assert old_wages_and_contributions_history.is_opted_in == existing_boolean

    # A new history has been created.
    new_wages_and_contributions_history = wages_and_contributions_history_records[1]
    assert (
        new_wages_and_contributions_history.wages_and_contributions_datasource_id
        == new_datasource.wages_and_contributions_datasource_id
    )
    assert new_wages_and_contributions_history.employee_fam_contribution == new_wage_amount
    assert new_wages_and_contributions_history.employee_med_contribution == new_wage_amount
    assert new_wages_and_contributions_history.employee_qtr_wages == new_wage_amount
    assert new_wages_and_contributions_history.employee_ytd_wages == new_wage_amount
    assert new_wages_and_contributions_history.employer_fam_contribution == new_wage_amount
    assert new_wages_and_contributions_history.employer_med_contribution == new_wage_amount
    assert new_wages_and_contributions_history.is_independent_contractor == new_boolean
    assert new_wages_and_contributions_history.is_opted_in == new_boolean

    # A difference in quarterly wages was detected, and an update was made.
    assert import_report.differing_qtr_wages_count == 1
    assert import_report.unmodified_wages_and_contributions_count == 0
    assert import_report.updated_wages_and_contributions_count == 1

    # No WagesAndContributionsUnused are created
    wages_and_contributions_unused_records = test_db_session.query(
        WagesAndContributionsUnused
    ).all()
    assert len(wages_and_contributions_unused_records) == 0


@pytest.mark.timeout(60)
def test_report_warning_status_for_invalid_employees(test_db_session):

    employer_file_path = "{}/{}".format(str(test_fs_path), employer_file)

    dor_import = DORImport(
        employer_file_path=employer_file_path,
        employee_file_path=employer_file_path,
        decrypter=decrypter,
    )
    process_dor_import = ProcessDORImport(dor_import)
    dor_import.report_log_entry = process_dor_import.create_report_log(test_db_session)
    dor_import.parsed_employees_count = 2
    dor_import.report.skipped_employees_count = 1
    process_dor_import.finalize_report_log(test_db_session)

    assert dor_import.report.status == "warning"
    assert dor_import.report.message == "Skipped 50.00% of employees"
    assert dor_import.report_log_entry.status == "warning"


def test_import_employer_with_custom_account_key(test_db_session):
    employer_payload = copy.deepcopy(test_data.new_employer)
    employers = [employer_payload]

    # Perform the import
    report, report_log_entry = get_new_import_report(test_db_session)
    import_employers(test_db_session, employers, report, report_log_entry.import_log_id)

    # Verify the employer was imported correctly
    persisted_employer = (
        test_db_session.query(Employer)
        .filter(Employer.account_key == employer_payload["account_key"])
        .one_or_none()
    )
    assert persisted_employer is not None
    assert persisted_employer.employer_name == employer_payload["employer_name"]
    assert persisted_employer.family_exemption == employer_payload["family_exemption"]
    assert persisted_employer.medical_exemption == employer_payload["medical_exemption"]


def test_should_update_account_key_logic():
    from massgov.pfml.dor.importer.employers.employer_importer import _should_update_account_key

    existing_employer_model = Employer(
        account_key="***********",
    )

    # Case 1: Matching account_key
    employer_info = {"account_key": "***********"}
    assert not _should_update_account_key(existing_employer_model, employer_info)

    # Case 2: Mismatched account_key
    employer_info = {"account_key": "***********"}
    assert not _should_update_account_key(existing_employer_model, employer_info)

    # Case 3: Missing account_key in existing employer
    existing_employer_model.account_key = None
    employer_info = {"account_key": "***********"}
    assert not _should_update_account_key(existing_employer_model, employer_info)

    # Case 4: Missing account_key in employer_info
    employer_info = {"account_key": None}
    assert not _should_update_account_key(existing_employer_model, employer_info)

    # Case 5: Both account_key are None
    existing_employer_model.account_key = None
    employer_info = {"account_key": None}
    assert not _should_update_account_key(existing_employer_model, employer_info)
