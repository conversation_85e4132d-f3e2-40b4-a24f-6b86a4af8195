import json
import os

import pytest

from massgov.pfml.db.lookup_data.employer_exemptions import EmployerExemptionApplicationStatus
from massgov.pfml.db.models.factories import (
    EmployerExemptionApplicationFactory,
    ImportLogFactory,
    PhoneFactory,
    UserLeaveAdministratorFactory,
)
from massgov.pfml.dor.employer_exemptions.employer_exemptions_export_step import (
    EmployerExemptionsExportStep,
)
from massgov.pfml.util.batch.log import LogEntry
from massgov.pfml.util.datetime import get_now_us_eastern

now = get_now_us_eastern()


@pytest.fixture
def exemptions_file_path(tmp_path):
    return os.path.join(tmp_path, f"employer_exemptions_export_{now.strftime('%Y%m%d')}.txt")


@pytest.fixture
def employer_exemptions_create_file_step(
    initialize_factories_session, test_db_session, exemptions_file_path
):
    step = EmployerExemptionsExportStep(
        db_session=test_db_session,
        log_entry_db_session=test_db_session,
        exemptions_file_reference=exemptions_file_path,
    )
    step._log_entry = LogEntry(test_db_session, "")
    return step


def test_employer_exemptions_export_step(
    test_db_session, initialize_factories_session, employer_exemptions_create_file_step
):
    # TODO (PFMLPB-23258): Add tests for the step when we actually write the file
    # Set up the test data
    leave_admin = UserLeaveAdministratorFactory.create()
    phone = PhoneFactory.create(
        phone_number="**********",
    )
    employer_exemption = EmployerExemptionApplicationFactory.create(
        employer=leave_admin.employer,
        employer_exemption_application_status_id=EmployerExemptionApplicationStatus.APPROVED.employer_exemption_application_status_id,
        synced_to_dor=False,
        insurance_plan_effective_at="2025-01-01",
        insurance_plan_expires_at="2026-04-01",
        has_medical_exemption=True,
        has_family_exemption=False,
        is_self_insured_plan=False,
        insurance_provider_id=3,
        insurance_plan_id=7,
        average_workforce_count=400,
        contact_first_name="Rumple",
        contact_last_name="Stiltskin",
        contact_title="Sir",
        contact_phone_id=phone.phone_id,
        contact_email_address="<EMAIL>",
    )
    import_log = ImportLogFactory.create(
        source="EmployerExemptionsExportStep",
        import_type="Employer Exemptions Export",
        report=json.dumps({"records_count": 1}),
    )
    employer_exemption.import_log_id = import_log.import_log_id

    employer_exemptions_create_file_step.run_step()

    # Check the output file
    contents = open(employer_exemptions_create_file_step.exemptions_file_reference, "r").readlines()
    # Check the header
    header = contents[0].strip()
    assert header == "H0000000001"
    assert len(contents) == 2
    # Check the exemption line
    exemption = contents[1].replace("\n", "")
    assert exemption[0:14] == "N" + leave_admin.employer.employer_fein + "FEIN"
    assert exemption[14:22] == "20250101"
    assert exemption[22:30] == "20260401"
    assert exemption[30] == "1"
    assert exemption[31] == "0"
    assert exemption[32:42] == "Private   "
    assert exemption[497:507] == "0000000400"
    assert len(exemption) == 1147

    # Check the import log
    assert import_log.source == "EmployerExemptionsExportStep"
    assert import_log.import_type == "Employer Exemptions Export"
    assert import_log.report == json.dumps({"records_count": 1})
