import pathlib
import uuid
from unittest.mock import MagicMock

import pytest

import massgov.pfml.util.files as file_util
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.employees import Claim
from massgov.pfml.db.models.factories import ApplicationFactory, ClaimFactory
from massgov.pfml.util.admin.update_application_util import UpdateApplication


@pytest.fixture()
def employee_claim(initialize_factories_session):
    claim = ClaimFactory.create()
    return claim


@pytest.fixture()
def single_application(initialize_factories_session):
    app = ApplicationFactory.create()
    return app


@pytest.fixture()
def single_application_with_claim(initialize_factories_session):
    claim = ClaimFactory.create()
    app = ApplicationFactory.create(claim_id=claim.claim_id)
    return app


@pytest.fixture()
def multiple_applications(initialize_factories_session):
    apps = []
    for _ in range(10):
        apps.append(ApplicationFactory.create())
    return apps


@pytest.fixture()
def multiple_claims(initialize_factories_session):
    claims = []
    for _ in range(10):
        claims.append(ClaimFactory.create())
    return claims


def create_unf_file(multiple_applications, multiple_claims) -> pathlib.Path:

    data = []
    for i in range(len(multiple_applications)):
        data.append(
            {
                "ApplicationID": multiple_applications[i].application_id,
                "FineosAbsenceID": multiple_claims[i].fineos_absence_id,
            }
        )

    return file_util.create_csv_from_list(
        data, ["ApplicationID", "FineosAbsenceID"], "unf", "local_s3/agency-transfer/unf/"
    )


def create_unf_file_incorrect_heading(multiple_applications, multiple_claims) -> pathlib.Path:

    data = []
    for i in range(len(multiple_applications)):
        data.append(
            {
                "Application ID": multiple_applications[i].application_id,
                "Fineos AbsenceID": multiple_claims[i].fineos_absence_id,
            }
        )

    return file_util.create_csv_from_list(
        data, ["Application ID", "Fineos AbsenceID"], "unf", "local_s3/agency-transfer/unf/"
    )


def update_application_single(
    application_id, absence_id, dry_run, db_session=None
) -> UpdateApplication:
    return UpdateApplication(
        psd_number="PSD-12345",
        application_id=application_id,
        fineos_absence_id=absence_id,
        file_location="",
        dry_run=dry_run,
        db_session=db_session if db_session else MagicMock(),
    )


class TestUpdateApplication:
    def test_update_application_dry_run_true(
        self, single_application, employee_claim, test_db_session
    ):
        app = (
            test_db_session.query(Application)
            .filter(Application.application_id == single_application.application_id)
            .first()
        )
        assert app.tax_identifier_id == single_application.tax_identifier_id
        update_app_instance = update_application_single(
            application_id=single_application.application_id,
            absence_id=employee_claim.fineos_absence_id,
            dry_run=True,
            db_session=test_db_session,
        )

        assert update_app_instance.commit_changes is False
        update_app_instance.run()
        test_db_session.refresh(app)
        assert app.tax_identifier_id == single_application.tax_identifier_id
        assert app.tax_identifier_id != employee_claim.employee.tax_identifier_id

    def test_update_application_dry_run_false(
        self, single_application, employee_claim, test_db_session
    ):
        app = (
            test_db_session.query(Application)
            .filter(Application.application_id == single_application.application_id)
            .first()
        )
        assert app.tax_identifier_id == single_application.tax_identifier_id
        old_taxid = single_application.tax_identifier_id
        update_app_instance = update_application_single(
            application_id=single_application.application_id,
            absence_id=employee_claim.fineos_absence_id,
            dry_run=False,
            db_session=test_db_session,
        )
        assert update_app_instance.commit_changes is True
        update_app_instance.run()
        test_db_session.refresh(app)
        assert app.tax_identifier_id != old_taxid
        assert update_app_instance.total_records_updated == 1
        assert app.tax_identifier_id == employee_claim.employee.tax_identifier_id

    def test_update_application_dry_run_false_with_claim(
        self, single_application_with_claim, employee_claim, test_db_session
    ):
        app = (
            test_db_session.query(Application)
            .filter(Application.application_id == single_application_with_claim.application_id)
            .first()
        )
        assert app.tax_identifier_id == single_application_with_claim.tax_identifier_id
        old_taxid = single_application_with_claim.tax_identifier_id
        update_app_instance = update_application_single(
            application_id=single_application_with_claim.application_id,
            absence_id=employee_claim.fineos_absence_id,
            dry_run=False,
            db_session=test_db_session,
        )
        assert update_app_instance.commit_changes is True
        update_app_instance.run()
        test_db_session.refresh(app)
        assert update_app_instance.total_records_skipped == 1
        assert app.tax_identifier_id == old_taxid
        assert app.tax_identifier_id != employee_claim.employee.tax_identifier_id

    def test_update_application_dry_run_false_with_non_existing_application(
        self, employee_claim, test_db_session
    ):

        update_app_instance = update_application_single(
            application_id=uuid.uuid4(),
            absence_id=employee_claim.fineos_absence_id,
            dry_run=False,
            db_session=test_db_session,
        )
        assert update_app_instance.commit_changes is True
        update_app_instance.run()
        assert update_app_instance.total_records_skipped == 1

    def test_update_application_dry_run_false_with_non_existing_claim(
        self, single_application, test_db_session
    ):
        app = (
            test_db_session.query(Application)
            .filter(Application.application_id == single_application.application_id)
            .first()
        )
        assert app.tax_identifier_id == single_application.tax_identifier_id
        old_taxid = single_application.tax_identifier_id
        update_app_instance = update_application_single(
            application_id=single_application.application_id,
            absence_id="NTN-001-ABS-01",
            dry_run=False,
            db_session=test_db_session,
        )
        assert update_app_instance.commit_changes is True
        update_app_instance.run()
        test_db_session.refresh(app)
        assert app.tax_identifier_id == old_taxid
        assert update_app_instance.total_records_skipped == 1

    def test_update_application_dry_run_true_with_file(
        self, multiple_applications, multiple_claims, test_db_session
    ):
        assert len(multiple_applications) == 10
        file = create_unf_file(multiple_applications, multiple_claims)
        update_application_file_instance = UpdateApplication(
            psd_number="PSD-12345",
            application_id="",
            fineos_absence_id="",
            file_location=file,
            dry_run=True,
            db_session=test_db_session,
        )

        update_application_file_instance.run()
        assert update_application_file_instance.commit_changes is False
        assert update_application_file_instance.total_records_processed == 10
        assert update_application_file_instance.total_records_updated == 0
        assert update_application_file_instance.total_records_skipped == 10

    def test_update_application_dry_run_false_with_file(
        self, multiple_applications, multiple_claims, test_db_session
    ):
        assert len(multiple_applications) == 10
        file = create_unf_file(multiple_applications, multiple_claims)
        update_application_file_instance = UpdateApplication(
            psd_number="PSD-12345",
            application_id="",
            fineos_absence_id="",
            file_location=file,
            dry_run=False,
            db_session=test_db_session,
        )
        update_application_file_instance.run()
        assert update_application_file_instance.commit_changes is True
        assert update_application_file_instance.total_records_processed == 10
        assert update_application_file_instance.total_records_updated == 10
        assert update_application_file_instance.total_records_skipped == 0

    def test_update_application_dry_run_false_with_file_and_non_existing_application(
        self, multiple_applications, multiple_claims, test_db_session
    ):
        assert len(multiple_applications) == 10
        file = create_unf_file(multiple_applications, multiple_claims)
        # Add a non-existing application to the file
        with open(file, "a") as f:
            f.write(f"{uuid.uuid4()},{multiple_claims[0].fineos_absence_id}\n")

        update_application_file_instance = UpdateApplication(
            psd_number="PSD-12345",
            application_id="",
            fineos_absence_id="",
            file_location=file,
            dry_run=False,
            db_session=test_db_session,
        )
        update_application_file_instance.run()
        assert update_application_file_instance.commit_changes is True
        assert update_application_file_instance.total_records_processed == 11
        assert update_application_file_instance.total_records_updated == 10
        assert update_application_file_instance.total_records_skipped == 1

    def test_update_application_dry_run_false_with_file_and_non_existing_claim(
        self, multiple_applications, multiple_claims, test_db_session
    ):
        assert len(multiple_applications) == 10
        file = create_unf_file(multiple_applications, multiple_claims)
        # Add a non-existing claim to the file
        with open(file, "a") as f:
            f.write(f"{multiple_applications[0].application_id},{uuid.uuid4()}\n")
        # Add a non-existing claim to the file
        update_application_file_instance = UpdateApplication(
            psd_number="PSD-12345",
            application_id="",
            fineos_absence_id="",
            file_location=file,
            dry_run=False,
            db_session=test_db_session,
        )
        update_application_file_instance.run()
        assert update_application_file_instance.commit_changes is True
        assert update_application_file_instance.total_records_processed == 11
        assert update_application_file_instance.total_records_updated == 10
        assert update_application_file_instance.total_records_skipped == 1

    def test_update_application_dry_run_false_with_file_and_non_existing_application_and_claim(
        self, multiple_applications, multiple_claims, test_db_session
    ):
        assert len(multiple_applications) == 10
        file = create_unf_file(multiple_applications, multiple_claims)
        # Add a non-existing application and claim to the file
        with open(file, "a") as f:
            f.write(f"{uuid.uuid4()},{uuid.uuid4()}\n")

        update_application_file_instance = UpdateApplication(
            psd_number="PSD-12345",
            application_id="",
            fineos_absence_id="",
            file_location=file,
            dry_run=False,
            db_session=test_db_session,
        )
        update_application_file_instance.run()
        assert update_application_file_instance.commit_changes is True
        assert update_application_file_instance.total_records_processed == 11
        assert update_application_file_instance.total_records_updated == 10
        assert update_application_file_instance.total_records_skipped == 1

    def test_update_application_with_no_args(self, test_db_session):
        update_application_file_instance = UpdateApplication(
            psd_number="PSD-12345",
            application_id="",
            fineos_absence_id="",
            file_location="",
            dry_run=False,
            db_session=test_db_session,
        )
        update_application_file_instance.run()
        assert update_application_file_instance.total_records_skipped == 1
        assert update_application_file_instance.total_records_processed == 1

    def test_update_application_with_incorrect_heading(
        self, multiple_applications, multiple_claims, test_db_session
    ):
        assert len(multiple_applications) == 10
        file = create_unf_file_incorrect_heading(multiple_applications, multiple_claims)
        update_application_file_instance = UpdateApplication(
            psd_number="PSD-12345",
            application_id="",
            fineos_absence_id="",
            file_location=file,
            dry_run=False,
            db_session=test_db_session,
        )
        update_application_file_instance.run()
        assert update_application_file_instance.commit_changes is True
        assert update_application_file_instance.total_records_processed == 10
        assert update_application_file_instance.total_records_updated == 0
        assert update_application_file_instance.total_records_skipped == 10

    def test_update_application_with_claim_already_associated(
        self, single_application_with_claim, single_application, test_db_session
    ):
        existing_app_with_claim = (
            test_db_session.query(Application)
            .filter(Application.application_id == single_application_with_claim.application_id)
            .first()
        )
        existing_fineos_absence_id = (
            test_db_session.query(Claim)
            .filter(Claim.claim_id == existing_app_with_claim.claim_id)
            .first()
            .fineos_absence_id
        )

        old_taxid = single_application.tax_identifier_id
        update_app_instance = update_application_single(
            application_id=single_application.application_id,
            absence_id=existing_fineos_absence_id,
            dry_run=False,
            db_session=test_db_session,
        )
        assert update_app_instance.commit_changes is True
        update_app_instance.run()
        test_db_session.refresh(single_application)
        assert single_application.tax_identifier_id == old_taxid
        assert update_app_instance.total_records_skipped == 1
