from unittest.mock import MagicMock, patch

import pytest

from massgov.pfml.db.models.base import uuid_gen
from massgov.pfml.db.models.factories import AddressFactory, EmployerFactory
from massgov.pfml.util.uuids.update_uuid_resolver import UpdateUUIDResolver
from tests.helpers.logging import get_mock_logger


def create_uuid_resolver(dry_run=True):

    return UpdateUUIDResolver(
        psd_number="PSD-1234",
        employer_fein="844787516",
        dry_run=dry_run,
        env="test",
        db_session=MagicMock(),
    )


def get_employer():
    employer = EmployerFactory.build(employer_fein="844787516")
    return employer


def get_address():
    address = AddressFactory.build()
    return address


@pytest.fixture
def get_new_employer_id():
    return uuid_gen()


@pytest.fixture
def mock_db_session():
    session = MagicMock()
    session.query.return_value.filter.return_value.one_or_none.return_value = EmployerFactory.build(
        employer_fein="844787516"
    )
    session.query.return_value.filter.return_value.one.return_value = AddressFactory.build()
    return session


class TestUpdateUUIDResolver:

    mock_logger = get_mock_logger()
    update_uuid_resolver = create_uuid_resolver(dry_run=False)
    new_employer_id = uuid_gen()
    new_address_id = uuid_gen()
    existing_employer = get_employer()
    existing_address = get_address()

    @patch(
        "massgov.pfml.util.uuids.update_uuid_resolver.UpdateUUIDResolver._delete_employer_and_address_record"
    )
    @patch("massgov.pfml.util.uuids.update_uuid_resolver.UpdateUUIDResolver._update_employer_uuid")
    @patch(
        "massgov.pfml.util.uuids.update_uuid_resolver.UpdateUUIDResolver._update_employer_address_uuid"
    )
    @patch(
        "massgov.pfml.util.uuids.update_uuid_resolver.UpdateUUIDResolver._get_existing_records_for_the_employer_fein"
    )
    def test_update_uuid_dry_run(
        self,
        mock_get_existing_records_for_the_employer_fein,
        mock_update_employer_uuid,
        mock_update_employer_address_id,
        mock_delete_employer_address_id,
    ):
        mock_get_existing_records_for_the_employer_fein.return_value = (
            self.existing_employer,
            self.existing_address,
        )
        update_uuid_resolver = create_uuid_resolver()
        assert update_uuid_resolver.employer_fein == "844787516"

        log_attrs = {"psd_ticket_number": "PSD-1234", "dry_run": True, "employer_fein": "844787516"}
        assert update_uuid_resolver.log_attr == log_attrs
        assert update_uuid_resolver.should_commit_changes is False

        update_uuid_resolver._process_update_uuid(self.existing_employer, self.existing_address)
        mock_update_employer_uuid.assert_called_once()
        mock_update_employer_address_id.assert_called_once()
        mock_delete_employer_address_id.assert_called_once()

    @patch("massgov.pfml.util.uuids.update_uuid_resolver.logger", mock_logger)
    def test_update_uuid_init(self):
        assert self.update_uuid_resolver.employer_fein == "844787516"
        log_attrs = {
            "psd_ticket_number": "PSD-1234",
            "dry_run": False,
            "employer_fein": "844787516",
        }
        assert self.update_uuid_resolver.log_attr == log_attrs
        assert self.update_uuid_resolver.should_commit_changes is True

    @patch("massgov.pfml.util.uuids.update_uuid_resolver.logger", mock_logger)
    @patch("massgov.pfml.util.uuids.update_uuid_resolver.UpdateUUIDResolver._process_update_uuid")
    def test_logs_on_run(self, mock_process_update_uuid):

        self.update_uuid_resolver.run()
        self.update_uuid_resolver._process_update_uuid.assert_called_once()
        self.mock_logger.info.assert_any_call(
            "Completed",
            extra=self.update_uuid_resolver.log_attr,
        )

    @patch(
        "massgov.pfml.util.uuids.update_uuid_resolver.UpdateUUIDResolver._delete_employer_and_address_record"
    )
    @patch("massgov.pfml.util.uuids.update_uuid_resolver.UpdateUUIDResolver._update_employer_uuid")
    @patch(
        "massgov.pfml.util.uuids.update_uuid_resolver.UpdateUUIDResolver._update_employer_address_uuid"
    )
    @patch(
        "massgov.pfml.util.uuids.update_uuid_resolver.UpdateUUIDResolver._get_existing_records_for_the_employer_fein"
    )
    def test_update_uuid_run(
        self,
        mock_get_existing_records_for_the_employer_fein,
        mock_update_employer_uuid,
        mock_update_employer_address_id,
        mock_delete_employer_address_id,
    ):
        mock_get_existing_records_for_the_employer_fein.return_value = (
            self.existing_employer,
            self.existing_address,
        )
        assert self.update_uuid_resolver.employer_fein == "844787516"

        log_attrs = {
            "psd_ticket_number": "PSD-1234",
            "dry_run": False,
            "employer_fein": "844787516",
        }
        assert self.update_uuid_resolver.log_attr == log_attrs
        assert self.update_uuid_resolver.should_commit_changes is True

        self.update_uuid_resolver._process_update_uuid(
            self.existing_employer, self.existing_address
        )

        mock_update_employer_uuid.assert_called_once()
        mock_update_employer_address_id.assert_called_once()
        mock_delete_employer_address_id.assert_called_once()

    @patch("massgov.pfml.util.uuids.update_uuid_resolver.logger", mock_logger)
    @patch(
        "massgov.pfml.util.uuids.update_uuid_resolver.UpdateUUIDResolver._get_existing_records_for_the_employer_fein"
    )
    def test_get_employer_found(self, mock_get_existing_records_for_the_employer_fein):
        mock_get_existing_records_for_the_employer_fein.return_value = (
            self.existing_employer,
            self.existing_address,
        )
        self.update_uuid_resolver = UpdateUUIDResolver(
            psd_number="PSD-1234",
            employer_fein="844787516",
            dry_run=False,
            env="test",
            db_session=MagicMock(),
        )

        self.update_uuid_resolver.run()

        mock_get_existing_records_for_the_employer_fein.assert_called_once()

        self.mock_logger.info.assert_any_call(
            "***DRY RUN MODE DISABLED***", extra=self.update_uuid_resolver.log_attr
        )
        self.mock_logger.info.assert_any_call("Completed", extra=self.update_uuid_resolver.log_attr)
        # user id added to list of attrs we're logging
        assert (
            self.update_uuid_resolver.log_attr["employer_fein"]
            == self.existing_employer.employer_fein
        )

    @patch("massgov.pfml.util.uuids.update_uuid_resolver.logger", mock_logger)
    @patch(
        "massgov.pfml.util.uuids.update_uuid_resolver.UpdateUUIDResolver._get_existing_records_for_the_employer_fein",
        return_value=None,
    )
    def test_get_employer_not_found(self, mock_get_existing_records_for_the_employer_fein):
        with pytest.raises(Exception) as e:
            self.update_uuid_resolver._get_existing_records_for_the_employer_fein(
                self.update_uuid_resolver.employer_fein
            )

            assert str(e.value) == "Unable to find employer with the given fein"
            mock_get_existing_records_for_the_employer_fein.assert_called_once()
            self.mock_logger.info.assert_any_call(
                "Querying to find employer", extra=self.update_uuid_resolver.log_attr
            )
            self.mock_logger.error.assert_any_call(
                "Error finding employer in PFML database",
                e,
                extra=self.update_uuid_resolver.log_attr,
            )

    @patch("massgov.pfml.util.uuids.update_uuid_resolver.logger", mock_logger)
    def test_get_existing_records_for_the_employer_fein_success(self):
        employer = self.existing_employer
        address = self.existing_address
        db_session = MagicMock()
        db_session.query.return_value.filter.return_value.one_or_none.return_value = employer

        employer_address_link = MagicMock()
        employer_address_link.address_id = address.address_id
        db_session.query.return_value.filter.return_value.one.return_value = employer_address_link
        db_session.query.return_value.filter.return_value.one.return_value = address

        resolver = UpdateUUIDResolver(
            psd_number="PSD-1234",
            employer_fein=employer.employer_fein,
            dry_run=False,
            env="test",
            db_session=db_session,
        )

        result_employer, result_address = resolver._get_existing_records_for_the_employer_fein(
            employer.employer_fein
        )

        assert result_employer == employer
        assert result_address == address
        self.mock_logger.info.assert_any_call("Employer record found", extra=resolver.log_attr)
        self.mock_logger.info.assert_any_call("Address found for employer", extra=resolver.log_attr)

    @patch("massgov.pfml.util.uuids.update_uuid_resolver.logger", mock_logger)
    def test_update_fineos_employer_id(self):
        db_session = MagicMock()
        resolver = UpdateUUIDResolver(
            psd_number="PSD-1234",
            employer_fein="844787516",
            dry_run=False,
            env="test",
            db_session=db_session,
        )
        fineos_emp_id = "FINEOS123"
        mtc_number = "MTC456"

        resolver.update_fineos_employer_id(fineos_emp_id, mtc_number)

        db_session.query.assert_called_once()
        db_session.query.return_value.filter.return_value.update.assert_called_once()
        db_session.commit.assert_called_once()
        self.mock_logger.info.assert_any_call(
            "Updating Employer fineos employer id", extra=resolver.log_attr
        )
        self.mock_logger.info.assert_any_call(fineos_emp_id, extra=resolver.log_attr)
        self.mock_logger.info.assert_any_call(
            "Updated Employer fineos id before inserting a new employer record",
            extra=resolver.log_attr,
        )
