import uuid
from datetime import date
from typing import List, Optional

import pytest

from massgov.pfml import db
from massgov.pfml.api.models.claims.responses import (
    AbsencePeriodResponse,
    EpisodicLeavePeriodResponse,
)
from massgov.pfml.api.validation.exceptions import ValidationException
from massgov.pfml.db.lookup_data.absences import (
    AbsencePeriodType,
    AbsenceReason,
    AbsenceReasonQualifierOne,
    AbsenceStatus,
)
from massgov.pfml.db.lookup_data.employees import LeaveRequestDecision
from massgov.pfml.db.models.absences import AbsencePeriod
from massgov.pfml.db.models.factories import AbsencePeriodFactory, ClaimFactory, EmployeeFactory
from massgov.pfml.db.queries.absence_periods import (
    FINEOS_ABSENCE_PERIOD_ID_CLASS_ID,
    calculate_modified_period_dates,
    convert_fineos_absence_period_to_claim_response_absence_period,
    create_absence_period_from_fineos_ids_and_claim_id,
    find_db_absence_period_for_fineos_absence_period,
    get_employee_absence_periods,
    split_fineos_absence_period_id,
    upsert_absence_period_from_fineos_period,
)
from massgov.pfml.fineos.models.customer_api import AbsencePeriod as FineosAbsencePeriod
from massgov.pfml.fineos.models.customer_api import AbsencePeriodDecision, EpisodicLeavePeriodDetail
from massgov.pfml.fineos.models.group_client_api import LeaveRequest, Period


def test_create_absence_period_from_fineos_ids_and_claim_id():

    claim_id = uuid.UUID("12345678-1234-5678-1234-************")

    # old format (client_id and index_id)
    class_id = 12345
    index_id = 67890
    period_id = None
    absence_period = create_absence_period_from_fineos_ids_and_claim_id(
        claim_id, class_id, index_id, period_id
    )
    assert isinstance(absence_period, AbsencePeriod)
    assert absence_period.claim_id == claim_id
    assert absence_period.fineos_absence_period_class_id == class_id
    assert absence_period.fineos_absence_period_index_id == index_id
    assert absence_period.fineos_absence_period_id == period_id

    # new format (just period_id)
    class_id = None
    index_id = None
    period_id = 76543
    absence_period = create_absence_period_from_fineos_ids_and_claim_id(
        claim_id, class_id, index_id, period_id
    )
    assert isinstance(absence_period, AbsencePeriod)
    assert absence_period.claim_id == claim_id
    assert absence_period.fineos_absence_period_class_id == class_id
    assert absence_period.fineos_absence_period_index_id == index_id
    assert absence_period.fineos_absence_period_id == period_id


@pytest.mark.parametrize(
    "absence_statuses",
    [None, [AbsenceStatus.APPROVED, AbsenceStatus.COMPLETED, AbsenceStatus.IN_REVIEW]],
)
@pytest.mark.parametrize("absence_period_type", [None, AbsencePeriodType.CONTINUOUS])
def test_get_employee_absence_periods(
    initialize_factories_session,
    test_db_session: db.Session,
    absence_statuses: Optional[List[AbsenceStatus]],
    absence_period_type: Optional[AbsencePeriodType],
):
    """
    The main logic we are testing is whether the conditionals are
    entered. There are a lot of scenarios to consider
    2^(7+1) * (10+1) = 256 * 11 = 2,816
    Above cases meant to cover the initial use case and some edge cases.
    """
    absence_period_type_id = (
        absence_period_type.absence_period_type_id if absence_period_type else None
    )
    absence_status_ids = (
        [absence_status.absence_status_id for absence_status in absence_statuses]
        if absence_statuses
        else None
    )

    # Create dummy claims for other employees
    for _ in range(3):
        claim = ClaimFactory.create()
        AbsencePeriodFactory.create(claim=claim)

    employee = EmployeeFactory.create()
    expected_number_of_absence_periods = 0

    # Create dummy claims for same employee
    for _ in range(3):
        claim = ClaimFactory.create(employee=employee)
        # AbsencePeriod Will only appear in results
        # when absence status and absence period type are undefined
        AbsencePeriodFactory.create(claim=claim)
        if (
            absence_status_ids is None or len(absence_status_ids) == 0
        ) and absence_period_type is None:
            expected_number_of_absence_periods += 1

    # Create some absence period periods on the claim to filter on
    for absence_status_id in absence_status_ids if absence_status_ids is not None else []:
        claim = ClaimFactory.create(employee=employee, fineos_absence_status_id=absence_status_id)
        AbsencePeriodFactory.create(claim=claim, absence_period_type_id=absence_period_type_id)
        expected_number_of_absence_periods += 1

        # AbsencePeriod Will only appear in results
        # when absence period type is undefined
        AbsencePeriodFactory.create(claim=claim)
        if absence_period_type is None:
            expected_number_of_absence_periods += 1

    absence_periods = get_employee_absence_periods(
        test_db_session, employee.employee_id, absence_status_ids, absence_period_type_id
    )

    assert len(absence_periods) == expected_number_of_absence_periods


class TestAbsencePeriodHelpers:
    @pytest.fixture
    def db_absence_periods(self, initialize_factories_session):
        claim = ClaimFactory.create()

        # using absence_reason_id values for unique identifier to avoid UUIDs
        return [
            AbsencePeriodFactory.create(
                absence_reason_id=1,
                fineos_absence_period_class_id=14449,
                fineos_absence_period_index_id=10001,
                claim=claim,
            ),
            AbsencePeriodFactory.create(
                absence_reason_id=2,
                fineos_absence_period_class_id=14449,
                fineos_absence_period_index_id=10002,
                claim=claim,
            ),
            AbsencePeriodFactory.create(
                absence_reason_id=3,
                fineos_absence_period_class_id=14448,
                fineos_absence_period_index_id=10001,
                claim=claim,
            ),
            AbsencePeriodFactory.create(
                absence_reason_id=4,
                fineos_absence_period_class_id=14474,
                fineos_absence_period_index_id=10001,
                claim=claim,
            ),
            AbsencePeriodFactory.create(
                absence_reason_id=5,
                fineos_absence_period_class_id=FINEOS_ABSENCE_PERIOD_ID_CLASS_ID,
                fineos_absence_period_index_id=10001,
                claim=claim,
            ),
            AbsencePeriodFactory.create(
                absence_reason_id=6,
                fineos_absence_period_class_id=FINEOS_ABSENCE_PERIOD_ID_CLASS_ID,
                fineos_absence_period_index_id=10002,
                claim=claim,
            ),
        ]

    @pytest.mark.parametrize(
        "id_to_parse,class_id,index_id,period_id",
        (
            ("PL-14448-12345", 14448, 12345, None),
            ("PL-14449-23456", 14449, 23456, None),
            ("PL-14474-34567", 14474, 34567, None),
            ("PL-12345-67890-", 12345, 67890, None),
            ("PL-12345-67890-98765", 12345, 67890, None),
            ("PL-12345-67890-ABC", 12345, 67890, None),
            ("45678", FINEOS_ABSENCE_PERIOD_ID_CLASS_ID, 45678, 45678),
        ),
    )
    def test_split_fineos_absence_period_id(self, id_to_parse, class_id, index_id, period_id):
        assert split_fineos_absence_period_id(id_to_parse) == (class_id, index_id, period_id)

    @pytest.mark.parametrize(
        "period_id",
        (
            ("PL"),
            ("PL-"),
            ("-12345"),
            ("PL-12345"),
            ("PL-ABC-XYZ"),
            ("PL-ABC-123"),
            ("PL-123-XYZ"),
        ),
    )
    def test_split_fineos_absence_period_id__invalid_id_throws_error(self, period_id):
        with pytest.raises(ValidationException):
            split_fineos_absence_period_id(period_id)

    @pytest.mark.parametrize(
        "fineos_absense_id,expected_reason_id",
        (
            ("PL-14449-10001", 1),
            ("PL-14449-10002", 2),
            ("PL-14449-10003", None),
            ("PL-14448-10001", 3),
            ("PL-14448-10002", None),
            ("PL-14474-10001", 4),
            ("PL-14474-10002", None),
            ("10001", 5),
            ("10002", 6),
            ("10003", None),
        ),
    )
    def test_find_db_absence_period_for_fineos_absence_period(
        self,
        initialize_factories_session,
        db_absence_periods,
        fineos_absense_id,
        expected_reason_id,
    ):
        fineos_absence_period = FineosAbsencePeriod(id=fineos_absense_id)

        db_absence_period = find_db_absence_period_for_fineos_absence_period(
            fineos_absence_period, db_absence_periods
        )
        if expected_reason_id is None:
            assert db_absence_period is None
        else:
            assert db_absence_period is not None
            assert db_absence_period.absence_reason_id == expected_reason_id


class TestCalculateModifiedPeriodDates:
    @pytest.fixture
    def fineos_absence_period(self):
        return Period(
            id="PL-14449-0000002237",
            periodReference="PL-14449-0000002237",
            reason="Child Bonding",
            reasonQualifier1="Foster Care",
            reasonQualifier2="",
            startDate=date(2021, 1, 29),
            endDate=date(2021, 1, 30),
            absenceType="Continuous",
            requestStatus="Approved",
            leaveRequest=LeaveRequest(id="PL-14449-0000002237", decisionStatus="Approved"),
        )

    @pytest.fixture
    def fineos_absence_period_decisions(self):
        return [
            AbsencePeriodDecision(
                periodId="PL-14449-0000002237",
                startDate=date(2021, 1, 15),
                endDate=date(2021, 1, 20),
                absencePeriodStatus="Known",
            ),
            AbsencePeriodDecision(
                periodId="PL-14449-0000002237",
                startDate=date(2021, 1, 21),
                endDate=date(2021, 1, 25),
                absencePeriodStatus="Known",
            ),
            AbsencePeriodDecision(
                periodId="PL-14449-0000002237",
                startDate=date(2021, 1, 26),
                endDate=date(2021, 2, 15),
                absencePeriodStatus="Known",
            ),
        ]

    def test_success_no_cancelled_time(
        self, fineos_absence_period, fineos_absence_period_decisions
    ):
        modified_start_date, modified_end_date = calculate_modified_period_dates(
            fineos_absence_period, fineos_absence_period_decisions
        )

        assert modified_start_date == date(2021, 1, 15)
        assert modified_end_date == date(2021, 2, 15)

    def test_success_cancelled_time(self, fineos_absence_period, fineos_absence_period_decisions):
        cancelled_time = [
            AbsencePeriodDecision(
                periodId="PL-14449-0000002237",
                startDate=date(2021, 2, 16),
                endDate=date(2021, 2, 28),
                absencePeriodStatus="Known",
            ),
            AbsencePeriodDecision(
                periodId="PL-14449-0000002222",
                parentPeriodId="PL:14449:0000002237",
                startDate=date(2021, 2, 16),
                endDate=date(2021, 2, 28),
                absencePeriodStatus="Cancelled",
            ),
        ]
        fineos_absence_period_decisions += cancelled_time
        modified_start_date, modified_end_date = calculate_modified_period_dates(
            fineos_absence_period, fineos_absence_period_decisions
        )

        assert modified_start_date == date(2021, 1, 15)
        assert modified_end_date == date(2021, 2, 15)

    def test_success_partially_cancelled_time(
        self, fineos_absence_period, fineos_absence_period_decisions
    ):
        partially_cancelled_time = [
            AbsencePeriodDecision(
                periodId="PL-14449-0000002237",
                startDate=date(2021, 2, 16),
                endDate=date(2021, 2, 28),
                absencePeriodStatus="Known",
            ),
            AbsencePeriodDecision(
                periodId="PL-14449-0000002222",
                parentPeriodId="PL:14449:0000002237",
                startDate=date(2021, 2, 21),
                endDate=date(2021, 2, 28),
                absencePeriodStatus="Cancelled",
            ),
        ]
        fineos_absence_period_decisions += partially_cancelled_time
        modified_start_date, modified_end_date = calculate_modified_period_dates(
            fineos_absence_period, fineos_absence_period_decisions
        )

        assert modified_start_date == date(2021, 1, 15)
        assert modified_end_date == date(2021, 2, 20)

    @pytest.mark.parametrize("status", ["Denied", "Cancelled", "Withdrawn"])
    def test_success_cancelled_period(
        self, fineos_absence_period, fineos_absence_period_decisions, status
    ):
        fineos_absence_period.leaveRequest.decisionStatus = status
        modified_start_date, modified_end_date = calculate_modified_period_dates(
            fineos_absence_period, fineos_absence_period_decisions
        )
        assert modified_start_date is None
        assert modified_end_date is None

    def test_no_id_match(self, fineos_absence_period, fineos_absence_period_decisions):
        fineos_absence_period.periodReference = "nope"
        modified_start_date, modified_end_date = calculate_modified_period_dates(
            fineos_absence_period, fineos_absence_period_decisions
        )

        assert modified_start_date is None
        assert modified_end_date is None

    def test_empty_array(self, fineos_absence_period):
        modified_start_date, modified_end_date = calculate_modified_period_dates(
            fineos_absence_period, []
        )

        assert modified_start_date is None
        assert modified_end_date is None


class TestConvertFineosAbsencePeriodToClaimResponseAbsencePeriod:
    @pytest.fixture
    def fineos_absence_period(self):
        return FineosAbsencePeriod(
            id="PL-14449-0000002237",
            reason="Child Bonding",
            reasonQualifier1="Foster Care",
            reasonQualifier2="",
            startDate=date(2021, 1, 29),
            endDate=date(2021, 1, 30),
            absenceType="Continuous",
            requestStatus="Approved",
        )

    @pytest.fixture
    def fineos_absence_period_intermittent(self):
        episodic_leave_period_detail = EpisodicLeavePeriodDetail(
            duration=8,
            durationBasis="Hours",
            frequency=3,
            frequencyInterval=1,
            frequencyIntervalBasis="Weeks",
        )
        return FineosAbsencePeriod(
            id="PL-14449-0000002237",
            reason="Child Bonding",
            reasonQualifier1="Foster Care",
            reasonQualifier2="",
            startDate=date(2021, 1, 29),
            endDate=date(2021, 1, 30),
            absenceType="Intermittent",
            requestStatus="Approved",
            episodicLeavePeriodDetail=episodic_leave_period_detail,
        )

    @pytest.fixture
    def fineos_absence_period_decisions(self):
        return [
            AbsencePeriodDecision(
                periodId="PL-14449-0000002237",
                startDate=date(2021, 1, 15),
                endDate=date(2021, 2, 15),
                absencePeriodStatus="Known",
            ),
            AbsencePeriodDecision(
                periodId="PL-14449-0000002237",
                startDate=date(2021, 2, 15),
                endDate=date(2021, 2, 28),
                absencePeriodStatus="Cancelled",
            ),
        ]

    def test_success(self, fineos_absence_period, fineos_absence_period_decisions):
        absence_period_response = convert_fineos_absence_period_to_claim_response_absence_period(
            fineos_absence_period, {}, fineos_absence_period_decisions
        )

        assert type(absence_period_response) == AbsencePeriodResponse
        assert absence_period_response.absence_period_start_date == date(2021, 1, 29)
        assert absence_period_response.absence_period_end_date == date(2021, 1, 30)
        assert absence_period_response.modified_start_date == date(2021, 1, 15)
        assert absence_period_response.modified_end_date == date(2021, 2, 15)
        assert absence_period_response.period_type == "Continuous"
        assert absence_period_response.reason == "Child Bonding"
        assert absence_period_response.reason_qualifier_one == "Foster Care"
        assert absence_period_response.reason_qualifier_two is None
        assert absence_period_response.request_decision == "Approved"

    def test_success_no_absence_period_decisions(self, fineos_absence_period):
        absence_period_response = convert_fineos_absence_period_to_claim_response_absence_period(
            fineos_absence_period, {}
        )

        assert type(absence_period_response) == AbsencePeriodResponse
        assert absence_period_response.absence_period_start_date == date(2021, 1, 29)
        assert absence_period_response.absence_period_end_date == date(2021, 1, 30)
        assert absence_period_response.modified_start_date is None
        assert absence_period_response.modified_end_date is None
        assert absence_period_response.period_type == "Continuous"
        assert absence_period_response.reason == "Child Bonding"
        assert absence_period_response.reason_qualifier_one == "Foster Care"
        assert absence_period_response.reason_qualifier_two is None
        assert absence_period_response.request_decision == "Approved"

    def test_success_episodic_leave_period_detail_exists(self, fineos_absence_period_intermittent):
        absence_period_response = convert_fineos_absence_period_to_claim_response_absence_period(
            fineos_absence_period_intermittent, {}
        )

        assert (
            type(absence_period_response.episodic_leave_period_detail)
            == EpisodicLeavePeriodResponse
        )

        assert (
            absence_period_response.fineos_absence_period_id
            == fineos_absence_period_intermittent.id
        )
        assert (
            absence_period_response.episodic_leave_period_detail.duration
            == fineos_absence_period_intermittent.episodicLeavePeriodDetail.duration
        )
        assert (
            absence_period_response.episodic_leave_period_detail.duration_basis
            == fineos_absence_period_intermittent.episodicLeavePeriodDetail.durationBasis
        )
        assert (
            absence_period_response.episodic_leave_period_detail.end_date
            == fineos_absence_period_intermittent.endDate
        )
        assert (
            absence_period_response.episodic_leave_period_detail.frequency
            == fineos_absence_period_intermittent.episodicLeavePeriodDetail.frequency
        )
        assert (
            absence_period_response.episodic_leave_period_detail.frequency_interval
            == fineos_absence_period_intermittent.episodicLeavePeriodDetail.frequencyInterval
        )
        assert (
            absence_period_response.episodic_leave_period_detail.frequency_interval_basis
            == fineos_absence_period_intermittent.episodicLeavePeriodDetail.frequencyIntervalBasis
        )
        assert (
            absence_period_response.episodic_leave_period_detail.start_date
            == fineos_absence_period_intermittent.startDate
        )

    def test_success_episodic_leave_period_detail_null(self, fineos_absence_period):
        absence_period_response = convert_fineos_absence_period_to_claim_response_absence_period(
            fineos_absence_period, {}
        )

        assert absence_period_response.fineos_absence_period_id == fineos_absence_period.id
        assert absence_period_response.episodic_leave_period_detail is None

    def test_incomplete_fineos_episodic_leave_period_detail_exists(
        self, fineos_absence_period_intermittent
    ):

        fineos_absence_period_intermittent.episodicLeavePeriodDetail.durationBasis = None
        fineos_absence_period_intermittent.episodicLeavePeriodDetail.frequencyIntervalBasis = None

        absence_period_response = convert_fineos_absence_period_to_claim_response_absence_period(
            fineos_absence_period_intermittent, {}
        )

        assert absence_period_response.episodic_leave_period_detail is None


class TestUpsertAbsencePeriodFromFineosPeriod:
    @pytest.fixture
    def fineos_absence_period(self):
        return FineosAbsencePeriod(
            id="PL-14449-0000002237",
            reason="Child Bonding",
            reasonQualifier1="Foster Care",
            reasonQualifier2="",
            startDate=date(2021, 1, 29),
            endDate=date(2021, 1, 30),
            absenceType="Continuous",
            requestStatus="Approved",
        )

    @pytest.fixture
    def fineos_absence_period_decisions(self):
        return [
            AbsencePeriodDecision(
                periodId="PL-14449-0000002237",
                startDate=date(2021, 1, 15),
                endDate=date(2021, 2, 15),
                absencePeriodStatus="Known",
            ),
            AbsencePeriodDecision(
                periodId="PL-14449-0000002237",
                startDate=date(2021, 2, 15),
                endDate=date(2021, 2, 28),
                absencePeriodStatus="Cancelled",
            ),
        ]

    def test_success(
        self, test_db_session, claim, fineos_absence_period, fineos_absence_period_decisions
    ):
        db_absence_period = upsert_absence_period_from_fineos_period(
            test_db_session,
            claim.claim_id,
            fineos_absence_period,
            {},
            fineos_absence_period_decisions,
        )

        assert type(db_absence_period) == AbsencePeriod
        assert db_absence_period.absence_period_start_date == date(2021, 1, 29)
        assert db_absence_period.absence_period_end_date == date(2021, 1, 30)
        assert db_absence_period.modified_start_date == date(2021, 1, 15)
        assert db_absence_period.modified_end_date == date(2021, 2, 15)
        assert (
            db_absence_period.absence_period_type_id
            == AbsencePeriodType.CONTINUOUS.absence_period_type_id
        )
        assert db_absence_period.absence_reason_id == AbsenceReason.CHILD_BONDING.absence_reason_id
        assert (
            db_absence_period.absence_reason_qualifier_one_id
            == AbsenceReasonQualifierOne.FOSTER_CARE.absence_reason_qualifier_one_id
        )
        assert db_absence_period.absence_reason_qualifier_two_id is None
        assert (
            db_absence_period.leave_request_decision_id
            == LeaveRequestDecision.APPROVED.leave_request_decision_id
        )

        assert db_absence_period.fineos_absence_period_class_id == 14449
        assert db_absence_period.fineos_absence_period_index_id == 2237
        assert db_absence_period.fineos_absence_period_id is None

    @pytest.mark.parametrize(
        "testcase_fineos_absence_period_id, expected_class_id, expected_index_id, expected_period_id",
        [
            ("PL-14449-0000002237", 14449, 2237, None),
            ("PL-14449-12345", 14449, 12345, None),
            ("12345", FINEOS_ABSENCE_PERIOD_ID_CLASS_ID, 12345, 12345),
        ],
    )
    def test_success_fineos_id_variants(
        self,
        test_db_session,
        claim,
        fineos_absence_period,
        fineos_absence_period_decisions,
        testcase_fineos_absence_period_id,
        expected_class_id,
        expected_index_id,
        expected_period_id,
    ):
        # prep scenario
        fineos_absence_period.id = testcase_fineos_absence_period_id
        # child decisions need the same parent absecne period id
        for decision in fineos_absence_period_decisions:
            decision.periodId = testcase_fineos_absence_period_id

        db_absence_period = upsert_absence_period_from_fineos_period(
            test_db_session,
            claim.claim_id,
            fineos_absence_period,
            {},
            fineos_absence_period_decisions,
        )

        assert type(db_absence_period) == AbsencePeriod
        assert db_absence_period.fineos_absence_period_class_id == expected_class_id
        assert db_absence_period.fineos_absence_period_index_id == expected_index_id
        assert db_absence_period.fineos_absence_period_id == expected_period_id
        assert db_absence_period.absence_period_start_date == date(2021, 1, 29)
        assert db_absence_period.absence_period_end_date == date(2021, 1, 30)
        assert db_absence_period.modified_start_date == date(2021, 1, 15)
        assert db_absence_period.modified_end_date == date(2021, 2, 15)
        assert (
            db_absence_period.absence_period_type_id
            == AbsencePeriodType.CONTINUOUS.absence_period_type_id
        )
        assert db_absence_period.absence_reason_id == AbsenceReason.CHILD_BONDING.absence_reason_id
        assert (
            db_absence_period.absence_reason_qualifier_one_id
            == AbsenceReasonQualifierOne.FOSTER_CARE.absence_reason_qualifier_one_id
        )
        assert db_absence_period.absence_reason_qualifier_two_id is None
        assert (
            db_absence_period.leave_request_decision_id
            == LeaveRequestDecision.APPROVED.leave_request_decision_id
        )

    def test_success_no_absence_period_decisions(
        self, test_db_session, claim, fineos_absence_period
    ):
        db_absence_period = upsert_absence_period_from_fineos_period(
            test_db_session, claim.claim_id, fineos_absence_period, {}, []
        )

        assert type(db_absence_period) == AbsencePeriod
        assert db_absence_period.absence_period_start_date == date(2021, 1, 29)
        assert db_absence_period.absence_period_end_date == date(2021, 1, 30)
        assert db_absence_period.modified_start_date is None
        assert db_absence_period.modified_end_date is None
        assert (
            db_absence_period.absence_period_type_id
            == AbsencePeriodType.CONTINUOUS.absence_period_type_id
        )
        assert db_absence_period.absence_reason_id == AbsenceReason.CHILD_BONDING.absence_reason_id
        assert (
            db_absence_period.absence_reason_qualifier_one_id
            == AbsenceReasonQualifierOne.FOSTER_CARE.absence_reason_qualifier_one_id
        )
        assert db_absence_period.absence_reason_qualifier_two_id is None
        assert (
            db_absence_period.leave_request_decision_id
            == LeaveRequestDecision.APPROVED.leave_request_decision_id
        )
