from datetime import date, datetime

import pytest
from dateutil.relativedelta import relativedelta
from freezegun import freeze_time

from massgov.pfml.db.lookup_data.employees import LeaveRequestDecision
from massgov.pfml.db.lookup_data.verifications import VerificationType
from massgov.pfml.db.models.absences import A<PERSON><PERSON><PERSON>eriod
from massgov.pfml.db.models.employees import <PERSON><PERSON>m, UserLeaveAdministrator
from massgov.pfml.db.models.factories import (
    AbsencePeriodFactory,
    DuaEmployeeDemographicsFactory,
    DuaReportingUnitFactory,
    EmployeeFactory,
    EmployeeOccupationFactory,
    EmployeeWithFineosNumberFactory,
    EmployerDORExemptionFactory,
    EmployerFactory,
    EmployerQuarterlyContributionFactory,
    FamilyExemptEmployerAmendmentFactory,
    FamilyExemptEmployerFactory,
    FamilyExemptPyMedicalExemptCyEmployerFactory,
    FullyExemptEmployerAmendmentFactory,
    FullyExemptEmployerFactory,
    ImportLogFactory,
    MedicalExemptEmployerAmendmentFactory,
    MedicalExemptEmployerFactory,
    NonExemptEmployerFactory,
    OrganizationUnitFactory,
    PartiallyExemptEmployerAmendmentFactory,
    PartiallyExemptEmployerFactory,
    UserFactory,
    UserLeaveAdministratorActionFactory,
    UserLeaveAdministratorFactory,
    UserLeaveAdministratorVerifiedFactory,
    VerificationFactory,
)


@pytest.fixture()
def absence_period_with_final_decision(claim):
    absence_period = AbsencePeriodFactory.create(
        claim=claim,
        leave_request_decision_id=LeaveRequestDecision.CANCELLED.leave_request_decision_id,
    )
    return absence_period


@pytest.fixture()
def first_day_of_previous_year():
    return date(date.today().year - 1, 1, 1)


@pytest.fixture()
def first_day_of_current_year():
    return date(date.today().year, 1, 1)


@pytest.fixture()
def first_day_of_next_year():
    return date(date.today().year + 1, 1, 1)


@pytest.fixture()
def last_day_of_previous_year():
    return date(date.today().year - 1, 12, 31)


@pytest.fixture()
def last_day_of_current_year():
    return date(date.today().year, 12, 31)


@pytest.fixture()
def date_in_current_year():
    return date(date.today().year, 11, 13)


@pytest.fixture()
def date_in_previous_year():
    return date(date.today().year - 1, 11, 13)


def test_user_leave_admin_has_fineos_registration():
    user_leave_admin = UserLeaveAdministrator(
        user=UserFactory.build(), employer=EmployerFactory.build()
    )
    assert user_leave_admin.has_fineos_registration is False

    user_leave_admin.fineos_web_id = "fake-fineos-web-id"
    assert user_leave_admin.has_fineos_registration is True


VERIFICATION_DATA_DATE = date(2021, 5, 1)


@freeze_time(VERIFICATION_DATA_DATE)
@pytest.mark.parametrize(
    "filing_period, amount, expect_verification_data",
    [
        (
            # Past non-zero contribution
            VERIFICATION_DATA_DATE - relativedelta(months=1),
            100,
            True,
        ),
        (
            # Past zero contribution
            VERIFICATION_DATA_DATE - relativedelta(months=1),
            0,
            False,
        ),
        (
            # Same day non-zero contribution
            VERIFICATION_DATA_DATE,
            100,
            True,
        ),
        (
            # Future non-zero contribution, with no past contributions
            VERIFICATION_DATA_DATE + relativedelta(months=1),
            100,
            True,
        ),
        (
            # Future zero contribution, with no past contributions
            VERIFICATION_DATA_DATE + relativedelta(months=1),
            0,
            False,
        ),
    ],
)
def test_employer_with_verification_data(
    filing_period, amount, expect_verification_data, initialize_factories_session
):
    employer = EmployerFactory.create()
    EmployerQuarterlyContributionFactory.create(
        employer=employer, employer_total_pfml_contribution=amount, filing_period=filing_period
    )

    assert employer.has_verification_data is expect_verification_data

    # This should be consistent with the above
    if employer.has_verification_data:
        assert employer.verification_data is not None
    else:
        assert employer.verification_data is None


class TestUserLeaveAdministrator:
    def test_unverified_attributes(self):
        user_leave_admin = UserLeaveAdministratorFactory.build()
        assert user_leave_admin.verified is False
        assert user_leave_admin.verified_at is None
        assert user_leave_admin.verification_type is None

    def test_verified_attributes(self, initialize_factories_session):
        created_at = datetime(2022, 5, 3)
        verification = VerificationFactory.create(
            created_at=created_at, verification_type_id=VerificationType.ADD.verification_type_id
        )
        user_leave_admin = UserLeaveAdministratorVerifiedFactory.create(verification=verification)
        assert user_leave_admin.verified is True
        assert user_leave_admin.verified_at == created_at
        assert (
            user_leave_admin.verification_type == VerificationType.ADD.verification_type_description
        )

    def test_added_at(self, initialize_factories_session):
        added_at = datetime(2022, 5, 3)

        action = UserLeaveAdministratorActionFactory.create(
            created_at=added_at,
            recipient_user_leave_administrator_id=UserLeaveAdministratorVerifiedFactory.create().user_leave_administrator_id,
        )

        user_leave_admin = action.recipient_user_leave_administrator
        assert user_leave_admin.added_at == added_at
        assert user_leave_admin.added_by == action.user

    def test_added_at_with_unverified_leave_admin(self, initialize_factories_session):
        added_at = datetime(2022, 5, 3)

        action = UserLeaveAdministratorActionFactory.create(
            created_at=added_at,
            recipient_user_leave_administrator_id=UserLeaveAdministratorFactory.create().user_leave_administrator_id,
        )

        user_leave_admin = action.recipient_user_leave_administrator
        assert user_leave_admin.added_at is None
        assert user_leave_admin.added_by is None


def test_user_verified_employers(initialize_factories_session):
    user = UserFactory.create()
    UserLeaveAdministratorFactory.create(user=user)
    verified_active_employer = UserLeaveAdministratorVerifiedFactory.create(user=user)
    UserLeaveAdministratorFactory.create(user=user, deactivated=True)
    UserLeaveAdministratorVerifiedFactory.create(user=user, deactivated=True)

    employers = user.verified_employers
    assert [e.employer_id for e in employers] == [verified_active_employer.employer_id]


def test_user_verified_leave_admins(initialize_factories_session):
    user = UserFactory.create()
    UserLeaveAdministratorFactory.create(user=user)
    verified_active_leave_admin = UserLeaveAdministratorVerifiedFactory.create(user=user)
    UserLeaveAdministratorFactory.create(user=user, deactivated=True)
    UserLeaveAdministratorVerifiedFactory.create(user=user, deactivated=True)

    leave_admins = user.verified_leave_admins
    assert [la.user_leave_administrator_id for la in leave_admins] == [
        verified_active_leave_admin.user_leave_administrator_id
    ]


def test_user_is_worker_user(user, employer_user):
    assert employer_user.is_worker_user is False
    assert user.is_worker_user is True


@pytest.mark.parametrize(
    ["leave_request_decision_id", "expected_has_final_decision"],
    [
        [LeaveRequestDecision.PENDING.leave_request_decision_id, False],
        [LeaveRequestDecision.IN_REVIEW.leave_request_decision_id, False],
        [LeaveRequestDecision.APPROVED.leave_request_decision_id, True],
        [LeaveRequestDecision.DENIED.leave_request_decision_id, True],
        [LeaveRequestDecision.CANCELLED.leave_request_decision_id, True],
        [LeaveRequestDecision.WITHDRAWN.leave_request_decision_id, True],
        [LeaveRequestDecision.PROJECTED.leave_request_decision_id, False],
        [LeaveRequestDecision.VOIDED.leave_request_decision_id, True],
        [LeaveRequestDecision.UNKNOWN.leave_request_decision_id, True],
        [None, False],
    ],
)
def test_has_final_decision_by_request_decision_id(
    test_db_session,
    initialize_factories_session,
    claim,
    leave_request_decision_id,
    expected_has_final_decision,
):
    absence_period = AbsencePeriodFactory.create(
        claim=claim,
        leave_request_decision_id=leave_request_decision_id,
    )
    assert absence_period.has_final_decision == expected_has_final_decision

    if expected_has_final_decision:
        filtered_claims = (
            test_db_session.query(Claim)
            .filter(~Claim.absence_periods.any(~AbsencePeriod.has_final_decision))  # type:ignore
            .all()
        )
        assert len(filtered_claims) == 1

        queried_absence_periods_with_final_decision = (
            test_db_session.query(AbsencePeriod).filter(AbsencePeriod.has_final_decision).all()
        )
        assert len(queried_absence_periods_with_final_decision) == 1
        assert (
            queried_absence_periods_with_final_decision[0].absence_period_id
            == absence_period.absence_period_id
        )
        assert len(claim.absence_periods) == 1
    else:
        assert len(claim.absence_periods) == 1
        filtered_claims = (
            test_db_session.query(Claim)
            .filter(Claim.absence_periods.any(~AbsencePeriod.has_final_decision))  # type: ignore
            .all()
        )
        assert len(filtered_claims) == 1

        queried_absence_periods_without_final_decision = (
            test_db_session.query(AbsencePeriod)
            .filter(~AbsencePeriod.has_final_decision)  # type: ignore
            .all()
        )
        assert len(queried_absence_periods_without_final_decision) == 1
        assert (
            queried_absence_periods_without_final_decision[0].absence_period_id
            == absence_period.absence_period_id
        )


def test_has_final_decision(
    test_db_session, initialize_factories_session, absence_period_with_final_decision
):
    absence_period = test_db_session.query(AbsencePeriod).first()
    assert (
        absence_period.leave_request_decision_id
        == LeaveRequestDecision.CANCELLED.leave_request_decision_id
    )

    assert absence_period.has_final_decision is True

    absence_period.leave_request_decision_id = (
        LeaveRequestDecision.PENDING.leave_request_decision_id
    )
    test_db_session.commit()
    assert absence_period.has_final_decision is False


def test_employee_get_organization_units_no_fineos_customer_number(
    test_db_session, initialize_factories_session
):
    employee = EmployeeFactory.create()
    employer = EmployerFactory.create()

    org_units = employee.get_organization_units(employer)

    assert org_units == []


def test_employee_get_organization_units_for_unassociated_employer(
    test_db_session, initialize_factories_session
):
    employee = EmployeeWithFineosNumberFactory.create()
    employer = EmployerFactory.create()

    org_units = employee.get_organization_units(employer)

    assert org_units == []


def test_employee_get_organization_units_not_if_org_unit_is_not_on_occupation_or_dua(
    test_db_session, initialize_factories_session
):
    employee = EmployeeWithFineosNumberFactory.create()
    employer = EmployerFactory.create()

    EmployeeOccupationFactory.create(employee=employee, employer=employer)

    OrganizationUnitFactory(employer_id=employer.employer_id)

    org_units = employee.get_organization_units(employer)

    assert org_units == []


def test_employee_get_organization_units_not_if_org_unit_does_not_have_fineos_id(
    test_db_session, initialize_factories_session
):
    employee = EmployeeWithFineosNumberFactory.create()
    employer = EmployerFactory.create()
    org_unit = OrganizationUnitFactory(employer_id=employer.employer_id, fineos_id=None)

    EmployeeOccupationFactory.create(
        employee=employee, employer=employer, organization_unit=org_unit
    )

    org_units = employee.get_organization_units(employer)

    assert org_units == []


def test_employee_get_organization_units_success_through_occupation(
    test_db_session, initialize_factories_session
):
    employee = EmployeeWithFineosNumberFactory.create()
    employer = EmployerFactory.create()
    org_unit = OrganizationUnitFactory(employer_id=employer.employer_id)

    EmployeeOccupationFactory.create(
        employee=employee, employer=employer, organization_unit=org_unit
    )

    assert org_unit.fineos_id is not None
    assert employee.fineos_customer_number is not None

    # an unassociated org unit
    OrganizationUnitFactory.create(employer_id=employer.employer_id)

    org_units = employee.get_organization_units(employer)

    assert len(org_units) == 1
    assert org_units[0].organization_unit_id == org_unit.organization_unit_id


def test_employee_get_organization_units_none_if_no_dua_reporting_unit(
    test_db_session, initialize_factories_session
):
    employee = EmployeeWithFineosNumberFactory.create()
    employer = EmployerFactory.create()
    org_unit = OrganizationUnitFactory.create(employer_id=employer.employer_id)

    assert org_unit.fineos_id is not None
    assert employee.fineos_customer_number is not None

    # demographic record connecting employee and employer, but not
    # `DuaReportingUnit` entry means it's not returned
    DuaEmployeeDemographicsFactory.create(
        fineos_customer_number=employee.fineos_customer_number,
        employer_fein=employer.employer_fein,
        employer_reporting_unit_number="1",
    )

    org_units = employee.get_organization_units(employer)

    assert org_units == []


def test_employee_get_organization_units_none_if_dua_reporting_unit_employer_incorrect(
    test_db_session, initialize_factories_session
):
    employee = EmployeeWithFineosNumberFactory.create()
    employer = EmployerFactory.create()
    org_unit = OrganizationUnitFactory.create(employer_id=employer.employer_id)

    assert org_unit.fineos_id is not None
    assert employee.fineos_customer_number is not None

    employer_incorrect = EmployerFactory.create()

    reporting_unit_with_org_unit_but_incorrect_employer = DuaReportingUnitFactory.create(
        organization_unit=org_unit, employer_id=employer_incorrect.employer_id
    )

    DuaEmployeeDemographicsFactory.create(
        fineos_customer_number=employee.fineos_customer_number,
        employer_fein=employer.employer_fein,
        employer_reporting_unit_number=reporting_unit_with_org_unit_but_incorrect_employer.dua_id,
    )

    org_units = employee.get_organization_units(employer)

    assert org_units == []


def test_employee_get_organization_units_success_through_dua_demographics(
    test_db_session, initialize_factories_session
):
    employee = EmployeeWithFineosNumberFactory.create()
    employer = EmployerFactory.create()
    org_unit = OrganizationUnitFactory.create(employer_id=employer.employer_id)

    assert org_unit.fineos_id is not None
    assert employee.fineos_customer_number is not None

    reporting_unit_with_org_unit = DuaReportingUnitFactory.create(
        organization_unit=org_unit, employer_id=employer.employer_id
    )

    DuaEmployeeDemographicsFactory.create(
        fineos_customer_number=employee.fineos_customer_number,
        employer_fein=employer.employer_fein,
        employer_reporting_unit_number=reporting_unit_with_org_unit.dua_id,
    )

    # an unassociated org unit
    OrganizationUnitFactory.create(employer_id=employer.employer_id)

    org_units = employee.get_organization_units(employer)

    assert len(org_units) == 1
    assert org_units[0].organization_unit_id == org_unit.organization_unit_id


def test_employee_get_organization_units_success_through_both_occupation_and_dua_demographics(
    test_db_session, initialize_factories_session
):
    employee = EmployeeWithFineosNumberFactory.create()
    employer = EmployerFactory.create()

    occupation_org_unit = OrganizationUnitFactory(employer_id=employer.employer_id)

    EmployeeOccupationFactory.create(
        employee=employee, employer=employer, organization_unit=occupation_org_unit
    )

    dua_org_unit = OrganizationUnitFactory.create(employer_id=employer.employer_id)

    reporting_unit_with_org_unit = DuaReportingUnitFactory.create(
        organization_unit=dua_org_unit, employer_id=employer.employer_id
    )

    DuaEmployeeDemographicsFactory.create(
        fineos_customer_number=employee.fineos_customer_number,
        employer_fein=employer.employer_fein,
        employer_reporting_unit_number=reporting_unit_with_org_unit.dua_id,
    )

    # an unassociated org unit
    OrganizationUnitFactory.create(employer_id=employer.employer_id)

    org_units = employee.get_organization_units(employer)
    org_unit_ids = list(map(lambda o: o.organization_unit_id, org_units))

    assert len(org_units) == 2
    assert occupation_org_unit.organization_unit_id in org_unit_ids
    assert dua_org_unit.organization_unit_id in org_unit_ids


def test_employee_get_organization_units_success_through_both_occupation_and_dua_demographics_same_org_unit(
    test_db_session, initialize_factories_session
):
    employee = EmployeeWithFineosNumberFactory.create()
    employer = EmployerFactory.create()

    shared_org_unit = OrganizationUnitFactory(employer_id=employer.employer_id)

    EmployeeOccupationFactory.create(
        employee=employee, employer=employer, organization_unit=shared_org_unit
    )

    reporting_unit_with_org_unit = DuaReportingUnitFactory.create(
        organization_unit=shared_org_unit, employer_id=employer.employer_id
    )

    DuaEmployeeDemographicsFactory.create(
        fineos_customer_number=employee.fineos_customer_number,
        employer_fein=employer.employer_fein,
        employer_reporting_unit_number=reporting_unit_with_org_unit.dua_id,
    )

    # an unassociated org unit
    OrganizationUnitFactory.create(employer_id=employer.employer_id)

    org_units = employee.get_organization_units(employer)

    assert len(org_units) == 1
    assert org_units[0].organization_unit_id == shared_org_unit.organization_unit_id


def test_is_exempt_family(
    initialize_factories_session,
):
    family_exempt_employer = FamilyExemptEmployerFactory.create()
    family_exempt_employer_amendment = FamilyExemptEmployerAmendmentFactory.create()
    non_exempt_employer = NonExemptEmployerFactory.create()
    assert family_exempt_employer.is_exempt_family is True
    assert family_exempt_employer_amendment.is_exempt_family is True
    assert non_exempt_employer.is_exempt_family is False


def test_is_exempt_medical(
    initialize_factories_session,
):
    medical_exempt_employer = MedicalExemptEmployerFactory.create()
    medical_exempt_employer_amendment = MedicalExemptEmployerAmendmentFactory.create()
    non_exempt_employer = NonExemptEmployerFactory.create()
    assert medical_exempt_employer.is_exempt_medical is True
    assert medical_exempt_employer_amendment.is_exempt_medical is True
    assert non_exempt_employer.is_exempt_medical is False


def test_employer_is_partially_exempt(
    initialize_factories_session,
):
    fully_exempt_employer = FullyExemptEmployerFactory.create()
    fully_exempt_employer_amendment = FullyExemptEmployerAmendmentFactory.create()
    partially_exempt_employer = PartiallyExemptEmployerFactory.create()
    partially_exempt_employer_amendment = PartiallyExemptEmployerAmendmentFactory.create()
    non_exempt_employer = NonExemptEmployerFactory.create()
    assert fully_exempt_employer.is_partially_exempt is False
    assert fully_exempt_employer_amendment.is_partially_exempt is False
    assert partially_exempt_employer.is_partially_exempt is True
    assert partially_exempt_employer_amendment.is_partially_exempt is True
    assert non_exempt_employer.is_partially_exempt is False


def test_employer_is_fully_exempt(
    initialize_factories_session,
):
    fully_exempt_employer = FullyExemptEmployerFactory.create()
    fully_exempt_employer_amendment = FullyExemptEmployerAmendmentFactory.create()
    partially_exempt_employer = PartiallyExemptEmployerFactory.create()
    partially_exempt_employer_amendment = PartiallyExemptEmployerAmendmentFactory.create()
    non_exempt_employer = NonExemptEmployerFactory.create()
    assert fully_exempt_employer.is_fully_exempt is True
    assert fully_exempt_employer_amendment.is_fully_exempt is True
    assert partially_exempt_employer.is_fully_exempt is False
    assert partially_exempt_employer_amendment.is_fully_exempt is False
    assert non_exempt_employer.is_fully_exempt is False


def test_employer_has_family_exemption(
    initialize_factories_session,
    first_day_of_previous_year,
    first_day_of_current_year,
    first_day_of_next_year,
    last_day_of_previous_year,
    last_day_of_current_year,
    date_in_current_year,
    date_in_previous_year,
):
    family_exempt_employer = FamilyExemptEmployerFactory.create()
    family_exempt_employer_amendment = FamilyExemptEmployerAmendmentFactory.create()
    family_py_medical_cy_employer = FamilyExemptPyMedicalExemptCyEmployerFactory.create()
    non_exempt_employer = NonExemptEmployerFactory.create()

    # effective date first day of previous year
    assert family_exempt_employer.has_family_exemption(effective_date=first_day_of_previous_year) is False  # fmt: skip
    assert family_exempt_employer_amendment.has_family_exemption(effective_date=first_day_of_previous_year) is False  # fmt: skip
    assert family_py_medical_cy_employer.has_family_exemption(effective_date=first_day_of_previous_year) is True  # fmt: skip
    assert non_exempt_employer.has_family_exemption(effective_date=first_day_of_previous_year) is False  # fmt: skip

    # effective date in previous year
    assert family_exempt_employer.has_family_exemption(effective_date=date_in_previous_year) is False  # fmt: skip
    assert family_exempt_employer_amendment.has_family_exemption(effective_date=date_in_previous_year) is False  # fmt: skip
    assert family_py_medical_cy_employer.has_family_exemption(effective_date=date_in_previous_year) is True  # fmt: skip
    assert non_exempt_employer.has_family_exemption(effective_date=date_in_previous_year) is False  # fmt: skip

    # effective date last day of previous year
    assert family_exempt_employer.has_family_exemption(effective_date=last_day_of_previous_year) is False  # fmt: skip
    assert family_exempt_employer_amendment.has_family_exemption(effective_date=last_day_of_previous_year) is False  # fmt: skip
    assert family_py_medical_cy_employer.has_family_exemption(effective_date=last_day_of_previous_year) is True  # fmt: skip
    assert non_exempt_employer.has_family_exemption(effective_date=last_day_of_previous_year) is False  # fmt: skip

    # effective date first day of current year
    assert family_exempt_employer.has_family_exemption(effective_date=first_day_of_current_year) is True  # fmt: skip
    assert family_exempt_employer_amendment.has_family_exemption(effective_date=first_day_of_current_year) is True  # fmt: skip
    assert family_py_medical_cy_employer.has_family_exemption(effective_date=first_day_of_current_year) is False  # fmt: skip
    assert non_exempt_employer.has_family_exemption(effective_date=first_day_of_current_year) is False  # fmt: skip

    # effective date in current year
    assert family_exempt_employer.has_family_exemption(effective_date=date_in_current_year) is True  # fmt: skip
    assert family_exempt_employer_amendment.has_family_exemption(effective_date=date_in_current_year) is True  # fmt: skip
    assert family_py_medical_cy_employer.has_family_exemption(effective_date=date_in_current_year) is False  # fmt: skip
    assert non_exempt_employer.has_family_exemption(effective_date=date_in_current_year) is False  # fmt: skip

    # effective date last day of current year
    assert family_exempt_employer.has_family_exemption(effective_date=last_day_of_current_year) is True  # fmt: skip
    assert family_exempt_employer_amendment.has_family_exemption(effective_date=last_day_of_current_year) is True  # fmt: skip
    assert family_py_medical_cy_employer.has_family_exemption(effective_date=last_day_of_current_year) is False  # fmt: skip
    assert non_exempt_employer.has_family_exemption(effective_date=last_day_of_current_year) is False  # fmt: skip

    # effective date first day of next year
    assert family_exempt_employer.has_family_exemption(effective_date=first_day_of_next_year) is False  # fmt: skip
    assert family_exempt_employer_amendment.has_family_exemption(effective_date=first_day_of_next_year) is False  # fmt: skip
    assert family_py_medical_cy_employer.has_family_exemption(effective_date=first_day_of_next_year) is False  # fmt: skip
    assert non_exempt_employer.has_family_exemption(effective_date=first_day_of_next_year) is False  # fmt: skip


def test_employer_has_medical_exemption(
    initialize_factories_session,
    first_day_of_previous_year,
    first_day_of_current_year,
    first_day_of_next_year,
    last_day_of_previous_year,
    last_day_of_current_year,
    date_in_current_year,
    date_in_previous_year,
):
    medical_exempt_employer = MedicalExemptEmployerFactory.create()
    medical_exempt_employer_amendment = MedicalExemptEmployerAmendmentFactory.create()
    family_py_medical_cy_employer = FamilyExemptPyMedicalExemptCyEmployerFactory.create()
    non_exempt_employer = NonExemptEmployerFactory.create()

    # effective date first day of previous year
    assert medical_exempt_employer.has_medical_exemption(effective_date=first_day_of_previous_year) is False  # fmt: skip
    assert medical_exempt_employer_amendment.has_medical_exemption(effective_date=first_day_of_previous_year) is False  # fmt: skip
    assert family_py_medical_cy_employer.has_medical_exemption(effective_date=first_day_of_previous_year) is False  # fmt: skip
    assert non_exempt_employer.has_medical_exemption(effective_date=first_day_of_previous_year) is False  # fmt: skip

    # effective date in previous year
    assert medical_exempt_employer.has_medical_exemption(effective_date=date_in_previous_year) is False  # fmt: skip
    assert medical_exempt_employer_amendment.has_medical_exemption(effective_date=date_in_previous_year) is False  # fmt: skip
    assert family_py_medical_cy_employer.has_medical_exemption(effective_date=date_in_previous_year) is False  # fmt: skip
    assert non_exempt_employer.has_medical_exemption(effective_date=date_in_previous_year) is False  # fmt: skip

    # effective date last day of previous year
    assert medical_exempt_employer.has_medical_exemption(effective_date=last_day_of_previous_year) is False  # fmt: skip
    assert medical_exempt_employer_amendment.has_medical_exemption(effective_date=last_day_of_previous_year) is False  # fmt: skip
    assert family_py_medical_cy_employer.has_medical_exemption(effective_date=last_day_of_previous_year) is False  # fmt: skip
    assert non_exempt_employer.has_medical_exemption(effective_date=last_day_of_previous_year) is False  # fmt: skip

    # effective date first day of current year
    assert medical_exempt_employer.has_medical_exemption(effective_date=first_day_of_current_year) is True  # fmt: skip
    assert medical_exempt_employer_amendment.has_medical_exemption(effective_date=first_day_of_current_year) is True  # fmt: skip
    assert family_py_medical_cy_employer.has_medical_exemption(effective_date=first_day_of_current_year) is True  # fmt: skip
    assert non_exempt_employer.has_medical_exemption(effective_date=first_day_of_current_year) is False  # fmt: skip

    # effective date in current year
    assert medical_exempt_employer.has_medical_exemption(effective_date=date_in_current_year) is True  # fmt: skip
    assert medical_exempt_employer_amendment.has_medical_exemption(effective_date=date_in_current_year) is True  # fmt: skip
    assert family_py_medical_cy_employer.has_medical_exemption(effective_date=date_in_current_year) is True  # fmt: skip
    assert non_exempt_employer.has_medical_exemption(effective_date=date_in_current_year) is False  # fmt: skip

    # effective date last day of current year
    assert medical_exempt_employer.has_medical_exemption(effective_date=last_day_of_current_year) is True  # fmt: skip
    assert medical_exempt_employer_amendment.has_medical_exemption(effective_date=last_day_of_current_year) is True  # fmt: skip
    assert family_py_medical_cy_employer.has_medical_exemption(effective_date=last_day_of_current_year) is True  # fmt: skip
    assert non_exempt_employer.has_medical_exemption(effective_date=last_day_of_current_year) is False  # fmt: skip

    # effective date first day of next year
    assert medical_exempt_employer.has_medical_exemption(effective_date=first_day_of_next_year) is False  # fmt: skip
    assert medical_exempt_employer_amendment.has_medical_exemption(effective_date=first_day_of_next_year) is False  # fmt: skip
    assert family_py_medical_cy_employer.has_medical_exemption(effective_date=first_day_of_next_year) is False  # fmt: skip
    assert non_exempt_employer.has_medical_exemption(effective_date=first_day_of_next_year) is False  # fmt: skip


def test_employer_has_partial_exemption(
    initialize_factories_session,
    first_day_of_previous_year,
    first_day_of_current_year,
    first_day_of_next_year,
    last_day_of_previous_year,
    last_day_of_current_year,
    date_in_current_year,
    date_in_previous_year,
):
    partially_exempt_employer = PartiallyExemptEmployerFactory.create()
    partially_exempt_employer_amendment = PartiallyExemptEmployerAmendmentFactory.create()
    family_py_medical_cy_employer = FamilyExemptPyMedicalExemptCyEmployerFactory.create()
    non_exempt_employer = NonExemptEmployerFactory.create()

    # effective date first day of previous year
    assert partially_exempt_employer.has_partial_exemption(effective_date=first_day_of_previous_year) is False  # fmt: skip
    assert partially_exempt_employer_amendment.has_partial_exemption(effective_date=first_day_of_previous_year) is False  # fmt: skip
    assert family_py_medical_cy_employer.has_partial_exemption(effective_date=first_day_of_previous_year) is True  # fmt: skip
    assert non_exempt_employer.has_partial_exemption(effective_date=first_day_of_previous_year) is False  # fmt: skip

    # effective date in previous year
    assert partially_exempt_employer.has_partial_exemption(effective_date=date_in_previous_year) is False  # fmt: skip
    assert partially_exempt_employer_amendment.has_partial_exemption(effective_date=date_in_previous_year) is False  # fmt: skip
    assert family_py_medical_cy_employer.has_partial_exemption(effective_date=date_in_previous_year) is True  # fmt: skip
    assert non_exempt_employer.has_partial_exemption(effective_date=date_in_previous_year) is False  # fmt: skip

    # effective date last day of previous year
    assert partially_exempt_employer.has_partial_exemption(effective_date=last_day_of_previous_year) is False  # fmt: skip
    assert partially_exempt_employer_amendment.has_partial_exemption(effective_date=last_day_of_previous_year) is False  # fmt: skip
    assert family_py_medical_cy_employer.has_partial_exemption(effective_date=last_day_of_previous_year) is True  # fmt: skip
    assert non_exempt_employer.has_partial_exemption(effective_date=last_day_of_previous_year) is False  # fmt: skip

    # effective date first day of current year
    assert partially_exempt_employer.has_partial_exemption(effective_date=first_day_of_current_year) is True  # fmt: skip
    assert partially_exempt_employer_amendment.has_partial_exemption(effective_date=first_day_of_current_year) is True  # fmt: skip
    assert family_py_medical_cy_employer.has_partial_exemption(effective_date=first_day_of_current_year) is True  # fmt: skip
    assert non_exempt_employer.has_partial_exemption(effective_date=first_day_of_current_year) is False  # fmt: skip

    # effective date in current year
    assert partially_exempt_employer.has_partial_exemption(effective_date=date_in_current_year) is True  # fmt: skip
    assert partially_exempt_employer_amendment.has_partial_exemption(effective_date=date_in_current_year) is True  # fmt: skip
    assert family_py_medical_cy_employer.has_partial_exemption(effective_date=date_in_current_year) is True  # fmt: skip
    assert non_exempt_employer.has_partial_exemption(effective_date=date_in_current_year) is False  # fmt: skip

    # effective date last day of current year
    assert partially_exempt_employer.has_partial_exemption(effective_date=last_day_of_current_year) is True  # fmt: skip
    assert partially_exempt_employer_amendment.has_partial_exemption(effective_date=last_day_of_current_year) is True  # fmt: skip
    assert family_py_medical_cy_employer.has_partial_exemption(effective_date=last_day_of_current_year) is True  # fmt: skip
    assert non_exempt_employer.has_partial_exemption(effective_date=last_day_of_current_year) is False  # fmt: skip

    # effective date first day of next year
    assert partially_exempt_employer.has_partial_exemption(effective_date=first_day_of_next_year) is False  # fmt: skip
    assert partially_exempt_employer_amendment.has_partial_exemption(effective_date=first_day_of_next_year) is False  # fmt: skip
    assert family_py_medical_cy_employer.has_partial_exemption(effective_date=first_day_of_next_year) is False  # fmt: skip
    assert non_exempt_employer.has_partial_exemption(effective_date=first_day_of_next_year) is False  # fmt: skip


def test_employer_has_full_exemption(
    initialize_factories_session,
    first_day_of_previous_year,
    first_day_of_current_year,
    first_day_of_next_year,
    last_day_of_previous_year,
    last_day_of_current_year,
    date_in_current_year,
    date_in_previous_year,
):
    fully_exempt_employer = FullyExemptEmployerFactory.create()
    fully_exempt_employer_amendment = FullyExemptEmployerAmendmentFactory.create()
    partially_exempt_employer = PartiallyExemptEmployerFactory.create()
    partially_exempt_employer_amendment = PartiallyExemptEmployerAmendmentFactory.create()
    family_py_medical_cy_employer = FamilyExemptPyMedicalExemptCyEmployerFactory.create()
    non_exempt_employer = NonExemptEmployerFactory.create()

    # effective date first day of previous year
    assert fully_exempt_employer.has_full_exemption(effective_date=first_day_of_previous_year) is False  # fmt: skip
    assert fully_exempt_employer_amendment.has_full_exemption(effective_date=first_day_of_previous_year) is False  # fmt: skip
    assert partially_exempt_employer.has_full_exemption(effective_date=first_day_of_previous_year) is False  # fmt: skip
    assert partially_exempt_employer_amendment.has_full_exemption(effective_date=first_day_of_previous_year) is False  # fmt: skip
    assert family_py_medical_cy_employer.has_full_exemption(effective_date=first_day_of_previous_year) is False  # fmt: skip
    assert non_exempt_employer.has_full_exemption(effective_date=first_day_of_previous_year) is False  # fmt: skip

    # effective date in previous year
    assert fully_exempt_employer.has_full_exemption(effective_date=date_in_previous_year) is False  # fmt: skip
    assert fully_exempt_employer_amendment.has_full_exemption(effective_date=date_in_previous_year) is False  # fmt: skip
    assert partially_exempt_employer.has_full_exemption(effective_date=date_in_previous_year) is False  # fmt: skip
    assert partially_exempt_employer_amendment.has_full_exemption(effective_date=date_in_previous_year) is False  # fmt: skip
    assert family_py_medical_cy_employer.has_full_exemption(effective_date=date_in_previous_year) is False  # fmt: skip
    assert non_exempt_employer.has_full_exemption(effective_date=date_in_previous_year) is False  # fmt: skip

    # effective date last day of previous year
    assert fully_exempt_employer.has_full_exemption(effective_date=last_day_of_previous_year) is False  # fmt: skip
    assert fully_exempt_employer_amendment.has_full_exemption(effective_date=last_day_of_previous_year) is False  # fmt: skip
    assert partially_exempt_employer.has_full_exemption(effective_date=last_day_of_previous_year) is False  # fmt: skip
    assert partially_exempt_employer_amendment.has_full_exemption(effective_date=last_day_of_previous_year) is False  # fmt: skip
    assert family_py_medical_cy_employer.has_full_exemption(effective_date=last_day_of_previous_year) is False  # fmt: skip
    assert non_exempt_employer.has_full_exemption(effective_date=last_day_of_previous_year) is False  # fmt: skip

    # effective date first day of current year
    assert fully_exempt_employer.has_full_exemption(effective_date=first_day_of_current_year) is True  # fmt: skip
    assert fully_exempt_employer_amendment.has_full_exemption(effective_date=first_day_of_current_year) is True  # fmt: skip
    assert partially_exempt_employer.has_full_exemption(effective_date=first_day_of_current_year) is False  # fmt: skip
    assert partially_exempt_employer_amendment.has_full_exemption(effective_date=first_day_of_current_year) is False  # fmt: skip
    assert family_py_medical_cy_employer.has_full_exemption(effective_date=first_day_of_current_year) is False  # fmt: skip
    assert non_exempt_employer.has_full_exemption(effective_date=first_day_of_current_year) is False  # fmt: skip

    # effective date in current year
    assert fully_exempt_employer.has_full_exemption(effective_date=date_in_current_year) is True  # fmt: skip
    assert fully_exempt_employer_amendment.has_full_exemption(effective_date=date_in_current_year) is True  # fmt: skip
    assert partially_exempt_employer.has_full_exemption(effective_date=date_in_current_year) is False  # fmt: skip
    assert partially_exempt_employer_amendment.has_full_exemption(effective_date=date_in_current_year) is False  # fmt: skip
    assert family_py_medical_cy_employer.has_full_exemption(effective_date=date_in_current_year) is False  # fmt: skip
    assert non_exempt_employer.has_full_exemption(effective_date=date_in_current_year) is False  # fmt: skip

    # effective date last day of current year
    assert fully_exempt_employer.has_full_exemption(effective_date=last_day_of_current_year) is True  # fmt: skip
    assert fully_exempt_employer_amendment.has_full_exemption(effective_date=last_day_of_current_year) is True  # fmt: skip
    assert partially_exempt_employer.has_full_exemption(effective_date=last_day_of_current_year) is False  # fmt: skip
    assert partially_exempt_employer_amendment.has_full_exemption(effective_date=last_day_of_current_year) is False  # fmt: skip
    assert family_py_medical_cy_employer.has_full_exemption(effective_date=last_day_of_current_year) is False  # fmt: skip
    assert non_exempt_employer.has_full_exemption(effective_date=last_day_of_current_year) is False  # fmt: skip

    # effective date first day of next year
    assert fully_exempt_employer.has_full_exemption(effective_date=first_day_of_next_year) is False  # fmt: skip
    assert fully_exempt_employer_amendment.has_full_exemption(effective_date=first_day_of_next_year) is False  # fmt: skip
    assert partially_exempt_employer.has_full_exemption(effective_date=first_day_of_next_year) is False  # fmt: skip
    assert partially_exempt_employer_amendment.has_full_exemption(effective_date=first_day_of_next_year) is False  # fmt: skip
    assert family_py_medical_cy_employer.has_full_exemption(effective_date=first_day_of_next_year) is False  # fmt: skip
    assert non_exempt_employer.has_full_exemption(effective_date=first_day_of_next_year) is False  # fmt: skip


def test_one_activity_key_multiple_exemption_records_same_year(initialize_factories_session):

    employer = NonExemptEmployerFactory.create()

    import_logs = {
        "175598": [
            {
                "dor_activity_key": 361883648,
                "decision_commence_date": date(2019, 10, 1),
                "decision_cease_date": date(2020, 12, 31),
                "family_exemption": False,
                "medical_exemption": True,
            },
            {
                "dor_activity_key": **********,
                "decision_commence_date": date(2021, 1, 1),
                "decision_cease_date": date(2021, 12, 31),
                "family_exemption": True,
                "medical_exemption": True,
            },
            {
                "dor_activity_key": **********,
                "decision_commence_date": date(2022, 1, 1),
                "decision_cease_date": date(2022, 12, 31),
                "family_exemption": True,
                "medical_exemption": True,
            },
            {
                "dor_activity_key": 697369600,
                "decision_commence_date": date(2023, 1, 1),
                "decision_cease_date": date(2023, 12, 31),
                "family_exemption": True,
                "medical_exemption": True,
            },
            {
                "dor_activity_key": **********,
                "decision_commence_date": date(2024, 1, 1),
                "decision_cease_date": date(2024, 3, 31),
                "family_exemption": False,
                "medical_exemption": False,
            },
        ],
        "178079": [
            {
                "dor_activity_key": **********,
                "decision_commence_date": date(2024, 1, 1),
                "decision_cease_date": date(2024, 12, 31),
                "family_exemption": True,
                "medical_exemption": True,
            },
        ],
        "250276": [
            {
                "dor_activity_key": **********,
                "decision_commence_date": date(2025, 1, 1),
                "decision_cease_date": date(2025, 12, 31),
                "family_exemption": True,
                "medical_exemption": True,
            },
        ],
    }

    for import_log_id in import_logs:
        import_log = ImportLogFactory.create(import_log_id=import_log_id)

        for dor_exemption in import_logs[import_log_id]:
            EmployerDORExemptionFactory.create(
                employer_id=employer.employer_id,
                import_log=import_log,
                dor_activity_key=dor_exemption["dor_activity_key"],
                decision_commence_date=dor_exemption["decision_commence_date"],
                decision_cease_date=dor_exemption["decision_cease_date"],
                family_exemption=dor_exemption["family_exemption"],
                medical_exemption=dor_exemption["medical_exemption"],
            )

    # sample data results in the following employer_dor_exemption records:
    #
    # # | activity_key | log_id | commence   | cease      | fam ex. | med ex.
    # ------------------------------------------------------------------
    # 1 | 361883648    | 175598 | 2019-10-01 | 2020-12-31 | False   | True
    # 2 | **********   | 175598 | 2021-01-01 | 2021-12-31 | True    | True
    # 3 | **********   | 175598 | 2022-01-01 | 2022-12-31 | True    | True
    # 4 | 697369600    | 175598 | 2023-01-01 | 2023-12-31 | True    | True
    # 5 | **********   | 175598 | 2024-01-01 | 2024-03-31 | False   | False
    # 6 | **********   | 178079 | 2024-01-01 | 2024-12-31 | True    | True  <- overrides row 5

    # for records with same activity_key, most recent log_id should be used
    # (eg. rows 5 and 6 have same activity_key; row 6 overrides row 5, employer
    # is fully exempt between 2024-01-01 and 2024-12-31

    # 2019-01-01 tests
    effective_date = date(2019, 1, 1)
    assert employer.has_family_exemption(effective_date=effective_date) is False  # fmt: skip
    assert employer.has_medical_exemption(effective_date=effective_date) is False  # fmt: skip
    assert employer.has_partial_exemption(effective_date=effective_date) is False  # fmt: skip
    assert employer.has_full_exemption(effective_date=effective_date) is False  # fmt: skip

    exemption_details = employer.get_exemption_details(effective_date=effective_date)
    assert exemption_details.has_family_exemption is False
    assert exemption_details.has_medical_exemption is False
    assert exemption_details.is_partially_exempt is False
    assert exemption_details.is_fully_exempt is False
    assert exemption_details.exemption_commence_date == date(9999, 12, 31)
    assert exemption_details.exemption_cease_date == date(9999, 12, 31)

    # 2019-10-01 tests
    effective_date = date(2019, 10, 1)
    assert employer.has_family_exemption(effective_date=effective_date) is False  # fmt: skip
    assert employer.has_medical_exemption(effective_date=effective_date) is True  # fmt: skip
    assert employer.has_partial_exemption(effective_date=effective_date) is True  # fmt: skip
    assert employer.has_full_exemption(effective_date=effective_date) is False  # fmt: skip

    exemption_details = employer.get_exemption_details(effective_date=effective_date)
    assert exemption_details.has_family_exemption is False
    assert exemption_details.has_medical_exemption is True
    assert exemption_details.is_partially_exempt is True
    assert exemption_details.is_fully_exempt is False
    assert exemption_details.exemption_commence_date == date(2019, 10, 1)
    assert exemption_details.exemption_cease_date == date(2020, 12, 31)

    # 2020-6-30 tests
    effective_date = date(2020, 6, 30)
    assert employer.has_family_exemption(effective_date=effective_date) is False  # fmt: skip
    assert employer.has_medical_exemption(effective_date=effective_date) is True  # fmt: skip
    assert employer.has_partial_exemption(effective_date=effective_date) is True  # fmt: skip
    assert employer.has_full_exemption(effective_date=effective_date) is False  # fmt: skip

    exemption_details = employer.get_exemption_details(effective_date=effective_date)
    assert exemption_details.has_family_exemption is False
    assert exemption_details.has_medical_exemption is True
    assert exemption_details.is_partially_exempt is True
    assert exemption_details.is_fully_exempt is False
    assert exemption_details.exemption_commence_date == date(2019, 10, 1)
    assert exemption_details.exemption_cease_date == date(2020, 12, 31)

    # 2021-6-30 tests
    effective_date = date(2021, 6, 30)
    assert employer.has_family_exemption(effective_date=effective_date) is True  # fmt: skip
    assert employer.has_medical_exemption(effective_date=effective_date) is True  # fmt: skip
    assert employer.has_partial_exemption(effective_date=effective_date) is False  # fmt: skip
    assert employer.has_full_exemption(effective_date=effective_date) is True  # fmt: skip

    exemption_details = employer.get_exemption_details(effective_date=effective_date)
    assert exemption_details.has_family_exemption is True
    assert exemption_details.has_medical_exemption is True
    assert exemption_details.is_partially_exempt is False
    assert exemption_details.is_fully_exempt is True
    assert exemption_details.exemption_commence_date == date(2021, 1, 1)
    assert exemption_details.exemption_cease_date == date(2021, 12, 31)

    # 2022-6-30 tests
    effective_date = date(2022, 6, 30)
    assert employer.has_family_exemption(effective_date=effective_date) is True  # fmt: skip
    assert employer.has_medical_exemption(effective_date=effective_date) is True  # fmt: skip
    assert employer.has_partial_exemption(effective_date=effective_date) is False  # fmt: skip
    assert employer.has_full_exemption(effective_date=effective_date) is True  # fmt: skip

    exemption_details = employer.get_exemption_details(effective_date=effective_date)
    assert exemption_details.has_family_exemption is True
    assert exemption_details.has_medical_exemption is True
    assert exemption_details.is_partially_exempt is False
    assert exemption_details.is_fully_exempt is True
    assert exemption_details.exemption_commence_date == date(2022, 1, 1)
    assert exemption_details.exemption_cease_date == date(2022, 12, 31)

    # 2023-6-30 tests
    effective_date = date(2023, 6, 30)
    assert employer.has_family_exemption(effective_date=effective_date) is True  # fmt: skip
    assert employer.has_medical_exemption(effective_date=effective_date) is True  # fmt: skip
    assert employer.has_partial_exemption(effective_date=effective_date) is False  # fmt: skip
    assert employer.has_full_exemption(effective_date=effective_date) is True  # fmt: skip

    exemption_details = employer.get_exemption_details(effective_date=effective_date)
    assert exemption_details.has_family_exemption is True
    assert exemption_details.has_medical_exemption is True
    assert exemption_details.is_partially_exempt is False
    assert exemption_details.is_fully_exempt is True
    assert exemption_details.exemption_commence_date == date(2023, 1, 1)
    assert exemption_details.exemption_cease_date == date(2023, 12, 31)

    # 2024-6-30 tests
    effective_date = date(2024, 6, 30)
    assert employer.has_family_exemption(effective_date=effective_date) is True  # fmt: skip
    assert employer.has_medical_exemption(effective_date=effective_date) is True  # fmt: skip
    assert employer.has_partial_exemption(effective_date=effective_date) is False  # fmt: skip
    assert employer.has_full_exemption(effective_date=effective_date) is True  # fmt: skip

    exemption_details = employer.get_exemption_details(effective_date=effective_date)
    assert exemption_details.has_family_exemption is True
    assert exemption_details.has_medical_exemption is True
    assert exemption_details.is_partially_exempt is False
    assert exemption_details.is_fully_exempt is True
    assert exemption_details.exemption_commence_date == date(2024, 1, 1)
    assert exemption_details.exemption_cease_date == date(2024, 12, 31)


def test_one_activity_key_multiple_exemption_records_different_years(initialize_factories_session):
    employer = NonExemptEmployerFactory.create()

    import_logs = {
        "243875": [
            {
                "dor_activity_key": **********,
                "decision_commence_date": date(2023, 1, 1),
                "decision_cease_date": date(2023, 12, 31),
                "family_exemption": True,
                "medical_exemption": True,
            }
        ],
        "178079": [
            {
                "dor_activity_key": **********,
                "decision_commence_date": date(2024, 1, 1),
                "decision_cease_date": date(2024, 12, 31),
                "family_exemption": True,
                "medical_exemption": True,
            }
        ],
        "188504": [
            {
                "dor_activity_key": **********,
                "decision_commence_date": date(2024, 1, 1),
                "decision_cease_date": date(2024, 3, 31),
                "family_exemption": True,
                "medical_exemption": True,
            }
        ],
        "205120": [
            {
                "dor_activity_key": **********,
                "decision_commence_date": date(2024, 1, 1),
                "decision_cease_date": date(2024, 12, 31),
                "family_exemption": True,
                "medical_exemption": True,
            },
        ],
    }

    for import_log_id in import_logs:
        import_log = ImportLogFactory.create(import_log_id=import_log_id)

        for dor_exemption in import_logs[import_log_id]:
            EmployerDORExemptionFactory.create(
                employer_id=employer.employer_id,
                import_log=import_log,
                dor_activity_key=dor_exemption["dor_activity_key"],
                decision_commence_date=dor_exemption["decision_commence_date"],
                decision_cease_date=dor_exemption["decision_cease_date"],
                family_exemption=dor_exemption["family_exemption"],
                medical_exemption=dor_exemption["medical_exemption"],
            )

    # sample data results in the following employer_dor_exemption records:
    #
    # # | activity_key | log_id | commence   | cease      | fam ex. | med ex.
    # ------------------------------------------------------------------
    # 1 | **********   | 243875 | 2023-01-01 | 2023-12-31 | True    | True    <- overrides rows 2, 3, 4
    # 2 | **********   | 178079 | 2024-01-01 | 2024-12-31 | True    | True
    # 3 | **********   | 188504 | 2024-01-01 | 2024-03-31 | False   | False
    # 4 | **********   | 205120 | 2024-01-01 | 2024-12-31 | True    | True

    # for records with same activity_key, most recent log_id should be used
    # (eg. rows 1, 2, 3, 4 have same activity_key; row 1 overrides rows 2, 3, 4
    # employer is fully exempt between 2023-01-01 and 2023-12-31
    # employer has no other exemptions

    # 2023-6-30 tests
    effective_date = date(2023, 6, 30)
    assert employer.has_family_exemption(effective_date=effective_date) is True  # fmt: skip
    assert employer.has_medical_exemption(effective_date=effective_date) is True  # fmt: skip
    assert employer.has_partial_exemption(effective_date=effective_date) is False  # fmt: skip
    assert employer.has_full_exemption(effective_date=effective_date) is True  # fmt: skip

    exemption_details = employer.get_exemption_details(effective_date=effective_date)

    # 2024-6-30 tests
    effective_date = date(2024, 6, 30)
    assert employer.has_family_exemption(effective_date=effective_date) is False  # fmt: skip
    assert employer.has_medical_exemption(effective_date=effective_date) is False  # fmt: skip
    assert employer.has_partial_exemption(effective_date=effective_date) is False  # fmt: skip
    assert employer.has_full_exemption(effective_date=effective_date) is False  # fmt: skip

    assert exemption_details.has_family_exemption is True
    assert exemption_details.has_medical_exemption is True
    assert exemption_details.is_partially_exempt is False
    assert exemption_details.is_fully_exempt is True
    assert exemption_details.exemption_commence_date == date(2023, 1, 1)
    assert exemption_details.exemption_cease_date == date(2023, 12, 31)

    exemption_details = employer.get_exemption_details(effective_date=effective_date)
    assert exemption_details.has_family_exemption is False
    assert exemption_details.has_medical_exemption is False
    assert exemption_details.is_partially_exempt is False
    assert exemption_details.is_fully_exempt is False
    assert exemption_details.exemption_commence_date == date(9999, 12, 31)
    assert exemption_details.exemption_cease_date == date(9999, 12, 31)
