from unittest import mock

from massgov.pfml.db.lookup_data.documents import DocumentType
from massgov.pfml.db.models.factories import ApplicationFactory, Generators
from massgov.pfml.fineos.models.customer_api import Document as FineosDocument
from massgov.pfml.services.documents.get_document_service import GetDocumentService


class TestGetCustomerDocuments:
    @mock.patch("massgov.pfml.api.services.fineos_actions.register_employee")
    def test_success(self, mock_register, user, test_db_session):
        mock_register.return_value = "web_id"
        application = ApplicationFactory.create(
            user=user, submitted_time=Generators.TransactionDateTime
        )

        get_document_service = GetDocumentService(db_session=test_db_session)
        fineos_documents = get_document_service.get_user_documents(
            user=user,
            application=application,
        )

        assert len(fineos_documents) == 1
        assert fineos_documents[0].user_id == user.user_id
        assert fineos_documents[0].application_id is None

    @mock.patch("massgov.pfml.fineos.MockFINEOSClient.get_documents")
    @mock.patch("massgov.pfml.fineos.transforms.from_fineos.documents._format_1099_document_name")
    @mock.patch("massgov.pfml.api.services.fineos_actions.register_employee")
    def test_success_multiple_documents(
        self,
        mock_register,
        mock_format_name,
        mock_get_documents,
        user,
        test_db_session,
    ):
        mock_register.return_value = "web_id"
        application = ApplicationFactory.create(
            user=user, submitted_time=Generators.TransactionDateTime
        )
        mock_format_name.return_value = "2021 1099 tax doc"

        document_one = FineosDocument(
            documentId="10092-123",
            name=DocumentType.IRS_1099G_TAX_FORM_FOR_CLAIMANTS.document_type_description,
            type="document",
            userId=user.user_id,
            fileName=DocumentType.IRS_1099G_TAX_FORM_FOR_CLAIMANTS.document_type_description,
        )

        document_two = FineosDocument(
            documentId="10012-123", name="test 1", type="document", userId=user.user_id
        )

        mock_get_documents.return_value = [document_one, document_two]

        get_document_service = GetDocumentService(db_session=test_db_session)
        fineos_documents = get_document_service.get_user_documents(
            user=user,
            application=application,
        )

        assert len(fineos_documents) == 2

        mock_format_name.assert_called_with(document_one, test_db_session)
