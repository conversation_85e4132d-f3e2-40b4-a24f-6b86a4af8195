from datetime import date, datetime, timezone
from unittest import mock

import pytest

from massgov.pfml.db.models.factories import AppealFactory, ApplicationFactory
from massgov.pfml.services.documents.get_document_service import GetDocumentService


# Run `initialize_factories_session` for all tests,
# so that it doesn't need to be manually included
@pytest.fixture(autouse=True)
def setup_factories(initialize_factories_session):
    return


@pytest.fixture
def submitted_appeal(claim):
    appeal = AppealFactory.create(
        claim=claim,
        appeal_phone_number="+12314535511",
        appeal_reason="Missed deadline",
        appeal_representative_option_id=2,
        for_private_insurance=False,
        fineos_appeal_id="NTN-12345-ABS-01-AP-01",
        has_read_notices=True,
        needs_interpreter=False,
        originally_decided_at=date(2019, 12, 1),
        submitted_at=datetime(2019, 12, 15).replace(tzinfo=timezone.utc),
    )
    return appeal


def test_with_application(claim, test_db_session):
    application = claim.application
    get_document_service = GetDocumentService(test_db_session)
    documents = get_document_service.get_documents_from_fineos_for_application(application)

    # asserts based on mock FINEOS response
    assert len(documents) == 1
    document = documents[0]
    assert document.user_id == application.user_id


def test_with_application_and_no_absence_id(claim, test_db_session):
    claim.fineos_absence_id = None
    application = claim.application
    get_document_service = GetDocumentService(test_db_session)

    with pytest.raises(ValueError, match="Missing absence case id"):
        get_document_service.get_documents_from_fineos_for_application(application)


def test_with_application_and_no_claim(test_db_session):
    application = ApplicationFactory.create(claim_id=None)

    get_document_service = GetDocumentService(test_db_session)
    documents = get_document_service.get_documents_from_fineos_for_application(application)

    assert documents == []


def test_with_appeal(claim, submitted_appeal, test_db_session):
    application = claim.application
    get_document_service = GetDocumentService(test_db_session)
    documents = get_document_service.get_documents_from_fineos_for_appeal(submitted_appeal)

    # asserts based on mock FINEOS response
    assert len(documents) == 1
    document = documents[0]
    assert document.user_id == application.user_id


def test_with_appeal_and_no_application(claim, submitted_appeal, test_db_session):
    claim.application = None

    with pytest.raises(
        Exception, match="Application with the given appeal's claim_id could not be found"
    ):
        get_document_service = GetDocumentService(test_db_session)
        get_document_service.get_documents_from_fineos_for_appeal(submitted_appeal)


def test_with_appeal_and_no_absence_id(submitted_appeal, test_db_session):
    submitted_appeal.fineos_appeal_id = None
    with pytest.raises(ValueError, match="Missing appeal id"):
        get_document_service = GetDocumentService(test_db_session)
        get_document_service.get_documents_from_fineos_for_appeal(submitted_appeal)


def test_with_claim(claim, test_db_session):
    get_document_service = GetDocumentService(test_db_session)
    documents = get_document_service.get_documents_from_fineos_for_claim(claim)

    # asserts based on mock FINEOS response
    assert len(documents) == 1
    document = documents[0]
    assert document.user_id is None


def test_with_claim_and_no_absence_id(claim, test_db_session):
    claim.fineos_absence_id = None
    with pytest.raises(ValueError, match="Missing absence case id"):
        get_document_service = GetDocumentService(test_db_session)
        get_document_service.get_documents_from_fineos_for_claim(claim)


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.get_documents")
def test_with_claim_and_no_documents(mock_get_documents, claim, test_db_session):
    mock_get_documents.return_value = []

    get_document_service = GetDocumentService(test_db_session)
    documents = get_document_service.get_documents_from_fineos_for_claim(claim)

    mock_get_documents.assert_called_once()
    assert documents == []
