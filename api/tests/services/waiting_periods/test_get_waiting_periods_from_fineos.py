from datetime import date
from sqlite3 import Date
from unittest import mock

import pytest

from massgov.pfml.db.models.employees import Claim, LeaveRequest
from massgov.pfml.db.models.factories import (
    AbsencePaidLeaveCaseFactory,
    ApplicationFactory,
    ClaimFactory,
    LeaveRequestFactory,
)
from massgov.pfml.services.waiting_periods.get_waiting_periods_from_fineos import (
    get_waiting_periods_from_fineos,
    parse_waiting_period,
)


# Run `initialize_factories_session` for all tests,
# so that it doesn't need to be manually included
@pytest.fixture(autouse=True)
def setup_factories(initialize_factories_session):
    return


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.read_disability_benefit")
def test_parse_waiting_period(disability_benefit_details):
    waiting_period = parse_waiting_period(disability_benefit_details, date(2024, 1, 1))
    assert waiting_period.waiting_period_end_date
    assert waiting_period.waiting_period_start_date
    assert waiting_period._earliest_approved_start_date


@mock.patch("massgov.pfml.api.services.fineos_actions.register_employee_with_claim")
def test_get_waiting_periods_from_fineos(claim, test_db_session):
    ClaimFactory.create()
    claim = test_db_session.query(Claim).one_or_none()
    ApplicationFactory.create(claim=claim)
    LeaveRequestFactory.create(claim_id=claim.claim_id)
    leave_req = test_db_session.query(LeaveRequest).one_or_none()
    AbsencePaidLeaveCaseFactory.create(leave_request_id=leave_req.leave_request_id, claim=claim)
    test_db_session.commit()

    waiting_period = get_waiting_periods_from_fineos(claim, test_db_session)
    assert waiting_period[0].waiting_period_start_date
    assert type(waiting_period[0].waiting_period_start_date) == Date


@mock.patch("massgov.pfml.api.services.fineos_actions.register_employee_with_claim")
def test_get_multiple_waiting_periods_from_fineos(claim, test_db_session):
    ClaimFactory.create()
    claim = test_db_session.query(Claim).one_or_none()
    ApplicationFactory.create(claim=claim)
    LeaveRequestFactory.create(claim_id=claim.claim_id)
    LeaveRequestFactory.create(claim_id=claim.claim_id)
    leave_reqs = test_db_session.query(LeaveRequest).all()
    for leave_req in leave_reqs:
        AbsencePaidLeaveCaseFactory.create(leave_request_id=leave_req.leave_request_id, claim=claim)
    test_db_session.commit()

    waiting_period = get_waiting_periods_from_fineos(claim, test_db_session)
    assert len(waiting_period) == 2
    assert type(waiting_period[0].waiting_period_end_date) == Date
