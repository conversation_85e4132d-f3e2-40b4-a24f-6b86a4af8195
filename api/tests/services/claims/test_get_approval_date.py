import copy
import datetime
from datetime import date
from unittest import mock

import pytest

import massgov.pfml.services.claims as service
from massgov.pfml.api.models.documents.responses import DocumentResponse
from massgov.pfml.db.lookup_data.absences import AbsenceStatus
from massgov.pfml.db.lookup_data.documents import DocumentType


# Run `initialize_factories_session` for all tests,
# so that it doesn't need to be manually included
@pytest.fixture(autouse=True)
def setup_factories(initialize_factories_session):
    return


@pytest.fixture
def claim(claim):
    claim.fineos_absence_status = AbsenceStatus.APPROVED
    return claim


@pytest.fixture
def approval_doc_response(document):
    document.document_type_id = DocumentType.APPROVAL_NOTICE.document_type_id
    return DocumentResponse.from_orm(document)


@pytest.fixture
def earliest_date():
    return date(2022, 1, 1)


@pytest.fixture
def approval_docs(approval_doc_response, earliest_date):
    approval_doc_response_1 = copy.deepcopy(approval_doc_response)
    approval_doc_response_1.fineos_document_id = "1"
    approval_doc_response_1.created_at = earliest_date

    approval_doc_response_2 = copy.deepcopy(approval_doc_response)
    approval_doc_response_2.fineos_document_id = "2"
    approval_doc_response_2.created_at = earliest_date + datetime.timedelta(days=1)

    return [approval_doc_response_1, approval_doc_response_2]


@mock.patch(
    "massgov.pfml.services.documents.get_document_service.GetDocumentService.get_documents_from_fineos_for_claim"
)
def test_with_approval_docs(mock_get_docs, approval_docs, claim, test_db_session, earliest_date):
    mock_get_docs.return_value = approval_docs

    approval_date = service.get_approval_date(claim, test_db_session)

    assert approval_date == earliest_date

    # check that we've persisted the approval date
    assert claim.approval_date == earliest_date


# Ideally we would remove this test and parametrize the above with differently-sorted lists
# But "pytest.mark.parametrize" does not work well with fixtures as inputs
@mock.patch(
    "massgov.pfml.services.documents.get_document_service.GetDocumentService.get_documents_from_fineos_for_claim"
)
def test_with_approval_docs_reverse_order(
    mock_get_docs, approval_docs, claim, test_db_session, earliest_date
):
    approval_docs.reverse()
    mock_get_docs.return_value = approval_docs

    approval_date = service.get_approval_date(claim, test_db_session)

    assert approval_date == earliest_date


@mock.patch(
    "massgov.pfml.services.documents.get_document_service.GetDocumentService.get_documents_from_fineos_for_claim"
)
def test_with_no_approval_docs(mock_get_docs, claim, test_db_session):
    mock_get_docs.return_value = []

    approval_date = service.get_approval_date(claim, test_db_session)

    assert approval_date is None


@mock.patch(
    "massgov.pfml.services.documents.get_document_service.GetDocumentService.get_documents_from_fineos_for_claim"
)
def test_with_approval_date_set(mock_get_docs, claim, test_db_session):

    claim.approval_date = date(2021, 1, 5)

    approval_date = service.get_approval_date(claim, test_db_session)

    mock_get_docs.assert_not_called()

    assert approval_date == date(2021, 1, 5)
