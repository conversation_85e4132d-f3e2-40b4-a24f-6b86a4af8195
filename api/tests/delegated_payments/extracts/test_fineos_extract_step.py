import csv
import logging  # noqa: B1
import os
import uuid
from datetime import datetime
from unittest import mock

import pytest
from freezegun import freeze_time

import massgov.pfml.delegated_payments.extracts.fineos_extract_config as fineos_extract_config
import massgov.pfml.util.files as file_util
from massgov.pfml.db.lookup_data.reference_file_type import ReferenceFileType
from massgov.pfml.db.models.factories import ImportLogFactory, ReferenceFileFactory
from massgov.pfml.db.models.payments import (
    FineosExtractCancelledPayments,
    FineosExtractEmployeeFeed,
    FineosExtractPaymentFullSnapshot,
    FineosExtractReplacedPayments,
    FineosExtractVbiDocumentDeltaSom,
    FineosExtractVbiDocumentSom,
    FineosExtractVbiLeavePlanRequestedAbsence,
    FineosExtractVbiRequestedAbsence,
    FineosExtractVbiRequestedAbsenceSom,
    FineosExtractVbiTaskReportDeltaSom,
    FineosExtractVbiTaskReportSom,
    FineosExtractVPaidLeaveInstruction,
    FineosExtractVpei,
    FineosExtractVpeiClaimDetails,
    FineosExtractVpeiPaymentDetails,
    FineosExtractVpeiPaymentLine,
)
from massgov.pfml.db.models.reference_file.reference_file import ReferenceFile
from massgov.pfml.delegated_payments.extracts.fineos_extract_config import (
    CLAIMANT_EXTRACT_CONFIG,
    CLAIMANT_EXTRACT_FILE_NAMES,
    CLAIMANT_EXTRACT_FILES,
    IAWW_EXTRACT_CONFIG,
    PAYMENT_EXTRACT_CONFIG,
    PAYMENT_EXTRACT_FILE_NAMES,
    PAYMENT_EXTRACT_FILES,
    PAYMENT_RECONCILIATION_EXTRACT_CONFIG,
    VBI_DOCUMENT_DELTA_SOM_EXTRACT_CONFIG,
    VBI_DOCUMENT_SOM_EXTRACT_CONFIG,
    VBI_TASKREPORT_DELTA_SOM_EXTRACT_CONFIG,
    VBI_TASKREPORT_SOM_EXTRACT_CONFIG,
    FineosExtract,
    FineosExtractConstants,
)
from massgov.pfml.delegated_payments.extracts.fineos_extract_step import FineosExtractStep
from massgov.pfml.delegated_payments.mock.fineos_extract_data import (
    FineosIAWWData,
    FineosPaymentData,
    create_fineos_claimant_extract_files,
    create_fineos_payment_extract_files,
    create_vbi_document_som_extract_files,
    create_vbi_taskreport_delta_som_extract_files,
    create_vbi_taskreport_som_extract_files,
    generate_iaww_extract_files,
    generate_payment_reconciliation_extract_files,
    get_vbi_document_som_extract_filtered_records,
    get_vbi_document_som_extract_records,
    get_vbi_taskreport_som_extract_filtered_records,
    get_vbi_taskreport_som_extract_records,
)
from massgov.pfml.util.batch.log import LogEntry

earlier_date_str = "2020-07-01-12-00-00"
date_str = "2020-08-01-12-00-00"
doc_delta_date_str = "2024-07-26-12-00-00"


def create_malformed_file(extract, date_of_extract, folder_path, malformed_content=None):
    if malformed_content is None:
        malformed_content_line_one = "Some,Other,Column,Names"
        malformed_content_line_two = "1,2,3,4"
        malformed_content = "\n".join([malformed_content_line_one, malformed_content_line_two])

    date_prefix = date_of_extract.strftime("%Y-%m-%d-%H-%M-%S-")

    file_name = os.path.join(folder_path, f"{date_prefix}{extract.file_name}")
    with file_util.write_file(file_name) as outfile:
        outfile.write(malformed_content)


def upload_fineos_payment_data(
    mock_payments_s3_config,
    fineos_dataset,
    timestamp=date_str,
    malformed_extract=None,
    malformed_content=None,
):
    folder_path = mock_payments_s3_config.fineos_data_export_path
    date_of_extract = datetime.strptime(timestamp, "%Y-%m-%d-%H-%M-%S")
    create_fineos_payment_extract_files(fineos_dataset, folder_path, date_of_extract)

    if malformed_extract:
        create_malformed_file(
            malformed_extract, date_of_extract, folder_path, malformed_content=malformed_content
        )

    return folder_path


def upload_fineos_claimant_data(
    mock_payments_s3_config,
    fineos_dataset,
    timestamp=date_str,
    malformed_extract=None,
    malformed_content=None,
):
    folder_path = mock_payments_s3_config.fineos_data_export_path
    date_of_extract = datetime.strptime(timestamp, "%Y-%m-%d-%H-%M-%S")
    create_fineos_claimant_extract_files(fineos_dataset, folder_path, date_of_extract)

    if malformed_extract:
        create_malformed_file(
            malformed_extract, date_of_extract, folder_path, malformed_content=malformed_content
        )

    return folder_path


def validate_records(records, table, index_key, test_db_session):
    data = test_db_session.query(table).all()

    # Verify everything was loaded
    assert len(data) == len(records)

    # Index the records (which are dictionaries)
    # so we can compare the DB value
    indexed_records = {}
    for record in records:
        indexed_records[record.get(index_key)] = record

    for db_record in data:
        assert db_record.reference_file_id
        assert db_record.fineos_extract_import_log_id
        assert db_record.created_at
        assert db_record.updated_at

        # Figure out the dataset that corresponds
        # to the one in the DB
        key = getattr(db_record, index_key.lower())
        indexed_record = indexed_records.get(key, None)
        assert indexed_record

        # For each key in the dataset we made,
        # verify that value was accurately stored
        # into the DB
        for k, v in indexed_record.items():
            assert getattr(db_record, k.lower(), None) == v


# needed to initialize the feature config before code being tested checks feature flags
@pytest.fixture(autouse=True)
def use_initialize_feature_config(initialize_feature_config):
    pass


@pytest.fixture(autouse=True)
def setup_and_teardown():
    # Setup code: runs before each test

    # TODO remove with feature flag (https://lwd.atlassian.net/browse/PFMLPB-22515)
    # We dynamically remove "ABSENCEPERIOD_ID" from the config's field_names (in fineos_extract_step)
    # when on a pre-v24 version of FINEOS (since that column isn't present in the extracts for that version).
    # This works fine, but in testing that change to the config's field_names persists beyond a test
    # so this teardown code ensures the column is added back in so that other tests have it
    field_names = fineos_extract_config.FineosExtractConstants.VBI_REQUESTED_ABSENCE_SOM.field_names
    absence_period_id_column = "ABSENCEPERIOD_ID"
    if absence_period_id_column not in field_names:
        field_names.append(absence_period_id_column)

    yield
    # Teardown code: runs after each test


# TODO remove with feature flag (https://lwd.atlassian.net/browse/PFMLPB-22515)
#   (column should always be included once we switch fully to 24.8.1 and beyond)
# FINEOS v24.8.1
def test_modify_extract_fields_based_on_fineos_version_keeps_absenceperiod_id_fin24(
    test_db_session, mock_payments_s3_config, fineos_v24_feature_config
):

    # build step
    fineos_extract_step = FineosExtractStep(
        db_session=test_db_session,
        log_entry_db_session=test_db_session,
        extract_config=CLAIMANT_EXTRACT_CONFIG,
        s3_config=mock_payments_s3_config,
    )

    # run the column modification
    fineos_extract_step.modify_extract_fields_based_on_fineos_version()

    # check that extract config columns DO have ABSENCEPERIOD_ID
    extracts = CLAIMANT_EXTRACT_CONFIG.extracts
    som_extract = extracts[0]
    assert som_extract.file_name == "VBI_REQUESTEDABSENCE_SOM.csv"
    assert "ABSENCEPERIOD_ID" in som_extract.field_names


# TODO remove with feature flag (https://lwd.atlassian.net/browse/PFMLPB-22515)
#   (column should always be included once we switch fully to 24.8.1 and beyond)
# pre-FINEOS v24.8.1
def test_modify_extract_fields_based_on_fineos_version_removes_absenceperiod_id_fin22(
    test_db_session, mock_payments_s3_config
):

    # build step
    fineos_extract_step = FineosExtractStep(
        db_session=test_db_session,
        log_entry_db_session=test_db_session,
        extract_config=CLAIMANT_EXTRACT_CONFIG,
        s3_config=mock_payments_s3_config,
    )

    # run the column modification
    fineos_extract_step.modify_extract_fields_based_on_fineos_version()

    # check that extract config columns DON'T have ABSENCEPERIOD_ID
    extracts = CLAIMANT_EXTRACT_CONFIG.extracts
    som_extract = extracts[0]
    assert som_extract.file_name == "VBI_REQUESTEDABSENCE_SOM.csv"
    assert "ABSENCEPERIOD_ID" not in som_extract.field_names


def test_run_happy_path(
    test_db_session,
    mock_payments_s3_config,
    monkeypatch,
):

    # Show that we can run these steps back-to-back without any issue
    monkeypatch.setenv("FINEOS_PAYMENT_EXTRACT_MAX_HISTORY_DATE", "2019-12-31")
    monkeypatch.setenv("FINEOS_CLAIMANT_EXTRACT_MAX_HISTORY_DATE", "2019-12-31")

    payment_data = [FineosPaymentData(), FineosPaymentData(), FineosPaymentData()]
    claimant_data = [FineosPaymentData(), FineosPaymentData(), FineosPaymentData()]

    upload_fineos_payment_data(mock_payments_s3_config, payment_data)
    upload_fineos_claimant_data(mock_payments_s3_config, claimant_data)

    # Earlier timestamp will be moved to a skipped folder
    skipped_date_str = "2020-01-01-12-00-00"
    upload_fineos_payment_data(mock_payments_s3_config, payment_data, skipped_date_str)
    upload_fineos_claimant_data(mock_payments_s3_config, claimant_data, skipped_date_str)

    # Repeat running the task multiple times to show
    # it doesn't reprocess files it already processed
    # and that it effectively no-ops on subsequent runs
    for _ in range(3):
        fineos_extract_step = FineosExtractStep(
            db_session=test_db_session,
            log_entry_db_session=test_db_session,
            extract_config=CLAIMANT_EXTRACT_CONFIG,
            s3_config=mock_payments_s3_config,
        )
        fineos_extract_step.run()
        fineos_extract_step = FineosExtractStep(
            db_session=test_db_session,
            log_entry_db_session=test_db_session,
            extract_config=PAYMENT_EXTRACT_CONFIG,
            s3_config=mock_payments_s3_config,
        )
        fineos_extract_step.run()

        # Verify all files are present and in the expected processed paths
        expected_path_prefix = (
            mock_payments_s3_config.pfml_fineos_extract_archive_path + "processed/"
        )
        files = file_util.list_files(expected_path_prefix, recursive=True)
        assert len(files) == 9

        claimant_prefix = f"{date_str}-claimant-extract/{date_str}"
        assert (
            f"{claimant_prefix}-{FineosExtractConstants.VBI_REQUESTED_ABSENCE_SOM.file_name}"
            in files
        )
        assert f"{claimant_prefix}-{FineosExtractConstants.EMPLOYEE_FEED.file_name}" in files
        assert f"{claimant_prefix}-{FineosExtractConstants.EMPLOYEE_FEED_DELTA.file_name}" in files
        assert (
            f"{claimant_prefix}-{FineosExtractConstants.VBI_REQUESTED_ABSENCE.file_name}" in files
        )

        payment_prefix = f"{date_str}-payment-extract/{date_str}"
        assert f"{payment_prefix}-{FineosExtractConstants.PAYMENT_DETAILS.file_name}" in files
        assert f"{payment_prefix}-{FineosExtractConstants.CLAIM_DETAILS.file_name}" in files
        assert f"{claimant_prefix}-{FineosExtractConstants.VBI_LEAVESUMMARY.file_name}" in files

        claimant_reference_file = (
            test_db_session.query(ReferenceFile)
            .filter(
                ReferenceFile.file_location == expected_path_prefix + f"{date_str}-claimant-extract"
            )
            .one_or_none()
        )
        assert claimant_reference_file
        assert (
            claimant_reference_file.reference_file_type_id
            == ReferenceFileType.FINEOS_CLAIMANT_EXTRACT.reference_file_type_id
        )

        payment_reference_file = (
            test_db_session.query(ReferenceFile)
            .filter(
                ReferenceFile.file_location == expected_path_prefix + f"{date_str}-payment-extract"
            )
            .one_or_none()
        )
        assert payment_reference_file
        assert (
            payment_reference_file.reference_file_type_id
            == ReferenceFileType.FINEOS_PAYMENT_EXTRACT.reference_file_type_id
        )

        employee_feed_records = [record.get_employee_feed_record() for record in claimant_data]
        validate_records(employee_feed_records, FineosExtractEmployeeFeed, "I", test_db_session)

        requested_absence_som_records = [
            record.get_requested_absence_som_record() for record in claimant_data
        ]
        validate_records(
            requested_absence_som_records,
            FineosExtractVbiRequestedAbsenceSom,
            "ABSENCE_CASENUMBER",
            test_db_session,
        )

        requested_absence_records = [
            record.get_requested_absence_record() for record in claimant_data
        ]
        validate_records(
            requested_absence_records,
            FineosExtractVbiRequestedAbsence,
            "LEAVEREQUEST_ID",
            test_db_session,
        )

        vpei_records = [record.get_vpei_record() for record in payment_data]
        validate_records(vpei_records, FineosExtractVpei, "I", test_db_session)

        claim_details_records = [record.get_claim_details_record() for record in payment_data]
        validate_records(
            claim_details_records,
            FineosExtractVpeiClaimDetails,
            "LEAVEREQUESTI",
            test_db_session,
        )

        payment_details_records = [record.get_payment_details_record() for record in payment_data]
        validate_records(
            payment_details_records,
            FineosExtractVpeiPaymentDetails,
            "PEINDEXID",
            test_db_session,
        )

        payment_line_records = [record.get_payment_line_record() for record in payment_data]
        validate_records(
            payment_line_records,
            FineosExtractVpeiPaymentLine,
            "I",
            test_db_session,
        )

        ### and verify the skipped files as well
        expected_path_prefix = mock_payments_s3_config.pfml_fineos_extract_archive_path + "skipped/"
        files = file_util.list_files(expected_path_prefix, recursive=True)
        assert len(files) == 9

        claimant_prefix = f"{skipped_date_str}-claimant-extract/{skipped_date_str}"
        assert (
            f"{claimant_prefix}-{FineosExtractConstants.VBI_REQUESTED_ABSENCE_SOM.file_name}"
            in files
        )
        assert f"{claimant_prefix}-{FineosExtractConstants.EMPLOYEE_FEED.file_name}" in files
        assert f"{claimant_prefix}-{FineosExtractConstants.EMPLOYEE_FEED_DELTA.file_name}" in files
        assert (
            f"{claimant_prefix}-{FineosExtractConstants.VBI_REQUESTED_ABSENCE.file_name}" in files
        )

        payment_prefix = f"{skipped_date_str}-payment-extract/{skipped_date_str}"
        assert f"{payment_prefix}-{FineosExtractConstants.VPEI.file_name}" in files
        assert f"{payment_prefix}-{FineosExtractConstants.PAYMENT_DETAILS.file_name}" in files
        assert f"{payment_prefix}-{FineosExtractConstants.CLAIM_DETAILS.file_name}" in files
        assert f"{payment_prefix}-{FineosExtractConstants.PAYMENT_LINE.file_name}" in files
        assert f"{claimant_prefix}-{FineosExtractConstants.VBI_LEAVESUMMARY.file_name}" in files

        # And the skipped reference files
        claimant_reference_file = (
            test_db_session.query(ReferenceFile)
            .filter(
                ReferenceFile.file_location
                == expected_path_prefix + f"{skipped_date_str}-claimant-extract"
            )
            .one_or_none()
        )
        assert claimant_reference_file
        assert (
            claimant_reference_file.reference_file_type_id
            == ReferenceFileType.FINEOS_CLAIMANT_EXTRACT.reference_file_type_id
        )

        payment_reference_file = (
            test_db_session.query(ReferenceFile)
            .filter(
                ReferenceFile.file_location
                == expected_path_prefix + f"{skipped_date_str}-payment-extract"
            )
            .one_or_none()
        )
        assert payment_reference_file
        assert (
            payment_reference_file.reference_file_type_id
            == ReferenceFileType.FINEOS_PAYMENT_EXTRACT.reference_file_type_id
        )


def test_payment_reconciliation_extracts(
    test_db_session,
    mock_payments_s3_config,
    monkeypatch,
):
    monkeypatch.setenv("FINEOS_PAYMENT_RECONCILIATION_EXTRACT_MAX_HISTORY_DATE", "2019-12-31")

    # Create payment reconciliation extract files
    folder_path = mock_payments_s3_config.fineos_adhoc_data_export_path
    extract_records = generate_payment_reconciliation_extract_files(folder_path, f"{date_str}-", 10)

    # Run the extract
    fineos_extract_step = FineosExtractStep(
        db_session=test_db_session,
        log_entry_db_session=test_db_session,
        extract_config=PAYMENT_RECONCILIATION_EXTRACT_CONFIG,
        s3_config=mock_payments_s3_config,
    )
    fineos_extract_step.run()

    # Verify files
    expected_path_prefix = mock_payments_s3_config.pfml_fineos_extract_archive_path + "processed/"
    files = file_util.list_files(expected_path_prefix, recursive=True)
    assert len(files) == 3

    payment_reconciliation_prefix = f"{date_str}-payment-reconciliation-extract/{date_str}"
    assert (
        f"{payment_reconciliation_prefix}-{FineosExtractConstants.PAYMENT_FULL_SNAPSHOT.file_name}"
        in files
    )
    assert (
        f"{payment_reconciliation_prefix}-{FineosExtractConstants.REPLACED_PAYMENTS_EXTRACT.file_name}"
        in files
    )
    assert (
        f"{payment_reconciliation_prefix}-{FineosExtractConstants.CANCELLED_PAYMENTS_EXTRACT.file_name}"
        in files
    )

    payment_reference_file = (
        test_db_session.query(ReferenceFile)
        .filter(
            ReferenceFile.file_location
            == expected_path_prefix + f"{date_str}-payment-reconciliation-extract"
        )
        .one_or_none()
    )
    assert payment_reference_file
    assert (
        payment_reference_file.reference_file_type_id
        == ReferenceFileType.FINEOS_PAYMENT_RECONCILIATION_EXTRACT.reference_file_type_id
    )

    validate_records(
        extract_records[FineosExtractConstants.PAYMENT_FULL_SNAPSHOT.file_name],
        FineosExtractPaymentFullSnapshot,
        "I",
        test_db_session,
    )
    validate_records(
        extract_records[FineosExtractConstants.CANCELLED_PAYMENTS_EXTRACT.file_name],
        FineosExtractCancelledPayments,
        "I",
        test_db_session,
    )
    validate_records(
        extract_records[FineosExtractConstants.REPLACED_PAYMENTS_EXTRACT.file_name],
        FineosExtractReplacedPayments,
        "I",
        test_db_session,
    )


def test_iaww_extracts(
    test_db_session,
    mock_payments_s3_config,
    monkeypatch,
):
    monkeypatch.setenv("FINEOS_IAWW_EXTRACT_MAX_HISTORY_DATE", "2019-12-31")

    # Create IAWW extract files
    folder_path = mock_payments_s3_config.fineos_data_export_path
    extract_records = generate_iaww_extract_files(
        [
            FineosIAWWData(aww_value="1331.66"),
            FineosIAWWData(aww_value="1538"),
            FineosIAWWData(aww_value="1700.50"),
        ],
        folder_path,
        f"{date_str}-",
    )

    # Run the extract
    fineos_extract_step = FineosExtractStep(
        db_session=test_db_session,
        log_entry_db_session=test_db_session,
        extract_config=IAWW_EXTRACT_CONFIG,
        s3_config=mock_payments_s3_config,
    )
    fineos_extract_step.run()

    # Verify files
    expected_path_prefix = mock_payments_s3_config.pfml_fineos_extract_archive_path + "processed/"
    files = file_util.list_files(expected_path_prefix, recursive=True)
    assert len(files) == 5

    iaww_prefix = f"{date_str}-iaww-extract/{date_str}"
    assert (
        f"{iaww_prefix}-{FineosExtractConstants.VBI_LEAVE_PLAN_REQUESTED_ABSENCE.file_name}"
        in files
    )
    assert f"{iaww_prefix}-{FineosExtractConstants.PAID_LEAVE_INSTRUCTION.file_name}" in files

    iaww_reference_file = (
        test_db_session.query(ReferenceFile)
        .filter(ReferenceFile.file_location == expected_path_prefix + f"{date_str}-iaww-extract")
        .one_or_none()
    )
    assert iaww_reference_file
    assert (
        iaww_reference_file.reference_file_type_id
        == ReferenceFileType.FINEOS_IAWW_EXTRACT.reference_file_type_id
    )

    validate_records(
        extract_records[FineosExtractConstants.VBI_LEAVE_PLAN_REQUESTED_ABSENCE.file_name],
        FineosExtractVbiLeavePlanRequestedAbsence,
        "SELECTEDPLAN_INDEXID",
        test_db_session,
    )
    validate_records(
        extract_records[FineosExtractConstants.PAID_LEAVE_INSTRUCTION.file_name],
        FineosExtractVPaidLeaveInstruction,
        "I",
        test_db_session,
    )


@freeze_time("2022-06-01", auto_tick_seconds=10)
def test_vbi_taskreport_som_extracts(
    test_db_session,
    mock_payments_s3_config,
    monkeypatch,
):
    monkeypatch.setenv("FINEOS_VBI_TASKREPORT_SOM_EXTRACT_MAX_HISTORY_DATE", "2019-12-31")

    records = get_vbi_taskreport_som_extract_records()

    # Create VBI Task Report Som extract files
    folder_path = mock_payments_s3_config.fineos_data_export_path
    create_vbi_taskreport_som_extract_files(
        records, folder_path, datetime.strptime(date_str, "%Y-%m-%d-%H-%M-%S")
    )

    # Run the extract
    fineos_extract_step = FineosExtractStep(
        db_session=test_db_session,
        log_entry_db_session=test_db_session,
        extract_config=VBI_TASKREPORT_SOM_EXTRACT_CONFIG,
        s3_config=mock_payments_s3_config,
    )
    fineos_extract_step.run()

    # Verify file
    expected_path_prefix = mock_payments_s3_config.pfml_fineos_extract_archive_path + "processed/"
    files = file_util.list_files(expected_path_prefix, recursive=True)
    assert len(files) == 1

    file_prefix = f"{date_str}-vbi-taskreport-som-extract/{date_str}"
    assert f"{file_prefix}-{FineosExtractConstants.VBI_TASKREPORT_SOM.file_name}" in files

    reference_file = (
        test_db_session.query(ReferenceFile)
        .filter(
            ReferenceFile.file_location
            == expected_path_prefix + f"{date_str}-vbi-taskreport-som-extract"
        )
        .one_or_none()
    )
    assert reference_file
    assert (
        reference_file.reference_file_type_id
        == ReferenceFileType.FINEOS_VBI_TASKREPORT_SOM_EXTRACT.reference_file_type_id
    )

    filtered_records = get_vbi_taskreport_som_extract_filtered_records(records)
    validate_records(filtered_records, FineosExtractVbiTaskReportSom, "TASKID", test_db_session)


@freeze_time("2022-06-01", auto_tick_seconds=10)
def test_vbi_taskreport_delta_som_extracts(
    test_db_session,
    mock_payments_s3_config,
    monkeypatch,
):
    monkeypatch.setenv("FINEOS_VBI_TASKREPORT_DELTA_SOM_EXTRACT_MAX_HISTORY_DATE", "2019-12-31")

    # It should be able to ingest data in the same format as the cumulative csv
    records = get_vbi_taskreport_som_extract_records()

    # Create VBI Task Report Som extract files
    folder_path = mock_payments_s3_config.fineos_data_export_path
    create_vbi_taskreport_delta_som_extract_files(
        records, folder_path, datetime.strptime(date_str, "%Y-%m-%d-%H-%M-%S")
    )

    # Run the extract
    fineos_extract_step = FineosExtractStep(
        db_session=test_db_session,
        log_entry_db_session=test_db_session,
        extract_config=VBI_TASKREPORT_DELTA_SOM_EXTRACT_CONFIG,
        s3_config=mock_payments_s3_config,
    )
    fineos_extract_step.run()

    # Verify file
    expected_path_prefix = mock_payments_s3_config.pfml_fineos_extract_archive_path + "processed/"
    files = file_util.list_files(expected_path_prefix, recursive=True)
    assert len(files) == 1

    file_prefix = f"{date_str}-vbi-taskreport-delta-som-extract/{date_str}"
    assert f"{file_prefix}-{FineosExtractConstants.VBI_TASKREPORT_DELTA_SOM.file_name}" in files

    reference_file = (
        test_db_session.query(ReferenceFile)
        .filter(
            ReferenceFile.file_location
            == expected_path_prefix + f"{date_str}-vbi-taskreport-delta-som-extract"
        )
        .one_or_none()
    )
    assert reference_file
    assert (
        reference_file.reference_file_type_id
        == ReferenceFileType.FINEOS_VBI_TASKREPORT_DELTA_SOM_EXTRACT.reference_file_type_id
    )

    filtered_records = get_vbi_taskreport_som_extract_filtered_records(records)
    validate_records(
        filtered_records, FineosExtractVbiTaskReportDeltaSom, "TASKID", test_db_session
    )


@freeze_time("2022-06-01", auto_tick_seconds=10)
def test_vbi_document_som_extracts(
    test_db_session,
    mock_payments_s3_config,
    monkeypatch,
):
    monkeypatch.setenv("FINEOS_VBI_DOCUMENT_SOM_EXTRACT_MAX_HISTORY_DATE", "2019-12-31")
    monkeypatch.setenv("FINEOS_VBI_DOCUMENT_DELTA_SOM_EXTRACT_MAX_HISTORY_DATE", "2024-07-26")

    records = get_vbi_document_som_extract_records()

    # Create VBI Document Som extract files
    folder_path = mock_payments_s3_config.fineos_data_export_path
    create_vbi_document_som_extract_files(
        records,
        folder_path,
        datetime.strptime(doc_delta_date_str, "%Y-%m-%d-%H-%M-%S"),
    )

    # Run the extracts
    FineosExtractStep(
        db_session=test_db_session,
        log_entry_db_session=test_db_session,
        extract_config=VBI_DOCUMENT_SOM_EXTRACT_CONFIG,
        s3_config=mock_payments_s3_config,
    ).run()
    FineosExtractStep(
        db_session=test_db_session,
        log_entry_db_session=test_db_session,
        extract_config=VBI_DOCUMENT_DELTA_SOM_EXTRACT_CONFIG,
        s3_config=mock_payments_s3_config,
    ).run()

    # Verify file
    expected_path_prefix = mock_payments_s3_config.pfml_fineos_extract_archive_path + "processed/"
    files = file_util.list_files(expected_path_prefix, recursive=True)
    # extract and delta extract
    assert len(files) == 2

    file_prefix = f"{doc_delta_date_str}-vbi-document-som-extract/{doc_delta_date_str}"
    assert f"{file_prefix}-{FineosExtractConstants.VBI_DOCUMENT_SOM.file_name}" in files
    file_prefix = f"{doc_delta_date_str}-vbi-document-delta-som-extract/{doc_delta_date_str}"
    assert f"{file_prefix}-{FineosExtractConstants.VBI_DOCUMENT_DELTA_SOM.file_name}" in files

    reference_file = (
        test_db_session.query(ReferenceFile)
        .filter(
            ReferenceFile.file_location
            == expected_path_prefix + f"{doc_delta_date_str}-vbi-document-som-extract"
        )
        .one_or_none()
    )
    assert reference_file
    assert (
        reference_file.reference_file_type_id
        == ReferenceFileType.FINEOS_VBI_DOCUMENT_SOM_EXTRACT.reference_file_type_id
    )

    reference_file = (
        test_db_session.query(ReferenceFile)
        .filter(
            ReferenceFile.file_location
            == expected_path_prefix + f"{doc_delta_date_str}-vbi-document-delta-som-extract"
        )
        .one_or_none()
    )
    assert reference_file
    assert (
        reference_file.reference_file_type_id
        == ReferenceFileType.FINEOS_VBI_DOCUMENT_DELTA_SOM_EXTRACT.reference_file_type_id
    )

    filtered_records = get_vbi_document_som_extract_filtered_records(records)
    validate_records(filtered_records, FineosExtractVbiDocumentSom, "INDEXID", test_db_session)
    validate_records(filtered_records, FineosExtractVbiDocumentDeltaSom, "INDEXID", test_db_session)


def test_run_with_error_during_processing(
    local_test_db_session,
    local_test_db_other_session,
    mock_payments_s3_config,
    monkeypatch,
):
    # *********************************************
    #
    # LOCAL sessions required due to rollback test
    # - Requires two separate sessions and only
    #   the local sessions let you reference values
    #   created in another import log
    # *********************************************
    monkeypatch.setenv("FINEOS_PAYMENT_EXTRACT_MAX_HISTORY_DATE", "2019-12-31")
    monkeypatch.setenv("FINEOS_CLAIMANT_EXTRACT_MAX_HISTORY_DATE", "2019-12-31")

    # Prior skipped data
    prior_payment_data = [FineosPaymentData()]
    upload_fineos_payment_data(
        mock_payments_s3_config, prior_payment_data, timestamp=earlier_date_str
    )

    payment_data = [FineosPaymentData(), FineosPaymentData(), FineosPaymentData()]
    upload_fineos_payment_data(mock_payments_s3_config, payment_data)

    fineos_extract_step = FineosExtractStep(
        db_session=local_test_db_session,
        log_entry_db_session=local_test_db_other_session,
        extract_config=PAYMENT_EXTRACT_CONFIG,
        s3_config=mock_payments_s3_config,
    )

    with mock.patch(
        "massgov.pfml.delegated_payments.extracts.fineos_extract_step.FineosExtractStep._download_and_index_data",
        side_effect=Exception("Raising error to test rollback"),
    ), pytest.raises(Exception, match="Raising error to test rollback"):
        fineos_extract_step.run()

    # Files were moved to error directory
    expected_path_prefix = mock_payments_s3_config.pfml_fineos_extract_archive_path + "/error/"
    files = file_util.list_files(expected_path_prefix, recursive=True)
    assert len(files) == 4
    payment_prefix = f"{date_str}-payment-extract/{date_str}"
    assert f"{payment_prefix}-{FineosExtractConstants.VPEI.file_name}" in files
    assert f"{payment_prefix}-{FineosExtractConstants.PAYMENT_DETAILS.file_name}" in files
    assert f"{payment_prefix}-{FineosExtractConstants.CLAIM_DETAILS.file_name}" in files
    assert f"{payment_prefix}-{FineosExtractConstants.PAYMENT_LINE.file_name}" in files

    # Verify that the skipped file ended up in the right place
    expected_path_prefix = mock_payments_s3_config.pfml_fineos_extract_archive_path + "/skipped/"
    files = file_util.list_files(expected_path_prefix, recursive=True)
    assert len(files) == 4

    payment_prefix = f"{earlier_date_str}-payment-extract/{earlier_date_str}"
    assert f"{payment_prefix}-{FineosExtractConstants.VPEI.file_name}" in files
    assert f"{payment_prefix}-{FineosExtractConstants.PAYMENT_DETAILS.file_name}" in files
    assert f"{payment_prefix}-{FineosExtractConstants.CLAIM_DETAILS.file_name}" in files
    assert f"{payment_prefix}-{FineosExtractConstants.PAYMENT_LINE.file_name}" in files

    # No reference files are created for errored files
    # The skipped files' reference file was rolled back
    # so isn't present in the DB. We'd pick them up again next time
    reference_files = local_test_db_session.query(ReferenceFile).all()
    assert len(reference_files) == 0

    # Verify nothing is in any of the tables
    validate_records([], FineosExtractEmployeeFeed, "I", local_test_db_session)
    validate_records(
        [], FineosExtractVbiRequestedAbsenceSom, "ABSENCE_CASENUMBER", local_test_db_session
    )
    validate_records([], FineosExtractVpei, "I", local_test_db_session)
    validate_records([], FineosExtractVpeiClaimDetails, "LEAVEREQUESTI", local_test_db_session)
    validate_records([], FineosExtractVpeiPaymentDetails, "PEINDEXID", local_test_db_session)
    validate_records([], FineosExtractVpeiPaymentLine, "I", local_test_db_session)


def test_run_with_missing_fineos_file(
    local_test_db_session,
    local_test_db_other_session,
    mock_payments_s3_config,
    monkeypatch,
):
    monkeypatch.setenv("FINEOS_PAYMENT_EXTRACT_MAX_HISTORY_DATE", "2019-12-31")
    monkeypatch.setenv("FINEOS_CLAIMANT_EXTRACT_MAX_HISTORY_DATE", "2019-12-31")

    claimant_data = [FineosPaymentData(), FineosPaymentData(), FineosPaymentData()]
    upload_fineos_claimant_data(mock_payments_s3_config, claimant_data)

    # Delete the employee feed file
    expected_fineos_path_prefix = mock_payments_s3_config.fineos_data_export_path
    file_util.delete_file(
        expected_fineos_path_prefix + f"{date_str}-{FineosExtractConstants.EMPLOYEE_FEED.file_name}"
    )

    fineos_extract_step = FineosExtractStep(
        db_session=local_test_db_session,
        log_entry_db_session=local_test_db_other_session,
        extract_config=CLAIMANT_EXTRACT_CONFIG,
        s3_config=mock_payments_s3_config,
    )

    with pytest.raises(Exception, match="Expected to find files"):
        fineos_extract_step.run()

    # No reference files created because it failed before that was created
    reference_files = local_test_db_session.query(ReferenceFile).all()
    assert len(reference_files) == 0

    # Verify nothing is in any of the tables
    validate_records([], FineosExtractEmployeeFeed, "I", local_test_db_session)
    validate_records(
        [], FineosExtractVbiRequestedAbsenceSom, "ABSENCE_CASENUMBER", local_test_db_session
    )
    validate_records([], FineosExtractVpei, "I", local_test_db_session)
    validate_records([], FineosExtractVpeiClaimDetails, "LEAVEREQUESTI", local_test_db_session)
    validate_records([], FineosExtractVpeiPaymentDetails, "PEINDEXID", local_test_db_session)
    validate_records([], FineosExtractVbiRequestedAbsence, "LEAVEREQUEST_ID", local_test_db_session)
    validate_records([], FineosExtractVpeiPaymentLine, "I", local_test_db_session)


def test_run_with_missing_files_skipped_run(
    test_db_session,
    mock_payments_s3_config,
    monkeypatch,
):
    # Validate that if we're missing files that are going to be skipped
    # that the process won't fail.
    monkeypatch.setenv("FINEOS_CLAIMANT_EXTRACT_MAX_HISTORY_DATE", "2019-12-31")

    prior_claimant_data = [FineosPaymentData(), FineosPaymentData()]
    upload_fineos_claimant_data(
        mock_payments_s3_config, prior_claimant_data, timestamp=earlier_date_str
    )

    claimant_data = [FineosPaymentData(), FineosPaymentData(), FineosPaymentData()]
    upload_fineos_claimant_data(mock_payments_s3_config, claimant_data)

    # Delete the employee feed file for the older skipped record
    expected_fineos_path_prefix = mock_payments_s3_config.fineos_data_export_path
    file_util.delete_file(
        expected_fineos_path_prefix
        + f"{earlier_date_str}-{FineosExtractConstants.EMPLOYEE_FEED.file_name}"
    )

    fineos_extract_step = FineosExtractStep(
        db_session=test_db_session,
        log_entry_db_session=test_db_session,
        extract_config=CLAIMANT_EXTRACT_CONFIG,
        s3_config=mock_payments_s3_config,
    )

    fineos_extract_step.run()

    # Verify that the skipped file ended up in the right place
    expected_path_prefix = mock_payments_s3_config.pfml_fineos_extract_archive_path + "/skipped/"
    files = file_util.list_files(expected_path_prefix, recursive=True)
    assert len(files) == 4

    claimant_prefix = f"{earlier_date_str}-claimant-extract/{earlier_date_str}"
    assert (
        f"{claimant_prefix}-{FineosExtractConstants.VBI_REQUESTED_ABSENCE_SOM.file_name}" in files
    )
    assert f"{claimant_prefix}-{FineosExtractConstants.VBI_REQUESTED_ABSENCE.file_name}" in files

    # Verify the unskipped file was still loaded properly
    employee_feed_records = [record.get_employee_feed_record() for record in claimant_data]
    validate_records(employee_feed_records, FineosExtractEmployeeFeed, "I", test_db_session)

    requested_absence_som_records = [
        record.get_requested_absence_som_record() for record in claimant_data
    ]
    validate_records(
        requested_absence_som_records,
        FineosExtractVbiRequestedAbsenceSom,
        "ABSENCE_CASENUMBER",
        test_db_session,
    )

    requested_absence_records = [record.get_requested_absence_record() for record in claimant_data]
    validate_records(
        requested_absence_records,
        FineosExtractVbiRequestedAbsence,
        "LEAVEREQUEST_ID",
        test_db_session,
    )


@pytest.mark.parametrize(
    "claimant_extract_file",
    CLAIMANT_EXTRACT_FILES,
    ids=CLAIMANT_EXTRACT_FILE_NAMES,
)
def test_run_with_malformed_claimant_data(
    local_test_db_session,
    local_test_db_other_session,
    mock_payments_s3_config,
    monkeypatch,
    claimant_extract_file,
):
    monkeypatch.setenv("FINEOS_PAYMENT_EXTRACT_MAX_HISTORY_DATE", "2019-12-31")
    monkeypatch.setenv("FINEOS_CLAIMANT_EXTRACT_MAX_HISTORY_DATE", "2019-12-31")

    claimant_data = [FineosPaymentData()]
    upload_fineos_claimant_data(
        mock_payments_s3_config, claimant_data, malformed_extract=claimant_extract_file
    )

    with pytest.raises(
        Exception,
        match=f"FINEOS extract {claimant_extract_file.file_name} is missing required fields",
    ):
        fineos_extract_step = FineosExtractStep(
            db_session=local_test_db_session,
            log_entry_db_session=local_test_db_other_session,
            extract_config=CLAIMANT_EXTRACT_CONFIG,
            s3_config=mock_payments_s3_config,
        )
        fineos_extract_step.run()

    # Verify nothing created
    validate_records(
        [],
        claimant_extract_file.table,
        None,
        local_test_db_session,
    )


@pytest.mark.parametrize(
    "payment_extract_file",
    PAYMENT_EXTRACT_FILES,
    ids=PAYMENT_EXTRACT_FILE_NAMES,
)
def test_run_with_malformed_payment_data(
    local_test_db_session,
    local_test_db_other_session,
    mock_payments_s3_config,
    monkeypatch,
    payment_extract_file,
):
    monkeypatch.setenv("FINEOS_PAYMENT_EXTRACT_MAX_HISTORY_DATE", "2019-12-31")
    monkeypatch.setenv("FINEOS_CLAIMANT_EXTRACT_MAX_HISTORY_DATE", "2019-12-31")

    payment_data = [FineosPaymentData()]
    upload_fineos_payment_data(
        mock_payments_s3_config, payment_data, malformed_extract=payment_extract_file
    )

    with pytest.raises(
        Exception,
        match=f"FINEOS extract {payment_extract_file.file_name} is missing required fields",
    ):
        fineos_extract_step = FineosExtractStep(
            db_session=local_test_db_session,
            log_entry_db_session=local_test_db_other_session,
            extract_config=PAYMENT_EXTRACT_CONFIG,
            s3_config=mock_payments_s3_config,
        )
        fineos_extract_step.run()

    # Verify nothing created
    validate_records(
        [],
        payment_extract_file.table,
        None,
        local_test_db_session,
    )


# Test that if there are extra columns in the extract file,
# we log those only once if they're in the header row
def test_log_unconfigured_on_first_record(
    test_db_session,
    mock_payments_s3_config,
    monkeypatch,
    caplog,
):
    monkeypatch.setenv("FINEOS_PAYMENT_EXTRACT_MAX_HISTORY_DATE", "2019-12-31")
    monkeypatch.setenv("FINEOS_CLAIMANT_EXTRACT_MAX_HISTORY_DATE", "2019-12-31")

    # We add one unexpected column in the header, and we plant it, plus yet
    # another unexpected column, in two of the records. The first should only
    # be logged once. The second should be logged twice: once for each time
    # it shows up in a record.
    malformed_extract = FineosExtractConstants.CLAIM_DETAILS
    malformed_content = (
        "PECLASSID,PEINDEXID,ABSENCECASENU,LEAVEREQUESTI,EXTRACOL\n"
        + "1,2,3,4,5\n"
        + "1,2,3,4,5,6\n"
        + "1,2,3,4,5,6"
    )

    payment_data = [FineosPaymentData()]
    upload_fineos_payment_data(
        mock_payments_s3_config,
        payment_data,
        malformed_extract=malformed_extract,
        malformed_content=malformed_content,
    )

    fineos_extract_step = FineosExtractStep(
        db_session=test_db_session,
        log_entry_db_session=test_db_session,
        extract_config=PAYMENT_EXTRACT_CONFIG,
        s3_config=mock_payments_s3_config,
    )

    caplog.set_level(logging.INFO)  # noqa: B1
    fineos_extract_step.run()

    first_record_warnings = 0
    after_first_warnings = 0
    for record in caplog.records:
        if record.msg == "Unconfigured columns in FINEOS extract.":
            first_record_warnings += 1

        if record.msg == "Unconfigured columns in FINEOS extract after first record.":
            after_first_warnings += 1

    assert first_record_warnings == 1
    assert after_first_warnings == 2


# Test that if there are extra columns in the extract file,
# we log those only once if they're in the header row,
# but we don't log them if they're ignored
def test_does_not_log_known_unconfigured_columns(
    test_db_session,
    mock_payments_s3_config,
    monkeypatch,
    caplog,
):
    monkeypatch.setenv("FINEOS_PAYMENT_EXTRACT_MAX_HISTORY_DATE", "2019-12-31")
    monkeypatch.setenv("FINEOS_CLAIMANT_EXTRACT_MAX_HISTORY_DATE", "2019-12-31")

    malformed_extract: FineosExtract = FineosExtractConstants.VPEI
    # SERVICELEVEL is ignored, but EXTRACOL is not
    malformed_content = (
        "C,I,PAYEECUSTOMER,PAYEESOCNUMBE,PAYMENTADD1,PAYMENTADD2,PAYMENTADD4,PAYMENTADD6,PAYMENTPOSTCO,PAYMENTMETHOD,PAYMENTDATE,AMOUNT_MONAMT,PAYEEBANKSORT,PAYEEACCOUNTN,PAYEEACCOUNTT,EVENTTYPE,PAYEEIDENTIFI,PAYEEFULLNAME,EVENTREASON,AMALGAMATIONC,PAYMENTTYPE,EXTRACOL,SERVICELEVEL\n"
        + "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21\n"
        + "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25\n"
        + "1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26\n"
    )

    payment_data = [FineosPaymentData()]
    upload_fineos_payment_data(
        mock_payments_s3_config,
        payment_data,
        malformed_extract=malformed_extract,
        malformed_content=malformed_content,
    )

    fineos_extract_step: FineosExtractStep = FineosExtractStep(
        db_session=test_db_session,
        log_entry_db_session=test_db_session,
        extract_config=PAYMENT_EXTRACT_CONFIG,
        s3_config=mock_payments_s3_config,
    )

    caplog.set_level(logging.INFO)  # noqa: B1
    fineos_extract_step.run()

    first_record_warnings = 0
    after_first_warnings = 0
    for record in caplog.records:
        if record.msg == "Unconfigured columns in FINEOS extract.":
            first_record_warnings += 1
            assert record.fields == "extracol"

        if record.msg == "Unconfigured columns in FINEOS extract after first record.":
            after_first_warnings += 1

    assert first_record_warnings == 1
    assert after_first_warnings == 2


def test_store_file_to_staging_table(
    test_db_session,
    initialize_factories_session,
    tmp_path,
    mock_payments_s3_config,
):
    payment_data = [FineosPaymentData(), FineosPaymentData(), FineosPaymentData()]
    folder_path = upload_fineos_payment_data(mock_payments_s3_config, payment_data)

    fineos_extract_step = FineosExtractStep(
        db_session=test_db_session,
        log_entry_db_session=test_db_session,
        extract_config=PAYMENT_EXTRACT_CONFIG,
        s3_config=mock_payments_s3_config,
    )
    fineos_extract_step._log_entry = LogEntry(test_db_session, "")

    reference_file = ReferenceFileFactory.create()
    extract_config = FineosExtractConstants.VPEI
    file_name = f"{date_str}-{extract_config.file_name}"
    file_location = str(os.path.join(folder_path, file_name))

    fineos_extract_step._store_file_to_staging_table(
        reference_file, tmp_path, file_location, extract_config
    )

    vpei_records = [record.get_vpei_record() for record in payment_data]
    validate_records(vpei_records, FineosExtractVpei, "I", test_db_session)

    # Verify that there are two versions of the file
    files = file_util.list_files(str(tmp_path))
    assert len(files) == 2
    assert file_name in files
    assert f"updated-{extract_config.file_name}" in files

    original_records = list(
        csv.DictReader(file_util.open_stream(os.path.join(tmp_path, file_name)))
    )
    new_records = list(
        csv.DictReader(
            file_util.open_stream(os.path.join(tmp_path, f"updated-{extract_config.file_name}"))
        )
    )

    # No filtering was done, so they're effectively the same records
    assert len(original_records) == len(new_records)

    # Verify the contents match
    indexed_records = {}
    for record in original_records:
        indexed_records[record["I"]] = record

    for new_record in new_records:
        # Note that the new records have lower-case
        # keys, while the old ones are upper-case
        indexed_record = indexed_records.get(new_record["i"])
        assert indexed_record, "did not find matching records"

        for k, v in indexed_record.items():
            assert new_record[k.lower()] == v, f"record was missing {k}"

        # These were additionally added
        assert new_record["reference_file_id"] == str(reference_file.reference_file_id)
        assert new_record["fineos_extract_import_log_id"] == str(
            fineos_extract_step.get_import_log_id()
        )
        assert new_record[extract_config.primary_key] is not None


# These tests serve as a test-bed for
# validating how the LOAD CSV logic works in various scenarios.


def test_load_csv_to_table(test_db_session, tmp_path, mock_payments_s3_config):
    file_path = os.path.join(tmp_path, "test-file.csv")
    fineos_extract_step = FineosExtractStep(
        db_session=test_db_session,
        log_entry_db_session=test_db_session,
        extract_config=PAYMENT_EXTRACT_CONFIG,
        s3_config=mock_payments_s3_config,
    )

    # First let's test what happens if we load just
    # a CSV with the primary key and no other fields
    field_names = ["vpei_id"]
    records = []
    for _ in range(3):
        records.append({"vpei_id": str(uuid.uuid4())})
    fineos_extract_step._write_csv(file_path, field_names, records)
    fineos_extract_step._load_csv_to_table(FineosExtractVpei.__tablename__, file_path, field_names)

    for record in records:
        db_record = (
            test_db_session.query(FineosExtractVpei)
            .filter(FineosExtractVpei.vpei_id == record["vpei_id"])
            .one_or_none()
        )
        assert db_record
        # Columns not in the file default to null
        assert db_record.i is None
        assert db_record.paymentmethod is None

        # However the created_at/updated_at fields
        # auto-generate still without any issue
        assert db_record.created_at is not None
        assert db_record.updated_at is not None


def test_load_csv_to_table_no_primary_key(test_db_session, tmp_path, mock_payments_s3_config):
    file_path = os.path.join(tmp_path, "test-file.csv")
    fineos_extract_step = FineosExtractStep(
        db_session=test_db_session,
        log_entry_db_session=test_db_session,
        extract_config=PAYMENT_EXTRACT_CONFIG,
        s3_config=mock_payments_s3_config,
    )
    # Now let's test the case where the primary key
    # is not present. We expect this to error as that
    # field is required.
    field_names = ["i"]
    records = []
    for i in range(3):
        records.append({"i": str(i)})
    fineos_extract_step._write_csv(file_path, field_names, records)
    with pytest.raises(
        Exception,
        match='null value in column "vpei_id" of relation "fineos_extract_vpei" violates not-null constraint',
    ):
        fineos_extract_step._load_csv_to_table(
            FineosExtractVpei.__tablename__, file_path, field_names
        )

    # Note that we cannot query the DB again as
    # the cursor is a failed transaction
    # Any query will return
    # psycopg2.errors.InFailedSqlTransaction: current transaction is aborted, commands ignored until end of transaction block
    test_db_session.rollback()  # Rollback to make cursor work
    records = test_db_session.query(FineosExtractVpei).all()
    assert len(records) == 0


def test_load_csv_to_table_column_not_in_table(test_db_session, tmp_path, mock_payments_s3_config):
    file_path = os.path.join(tmp_path, "test-file.csv")
    fineos_extract_step = FineosExtractStep(
        db_session=test_db_session,
        log_entry_db_session=test_db_session,
        extract_config=PAYMENT_EXTRACT_CONFIG,
        s3_config=mock_payments_s3_config,
    )
    # Testing scenario where one of the columns does
    # not exist in the table
    field_names = ["vpei_id", "this_column_is_not_real"]
    records = []
    for _ in range(3):
        records.append({"vpei_id": str(uuid.uuid4()), "this_column_is_not_real": "some value"})
    fineos_extract_step._write_csv(file_path, field_names, records)
    with pytest.raises(
        Exception,
        match='column "this_column_is_not_real" of relation "fineos_extract_vpei" does not exist',
    ):
        fineos_extract_step._load_csv_to_table(
            FineosExtractVpei.__tablename__, file_path, field_names
        )

    test_db_session.rollback()  # Rollback to make cursor work
    records = test_db_session.query(FineosExtractVpei).all()
    assert len(records) == 0


def test_load_csv_to_table_uuid_malformed(test_db_session, tmp_path, mock_payments_s3_config):
    file_path = os.path.join(tmp_path, "test-file.csv")
    fineos_extract_step = FineosExtractStep(
        db_session=test_db_session,
        log_entry_db_session=test_db_session,
        extract_config=PAYMENT_EXTRACT_CONFIG,
        s3_config=mock_payments_s3_config,
    )

    # Column is valid, but VPEI ID
    # is a UUID and we're passing in an int
    field_names = ["vpei_id"]
    records = []
    for i in range(3):
        records.append({"vpei_id": str(i)})

    fineos_extract_step._write_csv(file_path, field_names, records)
    with pytest.raises(Exception, match="invalid input syntax for type uuid"):
        fineos_extract_step._load_csv_to_table(
            FineosExtractVpei.__tablename__, file_path, field_names
        )

    test_db_session.rollback()  # Rollback to make cursor work
    records = test_db_session.query(FineosExtractVpei).all()
    assert len(records) == 0


def test_load_csv_to_table_int_id_malformed(test_db_session, tmp_path, mock_payments_s3_config):
    file_path = os.path.join(tmp_path, "test-file.csv")
    fineos_extract_step = FineosExtractStep(
        db_session=test_db_session,
        log_entry_db_session=test_db_session,
        extract_config=PAYMENT_EXTRACT_CONFIG,
        s3_config=mock_payments_s3_config,
    )

    # Show that if the FINEOS extract import log ID is empty
    # it's treated as an empty string, not null and will not process
    # Note: The full process should always have this set as it's
    # tied directly to the step running
    field_names = ["vpei_id", "fineos_extract_import_log_id"]
    records = []
    for _ in range(3):
        records.append({"vpei_id": str(uuid.uuid4()), "fineos_extract_import_log_id": ""})

    fineos_extract_step._write_csv(file_path, field_names, records)
    with pytest.raises(Exception, match="invalid input syntax for type int"):
        fineos_extract_step._load_csv_to_table(
            FineosExtractVpei.__tablename__, file_path, field_names
        )

    test_db_session.rollback()  # Rollback to make cursor work
    records = test_db_session.query(FineosExtractVpei).all()
    assert len(records) == 0


def test_load_csv_to_table_foreign_key_not_present(
    test_db_session, tmp_path, initialize_factories_session, mock_payments_s3_config
):
    file_path = os.path.join(tmp_path, "test-file.csv")
    fineos_extract_step = FineosExtractStep(
        db_session=test_db_session,
        log_entry_db_session=test_db_session,
        extract_config=PAYMENT_EXTRACT_CONFIG,
        s3_config=mock_payments_s3_config,
    )

    # We can load something if the foreign key exists
    import_log = ImportLogFactory.create()

    field_names = ["vpei_id", "fineos_extract_import_log_id"]
    records = []
    for _ in range(3):
        records.append(
            {
                "vpei_id": str(uuid.uuid4()),
                "fineos_extract_import_log_id": str(import_log.import_log_id),
            }
        )

    fineos_extract_step._write_csv(file_path, field_names, records)
    fineos_extract_step._load_csv_to_table(FineosExtractVpei.__tablename__, file_path, field_names)

    for record in records:
        db_record = (
            test_db_session.query(FineosExtractVpei)
            .filter(FineosExtractVpei.vpei_id == record["vpei_id"])
            .one_or_none()
        )
        assert db_record

    # But if the import log ID isn't in the DB
    # then it will fail due to the foreign key constraint
    records = []
    for _ in range(3):
        records.append({"vpei_id": str(uuid.uuid4()), "fineos_extract_import_log_id": "10000"})

    test_db_session.commit()

    fineos_extract_step._write_csv(file_path, field_names, records)
    with pytest.raises(
        Exception,
        match='violates foreign key constraint "fineos_extract_vpei_fineos_extract_import_log_id_fkey"',
    ):
        fineos_extract_step._load_csv_to_table(
            FineosExtractVpei.__tablename__, file_path, field_names
        )

    test_db_session.rollback()  # Rollback to make cursor work
    records = test_db_session.query(FineosExtractVpei).all()
    # The 3 loaded successfully initially aren't rolled back
    # as there was a commit above.
    assert len(records) == 3


def test_load_csv_to_table_duplicate_primary_key(
    test_db_session, tmp_path, mock_payments_s3_config
):
    file_path = os.path.join(tmp_path, "test-file.csv")
    fineos_extract_step = FineosExtractStep(
        db_session=test_db_session,
        log_entry_db_session=test_db_session,
        extract_config=PAYMENT_EXTRACT_CONFIG,
        s3_config=mock_payments_s3_config,
    )

    # Each of the records created has an identical
    # primary key which will get a uniqueness error
    field_names = ["vpei_id"]
    records = []
    primary_key = str(uuid.uuid4())
    for _ in range(3):
        records.append({"vpei_id": primary_key})

    fineos_extract_step._write_csv(file_path, field_names, records)
    with pytest.raises(
        Exception, match='duplicate key value violates unique constraint "fineos_extract_vpei_pkey"'
    ):
        fineos_extract_step._load_csv_to_table(
            FineosExtractVpei.__tablename__, file_path, field_names
        )

    test_db_session.rollback()  # Rollback to make cursor work
    records = test_db_session.query(FineosExtractVpei).all()
    assert len(records) == 0
