from unittest import mock

import pytest

import massgov.pfml.delegated_payments.delegated_payments_util as payments_util
from massgov.pfml.db.lookup_data.payments import MmarsEventStatusType
from massgov.pfml.db.models.employees import ExperianAddressPair
from massgov.pfml.db.models.factories import (
    AddressFactory,
    ClaimantAddressFactory,
    EmployeeFactory,
    ExperianAddressPairFactory,
    MmarsEventFactory,
)
from massgov.pfml.delegated_payments.sync_vcc_step import SyncVCCStep
from massgov.pfml.edm.models.vendor.models import EDMVendorData
from massgov.pfml.util.batch.log import LogEntry
from tests.helpers.logging import assert_log_contains


@pytest.fixture
def sync_vcc_step(initialize_factories_session, test_db_session):
    step = SyncVCCStep(db_session=test_db_session, log_entry_db_session=test_db_session)
    step._log_entry = LogEntry(test_db_session, "")
    return step


@mock.patch.object(SyncVCCStep, "increment")
@mock.patch("massgov.pfml.delegated_payments.sync_vcc_step.create_client")
def test_run_step_no_records(mock_create_client, mock_increment, sync_vcc_step):
    mock_client = mock.MagicMock()
    mock_create_client.return_value = mock_client
    sync_vcc_step.run_step()
    # Ensure no records to process, so nothing is incremented for processed
    mock_increment.assert_not_called()


@mock.patch("massgov.pfml.delegated_payments.sync_vcc_step.create_client")
def test_run_step_with_matching_employee_record_in_mmars(
    mock_create_client, initialize_factories_session, sync_vcc_step, test_db_session
):
    mock_client = mock.MagicMock()
    mock_create_client.return_value = mock_client

    mmars_event = MmarsEventFactory()
    ClaimantAddressFactory(
        employee_id=mmars_event.employee_id,
        residential_address=ExperianAddressPair(
            fineos_address=AddressFactory(address_line_one="original residential address")
        ),
        mailing_address=ExperianAddressPair(
            fineos_address=AddressFactory(address_line_one="original mailing address")
        ),
    )

    mock_client.get_vendor_information.return_value.data = [
        EDMVendorData(
            VENDOR_CUSTOMER_CODE=mmars_event.employee.ctr_vendor_customer_code,
            TIN=mmars_event.employee.tax_identifier.tax_identifier,
            FIRST_NAME=mmars_event.employee.first_name,
            LAST_NAME=mmars_event.employee.last_name,
            LEGAL_NAME=f"{mmars_event.employee.first_name} {mmars_event.employee.last_name}",
            ADDRESS_ID="",
            STREET_1=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.address_line_one,
            STREET_2=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.address_line_two,
            CITY=mmars_event.employee.claimant_addresses[0].mailing_address.fineos_address.city,
            STATE=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.geo_state.geo_state_description,
            ZIP_CODE=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.zip_code,
            CUSTOMER_ACTIVE_STATUS=payments_util.MMARS_Constants.VEND_ACT_STATUS.ACTIVE,
            CUSTOMER_ACTIVE_STATUS_NAME="Active",
            CUSTOMER_APPROVAL_STATUS=payments_util.MMARS_Constants.VEND_APRV_STATUS.COMPLETE,
            CUSTOMER_APPROVAL_STATUS_NAME="Complete",
        )
    ]

    sync_vcc_step.run_step()
    import_log = sync_vcc_step._log_entry.metrics

    assert import_log is not None
    assert import_log["total_vcc_records"] == 1
    assert import_log["total_vcc_already_registered_in_mmars"] == 1
    assert (
        mmars_event.mmars_status_type_id
        == MmarsEventStatusType.RE_PENDING.mmars_event_status_type_id
    )


@mock.patch("massgov.pfml.delegated_payments.sync_vcc_step.create_client")
def test_run_step_with_different_employee_record_in_mmars(
    mock_create_client, initialize_factories_session, sync_vcc_step, test_db_session
):
    mock_client = mock.MagicMock()
    mock_create_client.return_value = mock_client

    mmars_event = MmarsEventFactory()
    ClaimantAddressFactory(
        employee_id=mmars_event.employee_id,
        residential_address=ExperianAddressPair(
            fineos_address=AddressFactory(address_line_one="original residential address")
        ),
        mailing_address=ExperianAddressPair(
            fineos_address=AddressFactory(address_line_one="original mailing address")
        ),
    )

    mock_client.get_vendor_information.return_value.data = [
        EDMVendorData(
            VENDOR_CUSTOMER_CODE=mmars_event.employee.ctr_vendor_customer_code,
            TIN=mmars_event.employee.tax_identifier.tax_identifier,
            FIRST_NAME="edm_first_name",
            LAST_NAME="edm_last_name",
            LEGAL_NAME=f"{mmars_event.employee.first_name} {mmars_event.employee.last_name}",
            ADDRESS_ID="",
            STREET_1=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.address_line_one,
            STREET_2=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.address_line_two,
            CITY=mmars_event.employee.claimant_addresses[0].mailing_address.fineos_address.city,
            STATE=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.geo_state.geo_state_description,
            ZIP_CODE=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.zip_code,
            CUSTOMER_ACTIVE_STATUS=payments_util.MMARS_Constants.VEND_ACT_STATUS.ACTIVE,
            CUSTOMER_ACTIVE_STATUS_NAME="Active",
            CUSTOMER_APPROVAL_STATUS=payments_util.MMARS_Constants.VEND_APRV_STATUS.COMPLETE,
            CUSTOMER_APPROVAL_STATUS_NAME="Complete",
        )
    ]

    sync_vcc_step.run_step()
    import_log = sync_vcc_step._log_entry.metrics

    assert import_log is not None
    assert import_log["total_vcc_records"] == 1
    assert import_log["total_vcm_records_written"] == 1
    assert (
        mmars_event.mmars_status_type_id
        == MmarsEventStatusType.VCM_REQUIRE.mmars_event_status_type_id
    )


@mock.patch("massgov.pfml.delegated_payments.sync_vcc_step.create_client")
def test_run_step_with_employee_record_with_no_address(
    mock_create_client, initialize_factories_session, sync_vcc_step, test_db_session
):
    mock_client = mock.MagicMock()
    mock_create_client.return_value = mock_client

    mmars_event = MmarsEventFactory()
    mmars_event.employee.ctr_vendor_customer_code = None

    mock_client.get_vendor_information.return_value.data = []

    sync_vcc_step.run_step()
    import_log = sync_vcc_step._log_entry.metrics

    assert import_log is not None
    assert import_log["total_vcc_records"] == 1
    assert import_log["total_vcc_issue_count"] == 1
    assert (
        mmars_event.mmars_status_type_id
        == MmarsEventStatusType.VCC_ERROR.mmars_event_status_type_id
    )


@mock.patch("massgov.pfml.delegated_payments.sync_vcc_step.create_client")
def test_run_step_with_employee_record(
    mock_create_client, initialize_factories_session, sync_vcc_step, test_db_session
):
    mock_client = mock.MagicMock()
    mock_create_client.return_value = mock_client

    mmars_event = MmarsEventFactory()
    ClaimantAddressFactory(
        employee_id=mmars_event.employee_id,
        residential_address=ExperianAddressPair(
            fineos_address=AddressFactory(address_line_one="original residential address")
        ),
        mailing_address=ExperianAddressPair(
            fineos_address=AddressFactory(address_line_one="original mailing address")
        ),
    )
    mmars_event.employee.ctr_vendor_customer_code = None
    mock_client.get_vendor_information.return_value.data = []

    sync_vcc_step.run_step()
    import_log = sync_vcc_step._log_entry.metrics

    assert import_log is not None
    assert import_log["total_vcc_records"] == 1
    assert import_log["total_vcc_valid_records"] == 1
    assert (
        mmars_event.mmars_status_type_id
        == MmarsEventStatusType.VCC_PENDING.mmars_event_status_type_id
    )


@mock.patch("massgov.pfml.delegated_payments.sync_vcc_step.create_client")
def test_run_step_with_mismatching_last_name(
    mock_create_client, initialize_factories_session, sync_vcc_step, test_db_session, caplog_info
):
    mock_client = mock.MagicMock()
    mock_create_client.return_value = mock_client

    mmars_event = MmarsEventFactory()
    ClaimantAddressFactory(
        employee_id=mmars_event.employee_id,
        mailing_address=ExperianAddressPair(
            fineos_address=AddressFactory(address_line_one="original residential address")
        ),
        residential_address=None,
    )

    mismatching_last_name = "non matching last name"

    mock_client.get_vendor_information.return_value.data = [
        EDMVendorData(
            VENDOR_CUSTOMER_CODE=mmars_event.employee.ctr_vendor_customer_code,
            TIN=mmars_event.employee.tax_identifier.tax_identifier,
            FIRST_NAME="edm_first_name",
            LAST_NAME=mismatching_last_name,
            LEGAL_NAME=f"{mmars_event.employee.first_name} {mmars_event.employee.last_name}",
            ADDRESS_ID="",
            STREET_1=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.address_line_one,
            STREET_2=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.address_line_two,
            CITY=mmars_event.employee.claimant_addresses[0].mailing_address.fineos_address.city,
            STATE=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.geo_state.geo_state_description,
            ZIP_CODE=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.zip_code,
            CUSTOMER_ACTIVE_STATUS=payments_util.MMARS_Constants.VEND_ACT_STATUS.ACTIVE,
            CUSTOMER_ACTIVE_STATUS_NAME="Active",
            CUSTOMER_APPROVAL_STATUS=payments_util.MMARS_Constants.VEND_APRV_STATUS.COMPLETE,
            CUSTOMER_APPROVAL_STATUS_NAME="Complete",
        )
    ]

    sync_vcc_step.run_step()
    import_log = sync_vcc_step._log_entry.metrics

    assert import_log is not None
    assert import_log["total_vcc_records"] == 1
    assert import_log["total_vcm_records_written"] == 1
    assert (
        mmars_event.mmars_status_type_id
        == MmarsEventStatusType.VCM_REQUIRE.mmars_event_status_type_id
    )

    assert_log_contains(caplog_info, "Last Name Mismatch")


@mock.patch("massgov.pfml.delegated_payments.sync_vcc_step.create_client")
def test_run_step_with_mismatching_address(
    mock_create_client, initialize_factories_session, sync_vcc_step, test_db_session, caplog_info
):
    mock_client = mock.MagicMock()
    mock_create_client.return_value = mock_client

    mmars_event = MmarsEventFactory()
    ClaimantAddressFactory(
        employee_id=mmars_event.employee_id,
        mailing_address=ExperianAddressPair(
            fineos_address=AddressFactory(address_line_one="original residential address")
        ),
        residential_address=None,
    )

    mock_client.get_vendor_information.return_value.data = [
        EDMVendorData(
            VENDOR_CUSTOMER_CODE=mmars_event.employee.ctr_vendor_customer_code,
            TIN=mmars_event.employee.tax_identifier.tax_identifier,
            FIRST_NAME="edm_first_name",
            LAST_NAME=mmars_event.employee.last_name,
            LEGAL_NAME=f"{mmars_event.employee.first_name} {mmars_event.employee.last_name}",
            ADDRESS_ID="",
            STREET_1="STREET_1",
            STREET_2=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.address_line_two,
            CITY=mmars_event.employee.claimant_addresses[0].mailing_address.fineos_address.city,
            STATE=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.geo_state.geo_state_description,
            ZIP_CODE=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.zip_code,
            CUSTOMER_ACTIVE_STATUS=payments_util.MMARS_Constants.VEND_ACT_STATUS.ACTIVE,
            CUSTOMER_ACTIVE_STATUS_NAME="Active",
            CUSTOMER_APPROVAL_STATUS=payments_util.MMARS_Constants.VEND_APRV_STATUS.COMPLETE,
            CUSTOMER_APPROVAL_STATUS_NAME="Complete",
        )
    ]

    sync_vcc_step.run_step()
    import_log = sync_vcc_step._log_entry.metrics

    assert import_log is not None
    assert import_log["total_vcc_records"] == 1
    assert import_log["total_vcm_records_written"] == 1
    assert (
        mmars_event.mmars_status_type_id
        == MmarsEventStatusType.VCM_REQUIRE.mmars_event_status_type_id
    )

    assert_log_contains(caplog_info, "Address Mismatch")


@mock.patch("massgov.pfml.delegated_payments.sync_vcc_step.create_client")
def test_run_step_with_matching_address(
    mock_create_client, initialize_factories_session, sync_vcc_step, test_db_session, caplog_info
):
    mock_client = mock.MagicMock()
    mock_create_client.return_value = mock_client

    mmars_event = MmarsEventFactory()
    ClaimantAddressFactory(
        employee_id=mmars_event.employee_id,
        mailing_address=ExperianAddressPair(
            fineos_address=AddressFactory(address_line_one="address line1 address line2")
        ),
        residential_address=None,
    )

    mock_client.get_vendor_information.return_value.data = [
        EDMVendorData(
            VENDOR_CUSTOMER_CODE=mmars_event.employee.ctr_vendor_customer_code,
            TIN=mmars_event.employee.tax_identifier.tax_identifier,
            FIRST_NAME="edm_first_name",
            LAST_NAME=mmars_event.employee.last_name,
            LEGAL_NAME=f"{mmars_event.employee.first_name} {mmars_event.employee.last_name}",
            ADDRESS_ID="AD010",
            STREET_1="address line1",
            STREET_2="address line2 ",
            CITY=mmars_event.employee.claimant_addresses[0].mailing_address.fineos_address.city,
            STATE=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.geo_state.geo_state_description,
            ZIP_CODE=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.zip_code,
            CUSTOMER_ACTIVE_STATUS=payments_util.MMARS_Constants.VEND_ACT_STATUS.ACTIVE,
            CUSTOMER_ACTIVE_STATUS_NAME="Active",
            CUSTOMER_APPROVAL_STATUS=payments_util.MMARS_Constants.VEND_APRV_STATUS.COMPLETE,
            CUSTOMER_APPROVAL_STATUS_NAME="Complete",
        )
    ]

    sync_vcc_step.run_step()
    import_log = sync_vcc_step._log_entry.metrics

    assert import_log is not None
    assert import_log["total_vcc_records"] == 1
    assert import_log["total_vcc_valid_records"] == 1
    assert (
        mmars_event.mmars_status_type_id
        == MmarsEventStatusType.RE_PENDING.mmars_event_status_type_id
    )


@mock.patch("massgov.pfml.delegated_payments.sync_vcc_step.create_client")
def test_run_step_with_inactive_employee_record_in_mmars(
    mock_create_client, initialize_factories_session, sync_vcc_step, test_db_session
):
    mock_client = mock.MagicMock()
    mock_create_client.return_value = mock_client

    mmars_event = MmarsEventFactory()
    ClaimantAddressFactory(
        employee_id=mmars_event.employee_id,
        residential_address=ExperianAddressPair(
            fineos_address=AddressFactory(address_line_one="original residential address")
        ),
        mailing_address=ExperianAddressPair(
            fineos_address=AddressFactory(address_line_one="original mailing address")
        ),
    )

    mock_client.get_vendor_information.return_value.data = [
        EDMVendorData(
            VENDOR_CUSTOMER_CODE=mmars_event.employee.ctr_vendor_customer_code,
            TIN=mmars_event.employee.tax_identifier.tax_identifier,
            FIRST_NAME=mmars_event.employee.first_name,
            LAST_NAME=mmars_event.employee.last_name,
            LEGAL_NAME=f"{mmars_event.employee.first_name} {mmars_event.employee.last_name}",
            ADDRESS_ID="",
            STREET_1=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.address_line_one,
            STREET_2=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.address_line_two,
            CITY=mmars_event.employee.claimant_addresses[0].mailing_address.fineos_address.city,
            STATE=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.geo_state.geo_state_description,
            ZIP_CODE=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.zip_code,
            CUSTOMER_ACTIVE_STATUS=payments_util.MMARS_Constants.VEND_ACT_STATUS.INACTIVE,
            CUSTOMER_ACTIVE_STATUS_NAME="Inactive",
            CUSTOMER_APPROVAL_STATUS=payments_util.MMARS_Constants.VEND_APRV_STATUS.COMPLETE,
            CUSTOMER_APPROVAL_STATUS_NAME="Complete",
        )
    ]

    sync_vcc_step.run_step()
    import_log = sync_vcc_step._log_entry.metrics

    assert import_log is not None
    assert import_log["total_vcc_records"] == 1
    assert import_log["total_vcm_records_written"] == 1
    assert (
        mmars_event.mmars_status_type_id
        == MmarsEventStatusType.VCM_REQUIRE.mmars_event_status_type_id
    )


@mock.patch("massgov.pfml.delegated_payments.sync_vcc_step.create_client")
def test_run_step_with_incomplete_employee_record_in_mmars(
    mock_create_client, initialize_factories_session, sync_vcc_step, test_db_session
):
    mock_client = mock.MagicMock()
    mock_create_client.return_value = mock_client

    mmars_event = MmarsEventFactory()
    ClaimantAddressFactory(
        employee_id=mmars_event.employee_id,
        residential_address=ExperianAddressPair(
            fineos_address=AddressFactory(address_line_one="original residential address")
        ),
        mailing_address=ExperianAddressPair(
            fineos_address=AddressFactory(address_line_one="original mailing address")
        ),
    )

    mock_client.get_vendor_information.return_value.data = [
        EDMVendorData(
            VENDOR_CUSTOMER_CODE=mmars_event.employee.ctr_vendor_customer_code,
            TIN=mmars_event.employee.tax_identifier.tax_identifier,
            FIRST_NAME=mmars_event.employee.first_name,
            LAST_NAME=mmars_event.employee.last_name,
            LEGAL_NAME=f"{mmars_event.employee.first_name} {mmars_event.employee.last_name}",
            ADDRESS_ID="",
            STREET_1=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.address_line_one,
            STREET_2=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.address_line_two,
            CITY=mmars_event.employee.claimant_addresses[0].mailing_address.fineos_address.city,
            STATE=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.geo_state.geo_state_description,
            ZIP_CODE=mmars_event.employee.claimant_addresses[
                0
            ].mailing_address.fineos_address.zip_code,
            CUSTOMER_ACTIVE_STATUS=payments_util.MMARS_Constants.VEND_ACT_STATUS.ACTIVE,
            CUSTOMER_ACTIVE_STATUS_NAME="Active",
            CUSTOMER_APPROVAL_STATUS=payments_util.MMARS_Constants.VEND_APRV_STATUS.INCOMPLETE,
            CUSTOMER_APPROVAL_STATUS_NAME="Incomplete",
        )
    ]

    sync_vcc_step.run_step()
    import_log = sync_vcc_step._log_entry.metrics

    assert import_log is not None
    assert import_log["total_vcc_records"] == 1
    assert import_log["total_vcm_records_written"] == 1
    assert (
        mmars_event.mmars_status_type_id
        == MmarsEventStatusType.VCM_REQUIRE.mmars_event_status_type_id
    )


@mock.patch("massgov.pfml.delegated_payments.sync_vcc_step.create_client")
@mock.patch("massgov.pfml.delegated_payments.sync_vcc_step.SyncVCCStep.check_for_vcm")
@mock.patch("massgov.pfml.delegated_payments.sync_vcc_step.SyncVCCStep.validate_vcc_record")
def test_run_step_syncs_vcm_require_and_vcc_pending_records(
    mock_check_for_vcm,
    mock_create_client,
    mock_validate_vcc_record,
    initialize_factories_session,
    sync_vcc_step,
    test_db_session,
):

    mock_validate_vcc_record.return_value = True

    # vcm reqire mmars event is expected to be syned with mmars
    mmars_event_vcm_reqire = MmarsEventFactory(
        mmars_status_type_id=MmarsEventStatusType.VCM_REQUIRE.mmars_event_status_type_id
    )

    # vcc pending mmars event is expected to be synced with mmars
    mmars_event_vcc_pending = MmarsEventFactory(
        mmars_status_type_id=MmarsEventStatusType.VCC_PENDING.mmars_event_status_type_id
    )

    # re pending records will not be processed
    mmars_event_re_pending = MmarsEventFactory(
        mmars_status_type_id=MmarsEventStatusType.RE_PENDING.mmars_event_status_type_id
    )

    mock_create_client.return_value = mock.MagicMock()

    sync_vcc_step.run_step()

    # check_for_vcm method should be called for vcm require and vcc pending records
    assert mock_check_for_vcm.call_count == 2

    # get list of arguments passed to check_for_vcm() method
    calls = mock_check_for_vcm.call_args_list

    called_mmars_event_ids = {
        call_args[0].mmars_event_id  # the first arg is the MmarsEvent passed
        for call_args, _ in calls
    }

    assert mmars_event_vcm_reqire.mmars_event_id in called_mmars_event_ids
    assert mmars_event_vcc_pending.mmars_event_id in called_mmars_event_ids
    assert mmars_event_re_pending.mmars_event_id not in called_mmars_event_ids


@mock.patch("massgov.pfml.delegated_payments.sync_vcc_step.create_client")
def test_run_step_transitions_vcm_require_to_re_pending_after_successful_validation(
    mock_create_client, sync_vcc_step, test_db_session
):

    # in this scenario, we test VCC_PENDING mmars event to switch to VCM_REQUIRE then the same mmars event to switch to RE_PENDING after succesfull validation

    # 1) VCC_PENDING mmars event goes to VCM_REQUIRE
    mmars_event = MmarsEventFactory(
        mmars_status_type_id=MmarsEventStatusType.VCC_PENDING.mmars_event_status_type_id
    )

    claimant_address = ClaimantAddressFactory(
        mailing_address=ExperianAddressPairFactory(), employee=mmars_event.employee
    )

    mock_edm_client = mock.MagicMock()
    mock_create_client.return_value = mock_edm_client

    mock_edm_data_returned_from_mmars = EDMVendorData(
        VENDOR_CUSTOMER_CODE=mmars_event.employee.ctr_vendor_customer_code,
        TIN=mmars_event.employee.tax_identifier.tax_identifier,
        FIRST_NAME=mmars_event.employee.first_name,
        LAST_NAME=mmars_event.employee.last_name,
        LEGAL_NAME=f"{mmars_event.employee.first_name} {mmars_event.employee.last_name}",
        ADDRESS_ID="",
        STREET_1=claimant_address.mailing_address.fineos_address.address_line_one,
        STREET_2=claimant_address.mailing_address.fineos_address.address_line_two,
        CITY=claimant_address.mailing_address.fineos_address.city,
        STATE=claimant_address.mailing_address.fineos_address.geo_state.geo_state_description,
        ZIP_CODE=claimant_address.mailing_address.fineos_address.zip_code,
        CUSTOMER_ACTIVE_STATUS=payments_util.MMARS_Constants.VEND_ACT_STATUS.ACTIVE,
        CUSTOMER_ACTIVE_STATUS_NAME="Active",
        CUSTOMER_APPROVAL_STATUS=payments_util.MMARS_Constants.VEND_APRV_STATUS.COMPLETE,
        CUSTOMER_APPROVAL_STATUS_NAME="Complete",
    )

    mock_edm_data_returned_from_mmars.LAST_NAME = "non matching last name"

    mock_edm_client.get_vendor_information.return_value.data = [mock_edm_data_returned_from_mmars]

    sync_vcc_step.run_step()

    assert (
        mmars_event.mmars_status_type_id
        == MmarsEventStatusType.VCM_REQUIRE.mmars_event_status_type_id
    )

    # 2) VCM_REQUIRE mmars event goes to RE_PENDING when the validation is successful

    # this time the address and name in pfml will match with the data returned from mmars
    mock_edm_data_returned_from_mmars.LAST_NAME = mmars_event.employee.last_name

    sync_vcc_step.run_step()

    assert (
        mmars_event.mmars_status_type_id
        == MmarsEventStatusType.RE_PENDING.mmars_event_status_type_id
    )


@mock.patch("massgov.pfml.delegated_payments.sync_vcc_step.create_client")
def test_run_step_updates_ctr_vendor_customer_code(
    mock_create_client,
    sync_vcc_step,
    initialize_factories_session,
    test_db_session,
):
    # in this scenario, we test sync_vcc_step updates the ctr_vendor_customer_code field in the employee table

    # VCC information from pfml matches with the data from mmars and ctr_vendor_customer_code is None in employee table

    employee = EmployeeFactory(
        ctr_vendor_customer_code=None,
    )

    mmars_event = MmarsEventFactory(
        mmars_status_type_id=MmarsEventStatusType.VCC_PENDING.mmars_event_status_type_id,
        employee=employee,
    )

    claimant_address = ClaimantAddressFactory(
        employee=mmars_event.employee,
        mailing_address=ExperianAddressPairFactory(),
    )

    mock_edm_client = mock.MagicMock()
    mock_create_client.return_value = mock_edm_client

    mock_edm_data_returned_from_mmars = EDMVendorData(
        VENDOR_CUSTOMER_CODE="new_vendor_customer_code",
        TIN=mmars_event.employee.tax_identifier.tax_identifier,
        FIRST_NAME=mmars_event.employee.first_name,
        LAST_NAME=mmars_event.employee.last_name,
        LEGAL_NAME=f"{mmars_event.employee.first_name} {mmars_event.employee.last_name}",
        ADDRESS_ID="",
        STREET_1=claimant_address.mailing_address.fineos_address.address_line_one,
        CITY=claimant_address.mailing_address.fineos_address.city,
        STATE=claimant_address.mailing_address.fineos_address.geo_state.geo_state_description,
        ZIP_CODE=claimant_address.mailing_address.fineos_address.zip_code,
        CUSTOMER_ACTIVE_STATUS=payments_util.MMARS_Constants.VEND_ACT_STATUS.ACTIVE,
        CUSTOMER_ACTIVE_STATUS_NAME="Active",
        CUSTOMER_APPROVAL_STATUS=payments_util.MMARS_Constants.VEND_APRV_STATUS.COMPLETE,
        CUSTOMER_APPROVAL_STATUS_NAME="Complete",
    )

    mock_edm_client.get_vendor_information.return_value.data = [mock_edm_data_returned_from_mmars]

    sync_vcc_step.run_step()

    assert (
        mmars_event.mmars_status_type_id
        == MmarsEventStatusType.RE_PENDING.mmars_event_status_type_id
    )
    assert mmars_event.employee.ctr_vendor_customer_code == "new_vendor_customer_code"


@mock.patch("massgov.pfml.delegated_payments.sync_vcc_step.create_client")
def test_run_step_with_missing_employee_mailing_address(
    mock_create_client, sync_vcc_step, caplog_error
):
    # in this scenario, we test sync_vcc_step behaves correctly when the employee does not have a mailing address

    mmars_event = MmarsEventFactory(
        mmars_status_type_id=MmarsEventStatusType.VCC_PENDING.mmars_event_status_type_id
    )

    claimant_address = ClaimantAddressFactory(
        employee=mmars_event.employee,
        mailing_address=None,
    )

    sync_vcc_step.run_step()

    assert claimant_address.mailing_address is None
    assert (
        mmars_event.mmars_status_type_id
        == MmarsEventStatusType.VCC_ERROR.mmars_event_status_type_id
    )

    assert_log_contains(
        caplog_error,
        "Encountered validation issue while processing vcc record: [<ValidationReason.MISSING_EMPLOYEE_ADDRESS: 'MissingEmployeeVCCAddress'>]",
    )
