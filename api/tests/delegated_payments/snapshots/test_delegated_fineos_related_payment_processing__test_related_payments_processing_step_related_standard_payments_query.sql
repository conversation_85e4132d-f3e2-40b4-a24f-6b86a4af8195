SELECT
    payment.payment_id,
    payment.claim_id,
    payment.employee_id,
    payment.payment_transaction_type_id,
    payment.payment_relevant_party_id,
    payment.period_start_date,
    payment.period_end_date,
    payment.payment_date,
    payment.absence_case_creation_date,
    payment.amount,
    payment.fineos_pei_c_value,
    payment.fineos_pei_i_value,
    payment.is_adhoc_payment,
    payment.fineos_extraction_date,
    payment.disb_method_id,
    payment.experian_address_pair_id,
    payment.fineos_extract_import_log_id,
    payment.pub_eft_id,
    payment.pub_individual_id,
    payment.claim_type_id,
    payment.vpei_id,
    payment.has_active_writeback_issue,
    payment.fineos_employee_first_name,
    payment.fineos_employee_middle_name,
    payment.fineos_employee_last_name,
    payment.payee_name,
    payment.created_at,
    payment.updated_at
FROM payment
INNER JOIN payment_details ON payment.payment_id = payment_details.payment_id
INNER JOIN
    payment_line
    ON payment_details.payment_details_id = payment_line.payment_details_id
WHERE
    payment.claim_id = '00000000-0000-0000-0000-000000000000'
    AND payment_details.period_start_date = '2021-01-13 00:00:00'
    AND payment_details.period_end_date = '2021-01-26 00:00:00'
    AND payment_line.amount = -100
    AND payment.payment_transaction_type_id = 1
