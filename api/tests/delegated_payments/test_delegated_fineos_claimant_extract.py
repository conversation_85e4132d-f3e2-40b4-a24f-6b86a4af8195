import copy
import datetime
from typing import Optional

import pytest

import massgov.pfml.delegated_payments.extracts.fineos_extract_util as extract_util
import massgov.pfml.util.datetime as datetime_util
from massgov.pfml.db.lookup_data.absences import (
    AbsencePeriodType,
    AbsenceReason,
    AbsenceReasonQualifierOne,
    AbsenceReasonQualifierTwo,
    AbsenceStatus,
)
from massgov.pfml.db.lookup_data.employees import (
    ABSENCE_REASON_ID_CLAIM_TYPE_ID_MAPPING,
    BankAccountType,
    ClaimType,
    EligibilityDecision,
    LeaveRequestDecision,
    PrenoteState,
)
from massgov.pfml.db.lookup_data.reference_file_type import ReferenceFileType
from massgov.pfml.db.models.absences import AbsencePeriod
from massgov.pfml.db.models.employees import AbsencePaidLeaveCase, <PERSON>laim, Employee, LeaveRequest
from massgov.pfml.db.models.factories import (
    ClaimFactory,
    EmployeeFactory,
    EmployerFactory,
    LeaveRequestFactory,
    ReferenceFileFactory,
    TaxIdentifierFactory,
)
from massgov.pfml.db.models.payments import (
    FineosExtractEmployeeFeed,
    FineosExtractVbiLeavePlanRequestedAbsence,
    FineosExtractVbiRequestedAbsence,
    FineosExtractVbiRequestedAbsenceSom,
)
from massgov.pfml.db.models.reference_file.reference_file import ReferenceFile
from massgov.pfml.delegated_payments.claimant_data import ClaimantData
from massgov.pfml.delegated_payments.claimant_extract_metrics import ClaimantExtractMetrics
from massgov.pfml.delegated_payments.mock.fineos_extract_data import FineosPaymentData
from tests.delegated_payments.conftest import (
    add_db_records_from_fineos_data,
    stage_claimant_extract_data,
)


@pytest.fixture
def now_eastern():
    return datetime_util.get_now_us_eastern()


def test_run_step_happy_path(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
):
    organization_unit_name = "Appeals Court"
    claimant_data = FineosPaymentData(organization_unit_name=organization_unit_name)
    employee, _ = add_db_records_from_fineos_data(
        test_db_session,
        claimant_data,
        add_eft=False,
        fineos_employee_first_name="Original-FINEOS-First",
        fineos_employee_last_name="Original-FINEOS-Last",
    )

    stage_claimant_extract_data([claimant_data], test_db_session, mock_payments_s3_config)

    run_decomposed_claimant_extract_steps()

    claim = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == claimant_data.absence_case_number)
        .first()
    )

    LeaveRequestFactory.create(
        claim_id=claim.claim_id,
        is_id_proofed=True,
    )

    assert claim is not None
    assert claim.employee_id == employee.employee_id
    assert claim.organization_unit.name == organization_unit_name

    assert claim.fineos_notification_id == claimant_data.notification_number
    assert claim.fineos_absence_status_id == AbsenceStatus.APPROVED.absence_status_id
    assert claim.claim_start_date == datetime_util.datetime_str_to_date(
        claimant_data.leave_request_start
    )
    assert claim.claim_end_date == datetime_util.datetime_str_to_date(
        claimant_data.leave_request_end
    )
    assert claim.get_is_id_proofed is True
    assert claim.employer.fineos_employer_id == int(claimant_data.employer_customer_num)

    updated_employee = (
        test_db_session.query(Employee)
        .filter(Employee.fineos_customer_number == claimant_data.customer_number)
        .one_or_none()
    )

    assert updated_employee is not None
    # We are not updating first or last name with FINEOS data as DOR is source of truth.
    assert updated_employee.first_name == employee.first_name
    assert updated_employee.last_name == employee.last_name
    # Make sure we have captured the claimant name in fineos specific employee columns (initial fineos names from above overwritten)
    assert updated_employee.fineos_employee_first_name == claimant_data.fineos_employee_first_name
    assert updated_employee.fineos_employee_last_name == claimant_data.fineos_employee_last_name
    assert updated_employee.date_of_birth == datetime.date(1980, 1, 1)
    assert updated_employee.fineos_customer_number is not None

    pub_efts = updated_employee.pub_efts.all()
    assert len(pub_efts) == 1
    assert pub_efts[0].pub_eft.routing_nbr == claimant_data.routing_nbr
    assert pub_efts[0].pub_eft.account_nbr == claimant_data.account_nbr
    assert pub_efts[0].pub_eft.bank_account_type_id == BankAccountType.CHECKING.bank_account_type_id
    assert pub_efts[0].pub_eft.prenote_state_id == PrenoteState.PENDING_PRE_PUB.prenote_state_id

    # Make sure we have captured the claimant name in pub_eft
    assert (
        pub_efts[0].pub_eft.fineos_employee_first_name == claimant_data.fineos_employee_first_name
    )
    assert pub_efts[0].pub_eft.fineos_employee_last_name == claimant_data.fineos_employee_last_name

    # Confirm StateLogs
    assert len(claim.state_logs) == 0


def test_run_step_happy_with_mass_id(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
):
    organization_unit_name = "Appeals Court"
    mass_id = "S********"
    claimant_data = FineosPaymentData(
        organization_unit_name=organization_unit_name, mass_id_number=mass_id
    )
    employee, _ = add_db_records_from_fineos_data(
        test_db_session,
        claimant_data,
        add_eft=False,
        fineos_employee_first_name="Original-FINEOS-First",
        fineos_employee_last_name="Original-FINEOS-Last",
    )

    stage_claimant_extract_data([claimant_data], test_db_session, mock_payments_s3_config)

    run_decomposed_claimant_extract_steps()

    claim = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == claimant_data.absence_case_number)
        .first()
    )

    assert claim is not None
    assert claim.employee_id == employee.employee_id

    updated_employee = (
        test_db_session.query(Employee)
        .filter(Employee.fineos_customer_number == claimant_data.customer_number)
        .one_or_none()
    )

    assert updated_employee is not None
    assert updated_employee.mass_id_number == mass_id


def test_run_step_without_som_record_happy_path(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
):
    standard_requested_absence_data = FineosPaymentData()
    # Requested absences without SOM records only get imported when
    # Absence status is "Closed" and
    # Intake source is "Self-Service"
    requested_absence_without_som_record = FineosPaymentData(
        absence_case_status_id=AbsenceStatus.CLOSED.absence_status_id,
        absence_case_status=AbsenceStatus.CLOSED.absence_status_description,
        absence_intake_source="Self-Service",
        include_requested_absence_som=False,
    )
    employee1, _ = add_db_records_from_fineos_data(
        test_db_session,
        standard_requested_absence_data,
        add_eft=False,
    )
    employee2, _ = add_db_records_from_fineos_data(
        test_db_session,
        requested_absence_without_som_record,
        add_eft=False,
    )

    stage_claimant_extract_data(
        [standard_requested_absence_data],
        test_db_session,
        mock_payments_s3_config,
        additional_employee_feed_records=[requested_absence_without_som_record],
        additional_requested_absence_records=[requested_absence_without_som_record],
    )

    run_decomposed_claimant_extract_steps()

    standard_claim = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == standard_requested_absence_data.absence_case_number)
        .first()
    )

    other_claim = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == requested_absence_without_som_record.absence_case_number)
        .first()
    )

    assert standard_claim is not None
    assert other_claim is not None

    standard_claim_som_record = (
        test_db_session.query(FineosExtractVbiRequestedAbsenceSom)
        .filter(
            FineosExtractVbiRequestedAbsenceSom.absence_casenumber
            == standard_requested_absence_data.absence_case_number
        )
        .first()
    )

    standard_claim_non_som_record = (
        test_db_session.query(FineosExtractVbiRequestedAbsence)
        .filter(
            FineosExtractVbiRequestedAbsence.absence_casenumber
            == standard_requested_absence_data.absence_case_number
        )
        .first()
    )

    assert standard_claim_som_record is not None
    assert standard_claim_non_som_record is not None

    other_claim_som_record = (
        test_db_session.query(FineosExtractVbiRequestedAbsenceSom)
        .filter(
            FineosExtractVbiRequestedAbsenceSom.absence_casenumber
            == requested_absence_without_som_record.absence_case_number
        )
        .first()
    )

    other_claim_non_som_record = (
        test_db_session.query(FineosExtractVbiRequestedAbsence)
        .filter(
            FineosExtractVbiRequestedAbsence.absence_casenumber
            == requested_absence_without_som_record.absence_case_number
        )
        .first()
    )

    assert other_claim_som_record is None
    assert other_claim_non_som_record is not None

    assert (
        other_claim.fineos_notification_id
        == requested_absence_without_som_record.notification_number
    )
    assert other_claim.fineos_absence_status_id == AbsenceStatus.CLOSED.absence_status_id
    assert other_claim.claim_start_date == datetime_util.datetime_str_to_date(
        requested_absence_without_som_record.leave_request_start
    )
    assert other_claim.claim_end_date == datetime_util.datetime_str_to_date(
        requested_absence_without_som_record.leave_request_end
    )
    assert other_claim.get_is_id_proofed is False
    assert other_claim.employer.fineos_employer_id == int(
        requested_absence_without_som_record.employer_customer_num
    )

    employee = (
        test_db_session.query(Employee)
        .filter(
            Employee.fineos_customer_number == requested_absence_without_som_record.customer_number
        )
        .one_or_none()
    )

    assert (
        employee.fineos_employee_first_name
        == requested_absence_without_som_record.fineos_employee_first_name
    )
    assert (
        employee.fineos_employee_last_name
        == requested_absence_without_som_record.fineos_employee_last_name
    )
    assert employee.date_of_birth == datetime.date(1980, 1, 1)
    assert employee.fineos_customer_number is not None

    pub_efts = employee.pub_efts.all()
    assert len(pub_efts) == 1
    assert pub_efts[0].pub_eft.routing_nbr == requested_absence_without_som_record.routing_nbr
    assert pub_efts[0].pub_eft.account_nbr == requested_absence_without_som_record.account_nbr
    assert pub_efts[0].pub_eft.bank_account_type_id == BankAccountType.CHECKING.bank_account_type_id
    assert pub_efts[0].pub_eft.prenote_state_id == PrenoteState.PENDING_PRE_PUB.prenote_state_id

    # Make sure we have captured the claimant name in pub_eft
    assert (
        pub_efts[0].pub_eft.fineos_employee_first_name
        == requested_absence_without_som_record.fineos_employee_first_name
    )
    assert (
        pub_efts[0].pub_eft.fineos_employee_last_name
        == requested_absence_without_som_record.fineos_employee_last_name
    )

    # Confirm StateLogs
    assert len(other_claim.state_logs) == 0


def test_run_step_multiple_times(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
):
    # Test what happens if we run multiple times on the same data
    # After the first run, the step should no-op as the reference file
    # has already been processed.

    claimant_data = FineosPaymentData()
    add_db_records_from_fineos_data(test_db_session, claimant_data)

    stage_claimant_extract_data([claimant_data], test_db_session, mock_payments_s3_config)

    # First run
    run_decomposed_claimant_extract_steps()

    # Make sure the processed ID is set.
    reference_files = (
        test_db_session.query(ReferenceFile)
        .filter(
            ReferenceFile.reference_file_type_id
            == ReferenceFileType.FINEOS_CLAIMANT_EXTRACT.reference_file_type_id
        )
        .order_by(ReferenceFile.created_at.desc())
        .all()
    )
    assert reference_files
    # assert reference_files[0].processed_import_log_id == claimant_extract_step.get_import_log_id()

    claim_after_first_run = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == claimant_data.absence_case_number)
        .one_or_none()
    )

    # Run again a few times
    run_decomposed_claimant_extract_steps()
    run_decomposed_claimant_extract_steps()
    run_decomposed_claimant_extract_steps()

    # Verify the claim hasn't been updated again
    claim_after_many_runs = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == claimant_data.absence_case_number)
        .one_or_none()
    )
    assert claim_after_first_run.updated_at == claim_after_many_runs.updated_at


def test_run_step_existing_approved_eft_info(
    run_decomposed_claimant_extract_steps, test_db_session, mock_payments_s3_config, sync_eft_step
):
    # Very similar to the happy path test, but EFT info has already been
    # previously approved and we do not need to start the prenoting process

    claimant_data = FineosPaymentData()
    add_db_records_from_fineos_data(
        test_db_session, claimant_data, prenote_state=PrenoteState.APPROVED
    )
    stage_claimant_extract_data([claimant_data], test_db_session, mock_payments_s3_config)

    run_decomposed_claimant_extract_steps()
    updated_employee = (
        test_db_session.query(Employee)
        .filter(Employee.fineos_customer_number == claimant_data.customer_number)
        .one_or_none()
    )

    pub_efts = updated_employee.pub_efts.all()
    assert len(pub_efts) == 1
    assert pub_efts[0].pub_eft.routing_nbr == claimant_data.routing_nbr
    assert pub_efts[0].pub_eft.account_nbr == claimant_data.account_nbr
    assert pub_efts[0].pub_eft.bank_account_type_id == BankAccountType.CHECKING.bank_account_type_id
    assert pub_efts[0].pub_eft.prenote_state_id == PrenoteState.APPROVED.prenote_state_id

    metrics = sync_eft_step.get_log_entry().metrics
    # We now only process new EFTs, if there is an existing EFT, we would not update it
    assert metrics[sync_eft_step.Metrics.TOTAL_FINEOS_EFT_RECORDS_FAILED_VALIDATION] == 0
    assert metrics[sync_eft_step.Metrics.EFTS_PROCESSED] == 0


def test_run_step_existing_rejected_eft_info(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
    sync_eft_step,
):
    # Very similar to the happy path test, but EFT info has already been
    # previously rejected and thus it goes into an error state instead

    claimant_data = FineosPaymentData()
    add_db_records_from_fineos_data(
        test_db_session, claimant_data, prenote_state=PrenoteState.REJECTED
    )
    stage_claimant_extract_data([claimant_data], test_db_session, mock_payments_s3_config)

    run_decomposed_claimant_extract_steps()

    updated_employee = (
        test_db_session.query(Employee)
        .filter(Employee.fineos_customer_number == claimant_data.customer_number)
        .one_or_none()
    )

    pub_efts = updated_employee.pub_efts.all()
    assert len(pub_efts) == 1
    assert pub_efts[0].pub_eft.routing_nbr == claimant_data.routing_nbr
    assert pub_efts[0].pub_eft.account_nbr == claimant_data.account_nbr
    assert pub_efts[0].pub_eft.bank_account_type_id == BankAccountType.CHECKING.bank_account_type_id
    assert pub_efts[0].pub_eft.prenote_state_id == PrenoteState.REJECTED.prenote_state_id

    metrics = sync_eft_step.get_log_entry().metrics
    # We now only process new EFTs, if there is an existing EFT, we would not update it
    assert metrics[sync_eft_step.Metrics.TOTAL_FINEOS_EFT_RECORDS_FAILED_VALIDATION] == 0
    assert metrics[sync_eft_step.Metrics.EFTS_PROCESSED] == 0


def test_run_step_no_employee(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
    sync_claims_step,
):
    claimant_data = FineosPaymentData()
    stage_claimant_extract_data([claimant_data], test_db_session, mock_payments_s3_config)

    run_decomposed_claimant_extract_steps()

    claim: Optional[Claim] = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == claimant_data.absence_case_number)
        .one_or_none()
    )

    # Claim still gets created even if employee doesn't exist
    assert claim
    assert claim.employee_id is None

    metrics = sync_claims_step.get_log_entry().metrics
    assert metrics[ClaimantExtractMetrics.EMPLOYEE_NOT_FOUND_IN_DATABASE_COUNT] == 1


def test_run_duplicate_organization_unit(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
    sync_claims_step,
):
    organization_unit_name = "Appeals Court"
    claimant_data_one = FineosPaymentData(organization_unit_name=organization_unit_name)
    claimant_data_one = FineosPaymentData(organization_unit_name=organization_unit_name)
    claimant_data_two = copy.deepcopy(claimant_data_one)
    claimant_data_two.organization_unit_name = "Different org unit"
    employee, _ = add_db_records_from_fineos_data(
        test_db_session,
        claimant_data_one,
        add_eft=False,
        fineos_employee_first_name="Original-FINEOS-First",
        fineos_employee_last_name="Original-FINEOS-Last",
    )

    stage_claimant_extract_data(
        [claimant_data_one, claimant_data_two], test_db_session, mock_payments_s3_config
    )

    run_decomposed_claimant_extract_steps()

    claim = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == claimant_data_one.absence_case_number)
        .first()
    )

    assert claim is not None
    assert claim.employee_id == employee.employee_id
    # In the case of duplicates, it would use the first one it found in db
    assert claim.organization_unit.name == organization_unit_name

    metrics = sync_claims_step.get_log_entry().metrics
    assert metrics[ClaimantExtractMetrics.ORG_UNIT_FOUND_COUNT] == 1


def test_run_missing_record_in_employee_feed(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
    sync_claims_step,
):
    organization_unit_name = "Appeals Court"
    claimant_data = FineosPaymentData(organization_unit_name=organization_unit_name)
    employee, _ = add_db_records_from_fineos_data(
        test_db_session,
        claimant_data,
        add_eft=False,
        fineos_employee_first_name="Original-FINEOS-First",
        fineos_employee_last_name="Original-FINEOS-Last",
    )

    stage_claimant_extract_data([claimant_data], test_db_session, mock_payments_s3_config)
    # delete the staged Employee feed records
    test_db_session.delete(test_db_session.query(FineosExtractEmployeeFeed).all()[0])

    run_decomposed_claimant_extract_steps()

    claim = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == claimant_data.absence_case_number)
        .first()
    )

    assert claim is not None

    metrics = sync_claims_step.get_log_entry().metrics
    assert metrics[ClaimantExtractMetrics.EMPLOYEE_NOT_FOUND_IN_DATABASE_COUNT] == 1

    updated_employee = (
        test_db_session.query(Employee)
        .filter(Employee.fineos_customer_number == claimant_data.customer_number)
        .one_or_none()
    )

    assert updated_employee is None


def format_claimant_data() -> FineosPaymentData:
    return FineosPaymentData(
        absence_case_number="NTN-001-ABS-01",
        notification_number="NTN-001",
        absence_case_status="Adjudication",
        leave_request_start="2021-02-14",
        leave_request_end="2021-02-28",
        claim_type="Family",
        leave_request_evidence="Satisfied",
        customer_number="12345",
        ssn="********9",
        date_of_birth="1967-04-27",
        payment_method="Elec Funds Transfer",
        routing_nbr="*********",
        account_nbr="********9",
        account_type="Checking",
    )


@pytest.fixture
def formatted_claim(initialize_factories_session) -> Claim:
    employer = EmployerFactory()
    claim = ClaimFactory(
        fineos_notification_id="NTN-001",
        fineos_absence_id="NTN-001-ABS-01",
        employer_id=employer.employer_id,
        claim_type_id=ClaimType.FAMILY_LEAVE.claim_type_id,
        fineos_absence_status_id=AbsenceStatus.COMPLETED.absence_status_id,
        claim_start_date=datetime.date(2021, 2, 10),
        claim_end_date=datetime.date(2021, 2, 16),
    )

    return claim


def make_claimant_data_from_fineos_data(
    db_session, fineos_data, additional_requested_absence_data=None, metrics_obj=None
):
    reference_file = ReferenceFileFactory.build()

    requested_absence_som = extract_util.create_staging_table_instance(
        fineos_data.get_requested_absence_som_record(),
        FineosExtractVbiRequestedAbsenceSom,
        reference_file,
        None,
    )
    requested_absence_non_som = extract_util.create_staging_table_instance(
        fineos_data.get_requested_absence_record(),
        FineosExtractVbiRequestedAbsence,
        reference_file,
        None,
    )
    employee_feed = extract_util.create_staging_table_instance(
        fineos_data.get_employee_feed_record(),
        FineosExtractEmployeeFeed,
        reference_file,
        None,
    )

    # Add to db to get the UUIDs populated
    db_session.add(reference_file)
    if requested_absence_som:
        db_session.add(requested_absence_som)
        requested_absence_som = db_session.query(FineosExtractVbiRequestedAbsenceSom).all()[-1]

    if requested_absence_non_som:
        db_session.add(requested_absence_non_som)
        requested_absence_non_som = db_session.query(FineosExtractVbiRequestedAbsence).all()[-1]

    requested_absence_union = fineos_data.get_requested_absence_union()
    requested_absence_union.vbi_requested_absence_som_serial_id = (
        requested_absence_som.vbi_requested_absence_som_serial_id
    )
    requested_absence_union.vbi_requested_absence_serial_id = (
        requested_absence_non_som.vbi_requested_absence_serial_id
    )

    requested_absences = [requested_absence_union]

    if additional_requested_absence_data:
        additional_requested_absence_som = extract_util.create_staging_table_instance(
            additional_requested_absence_data.get_requested_absence_som_record(),
            FineosExtractVbiRequestedAbsenceSom,
            reference_file,
            None,
        )
        additional_requested_absence_non_som = extract_util.create_staging_table_instance(
            additional_requested_absence_data.get_requested_absence_record(),
            FineosExtractVbiRequestedAbsence,
            reference_file,
            None,
        )

        # Add to db to get the UUIDs populated
        if additional_requested_absence_som:
            db_session.add(additional_requested_absence_som)
            additional_requested_absence_som = db_session.query(
                FineosExtractVbiRequestedAbsenceSom
            ).all()[-1]

        if additional_requested_absence_non_som:
            db_session.add(additional_requested_absence_non_som)
            additional_requested_absence_non_som = db_session.query(
                FineosExtractVbiRequestedAbsence
            ).all()[-1]

        additional_requested_absence_union = (
            additional_requested_absence_data.get_requested_absence_union()
        )
        additional_requested_absence_union.vbi_requested_absence_som_serial_id = (
            additional_requested_absence_som.vbi_requested_absence_som_serial_id
        )
        additional_requested_absence_union.vbi_requested_absence_serial_id = (
            additional_requested_absence_non_som.vbi_requested_absence_serial_id
        )
        requested_absences.append(additional_requested_absence_union)

    # For tests that don't run the full step, can still see metrics
    count_incrementer = None
    if metrics_obj is not None:

        def count_incrementer(name: str, increment: int = 1) -> None:
            if name not in metrics_obj:
                metrics_obj[name] = 0
            metrics_obj[name] += increment

    return ClaimantData(
        fineos_data.absence_case_number,
        requested_absences,
        employee_feed,
        count_incrementer,
    )


def make_claimant_data_with_incorrect_request_absence(db_session, fineos_data):
    # This method guarantees the request absence fields ABSENCEPERIOD_CLASSID, ABSENCEPERIOD_INDEXID are set to Unknown
    reference_file = ReferenceFileFactory.build()

    raw_requested_absence = fineos_data.get_requested_absence_som_record()
    raw_requested_absence["ABSENCEPERIOD_CLASSID"] = "Unknown"
    raw_requested_absence["ABSENCEPERIOD_INDEXID"] = "Unknown"
    requested_absence_som = extract_util.create_staging_table_instance(
        raw_requested_absence, FineosExtractVbiRequestedAbsenceSom, reference_file, None
    )
    requested_absence_non_som = extract_util.create_staging_table_instance(
        fineos_data.get_requested_absence_record(),
        FineosExtractVbiRequestedAbsence,
        reference_file,
        None,
    )
    employee_feed = extract_util.create_staging_table_instance(
        fineos_data.get_employee_feed_record(),
        FineosExtractEmployeeFeed,
        reference_file,
        None,
    )

    # Add to db to get the UUIDs populated
    db_session.add(reference_file)
    db_session.add(requested_absence_som)
    db_session.add(requested_absence_non_som)
    requested_absence_som = db_session.query(FineosExtractVbiRequestedAbsenceSom).one()
    requested_absence_non_som = db_session.query(FineosExtractVbiRequestedAbsence).one()

    raw_requested_absence_union = fineos_data.get_requested_absence_union()
    raw_requested_absence_union.absenceperiod_classid = "Unknown"
    raw_requested_absence_union.absenceperiod_indexid = "Unknown"
    raw_requested_absence_union.vbi_requested_absence_som_serial_id = (
        requested_absence_som.vbi_requested_absence_som_serial_id
    )
    raw_requested_absence_union.vbi_requested_absence_serial_id = (
        requested_absence_non_som.vbi_requested_absence_serial_id
    )

    return ClaimantData(
        fineos_data.absence_case_number,
        [raw_requested_absence_union],
        employee_feed,
    )


def test_create_or_update_claim_happy_path_update_claim(
    test_db_session,
    mock_payments_s3_config,
    run_decomposed_claimant_extract_steps,
    formatted_claim,
    caplog,
):
    claimant_data = format_claimant_data()
    _, claim = add_db_records_from_fineos_data(
        test_db_session,
        claimant_data,
        add_eft=False,
        fineos_employee_first_name="Original-FINEOS-First",
        fineos_employee_last_name="Original-FINEOS-Last",
    )

    stage_claimant_extract_data([claimant_data], test_db_session, mock_payments_s3_config)
    run_decomposed_claimant_extract_steps()

    claim = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == claimant_data.absence_case_number)
        .first()
    )

    LeaveRequestFactory.create(
        claim_id=claim.claim_id,
        is_id_proofed=True,
    )

    assert claim is not None
    # State log is only stored when validation errors are encountered
    assert claim.state_logs == []
    # Existing claim, check claim_id
    assert claim.claim_id == formatted_claim.claim_id
    assert claim.fineos_notification_id == "NTN-001"
    assert claim.fineos_absence_id == "NTN-001-ABS-01"
    assert claim.claim_type_id == ClaimType.FAMILY_LEAVE.claim_type_id
    assert claim.fineos_absence_status_id == AbsenceStatus.ADJUDICATION.absence_status_id
    assert claim.claim_start_date == datetime.date(2021, 2, 14)
    assert claim.claim_end_date == datetime.date(2021, 2, 28)
    assert claim.get_is_id_proofed is True


def test_absence_period_deduplication(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
):
    # If the same requested absence appears multiple times, we dedupe that to a single one
    reference_file = ReferenceFileFactory.build()

    claimant_data = FineosPaymentData(absence_period_i_value="1234")
    requested_absences = []

    extract_util.create_staging_table_instance(
        claimant_data.get_requested_absence_som_record(),
        FineosExtractVbiRequestedAbsenceSom,
        reference_file,
        None,
    )
    extract_util.create_staging_table_instance(
        claimant_data.get_requested_absence_record(),
        FineosExtractVbiRequestedAbsence,
        reference_file,
        None,
    )
    requested_absences.append(claimant_data.get_requested_absence_union())

    # Add an exact duplicate of the first absence record
    extract_util.create_staging_table_instance(
        claimant_data.get_requested_absence_som_record(),
        FineosExtractVbiRequestedAbsenceSom,
        reference_file,
        None,
    )
    requested_absences.append(claimant_data.get_requested_absence_union())

    # Create a record with a different I value so it doesn't get deduped
    different_payment_data = FineosPaymentData(absence_period_i_value="5678")
    extract_util.create_staging_table_instance(
        different_payment_data.get_requested_absence_som_record(),
        FineosExtractVbiRequestedAbsenceSom,
        reference_file,
        None,
    )
    extract_util.create_staging_table_instance(
        different_payment_data.get_requested_absence_record(),
        FineosExtractVbiRequestedAbsence,
        reference_file,
        None,
    )
    requested_absences.append(different_payment_data.get_requested_absence_union())

    employee_feed = extract_util.create_staging_table_instance(
        claimant_data.get_employee_feed_record(),
        FineosExtractEmployeeFeed,
        reference_file,
        None,
    )

    claimant_data = ClaimantData(
        claimant_data.absence_case_number, requested_absences, employee_feed
    )

    # Despite passing in 3 requested absences, one gets deduped away (the 2nd)
    assert len(claimant_data.absence_period_data) == 2
    assert set(
        [absence_period_data.index_id for absence_period_data in claimant_data.absence_period_data]
    ) == set([1234, 5678])


def test_create_or_update_claim_invalid_values(
    test_db_session,
    mock_payments_s3_config,
    run_decomposed_claimant_extract_steps,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
    sync_claims_step,
):
    # Create claimant data with just an absence case number
    claimant_data = FineosPaymentData(generate_defaults=False, absence_case_number="NTN-001-ABS-01")

    stage_claimant_extract_data([claimant_data], test_db_session, mock_payments_s3_config)

    run_decomposed_claimant_extract_steps()

    claim = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == claimant_data.absence_case_number)
        .first()
    )

    # The claim will be created, but with just an absence case number
    assert claim is not None
    assert claim.fineos_notification_id is None
    assert claim.fineos_absence_id == "NTN-001-ABS-01"
    assert claim.fineos_absence_status_id is None
    assert claim.claim_start_date is None
    assert claim.claim_end_date is None
    assert not claim.get_is_id_proofed


def test_create_or_update_absence_period_happy_path(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
):
    # Create claimant data, and make sure there aren't any initial validation issues
    claimant_data = FineosPaymentData(
        absence_case_number="ABS_001",
        leave_request_start="2021-02-14",
        leave_request_end="2021-02-28",
        leave_request_id=5,
        leave_request_evidence="Satisfied",
        absence_period_c_value=1448,
        absence_period_i_value=1,
        absence_period_type="Episodic",
        absence_reason_qualifier_one="Blood",
        absence_reason_qualifier_two="Accident / Injury",
        absence_reason="Bereavement",
        leave_request_decision="Denied",
    )

    stage_claimant_extract_data(
        [claimant_data],
        test_db_session,
        mock_payments_s3_config,
        date_of_extract=datetime.date(2023, 1, 30),
    )

    run_decomposed_claimant_extract_steps()
    claim = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == claimant_data.absence_case_number)
        .one_or_none()
    )
    assert claim is not None
    absence_period = claim.absence_periods[0]

    assert claim is not None
    assert absence_period is not None

    assert absence_period.claim_id == claim.claim_id
    assert absence_period.fineos_absence_period_class_id == 1448
    assert absence_period.fineos_absence_period_index_id == 1
    assert absence_period.absence_period_start_date == datetime.date(2021, 2, 14)
    assert absence_period.absence_period_end_date == datetime.date(2021, 2, 28)
    assert absence_period.fineos_leave_request_id == 5
    assert (
        absence_period.absence_period_type_id == AbsencePeriodType.EPISODIC.absence_period_type_id
    )
    assert (
        absence_period.absence_reason_qualifier_one_id
        == AbsenceReasonQualifierOne.BLOOD.absence_reason_qualifier_one_id
    )
    assert (
        absence_period.absence_reason_qualifier_two_id
        == AbsenceReasonQualifierTwo.ACCIDENT_INJURY.absence_reason_qualifier_two_id
    )
    assert absence_period.absence_reason_id == AbsenceReason.BEREAVEMENT.absence_reason_id
    assert (
        absence_period.leave_request_decision_id
        == LeaveRequestDecision.DENIED.leave_request_decision_id
    )

    assert claim.get_is_id_proofed is True

    # Create new claimant data to update existing absence_period. We make sure the claim and claimant_data's
    # absence_period_c_value and absence_period_i_value remain unchanged.
    new_formatted_claimant_data = FineosPaymentData(
        absence_case_number="ABS_001",
        leave_request_start="2021-03-07",
        leave_request_end="2021-12-11",
        leave_request_id=5,
        leave_request_evidence="UnSatisfied",
        absence_period_c_value=1448,
        absence_period_i_value=1,
        absence_period_type="Office Visit",
        absence_reason_qualifier_one="Adoption",
        absence_reason_qualifier_two="Sickness",
        absence_reason="Medical Donation - Employee",
        leave_request_decision="Projected",
    )

    stage_claimant_extract_data(
        [new_formatted_claimant_data],
        test_db_session,
        mock_payments_s3_config,
        date_of_extract=datetime.date(2023, 1, 31),
    )

    run_decomposed_claimant_extract_steps()

    absence_period = (
        test_db_session.query(AbsencePeriod)
        .filter(
            AbsencePeriod.fineos_absence_period_class_id == 1448,
            AbsencePeriod.fineos_absence_period_index_id == 1,
        )
        .one_or_none()
    )

    assert absence_period is not None

    assert absence_period.claim_id == claim.claim_id
    assert absence_period.fineos_absence_period_class_id == 1448
    assert absence_period.fineos_absence_period_index_id == 1
    assert absence_period.absence_period_start_date == datetime.date(2021, 3, 7)
    assert absence_period.absence_period_end_date == datetime.date(2021, 12, 11)
    assert absence_period.fineos_leave_request_id == 5

    assert (
        absence_period.absence_period_type_id
        == AbsencePeriodType.OFFICE_VISIT.absence_period_type_id
    )
    assert (
        absence_period.absence_reason_qualifier_one_id
        == AbsenceReasonQualifierOne.ADOPTION.absence_reason_qualifier_one_id
    )
    assert (
        absence_period.absence_reason_qualifier_two_id
        == AbsenceReasonQualifierTwo.SICKNESS.absence_reason_qualifier_two_id
    )
    assert (
        absence_period.absence_reason_id
        == AbsenceReason.MEDICAL_DONATION_EMPLOYEE.absence_reason_id
    )
    assert (
        absence_period.leave_request_decision_id
        == LeaveRequestDecision.PROJECTED.leave_request_decision_id
    )
    assert absence_period.leave_request_id is not None
    # Value is set from leave request
    test_db_session.refresh(claim)
    assert claim.get_is_id_proofed is True


def test_create_or_update_absence_period_invalid_values(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
):
    # Create claimant data with just an absence case number
    claimant_data = FineosPaymentData(
        generate_defaults=False,
        absence_case_number="NTN-001-ABS-01",
        absence_period_c_value=1010,
        absence_period_i_value=201,
    )
    stage_claimant_extract_data([claimant_data], test_db_session, mock_payments_s3_config)

    run_decomposed_claimant_extract_steps()

    claim = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == claimant_data.absence_case_number)
        .one_or_none()
    )

    assert claim is not None

    absence_period = claim.absence_periods[0]

    assert claim is not None
    assert absence_period is not None
    # absence periods is created but only with IDs
    assert absence_period.claim_id == claim.claim_id
    assert absence_period.fineos_absence_period_class_id == 1010
    assert absence_period.fineos_absence_period_index_id == 201
    assert absence_period.absence_period_start_date is None
    assert absence_period.absence_period_end_date is None
    assert absence_period.fineos_leave_request_id is None
    assert absence_period.absence_period_type_id is None
    assert absence_period.absence_reason_qualifier_one_id is None
    assert absence_period.absence_reason_qualifier_two_id is None
    assert absence_period.absence_reason_id is None
    assert absence_period.leave_request_decision_id is None


def test_update_absence_period_with_mismatching_claim_id_from_same_reference_file_id(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
    sync_absence_periods_step,
):
    # We test if an absence period with matching absence_period.(class_id, index_id) has a mis-match on
    # absence_period.claim_id and claim.claim_id

    # Create claimant data with just an absence case number
    claimant_data_1 = FineosPaymentData(
        absence_case_number="NTN-001-ABS-01",
        absence_period_c_value=1448,
        absence_period_i_value=1,
        leave_request_id=1,
    )
    claimant_data_2 = FineosPaymentData(
        absence_case_number="NTN-001-ABS-02",
        absence_period_c_value=1448,
        absence_period_i_value=1,
        leave_request_id=1,
    )

    _, _ = add_db_records_from_fineos_data(test_db_session, claimant_data_1)
    _, _ = add_db_records_from_fineos_data(test_db_session, claimant_data_2)

    stage_claimant_extract_data(
        [claimant_data_1, claimant_data_2], test_db_session, mock_payments_s3_config
    )

    run_decomposed_claimant_extract_steps()

    claim_1 = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == claimant_data_1.absence_case_number)
        .one_or_none()
    )
    assert claim_1 is not None
    absence_period_1 = claim_1.absence_periods[0]
    claim_2 = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == claimant_data_2.absence_case_number)
        .one_or_none()
    )

    assert absence_period_1 is not None
    assert claim_2 is not None
    assert claim_2.absence_periods == []

    metrics = sync_absence_periods_step.get_log_entry().metrics
    assert metrics[sync_absence_periods_step.Metrics.ABSENCE_PERIOD_CLAIM_MISMATCH] == 0
    # It doesn't log an error here but has the correct data only


def test_update_absence_period_with_mismatching_claim_id_from_different_reference_file_ids(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
    sync_absence_periods_step,
):
    # We test if an absence period with matching absence_period.(class_id, index_id) has a mis-match on
    # absence_period.claim_id and claim.claim_id

    # Create claimant data with just an absence case number
    claimant_data_1 = FineosPaymentData(
        absence_case_number="NTN-001-ABS-01",
        absence_period_c_value=1448,
        absence_period_i_value=1,
        leave_request_id=1,
    )
    _, _ = add_db_records_from_fineos_data(test_db_session, claimant_data_1)

    stage_claimant_extract_data(
        [claimant_data_1],
        test_db_session,
        mock_payments_s3_config,
        date_of_extract=datetime.date(2024, 3, 23),
    )

    run_decomposed_claimant_extract_steps()

    # Mocking as if this was ran on a following day
    claimant_data_2 = FineosPaymentData(
        absence_case_number="NTN-001-ABS-02",
        absence_period_c_value=1448,
        absence_period_i_value=1,
        leave_request_id=1,
    )

    _, _ = add_db_records_from_fineos_data(test_db_session, claimant_data_2)

    stage_claimant_extract_data(
        [claimant_data_2],
        test_db_session,
        mock_payments_s3_config,
        date_of_extract=datetime.date(2024, 3, 24),
    )

    run_decomposed_claimant_extract_steps()

    claim_1 = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == claimant_data_1.absence_case_number)
        .one_or_none()
    )
    assert claim_1 is not None
    absence_period_1 = claim_1.absence_periods[0]
    claim_2 = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == claimant_data_2.absence_case_number)
        .one_or_none()
    )

    assert absence_period_1 is not None
    assert claim_2 is not None
    assert claim_2.absence_periods == []

    metrics = sync_absence_periods_step.get_log_entry().metrics
    assert metrics[sync_absence_periods_step.Metrics.ABSENCE_PERIOD_CLAIM_MISMATCH] == 2


def test_create_or_update_absence_period_with_incomplete_request_absence_data(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
    sync_absence_periods_step,
):
    # Create claimant data, with request absence fields ABSENCEPERIOD_CLASSID, ABSENCEPERIOD_INDEXID as Unknown
    claimant_data = FineosPaymentData(
        leave_request_start="2021-02-14",
        leave_request_end="2021-02-28",
        leave_request_id=5,
        leave_request_evidence="UnSatisfied",
        absence_period_c_value="",
        absence_period_i_value="",
    )
    _, _ = add_db_records_from_fineos_data(test_db_session, claimant_data)
    stage_claimant_extract_data([claimant_data], test_db_session, mock_payments_s3_config)

    run_decomposed_claimant_extract_steps()

    claim = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == claimant_data.absence_case_number)
        .first()
    )

    assert len(claim.absence_periods) == 0

    metrics = sync_absence_periods_step.get_log_entry().metrics
    assert (
        metrics[
            sync_absence_periods_step.Metrics.ABSENCE_PERIOD_CLASS_ID_OR_INDEX_ID_NOT_FOUND_COUNT
        ]
        == 1
    )


def test_update_employee_info_happy_path(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    formatted_claim,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
):
    tax_identifier = TaxIdentifierFactory(tax_identifier="********9")
    EmployeeFactory(tax_identifier=tax_identifier)

    claimant_data = format_claimant_data()
    stage_claimant_extract_data([claimant_data], test_db_session, mock_payments_s3_config)

    run_decomposed_claimant_extract_steps()
    employee = (
        test_db_session.query(Employee)
        .filter(Employee.fineos_customer_number == claimant_data.customer_number)
        .one()
    )

    assert employee is not None
    assert employee.date_of_birth == datetime.date(1967, 4, 27)
    assert employee.mass_id_number == claimant_data.mass_id_number
    assert employee.out_of_state_id_number == claimant_data.out_of_state_id_number


def test_employee_mass_id_out_of_state_id_not_provided(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    formatted_claim,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
):
    claimant_data = FineosPaymentData(mass_id_number=None, out_of_state_id_number=None)

    tax_identifier = TaxIdentifierFactory(tax_identifier=claimant_data.tin)
    EmployeeFactory(tax_identifier=tax_identifier)
    stage_claimant_extract_data([claimant_data], test_db_session, mock_payments_s3_config)

    run_decomposed_claimant_extract_steps()
    employee = (
        test_db_session.query(Employee)
        .filter(Employee.fineos_customer_number == claimant_data.customer_number)
        .one_or_none()
    )

    assert employee is not None
    assert employee.mass_id_number is None
    assert employee.out_of_state_id_number is None


def test_employee_mass_id_invalid_value(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    formatted_claim,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
):
    invalid_mass_id = "********"
    claimant_data = FineosPaymentData(mass_id_number=invalid_mass_id, out_of_state_id_number=None)

    tax_identifier = TaxIdentifierFactory(tax_identifier=claimant_data.tin)
    employee = EmployeeFactory(tax_identifier=tax_identifier)
    stage_claimant_extract_data([claimant_data], test_db_session, mock_payments_s3_config)
    run_decomposed_claimant_extract_steps()
    employee = (
        test_db_session.query(Employee)
        .filter(Employee.fineos_customer_number == claimant_data.customer_number)
        .one()
    )

    assert employee is not None
    assert employee.mass_id_number is None
    assert employee.out_of_state_id_number is None


def test_update_employee_info_not_in_db(
    test_db_session,
    mock_payments_s3_config,
    run_decomposed_claimant_extract_steps,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
):
    claimant_data = format_claimant_data()

    tax_identifier = TaxIdentifierFactory(tax_identifier="*********")
    EmployeeFactory(tax_identifier=tax_identifier)
    stage_claimant_extract_data([claimant_data], test_db_session, mock_payments_s3_config)

    run_decomposed_claimant_extract_steps()
    employee = (
        test_db_session.query(Employee)
        .filter(Employee.fineos_customer_number == claimant_data.customer_number)
        .one_or_none()
    )

    assert employee is None


def test_update_eft_info_happy_path(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
):
    claimant_data = FineosPaymentData(
        routing_nbr="*********", account_nbr="********9", account_type="Checking"
    )
    employee, _ = add_db_records_from_fineos_data(test_db_session, claimant_data, add_eft=False)
    assert len(employee.pub_efts.all()) == 0

    stage_claimant_extract_data([claimant_data], test_db_session, mock_payments_s3_config)

    run_decomposed_claimant_extract_steps()

    updated_employee: Optional[Employee] = (
        test_db_session.query(Employee)
        .filter(Employee.employee_id == employee.employee_id)
        .one_or_none()
    )

    pub_efts = updated_employee.pub_efts.all()
    assert len(pub_efts) == 1
    assert pub_efts[0].pub_eft.routing_nbr == "*********"
    assert pub_efts[0].pub_eft.account_nbr == "********9"
    assert pub_efts[0].pub_eft.bank_account_type_id == BankAccountType.CHECKING.bank_account_type_id
    assert pub_efts[0].pub_eft.prenote_state_id == PrenoteState.PENDING_PRE_PUB.prenote_state_id


def test_update_eft_info_validation_issues(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
):
    # Routing number doesn't pass checksum, but is correct length
    claimant_data1 = FineosPaymentData(
        routing_nbr="*********", account_nbr="********9", account_type="Checking"
    )
    employee1, _ = add_db_records_from_fineos_data(test_db_session, claimant_data1, add_eft=False)

    # Routing number incorrect length.
    claimant_data2 = FineosPaymentData(
        routing_nbr="123", account_nbr="********9", account_type="Checking"
    )
    employee2, _ = add_db_records_from_fineos_data(test_db_session, claimant_data2, add_eft=False)

    # Account number incorrect length.
    long_num = "********90********"
    claimant_data3 = FineosPaymentData(
        routing_nbr="*********", account_nbr=long_num, account_type="Checking"
    )
    employee3, _ = add_db_records_from_fineos_data(test_db_session, claimant_data3, add_eft=False)

    # Account type incorrect.
    claimant_data4 = FineosPaymentData(
        routing_nbr="*********",
        account_nbr="********901234567",
        account_type="Certificate of Deposit",
    )
    employee4, _ = add_db_records_from_fineos_data(test_db_session, claimant_data4, add_eft=False)

    # Account type and Routing number incorrect.
    claimant_data5 = FineosPaymentData(
        routing_nbr="********",
        account_nbr="********901234567",
        account_type="Certificate of Deposit",
    )
    employee5, _ = add_db_records_from_fineos_data(test_db_session, claimant_data5, add_eft=False)

    stage_claimant_extract_data(
        [
            claimant_data1,
            claimant_data2,
            claimant_data3,
            claimant_data4,
            claimant_data5,
        ],
        test_db_session,
        mock_payments_s3_config,
    )

    run_decomposed_claimant_extract_steps()

    updated_employee1: Optional[Employee] = (
        test_db_session.query(Employee)
        .filter(Employee.employee_id == employee1.employee_id)
        .one_or_none()
    )
    claim1 = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == claimant_data1.absence_case_number)
        .first()
    )
    assert claim1 is not None

    # assert claim1.state_logs[0].outcome["validation_container"]["validation_issues"] == [
    #     {
    #         "reason": "RoutingNumberFailsChecksum",
    #         "details": "SORTCODE: *********",
    #         "field_name": "SORTCODE",
    #     }
    # ]
    assert len(updated_employee1.pub_efts.all()) == 0

    updated_employee2: Optional[Employee] = (
        test_db_session.query(Employee)
        .filter(Employee.employee_id == employee2.employee_id)
        .one_or_none()
    )
    claim2 = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == claimant_data2.absence_case_number)
        .one_or_none()
    )
    assert claim2 is not None
    # assert claim2.state_logs[0].outcome["validation_container"]["validation_issues"] == [
    #     {
    #         "reason": "FieldTooShort",
    #         "details": "SORTCODE: 123",
    #         "field_name": "SORTCODE",
    #     },
    #     {
    #         "reason": "RoutingNumberFailsChecksum",
    #         "details": "SORTCODE: 123",
    #         "field_name": "SORTCODE",
    #     },
    # ]
    assert len(updated_employee2.pub_efts.all()) == 0

    updated_employee3: Optional[Employee] = (
        test_db_session.query(Employee)
        .filter(Employee.employee_id == employee3.employee_id)
        .one_or_none()
    )
    claim3 = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == claimant_data3.absence_case_number)
        .one_or_none()
    )
    assert claim3 is not None

    # assert claim3.state_logs[0].outcome["validation_container"]["validation_issues"] == [
    #     {
    #         "reason": "FieldTooLong",
    #         "details": "ACCOUNTNO: ********90********",
    #         "field_name": "ACCOUNTNO",
    #     }
    # ]
    assert len(updated_employee3.pub_efts.all()) == 0

    updated_employee4: Optional[Employee] = (
        test_db_session.query(Employee)
        .filter(Employee.employee_id == employee4.employee_id)
        .one_or_none()
    )
    claim4 = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == claimant_data4.absence_case_number)
        .one_or_none()
    )
    assert claim4 is not None

    # assert claim4.state_logs[0].outcome["validation_container"]["validation_issues"] == [
    #     {
    #         "reason": "InvalidLookupValue",
    #         "details": "ACCOUNTTYPE: Certificate of Deposit",
    #         "field_name": "ACCOUNTTYPE",
    #     }
    # ]
    assert len(updated_employee4.pub_efts.all()) == 0

    updated_employee5: Optional[Employee] = (
        test_db_session.query(Employee)
        .filter(Employee.employee_id == employee5.employee_id)
        .one_or_none()
    )
    claim5 = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == claimant_data5.absence_case_number)
        .one_or_none()
    )
    assert claim5 is not None

    # assert claim5.state_logs[0].outcome["validation_container"]["validation_issues"] == [
    #     {
    #         "reason": "FieldTooShort",
    #         "details": "SORTCODE: ********",
    #         "field_name": "SORTCODE",
    #     },
    #     {
    #         "reason": "RoutingNumberFailsChecksum",
    #         "details": "SORTCODE: ********",
    #         "field_name": "SORTCODE",
    #     },
    #     {
    #         "reason": "InvalidLookupValue",
    #         "details": "ACCOUNTTYPE: Certificate of Deposit",
    #         "field_name": "ACCOUNTTYPE",
    #     },
    # ]
    assert len(updated_employee5.pub_efts.all()) == 0


def test_run_step_validation_issues(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    formatted_claim,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
):
    # Create some validation issues
    fineos_data = FineosPaymentData(
        routing_nbr="",
        leave_request_end="",
        date_of_birth="",
        fineos_employee_first_name="",
        fineos_employee_last_name="",
    )

    employee_before, _ = add_db_records_from_fineos_data(
        test_db_session, fineos_data, add_eft=False
    )

    stage_claimant_extract_data([fineos_data], test_db_session, mock_payments_s3_config)

    # Run the process
    run_decomposed_claimant_extract_steps()

    # Verify the Employee was still updated with valid fields
    employee = (
        test_db_session.query(Employee)
        .filter(Employee.employee_id == employee_before.employee_id)
        .one_or_none()
    )
    assert employee
    assert employee.fineos_customer_number == fineos_data.customer_number
    assert employee.date_of_birth == employee_before.date_of_birth

    # Because one piece of EFT info was invalid, we did not create it
    assert len(employee.pub_efts.all()) == 0

    # Verify the claim was still created despite an invalid field (that isn't set)
    assert len(employee.claims) == 1
    claim = employee.claims[0]
    LeaveRequestFactory.create(
        claim_id=claim.claim_id,
        is_id_proofed=True,
    )
    assert claim.fineos_absence_id == fineos_data.absence_case_number
    assert claim.employee_id == employee.employee_id
    assert claim.fineos_notification_id == fineos_data.notification_number
    assert (
        ClaimType.get_id(claim.claim_type_description)
        == ABSENCE_REASON_ID_CLAIM_TYPE_ID_MAPPING[AbsenceReason.get_id(fineos_data.absence_reason)]
    )
    assert claim.fineos_absence_status_id == AbsenceStatus.get_id(fineos_data.absence_case_status)

    # Start Date is not set because of logic changes. If either start_date or end_date is not set, ignore both.
    assert claim.claim_start_date is None
    assert claim.claim_end_date is None  # Due to being empty
    assert claim.get_is_id_proofed
    assert claim.employer.fineos_employer_id == int(fineos_data.employer_customer_num)

    # # Verify the state logs and outcome
    # assert len(claim.state_logs) == 1
    # state_log = claim.state_logs[0]
    # assert (
    #     state_log.end_state_id == State.DELEGATED_CLAIM_ADD_TO_CLAIM_EXTRACT_ERROR_REPORT.state_id
    # )
    # validation_issues = state_log.outcome["validation_container"]["validation_issues"]
    # # AbsencePeriod Start is not included in validation issues because it is technically a valid field.
    # # Even though it is technically valid, it should not be set on the claim unless both start_date and end_date are present.
    # assert validation_issues == [
    #     {
    #         "reason": "MissingField",
    #         "details": "ABSENCEPERIOD_END",
    #         "field_name": "ABSENCEPERIOD_END",
    #     },
    #     {
    #         "reason": "MissingField",
    #         "details": "DATEOFBIRTH",
    #         "field_name": "DATEOFBIRTH",
    #     },
    #     {"reason": "MissingField", "details": "FIRSTNAMES", "field_name": "FIRSTNAMES"},
    #     {"reason": "MissingField", "details": "LASTNAME", "field_name": "LASTNAME"},
    #     {"reason": "MissingField", "details": "SORTCODE", "field_name": "SORTCODE"},
    # ]


def test_run_step_minimal_viable_claim(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
    sync_claims_step,
):
    # Create a record with only an absence case number
    # This should still end up created in the DB, but with
    # significant validation issues
    fineos_data = FineosPaymentData(
        False, include_employee_feed=False, absence_case_number="ABS-001"
    )

    stage_claimant_extract_data([fineos_data], test_db_session, mock_payments_s3_config)

    # Run the process
    run_decomposed_claimant_extract_steps()

    claim = test_db_session.query(Claim).one_or_none()
    assert claim
    assert claim.fineos_absence_id == fineos_data.absence_case_number
    assert claim.employee_id is None
    assert not claim.fineos_notification_id
    assert claim.claim_type_id is None
    assert claim.fineos_absence_status_id is None
    assert claim.claim_start_date is None
    assert claim.claim_end_date is None
    assert claim.get_is_id_proofed is False
    assert claim.organization_unit_id is None

    # State logs are not managed in sync_claims_step
    # Using metrics to verify validation issues
    metrics = sync_claims_step.get_log_entry().metrics
    assert metrics[ClaimantExtractMetrics.EMPLOYER_CUSTOMERNO_NOT_FOUND_COUNT] == 1
    assert metrics[ClaimantExtractMetrics.CLAIM_NOT_FOUND_COUNT] == 1
    assert metrics[ClaimantExtractMetrics.EMPLOYEE_NOT_FOUND_IN_DATABASE_COUNT] == 1
    assert metrics[ClaimantExtractMetrics.CLAIM_PROCESSED_COUNT] == 1


def test_run_step_not_id_proofed(
    run_decomposed_claimant_extract_steps, test_db_session, mock_payments_s3_config
):
    fineos_data = FineosPaymentData(leave_request_evidence="Rejected")

    add_db_records_from_fineos_data(test_db_session, fineos_data)
    stage_claimant_extract_data([fineos_data], test_db_session, mock_payments_s3_config)

    # Run the process
    run_decomposed_claimant_extract_steps()

    # Validate the claim was created properly
    claim = test_db_session.query(Claim).one_or_none()
    assert claim
    assert claim.fineos_absence_id == fineos_data.absence_case_number
    assert claim.employee_id is not None
    assert claim.fineos_notification_id == fineos_data.notification_number
    assert (
        ClaimType.get_id(claim.claim_type_description)
        == ABSENCE_REASON_ID_CLAIM_TYPE_ID_MAPPING[AbsenceReason.get_id(fineos_data.absence_reason)]
    )
    assert claim.fineos_absence_status_id == AbsenceStatus.get_id(fineos_data.absence_case_status)
    assert claim.claim_start_date is not None
    assert claim.claim_end_date is not None
    assert not claim.get_is_id_proofed

    # Verify the state logs
    assert len(claim.state_logs) == 0


def test_run_step_no_default_payment_pref(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
):
    # Create records without a default payment preference
    # None of the payment preference related fields will be set
    fineos_data = FineosPaymentData(
        default_payment_pref="N",
        payment_method="Elec Funds Transfer",
        account_nbr="********9",
        routing_nbr="*********",
        account_type="Checking",
    )

    employee_before, _ = add_db_records_from_fineos_data(
        test_db_session, fineos_data, add_eft=False
    )

    stage_claimant_extract_data([fineos_data], test_db_session, mock_payments_s3_config)

    # Run the process
    run_decomposed_claimant_extract_steps()

    # Verify the Employee was still updated
    employee = (
        test_db_session.query(Employee)
        .filter(Employee.employee_id == employee_before.employee_id)
        .one_or_none()
    )
    assert employee
    assert employee.fineos_customer_number == fineos_data.customer_number

    # Because the payment preferences weren't the default, no EFT records are created
    assert len(employee.pub_efts.all()) == 0

    # The claim still is attached to the employee
    assert len(employee.claims) == 1
    claim = employee.claims[0]
    assert claim.fineos_absence_id == fineos_data.absence_case_number
    assert claim.employee_id == employee.employee_id


def test_run_step_mix_of_payment_prefs(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
):
    # Create a record that isn't a default payment preference
    # then create another record with the same customer number & absence case number
    # but with default payment preference set to Y
    # We will use the default payment preference and ignore the other record
    not_default_fineos_data = FineosPaymentData(
        default_payment_pref="N",
        payment_method="Check",
        account_nbr="Unknown",
        routing_nbr="Unknown",
        account_type="Unknown",
    )

    default_fineos_data = copy.deepcopy(not_default_fineos_data)
    default_fineos_data.default_payment_pref = "Y"
    default_fineos_data.payment_method = "Elec Funds Transfer"
    default_fineos_data.account_nbr = "********9"
    default_fineos_data.routing_nbr = "*********"
    default_fineos_data.account_type = "Checking"

    # Create the employee record
    employee_before, _ = add_db_records_from_fineos_data(
        test_db_session, default_fineos_data, add_eft=False
    )

    stage_claimant_extract_data(
        [default_fineos_data],
        test_db_session,
        mock_payments_s3_config,
        additional_employee_feed_records=[not_default_fineos_data],
    )

    # Run the process
    run_decomposed_claimant_extract_steps()

    # Verify the Employee was updated
    employee = (
        test_db_session.query(Employee)
        .filter(Employee.employee_id == employee_before.employee_id)
        .one_or_none()
    )
    assert employee
    assert employee.fineos_customer_number == default_fineos_data.customer_number

    # The default payment preferences were used.
    pub_efts = employee.pub_efts.all()
    assert len(pub_efts) == 1
    assert pub_efts[0].pub_eft.routing_nbr == default_fineos_data.routing_nbr
    assert pub_efts[0].pub_eft.account_nbr == default_fineos_data.account_nbr
    assert pub_efts[0].pub_eft.bank_account_type_id == BankAccountType.CHECKING.bank_account_type_id

    # The claim was attached to the employee
    assert len(employee.claims) == 1
    claim = employee.claims[0]
    assert claim.fineos_absence_id == default_fineos_data.absence_case_number
    assert claim.employee_id == employee.employee_id


def test_run_step_uses_correct_start_and_end_dates(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
):
    not_default_fineos_data = FineosPaymentData(
        leave_request_start="2021-01-01 12:00:00",
        leave_request_end="2021-04-01 12:00:00",
        absence_period_c_value="111",
        absence_period_i_value="555",
    )

    default_fineos_data = copy.deepcopy(not_default_fineos_data)

    default_fineos_data.leave_request_start = "2021-02-01 12:00:00"
    default_fineos_data.leave_request_end = "2021-05-01 12:00:00"
    default_fineos_data.absence_period_class_id = 111
    default_fineos_data.absence_period_index_id = 444

    # Create the employee record
    employee_before, _ = add_db_records_from_fineos_data(
        test_db_session, default_fineos_data, add_eft=False
    )

    stage_claimant_extract_data(
        [default_fineos_data],
        test_db_session,
        mock_payments_s3_config,
        additional_requested_absence_records=[not_default_fineos_data],
    )

    # Ensure there are two requested absences to process
    requested_absences = (
        test_db_session.query(FineosExtractVbiRequestedAbsence).all()
        + test_db_session.query(FineosExtractVbiRequestedAbsenceSom).all()
    )
    assert len(requested_absences) == 4

    # Run the process
    run_decomposed_claimant_extract_steps()

    # Verify the Employee was updated
    employee = (
        test_db_session.query(Employee)
        .filter(Employee.employee_id == employee_before.employee_id)
        .one_or_none()
    )
    assert employee
    assert employee.fineos_customer_number == default_fineos_data.customer_number

    # The claim was attached to the employee
    assert len(employee.claims) == 1
    claim = employee.claims[0]
    assert claim.fineos_absence_id == default_fineos_data.absence_case_number
    assert claim.employee_id == employee.employee_id

    # The earliest start date and latest end date of the requested_absences were used
    assert claim.claim_start_date == datetime.date(2021, 1, 1)
    assert claim.claim_end_date == datetime.date(2021, 5, 1)


def test_run_step_with_missing_start_and_end_dates(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
):
    not_default_fineos_data = FineosPaymentData(
        leave_request_start="2021-01-01 12:00:00", leave_request_end=""
    )

    default_fineos_data = copy.deepcopy(not_default_fineos_data)

    default_fineos_data.leave_request_start = "2021-02-01 12:00:00"
    default_fineos_data.leave_request_end = "2021-05-01 12:00:00"

    # Create the employee record
    employee_before, _ = add_db_records_from_fineos_data(
        test_db_session, default_fineos_data, add_eft=False
    )

    stage_claimant_extract_data(
        [default_fineos_data],
        test_db_session,
        mock_payments_s3_config,
        additional_requested_absence_records=[not_default_fineos_data],
    )

    # Run the process
    run_decomposed_claimant_extract_steps()

    # Verify the Employee was updated
    employee = (
        test_db_session.query(Employee)
        .filter(Employee.employee_id == employee_before.employee_id)
        .one_or_none()
    )
    assert employee
    assert employee.fineos_customer_number == default_fineos_data.customer_number

    # The claim was attached to the employee
    assert len(employee.claims) == 1
    claim = employee.claims[0]
    assert claim.fineos_absence_id == default_fineos_data.absence_case_number
    assert claim.employee_id == employee.employee_id

    # The process now considers any requested absence where both start and end are there
    # But ignores if one of start_date or end_date was missing or invalid, does not update claim
    assert claim.claim_start_date == datetime.date(2021, 2, 1)
    # Start was not updated to the earlier 2021-1-1 date because end was missing on the second period
    assert claim.claim_end_date == datetime.date(2021, 5, 1)


def test_claimant_data_validation_matching_dupes(
    test_db_session,
    mock_payments_s3_config,
    initialize_factories_session,
    run_decomposed_claimant_extract_steps,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
):
    # See the test_claimant_data_validation_matching_dupes test, this is the happy case
    # and is just present to verify and avoid regression of behavior
    claimant_data = FineosPaymentData(
        employer_customer_num="1234",
        customer_number="********",
        absence_case_status="Approved",
    )
    # Make a 2nd set of data exactly the same
    claimant_data2 = copy.deepcopy(claimant_data)
    _, _ = add_db_records_from_fineos_data(test_db_session, claimant_data, add_eft=False)

    stage_claimant_extract_data(
        [claimant_data, claimant_data2], test_db_session, mock_payments_s3_config
    )

    run_decomposed_claimant_extract_steps()
    claim = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == claimant_data.absence_case_number)
        .first()
    )

    # No issues
    assert claim.state_logs == []


def test_claimant_data_validation_nonmatching_dupes(
    test_db_session,
    mock_payments_s3_config,
    initialize_factories_session,
    run_decomposed_claimant_extract_steps,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
):
    # Verify validation issues are found when
    # we receive two rows in the requested absence
    # file with differing values for a few key fields

    claimant_data = FineosPaymentData(
        employer_customer_num="1234",
        customer_number="********",
        absence_case_status="Approved",
        absence_period_index_id="1111",
        organization_unit_name="Org A",
    )
    # Make a 2nd set of data with a few values different
    claimant_data2 = copy.deepcopy(claimant_data)
    claimant_data2.employer_customer_num = ""
    claimant_data2.customer_number = "999999999"
    claimant_data2.absence_case_status = "Intake In Progress"
    claimant_data2.absence_period_index_id = (
        "861732"  # needs to be different or would be dedupped in sync_absence_periods_step
    )
    claimant_data2.organization_unit_name = "Org B"
    _, _ = add_db_records_from_fineos_data(test_db_session, claimant_data, add_eft=False)

    stage_claimant_extract_data(
        [claimant_data, claimant_data2], test_db_session, mock_payments_s3_config
    )

    run_decomposed_claimant_extract_steps()

    claim = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == claimant_data.absence_case_number)
        .first()
    )

    # Verify it unset the fields so we won't update these downstream
    assert claim.employer is None
    assert claim.employee is None
    assert claim.fineos_absence_status is None
    assert claim.organization_unit is None


def test_claimant_data_validation_matching_organization_unit_name(
    test_db_session,
    mock_payments_s3_config,
    initialize_factories_session,
    run_decomposed_claimant_extract_steps,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
):
    claimant_data = FineosPaymentData(
        employer_customer_num="1234",
        customer_number="********",
        absence_case_status="Approved",
        absence_period_index_id="1111",
        organization_unit_name="Org A",
    )
    # Same organization unit name.
    claimant_data2 = copy.deepcopy(claimant_data)
    claimant_data2.absence_period_index_id = "1112"

    # None will be excluded from the duplication check.
    claimant_data3 = copy.deepcopy(claimant_data)
    claimant_data3.organization_unit_name = None
    claimant_data3.absence_period_index_id = "1113"

    # Empty string will be excluded from the duplication check.
    claimant_data4 = copy.deepcopy(claimant_data)
    claimant_data4.organization_unit_name = ""
    claimant_data4.absence_period_index_id = "1114"

    _, _ = add_db_records_from_fineos_data(test_db_session, claimant_data, add_eft=False)

    stage_claimant_extract_data(
        [claimant_data, claimant_data2, claimant_data3, claimant_data4],
        test_db_session,
        mock_payments_s3_config,
    )

    run_decomposed_claimant_extract_steps()

    claim = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == claimant_data.absence_case_number)
        .first()
    )

    assert claim.organization_unit is not None


def test_create_or_update_leave_request(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    set_exporter_env_vars,
):
    # Verify the leave request is successfully created

    payment_data = FineosPaymentData(
        absence_reason="Serious Health Condition - Employee",
        leave_request_notes="Claimant Wages Under Minimum",
        leave_request_decision="Approved",
        leave_request_evidence="Pending",
    )
    stage_claimant_extract_data(
        [payment_data],
        test_db_session,
        mock_payments_s3_config,
        date_of_extract=datetime.date(2022, 12, 22),
    )
    run_decomposed_claimant_extract_steps()

    leave_request = test_db_session.query(LeaveRequest).one_or_none()

    claim = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == payment_data.absence_case_number)
        .one()
    )

    assert leave_request.claim_id == claim.claim_id
    assert leave_request.fineos_leave_request_id == int(payment_data.leave_request_id)
    assert leave_request.is_id_proofed == (payment_data.leave_request_evidence == "Satisfied")
    assert leave_request.absence_reason_id == AbsenceReason.get_id(payment_data.absence_reason)
    assert leave_request.eligibility_decision_id == EligibilityDecision.get_id(
        payment_data.leave_request_notes.lower()
    )
    assert leave_request.leave_approval_decision_id == LeaveRequestDecision.get_id(
        payment_data.leave_request_decision
    )

    # Verify the leave request is successfully updated
    updated_payment_data = FineosPaymentData(
        absence_case_number=payment_data.absence_case_number,
        leave_request_id=payment_data.leave_request_id,
        absence_reason="Care for a Family Member",
        leave_request_notes="Financially Eligible",
        leave_request_decision="Approved",
        leave_request_evidence="Satisfied",
    )
    stage_claimant_extract_data(
        [updated_payment_data],
        test_db_session,
        mock_payments_s3_config,
        date_of_extract=datetime.date(2022, 12, 23),
    )
    run_decomposed_claimant_extract_steps()

    leave_requests = test_db_session.query(LeaveRequest).all()

    assert len(leave_requests) == 1
    leave_request = leave_requests[0]
    assert leave_request.claim_id == claim.claim_id
    assert leave_request.fineos_leave_request_id == int(updated_payment_data.leave_request_id)
    assert leave_request.is_id_proofed == (
        updated_payment_data.leave_request_evidence == "Satisfied"
    )
    assert leave_request.absence_reason_id == AbsenceReason.get_id(
        updated_payment_data.absence_reason
    )
    assert leave_request.eligibility_decision_id == EligibilityDecision.get_id(
        updated_payment_data.leave_request_notes.lower()
    )
    assert leave_request.leave_approval_decision_id == LeaveRequestDecision.get_id(
        updated_payment_data.leave_request_decision
    )


def test_create_or_update_leave_request_unknown_notes(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    set_exporter_env_vars,
):
    # Verify the leave request is successfully created with UNKNOWN status
    payment_data = FineosPaymentData(
        absence_reason="Serious Health Condition - Employee",
        leave_request_notes="some fineos error message",
        leave_request_decision="Approved",
        leave_request_evidence="Pending",
    )
    stage_claimant_extract_data([payment_data], test_db_session, mock_payments_s3_config)
    run_decomposed_claimant_extract_steps()

    leave_request = test_db_session.query(LeaveRequest).one()
    assert (
        leave_request.eligibility_decision_id == EligibilityDecision.UNKNOWN.eligibility_decision_id
    )


def test_create_leave_request_and_absence_paid_leave_case(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    set_exporter_env_vars,
    monkeypatch,
):
    monkeypatch.setenv("ENABLE_SYNC_LEAVE_REQUESTS_STEP", "true")

    stage_claimant_extract_data(
        [
            FineosPaymentData(
                leave_request_id=58290,
                leave_request_start="2022-12-19 00:00:00",
                leave_request_end="2023-01-13 00:00:00",
                absenceperiod_start="2022-12-19 00:00:00",
                absenceperiod_end="2022-12-26 00:00:00",
                averageweeklywage_monamt="776.92",
                extawwpart2_monamt="0",
                notification_number="NTN-479051",
                absence_case_number="NTN-479051-ABS-01",
                paidleave_casenumber="PL ABS-479052",
                period_from_date="2022-12-19 00:00:00",
                period_to_date="2023-01-13 00:00:00",
                benefit_case_number="PL ABS-479052-PL ABS-01",
                include_vbi_leavesummary=True,
            )
        ],
        test_db_session,
        mock_payments_s3_config,
    )

    # This tests a bug addressed in PFMLPB-12832, which would cause an exception to be raised
    # during this step if an APLC was created at the same time as the LR when
    # ENABLE_SYNC_LEAVE_REQUESTS_STEP was true
    run_decomposed_claimant_extract_steps()


def test_create_or_update_absence_paid_leave_case(
    run_decomposed_claimant_extract_steps, test_db_session, now_eastern, mock_payments_s3_config
):
    # Verify the absence paid leave case is successfully created
    # See this case information on the tech spec: PFMLPB-8555
    # https://lwd.atlassian.net/wiki/spaces/DD/pages/2657910899/Tech+Spec+PFMLPB-8555+Add+Paid+Leave+Case+to+PFML+data+model
    payment_data1 = FineosPaymentData(
        leave_request_id=58290,
        leave_request_start="2022-12-19 00:00:00",
        leave_request_end="2023-01-13 00:00:00",
        absenceperiod_start="2022-12-19 00:00:00",
        absenceperiod_end="2022-12-26 00:00:00",
        averageweeklywage_monamt="776.92",
        extawwpart2_monamt="0",
        notification_number="NTN-479051",
        absence_case_number="NTN-479051-ABS-01",
        paidleave_casenumber="PL ABS-479052",
        period_from_date="2022-12-19 00:00:00",
        period_to_date="2023-01-13 00:00:00",
        benefit_case_number="PL ABS-479052-PL ABS-01",
        include_vbi_leavesummary=True,
        c_selectedleaveplan=1,
        i_selectedleaveplan=1,
        lastupdatedate="2022-12-19 00:00:00",
    )
    payment_data1_second_absence_period = FineosPaymentData(
        leave_request_id=58290,
        leave_request_start="2022-12-19 00:00:00",
        leave_request_end="2023-01-13 00:00:00",
        absenceperiod_start="2022-12-27 00:00:00",
        absenceperiod_end="2023-01-13 00:00:00",
        averageweeklywage_monamt="776.92",
        extawwpart2_monamt="0",
        notification_number="NTN-479051",
        absence_case_number="NTN-479051-ABS-01",
        paidleave_casenumber="PL ABS-479052",
        period_from_date="2022-12-19 00:00:00",
        period_to_date="2023-01-13 00:00:00",
        benefit_case_number="PL ABS-479052-PL ABS-01",
        include_vbi_leavesummary=False,
        c_selectedleaveplan=1,
        i_selectedleaveplan=1,
        lastupdatedate="2022-12-19 00:00:00",
    )
    payment_data2 = FineosPaymentData(
        leave_request_id=58291,
        leave_request_start="2023-01-14 00:00:00",
        leave_request_end="2023-02-24 00:00:00",
        absenceperiod_start="2023-01-14 00:00:00",
        absenceperiod_end="2023-02-24 00:00:00",
        averageweeklywage_monamt="876.92",
        extawwpart2_monamt="376.92",
        notification_number="NTN-479051",
        absence_case_number="NTN-479051-ABS-01",
        paidleave_casenumber="PL ABS-479054",
        period_from_date="2023-01-14 00:00:00",
        period_to_date="2023-02-11 00:00:00",
        benefit_case_number="PL ABS-479054-PL ABS-01",
        include_vbi_leavesummary=True,
        c_selectedleaveplan=1,
        i_selectedleaveplan=2,
        lastupdatedate="2022-12-19 00:00:00",
    )
    payment_data3 = FineosPaymentData(
        absence_case_number="NTN-479051-ABS-01",
        notification_number="NTN-479051",
        leave_request_id=58291,
        leave_request_start="2023-01-14 00:00:00",
        leave_request_end="2023-02-24 00:00:00",
        absenceperiod_start="2023-01-14 00:00:00",
        absenceperiod_end="2023-02-24 00:00:00",
        averageweeklywage_monamt="876.92",
        extawwpart2_monamt="376.92",
        paidleave_casenumber="PL ABS-479055",
        period_from_date="2023-02-12 00:00:00",
        period_to_date="2023-02-24 00:00:00",
        benefit_case_number="PL ABS-479055-PL ABS-01",
        include_vbi_leavesummary=False,
        c_selectedleaveplan=1,
        i_selectedleaveplan=2,
        lastupdatedate="2022-12-19 00:00:00",
    )

    stage_claimant_extract_data(
        [
            payment_data1,
            payment_data1_second_absence_period,
            payment_data2,
            payment_data3,
        ],
        test_db_session,
        mock_payments_s3_config,
        date_of_extract=now_eastern,
    )
    run_decomposed_claimant_extract_steps()

    absence_paid_leave_cases = test_db_session.query(AbsencePaidLeaveCase).all()
    assert test_db_session.query(FineosExtractVbiLeavePlanRequestedAbsence).all() is not None
    claim = (
        test_db_session.query(Claim)
        .filter(Claim.fineos_absence_id == payment_data1.absence_case_number)
        .one()
    )

    expected_records = {}

    leave_request_58290 = (
        test_db_session.query(LeaveRequest)
        .filter(LeaveRequest.fineos_leave_request_id == 58290)
        .one_or_none()
    )
    leave_request_58291 = (
        test_db_session.query(LeaveRequest)
        .filter(LeaveRequest.fineos_leave_request_id == 58291)
        .one_or_none()
    )
    assert leave_request_58291 is not None
    assert leave_request_58290 is not None
    expected_records: dict[str, dict[str, any]] = {
        "PL ABS-479052": dict(
            absence_paid_leave_case_number="PL ABS-479052",
            start_date=datetime.date(2022, 12, 19),
            end_date=datetime.date(2023, 1, 13),
            average_weekly_wage="776.92",
            leave_request_id=leave_request_58290.leave_request_id,
            claim_id=claim.claim_id,
        ),
        "PL ABS-479054": dict(
            absence_paid_leave_case_number="PL ABS-479054",
            start_date=datetime.date(2023, 1, 14),
            end_date=datetime.date(2023, 2, 11),
            average_weekly_wage="876.92",
            leave_request_id=leave_request_58291.leave_request_id,
            claim_id=claim.claim_id,
        ),
        "PL ABS-479055": dict(
            absence_paid_leave_case_number="PL ABS-479055",
            start_date=datetime.date(2023, 2, 12),
            end_date=datetime.date(2023, 2, 24),
            average_weekly_wage="376.92",
            leave_request_id=leave_request_58291.leave_request_id,
            claim_id=claim.claim_id,
        ),
    }
    assert len(absence_paid_leave_cases) == 3
    for absence_paid_leave_case in absence_paid_leave_cases:
        expected = expected_records[absence_paid_leave_case.absence_paid_leave_case_number]
        assert absence_paid_leave_case.start_date == expected["start_date"]
        assert absence_paid_leave_case.end_date == expected["end_date"]
        assert str(absence_paid_leave_case.average_weekly_wage) == expected["average_weekly_wage"]
        assert absence_paid_leave_case.leave_request_id == expected["leave_request_id"]
        assert absence_paid_leave_case.claim_id == expected["claim_id"]

    # Verify the leave request is successfully updated
    updated_payment_data3 = FineosPaymentData(
        absence_case_number="NTN-479051-ABS-01",
        notification_number="NTN-479051",
        leave_request_id=58291,
        leave_request_start="2023-01-14 00:00:00",
        leave_request_end="2023-02-24 00:00:00",
        absenceperiod_start="2023-01-14 00:00:00",
        absenceperiod_end="2023-02-24 00:00:00",
        averageweeklywage_monamt="876.92",
        extawwpart2_monamt="999.99",
        paidleave_casenumber="PL ABS-479055",
        period_from_date="2023-02-12 00:00:00",
        period_to_date="2023-02-24 00:00:00",
        benefit_case_number="PL ABS-479055-PL ABS-01",
        include_vbi_leavesummary=False,
        c_selectedleaveplan=1,
        i_selectedleaveplan=2,
        lastupdatedate="2022-12-20 00:00:00",
    )
    stage_claimant_extract_data(
        [payment_data2, updated_payment_data3],
        test_db_session,
        mock_payments_s3_config,
        date_of_extract=now_eastern + datetime.timedelta(days=1),
    )
    run_decomposed_claimant_extract_steps()

    absence_paid_leave_cases = test_db_session.query(AbsencePaidLeaveCase).all()

    # Should not have created a new absence paid leave case
    assert len(absence_paid_leave_cases) == 3
    # Should have updated the existing absence paid leave case with the new average weekly wage
    # But other data did not change, thus should not have been updated
    for absence_paid_leave_case in absence_paid_leave_cases:
        expected = expected_records[absence_paid_leave_case.absence_paid_leave_case_number]
        assert absence_paid_leave_case.start_date == expected["start_date"]
        assert absence_paid_leave_case.end_date == expected["end_date"]
        if absence_paid_leave_case.absence_paid_leave_case_number == "PL ABS-479055":
            assert str(absence_paid_leave_case.average_weekly_wage) == "999.99"
        else:
            assert (
                str(absence_paid_leave_case.average_weekly_wage) == expected["average_weekly_wage"]
            )
        assert absence_paid_leave_case.leave_request_id == expected["leave_request_id"]
        assert absence_paid_leave_case.claim_id == expected["claim_id"]


def test_create_or_update_absence_paid_leave_case_when_not_included_in_vpaidleaveinstruction_som(
    run_decomposed_claimant_extract_steps,
    test_db_session,
    mock_payments_s3_config,
    set_exporter_env_vars,
):
    # Verify the absence paid leave case is successfully created
    # See this case information on the tech spec: PFMLPB-8555
    # https://lwd.atlassian.net/wiki/spaces/DD/pages/2657910899/Tech+Spec+PFMLPB-8555+Add+Paid+Leave+Case+to+PFML+data+model
    payment_data1 = FineosPaymentData(
        leave_request_id=58290,
        leave_request_start="2022-12-19 00:00:00",
        leave_request_end="2023-01-13 00:00:00",
        absenceperiod_start="2022-12-19 00:00:00",
        absenceperiod_end="2022-12-26 00:00:00",
        averageweeklywage_monamt="776.92",
        extawwpart2_monamt="0",
        notification_number="NTN-479051",
        absence_case_number="NTN-479051-ABS-01",
        paidleave_casenumber="PL ABS-479052",
        period_from_date="2022-12-19 00:00:00",
        period_to_date="2023-01-13 00:00:00",
        benefit_case_number="PL ABS-479052-PL ABS-01",
        include_vbi_leavesummary=True,
        include_v_paidleaveinstruction_som_record=False,
        c_selectedleaveplan=1,
        i_selectedleaveplan=1,
    )
    payment_data1_second_absence_period = FineosPaymentData(
        leave_request_id=58290,
        leave_request_start="2022-12-19 00:00:00",
        leave_request_end="2023-01-13 00:00:00",
        absenceperiod_start="2022-12-27 00:00:00",
        absenceperiod_end="2023-01-13 00:00:00",
        averageweeklywage_monamt="776.92",
        extawwpart2_monamt="0",
        notification_number="NTN-479051",
        absence_case_number="NTN-479051-ABS-01",
        paidleave_casenumber="PL ABS-479052",
        period_from_date="2022-12-19 00:00:00",
        period_to_date="2023-01-13 00:00:00",
        benefit_case_number="PL ABS-479052-PL ABS-01",
        include_vbi_leavesummary=False,
        include_v_paidleaveinstruction_som_record=False,
        c_selectedleaveplan=1,
        i_selectedleaveplan=1,
    )
    payment_data2 = FineosPaymentData(
        leave_request_id=58291,
        leave_request_start="2023-01-14 00:00:00",
        leave_request_end="2023-02-24 00:00:00",
        absenceperiod_start="2023-01-14 00:00:00",
        absenceperiod_end="2023-02-24 00:00:00",
        averageweeklywage_monamt="876.92",
        extawwpart2_monamt="376.92",
        notification_number="NTN-479051",
        absence_case_number="NTN-479051-ABS-01",
        paidleave_casenumber="PL ABS-479054",
        period_from_date="2023-01-14 00:00:00",
        period_to_date="2023-02-11 00:00:00",
        benefit_case_number="PL ABS-479054-PL ABS-01",
        include_vbi_leavesummary=True,
        include_v_paidleaveinstruction_som_record=False,
        c_selectedleaveplan=1,
        i_selectedleaveplan=2,
    )
    payment_data3 = FineosPaymentData(
        absence_case_number="NTN-479051-ABS-01",
        notification_number="NTN-479051",
        leave_request_id=58291,
        leave_request_start="2023-01-14 00:00:00",
        leave_request_end="2023-02-24 00:00:00",
        absenceperiod_start="2023-01-14 00:00:00",
        absenceperiod_end="2023-02-24 00:00:00",
        averageweeklywage_monamt="876.92",
        extawwpart2_monamt="376.92",
        paidleave_casenumber="PL ABS-479055",
        period_from_date="2023-02-12 00:00:00",
        period_to_date="2023-02-24 00:00:00",
        benefit_case_number="PL ABS-479055-PL ABS-01",
        include_vbi_leavesummary=False,
        include_v_paidleaveinstruction_som_record=False,
        c_selectedleaveplan=1,
        i_selectedleaveplan=2,
    )

    stage_claimant_extract_data(
        [
            payment_data1,
            payment_data1_second_absence_period,
            payment_data2,
            payment_data3,
        ],
        test_db_session,
        mock_payments_s3_config,
        date_of_extract=datetime.date(2023, 7, 21),
    )
    run_decomposed_claimant_extract_steps()

    absence_paid_leave_cases = test_db_session.query(AbsencePaidLeaveCase).all()
    assert test_db_session.query(FineosExtractVbiLeavePlanRequestedAbsence).all() is not None
    claims = (
        test_db_session.query(Claim).filter(
            Claim.fineos_absence_id == payment_data1.absence_case_number
        )
    ).all()
    assert len(claims) == 1
    claim = claims[0]
    assert claim.claim_start_date == datetime.date(2022, 12, 19)
    assert claim.claim_end_date == datetime.date(2023, 2, 24)

    expected_records = {}

    leave_request_58290 = (
        test_db_session.query(LeaveRequest)
        .filter(LeaveRequest.fineos_leave_request_id == 58290)
        .one_or_none()
    )
    leave_request_58291 = (
        test_db_session.query(LeaveRequest)
        .filter(LeaveRequest.fineos_leave_request_id == 58291)
        .one_or_none()
    )
    expected_records: dict[str, dict[str, any]] = {
        "PL ABS-479052": dict(
            absence_paid_leave_case_number="PL ABS-479052",
            start_date=datetime.date(2022, 12, 19),
            end_date=datetime.date(2023, 1, 13),
            average_weekly_wage="776.92",
            leave_request_id=leave_request_58290.leave_request_id,
            claim_id=claim.claim_id,
        ),
        "PL ABS-479054": dict(
            absence_paid_leave_case_number="PL ABS-479054",
            start_date=datetime.date(2023, 1, 14),
            end_date=datetime.date(2023, 2, 11),
            average_weekly_wage="876.92",
            leave_request_id=leave_request_58291.leave_request_id,
            claim_id=claim.claim_id,
        ),
        "PL ABS-479055": dict(
            absence_paid_leave_case_number="PL ABS-479055",
            start_date=datetime.date(2023, 2, 12),
            end_date=datetime.date(2023, 2, 24),
            average_weekly_wage=None,  # This is blank because the record was not included in the vpaidleaveinstruction_som extract
            leave_request_id=leave_request_58291.leave_request_id,
            claim_id=claim.claim_id,
        ),
    }
    assert len(absence_paid_leave_cases) == 3
    for absence_paid_leave_case in absence_paid_leave_cases:
        expected = expected_records[absence_paid_leave_case.absence_paid_leave_case_number]
        assert absence_paid_leave_case.start_date == expected["start_date"]
        assert absence_paid_leave_case.end_date == expected["end_date"]
        if absence_paid_leave_case.absence_paid_leave_case_number != "PL ABS-479055":
            assert (
                str(absence_paid_leave_case.average_weekly_wage) == expected["average_weekly_wage"]
            )
        else:  # This is blank because the record was not included in the vpaidleaveinstruction_som extract
            assert absence_paid_leave_case.average_weekly_wage is None
        assert absence_paid_leave_case.leave_request_id == expected["leave_request_id"]
        assert absence_paid_leave_case.claim_id == expected["claim_id"]

    # Verify the leave request is successfully updated
    # If later included in the vpaidleaveinstruction_som extract, the absence paid leave case should be updated
    updated_payment_data3 = FineosPaymentData(
        absence_case_number="NTN-479051-ABS-01",
        notification_number="NTN-479051",
        leave_request_id=58291,
        leave_request_start="2023-01-14 00:00:00",
        leave_request_end="2023-02-24 00:00:00",
        absenceperiod_start="2023-01-14 00:00:00",
        absenceperiod_end="2023-02-24 00:00:00",
        averageweeklywage_monamt="876.92",
        extawwpart2_monamt="999.99",
        paidleave_casenumber="PL ABS-479055",
        period_from_date="2023-02-12 00:00:00",
        period_to_date="2023-02-24 00:00:00",
        benefit_case_number="PL ABS-479055-PL ABS-01",
        include_vbi_leavesummary=False,
        include_v_paidleaveinstruction_som_record=True,
        c_selectedleaveplan=1,
        i_selectedleaveplan=2,
    )
    stage_claimant_extract_data(
        [payment_data2, updated_payment_data3],
        test_db_session,
        mock_payments_s3_config,
        date_of_extract=datetime.date(2023, 7, 22),
    )
    run_decomposed_claimant_extract_steps()

    absence_paid_leave_cases = test_db_session.query(AbsencePaidLeaveCase).all()

    # Should not have created a new absence paid leave case
    assert len(absence_paid_leave_cases) == 3
    # Should have updated the existing absence paid leave case with the new average weekly wage
    # But other data did not change, thus should not have been updated
    for absence_paid_leave_case in absence_paid_leave_cases:
        expected = expected_records[absence_paid_leave_case.absence_paid_leave_case_number]
        assert absence_paid_leave_case.start_date == expected["start_date"]
        assert absence_paid_leave_case.end_date == expected["end_date"]
        if absence_paid_leave_case.absence_paid_leave_case_number == "PL ABS-479055":
            assert str(absence_paid_leave_case.average_weekly_wage) == "999.99"
        else:
            assert (
                str(absence_paid_leave_case.average_weekly_wage) == expected["average_weekly_wage"]
            )
        assert absence_paid_leave_case.leave_request_id == expected["leave_request_id"]
        assert absence_paid_leave_case.claim_id == expected["claim_id"]
