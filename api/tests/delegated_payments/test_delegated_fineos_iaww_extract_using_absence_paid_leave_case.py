import datetime
import decimal

import pytest

import massgov.pfml.delegated_payments.extracts.fineos_extract_util as extract_util
import massgov.pfml.services.wages as wage_service
from massgov.pfml.api.eligibility.benefit_year import get_benefit_year_for_leave_start_date
from massgov.pfml.api.eligibility.benefit_year_dates import calculate_benefit_year_dates
from massgov.pfml.db.lookup_data.absences import AbsenceStatus
from massgov.pfml.db.lookup_data.reference_file_type import ReferenceFileType
from massgov.pfml.db.models.employees import (
    AbsencePaidLeaveCase,
    BenefitYear,
    BenefitYearContribution,
    LeaveRequest,
)
from massgov.pfml.db.models.factories import (
    AbsencePeriodFactory,
    ClaimFactory,
    EmployeeFactory,
    EmployerFactory,
    ImportLogFactory,
    ReferenceFileFactory,
)
from massgov.pfml.db.models.payments import (
    FineosExtractVbiLeavePlanRequestedAbsence,
    FineosExtractVPaidLeaveInstruction,
)
from massgov.pfml.delegated_payments.delegated_fineos_iaww_extract import IAWWExtractStep
from massgov.pfml.delegated_payments.mock.fineos_extract_data import (
    FineosIAWWData,
    FineosPaymentData,
)
from massgov.pfml.delegated_payments.sync_absence_paid_leave_cases_step import (
    SyncAbsencePaidLeaveCasesStep,
)
from massgov.pfml.util.batch.log import LogEntry
from tests.delegated_payments.conftest import stage_claimant_extract_data


# needed to initialize the feature config before code being tested checks feature flags
@pytest.fixture(autouse=True)
def use_initialize_feature_config(initialize_feature_config):
    pass


def stage_data(records, db_session, reference_file=None, import_log=None):
    if not reference_file:
        reference_file = ReferenceFileFactory.create(
            reference_file_type_id=ReferenceFileType.FINEOS_IAWW_EXTRACT.reference_file_type_id
        )
    if not import_log:
        import_log = ImportLogFactory.create()

    for record in records:
        instance = extract_util.create_staging_table_instance(
            record.get_leave_plan_request_absence_record(),
            FineosExtractVbiLeavePlanRequestedAbsence,
            reference_file,
            import_log.import_log_id,
        )
        db_session.add(instance)
        instance = extract_util.create_staging_table_instance(
            record.get_vpaid_leave_instruction_record(),
            FineosExtractVPaidLeaveInstruction,
            reference_file,
            import_log.import_log_id,
        )
        db_session.add(instance)

    db_session.commit()


@pytest.fixture()
def iaww_extract_step(
    initialize_factories_session,
    test_db_session,
):
    step = IAWWExtractStep(db_session=test_db_session, log_entry_db_session=test_db_session)
    step._log_entry = LogEntry(test_db_session, "")
    return step


@pytest.fixture()
def aplc_sync_step(
    initialize_factories_session,
    test_db_session,
):
    step = SyncAbsencePaidLeaveCasesStep(
        db_session=test_db_session, log_entry_db_session=test_db_session
    )
    step._log_entry = LogEntry(test_db_session, "")
    return step


def test_run_overwrite_existing_iaww_data_when_multiple_aplc_override_the_aww_of_benefit_year(
    run_decomposed_claimant_extract_steps,
    aplc_sync_step,
    iaww_extract_step,
    test_db_session,
    set_exporter_env_vars,
    mock_payments_s3_config,
):
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create(
        date_of_birth=datetime.date(1990, 1, 1), fineos_customer_number=12345678
    )
    claim = ClaimFactory.create(employer_id=employer.employer_id, employee_id=employee.employee_id)

    # create absence cases and get the leave request ID so we can generate FINEOS IAWW data
    start_date = "2022-12-19 00:00:00"
    absence_case_1 = AbsencePeriodFactory.create(
        claim=claim, absence_period_start_date=datetime.date(2022, 12, 19)
    )
    leave_request_1 = absence_case_1.fineos_leave_request_id
    absence_case_2 = AbsencePeriodFactory.create()
    leave_request_2 = absence_case_2.fineos_leave_request_id

    claim.fineos_absence_status_id = AbsenceStatus.APPROVED.absence_status_id
    claim.absence_period_start_date = absence_case_1.absence_period_start_date

    # Create two paid leave cases for the same benefit year
    payment_data1 = FineosPaymentData(
        leave_request_id=leave_request_1,
        averageweeklywage_monamt="2000",
        leave_request_start="2022-12-19 00:00:00",
        leave_request_end="2023-01-13 00:00:00",
        absenceperiod_start=start_date,
        absenceperiod_end="2022-12-26 00:00:00",
        extawwpart2_monamt="0",
        notification_number="NTN-479051",
        absence_case_number="NTN-479051-ABS-01",
        paidleave_casenumber="PL ABS-479052",
        period_from_date="2022-12-19 00:00:00",
        period_to_date="2023-01-13 00:00:00",
        benefit_case_number="PL ABS-479052-PL ABS-01",
        employer_customer_num=employer.fineos_employer_id,
        employee=employee,
        employee_customer_number=employee.fineos_customer_number,
        customer_number=employee.fineos_customer_number,
        include_vbi_leavesummary=True,
    )
    payment_data2 = FineosPaymentData(
        leave_request_id=leave_request_2,
        averageweeklywage_monamt="2000",
        leave_request_start="2023-01-14 00:00:00",
        leave_request_end="2023-02-24 00:00:00",
        absenceperiod_start="2023-01-14 00:00:00",
        absenceperiod_end="2023-02-24 00:00:00",
        extawwpart2_monamt="376.92",
        notification_number="NTN-479051",
        absence_case_number="NTN-479051-ABS-01",
        paidleave_casenumber="PL ABS-479054",
        period_from_date="2023-01-14 00:00:00",
        period_to_date="2023-02-11 00:00:00",
        benefit_case_number="PL ABS-479054-PL ABS-01",
        employer_customer_num=employer.fineos_employer_id,
        employee=employee,
        employee_customer_number=employee.fineos_customer_number,
        customer_number=employee.fineos_customer_number,
        include_vbi_leavesummary=True,
    )

    stage_claimant_extract_data(
        [
            payment_data1,
            payment_data2,
        ],
        test_db_session,
        mock_payments_s3_config,
        date_of_extract=datetime.date(2024, 2, 14),
    )
    run_decomposed_claimant_extract_steps()
    aplc_sync_step.run()

    aplc_1 = (
        test_db_session.query(AbsencePaidLeaveCase)
        .filter(
            AbsencePaidLeaveCase.leave_request.has(
                LeaveRequest.fineos_leave_request_id == leave_request_1
            )
        )
        .one()
    )
    aplc_2 = (
        test_db_session.query(AbsencePaidLeaveCase)
        .filter(
            AbsencePaidLeaveCase.leave_request.has(
                LeaveRequest.fineos_leave_request_id == leave_request_2
            )
        )
        .one()
    )
    # These are within the same BY so the IAWW should be the same
    assert aplc_1.average_weekly_wage == decimal.Decimal("2000")
    assert aplc_2.average_weekly_wage == decimal.Decimal("2000")

    # Create a benefit year for the claim and add a contribution for the employer
    # so we can test that the IAWW value is updated for the benefit year
    benefit_year_dates = calculate_benefit_year_dates(claim.absence_period_start_date)
    base_period_qtrs = wage_service.get_base_period_quarters(
        test_db_session, employee.employee_id, benefit_year_dates.start_date
    )
    benefit_year = BenefitYear(
        employee_id=employee.employee_id,
        contributions=[
            BenefitYearContribution(
                employee_id=employee.employee_id,
                employer_id=employer.employer_id,
                # Set the non overridden IAWW
                average_weekly_wage=decimal.Decimal("2000"),
            )
        ],
        start_date=benefit_year_dates.start_date,
        end_date=benefit_year_dates.end_date,
        base_period_start_date=base_period_qtrs[-1].start_date(),
        base_period_end_date=base_period_qtrs[0].as_date(),
    )
    # Both APLC would be in the this benefit year
    test_db_session.add(benefit_year)
    test_db_session.commit()

    assert get_benefit_year_for_leave_start_date(
        test_db_session,
        employee.employee_id,
        claim.absence_period_start_date,
    )
    # change the IAWW for the absence cases and ensure it gets updated with the new value
    payment_data1 = FineosPaymentData(
        leave_request_id=leave_request_1,
        averageweeklywage_monamt="1100",  # change the IAWW, this is the only thing changed
        leave_request_start="2022-12-19 00:00:00",
        leave_request_end="2023-01-13 00:00:00",
        absenceperiod_start="2022-12-19 00:00:00",
        absenceperiod_end="2022-12-26 00:00:00",
        extawwpart2_monamt="0",
        notification_number="NTN-479051",
        absence_case_number="NTN-479051-ABS-01",
        paidleave_casenumber="PL ABS-479052",
        period_from_date="2022-12-19 00:00:00",
        period_to_date="2023-01-13 00:00:00",
        benefit_case_number="PL ABS-479052-PL ABS-01",
        employer_customer_num=employer.fineos_employer_id,
        employee=employee,
        employee_customer_number=employee.fineos_customer_number,
        customer_number=employee.fineos_customer_number,
        include_vbi_leavesummary=True,
    )
    # Because the BY is the same, the IAWW should be the same
    payment_data2 = FineosPaymentData(
        leave_request_id=leave_request_2,
        averageweeklywage_monamt="1100",  # change the IAWW, this is the only thing changed
        leave_request_start="2023-01-14 00:00:00",
        leave_request_end="2023-02-24 00:00:00",
        absenceperiod_start="2023-01-14 00:00:00",
        absenceperiod_end="2023-02-24 00:00:00",
        extawwpart2_monamt="376.92",
        notification_number="NTN-479051",
        absence_case_number="NTN-479051-ABS-01",
        paidleave_casenumber="PL ABS-479054",
        period_from_date="2023-01-14 00:00:00",
        period_to_date="2023-02-11 00:00:00",
        benefit_case_number="PL ABS-479054-PL ABS-01",
        employer_customer_num=employer.fineos_employer_id,
        employee=employee,
        employee_customer_number=employee.fineos_customer_number,
        customer_number=employee.fineos_customer_number,
        include_vbi_leavesummary=True,
    )
    stage_claimant_extract_data(
        [payment_data1, payment_data2],
        test_db_session,
        mock_payments_s3_config,
        date_of_extract=datetime.date(2024, 2, 15),
    )

    run_decomposed_claimant_extract_steps()
    aplc_sync_step.run()

    iaww_data_1 = FineosIAWWData(leave_request_id_value=leave_request_1)
    stage_data([iaww_data_1], test_db_session)
    iaww_extract_step.run()

    test_db_session.refresh(aplc_1)
    test_db_session.refresh(aplc_2)
    # aplc aww should be updated
    assert aplc_1.average_weekly_wage == decimal.Decimal("1100")  # This was overridden
    assert aplc_2.average_weekly_wage == decimal.Decimal("1100")

    for contribution in benefit_year.contributions:
        # refresh the contribution from the database to ensure that data is persisted to the database
        test_db_session.refresh(contribution)
        if contribution.employer_id == claim.employer_id:
            assert contribution.is_aww_overridden
            # IAWW should be updated to overridden value
            assert contribution.average_weekly_wage == decimal.Decimal("1100")


def test_update_benefit_year_aww_for_second_aplc_of_a_leave_request(
    run_decomposed_claimant_extract_steps,
    aplc_sync_step,
    iaww_extract_step,
    test_db_session,
    mock_payments_s3_config,
):
    """
    LeaveRequests have multiple AbsencePaidLeaveCases when the leave request spans multiple benefit years.
    """
    employer = EmployerFactory.create()
    employee = EmployeeFactory.create(
        date_of_birth=datetime.date(1990, 1, 1), fineos_customer_number=12345678
    )
    claim = ClaimFactory.create(employer_id=employer.employer_id, employee_id=employee.employee_id)

    absence_case_1 = AbsencePeriodFactory.create(
        claim=claim, absence_period_start_date=datetime.date(2022, 12, 19)
    )
    leave_request_1 = absence_case_1.fineos_leave_request_id

    claim.fineos_absence_status_id = AbsenceStatus.APPROVED.absence_status_id
    claim.absence_period_start_date = absence_case_1.absence_period_start_date

    # Create a benefit year for the claim and add a contribution for the employer
    # so we can test that the IAWW value is updated for the benefit year
    benefit_year_dates_1 = calculate_benefit_year_dates(datetime.date(2022, 2, 12))
    base_period_qtrs_1 = wage_service.get_base_period_quarters(
        test_db_session, employee.employee_id, benefit_year_dates_1.start_date
    )
    benefit_year_1 = BenefitYear(
        employee_id=employee.employee_id,
        contributions=[
            BenefitYearContribution(
                employee_id=employee.employee_id,
                employer_id=employer.employer_id,
                average_weekly_wage=decimal.Decimal("1111"),
            )
        ],
        start_date=benefit_year_dates_1.start_date,
        end_date=benefit_year_dates_1.end_date,
        base_period_start_date=base_period_qtrs_1[-1].start_date(),
        base_period_end_date=base_period_qtrs_1[0].as_date(),
    )
    test_db_session.add(benefit_year_1)

    benefit_year_dates_2 = calculate_benefit_year_dates(datetime.date(2023, 2, 12))
    base_period_qtrs_2 = wage_service.get_base_period_quarters(
        test_db_session, employee.employee_id, benefit_year_dates_2.start_date
    )

    benefit_year_2 = BenefitYear(
        employee_id=employee.employee_id,
        contributions=[
            BenefitYearContribution(
                employee_id=employee.employee_id,
                employer_id=employer.employer_id,
                average_weekly_wage=decimal.Decimal("1111"),
            )
        ],
        start_date=benefit_year_dates_2.start_date,
        end_date=benefit_year_dates_2.end_date,
        base_period_start_date=base_period_qtrs_2[-1].start_date(),
        base_period_end_date=base_period_qtrs_2[0].as_date(),
    )
    test_db_session.add(benefit_year_2)
    test_db_session.commit()

    payment_data1 = FineosPaymentData(
        leave_request_id=leave_request_1,
        leave_request_start="2023-01-14 00:00:00",
        leave_request_end="2023-02-24 00:00:00",
        absenceperiod_start="2023-01-14 00:00:00",
        absenceperiod_end="2023-02-24 00:00:00",
        averageweeklywage_monamt="1111",
        extawwpart2_monamt="2222",
        notification_number="NTN-479051",
        absence_case_number="NTN-479051-ABS-01",
        paidleave_casenumber="PL ABS-479054",
        period_from_date="2023-01-14 00:00:00",
        period_to_date="2023-02-11 00:00:00",
        benefit_case_number="PL ABS-479054-PL ABS-01",
        include_vbi_leavesummary=True,
        employer_customer_num=employer.fineos_employer_id,
        employee=employee,
        employee_customer_number=employee.fineos_customer_number,
        customer_number=employee.fineos_customer_number,
    )
    payment_data2 = FineosPaymentData(
        absence_case_number="NTN-479051-ABS-01",
        notification_number="NTN-479051",
        leave_request_id=leave_request_1,
        leave_request_start="2023-01-14 00:00:00",
        leave_request_end="2023-02-24 00:00:00",
        absenceperiod_start="2023-01-14 00:00:00",
        absenceperiod_end="2023-02-24 00:00:00",
        averageweeklywage_monamt="1111",
        extawwpart2_monamt="2222",
        paidleave_casenumber="PL ABS-479055",
        period_from_date="2023-02-12 00:00:00",
        period_to_date="2023-02-24 00:00:00",
        benefit_case_number="PL ABS-479055-PL ABS-01",
        include_vbi_leavesummary=False,
        employer_customer_num=employer.fineos_employer_id,
        employee=employee,
        employee_customer_number=employee.fineos_customer_number,
        customer_number=employee.fineos_customer_number,
    )

    stage_claimant_extract_data(
        [
            payment_data1,
            payment_data2,
        ],
        test_db_session,
        mock_payments_s3_config,
    )
    run_decomposed_claimant_extract_steps()
    aplc_sync_step.run()

    iaww_data_1 = FineosIAWWData(leave_request_id_value=leave_request_1)
    stage_data([iaww_data_1], test_db_session)
    iaww_extract_step.run()

    aplc_1 = (
        test_db_session.query(AbsencePaidLeaveCase)
        .filter(
            AbsencePaidLeaveCase.leave_request.has(
                LeaveRequest.fineos_leave_request_id == leave_request_1
            ),
            AbsencePaidLeaveCase.start_date == datetime.date(2023, 1, 14),
        )
        .one()
    )
    aplc_2 = (
        test_db_session.query(AbsencePaidLeaveCase)
        .filter(
            AbsencePaidLeaveCase.leave_request.has(
                LeaveRequest.fineos_leave_request_id == leave_request_1
            ),
            AbsencePaidLeaveCase.start_date == datetime.date(2023, 2, 12),
        )
        .one()
    )
    assert aplc_1.average_weekly_wage == decimal.Decimal("1111")
    assert aplc_2.average_weekly_wage == decimal.Decimal("2222")

    # Benefit year 1 should not have the IAWW overridden
    for contribution in benefit_year_1.contributions:
        # refresh the contribution from the database to ensure that data is persisted to the database
        test_db_session.refresh(contribution)
        if contribution.employer_id == claim.employer_id:
            assert not contribution.is_aww_overridden
            assert contribution.average_weekly_wage == decimal.Decimal("1111")

    # Benefit year 2 should have the IAWW overridden
    for contribution in benefit_year_2.contributions:
        # refresh the contribution from the database to ensure that data is persisted to the database
        test_db_session.refresh(contribution)
        if contribution.employer_id == claim.employer_id:
            assert contribution.is_aww_overridden
            assert contribution.average_weekly_wage == decimal.Decimal("2222")


def test_update_benefit_year_same_day_as_claim(
    run_decomposed_claimant_extract_steps,
    aplc_sync_step,
    iaww_extract_step,
    test_db_session,
    mock_payments_s3_config,
):
    # This test covers a bug found in https://lwd.atlassian.net/browse/PFMLPB-12530
    # where the sync step only updated benefit years on the second run of the step
    # for new claims

    employer = EmployerFactory.create()
    employee = EmployeeFactory.create(
        date_of_birth=datetime.date(1990, 1, 1), fineos_customer_number=12345678
    )
    claim = ClaimFactory.create(employer_id=employer.employer_id, employee_id=employee.employee_id)

    # create absence cases and get the leave request ID so we can generate FINEOS IAWW data
    absence_case_1 = AbsencePeriodFactory.create(
        claim=claim, absence_period_start_date=datetime.date(2022, 12, 19)
    )
    leave_request_1 = absence_case_1.fineos_leave_request_id

    claim.fineos_absence_status_id = AbsenceStatus.APPROVED.absence_status_id
    claim.absence_period_start_date = absence_case_1.absence_period_start_date

    # Create a benefit year for the claim and add a contribution for the employer
    # so we can test that the IAWW value is updated for the benefit year
    benefit_year_dates = calculate_benefit_year_dates(claim.absence_period_start_date)
    base_period_qtrs = wage_service.get_base_period_quarters(
        test_db_session, employee.employee_id, benefit_year_dates.start_date
    )
    benefit_year = BenefitYear(
        employee_id=employee.employee_id,
        contributions=[
            BenefitYearContribution(
                employee_id=employee.employee_id,
                employer_id=employer.employer_id,
                average_weekly_wage=decimal.Decimal("1111"),
            )
        ],
        start_date=benefit_year_dates.start_date,
        end_date=benefit_year_dates.end_date,
        base_period_start_date=base_period_qtrs[-1].start_date(),
        base_period_end_date=base_period_qtrs[0].as_date(),
    )
    test_db_session.add(benefit_year)
    test_db_session.commit()

    assert get_benefit_year_for_leave_start_date(
        test_db_session,
        employee.employee_id,
        claim.absence_period_start_date,
    )
    # change the IAWW for the first absence case and ensure it gets updated with the new value
    payment_data1 = FineosPaymentData(
        leave_request_id=leave_request_1,
        averageweeklywage_monamt="1100",  # change the IAWW, this is the only thing changed
        leave_request_start="2022-12-19 00:00:00",
        leave_request_end="2023-01-13 00:00:00",
        absenceperiod_start="2022-12-19 00:00:00",
        absenceperiod_end="2022-12-26 00:00:00",
        extawwpart2_monamt="0",
        notification_number="NTN-479051",
        absence_case_number="NTN-479051-ABS-01",
        paidleave_casenumber="PL ABS-479052",
        period_from_date="2022-12-19 00:00:00",
        period_to_date="2023-01-13 00:00:00",
        benefit_case_number="PL ABS-479052-PL ABS-01",
        employer_customer_num=employer.fineos_employer_id,
        employee=employee,
        employee_customer_number=employee.fineos_customer_number,
        customer_number=employee.fineos_customer_number,
        include_vbi_leavesummary=True,
    )
    stage_claimant_extract_data([payment_data1], test_db_session, mock_payments_s3_config)

    run_decomposed_claimant_extract_steps()
    aplc_sync_step.run()

    iaww_data_1 = FineosIAWWData(leave_request_id_value=leave_request_1)
    stage_data([iaww_data_1], test_db_session)
    iaww_extract_step.run()

    aplc_1 = (
        test_db_session.query(AbsencePaidLeaveCase)
        .filter(
            AbsencePaidLeaveCase.leave_request.has(
                LeaveRequest.fineos_leave_request_id == leave_request_1
            )
        )
        .one()
    )

    assert aplc_1.average_weekly_wage == decimal.Decimal("1100")

    for contribution in benefit_year.contributions:
        # refresh the contribution from the database to ensure that data is persisted to the database
        test_db_session.refresh(contribution)
        if contribution.employer_id == claim.employer_id:
            assert contribution.is_aww_overridden
            assert contribution.average_weekly_wage == decimal.Decimal("1100")
