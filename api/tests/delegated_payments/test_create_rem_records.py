import os
from decimal import Decimal
from unittest import mock
from xml.dom.minidom import Document, Element

import defusedxml.minidom
import pytest

import massgov.pfml.delegated_payments.delegated_payments_util as payments_util
import massgov.pfml.util.files as file_util
from massgov.pfml.db.lookup_data.employees import PaymentTransactionType
from massgov.pfml.db.lookup_data.payments import MmarsEventStatusType, MmarsEventType
from massgov.pfml.db.models.ctr.batch_identifier import CtrBatchIdentifier
from massgov.pfml.db.models.factories import (
    EmployeeAddressFactory,
    MmarsEventFactory,
    OverpaymentCollectionFactory,
    OverpaymentFactory,
)
from massgov.pfml.db.models.payments import Mmars<PERSON>uditLog, MmarsEvent
from massgov.pfml.delegated_payments.create_rem_records_step import ProcessREMRecordsStep
from massgov.pfml.util.batch.log import LogEntry
from massgov.pfml.util.datetime import get_now_us_eastern


def validate_elements(element, expected_elements):
    """
    Utility method for easily validating the inner most elements of a comptroller document.
    Note that expected elements is a map of: attribute tag -> value
    This method also checks a few other static values
    """

    assert len(element.childNodes) == len(expected_elements)

    for tag, text in expected_elements.items():
        subelements = element.getElementsByTagName(tag)
        assert len(subelements) == 1

        subelement = subelements[0]
        assert subelement.tagName == tag
        validate_attributes(subelement, {"Attribute": "Y"})
        assert len(subelement.childNodes) == 1  # Should just have a CDATA

        cdata = subelement.childNodes[0]
        if isinstance(text, str):
            assert cdata.data == text.upper()
        else:
            assert cdata.data == str(text)


def validate_attributes(element, expected_attributes):
    """Elements have an _attrs map, but its from attribute name to
    and attrs object with a value stored within it.
    """
    assert len(element._attrs) == len(expected_attributes)
    for name, value in expected_attributes.items():
        assert element._attrs[name].value == value


@pytest.fixture
def create_rem_records_step(initialize_factories_session, test_db_session, mock_mmars_s3_config):
    step = ProcessREMRecordsStep(
        db_session=test_db_session,
        log_entry_db_session=test_db_session,
        s3_config=mock_mmars_s3_config,
    )
    step._log_entry = LogEntry(test_db_session, "")
    return step


@mock.patch.object(ProcessREMRecordsStep, "_get_rem_active_records", return_value=[])
@mock.patch.object(ProcessREMRecordsStep, "increment")
def test_run_step_no_records(mock_increment, mock_get_rem_active_records, create_rem_records_step):
    create_rem_records_step.run_step()

    # Ensure it checked for RE records
    mock_get_rem_active_records.assert_called_once()
    # Ensure no records to process, so nothing is incremented for processed
    mock_increment.assert_not_called()


@mock.patch.object(
    ProcessREMRecordsStep, "_get_rem_active_records", return_value=[mock.Mock(spec=MmarsEvent)] * 5
)
@mock.patch.object(ProcessREMRecordsStep, "_build_rem_file")
@mock.patch.object(ProcessREMRecordsStep, "increment")
def test_run_step_with_records(
    mock_increment,
    mock_build_rem_file,
    mock_get_rem_active_records,
    create_rem_records_step,
):
    create_rem_records_step.run_step()

    # # Ensure it fetched RE records
    mock_get_rem_active_records.assert_called_once()
    # Ensure metrics were incremented
    mock_increment.assert_any_call(create_rem_records_step.Metrics.TOTAL_REM_RECORDS, increment=5)


@mock.patch("massgov.pfml.util.files.copy_file", new_callable=mock.mock_open)
@mock.patch("massgov.pfml.util.files.write_file", new_callable=mock.mock_open)
@mock.patch("xml.dom.minidom.Document.toprettyxml", return_value=b"<xml>mocked</xml>")
@mock.patch("massgov.pfml.util.datetime.get_now_us_eastern", return_value=mock.Mock())
@mock.patch.object(ProcessREMRecordsStep, "_build_individual_rem_record")
def test_build_rem_file(
    mock_build_individual_rem_record,
    mock_get_now_us_eastern,
    mock_toprettyxml,
    mock_write_file,
    mock_copy_file,
    create_rem_records_step,
):
    # Mock RE records
    overpayment = OverpaymentFactory(
        overpayment_casenumber="123",
        ctr_doc_id="INTFRE0000000000001",
        payment_transaction_type_id=PaymentTransactionType.OVERPAYMENT.payment_transaction_type_id,
    )
    mmars_event = MmarsEventFactory(
        mmars_event_type_id=MmarsEventType.REM_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.REM_PENDING.mmars_event_status_type_id,
        overpayment=overpayment,
    )
    EmployeeAddressFactory(employee=mmars_event.employee)
    overpayment.outstanding_amount = Decimal(100.0)
    mock_build_individual_rem_record.return_value = Element("mocked_element")

    create_rem_records_step._build_rem_file([mmars_event])

    mock_build_individual_rem_record.assert_called_once()


def test_run_step_with_employee_record(
    initialize_factories_session, create_rem_records_step, test_db_session, mock_mmars_s3_config
):

    overpayment = OverpaymentFactory(
        overpayment_casenumber="123",
        ctr_doc_id="INTFRE0000000000001",
        payment_transaction_type_id=PaymentTransactionType.OVERPAYMENT.payment_transaction_type_id,
    )
    mmars_event = MmarsEventFactory(
        mmars_event_type_id=MmarsEventType.REM_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.REM_PENDING.mmars_event_status_type_id,
        overpayment=overpayment,
    )
    EmployeeAddressFactory(employee=mmars_event.employee)
    expected_ctr_doc_version_number = mmars_event.overpayment.ctr_doc_version_number + 1

    create_rem_records_step.run_step()
    ctr_batch = test_db_session.query(CtrBatchIdentifier).first()
    rem_file_name = ctr_batch.ctr_batch_identifier
    file_path = os.path.join(
        mock_mmars_s3_config.pfml_mmars_file_base_location,
        payments_util.MMARS_Constants.REM_FILE_FOLDER,
        rem_file_name,
    )
    import_log = create_rem_records_step._log_entry.metrics
    rem_file = file_util.list_files(file_path)
    rem_file_path = os.path.join(file_path, rem_file_name + ".dat")
    inf_file_path = os.path.join(file_path, rem_file_name + ".inf")

    assert import_log is not None
    assert import_log["total_rem_records"] == 1
    assert import_log["total_rem_records_processed"] == 1
    assert import_log["rem_file_path"] == rem_file_path
    assert import_log["inf_file_path"] == inf_file_path
    assert overpayment.ctr_doc_id == mmars_event.overpayment.ctr_doc_id
    assert len(rem_file) == 2
    assert (
        mmars_event.mmars_status_type_id
        == MmarsEventStatusType.REM_SUBMITTED.mmars_event_status_type_id
    )
    assert overpayment.ctr_doc_version_number == expected_ctr_doc_version_number

    # Read the file and validate the doc_id
    with open(rem_file_path, "r") as file:
        content = file.read()
        document = defusedxml.minidom.parseString(content)

        ams_document_elements = document.getElementsByTagName("AMS_DOCUMENT")
        ams_document = ams_document_elements[0]
        doc_id = ams_document.getAttribute("DOC_ID")
        assert doc_id == mmars_event.overpayment.ctr_doc_id


def test_build_individual_rem_record(
    initialize_factories_session, create_rem_records_step, test_db_session
):
    now = get_now_us_eastern()
    overpayment = OverpaymentFactory(
        overpayment_casenumber="123",
        ctr_doc_id="INTFRE0000000000001",
        payment_transaction_type_id=PaymentTransactionType.OVERPAYMENT_ADJUSTMENT.payment_transaction_type_id,
        amount=100,
        outstanding_amount=50,
    )
    mmars_event = MmarsEventFactory(overpayment=overpayment)
    employee = mmars_event.employee
    fineos_absence_id = mmars_event.overpayment.claim.fineos_absence_id
    document = create_rem_records_step._build_individual_rem_record(
        Document(),
        mmars_event,
        mmars_event.overpayment.outstanding_amount,
        now,
        mmars_event.overpayment.ctr_doc_id,
        fineos_absence_id,
    )
    doc_id = document._attrs["DOC_ID"].value
    assert doc_id == mmars_event.overpayment.ctr_doc_id
    assert mmars_event.re_amount == mmars_event.overpayment.outstanding_amount

    re_doc_hdr = document.childNodes[0]
    validate_attributes(re_doc_hdr, {"AMSDataObject": "Y"})
    expected_hdr_subelements = {
        "DOC_ID": doc_id,
        "DOC_NM": mmars_event.overpayment.overpayment_casenumber,
        "DOC_VERS_NO": str(mmars_event.overpayment.ctr_doc_version_number),
    }
    expected_hdr_subelements.update(payments_util.MMARS_Constants.rem_generic_attributes.copy())
    validate_elements(re_doc_hdr, expected_hdr_subelements)

    # Validate the RE_DOC_VEND section
    re_doc_vend = document.childNodes[1]
    assert re_doc_vend.tagName == "RE_DOC_VEND"
    validate_attributes(re_doc_vend, {"AMSDataObject": "Y"})
    legal_name = employee.first_name + " " + employee.last_name

    expected_vcust_subelements = {
        "DOC_ID": doc_id,
        "LGL_NM": legal_name,
        "VEND_CUST_CD": employee.ctr_vendor_customer_code,
        "BPRO_CD": payments_util.MMARS_Constants.BPRO_CD,
        "AD_ID": "AD010",  # hard coded value (to be discussed)
        "AR_DEPT_CD": "EOL",
        "AR_UNIT_CD": "ALL",
        "DOC_VEND_LN_NO": "1",
        "DOC_VERS_NO": str(mmars_event.overpayment.ctr_doc_version_number),
    }
    expected_vcust_subelements.update(payments_util.MMARS_Constants.rem_generic_attributes.copy())
    validate_elements(re_doc_vend, expected_vcust_subelements)

    # Validate the RE_DOC_ACTG section
    re_doc_actg = document.childNodes[2]
    assert re_doc_actg.tagName == "RE_DOC_ACTG"
    validate_attributes(re_doc_actg, {"AMSDataObject": "Y"})
    legal_name = employee.first_name + " " + employee.last_name

    financial_year = payments_util.get_financial_year(now)
    per_dc = payments_util.get_month_period(now)

    expected_vcust_subelements = {
        "DOC_ID": doc_id,
        "LN_AM": f"{mmars_event.overpayment.outstanding_amount:.2f}",
        "BFY": financial_year,
        "FY_DC": financial_year,
        "PER_DC": str(per_dc),
        "DEPT_CD": payments_util.MMARS_Constants.COMPTROLLER_DEPT_CODE,
        "UNIT_CD": payments_util.MMARS_Constants.COMPTROLLER_UNIT_CODE,
        "ACTG_LN_DSCR": mmars_event.overpayment.claim.fineos_absence_id,
        "DOC_VERS_NO": str(mmars_event.overpayment.ctr_doc_version_number),
    }
    expected_vcust_subelements.update(payments_util.MMARS_Constants.rem_doc_acctg_attributes.copy())
    expected_vcust_subelements.update(payments_util.MMARS_Constants.rem_generic_attributes.copy())
    validate_elements(re_doc_actg, expected_vcust_subelements)


def test_build_rem_inf(
    initialize_factories_session,
    create_rem_records_step,
    test_db_session,
):
    now = get_now_us_eastern()
    rem_records_count = 2
    total_amount = 145.842
    ctr_batch = CtrBatchIdentifier(
        ctr_batch_identifier="ctr_rem_batch_identifier",
        batch_counter=0,
        batch_date=now.date(),
        year=now.year,
        mmars_event_type_id=MmarsEventType.REM_TRX.mmars_event_type_id,
    )
    inf_data = create_rem_records_step._build_rem_inf(rem_records_count, total_amount, ctr_batch)
    expected_inf_data = {
        "NewMmarsBatchID": ctr_batch.ctr_batch_identifier,
        "NewMmarsBatchDeptCode": payments_util.MMARS_Constants.COMPTROLLER_DEPT_CODE,
        "NewMmarsUnitCode": payments_util.MMARS_Constants.COMPTROLLER_UNIT_CODE,
        "NewMmarsImportDate": now.strftime("%Y-%m-%d"),
        "NewMmarsTransCode": "RE",
        "NewMmarsTableName": "",
        "NewMmarsTransCount": str(rem_records_count),
        "NewMmarsTransDollarAmount": "145.84",  # Decimal is rounded to 2 decimal places
    }
    assert inf_data == expected_inf_data


# In this scenario, we expect the process to skip duplicate REM (REM with the same overpayment_id)
def test_skip_duplicate_rem_records(
    initialize_factories_session, create_rem_records_step, test_db_session, mock_mmars_s3_config
):
    overpayment = OverpaymentFactory(
        overpayment_casenumber="123",
        ctr_doc_id="INTFRE0000000000001",
        payment_transaction_type_id=PaymentTransactionType.OVERPAYMENT.payment_transaction_type_id,
    )
    mmars_event = MmarsEventFactory(
        mmars_event_type_id=MmarsEventType.REM_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.REM_PENDING.mmars_event_status_type_id,
        overpayment=overpayment,
    )

    mmars_event = MmarsEventFactory(
        mmars_event_type_id=MmarsEventType.REM_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.REM_PENDING.mmars_event_status_type_id,
        overpayment=overpayment,
    )

    EmployeeAddressFactory(employee=mmars_event.employee)

    create_rem_records_step.run_step()
    ctr_batch = test_db_session.query(CtrBatchIdentifier).first()
    rem_file_name = ctr_batch.ctr_batch_identifier
    file_path = os.path.join(
        mock_mmars_s3_config.pfml_mmars_file_base_location,
        payments_util.MMARS_Constants.REM_FILE_FOLDER,
        rem_file_name,
    )
    import_log = create_rem_records_step._log_entry.metrics
    rem_file = file_util.list_files(file_path)
    rem_file_path = os.path.join(file_path, rem_file_name + ".dat")
    inf_file_path = os.path.join(file_path, rem_file_name + ".inf")

    assert import_log is not None
    assert import_log["total_rem_records"] == 2
    assert import_log["total_rem_records_processed"] == 1
    # Only one record processed, the other is a duplicate
    assert import_log["total_duplicate_rem_records"] == 1
    assert import_log["rem_file_path"] == rem_file_path
    assert import_log["inf_file_path"] == inf_file_path
    assert overpayment.ctr_doc_id == mmars_event.overpayment.ctr_doc_id
    assert len(rem_file) == 2
    assert (
        mmars_event.mmars_status_type_id
        == MmarsEventStatusType.REM_SUBMITTED.mmars_event_status_type_id
    )

    # Read the file and validate the doc_id
    with open(rem_file_path, "r") as file:
        content = file.read()
        document = defusedxml.minidom.parseString(content)

        ams_document_elements = document.getElementsByTagName("AMS_DOCUMENT")
        ams_document = ams_document_elements[0]
        doc_id = ams_document.getAttribute("DOC_ID")
        assert doc_id == mmars_event.overpayment.ctr_doc_id
        assert len(ams_document_elements) == 1

    mmars_audit_logs = (
        test_db_session.query(MmarsAuditLog)
        .join(MmarsEvent, MmarsAuditLog.mmars_event_id == MmarsEvent.mmars_event_id)
        .filter(MmarsEvent.overpayment_id == overpayment.overpayment_id)
        .all()
    )

    assert len(mmars_audit_logs) == 2


@mock.patch.object(ProcessREMRecordsStep, "_build_individual_rem_record")
def test_rem_with_overpayment_collection(
    mock_build_individual_rem_record,
    initialize_factories_session,
    create_rem_records_step,
    test_db_session,
):
    # create rem
    overpayment = OverpaymentFactory(
        overpayment_casenumber="123",
        ctr_doc_id="INTFRE0000000000001",
        payment_transaction_type_id=PaymentTransactionType.OVERPAYMENT.payment_transaction_type_id,
        outstanding_amount=-250,
    )
    mmars_event = MmarsEventFactory(
        mmars_event_type_id=MmarsEventType.REM_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.REM_PENDING.mmars_event_status_type_id,
        overpayment=overpayment,
    )

    EmployeeAddressFactory(employee=mmars_event.employee)

    OverpaymentCollectionFactory(overpayment=overpayment, overpayment_collection_amount=100)

    mock_build_individual_rem_record.return_value = Element("mocked_element")
    create_rem_records_step.run_step()

    calls = mock_build_individual_rem_record.call_args_list
    args, _ = calls[0]

    rem_line_amount_parameter_index = 2

    amount = args[rem_line_amount_parameter_index]

    assert amount == 350


@mock.patch.object(ProcessREMRecordsStep, "_build_individual_rem_record")
def test_rem_with_multiple_overpayment_collection(
    mock_build_individual_rem_record,
    initialize_factories_session,
    create_rem_records_step,
    test_db_session,
):
    # create rem
    overpayment_1 = OverpaymentFactory(
        overpayment_casenumber="111",
        ctr_doc_id="INTFRE0000000000001",
        payment_transaction_type_id=PaymentTransactionType.OVERPAYMENT.payment_transaction_type_id,
        outstanding_amount=-250,
    )
    MmarsEventFactory(
        mmars_event_type_id=MmarsEventType.REM_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.REM_PENDING.mmars_event_status_type_id,
        overpayment=overpayment_1,
    )

    OverpaymentCollectionFactory(overpayment=overpayment_1, overpayment_collection_amount=100)
    OverpaymentCollectionFactory(overpayment=overpayment_1, overpayment_collection_amount=20)

    overpayment_2 = OverpaymentFactory(
        overpayment_casenumber="222",
        ctr_doc_id="INTFRE0000000000002",
        payment_transaction_type_id=PaymentTransactionType.OVERPAYMENT.payment_transaction_type_id,
        outstanding_amount=-100,
    )
    MmarsEventFactory(
        mmars_event_type_id=MmarsEventType.REM_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.REM_PENDING.mmars_event_status_type_id,
        overpayment=overpayment_2,
    )

    overpayment_3 = OverpaymentFactory(
        overpayment_casenumber="333",
        ctr_doc_id="INTFRE0000000000003",
        payment_transaction_type_id=PaymentTransactionType.OVERPAYMENT.payment_transaction_type_id,
        outstanding_amount=-1000,
    )
    MmarsEventFactory(
        mmars_event_type_id=MmarsEventType.REM_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.REM_PENDING.mmars_event_status_type_id,
        overpayment=overpayment_3,
    )

    OverpaymentCollectionFactory(overpayment=overpayment_3, overpayment_collection_amount=250)

    mock_build_individual_rem_record.return_value = Element("mocked_element")
    create_rem_records_step.run_step()

    rem_line_amount_parameter_index = 2
    ctr_doc_id_parameter_index = 4

    calls = mock_build_individual_rem_record.call_args_list
    line_amount_dict = {
        call[0][ctr_doc_id_parameter_index]: call[0][rem_line_amount_parameter_index]
        for call in calls
    }

    assert (
        line_amount_dict.get("INTFRE0000000000001") == 370
    )  # overpayment collection is attached (250 mmars event + 100 collection + 20 collection)

    assert (
        line_amount_dict.get("INTFRE0000000000002") == 100
    )  # no overpayment collection is attached

    assert (
        line_amount_dict.get("INTFRE0000000000003") == 1250
    )  # overpayment collection is attached (1000 mmars event + 250 collection)


@mock.patch.object(ProcessREMRecordsStep, "_build_individual_rem_record")
def test_rem_no_overpayment_collection(
    mock_build_individual_rem_record,
    initialize_factories_session,
    create_rem_records_step,
    test_db_session,
):
    # create rem
    overpayment = OverpaymentFactory(
        overpayment_casenumber="123",
        ctr_doc_id="INTFRE0000000000001",
        payment_transaction_type_id=PaymentTransactionType.OVERPAYMENT.payment_transaction_type_id,
        outstanding_amount=-250,
    )
    MmarsEventFactory(
        mmars_event_type_id=MmarsEventType.REM_TRX.mmars_event_type_id,
        mmars_status_type_id=MmarsEventStatusType.REM_PENDING.mmars_event_status_type_id,
        overpayment=overpayment,
    )

    mock_build_individual_rem_record.return_value = Element("mocked_element")
    create_rem_records_step.run_step()

    calls = mock_build_individual_rem_record.call_args_list
    args, _ = calls[0]

    rem_line_amount_parameter_index = 2

    amount = args[rem_line_amount_parameter_index]

    assert amount == 250
