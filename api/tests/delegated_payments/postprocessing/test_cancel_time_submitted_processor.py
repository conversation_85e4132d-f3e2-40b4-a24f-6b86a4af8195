from datetime import date, timedelta

import pytest

from massgov.pfml.db.models.factories import (
    ClaimFactory,
    FineosExtractVbiTaskReportDeltaSomFactory,
    FineosExtractVbiTaskReportSomFactory,
)
from massgov.pfml.db.models.payments import PaymentIssueResolution
from massgov.pfml.delegated_payments.audit.audit_writeback_util import AuditReportAction
from massgov.pfml.delegated_payments.mock.delegated_payments_factory import DelegatedPaymentFactory
from massgov.pfml.delegated_payments.postprocessing.cancel_time_submitted_processor import (
    CancelTimeSubmittedProcessor,
)
from massgov.pfml.fineos.tasks import Constants


@pytest.fixture
def cancel_time_submitted_processor(empty_test_step):
    return CancelTimeSubmittedProcessor(empty_test_step)


def test_with_cancel_time_submitted(
    test_db_session,
    initialize_factories_session,
    cancel_time_submitted_processor,
    monkeypatch,
):
    claim_start_date = date(2022, 1, 1)
    claim = ClaimFactory.create(claim_start_date=claim_start_date)
    delegated_payment = DelegatedPaymentFactory(
        test_db_session, claim=claim, period_start_date=claim_start_date + timedelta(7)
    )
    payment = delegated_payment.get_or_create_payment()
    assert payment
    FineosExtractVbiTaskReportSomFactory.create(
        casenumber=claim.fineos_absence_id,
        status=Constants.VBI_TASK_REPORT_STATUS_OPEN,
        tasktypename="Review and Decision Cancel Time Submitted",
    )
    FineosExtractVbiTaskReportDeltaSomFactory.create(
        casenumber=claim.fineos_absence_id,
        status=Constants.VBI_TASK_REPORT_STATUS_OPEN,
        tasktypename="Review and Decision Cancel Time Submitted",
    )
    result = cancel_time_submitted_processor.process(payment)

    assert result
    assert result.message == "Cancel time submitted"
    assert result.audit_report_reject_notes == "Cancel time submitted"
    assert result.audit_report_action == AuditReportAction.REJECTED

    payment_issue_resolutions = test_db_session.query(PaymentIssueResolution).all()
    assert len(payment_issue_resolutions) == 0


def test_without_cancel_time_submitted(
    test_db_session,
    initialize_factories_session,
    cancel_time_submitted_processor,
    monkeypatch,
):
    claim_start_date = date(2022, 1, 1)
    claim = ClaimFactory.create(claim_start_date=claim_start_date)
    delegated_payment = DelegatedPaymentFactory(
        test_db_session, claim=claim, period_start_date=claim_start_date + timedelta(7)
    )
    payment = delegated_payment.get_or_create_payment()
    FineosExtractVbiTaskReportSomFactory.create(casenumber=claim.fineos_absence_id)
    FineosExtractVbiTaskReportDeltaSomFactory.create(casenumber=claim.fineos_absence_id)

    result = cancel_time_submitted_processor.process(payment)
    assert not result

    payment_issue_resolutions = test_db_session.query(PaymentIssueResolution).all()
    assert len(payment_issue_resolutions) == 0
