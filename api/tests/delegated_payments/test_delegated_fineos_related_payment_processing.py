import datetime
from functools import reduce
from typing import List, Optional

import pytest

from massgov.pfml.db.lookup_data.employees import PaymentTransactionType
from massgov.pfml.db.lookup_data.state import State
from massgov.pfml.db.models.employees import Payment
from massgov.pfml.db.models.factories import (
    ClaimFactory,
    EmployeeFactory,
    ImportLogFactory,
    PaymentDetailsFactory,
    PaymentFactory,
    PaymentLineFactory,
)
from massgov.pfml.delegated_payments.delegated_fineos_related_payment_processing import (
    RelatedPaymentsProcessingStep,
)
from tests.helpers.logging import assert_log_has_match
from tests.helpers.sql_snapshot import assert_queries_are_equal


@pytest.fixture
def related_payment_step_with_enable_payment_line_matching(test_db_session, monkeypatch):
    related_payment_step = RelatedPaymentsProcessingStep(
        db_session=test_db_session, log_entry_db_session=test_db_session
    )
    related_payment_step.config.enable_payment_line_matching = True

    return related_payment_step


@pytest.fixture
def related_payment_step(test_db_session):
    return RelatedPaymentsProcessingStep(
        db_session=test_db_session, log_entry_db_session=test_db_session
    )


def test_related_payments_processing_step_previous_extract_query(
    initialize_factories_session, related_payment_step, snapshot_file
):
    payment = PaymentFactory.build(
        fineos_extract_import_log_id=1000,
    )
    qry = related_payment_step._previous_extract_query(payment)
    assert_queries_are_equal(
        qry,
        snapshot_file,
    )


def test_related_payments_processing_step_related_standard_payments_query(
    initialize_factories_session, related_payment_step, snapshot_file
):
    payment = PaymentFactory.build(
        fineos_extract_import_log_id=1000,
        claim=ClaimFactory.build(
            claim_id="00000000-0000-0000-0000-000000000000",
        ),
        period_start_date=datetime.datetime(2021, 1, 13),
        period_end_date=datetime.datetime(2021, 1, 26),
        amount=100,
        payment_transaction_type_id=1,
    )
    qry = related_payment_step._related_standard_payments_query(payment)
    assert_queries_are_equal(
        qry,
        snapshot_file,
    )


class RelatedPaymentDetailsFactory:
    def __init__(
        self,
        parent_payment: Payment,
        child_payments: List[Payment],
    ):
        self.parent_payment = parent_payment
        self.parent_details = PaymentDetailsFactory(
            payment=parent_payment,
            period_start_date=parent_payment.period_start_date,
            period_end_date=parent_payment.period_end_date,
            amount=parent_payment.amount,
        )

        gross_entitlement = parent_payment.amount

        for child_payment in child_payments:
            self.gen_payment_lines(child_payment)
            gross_entitlement += child_payment.amount

        PaymentLineFactory(
            payment=parent_payment,
            payment_details=self.parent_details,
            line_type="Auto Gross Entitlement",
            amount=gross_entitlement,
        )

    def gen_payment_lines(self, child_payment):
        line_type = self.child_line_type(child_payment)
        child_details = PaymentDetailsFactory(
            payment=child_payment,
            period_start_date=child_payment.period_start_date,
            period_end_date=child_payment.period_end_date,
            amount=child_payment.amount,
        )
        PaymentLineFactory(
            payment=child_payment,
            payment_details=child_details,
            line_type=line_type,
            amount=child_payment.amount,
        )
        PaymentLineFactory(
            payment=self.parent_payment,
            payment_details=self.parent_details,
            line_type=line_type,
            amount=-child_payment.amount,
        )

    def child_line_type(self, payment):
        return {
            PaymentTransactionType.STATE_TAX_WITHHOLDING.payment_transaction_type_id: "State Income Tax",
            PaymentTransactionType.FEDERAL_TAX_WITHHOLDING.payment_transaction_type_id: "FIT Amount",
            PaymentTransactionType.EMPLOYER_REIMBURSEMENT.payment_transaction_type_id: "Employer Reimbursement",
            PaymentTransactionType.CHILD_SUPPORT_PAYMENT.payment_transaction_type_id: "Child Support",
        }[payment.payment_transaction_type_id]


def merge(*args, **kwargs):
    return reduce(lambda x, y: {**(x or {}), **(y or {})}, [kwargs] + list(args))


class RelatedPaymentFactory:
    def __init__(
        self,
        employee: Optional[dict] = None,
        primary_extract: Optional[dict] = None,
        primary_payment: Optional[dict] = None,
        primary_state_log: Optional[dict] = None,
        related_extract: Optional[dict] = None,
        federal_related_payment: Optional[dict] = None,
        federal_state_log: Optional[dict] = None,
        state_related_payment: Optional[dict] = None,
        state_state_log: Optional[dict] = None,
        employer_related_payment: Optional[dict] = None,
        employer_state_log: Optional[dict] = None,
        child_support_related_payment: Optional[dict] = None,
        child_support_state_log: Optional[dict] = None,
    ):
        self.employee = EmployeeFactory(
            **(employee or {}),
        )
        self.primary_extract = ImportLogFactory(
            **merge(
                primary_extract,
                source="PaymentExtractStep",
                status="success",
            )
        )
        self.primary_payment = PaymentFactory.with_latest_state_log(
            state_log=merge(
                primary_state_log,
                end_state_id=State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_AUDIT_REPORT.state_id,
            ),
            **merge(
                primary_payment,
                fineos_extract_import_log_id=self.primary_extract.import_log_id,
                employee=self.employee,
                payment_transaction_type_id=PaymentTransactionType.STANDARD.payment_transaction_type_id,
            ),
        )
        self.federal_related_payment = None
        self.state_related_payment = None
        self.employer_related_payment = None
        self.child_support_related_payment = None

        self.related_extract = (
            ImportLogFactory(
                **merge(
                    related_extract,
                    source="PaymentExtractStep",
                    status="success",
                )
            )
            if related_extract
            else self.primary_extract
        )
        base_related_payment = dict(
            employee=self.employee,
            fineos_extract_import_log_id=self.related_extract.import_log_id,
            claim=self.primary_payment.claim,
            period_start_date=self.primary_payment.period_start_date,
            period_end_date=self.primary_payment.period_end_date,
        )
        if federal_state_log:
            self.federal_related_payment = PaymentFactory.with_latest_state_log(
                state_log=federal_state_log,
                **merge(
                    federal_related_payment,
                    base_related_payment,
                    payment_transaction_type_id=PaymentTransactionType.FEDERAL_TAX_WITHHOLDING.payment_transaction_type_id,
                ),
            )
        if state_state_log:
            self.state_related_payment = PaymentFactory.with_latest_state_log(
                state_log=state_state_log,
                **merge(
                    state_related_payment,
                    base_related_payment,
                    payment_transaction_type_id=PaymentTransactionType.STATE_TAX_WITHHOLDING.payment_transaction_type_id,
                ),
            )
        if employer_state_log:
            self.employer_related_payment = PaymentFactory.with_latest_state_log(
                state_log=employer_state_log,
                **merge(
                    employer_related_payment,
                    base_related_payment,
                    payment_transaction_type_id=PaymentTransactionType.EMPLOYER_REIMBURSEMENT.payment_transaction_type_id,
                ),
            )
        if child_support_state_log:
            self.child_support_related_payment = PaymentFactory.with_latest_state_log(
                state_log=child_support_state_log,
                **merge(
                    child_support_related_payment,
                    base_related_payment,
                    payment_transaction_type_id=PaymentTransactionType.CHILD_SUPPORT_PAYMENT.payment_transaction_type_id,
                ),
            )
        RelatedPaymentDetailsFactory(
            parent_payment=self.primary_payment,
            child_payments=[
                p
                for p in [
                    self.federal_related_payment,
                    self.state_related_payment,
                    self.employer_related_payment,
                    self.child_support_related_payment,
                ]
                if p is not None
            ],
        )


def test_related_payments_ready_for_processing_step(
    initialize_factories_session, related_payment_step, caplog
):
    caplog.set_level("INFO")
    related_payments = RelatedPaymentFactory(
        primary_state_log=dict(
            end_state_id=State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_AUDIT_REPORT.state_id,
        ),
        state_state_log=dict(
            end_state_id=State.STATE_WITHHOLDING_READY_FOR_PROCESSING.state_id,
        ),
        federal_state_log=dict(
            end_state_id=State.FEDERAL_WITHHOLDING_READY_FOR_PROCESSING.state_id,
        ),
        employer_state_log=dict(
            end_state_id=State.EMPLOYER_REIMBURSEMENT_READY_FOR_PROCESSING.state_id,
        ),
        child_support_state_log=dict(
            end_state_id=State.CHILD_SUPPORT_READY_FOR_PROCESSING.state_id,
        ),
    )

    related_payment_step.run()

    metrics = related_payment_step.get_log_entry().metrics
    assert metrics["standard_payment_record_count"] == 1
    assert metrics["federal_withholding_record_count"] == 1
    assert metrics["state_withholding_record_count"] == 1
    assert metrics["employer_reimbursement_record_count"] == 1
    assert metrics["child_support_record_count"] == 1

    assert_log_has_match(
        caplog,
        "payments list matched",
        {
            "count": 1,
            "related_payment_id": related_payments.federal_related_payment.payment_id,
            "standard_payments": [related_payments.primary_payment.payment_id],
        },
    )
    assert_log_has_match(
        caplog,
        "payments list matched",
        {
            "count": 1,
            "related_payment_id": related_payments.state_related_payment.payment_id,
            "standard_payments": [related_payments.primary_payment.payment_id],
        },
    )
    assert_log_has_match(
        caplog,
        "payments list matched",
        {
            "count": 1,
            "related_payment_id": related_payments.employer_related_payment.payment_id,
            "standard_payments": [related_payments.primary_payment.payment_id],
        },
    )
    assert_log_has_match(
        caplog,
        "payments list matched",
        {
            "count": 1,
            "related_payment_id": related_payments.child_support_related_payment.payment_id,
            "standard_payments": [related_payments.primary_payment.payment_id],
        },
    )


def test_related_payments_processing_step_with_orphans(
    initialize_factories_session, related_payment_step, caplog
):
    caplog.set_level("INFO")
    yesterdays_import = ImportLogFactory.build(
        import_log_id=1000,
    )
    todays_import = ImportLogFactory.build(
        import_log_id=1001,
    )
    related_payments = RelatedPaymentFactory(
        primary_extract=dict(
            import_log_id=yesterdays_import.import_log_id,
        ),
        related_extract=dict(
            import_log_id=todays_import.import_log_id,
        ),
        primary_state_log=dict(
            end_state_id=State.DELEGATED_PAYMENT_COMPLETE.state_id,
        ),
        state_state_log=dict(
            end_state_id=State.STATE_WITHHOLDING_READY_FOR_PROCESSING.state_id,
        ),
        federal_state_log=dict(
            end_state_id=State.FEDERAL_WITHHOLDING_READY_FOR_PROCESSING.state_id,
        ),
        employer_state_log=dict(
            end_state_id=State.EMPLOYER_REIMBURSEMENT_READY_FOR_PROCESSING.state_id,
        ),
        child_support_state_log=dict(
            end_state_id=State.CHILD_SUPPORT_READY_FOR_PROCESSING.state_id,
        ),
    )

    related_payment_step.run()

    metrics = related_payment_step.get_log_entry().metrics
    assert metrics["federal_withholding_record_count"] == 1
    assert metrics["state_withholding_record_count"] == 1
    assert metrics["employer_reimbursement_record_count"] == 1
    assert metrics["child_support_record_count"] == 1
    assert metrics["orphaned_payment_no_parents"] == 4
    assert metrics["matched_orphaned_payments_yesterdays_parents"] == 4

    assert_log_has_match(
        caplog,
        "Orphan Payment matched one record",
        {
            "orphan_payment_id": related_payments.federal_related_payment.payment_id,
            "parent_payment_id": related_payments.primary_payment.payment_id,
            "parent_payment_state.end_state_id": State.DELEGATED_PAYMENT_COMPLETE.state_id,
            "parent_payment_state.state_description": State.DELEGATED_PAYMENT_COMPLETE.state_description,
        },
    )
    assert_log_has_match(
        caplog,
        "Orphan Payment matched one record",
        {
            "orphan_payment_id": related_payments.state_related_payment.payment_id,
            "parent_payment_id": related_payments.primary_payment.payment_id,
            "parent_payment_state.end_state_id": State.DELEGATED_PAYMENT_COMPLETE.state_id,
            "parent_payment_state.state_description": State.DELEGATED_PAYMENT_COMPLETE.state_description,
        },
    )
    assert_log_has_match(
        caplog,
        "Orphan Payment matched one record",
        {
            "orphan_payment_id": related_payments.employer_related_payment.payment_id,
            "parent_payment_id": related_payments.primary_payment.payment_id,
            "parent_payment_state.end_state_id": State.DELEGATED_PAYMENT_COMPLETE.state_id,
            "parent_payment_state.state_description": State.DELEGATED_PAYMENT_COMPLETE.state_description,
        },
    )
    assert_log_has_match(
        caplog,
        "Orphan Payment matched one record",
        {
            "orphan_payment_id": related_payments.child_support_related_payment.payment_id,
            "parent_payment_id": related_payments.primary_payment.payment_id,
            "parent_payment_state.end_state_id": State.DELEGATED_PAYMENT_COMPLETE.state_id,
            "parent_payment_state.state_description": State.DELEGATED_PAYMENT_COMPLETE.state_description,
        },
    )


def test_related_payments_processing_step_orphans_sent_audit(
    initialize_factories_session, related_payment_step, test_db_session
):
    yesterdays_import = ImportLogFactory.build(
        import_log_id=1000,
    )
    todays_import = ImportLogFactory.build(
        import_log_id=1001,
    )
    related_payments = RelatedPaymentFactory(
        primary_extract=dict(
            import_log_id=yesterdays_import.import_log_id,
        ),
        related_extract=dict(
            import_log_id=todays_import.import_log_id,
        ),
        primary_state_log=dict(
            end_state_id=State.DELEGATED_PAYMENT_COMPLETE.state_id,
        ),
        state_state_log=dict(
            end_state_id=State.STATE_WITHHOLDING_READY_FOR_PROCESSING.state_id,
        ),
    )

    related_payment_step.run()

    test_db_session.refresh(related_payments.state_related_payment)

    assert (
        related_payments.state_related_payment.latest_state_log.state_log.end_state_id
        == State.STATE_WITHHOLDING_ORPHANED_PENDING_AUDIT.state_id
    )


def test_related_payments_processing_step_orphans_previously_processed_state(
    initialize_factories_session,
    related_payment_step_with_enable_payment_line_matching,
    test_db_session,
    caplog,
):

    caplog.set_level("INFO")
    yesterdays_import = ImportLogFactory.build(
        import_log_id=1000,
    )
    todays_import = ImportLogFactory.build(
        import_log_id=1001,
    )
    related_payments_paid = RelatedPaymentFactory(
        primary_extract=dict(
            import_log_id=yesterdays_import.import_log_id,
        ),
        related_extract=dict(
            import_log_id=todays_import.import_log_id,
        ),
        primary_state_log=dict(
            end_state_id=State.DELEGATED_PAYMENT_COMPLETE.state_id,
        ),
        state_state_log=dict(
            end_state_id=State.STATE_WITHHOLDING_READY_FOR_PROCESSING.state_id,
        ),
    )
    related_payments_rejected = RelatedPaymentFactory(
        primary_extract=dict(
            import_log_id=yesterdays_import.import_log_id,
        ),
        related_extract=dict(
            import_log_id=todays_import.import_log_id,
        ),
        primary_state_log=dict(
            end_state_id=State.DELEGATED_PAYMENT_ADD_TO_PAYMENT_REJECT_REPORT.state_id,
        ),
        state_state_log=dict(
            end_state_id=State.STATE_WITHHOLDING_READY_FOR_PROCESSING.state_id,
        ),
    )

    related_payment_step_with_enable_payment_line_matching.run()

    test_db_session.refresh(related_payments_paid.state_related_payment)

    assert (
        related_payments_paid.state_related_payment.latest_state_log.state_log.end_state_id
        == State.MAIN_PAYMENT_PREVIOUSLY_PAID.state_id
    )

    test_db_session.refresh(related_payments_rejected.state_related_payment)

    assert (
        related_payments_rejected.state_related_payment.latest_state_log.state_log.end_state_id
        == State.MAIN_PAYMENT_PREVIOUSLY_REJECTED.state_id
    )
