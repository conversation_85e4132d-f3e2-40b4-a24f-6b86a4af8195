import datetime
from unittest import mock

import pytest

import massgov.pfml.delegated_payments.extracts.fineos_extract_config as fineos_extract_config
import massgov.pfml.delegated_payments.extracts.fineos_extract_util as extract_util
from massgov.pfml.db.lookup_data.absences import (
    AbsencePeriodType,
    AbsenceReason,
    AbsenceReasonQualifierOne,
    AbsenceReasonQualifierTwo,
)
from massgov.pfml.db.lookup_data.employees import LeaveRequestDecision
from massgov.pfml.db.lookup_data.reference_file_type import ReferenceFileType
from massgov.pfml.db.models.absences import AbsencePeriod
from massgov.pfml.db.models.factories import ClaimFactory, LeaveRequestFactory
from massgov.pfml.db.models.payments import (
    FineosExtractVbiRequestedAbsence,
    FineosExtractVbiRequestedAbsenceSom,
)
from massgov.pfml.db.models.reference_file.reference_file import ReferenceFile
from massgov.pfml.db.queries.absence_periods import FINEOS_ABSENCE_PERIOD_ID_CLASS_ID
from massgov.pfml.delegated_payments.mock.fineos_extract_data import FineosPaymentData
from massgov.pfml.delegated_payments.sync_absence_periods_step import SyncAbsencePeriodsStep
from tests.delegated_payments.conftest import (
    add_db_records_from_fineos_data,
    stage_claimant_extract_data,
)


# needed to initialize the feature config before code being tested checks feature flags
@pytest.fixture(autouse=True)
def use_initialize_feature_config(initialize_feature_config):
    pass


@pytest.fixture(autouse=True)
def setup_and_teardown():
    # Setup code: runs before each test

    # TODO remove with feature flag (https://lwd.atlassian.net/browse/PFMLPB-22515)
    # We dynamically remove "ABSENCEPERIOD_ID" from the config's field_names (in fineos_extract_step)
    # when on a pre-v24 version of FINEOS (since that column isn't present in the extracts for that version).
    # This works fine, but in testing that change to the config's field_names persists beyond a test
    # so this teardown code ensures the column is added back in so that other tests have it
    field_names = fineos_extract_config.FineosExtractConstants.VBI_REQUESTED_ABSENCE_SOM.field_names
    absence_period_id_column = "ABSENCEPERIOD_ID"
    if absence_period_id_column not in field_names:
        field_names.append(absence_period_id_column)

    yield
    # Teardown code: runs after each test


def format_claimant_data() -> FineosPaymentData:
    return FineosPaymentData(
        absence_case_number="NTN-001-ABS-01",
        notification_number="NTN-001",
        absence_case_status="Adjudication",
        leave_request_start="2021-02-14",
        leave_request_end="2021-02-28",
        claim_type="Family",
        leave_request_evidence="Satisfied",
        customer_number="12345",
        ssn="*********",
        date_of_birth="1967-04-27",
        payment_method="Elec Funds Transfer",
        routing_nbr="*********",
        account_nbr="*********",
        account_type="Checking",
    )


def test_absence_period_deduplication(
    caplog,
    initialize_factories_session,
    test_db_session,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
    claimant_extract_reference_file,
    mock_payments_s3_config,
):
    claimant_data = FineosPaymentData(absence_period_i_value="1234")
    extract_util.create_staging_table_instance(
        claimant_data.get_requested_absence_som_record(),
        FineosExtractVbiRequestedAbsenceSom,
        claimant_extract_reference_file,
        None,
    )
    extract_util.create_staging_table_instance(
        claimant_data.get_requested_absence_record(),
        FineosExtractVbiRequestedAbsence,
        claimant_extract_reference_file,
        None,
    )

    # Add an exact duplicate of the first absence record
    extract_util.create_staging_table_instance(
        claimant_data.get_requested_absence_som_record(),
        FineosExtractVbiRequestedAbsenceSom,
        claimant_extract_reference_file,
        None,
    )
    # Create a record with a different I value so it doesn't get deduped
    different_payment_data = FineosPaymentData(absence_period_i_value="5678")
    extract_util.create_staging_table_instance(
        different_payment_data.get_requested_absence_som_record(),
        FineosExtractVbiRequestedAbsenceSom,
        claimant_extract_reference_file,
        None,
    )
    extract_util.create_staging_table_instance(
        different_payment_data.get_requested_absence_record(),
        FineosExtractVbiRequestedAbsence,
        claimant_extract_reference_file,
        None,
    )

    claim_1 = ClaimFactory.create(fineos_absence_id=claimant_data.absence_case_number)
    claim_2 = ClaimFactory.create(fineos_absence_id=different_payment_data.absence_case_number)
    stage_claimant_extract_data(
        [claimant_data, different_payment_data], test_db_session, mock_payments_s3_config
    )

    step = SyncAbsencePeriodsStep(db_session=test_db_session, log_entry_db_session=test_db_session)
    step.run()

    # Despite passing in 3 requested absences, one gets deduped away (the 2nd)
    # And only 2 are created
    assert len(claim_1.absence_periods + claim_2.absence_periods) == 2


@pytest.mark.parametrize(
    "db_c_val, db_i_val, db_p_val, feed_c_val, feed_i_val, feed_p_val, expected_c_val, expected_i_val, expected_p_val",
    [
        # pre v24.8 behavior:
        # database has OLD format, extract has OLD format
        [
            14472,
            1234,
            None,  # db
            14472,
            1234,
            None,  # extract
            14472,
            1234,
            None,
        ],  # expected final db
        # transition state (before first sync has set period_id):
        # database has OLD format, extract has NEW format (first sync)
        [
            14472,
            1234,
            None,  # db
            14472,
            1234,
            7777,  # extract
            14472,
            1234,
            7777,
        ],  # expected final db
        # intended final post-v24.8 behavior:
        # database has new format, extract has new format
        [
            FINEOS_ABSENCE_PERIOD_ID_CLASS_ID,
            7777,
            7777,  # db
            14472,
            1234,
            7777,  # extract (both ID sets)
            14472,
            1234,
            7777,
        ],  # expected final db
        # subsequent runs
        [
            14472,
            1234,
            7777,  # db
            14472,
            1234,
            7777,  # extract (both ID sets)
            14472,
            1234,
            7777,
        ],  # expected final db
    ],
)
def test_create_or_update_absence_period_handles_new_and_old_period_id_formats(
    caplog,
    initialize_factories_session,
    test_db_session,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
    claimant_extract_reference_file,
    mock_payments_s3_config,
    fineos_v24_feature_config,
    db_c_val,
    db_i_val,
    db_p_val,
    feed_c_val,
    feed_i_val,
    feed_p_val,
    expected_c_val,
    expected_i_val,
    expected_p_val,
):

    # create extract data for AbsencePeriod to be processed (extract values)
    claimant_data = FineosPaymentData(
        absence_period_i_value=feed_i_val,
        absence_period_c_value=feed_c_val,
        absence_period_id=feed_p_val,
    )
    extract_util.create_staging_table_instance(
        claimant_data.get_requested_absence_som_record(),
        FineosExtractVbiRequestedAbsenceSom,
        claimant_extract_reference_file,
        None,
    )
    extract_util.create_staging_table_instance(
        claimant_data.get_requested_absence_record(),
        FineosExtractVbiRequestedAbsence,
        claimant_extract_reference_file,
        None,
    )
    claim_1 = ClaimFactory.create(fineos_absence_id=claimant_data.absence_case_number)
    stage_claimant_extract_data([claimant_data], test_db_session, mock_payments_s3_config)

    # save AbsencePeriod to db as if already created by the API (db values)
    db_absence_period = AbsencePeriod(
        fineos_absence_period_class_id=db_c_val,
        fineos_absence_period_index_id=db_i_val,
        fineos_absence_period_id=db_p_val,
        claim_id=claim_1.claim_id,
        absence_period_start_date=claim_1.claim_start_date,
        absence_period_end_date=claim_1.claim_end_date,
    )
    test_db_session.add(db_absence_period)
    test_db_session.commit()

    # setup and run the step
    step = SyncAbsencePeriodsStep(db_session=test_db_session, log_entry_db_session=test_db_session)
    step.run()

    # should not have created a duplicate and should not have encountered IntegrityError
    assert len(claim_1.absence_periods) == 1

    # check table count directly
    absence_periods = test_db_session.query(AbsencePeriod).all()
    assert len(absence_periods) == 1

    # check expected absence period ids
    absence_period = absence_periods[0]
    assert absence_period.fineos_absence_period_class_id == expected_c_val
    assert absence_period.fineos_absence_period_index_id == expected_i_val
    assert absence_period.fineos_absence_period_id == expected_p_val


def test_absence_period_created_by_API_during_processing(
    caplog,
    initialize_factories_session,
    test_db_session,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
    claimant_extract_reference_file,
    mock_payments_s3_config,
):
    # This test covers a regression fixed in PFMLPB-15175
    claimant_data = FineosPaymentData(absence_period_i_value="1234", absence_period_c_value="6789")
    extract_util.create_staging_table_instance(
        claimant_data.get_requested_absence_som_record(),
        FineosExtractVbiRequestedAbsenceSom,
        claimant_extract_reference_file,
        None,
    )
    extract_util.create_staging_table_instance(
        claimant_data.get_requested_absence_record(),
        FineosExtractVbiRequestedAbsence,
        claimant_extract_reference_file,
        None,
    )

    claim_1 = ClaimFactory.create(fineos_absence_id=claimant_data.absence_case_number)
    stage_claimant_extract_data([claimant_data], test_db_session, mock_payments_s3_config)

    step = SyncAbsencePeriodsStep(db_session=test_db_session, log_entry_db_session=test_db_session)

    def get_absence_period():
        # Mock an AbsencePeriod that was created by the API
        db_absence_period = AbsencePeriod(
            fineos_absence_period_class_id="6789",
            fineos_absence_period_index_id="1234",
            claim_id=claim_1.claim_id,
            absence_period_start_date=claim_1.claim_start_date,
            absence_period_end_date=claim_1.claim_end_date,
        )
        test_db_session.add(db_absence_period)
        test_db_session.commit()
        return step.query_for_absence_period_incase_api_created_it(
            class_id=6789, index_id=1234, period_id=None
        )

    with mock.patch.object(
        step, "query_for_absence_period_incase_api_created_it", new_callable=get_absence_period
    ) as mock_absence_period_created_by_api:
        step.run()

        # Should not have created a duplicate and should not have encountered IntegrityError
        assert len(claim_1.absence_periods) == 1
        absence_period = (
            test_db_session.query(AbsencePeriod)
            .filter(
                AbsencePeriod.fineos_absence_period_class_id == "6789",
                AbsencePeriod.fineos_absence_period_index_id == "1234",
            )
            .one()
        )
        assert absence_period is not None
        assert claim_1.absence_periods[0] == absence_period
        assert (
            absence_period.absence_period_id == mock_absence_period_created_by_api.absence_period_id
        )


def test_create_or_update_absence_period_happy_path(
    caplog,
    initialize_factories_session,
    test_db_session,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
    mock_payments_s3_config,
):
    # Create claimant data, and make sure there aren't any initial validation issues
    claimant_data = FineosPaymentData(
        absence_case_number="ABS_001",
        leave_request_start="2021-02-14",
        leave_request_end="2021-02-28",
        leave_request_id=5,
        leave_request_evidence="Satisfied",
        absence_period_c_value=1448,
        absence_period_i_value=1,
        absence_period_type="Episodic",
        absence_reason_qualifier_one="Blood",
        absence_reason_qualifier_two="Accident / Injury",
        absence_reason="Bereavement",
        leave_request_decision="Denied",
    )

    stage_claimant_extract_data(
        [claimant_data],
        test_db_session,
        mock_payments_s3_config,
        date_of_extract=datetime.date(2024, 1, 1),
    )

    # The sync_claim step should have ran prior to the sync_absence_period step
    claim = ClaimFactory.create(fineos_absence_id="ABS_001")

    step = SyncAbsencePeriodsStep(db_session=test_db_session, log_entry_db_session=test_db_session)
    step.run()

    absence_period = (
        test_db_session.query(AbsencePeriod)
        .filter(
            AbsencePeriod.fineos_absence_period_class_id == 1448,
            AbsencePeriod.fineos_absence_period_index_id == 1,
        )
        .one_or_none()
    )
    assert absence_period is not None

    assert absence_period.claim_id == claim.claim_id
    assert absence_period.fineos_absence_period_class_id == 1448
    assert absence_period.fineos_absence_period_index_id == 1
    assert absence_period.absence_period_start_date == datetime.date(2021, 2, 14)
    assert absence_period.absence_period_end_date == datetime.date(2021, 2, 28)
    assert absence_period.fineos_leave_request_id == 5
    assert (
        absence_period.absence_period_type_id == AbsencePeriodType.EPISODIC.absence_period_type_id
    )
    assert (
        absence_period.absence_reason_qualifier_one_id
        == AbsenceReasonQualifierOne.BLOOD.absence_reason_qualifier_one_id
    )
    assert (
        absence_period.absence_reason_qualifier_two_id
        == AbsenceReasonQualifierTwo.ACCIDENT_INJURY.absence_reason_qualifier_two_id
    )
    assert absence_period.absence_reason_id == AbsenceReason.BEREAVEMENT.absence_reason_id
    assert (
        absence_period.leave_request_decision_id
        == LeaveRequestDecision.DENIED.leave_request_decision_id
    )
    # cannot link to leave_request since instance doesn't exist yet
    assert absence_period.leave_request_id is None

    # Syncing absence periods updates start and end on claim
    test_db_session.refresh(claim)
    assert claim.claim_start_date == datetime.date(2021, 2, 14)
    assert claim.claim_end_date == datetime.date(2021, 2, 28)

    # Create new claimant data to update existing absence_period. We make sure the claim and claimant_data's
    # absence_period_c_value and absence_period_i_value remain unchanged.
    new_formatted_claimant_data = FineosPaymentData(
        absence_case_number="ABS_001",
        leave_request_start="2021-03-07",
        leave_request_end="2021-12-11",
        leave_request_id=5,
        leave_request_evidence="UnSatisfied",
        absence_period_c_value=1448,
        absence_period_i_value=1,
        absence_period_type="Office Visit",
        absence_reason_qualifier_one="Adoption",
        absence_reason_qualifier_two="Sickness",
        absence_reason="Medical Donation - Employee",
        leave_request_decision="Projected",
    )

    # Create LeaveRequest to validate that they would be linked if exists
    leave_request = LeaveRequestFactory.create(fineos_leave_request_id=5, claim=claim)

    stage_claimant_extract_data(
        [new_formatted_claimant_data],
        test_db_session,
        mock_payments_s3_config,
        date_of_extract=datetime.date(2024, 1, 3),
    )
    step.run()

    absence_period = (
        test_db_session.query(AbsencePeriod)
        .filter(
            AbsencePeriod.fineos_absence_period_class_id == 1448,
            AbsencePeriod.fineos_absence_period_index_id == 1,
        )
        .one_or_none()
    )
    assert absence_period is not None

    # Should have linked to other existing models
    assert absence_period.claim_id == claim.claim_id
    assert absence_period.leave_request_id == leave_request.leave_request_id

    assert absence_period.fineos_absence_period_class_id == 1448
    assert absence_period.fineos_absence_period_index_id == 1
    assert absence_period.absence_period_start_date == datetime.date(2021, 3, 7)
    assert absence_period.absence_period_end_date == datetime.date(2021, 12, 11)
    assert absence_period.fineos_leave_request_id == 5
    assert (
        absence_period.absence_period_type_id
        == AbsencePeriodType.OFFICE_VISIT.absence_period_type_id
    )
    assert (
        absence_period.absence_reason_qualifier_one_id
        == AbsenceReasonQualifierOne.ADOPTION.absence_reason_qualifier_one_id
    )
    assert (
        absence_period.absence_reason_qualifier_two_id
        == AbsenceReasonQualifierTwo.SICKNESS.absence_reason_qualifier_two_id
    )
    assert (
        absence_period.absence_reason_id
        == AbsenceReason.MEDICAL_DONATION_EMPLOYEE.absence_reason_id
    )
    assert (
        absence_period.leave_request_decision_id
        == LeaveRequestDecision.PROJECTED.leave_request_decision_id
    )

    # Should update the claim start & end since we are updating the same absence period
    assert claim.claim_start_date == datetime.date(2021, 3, 7)
    assert claim.claim_end_date == datetime.date(2021, 12, 11)


def test_create_or_update_absence_period_missing_claim(
    caplog,
    initialize_factories_session,
    test_db_session,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
    mock_payments_s3_config,
):
    # Create claimant data with just an absence case number
    claimant_data = FineosPaymentData(
        generate_defaults=False,
        absence_case_number="NTN-001-ABS-01",
        absence_period_c_value=1010,
        absence_period_i_value=201,
    )
    stage_claimant_extract_data([claimant_data], test_db_session, mock_payments_s3_config)

    step = SyncAbsencePeriodsStep(db_session=test_db_session, log_entry_db_session=test_db_session)
    step.run()

    absence_period = (
        test_db_session.query(AbsencePeriod)
        .filter(
            AbsencePeriod.fineos_absence_period_class_id == 1010,
            AbsencePeriod.fineos_absence_period_index_id == 201,
        )
        .one_or_none()
    )

    # Expect the absence period to not be created if missing claim
    assert absence_period is None

    # if the claim is processed successfully later, the AbsencePeriod should be created too
    claim = ClaimFactory.create(fineos_absence_id="NTN-001-ABS-01")
    reference_file = (
        test_db_session.query(ReferenceFile)
        .filter(
            ReferenceFile.reference_file_type_id
            == ReferenceFileType.FINEOS_CLAIMANT_EXTRACT.reference_file_type_id
        )
        .one()
    )
    reference_file.processed_import_log_id = None
    step.run()

    absence_period = (
        test_db_session.query(AbsencePeriod)
        .filter(
            AbsencePeriod.fineos_absence_period_class_id == 1010,
            AbsencePeriod.fineos_absence_period_index_id == 201,
        )
        .one_or_none()
    )

    assert absence_period
    assert absence_period.claim_id == claim.claim_id


def test_update_absence_period_with_mismatching_claim_id(
    caplog,
    initialize_factories_session,
    test_db_session,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
    mock_payments_s3_config,
):
    # We test if an absence period with matching absence_period.(class_id, index_id) has a mis-match on
    # absence_period.claim_id and claim.claim_id

    # Create claimant data with just an absence case number
    claimant_data_1 = FineosPaymentData(
        absence_case_number="NTN-001-ABS-01",
        absence_period_c_value=1448,
        absence_period_i_value=1,
    )
    claimant_data_2 = FineosPaymentData(
        absence_case_number="NTN-001-ABS-02",
        absence_period_c_value=1448,
        absence_period_i_value=1,
    )

    add_db_records_from_fineos_data(test_db_session, claimant_data_1)
    add_db_records_from_fineos_data(test_db_session, claimant_data_2)

    stage_claimant_extract_data(
        [claimant_data_1, claimant_data_2], test_db_session, mock_payments_s3_config
    )

    # Sync Claim Step would have happened first
    claim_1 = ClaimFactory.create(fineos_absence_id=claimant_data_1.absence_case_number)
    claim_2 = ClaimFactory.create(fineos_absence_id=claimant_data_2.absence_case_number)

    step = SyncAbsencePeriodsStep(db_session=test_db_session, log_entry_db_session=test_db_session)
    step.run()

    absence_period_1 = claim_1.absence_periods[0]
    assert absence_period_1 is not None
    # Should not have linked this absence_period to another claim if already linked to claim
    assert claim_2.absence_periods == []


def test_update_absence_period_with_mismatching_leave_request_id(
    caplog,
    initialize_factories_session,
    test_db_session,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
    mock_payments_s3_config,
):
    # We test if an absence period with matching absence_period.(class_id, index_id) has a mis-match on
    # absence_period.leave_request_id and leave_request.leave_request_id
    # which happens when the fineos_leave_request_id on requested absence was changed

    original_fineos_leave_request_id = 1
    original_absence_period_start_date = "2022-12-19 00:00:00"
    updated_fineos_leave_request_id = 2
    updated_absence_period_start_date = "2022-12-28 00:00:00"

    claim = ClaimFactory.create(fineos_absence_id="NTN-001-ABS-01")
    # LeaveRequest has to exist before it would be linked to the claim
    leave_request = LeaveRequestFactory(
        claim=claim, fineos_leave_request_id=original_fineos_leave_request_id
    )

    # Create claimant data with fineos_leave_request_id
    claimant_data_1 = FineosPaymentData(
        absence_case_number="NTN-001-ABS-01",
        absence_period_c_value=1448,
        absence_period_i_value=1,
        leave_request_id=original_fineos_leave_request_id,
        absenceperiod_start=original_absence_period_start_date,
        absenceperiod_end="2023-01-13 00:00:00",
        # TODO: Remove once FineosPaymentData isn't reading absenceperiod_start from leave_request_start, same with end
        leave_request_start=original_absence_period_start_date,
        leave_request_end="2023-01-13 00:00:00",
    )

    stage_claimant_extract_data(
        [claimant_data_1],
        test_db_session,
        mock_payments_s3_config,
        date_of_extract=datetime.date(2024, 8, 1),
    )
    step = SyncAbsencePeriodsStep(db_session=test_db_session, log_entry_db_session=test_db_session)
    step.run()

    test_db_session.refresh(claim)
    assert len(claim.absence_periods) == 1
    absence_period = claim.absence_periods[0]
    assert absence_period is not None
    assert absence_period.fineos_absence_period_class_id == 1448
    assert absence_period.fineos_absence_period_index_id == 1
    assert absence_period.leave_request_id == leave_request.leave_request_id
    assert absence_period.fineos_leave_request_id == original_fineos_leave_request_id
    assert absence_period.absence_period_start_date == datetime.date(2022, 12, 19)

    # Updated claimant_data
    LeaveRequestFactory(claim=claim, fineos_leave_request_id=updated_fineos_leave_request_id)
    claimant_data_2 = FineosPaymentData(
        absence_case_number="NTN-001-ABS-01",
        absence_period_c_value=1448,
        absence_period_i_value=1,
        leave_request_id=updated_fineos_leave_request_id,
        absenceperiod_start=updated_absence_period_start_date,
        absenceperiod_end="2023-01-13 00:00:00",
        # TODO: Remove once FineosPaymentData isn't reading absenceperiod_start from leave_request_start, same with end
        leave_request_start=updated_absence_period_start_date,
        leave_request_end="2023-01-13 00:00:00",
    )

    stage_claimant_extract_data(
        [claimant_data_2],
        test_db_session,
        mock_payments_s3_config,
        date_of_extract=datetime.date(2024, 8, 2),
    )
    step = SyncAbsencePeriodsStep(db_session=test_db_session, log_entry_db_session=test_db_session)
    step.run()

    test_db_session.refresh(claim)
    # Should not create new absence_period
    assert len(claim.absence_periods) == 1
    absence_period_2 = claim.absence_periods[0]
    assert absence_period_2 is not None
    # the same absence_period is still linked to claim
    assert absence_period.absence_period_id == absence_period_2.absence_period_id

    # The existing absence period data should be un-modified
    assert absence_period.leave_request_id == leave_request.leave_request_id
    assert absence_period.fineos_leave_request_id == original_fineos_leave_request_id
    assert absence_period.absence_period_start_date == datetime.date(2022, 12, 19)

    # The metrics should show a mismatch happened
    metrics = step.get_log_entry().metrics
    assert metrics[step.Metrics.ABSENCE_PERIOD_LEAVE_REQUEST_MISMATCH.value] == 1


def test_update_absence_period_with_mismatching_claim_id_should_not_update_other_fields(
    caplog,
    initialize_factories_session,
    test_db_session,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
    mock_payments_s3_config,
):
    # We test if an absence period with matching absence_period.(class_id, index_id) has a mis-match on
    # absence_period.claim_id and claim.claim_id

    # Create claimant data with just an absence case number
    claimant_data_1 = FineosPaymentData(
        absence_case_number="NTN-001-ABS-01",
        absence_period_c_value=1448,
        absence_period_i_value=1,
        leave_request_id=2,
    )
    add_db_records_from_fineos_data(test_db_session, claimant_data_1)
    stage_claimant_extract_data([claimant_data_1], test_db_session, mock_payments_s3_config)
    # Sync Claim Step would have happened first
    claim_1 = ClaimFactory.create(fineos_absence_id=claimant_data_1.absence_case_number)
    step = SyncAbsencePeriodsStep(db_session=test_db_session, log_entry_db_session=test_db_session)
    step.run()

    absence_period_1 = claim_1.absence_periods[0]
    assert absence_period_1 is not None

    absence_period = (
        test_db_session.query(AbsencePeriod)
        .filter(
            AbsencePeriod.fineos_absence_period_class_id == 1448,
            AbsencePeriod.fineos_absence_period_index_id == 1,
        )
        .one_or_none()
    )
    assert absence_period is not None

    # Should have linked to other existing models
    assert absence_period.claim_id == claim_1.claim_id
    assert absence_period.fineos_leave_request_id == 2

    claimant_data_2 = FineosPaymentData(
        absence_case_number="NTN-001-ABS-02",  # Different absence_case_number
        absence_period_c_value=1448,
        absence_period_i_value=1,
        leave_request_id=5,  # Changed value
    )
    add_db_records_from_fineos_data(test_db_session, claimant_data_2)
    # Sync Claim Step would have happened first
    claim_2 = ClaimFactory.create(fineos_absence_id=claimant_data_2.absence_case_number)
    stage_claimant_extract_data([claimant_data_2], test_db_session, mock_payments_s3_config)

    step = SyncAbsencePeriodsStep(db_session=test_db_session, log_entry_db_session=test_db_session)
    step.run()

    # Should not have linked this absence_period to another claim if already linked to claim
    assert claim_2.absence_periods == []

    absence_period = (
        test_db_session.query(AbsencePeriod)
        .filter(
            AbsencePeriod.fineos_absence_period_class_id == 1448,
            AbsencePeriod.fineos_absence_period_index_id == 1,
        )
        .one_or_none()
    )
    assert absence_period is not None

    # Should have linked to other existing models
    assert absence_period.claim_id == claim_1.claim_id
    assert absence_period.leave_request_id is None

    # Should not have updated the values for existing AbsencePeriod
    assert absence_period.fineos_leave_request_id == 2


def test_create_or_update_absence_period_with_incomplete_request_absence_data(
    caplog,
    initialize_factories_session,
    test_db_session,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
    mock_payments_s3_config,
):
    # Create claimant data, with request absence fields ABSENCEPERIOD_CLASSID, ABSENCEPERIOD_INDEXID as Unknown
    claimant_data = FineosPaymentData(
        leave_request_start="2021-02-14",
        leave_request_end="2021-02-28",
        leave_request_id=5,
        leave_request_evidence="UnSatisfied",
        absence_period_c_value="",
        absence_period_i_value="",
    )
    add_db_records_from_fineos_data(test_db_session, claimant_data)
    stage_claimant_extract_data([claimant_data], test_db_session, mock_payments_s3_config)

    num_absence_periods_before = test_db_session.query(AbsencePeriod).count()
    step = SyncAbsencePeriodsStep(db_session=test_db_session, log_entry_db_session=test_db_session)
    step.run()

    # Should not have created an absence period
    num_absence_periods_after = test_db_session.query(AbsencePeriod).count()
    assert num_absence_periods_before == num_absence_periods_after


def test_absence_period_no_oob_record(
    caplog,
    initialize_factories_session,
    test_db_session,
    mock_fineos_s3_bucket,
    set_exporter_env_vars,
    claimant_extract_reference_file,
):
    claimant_data = FineosPaymentData(absence_period_i_value="1234")
    test_db_session.add(
        extract_util.create_staging_table_instance(
            claimant_data.get_requested_absence_som_record(),
            FineosExtractVbiRequestedAbsenceSom,
            claimant_extract_reference_file,
            None,
        )
    )
    claim_1 = ClaimFactory.create(fineos_absence_id=claimant_data.absence_case_number)

    step = SyncAbsencePeriodsStep(db_session=test_db_session, log_entry_db_session=test_db_session)
    step.run()

    # This test exercises a bug caught by the E2E team in EDM-1261
    # Fields that should have been marked optional on RequestedAbsenceForAbsencePeriod
    # weren't, which meant Pydantic would raise an exception when from_orm was called
    # due to the fields being missing when there was no OOB record for the absence period
    # This test ensures that the absence period is still created even if there is no OOB record
    assert len(claim_1.absence_periods) == 1
