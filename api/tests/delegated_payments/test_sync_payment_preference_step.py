from uuid import UUID

import pytest

import massgov.pfml.db as db
from massgov.pfml.db.lookup_data.employees import BankAccountType, PaymentMethod
from massgov.pfml.db.lookup_data.reference_file_type import ReferenceFileType
from massgov.pfml.db.models.factories import (
    EmployeePaymentPreferenceFactory,
    EmployeeWithFineosNumberFactory,
    FineosExtractEmployeeFeedAggregateFactory,
    FineosExtractEmployeeFeedFactory,
    ReferenceFileFactory,
)
from massgov.pfml.db.models.payments import ClaimantPrepaidRegistration, PaymentPreference
from massgov.pfml.delegated_payments.sync_payment_preference_step import SyncPaymentPreferenceStep
from tests.helpers.sql_snapshot import assert_queries_are_equal


@pytest.fixture
def sync_payment_preference_step(test_db_session):
    return SyncPaymentPreferenceStep(
        db_session=test_db_session, log_entry_db_session=test_db_session
    )


@pytest.fixture
def sync_agg_payment_preference_step(test_db_session, monkeypatch):
    monkeypatch.setenv("ENABLE_SYNC_PAYMENT_PREFERENCES_USING_AGGREGATE_TABLE", "1")
    return SyncPaymentPreferenceStep(
        db_session=test_db_session, log_entry_db_session=test_db_session
    )


def test_full_extract_query_snapshot(
    initialize_factories_session,
    sync_payment_preference_step,
    snapshot_file,
):
    reference_file = ReferenceFileFactory.create(
        reference_file_id=UUID("00000000-0000-0000-0000-000000000000")
    )
    assert_queries_are_equal(
        sync_payment_preference_step._full_extract_query(reference_file),
        snapshot_file,
    )


def test_agg_extract_query_snapshot(
    sync_payment_preference_step,
    snapshot_file,
):
    assert_queries_are_equal(
        sync_payment_preference_step._agg_extract_query(),
        snapshot_file,
    )


def test_comparison_extract_query_snapshot(
    initialize_factories_session,
    sync_payment_preference_step,
    snapshot_file,
):
    reference_file = ReferenceFileFactory.create(
        reference_file_id=UUID("00000000-0000-0000-0000-000000000000")
    )
    assert_queries_are_equal(
        sync_payment_preference_step._compare_query_result_sets_query(reference_file),
        snapshot_file,
    )


class FineosExtractPaymentPreferenceFactory:
    def __init__(
        self,
        employee,
        payment_preference,
        reference_file,
        with_full=True,
        with_aggregate=False,
        fineos_extract_employee_feed=None,
    ):
        params = dict(
            reference_file=reference_file,
            natinsno=employee.tax_identifier.tax_identifier,
            customerno=employee.fineos_customer_number,
            defpaymentpref="Y",
            sortcode=payment_preference.routing_number,
            accountno=payment_preference.account_number,
            accountname="Updated",  # Setting to something different from Payment Preference to trigger an upsert
            accounttype=BankAccountType.CHECKING.bank_account_type_description,
            paymentmethod=payment_preference.payment_method.payment_method_description,
            effectivefrom="2025-01-01",
            effectiveto="2025-01-02",
            **(fineos_extract_employee_feed or {})
        )

        if with_full:
            self.full_extract = FineosExtractEmployeeFeedFactory.create(**params)

        if with_aggregate:
            self.agg_extract = FineosExtractEmployeeFeedAggregateFactory.create(**params)


@pytest.fixture
def claimant_reference_file(initialize_factories_session):
    return ReferenceFileFactory(
        reference_file_type_id=ReferenceFileType.FINEOS_CLAIMANT_EXTRACT.reference_file_type_id
    )


def test_run_sync_payment_preference_step_missing_aggregate(
    initialize_factories_session,
    claimant_reference_file,
    sync_payment_preference_step,
):

    employee = EmployeeWithFineosNumberFactory()
    payment_preference = EmployeePaymentPreferenceFactory()

    FineosExtractPaymentPreferenceFactory(
        employee=employee,
        payment_preference=payment_preference,
        reference_file=claimant_reference_file,
    )
    sync_payment_preference_step.run()
    metrics = sync_payment_preference_step.get_log_entry().metrics
    assert metrics["aggregate_missing"] == 1
    assert metrics["aggregate_extra"] == 0
    assert metrics["aggregate_matched"] == 0


def test_run_sync_payment_preference_step_extra_aggregate(
    initialize_factories_session,
    claimant_reference_file,
    sync_payment_preference_step,
):

    employee = EmployeeWithFineosNumberFactory()
    payment_preference = EmployeePaymentPreferenceFactory()

    FineosExtractPaymentPreferenceFactory(
        employee=employee,
        payment_preference=payment_preference,
        reference_file=claimant_reference_file,
        with_full=False,
        with_aggregate=True,
    )
    sync_payment_preference_step.run()
    metrics = sync_payment_preference_step.get_log_entry().metrics
    assert metrics["aggregate_missing"] == 0
    assert metrics["aggregate_extra"] == 1
    assert metrics["aggregate_matched"] == 0


def test_run_sync_payment_preference_step_matched_aggregate(
    initialize_factories_session,
    claimant_reference_file,
    sync_payment_preference_step,
):

    employee = EmployeeWithFineosNumberFactory()
    payment_preference = EmployeePaymentPreferenceFactory()

    FineosExtractPaymentPreferenceFactory(
        employee=employee,
        payment_preference=payment_preference,
        reference_file=claimant_reference_file,
        with_full=True,
        with_aggregate=True,
    )
    sync_payment_preference_step.run()
    metrics = sync_payment_preference_step.get_log_entry().metrics
    assert metrics["aggregate_missing"] == 0
    assert metrics["aggregate_extra"] == 0
    assert metrics["aggregate_matched"] == 1


def test_run_sync_payment_preference_step_sync_full_query(
    initialize_factories_session, sync_payment_preference_step, snapshot_file
):
    reference_file = ReferenceFileFactory.create(
        reference_file_id=UUID("00000000-0000-0000-0000-000000000000")
    )

    assert_queries_are_equal(
        sync_payment_preference_step._payment_preference_query(reference_file), snapshot_file
    )


def test_run_sync_payment_preference_step_sync_aggregate_query(
    initialize_factories_session, sync_agg_payment_preference_step, snapshot_file
):
    reference_file = ReferenceFileFactory.create(
        reference_file_id=UUID("00000000-0000-0000-0000-000000000000")
    )

    assert_queries_are_equal(
        sync_agg_payment_preference_step._payment_preference_query(reference_file), snapshot_file
    )


def test_run_sync_payment_preference_step_sync_agg_no_full(
    initialize_factories_session,
    claimant_reference_file,
    sync_agg_payment_preference_step,
):
    employee = EmployeeWithFineosNumberFactory()
    payment_preference = EmployeePaymentPreferenceFactory()

    FineosExtractPaymentPreferenceFactory(
        employee=employee,
        payment_preference=payment_preference,
        reference_file=claimant_reference_file,
        with_full=False,
        with_aggregate=True,
    )
    sync_agg_payment_preference_step.run()
    metrics = sync_agg_payment_preference_step.get_log_entry().metrics
    assert metrics["enable_sync_payment_preferences_using_aggregate_table"] is True
    assert metrics["aggregate_missing"] == 0
    assert metrics["aggregate_extra"] == 1
    assert metrics["aggregate_matched"] == 0
    assert metrics["payment_preference_processed"] == 1


def test_run_sync_payment_preference_step_sync_full_no_agg(
    initialize_factories_session,
    claimant_reference_file,
    sync_payment_preference_step,
):
    employee = EmployeeWithFineosNumberFactory()
    payment_preference = EmployeePaymentPreferenceFactory()

    FineosExtractPaymentPreferenceFactory(
        employee=employee,
        payment_preference=payment_preference,
        reference_file=claimant_reference_file,
        with_full=True,
        with_aggregate=False,
    )
    sync_payment_preference_step.run()
    metrics = sync_payment_preference_step.get_log_entry().metrics
    assert metrics["enable_sync_payment_preferences_using_aggregate_table"] is False
    assert metrics["aggregate_missing"] == 1
    assert metrics["aggregate_extra"] == 0
    assert metrics["aggregate_matched"] == 0
    assert metrics["payment_preference_processed"] == 1


def test_sync_payment_preference_check(
    initialize_factories_session,
    claimant_reference_file,
    sync_payment_preference_step,
    test_db_session: db.Session,
):

    employee = EmployeeWithFineosNumberFactory()
    payment_preference = EmployeePaymentPreferenceFactory(
        employee=employee,
        payment_method_id=PaymentMethod.CHECK.payment_method_id,
        account_number=None,
        routing_number=None,
    )

    FineosExtractPaymentPreferenceFactory(
        employee=employee,
        payment_preference=payment_preference,
        reference_file=claimant_reference_file,
    )
    sync_payment_preference_step.run()

    payment_preferences = (
        test_db_session.query(PaymentPreference)
        .filter(PaymentPreference.employee_id == employee.employee_id)
        .all()
    )

    prepaid_registrations = (
        test_db_session.query(ClaimantPrepaidRegistration)
        .filter(ClaimantPrepaidRegistration.employee_id == employee.employee_id)
        .all()
    )

    assert payment_preferences[0].payment_method_id == PaymentMethod.CHECK.payment_method_id
    assert len(prepaid_registrations) == 0


def test_sync_payment_preference_eft(
    initialize_factories_session,
    claimant_reference_file,
    sync_payment_preference_step,
    test_db_session: db.Session,
):

    employee = EmployeeWithFineosNumberFactory()
    payment_preference = EmployeePaymentPreferenceFactory(
        employee=employee,
        payment_method_id=PaymentMethod.ACH.payment_method_id,
    )

    FineosExtractPaymentPreferenceFactory(
        employee=employee,
        payment_preference=payment_preference,
        reference_file=claimant_reference_file,
    )
    sync_payment_preference_step.run()

    payment_preferences = (
        test_db_session.query(PaymentPreference)
        .filter(PaymentPreference.employee_id == employee.employee_id)
        .all()
    )

    prepaid_registrations = (
        test_db_session.query(ClaimantPrepaidRegistration)
        .filter(ClaimantPrepaidRegistration.employee_id == employee.employee_id)
        .all()
    )

    assert payment_preferences[0].payment_method_id == PaymentMethod.ACH.payment_method_id
    assert len(prepaid_registrations) == 0


def test_sync_payment_preference_prepaid(
    initialize_factories_session,
    claimant_reference_file,
    sync_payment_preference_step,
    test_db_session: db.Session,
):

    employee = EmployeeWithFineosNumberFactory()
    payment_preference = EmployeePaymentPreferenceFactory(
        employee=employee,
        payment_method_id=PaymentMethod.PREPAID_CARD.payment_method_id,
    )

    FineosExtractPaymentPreferenceFactory(
        employee=employee,
        payment_preference=payment_preference,
        reference_file=claimant_reference_file,
    )
    sync_payment_preference_step.run()

    payment_preferences = (
        test_db_session.query(PaymentPreference)
        .filter(PaymentPreference.employee_id == employee.employee_id)
        .all()
    )

    prepaid_registrations = (
        test_db_session.query(ClaimantPrepaidRegistration)
        .filter(ClaimantPrepaidRegistration.employee_id == employee.employee_id)
        .all()
    )

    assert payment_preferences[0].payment_method_id == PaymentMethod.PREPAID_CARD.payment_method_id
    assert len(prepaid_registrations) == 1


def test_sync_payment_preference_prepaid_empty(
    initialize_factories_session,
    claimant_reference_file,
    sync_payment_preference_step,
    test_db_session: db.Session,
):

    employee = EmployeeWithFineosNumberFactory()
    payment_preference = EmployeePaymentPreferenceFactory(
        employee=employee,
        payment_method_id=PaymentMethod.PREPAID_CARD.payment_method_id,
        account_number="",
        routing_number="",
    )

    FineosExtractPaymentPreferenceFactory(
        employee=employee,
        payment_preference=payment_preference,
        reference_file=claimant_reference_file,
    )
    sync_payment_preference_step.run()

    payment_preferences = (
        test_db_session.query(PaymentPreference)
        .filter(PaymentPreference.employee_id == employee.employee_id)
        .all()
    )

    assert payment_preferences[0].payment_method_id == PaymentMethod.PREPAID_CARD.payment_method_id
    assert payment_preferences[0].account_number is None
    assert payment_preferences[0].routing_number is None


def test_sync_payment_preference_prepaid_zeros(
    initialize_factories_session,
    claimant_reference_file,
    sync_payment_preference_step,
    test_db_session: db.Session,
):

    employee = EmployeeWithFineosNumberFactory()
    payment_preference = EmployeePaymentPreferenceFactory(
        employee=employee,
        payment_method_id=PaymentMethod.PREPAID_CARD.payment_method_id,
        account_number="0",
        routing_number="0",
    )

    FineosExtractPaymentPreferenceFactory(
        employee=employee,
        payment_preference=payment_preference,
        reference_file=claimant_reference_file,
    )
    sync_payment_preference_step.run()

    payment_preferences = (
        test_db_session.query(PaymentPreference)
        .filter(PaymentPreference.employee_id == employee.employee_id)
        .all()
    )

    assert payment_preferences[0].payment_method_id == PaymentMethod.PREPAID_CARD.payment_method_id
    assert payment_preferences[0].account_number is None
    assert payment_preferences[0].routing_number is None
