import csv
import datetime
import os
from dataclasses import asdict
from typing import List, Optional

from freezegun import freeze_time

import massgov.pfml.delegated_payments.extracts.fineos_extract_config as fineos_extract_config
import massgov.pfml.util.files as file_util
from massgov.pfml.db.lookup_data.employees import PaymentTransactionType, PrenoteState
from massgov.pfml.db.lookup_data.reference_file_type import ReferenceFileType
from massgov.pfml.db.models.employees import Payment, PubEft
from massgov.pfml.db.models.factories import (
    ClaimFactory,
    EmployeeFactory,
    EmployeePubEftPairFactory,
    EmployerFactory,
    FineosExtractVbiRequestedAbsenceFactory,
    FineosExtractVpeiClaimDetailsFactory,
    FineosExtractVpeiFactory,
    FineosExtractVpeiPaymentDetailsFactory,
    FineosExtractVpeiPaymentLineFactory,
    PaymentFactory,
    PubEftFactory,
    ReferenceFileFactory,
)
from massgov.pfml.db.models.payments import (
    FineosExtractEmployeeFeed,
    FineosExtractEmployeeFeedDelta,
    FineosExtractVbi1099DataSom,
    FineosExtractVbiBenefitPeriod,
    FineosExtractVbiDocumentSom,
    FineosExtractVbiLeavePlanRequestedAbsence,
    FineosExtractVbiLeaveSummary,
    FineosExtractVbiPaidLeaveCaseInfoSom,
    FineosExtractVbiRequestedAbsence,
    FineosExtractVbiRequestedAbsenceSom,
    FineosExtractVbiTaskReportDeltaSom,
    FineosExtractVPaidLeaveInstruction,
    FineosExtractVPaidLeaveInstructionSom,
    FineosExtractVpei,
    FineosExtractVpeiClaimDetails,
    FineosExtractVpeiPaymentDetails,
    FineosExtractVpeiPaymentLine,
)
from massgov.pfml.delegated_payments.audit.delegated_payment_audit_csv import (
    PAYMENT_AUDIT_CSV_HEADERS,
)
from massgov.pfml.delegated_payments.mock.fineos_extract_data import _create_file
from massgov.pfml.delegated_payments.task.process_fineos_extracts import FineosExtractTaskRunner
from massgov.pfml.delegated_payments.task.process_pub_payments import CreatePubFileTaskRunner


def add_lists(l1: Optional[List], l2: Optional[List]) -> List:
    return (l1 or []) + (l2 or [])


class ScenarioData:
    fineos_extract_employee_feed_delta: List[FineosExtractEmployeeFeedDelta] = []
    fineos_extract_employee_feed: List[FineosExtractEmployeeFeed] = []
    fineos_extract_v_paid_leave_instruction_som: List[FineosExtractVPaidLeaveInstructionSom] = []
    fineos_extract_v_paid_leave_instruction: List[FineosExtractVPaidLeaveInstruction] = []
    fineos_extract_vbi_1099_data_som: List[FineosExtractVbi1099DataSom] = []
    fineos_extract_vbi_benefit_period: List[FineosExtractVbiBenefitPeriod] = []
    fineos_extract_vbi_document_som: List[FineosExtractVbiDocumentSom] = []
    fineos_extract_vbi_leave_plan_requested_absence: List[
        FineosExtractVbiLeavePlanRequestedAbsence
    ] = []
    fineos_extract_vbi_leave_summary: List[FineosExtractVbiLeaveSummary] = []
    fineos_extract_vbi_paid_leave_case_info_som: List[FineosExtractVbiPaidLeaveCaseInfoSom] = []
    fineos_extract_vbi_requested_absence_som: List[FineosExtractVbiRequestedAbsenceSom] = []
    fineos_extract_vbi_requested_absence: List[FineosExtractVbiRequestedAbsence] = []
    fineos_extract_vbi_task_report_delta_som: List[FineosExtractVbiTaskReportDeltaSom] = []
    fineos_extract_vpei_claim_details: List[FineosExtractVpeiClaimDetails] = []
    fineos_extract_vpei_payment_details: List[FineosExtractVpeiPaymentDetails] = []
    fineos_extract_vpei_payment_line: List[FineosExtractVpeiPaymentLine] = []
    fineos_extract_vpei: List[FineosExtractVpei] = []

    def __init__(
        self,
        fineos_extract_employee_feed_delta=None,
        fineos_extract_employee_feed=None,
        fineos_extract_v_paid_leave_instruction_som=None,
        fineos_extract_v_paid_leave_instruction=None,
        fineos_extract_vbi_1099_data_som=None,
        fineos_extract_vbi_benefit_period=None,
        fineos_extract_vbi_document_som=None,
        fineos_extract_vbi_leave_plan_requested_absence=None,
        fineos_extract_vbi_leave_summary=None,
        fineos_extract_vbi_paid_leave_case_info_som=None,
        fineos_extract_vbi_requested_absence_som=None,
        fineos_extract_vbi_requested_absence=None,
        fineos_extract_vbi_task_report_delta_som=None,
        fineos_extract_vpei_claim_details=None,
        fineos_extract_vpei_payment_details=None,
        fineos_extract_vpei_payment_line=None,
        fineos_extract_vpei=None,
    ):
        self.fineos_extract_employee_feed_delta = fineos_extract_employee_feed_delta
        self.fineos_extract_employee_feed = fineos_extract_employee_feed
        self.fineos_extract_v_paid_leave_instruction_som = (
            fineos_extract_v_paid_leave_instruction_som
        )
        self.fineos_extract_v_paid_leave_instruction = fineos_extract_v_paid_leave_instruction
        self.fineos_extract_vbi_1099_data_som = fineos_extract_vbi_1099_data_som
        self.fineos_extract_vbi_benefit_period = fineos_extract_vbi_benefit_period
        self.fineos_extract_vbi_document_som = fineos_extract_vbi_document_som
        self.fineos_extract_vbi_leave_plan_requested_absence = (
            fineos_extract_vbi_leave_plan_requested_absence
        )
        self.fineos_extract_vbi_leave_summary = fineos_extract_vbi_leave_summary
        self.fineos_extract_vbi_paid_leave_case_info_som = (
            fineos_extract_vbi_paid_leave_case_info_som
        )
        self.fineos_extract_vbi_requested_absence_som = fineos_extract_vbi_requested_absence_som
        self.fineos_extract_vbi_requested_absence = fineos_extract_vbi_requested_absence
        self.fineos_extract_vbi_task_report_delta_som = fineos_extract_vbi_task_report_delta_som
        self.fineos_extract_vpei_claim_details = fineos_extract_vpei_claim_details
        self.fineos_extract_vpei_payment_details = fineos_extract_vpei_payment_details
        self.fineos_extract_vpei_payment_line = fineos_extract_vpei_payment_line
        self.fineos_extract_vpei = fineos_extract_vpei

    def __add__(self, other: "ScenarioData") -> "ScenarioData":
        new_data = ScenarioData(
            fineos_extract_employee_feed_delta=add_lists(
                self.fineos_extract_employee_feed_delta, other.fineos_extract_employee_feed_delta
            ),
            fineos_extract_employee_feed=add_lists(
                self.fineos_extract_employee_feed, other.fineos_extract_employee_feed
            ),
            fineos_extract_v_paid_leave_instruction_som=add_lists(
                self.fineos_extract_v_paid_leave_instruction_som,
                other.fineos_extract_v_paid_leave_instruction_som,
            ),
            fineos_extract_v_paid_leave_instruction=add_lists(
                self.fineos_extract_v_paid_leave_instruction,
                other.fineos_extract_v_paid_leave_instruction,
            ),
            fineos_extract_vbi_1099_data_som=add_lists(
                self.fineos_extract_vbi_1099_data_som, other.fineos_extract_vbi_1099_data_som
            ),
            fineos_extract_vbi_benefit_period=add_lists(
                self.fineos_extract_vbi_benefit_period, other.fineos_extract_vbi_benefit_period
            ),
            fineos_extract_vbi_document_som=add_lists(
                self.fineos_extract_vbi_document_som, other.fineos_extract_vbi_document_som
            ),
            fineos_extract_vbi_leave_plan_requested_absence=add_lists(
                self.fineos_extract_vbi_leave_plan_requested_absence,
                other.fineos_extract_vbi_leave_plan_requested_absence,
            ),
            fineos_extract_vbi_leave_summary=add_lists(
                self.fineos_extract_vbi_leave_summary, other.fineos_extract_vbi_leave_summary
            ),
            fineos_extract_vbi_paid_leave_case_info_som=add_lists(
                self.fineos_extract_vbi_paid_leave_case_info_som,
                other.fineos_extract_vbi_paid_leave_case_info_som,
            ),
            fineos_extract_vbi_requested_absence_som=add_lists(
                self.fineos_extract_vbi_requested_absence_som,
                other.fineos_extract_vbi_requested_absence_som,
            ),
            fineos_extract_vbi_requested_absence=add_lists(
                self.fineos_extract_vbi_requested_absence,
                other.fineos_extract_vbi_requested_absence,
            ),
            fineos_extract_vbi_task_report_delta_som=add_lists(
                self.fineos_extract_vbi_task_report_delta_som,
                other.fineos_extract_vbi_task_report_delta_som,
            ),
            fineos_extract_vpei_claim_details=add_lists(
                self.fineos_extract_vpei_claim_details, other.fineos_extract_vpei_claim_details
            ),
            fineos_extract_vpei_payment_details=add_lists(
                self.fineos_extract_vpei_payment_details, other.fineos_extract_vpei_payment_details
            ),
            fineos_extract_vpei_payment_line=add_lists(
                self.fineos_extract_vpei_payment_line, other.fineos_extract_vpei_payment_line
            ),
            fineos_extract_vpei=add_lists(self.fineos_extract_vpei, other.fineos_extract_vpei),
        )
        return new_data

    def write(self, s3_config, date_of_extract: datetime):

        folder_path = s3_config.fineos_data_export_path
        date_prefix = date_of_extract.strftime("%Y-%m-%d-%H-%M-%S-")
        data = [
            {
                "file_name": fineos_extract_config.FineosExtractConstants.EMPLOYEE_FEED_DELTA.file_name,
                "field_names": fineos_extract_config.FineosExtractConstants.EMPLOYEE_FEED_DELTA.field_names,
                "data": self.fineos_extract_employee_feed_delta,
            },
            {
                "file_name": fineos_extract_config.FineosExtractConstants.EMPLOYEE_FEED.file_name,
                "field_names": fineos_extract_config.FineosExtractConstants.EMPLOYEE_FEED.field_names,
                "data": self.fineos_extract_employee_feed,
            },
            {
                "file_name": fineos_extract_config.FineosExtractConstants.V_PAIDLEAVEINSTRUCTION_SOM.file_name,
                "field_names": fineos_extract_config.FineosExtractConstants.V_PAIDLEAVEINSTRUCTION_SOM.field_names,
                "data": self.fineos_extract_v_paid_leave_instruction_som,
            },
            {
                "file_name": fineos_extract_config.FineosExtractConstants.PAID_LEAVE_INSTRUCTION.file_name,
                "field_names": fineos_extract_config.FineosExtractConstants.PAID_LEAVE_INSTRUCTION.field_names,
                "data": self.fineos_extract_v_paid_leave_instruction,
            },
            {
                "file_name": fineos_extract_config.FineosExtractConstants.VBI_1099DATA_SOM.file_name,
                "field_names": fineos_extract_config.FineosExtractConstants.VBI_1099DATA_SOM.field_names,
                "data": self.fineos_extract_vbi_1099_data_som,
            },
            {
                "file_name": fineos_extract_config.FineosExtractConstants.VBI_BENEFITPERIOD.file_name,
                "field_names": fineos_extract_config.FineosExtractConstants.VBI_BENEFITPERIOD.field_names,
                "data": self.fineos_extract_vbi_benefit_period,
            },
            {
                "file_name": fineos_extract_config.FineosExtractConstants.VBI_DOCUMENT_SOM.file_name,
                "field_names": fineos_extract_config.FineosExtractConstants.VBI_DOCUMENT_SOM.field_names,
                "data": self.fineos_extract_vbi_document_som,
            },
            {
                "file_name": fineos_extract_config.FineosExtractConstants.VBI_LEAVE_PLAN_REQUESTED_ABSENCE.file_name,
                "field_names": fineos_extract_config.FineosExtractConstants.VBI_LEAVE_PLAN_REQUESTED_ABSENCE.field_names,
                "data": self.fineos_extract_vbi_leave_plan_requested_absence,
            },
            {
                "file_name": fineos_extract_config.FineosExtractConstants.VBI_LEAVESUMMARY.file_name,
                "field_names": fineos_extract_config.FineosExtractConstants.VBI_LEAVESUMMARY.field_names,
                "data": self.fineos_extract_vbi_leave_summary,
            },
            {
                "file_name": fineos_extract_config.FineosExtractConstants.VBI_PAIDLEAVECASEINFO_SOM.file_name,
                "field_names": fineos_extract_config.FineosExtractConstants.VBI_PAIDLEAVECASEINFO_SOM.field_names,
                "data": self.fineos_extract_vbi_paid_leave_case_info_som,
            },
            {
                "file_name": fineos_extract_config.FineosExtractConstants.VBI_REQUESTED_ABSENCE_SOM.file_name,
                "field_names": fineos_extract_config.FineosExtractConstants.VBI_REQUESTED_ABSENCE_SOM.field_names,
                "data": self.fineos_extract_vbi_requested_absence_som,
            },
            {
                "file_name": fineos_extract_config.FineosExtractConstants.VBI_REQUESTED_ABSENCE.file_name,
                "field_names": fineos_extract_config.FineosExtractConstants.VBI_REQUESTED_ABSENCE.field_names,
                "data": self.fineos_extract_vbi_requested_absence,
            },
            {
                "file_name": fineos_extract_config.FineosExtractConstants.VBI_TASKREPORT_DELTA_SOM.file_name,
                "field_names": fineos_extract_config.FineosExtractConstants.VBI_TASKREPORT_DELTA_SOM.field_names,
                "data": self.fineos_extract_vbi_task_report_delta_som,
            },
            {
                "file_name": fineos_extract_config.FineosExtractConstants.CLAIM_DETAILS.file_name,
                "field_names": fineos_extract_config.FineosExtractConstants.CLAIM_DETAILS.field_names,
                "data": self.fineos_extract_vpei_claim_details,
            },
            {
                "file_name": fineos_extract_config.FineosExtractConstants.PAYMENT_DETAILS.file_name,
                "field_names": fineos_extract_config.FineosExtractConstants.PAYMENT_DETAILS.field_names,
                "data": self.fineos_extract_vpei_payment_details,
            },
            {
                "file_name": fineos_extract_config.FineosExtractConstants.PAYMENT_LINE.file_name,
                "field_names": fineos_extract_config.FineosExtractConstants.PAYMENT_LINE.field_names,
                "data": self.fineos_extract_vpei_payment_line,
            },
            {
                "file_name": fineos_extract_config.FineosExtractConstants.VPEI.file_name,
                "field_names": fineos_extract_config.FineosExtractConstants.VPEI.field_names,
                "data": self.fineos_extract_vpei,
            },
        ]

        for extract in data:
            writer = _create_file(
                folder_path, date_prefix, extract["file_name"], extract["field_names"]
            )
            rows = extract["data"] or []
            for row in rows:
                row = dict(
                    [
                        (key.upper(), value)
                        for key, value in row.__dict__.items()
                        if key.upper() in extract["field_names"]
                    ]
                )
                writer.csv_writer.writerow(row)


class StandardPaymentScenarioData(ScenarioData):

    def __init__(self, parent_payment: Payment, pub_eft: PubEft, child_payments: List[Payment]):
        self.parent_payment = parent_payment
        self.pub_eft = pub_eft
        self.child_payments = child_payments
        net_amount = self.parent_payment.amount + sum(child.amount for child in self.child_payments)

        vpei = FineosExtractVpeiFactory.build(
            c=self.parent_payment.fineos_pei_c_value,
            i=self.parent_payment.fineos_pei_i_value,
            payeecustomer="claimant",  # used to determine who the payer is
            payeeidentifi="Social Security Number",
            eventtype="PaymentOut",
            eventreason="Automatic Main Payment",
            paymenttype="",
            payeesocnumbe=self.parent_payment.employee.tax_identifier.tax_identifier,
            paymentdate=self.parent_payment.payment_date,
            amount_monamt=self.parent_payment.amount,
            paymentmethod="Elec Funds Transfer",
            payeebanksort=self.pub_eft.routing_nbr,
            payeeaccountn=self.pub_eft.account_nbr,
            payeeaccountt="Checking",
        )
        detail = FineosExtractVpeiPaymentDetailsFactory.build(
            peclassid=vpei.c,
            peindexid=vpei.i,
            paymentstartp=self.parent_payment.period_start_date,
            paymentendper=self.parent_payment.period_end_date,
            balancingamou_monamt=self.parent_payment.amount,
            businessnetbe_monamt=net_amount,
        )
        claim_details = FineosExtractVpeiClaimDetailsFactory.build(
            peclassid=vpei.c,
            peindexid=vpei.i,
            absencecasenu=self.parent_payment.claim.fineos_absence_id,
        )
        super().__init__(
            fineos_extract_vpei=[vpei],
            fineos_extract_vpei_claim_details=[claim_details],
            fineos_extract_vpei_payment_details=[detail],
            fineos_extract_vpei_payment_line=(
                [
                    FineosExtractVpeiPaymentLineFactory.build(
                        amount_monamt=net_amount,
                        linetype="Auto Gross Entitlement",
                        paymentdetailclassid=detail.c,
                        paymentdetailindexid=detail.i,
                        c_pymnteif_paymentlines=vpei.c,
                        i_pymnteif_paymentlines=vpei.i,
                    )
                ]
                + [
                    FineosExtractVpeiPaymentLineFactory.build(
                        amount_monamt=-child.amount,
                        linetype="Child Payment Reduction",
                        paymentdetailclassid=detail.c,
                        paymentdetailindexid=detail.i,
                        c_pymnteif_paymentlines=vpei.c,
                        i_pymnteif_paymentlines=vpei.i,
                    )
                    for child in self.child_payments
                ]
            ),
            fineos_extract_vbi_requested_absence=[
                FineosExtractVbiRequestedAbsenceFactory.build(
                    leaverequest_id=claim_details.leaverequesti,
                    leaverequest_decision="Approved",
                    absence_casecreationdate=datetime.datetime(2025, 1, 1),
                    absencereason_coverage="Employee",
                )
            ],
        )


class OrphanPaymentScenarioData(ScenarioData):

    def __init__(self, parent_payment: Payment, orphan_payment: Payment):
        self.parent_payment = parent_payment
        self.orphan_payment = orphan_payment
        vpei = FineosExtractVpeiFactory.build(
            c=self.orphan_payment.fineos_pei_c_value,
            i=self.orphan_payment.fineos_pei_i_value,
            payeecustomer="FITAMOUNTPAYEE006",  # used to determine who the payer is
            payeeidentifi="ID",
            eventtype="PaymentOut",
            eventreason="Automatic Alternate Payment",
            paymenttype="Recurring",
            payeesocnumbe="FITAMOUNTPAYEE",
            paymentdate=self.orphan_payment.payment_date,
            amount_monamt=self.orphan_payment.amount,
            paymentmethod="Accounting",
        )
        detail = FineosExtractVpeiPaymentDetailsFactory.build(
            peclassid=vpei.c,
            peindexid=vpei.i,
            paymentstartp=self.orphan_payment.period_start_date,
            paymentendper=self.orphan_payment.period_end_date,
            balancingamou_monamt=self.orphan_payment.amount,
            businessnetbe_monamt=self.orphan_payment.amount,
        )
        claim_details = FineosExtractVpeiClaimDetailsFactory.build(
            peclassid=vpei.c,
            peindexid=vpei.i,
            absencecasenu=self.parent_payment.claim.fineos_absence_id,
        )
        super().__init__(
            fineos_extract_vpei=[vpei],
            fineos_extract_vpei_claim_details=[claim_details],
            fineos_extract_vpei_payment_details=[detail],
            fineos_extract_vpei_payment_line=[
                FineosExtractVpeiPaymentLineFactory.build(
                    amount_monamt=self.orphan_payment.amount,
                    linetype="Child Payment",
                    paymentdetailclassid=detail.c,
                    paymentdetailindexid=detail.i,
                    c_pymnteif_paymentlines=vpei.c,
                    i_pymnteif_paymentlines=vpei.i,
                )
            ],
            fineos_extract_vbi_requested_absence=[
                FineosExtractVbiRequestedAbsenceFactory.build(
                    leaverequest_id=claim_details.leaverequesti,
                    leaverequest_decision="Approved",
                    absence_casecreationdate=datetime.datetime(2025, 1, 1),
                    absencereason_coverage="Employee",
                )
            ],
        )


DEFAULT_STEPS = ["--steps", "ALL"]


def generate_rejects_file(s3_config, audit_data, payment_options):
    rejects_file_received_path = os.path.join(
        s3_config.dfml_response_inbound_path, "Payment-Audit-Report-Response.csv"
    )

    csv_file = file_util.write_file(rejects_file_received_path)
    csv_output = csv.DictWriter(
        csv_file,
        fieldnames=asdict(PAYMENT_AUDIT_CSV_HEADERS).values(),
        lineterminator="\n",
        quotechar='"',
        quoting=csv.QUOTE_ALL,
    )
    csv_output.writeheader()

    for parsed_audit_row in audit_data:
        c_value = parsed_audit_row[PAYMENT_AUDIT_CSV_HEADERS.c_value]
        i_value = parsed_audit_row[PAYMENT_AUDIT_CSV_HEADERS.i_value]
        payment_option = payment_options.get((c_value, i_value), {})
        parsed_audit_row[PAYMENT_AUDIT_CSV_HEADERS.rejected_by_program_integrity] = (
            payment_option["reject"] or "N"
        )
        parsed_audit_row[PAYMENT_AUDIT_CSV_HEADERS.skipped_by_program_integrity] = (
            payment_option["skip"] or "N"
        )
        parsed_audit_row[PAYMENT_AUDIT_CSV_HEADERS.rejected_notes] = payment_option["notes"] or ""

    csv_output.writerows(audit_data)
    csv_file.close()


def test_audit_orphan_payments(
    initialize_factories_session,
    initialize_feature_config,
    mock_payments_s3_config,
    test_db_session,
    monkeypatch,
):

    monkeypatch.setenv("enable_payment_line_matching", "1")
    # Create an empty reference file for the SyncEntitlementPeriodsToBenefitYearsStep
    ReferenceFileFactory.create(
        reference_file_type_id=ReferenceFileType.FINEOS_VBI_ENTITLEMTPERIOD_SOM_EXTRACT.reference_file_type_id
    )

    employee = EmployeeFactory()
    pub_eft = PubEftFactory.create(
        routing_nbr="*********",
        account_nbr="*********",
        prenote_state_id=PrenoteState.APPROVED.prenote_state_id,
    )
    EmployeePubEftPairFactory.create(
        employee=employee,
        pub_eft=pub_eft,
    )
    employer = EmployerFactory()

    approved_claim = ClaimFactory.create_with_leave_request_and_absence_period(
        employee=employee,
        employer=employer,
        is_id_proofed=True,
    )
    rejected_claim = ClaimFactory.create_with_leave_request_and_absence_period(
        employee=employee,
        employer=employer,
        is_id_proofed=True,
    )

    # The difference between Factory.build and Factory.create is that build does not
    # save the object to the database. This is because we want the PaymentExtractStep
    # to create the actual payment objects.
    approved_payment = PaymentFactory.build(
        employee=employee,
        claim=approved_claim,
        payment_date=datetime.datetime(2025, 1, 10),
        period_start_date=datetime.datetime(2025, 1, 1),
        period_end_date=datetime.datetime(2025, 1, 10),
        amount=1000.00,
    )
    approved_child = PaymentFactory.build(
        employee=employee,
        claim=approved_claim,
        payment_date=datetime.datetime(2025, 1, 10),
        period_start_date=datetime.datetime(2025, 1, 1),
        period_end_date=datetime.datetime(2025, 1, 10),
        payment_transaction_type_id=PaymentTransactionType.FEDERAL_TAX_WITHHOLDING.payment_transaction_type_id,
        amount=50.00,
    )

    rejected_payment = PaymentFactory.build(
        employee=employee,
        claim=rejected_claim,
        payment_date=datetime.datetime(2025, 1, 10),
        period_start_date=datetime.datetime(2025, 1, 1),
        period_end_date=datetime.datetime(2025, 1, 10),
        amount=1000.00,
    )
    rejected_child = PaymentFactory.build(
        employee=employee,
        claim=rejected_claim,
        period_start_date=datetime.datetime(2025, 1, 1),
        period_end_date=datetime.datetime(2025, 1, 10),
        payment_transaction_type_id=PaymentTransactionType.FEDERAL_TAX_WITHHOLDING.payment_transaction_type_id,
        amount=50.00,
    )
    data = StandardPaymentScenarioData(
        approved_payment, pub_eft, [approved_child]
    ) + StandardPaymentScenarioData(rejected_payment, pub_eft, [rejected_child])
    data.write(mock_payments_s3_config, datetime.datetime.now())

    # Freezing the time to simulate the date of the extract
    # This will ensure that the CreatePubFileTaskRunner creates
    # different extract then the FineosExtractTaskRunner
    with freeze_time("2021-05-02 21:00:00", tz_offset=5):

        task_runner = FineosExtractTaskRunner(mock_payments_s3_config, DEFAULT_STEPS)
        task_runner.run_steps(test_db_session, test_db_session)

    new_approved_payment = (
        test_db_session.query(Payment)
        .filter(
            Payment.fineos_pei_c_value == approved_payment.fineos_pei_c_value,
            Payment.fineos_pei_i_value == approved_payment.fineos_pei_i_value,
        )
        .one()
    )
    new_rejected_payment = (
        test_db_session.query(Payment)
        .filter(
            Payment.fineos_pei_c_value == rejected_payment.fineos_pei_c_value,
            Payment.fineos_pei_i_value == rejected_payment.fineos_pei_i_value,
        )
        .one()
    )

    # Both Payments should have been sent to on the Audit Report
    assert (
        new_approved_payment.latest_state_log.state_log.end_state.state_description
        == "Payment Audit Report sent"
    )
    assert (
        new_rejected_payment.latest_state_log.state_log.end_state.state_description
        == "Payment Audit Report sent"
    )

    payment_audit_report_outbound_folder_path = mock_payments_s3_config.dfml_report_outbound_path
    audit_report_file_name = "Payment-Audit-Report.csv"
    audit_report_file_path = os.path.join(
        payment_audit_report_outbound_folder_path, audit_report_file_name
    )
    parsed_csv = csv.DictReader(file_util.open_stream(audit_report_file_path))
    audit_data = list(parsed_csv)

    generate_rejects_file(
        mock_payments_s3_config,
        audit_data,
        {
            (new_approved_payment.fineos_pei_c_value, new_approved_payment.fineos_pei_i_value): {
                "reject": "N",
                "skip": "N",
                "notes": "",
            },
            (new_rejected_payment.fineos_pei_c_value, new_rejected_payment.fineos_pei_i_value): {
                "reject": "Y",
                "skip": "N",
                "notes": "rejected for testing purposes",
            },
        },
    )

    with freeze_time("2021-05-02 22:00:00", tz_offset=5):
        task_runner = CreatePubFileTaskRunner(mock_payments_s3_config, DEFAULT_STEPS)
        task_runner.run_steps(test_db_session, test_db_session)

    assert (
        new_approved_payment.latest_state_log.state_log.end_state.state_description
        == "PUB Transaction sent - EFT"
    )
    assert (
        new_rejected_payment.latest_state_log.state_log.end_state.state_description
        == "Add to Payment Reject Report"
    )

    # Standard Payments have now been paid and rejected

    # Day 2
    data = OrphanPaymentScenarioData(approved_payment, approved_child) + OrphanPaymentScenarioData(
        rejected_payment, rejected_child
    )
    data.write(mock_payments_s3_config, datetime.datetime.now())

    with freeze_time("2021-05-03 0:00:00", tz_offset=5):
        task_runner = FineosExtractTaskRunner(mock_payments_s3_config, DEFAULT_STEPS)
        task_runner.run_steps(test_db_session, test_db_session)

    new_approved_child = (
        test_db_session.query(Payment)
        .filter(
            Payment.fineos_pei_c_value == approved_child.fineos_pei_c_value,
            Payment.fineos_pei_i_value == approved_child.fineos_pei_i_value,
        )
        .one()
    )
    new_rejected_child = (
        test_db_session.query(Payment)
        .filter(
            Payment.fineos_pei_c_value == rejected_child.fineos_pei_c_value,
            Payment.fineos_pei_i_value == rejected_child.fineos_pei_i_value,
        )
        .one()
    )

    # Both Orphans should have been sent to on the Audit Report
    assert (
        new_approved_child.latest_state_log.state_log.end_state.state_description
        == "Payment Audit Report sent"
    )
    assert (
        new_rejected_child.latest_state_log.state_log.end_state.state_description
        == "Payment Audit Report sent"
    )

    audit_report_file_name = "Payment-Audit-Report.csv"
    audit_report_file_path = os.path.join(
        payment_audit_report_outbound_folder_path, audit_report_file_name
    )
    parsed_csv = csv.DictReader(file_util.open_stream(audit_report_file_path))
    audit_data = list(parsed_csv)

    approved_child_audit_row = next(
        (
            row
            for row in audit_data
            if (
                row[PAYMENT_AUDIT_CSV_HEADERS.pfml_payment_id] == str(new_approved_child.payment_id)
            )
        ),
        None,
    )
    rejected_child_audit_row = next(
        (
            row
            for row in audit_data
            if (
                row[PAYMENT_AUDIT_CSV_HEADERS.pfml_payment_id] == str(new_rejected_child.payment_id)
            )
        ),
        None,
    )

    assert (
        approved_child_audit_row[PAYMENT_AUDIT_CSV_HEADERS.related_parent_payment_status]
        == "Main Payment Previously Paid"
    )
    assert (
        rejected_child_audit_row[PAYMENT_AUDIT_CSV_HEADERS.related_parent_payment_status]
        == "Main Payment Previously Rejected"
    )
