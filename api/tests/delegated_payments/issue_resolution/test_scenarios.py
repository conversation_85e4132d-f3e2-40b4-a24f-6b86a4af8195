import pytest

from massgov.pfml.delegated_payments.audit.audit_writeback_util import RejectNote
from massgov.pfml.delegated_payments.delegated_payments_util import (
    ValidationIssue,
    ValidationReason,
)
from massgov.pfml.delegated_payments.issue_resolution.scenarios import (
    DIA_ADDITIONAL_INCOME_RESOLUTION,
    DUA_ADDITIONAL_INCOME_RESOLUTION,
    FAILED_AUTOMATED_VALIDATION_RESOLUTION,
    INVALID_PAYMENT_DATE_PAID_RESOLUTION,
    PAYMENT_AUDIT_ERROR_RESOLUTION,
    get_issue_resolutions_for_reject_notes,
)


def test_get_description_for_reasons_none():

    # No validation issues present
    assert FAILED_AUTOMATED_VALIDATION_RESOLUTION.get_description_for_issues([]) == ""

    # Issue present but not in failed automated validation group
    issue = ValidationIssue(ValidationReason.CLAIM_NOT_ID_PROOFED, None, None)
    assert FAILED_AUTOMATED_VALIDATION_RESOLUTION.get_description_for_issues([issue]) == ""


def test_get_description_for_reasons_multiple_reasons():

    issues = [
        # Appear in alphabetical order
        ValidationIssue(ValidationReason.MISSING_FIELD, None, "Field"),
        ValidationIssue(ValidationReason.MISSING_FIELD, None, "Another field"),
        ValidationIssue(ValidationReason.FIELD_TOO_SHORT, None, "Field 3"),
        # Doesn't appear in field names list
        ValidationIssue(ValidationReason.FIELD_TOO_SHORT, None, None),
        # Shows up but without field names
        ValidationIssue(ValidationReason.OPEN_FRAUD_TASKS, None),
    ]

    assert (
        FAILED_AUTOMATED_VALIDATION_RESOLUTION.get_description_for_issues(issues)
        == "FieldTooShort: Field 3\nMissingField: Another field, Field\nOpenFraudTasks"
    )


@pytest.mark.parametrize(
    "rejected_notes, expected_issue_resolution_scenarios, is_rejected",
    [
        [RejectNote.DUA_ADDITIONAL_INCOME.value, [DUA_ADDITIONAL_INCOME_RESOLUTION], True],
        [RejectNote.DIA_ADDITIONAL_INCOME.value, [DIA_ADDITIONAL_INCOME_RESOLUTION], True],
        [RejectNote.ALREADY_PAID_FOR_DATES.value, [INVALID_PAYMENT_DATE_PAID_RESOLUTION], True],
        [
            RejectNote.OTHER,
            [PAYMENT_AUDIT_ERROR_RESOLUTION],
            True,
        ],
        [
            "Some Reject Reason without mapped issue resolution",
            [],
            True,
        ],
        ["Some Reject Reason without mapped issue resolution", [], False],
        ["", [], True],
        ["", [], False],
        [None, [], False],
    ],
)
def test_get_issue_resolutions_for_reject_notes(
    rejected_notes, expected_issue_resolution_scenarios, is_rejected
):
    scenarios = get_issue_resolutions_for_reject_notes(rejected_notes, is_rejected)
    assert scenarios == expected_issue_resolution_scenarios
