from re import <PERSON><PERSON>
from typing import Any, Optional, Union
from unittest.mock import <PERSON><PERSON>ock

import pytest


def get_mock_logger():
    mock_logger = MagicMock()
    mock_logger.info = MagicMock()
    mock_logger.warning = MagicMock()
    mock_logger.error = MagicMock()

    return mock_logger


def assert_log_contains(
    caplog: pytest.LogCaptureFixture,
    msg: Union[Pattern, str],
    data: Optional[dict[str, Any]] = None,
) -> None:
    """Ensure a particular message, and optionally particular attributes with it, were logged

    Args:
        caplog: The fixture used for the test.
        msg: The log message to look for. If a plain string, will check for exact match. If a regex, will search for a match.
        data: Any "extra" attributes to assert are present in found log message
    """
    found_message = False

    for record in caplog.records:
        if match_str(record.message, msg):
            found_message = True

            if data is not None:
                assert data.items() <= record.__dict__.items()

            break

    if not found_message:
        raise AssertionError(f"Did not find expected log message '{msg}'")


def match_str(haystack: str, needle: Union[Pattern, str]) -> bool:
    """Match with regex or exact string"""
    if isinstance(needle, Pattern):
        return bool(needle.search(haystack))

    return haystack == needle


def assert_log_has_match(
    caplog: pytest.LogCaptureFixture,
    msg: Union[Pattern, str],
    data: Optional[dict[str, Any]] = None,
) -> None:
    """Ensure a particular message, and optionally particular attributes with it, were logged

    Args:
        caplog: The fixture used for the test.
        msg: The log message to look for. If a plain string, will check for exact match. If a regex, will search for a match.
        data: Any "extra" attributes to assert are present in found log message
    """
    found_message = False

    for record in caplog.records:
        if match_str(record.message, msg):

            if data is not None:
                if data.items() <= record.__dict__.items():
                    found_message = True
                    break
            else:
                found_message = True
                break

    if not found_message:
        raise AssertionError(f"Did not find expected log message '{msg}'")
