#
# Database related test helpers.
#

import datetime
import sys
import uuid

import rich.console
import rich.pretty
import rich.table

import massgov.pfml.db.models.base

TERMINAL_WIDTH = 200
MAXIMUM_ROWS = 100
MAXIMUM_COLUMNS_FOR_COMPACT_OUTPUT = 12


def dump_tables(db_session, stream=sys.stdout):
    """Print contents of all tables in db_session to an output stream."""
    metadata_tables = massgov.pfml.db.models.base.Base.metadata.tables
    console = rich.console.Console(file=stream, width=TERMINAL_WIDTH, color_system="256")
    for table_name, db_table in sorted(metadata_tables.items()):
        if table_name.startswith("lk_"):
            continue
        query = db_session.query(db_table).order_by(db_table.columns.keys()[0]).limit(MAXIMUM_ROWS)
        query_count = query.count()
        if query_count == 0:
            continue

        output_table = render_table(db_table.columns.keys(), query)
        output_table.title = (
            f"{table_name} (1-{query_count} of {db_session.query(db_table).count()})"
        )
        console.print(output_table)


def render_table(columns, query):
    """Render the rows in the given query as a rich table."""
    output_table = rich.table.Table(show_header=False)
    if len(columns) <= MAXIMUM_COLUMNS_FOR_COMPACT_OUTPUT:
        for row in query:
            output_table.add_row(*map(format_database_value, row))
    else:
        for row in query:
            for name, value in zip(columns, row):
                output_table.add_row(name, format_database_value(value))
            output_table.add_section()
    return output_table


def format_database_value(value):
    """Format a value in a compact way."""
    if isinstance(value, datetime.datetime):
        return value.strftime("%Y%m%d %X.%f")
    elif isinstance(value, datetime.date):
        return str(value)
    elif isinstance(value, str):
        return value
    elif isinstance(value, uuid.UUID):
        return str(value)
    return rich.pretty.Pretty(value)
