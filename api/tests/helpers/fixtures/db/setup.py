import io
import os
import sys
import traceback
import uuid

import pytest
import sqlalchemy
from sqlalchemy.orm import sessionmaker

import massgov.pfml.util.logging
import tests.helpers.db

logger = massgov.pfml.util.logging.get_logger(f"massgov.{__name__}")


@pytest.fixture
def mock_db_session(mocker):
    return mocker.patch("sqlalchemy.orm.Session", autospec=True)


def _db_schema_fixture(request: pytest.FixtureRequest, monkeypatch: pytest.MonkeyPatch) -> str:
    """
    Create a test schema, if it doesn't already exist, and drop it after the
    fixture goes out of scope (i.e., at end of test session, after individual
    test, etc.).
    """
    schema_name = f"api_test_{uuid.uuid4().int}"

    monkeypatch.setenv("DB_SCHEMA", schema_name)

    _db_schema_create(schema_name)

    request.addfinalizer(lambda: _db_schema_drop(schema_name))

    return schema_name


def _db_schema_create(schema_name):
    """Create a database schema."""
    db_config = massgov.pfml.db.config.get_config()
    db_test_user = db_config.username

    _exec_sql_admin(f"CREATE SCHEMA IF NOT EXISTS {schema_name} AUTHORIZATION {db_test_user};")
    logger.info("create schema %s", schema_name)


def _db_schema_drop(schema_name):
    """Drop a database schema."""
    _exec_sql_admin(f"DROP SCHEMA {schema_name} CASCADE;")
    logger.info("drop schema %s", schema_name)


def _exec_sql_admin(sql):
    db_admin_config = massgov.pfml.db.config.get_config(prefer_admin=True)
    engine = massgov.pfml.db.create_engine(db_admin_config)
    with engine.begin() as connection:
        connection.execute(sqlalchemy.text(sql))


@pytest.fixture(scope="session")
def db_schema_scope_session(has_external_dependencies, monkeypatch_session, request):
    return _db_schema_fixture(request, monkeypatch_session)


@pytest.fixture(scope="session")
def test_db(db_schema_scope_session):
    """
    Creates a test schema, directly creating all tables with SQLAlchemy. Schema
    is dropped after the test completes.
    """

    # not used directly, but loads models into Base
    import massgov.pfml.db as db
    import massgov.pfml.db.models.applications as applications  # noqa: F401
    import massgov.pfml.db.models.employees as employees  # noqa: F401
    from massgov.pfml.db.models.base import Base

    engine = db.create_engine()
    Base.metadata.create_all(bind=engine)

    db_session = db.init(sync_lookups=True)
    db_session.close()
    # db.init() does return a scoped_session, but is typed as a plain Session
    # for simplicity elsewhere, so ignore type issue
    db_session.remove()  # type: ignore

    return engine


@pytest.fixture(scope="session")
def _db_session_maker(test_db):
    return sessionmaker(autocommit=False, expire_on_commit=False)


@pytest.fixture
def test_db_session(test_db, _db_session_maker, request):
    # Based on https://docs.sqlalchemy.org/en/20/orm/session_transaction.html#joining-a-session-into-an-external-transaction-such-as-for-test-suites
    connection = test_db.connect()
    trans = connection.begin()
    session = _db_session_maker(bind=connection, join_transaction_mode="create_savepoint")

    yield session

    if request.session.testsfailed:
        try:
            stream = io.StringIO()
            tests.helpers.db.dump_tables(session, stream)
            request.node.add_report_section(
                "teardown", "test_db_session table dump", stream.getvalue()
            )
        except Exception as ex:
            print(f"caught exception when dumping database tables: {ex!r}", file=sys.stderr)
            traceback.print_exc()

    session.close()
    trans.rollback()
    connection.close()


@pytest.fixture
def test_db_other_session(test_db, _db_session_maker):
    # Based on https://docs.sqlalchemy.org/en/20/orm/session_transaction.html#joining-a-session-into-an-external-transaction-such-as-for-test-suites
    connection = test_db.connect()
    trans = connection.begin()
    session = _db_session_maker(bind=connection, join_transaction_mode="create_savepoint")

    yield session

    session.close()
    trans.rollback()
    connection.close()


@pytest.fixture
def initialize_factories_session(monkeypatch, test_db_session):
    monkeypatch.delenv("DB_FACTORIES_DISABLE_DB_ACCESS")

    import massgov.pfml.db.models.factories as factories

    logger.info("set factories db_session to %s", test_db_session)
    factories.db_session = test_db_session


@pytest.fixture
def db_schema_scope_function(has_external_dependencies, monkeypatch, request):
    return _db_schema_fixture(request, monkeypatch)


@pytest.fixture
def local_test_db(db_schema_scope_function):
    """
    Creates a test schema, directly creating all tables with SQLAlchemy. Schema
    is dropped after the test completes.
    """

    # not used directly, but loads models into Base
    import massgov.pfml.db as db
    import massgov.pfml.db.models.applications as applications  # noqa: F401
    import massgov.pfml.db.models.employees as employees  # noqa: F401
    import massgov.pfml.db.models.payments as payments  # noqa: F401
    from massgov.pfml.db.models.base import Base

    engine = db.create_engine()
    Base.metadata.create_all(bind=engine)

    db_session = db.init(sync_lookups=True, check_migrations_current=False)
    db_session.close()
    db_session.remove()

    return engine


@pytest.fixture
def local_test_db_session(local_test_db):
    import massgov.pfml.db as db

    db_session = db.init(sync_lookups=False, check_migrations_current=False)

    yield db_session

    db_session.close()
    db_session.remove()


@pytest.fixture
def local_test_db_other_session(local_test_db):
    import massgov.pfml.db as db

    db_session = db.init(sync_lookups=False, check_migrations_current=False)

    yield db_session

    db_session.close()
    db_session.remove()


@pytest.fixture
def local_initialize_factories_session(monkeypatch, local_test_db_session):
    monkeypatch.delenv("DB_FACTORIES_DISABLE_DB_ACCESS")
    import massgov.pfml.db.models.factories as factories

    logger.info("set factories db_session to %s", local_test_db_session)
    factories.db_session = local_test_db_session


@pytest.fixture
def test_db_via_migrations(has_external_dependencies, db_schema_scope_function, logging_fix):
    """
    Creates a test schema, runs migrations through Alembic. Schema is dropped
    after the test completes.
    """
    from pathlib import Path

    from alembic import command
    from alembic.config import Config

    alembic_cfg = Config(
        os.path.join(
            os.path.dirname(__file__), "../../../../massgov/pfml/db/migrations/alembic.ini"
        )
    )
    # Change directory location so the relative script_location in alembic config works.
    os.chdir(Path(__file__).parent.parent.parent.parent.parent)
    command.upgrade(alembic_cfg, "head")

    return db_schema_scope_function


@pytest.fixture
def test_db_session_via_migrations(test_db_via_migrations):
    import massgov.pfml.db as db

    db_session = db.init()

    yield db_session

    db_session.close()
    db_session.remove()


@pytest.fixture
def initialize_factories_session_via_migrations(test_db_session_via_migrations):
    import massgov.pfml.db.models.factories as factories

    factories.db_session = test_db_session_via_migrations


@pytest.fixture(scope="module")
def module_persistent_db(has_external_dependencies, monkeypatch_module, request):
    # not used directly, but loads models into Base
    import massgov.pfml.db as db
    import massgov.pfml.db.models.applications as applications  # noqa: F401
    import massgov.pfml.db.models.employees as employees  # noqa: F401
    from massgov.pfml.db.models.base import Base

    schema_name = f"api_test_persistent_{uuid.uuid4().int}"
    logger.info("use persistent test db for module %s", request.module.__name__)

    monkeypatch_module.setenv("DB_SCHEMA", schema_name)
    _db_schema_create(schema_name)

    engine = db.create_engine()
    Base.metadata.create_all(bind=engine)

    db_session = db.init(sync_lookups=True)

    try:
        yield schema_name
    finally:
        db_session.close()
        db_session.remove()
        _db_schema_drop(schema_name)


@pytest.fixture
def module_persistent_db_session(module_persistent_db, monkeypatch):
    import massgov.pfml.db as db
    import massgov.pfml.db.models.factories as factories

    db_session = db.init(sync_lookups=False)

    monkeypatch.delenv("DB_FACTORIES_DISABLE_DB_ACCESS")

    logger.info("set factories db_session to %s", db_session)
    factories.db_session = db_session

    yield db_session

    db_session.close()
    db_session.remove()
