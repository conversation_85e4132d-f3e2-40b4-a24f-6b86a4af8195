import copy
from datetime import date

import pytest

from massgov.pfml.db.lookup_data.applications import EmploymentStatus, Ethnicity, LeaveReason, Race
from massgov.pfml.db.lookup_data.change_request import ChangeRequestType
from massgov.pfml.db.lookup_data.documents import DocumentType
from massgov.pfml.db.lookup_data.employees import BankAccountType, Gender, PaymentMethod, Role
from massgov.pfml.db.lookup_data.phone import PhoneType
from massgov.pfml.db.models.factories import (
    AddressFactory,
    ApplicationFactory,
    ApplicationUserNotFoundInfoFactory,
    ChangeRequestFactory,
    ClaimFactory,
    ContinuousLeavePeriodFactory,
    DocumentFactory,
    EmployeeFactory,
    EmployeeWithFineosNumberFactory,
    EmployerFactory,
    PaymentPreferenceFactory,
    PhoneFactory,
    TaxIdentifierFactory,
    UserFactory,
    WagesAndContributionsFactory,
    WorkPatternFixedFactory,
)
from tests.dor.importer import dor_test_data as test_data


@pytest.fixture
def employee_factory(initialize_factories_session):
    return EmployeeWithFineosNumberFactory.create


@pytest.fixture
def claim_factory(initialize_factories_session):
    return ClaimFactory.create


@pytest.fixture
def employee_wage_factory():
    def factory(data=None, **kwargs):
        if not data:
            data = {}
        wage_data = copy.deepcopy(test_data.get_new_employee_wage_data())
        if "employee" in kwargs:
            employee = kwargs["employee"]
            wage_data = {
                **wage_data,
                "employee_first_name": employee.first_name,
                "employee_last_name": employee.last_name,
                "employee_ssn": employee.tax_identifier.tax_identifier,
            }
        if "employer" in kwargs:
            employer = kwargs["employer"]
            wage_data = {
                **wage_data,
                "account_key": employer.account_key,
            }

        return {**wage_data, **data}

    return factory


@pytest.fixture
def user(initialize_factories_session):
    user = UserFactory.create()
    return user


@pytest.fixture
def consented_user(initialize_factories_session):
    user = UserFactory.create(consented_to_data_sharing=True)
    return user


@pytest.fixture
def fineos_user(initialize_factories_session):
    user = UserFactory.create(roles=[Role.FINEOS])
    return user


@pytest.fixture
def idp_user(initialize_factories_session):
    user = UserFactory.create(roles=[Role.IDP])
    return user


@pytest.fixture
def snow_user(initialize_factories_session):
    user = UserFactory.create(roles=[Role.PFML_CRM])
    return user


@pytest.fixture
def employer_user(initialize_factories_session):
    user = UserFactory.create(roles=[Role.EMPLOYER])
    return user


@pytest.fixture
def employer(initialize_factories_session):
    return EmployerFactory.create(employer_fein="*********")


@pytest.fixture
def tax_identifier(initialize_factories_session):
    return TaxIdentifierFactory.create(tax_identifier="*********")


@pytest.fixture
def employee(tax_identifier):
    return EmployeeFactory.create(tax_identifier_id=tax_identifier.tax_identifier_id)


@pytest.fixture
def claim(employer, employee, application):
    return ClaimFactory.create(
        employer=employer,
        employee=employee,
        application=application,
        fineos_absence_status_id=1,
        claim_type_id=1,
        fineos_absence_id="foo",
    )


@pytest.fixture
def application(user):
    return ApplicationFactory.create(user=user)


@pytest.fixture
def application_with_claim(claim):
    return claim.application


@pytest.fixture
def application_with_wages(user, employer, employee, initialize_factories_session):
    WagesAndContributionsFactory.create(
        employee=employee,
        employer=employer,
        filing_period=date(2019, 7, 1),
        employee_qtr_wages=1000,
    )
    return ApplicationFactory.create(
        user=user,
        employer_fein=employer.employer_fein,
        tax_identifier=employee.tax_identifier,
    )


@pytest.fixture
def application_additional_user_not_found_info(user):
    application = ApplicationFactory.create(
        continuous_leave_periods=[
            ContinuousLeavePeriodFactory.create(
                start_date=date(2022, 1, 1), end_date=date(2022, 2, 1)
            )
        ],
        date_of_birth=date(1977, 7, 27),
        employer_fein="*********",
        employer_notification_date=date(2021, 1, 3),
        employer_notified=True,
        employment_status_id=EmploymentStatus.EMPLOYED.employment_status_id,
        first_name="First",
        gender_id=Gender.WOMAN.gender_id,
        race_id=Race.ANOTHER_RACE_NOT_LISTED_ABOVE.race_id,
        race_custom="Custom",
        ethnicity_id=Ethnicity.HISPANIC_OR_LATINO.ethnicity_id,
        has_continuous_leave_periods=True,
        hours_worked_per_week=38,
        is_withholding_tax=False,
        last_name="Last",
        leave_reason_id=LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id,
        mass_id="*********",
        middle_name="Middle",
        payment_preference=PaymentPreferenceFactory.create(
            account_number="*********",
            bank_account_type_id=BankAccountType.CHECKING.bank_account_type_id,
            payment_method_id=PaymentMethod.CHECK.payment_method_id,
            routing_number="*********",
        ),
        phone=PhoneFactory.create(
            phone_number="+***********", phone_type_id=PhoneType.CELL.phone_type_id
        ),
        residential_address=AddressFactory.create(
            address_line_one="123 Fake St", city="Boston", zip_code="02108"
        ),
        user=user,
        work_pattern=WorkPatternFixedFactory.build(),
    )
    application.additional_user_not_found_info = ApplicationUserNotFoundInfoFactory.create(
        application_id=application.application_id,
        currently_employed=False,
        date_of_hire=date(2020, 1, 1),
        date_of_separation=date(2021, 6, 1),
        employer_name="Fake Employer",
        recently_acquired_or_merged=False,
        submitted_time=None,
    )
    return application


@pytest.fixture
def change_request(claim, initialize_factories_session):
    return ChangeRequestFactory.create(
        claim=claim,
        change_request_type_id=ChangeRequestType.EXTENSION.change_request_type_id,
    )


@pytest.fixture
def document(user, application, initialize_factories_session):
    return DocumentFactory.create(
        user_id=user.user_id,
        application_id=application.application_id,
        document_type_id=DocumentType.APPROVAL_NOTICE.document_type_id,
    )
