#
# Tests for massgov.pfml.util.logging.applications
#
from datetime import datetime

import pytest
from freezegun import freeze_time

from massgov.pfml.api.models.applications.requests import ApplicationRequestBody
from massgov.pfml.db.lookup_data.applications import LeaveReason, LeaveReasonQualifier
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.factories import (
    ApplicationFactory,
    ApplicationUserNotFoundInfoFactory,
    ContinuousLeavePeriodFactory,
    EmployerBenefitFactory,
    IntermittentLeavePeriodFactory,
    OtherIncomeFactory,
    PreviousLeaveFactory,
    ReducedScheduleLeavePeriodFactory,
)
from massgov.pfml.fineos.models.customer_api import AbsencePeriod
from massgov.pfml.util.logging.applications import (
    get_absence_period_log_attributes,
    get_app_progress_attributes,
    get_application_log_attributes,
)


def get_application_expected_attributes(application):
    return {
        "absence_case_id": None,
        "application.absence_case_id": None,
        "application.application_id": str(application.application_id),
        "application.completed_time": None,
        "application.completed_time.timestamp": None,
        "application.has_continuous_leave_periods": "False",
        "application.has_employer_benefits": "False",
        "application.has_full_wage_replacement": "False",
        "application.has_future_child_date": None,
        "application.has_intermittent_leave_periods": "False",
        "application.has_mailing_address": "False",
        "application.has_other_incomes": "False",
        "application.has_previous_leaves": "False",
        "application.has_reduced_schedule_leave_periods": "False",
        "application.has_state_id": "False",
        "application.has_submitted_payment_preference": None,
        "application.industry_sector": None,
        "application.leave_reason": "Serious Health Condition - Employee",
        "application.leave_reason_qualifier": None,
        "application.num_employer_benefits": "1",
        "application.num_other_incomes": "1",
        "application.num_employer_benefit_types.Accrued paid leave": "0",
        "application.num_employer_benefit_types.Family or medical leave insurance": "0",
        "application.num_employer_benefit_types.Permanent disability insurance": "0",
        "application.num_employer_benefit_types.Short-term disability insurance": "0",
        "application.num_employer_benefit_types.Paid time off": "0",
        "application.num_other_income_types.Disability benefits under Gov't retirement plan": "0",
        "application.num_other_income_types.Earnings from another employment/self-employment": "0",
        "application.num_other_income_types.Jones Act benefits": "0",
        "application.num_other_income_types.Railroad Retirement benefits": "0",
        "application.num_other_income_types.SSDI": "1",
        "application.num_other_income_types.Unemployment Insurance": "0",
        "application.num_other_income_types.Workers Compensation": "0",
        "application.organization_unit_id": None,
        "application.pregnant_or_recent_birth": "False",
        "application.created_at": str(application.created_at),
        "application.created_at.timestamp": str(application.created_at.timestamp()),
        "application.submitted_time": None,
        "application.submitted_time.timestamp": None,
        "application.updated_at": str(application.updated_at),
        "application.updated_at.timestamp": str(application.updated_at.timestamp()),
        "work_pattern.work_pattern_type": None,
        "application.is_withholding_tax": None,
        "application.split_from_application_id": None,
        "application.additional_user_not_found_info.employer_name": application.additional_user_not_found_info.employer_name,
        "application.additional_user_not_found_info.date_of_hire": str(
            application.additional_user_not_found_info.date_of_hire
        ),
        "application.additional_user_not_found_info.currently_employed": str(
            application.additional_user_not_found_info.currently_employed
        ),
        "application.additional_user_not_found_info.date_of_separation": str(
            application.additional_user_not_found_info.date_of_separation
        ),
        "application.additional_user_not_found_info.recently_acquired_or_merged": str(
            application.additional_user_not_found_info.recently_acquired_or_merged
        ),
        "application.additional_user_not_found_info.submitted_time": str(
            application.additional_user_not_found_info.submitted_time
        ),
        "application.additional_user_not_found_info.submitted_time.timestamp": str(
            application.additional_user_not_found_info.submitted_time.timestamp()
        ),
    }


def test_get_leave_period_log_attributes(user, test_db_session, initialize_factories_session):
    application = ApplicationFactory.create(
        user=user,
        updated_at=datetime.now(),
        has_continuous_leave_periods=True,
        has_intermittent_leave_periods=True,
        has_reduced_schedule_leave_periods=True,
    )
    continuous_leave_1 = ContinuousLeavePeriodFactory.create(
        application_id=application.application_id
    )
    continuous_leave_2 = ContinuousLeavePeriodFactory.create(
        application_id=application.application_id
    )
    intermittent_leave_1 = IntermittentLeavePeriodFactory.create(
        application_id=application.application_id
    )
    intermittent_leave_2 = IntermittentLeavePeriodFactory.create(
        application_id=application.application_id
    )
    reduced_schedule_leave_1 = ReducedScheduleLeavePeriodFactory.create(
        application_id=application.application_id
    )
    reduced_schedule_leave_2 = ReducedScheduleLeavePeriodFactory.create(
        application_id=application.application_id
    )

    log_attributes = get_application_log_attributes(application)

    expected_attributes = {
        "application.continuous_leave[1].start_date": continuous_leave_1.start_date.isoformat(),
        "application.continuous_leave[1].end_date": continuous_leave_1.end_date.isoformat(),
        "application.continuous_leave[2].start_date": continuous_leave_2.start_date.isoformat(),
        "application.continuous_leave[2].end_date": continuous_leave_2.end_date.isoformat(),
        "application.intermittent_leave[1].start_date": intermittent_leave_1.start_date.isoformat(),
        "application.intermittent_leave[1].end_date": intermittent_leave_1.end_date.isoformat(),
        "application.intermittent_leave[2].start_date": intermittent_leave_2.start_date.isoformat(),
        "application.intermittent_leave[2].end_date": intermittent_leave_2.end_date.isoformat(),
        "application.reduced_schedule_leave[1].start_date": reduced_schedule_leave_1.start_date.isoformat(),
        "application.reduced_schedule_leave[1].end_date": reduced_schedule_leave_1.end_date.isoformat(),
        "application.reduced_schedule_leave[2].start_date": reduced_schedule_leave_2.start_date.isoformat(),
        "application.reduced_schedule_leave[2].end_date": reduced_schedule_leave_2.end_date.isoformat(),
    }

    log_attribute_subset = {
        key: value for key, value in log_attributes.items() if key in expected_attributes
    }

    # log_attributes is a superset of expected_attributes
    assert log_attribute_subset.items() >= expected_attributes.items()


def test_get_absence_period_log_attributes(user, test_db_session):
    absence_details = [
        AbsencePeriod(
            id="PL-14449-0000002237",
            reason="Pregnancy/Maternity",
            reasonQualifier1="Foster Care",
            reasonQualifier2="",
            absenceType="Episodic",
            requestStatus="Pending",
        ),
        AbsencePeriod(
            id="PL-14449-0000002238",
            reason="Child Bonding",
            reasonQualifier1="Foster Care",
            reasonQualifier2="",
            absenceType="Episodic",
            requestStatus="Approved",
        ),
    ]
    log_attributes = get_absence_period_log_attributes(absence_details, absence_details[1])

    expected_attributes = {
        "num_absence_periods": str(len(absence_details)),
        "num_pending_leave_request_decision_status": "1",
        "absence_period_0_reason": "Pregnancy/Maternity",
        "absence_period_0_request_status": "Pending",
        "imported_absence_period_1_reason": "Child Bonding",
        "imported_absence_period_1_request_status": "Approved",
    }

    # log_attributes is a superset of expected_attributes
    assert log_attributes.items() >= expected_attributes.items()


class TestGetApplicationLogAttributes:
    @pytest.fixture
    def application(self, user):
        application: Application = ApplicationFactory.create(user=user, updated_at=datetime.now())

        PreviousLeaveFactory.create(application_id=application.application_id)

        # set the application up with additional data
        EmployerBenefitFactory.create(
            application_id=application.application_id,
            benefit_type_id=None,
            is_full_salary_continuous=False,
        )
        OtherIncomeFactory.create(application_id=application.application_id)
        ApplicationUserNotFoundInfoFactory.create(application_id=application.application_id)

        return application

    def test_basic_attributes(self, application):
        log_attributes = get_application_log_attributes(application)
        expected_attributes = get_application_expected_attributes(application)

        # log_attributes is a superset of expected_attributes
        assert log_attributes.items() >= expected_attributes.items()

    def test_attribute_employer_notified(self, application):
        attributes = get_application_log_attributes(application)
        assert attributes.get("application.employer_notified") == "False"

    def test_attribute_fields_to_use_from_user_profile(self, application, test_db_session):
        application.fields_to_use_from_user_profile = {"gender"}
        test_db_session.commit()
        test_db_session.refresh(application)

        attributes = get_application_log_attributes(application)
        assert attributes.get("application.fields_to_use_from_user_profile") == "['gender']"

    def test_attribute_has_previous_leaves(self, application):
        application.has_previous_leaves = True

        attributes = get_application_log_attributes(application)
        assert attributes.get("application.has_previous_leaves") == "True"

    def test_attribute_num_previous_leaves(self, application):
        # we already have a previous_leave_other_reason; add a same_reason
        PreviousLeaveFactory.create(application_id=application.application_id)

        attributes = get_application_log_attributes(application)

        assert attributes.get("application.num_previous_leaves") == "2"

    def test_attribute_has_concurrent_employers_true(self, application):
        application.has_concurrent_employers = True

        attributes = get_application_log_attributes(application)

        assert attributes.get("application.has_concurrent_employers") == "True"

    def test_attribute_has_concurrent_employers_false(self, application):
        application.has_concurrent_employers = False

        attributes = get_application_log_attributes(application)

        assert attributes.get("application.has_concurrent_employers") == "False"

    def test_attribute_has_concurrent_employers_null(self, application):
        application.has_concurrent_employers = None

        attributes = get_application_log_attributes(application)

        assert attributes.get("application.has_concurrent_employers") is None


class TestGetParentalBondingAttributes:
    @freeze_time("03-01-2025")
    @pytest.fixture
    def application(self, user):
        application: Application = ApplicationFactory.create(
            user=user,
            updated_at=datetime.now(),
            leave_reason_id=LeaveReason.CHILD_BONDING.leave_reason_id,
            leave_reason_qualifier_id=LeaveReasonQualifier.NEWBORN.leave_reason_qualifier_id,
            child_birth_date=datetime.date(datetime(2025, 4, 1)),
        )

        return application

    @freeze_time("03-01-2025")
    def test_bonding_leave_days_before_birth_is_null_if_not_bonding(self, application):
        application.leave_reason_id = LeaveReason.PREGNANCY_MATERNITY.leave_reason_id

        attributes = get_application_log_attributes(application)

        assert attributes.get("application.bonding_leave_days_before_birth") is None

    @freeze_time("03-01-2025")
    def test_bonding_leave_days_before_birth_is_null_if_not_newborn(self, application):
        application.leave_reason_qualifier_id = (
            LeaveReasonQualifier.ADOPTION.leave_reason_qualifier_id
        )

        attributes = get_application_log_attributes(application)

        assert attributes.get("application.bonding_leave_days_before_birth") is None

    @freeze_time("03-01-2025")
    def test_bonding_leave_days_before_birth_is_null_if_no_child_birth_date(self, application):
        application.child_birth_date = None

        attributes = get_application_log_attributes(application)

        assert attributes.get("application.bonding_leave_days_before_birth") is None

    @freeze_time("03-01-2025")
    def test_bonding_leave_days_before_birth_positive_if_prenatal(self, application):
        attributes = get_application_log_attributes(application)

        assert attributes.get("application.bonding_leave_days_before_birth") == "31"

    @freeze_time("05-01-2025")
    def test_bonding_leave_days_before_birth_negative_if_postnatal(self, application):
        attributes = get_application_log_attributes(application)

        assert attributes.get("application.bonding_leave_days_before_birth") == "-30"

    @freeze_time("05-01-2025")
    def test_bonding_leave_days_before_birth_uses_submitted_time(self, application):
        application.submitted_time = datetime(2025, 3, 1)

        attributes = get_application_log_attributes(application)

        assert attributes.get("application.bonding_leave_days_before_birth") == "31"


class TestGetAppProgressAttributes:
    @pytest.fixture
    def application(self, user):
        application: Application = ApplicationFactory.create(user=user, updated_at=datetime.now())

        # application has no progress on the OLB section
        application.has_employer_benefits = None

        return application

    def test_no_olb_progress(self, application):
        application_updates = ApplicationRequestBody()
        application_updates.has_previous_leaves = None

        attributes = get_app_progress_attributes(application, application_updates)
        assert attributes.get("olb_section_start_time") is None

    @freeze_time("2023-05-05")
    def test_olb_progress(self, application):
        application.has_previous_leaves = None

        application_updates = ApplicationRequestBody()
        application_updates.has_previous_leaves = True

        attributes = get_app_progress_attributes(application, application_updates)
        assert attributes.get("olb_section_start_time") == "2023-05-05 00:00:00+00:00"
