from massgov.pfml.db.models.employer_exemptions import EmployerExemptionApplication
from massgov.pfml.db.models.factories import (
    EmployerExemptionApplicationDraftPrivatePlanFactory,
    EmployerExemptionApplicationDraftSelfInsuredFamilyAndMedicalFactory,
)
from massgov.pfml.util.logging.employer_exemptions import (
    get_employer_exemption_application_log_attributes,
)


def validate_log_attributes(
    employer_exemption_application: EmployerExemptionApplication, expected: dict[str, str]
):
    attr_prefix = "employer_exemption_application"
    attr = get_employer_exemption_application_log_attributes(employer_exemption_application)

    for field_name, value in expected.items():
        received = attr.get(f"{attr_prefix}.{field_name}")

        if value is not None:
            assert received == str(value)
        else:
            assert received is None


def test_get_employer_exemption_application_purchased_log_attributes(initialize_factories_session):
    expected = {
        "are_employer_withholdings_within_allowable_amount": None,
        "does_employer_withhold_premiums": None,
        "does_plan_cover_all_employees": None,
        "does_plan_cover_employee_contribution": None,
        "does_plan_cover_former_employees": None,
        "does_plan_favor_paid_leave_benefits": None,
        "does_plan_pay_enough_benefits": None,
        "does_plan_provide_enough_armed_forces_illness_leave": None,
        "does_plan_provide_enough_armed_forces_leave": None,
        "does_plan_provide_enough_bonding_leave": None,
        "does_plan_provide_enough_caring_leave": None,
        "does_plan_provide_enough_leave": None,
        "does_plan_provide_enough_medical_leave": None,
        "does_plan_provide_intermittent_armed_forces_leave": None,
        "does_plan_provide_intermittent_bonding_leave": None,
        "does_plan_provide_intermittent_caring_leave": None,
        "does_plan_provide_intermittent_medical_leave": None,
        "does_plan_provide_pfml_job_protection": None,
        "does_plan_provide_return_to_work_benefits": None,
        "employer_exemption_application_status": "Draft",
        "has_family_exemption": "True",
        "has_medical_exemption": "True",
        "insurance_plan_name": "G1500",
        "insurance_provider_name": "American Fidelity Assurance Company",
        "is_self_insured_plan": "False",
    }

    validate_log_attributes(EmployerExemptionApplicationDraftPrivatePlanFactory.create(), expected)


def test_get_employer_exemption_application_self_insured_log_attributes(
    initialize_factories_session,
):
    expected = {
        "are_employer_withholdings_within_allowable_amount": "True",
        "does_employer_withhold_premiums": "True",
        "does_plan_cover_all_employees": "True",
        "does_plan_cover_employee_contribution": "True",
        "does_plan_cover_former_employees": "True",
        "does_plan_favor_paid_leave_benefits": "True",
        "does_plan_pay_enough_benefits": "True",
        "does_plan_provide_enough_armed_forces_illness_leave": "True",
        "does_plan_provide_enough_armed_forces_leave": "True",
        "does_plan_provide_enough_bonding_leave": "True",
        "does_plan_provide_enough_caring_leave": "True",
        "does_plan_provide_enough_leave": "True",
        "does_plan_provide_enough_medical_leave": "True",
        "does_plan_provide_intermittent_armed_forces_leave": "True",
        "does_plan_provide_intermittent_bonding_leave": "True",
        "does_plan_provide_intermittent_caring_leave": "True",
        "does_plan_provide_intermittent_medical_leave": "True",
        "does_plan_provide_pfml_job_protection": "True",
        "does_plan_provide_return_to_work_benefits": "True",
        "has_family_exemption": "True",
        "has_medical_exemption": "True",
        "insurance_plan_id": None,
        "insurance_provider_id": None,
        "is_legally_acknowledged": "True",
        "is_self_insured_plan": "True",
    }

    validate_log_attributes(
        EmployerExemptionApplicationDraftSelfInsuredFamilyAndMedicalFactory.create(), expected
    )
