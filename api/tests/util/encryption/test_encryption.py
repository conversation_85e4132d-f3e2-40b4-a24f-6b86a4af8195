import io
import tempfile

import gnupg
import pytest
import urllib3

from massgov.pfml.util.encryption import Crypt, GpgCrypt, PassthruCrypt

test_email = "<EMAIL>"
passphrase = "my passphrase"
other_test_email = "<EMAIL>"
other_passphrase = "my other passphrase"


@pytest.fixture
def passthru_crypt():
    return PassthruCrypt()


def test_utf8_encrypt():
    assert PassthruCrypt().encrypt(b"This is a test!", test_email) == b"This is a test!"


def test_utf8_decrypt():
    assert PassthruCrypt().decrypt(b"This is a test!") == b"This is a test!"


def test_utf8_decrypt_stream():
    encoded_str = b"This is a test!"
    assert PassthruCrypt().decrypt_stream(io.BytesIO(encoded_str)) == encoded_str


@pytest.fixture
def gpg_crypt():
    tempdir = tempfile.mkdtemp()
    gpg = gnupg.GPG(gnupghome=tempdir)

    key_input_data = gpg.gen_key_input(name_email=test_email, passphrase=passphrase)
    key = gpg.gen_key(key_input_data)
    private_decryption_key = gpg.export_keys(
        keyids=key.fingerprint, secret=True, passphrase=passphrase
    )

    other_key_input_data = gpg.gen_key_input(
        name_email=other_test_email, passphrase=other_passphrase
    )
    other_key = gpg.gen_key(other_key_input_data)
    other_private_decryption_key = gpg.export_keys(
        keyids=other_key.fingerprint, secret=True, passphrase=other_passphrase
    )

    crypt = GpgCrypt(
        [[private_decryption_key, passphrase], [other_private_decryption_key, other_passphrase]],
        homedir=tempdir,
    )

    return crypt


def test_gpg(gpg_crypt):
    test_str = b"This is a test!"

    encrypted_data = gpg_crypt.encrypt(test_str, test_email)
    assert encrypted_data != test_str

    decrypted_str = gpg_crypt.decrypt(encrypted_data)
    assert decrypted_str == test_str


def test_gpg_decrypt_supports_multiple_keys_and_passphrases(gpg_crypt: Crypt):
    test_str = b"This is a test!"

    encrypted_data = gpg_crypt.encrypt(test_str, test_email)
    decrypted_str = gpg_crypt.decrypt(encrypted_data)
    assert decrypted_str == test_str

    other_encrypted_data = gpg_crypt.encrypt(test_str, other_test_email)
    other_decrypted_str = gpg_crypt.decrypt(other_encrypted_data)
    assert other_decrypted_str == test_str


def test_gpg_decrypt_stream_supports_multiple_keys_and_passphrases(gpg_crypt: Crypt):
    test_str = b"This is a test!"

    encrypted_data = gpg_crypt.encrypt(test_str, test_email)
    encrypted_fileobj = io.BytesIO(encrypted_data)
    decrypted_stream = gpg_crypt.decrypt_stream(encrypted_fileobj)
    assert decrypted_stream == test_str

    other_encrypted_data = gpg_crypt.encrypt(test_str, other_test_email)
    other_encrypted_fileobj = io.BytesIO(other_encrypted_data)
    other_decrypted_stream = gpg_crypt.decrypt_stream(other_encrypted_fileobj)
    assert other_decrypted_stream == test_str


@pytest.mark.parametrize("crypt_fixture", ["passthru_crypt", "gpg_crypt"])
def test_decrypt_stream_buffered_output(crypt_fixture, request):
    crypt: Crypt = request.getfixturevalue(crypt_fixture)
    test_str = b"This is a test!"

    encrypted_data = crypt.encrypt(test_str, test_email)
    encrypted_fileobj = io.BytesIO(encrypted_data)

    decrypted_str = crypt.decrypt_stream(encrypted_fileobj)
    assert decrypted_str == test_str


@pytest.mark.parametrize("crypt_fixture", ["passthru_crypt", "gpg_crypt"])
def test_decrypt_stream_unbuffered_output(crypt_fixture, request, mocker):
    crypt: Crypt = request.getfixturevalue(crypt_fixture)
    test_str = b"This is a test!"

    encrypted_data = crypt.encrypt(test_str, test_email)
    encrypted_fileobj = io.BytesIO(encrypted_data)

    on_data = mocker.Mock(return_value=False)
    crypt.set_on_data(on_data)

    assert crypt.decrypt_stream(encrypted_fileobj) == b""
    assert test_str == b"".join(call.args[0] for call in on_data.call_args_list)


@pytest.mark.parametrize("crypt_fixture", ["passthru_crypt", "gpg_crypt"])
def test_decrypt_on_end(crypt_fixture, request, mocker):
    crypt: Crypt = request.getfixturevalue(crypt_fixture)
    test_str = b"This is a test!"
    encrypted_data = crypt.encrypt(test_str, test_email)
    encrypted_fileobj = io.BytesIO(encrypted_data)
    on_end = mocker.Mock()
    crypt.set_on_end(on_end)

    crypt.decrypt_stream(encrypted_fileobj)

    on_end.assert_called_once()


@pytest.mark.parametrize(
    "exception",
    [
        pytest.param(UnicodeError("Unicode issue"), id="unicode"),
        pytest.param(urllib3.exceptions.ProtocolError("Protocol exception"), id="urllib"),
        pytest.param(Exception("Example exception"), id="generic"),
    ],
)
@pytest.mark.timeout(10)
def test_gpg_decrypt_stream_exception_handling(exception, gpg_crypt, mocker):
    broken_fileobj = mocker.MagicMock()
    broken_fileobj.read = mocker.MagicMock(side_effect=exception)

    with pytest.raises(ValueError):
        gpg_crypt.decrypt_stream(broken_fileobj)


@pytest.mark.parametrize(
    ("input", "expected_output_lines"),
    [
        pytest.param(b"foo\nbar\nbaz\n", [b"foo\n", b"bar\n", b"baz\n"], id="full-lines"),
        pytest.param(b"foo\nbar\nbaz", [b"foo\n", b"bar\n", b"baz"], id="partial-last-line"),
        pytest.param(b"foobarbaz", [b"foobarbaz"], id="no-newline"),
        pytest.param(
            b"\n\n\nfoobarbaz\n", [*((b"\n",) * 3), b"foobarbaz\n"], id="leading-newlines"
        ),
    ],
)
@pytest.mark.parametrize("crypt_fixture", ["passthru_crypt", "gpg_crypt"])
def test_decrypt_stream_iter_lines(
    request: pytest.FixtureRequest,
    mocker,
    crypt_fixture,
    input,
    expected_output_lines,
):
    crypt: Crypt = request.getfixturevalue(crypt_fixture)
    encrypted = crypt.encrypt(input, test_email)

    spy = mocker.spy(crypt, "decrypt_stream")

    decrypted_lines_iter = crypt.decrypt_stream_iter_lines(io.BytesIO(encrypted))
    assert list(decrypted_lines_iter) == expected_output_lines

    # assert that `decrypt_stream` did not buffer its own bytes
    assert spy.spy_return_list == [b""]
