from unittest.mock import Mock

from massgov.pfml.api.validation.exceptions import ValidationErrorDetail
from massgov.pfml.api.validation.validators import _get_error_value


class TestGetErrorValue:
    def test_type_validator_returns_instance(self):
        # Create a mock ValidationErrorDetail with 'type' validator
        error = Mock(spec=ValidationErrorDetail)
        error.validator = "type"
        error.instance = "test_value"

        assert _get_error_value(error) == "test_value"

    def test_enum_validator_returns_instance(self):
        # Create a mock ValidationErrorDetail with 'enum' validator
        error = Mock(spec=ValidationErrorDetail)
        error.validator = "enum"
        error.instance = "enum_value"

        assert _get_error_value(error) == "enum_value"

    def test_format_validator_with_maskable_date_returns_instance(self):
        # Create a mock ValidationErrorDetail with 'format' validator and 'maskable_date' value
        error = Mock(spec=ValidationErrorDetail)
        error.validator = "format"
        error.validator_value = "maskable_date"
        error.instance = "2022-01-01"

        assert _get_error_value(error) == "2022-01-01"

    def test_format_validator_with_date_returns_instance(self):
        # Create a mock ValidationErrorDetail with 'format' validator and 'date' value
        error = Mock(spec=ValidationErrorDetail)
        error.validator = "format"
        error.validator_value = "date"
        error.instance = "2022-01-01"

        assert _get_error_value(error) == "2022-01-01"

    def test_format_validator_with_other_value_returns_none(self):
        # Create a mock ValidationErrorDetail with 'format' validator but other value
        error = Mock(spec=ValidationErrorDetail)
        error.validator = "format"
        error.validator_value = "datetime"
        error.instance = "2022-01-01T12:00:00"

        assert _get_error_value(error) is None

    def test_other_validator_returns_none(self):
        # Create a mock ValidationErrorDetail with different validator
        error = Mock(spec=ValidationErrorDetail)
        error.validator = "required"
        error.instance = "some_value"

        assert _get_error_value(error) is None

    def test_no_validator_attribute_returns_none(self):
        # Create a mock ValidationErrorDetail without validator attribute
        error = Mock(spec=ValidationErrorDetail)
        # No validator attribute set

        assert _get_error_value(error) is None
