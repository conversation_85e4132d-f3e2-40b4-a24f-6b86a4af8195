import pytest

from massgov.pfml.api.validation.formats import is_date, is_maskable_date, is_uuid


def test_is_date_with_non_str():
    assert is_date(123)


def test_is_date_with_date():
    assert is_date("2020-01-29")


def test_is_date_with_invalid_date():
    with pytest.raises(ValueError):
        is_date("2020-00-01")


def test_is_date_with_non_date():
    with pytest.raises(ValueError):
        is_date("hello")

    with pytest.raises(ValueError):
        is_date("****-01-29")


def test_is_maskable_date_with_non_str():
    assert is_maskable_date(123)


def test_is_maskable_date_with_date():
    assert is_maskable_date("2020-01-29")


def test_is_maskable_date_with_leap_day():
    assert is_maskable_date("2000-02-29")


def test_is_maskable_date_with_masked_date():
    assert is_maskable_date("****-01-29")


def test_is_maskable_date_with_masked_leap_day():
    assert is_maskable_date("****-02-29")


def test_is_maskable_date_with_invalid_date():
    with pytest.raises(ValueError):
        is_maskable_date("2020-00-01")


def test_is_maskable_date_with_invalid_leap_day():
    with pytest.raises(ValueError):
        is_maskable_date("2025-02-29")


def test_is_maskable_date_with_non_date():
    with pytest.raises(ValueError):
        is_maskable_date("hello")


def test_is_maskable_date_with_masked_invalid_date():
    with pytest.raises(ValueError):
        is_maskable_date("****-00-29")


def test_is_maskable_date_with_masked_non_date():
    with pytest.raises(ValueError):
        is_maskable_date("****-ab-cd")


def test_is_uuid():
    with pytest.raises(ValueError):
        is_uuid("undefined")


def test_nonstring_is_uuid():
    assert is_uuid(list())
