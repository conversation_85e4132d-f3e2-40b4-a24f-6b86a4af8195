from datetime import date

import pytest
from dateutil.relativedelta import relativedelta
from freezegun import freeze_time

import massgov.pfml.util.datetime as datetime_util
from massgov.pfml.api.validation.change_request_rules import get_change_request_issues
from massgov.pfml.api.validation.exceptions import IssueType
from massgov.pfml.db.lookup_data.absences import AbsenceReason, AbsenceReasonQualifierOne
from massgov.pfml.db.lookup_data.change_request import ChangeRequestType
from massgov.pfml.db.lookup_data.employees import LeaveRequestDecision
from massgov.pfml.db.models.absences import AbsencePeriod
from massgov.pfml.db.models.factories import (
    AbsencePeriodFactory,
    ApplicationFactory,
    ChangeRequestFactory,
    ClaimFactory,
)


# Run `initialize_factories_session` for all tests,
# so that it doesn't need to be manually included
@pytest.fixture(autouse=True)
def setup_factories(initialize_factories_session):
    return


@pytest.fixture
def cancellation_change_request():
    return ChangeRequestFactory.create(
        change_request_type_id=ChangeRequestType.CANCELLATION.change_request_type_id,
        start_date=None,
        end_date=date(2020, 1, 15),
        documents_submitted_at=datetime_util.utcnow(),
    )


@pytest.fixture
def extension_change_request():
    return ChangeRequestFactory.create(
        change_request_type_id=ChangeRequestType.EXTENSION.change_request_type_id,
        start_date=None,
        end_date=date(2020, 3, 1),
        documents_submitted_at=datetime_util.utcnow(),
    )


@pytest.fixture
def withdrawal_change_request():
    return ChangeRequestFactory.create(
        change_request_type_id=ChangeRequestType.WITHDRAWAL.change_request_type_id,
        start_date=None,
        end_date=None,
    )


@pytest.fixture
def mtb_change_request():
    return ChangeRequestFactory.create(
        change_request_type_id=ChangeRequestType.MEDICAL_TO_BONDING.change_request_type_id,
        start_date=date(2020, 2, 1),
        end_date=date(2020, 3, 1),
        date_of_birth=date(2020, 1, 1),
    )


@pytest.fixture()
def claim():
    claim = ClaimFactory.create()
    claim.claim_start_date = date(2020, 1, 1)
    claim.claim_end_date = date(2020, 2, 1)
    absence_period: AbsencePeriod = AbsencePeriodFactory.create(claim=claim)
    absence_period.leave_request_decision_id = (
        LeaveRequestDecision.APPROVED.leave_request_decision_id
    )
    absence_period.absence_reason_qualifier_one_id = (
        AbsenceReasonQualifierOne.BIRTH_DISABILITY.absence_reason_qualifier_one_id
    )
    claim.absence_periods = [absence_period]
    return claim


@pytest.fixture()
def claim_without_absence_periods():
    claim = ClaimFactory.create()
    claim.claim_start_date = date(2020, 1, 1)
    claim.claim_end_date = date(2020, 2, 1)
    claim.absence_periods = []
    claim.application = ApplicationFactory.create()
    return claim


@freeze_time("2020-02-01")
class TestGetChangeRequestIssues:
    def test_start_date_later_than_end_date(self, mtb_change_request, claim):
        mtb_change_request.start_date = date(2020, 5, 1)
        resp = get_change_request_issues(mtb_change_request, claim)
        assert resp[0].type == IssueType.maximum
        assert resp[0].field == "end_date"
        assert "Start date must be less than end date" in resp[0].message

    def test_null_change_request_type(self, withdrawal_change_request, claim):
        withdrawal_change_request.change_request_type_id = None
        resp = get_change_request_issues(withdrawal_change_request, claim)
        assert resp[0].type == IssueType.required
        assert resp[0].field == "change_request_type"
        assert "Change request needs a valid type" in resp[0].message

    def test_end_date_same_as_claim_start_date(self, extension_change_request, claim):
        extension_change_request.end_date = date(2020, 1, 1)
        resp = get_change_request_issues(extension_change_request, claim)
        assert resp[0].type == IssueType.change_request_end_date_same_as_start
        assert resp[0].field == "end_date"
        assert "Start date should not be same as end date" in resp[0].message

    def test_in_progress_app_no_valid_absence_period(
        self, withdrawal_change_request, claim_without_absence_periods
    ):
        resp = get_change_request_issues(withdrawal_change_request, claim_without_absence_periods)
        assert resp == []

    def test_withdrawal_already_submitted(self, withdrawal_change_request, claim):
        ChangeRequestFactory.create(
            change_request_type_id=ChangeRequestType.WITHDRAWAL.change_request_type_id,
            submitted_time=date(2020, 2, 1),
            claim=claim,
        )

        resp = get_change_request_issues(withdrawal_change_request, claim)
        assert resp[0].type == IssueType.has_submitted_withdrawal
        assert "Claimant already requested to withdraw this leave" in resp[0].message

    @freeze_time("2020-04-01")
    def test_claim_too_far_past_leave_end(self, extension_change_request, claim):
        resp = get_change_request_issues(extension_change_request, claim)

        assert resp[0].type == IssueType.leave_end_past_max
        assert "Leave ended more than 30 days ago" in resp[0].message

    @freeze_time("2020-02-01")
    class TestChangeRequestTypeExtension:
        # Test rules specific to the Extension type

        def test_no_issues(self, extension_change_request, claim):
            issues = get_change_request_issues(extension_change_request, claim)

            assert issues == []

        def test_start_date_error(self, extension_change_request, claim):
            extension_change_request.start_date = date(2020, 2, 1)

            resp = get_change_request_issues(extension_change_request, claim)

            assert resp[0].type == IssueType.change_start_date_is_unsupported
            assert resp[0].field == "start_date"
            assert "Start date is invalid for this request type" in resp[0].message

        def test_no_end_date_error(self, extension_change_request, claim):
            extension_change_request.end_date = None

            resp = get_change_request_issues(extension_change_request, claim)

            assert resp[0].type == IssueType.required
            assert resp[0].field == "end_date"
            assert "End date is required for this request type" in resp[0].message

        def test_has_non_approved_period(self, extension_change_request, claim):
            absence_period: AbsencePeriod = AbsencePeriodFactory.create(claim=claim)
            absence_period.leave_request_decision_id = (
                LeaveRequestDecision.PENDING.leave_request_decision_id
            )
            claim.absence_periods.append(absence_period)

            resp = get_change_request_issues(extension_change_request, claim)

            assert resp[0].type == IssueType.only_approved_periods_valid
            assert resp[0].field == "fineos_absence_id"
            assert (
                "Claim must have only approved periods for this modification type"
                in resp[0].message
            )

        def test_claim_start_date_same_as_end_date(self, extension_change_request, claim):
            extension_change_request.end_date = date(2020, 2, 1)
            resp = get_change_request_issues(extension_change_request, claim)
            assert resp[0].type == IssueType.change_end_date_same_as_approved_end_date
            assert resp[0].field == "end_date"
            assert (
                "Request should have different end date from approved end date" in resp[0].message
            )

        def test_missing_documents(self, extension_change_request, claim):
            extension_change_request.documents_submitted_at = None

            resp = get_change_request_issues(extension_change_request, claim)

            assert resp[0].type == IssueType.required
            assert resp[0].field == "documents_submitted_at"
            assert "Supporting documents must be submitted when extending leave" in resp[0].message

        def test_missing_documents_no_errors_when_bonding(self, extension_change_request, claim):
            claim.absence_periods[0].absence_reason_id = (
                AbsenceReason.CHILD_BONDING.absence_reason_id
            )
            extension_change_request.documents_submitted_at = None

            issues = get_change_request_issues(extension_change_request, claim)

            assert not issues

        def test_disallow_12mo_extension(self, extension_change_request, claim):
            extension_change_request.end_date = claim.claim_end_date + relativedelta(
                months=12, days=1
            )
            resp = get_change_request_issues(extension_change_request, claim)
            assert resp[0].type == IssueType.disallow_12mo_extension
            assert resp[0].field == "end_date"
            assert "Your additional leave cannot be 12 months or more" in resp[0].message

    @freeze_time("2020-02-01")
    class TestChangeRequestTypeCancellation:
        # Test rules specific to the Cancellation type

        def test_no_issues(self, cancellation_change_request, claim):
            issues = get_change_request_issues(cancellation_change_request, claim)

            assert issues == []

        def test_start_date_error(self, cancellation_change_request, claim):
            cancellation_change_request.start_date = date(2020, 2, 1)

            resp = get_change_request_issues(cancellation_change_request, claim)

            assert resp[0].type == IssueType.change_start_date_is_unsupported
            assert resp[0].field == "start_date"
            assert "Start date is invalid for this request type" in resp[0].message

        def test_no_end_date_error(self, cancellation_change_request, claim):
            cancellation_change_request.end_date = None

            resp = get_change_request_issues(cancellation_change_request, claim)

            assert resp[0].type == IssueType.required
            assert resp[0].field == "end_date"
            assert "End date is required for this request type" in resp[0].message

        def test_has_non_approved_period(self, cancellation_change_request, claim):
            absence_period: AbsencePeriod = AbsencePeriodFactory.create(claim=claim)
            absence_period.leave_request_decision_id = (
                LeaveRequestDecision.PENDING.leave_request_decision_id
            )
            claim.absence_periods.append(absence_period)

            resp = get_change_request_issues(cancellation_change_request, claim)

            assert resp[0].type == IssueType.only_approved_periods_valid
            assert resp[0].field == "fineos_absence_id"
            assert (
                "Claim must have only approved periods for this modification type"
                in resp[0].message
            )

        def test_missing_documents_no_errors(self, cancellation_change_request, claim):
            cancellation_change_request.documents_submitted_at = None
            cancellation_change_request.end_date = date(2020, 1, 15)

            issues = get_change_request_issues(cancellation_change_request, claim)

            assert issues == []

        def test_claim_start_date_same_as_end_date(self, cancellation_change_request, claim):
            cancellation_change_request.end_date = date(2020, 2, 1)
            resp = get_change_request_issues(cancellation_change_request, claim)
            assert resp[0].type == IssueType.change_end_date_same_as_approved_end_date
            assert resp[0].field == "end_date"
            assert (
                "Request should have different end date from approved end date" in resp[0].message
            )

    @freeze_time("2020-02-01")
    class TestChangeRequestTypeWithdrawal:
        # Test rules specific to the Withdrawal type

        def issues_if_approved_period(self, withdrawal_change_request, claim):
            resp = get_change_request_issues(withdrawal_change_request, claim)
            assert resp[0].type == IssueType.only_in_review_or_pending_periods_valid
            assert (
                resp[0].message
                == "Claim must have only in_review or pending periods for this modification type"
            )

        def test_no_issues_pending_period(self, withdrawal_change_request, claim):
            claim.absence_periods[0].leave_request_decision_id = (
                LeaveRequestDecision.PENDING.leave_request_decision_id
            )

            issues = get_change_request_issues(withdrawal_change_request, claim)

            assert issues == []

        def test_no_issues_in_review_period(self, withdrawal_change_request, claim):
            claim.absence_periods[0].leave_request_decision_id = (
                LeaveRequestDecision.IN_REVIEW.leave_request_decision_id
            )

            issues = get_change_request_issues(withdrawal_change_request, claim)

            assert issues == []

        def test_start_date_error(self, withdrawal_change_request, claim):
            withdrawal_change_request.start_date = date(2020, 2, 1)
            resp = get_change_request_issues(withdrawal_change_request, claim)
            assert resp[0].type == IssueType.withdrawal_dates_must_be_null
            assert resp[0].field == "start_date"
            assert "Start date is invalid for this request type" in resp[0].message

        def test_end_date_error(self, withdrawal_change_request, claim):
            withdrawal_change_request.end_date = date(2020, 2, 1)
            resp = get_change_request_issues(withdrawal_change_request, claim)
            assert resp[0].type == IssueType.withdrawal_dates_must_be_null
            assert resp[0].field == "end_date"
            assert "End date is invalid for this request type" in resp[0].message

        def test_wrong_absence_status(self, withdrawal_change_request, claim):
            claim.absence_periods[0].leave_request_decision_id = (
                LeaveRequestDecision.DENIED.leave_request_decision_id
            )
            resp = get_change_request_issues(withdrawal_change_request, claim)
            assert resp[0].type == IssueType.must_have_valid_decision_status
            assert resp[0].field == "fineos_absence_id"
            assert (
                "Claim must have at least one absence period in approved, pending, in_review"
                in resp[0].message
            )

        def test_in_review_status_valid(self, withdrawal_change_request, claim):
            claim.absence_periods[0].leave_request_decision_id = (
                LeaveRequestDecision.IN_REVIEW.leave_request_decision_id
            )

            issues = get_change_request_issues(withdrawal_change_request, claim)

            assert issues == []

    @freeze_time("2020-02-01")
    class TestChangeRequestTypeMedicalToBonding:
        # Test rules specific to the Medical To Bonding type

        def test_no_issues(self, mtb_change_request, claim):
            issues = get_change_request_issues(mtb_change_request, claim)
            assert issues == []

        def test_no_start_date_error(self, mtb_change_request, claim):
            mtb_change_request.start_date = None
            resp = get_change_request_issues(mtb_change_request, claim)
            assert resp[0].type == IssueType.required
            assert resp[0].field == "start_date"
            assert "Start date is required for a medical to bonding transition" in resp[0].message

        def test_no_end_date_error(self, mtb_change_request, claim):
            mtb_change_request.end_date = None
            resp = get_change_request_issues(mtb_change_request, claim)
            assert resp[0].type == IssueType.required
            assert resp[0].field == "end_date"
            assert "End date is required for a medical to bonding transition" in resp[0].message

        def test_date_of_birth_error(self, mtb_change_request, claim):
            mtb_change_request.date_of_birth = None
            resp = get_change_request_issues(mtb_change_request, claim)
            assert resp[0].type == IssueType.required
            assert resp[0].field == "date_of_birth"
            assert (
                "Date of birth is required for a medical to bonding transition" in resp[0].message
            )

        def test_missing_absence_period(self, mtb_change_request, claim):
            claim.absence_periods[0].absence_reason_qualifier_one_id = (
                AbsenceReasonQualifierOne.NOT_WORK_RELATED.absence_reason_qualifier_one_id
            )
            resp = get_change_request_issues(mtb_change_request, claim)
            assert resp[0].type == IssueType.not_medical_to_bonding_claim
            assert (
                "Claimant did not apply for bonding leave in initial application" in resp[0].message
            )

        def test_valid_absence_statuses(self, mtb_change_request, claim):
            valid_absence_ids = [
                AbsenceReasonQualifierOne.BIRTH_DISABILITY.absence_reason_qualifier_one_id,
                AbsenceReasonQualifierOne.PRENATAL_CARE.absence_reason_qualifier_one_id,
                AbsenceReasonQualifierOne.PRENATAL_DISABILITY.absence_reason_qualifier_one_id,
                AbsenceReasonQualifierOne.POSTNATAL_DISABILITY.absence_reason_qualifier_one_id,
            ]
            for absence_id in valid_absence_ids:
                claim.absence_periods[0].absence_reason_qualifier_one_id = absence_id
                issues = get_change_request_issues(mtb_change_request, claim)
                assert issues == []

        def test_wrong_absence_status(self, mtb_change_request, claim):
            claim.absence_periods[0].leave_request_decision_id = (
                LeaveRequestDecision.DENIED.leave_request_decision_id
            )
            resp = get_change_request_issues(mtb_change_request, claim)
            assert resp[0].type == IssueType.must_have_valid_decision_status
            assert resp[0].field == "fineos_absence_id"
            assert (
                "Claim must have at least one absence period in approved, in_review"
                in resp[0].message
            )

        def test_in_review_status_valid(self, mtb_change_request, claim):
            claim.absence_periods[0].leave_request_decision_id = (
                LeaveRequestDecision.IN_REVIEW.leave_request_decision_id
            )

            issues = get_change_request_issues(mtb_change_request, claim)

            assert issues == []

        def test_exisiting_bonding_absence_period(self, mtb_change_request, claim):
            claim.absence_periods[0].absence_reason_id = (
                AbsenceReason.CHILD_BONDING.absence_reason_id
            )
            resp = get_change_request_issues(mtb_change_request, claim)
            assert resp[0].type == IssueType.not_medical_to_bonding_claim
            assert "Claimant has existing bonding leave" in resp[0].message

        def test_already_submitted_med_to_bonding(self, mtb_change_request, claim):
            ChangeRequestFactory.create(
                change_request_type_id=ChangeRequestType.MEDICAL_TO_BONDING.change_request_type_id,
                submitted_time=date(2020, 2, 1),
                start_date=date(2020, 2, 1),
                end_date=date(2020, 3, 1),
                date_of_birth=date(2020, 1, 1),
                claim=claim,
            )

            resp = get_change_request_issues(mtb_change_request, claim)
            assert resp[0].type == IssueType.multiple_med_to_bonding
            assert "Claimant already requested to add a bonding period" in resp[0].message
