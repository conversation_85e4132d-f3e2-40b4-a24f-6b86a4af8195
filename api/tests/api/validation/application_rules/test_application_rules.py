import datetime
from datetime import date
from unittest import mock

import pytest
from freezegun import freeze_time

from massgov.pfml.api.models.documents.responses import DocumentResponse
from massgov.pfml.api.validation.application_rules import (
    get_always_required_issues,
    get_application_complete_issues,
    get_application_submit_issues,
    get_conditional_issues,
    get_payments_issues,
    get_work_pattern_issues,
    validate_application_state,
)
from massgov.pfml.api.validation.exceptions import (
    IssueRule,
    IssueType,
    ValidationErrorDetail,
    ValidationErrorList,
)
from massgov.pfml.db.lookup_data.applications import (
    EmploymentStatus,
    LeaveReason,
    LeaveReasonQualifier,
)
from massgov.pfml.db.lookup_data.documents import DocumentType
from massgov.pfml.db.lookup_data.employees import PaymentMethod
from massgov.pfml.db.models.applications import WorkPatternDay
from massgov.pfml.db.models.factories import (
    AddressFactory,
    ApplicationFactory,
    ApplicationUserNotFoundInfoFactory,
    ClaimFactory,
    ClaimWithApplicationFactory,
    DocumentFactory,
    EmployerOnlyRequiredFactory,
    PaymentPreferenceFactory,
    WorkPatternFixedFactory,
    WorkPatternVariableFactory,
)

PFML_PROGRAM_LAUNCH_DATE = date(2021, 1, 1)


class TestAlwaysRequiredIssues:
    def setup_env(self, feature_config):
        feature_config.universal_profile.enable_mmg_idv = False
        feature_config.occupation_data_collection.enable_occupation_data_collection = False

    def test_first_name_required(self):
        test_app = ApplicationFactory.build(
            first_name=None,
            has_state_id=True,
            employment_status_id=EmploymentStatus.EMPLOYED.employment_status_id,
            hours_worked_per_week=70,
            residential_address=AddressFactory.build(),
            work_pattern=WorkPatternFixedFactory.build(),
        )
        issues = get_always_required_issues(test_app)
        assert [
            ValidationErrorDetail(
                type=IssueType.required, message="first_name is required", field="first_name"
            )
        ] == issues

    def test_last_name_required(self):
        test_app = ApplicationFactory.build(
            last_name=None,
            has_state_id=True,
            employment_status_id=EmploymentStatus.EMPLOYED.employment_status_id,
            hours_worked_per_week=70,
            residential_address=AddressFactory.build(),
            work_pattern=WorkPatternFixedFactory.build(),
        )
        issues = get_always_required_issues(test_app)
        assert [
            ValidationErrorDetail(
                type=IssueType.required, message="last_name is required", field="last_name"
            )
        ] == issues

    def test_phone_required(self):
        test_app = ApplicationFactory.build(
            phone=None,
            has_state_id=True,
            employment_status_id=EmploymentStatus.EMPLOYED.employment_status_id,
            hours_worked_per_week=70,
            residential_address=AddressFactory.build(),
            work_pattern=WorkPatternFixedFactory.build(),
        )

        issues = get_always_required_issues(test_app)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                message="phone.phone_number is required",
                field="phone.phone_number",
            ),
            ValidationErrorDetail(
                type=IssueType.required,
                message="phone.phone_type is required",
                field="phone.phone_type",
            ),
        ] == issues

    def test_date_of_birth_required(self):
        test_app = ApplicationFactory.build(
            date_of_birth=None,
            has_state_id=True,
            employment_status_id=EmploymentStatus.EMPLOYED.employment_status_id,
            hours_worked_per_week=70,
            residential_address=AddressFactory.build(),
            work_pattern=WorkPatternFixedFactory.build(),
        )
        issues = get_always_required_issues(test_app)
        assert [
            ValidationErrorDetail(
                type=IssueType.required, message="date_of_birth is required", field="date_of_birth"
            )
        ] == issues

    def test_has_state_id_required(self):
        test_app = ApplicationFactory.build(
            has_state_id=None,
            employment_status_id=EmploymentStatus.EMPLOYED.employment_status_id,
            hours_worked_per_week=70,
            residential_address=AddressFactory.build(),
            work_pattern=WorkPatternFixedFactory.build(),
        )
        issues = get_always_required_issues(test_app)
        assert [
            ValidationErrorDetail(
                type=IssueType.required, message="has_state_id is required", field="has_state_id"
            )
        ] == issues

    def test_tax_identifier_required(self):
        test_app = ApplicationFactory.build(
            tax_identifier=None,
            tax_identifier_id=None,
            has_state_id=True,
            employment_status_id=EmploymentStatus.EMPLOYED.employment_status_id,
            hours_worked_per_week=70,
            residential_address=AddressFactory.build(),
            work_pattern=WorkPatternFixedFactory.build(),
        )
        issues = get_always_required_issues(test_app)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                message="tax_identifier is required",
                field="tax_identifier",
            )
        ] == issues

    def test_leave_reason_required(self):
        test_app = ApplicationFactory.build(
            leave_reason_id=None,
            has_state_id=True,
            employment_status_id=EmploymentStatus.EMPLOYED.employment_status_id,
            hours_worked_per_week=70,
            residential_address=AddressFactory.build(),
            work_pattern=WorkPatternFixedFactory.build(),
        )
        issues = get_always_required_issues(test_app)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                message="leave_details.reason is required",
                field="leave_details.reason",
            )
        ] == issues

    def test_employment_status_required(self):
        test_app = ApplicationFactory.build(
            employment_status=None,
            hours_worked_per_week=70,
            has_state_id=True,
            residential_address=AddressFactory.build(),
            work_pattern=WorkPatternFixedFactory.build(),
        )
        issues = get_always_required_issues(test_app)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                message="employment_status is required",
                field="employment_status",
            )
        ] == issues

    def test_language_required(self):
        test_app = ApplicationFactory.build(
            language_id=None,
            has_state_id=True,
            employment_status_id=EmploymentStatus.EMPLOYED.employment_status_id,
            hours_worked_per_week=70,
            residential_address=AddressFactory.build(),
            work_pattern=WorkPatternFixedFactory.build(),
        )
        issues = get_always_required_issues(test_app)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                message="language is required",
                field="language",
            )
        ] == issues

    def test_employer_notified_required(self):
        test_app = ApplicationFactory.build(
            employer_notified=None,
            employment_status_id=EmploymentStatus.EMPLOYED.employment_status_id,
            hours_worked_per_week=70,
            has_state_id=True,
            residential_address=AddressFactory.build(),
            work_pattern=WorkPatternFixedFactory.build(),
        )
        issues = get_always_required_issues(test_app)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                message="leave_details.employer_notified is required",
                field="leave_details.employer_notified",
            )
        ] == issues

    def test_has_mailing_address_required(self):
        test_app = ApplicationFactory.build(
            has_mailing_address=None,
            employment_status_id=EmploymentStatus.EMPLOYED.employment_status_id,
            hours_worked_per_week=70,
            has_state_id=True,
            residential_address=AddressFactory.build(),
            work_pattern=WorkPatternFixedFactory.build(),
        )
        issues = get_always_required_issues(test_app)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                message="has_mailing_address is required",
                field="has_mailing_address",
            )
        ] == issues

    def test_work_pattern_type_required(self):
        test_app = ApplicationFactory.build(
            employment_status_id=EmploymentStatus.EMPLOYED.employment_status_id,
            hours_worked_per_week=70,
            residential_address=AddressFactory.build(),
            work_pattern=None,
        )
        issues = get_always_required_issues(test_app)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                message="work_pattern.work_pattern_type is required",
                field="work_pattern.work_pattern_type",
            )
        ] == issues

    def test_has_leave_periods_required(self):
        test_app = ApplicationFactory.build(
            employment_status_id=EmploymentStatus.EMPLOYED.employment_status_id,
            hours_worked_per_week=70,
            residential_address=AddressFactory.build(),
            work_pattern=WorkPatternFixedFactory.build(),
            has_continuous_leave_periods=None,
            has_intermittent_leave_periods=None,
            has_reduced_schedule_leave_periods=None,
        )
        issues = get_always_required_issues(test_app)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                message="has_continuous_leave_periods is required",
                field="has_continuous_leave_periods",
            ),
            ValidationErrorDetail(
                type=IssueType.required,
                message="has_intermittent_leave_periods is required",
                field="has_intermittent_leave_periods",
            ),
            ValidationErrorDetail(
                type=IssueType.required,
                message="has_reduced_schedule_leave_periods is required",
                field="has_reduced_schedule_leave_periods",
            ),
        ] == issues

    def test_hours_worked_per_week_required(self):
        test_app = ApplicationFactory.build(
            employment_status_id=EmploymentStatus.EMPLOYED.employment_status_id,
            has_state_id=True,
            residential_address=AddressFactory.build(),
            work_pattern=WorkPatternFixedFactory.build(),
        )
        issues = get_always_required_issues(test_app)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                message="hours_worked_per_week is required",
                field="hours_worked_per_week",
            )
        ] == issues

    def test_residential_address_required(self):
        test_app = ApplicationFactory.build(
            residential_address=None,
            employment_status_id=EmploymentStatus.EMPLOYED.employment_status_id,
            hours_worked_per_week=70,
            has_state_id=True,
            work_pattern=WorkPatternFixedFactory.build(),
        )
        issues = get_always_required_issues(test_app)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                message="residential_address is required",
                field="residential_address",
            )
        ] == issues


class TestConditionalIssues:
    @pytest.fixture
    def application(self):
        return ApplicationFactory.build(
            has_employer_benefits=None,
            has_other_incomes=None,
            has_previous_leaves=None,
        )

    def test_leave_rules(self, application, feature_config):
        issues = get_conditional_issues(application, feature_config)

        # has_previous_leaves is required
        assert (
            ValidationErrorDetail(
                field="has_previous_leaves",
                message="has_previous_leaves is required",
                type=IssueType.required,
            )
            in issues
        )

    def test_industry_sector_required(self, feature_config):
        test_app = ApplicationFactory.build(
            employment_status_id=EmploymentStatus.EMPLOYED.employment_status_id,
        )

        # industry_sector is required when employed and the FF is enabled
        feature_config.occupation_data_collection.enable_occupation_data_collection = True
        issues = get_conditional_issues(test_app, feature_config)

        assert (
            ValidationErrorDetail(
                type=IssueType.required,
                rule=IssueRule.conditional,
                message="industry_sector is required if employment_status is Employed",
                field="industry_sector",
            )
            in issues
        )

        # industry_sector is not required when the FF is disabled
        feature_config.occupation_data_collection.enable_occupation_data_collection = False
        issues = get_conditional_issues(test_app, feature_config)

        assert (
            ValidationErrorDetail(
                type=IssueType.required,
                rule=IssueRule.conditional,
                message="industry_sector is required if employment_status is Employed",
                field="industry_sector",
            )
            not in issues
        )

    def test_industry_sector_required_not_emplyed(self, feature_config):
        test_app = ApplicationFactory.build(
            employment_status_id=EmploymentStatus.UNEMPLOYED.employment_status_id,
        )

        # industry_sector is not required when unemployed and the FF is enabled
        feature_config.occupation_data_collection.enable_occupation_data_collection = True
        issues = get_conditional_issues(test_app, feature_config)

        assert (
            ValidationErrorDetail(
                type=IssueType.required,
                rule=IssueRule.conditional,
                message="industry_sector is required if employment_status is Employed",
                field="industry_sector",
            )
            not in issues
        )

        # industry_sector is not required when unemployed and the FF is disabled
        feature_config.occupation_data_collection.enable_occupation_data_collection = False
        issues = get_conditional_issues(test_app, feature_config)

        assert (
            ValidationErrorDetail(
                type=IssueType.required,
                rule=IssueRule.conditional,
                message="industry_sector is required if employment_status is Employed",
                field="industry_sector",
            )
            not in issues
        )

    @freeze_time("2021-01-01")
    def test_employer_notified_date_minimum(self, feature_config):
        test_app = ApplicationFactory.build(
            employer_notified=True, employer_notification_date=date(2018, 12, 31)
        )
        issues = get_conditional_issues(test_app, feature_config)
        assert (
            ValidationErrorDetail(
                type=IssueType.minimum,
                rule=IssueRule.conditional,
                message="employer_notification_date year must be within the past 2 years",
                field="leave_details.employer_notification_date",
            )
            in issues
        )

    @freeze_time("2021-01-01")
    def test_employer_notified_date_maximum(self, feature_config):
        test_app = ApplicationFactory.build(
            employer_notified=True, employer_notification_date=date(2021, 1, 2)
        )
        issues = get_conditional_issues(test_app, feature_config)
        assert (
            ValidationErrorDetail(
                type=IssueType.maximum,
                rule=IssueRule.conditional,
                message="employer_notification_date must be today or prior",
                field="leave_details.employer_notification_date",
            )
            in issues
        )

    @freeze_time("2022-12-01")
    def test_residential_address_fields_required(self, feature_config):
        test_app = ApplicationFactory.build(
            residential_address=AddressFactory.build(
                address_line_one=None, city=None, geo_state_id=None, zip_code=None
            ),
            employment_status_id=EmploymentStatus.EMPLOYED.employment_status_id,
            employer_notification_date=date(2021, 1, 3),
            employer_notified=True,
        )

        issues = get_conditional_issues(test_app, feature_config)

        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                message="residential_address.line_1 is required",
                field="residential_address.line_1",
            ),
            ValidationErrorDetail(
                type=IssueType.required,
                message="residential_address.city is required",
                field="residential_address.city",
            ),
            ValidationErrorDetail(
                type=IssueType.required,
                message="residential_address.state is required",
                field="residential_address.state",
            ),
            ValidationErrorDetail(
                type=IssueType.required,
                message="residential_address.zip is required",
                field="residential_address.zip",
            ),
        ] == issues

    @freeze_time("2022-12-01")
    def test_mailing_address_fields_required(self, feature_config):
        test_app = ApplicationFactory.build(
            mailing_address=AddressFactory.build(
                address_line_one=None, city=None, geo_state_id=None, zip_code=None
            ),
            employment_status_id=EmploymentStatus.EMPLOYED.employment_status_id,
            employer_notification_date=date(2021, 1, 3),
            employer_notified=True,
        )

        issues = get_conditional_issues(test_app, feature_config)

        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                message="mailing_address.line_1 is required",
                field="mailing_address.line_1",
            ),
            ValidationErrorDetail(
                type=IssueType.required,
                message="mailing_address.city is required",
                field="mailing_address.city",
            ),
            ValidationErrorDetail(
                type=IssueType.required,
                message="mailing_address.state is required",
                field="mailing_address.state",
            ),
            ValidationErrorDetail(
                type=IssueType.required,
                message="mailing_address.zip is required",
                field="mailing_address.zip",
            ),
        ] == issues

    @freeze_time("2022-12-01")
    def test_employer_fein_required_for_employed_claimants(self, feature_config):
        test_app = ApplicationFactory.build(
            employer_fein=None,
            employment_status_id=EmploymentStatus.EMPLOYED.employment_status_id,
            employer_notification_date=date(2021, 1, 3),
            employer_notified=True,
        )
        issues = get_conditional_issues(test_app, feature_config)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                rule=IssueRule.conditional,
                message="employer_fein is required if employment_status is Employed",
                field="employer_fein",
            )
        ] == issues

    def test_employer_fein_not_required_for_self_employed_claimants(self, feature_config):
        test_app = ApplicationFactory.build(
            employer_fein=None,
            employment_status_id=EmploymentStatus.SELF_EMPLOYED.employment_status_id,
        )
        issues = get_conditional_issues(test_app, feature_config)
        assert not issues

    def test_employer_notification_date_required(self, feature_config):
        test_app = ApplicationFactory.build(employer_notified=True, employer_notification_date=None)
        issues = get_conditional_issues(test_app, feature_config)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                rule=IssueRule.conditional,
                message="employer_notification_date is required for leave_details if employer_notified is set",
                field="leave_details.employer_notification_date",
            )
        ] == issues

    def test_employer_notification_date_not_required_when_unemployed(self, feature_config):
        test_app = ApplicationFactory.build(
            employer_notified=False,
            employment_status_id=EmploymentStatus.UNEMPLOYED.employment_status_id,
        )
        issues = get_conditional_issues(test_app, feature_config)
        assert [] == issues

    def test_employer_notification_date_not_required_when_self_employed(self, feature_config):
        test_app = ApplicationFactory.build(
            employer_notified=False,
            employment_status_id=EmploymentStatus.SELF_EMPLOYED.employment_status_id,
        )
        issues = get_conditional_issues(test_app, feature_config)
        assert [] == issues

    def test_other_leave_rules(self, feature_config):
        application = ApplicationFactory.build(
            has_employer_benefits=None,
            has_other_incomes=None,
        )
        issues = get_conditional_issues(application, feature_config)

        assert (
            ValidationErrorDetail(
                field="has_employer_benefits",
                message="has_employer_benefits is required",
                type=IssueType.required,
            )
            in issues
        )
        assert (
            ValidationErrorDetail(
                field="has_other_incomes",
                message="has_other_incomes is required",
                type=IssueType.required,
            )
            in issues
        )

    def test_other_leave_submitted_rules(self, feature_config):
        # TODO (CP-2455): Remove this test once we always require other leaves be present, even on submitted applications
        application = ApplicationFactory.build(submitted_time=datetime.datetime.now())
        issues = get_conditional_issues(application, feature_config)

        assert (
            ValidationErrorDetail(
                field="has_employer_benefits",
                message="has_employer_benefits is required",
                type=IssueType.required,
            )
            not in issues
        )
        assert (
            ValidationErrorDetail(
                field="has_other_incomes",
                message="has_other_incomes is required",
                type=IssueType.required,
            )
            not in issues
        )

    def test_employer_name_required_if_has_additional_user_not_found_info(
        self, test_db_session, initialize_factories_session, feature_config
    ):
        test_app = ApplicationFactory.build(
            is_withholding_tax=True,
            payment_preference=PaymentPreferenceFactory.build(
                payment_method_id=PaymentMethod.CHECK.payment_method_id,
            ),
        )
        test_app.additional_user_not_found_info = ApplicationUserNotFoundInfoFactory.build(
            application_id=test_app.application_id, employer_name=None
        )
        issues = get_conditional_issues(test_app, feature_config)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                rule="conditional",
                message="employer_name is required if additional_user_not_found_info is set",
                field="additional_user_not_found_info.employer_name",
            )
        ] == issues

    def test_date_of_hire_required_if_has_additional_user_not_found_info(
        self, test_db_session, initialize_factories_session, feature_config
    ):
        test_app = ApplicationFactory.build(
            is_withholding_tax=True,
            payment_preference=PaymentPreferenceFactory.build(
                payment_method_id=PaymentMethod.CHECK.payment_method_id,
            ),
        )
        test_app.additional_user_not_found_info = ApplicationUserNotFoundInfoFactory.build(
            application_id=test_app.application_id, date_of_hire=None
        )
        issues = get_conditional_issues(test_app, feature_config)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                rule="conditional",
                message="date_of_hire is required if additional_user_not_found_info is set",
                field="additional_user_not_found_info.date_of_hire",
            )
        ] == issues

    def test_currently_employed_required_if_has_additional_user_not_found_info(
        self, test_db_session, initialize_factories_session, feature_config
    ):
        test_app = ApplicationFactory.build(
            is_withholding_tax=True,
            payment_preference=PaymentPreferenceFactory.build(
                payment_method_id=PaymentMethod.CHECK.payment_method_id,
            ),
        )
        test_app.additional_user_not_found_info = ApplicationUserNotFoundInfoFactory.build(
            application_id=test_app.application_id, currently_employed=None
        )
        issues = get_conditional_issues(test_app, feature_config)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                rule="conditional",
                message="currently_employed is required if additional_user_not_found_info is set",
                field="additional_user_not_found_info.currently_employed",
            )
        ] == issues

    def test_date_of_separation_required_if_not_currently_employed(
        self, test_db_session, initialize_factories_session, feature_config
    ):
        test_app = ApplicationFactory.build(
            is_withholding_tax=True,
            payment_preference=PaymentPreferenceFactory.build(
                payment_method_id=PaymentMethod.CHECK.payment_method_id,
            ),
        )
        test_app.additional_user_not_found_info = ApplicationUserNotFoundInfoFactory.build(
            application_id=test_app.application_id,
            currently_employed=False,
            date_of_separation=None,
        )
        issues = get_conditional_issues(test_app, feature_config)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                rule="conditional",
                message="date_of_separation is required if currently_employed is set",
                field="additional_user_not_found_info.date_of_separation",
            )
        ] == issues

    def test_is_withholding_tax_if_has_additional_user_not_found_info(
        self, test_db_session, initialize_factories_session, feature_config
    ):
        test_app = ApplicationFactory.build(
            payment_preference=PaymentPreferenceFactory.build(
                payment_method_id=PaymentMethod.CHECK.payment_method_id,
            ),
        )
        test_app.additional_user_not_found_info = ApplicationUserNotFoundInfoFactory.build(
            application_id=test_app.application_id,
        )
        issues = get_conditional_issues(test_app, feature_config)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                message="Tax withholding preference is required",
                field="is_withholding_tax",
            )
        ] == issues

    def test_payment_issues_if_has_additional_user_not_found_info(
        self, test_db_session, initialize_factories_session, feature_config
    ):
        test_app = ApplicationFactory.build(
            is_withholding_tax=True,
        )
        test_app.additional_user_not_found_info = ApplicationUserNotFoundInfoFactory.build(
            application_id=test_app.application_id,
        )
        issues = get_conditional_issues(test_app, feature_config)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                message="Payment method is required",
                field="payment_preference.payment_method",
            )
        ] == issues

    def test_mass_id_required_if_has_mass_id(self, feature_config):
        test_app = ApplicationFactory.build(has_state_id=True, mass_id=None)
        issues = get_conditional_issues(test_app, feature_config)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                rule="conditional",
                message="mass_id is required if has_mass_id is set",
                field="mass_id",
            )
        ] == issues

    def test_mailing_addr_required_if_has_mailing_addr(self, feature_config):
        test_app = ApplicationFactory.build(has_mailing_address=True, mailing_address=None)
        issues = get_conditional_issues(test_app, feature_config)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                rule="conditional",
                message="mailing_address is required if has_mailing_address is set",
                field="mailing_address",
            )
        ] == issues

    def test_pregnant_required_medical_leave(self, feature_config):
        test_app = ApplicationFactory.build(
            leave_reason_id=LeaveReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.leave_reason_id,
            pregnant_or_recent_birth=None,
        )
        assert test_app.pregnant_or_recent_birth is None
        issues = get_conditional_issues(test_app, feature_config)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                rule="conditional",
                message="It is required to indicate if there has been a recent pregnancy or birth when medical leave is requested, regardless of if it is related to the leave request",
                field="leave_details.pregnant_or_recent_birth",
            )
        ] == issues

    def test_reason_qualifers_required_for_bonding(self, feature_config):
        test_app = ApplicationFactory.build(
            leave_reason_id=LeaveReason.CHILD_BONDING.leave_reason_id,
            leave_reason_qualifier_id=LeaveReasonQualifier.SERIOUS_HEALTH_CONDITION.leave_reason_qualifier_id,
        )
        issues = get_conditional_issues(test_app, feature_config)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                rule="conditional",
                message="Invalid leave reason qualifier for bonding leave type",
                field="leave_details.reason_qualifier",
            )
        ] == issues

    def test_child_birth_date_required_for_newborn_bonding(self, feature_config):
        test_app = ApplicationFactory.build(
            leave_reason_id=LeaveReason.CHILD_BONDING.leave_reason_id,
            leave_reason_qualifier_id=LeaveReasonQualifier.NEWBORN.leave_reason_qualifier_id,
            child_birth_date=None,
        )
        issues = get_conditional_issues(test_app, feature_config)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                rule="conditional",
                message="Child birth date is required for newborn bonding leave",
                field="leave_details.child_birth_date",
            )
        ] == issues

    def test_child_placement_date_required_for_adoption_bonding(self, feature_config):
        test_app = ApplicationFactory.build(
            leave_reason_id=LeaveReason.CHILD_BONDING.leave_reason_id,
            leave_reason_qualifier_id=LeaveReasonQualifier.ADOPTION.leave_reason_qualifier_id,
            child_placement_date=None,
        )
        issues = get_conditional_issues(test_app, feature_config)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                rule="conditional",
                message="Child placement date is required for foster or adoption bonding leave",
                field="leave_details.child_placement_date",
            )
        ] == issues

    def test_child_placement_date_required_for_fostercare_bonding(self, feature_config):
        test_app = ApplicationFactory.build(
            leave_reason_id=LeaveReason.CHILD_BONDING.leave_reason_id,
            leave_reason_qualifier_id=LeaveReasonQualifier.FOSTER_CARE.leave_reason_qualifier_id,
            child_placement_date=None,
        )
        issues = get_conditional_issues(test_app, feature_config)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                rule="conditional",
                message="Child placement date is required for foster or adoption bonding leave",
                field="leave_details.child_placement_date",
            )
        ] == issues

    class TestConcurrentEmployerQuestionsEnabled:
        def test_has_concurrent_employemnt_question_required(self, feature_config):
            test_app = ApplicationFactory.build(
                has_concurrent_employers=None,
            )

            issues = get_conditional_issues(test_app, feature_config)
            assert (
                ValidationErrorDetail(
                    type=IssueType.required,
                    rule="conditional",
                    message="has_concurrent_employers is required",
                    field="has_concurrent_employers",
                )
                in issues
            )

        def test_hours_worked_per_week_all_employers_too_few_issue_when_less_than_main_employer(
            self, feature_config
        ):
            test_dba = "Test Employer"
            test_fein = "*********"

            test_employer = EmployerOnlyRequiredFactory.build(
                employer_dba=test_dba,
                employer_fein=test_fein,
            )

            test_app = ApplicationFactory.build(
                hours_worked_per_week=40,
                hours_worked_per_week_all_employers=35,
                has_concurrent_employers=True,
                employer_fein=test_employer.employer_fein,
            )

            test_app.employer = test_employer

            test_hours_worked_per_week = str(test_app.hours_worked_per_week)

            issues = get_conditional_issues(test_app, feature_config)

            expected_issue = ValidationErrorDetail(
                message="hours_worked_per_week_all_employers must be greater than the hours of the main employer on the claim",
                rule=IssueRule.conditional,
                type=IssueType.minimum_dba,
                field="hours_worked_per_week_all_employers",
                extra={
                    "employer_dba": test_dba,
                    "employer_fein": test_fein,
                    "hours_worked_per_week": test_hours_worked_per_week,
                },
            )

            assert expected_issue in issues

            matched_issue = next(i for i in issues if i == expected_issue)

            assert matched_issue.extra["employer_dba"] == test_dba
            assert matched_issue.extra["employer_fein"] == test_fein
            assert matched_issue.extra["hours_worked_per_week"] == test_hours_worked_per_week

        def test_hours_worked_per_week_all_employers_too_few_issue_when_equal_to_main_employer(
            self, feature_config
        ):
            test_dba = "Test Employer"
            test_fein = "*********"

            test_employer = EmployerOnlyRequiredFactory.build(
                employer_dba=test_dba,
                employer_fein=test_fein,
            )

            test_app = ApplicationFactory.build(
                hours_worked_per_week=40,
                hours_worked_per_week_all_employers=40,
                has_concurrent_employers=True,
                employer_fein=test_employer.employer_fein,
            )

            test_app.employer = test_employer

            issues = get_conditional_issues(test_app, feature_config)

            test_hours_worked_per_week = str(test_app.hours_worked_per_week)

            expected_issue = ValidationErrorDetail(
                message="hours_worked_per_week_all_employers must be greater than the hours of the main employer on the claim",
                rule=IssueRule.conditional,
                type=IssueType.minimum_dba,
                field="hours_worked_per_week_all_employers",
                extra={
                    "employer_dba": test_dba,
                    "employer_fein": test_fein,
                    "hours_worked_per_week": test_hours_worked_per_week,
                },
            )

            assert expected_issue in issues

            matched_issue = next(i for i in issues if i == expected_issue)

            assert matched_issue.extra["employer_dba"] == test_dba
            assert matched_issue.extra["employer_fein"] == test_fein
            assert matched_issue.extra["hours_worked_per_week"] == test_hours_worked_per_week

        def test_hours_worked_per_week_all_employers_too_few_issue_with_no_employer_dba_info(
            self, feature_config
        ):
            test_fein = "*********"

            test_employer = EmployerOnlyRequiredFactory.build(
                employer_dba=None,
                employer_fein=test_fein,
            )

            test_app = ApplicationFactory.build(
                hours_worked_per_week=40,
                hours_worked_per_week_all_employers=40,
                has_concurrent_employers=True,
                employer_fein=test_employer.employer_fein,
            )

            test_app.employer = test_employer

            issues = get_conditional_issues(test_app, feature_config)

            test_hours_worked_per_week = str(test_app.hours_worked_per_week)

            expected_issue = ValidationErrorDetail(
                message="hours_worked_per_week_all_employers must be greater than the hours of the main employer on the claim",
                rule=IssueRule.conditional,
                type=IssueType.minimum_ein,
                field="hours_worked_per_week_all_employers",
                extra={
                    "employer_fein": test_fein,
                    "hours_worked_per_week": test_hours_worked_per_week,
                },
            )

            assert expected_issue in issues

            matched_issue = next(i for i in issues if i == expected_issue)

            assert matched_issue.extra["employer_fein"] == test_fein
            assert matched_issue.extra["hours_worked_per_week"] == test_hours_worked_per_week

        def test_hours_worked_per_week_all_employers_too_few_issue_with_invalid_employer_info(
            self, feature_config
        ):
            test_dba = "EMPLOYER NOT FOUND"
            test_fein = "000000000"

            test_employer = EmployerOnlyRequiredFactory.build(
                employer_dba=test_dba,
                employer_fein=test_fein,
            )

            test_app = ApplicationFactory.build(
                hours_worked_per_week=40,
                hours_worked_per_week_all_employers=40,
                has_concurrent_employers=True,
                employer_fein=test_employer.employer_fein,
            )

            test_app.employer = test_employer

            issues = get_conditional_issues(test_app, feature_config)

            test_hours_worked_per_week = str(test_app.hours_worked_per_week)

            expected_issue = ValidationErrorDetail(
                message="hours_worked_per_week_all_employers must be greater than the hours of the main employer on the claim",
                rule=IssueRule.conditional,
                type=IssueType.minimum,
                field="hours_worked_per_week_all_employers",
                extra={
                    "hours_worked_per_week": test_hours_worked_per_week,
                },
            )

            assert expected_issue in issues

            assert expected_issue.extra["hours_worked_per_week"] == test_hours_worked_per_week

        def test_hours_worked_per_week_all_employers_too_few_issue_with_no_employer_info(
            self, feature_config
        ):
            test_app = ApplicationFactory.build(
                hours_worked_per_week=40,
                hours_worked_per_week_all_employers=40,
                has_concurrent_employers=True,
                employer_fein=None,
            )

            issues = get_conditional_issues(test_app, feature_config)

            test_hours_worked_per_week = str(test_app.hours_worked_per_week)

            expected_issue = ValidationErrorDetail(
                message="hours_worked_per_week_all_employers must be greater than the hours of the main employer on the claim",
                rule=IssueRule.conditional,
                type=IssueType.minimum,
                field="hours_worked_per_week_all_employers",
                extra={
                    "hours_worked_per_week": test_hours_worked_per_week,
                },
            )

            assert expected_issue in issues

            assert expected_issue.extra["hours_worked_per_week"] == test_hours_worked_per_week

        def test_hours_worked_per_week_all_employers_required(self, feature_config):
            test_app = ApplicationFactory.build(has_concurrent_employers=True)
            issues = get_conditional_issues(test_app, feature_config)
            expected_issue = ValidationErrorDetail(
                message="hours_worked_per_week_all_employers is required",
                type=IssueType.required,
                field="hours_worked_per_week_all_employers",
            )
            assert expected_issue in issues

        def test_max_hours_worked_per_week_all_employers(self, feature_config):
            test_app = ApplicationFactory.build(
                has_concurrent_employers=True, hours_worked_per_week_all_employers=170
            )
            issues = get_conditional_issues(test_app, feature_config)
            expected_issue = ValidationErrorDetail(
                type=IssueType.maximum,
                message="hours_worked_per_week_all_employers must be 168 or fewer",
                field="hours_worked_per_week_all_employers",
            )

            assert expected_issue in issues


class TestPaymentsIssues:
    def test_account_number_required_for_ACH(self):
        test_app = ApplicationFactory.build(
            payment_preference=PaymentPreferenceFactory.build(
                payment_method_id=PaymentMethod.ACH.payment_method_id, account_number=None
            )
        )
        issues = get_payments_issues(test_app)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                rule="conditional",
                message="Account number is required for direct deposit",
                field="payment_preference.account_number",
            )
        ] == issues

    def test_routing_number_required_for_ACH(self):
        test_app = ApplicationFactory.build(
            payment_preference=PaymentPreferenceFactory.build(
                payment_method_id=PaymentMethod.ACH.payment_method_id, routing_number=None
            )
        )
        issues = get_payments_issues(test_app)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                rule="conditional",
                message="Routing number is required for direct deposit",
                field="payment_preference.routing_number",
            )
        ] == issues

    def test_bank_account_type_required_for_ACH(self):
        test_app = ApplicationFactory.build(
            payment_preference=PaymentPreferenceFactory.build(
                payment_method_id=PaymentMethod.ACH.payment_method_id, bank_account_type_id=None
            )
        )
        issues = get_payments_issues(test_app)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                rule="conditional",
                message="Account type is required for direct deposit",
                field="payment_preference.bank_account_type",
            )
        ] == issues

    def test_valid_routing_number(self):
        test_app = ApplicationFactory.build(
            payment_preference=PaymentPreferenceFactory.build(
                payment_method_id=PaymentMethod.ACH.payment_method_id, routing_number="*********"
            )
        )
        issues = get_payments_issues(test_app)
        assert [
            ValidationErrorDetail(
                type=IssueType.checksum,
                message="Routing number is invalid",
                field="payment_preference.routing_number",
            )
        ] == issues

    def test_payment_method_required_for_payment_preference(self):
        test_app = ApplicationFactory.build(
            payment_preference=PaymentPreferenceFactory.build(payment_method_id=None)
        )
        issues = get_payments_issues(test_app)
        assert [
            ValidationErrorDetail(
                type=IssueType.required,
                message="Payment method is required",
                field="payment_preference.payment_method",
            )
        ] == issues


class TestWorkPatternIssues:
    def test_min_work_pattern_total_minutes_worked(self):
        work_pattern = WorkPatternFixedFactory.build(
            work_pattern_days=[WorkPatternDay(day_of_week_id=i + 1, minutes=0) for i in range(7)]
        )

        issues = get_work_pattern_issues(work_pattern)

        assert [
            ValidationErrorDetail(
                type=IssueType.minimum,
                field="work_pattern.work_pattern_days",
                message="Total minutes for a work pattern must be greater than 0",
            )
        ] == issues

    def test_required_work_pattern_minutes(self):
        work_pattern = WorkPatternVariableFactory.build(
            work_pattern_days=[
                # index 0 will have 60 minutes, so work_pattern should pass minimum total minutes for work pattern
                WorkPatternDay(day_of_week_id=i + 1, minutes=60 if i == 0 else None)
                for i in range(7)
            ]
        )

        issues = get_work_pattern_issues(work_pattern)

        expected_issues = ValidationErrorList()

        for i in range(6):
            expected_issues.add_validation_error(
                type=IssueType.required,
                message=f"work_pattern.work_pattern_days[{i + 1}].minutes is required",
                field=f"work_pattern.work_pattern_days[{i + 1}].minutes",
            )

        assert expected_issues.get_errors() == issues

    def test_max_work_pattern_minutes(self):
        work_pattern = WorkPatternVariableFactory.build(
            work_pattern_days=[
                # index 0 will be 24 hours and should be valid
                WorkPatternDay(day_of_week_id=i + 1, minutes=24 * 60 + i)
                for i in range(7)
            ]
        )

        issues = get_work_pattern_issues(work_pattern)

        expected_issues = ValidationErrorList()

        for i in range(6):
            expected_issues.add_validation_error(
                type=IssueType.maximum,
                message="Total minutes in a work pattern week must be less than a day (1440 minutes)",
                field=f"work_pattern.work_pattern_days[{i + 1}].minutes",
            )

        assert expected_issues.get_errors() == issues


class TestApplicationSubmitIssues:
    @pytest.fixture
    def application(self):
        return ApplicationFactory.build()

    # Could be moved to TestConditionalIssues
    def test_submit_success_regardless_of_er_status(
        self, application, test_db_session, feature_config
    ):
        application.employer_notified = False
        application.employment_status_id = EmploymentStatus.EMPLOYED.employment_status_id
        issues = get_conditional_issues(application, feature_config)

        assert [] == issues

    def test_get_application_submit_issues_skips_if_already_submitted(
        self, test_db_session, feature_config
    ):
        application = ApplicationFactory.build(
            submitted_time=datetime.datetime.now(), first_name=None
        )
        assert (
            len(get_application_submit_issues(application, test_db_session, {}, feature_config))
            == 0
        )

        application.submitted_time = None
        assert (
            len(get_application_submit_issues(application, test_db_session, {}, feature_config)) > 0
        )

    def test_fraud_check_fail(self, initialize_factories_session, test_db_session):
        current_application = ApplicationFactory.build(
            submitted_time=datetime.datetime.now(),
        )
        # existing application
        ApplicationFactory.create(
            submitted_time=datetime.datetime.now(),
            tax_identifier=current_application.tax_identifier,
        )
        issues = validate_application_state(current_application, test_db_session)
        assert len(issues) == 1

    def test_fraud_check_pass(self, initialize_factories_session, test_db_session):
        # now simulate the situation when user has gone through Contact Center to assign the existent claim to thier second application
        current_application = ApplicationFactory.build(
            submitted_time=datetime.datetime.now(),
        )
        ClaimWithApplicationFactory.create(
            application=ApplicationFactory(
                submitted_time=datetime.datetime.now(),
                tax_identifier=current_application.tax_identifier,
                user=current_application.user,
            )
        )
        issues = validate_application_state(current_application, test_db_session)
        assert len(issues) == 0


class TestApplicationCompleteIssues:
    def add_passport_document(self, application) -> None:
        DocumentFactory.create(
            user_id=application.user_id,
            application_id=application.application_id,
            document_type_id=DocumentType.PASSPORT.document_type_id,
        )

    def add_own_serious_health_condition_form_document(self, application) -> None:
        DocumentFactory.create(
            user_id=application.user_id,
            application_id=application.application_id,
            document_type_id=DocumentType.OWN_SERIOUS_HEALTH_CONDITION_FORM.document_type_id,
        )

    def add_healthcare_provider_form_document(self, application) -> None:
        DocumentFactory.create(
            user_id=application.user_id,
            application_id=application.application_id,
            document_type_id=DocumentType.HEALTHCARE_PROVIDER_FORM.document_type_id,
        )

    def setup_env(self, feature_config):
        feature_config.universal_profile.enable_mmg_idv = False

    @pytest.fixture
    def application(self, user):
        return ApplicationFactory.create(
            is_withholding_tax=True,
            has_submitted_payment_preference=True,
            claim=ClaimFactory.build(),
            user_id=user.user_id,
            leave_reason_id=LeaveReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.leave_reason_id,
        )

    @pytest.mark.parametrize(
        "is_withholding_tax, has_submitted_payment_preference, include_id_document, expected_issues",
        [
            (
                None,
                True,
                True,
                [
                    ValidationErrorDetail(
                        type=IssueType.required,
                        message="Tax withholding preference is required",
                        field="is_withholding_tax",
                    )
                ],
            ),
            (False, True, True, []),
            (True, True, True, []),
            (
                False,
                False,
                True,
                [
                    ValidationErrorDetail(
                        message="Payment preference is required",
                        type=IssueType.required,
                        field="payment_method",
                    )
                ],
            ),
            (
                True,
                True,
                False,
                [
                    ValidationErrorDetail(
                        type=IssueType.required, message="An identification document is required"
                    )
                ],
            ),
        ],
    )
    @mock.patch(
        "massgov.pfml.services.documents.get_document_service.GetDocumentService.get_documents_from_fineos_for_application"
    )
    def test_get_application_complete_issues(
        self,
        mock_get_docs,
        is_withholding_tax,
        has_submitted_payment_preference,
        include_id_document,
        expected_issues,
        test_db_session,
        application,
        feature_config,
    ):
        application.is_withholding_tax = is_withholding_tax
        application.has_submitted_payment_preference = has_submitted_payment_preference
        application.leave_reason_id = LeaveReason.PREGNANCY_MATERNITY.leave_reason_id
        certificate_document_deferred = False

        mock_get_docs.return_value = []
        if include_id_document:
            self.add_passport_document(application)

        issues = get_application_complete_issues(
            application, test_db_session, certificate_document_deferred
        )

        assert issues == expected_issues

    @pytest.mark.parametrize(
        "is_withholding_tax, has_submitted_payment_preference, include_id_document, mmg_idv_status_id, has_state_id, expected_issues",
        [
            (
                True,
                True,
                False,
                None,
                True,
                [
                    ValidationErrorDetail(
                        type=IssueType.required, message="An identification document is required"
                    )
                ],
                # id="No MMG IDV status",
            ),
            (
                True,
                True,
                False,
                1,
                True,
                [
                    ValidationErrorDetail(
                        type=IssueType.required, message="An identification document is required"
                    )
                ],
                # id="MMG IDV 'Unverified' status",
            ),
            (
                True,
                True,
                False,
                2,
                True,
                [],
                # id="MMG IDV 'Verified' status and State ID",
            ),
            (
                True,
                True,
                False,
                2,
                False,
                [
                    ValidationErrorDetail(
                        type=IssueType.required, message="An identification document is required"
                    )
                ],
                # id="MMG IDV 'Verified' status and no State ID",
            ),
        ],
    )
    @mock.patch(
        "massgov.pfml.services.documents.get_document_service.GetDocumentService.get_documents_from_fineos_for_application"
    )
    def test_get_application_complete_issues_with_mmg_ff(
        self,
        mock_get_docs,
        is_withholding_tax,
        has_submitted_payment_preference,
        include_id_document,
        mmg_idv_status_id,
        has_state_id,
        expected_issues,
        test_db_session,
        application,
        feature_config,
    ):
        feature_config.universal_profile.enable_mmg_idv = True
        application.is_withholding_tax = is_withholding_tax
        application.has_submitted_payment_preference = has_submitted_payment_preference
        application.leave_reason_id = LeaveReason.PREGNANCY_MATERNITY.leave_reason_id
        application.mmg_idv_status_id = mmg_idv_status_id
        application.has_state_id = has_state_id
        certificate_document_deferred = False

        mock_get_docs.return_value = []
        if include_id_document:
            self.add_passport_document(application)

        issues = get_application_complete_issues(
            application, test_db_session, certificate_document_deferred
        )

        assert issues == expected_issues

    def test_no_issues(self, application, test_db_session):
        self.add_passport_document(application)
        self.add_own_serious_health_condition_form_document(application)
        certificate_document_deferred = False

        issues = get_application_complete_issues(
            application, test_db_session, certificate_document_deferred
        )
        assert issues == []

    def test_get_application_complete_issues_deferred_certification(
        self, application, test_db_session
    ):
        self.add_passport_document(application)
        certificate_document_deferred = True

        issues = get_application_complete_issues(
            application, test_db_session, certificate_document_deferred
        )

        assert issues == []

    def test_get_application_complete_issues_certification_validation(
        self, application, test_db_session
    ):
        self.add_passport_document(application)
        certificate_document_deferred = False

        issues = get_application_complete_issues(
            application, test_db_session, certificate_document_deferred
        )

        assert issues == [
            ValidationErrorDetail(
                type=IssueType.required, message="A certification document is required"
            )
        ]

    def test_healthcare_provider_form_is_cert_doc(self, application, test_db_session):
        self.add_passport_document(application)
        self.add_healthcare_provider_form_document(application)
        certificate_document_deferred = False

        issues = get_application_complete_issues(
            application, test_db_session, certificate_document_deferred
        )
        assert issues == []

    @pytest.mark.parametrize(
        "document_type, expected_issues",
        [
            (
                DocumentType.PASSPORT.document_type_description,
                [
                    ValidationErrorDetail(
                        type=IssueType.required, message="A certification document is required"
                    )
                ],
            ),
            (
                DocumentType.CHILD_BONDING_EVIDENCE_FORM.document_type_description,
                [
                    ValidationErrorDetail(
                        type=IssueType.required, message="An identification document is required"
                    )
                ],
            ),
        ],
    )
    @mock.patch(
        "massgov.pfml.services.documents.get_document_service.GetDocumentService.get_documents_from_fineos_for_application"
    )
    def test_get_application_complete_issues_fineos_fallback(
        self,
        mock_get_documents,
        document_type,
        expected_issues,
        application,
        test_db_session,
    ):
        mock_get_documents.return_value = [
            DocumentResponse(
                user_id=application.user_id,
                application_id=application.application_id,
                document_type=document_type,
                name="File.pdf",
                description="my file",
                is_legal_notice=False,
            )
        ]
        certificate_document_deferred = False

        issues = get_application_complete_issues(
            application, test_db_session, certificate_document_deferred
        )

        assert issues == expected_issues

    @mock.patch(
        "massgov.pfml.api.validation.application_rules.get_documents_issues", return_value=[]
    )
    def test_get_application_complete_issues_missing_absence_id(self, test_db_session):
        claim = ClaimFactory.build(fineos_absence_id=None)
        application = ApplicationFactory.build(
            is_withholding_tax=False, claim=claim, has_submitted_payment_preference=True
        )
        certificate_document_deferred = False

        issues = get_application_complete_issues(
            application, test_db_session, certificate_document_deferred
        )

        assert issues == [
            ValidationErrorDetail(
                type=IssueType.object_not_found,
                message="A case must exist before it can be marked as complete.",
            )
        ]

    @mock.patch(
        "massgov.pfml.api.validation.application_rules.get_documents_issues", return_value=[]
    )
    def test_get_application_complete_issues_missing_part1_field(self, test_db_session):
        # This validation doesn't care if a new "Submit" rule isn't fulfilled, as long as a claim
        # with a Fineos absence ID exists.
        application = ApplicationFactory.build(
            is_withholding_tax=False,
            claim=ClaimFactory.build(),
            first_name=None,
            has_submitted_payment_preference=True,
        )
        certificate_document_deferred = False

        issues = get_application_complete_issues(
            application, test_db_session, certificate_document_deferred
        )

        assert not issues
