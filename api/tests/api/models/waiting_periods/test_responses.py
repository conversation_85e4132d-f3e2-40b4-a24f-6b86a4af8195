from datetime import date

from massgov.pfml.api.models.waiting_periods.responses import WaitingPeriod, WaitingPeriodResponse


def test_is_benefit_year_waiting_period_true():
    waiting_period = WaitingPeriod(
        waiting_period_start_date=date(2024, 4, 14),
        waiting_period_end_date=date(2024, 4, 20),
        _earliest_approved_start_date=date(2024, 1, 1),
    )

    waiting_periods_response = WaitingPeriodResponse.from_orm(waiting_period).dict()

    assert waiting_periods_response["is_benefit_year_waiting_period"] is True


def test_is_benefit_year_waiting_period_false():
    waiting_period = WaitingPeriod(
        waiting_period_start_date=date(2024, 1, 1),
        waiting_period_end_date=date(2024, 1, 8),
        _earliest_approved_start_date=date(2024, 1, 1),
    )

    waiting_periods_responses = WaitingPeriodResponse.from_orm(waiting_period).dict()

    assert waiting_periods_responses["is_benefit_year_waiting_period"] is False
