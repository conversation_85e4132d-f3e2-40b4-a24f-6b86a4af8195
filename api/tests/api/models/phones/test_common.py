import pydantic
import pytest

from massgov.pfml.api.models.phones.common import Phone
from massgov.pfml.api.validation.exceptions import IssueType


@pytest.mark.parametrize(
    "int_code,phone_number,field_name",
    [
        # field name = None
        (None, "************", None),
        (None, "2028675309", None),
        (None, None, None),
        ("1", "************", None),
        ("1", "2028675309", None),
        ("1", None, None),
        # field name = ""
        (None, "************", ""),
        (None, "2028675309", ""),
        (None, None, ""),
        ("1", "************", ""),
        ("1", "2028675309", ""),
        ("1", None, ""),
        # field name = phone.phone_number
        (None, "************", "phone.phone_number"),
        (None, "2028675309", "phone.phone_number"),
        (None, None, "phone.phone_number"),
        ("1", "************", "phone.phone_number"),
        ("1", "2028675309", "phone.phone_number"),
        ("1", None, "phone.phone_number"),
        # field name = contact_phone.phone_number
        (None, "************", "contact_phone.phone_number"),
        (None, "2028675309", "contact_phone.phone_number"),
        (None, None, "contact_phone.phone_number"),
        ("1", "************", "contact_phone.phone_number"),
        ("1", "2028675309", "contact_phone.phone_number"),
        ("1", None, "contact_phone.phone_number"),
        # field name = tpa_contact_phone.phone_number
        (None, "************", "tpa_contact_phone.phone_number"),
        (None, "2028675309", "tpa_contact_phone.phone_number"),
        (None, None, "tpa_contact_phone.phone_numberr"),
        ("1", "************", "tpa_contact_phone.phone_number"),
        ("1", "2028675309", "tpa_contact_phone.phone_number"),
        ("1", None, "tpa_contact_phone.phone_number"),
    ],
)
def test_check_phone_number_validation_success(int_code, phone_number, field_name):
    phone = Phone(
        int_code=int_code,
        phone_number=phone_number,
        field_name=field_name,
    )

    assert phone.phone_number == phone_number


@pytest.mark.parametrize(
    "int_code,phone_number,field_name",
    [
        # field name = None
        (None, "************", None),
        (None, "5555555555", None),
        (None, "", None),
        ("1", "************", None),
        ("1", "5555555555", None),
        ("1", "", None),
        # field name = ""
        (None, "************", ""),
        (None, "5555555555", ""),
        (None, "", ""),
        ("1", "************", ""),
        ("1", "5555555555", ""),
        ("1", "", ""),
        # field name = phone.phone_number
        (None, "************", "phone.phone_number"),
        (None, "5555555555", "phone.phone_number"),
        (None, "", "phone.phone_number"),
        ("1", "************", "phone.phone_number"),
        ("1", "5555555555", "phone.phone_number"),
        ("1", "", "phone.phone_number"),
        # field name = contact_phone.phone_number
        (None, "************", "contact_phone.phone_number"),
        (None, "5555555555", "contact_phone.phone_number"),
        (None, "", "contact_phone.phone_number"),
        ("1", "************", "contact_phone.phone_number"),
        ("1", "5555555555", "contact_phone.phone_number"),
        ("1", "", "contact_phone.phone_number"),
        # field name = tpa_contact_phone.phone_number
        (None, "************", "tpa_contact_phone.phone_number"),
        (None, "5555555555", "tpa_contact_phone.phone_number"),
        (None, "", "tpa_contact_phone.phone_numberr"),
        ("1", "************", "tpa_contact_phone.phone_number"),
        ("1", "5555555555", "tpa_contact_phone.phone_number"),
        ("1", "", "tpa_contact_phone.phone_number"),
    ],
)
def test_check_phone_number_validation_exception(int_code, phone_number, field_name):
    with pytest.raises(pydantic.ValidationError, match="Phone number must be a valid number") as e:
        phone = Phone(  # noqa: F841
            int_code=int_code,
            phone_number=phone_number,
            field_name=field_name,
        )

    errors = e.value.errors()
    assert len(errors) == 1

    err_ctx = errors[0]["ctx"]
    err_loc = errors[0]["loc"]
    assert err_ctx["message"] == "Phone number must be a valid number"
    assert err_ctx["type"] == IssueType.invalid_phone_number
    assert err_ctx["rule"] == "phone_number_must_be_valid_number"
    assert len(err_loc) == 1
    assert err_loc[0] == "phone_number"
