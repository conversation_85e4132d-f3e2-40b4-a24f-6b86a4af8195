from datetime import datetime, timedelta, timezone

import pytest
from authlib.jose import jwt

import massgov.pfml.api.app
import massgov.pfml.api.authentication as authentication
import massgov.pfml.api.services.jwt
import massgov.pfml.api.services.oauth_server
from massgov.pfml.api.services.mock_jwt import (
    MOCK_OAUTH_SERVER_PAYLOAD,
    MockKMSAsymmetricJWTManager,
)


@pytest.fixture(autouse=True)
def mock_azure(monkeypatch, azure_auth_keys):
    def mock_get_public_keys(_, url):
        return azure_auth_keys.get("keys")

    monkeypatch.setattr(
        massgov.pfml.api.authentication.azure.AzureClientConfig,
        "_get_public_keys",
        mock_get_public_keys,
    )
    monkeypatch.setenv("AZURE_AD_CLIENT_ID", "client_id")
    monkeypatch.setenv("AZURE_AD_TENANT_ID", "tenant_id")
    monkeypatch.setenv("AZURE_AD_AUTHORITY_DOMAIN", "example.com")
    monkeypatch.setenv("AZURE_AD_CLIENT_SECRET", "secret_value")
    monkeypatch.setenv("ADMIN_PORTAL_BASE_URL", "http://localhost:3001")
    monkeypatch.setenv("AZURE_AD_PARENT_GROUP", "TSS-SG-PFML_ADMIN_PORTAL_NON_PROD")

    return authentication.configure_azure_ad()


@pytest.fixture(autouse=True)
def mock_usbank(monkeypatch):
    """
    Automatically set required USBankClientConfig environment variables for all tests.
    """
    monkeypatch.setenv("USBANK_CLIENT_BASE_URL", "https://3.ghi.test/oauth2/token")
    monkeypatch.setenv("USBANK_CLIENT_SOAP_BASE_URL", "https://1.ghi.test/oauth2/token")
    monkeypatch.setenv("USBANK_CLIENT_CERTIFICATE_BINARY_ARN", "1234567890abcdefghij")
    monkeypatch.setenv("USBANK_CLIENT_CERTIFICATE_PASSWORD", "abcdefghijklmnopqrstuvwxyz")


@pytest.fixture(autouse=True)
def mock_lmg(monkeypatch, lmg_auth_keys):

    def mock_get_public_keys(_, url):
        return lmg_auth_keys.get("keys")

    monkeypatch.setattr(
        massgov.pfml.api.authentication.lmg.LMGConfigs,
        "_get_public_keys",
        mock_get_public_keys,
    )

    monkeypatch.setenv("LMG_PERSONAL_BASE_URL", "https://lmg.example.com")
    monkeypatch.setenv("LMG_PERSONAL_APPLICATION_ID", "application-id")
    monkeypatch.setenv("LMG_PERSONAL_POLICY_SISU", "B2C_1A_CITIZEN_SISU")
    monkeypatch.setenv("LMG_PERSONAL_POLICY_CHANGE_EMAIL", "B2C_1A_CITIZEN_CHANGEEMAIL")
    monkeypatch.setenv("LMG_PERSONAL_POLICY_CHANGE_MFA", "B2C_1A_CITIZEN_CHANGEMFA")
    monkeypatch.setenv("LMG_PERSONAL_POLICY_CHANGE_NAME", "B2C_1A_CITIZEN_CHANGENAME")
    monkeypatch.setenv("LMG_PERSONAL_POLICY_CHANGE_PW", "B2C_1A_CITIZEN_CHANGEPASSWORD")
    monkeypatch.setenv("PORTAL_BASE_URL", "https://localhost:3000")

    monkeypatch.setenv("LMG_BUSINESS_BASE_URL", "https://lmg.example.com")
    monkeypatch.setenv("LMG_BUSINESS_APPLICATION_ID", "application-id")
    monkeypatch.setenv("LMG_BUSINESS_POLICY_SISU", "B2C_1A_PARTNER_SISU")
    monkeypatch.setenv("LMG_BUSINESS_POLICY_CHANGE_EMAIL", "B2C_1A_PARTNER_CHANGEEMAIL")
    monkeypatch.setenv("LMG_BUSINESS_POLICY_CHANGE_MFA", "B2C_1A_PARTNER_CHANGEMFA")
    monkeypatch.setenv("LMG_BUSINESS_POLICY_CHANGE_NAME", "B2C_1A_PARTNER_CHANGENAME")
    monkeypatch.setenv("LMG_BUSINESS_POLICY_CHANGE_PW", "B2C_1A_PARTNER_CHANGEPASSWORD")
    monkeypatch.setenv("PORTAL_BASE_URL", "https://localhost:3000")

    return authentication.configure_lmg()


@pytest.fixture
def app_cors(monkeypatch, test_db):
    monkeypatch.setenv("CORS_ORIGINS", "http://example.com")
    return massgov.pfml.api.app.create_app(check_migrations_current=False)


@pytest.fixture(scope="session")
def base_app():
    return massgov.pfml.api.app.create_app(
        check_migrations_current=False,
        db_session_factory=None,
        do_close_db=False,
        use_db_factories_session=True,
    )


@pytest.fixture
def app(test_db_session, initialize_factories_session, set_auth_public_keys, base_app):
    return base_app


@pytest.fixture
def client(app):
    return app.test_client()


@pytest.fixture
def app_config(app, monkeypatch):
    base_config = app.app.config["app_config"]

    test_config = base_config.copy(deep=True)

    monkeypatch.setitem(app.app.config, "app_config", test_config)

    return test_config


@pytest.fixture
def enable_verification_limits(app_config):
    app_config.enable_verification_limits = True


@pytest.fixture
def enable_application_fraud_check(app_config):
    app_config.enable_application_fraud_check = True


@pytest.fixture
def limit_ssn_fein_max_attempts(app_config):
    app_config.limit_ssn_fein_max_attempts = 2


@pytest.fixture
def set_auth_public_keys(monkeypatch, auth_key):
    monkeypatch.setattr(authentication, "public_keys", auth_key)


@pytest.fixture(scope="session")
def auth_key():
    hmac_key = {
        "alg": "RS256",
        "e": "AQAB",
        "kid": "c7f7e776-bd29-4d00-b110-d4d4a8652815",
        "kty": "RSA",
        "n": "iWBm-DQbycUqrPBSD5yk73zxyIr66hBUCyPCShW-btQ-nyBk1E-h4AvtqHpl4Y1aghQDTnn2gLHiRtV_XJtCpK1PEJ3SCqw6wGOEw5bbG7Q88KDvTMUF5k6gzRMHMBTD7lMNPIY-oCuh_Rwvg19hGBD2O6rA2sMHyTB-O2ZwL6M",
        "use": "sig",
    }

    return hmac_key


@pytest.fixture(scope="session")
def auth_private_key():
    hmac_key = {
        "alg": "RS256",
        "d": "WC8GyisA73teUpcNxjHCem0U86urN5b1rBTvQglFLfWWoST1NIhNm_lsPGsdfTT0tW1NVhHaV3BYlSm06AFKphL1UtHI0z_xS-CnRuqYljyca1YQWhuFETP01c1tVmA4g8iFGUW_VkQ6QgyHiC_kaz_v8skOLLgLoR6KPeo_yPE",
        "dp": "i8Sa6tKsKrSGsjE6H98dDiTbc_CDogP2-VgNPN5SMa02rki4972o5WmZhiQvcjxlU7NZbeE3fRiiXHt_E_wZan9MFkk",
        "dq": "QRYM74mdgrYHqutTmTY5tuEOsddFiE2NFa-qPagjKQKzvUPhl9EZbkm1VR06K1omw0SoFpxMLc4O3K8Z",
        "e": "AQAB",
        "kid": "67ad345e-8a77-45bb-8988-22aa2ab8ca62",
        "kty": "RSA",
        "n": "iWBm-DQbycUqrPBSD5yk73zxyIr66hBUCyPCShW-btQ-nyBk1E-h4AvtqHpl4Y1aghQDTnn2gLHiRtV_XJtCpK1PEJ3SCqw6wGOEw5bbG7Q88KDvTMUF5k6gzRMHMBTD7lMNPIY-oCuh_Rwvg19hGBD2O6rA2sMHyTB-O2ZwL6M",
        "p": "o2tSqdoRyCMnzT_CZx1Oq8WCwMo7rWMKFx-wlwaXOoxzqDv0YhjP1t7DqDn5V8yERCVBUP9ZPDIzNmBUQMul7bwIpfs",
        "q": "1zQdXV-7I2VNSUhzRAYvhJAOFvAKiJv8lJc2_66XNGww0g3og_sBPrGwFsO2stVd-rJ1mZWV8D78LHR5",
        "qi": "SebQz5QdxAvqGSDUvchSLpxXf0Ry0NhYdBCCMftTwqqVcNjY3GKQ8-YET5Y_dwMmEYM51DCCDolVxBAjbNDlKU7JIjU",
        "use": "sig",
    }

    return hmac_key


@pytest.fixture(scope="session")
def azure_auth_keys():
    """A fake public key for Azure AD"""
    return {
        "keys": [
            {
                "alg": "RS256",
                "e": "AQAB",
                "kid": "azure_kid",
                "kty": "RSA",
                "n": "iWBm-DQbycUqrPBSD5yk73zxyIr66hBUCyPCShW-btQ-nyBk1E-h4AvtqHpl4Y1aghQDTnn2gLHiRtV_XJtCpK1PEJ3SCqw6wGOEw5bbG7Q88KDvTMUF5k6gzRMHMBTD7lMNPIY-oCuh_Rwvg19hGBD2O6rA2sMHyTB-O2ZwL6M",
                "use": "sig",
            }
        ]
    }


@pytest.fixture(scope="session")
def lmg_auth_keys():
    """A fake public key for LMG"""
    return {
        "keys": [
            {
                "alg": "RS256",
                "e": "AQAB",
                "kid": "lmg_kid",
                "kty": "RSA",
                "n": "iWBm-DQbycUqrPBSD5yk73zxyIr66hBUCyPCShW-btQ-nyBk1E-h4AvtqHpl4Y1aghQDTnn2gLHiRtV_XJtCpK1PEJ3SCqw6wGOEw5bbG7Q88KDvTMUF5k6gzRMHMBTD7lMNPIY-oCuh_Rwvg19hGBD2O6rA2sMHyTB-O2ZwL6M",
                "use": "sig",
            }
        ]
    }


@pytest.fixture(scope="session")
def azure_auth_private_key():
    hmac_key = {
        "alg": "RS256",
        "d": "WC8GyisA73teUpcNxjHCem0U86urN5b1rBTvQglFLfWWoST1NIhNm_lsPGsdfTT0tW1NVhHaV3BYlSm06AFKphL1UtHI0z_xS-CnRuqYljyca1YQWhuFETP01c1tVmA4g8iFGUW_VkQ6QgyHiC_kaz_v8skOLLgLoR6KPeo_yPE",
        "dp": "i8Sa6tKsKrSGsjE6H98dDiTbc_CDogP2-VgNPN5SMa02rki4972o5WmZhiQvcjxlU7NZbeE3fRiiXHt_E_wZan9MFkk",
        "dq": "QRYM74mdgrYHqutTmTY5tuEOsddFiE2NFa-qPagjKQKzvUPhl9EZbkm1VR06K1omw0SoFpxMLc4O3K8Z",
        "e": "AQAB",
        "kid": "azure_kid",
        "kty": "RSA",
        "n": "iWBm-DQbycUqrPBSD5yk73zxyIr66hBUCyPCShW-btQ-nyBk1E-h4AvtqHpl4Y1aghQDTnn2gLHiRtV_XJtCpK1PEJ3SCqw6wGOEw5bbG7Q88KDvTMUF5k6gzRMHMBTD7lMNPIY-oCuh_Rwvg19hGBD2O6rA2sMHyTB-O2ZwL6M",
        "p": "o2tSqdoRyCMnzT_CZx1Oq8WCwMo7rWMKFx-wlwaXOoxzqDv0YhjP1t7DqDn5V8yERCVBUP9ZPDIzNmBUQMul7bwIpfs",
        "q": "1zQdXV-7I2VNSUhzRAYvhJAOFvAKiJv8lJc2_66XNGww0g3og_sBPrGwFsO2stVd-rJ1mZWV8D78LHR5",
        "qi": "SebQz5QdxAvqGSDUvchSLpxXf0Ry0NhYdBCCMftTwqqVcNjY3GKQ8-YET5Y_dwMmEYM51DCCDolVxBAjbNDlKU7JIjU",
        "use": "sig",
    }

    return hmac_key


@pytest.fixture(scope="session")
def azure_auth_private_key_bad(azure_auth_private_key):
    # This token is created with an azure private key but lacks the key ID.
    # Therefore, it should not be recognized as an Azure token.
    new_key = azure_auth_private_key.copy()
    del new_key["kid"]  # Authlib automatically moves the claim kid to the header.
    return new_key


@pytest.fixture(scope="session")
def mock_azure_id_token_claims():
    return {"name": "mock_name", "sub": "mock_sub_id", "preferred_username": "mock_email_address"}


@pytest.fixture(scope="session")
def mock_azure_tokens(mock_azure_id_token_claims):
    return {
        "access_token": "test",
        "refresh_token": "test",
        "id_token": "test",
        "id_token_claims": mock_azure_id_token_claims,
    }


@pytest.fixture(scope="session")
def lmg_auth_private_key():
    hmac_key = {
        "alg": "RS256",
        "d": "WC8GyisA73teUpcNxjHCem0U86urN5b1rBTvQglFLfWWoST1NIhNm_lsPGsdfTT0tW1NVhHaV3BYlSm06AFKphL1UtHI0z_xS-CnRuqYljyca1YQWhuFETP01c1tVmA4g8iFGUW_VkQ6QgyHiC_kaz_v8skOLLgLoR6KPeo_yPE",
        "dp": "i8Sa6tKsKrSGsjE6H98dDiTbc_CDogP2-VgNPN5SMa02rki4972o5WmZhiQvcjxlU7NZbeE3fRiiXHt_E_wZan9MFkk",
        "dq": "QRYM74mdgrYHqutTmTY5tuEOsddFiE2NFa-qPagjKQKzvUPhl9EZbkm1VR06K1omw0SoFpxMLc4O3K8Z",
        "e": "AQAB",
        "kid": "lmg_kid",
        "kty": "RSA",
        "n": "iWBm-DQbycUqrPBSD5yk73zxyIr66hBUCyPCShW-btQ-nyBk1E-h4AvtqHpl4Y1aghQDTnn2gLHiRtV_XJtCpK1PEJ3SCqw6wGOEw5bbG7Q88KDvTMUF5k6gzRMHMBTD7lMNPIY-oCuh_Rwvg19hGBD2O6rA2sMHyTB-O2ZwL6M",
        "p": "o2tSqdoRyCMnzT_CZx1Oq8WCwMo7rWMKFx-wlwaXOoxzqDv0YhjP1t7DqDn5V8yERCVBUP9ZPDIzNmBUQMul7bwIpfs",
        "q": "1zQdXV-7I2VNSUhzRAYvhJAOFvAKiJv8lJc2_66XNGww0g3og_sBPrGwFsO2stVd-rJ1mZWV8D78LHR5",
        "qi": "SebQz5QdxAvqGSDUvchSLpxXf0Ry0NhYdBCCMftTwqqVcNjY3GKQ8-YET5Y_dwMmEYM51DCCDolVxBAjbNDlKU7JIjU",
        "use": "sig",
    }

    return hmac_key


@pytest.fixture(scope="session")
def auth_claims_unit():
    claims = {"exp": datetime.now() + timedelta(days=1), "sub": "foo"}

    return claims


@pytest.fixture(scope="session")
def azure_auth_claims_unit():
    claims = {"exp": datetime.now() + timedelta(days=1), "sub": "foo", "aud": "client_id"}

    return claims


@pytest.fixture(scope="session")
def service_account_auth_claims_unit():
    claims = {"exp": datetime.now() + timedelta(days=1), "sub": "foo", "aud": "aud"}

    return claims


@pytest.fixture(scope="session")
def auth_token_unit(auth_claims_unit, auth_private_key):
    encoded = jwt.encode({"alg": "RS256"}, auth_claims_unit, auth_private_key).decode("utf-8")
    return encoded


@pytest.fixture
def auth_claims(auth_claims_unit, user):
    auth_claims = auth_claims_unit.copy()
    auth_claims["sub"] = str(user.auth_id)

    return auth_claims


@pytest.fixture
def employer_claims(employer_user):
    claims = {
        "exp": datetime.now(timezone.utc) + timedelta(days=1),
        "sub": str(employer_user.auth_id),
    }

    return claims


@pytest.fixture
def consented_user_claims(consented_user):
    claims = {
        "a": "b",
        "exp": datetime.now() + timedelta(days=1),
        "sub": str(consented_user.auth_id),
    }

    return claims


@pytest.fixture
def fineos_user_claims(fineos_user):
    claims = {"a": "b", "exp": datetime.now() + timedelta(days=1), "sub": str(fineos_user.auth_id)}

    return claims


@pytest.fixture
def idp_user_claims(idp_user):
    claims = {"a": "b", "exp": datetime.now() + timedelta(days=1), "sub": str(idp_user.auth_id)}

    return claims


@pytest.fixture
def snow_user_claims(snow_user):
    claims = {"a": "b", "exp": datetime.now() + timedelta(days=1), "sub": str(snow_user.auth_id)}

    return claims


@pytest.fixture
def consented_user_token(consented_user_claims, auth_private_key):
    encoded = jwt.encode({"alg": "RS256"}, consented_user_claims, auth_private_key).decode("utf-8")
    return encoded


@pytest.fixture
def fineos_user_token(fineos_user_claims, auth_private_key):
    encoded = jwt.encode({"alg": "RS256"}, fineos_user_claims, auth_private_key).decode("utf-8")
    return encoded


@pytest.fixture
def idp_user_token(idp_user_claims, auth_private_key):
    encoded = jwt.encode({"alg": "RS256"}, idp_user_claims, auth_private_key).decode("utf-8")
    return encoded


@pytest.fixture
def snow_user_token(snow_user_claims, auth_private_key):
    encoded = jwt.encode({"alg": "RS256"}, snow_user_claims, auth_private_key).decode("utf-8")
    return encoded


@pytest.fixture
def snow_user_headers(snow_user_token):
    return {"Authorization": "Bearer {}".format(snow_user_token), "Mass-PFML-Agent-ID": "123"}


@pytest.fixture
def auth_token(auth_claims, auth_private_key):
    encoded = jwt.encode({"alg": "RS256"}, auth_claims, auth_private_key).decode("utf-8")
    return encoded


@pytest.fixture(scope="session")
def lmg_auth_token_unit(auth_claims_unit, lmg_auth_private_key):
    encoded = jwt.encode(
        {"alg": "RS256", "kid": lmg_auth_private_key.get("kid")},
        auth_claims_unit,
        lmg_auth_private_key,
    ).decode("utf-8")
    return encoded


@pytest.fixture(scope="session")
def azure_auth_token_unit(azure_auth_claims_unit, azure_auth_private_key):
    encoded = jwt.encode(
        {"alg": "RS256", "kid": azure_auth_private_key.get("kid")},
        azure_auth_claims_unit,
        azure_auth_private_key,
    ).decode("utf-8")
    return encoded


@pytest.fixture(scope="session")
def azure_service_account_auth_token_unit(service_account_auth_claims_unit, azure_auth_private_key):
    encoded = jwt.encode(
        {"alg": "RS256", "kid": azure_auth_private_key.get("kid")},
        service_account_auth_claims_unit,
        azure_auth_private_key,
    ).decode("utf-8")
    return encoded


@pytest.fixture
def employer_auth_token(employer_claims, auth_private_key):
    encoded = jwt.encode({"alg": "RS256"}, employer_claims, auth_private_key).decode("utf-8")
    return encoded


@pytest.fixture
def oauth_server_jwt_token(employer_user):
    payload = MOCK_OAUTH_SERVER_PAYLOAD.copy()
    payload["sub"] = str(employer_user.auth_id)

    return MockKMSAsymmetricJWTManager(str(employer_user.auth_id)).sign(payload)


@pytest.fixture
def mock_jwt_manager(monkeypatch, employer_user):

    manager = MockKMSAsymmetricJWTManager(str(employer_user.auth_id))

    monkeypatch.setattr(
        "massgov.pfml.api.services.jwt.KMSAsymmetricJWTManager",
        lambda *args, **kwargs: manager,
    )

    monkeypatch.setattr(
        "massgov.pfml.api.services.oauth_server.KMSAsymmetricJWTManager",
        lambda *args, **kwargs: manager,
    )

    monkeypatch.setattr(
        "massgov.pfml.api.authentication.KMSAsymmetricJWTManager",
        lambda *args, **kwargs: manager,
    )

    monkeypatch.setenv("OAUTH2_KMS_ASYMMETRIC_KEY_ALIAS", "test_kms_key_id")

    return manager
