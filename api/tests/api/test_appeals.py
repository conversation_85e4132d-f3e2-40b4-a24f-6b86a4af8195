from datetime import date, datetime, timedelta, timezone
from unittest import mock

import pytest
from freezegun import freeze_time
from sqlalchemy import inspect

from massgov.pfml.db.lookup_data.appeal import AppealStatus
from massgov.pfml.db.lookup_data.documents import DocumentType
from massgov.pfml.db.models.appeal import Appeal
from massgov.pfml.db.models.factories import (
    AppealFactory,
    ApplicationFactory,
    ClaimFactory,
    DocumentFactory,
)
from tests.api import apply_custom_encoder


@pytest.fixture
def claim_with_application(user):
    claim = ClaimFactory.create(fineos_absence_id="NTN-1989-ABS-01")
    ApplicationFactory.create(claim=claim, user=user)
    return claim


@pytest.fixture
def appeal_ready_for_submission(claim_with_application):
    appeal = AppealFactory.create(
        claim=claim_with_application,
        appeal_phone_number="+12314535511",
        appeal_reason="Missed deadline",
        appeal_representative_option_id=2,
        for_private_insurance=False,
        has_read_notices=True,
        needs_interpreter=False,
        originally_decided_at=date(2022, 1, 12),
    )
    return appeal


@pytest.fixture
def submitted_appeal(claim_with_application):
    appeal = AppealFactory.create(
        claim=claim_with_application,
        appeal_phone_number="+12314535511",
        appeal_reason="Missed deadline",
        appeal_representative_option_id=2,
        for_private_insurance=False,
        fineos_appeal_id="NTN-12345-ABS-01-AP-01",
        has_read_notices=True,
        needs_interpreter=False,
        originally_decided_at=date(2019, 12, 1),
        submitted_at=datetime(2019, 12, 15).replace(tzinfo=timezone.utc),
    )
    return appeal


@pytest.fixture
def document_less_than_24_hours_old_no_matching_fineos_id(submitted_appeal, user):
    return DocumentFactory.create(
        user_id=user.user_id,
        application_id=None,
        appeal_id=submitted_appeal.appeal_id,
        document_type_id=DocumentType.DRIVERS_LICENSE_MASS.document_type_id,
        # less than 24 hours old
        created_at=(datetime(2020, 1, 1).replace(tzinfo=timezone.utc) - timedelta(hours=6)),
        # does not match one of the values of fineos_document_ids
        fineos_id="111111",
    )


@pytest.fixture
def document_less_than_24_hours_old(submitted_appeal, user):
    return DocumentFactory.create(
        user_id=user.user_id,
        application_id=None,
        appeal_id=submitted_appeal.appeal_id,
        document_type_id=DocumentType.DRIVERS_LICENSE_MASS.document_type_id,
        # less than 24 hours old
        created_at=(datetime(2020, 1, 1).replace(tzinfo=timezone.utc) - timedelta(hours=6)),
        # matches one of the values of fineos_document_ids
        fineos_id="123456",
    )


@pytest.fixture
def document_greater_than_24_hours_old(submitted_appeal, user):
    return DocumentFactory.create(
        user_id=user.user_id,
        application_id=None,
        appeal_id=submitted_appeal.appeal_id,
        document_type_id=DocumentType.DRIVERS_LICENSE_MASS.document_type_id,
        # More than 24 hours old
        created_at=(datetime(2020, 1, 1).replace(tzinfo=timezone.utc) - timedelta(hours=50)),
        # matches one of the values of fineos_document_ids
        fineos_id="654321",
    )


@pytest.fixture
def fineos_document_ids():
    return {"fineos_document_ids": ["123456", "654321"]}


def sqlalchemy_object_as_dict(obj):
    return {c.key: getattr(obj, c.key) for c in inspect(obj).mapper.column_attrs}


def test_create_appeal(client, auth_token, test_db_session, claim_with_application):
    appeal_request = {"fineos_absence_id": claim_with_application.fineos_absence_id}

    response = client.post(
        "/v1/appeals",
        headers={"Authorization": f"Bearer {auth_token}"},
        json=appeal_request,
    )

    assert response.status_code == 201

    response_body = response.json().get("data")
    appeal_id = response_body.get("appeal_id")
    assert appeal_id

    appeal = test_db_session.get(Appeal, appeal_id)
    assert appeal.created_at
    assert appeal.updated_at == appeal.created_at
    assert appeal.claim_id == claim_with_application.claim_id


def test_create_appeal_unauthenticated(client):
    response = client.post("/v1/appeals", headers={"Authorization": f"Bearer {''}"})
    assert response.status_code == 401


def test_create_appeal_forbidden(client, fineos_user_token, claim_with_application):
    appeal_request = {"fineos_absence_id": claim_with_application.fineos_absence_id}

    # Fineos role cannot access this endpoint
    response = client.post(
        "/v1/appeals",
        headers={"Authorization": f"Bearer {fineos_user_token}"},
        json=appeal_request,
    )
    assert response.status_code == 403


def test_create_appeal_invalid_request(client, auth_token):
    # No claim exists with this fineos_absence_id:
    appeal_request = {"fineos_absence_id": "NTN-2000-ABS-01"}

    response = client.post(
        "/v1/appeals",
        headers={"Authorization": f"Bearer {auth_token}"},
        json=appeal_request,
    )

    assert response.status_code == 400


def test_create_appeal_for_another_user(client, auth_token, fineos_user):
    claim = ClaimFactory.create(fineos_absence_id="NTN-2000-ABS-01")
    ApplicationFactory.create(claim=claim, user=fineos_user)
    appeal_request = {"fineos_absence_id": claim.fineos_absence_id}

    response = client.post(
        "/v1/appeals",
        headers={"Authorization": f"Bearer {auth_token}"},
        json=appeal_request,
    )

    assert response.status_code == 403


def test_create_appeal_when_claim_already_has_appeal(client, auth_token, claim, application):
    appeal_request = {"fineos_absence_id": claim.fineos_absence_id}

    response = client.post(
        "/v1/appeals",
        headers={"Authorization": f"Bearer {auth_token}"},
        json=appeal_request,
    )

    assert response.status_code == 201

    appeal_request = {"fineos_absence_id": claim.fineos_absence_id}

    response = client.post(
        "/v1/appeals",
        headers={"Authorization": f"Bearer {auth_token}"},
        json=appeal_request,
    )

    assert response.status_code == 400
    error_message = response.json().get("errors")[0].get("message")
    assert error_message == "Claim already has an appeal."


def test_get_appeal_with_no_uuid(client, auth_token):
    response = client.get(
        "/v1/appeals/undefined", headers={"Authorization": f"Bearer {auth_token}"}
    )

    response_body = response.json()
    assert response.status_code == 400
    assert (
        response_body.get("message")
        == "'undefined' is not a 'uuid'\n\nFailed validating 'format' in schema:\n    {'type': 'string', 'format': 'uuid'}\n\nOn instance:\n    'undefined'"
    )


def test_get_appeal_with_invalid_uuid(client, auth_token):
    response = client.get(
        "/v1/appeals/{}".format("786954d9-9afe-4875-ba48-d1db0261be43"),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 404


def test_get_appeal_unauthorized(client, auth_token):
    # create appeal not associated with user
    appeal = AppealFactory.create()

    response = client.get(
        "/v1/appeals/{}".format(appeal.appeal_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 403


def test_get_incomplete_appeal_succeeds_with_warnings(client, auth_token, claim_with_application):
    appeal = AppealFactory.create(
        claim=claim_with_application,
        # Cause validation errors to be present by omitting required fields
    )

    response = client.get(
        "/v1/appeals/{}".format(appeal.appeal_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 200
    assert len(response.json().get("warnings")) > 0

    response_body = response.json().get("data")
    assert response_body.get("appeal_id") == str(appeal.appeal_id)
    assert response_body.get("appeal_status") is None


def test_get_appeal_partially_displays_phone(client, auth_token, claim_with_application):
    appeal = AppealFactory.create(claim=claim_with_application, appeal_phone_number="+12314535511")

    response = client.get(
        "/v1/appeals/{}".format(appeal.appeal_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 200

    response_body = response.json().get("data")
    phone = response_body.get("appeal_phone_number")
    assert phone["phone_number"] == "***-***-5511"


def test_get_appeal_populates_correct_appeal_status(client, auth_token, claim_with_application):
    appeal = AppealFactory.create(
        claim=claim_with_application, fineos_appeal_id="NTN-1234-ABS-01-AP-01", appeal_status_id=1
    )
    response = client.get(
        "/v1/appeals/{}".format(appeal.appeal_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 200
    response_body = response.json().get("data")

    assert (
        response_body.get("appeal_status")
        == AppealStatus.CLOSED_CLAIM_DECISION_CHANGED.appeal_status_description
    )
    assert len(response.json().get("warnings")) == 0


def test_get_appeal_no_issues_if_submitted(client, auth_token, claim_with_application):
    appeal = AppealFactory.create(
        claim=claim_with_application, fineos_appeal_id="NTN-1234-ABS-01-AP-01"
    )
    response = client.get(
        "/v1/appeals/{}".format(appeal.appeal_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 200
    assert len(response.json().get("warnings")) == 0


def test_update_appeal_unauthorized(client, auth_token):
    # create appeal not associated with user
    appeal = AppealFactory.create()

    response = client.patch(
        "/v1/appeals/{}".format(appeal.appeal_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"for_private_insurance": False},
    )

    assert response.status_code == 403


def test_update_appeal_already_submitted(client, auth_token, claim_with_application):
    appeal = AppealFactory.create(
        claim=claim_with_application, fineos_appeal_id="NTN-1234-ABS-01-AP-01"
    )
    update_request = sqlalchemy_object_as_dict(appeal)
    response = client.patch(
        "/v1/appeals/{}".format(appeal.appeal_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=apply_custom_encoder(update_request),
    )

    assert response.status_code == 400
    error_message = response.json().get("errors")[0].get("message")
    assert (
        error_message
        == f"Appeal {appeal.appeal_id} could not be updated. Appeal already submitted and has ID: {appeal.fineos_appeal_id}"
    )


@freeze_time("2020-01-01")
def test_update_appeal(client, auth_token, test_db_session, claim_with_application):
    appeal = AppealFactory.create(claim=claim_with_application)
    update_request = sqlalchemy_object_as_dict(appeal)

    response = client.patch(
        "/v1/appeals/{}".format(appeal.appeal_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=apply_custom_encoder(update_request),
    )

    response_body = response.json()

    assert response.status_code == 200

    test_db_session.refresh(appeal)

    assert response_body.get("data").get("updated_at") == "2020-01-01T00:00:00+00:00"
    assert len(response.json().get("warnings")) > 0


def test_update_appeal_phone_save(
    client, user, auth_token, test_db_session, claim_with_application
):
    appeal = AppealFactory.create(claim=claim_with_application)
    assert appeal.appeal_phone_number is None

    update_request = sqlalchemy_object_as_dict(appeal)
    update_request["appeal_phone_number"] = {
        "phone_number": "************",
    }

    response = client.patch(
        "/v1/appeals/{}".format(appeal.appeal_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=apply_custom_encoder(update_request),
    )

    response_body = response.json()
    assert response.status_code == 200

    test_db_session.refresh(appeal)

    assert appeal.appeal_phone_number == "+12404879945"

    response_phone = response_body.get("data").get("appeal_phone_number")
    assert response_phone["phone_number"] == "***-***-9945"
    assert response_phone["int_code"] == "1"  # int_code falls back to "1" if not sent


def test_update_appeal_phone_update(
    client, user, auth_token, test_db_session, claim_with_application
):
    appeal = AppealFactory.create(claim=claim_with_application, appeal_phone_number="+12404879945")

    # updating with a masked value doesn't change the existing database value
    update_request_masked_phone_number = {
        "appeal_phone_number": {
            "phone_number": "***-***-9945",
            "int_code": "1",
            "phone_type": "Phone",
        }
    }

    response = client.patch(
        "/v1/appeals/{}".format(appeal.appeal_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_masked_phone_number,
    )

    assert response.status_code == 200

    test_db_session.refresh(appeal)
    assert appeal.appeal_phone_number == "+12404879945"


def test_update_appeal_phone_update_invalid(
    client, user, auth_token, test_db_session, claim_with_application
):
    appeal = AppealFactory.create(claim=claim_with_application, appeal_phone_number="+12404879945")
    # cannnot update to an invalid phone number (fake area code)
    update_request_invalid_phone = {"appeal_phone_number": {"phone_number": "************"}}

    response = client.patch(
        "/v1/appeals/{}".format(appeal.appeal_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=update_request_invalid_phone,
    )

    test_db_session.refresh(appeal)
    assert appeal.appeal_phone_number == "+12404879945"

    assert response.status_code == 400
    error = response.json().get("errors")[0]
    assert error["field"] == "appeal_phone_number.phone_number"
    assert error["message"] == "Phone number must be a valid number"


def test_update_appeal_with_none_sets_to_none(
    client, user, auth_token, claim_with_application, test_db_session
):
    appeal = AppealFactory.create(claim=claim_with_application)
    appeal.appeal_phone_number = "+11234567890"
    test_db_session.commit()

    response = client.get(
        "/v1/appeals/{}".format(appeal.appeal_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    response_body = response.json().get("data")
    assert response_body.get("appeal_phone_number").get("phone_number") == "***-***-7890"

    # set appeal_phone_number to None
    response = client.patch(
        "/v1/appeals/{}".format(appeal.appeal_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json={"appeal_phone_number": None},
    )

    assert response.status_code == 200

    test_db_session.refresh(appeal)
    assert appeal.appeal_phone_number is None
    response_body = response.json()
    assert response_body.get("appeal_phone_number") is None


def test_update_appeal_with_invalid_data(
    client, auth_token, test_db_session, claim_with_application
):
    appeal = AppealFactory.create(claim=claim_with_application)
    update_request = sqlalchemy_object_as_dict(appeal)
    update_request["for_private_insurance"] = "maybe"

    response = client.patch(
        "/v1/appeals/{}".format(appeal.appeal_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=apply_custom_encoder(update_request),
    )

    assert response.status_code == 400
    second_error = response.json().get("errors")[0]
    assert second_error["message"] == "'maybe' is not of type 'boolean'"

    test_db_session.refresh(appeal)
    assert appeal.for_private_insurance is False


def test_search_appeals_with_no_params(client, auth_token):
    response = client.post(
        "/v1/appeals/search",
        headers={"Authorization": f"Bearer {auth_token}"},
        json={},
    )
    assert response.status_code == 400


def test_search_appeals_with_valid_params(client, auth_token, claim_with_application):
    appeal = AppealFactory.create(
        claim=claim_with_application, fineos_appeal_id="NTN-1234-ABS-01-AP-01"
    )
    search_request = {"terms": {"fineos_appeal_id": "NTN-1234-ABS-01-AP-01"}}

    response = client.post(
        "/v1/appeals/search",
        headers={"Authorization": f"Bearer {auth_token}"},
        json=search_request,
    )

    assert response.status_code == 200

    response_body = response.json().get("data")
    assert len(response_body) == 1
    response_appeal = response_body[0]
    appeal_id = response_appeal.get("appeal_id")
    assert appeal_id == str(appeal.appeal_id)

    assert appeal.fineos_appeal_id == response_appeal.get("fineos_appeal_id")


def test_search_appeals_return_multi_appeals(client, user, auth_token, claim_with_application):
    # first appeal
    AppealFactory.create(claim=claim_with_application, appeal_phone_number="+12314535511")

    # second appeal
    claim = ClaimFactory.create()
    ApplicationFactory.create(claim=claim, user=user)
    AppealFactory.create(claim=claim, appeal_phone_number="+12314535511")
    search_request = {"terms": {"appeal_phone_number": {"phone_number": "************"}}}

    response = client.post(
        "/v1/appeals/search",
        headers={"Authorization": f"Bearer {auth_token}"},
        json=search_request,
    )
    response_body = response.json().get("data")
    assert len(response_body) == 2


def test_search_appeals_with_phone_number(client, auth_token, claim_with_application):
    AppealFactory.create(claim=claim_with_application, appeal_phone_number="+12314535511")

    search_request = {"terms": {"appeal_phone_number": {"phone_number": "************"}}}

    response = client.post(
        "/v1/appeals/search",
        headers={"Authorization": f"Bearer {auth_token}"},
        json=search_request,
    )
    response_body = response.json().get("data")
    assert len(response_body) == 1


def test_search_appeals_with_fineos_absence_id(client, auth_token, claim_with_application):
    AppealFactory.create(claim=claim_with_application)

    search_request = {"terms": {"fineos_absence_id": claim_with_application.fineos_absence_id}}

    response = client.post(
        "/v1/appeals/search",
        headers={"Authorization": f"Bearer {auth_token}"},
        json=search_request,
    )

    assert response.status_code == 200

    response_body = response.json().get("data")
    assert len(response_body) == 1


def test_search_appeals_with_unauthorized_appeals(client, auth_token, claim_with_application):
    # first appeal
    AppealFactory.create(claim=claim_with_application, appeal_phone_number="+12314535511")

    # second appeal not linked to the user
    AppealFactory.create(appeal_phone_number="+12314535511")
    search_request = {"terms": {"appeal_phone_number": {"phone_number": "************"}}}

    response = client.post(
        "/v1/appeals/search",
        headers={"Authorization": f"Bearer {auth_token}"},
        json=search_request,
    )

    assert response.status_code == 200
    response_body = response.json().get("data")
    assert len(response_body) == 1


def test_get_appeals_documents(client, user, auth_token, claim_with_application):
    appeal = AppealFactory.create(
        claim=claim_with_application, fineos_appeal_id="NTN-1234-ABS-01-AP-01"
    )
    DocumentFactory.create(
        user_id=user.user_id,
        application_id=None,
        appeal_id=appeal.appeal_id,
        document_type_id=DocumentType.DRIVERS_LICENSE_MASS.document_type_id,
    )
    response = client.get(
        "/v1/appeals/{}/documents".format(appeal.appeal_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 200
    response_data = response.json().get("data")[0]
    assert response_data["content_type"] == "application/pdf"
    assert response_data["document_type"] == "Approval Notice"
    assert response_data["fineos_document_id"] == "3011"
    assert response_data["name"] == "test.pdf"
    assert response_data["description"] == "Mock File"
    assert response_data["user_id"] == str(user.user_id)
    assert response_data["created_at"] is not None


def test_get_appeals_documents_with_invalid_appeal_id(client, auth_token):
    response = client.get(
        "/v1/appeals/{}/documents".format("48081c08-bfe2-45e5-8f5b-44a985e0aec3"),
        headers={"Authorization": f"Bearer {auth_token}"},
    )
    assert response.status_code == 404


def test_submit_incomplete_appeal(client, auth_token, claim_with_application):
    appeal = AppealFactory.create(claim=claim_with_application)

    response = client.post(
        "/v1/appeals/{}/complete".format(appeal.appeal_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    assert response.status_code == 400
    assert len(response.json().get("errors")) > 0


@freeze_time("2020-01-01")
def test_submit_appeal(client, auth_token, test_db_session, appeal_ready_for_submission):
    appeal = appeal_ready_for_submission

    client.post(
        "/v1/appeals/{}/complete".format(appeal.appeal_id),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    test_db_session.refresh(appeal)
    assert appeal.submitted_at == datetime(2020, 1, 1).replace(tzinfo=timezone.utc)
    assert appeal.fineos_appeal_id == "NTN-1989-ABS-01-AP-01"


@freeze_time("2020-01-01")
def test_submit_appeal_still_succeeds_when_pdf_upload_fails(
    client, auth_token, test_db_session, appeal_ready_for_submission
):
    # Mock an exception getting returned by the PDF service's upload_pdf_document function:
    with mock.patch(
        "massgov.pfml.appeals.generate_intake_pdfs.service.GenerateIntakePDFService.upload_pdf_document",
        side_effect=Exception(),
    ):
        appeal = appeal_ready_for_submission

        response = client.post(
            "/v1/appeals/{}/complete".format(appeal.appeal_id),
            headers={"Authorization": f"Bearer {auth_token}"},
        )

        assert response.status_code == 200

        test_db_session.refresh(appeal)
        # appeal is still submitted
        assert appeal.submitted_at == datetime(2020, 1, 1).replace(tzinfo=timezone.utc)
        # upload did not succeed, so no document record for the Appeal Form was created
        assert len(appeal.documents) == 0


@freeze_time("2020-01-03")
@mock.patch("massgov.pfml.api.services.fineos_actions.create_task")
def test_confirm_documents(
    mock_create_task,
    client,
    auth_token,
    submitted_appeal,
    document_greater_than_24_hours_old,
    fineos_document_ids,
):
    response = client.post(
        "/v1/appeals/{}/confirm-documents".format(submitted_appeal.appeal_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=fineos_document_ids,
    )
    response_message = response.json().get("message")
    assert response.status_code == 201
    assert response_message == "Successfully created task"
    mock_create_task.assert_called_once()


def test_confirm_documents_with_invalid_appeal_id(client, auth_token, fineos_document_ids):
    response = client.post(
        "/v1/appeals/{}/confirm-documents".format("200"),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=fineos_document_ids,
    )
    assert response.status_code == 400


@freeze_time("2020-01-01")
def test_confirm_documents_latest_document_is_less_than_24_hours_since_creation_with_multiple_documents(
    user,
    client,
    auth_token,
    submitted_appeal,
    document_less_than_24_hours_old,
    document_less_than_24_hours_old_no_matching_fineos_id,
    document_greater_than_24_hours_old,
    fineos_document_ids,
):
    response = client.post(
        "/v1/appeals/{}/confirm-documents".format(submitted_appeal.appeal_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=fineos_document_ids,
    )
    response_message = response.json().get("message")
    assert response.status_code == 201
    assert (
        response_message
        == "Did not create task due to the appeal or latest document being created less than 24 hours ago."
    )


@freeze_time("2020-01-01")
@mock.patch("massgov.pfml.api.services.fineos_actions.create_task")
def test_confirm_documents_where_document_less_than_24_hours_old_and_fineos_id_matches(
    mock_create_task,
    user,
    client,
    auth_token,
    submitted_appeal,
    document_less_than_24_hours_old,
    fineos_document_ids,
):
    response = client.post(
        "/v1/appeals/{}/confirm-documents".format(submitted_appeal.appeal_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=fineos_document_ids,
    )
    response_message = response.json().get("message")
    assert response.status_code == 201
    assert response_message == "Successfully created task"
    mock_create_task.assert_called_once()


@freeze_time("2020-01-01")
def test_confirm_documents_appeal_is_less_than_24_hours_since_creation(
    client, auth_token, submitted_appeal, fineos_document_ids, test_db_session
):
    submitted_appeal.submitted_at = datetime(2020, 1, 1).replace(tzinfo=timezone.utc)
    test_db_session.commit()
    test_db_session.refresh(submitted_appeal)
    response = client.post(
        "/v1/appeals/{}/confirm-documents".format(submitted_appeal.appeal_id),
        headers={"Authorization": f"Bearer {auth_token}"},
        json=fineos_document_ids,
    )
    response_message = response.json().get("message")
    assert response.status_code == 201
    assert (
        response_message
        == "Did not create task due to the appeal or latest document being created less than 24 hours ago."
    )
