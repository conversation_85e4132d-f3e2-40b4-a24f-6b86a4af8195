import io
from datetime import date, datetime
from typing import Dict
from unittest import mock

import pytest

import massgov.pfml.util.datetime as datetime_util
from massgov.pfml.api.validation.exceptions import ValidationErrorDetail
from massgov.pfml.db.lookup_data.absences import AbsenceReason, AbsenceReasonQualifierOne
from massgov.pfml.db.lookup_data.change_request import ChangeRequestType
from massgov.pfml.db.lookup_data.deferred_submission_item import DeferredSubmissionStatus
from massgov.pfml.db.models.change_request import ChangeRequest
from massgov.pfml.db.models.deferred_submission_item import DeferredSubmissionItem
from massgov.pfml.db.models.factories import (
    AbsencePeriodFactory,
    ApplicationFactory,
    ChangeRequestFactory,
    ClaimFactory,
    EmployeeFactory,
    EmployerFactory,
    TaxIdentifierFactory,
)
from tests.helpers.logging import get_mock_logger


# Run `initialize_factories_session` for all tests,
# so that it doesn't need to be manually included
@pytest.fixture(autouse=True)
def setup_factories(initialize_factories_session):
    return


class TestPostChangeRequest:
    @pytest.fixture
    def request_body(self) -> Dict[str, str]:
        return {
            "change_request_type": "Modification",
            "start_date": "2022-01-01",
            "end_date": "2022-02-01",
        }

    @mock.patch("massgov.pfml.api.change_requests.get_change_request_issues", return_value=[])
    def test_successful_call(
        self,
        mock_change_request_issues,
        auth_token,
        claim,
        client,
        request_body,
        user,
        test_db_session,
    ):
        response = client.post(
            "/v1/change-request?fineos_absence_id={}".format(claim.fineos_absence_id),
            headers={"Authorization": f"Bearer {auth_token}"},
            json=request_body,
        )

        # check whether response is what we expect
        response_body = response.json().get("data")
        assert response.status_code == 201
        assert response_body.get("change_request_id") is not None
        assert response_body.get("change_request_type") == request_body["change_request_type"]
        assert response_body.get("fineos_absence_id") == claim.fineos_absence_id
        assert response_body.get("start_date") == request_body["start_date"]
        assert response_body.get("end_date") == request_body["end_date"]

        # check whether db entry was successfully added
        db_entry = (
            test_db_session.query(ChangeRequest)
            .filter(ChangeRequest.claim_id == claim.claim_id)
            .one_or_none()
        )
        assert db_entry is not None

    @mock.patch("massgov.pfml.api.change_requests.get_change_request_issues", return_value=[])
    def test_empty_payload(
        self,
        mock_change_request_issues,
        auth_token,
        claim,
        client,
    ):
        response = client.post(
            "/v1/change-request?fineos_absence_id={}".format(claim.fineos_absence_id),
            headers={"Authorization": f"Bearer {auth_token}"},
            json={},
        )
        response_body = response.json().get("data")
        assert response.status_code == 201
        assert response_body.get("change_request_id") is not None
        assert response_body.get("change_request_type") is None
        assert response_body.get("fineos_absence_id") == claim.fineos_absence_id
        assert response_body.get("start_date") is None
        assert response_body.get("end_date") is None

    def test_returns_403_when_user_does_not_own_claim(
        self,
        auth_token,
        client,
    ):
        claim = ClaimFactory.create()
        response = client.post(
            "/v1/change-request?fineos_absence_id={}".format(claim.fineos_absence_id),
            headers={"Authorization": f"Bearer {auth_token}"},
            json={},
        )
        response.json().get("data")
        assert response.status_code == 403

    @mock.patch("massgov.pfml.api.change_requests.get_change_request_issues", return_value=[])
    @mock.patch("massgov.pfml.api.change_requests.get_claim_from_db", return_value=None)
    def test_missing_claim(
        self, mock_get_claim, mock_change_request_issues, auth_token, claim, client, request_body
    ):
        response = client.post(
            "/v1/change-request?fineos_absence_id={}".format(claim.fineos_absence_id),
            headers={"Authorization": f"Bearer {auth_token}"},
            json=request_body,
        )

        assert response.status_code == 404
        assert response.json()["message"] == "Claim does not exist for given absence ID"

    @mock.patch(
        "massgov.pfml.api.change_requests.change_request_service.claim_has_in_progress_requests",
        return_value=True,
    )
    def test_in_progress_change_requests(
        self, mock_in_progress_requests, auth_token, claim, client, request_body
    ):
        response = client.post(
            "/v1/change-request?fineos_absence_id={}".format(claim.fineos_absence_id),
            headers={"Authorization": f"Bearer {auth_token}"},
            json=request_body,
        )
        assert response.status_code == 400
        assert (
            response.json()["message"] == "Multiple in-progress requests are not allowed on a claim"
        )


class TestGetChangeRequests:
    mock_logger = get_mock_logger()

    @mock.patch("massgov.pfml.api.change_requests.logger", mock_logger)
    @mock.patch(
        "massgov.pfml.api.change_requests.change_request_service.get_change_requests_from_db"
    )
    def test_successful_get_request(
        self, mock_get_change_requests_from_db, claim, change_request, client, auth_token, user
    ):
        mock_get_change_requests_from_db.return_value = [change_request]

        response = client.get(
            "/v1/change-request?fineos_absence_id={}".format(claim.fineos_absence_id),
            headers={"Authorization": f"Bearer {auth_token}"},
        )

        assert response.status_code == 200
        response_body = response.json()
        assert len(response_body["data"]["change_requests"]) == 1
        assert response_body["data"]["change_requests"][0]["change_request_id"] is not None
        assert response_body["data"]["change_requests"][0]["change_request_type"] == "Extension"
        assert response_body["message"] == "Successfully retrieved change requests"

        self.mock_logger.info.assert_any_call(
            "get_change_requests success", extra={"absence_case_id": claim.fineos_absence_id}
        )

    def test_returns_403_when_user_does_not_own_claim(
        self,
        auth_token,
        client,
    ):
        claim = ClaimFactory.create()
        response = client.get(
            "/v1/change-request?fineos_absence_id={}".format(claim.fineos_absence_id),
            headers={"Authorization": f"Bearer {auth_token}"},
        )
        assert response.status_code == 403

    @mock.patch("massgov.pfml.api.services.change_requests.get_change_requests_from_db")
    def test_successful_get_request_no_change_requests(
        self, mock_get_change_requests_from_db, claim, client, auth_token, user
    ):
        mock_get_change_requests_from_db.return_value = []

        response = client.get(
            "/v1/change-request?fineos_absence_id={}".format(claim.fineos_absence_id),
            headers={"Authorization": f"Bearer {auth_token}"},
        )

        assert response.status_code == 200
        response_body = response.json()
        assert len(response_body["data"]["change_requests"]) == 0

    def test_no_existing_claim(self, client, auth_token, user):
        response = client.get(
            "/v1/change-request?fineos_absence_id={}".format("fake_absence_id"),
            headers={"Authorization": f"Bearer {auth_token}"},
        )

        assert response.status_code == 404
        response_body = response.json()
        assert response_body["message"] == "Claim does not exist for given absence ID"

    def test_unauthorized_user_unsuccessful(self, claim, client, user):
        response = client.get(
            "/v1/change-request?fineos_absence_id={}".format(claim.fineos_absence_id),
            headers={"Authorization": "Bearer fake_auth_token"},
        )

        assert response.status_code == 401


class TestSubmitChangeRequest:
    mock_logger = get_mock_logger()

    @mock.patch("massgov.pfml.api.change_requests.get_or_404")
    @mock.patch("massgov.pfml.api.change_requests.get_change_request_issues", return_value=[])
    @mock.patch("massgov.pfml.api.change_requests.submit_change_request_to_fineos")
    def test_successful_call(
        self,
        mock_submit,
        mock_get_issues,
        mock_get_or_404,
        auth_token,
        change_request,
        client,
    ):
        mock_get_or_404.return_value = change_request
        mock_submit.return_value = change_request
        response = client.post(
            f"/v1/change-request/{change_request.change_request_id}/submit",
            headers={"Authorization": f"Bearer {auth_token}"},
        )
        assert response.status_code == 200
        mock_submit.assert_called_once_with(change_request, change_request.claim, mock.ANY)

    @pytest.fixture
    def with_deferred_submission_enabled(self, mocker):
        mock_get_features_config = mocker.patch("massgov.pfml.api.app.get_features_config")
        mock_get_features_config.return_value.med_to_bonding.enable_delayed_submission_of_modifications = (
            True
        )

    @pytest.fixture
    def with_deferred_submission_disabled(self, mocker):
        mock_get_features_config = mocker.patch("massgov.pfml.api.app.get_features_config")
        mock_get_features_config.return_value.med_to_bonding.enable_delayed_submission_of_modifications = (
            False
        )

    @pytest.fixture
    def m2b_change_request(self, claim):
        claim.claim_start_date = date(2025, 6, 9)
        claim.claim_end_date = date(2025, 6, 20)
        AbsencePeriodFactory.create(
            claim=claim,
            absence_reason_id=AbsenceReason.PREGNANCY_MATERNITY.absence_reason_id,
            absence_reason_qualifier_one_id=AbsenceReasonQualifierOne.BIRTH_DISABILITY.absence_reason_qualifier_one_id,
            absence_period_start_date=date(2025, 6, 1),
            absence_period_end_date=date(2025, 6, 20),
        )
        change_request = ChangeRequestFactory.create(
            change_request_type_id=ChangeRequestType.MEDICAL_TO_BONDING.change_request_type_id,
            claim=claim,
            date_of_birth=date(2025, 6, 20),
            start_date=date(2025, 6, 20),
            end_date=date(2025, 6, 30),
        )
        return change_request

    @pytest.fixture
    def non_m2b_change_request(self, claim):
        claim.claim_start_date = date(2025, 6, 9)
        claim.claim_end_date = date(2025, 6, 20)
        AbsencePeriodFactory.create(
            claim=claim,
            absence_reason_id=AbsenceReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.absence_reason_id,
            absence_period_start_date=date(2025, 6, 1),
            absence_period_end_date=date(2025, 6, 20),
        )
        change_request = ChangeRequestFactory.create(
            change_request_type_id=ChangeRequestType.EXTENSION.change_request_type_id,
            claim=claim,
            start_date=None,
            end_date=date(2025, 6, 30),
            documents_submitted_at=datetime(2025, 6, 20),
        )
        return change_request

    def test_defers_m2b_request_when_ff_on(
        self,
        with_deferred_submission_enabled,
        m2b_change_request,
        auth_token,
        client,
        test_db_session,
    ):
        response = client.post(
            f"/v1/change-request/{m2b_change_request.change_request_id}/submit",
            headers={"Authorization": f"Bearer {auth_token}"},
        )

        deferred_submission_item = (
            test_db_session.query(DeferredSubmissionItem)
            .filter(
                DeferredSubmissionItem.change_request_id == m2b_change_request.change_request_id
            )
            .first()
        )
        test_db_session.refresh(m2b_change_request)

        assert response.status_code == 200
        assert deferred_submission_item is not None
        assert (
            deferred_submission_item.deferred_submission_status_id
            == DeferredSubmissionStatus.PENDING.deferred_submission_status_id
        )
        assert m2b_change_request.submitted_time is None

    def test_doesnt_defer_non_m2b_request_when_ff_on(
        self,
        with_deferred_submission_enabled,
        non_m2b_change_request,
        auth_token,
        client,
        test_db_session,
    ):

        response = client.post(
            f"/v1/change-request/{non_m2b_change_request.change_request_id}/submit",
            headers={"Authorization": f"Bearer {auth_token}"},
        )

        deferred_submission_item = (
            test_db_session.query(DeferredSubmissionItem)
            .filter(
                DeferredSubmissionItem.change_request_id == non_m2b_change_request.change_request_id
            )
            .first()
        )
        test_db_session.refresh(non_m2b_change_request)

        assert response.status_code == 200
        assert deferred_submission_item is None
        assert non_m2b_change_request.submitted_time is not None

    def test_doesnt_defer_m2b_request_when_ff_off(
        self,
        with_deferred_submission_disabled,
        m2b_change_request,
        auth_token,
        client,
        test_db_session,
    ):

        response = client.post(
            f"/v1/change-request/{m2b_change_request.change_request_id}/submit",
            headers={"Authorization": f"Bearer {auth_token}"},
        )

        deferred_submission_item = (
            test_db_session.query(DeferredSubmissionItem)
            .filter(
                DeferredSubmissionItem.change_request_id == m2b_change_request.change_request_id
            )
            .first()
        )
        test_db_session.refresh(m2b_change_request)

        assert response.status_code == 200
        assert deferred_submission_item is None
        assert m2b_change_request.submitted_time is not None

    def test_returns_403_when_user_does_not_own_change_request(
        self,
        auth_token,
        client,
    ):
        change_request = ChangeRequestFactory.create()
        response = client.post(
            f"/v1/change-request/{change_request.change_request_id}/submit",
            headers={"Authorization": f"Bearer {auth_token}"},
        )

        assert response.status_code == 403

    @mock.patch("massgov.pfml.api.change_requests.logger", mock_logger)
    @mock.patch("massgov.pfml.api.change_requests.get_change_request_issues")
    @mock.patch("massgov.pfml.api.change_requests.submit_change_request_to_fineos")
    def test_validation_issues(
        self, mock_submit, mock_get_issues, auth_token, change_request, client
    ):
        validation_errors = [
            ValidationErrorDetail(
                message="start_date required",
                type="required",
                field="start_date",
            )
        ]
        mock_get_issues.return_value = validation_errors
        response = client.post(
            f"/v1/change-request/{change_request.change_request_id}/submit",
            headers={"Authorization": f"Bearer {auth_token}"},
        )
        assert response.status_code == 400
        assert response.json()["message"] == "Invalid change request"
        mock_submit.assert_not_called()

        extra_attrs = {
            "absence_case_id": "foo",
            "change_request.change_request_id": str(change_request.change_request_id),
            "change_request.end_date": str(change_request.end_date),
            "change_request.change_request_type": "Extension",
            "change_request.start_date": str(change_request.start_date),
            "validation_error_list": "['start_date required']",
            "claim_leave_reason": change_request.claim.application.leave_reason.leave_reason_description,
        }

        self.mock_logger.error.assert_any_call(
            "submit_change_request failed - issues", extra=extra_attrs
        )

    def test_missing_claim(self, auth_token, claim, client):
        response = client.post(
            "/v1/change-request/5f91c12b-4d49-4eb0-b5d9-7fa0ce13eb32/submit",
            headers={"Authorization": f"Bearer {auth_token}"},
        )
        assert response.status_code == 404
        assert (
            response.json()["message"]
            == "Could not find ChangeRequest with ID 5f91c12b-4d49-4eb0-b5d9-7fa0ce13eb32"
        )


class TestDeleteChangeRequest:
    def test_success(self, client, auth_token, test_db_session, claim):
        change_request = ChangeRequestFactory.create(claim=claim)

        response = client.delete(
            f"/v1/change-request/{change_request.change_request_id}",
            headers={"Authorization": f"Bearer {auth_token}"},
        )
        assert response.status_code == 200
        response_body = response.json()
        assert response_body["message"] == "Successfully deleted change request"

        db_entry = (
            test_db_session.query(ChangeRequest)
            .filter(ChangeRequest.change_request_id == change_request.change_request_id)
            .one_or_none()
        )
        assert db_entry is None

    def test_returns_403_when_user_does_not_own_change_request(
        self,
        auth_token,
        client,
    ):
        change_request = ChangeRequestFactory.create()
        response = client.delete(
            f"/v1/change-request/{change_request.change_request_id}",
            headers={"Authorization": f"Bearer {auth_token}"},
        )
        assert response.status_code == 403

    def test_missing_change_request(self, client, auth_token):
        response = client.delete(
            "/v1/change-request/009fa369-291b-403f-a85a-15e938c26f2f",
            headers={"Authorization": f"Bearer {auth_token}"},
        )
        assert response.status_code == 404
        response_body = response.json()
        assert (
            response_body["message"]
            == "Could not find ChangeRequest with ID 009fa369-291b-403f-a85a-15e938c26f2f"
        )

    def test_error_on_submitted_change_request(self, client, auth_token, claim):
        change_request = ChangeRequestFactory.create(
            claim=claim, submitted_time=datetime_util.utcnow()
        )
        response = client.delete(
            f"/v1/change-request/{change_request.change_request_id}",
            headers={"Authorization": f"Bearer {auth_token}"},
        )
        assert response.status_code == 400
        response_body = response.json()
        assert response_body["message"] == "Cannot delete a submitted request"


class TestUpdateChangeRequest:
    @pytest.fixture
    def request_body(self) -> Dict[str, str]:
        return {
            "change_request_type": "Medical To Bonding Transition",
            "start_date": "2022-05-01",
            "end_date": "2022-06-01",
        }

    @mock.patch("massgov.pfml.api.change_requests.get_change_request_issues", return_value=[])
    def test_success(
        self, mock_issues, client, auth_token, change_request, request_body, test_db_session
    ):
        response = client.patch(
            f"/v1/change-request/{change_request.change_request_id}",
            headers={"Authorization": f"Bearer {auth_token}"},
            json=request_body,
        )

        test_db_session.refresh(change_request)

        assert response.status_code == 200
        response_body = response.json()
        assert response_body["message"] == "Successfully updated change request"
        assert change_request.start_date == date(2022, 5, 1)
        assert change_request.end_date == date(2022, 6, 1)
        assert change_request.type == "Medical To Bonding Transition"

    def test_returns_403_when_user_does_not_own_change_request(
        self, auth_token, client, request_body
    ):
        change_request = ChangeRequestFactory.create()
        response = client.patch(
            f"/v1/change-request/{change_request.change_request_id}",
            headers={"Authorization": f"Bearer {auth_token}"},
            json=request_body,
        )
        assert response.status_code == 403

    def test_missing_change_request(self, client, auth_token, request_body):
        response = client.patch(
            "/v1/change-request/009fa369-291b-403f-a85a-15e938c26f2f",
            headers={"Authorization": f"Bearer {auth_token}"},
            json=request_body,
        )
        assert response.status_code == 404
        response_body = response.json()
        assert (
            response_body["message"]
            == "Could not find ChangeRequest with ID 009fa369-291b-403f-a85a-15e938c26f2f"
        )


class TestUploadDocument:
    def test_success(self, client, consented_user, consented_user_token):
        form_data = {"document_type": "Passport", "name": "passport.png", "description": "Passport"}
        tax_identifier = TaxIdentifierFactory.create(tax_identifier="*********")
        employer = EmployerFactory.create(employer_fein="*********")
        employee = EmployeeFactory.create(tax_identifier_id=tax_identifier.tax_identifier_id)
        application = ApplicationFactory.create(user=consented_user)

        claim = ClaimFactory.create(
            employer=employer,
            employee=employee,
            application=application,
            fineos_absence_status_id=1,
            claim_type_id=1,
            fineos_absence_id="foo",
        )

        change_request = ChangeRequestFactory.create(
            claim=claim,
            change_request_type_id=ChangeRequestType.EXTENSION.change_request_type_id,
        )

        response = client.post(
            "/v1/change-request/{}/documents".format(change_request.change_request_id),
            headers={"Authorization": f"Bearer {consented_user_token}"},
            data=form_data,
            files={"file": ("test.png", io.BytesIO(b"abcdef"))},
        )

        response_json = response.json()

        assert response_json["status_code"] == 200

        response_data = response_json["data"]
        assert response_data["content_type"] == "image/png"
        assert response_data["description"] == "Passport"
        assert response_data["document_type"] == "Passport"
        assert (
            response_data["fineos_document_id"] == "3011"
        )  # See massgov/pfml/fineos/mock_client.py
        assert response_data["name"] == "passport.png"
        assert response_data["user_id"] == str(consented_user.user_id)
        assert response_data["created_at"] is not None

    def test_returns_403_when_user_does_not_own_change_request(
        self,
        auth_token,
        client,
    ):
        form_data = {"document_type": "Passport", "name": "passport.png", "description": "Passport"}
        change_request = ChangeRequestFactory.create()
        response = client.post(
            "/v1/change-request/{}/documents".format(change_request.change_request_id),
            headers={"Authorization": f"Bearer {auth_token}"},
            data=form_data,
            files={"file": ("test.png", io.BytesIO(b"abcdef"))},
        )

        assert response.status_code == 403

    def test_document_submitted_at(self, client, consented_user, consented_user_token):
        form_data = {"document_type": "Passport", "name": "passport.png", "description": "Passport"}
        tax_identifier = TaxIdentifierFactory.create(tax_identifier="*********")
        employer = EmployerFactory.create(employer_fein="*********")
        employee = EmployeeFactory.create(tax_identifier_id=tax_identifier.tax_identifier_id)
        application = ApplicationFactory.create(user=consented_user)

        claim = ClaimFactory.create(
            employer=employer,
            employee=employee,
            application=application,
            fineos_absence_status_id=1,
            claim_type_id=1,
            fineos_absence_id="foo",
        )

        change_request = ChangeRequestFactory.create(
            claim=claim,
            change_request_type_id=ChangeRequestType.EXTENSION.change_request_type_id,
        )

        client.post(
            "/v1/change-request/{}/documents".format(change_request.change_request_id),
            headers={"Authorization": f"Bearer {consented_user_token}"},
            data=form_data,
            files={"file": ("test.png", io.BytesIO(b"abcdef"))},
        )

        response = client.get(
            "/v1/change-request?fineos_absence_id={}".format(
                change_request.claim.fineos_absence_id
            ),
            headers={"Authorization": f"Bearer {consented_user_token}"},
        )
        response_body = response.json()

        assert response_body["data"]["change_requests"][0]["documents_submitted_at"] is not None
