import copy
import io
from datetime import datetime, timedelta
from unittest import mock

import pytest

import massgov.pfml.fineos.mock_client
import massgov.pfml.util.datetime as datetime_util
from massgov.pfml.api.app import get_app_config
from massgov.pfml.api.exceptions import ClaimWithdrawn
from massgov.pfml.api.models.documents.common import ContentType as AllowedContentTypes
from massgov.pfml.api.validation.exceptions import IssueType, ValidationErrorDetail
from massgov.pfml.db.lookup_data.applications import LeaveReason
from massgov.pfml.db.lookup_data.documents import DocumentType
from massgov.pfml.db.models.factories import (
    AppealFactory,
    ApplicationFactory,
    ClaimFactory,
    DocumentFactory,
    SubmittedAppealFactory,
)
from massgov.pfml.fineos import fineos_client, models
from massgov.pfml.fineos.models.group_client_api import Base64EncodedFileData


@pytest.fixture(autouse=True)
def disable_docs_multipart_upload(app_config):
    app_config.enable_document_multipart_upload = False


@pytest.fixture
def enable_docs_multipart_upload(app_config):
    app_config.enable_document_multipart_upload = True


CERTIFICATION_FORM_DATA = {"document_type": "Certification Form", "name": "certification_form.png"}

VALID_FORM_DATA = {"document_type": "Passport", "name": "passport.png", "description": "Passport"}

VALID_MISSING_NAME_DESCRIPTION_FORM_DATA = {"document_type": "Passport", "description": "Passport"}

MISSING_DOCUMENT_TYPE_FORM_DATA = {"description": "Passport"}

FILE_WITH_NO_EXTENSION = ("test", io.BytesIO(b"abcdef"))

VALID_FILE = ("test.png", io.BytesIO(b"abcdef"))

STATE_MANAGED_PAID_LEAVE_CONFIRMATION_DATA = {
    "document_type": "State managed Paid Leave Confirmation",
    "name": "state_managed_pl_conf_test.png",
    "description": "State managed Paid Leave Confirmation",
}

CARE_FOR_A_FAMILY_MEMBER_FORM_DATA = {
    "document_type": "Care for a family member form",
    "name": "care_test.png",
    "description": "Care for a family member form",
}

OWN_SERIOUS_HEALTH_CONDITION_FORM_DATA = {
    "document_type": "Own serious health condition form",
    "name": "own_test.png",
    "description": "Own serious health condition form",
}

PREGNANCY_MATERNITY_FORM_DATA = {
    "document_type": "Pregnancy/Maternity form",
    "name": "pregnancy_test.png",
    "description": "Pregnancy/Maternity form",
}

CHILD_BONDING_EVIDENCE_FORM_DATA = {
    "document_type": "Child bonding evidence form",
    "name": "bonding_test.png",
    "description": "Child bonding evidence form",
}

MILITARY_EXIGENCY_FORM_DATA = {
    "document_type": "Military exigency form",
    "name": "military_test.png",
    "description": "Military exigency form",
}

GENERAL_CERT_DOC_FORM_DATA = {
    "document_type": "Certification Form",
    "name": "general_test.png",
    "description": "General Certification Form",
}


# TODO: PFMLPB-15511
# https://lwd.atlassian.net/browse/PFMLPB-15511
# This doesn't seem to be used correctly by any of the tests. Investigate and clean up.
@pytest.fixture(autouse=True)
def make_new_plan_proofs_active(monkeypatch):
    monkeypatch.setenv(
        "NEW_PLAN_PROOFS_ACTIVE_AT", (datetime.now() - timedelta(days=1)).isoformat()
    )


def valid_file_connexion_3():
    return ("test.png", io.BytesIO(b"abcdef"))


def invalid_file():
    return ("test.txt", io.BytesIO(b"abcdef"))


def document_upload_helper(client, user, auth_token, form_data, leave_reason_id=None, file=None):
    claim = ClaimFactory.create(
        fineos_notification_id="NTN-111", fineos_absence_id="NTN-111-ABS-01"
    )

    if not leave_reason_id:
        leave_reason_id = LeaveReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.leave_reason_id

    pregnant_or_recent_birth = leave_reason_id == LeaveReason.PREGNANCY_MATERNITY.leave_reason_id

    application = ApplicationFactory.create(
        user=user,
        claim=claim,
        leave_reason_id=leave_reason_id,
        pregnant_or_recent_birth=pregnant_or_recent_birth,
        submitted_time=datetime_util.utcnow(),
    )

    response = client.post(
        "/v1/applications/{}/documents".format(application.application_id),
        headers={
            "Authorization": f"Bearer {auth_token}",
            "X-FF-Disable-Overlapping-Benefit-Year-Claim-Creation": "true",
        },
        data=form_data,
        files={"file": file},
    )

    return response.json()


def document_upload_payload_helper(form_data):
    payload = form_data

    return payload


def test_document_upload_success(client, consented_user, consented_user_token):
    response = document_upload_helper(
        client=client,
        user=consented_user,
        auth_token=consented_user_token,
        form_data=document_upload_payload_helper(VALID_FORM_DATA.copy()),
        file=VALID_FILE,
    )

    assert response["status_code"] == 200
    response_data = response["data"]
    assert response_data["content_type"] == "image/png"
    assert response_data["description"] == "Passport"
    assert response_data["document_type"] == "Passport"
    assert response_data["fineos_document_id"] == "3011"  # See massgov/pfml/fineos/mock_client.py
    assert response_data["name"] == "passport.png"
    assert response_data["user_id"] == str(consented_user.user_id)
    assert response_data["created_at"] is not None


def test_appeal_document_upload_success(client, consented_user, consented_user_token):
    claim = ClaimFactory.create(fineos_absence_id="NTN-1989-ABS-01")
    ApplicationFactory.create(claim_id=claim.claim_id, user=consented_user)
    appeal = SubmittedAppealFactory.create(claim=claim)

    response = client.post(
        "/v1/appeals/{}/documents".format(appeal.appeal_id),
        headers={"Authorization": f"Bearer {consented_user_token}"},
        data=document_upload_payload_helper(VALID_FORM_DATA.copy()),
        files={"file": VALID_FILE},
    )

    response = response.json()
    assert response["status_code"] == 200

    response_data = response["data"]
    assert response_data["appeal_id"] == str(appeal.appeal_id)
    assert response_data["application_id"] is None
    assert response_data["content_type"] == "image/png"
    assert response_data["description"] == "Passport"
    assert response_data["document_type"] == "Passport"
    assert response_data["fineos_document_id"] == "3011"  # See massgov/pfml/fineos/mock_client.py
    assert response_data["name"] == "passport.png"
    assert response_data["user_id"] == str(consented_user.user_id)
    assert response_data["created_at"] is not None


def test_appeal_document_upload_fails_without_fineos_appeal_id(
    client, consented_user, consented_user_token
):
    claim = ClaimFactory.create(fineos_absence_id="NTN-1989-ABS-01")
    ApplicationFactory.create(claim_id=claim.claim_id, user=consented_user)
    appeal = AppealFactory.create(claim=claim)

    response = client.post(
        "/v1/appeals/{}/documents".format(appeal.appeal_id),
        headers={"Authorization": f"Bearer {consented_user_token}"},
        data=document_upload_payload_helper(VALID_FORM_DATA.copy()),
        files={"file": VALID_FILE},
    )

    response = response.json()
    assert response["status_code"] == 400
    assert response["errors"] is not None
    assert len(response["errors"]) == 1
    assert response["errors"][0]["type"] == "object_not_found"
    assert response["errors"][0]["message"] == "Appeal does not have a FINEOS ID."


def test_document_upload_uses_multipart_upload_when_flag_enabled(
    enable_docs_multipart_upload, client, consented_user, consented_user_token
):
    massgov.pfml.fineos.mock_client.start_capture()

    response = document_upload_helper(
        client=client,
        user=consented_user,
        auth_token=consented_user_token,
        form_data=document_upload_payload_helper(VALID_FORM_DATA.copy()),
        file=VALID_FILE,
    )

    assert response["status_code"] == 200

    capture = massgov.pfml.fineos.mock_client.get_capture()
    actions = [fineos_action[0] for fineos_action in capture]
    assert "upload_document_multipart" in actions


def test_document_upload_unauthorized_application_user(client, user, consented_user_token):
    response = document_upload_helper(
        client=client,
        user=user,
        auth_token=consented_user_token,
        form_data=document_upload_payload_helper(VALID_FORM_DATA.copy()),
        file=VALID_FILE,
    )

    assert response["status_code"] == 403
    assert response["errors"] is not None


@mock.patch("massgov.pfml.api.validation.application_rules.get_invalid_benefit_year_issue")
def test_document_upload_invalid_benefit_years(
    mock_get_invalid_benefit_year_issue,
    client,
    consented_user,
    consented_user_token,
):
    mock_get_invalid_benefit_year_issue.return_value = ValidationErrorDetail(
        type=IssueType.employee_has_invalid_benefit_year,
        message="The employee with matching tax identifier has an invalid benefit year.",
    )

    response = document_upload_helper(
        client=client,
        user=consented_user,
        auth_token=consented_user_token,
        form_data=document_upload_payload_helper(VALID_MISSING_NAME_DESCRIPTION_FORM_DATA.copy()),
        file=VALID_FILE,
    )

    message = "The employee with matching tax identifier has an invalid benefit year."
    assert response["status_code"] == 400
    assert response["message"] == message
    assert response["errors"] == [
        {
            "type": IssueType.employee_has_invalid_benefit_year.value,
            "message": message,
        }
    ]


def test_document_upload_unauthorized_consented_user(client, user, auth_token):
    response = document_upload_helper(
        client=client,
        user=user,
        auth_token=auth_token,
        form_data=document_upload_payload_helper(VALID_FORM_DATA.copy()),
        file=VALID_FILE,
    )

    assert response["status_code"] == 403
    assert response["errors"] is not None


def test_document_upload_invalid_filename(client, consented_user, consented_user_token):
    response = document_upload_helper(
        client=client,
        user=consented_user,
        auth_token=consented_user_token,
        form_data=document_upload_payload_helper(VALID_FORM_DATA.copy()),
        file=FILE_WITH_NO_EXTENSION,
    )

    assert response["status_code"] == 400
    assert response["errors"] is not None
    assert len(response["errors"]) == 1
    assert response["errors"][0]["type"] == "file_name_extension"
    assert response["errors"][0]["rule"] == "File name extension required."


def test_document_upload_invalid_content_type(client, consented_user, consented_user_token):
    response = document_upload_helper(
        client=client,
        user=consented_user,
        auth_token=consented_user_token,
        form_data=document_upload_payload_helper(VALID_FORM_DATA.copy()),
        file=invalid_file(),
    )

    assert response["status_code"] == 400
    assert response["errors"] is not None
    assert len(response["errors"]) == 1
    assert response["errors"][0]["type"] == "file_type"

    allowed_content_types = [item.value for item in AllowedContentTypes]
    assert response["errors"][0]["rule"] == ", ".join(allowed_content_types)


def test_document_upload_invalid_form_data(client, consented_user, consented_user_token):
    response = document_upload_helper(
        client=client,
        user=consented_user,
        auth_token=consented_user_token,
        form_data=document_upload_payload_helper(MISSING_DOCUMENT_TYPE_FORM_DATA.copy()),
        file=VALID_FILE,
    )

    assert response["status_code"] == 400
    assert response["errors"] is not None
    assert len(response["errors"]) == 1
    assert response["errors"][0]["type"] == "required"
    assert response["errors"][0]["message"] == "'document_type' is a required property"


def test_document_upload_defaults_for_name(client, consented_user, consented_user_token):
    response = document_upload_helper(
        client=client,
        user=consented_user,
        auth_token=consented_user_token,
        form_data=document_upload_payload_helper(VALID_MISSING_NAME_DESCRIPTION_FORM_DATA.copy()),
        file=VALID_FILE,
    )

    assert response["status_code"] == 200
    assert response["data"]["name"] == "test.png"


def test_caring_leave_doc_upload(client, consented_user, consented_user_token):
    response = document_upload_helper(
        client=client,
        user=consented_user,
        auth_token=consented_user_token,
        form_data=document_upload_payload_helper(CARE_FOR_A_FAMILY_MEMBER_FORM_DATA.copy()),
        leave_reason_id=LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id,
        file=VALID_FILE,
    )

    assert response["status_code"] == 200

    response_data = response["data"]
    assert response_data["content_type"] == "image/png"
    assert response_data["description"] == "Care for a family member form"
    assert response_data["document_type"] == "Care for a family member form"
    assert response_data["fineos_document_id"] == "3011"  # See massgov/pfml/fineos/mock_client.py
    assert response_data["name"] == "care_test.png"
    assert response_data["user_id"] == str(consented_user.user_id)
    assert response_data["created_at"] is not None


def test_own_serious_health_doc_upload(client, consented_user, consented_user_token):
    response = document_upload_helper(
        client=client,
        user=consented_user,
        auth_token=consented_user_token,
        form_data=document_upload_payload_helper(OWN_SERIOUS_HEALTH_CONDITION_FORM_DATA.copy()),
        leave_reason_id=LeaveReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.leave_reason_id,
        file=VALID_FILE,
    )

    assert response["status_code"] == 200

    response_data = response["data"]
    assert response_data["content_type"] == "image/png"
    assert response_data["description"] == "Own serious health condition form"
    assert response_data["document_type"] == "Own serious health condition form"
    assert response_data["fineos_document_id"] == "3011"  # See massgov/pfml/fineos/mock_client.py
    assert response_data["name"] == "own_test.png"
    assert response_data["user_id"] == str(consented_user.user_id)
    assert response_data["created_at"] is not None


def test_pregnancy_maternity_doc_upload(client, consented_user, consented_user_token):
    response = document_upload_helper(
        client=client,
        user=consented_user,
        auth_token=consented_user_token,
        form_data=document_upload_payload_helper(PREGNANCY_MATERNITY_FORM_DATA.copy()),
        leave_reason_id=LeaveReason.PREGNANCY_MATERNITY.leave_reason_id,
        file=VALID_FILE,
    )

    assert response["status_code"] == 200

    response_data = response["data"]
    assert response_data["content_type"] == "image/png"
    assert response_data["description"] == "Pregnancy/Maternity form"
    assert response_data["document_type"] == "Pregnancy/Maternity form"
    assert response_data["fineos_document_id"] == "3011"  # See massgov/pfml/fineos/mock_client.py
    assert response_data["name"] == "pregnancy_test.png"
    assert response_data["user_id"] == str(consented_user.user_id)
    assert response_data["created_at"] is not None


def test_child_bonding_doc_upload(client, consented_user, consented_user_token):
    response = document_upload_helper(
        client=client,
        user=consented_user,
        auth_token=consented_user_token,
        form_data=document_upload_payload_helper(
            CHILD_BONDING_EVIDENCE_FORM_DATA.copy(),
        ),
        leave_reason_id=LeaveReason.CHILD_BONDING.leave_reason_id,
        file=VALID_FILE,
    )

    assert response["status_code"] == 200

    response_data = response["data"]
    assert response_data["content_type"] == "image/png"
    assert response_data["description"] == "Child bonding evidence form"
    assert response_data["document_type"] == "Child bonding evidence form"
    assert response_data["fineos_document_id"] == "3011"  # See massgov/pfml/fineos/mock_client.py
    assert response_data["name"] == "bonding_test.png"
    assert response_data["user_id"] == str(consented_user.user_id)
    assert response_data["created_at"] is not None


def test_military_exigency_doc_upload(
    client,
    consented_user,
    consented_user_token,
):
    response = document_upload_helper(
        client=client,
        user=consented_user,
        auth_token=consented_user_token,
        form_data=document_upload_payload_helper(MILITARY_EXIGENCY_FORM_DATA.copy()),
        file=VALID_FILE,
    )

    assert response["status_code"] == 200

    response_data = response["data"]
    assert response_data["content_type"] == "image/png"
    assert response_data["description"] == "Military exigency form"
    assert response_data["document_type"] == "Military exigency form"
    assert response_data["fineos_document_id"] == "3011"  # See massgov/pfml/fineos/mock_client.py
    assert response_data["name"] == "military_test.png"
    assert response_data["user_id"] == str(consented_user.user_id)
    assert response_data["created_at"] is not None


def test_general_cert_doc_upload(
    client,
    consented_user,
    consented_user_token,
):
    response = document_upload_helper(
        client=client,
        user=consented_user,
        auth_token=consented_user_token,
        form_data=document_upload_payload_helper(GENERAL_CERT_DOC_FORM_DATA.copy()),
        leave_reason_id=LeaveReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.leave_reason_id,
        file=VALID_FILE,
    )

    assert response["status_code"] == 200

    response_data = response["data"]
    assert response_data["content_type"] == "image/png"
    assert response_data["description"] == "General Certification Form"
    assert response_data["document_type"] == "Own serious health condition form"
    assert response_data["fineos_document_id"] == "3011"  # See massgov/pfml/fineos/mock_client.py
    assert response_data["name"] == "general_test.png"
    assert response_data["user_id"] == str(consented_user.user_id)
    assert response_data["created_at"] is not None


# TODO: (API-1647) This test should be removed once State manage Paid Leave Confirmation is obsolete
def test_old_document_type_saved(client, consented_user, consented_user_token):
    claim = ClaimFactory.create(
        fineos_notification_id="NTN-111", fineos_absence_id="NTN-111-ABS-01"
    )

    application = ApplicationFactory.create(user=consented_user, claim=claim)

    # Create a document with the STATE_MANAGED_PAID_LEAVE_CONFIRMATION document type
    DocumentFactory.create(
        user_id=consented_user.user_id,
        application_id=application.application_id,
        document_type_id=DocumentType.STATE_MANAGED_PAID_LEAVE_CONFIRMATION.document_type_id,
    )

    # POST a document with one of the new document types
    response = client.post(
        "/v1/applications/{}/documents".format(application.application_id),
        headers={"Authorization": f"Bearer {consented_user_token}"},
        data=document_upload_payload_helper(CARE_FOR_A_FAMILY_MEMBER_FORM_DATA.copy()),
        files={"file": VALID_FILE},
    ).json()

    assert response["status_code"] == 200

    response_data = response["data"]
    # Assert that the document has the old, rather than the new, document type
    assert response_data["document_type"] == "State managed Paid Leave Confirmation"
    assert response_data["content_type"] == "image/png"
    assert response_data["description"] == "Care for a family member form"
    assert response_data["fineos_document_id"] == "3011"  # See massgov/pfml/fineos/mock_client.py
    assert response_data["name"] == "care_test.png"
    assert response_data["user_id"] == str(consented_user.user_id)
    assert response_data["created_at"] is not None


# TODO: (API-1647) This test should also be removed once State manage Paid Leave Confirmation is obsolete
def test_use_old_doc_type_before_plan_proofs_active(
    client, consented_user, consented_user_token, app
):
    # When the new service pack is live, ALL existing claims should use the old doc types
    # Even claims where only part 1 is submitted and there are no existing documents

    claim = ClaimFactory.create(
        fineos_notification_id="NTN-111", fineos_absence_id="NTN-111-ABS-01"
    )

    application = ApplicationFactory.create(
        user=consented_user,
        claim=claim,
        submitted_time=get_app_config(app).new_plan_proofs_active_at - timedelta(days=1),
    )

    # POST a document with one of the new document types
    response = client.post(
        "/v1/applications/{}/documents".format(application.application_id),
        headers={"Authorization": f"Bearer {consented_user_token}"},
        data=document_upload_payload_helper(CARE_FOR_A_FAMILY_MEMBER_FORM_DATA.copy()),
        files={"file": VALID_FILE},
    ).json()

    assert response["status_code"] == 200

    response_data = response["data"]
    # Assert that the document has the old, rather than the new, document type
    assert response_data["document_type"] == "State managed Paid Leave Confirmation"
    assert response_data["content_type"] == "image/png"
    assert response_data["description"] == "Care for a family member form"


# TODO: (API-1647) This test should also be removed once State manage Paid Leave Confirmation is obsolete
def test_use_new_doc_type_after_plan_proofs_active(
    client, consented_user, consented_user_token, app
):
    # When the new service pack is live, ALL existing claims should use the old doc types
    # Even claims where only part 1 is submitted and there are no existing documents

    claim = ClaimFactory.create(
        fineos_notification_id="NTN-111", fineos_absence_id="NTN-111-ABS-01"
    )

    application = ApplicationFactory.create(
        user=consented_user,
        claim=claim,
        submitted_time=get_app_config(app).new_plan_proofs_active_at + timedelta(days=1),
        leave_reason_id=LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id,
    )

    # POST a document with one of the new document types
    response = client.post(
        "/v1/applications/{}/documents".format(application.application_id),
        headers={"Authorization": f"Bearer {consented_user_token}"},
        data=document_upload_payload_helper(
            CARE_FOR_A_FAMILY_MEMBER_FORM_DATA.copy(),
        ),
        files={"file": VALID_FILE},
    ).json()

    assert response["status_code"] == 200

    response_data = response["data"]
    # Assert that the document has the new document type, rather than State managed Paid Leave Confirmation
    assert response_data["document_type"] == "Care for a family member form"
    assert response_data["content_type"] == "image/png"
    assert response_data["description"] == "Care for a family member form"


# TODO: (API-1647) This test should also be removed once State manage Paid Leave Confirmation is obsolete
def test_document_type_with_id_doc(client, consented_user, consented_user_token):
    # Regression test to to verify that we do not switch ID doc types to the State managed
    # Paid Leave Confirmation type

    claim = ClaimFactory.create(
        fineos_notification_id="NTN-111", fineos_absence_id="NTN-111-ABS-01"
    )

    application = ApplicationFactory.create(user=consented_user, claim=claim)

    # Create a document with the STATE_MANAGED_PAID_LEAVE_CONFIRMATION document type
    DocumentFactory.create(
        user_id=consented_user.user_id,
        application_id=application.application_id,
        document_type_id=DocumentType.STATE_MANAGED_PAID_LEAVE_CONFIRMATION.document_type_id,
    )

    # POST a document with one of the ID document types to make sure it isn't overwritten with cert doc type
    response = client.post(
        "/v1/applications/{}/documents".format(application.application_id),
        headers={"Authorization": f"Bearer {consented_user_token}"},
        data=document_upload_payload_helper(VALID_FORM_DATA.copy()),
        files={"file": VALID_FILE},
    ).json()

    assert response["status_code"] == 200

    response_data = response["data"]
    assert response_data["content_type"] == "image/png"
    assert response_data["description"] == "Passport"
    assert response_data["document_type"] == "Passport"
    assert response_data["fineos_document_id"] == "3011"  # See massgov/pfml/fineos/mock_client.py
    assert response_data["name"] == "passport.png"
    assert response_data["user_id"] == str(consented_user.user_id)
    assert response_data["created_at"] is not None


@pytest.mark.parametrize(
    "document_type,expected_client_function_calls",
    (
        # Expect to make call because document type is associated with evidence.
        (
            DocumentType.IDENTIFICATION_PROOF.document_type_description,
            ("find_employer", "register_api_user", "upload_document", "mark_document_as_received"),
        ),
        # DO NOT expect to make call because document type is not associated with evidence.
        (
            DocumentType.PASSPORT.document_type_description,
            ("find_employer", "register_api_user", "upload_document"),
        ),
    ),
)
def test_document_upload(
    client,
    consented_user,
    consented_user_token,
    document_type,
    expected_client_function_calls,
):
    form_data = VALID_FORM_DATA.copy()
    form_data["document_type"] = document_type

    massgov.pfml.fineos.mock_client.start_capture()
    response = document_upload_helper(
        client=client,
        user=consented_user,
        auth_token=consented_user_token,
        form_data=document_upload_payload_helper(form_data),
        file=VALID_FILE,
    )
    assert response["status_code"] == 200

    capture = massgov.pfml.fineos.mock_client.get_capture()

    assert len(capture) == len(expected_client_function_calls)

    for i in range(len(capture)):
        assert capture[i][0] == expected_client_function_calls[i]


@pytest.mark.parametrize(
    "document_type,expected_client_function_calls",
    (
        (
            DocumentType.CARE_FOR_A_FAMILY_MEMBER_FORM.document_type_description,
            ("find_employer", "register_api_user", "upload_document", "mark_document_as_received"),
        ),
        (
            DocumentType.OWN_SERIOUS_HEALTH_CONDITION_FORM.document_type_description,
            ("find_employer", "register_api_user", "upload_document", "mark_document_as_received"),
        ),
        (
            DocumentType.PREGNANCY_MATERNITY_FORM.document_type_description,
            ("find_employer", "register_api_user", "upload_document", "mark_document_as_received"),
        ),
        (
            DocumentType.CHILD_BONDING_EVIDENCE_FORM.document_type_description,
            ("find_employer", "register_api_user", "upload_document", "mark_document_as_received"),
        ),
        (
            DocumentType.MILITARY_EXIGENCY_FORM.document_type_description,
            ("find_employer", "register_api_user", "upload_document", "mark_document_as_received"),
        ),
    ),
)
def test_new_document_types_upload(
    client,
    consented_user,
    consented_user_token,
    document_type,
    expected_client_function_calls,
):
    form_data = VALID_FORM_DATA.copy()
    form_data["document_type"] = document_type

    massgov.pfml.fineos.mock_client.start_capture()
    response = document_upload_helper(
        client=client,
        user=consented_user,
        auth_token=consented_user_token,
        form_data=document_upload_payload_helper(form_data),
        file=VALID_FILE,
    )
    assert response["status_code"] == 200

    capture = massgov.pfml.fineos.mock_client.get_capture()

    assert len(capture) == len(expected_client_function_calls)

    for i in range(len(capture)):
        assert capture[i][0] == expected_client_function_calls[i]


# When the new plan proofs go into effect on 7/1, the front end will upload certification documents
# with the document_type "Certification Form," and the API will map a plan proof based on the leave reason
# for the application.  In the meantime, if the front end uses "State managed Paid Leave Confirmation,"
# the existing logic will continue to be used
def test_document_upload_plan_proofs_state_managed_paid_leave_confirmation(
    client, consented_user, consented_user_token
):
    # TODO (CP-2029): Remove this test case once State manage Paid Leave Confirmation is obsolete
    STATE_MANAGED_PAID_LEAVE_CONFIRMATION_FORM_DATA = {
        "document_type": "State managed Paid Leave Confirmation",
        "name": "certification_form.png",
    }

    response = document_upload_helper(
        client=client,
        user=consented_user,
        auth_token=consented_user_token,
        form_data=document_upload_payload_helper(
            STATE_MANAGED_PAID_LEAVE_CONFIRMATION_FORM_DATA.copy(),
        ),
        file=VALID_FILE,
    )

    assert response["status_code"] == 200

    response_data = response["data"]
    assert response_data["content_type"] == "image/png"
    assert response_data["description"] == ""
    assert response_data["document_type"] == "State managed Paid Leave Confirmation"
    assert response_data["fineos_document_id"] == "3011"  # See massgov/pfml/fineos/mock_client.py
    assert response_data["name"] == "certification_form.png"
    assert response_data["user_id"] == str(consented_user.user_id)
    assert response_data["created_at"] is not None


def test_document_upload_plan_proofs_care_for_a_family_member_form(
    client, consented_user, consented_user_token
):
    response = document_upload_helper(
        client=client,
        user=consented_user,
        auth_token=consented_user_token,
        form_data=document_upload_payload_helper(CERTIFICATION_FORM_DATA.copy()),
        leave_reason_id=LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id,
        file=VALID_FILE,
    )

    assert response["status_code"] == 200

    response_data = response["data"]
    assert response_data["content_type"] == "image/png"
    assert response_data["description"] == ""
    assert (
        response_data["document_type"]
        == DocumentType.CARE_FOR_A_FAMILY_MEMBER_FORM.document_type_description
    )
    assert response_data["fineos_document_id"] == "3011"  # See massgov/pfml/fineos/mock_client.py
    assert response_data["name"] == "certification_form.png"
    assert response_data["user_id"] == str(consented_user.user_id)
    assert response_data["created_at"] is not None


def test_document_upload_plan_proofs_pregnancy_maternity_form(
    client, consented_user, consented_user_token
):
    response = document_upload_helper(
        client=client,
        user=consented_user,
        auth_token=consented_user_token,
        form_data=document_upload_payload_helper(CERTIFICATION_FORM_DATA.copy()),
        leave_reason_id=LeaveReason.PREGNANCY_MATERNITY.leave_reason_id,
        file=VALID_FILE,
    )

    assert response["status_code"] == 200

    response_data = response["data"]
    assert response_data["content_type"] == "image/png"
    assert response_data["description"] == ""
    assert (
        response_data["document_type"]
        == DocumentType.PREGNANCY_MATERNITY_FORM.document_type_description
    )
    assert response_data["fineos_document_id"] == "3011"  # See massgov/pfml/fineos/mock_client.py
    assert response_data["name"] == "certification_form.png"
    assert response_data["user_id"] == str(consented_user.user_id)
    assert response_data["created_at"] is not None


def test_document_upload_plan_proofs_child_bonding_evidence_form(
    client, consented_user, consented_user_token
):
    response = document_upload_helper(
        client=client,
        user=consented_user,
        auth_token=consented_user_token,
        form_data=document_upload_payload_helper(CERTIFICATION_FORM_DATA.copy()),
        leave_reason_id=LeaveReason.CHILD_BONDING.leave_reason_id,
        file=VALID_FILE,
    )

    assert response["status_code"] == 200

    response_data = response["data"]
    assert response_data["content_type"] == "image/png"
    assert response_data["description"] == ""
    assert (
        response_data["document_type"]
        == DocumentType.CHILD_BONDING_EVIDENCE_FORM.document_type_description
    )
    assert response_data["fineos_document_id"] == "3011"  # See massgov/pfml/fineos/mock_client.py
    assert response_data["name"] == "certification_form.png"
    assert response_data["user_id"] == str(consented_user.user_id)
    assert response_data["created_at"] is not None


def test_document_upload_plan_proofs_own_serious_health_condition_form(
    client, consented_user, consented_user_token
):
    response = document_upload_helper(
        client=client,
        user=consented_user,
        auth_token=consented_user_token,
        form_data=document_upload_payload_helper(CERTIFICATION_FORM_DATA.copy()),
        leave_reason_id=LeaveReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.leave_reason_id,
        file=VALID_FILE,
    )

    assert response["status_code"] == 200

    response_data = response["data"]
    assert response_data["content_type"] == "image/png"
    assert response_data["description"] == ""
    assert (
        response_data["document_type"]
        == DocumentType.OWN_SERIOUS_HEALTH_CONDITION_FORM.document_type_description
    )
    assert response_data["fineos_document_id"] == "3011"  # See massgov/pfml/fineos/mock_client.py
    assert response_data["name"] == "certification_form.png"
    assert response_data["user_id"] == str(consented_user.user_id)
    assert response_data["created_at"] is not None


def test_documents_get(client, consented_user, consented_user_token):
    claim = ClaimFactory.create(
        fineos_notification_id="NTN-111", fineos_absence_id="NTN-111-ABS-01"
    )
    application = ApplicationFactory.create(user=consented_user, claim=claim)

    response = client.get(
        "/v1/applications/{}/documents".format(application.application_id),
        headers={"Authorization": f"Bearer {consented_user_token}"},
    ).json()

    assert response["status_code"] == 200
    response_data = response["data"][0]

    # See massgov/pfml/fineos/mock_client for the following values
    assert response_data["content_type"] == "application/pdf"
    assert response_data["document_type"] == "Approval Notice"
    assert response_data["fineos_document_id"] == "3011"
    assert response_data["name"] == "test.pdf"
    assert response_data["description"] == "Mock File"
    assert response_data["user_id"] == str(consented_user.user_id)
    assert response_data["created_at"] is not None


def test_documents_get_missing_content_type(client, consented_user, consented_user_token):
    claim = ClaimFactory.create(
        fineos_notification_id="NTN-111", fineos_absence_id="NTN-111-ABS-01"
    )
    application = ApplicationFactory.create(user=consented_user, claim=claim)

    response = client.get(
        "/v1/applications/{}/documents".format(application.application_id),
        headers={"Authorization": f"Bearer {consented_user_token}"},
    ).json()

    assert response["status_code"] == 200


def test_documents_get_missing_document_type(client, consented_user, consented_user_token):
    claim = ClaimFactory.create(
        fineos_notification_id="NTN-111", fineos_absence_id="NTN-111-ABS-01"
    )
    application = ApplicationFactory.create(user=consented_user, claim=claim)

    response = client.get(
        "/v1/applications/{}/documents".format(application.application_id),
        headers={"Authorization": f"Bearer {consented_user_token}"},
    ).json()

    assert response["status_code"] == 200


def test_documents_get_date_created(client, consented_user, consented_user_token, monkeypatch):
    def mocked_mock_document(absence_id):

        # mock the response object using "dateCreated"
        mocked_document = copy.copy(massgov.pfml.fineos.mock_client.MOCK_DOCUMENT_DATA)
        mocked_document.update(
            {
                "caseId": absence_id,
                "dateCreated": "2020-09-01",
                "creationDateTime": "2020-09-01T17:08:17Z",
                "name": "Approval Notice",
                "originalFilename": "test.pdf",
                "fileExtension": ".pdf",
                "receivedDate": "",
            }
        )
        return mocked_document

    monkeypatch.setattr(massgov.pfml.fineos.mock_client, "mock_document", mocked_mock_document)

    claim = ClaimFactory.create(
        fineos_notification_id="NTN-111", fineos_absence_id="NTN-111-ABS-01"
    )
    application = ApplicationFactory.create(user=consented_user, claim=claim)

    response = client.get(
        "/v1/applications/{}/documents".format(application.application_id),
        headers={"Authorization": f"Bearer {consented_user_token}"},
    ).json()

    assert response["status_code"] == 200
    response_data = response["data"][0]

    assert response_data["created_at"] == "2020-09-01"


@mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.download_document")
@pytest.mark.parametrize(
    "name,extension,content,contentType",
    (
        ("test.pdf", "pdf", "Zm9v", "application/pdf"),
        (
            "test.jpeg",
            "jpeg",
            "iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABaElEQVR42mNk",
            "image/jpeg,image/pjpeg",
        ),
        (
            "test.jpeg",
            "jpeg",
            "iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABaElEQVR42mNk",
            "image/jpeg",
        ),
        (
            "test.png",
            "png",
            "iVBORw0KGgoAAAANSUhEUgAAABAAAAAQCAYAAAAf8/9hAAABaElEQVR42mNk",
            "image/png",
        ),
        ("test.pdf", "pdf", "Zm9v", "application/octet-stream"),
    ),
)
def test_documents_download(
    mock_download,
    client,
    consented_user,
    consented_user_token,
    name,
    extension,
    content,
    contentType,
):
    document_id = "3011"
    mock_download.return_value = document_data(
        name, extension, content, contentType if contentType != "application/octet-stream" else None
    )
    claim = ClaimFactory.create(
        fineos_notification_id="NTN-111", fineos_absence_id="NTN-111-ABS-01"
    )
    application = ApplicationFactory.create(user=consented_user, claim=claim)

    response = client.get(
        "/v1/applications/{}/documents/{}".format(application.application_id, document_id),
        headers={"Authorization": f"Bearer {consented_user_token}"},
    )

    assert response.status_code == 200
    assert response.headers.get("Content-Disposition") == f"attachment; filename={name}"
    assert response.headers["content-type"] == contentType


def test_documents_download_matches_document_id(
    client, consented_user, consented_user_token, monkeypatch
):
    # Regression test to ensure that get_document_by_id searches through all documents from FINEOS
    document_id = "3012"

    def mock_get_documents(self, user_id, absence_id):
        # mock the response to return multiple documents
        document_type = "Approval Notice"
        file_name = "test.pdf"
        description = "Mock File"
        document1 = copy.copy(massgov.pfml.fineos.mock_client.MOCK_DOCUMENT_DATA)
        document1.update(
            {
                "caseId": absence_id,
                "name": document_type,
                "fileExtension": ".pdf",
                "originalFilename": file_name,
                "description": description,
                "documentId": 3011,
            }
        )
        document2 = copy.copy(massgov.pfml.fineos.mock_client.MOCK_DOCUMENT_DATA)
        document2.update(
            {
                "caseId": absence_id,
                "name": document_type,
                "fileExtension": ".pdf",
                "originalFilename": file_name,
                "description": description,
                "documentId": 3012,
            }
        )
        return [
            models.customer_api.Document.parse_obj(
                fineos_client.fineos_document_empty_dates_to_none(document1)
            ),
            models.customer_api.Document.parse_obj(
                fineos_client.fineos_document_empty_dates_to_none(document2)
            ),
        ]

    monkeypatch.setattr(
        massgov.pfml.fineos.mock_client.MockFINEOSClient, "get_documents", mock_get_documents
    )

    claim = ClaimFactory.create(
        fineos_notification_id="NTN-111", fineos_absence_id="NTN-111-ABS-01"
    )
    application = ApplicationFactory.create(user=consented_user, claim=claim)

    response = client.get(
        "/v1/applications/{}/documents/{}".format(application.application_id, document_id),
        headers={"Authorization": f"Bearer {consented_user_token}"},
    )

    assert response.status_code == 200
    assert response.headers.get("Content-Disposition") == "attachment; filename=test.pdf"
    assert response.content.startswith(b"\x89PNG\r\n")


def test_documents_download_mismatch_case(
    client, consented_user, consented_user_token, monkeypatch
):
    # Regression test to ensure that get_document_by_id searches through all documents from FINEOS
    document_id = "3012"
    file_extension = ".pdf"

    def mock_get_documents(self, user_id, absence_id):
        # mock the response to return multiple documents
        document_type = "approval notice"
        document1 = copy.copy(massgov.pfml.fineos.mock_client.MOCK_DOCUMENT_DATA)
        document1.update(
            {
                "caseId": absence_id,
                "name": document_type,
                "documentId": document_id,
                "fileExtension": file_extension,
            }
        )

        return [
            models.customer_api.Document.parse_obj(
                fineos_client.fineos_document_empty_dates_to_none(document1)
            )
        ]

    monkeypatch.setattr(
        massgov.pfml.fineos.mock_client.MockFINEOSClient, "get_documents", mock_get_documents
    )

    claim = ClaimFactory.create(
        fineos_notification_id="NTN-111", fineos_absence_id="NTN-111-ABS-01"
    )
    application = ApplicationFactory.create(user=consented_user, claim=claim)

    response = client.get(
        "/v1/applications/{}/documents/{}".format(application.application_id, document_id),
        headers={"Authorization": f"Bearer {consented_user_token}"},
    )

    assert response.status_code == 200
    assert response.headers.get("Content-Disposition") == "attachment; filename=test.pdf"
    assert response.content.startswith(b"\x89PNG\r\n")


def test_documents_download_forbidden(client, fineos_user, fineos_user_token):
    document_id = "3011"

    claim = ClaimFactory.create(
        fineos_notification_id="NTN-111", fineos_absence_id="NTN-111-ABS-01"
    )
    application = ApplicationFactory.create(user=fineos_user, claim=claim)

    response = client.get(
        "/v1/applications/{}/documents/{}".format(application.application_id, document_id),
        headers={"Authorization": f"Bearer {fineos_user_token}"},
    )

    assert response.status_code == 403


def test_documents_get_not_submitted_application(client, consented_user, consented_user_token):
    application = ApplicationFactory.create(user=consented_user)

    response = client.get(
        "/v1/applications/{}/documents".format(application.application_id),
        headers={"Authorization": f"Bearer {consented_user_token}"},
    ).json()

    assert response["status_code"] == 200
    assert response["data"] is not None
    assert len(response["data"]) == 0


@mock.patch("massgov.pfml.api.applications.documents_service.upload_document_to_fineos")
def test_document_upload_withdrawn_error(mock_upload, client, consented_user, consented_user_token):

    mock_upload.side_effect = ClaimWithdrawn()

    response = document_upload_helper(
        client=client,
        user=consented_user,
        auth_token=consented_user_token,
        form_data=document_upload_payload_helper(VALID_FORM_DATA.copy()),
        file=VALID_FILE,
    )

    assert response["status_code"] == 403
    assert response["errors"][0]["type"] == "fineos_claim_withdrawn"


def document_data(name="test.pdf", extension="pdf", content="Zm9v", contentType="application/pdf"):
    return Base64EncodedFileData(
        fileName=name,
        fileExtension=extension,
        base64EncodedFileContents=content,  # for pdf this decodes to "foo"
        contentType=contentType,
        description=None,
        fileSizeInBytes=0,
        managedReqId=None,
    )


mock_response = models.customer_api.Document(
    caseId="PL ABS-1121685-PL ABS-01-OP1124663",
    documentId="1234123",
    name=DocumentType.PREGNANCY_MATERNITY_FORM.document_type_description,
    type="document",
)


@mock.patch(
    "massgov.pfml.services.documents.upload_document.upload_document", return_value=mock_response
)
def test_mocked_fineos_v24_pregnancy_maternity_doc_upload(
    mock_upload, client, consented_user, consented_user_token, fineos_v24_feature_config
):
    response = document_upload_helper(
        client=client,
        user=consented_user,
        auth_token=consented_user_token,
        form_data=document_upload_payload_helper(PREGNANCY_MATERNITY_FORM_DATA.copy()),
        file=VALID_FILE,
    )
    mock_upload.assert_called_once()
    assert response["status_code"] == 200

    response_data = response["data"]
    assert response_data["fineos_document_id"] == "1234123"
    assert "Pregnancy and Maternity form" in mock_upload.call_args.args


@mock.patch(
    "massgov.pfml.services.documents.upload_document.upload_document", return_value=mock_response
)
def test_mocked_pregnancy_maternity_doc_upload(
    mock_upload, client, consented_user, consented_user_token
):
    response = document_upload_helper(
        client=client,
        user=consented_user,
        auth_token=consented_user_token,
        form_data=document_upload_payload_helper(PREGNANCY_MATERNITY_FORM_DATA.copy()),
        file=VALID_FILE,
    )
    mock_upload.assert_called_once()
    assert response["status_code"] == 200

    response_data = response["data"]
    assert response_data["fineos_document_id"] == "1234123"
    assert "Pregnancy/Maternity form" in mock_upload.call_args.args
    assert "Pregnancy and Maternity form" not in mock_upload.call_args.args


@pytest.mark.parametrize(
    "input_json, expected_received_date",
    [
        # All empty: receivedDate should be None
        (
            {
                "effectiveFrom": "",
                "effectiveTo": "",
                "receivedDate": "",
                "dateCreated": "",
                "creationDateTime": "",
            },
            None,
        ),
        # dateCreated exists and creationDateTime is empty: receivedDate is populated
        (
            {
                "effectiveFrom": "",
                "effectiveTo": "",
                "receivedDate": "",
                "dateCreated": "2024-11-11",
                "creationDateTime": "",
            },
            "2024-11-11",
        ),
        # dateCreated exists and creationDateTime does not exist: receivedDate is populated
        (
            {
                "effectiveFrom": "",
                "effectiveTo": "",
                "receivedDate": "",
                "dateCreated": "2024-11-11",
            },
            "2024-11-11",
        ),
        # dateCreated exists and creationDateTime exists: receivedDate is populated
        (
            {
                "effectiveFrom": "",
                "effectiveTo": "",
                "receivedDate": "",
                "dateCreated": "2024-11-11",
                "creationDateTime": "2025-05-22T15:13:38Z",
            },
            "2024-11-11",
        ),
        # dateCreated is empty and creationDateTime exists: receivedDate is populated
        (
            {
                "effectiveFrom": "",
                "effectiveTo": "",
                "receivedDate": "",
                "dateCreated": "",
                "creationDateTime": "2025-05-22T15:13:38Z",
            },
            "2025-05-22",
        ),
        # dateCreated does not exist and creationDateTime exists: receivedDate is populated
        (
            {
                "effectiveFrom": "",
                "effectiveTo": "",
                "receivedDate": "",
                "creationDateTime": "2025-05-22T15:13:38Z",
            },
            "2025-05-22",
        ),
    ],
)
def test_fineos_document_empty_dates_to_none_v24(
    input_json, expected_received_date, fineos_v24_feature_config
):
    result = fineos_client.fineos_document_empty_dates_to_none(input_json.copy())
    assert result["effectiveFrom"] is None
    assert result["effectiveTo"] is None
    assert result["receivedDate"] == expected_received_date
