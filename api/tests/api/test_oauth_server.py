from datetime import timed<PERSON><PERSON>
from unittest.mock import Mock, patch

import pytest
from freezegun import freeze_time
from werkzeug.security import generate_password_hash

from massgov.pfml.api.models.oauth_server.responses import AuthorizationCodeGrantResponse
from massgov.pfml.api.oauth_server import token_exchange
from massgov.pfml.api.services.mock_jwt import (
    MOCK_OAUTH_SERVER_PAYLOAD,
    MockKMSAsymmetricJWTManager,
)
from massgov.pfml.api.validation.exceptions import ValidationException
from massgov.pfml.db.models.factories import (
    EmployerFactory,
    OAuthClientCredentialsFactory,
    OAuthServerCodeFactory,
    UserLeaveAdministratorFactory,
    VerificationFactory,
)
from massgov.pfml.db.models.oauth_server import OAuthClientCredentials


@pytest.fixture
def mock_db_session():
    return Mock()


@pytest.fixture
def valid_oauth_client():
    return OAuthClientCredentials(
        client_id="test_client_id", client_secret=generate_password_hash("test_client_secret")
    )


@pytest.fixture
def auth_code_request_body():
    return {
        "grant_type": "authorization_code",
        "client_id": "test_client_id",
        "client_secret": "test_client_secret",
        "code": "test_auth_code",
    }


@pytest.fixture
def refresh_token_request_body():
    return {
        "grant_type": "refresh_token",
        "client_id": "test_client_id",
        "client_secret": "test_client_secret",
        "refresh_token": "test_refresh_token",
    }


class TestOAuthTokenEndpoint:
    def test_authorization_code_grant_success(
        self,
        mock_jwt_manager,
        employer_user,
        client,
        auth_code_request_body,
        initialize_factories_session,
    ):
        jwt_manager = mock_jwt_manager

        OAuthClientCredentialsFactory()
        oauth_server_code = OAuthServerCodeFactory()
        oauth_server_code.expires_at = oauth_server_code.created_at + timedelta(weeks=52)
        oauth_server_code.user_id = employer_user.user_id

        response = client.post("/v1/oauth/token-exchange", json=auth_code_request_body)
        response_data = response.json()

        assert response.status_code == 200
        assert jwt_manager.verify(response_data["access_token"])["sub"] == str(
            employer_user.auth_id
        )
        assert response_data["expires_in"] == 1799
        assert jwt_manager.verify(response_data["refresh_token"])["sub"] == str(
            employer_user.auth_id
        )
        assert response_data["token_type"] == "Bearer"
        assert response_data["meta"]["grant_type"] == "authorization_code"
        assert "Successfully exchanged authorization code" in response_data["meta"]["message"]

    def test_invalid_grant_type(self, client, auth_code_request_body):
        auth_code_request_body["grant_type"] = "invalid_grant_type"

        response = client.post("/v1/oauth/token-exchange", json=auth_code_request_body)
        response_data = response.json()

        assert response.status_code == 400
        assert "invalid_grant_type" in [error["message"] for error in response_data["errors"]][0]

    def test_authorization_code_invalid_client_id(
        self, client, auth_code_request_body, initialize_factories_session
    ):
        OAuthClientCredentialsFactory()
        auth_code_request_body["client_id"] = "invalid_client_id"

        response = client.post("/v1/oauth/token-exchange", json=auth_code_request_body)
        response_data = response.json()

        assert response.status_code == 401
        assert response_data["message"] == "Invalid client credentials"

    def test_authorization_code_invalid_client_secret(
        self, client, auth_code_request_body, initialize_factories_session
    ):
        OAuthClientCredentialsFactory()
        oauth_server_code = OAuthServerCodeFactory()
        oauth_server_code.expires_at = oauth_server_code.created_at + timedelta(weeks=52)

        auth_code_request_body["code"] = "invalid_client_secret"

        response = client.post("/v1/oauth/token-exchange", json=auth_code_request_body)
        response_data = response.json()

        assert response.status_code == 401
        assert response_data["message"] == "Invalid or expired authorization code"

    def test_refresh_code_grant_success(
        self,
        mock_jwt_manager,
        employer_user,
        client,
        auth_code_request_body,
        refresh_token_request_body,
        initialize_factories_session,
    ):
        jwt_manager = mock_jwt_manager

        OAuthClientCredentialsFactory()
        oauth_server_code = OAuthServerCodeFactory()
        oauth_server_code.expires_at = oauth_server_code.created_at + timedelta(weeks=52)
        oauth_server_code.user_id = employer_user.user_id

        access_code_response = client.post("/v1/oauth/token-exchange", json=auth_code_request_body)
        access_code_response_json = access_code_response.json()

        refresh_token_request_body["refresh_token"] = access_code_response_json["refresh_token"]
        refresh_code_response = client.post(
            "/v1/oauth/token-exchange", json=refresh_token_request_body
        )
        refresh_code_response_data = refresh_code_response.json()

        assert refresh_code_response.status_code == 200
        assert jwt_manager.verify(refresh_code_response_data["access_token"])["sub"] == str(
            employer_user.auth_id
        )
        assert refresh_code_response_data["expires_in"] == 1799
        assert "refresh_token" not in refresh_code_response_data
        assert refresh_code_response_data["token_type"] == "Bearer"
        assert refresh_code_response_data["meta"]["grant_type"] == "refresh_token"
        assert (
            "Successfully exchanged refresh token for a new access token"
            in refresh_code_response_data["meta"]["message"]
        )

    def test_refresh_code_grant_invalid_user(
        self,
        mock_jwt_manager,
        employer_user,
        client,
        auth_code_request_body,
        refresh_token_request_body,
        test_db_session,
        initialize_factories_session,
    ):

        OAuthClientCredentialsFactory()
        oauth_server_code = OAuthServerCodeFactory()
        oauth_server_code.expires_at = oauth_server_code.created_at + timedelta(weeks=52)
        oauth_server_code.user_id = employer_user.user_id

        access_token_response = client.post("/v1/oauth/token-exchange", json=auth_code_request_body)
        access_token_response_data = access_token_response.json()
        # Delete the OAuthServerCode to avoid foreign key constraint
        test_db_session.delete(oauth_server_code)
        # Delete the user to simulate the user being deleted
        test_db_session.delete(employer_user)

        refresh_token_request_body["refresh_token"] = access_token_response_data["refresh_token"]
        refresh_code_response = client.post(
            "/v1/oauth/token-exchange", json=refresh_token_request_body
        )
        refresh_code_response_data = refresh_code_response.json()

        assert refresh_code_response.status_code == 401
        assert refresh_code_response_data["message"] == "User not found"

    def test_refresh_code_grant_invalid_client_id(
        self,
        mock_jwt_manager,
        employer_user,
        client,
        auth_code_request_body,
        refresh_token_request_body,
        initialize_factories_session,
    ):

        OAuthClientCredentialsFactory()
        oauth_server_code = OAuthServerCodeFactory()
        oauth_server_code.expires_at = oauth_server_code.created_at + timedelta(weeks=52)
        oauth_server_code.user_id = employer_user.user_id

        access_code_response = client.post("/v1/oauth/token-exchange", json=auth_code_request_body)
        access_code_response_json = access_code_response.json()

        refresh_token_request_body["refresh_token"] = access_code_response_json["refresh_token"]
        refresh_token_request_body["client_id"] = "invalid_client_id"

        refresh_code_response = client.post(
            "/v1/oauth/token-exchange", json=refresh_token_request_body
        )
        refresh_code_response_data = refresh_code_response.json()

        assert refresh_code_response.status_code == 401
        assert refresh_code_response_data["message"] == "Invalid client credentials"


class TestTokenExchange:
    def test_invalid_grant_type(self, auth_code_request_body):
        auth_code_request_body["grant_type"] = "invalid_type"

        with pytest.raises(ValidationException) as exc_info:
            token_exchange(auth_code_request_body)

        error_details = exc_info.value.errors[0]  # Get first error
        assert error_details.field == "grant_type"
        assert error_details.type == "value_error.const"
        assert "Unexpected value" in error_details.message

    @patch("massgov.pfml.api.oauth_server.handle_authorization_code_grant")
    def test_authorization_code_grant_success(
        self,
        mock_handle_auth_code,
        app,
        auth_code_request_body,
        valid_oauth_client,
        mock_db_session,
    ):
        mock_response = AuthorizationCodeGrantResponse(
            access_token="test_access_token",
            token_type="Bearer",
            expires_in=3600,
            refresh_token="test_refresh_token",
        )
        mock_handle_auth_code.return_value = mock_response

        with app.app.app_context():
            with patch("massgov.pfml.api.oauth_server.app.db_session") as mock_session:
                mock_session.return_value.__enter__.return_value = mock_db_session
                mock_db_session.query().filter().one_or_none.return_value = valid_oauth_client

                result = token_exchange(auth_code_request_body)

                assert result.status_code == 200
                assert "Successfully exchanged authorization code" in result.get_data(as_text=True)

    def test_invalid_client_credentials(self, auth_code_request_body, mock_db_session):
        with patch("massgov.pfml.api.oauth_server.app.db_session") as mock_session:
            mock_session.return_value.__enter__.return_value = mock_db_session
            mock_db_session.query().filter().one_or_none.return_value = None

            result = token_exchange(auth_code_request_body)

            assert result.status_code == 401
            assert "Invalid client credentials" in str(result.body)

    def test_missing_client_id(self):
        request_body = {
            "grant_type": "authorization_code",
            "client_secret": "test_client_secret",
            "code": "test_auth_code",
        }

        with pytest.raises(ValidationException) as exc_info:
            token_exchange(request_body)

        error_details = exc_info.value.errors[0]  # Get first error
        assert error_details.field == "client_id"
        assert error_details.type == "value_error.missing"
        assert "Field required" in error_details.message

    def test_missing_authorization_code(self):
        request_body = {
            "grant_type": "authorization_code",
            "client_id": "test_client_id",
            "client_secret": "test_client_secret",
        }

        with pytest.raises(ValidationException) as exc_info:
            token_exchange(request_body)

        error_details = exc_info.value.errors[0]
        assert error_details.field == "code"
        assert error_details.type == "value_error.missing"
        assert "Field required" in error_details.message


class TestOAuthMeEndpoint:
    @pytest.fixture
    def mock_verification(self, initialize_factories_session):
        return VerificationFactory.create()

    @pytest.fixture
    def leave_admin_one(self, mock_verification, employer_user, initialize_factories_session):
        return UserLeaveAdministratorFactory.create(
            employer=EmployerFactory.create(employer_fein="111111111"),
            verification=mock_verification,
            user=employer_user,
        )

    @pytest.fixture
    def leave_admin_two(self, mock_verification, employer_user, initialize_factories_session):
        return UserLeaveAdministratorFactory.create(
            employer=EmployerFactory.create(employer_fein="111111112"),
            verification=mock_verification,
            user=employer_user,
        )

    def test_me_success(
        self,
        mock_jwt_manager,
        oauth_server_jwt_token,
        employer_user,
        leave_admin_one,
        leave_admin_two,
        initialize_factories_session,
        client,
    ):
        employer_user.first_name = "Jane"
        employer_user.last_name = "Doe"

        OAuthClientCredentialsFactory()
        oauth_server_code = OAuthServerCodeFactory()
        oauth_server_code.expires_at = oauth_server_code.created_at + timedelta(weeks=52)
        oauth_server_code.user_id = employer_user.user_id

        response = client.get(
            "/v1/oauth/me",
            headers={"Authorization": f"Bearer {oauth_server_jwt_token}"},
        )

        assert response.status_code == 200

        response_json = response.json()
        assert response_json == {
            "user": {"id": employer_user.email_address},
            "firstName": "Jane",
            "lastName": "Doe",
            "email": employer_user.email_address,
            "fein": "111111111,111111112",
            "meta": {"method": "GET", "resource": "/v1/oauth/me"},
        }

    def test_me_no_associated_employers(
        self,
        mock_jwt_manager,
        oauth_server_jwt_token,
        employer_user,
        initialize_factories_session,
        client,
    ):
        employer_user.first_name = "Jane"
        employer_user.last_name = "Doe"

        OAuthClientCredentialsFactory()
        oauth_server_code = OAuthServerCodeFactory()
        oauth_server_code.expires_at = oauth_server_code.created_at + timedelta(weeks=52)
        oauth_server_code.user_id = employer_user.user_id

        response = client.get(
            "/v1/oauth/me",
            headers={"Authorization": f"Bearer {oauth_server_jwt_token}"},
        )

        assert response.status_code == 401
        assert "User not associated with any employers" in response.json()["message"]

    def test_me_no_jwt(self, client):
        response = client.get("/v1/oauth/me")

        assert response.status_code == 401
        assert "No authorization token provided" in response.json()["message"]

    def test_me_employee_user(self, user, initialize_factories_session, client, monkeypatch):
        manager = MockKMSAsymmetricJWTManager(str(user.auth_id))

        monkeypatch.setattr(
            "massgov.pfml.api.authentication.KMSAsymmetricJWTManager",
            lambda *args, **kwargs: manager,
        )
        monkeypatch.setattr(
            "massgov.pfml.api.services.oauth_server.KMSAsymmetricJWTManager",
            lambda *args, **kwargs: manager,
        )
        monkeypatch.setenv("OAUTH2_KMS_ASYMMETRIC_KEY_ALIAS", "test_kms_key_id")

        OAuthClientCredentialsFactory()
        oauth_server_code = OAuthServerCodeFactory()
        oauth_server_code.expires_at = oauth_server_code.created_at + timedelta(weeks=52)
        oauth_server_code.user_id = user.user_id

        payload = MOCK_OAUTH_SERVER_PAYLOAD.copy()
        payload["sub"] = str(user.auth_id)

        oauth_server_jwt_token = MockKMSAsymmetricJWTManager(str(user.auth_id)).sign(payload)

        response = client.get(
            "/v1/oauth/me",
            headers={"Authorization": f"Bearer {oauth_server_jwt_token}"},
        )

        assert response.status_code == 403
        assert "does not have read access to EMPLOYER_API" in response.json()["message"]


class TestAuthorizeCodeEndpoint:

    def test_get_authorization_code_success(
        self,
        employer_auth_token,
        client,
    ):

        response = client.get(
            "/v1/oauth/authorize",
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )

        assert response.status_code == 200
        assert response.json()["data"]["authz_code"] is not None
        assert response.json()["data"]["expires_at"] is not None
        assert "Successfully retrieved authz code" in response.json()["message"]

    def test_get_authorization_code_failure(
        self,
        client,
    ):
        response = client.get(
            "/v1/oauth/authorize",
        )

        assert response.status_code == 401
        assert "No authorization token provided" in response.json()["message"]

    @freeze_time("2020-01-01T00:00:00+00:00")
    def test_get_authorization_code_valid_expiration_of_one_hour(
        self,
        employer_auth_token,
        client,
    ):

        response = client.get(
            "/v1/oauth/authorize",
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )

        assert response.status_code == 200
        assert "Successfully retrieved authz code" in response.json()["message"]
        assert response.json()["data"]["expires_at"] is not None
        assert response.json()["data"]["expires_at"] > response.json()["data"]["created_at"]
        assert response.json()["data"]["expires_at"] == "2020-01-01T01:00:00+00:00"
