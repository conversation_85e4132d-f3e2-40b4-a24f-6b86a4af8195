from unittest import mock

import boto3
import pytest
import requests.exceptions

from massgov.pfml.api.constants.documents import DocumentType
from massgov.pfml.api.models.documents.requests import (
    CertificationDocumentData,
    CertificationDocumentIDPType,
)
from massgov.pfml.db.lookup_data.absences import AbsenceStatus
from massgov.pfml.db.lookup_data.employees import ClaimType
from massgov.pfml.db.models.factories import ClaimFactory, EmployeeFactory, EmployerFactory

FILE_NAME = "test_file.pdf"


@pytest.fixture
def employer(initialize_factories_session):
    return EmployerFactory.create(employer_fein="813648030")


@pytest.fixture
def employee():
    return EmployeeFactory.create()


@pytest.fixture
def presigned_url(mock_s3_bucket, expiration_duration=3600):
    # create bucket, object and presigned url
    file_content = b"This is a test file"
    file_content_type = "application/pdf"

    s3_client = boto3.client("s3")
    s3_client.put_object(
        Bucket=mock_s3_bucket, Key=FILE_NAME, Body=file_content, ContentType=file_content_type
    )
    presigned_url = s3_client.generate_presigned_url(
        ClientMethod="get_object",
        Params={"Bucket": mock_s3_bucket, "Key": FILE_NAME},
        ExpiresIn=expiration_duration,
    )
    return presigned_url


@pytest.fixture
def active_claim(employee, employer):
    return ClaimFactory.create_with_leave_request_and_absence_period(
        employee=employee,
        employer=employer,
        claim_type_id=ClaimType.FAMILY_LEAVE.claim_type_id,
        fineos_absence_status_id=AbsenceStatus.ADJUDICATION.absence_status_id,
    )


def _create_presigned_url(mock_s3_bucket, file_name, file_content_type, expiration_duration=3600):
    # create bucket, object and presigned url
    file_content = b"This is a test file"

    s3_client = boto3.client("s3")
    s3_client.put_object(
        Bucket=mock_s3_bucket, Key=file_name, Body=file_content, ContentType=file_content_type
    )
    presigned_url = s3_client.generate_presigned_url(
        ClientMethod="get_object",
        Params={"Bucket": mock_s3_bucket, "Key": file_name},
        ExpiresIn=expiration_duration,
    )
    return presigned_url


# happy path
@mock.patch(
    "massgov.pfml.api.documents.documents_service.get_doc_type_for_fineos_evidence",
)
def test_match_cert_doc_to_claim_success(
    mock_fineos_doc_type, client, employee, active_claim, presigned_url, idp_user_token
):
    document_type = CertificationDocumentIDPType.DFML_FAMILY_SERIOUS_HEALTH_COND
    document_data = CertificationDocumentData(
        first_name=employee.first_name,
        last_name=employee.last_name,
        fineos_absence_id=active_claim.fineos_absence_id,
    )
    cert_doc_request_body = {
        "document_type": document_type,
        "document_data": document_data.__dict__,
        "s3_bucket_url": presigned_url,
    }
    mock_fineos_doc_type.return_value = DocumentType.child_bonding_evidence_form

    # WHEN
    response = client.post(
        "/v1/claims/match-cert-doc-to-claim",
        headers={
            "Authorization": f"Bearer {idp_user_token}",
        },
        json=cert_doc_request_body,
    ).json()

    # THEN
    assert response["status_code"] == 201
    assert response["data"]["document_type"] == DocumentType.child_bonding_evidence_form


# invalid request body: missing Presigned URL
def test_match_cert_doc_to_claim_invalid_request_body(
    client, employee, idp_user_token, active_claim
):
    # GIVEN
    document_type = CertificationDocumentIDPType.DFML_FAMILY_SERIOUS_HEALTH_COND
    document_data = CertificationDocumentData(
        first_name=employee.first_name,
        last_name=employee.last_name,
        fineos_absence_id=active_claim.fineos_absence_id,
    )
    cert_doc_request_body = {
        "document_type": document_type,
        "document_data": document_data.__dict__,
        "s3_bucket_url": "",
    }

    # WHEN
    response = client.post(
        "/v1/claims/match-cert-doc-to-claim",
        headers={
            "Authorization": f"Bearer {idp_user_token}",
        },
        json=cert_doc_request_body,
    ).json()

    # THEN
    assert response["message"] == "Request Validation Error"
    assert response["status_code"] == 400
    assert response["errors"][0]["field"] == "s3_bucket_url"
    assert response["errors"][0]["message"] == "'' should be non-empty"


# Failed to download file from S3 bucket: invalid presigned url
def test_match_cert_doc_to_claim_expired_url(client, employee, active_claim, idp_user_token):

    # GIVEN
    presigned_url = "https://my-bucket-name.s3.amazonaws.com/path/to/file.pdf?AWSAccessKeyId=AKIAIOSFODNN7EXAMPLE&Signature=0H9:0&AWSExpires=0"
    document_type = CertificationDocumentIDPType.DFML_FAMILY_SERIOUS_HEALTH_COND
    document_data = CertificationDocumentData(
        first_name=employee.first_name,
        last_name=employee.last_name,
        fineos_absence_id=active_claim.fineos_absence_id,
    )
    cert_doc_request_body = {
        "document_type": document_type,
        "document_data": document_data.__dict__,
        "s3_bucket_url": presigned_url,
    }

    # WHEN
    response = client.post(
        "/v1/claims/match-cert-doc-to-claim",
        headers={
            "Authorization": f"Bearer {idp_user_token}",
        },
        json=cert_doc_request_body,
    ).json()

    # THEN
    assert response["status_code"] == 404
    assert response["message"] == "Failed to download file from S3 bucket"


# Failed to download file from S3 bucket: S3 error
def test_match_cert_doc_to_claim_s3_error(
    client, employee, active_claim, idp_user_token, presigned_url
):
    # GIVEN
    document_type = CertificationDocumentIDPType.DFML_FAMILY_SERIOUS_HEALTH_COND
    document_data = CertificationDocumentData(
        first_name=employee.first_name,
        last_name=employee.last_name,
        fineos_absence_id=active_claim.fineos_absence_id,
    )
    cert_doc_request_body = {
        "document_type": document_type,
        "document_data": document_data.__dict__,
        "s3_bucket_url": presigned_url,
    }

    # WHEN
    with mock.patch("requests.get", side_effect=requests.exceptions.ConnectTimeout):
        response = client.post(
            "/v1/claims/match-cert-doc-to-claim",
            headers={
                "Authorization": f"Bearer {idp_user_token}",
            },
            json=cert_doc_request_body,
        ).json()

    # THEN
    assert response["status_code"] == 404
    assert response["message"] == "Failed to download file from S3 bucket"


# invalid content_type
def test_match_cert_doc_to_claim_invalid_content_type(
    client, employee, mock_s3_bucket, idp_user_token, active_claim
):
    # GIVEN
    file_name = "test_file.INVALID"
    file_content_type = "application/pdf"
    presigned_url = _create_presigned_url(mock_s3_bucket, file_name, file_content_type)
    document_type = CertificationDocumentIDPType.DFML_FAMILY_SERIOUS_HEALTH_COND
    document_data = CertificationDocumentData(
        first_name=employee.first_name,
        last_name=employee.last_name,
        fineos_absence_id=active_claim.fineos_absence_id,
    )
    cert_doc_request_body = {
        "document_type": document_type,
        "document_data": document_data.__dict__,
        "s3_bucket_url": presigned_url,
    }

    # WHEN
    response = client.post(
        "/v1/claims/match-cert-doc-to-claim",
        headers={
            "Authorization": f"Bearer {idp_user_token}",
        },
        json=cert_doc_request_body,
    ).json()

    # THEN
    assert response["status_code"] == 400
    assert (
        response["message"]
        == "File associated with the presigned URL must have the file extension in its name"
    )


# invalid content_type
def test_match_cert_doc_to_claim_unauthorized_user(client, consented_user_token):
    # GIVEN
    cert_doc_request_body = {}

    # WHEN
    response = client.post(
        "/v1/claims/match-cert-doc-to-claim",
        headers={
            "Authorization": f"Bearer {consented_user_token}",
        },
        json=cert_doc_request_body,
    ).json()

    # THEN
    assert response["status_code"] == 403
    assert (
        response["message"]
        == "You don't have the permission to access the requested resource. It is either read-protected or not readable by the server."
    )
