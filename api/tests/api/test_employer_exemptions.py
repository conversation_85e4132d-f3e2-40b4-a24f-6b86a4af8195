import uuid
from datetime import date, datetime, timedelta, timezone

import faker
import pytest
from authlib.jose import jwt

import massgov.pfml.util.datetime as datetime_util
from massgov.pfml.api.models.employer_exemptions.common import SelfInsuredPlanQuestions
from massgov.pfml.db.lookup_data.employer_exemptions import EmployerExemptionApplicationStatus
from massgov.pfml.db.models.employees import User, UserLeaveAdministrator
from massgov.pfml.db.models.employer_exemptions import EmployerExemptionApplication, InsurancePlan
from massgov.pfml.db.models.factories import (
    EmployerExemptionApplicationDraftFactory,
    EmployerExemptionApplicationDraftPrivatePlanFactory,
    EmployerExemptionApplicationDraftSelfInsuredFamilyFactory,
    EmployerExemptionApplicationDraftSelfInsuredMedicalFactory,
    EmployerExemptionApplicationFactory,
    UserFactory,
    UserLeaveAdministratorFactory,
)
from massgov.pfml.util.collections.dict import flatten
from tests.api import apply_custom_encoder

fake = faker.Faker()


@pytest.fixture
def get_auth_token(auth_private_key):
    def get_auth_token_for_user(user: User):
        payload = {
            "exp": datetime.now(timezone.utc) + timedelta(days=1),
            "sub": str(user.auth_id),
        }
        return jwt.encode({"alg": "RS256"}, payload, auth_private_key).decode("utf-8")

    return get_auth_token_for_user


@pytest.fixture
def create_existing_employer_exemption_application_post_body(
    test_db_session,
    initialize_factories_session,
):
    def get_post_body(leave_admin: UserLeaveAdministrator):
        new_employer_exemption_application = EmployerExemptionApplicationFactory.create(
            employer=leave_admin.employer
        )

        return {
            "employer_id": new_employer_exemption_application.employer_id,
            "created_by_user_id": new_employer_exemption_application.created_by_user_id,
        }

    # test_existing_employer_exemption_application = EmployerExemptionApplicationFactory.create()

    return get_post_body


@pytest.fixture
def create_new_employer_exemption_application_post_body(
    test_db_session, initialize_factories_session
):
    def get_post_body(leave_admin: UserLeaveAdministrator):
        new_employer_exemption_application = EmployerExemptionApplicationFactory.build(
            employer=leave_admin.employer
        )

        return {
            "employer_id": new_employer_exemption_application.employer_id,
            "created_by_user_id": new_employer_exemption_application.created_by_user_id,
        }

    return get_post_body


# TODO (PFMLPB-19806): removal of feature flag. Remove fixture
@pytest.fixture
def create_employer_exemption_application(
    client,
    test_db_session,
    get_auth_token,
    create_new_employer_exemption_application_post_body,
):
    leave_admin = UserLeaveAdministratorFactory.create()
    auth_token = get_auth_token(leave_admin.user)
    post_body = create_new_employer_exemption_application_post_body(leave_admin)

    response = client.post(
        "/v1/employer-exemption-applications",
        headers={
            "Authorization": f"Bearer {auth_token}",
            "X-FF-enable_employer_exemptions_portal": "true",
        },
        json=apply_custom_encoder(post_body),
    )

    return response


# TODO (PFMLPB-19806): removal of feature flag. rename this fixture by removing "_no_ff"
@pytest.fixture
def create_employer_exemption_application_no_ff(
    client,
    test_db_session,
    get_auth_token,
    create_new_employer_exemption_application_post_body,
):
    leave_admin = UserLeaveAdministratorFactory.create()
    auth_token = get_auth_token(leave_admin.user)
    post_body = create_new_employer_exemption_application_post_body(leave_admin)

    response = client.post(
        "/v1/employer-exemption-applications",
        headers={
            "Authorization": f"Bearer {auth_token}",
        },
        json=apply_custom_encoder(post_body),
    )

    return response


# TODO (PFMLPB-19806): removal of feature flag. Remove test
def test_create_employer_exemption_application_service_unavailable(
    create_employer_exemption_application_no_ff,
):
    response = create_employer_exemption_application_no_ff
    assert response.status_code == 503


def test_create_employer_exemption_application_success(create_employer_exemption_application):
    response = create_employer_exemption_application
    data = response.json()["data"]

    assert response.status_code == 201
    assert data["created_by_user_id"]
    assert data["employer_exemption_application_id"]
    assert (
        data["employer_exemption_application_status_id"]
        == EmployerExemptionApplicationStatus.DRAFT.employer_exemption_application_status_id
    )


# TODO (PFMLPB-19806): removal of feature flag. Remove ff from headers
def test_create_employer_exemption_application_failure_record_already_exists(
    client,
    test_db_session,
    get_auth_token,
    create_existing_employer_exemption_application_post_body,
):
    leave_admin = UserLeaveAdministratorFactory.create()
    auth_token = get_auth_token(leave_admin.user)
    post_body = create_existing_employer_exemption_application_post_body(leave_admin)

    response = client.post(
        "/v1/employer-exemption-applications",
        headers={
            "Authorization": f"Bearer {auth_token}",
            "X-FF-enable_employer_exemptions_portal": "true",
        },
        json=apply_custom_encoder(post_body),
    )

    assert response.status_code == 400

    response_json = response.json()
    assert (
        response_json["message"] == "One draft Employer Exemption Application allowed per Employer"
    )
    assert response_json["errors"]

    errors = response_json["errors"][0]

    assert errors["type"] == "duplicate"
    assert errors["rule"] == "disallow_multiple_draft_employer_exemption_applications"

    data = response_json["data"]
    assert data["created_by_user_id"]
    assert data["employer_exemption_application_id"]
    assert (
        data["employer_exemption_application_status_id"]
        == EmployerExemptionApplicationStatus.DRAFT.employer_exemption_application_status_id
    )


def test_get_all_exemption_applications(
    client, test_db_session, auth_private_key, initialize_factories_session
):
    leave_admin = UserLeaveAdministratorFactory.create()
    payload = {
        "exp": datetime.now(timezone.utc) + timedelta(days=1),
        "sub": str(leave_admin.user.auth_id),
    }
    encoded = jwt.encode({"alg": "RS256"}, payload, auth_private_key).decode("utf-8")

    response = client.get(
        "/v1/employer-exemption-applications",
        headers={
            "Authorization": f"Bearer {encoded}",
            "X-FF-enable_employer_exemptions_portal": "true",
        },
    )
    data = response.json().get("data")
    assert len(data[0]["employer_exemption_applications"]) == 0

    EmployerExemptionApplicationFactory.create(employer=leave_admin.employer)
    EmployerExemptionApplicationFactory.create(
        employer=leave_admin.employer, employer_exemption_application_status_id=2
    )
    test_db_session.commit()
    response2 = client.get(
        "/v1/employer-exemption-applications",
        headers={
            "Authorization": f"Bearer {encoded}",
            "X-FF-enable_employer_exemptions_portal": "true",
        },
    )
    data2 = response2.json().get("data")
    assert len(data2[0]["employer_exemption_applications"]) == 2


def test_get_employer_exemption_application_leave_admin(
    client, auth_private_key, initialize_factories_session
):
    leave_admin = UserLeaveAdministratorFactory.create()
    payload = {
        "exp": datetime.now(timezone.utc) + timedelta(days=1),
        "sub": str(leave_admin.user.auth_id),
    }
    encoded = jwt.encode({"alg": "RS256"}, payload, auth_private_key).decode("utf-8")

    employer_exemption_application = EmployerExemptionApplicationFactory.create(
        employer=leave_admin.employer
    )
    employer_exemption_application_id = str(
        employer_exemption_application.employer_exemption_application_id
    )

    response = client.get(
        f"/v1/employer-exemption-applications/{employer_exemption_application_id}/",
        headers={
            "Authorization": f"Bearer {encoded}",
            "X-FF-enable_employer_exemptions_portal": "true",
        },
    )

    assert response.status_code == 200
    data = response.json().get("data")

    assert data["employer_exemption_application_id"] == employer_exemption_application_id


def test_get_employer_exemption_application_leave_admin_different_employer(
    client, auth_private_key, initialize_factories_session
):
    user = UserFactory.create()
    leave_admin = UserLeaveAdministratorFactory.create()
    second_leave_admin = UserLeaveAdministratorFactory.create(user_id=user.user_id)

    payload = {
        "exp": datetime.now(timezone.utc) + timedelta(days=1),
        "sub": str(second_leave_admin.user.auth_id),
    }
    encoded = jwt.encode({"alg": "RS256"}, payload, auth_private_key).decode("utf-8")

    employer_exemption_application = EmployerExemptionApplicationFactory.create(
        employer=leave_admin.employer
    )
    employer_exemption_application_id = str(
        employer_exemption_application.employer_exemption_application_id
    )

    response = client.get(
        f"/v1/employer-exemption-applications/{employer_exemption_application_id}/",
        headers={
            "Authorization": f"Bearer {encoded}",
            "X-FF-enable_employer_exemptions_portal": "true",
        },
    )

    assert response.status_code == 403


def test_get_employer_exemption_application_not_leave_admin(
    client, get_auth_token, initialize_factories_session
):
    user = UserFactory.create()
    leave_admin = UserLeaveAdministratorFactory.create()
    user_auth_token = get_auth_token(user)

    employer_exemption_application = EmployerExemptionApplicationFactory.create(
        employer=leave_admin.employer
    )
    employer_exemption_application_id = str(
        employer_exemption_application.employer_exemption_application_id
    )

    response = client.get(
        f"/v1/employer-exemption-applications/{employer_exemption_application_id}/",
        headers={
            "Authorization": f"Bearer {user_auth_token}",
            "X-FF-enable_employer_exemptions_portal": "true",
        },
    )

    assert response.status_code == 403


def test_delete_employer_exemption_application_leave_admin(
    client, auth_private_key, initialize_factories_session
):
    # Test to ensure golden path deletion case works as expected
    leave_admin = UserLeaveAdministratorFactory.create()
    payload = {
        "exp": datetime.now(timezone.utc) + timedelta(days=1),
        "sub": str(leave_admin.user.auth_id),
    }
    encoded = jwt.encode({"alg": "RS256"}, payload, auth_private_key).decode("utf-8")

    employer_exemption_application = EmployerExemptionApplicationFactory.create(
        employer=leave_admin.employer
    )
    employer_exemption_application_id = str(
        employer_exemption_application.employer_exemption_application_id
    )

    response = client.delete(
        f"/v1/employer-exemption-applications/{employer_exemption_application_id}/",
        headers={
            "Authorization": f"Bearer {encoded}",
            "X-FF-enable_employer_exemptions_portal": "true",
        },
    )

    assert response.status_code == 200


def test_delete_employer_exemption_application_not_found(
    client, auth_private_key, initialize_factories_session
):
    # Test to ensure trying to delete a nonexistent application throws a 404
    leave_admin = UserLeaveAdministratorFactory.create()
    payload = {
        "exp": datetime.now(timezone.utc) + timedelta(days=1),
        "sub": str(leave_admin.user.auth_id),
    }
    encoded = jwt.encode({"alg": "RS256"}, payload, auth_private_key).decode("utf-8")

    response = client.delete(
        f"/v1/employer-exemption-applications/{uuid.uuid4()}/",
        headers={
            "Authorization": f"Bearer {encoded}",
            "X-FF-enable_employer_exemptions_portal": "true",
        },
    )

    assert response.status_code == 404


def test_delete_employer_exemption_application_leave_admin_different_employer(
    client, auth_private_key, initialize_factories_session
):
    # Test to ensure attempting to delete an application for another employer throws a 403
    user = UserFactory.create()
    leave_admin = UserLeaveAdministratorFactory.create()
    second_leave_admin = UserLeaveAdministratorFactory.create(user_id=user.user_id)

    payload = {
        "exp": datetime.now(timezone.utc) + timedelta(days=1),
        "sub": str(second_leave_admin.user.auth_id),
    }
    encoded = jwt.encode({"alg": "RS256"}, payload, auth_private_key).decode("utf-8")

    employer_exemption_application = EmployerExemptionApplicationFactory.create(
        employer=leave_admin.employer
    )
    employer_exemption_application_id = str(
        employer_exemption_application.employer_exemption_application_id
    )

    response = client.delete(
        f"/v1/employer-exemption-applications/{employer_exemption_application_id}/",
        headers={
            "Authorization": f"Bearer {encoded}",
            "X-FF-enable_employer_exemptions_portal": "true",
        },
    )

    assert response.status_code == 403


def test_delete_employer_exemption_application_not_leave_admin(
    client, get_auth_token, initialize_factories_session
):
    user = UserFactory.create()
    leave_admin = UserLeaveAdministratorFactory.create()
    user_auth_token = get_auth_token(user)

    employer_exemption_application = EmployerExemptionApplicationFactory.create(
        employer=leave_admin.employer
    )
    employer_exemption_application_id = str(
        employer_exemption_application.employer_exemption_application_id
    )

    response = client.delete(
        f"/v1/employer-exemption-applications/{employer_exemption_application_id}/",
        headers={
            "Authorization": f"Bearer {user_auth_token}",
            "X-FF-enable_employer_exemptions_portal": "true",
        },
    )

    assert response.status_code == 403


def test_create_employer_exemption_application_malformed_body(
    client,
    test_db_session,
    employer_auth_token,
):
    response = client.post(
        "/v1/employer-exemption-applications",
        headers={
            "Authorization": f"Bearer {employer_auth_token}",
            "X-FF-enable_employer_exemptions_portal": "true",
        },
        json={},
    )
    assert response.status_code == 400

    response_json = response.json()
    assert response_json["message"] in "Request Validation Error"


def test_patch_employer_exemption_application_contact_details(
    client,
    test_db_session,
    initialize_factories_session,
    get_auth_token,
):
    leave_admin = UserLeaveAdministratorFactory.create()
    employer_exemption_application = EmployerExemptionApplicationFactory.create(
        employer=leave_admin.employer,
    )

    patch_request_body = {
        "contact_first_name": "Jane",
        "contact_last_name": "Doe",
        "contact_phone": {
            "int_code": "1",
            "phone_number": "************",
            "phone_type": "Cell",
        },
        "contact_title": "Software Engineer",
        "contact_email_address": "<EMAIL>",
        "has_third_party_administrator": True,
        "tpa_business_name": "TPA Business Name",
        "tpa_contact_first_name": "First",
        "tpa_contact_last_name": "Last",
        "tpa_contact_title": "Title",
        "tpa_contact_phone": {
            "int_code": "1",
            "phone_number": "************",
            "phone_type": "Cell",
        },
        "tpa_contact_email_address": "<EMAIL>",
    }

    validate_patch_employer_exemption_application(
        client,
        test_db_session,
        initialize_factories_session,
        get_auth_token(leave_admin.user),
        patch_request_body,
        employer_exemption_application,
    )


def test_patch_employer_exemption_application_organization_details(
    client,
    test_db_session,
    initialize_factories_session,
    get_auth_token,
):
    leave_admin = UserLeaveAdministratorFactory.create()
    employer_exemption_application = EmployerExemptionApplicationFactory.create(
        employer=leave_admin.employer,
    )

    patch_request_body = {
        "should_workforce_count_include_1099_misc": True,
        "average_workforce_count": 100,
    }

    validate_patch_employer_exemption_application(
        client,
        test_db_session,
        initialize_factories_session,
        get_auth_token(leave_admin.user),
        patch_request_body,
        employer_exemption_application,
    )


def test_patch_employer_exemption_application_insurance_details(
    client,
    test_db_session,
    initialize_factories_session,
    get_auth_token,
):
    leave_admin = UserLeaveAdministratorFactory.create()
    employer_exemption_application = EmployerExemptionApplicationFactory.create(
        employer=leave_admin.employer,
    )

    patch_request_body = {
        "has_family_exemption": True,
        "has_medical_exemption": True,
        "is_self_insured_plan": False,
    }

    validate_patch_employer_exemption_application(
        client,
        test_db_session,
        initialize_factories_session,
        get_auth_token(leave_admin.user),
        patch_request_body,
        employer_exemption_application,
    )


def test_patch_employer_exemption_application_self_insured_plan(
    client,
    test_db_session,
    initialize_factories_session,
    get_auth_token,
):
    leave_admin = UserLeaveAdministratorFactory.create()
    employer_exemption_application = EmployerExemptionApplicationFactory.create(
        employer=leave_admin.employer,
        is_self_insured_plan=True,
    )
    patch_request_body = {}

    self_insured_plan_question_fields = SelfInsuredPlanQuestions()

    for i in SelfInsuredPlanQuestions.__fields__:
        setattr(self_insured_plan_question_fields, i, fake.boolean())

    self_insured_plan_details_fields = {
        "has_obtained_surety_bond": True,
        "surety_bond_amount": fake.random_number(digits=7, fix_len=True) / 100,
        "surety_company": "Surety Company",
        "questions": self_insured_plan_question_fields.__dict__,
    }

    patch_request_body["self_insured"] = self_insured_plan_details_fields
    patch_request_body["insurance_plan_effective_at"] = f"{date.today().year}-01-01"

    validate_patch_employer_exemption_application(
        client,
        test_db_session,
        initialize_factories_session,
        get_auth_token(leave_admin.user),
        patch_request_body,
        employer_exemption_application,
    )


def test_patch_employer_exemption_application_purchased_private_plan(
    client,
    test_db_session,
    initialize_factories_session,
    get_auth_token,
):
    leave_admin = UserLeaveAdministratorFactory.create()
    employer_exemption_application = EmployerExemptionApplicationFactory.create(
        employer=leave_admin.employer,
    )

    patch_request_body = {
        "purchased_plan": {
            "insurance_provider_id": 3,
            "insurance_plan_id": 6,
        },
    }

    validate_patch_employer_exemption_application(
        client,
        test_db_session,
        initialize_factories_session,
        get_auth_token(leave_admin.user),
        patch_request_body,
        employer_exemption_application,
    )


def test_patch_employer_exemption_application_invalid_phone(
    client,
    test_db_session,
    initialize_factories_session,
    get_auth_token,
):
    leave_admin = UserLeaveAdministratorFactory.create()
    employer_exemption_application = EmployerExemptionApplicationFactory.create(
        employer=leave_admin.employer,
    )

    # ************ is a valid phone number in terms of length and structure
    # however, phone numbers starting with 555 are considered invalid by the
    # phonenumbers library (eg. phonenumbers.is_valid_number(************) returns
    # false
    patch_request_body = {
        "contact_phone": {
            "phone_number": "************",
        },
        "tpa_contact_phone": {
            "phone_number": "************",
        },
    }

    response = client.patch(
        "/v1/employer-exemption-applications/{}".format(
            str(employer_exemption_application.employer_exemption_application_id)
        ),
        headers={
            "Authorization": f"Bearer {get_auth_token(leave_admin.user)}",
            "X-FF-enable_employer_exemptions_portal": "true",
        },
        json=apply_custom_encoder(patch_request_body),
    )

    response_json = response.json()
    response_errors = response_json["errors"]

    assert response.status_code == 400
    assert len(response_errors) == 2

    assert response_errors == [
        {
            "field": "contact_phone.phone_number",
            "message": "Phone number must be a valid number",
            "rule": "phone_number_must_be_valid_number",
            "type": "invalid_phone_number",
        },
        {
            "field": "tpa_contact_phone.phone_number",
            "message": "Phone number must be a valid number",
            "rule": "phone_number_must_be_valid_number",
            "type": "invalid_phone_number",
        },
    ]


def test_patch_employer_exemption_application_not_leave_admin(
    client,
    test_db_session,
    initialize_factories_session,
    get_auth_token,
):
    user = UserFactory.create()
    leave_admin = UserLeaveAdministratorFactory.create()
    user_auth_token = get_auth_token(user)
    employer_exemption_application = EmployerExemptionApplicationFactory.create(
        employer=leave_admin.employer,
    )

    patch_request_body = {
        "contact_first_name": "Jane",
        "contact_last_name": "Doe",
    }

    response = client.patch(
        "/v1/employer-exemption-applications/{}".format(
            str(employer_exemption_application.employer_exemption_application_id)
        ),
        headers={
            "Authorization": f"Bearer {user_auth_token}",
            "X-FF-enable_employer_exemptions_portal": "true",
        },
        json=apply_custom_encoder(patch_request_body),
    )

    assert response.status_code == 403


def validate_patch_employer_exemption_application(
    client,
    test_db_session,
    initialize_factories_session,
    auth_token,
    patch_request_body,
    employer_exemption_application,
):
    response = client.patch(
        "/v1/employer-exemption-applications/{}".format(
            str(employer_exemption_application.employer_exemption_application_id)
        ),
        headers={
            "Authorization": f"Bearer {auth_token}",
            "X-FF-enable_employer_exemptions_portal": "true",
        },
        json=apply_custom_encoder(patch_request_body),
    )

    assert response.status_code == 200
    data = flatten(response.json().get("data"))
    patch_request_body = flatten(patch_request_body)

    for request_field_name in patch_request_body.keys():
        assert data[request_field_name] == patch_request_body[request_field_name]


def validate_submit_exemption_application(
    client,
    test_db_session,
    initialize_factories_session,
    auth_token,
    employer_exemption_application: EmployerExemptionApplication,
    expected_error_fields: list,
    expected_employer_exemption_application_status_id=EmployerExemptionApplicationStatus.DRAFT.employer_exemption_application_status_id,
):
    response = client.post(
        "/v1/employer-exemption-applications/{}/submit-application".format(
            str(employer_exemption_application.employer_exemption_application_id)
        ),
        headers={"Authorization": f"Bearer {auth_token}"},
    )

    response_json = response.json()

    data = response_json["data"]
    employer_exemption_application_status_id = data["employer_exemption_application_status_id"]

    assert (
        employer_exemption_application_status_id
        == expected_employer_exemption_application_status_id
    ), (
        f"Employer Exemption Application Status {employer_exemption_application_status_id} "
        f"does not match expected {expected_employer_exemption_application_status_id}"
    )

    if len(expected_error_fields) == 0:
        assert response.status_code == 201
        assert data["submitted_by_user_id"] is not None
        assert data["submitted_at"] is not None
    else:
        errors = response_json["errors"]
        message = response_json["message"]

        if (
            expected_employer_exemption_application_status_id
            != EmployerExemptionApplicationStatus.DRAFT.employer_exemption_application_status_id
        ):
            assert response.status_code == 403
            assert message.startswith(
                f"Employer Exemption Application {employer_exemption_application.employer_exemption_application_id} could not be submitted."
            )
        else:
            assert response.status_code == 400

            assert message == "Employer Exemption Application is not valid for submission"
            assert len(errors) == len(expected_error_fields)

            expected_error_dict = {
                field: {"field": field, "message": f"{field} is required", "type": "required"}
                for field in expected_error_fields
            }

            actual_error_dict = {error["field"]: error for error in errors}

            for k in actual_error_dict.keys():
                assert k in expected_error_dict, f"'{k}' not found in expected error list"

            for k in expected_error_dict.keys():
                assert k in actual_error_dict, f"'{k}' not found in actual error list"


def test_submit_exemption_application_missing_required_fields(
    client,
    test_db_session,
    initialize_factories_session,
    get_auth_token,
):
    expected_error_fields = [
        "average_workforce_count",
        "contact_email_address",
        "contact_first_name",
        "contact_last_name",
        "contact_phone.phone_number",
        "contact_title",
        "exemption_type",
        "has_third_party_administrator",
        "insurance_plan_effective_at",
        "is_self_insured_plan",
        "should_workforce_count_include_1099_misc",
    ]

    leave_admin = UserLeaveAdministratorFactory.create()
    employer_exemption_application = EmployerExemptionApplicationDraftFactory.create(
        employer=leave_admin.employer,
    )

    validate_submit_exemption_application(
        client,
        test_db_session,
        initialize_factories_session,
        auth_token=get_auth_token(leave_admin.user),
        employer_exemption_application=employer_exemption_application,
        expected_error_fields=expected_error_fields,
    )


def test_submit_exemption_application_missing_required_purchased_private_fields(
    client,
    test_db_session,
    initialize_factories_session,
    get_auth_token,
):

    expected_error_fields = [
        "purchased_plan.insurance_plan_id",
        "purchased_plan.insurance_provider_id",
    ]

    leave_admin = UserLeaveAdministratorFactory.create()
    employer_exemption_application = EmployerExemptionApplicationDraftPrivatePlanFactory.create(
        employer=leave_admin.employer,
        insurance_provider_id=None,
        insurance_plan_id=None,
    )

    validate_submit_exemption_application(
        client,
        test_db_session,
        initialize_factories_session,
        auth_token=get_auth_token(leave_admin.user),
        employer_exemption_application=employer_exemption_application,
        expected_error_fields=expected_error_fields,
    )


def test_submit_exemption_application_missing_required_self_insured_family_fields(
    client,
    test_db_session,
    initialize_factories_session,
    get_auth_token,
):
    expected_error_fields = [
        "self_insured.questions.are_employer_withholdings_within_allowable_amount",
        "average_workforce_count",
        "contact_email_address",
        "contact_first_name",
        "contact_last_name",
        "contact_phone.phone_number",
        "contact_title",
        "self_insured.surety_bond_amount",
        "self_insured.has_obtained_surety_bond",
        "self_insured.surety_company",
        "self_insured.questions.does_employer_withhold_premiums",
        "self_insured.questions.does_plan_cover_all_employees",
        "self_insured.questions.does_plan_cover_employee_contribution",
        "self_insured.questions.does_plan_cover_former_employees",
        "self_insured.questions.does_plan_favor_paid_leave_benefits",
        "self_insured.questions.does_plan_pay_enough_benefits",
        "self_insured.questions.does_plan_provide_enough_armed_forces_illness_leave",
        "self_insured.questions.does_plan_provide_enough_armed_forces_leave",
        "self_insured.questions.does_plan_provide_enough_bonding_leave",
        "self_insured.questions.does_plan_provide_enough_caring_leave",
        "self_insured.questions.does_plan_provide_enough_leave",
        # "self_insured.questions.does_plan_provide_enough_medical_leave", # medical only question
        "self_insured.questions.does_plan_provide_intermittent_armed_forces_leave",
        "self_insured.questions.does_plan_provide_intermittent_bonding_leave",
        "self_insured.questions.does_plan_provide_intermittent_caring_leave",
        "self_insured.questions.does_plan_provide_intermittent_medical_leave",
        "self_insured.questions.does_plan_provide_pfml_job_protection",
        "self_insured.questions.does_plan_provide_return_to_work_benefits",
        "has_third_party_administrator",
        "insurance_plan_effective_at",
        "insurance_plan_expires_at",
        "should_workforce_count_include_1099_misc",
    ]

    leave_admin = UserLeaveAdministratorFactory.create()
    employer_exemption_application = EmployerExemptionApplicationDraftFactory.create(
        employer=leave_admin.employer,
        is_self_insured_plan=True,
        has_family_exemption=True,
        has_medical_exemption=False,
    )

    validate_submit_exemption_application(
        client,
        test_db_session,
        initialize_factories_session,
        auth_token=get_auth_token(leave_admin.user),
        employer_exemption_application=employer_exemption_application,
        expected_error_fields=expected_error_fields,
    )


def test_submit_exemption_application_missing_required_self_insured_medical_fields(
    client,
    test_db_session,
    initialize_factories_session,
    get_auth_token,
):
    expected_error_fields = [
        "self_insured.questions.are_employer_withholdings_within_allowable_amount",
        "average_workforce_count",
        "contact_email_address",
        "contact_first_name",
        "contact_last_name",
        "contact_phone.phone_number",
        "contact_title",
        "self_insured.surety_bond_amount",
        "self_insured.has_obtained_surety_bond",
        "self_insured.surety_company",
        "self_insured.questions.does_employer_withhold_premiums",
        "self_insured.questions.does_plan_cover_all_employees",
        "self_insured.questions.does_plan_cover_employee_contribution",
        "self_insured.questions.does_plan_cover_former_employees",
        "self_insured.questions.does_plan_favor_paid_leave_benefits",
        "self_insured.questions.does_plan_pay_enough_benefits",
        # "self_insured.questions.does_plan_provide_enough_armed_forces_illness_leave", # family only question
        # "self_insured.questions.does_plan_provide_enough_armed_forces_leave", # family only question
        # "self_insured.questions.does_plan_provide_enough_bonding_leave", # family only question
        # "self_insured.questions.does_plan_provide_enough_caring_leave", # family only question
        "self_insured.questions.does_plan_provide_enough_leave",
        "self_insured.questions.does_plan_provide_enough_medical_leave",
        # "self_insured.questions.does_plan_provide_intermittent_armed_forces_leave", # family only question
        # "self_insured.questions.does_plan_provide_intermittent_bonding_leave", # family only question
        # "self_insured.questions.does_plan_provide_intermittent_caring_leave", # family only question
        "self_insured.questions.does_plan_provide_intermittent_medical_leave",
        "self_insured.questions.does_plan_provide_pfml_job_protection",
        "self_insured.questions.does_plan_provide_return_to_work_benefits",
        "has_third_party_administrator",
        "insurance_plan_effective_at",
        "insurance_plan_expires_at",
        "should_workforce_count_include_1099_misc",
    ]

    leave_admin = UserLeaveAdministratorFactory.create()
    employer_exemption_application = EmployerExemptionApplicationDraftFactory.create(
        employer=leave_admin.employer,
        is_self_insured_plan=True,
        has_family_exemption=False,
        has_medical_exemption=True,
    )

    validate_submit_exemption_application(
        client,
        test_db_session,
        initialize_factories_session,
        auth_token=get_auth_token(leave_admin.user),
        employer_exemption_application=employer_exemption_application,
        expected_error_fields=expected_error_fields,
    )


def test_submit_exemption_application_self_insured_family_in_review(
    client,
    test_db_session,
    initialize_factories_session,
    get_auth_token,
):
    leave_admin = UserLeaveAdministratorFactory.create()
    employer_exemption_application = (
        EmployerExemptionApplicationDraftSelfInsuredFamilyFactory.create(
            employer=leave_admin.employer,
        )
    )

    validate_submit_exemption_application(
        client,
        test_db_session,
        initialize_factories_session,
        auth_token=get_auth_token(leave_admin.user),
        employer_exemption_application=employer_exemption_application,
        expected_error_fields=[],
        expected_employer_exemption_application_status_id=EmployerExemptionApplicationStatus.IN_REVIEW.employer_exemption_application_status_id,
    )


def test_submit_exemption_application_self_insured_family_denied(
    client,
    test_db_session,
    initialize_factories_session,
    get_auth_token,
):
    leave_admin = UserLeaveAdministratorFactory.create()
    employer_exemption_application = (
        EmployerExemptionApplicationDraftSelfInsuredFamilyFactory.create(
            employer=leave_admin.employer, does_plan_provide_enough_caring_leave=False
        )
    )

    validate_submit_exemption_application(
        client,
        test_db_session,
        initialize_factories_session,
        auth_token=get_auth_token(leave_admin.user),
        employer_exemption_application=employer_exemption_application,
        expected_error_fields=[],
        expected_employer_exemption_application_status_id=EmployerExemptionApplicationStatus.DENIED.employer_exemption_application_status_id,
    )


def test_submit_exemption_application_self_insured_medical_in_review(
    client,
    test_db_session,
    initialize_factories_session,
    get_auth_token,
):
    leave_admin = UserLeaveAdministratorFactory.create()
    employer_exemption_application = (
        EmployerExemptionApplicationDraftSelfInsuredMedicalFactory.create(
            employer=leave_admin.employer,
        )
    )

    validate_submit_exemption_application(
        client,
        test_db_session,
        initialize_factories_session,
        auth_token=get_auth_token(leave_admin.user),
        employer_exemption_application=employer_exemption_application,
        expected_error_fields=[],
        expected_employer_exemption_application_status_id=EmployerExemptionApplicationStatus.IN_REVIEW.employer_exemption_application_status_id,
    )


def test_submit_exemption_application_self_insured_medical_denied(
    client,
    test_db_session,
    initialize_factories_session,
    get_auth_token,
):
    leave_admin = UserLeaveAdministratorFactory.create()
    employer_exemption_application = (
        EmployerExemptionApplicationDraftSelfInsuredMedicalFactory.create(
            employer=leave_admin.employer, does_plan_provide_intermittent_medical_leave=False
        )
    )

    validate_submit_exemption_application(
        client,
        test_db_session,
        initialize_factories_session,
        auth_token=get_auth_token(leave_admin.user),
        employer_exemption_application=employer_exemption_application,
        expected_error_fields=[],
        expected_employer_exemption_application_status_id=EmployerExemptionApplicationStatus.DENIED.employer_exemption_application_status_id,
    )


def test_submit_exemption_application_self_insured_not_start_of_next_quarter(
    client,
    test_db_session,
    initialize_factories_session,
    get_auth_token,
):
    leave_admin = UserLeaveAdministratorFactory.create()
    employer_exemption_application = (
        EmployerExemptionApplicationDraftSelfInsuredMedicalFactory.create(
            employer=leave_admin.employer,
            insurance_provider_id=1,
            insurance_plan_id=1,
            insurance_plan_effective_at=date(date.today().year, 2, 11),
        )
    )

    validate_submit_exemption_application(
        client,
        test_db_session,
        initialize_factories_session,
        auth_token=get_auth_token(leave_admin.user),
        employer_exemption_application=employer_exemption_application,
        expected_error_fields=[],
        expected_employer_exemption_application_status_id=EmployerExemptionApplicationStatus.IN_REVIEW.employer_exemption_application_status_id,
    )


def test_submit_exemption_application_private_insurance_valid(
    client,
    test_db_session,
    initialize_factories_session,
    get_auth_token,
):
    insurance_plan = test_db_session.query(InsurancePlan).first()
    leave_admin = UserLeaveAdministratorFactory.create()
    employer_exemption_application = EmployerExemptionApplicationDraftPrivatePlanFactory.create(
        employer=leave_admin.employer,
        insurance_provider_id=insurance_plan.insurance_plan_id,
        insurance_plan_id=insurance_plan.insurance_plan_id,
        has_family_exemption=insurance_plan.has_family_exemption,
        has_medical_exemption=insurance_plan.has_medical_exemption,
    )

    validate_submit_exemption_application(
        client,
        test_db_session,
        initialize_factories_session,
        auth_token=get_auth_token(leave_admin.user),
        employer_exemption_application=employer_exemption_application,
        expected_error_fields=[],
        expected_employer_exemption_application_status_id=EmployerExemptionApplicationStatus.APPROVED.employer_exemption_application_status_id,
    )


def test_submit_exemption_application_private_insurance_invalid(
    client,
    test_db_session,
    initialize_factories_session,
    get_auth_token,
):
    insurance_plan = test_db_session.query(InsurancePlan).first()
    leave_admin = UserLeaveAdministratorFactory.create()
    employer_exemption_application = EmployerExemptionApplicationDraftPrivatePlanFactory.create(
        employer=leave_admin.employer,
        is_self_insured_plan=False,
        insurance_provider_id=insurance_plan.insurance_plan_id,
        insurance_plan_id=insurance_plan.insurance_plan_id,
        has_family_exemption=not insurance_plan.has_family_exemption,
        has_medical_exemption=insurance_plan.has_medical_exemption,
    )

    validate_submit_exemption_application(
        client,
        test_db_session,
        initialize_factories_session,
        auth_token=get_auth_token(leave_admin.user),
        employer_exemption_application=employer_exemption_application,
        expected_error_fields=[],
        expected_employer_exemption_application_status_id=EmployerExemptionApplicationStatus.IN_REVIEW.employer_exemption_application_status_id,
    )


def test_submit_exemption_application_not_leave_admin(
    client,
    test_db_session,
    initialize_factories_session,
    get_auth_token,
):
    insurance_plan = test_db_session.query(InsurancePlan).first()
    leave_admin = UserLeaveAdministratorFactory.create()
    user = UserFactory.create()
    employer_exemption_application = EmployerExemptionApplicationDraftPrivatePlanFactory.create(
        employer=leave_admin.employer,
        insurance_provider_id=insurance_plan.insurance_plan_id,
        insurance_plan_id=insurance_plan.insurance_plan_id,
        has_family_exemption=insurance_plan.has_family_exemption,
        has_medical_exemption=insurance_plan.has_medical_exemption,
    )

    user_auth_token = get_auth_token(user)

    response = client.post(
        "/v1/employer-exemption-applications/{}/submit-application".format(
            str(employer_exemption_application.employer_exemption_application_id)
        ),
        headers={"Authorization": f"Bearer {user_auth_token}"},
    )

    assert response.status_code == 403


def test_submit_exemption_application_not_draft_status(
    client,
    test_db_session,
    initialize_factories_session,
    get_auth_token,
):
    expected_error_fields = ["employer_exemption_application"]
    leave_admin = UserLeaveAdministratorFactory.create()

    non_editable_statuses = [
        EmployerExemptionApplicationStatus.APPROVED.employer_exemption_application_status_id,
        EmployerExemptionApplicationStatus.DENIED.employer_exemption_application_status_id,
        EmployerExemptionApplicationStatus.IN_REVIEW.employer_exemption_application_status_id,
    ]

    employer_exemption_application = EmployerExemptionApplicationDraftPrivatePlanFactory.create(
        employer=leave_admin.employer,
        employer_exemption_application_status_id=EmployerExemptionApplicationStatus.APPROVED.employer_exemption_application_status_id,
    )

    for status_id in non_editable_statuses:
        employer_exemption_application.employer_exemption_application_status_id = status_id
        employer_exemption_application.submitted_at = datetime_util.utcnow()
        employer_exemption_application.submitted_by_user_id = leave_admin.user_id

        validate_submit_exemption_application(
            client,
            test_db_session,
            initialize_factories_session,
            auth_token=get_auth_token(leave_admin.user),
            employer_exemption_application=employer_exemption_application,
            expected_error_fields=expected_error_fields,
            expected_employer_exemption_application_status_id=status_id,
        )
