import pytest

import massgov.pfml.delegated_payments.util.payment_preference_util as payment_preference_util
from massgov.pfml.db.models.factories import EmployerFactory
from massgov.pfml.db.models.fineos_web_id import FINEOSWebIdExt
from massgov.pfml.delegated_payments.mock.delegated_payments_factory import DelegatedPaymentFactory
from massgov.pfml.fineos import create_client
from massgov.pfml.fineos.models.customer_api import (
    PaymentMethodResponse,
    PaymentPreferenceAddressEmbeddable,
    PaymentPreferenceResource,
)


@pytest.fixture
def payment_preference_resource():
    return PaymentPreferenceResource(
        paymentMethod=PaymentMethodResponse(
            domainId=101, domainName="PaymentMethodDomain", fullId=123, name="Prepaid Debit Card"
        ),
        address=PaymentPreferenceAddressEmbeddable(
            addressLine1="123 Main St",
            addressLine2="Unit 101",
            addressLine4="Boston",
            addressLine6="MA",
            postCode="02108",
        ),
    )


def test_existing_fineos_web_id(test_db_session, initialize_factories_session):

    employer = EmployerFactory.create(employer_fein="*********")
    employee = DelegatedPaymentFactory(test_db_session).get_or_create_employee()

    fineos_web_id_ext = FINEOSWebIdExt()
    fineos_web_id_ext.employee_tax_identifier = employee.tax_identifier.tax_identifier
    fineos_web_id_ext.employer_fein = employer.employer_fein
    fineos_web_id_ext.fineos_web_id = "pfml_api_468df93c-cb2d-424e-9690-f61cc65506bb"
    test_db_session.add(fineos_web_id_ext)
    test_db_session.commit()

    fineos_web_id = payment_preference_util.get_fineos_web_id(test_db_session, employee.employee_id)

    assert fineos_web_id == fineos_web_id_ext.fineos_web_id


def test_no_fineos_web_id(test_db_session, initialize_factories_session):
    employee = DelegatedPaymentFactory(test_db_session).get_or_create_employee()
    fineos_web_id = payment_preference_util.get_fineos_web_id(test_db_session, employee.employee_id)

    assert fineos_web_id is None


def test_find_default_payment_preference(test_db_session, initialize_factories_session):
    employee = DelegatedPaymentFactory(test_db_session).get_or_create_employee()
    fineos_web_id = payment_preference_util.get_fineos_web_id(test_db_session, employee.employee_id)
    fineos_client = create_client()
    fineos_payment_preference = payment_preference_util.find_default_payment_preference(
        fineos_client, fineos_web_id
    )

    assert fineos_payment_preference is not None
