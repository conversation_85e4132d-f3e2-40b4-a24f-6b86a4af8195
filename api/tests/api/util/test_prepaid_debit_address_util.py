import re
import uuid

import pytest

import massgov.pfml.delegated_payments.util.prepaid_debit_address_util as prepaid_debit_address_util
from massgov.pfml.db.lookup_data.geo import GeoState
from massgov.pfml.db.models.employees import Address, ExperianAddressPair
from massgov.pfml.db.models.factories import (
    AddressFactory,
    EmployeeAddressFactory,
    ExperianAddressPairFactory,
)
from massgov.pfml.fineos.models.customer_api import (
    PaymentMethodResponse,
    PaymentPreferenceAddressEmbeddable,
    PaymentPreferenceResource,
)
from tests.helpers.logging import assert_log_contains


@pytest.fixture
def payment_preference_resource():
    return PaymentPreferenceResource(
        paymentMethod=PaymentMethodResponse(
            domainId=101, domainName="PaymentMethodDomain", fullId=123, name="Prepaid Debit Card"
        ),
        address=PaymentPreferenceAddressEmbeddable(
            addressLine1="123 Main St",
            addressLine2="Unit 101",
            addressLine4="Boston",
            addressLine6="MA",
            postCode="02108",
        ),
    )


def test_create_experian_address_pair(test_db_session, initialize_factories_session):

    address = AddressFactory.create()
    experian_address_pair = prepaid_debit_address_util.create_experian_address_pair(
        test_db_session, address
    )
    test_db_session.commit()
    assert address.address_id == experian_address_pair.fineos_address_id


def test_upsert_address_existing_address(
    test_db_session,
    initialize_factories_session,
    payment_preference_resource,
    employee,
    caplog_info,
):
    state_id = GeoState.get_id(payment_preference_resource.address.addressLine6)
    address = AddressFactory.create(
        address_line_one=payment_preference_resource.address.addressLine1,
        address_line_two=payment_preference_resource.address.addressLine2,
        city=payment_preference_resource.address.addressLine4,
        geo_state_id=state_id,
        zip_code=payment_preference_resource.address.postCode,
    )
    ExperianAddressPairFactory(fineos_address=address)
    EmployeeAddressFactory(employee=employee, address=address)

    experian_address_pair = prepaid_debit_address_util.upsert_address(
        test_db_session, employee, address
    )

    assert experian_address_pair.fineos_address_id is not None
    assert isinstance(experian_address_pair.fineos_address_id, uuid.UUID)
    assert experian_address_pair.fineos_address_id == address.address_id

    assert_log_contains(caplog_info, re.compile(r"Matching address for employee."))


def test_upsert_address_created_new_address(
    test_db_session,
    initialize_factories_session,
    payment_preference_resource,
    employee,
    caplog_info,
):
    address = AddressFactory.build()
    ExperianAddressPairFactory.build(experian_address=address)
    before_address_count = test_db_session.query(Address).count()
    experian_address_pair = prepaid_debit_address_util.upsert_address(
        test_db_session, employee, address
    )
    after_address_count = test_db_session.query(Address).count()
    test_db_session.commit()
    assert after_address_count == before_address_count + 1
    assert experian_address_pair.fineos_address_id is not None
    assert isinstance(experian_address_pair.fineos_address_id, uuid.UUID)


def test_prepaid_debit_card_upsert_address_experian_not_validated(
    test_db_session,
    initialize_factories_session,
    payment_preference_resource,
    employee,
    caplog_error,
):
    state_id = GeoState.get_id(payment_preference_resource.address.addressLine6)
    address = AddressFactory.create(
        address_line_one=payment_preference_resource.address.addressLine1,
        address_line_two=payment_preference_resource.address.addressLine2,
        city=payment_preference_resource.address.addressLine4,
        geo_state_id=state_id,
        zip_code=payment_preference_resource.address.postCode,
    )

    EmployeeAddressFactory(employee=employee, address=address)
    experian_address = ExperianAddressPair(
        fineos_address_id=address.address_id,
        experian_address_id=None,
    )
    test_db_session.add(experian_address)
    test_db_session.commit()

    experian_address_pair = prepaid_debit_address_util.upsert_address(
        test_db_session, employee, address
    )

    test_db_session.commit()

    assert experian_address_pair.fineos_address_id is not None
    assert isinstance(experian_address_pair.fineos_address_id, uuid.UUID)
