import datetime
import uuid
from datetime import date, timedelta
from io import String<PERSON>
from typing import Dict, List
from unittest import mock

import pytest
from freezegun import freeze_time

from massgov.pfml.api.eligibility.benefit_year_dates import calculate_benefit_year_dates
from massgov.pfml.api.models.phones.common import Phone
from massgov.pfml.api.models.users.requests import UserUpdateRequest
from massgov.pfml.api.services.leave_admins import (
    get_pending_leave_admins_by_employer_id,
    record_leave_admin_action_add,
)
from massgov.pfml.db.lookup_data.employees import (
    LeaveRequestDecision,
    Role,
    UserLeaveAdministratorActionType,
)
from massgov.pfml.db.models.employees import (
    <PERSON><PERSON><PERSON>,
    Employer,
    User,
    UserLeaveAdministrator,
    UserLeaveAdministratorAction,
    UserLeaveAdministratorOrgUnit,
)
from massgov.pfml.db.models.factories import (
    AbsencePeriodFactory,
    ApplicationFactory,
    BenefitYearContributionFactory,
    BenefitYearFactory,
    ClaimFactory,
    ContinuousLeavePeriodFactory,
    EmployeeFactory,
    EmployerFactory,
    IntermittentLeavePeriodFactory,
    ManagedRequirementFactory,
    OrganizationUnitFactory,
    ReducedScheduleLeavePeriodFactory,
    TaxIdentifierFactory,
    UserFactory,
    UserLeaveAdministratorActionFactory,
    UserLeaveAdministratorFactory,
    UserLeaveAdministratorVerifiedFactory,
    VerificationFactory,
)
from massgov.pfml.util import logging
from massgov.pfml.util.aws.ses import EmailRecipient
from massgov.pfml.util.users import add_leave_admin_and_role
from tests.api import apply_custom_encoder
from tests.helpers.logging import assert_log_contains


@pytest.fixture
def test_verification(initialize_factories_session):
    return VerificationFactory.create()


@pytest.fixture
def employer_one(initialize_factories_session):
    return EmployerFactory.create(employer_fein="*********")


@pytest.fixture
def employer_two(initialize_factories_session):
    return EmployerFactory.create(employer_fein="*********")


@pytest.fixture()
def leave_admin_one(employer_one, test_verification, employer_user, initialize_factories_session):
    employer_user.phone_extension = "123"
    return UserLeaveAdministratorFactory.create(
        employer=employer_one, verification=test_verification, user=employer_user
    )


@pytest.fixture()
def leave_admin_two(employer_two, test_verification, employer_user, initialize_factories_session):
    return UserLeaveAdministratorFactory.create(
        employer=employer_two, verification=test_verification, user=employer_user
    )


@pytest.fixture()
def employer_user_alt(initialize_factories_session):
    return UserFactory.create(roles=[Role.EMPLOYER], first_name="Slinky", last_name="Glenesk")


@pytest.fixture()
def leave_admin_three(
    employer_two, test_verification, employer_user_alt, initialize_factories_session
):
    return UserLeaveAdministratorFactory.create(
        employer=employer_two, verification=test_verification, user=employer_user_alt
    )


@pytest.fixture()
def leave_admin_four(employer_one, test_verification, employer_user, initialize_factories_session):
    return UserLeaveAdministratorFactory.create(
        employer=employer_one, verification=test_verification, user=employer_user, deactivated=True
    )


@pytest.fixture()
def leave_admin_one_alt(
    employer_one, test_verification, employer_user_alt, initialize_factories_session
):
    return UserLeaveAdministratorFactory.create(
        employer=employer_one, verification=test_verification, user_id=employer_user_alt.user_id
    )


class TestLeaveAdminsSearch:
    def test_user_id_success_case(
        self,
        client,
        leave_admin_one,
        leave_admin_one_alt,
        employer_user,
        employer_auth_token,
        test_db_session,
    ):
        # for future reference https://discuss.python.org/t/solved-why-does-python-not-support-the-z-suffix-for-utc-timezone/42219/3
        leave_admin_one.verification.created_at = datetime.datetime(
            2022, 5, 3, 00, 00, 00, tzinfo=datetime.timezone.utc
        )
        employer_user.phone_number = "+11234567890"

        test_db_session.add(leave_admin_one)
        test_db_session.commit()

        request_body = {"user_id": employer_user.user_id}
        response = client.post(
            "/v1/leave-admins/search",
            json=apply_custom_encoder(request_body),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        resp = response.json()

        assert response.status_code == 200
        assert len(resp["data"]) == 1
        assert resp["data"][0]["email_address"] == employer_user.email_address
        assert resp["data"][0]["first_name"] == employer_user.first_name
        assert resp["data"][0]["user_leave_administrator_id"] == str(
            leave_admin_one.user_leave_administrator_id
        )
        assert resp["data"][0]["verified"] == leave_admin_one.verified
        assert resp["data"][0]["verified_at"] == "2022-05-03T00:00:00+00:00"
        assert (
            resp["data"][0]["verification_type"]
            == leave_admin_one.verification.verification_type.verification_type_description
        )
        assert resp["data"][0]["added_at"] is None
        assert resp["data"][0]["added_by"] is None
        assert resp["data"][0]["has_verified_leave_admin"]

        # Masking rules should be applied
        assert resp["data"][0]["phone_number"] == f"***-***-{employer_user.phone_number[-4:]}"

        total_in_db = test_db_session.query(UserLeaveAdministrator).all()
        assert len(total_in_db) == 2

    def test_user_id_without_existing_verified_leave_admin_success_case(
        self,
        client,
        leave_admin_one,
        employer_user,
        employer_auth_token,
        test_db_session,
    ):
        leave_admin_one.verification = None
        test_db_session.add(leave_admin_one)
        test_db_session.commit()

        request_body = {"user_id": employer_user.user_id}
        response = client.post(
            "/v1/leave-admins/search",
            json=apply_custom_encoder(request_body),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        resp = response.json()

        assert response.status_code == 200
        assert len(resp["data"]) == 1
        assert resp["data"][0]["verified"] == leave_admin_one.verified
        assert resp["data"][0]["verified_at"] is None
        assert resp["data"][0]["verification_type"] is None
        assert resp["data"][0]["verification_type"] is None
        assert resp["data"][0]["has_verified_leave_admin"] is False
        total_in_db = test_db_session.query(UserLeaveAdministrator).all()
        assert len(total_in_db) == 1

    def test_user_id_success_case_added_by_leave_admin(
        self,
        client,
        leave_admin_one,
        leave_admin_one_alt,
        employer_user,
        employer_auth_token,
        test_db_session,
    ):
        leave_admin_one.verification.created_at = datetime.datetime(
            2022, 5, 3, 00, 00, 00, tzinfo=datetime.timezone.utc
        )
        test_db_session.add(leave_admin_one)
        test_db_session.commit()

        employer_user.phone_number = "+11234567890"
        added_by_user = UserFactory.create(
            email_address="<EMAIL>", first_name="Slinky", last_name="Blinkers"
        )

        UserLeaveAdministratorActionFactory.create(
            user=added_by_user,
            recipient_user_leave_administrator_id=leave_admin_one.user_leave_administrator_id,
            created_at=datetime.datetime(2022, 6, 3),
        )

        request_body = {"user_id": employer_user.user_id}
        response = client.post(
            "/v1/leave-admins/search",
            json=apply_custom_encoder(request_body),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        resp = response.json()

        assert response.status_code == 200
        assert len(resp["data"]) == 1
        assert resp["data"][0]["email_address"] == employer_user.email_address
        assert resp["data"][0]["first_name"] == employer_user.first_name
        assert resp["data"][0]["user_leave_administrator_id"] == str(
            leave_admin_one.user_leave_administrator_id
        )
        assert resp["data"][0]["verified"] == leave_admin_one.verified
        assert resp["data"][0]["verified_at"] == "2022-05-03T00:00:00+00:00"
        assert (
            resp["data"][0]["verification_type"]
            == leave_admin_one.verification.verification_type.verification_type_description
        )
        assert resp["data"][0]["added_at"] == "2022-06-03T00:00:00+00:00"
        assert resp["data"][0]["added_by"] == {
            "first_name": "Slinky",
            "last_name": "Blinkers",
            "email_address": "<EMAIL>",
        }

        # Masking rules should be applied
        assert resp["data"][0]["phone_number"] == f"***-***-{employer_user.phone_number[-4:]}"

        total_in_db = test_db_session.query(UserLeaveAdministrator).all()
        assert len(total_in_db) == 2

    def test_employer_id_success_case(
        self,
        client,
        leave_admin_one,
        leave_admin_two,
        leave_admin_one_alt,
        employer_one,
        employer_two,
        employer_auth_token,
        test_db_session,
    ):
        request_body = {"employer_id": employer_one.employer_id}
        response = client.post(
            "/v1/leave-admins/search",
            json=apply_custom_encoder(request_body),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        assert response.status_code == 200
        resp = response.json()
        assert len(resp["data"]) == 2
        admin_one, admin_two = resp["data"]
        assert admin_one["first_name"] == leave_admin_one.user.first_name
        assert admin_two["first_name"] == leave_admin_one_alt.user.first_name
        assert admin_one["employer_fein"] == "99-9999998"
        assert admin_two["employer_fein"] == "99-9999998"
        total_in_db = test_db_session.query(UserLeaveAdministrator).all()
        assert len(total_in_db) == 3

    def test_employer_id_with_existing_verified_leave_admin_success_case(
        self,
        client,
        leave_admin_one,
        employer_one,
        employer_user,
        employer_auth_token,
        test_db_session,
    ):
        recipient_email = f"janedoe+{uuid.uuid4()}@acme.com"
        UserLeaveAdministratorActionFactory.create(
            recipient_email=recipient_email,
            recipient_employer=employer_one,
            created_at=datetime.datetime(2022, 5, 3),
        )

        request_body = {"employer_id": employer_one.employer_id}
        response = client.post(
            "/v1/leave-admins/search",
            json=apply_custom_encoder(request_body),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        resp = response.json()
        assert response.status_code == 200
        assert len(resp["data"]) == 2
        assert resp["data"][0]["has_verified_leave_admin"]
        assert resp["data"][1]["has_verified_leave_admin"]
        total_in_db = test_db_session.query(UserLeaveAdministrator).all()
        assert len(total_in_db) == 1

    def test_user_id_search_deactivated_las_excluded(
        self,
        client,
        leave_admin_one,
        employer_user,
        employer_auth_token,
        test_db_session,
    ):
        leave_admin_one.deactivated = True
        test_db_session.commit()

        request_body = {"user_id": employer_user.user_id}
        response = client.post(
            "/v1/leave-admins/search",
            json=apply_custom_encoder(request_body),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        resp = response.json()

        assert response.status_code == 200
        assert len(resp["data"]) == 0
        total_in_db = test_db_session.query(UserLeaveAdministrator).all()
        assert len(total_in_db) == 1

    def test_employer_id_search_deactivated_las_excluded(
        self,
        client,
        leave_admin_one,
        leave_admin_two,
        leave_admin_one_alt,
        employer_one,
        employer_two,
        employer_auth_token,
        test_db_session,
    ):
        leave_admin_one_alt.deactivated = True
        test_db_session.commit()
        request_body = {"employer_id": employer_one.employer_id}
        response = client.post(
            "/v1/leave-admins/search",
            json=apply_custom_encoder(request_body),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        assert response.status_code == 200
        resp = response.json()
        assert len(resp["data"]) == 1
        admin_one = resp["data"][0]
        assert admin_one["first_name"] == leave_admin_one.user.first_name
        assert admin_one["employer_fein"] == "99-9999998"
        total_in_db = test_db_session.query(UserLeaveAdministrator).all()
        assert len(total_in_db) == 3

    def test_users_can_only_request_information_for_their_own_user_id(
        self, client, employer_auth_token
    ):
        request_body = {"user_id": uuid.uuid4()}
        response = client.post(
            "/v1/leave-admins/search",
            json=apply_custom_encoder(request_body),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        assert response.status_code == 403

    def test_users_can_only_request_information_for_their_own_employer(
        self, client, employer_auth_token, employer_user
    ):
        request_body = {"employer_id": uuid.uuid4()}
        response = client.post(
            "/v1/leave-admins/search",
            json=apply_custom_encoder(request_body),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        assert response.status_code == 403

    def test_invalid_request(self, client, employer_auth_token):
        request_body = {"user_id": "123"}
        response = client.post(
            "/v1/leave-admins/search",
            json=request_body,
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        assert response.status_code == 400
        resp = response.json()
        assert resp["errors"][0]["type"] == "format"

    def test_non_employer_cannot_request(self, client, auth_token, employer_user):
        request_body = {"user_id": employer_user.user_id}
        response = client.post(
            "/v1/leave-admins/search",
            json=apply_custom_encoder(request_body),
            headers={"Authorization": f"Bearer {auth_token}"},
        )
        assert response.status_code == 403

    def test_employer_with_org_units(
        self,
        client,
        leave_admin_one,
        leave_admin_two,
        leave_admin_one_alt,
        employer_one,
        employer_two,
        employer_auth_token,
        test_db_session,
    ):
        org_unit = OrganizationUnitFactory.create(employer_id=employer_one.employer_id)
        la_org_unit = UserLeaveAdministratorOrgUnit(
            user_leave_administrator_id=leave_admin_one.user_leave_administrator_id,
            organization_unit_id=org_unit.organization_unit_id,
        )
        test_db_session.add(la_org_unit)
        test_db_session.commit()
        request_body = {"employer_id": employer_one.employer_id}
        response = client.post(
            "/v1/leave-admins/search",
            json=apply_custom_encoder(request_body),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        resp = response.json()
        assert response.status_code == 200
        total_in_db = test_db_session.query(UserLeaveAdministrator).all()
        assert len(total_in_db) == 3
        assert len(resp["data"]) == 1
        assert resp["data"][0]["first_name"] == leave_admin_one.user.first_name
        assert resp["data"][0]["organization_units"][0]["name"] == org_unit.name
        assert resp["data"][0]["organization_units"][0]["organization_unit_id"] == str(
            org_unit.organization_unit_id
        )

    def test_pending_leave_admin_in_response(
        self,
        client,
        leave_admin_one,
        employer_one,
        employer_auth_token,
        test_db_session,
    ):
        leave_admin_one.user.first_name = "Benji"
        leave_admin_one.user.last_name = "Buttons"
        test_db_session.add(leave_admin_one)
        test_db_session.commit()

        request_body = {"employer_id": employer_one.employer_id}
        recipient_email = f"janedoe+{uuid.uuid4()}@acme.com"
        leave_admin_action = UserLeaveAdministratorActionFactory.create(
            recipient_email=recipient_email,
            user=leave_admin_one.user,
            recipient_employer=employer_one,
            created_at=datetime.datetime(2022, 5, 3, 00, 00, 00, tzinfo=datetime.timezone.utc),
        )
        response = client.post(
            "/v1/leave-admins/search",
            json=apply_custom_encoder(request_body),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )

        assert response.status_code == 200
        response_json = response.json()
        assert len(response_json["data"]) == 2
        assert response_json["data"][1]["email_address"] == recipient_email
        assert response_json["data"][1]["user_leave_administrator_action_id"] == str(
            leave_admin_action.user_leave_administrator_action_id
        )
        assert response_json["data"][1].get("has_fineos_registration") is False
        assert response_json["data"][1].get("verified") is False
        assert response_json["data"][1].get("employer_fein") == "99-9999998"
        assert response_json["data"][1].get("verified_at") is None
        assert response_json["data"][1].get("verification_type") == "Add"
        assert response_json["data"][1].get("added_at") == "2022-05-03T00:00:00+00:00"
        assert response_json["data"][1].get("added_by") == {
            "first_name": "Benji",
            "last_name": "Buttons",
            "email_address": leave_admin_one.user.email_address,
        }

        pending_leave_admins = get_pending_leave_admins_by_employer_id(
            test_db_session, employer_one.employer_id
        )
        assert len(pending_leave_admins) == 1

    def test_expired_pending_leave_admin_filter_out(
        self, client, leave_admin_one, employer_one, employer_auth_token, test_db_session
    ):
        request_body = {"employer_id": employer_one.employer_id}
        recipient_email = f"janedoe+{uuid.uuid4()}@acme.com"
        leave_admin_action = record_leave_admin_action_add(
            test_db_session,
            recipient_email,
            employer_one.employer_id,
            leave_admin_one.user,
        )
        leave_admin_action.expires_at -= timedelta(days=14)

        test_db_session.commit()
        response = client.post(
            "/v1/leave-admins/search",
            json=apply_custom_encoder(request_body),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        assert response.status_code == 200
        response_json = response.json()
        assert len(response_json["data"]) == 1
        pending_leave_admins = get_pending_leave_admins_by_employer_id(
            test_db_session, employer_one.employer_id
        )
        assert len(pending_leave_admins) == 0


class TestLeaveAdminAdd:
    @pytest.fixture
    def request_body(
        self,
        leave_admin_one: UserLeaveAdministrator,
        employer_one: Employer,
    ) -> Dict[str, str]:
        return {
            "email_address": "<EMAIL>",
            "employer_id": str(employer_one.employer_id),
        }

    @mock.patch("massgov.pfml.api.services.leave_admins.send_leave_admin_action_email")
    def test_success_case(
        self,
        mock_send_email,
        client,
        employer_auth_token,
        test_db_session,
        request_body,
        employer_one,
    ):
        employer_user = UserFactory.create(
            email_address="<EMAIL>", roles=[Role.EMPLOYER]
        )
        UserLeaveAdministratorVerifiedFactory.create(employer=employer_one, user=employer_user)

        response = client.post(
            "/v1/leave-admins/add",
            json=request_body,
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        leave_user_action: List[UserLeaveAdministratorAction] = (
            test_db_session.query(UserLeaveAdministratorAction)
            .filter(
                UserLeaveAdministratorAction.recipient_email == request_body.get("email_address"),
                UserLeaveAdministratorAction.recipient_employer_id
                == request_body.get("employer_id"),
            )
            .all()
        )
        assert response.status_code == 201
        assert len(leave_user_action) == 1
        assert leave_user_action[0].user_id is not None
        assert leave_user_action[0].recipient_employer_id is not None
        assert leave_user_action[0].completed_at is None
        assert leave_user_action[0].recipient_user_leave_administrator_id is None
        assert leave_user_action[0].user_leave_administrator_action_type_id == 1
        assert leave_user_action[0].recipient_email == "<EMAIL>"
        assert mock_send_email.call_args_list[0][0][0].to_addresses[0] == "<EMAIL>"
        """ Test to check that all organisation leave admin recipients was pulled properly """
        assert "<EMAIL>" in mock_send_email.call_args_list[1][0][0][0].to_addresses

    @mock.patch("massgov.pfml.api.services.leave_admins.send_leave_admin_action_email")
    def test_no_emails_for_unverified_leave_admin(
        self,
        mock_send_email,
        client,
        employer_auth_token,
        request_body,
        employer_one,
    ):
        unverified_employer_user = UserFactory.create(
            email_address="<EMAIL>",
            roles=[Role.EMPLOYER],
        )
        UserLeaveAdministratorFactory.create(employer=employer_one, user=unverified_employer_user)

        client.post(
            "/v1/leave-admins/add",
            json=request_body,
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )

        assert "<EMAIL>" not in mock_send_email.call_args_list[1][0][0]

    @freeze_time("2020-01-01")
    @mock.patch("massgov.pfml.api.services.leave_admins.register_leave_admin_with_fineos")
    @mock.patch("massgov.pfml.api.services.leave_admins.send_leave_admin_action_email")
    def test_existing_user_success_case(
        self,
        mock_send_email,
        mock_fineos_call,
        client,
        employer_user,
        employer_auth_token,
        test_db_session,
        initialize_factories_session,
    ):
        employer = EmployerFactory.create(employer_fein="193605313")
        leave_admin = UserLeaveAdministratorVerifiedFactory.create(
            user=employer_user, employer=employer
        )
        user: User = UserFactory.create(roles=[Role.EMPLOYER])
        request_body = {
            "email_address": user.email_address,
            "employer_id": employer.employer_id,
        }
        test_db_session.commit()
        response = client.post(
            "/v1/leave-admins/add",
            json=apply_custom_encoder(request_body),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        leave_user_action: List[UserLeaveAdministratorAction] = (
            test_db_session.query(UserLeaveAdministratorAction)
            .filter(
                UserLeaveAdministratorAction.recipient_email == request_body.get("email_address"),
                UserLeaveAdministratorAction.recipient_employer_id
                == request_body.get("employer_id"),
            )
            .all()
        )
        assert response.status_code == 201
        assert len(leave_user_action) == 1
        assert leave_user_action[0].recipient_user_leave_administrator_id is not None
        assert leave_user_action[0].completed_at is not None
        assert leave_user_action[0].completed_at.isoformat() == "2020-01-01T00:00:00+00:00"

        leave_admin_record: UserLeaveAdministrator = (
            test_db_session.query(UserLeaveAdministrator)
            .filter(
                UserLeaveAdministrator.user_leave_administrator_id
                == leave_user_action[0].recipient_user_leave_administrator_id
            )
            .one_or_none()
        )

        assert leave_admin_record is not None
        assert leave_admin_record.verification.verification_type_id == 4
        test_db_session.refresh(user)
        assert user.roles[0].role_id == 3

        # mock_fineos_call.assert_called_once_with(employer, user, test_db_session, None)

        mock_fineos_call.assert_called_once_with(mock.ANY, mock.ANY, test_db_session, None)
        args = mock_fineos_call.call_args[0]
        employer_arg = args[0]
        user_arg = args[1]

        assert isinstance(employer_arg, Employer)
        assert isinstance(user_arg, User)
        assert employer_arg.employer_id == employer.employer_id
        assert user_arg.user_id == user.user_id

        # Test to check send_leave_admin_action_email() was called, including call for deactivated LA as well as others in their org
        user_email = user.email_address or ""
        leave_admin_email = leave_admin.user.email_address or ""
        assert user_email != ""
        assert leave_admin_email != ""
        mock_send_email.assert_has_calls(
            [
                mock.call(
                    EmailRecipient(to_addresses=[user_email]),
                    "ManageLeaveAdminAdd",
                    user.email_address,
                    leave_admin.employer.employer_name,
                    "added",
                    leave_admin.user.full_name or leave_admin.user.email_address,
                    "19-3605313",
                ),
                mock.call(
                    [EmailRecipient(to_addresses=[leave_admin_email])],
                    "ManageLeaveAdminAll",
                    user.email_address,
                    leave_admin.employer.employer_name,
                    "added",
                    leave_admin.user.full_name or leave_admin.user.email_address,
                    "19-3605313",
                ),
            ]
        )

    def test_returns_404_when_employer_not_found(self, client, employer_auth_token, request_body):
        request_body = {
            "email_address": "<EMAIL>",
            "employer_id": str(uuid.uuid4()),
        }
        response = client.post(
            "/v1/leave-admins/add",
            json=request_body,
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        assert response.status_code == 404

    def test_endpoint_error_with_invalid_authorization_header(self, client, request_body):
        response = client.post(
            "/v1/leave-admins/add", json=request_body, headers={"Authorization": f"Bearer {''}"}
        )
        assert response.status_code == 401

    def test_invalid_email_key_in_request_body(
        self, client, leave_admin_one, employer_one, employer_auth_token
    ):
        # Testing invalid request body key for email, sending email instead of email_address
        request_body = {
            "email": "<EMAIL>",
            "employer_id": employer_one.employer_id,
        }
        response = client.post(
            "/v1/leave-admins/add",
            json=apply_custom_encoder(request_body),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        assert response.status_code == 400
        assert response.json().get("errors")
        assert response.json().get("message") == "Request Validation Error"

    def test_invalid_uuid_for_employer_id(
        self, client, leave_admin_one, employer_one, employer_auth_token
    ):
        request_body = {
            "email": "<EMAIL>",
            "employer_id": "1234567890",
        }
        response = client.post(
            "/v1/leave-admins/add",
            json=request_body,
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        assert response.status_code == 400
        assert response.json().get("errors")
        assert response.json().get("message") == "Request Validation Error"

    def test_non_employer_cannot_request(self, client, auth_token, request_body):
        response = client.post(
            "/v1/leave-admins/add",
            json=apply_custom_encoder(request_body),
            headers={"Authorization": f"Bearer {auth_token}"},
        )
        assert response.status_code == 403

    def test_deactivated_leave_admin_cannot_request(
        self,
        client,
        leave_admin_four,
        employer_one,
        employer_auth_token,
    ):
        request_body = {
            "email_address": "<EMAIL>",
            "employer_id": str(employer_one.employer_id),
        }
        response = client.post(
            "/v1/leave-admins/add",
            json=request_body,
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )

        assert response.status_code == 403

    def test_unverified_employer_cannot_request(
        self,
        client,
        leave_admin_one,
        employer_one,
        employer_auth_token,
        test_db_session,
    ):
        employer_one.fineos_employer_id = None
        request_body = {
            "email_address": "<EMAIL>",
            "employer_id": str(employer_one.employer_id),
        }
        test_db_session.commit()
        response = client.post(
            "/v1/leave-admins/add",
            json=request_body,
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        assert response.status_code == 403

    def test_error_adding_existing_employee_account(
        self,
        leave_admin_one: UserLeaveAdministrator,
        employer_one: Employer,
        client,
        employer_auth_token,
    ):
        user: User = UserFactory.create()
        request_body = {
            "email_address": user.email_address,
            "employer_id": employer_one.employer_id,
        }
        response = client.post(
            "/v1/leave-admins/add",
            json=apply_custom_encoder(request_body),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )

        response_json = response.json()
        assert response.status_code == 400
        assert response_json.get("errors")
        assert response_json.get("errors")[0]["field"] == "employer_role"
        assert (
            response_json.get("errors")[0]["message"]
            == "Manual verification failure - User does not have an employer account"
        )

    def test_adding_user_from_employee_to_employer_account(
        self,
        leave_admin_one: UserLeaveAdministrator,
        employer_one: Employer,
        client,
        employer_auth_token,
        test_db_session,
    ):
        user: User = UserFactory.create()
        add_leave_admin_and_role(test_db_session, user, employer_one)
        test_db_session.commit()
        test_db_session.refresh(user)
        request_body = {
            "email_address": user.email_address,
            "employer_id": employer_one.employer_id,
        }
        response = client.post(
            "/v1/leave-admins/add",
            json=apply_custom_encoder(request_body),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        assert response.status_code == 201

    def test_employer_with_org_units_cannot_request(
        self,
        leave_admin_one,
        employer_one: Employer,
        client,
        employer_auth_token,
    ):
        request_body = {
            "email_address": "<EMAIL>",
            "employer_id": employer_one.employer_id,
        }
        OrganizationUnitFactory(employer_id=employer_one.employer_id)
        response = client.post(
            "/v1/leave-admins/add",
            json=apply_custom_encoder(request_body),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )

        assert response.status_code == 403


class TestLeaveAdminDeactivate:

    @mock.patch(
        "massgov.pfml.api.leave_admins.update_leave_admin_with_fineos", return_value=(None, None)
    )
    @mock.patch("massgov.pfml.api.services.leave_admins.send_leave_admin_action_email")
    def test_leave_admin_deactivate_self_success_case(
        self,
        mock_send_email,
        mock_update_with_fineos,
        client,
        leave_admin_one,
        leave_admin_one_alt,
        employer_one,
        employer_auth_token,
        employer_user,
        test_db_session,
    ):
        response = client.post(
            "/v1/leave-admins/{}/deactivate".format(leave_admin_one.user_leave_administrator_id),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )

        test_db_session.refresh(leave_admin_one)

        assert response.status_code == 200
        assert leave_admin_one.deactivated

        # Test that the DB record was created accurately
        leave_admin_action = (
            test_db_session.query(UserLeaveAdministratorAction)
            .filter(
                UserLeaveAdministratorAction.recipient_user_leave_administrator_id
                == leave_admin_one.user_leave_administrator_id
            )
            .filter(UserLeaveAdministratorAction.user_id == employer_user.user_id)
            .one_or_none()
        )
        assert leave_admin_action
        assert (
            leave_admin_action.user_leave_administrator_action_type_id
            == UserLeaveAdministratorActionType.DEACTIVATE.user_leave_administrator_action_type_id
        )

        # Test to check update_leave_admin_with_fineos() was called with the right parameters
        mock_update_with_fineos.assert_called_once_with(
            mock.ANY,
            UserUpdateRequest(
                first_name=leave_admin_one.user.first_name,
                last_name=leave_admin_one.user.last_name,
                phone=Phone(
                    phone_number=leave_admin_one.user.phone_number,
                    extension=leave_admin_one.user.phone_extension,
                ),
            ),
            mock.ANY,
            True,
        )

        args = mock_update_with_fineos.call_args[0]
        employer_user_arg = args[0]
        leave_admin_one_arg = args[2]

        assert isinstance(employer_user_arg, User)
        assert isinstance(leave_admin_one, UserLeaveAdministrator)
        assert employer_user_arg.user_id == employer_user.user_id
        assert (
            leave_admin_one_arg.user_leave_administrator_id
            == leave_admin_one.user_leave_administrator_id
        )

        assert leave_admin_one_arg.fineos_web_id
        assert leave_admin_one_arg.fineos_web_id == leave_admin_one.fineos_web_id
        assert leave_admin_one_arg.employer.fineos_employer_id
        assert (
            leave_admin_one_arg.employer.fineos_employer_id
            == leave_admin_one.employer.fineos_employer_id
        )
        assert employer_user_arg.email_address
        assert employer_user_arg.email_address == employer_user.email_address
        assert employer_user_arg.phone_extension == "123"
        # exact extension from employer_user fixture

        # Test to check send_leave_admin_action_email() was called, including call for deactivated LA as well as others in their org
        mock_send_email.assert_has_calls(
            [
                mock.call(
                    EmailRecipient(to_addresses=[leave_admin_one.user.email_address]),
                    "ManageLeaveAdminDeactivate",
                    leave_admin_one.user.email_address,
                    leave_admin_one.employer.employer_name,
                    "removed",
                    employer_user.full_name or employer_user.email_address,
                    "99-9999998",
                ),
                mock.call(
                    [EmailRecipient(to_addresses=[leave_admin_one_alt.user.email_address])],
                    "ManageLeaveAdminAll",
                    leave_admin_one.user.email_address,
                    leave_admin_one_alt.employer.employer_name,
                    "removed",
                    employer_user.full_name or employer_user.email_address,
                    "99-9999998",
                ),
            ]
        )

    @mock.patch("massgov.pfml.api.services.leave_admins.send_leave_admin_action_email")
    def test_leave_admin_deactivate_last_la_success_case(
        self,
        mock_send_email,
        client,
        leave_admin_one,
        employer_auth_token,
        employer_user,
        caplog,
    ):
        caplog.set_level(logging.INFO)  # noqa: B1
        response = client.post(
            "/v1/leave-admins/{}/deactivate".format(leave_admin_one.user_leave_administrator_id),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        assert response.status_code == 200

        # Test to check send_leave_admin_action_email() was called, including call for deactivated LA as well as others in their org
        assert "No other leave administrators associated with this employer." in caplog.text
        mock_send_email.assert_has_calls(
            [
                mock.call(
                    EmailRecipient(to_addresses=[leave_admin_one.user.email_address]),
                    "ManageLeaveAdminDeactivate",
                    leave_admin_one.user.email_address,
                    leave_admin_one.employer.employer_name,
                    "removed",
                    employer_user.full_name or employer_user.email_address,
                    "99-9999998",
                )
            ]
        )

    def test_leave_admin_deactivate_other_admin_success_case(
        self,
        client,
        employer_auth_token,
        employer_user,
        employer_user_alt,
        employer_two,
        initialize_factories_session,
        test_db_session,
    ):
        leave_admin_user_one = UserLeaveAdministratorVerifiedFactory.create(
            employer=employer_two, user=employer_user
        )
        leave_admin_user_two = UserLeaveAdministratorVerifiedFactory.create(
            employer=employer_two, user=employer_user_alt
        )

        assert not leave_admin_user_one.deactivated
        assert not leave_admin_user_two.deactivated

        # Test employer_user attempting to deactivate employer_user_alt
        response = client.post(
            "/v1/leave-admins/{}/deactivate".format(
                leave_admin_user_two.user_leave_administrator_id
            ),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )

        test_db_session.refresh(leave_admin_user_two)

        assert response.status_code == 200
        assert not leave_admin_user_one.deactivated
        assert leave_admin_user_two.deactivated

    def test_success_if_already_deactivated(
        self,
        client,
        employer_auth_token,
        employer_user,
        employer_user_alt,
        employer_two,
        test_db_session,
        initialize_factories_session,
    ):
        leave_admin_user_one = UserLeaveAdministratorVerifiedFactory.create(
            employer=employer_two,
            user=employer_user,
        )
        leave_admin_user_two = UserLeaveAdministratorVerifiedFactory.create(
            employer=employer_two, user=employer_user_alt, deactivated=True
        )

        assert not leave_admin_user_one.deactivated
        assert leave_admin_user_two.deactivated

        # Test employer_user attempting to deactivate employer_user_alt
        response = client.post(
            "/v1/leave-admins/{}/deactivate".format(
                leave_admin_user_two.user_leave_administrator_id
            ),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        assert response.status_code == 200
        assert not leave_admin_user_one.deactivated
        assert leave_admin_user_two.deactivated

        # We do not expect to create an action record for this since
        # we didn't actually do anything.
        leave_admin_actions = test_db_session.query(UserLeaveAdministratorAction).count()
        assert leave_admin_actions == 0

    def test_forbidden_raised_if_requester_not_associated_with_employer(
        self,
        client,
        leave_admin_one,
        employer_two,
        employer_auth_token,
        employer_user,
        leave_admin_three,
    ):
        response = client.post(
            "/v1/leave-admins/{}/deactivate".format(leave_admin_three.user_leave_administrator_id),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        assert response.status_code == 403

    def test_forbidden_raised_if_requestor_is_deactivated(
        self,
        client,
        employer_user,
        employer_user_alt,
        employer_one,
        employer_auth_token,
    ):
        UserLeaveAdministratorVerifiedFactory.create(
            employer=employer_one, user=employer_user, deactivated=True
        )

        leave_admin_user_two = UserLeaveAdministratorVerifiedFactory.create(
            employer=employer_one, user=employer_user_alt
        )

        response = client.post(
            "/v1/leave-admins/{}/deactivate".format(
                leave_admin_user_two.user_leave_administrator_id
            ),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        assert response.status_code == 403

    def test_forbidden_raised_if_requestor_is_unverified(
        self,
        client,
        employer_user,
        employer_user_alt,
        employer_one,
        employer_auth_token,
    ):
        UserLeaveAdministratorFactory.create(employer=employer_one, user=employer_user)

        leave_admin_user_two = UserLeaveAdministratorVerifiedFactory.create(
            employer=employer_one, user=employer_user_alt
        )

        response = client.post(
            "/v1/leave-admins/{}/deactivate".format(
                leave_admin_user_two.user_leave_administrator_id
            ),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        assert response.status_code == 403

    @mock.patch(
        "massgov.pfml.api.leave_admins.update_leave_admin_with_fineos",
        return_value=Exception("A bad thing"),
    )
    def test_fineos_failed_to_update_leave_admin(
        self,
        mock_update_with_fineos,
        client,
        employer_auth_token,
        employer_user,
        employer_user_alt,
        employer_two,
        initialize_factories_session,
    ):
        leave_admin_user_one = UserLeaveAdministratorVerifiedFactory.create(
            employer=employer_two, user=employer_user
        )
        leave_admin_user_two = UserLeaveAdministratorVerifiedFactory.create(
            employer=employer_two, user=employer_user_alt
        )

        assert not leave_admin_user_one.deactivated
        assert not leave_admin_user_two.deactivated

        # Test employer_user attempting to deactivate employer_user_alt
        response = client.post(
            "/v1/leave-admins/{}/deactivate".format(
                leave_admin_user_two.user_leave_administrator_id
            ),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )

        # Ensure the database is not updated, so the user can try again.
        mock_update_with_fineos.assert_called_once()
        assert response.status_code == 500

        assert not leave_admin_user_one.deactivated
        assert not leave_admin_user_two.deactivated

    @mock.patch(
        "massgov.pfml.api.services.leave_admins.send_leave_admin_action_email",
        side_effect=Exception("A bad thing"),
    )
    def test_email_sending_failed_deactivation_email_due_to_ses(
        self,
        mock_send_email,
        client,
        employer_auth_token,
        employer_user,
        employer_user_alt,
        employer_two,
        initialize_factories_session,
        test_db_session,
    ):
        leave_admin_user_one = UserLeaveAdministratorVerifiedFactory.create(
            employer=employer_two, user=employer_user
        )
        leave_admin_user_two = UserLeaveAdministratorVerifiedFactory.create(
            employer=employer_two, user=employer_user_alt
        )

        assert not leave_admin_user_one.deactivated
        assert not leave_admin_user_two.deactivated

        # Test employer_user attempting to deactivate employer_user_alt
        response = client.post(
            "/v1/leave-admins/{}/deactivate".format(
                leave_admin_user_two.user_leave_administrator_id
            ),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )

        test_db_session.refresh(leave_admin_user_two)
        # Ensure the database was updated, even with a failure.
        assert response.status_code == 200
        assert not leave_admin_user_one.deactivated
        assert leave_admin_user_two.deactivated
        mock_send_email.assert_called_once()

    @mock.patch(
        "massgov.pfml.api.services.leave_admins.send_leave_admin_action_email",
        side_effect=[None, Exception("A bad thing")],
    )
    def test_email_sending_failed_email_to_all_due_to_ses(
        self,
        mock_send_email,
        client,
        employer_auth_token,
        employer_user,
        employer_user_alt,
        employer_two,
        initialize_factories_session,
        caplog,
        test_db_session,
    ):
        leave_admin_user_one = UserLeaveAdministratorVerifiedFactory.create(
            employer=employer_two, user=employer_user
        )
        leave_admin_user_two = UserLeaveAdministratorVerifiedFactory.create(
            employer=employer_two, user=employer_user_alt
        )

        assert not leave_admin_user_one.deactivated
        assert not leave_admin_user_two.deactivated

        # Test employer_user attempting to deactivate employer_user_alt
        response = client.post(
            "/v1/leave-admins/{}/deactivate".format(
                leave_admin_user_two.user_leave_administrator_id
            ),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )

        test_db_session.refresh(leave_admin_user_two)
        # Ensure the database was updated, even with a failure.
        assert response.status_code == 200
        assert not leave_admin_user_one.deactivated
        assert leave_admin_user_two.deactivated
        assert mock_send_email.call_count == 2

    def test_bad_request_response(self, client, employer_auth_token, leave_admin_one):
        response = client.post(
            "/v1/leave-admins/{}/deactivate".format(str(uuid.uuid4())),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        assert response.status_code == 400

    def test_non_employer_cannot_request(self, client, auth_token, employer_user, leave_admin_one):
        response = client.post(
            "/v1/leave-admins/{}/deactivate".format(leave_admin_one.user_leave_administrator_id),
            headers={"Authorization": f"Bearer {auth_token}"},
        )
        assert response.status_code == 403

    def test_employer_with_org_uints_cannot_request(
        self,
        client,
        leave_admin_one,
        employer_one,
        employer_auth_token,
    ):
        OrganizationUnitFactory(employer_id=employer_one.employer_id)
        response = client.post(
            "/v1/leave-admins/{}/deactivate".format(leave_admin_one.user_leave_administrator_id),
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )
        assert response.status_code == 403


class TestLeaveAdminDownloadCSV:
    """Tests for downloading leave admin CSV data."""

    @pytest.fixture
    def mock_logger(self, mocker):
        return mocker.patch("massgov.pfml.api.leave_admins.logger")

    @mock.patch("massgov.pfml.api.leave_admins.logger")
    @pytest.fixture
    def employer1(self, employer_user, test_verification):
        csv_employer_one = EmployerFactory.create(
            employer_dba="Test Employer 1", employer_fein="123456789"
        )
        UserLeaveAdministratorFactory.create(
            employer=csv_employer_one, verification=test_verification, user=employer_user
        )
        return csv_employer_one

    @pytest.fixture
    def employer2(self, employer_user):
        csv_employer_two = EmployerFactory.create(
            employer_dba="Test Employer 2", employer_fein="*********"
        )
        UserLeaveAdministratorFactory.create(employer=csv_employer_two, user=employer_user)
        return csv_employer_two

    @pytest.fixture
    def employee1(self):
        tax_id = TaxIdentifierFactory.create(tax_identifier="*********")
        return EmployeeFactory.create(
            first_name="John", last_name="Employee", tax_identifier=tax_id
        )

    @pytest.fixture
    def employee2(self):
        tax_id = TaxIdentifierFactory.create(tax_identifier="*********")
        return EmployeeFactory.create(first_name="Jane", last_name="Worker", tax_identifier=tax_id)

    @pytest.fixture
    def application1(self):
        return ApplicationFactory.create(
            date_of_birth=date(1980, 5, 15),
            submitted_time=datetime.datetime(2023, 1, 5, 14, 30),
            completed_time=datetime.datetime(2023, 1, 10, 9, 15),
        )

    @pytest.fixture
    def application2(self):
        return ApplicationFactory.create(
            date_of_birth=date(1985, 8, 20),
            submitted_time=datetime.datetime(2023, 2, 12, 10, 45),
            completed_time=datetime.datetime(2023, 2, 15, 16, 30),
        )

    @pytest.fixture
    def setup_test_data(
        self,
        employer1,
        employer2,
        employee1,
        employee2,
        application1,
        application2,
        initialize_factories_session,
    ):
        """Set up complex test data for leave admin CSV download."""

        # Create claims for employer 1
        # TODO (PFMLPB-22244): Fix organization unit ID when it's available
        claim1 = ClaimFactory.create(
            fineos_absence_id="ABS-123-456",
            employee=employee1,
            employer=employer1,
            application=application1,
            organization_unit_id="DEPT-123",
            approval_date=datetime.datetime(2023, 2, 5),
        )
        ReducedScheduleLeavePeriodFactory.create(
            application_id=application1.application_id,
            start_date=date(2023, 1, 18),
        )
        ReducedScheduleLeavePeriodFactory.create(
            application_id=application1.application_id,
            start_date=date(2023, 1, 10),
        )
        ContinuousLeavePeriodFactory.create(
            application_id=application1.application_id,
            start_date=date(2023, 1, 15),
        )
        IntermittentLeavePeriodFactory.create(
            application_id=application1.application_id,
            start_date=date(2023, 1, 24),
        )
        AbsencePeriodFactory.create(
            claim=claim1,
            claim_id=claim1.claim_id,
            absence_period_start_date=date(2023, 1, 15),
            absence_period_end_date=date(2023, 2, 15),
            absence_period_type_id=1,  # Continuous
            absence_reason_id=1,  # Medical Leave
            leave_request_decision_id=1,  # Approved
        )

        # Create claims for employer 2
        claim2 = ClaimFactory.create(
            fineos_absence_id="ABS-789-012",
            employee=employee2,
            employer=employer2,
            application=application2,
            organization_unit_id="DEPT-456",
            approval_date=datetime.datetime(2023, 3, 10),
        )
        ContinuousLeavePeriodFactory.create(
            application_id=application2.application_id,
            start_date=date(2023, 2, 20),
        )
        AbsencePeriodFactory.create(
            claim=claim2,
            claim_id=claim2.claim_id,
            absence_period_start_date=date(2023, 2, 20),
            absence_period_end_date=date(2023, 3, 20),
            absence_period_type_id=1,  # Continuous
            absence_reason_id=3,  # Medical Donation
            leave_request_decision_id=1,  # Approved
        )

        return claim1, claim2

    @pytest.fixture
    def expected_csv_headers(self):
        """Return the expected CSV header fields."""
        return [
            "Application ID",
            "First name",
            "Last name",
            "Date of birth",
            "SSN or ITIN",
            "Organization",
            "EIN",
            "Department",
            "Leave type",
            "Leave start date",
            "Leave end date",
            "Leave frequency",
            "Application started",
            "Application completed",
            "Employer review due date",
            "Employer review completed by",
            "Employer review completion date",
            "Application status",
            "DFML decision expected date",
            "Weekly benefit amount",
            "IAWW",
        ]

    def test_download_csv_real_data(
        self,
        client,
        employer_user,
        employer_auth_token,
        setup_test_data,
        expected_csv_headers,
        caplog,
    ):
        """Test downloading CSV data with absence periods."""
        caplog.set_level(logging.INFO)

        # Make the request
        response = client.get(
            "/v1/leave-admins/download-csv",
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )

        # Check response
        assert response.status_code == 200

        has_content_attr = hasattr(response, "content")
        assert has_content_attr

        # Parse CSV content
        csv_content = response.content.decode("utf-8")
        import csv
        from io import StringIO

        csv_rows = list(csv.reader(StringIO(csv_content)))

        assert len(csv_rows) == 3

        header_row = csv_rows[0]
        header_row_list = header_row
        assert len(header_row_list) == 21

        assert header_row_list == expected_csv_headers

        # Check for data rows
        body_rows = [csv_rows[1], csv_rows[2]]
        expected_body_row_1 = [
            "ABS-789-012",
            "Jane",  # First name
            "Worker",  # Last name
            "08/20/****",
            "***-**-6666",
            "Test Employer 2",
            "*********",
            "N/A",
            "Medical Leave",  # remapped value
            "02/20/2023",
            "03/20/2023",
            "Continuous",  # remapped value
            "02/12/2023",
            "02/15/2023",
            "N/A",
            "N/A",  # Employer review completed by
            "N/A",  # Employer review completion date
            "Pending",
            "N/A",
            "N/A",
            "N/A",
        ]
        assert expected_body_row_1 in body_rows

        expected_body_row_2 = [
            "ABS-123-456",
            "John",  # First name
            "Employee",  # Last name
            "05/15/****",
            "***-**-3333",
            "Test Employer 1",
            "123456789",
            "N/A",
            "Medical Leave",  # remapped value
            "01/15/2023",
            "02/15/2023",
            "Continuous",  # remapped value
            "01/05/2023",
            "01/10/2023",
            "N/A",
            "N/A",  # Employer review completed by
            "N/A",  # Employer review completion date
            "Pending",
            "N/A",
            "N/A",
            "N/A",
        ]
        assert expected_body_row_2 in body_rows

        assert_log_contains(
            caplog, "Leave admin CSV download retrieved data", {"user_id": employer_user.user_id}
        )

    def test_download_multiple_review(
        self,
        client,
        employer_user,
        employer_auth_token,
        setup_test_data,
        expected_csv_headers,
        caplog,
    ):
        """Test downloading CSV data with absence periods."""
        caplog.set_level(logging.INFO)

        claim1, _ = setup_test_data

        _ = ManagedRequirementFactory.create(
            claim=claim1,
            respondent_user_id=employer_user.user_id,
            responded_at=datetime.datetime(2023, 1, 20, 10, 0),
        )

        managed_requirement_2 = ManagedRequirementFactory.create(
            claim=claim1,
            respondent_user_id=employer_user.user_id,
            responded_at=datetime.datetime(2023, 1, 21, 11, 0),
        )
        # Make the request
        response = client.get(
            "/v1/leave-admins/download-csv",
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )

        # Check response
        assert response.status_code == 200

        has_content_attr = hasattr(response, "content")
        assert has_content_attr

        # Parse CSV content
        csv_content = response.content.decode("utf-8")
        import csv
        from io import StringIO

        csv_rows = list(csv.reader(StringIO(csv_content)))

        csv_body = csv_rows[1:]
        reviewer_fields = [
            (row[15], row[16]) for row in csv_body if row[15] != "N/A" and row[16] != "N/A"
        ]

        dfml_expected_decision_expected_date_fields = [
            row[18] for row in csv_body if row[18] != "N/A"
        ]

        assert len(csv_rows) == 3
        assert reviewer_fields == [(managed_requirement_2.respondent_user.full_name, "01/21/2023")]
        assert dfml_expected_decision_expected_date_fields == ["02/04/2023"]

    def test_download_csv_unauthorized(self, client):
        """Test that an unauthorized user without a JWT cannot download the CSV."""
        response = client.get("/v1/leave-admins/download-csv")

        assert response.status_code == 401
        assert response.json().get("message") == "No authorization token provided"

    def test_employee_download_csv_unauthorized(self, client, consented_user_token):
        """
        Test that an employee user cannot download the CSV.
        """
        response = client.get(
            "/v1/leave-admins/download-csv",
            headers={"Authorization": f"Bearer {consented_user_token}"},
        )

        assert response.status_code == 403

    @mock.patch("massgov.pfml.api.leave_admins.ClaimExport.create_csv_content")
    def test_download_csv_internal_server_error(
        self, mock_download_csv, client, employer_user, employer_auth_token, setup_test_data, caplog
    ):
        """Test that the API returns 500 when CSV generation fails."""
        caplog.set_level(logging.INFO)  # noqa: B1

        error_message = "Error generating CSV file"
        mock_download_csv.side_effect = Exception(error_message)

        response = client.get(
            "/v1/leave-admins/download-csv",
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )

        mock_download_csv.assert_called_once()
        assert response.status_code == 500

        assert_log_contains(
            caplog, "Leave admin CSV download request starting", {"user_id": employer_user.user_id}
        )

        with pytest.raises(AssertionError):
            assert_log_contains(
                caplog,
                "Leave admin CSV download retrieved data",
                {"user_id": employer_user.user_id},
            )

    @mock.patch("massgov.pfml.api.leave_admins.ClaimExport._process_absence_period_response")
    def test_download_csv_no_data(
        self, mock_process_absence_period_response, client, employer_auth_token, caplog
    ):
        caplog.set_level(logging.INFO)

        """Test that the API returns 404 when no data is available."""
        mock_process_absence_period_response.return_value = []

        response = client.get(
            "/v1/leave-admins/download-csv",
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )

        assert response.status_code == 404

        assert_log_contains(caplog, "Leave admin CSV download request starting")
        assert_log_contains(caplog, "Leave admin requested CSV download but no data available")

    def test_download_csv_with_approved_and_denied_claims(
        self, client, employer_auth_token, test_db_session, setup_test_data, caplog
    ):
        caplog.set_level(logging.INFO)
        """Test that IAWW and WBA only appear for approved claims in the CSV download."""
        # Get claims and set one to approved and one to denied
        claims = test_db_session.query(Claim).all()

        # Make sure we have at least 2 claims to work with
        assert len(claims) >= 2

        # Get the absence periods for our claims
        approved_claim = claims[0]
        denied_claim = claims[1]

        # Set one to explicitly approved and one to denied
        approved_claim.absence_periods[0].leave_request_decision_id = (
            LeaveRequestDecision.APPROVED.leave_request_decision_id
        )
        denied_claim.absence_periods[0].leave_request_decision_id = (
            LeaveRequestDecision.DENIED.leave_request_decision_id
        )

        # Ensure benefit year exists for approved employee using BenefitYearFactory
        approved_absence_dates = calculate_benefit_year_dates(approved_claim.application.start_date)
        approved_employee = approved_claim.employee
        benefit_year = BenefitYearFactory.create(
            employee=approved_employee,
            start_date=approved_absence_dates.start_date,
            end_date=approved_absence_dates.end_date,
        )

        # Add average_weekly_wage via BenefitYearContribution
        BenefitYearContributionFactory.create(
            benefit_year=benefit_year,
            benefit_year_id=benefit_year.benefit_year_id,
            employee=approved_employee,
            employer=approved_claim.employer,
            average_weekly_wage=1200.50,
        )

        test_db_session.commit()

        response = client.get(
            "/v1/leave-admins/download-csv",
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )

        # Check response
        assert response.status_code == 200

        # Parse CSV content using csv.reader
        csv_content = response.content.decode("utf-8")
        import csv as stdlib_csv

        csv_reader = list(stdlib_csv.reader(StringIO(csv_content)))

        # We should have a header plus 2 rows
        assert len(csv_reader) == 3

        # Find the rows corresponding to our claims by fineos_absence_id
        approved_row = None
        denied_row = None

        for row_data in csv_reader[1:]:  # Skip header row
            fineos_id = row_data[0]  # First column is "Application ID" (fineos_absence_id)

            if fineos_id == approved_claim.fineos_absence_id:
                approved_row = row_data
            elif fineos_id == denied_claim.fineos_absence_id:
                denied_row = row_data

        # Ensure we found both rows
        assert approved_row is not None
        assert denied_row is not None

        # Check the decision values
        decision_index = 17  # "Application status" column
        assert approved_row[decision_index] == "Approved"
        assert denied_row[decision_index] == "Denied"

        # Check monetary fields (WBA and IAWW)
        wba_index = 19  # Weekly benefit amount
        iaww_index = 20  # IAWW

        # Approved claim should have monetary values
        assert approved_row[wba_index].replace("$", "") == "865.05"
        assert approved_row[iaww_index].replace("$", "").replace(",", "") == "1200.50"

        # Denied claim should not have monetary values
        assert denied_row[wba_index] == "N/A"
        assert denied_row[iaww_index] == "N/A"

        expected_log_message = "Successfully formatted CSV data"
        log_record = next(
            record for record in caplog.records if record.message == expected_log_message
        )

        assert log_record.absence_period_with_monetary_fields == 1
        assert log_record.absence_period_processed_count == 2
        assert log_record.absence_period_unapproved_count == 1
        assert log_record.absence_period_approved_count == 1
        assert log_record.benefits_years_count == 1

    def test_download_csv_log_data(
        self,
        client,
        employer_user,
        employer_auth_token,
        setup_test_data,
        expected_csv_headers,
        caplog,
    ):
        """Test downloading CSV data with absence periods."""
        caplog.set_level(logging.INFO)

        # Make the request
        response = client.get(
            "/v1/leave-admins/download-csv",
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )

        # Check response
        assert response.status_code == 200

        expected_log_message = "Successfully formatted CSV data"
        log_record = next(
            record for record in caplog.records if record.message == expected_log_message
        )

        assert log_record.absence_period_without_reviewer_name == 2
        assert log_record.employee_without_tax_identifier == 0
        assert log_record.absence_period_without_id == 0
        assert log_record.absence_period_without_application_id == 0
        assert log_record.absence_period_without_employee_id == 0
        assert log_record.absence_period_without_employer_id == 0
        assert log_record.absence_period_without_leave_start_date == 0
        assert log_record.absence_period_without_benefits_years_set == 0
        assert log_record.absence_period_without_benefits_years == 0
        assert log_record.absence_period_with_reviewer_name == 0
        assert log_record.absence_period_with_identifier == 2
        assert log_record.absence_period_with_period_fields == 2
        assert log_record.absence_period_with_application_fields == 2
        assert log_record.absence_period_with_review_fields == 2
        assert log_record.absence_period_with_monetary_fields == 0
        assert log_record.absence_period_processed_count == 2
        assert log_record.absence_period_unapproved_count == 2
        assert log_record.absence_period_approved_count == 0
        assert log_record.employer_count == 2
        assert log_record.employee_count == 2
        assert log_record.earliest_leave_period_count == 2
        assert log_record.benefits_years_count == 0
        assert log_record.absence_period_without_iaww == 0
        assert log_record.absence_period_without_wba == 0

        # Check for the final log message and assert its attributes
        expected_log_message = "Final log attributes"
        log_record = next(
            record for record in caplog.records if record.message == expected_log_message
        )

        assert not log_record.found_identifier_issues
        assert not log_record.found_wba_issues
        assert not log_record.unprocessed_absence_periods_issues
        assert not log_record.approved_vs_unapproved_issues

    def test_download_csv_maximum_limit(
        self,
        client,
        employer_user,
        employer_auth_token,
        setup_test_data,
        expected_csv_headers,
        caplog,
        app_config,
    ):
        """Test downloading CSV data with absence periods."""
        caplog.set_level(logging.INFO)

        app_config.leave_admin_csv_max_rows = 1

        # Make the request
        response = client.get(
            "/v1/leave-admins/download-csv",
            headers={"Authorization": f"Bearer {employer_auth_token}"},
        )

        # Check response
        assert response.status_code == 500

        expected_log_message = f"Too many absence periods (2) found for user {employer_user.user_id}. Max rows set to 1. "
        log_record = next(
            record for record in caplog.records if record.message == expected_log_message
        )

        assert log_record.absence_period_count == 2
