import uuid
from datetime import datetime, timedelta
from unittest.mock import <PERSON><PERSON><PERSON>, call, patch

import pytest
from sqlalchemy import func
from sqlalchemy.engine.row import Row

import massgov.pfml.util.logging as logging
from massgov.pfml.db.lookup_data.employees import PaymentMethod
from massgov.pfml.db.lookup_data.payments import FineosTaskType, PrepaidRegistrationStatus
from massgov.pfml.db.lookup_data.reference_file_type import ReferenceFileType
from massgov.pfml.db.models.employees import Address, Employee, ExperianAddressPair
from massgov.pfml.db.models.factories import (
    EmployeeFactory,
    FineosExtractVbiTaskReportDeltaSomFactory,
    ReferenceFileFactory,
)
from massgov.pfml.db.models.payments import ClaimantPrepaidRegistration, PaymentPreference
from massgov.pfml.delegated_payments.prepaid_debit_card_registration_step import (
    PrepaidDebitCardRegistrationStep,
)
from massgov.pfml.delegated_payments.sync_payment_preference_with_fineos_step import (
    SyncPrepaidDebitPaymentPreferenceWithFineosStep,
)
from massgov.pfml.delegated_payments.sync_prepaid_debit_card_registration import (
    SyncPrepaidDebitCardRegistrationStep,
)
from massgov.pfml.delegated_payments.update_fineos_payment_preference_step import (
    UpdateFineosPaymentPreferenceStep,
)
from massgov.pfml.fineos.models.customer_api import (
    AccountDetailEmbeddable,
    AccountTypeResponse,
    PaymentMethodResponse,
    PaymentPreferenceAddressEmbeddable,
    PaymentPreferenceCustomerResources,
    PaymentPreferenceResource,
)
from massgov.pfml.fineos.tasks import Constants


@pytest.fixture(autouse=True)
def set_env_vars(monkeypatch):
    monkeypatch.setenv("USBANK_TRANSID", "4444")
    monkeypatch.setenv("USBANK_CERTCARDID", "**********")
    monkeypatch.setenv("USBANK_CERTCARDPASSCODE", "3333")
    monkeypatch.setenv("USBANK_FUNDINGCARDID", "**********")
    monkeypatch.setenv("USBANK_CARDTYPE", "2")


@pytest.fixture
def payment_preference_resource():
    return PaymentPreferenceResource(
        paymentMethod=PaymentMethodResponse(
            domainId=101, domainName="PaymentMethodDomain", fullId=123, name="Check"
        ),
        address=PaymentPreferenceAddressEmbeddable(
            addressLine1="123 Main St",
            addressLine2="Unit 101",
            addressLine4="Boston",
            addressLine6="MA",
            postCode="02108",
        ),
        accountDetail=AccountDetailEmbeddable(
            accountName="US Bank Checking",
            accountNo="*********",
            accountType=AccountTypeResponse(
                domainId=2087,
                domainName="accountType",
                fullId=54678,
                name="Checking",
            ),
            routingNumber="*********",
        ),
        default=True,
    )


@pytest.fixture
def task_reference_file(initialize_factories_session):
    return ReferenceFileFactory.create(
        reference_file_type_id=ReferenceFileType.FINEOS_VBI_TASKREPORT_DELTA_SOM_EXTRACT.reference_file_type_id
    )


def mock_payment_preference_customer_resources():
    return PaymentPreferenceCustomerResources(
        elements=[
            PaymentPreferenceResource(
                paymentMethod=PaymentMethodResponse(
                    domainId=101, domainName="PaymentMethodDomain", fullId=123, name="Check"
                ),
                address=PaymentPreferenceAddressEmbeddable(
                    addressLine1="123 Main St",
                    addressLine2="Unit 101",
                    addressLine4="Boston",
                    addressLine6="MA",
                    postCode="02108",
                ),
                accountDetail=AccountDetailEmbeddable(
                    accountName="US Bank Checking",
                    accountNo="*********",
                    accountType=AccountTypeResponse(
                        domainId=2087,
                        domainName="accountType",
                        fullId=54678,
                        name="Checking",
                    ),
                    routingNumber="*********",
                ),
                default=True,
            )
        ],
    )


@pytest.fixture
def create_registration_with_addresses(test_db_session, employee):
    def _create_registration(
        is_experian_verified=True, registration_status=PrepaidRegistrationStatus.PENDING
    ):
        employee.date_of_birth = "1990-01-01"
        test_db_session.commit()

        # Create registration
        registration = ClaimantPrepaidRegistration(
            claimant_prepaid_registration_id=uuid.uuid4(),
            employee_id=employee.employee_id,
            prepaid_registration_status_id=registration_status.prepaid_registration_status_id,
            fineos_absence_id="NTN-001-001",
        )
        test_db_session.add(registration)
        test_db_session.commit()

        # Create addresses
        address_one = Address(
            address_type_id=1,
            address_line_one="123 Main St",
            city="Boston",
            geo_state_id=1,
            zip_code="02101",
            country_id=232,
        )
        test_db_session.add(address_one)
        test_db_session.commit()

        address_two = Address(
            address_type_id=1,
            address_line_one="123 Main St",
            city="Boston",
            geo_state_id=1,
            zip_code="02101",
            country_id=232,
        )
        test_db_session.add(address_two)
        test_db_session.commit()

        # Create ExperianAddressPair
        experian_address = ExperianAddressPair(
            fineos_address_id=address_one.address_id,
            experian_address_id=address_two.address_id if is_experian_verified else None,
        )
        test_db_session.add(experian_address)
        test_db_session.commit()

        payment_preference = PaymentPreference(
            payment_method_id=PaymentMethod.PREPAID_CARD.payment_method_id,
            employee_id=employee.employee_id,
            experian_address_pair_id=experian_address.fineos_address_id,
            default=True,
        )

        test_db_session.add(payment_preference)
        test_db_session.commit()

        return registration

    return _create_registration


@patch("massgov.pfml.delegated_payments.util.payment_preference_util.get_fineos_web_id")
@patch("massgov.pfml.delegated_payments.sync_payment_preference_with_fineos_step.create_client")
def test_prepaid_debit_card_with_pending_registration(
    mock_fineos_create_client, mock_get_fineos_web_id, test_db_session, employee
):
    """
    Verify that with a pending prepaid debit card registration, the service will call the Fineos API
    to get the payment preference.
    """
    registration = ClaimantPrepaidRegistration(
        claimant_prepaid_registration_id=uuid.uuid4(),
        employee_id=employee.employee_id,
        prepaid_registration_status_id=PrepaidRegistrationStatus.PENDING.prepaid_registration_status_id,
    )
    test_db_session.add(registration)
    test_db_session.commit()

    mock_fineos_client = MagicMock()
    mock_fineos_create_client.return_value = mock_fineos_client
    mock_fineos_client.get_customer_payment_preference.return_value = (
        mock_payment_preference_customer_resources()
    )

    mock_get_fineos_web_id.return_value = "pfml_api_eb6809e8-fec2-474b-8795-45942fd9a084"

    count_before = test_db_session.query(func.count("*")).select_from(PaymentPreference).scalar()

    service = SyncPrepaidDebitPaymentPreferenceWithFineosStep(test_db_session, test_db_session)
    service.sync_payment_preference_with_fineos()

    records = test_db_session.query(PaymentPreference).all()

    mock_get_fineos_web_id.assert_called_once_with(test_db_session, employee.employee_id)
    mock_fineos_client.get_customer_payment_preference.assert_called_with(
        "pfml_api_eb6809e8-fec2-474b-8795-45942fd9a084"
    )

    address = (
        test_db_session.query(Address)
        .filter_by(address_id=records[0].experian_address_pair_id)
        .first()
    )
    experian_address_pairs = (
        test_db_session.query(ExperianAddressPair)
        .filter_by(fineos_address_id=records[0].experian_address_pair_id)
        .all()
    )

    assert len(records) == count_before + 1
    assert records[0].experian_address_pair_id is not None
    assert len(experian_address_pairs) == 1
    assert address.address_line_one == "123 Main St"


@patch("massgov.pfml.delegated_payments.util.payment_preference_util.get_fineos_web_id")
@patch("massgov.pfml.delegated_payments.sync_payment_preference_with_fineos_step.create_client")
def test_prepaid_debit_card_without_pending_registration(
    mock_fineos_create_client, mock_get_fineos_web_id, test_db_session, employee
):
    """
    Verify that without a pending prepaid debit card registration, the service will not call the Fineos API
    to get the payment preference.
    """
    registration = ClaimantPrepaidRegistration(
        claimant_prepaid_registration_id=uuid.uuid4(),
        employee_id=employee.employee_id,
        prepaid_registration_status_id=PrepaidRegistrationStatus.UPDATE.prepaid_registration_status_id,
    )
    test_db_session.add(registration)
    test_db_session.commit()

    mock_fineos_client = MagicMock()
    mock_fineos_create_client.return_value = mock_fineos_client
    mock_fineos_client.get_customer_payment_preference.return_value = {}

    mock_get_fineos_web_id.return_value = "pfml_api_eb6809e8-fec2-474b-8795-45942fd9a084"

    service = SyncPrepaidDebitPaymentPreferenceWithFineosStep(test_db_session, test_db_session)
    service.sync_payment_preference_with_fineos()

    mock_get_fineos_web_id.assert_not_called()


@patch("massgov.pfml.delegated_payments.util.prepaid_debit_address_util.upsert_address")
@patch(
    "massgov.pfml.delegated_payments.sync_payment_preference_with_fineos_step.SyncPrepaidDebitPaymentPreferenceWithFineosStep.upsert_payment_preference"
)
@patch("massgov.pfml.delegated_payments.util.payment_preference_util.get_fineos_web_id")
@patch("massgov.pfml.delegated_payments.sync_payment_preference_with_fineos_step.create_client")
def test_prepaid_debit_card_with_default_payment(
    mock_fineos_create_client,
    mock_get_fineos_web_id,
    mock_upsert_payment_preference,
    mock_upsert_address,
    test_db_session,
    employee,
):
    """
    Verify that with a pending prepaid debit card registration and a default payment preference,
    the service will set the payment preference and address record.
    """
    registration = ClaimantPrepaidRegistration(
        claimant_prepaid_registration_id=uuid.uuid4(),
        employee_id=employee.employee_id,
        prepaid_registration_status_id=PrepaidRegistrationStatus.PENDING.prepaid_registration_status_id,
    )
    test_db_session.add(registration)
    test_db_session.commit()

    mock_fineos_client = MagicMock()
    mock_fineos_create_client.return_value = mock_fineos_client
    mock_payment_preferences = mock_payment_preference_customer_resources()
    mock_fineos_client.get_customer_payment_preference.return_value = mock_payment_preferences

    mock_get_fineos_web_id.return_value = "pfml_api_eb6809e8-fec2-474b-8795-45942fd9a084"

    service = SyncPrepaidDebitPaymentPreferenceWithFineosStep(test_db_session, test_db_session)
    service.sync_payment_preference_with_fineos()

    mock_get_fineos_web_id.assert_called_once_with(test_db_session, employee.employee_id)
    mock_fineos_client.get_customer_payment_preference.assert_called_with(
        "pfml_api_eb6809e8-fec2-474b-8795-45942fd9a084"
    )
    mock_upsert_payment_preference.assert_called_once_with(
        mock_payment_preferences.elements[0], employee
    )


@patch("massgov.pfml.delegated_payments.util.prepaid_debit_address_util.upsert_address")
@patch(
    "massgov.pfml.delegated_payments.sync_payment_preference_with_fineos_step.SyncPrepaidDebitPaymentPreferenceWithFineosStep.upsert_payment_preference"
)
@patch("massgov.pfml.delegated_payments.util.payment_preference_util.get_fineos_web_id")
@patch("massgov.pfml.delegated_payments.sync_payment_preference_with_fineos_step.create_client")
def test_prepaid_debit_card_without_default_payment(
    mock_fineos_create_client,
    mock_get_fineos_web_id,
    mock_upsert_payment_preference,
    mock_upsert_address,
    test_db_session,
    employee,
):
    """
    Verify that with a pending prepaid debit card registration and a non-default payment preference,
    the service will not set the payment preference and address record.
    """
    registration = ClaimantPrepaidRegistration(
        claimant_prepaid_registration_id=uuid.uuid4(),
        employee_id=employee.employee_id,
        prepaid_registration_status_id=PrepaidRegistrationStatus.PENDING.prepaid_registration_status_id,
    )
    test_db_session.add(registration)
    test_db_session.commit()

    mock_fineos_client = MagicMock()
    mock_fineos_create_client.return_value = mock_fineos_client

    mock_payment_preference = MagicMock()
    mock_payment_preference.default = False
    mock_payment_preference.paymentMethod.name = "Check"
    mock_payment_preferences = MagicMock()
    mock_payment_preferences.elements = [mock_payment_preference]

    mock_fineos_client.get_customer_payment_preference.return_value = mock_payment_preferences

    mock_get_fineos_web_id.return_value = "pfml_api_eb6809e8-fec2-474b-8795-45942fd9a084"

    service = SyncPrepaidDebitPaymentPreferenceWithFineosStep(test_db_session, test_db_session)
    service.sync_payment_preference_with_fineos()

    mock_get_fineos_web_id.assert_called_once_with(test_db_session, employee.employee_id)
    mock_fineos_client.get_customer_payment_preference.assert_called_with(
        "pfml_api_eb6809e8-fec2-474b-8795-45942fd9a084"
    )
    mock_upsert_payment_preference.assert_not_called()
    mock_upsert_address.assert_not_called()


@patch("massgov.pfml.delegated_payments.sync_payment_preference_with_fineos_step.logger.info")
def test_prepaid_debit_card_upsert_payment_preference_not_updated(
    mock_info_logger, test_db_session, employee
):
    """
    Verify that the service will not set the payment preference for the employee if it already exists
    and matches the new payment preference.
    """

    payment_preference_response = PaymentPreferenceResource(
        paymentMethod=PaymentMethodResponse(
            domainId=101, domainName="PaymentMethodDomain", fullId=123, name="Check"
        )
    )

    payment_preference_before = PaymentPreference(
        payment_method_id=PaymentMethod.CHECK.payment_method_id,
        employee_id=employee.employee_id,
        default=True,
    )

    test_db_session.add(payment_preference_before)
    test_db_session.commit()

    service = SyncPrepaidDebitPaymentPreferenceWithFineosStep(test_db_session, test_db_session)
    fineos_payment_preference = service.upsert_payment_preference(
        payment_preference_response, employee
    )

    assert isinstance(fineos_payment_preference, PaymentPreference)

    payment_preference_after = PaymentPreference(
        payment_method_id=PaymentMethod.CHECK.payment_method_id,
        employee_id=employee.employee_id,
        default=True,
    )

    assert payment_preference_before.payment_method_id == payment_preference_after.payment_method_id

    mock_info_logger.assert_called_once_with(
        f"Updated payment preference for employee {employee.employee_id} to Check"
    )


@patch("massgov.pfml.delegated_payments.sync_payment_preference_with_fineos_step.logger.info")
def test_prepaid_debit_card_upsert_payment_preference_with_blank_account_details(
    mock_info_logger, test_db_session, payment_preference_resource, employee
):
    """
    Verify that the service will set account and routing number as Null values in database instead
    of empty string.
    """

    payment_preference_resource.accountDetail.routingNumber = ""
    payment_preference_resource.accountDetail.accountNo = ""

    payment_preference_before = PaymentPreference(
        payment_method_id=PaymentMethod.PREPAID_CARD.payment_method_id,
        employee_id=employee.employee_id,
        default=True,
    )

    test_db_session.add(payment_preference_before)
    test_db_session.commit()

    service = SyncPrepaidDebitPaymentPreferenceWithFineosStep(test_db_session, test_db_session)
    fineos_payment_preference = service.upsert_payment_preference(
        payment_preference_resource, employee
    )

    assert isinstance(fineos_payment_preference, PaymentPreference)

    payment_preference_after = (
        test_db_session.query(PaymentPreference)
        .filter(
            PaymentPreference.employee_id == employee.employee_id,
            PaymentPreference.default.is_(True),
            PaymentPreference.account_number.is_(None),
        )
        .first()
    )

    assert payment_preference_before.payment_method_id == payment_preference_after.payment_method_id
    assert payment_preference_after is not None
    assert payment_preference_after.account_number is None
    assert payment_preference_after.routing_number is None

    mock_info_logger.assert_called_once_with(
        f"Updated payment preference for employee {employee.employee_id} to Check"
    )


@patch("massgov.pfml.delegated_payments.sync_payment_preference_with_fineos_step.logger.info")
def test_prepaid_debit_card_upsert_payment_preference_with_zero_account_details(
    mock_info_logger, test_db_session, payment_preference_resource, employee
):
    """
    Verify that the service will set account and routing number as Null values in database instead
    of empty string.
    """

    payment_preference_resource.accountDetail.routingNumber = "0"
    payment_preference_resource.accountDetail.accountNo = "0"

    payment_preference_before = PaymentPreference(
        payment_method_id=PaymentMethod.PREPAID_CARD.payment_method_id,
        employee_id=employee.employee_id,
        default=True,
    )

    test_db_session.add(payment_preference_before)
    test_db_session.commit()

    service = SyncPrepaidDebitPaymentPreferenceWithFineosStep(test_db_session, test_db_session)
    fineos_payment_preference = service.upsert_payment_preference(
        payment_preference_resource, employee
    )

    assert isinstance(fineos_payment_preference, PaymentPreference)

    payment_preference_after = (
        test_db_session.query(PaymentPreference)
        .filter(
            PaymentPreference.employee_id == employee.employee_id,
            PaymentPreference.default.is_(True),
            PaymentPreference.account_number.is_(None),
            PaymentPreference.routing_number.is_(None),
        )
        .first()
    )

    assert payment_preference_before.payment_method_id == payment_preference_after.payment_method_id
    assert payment_preference_after is not None
    assert payment_preference_after.account_number is None
    assert payment_preference_after.routing_number is None

    mock_info_logger.assert_called_once_with(
        f"Updated payment preference for employee {employee.employee_id} to Check"
    )


@patch("massgov.pfml.delegated_payments.sync_payment_preference_with_fineos_step.logger.info")
def test_prepaid_debit_card_upsert_payment_preference_with_null_account_details(
    mock_info_logger, test_db_session, payment_preference_resource, employee
):
    """
    Verify that the service will set account and routing number as Null values in database instead
    of empty string.
    """

    payment_preference_resource.accountDetail.routingNumber = None
    payment_preference_resource.accountDetail.accountNo = None

    payment_preference_before = PaymentPreference(
        payment_method_id=PaymentMethod.PREPAID_CARD.payment_method_id,
        employee_id=employee.employee_id,
        default=True,
    )

    test_db_session.add(payment_preference_before)
    test_db_session.commit()

    service = SyncPrepaidDebitPaymentPreferenceWithFineosStep(test_db_session, test_db_session)
    fineos_payment_preference = service.upsert_payment_preference(
        payment_preference_resource, employee
    )

    assert isinstance(fineos_payment_preference, PaymentPreference)

    payment_preference_after = (
        test_db_session.query(PaymentPreference)
        .filter(
            PaymentPreference.employee_id == employee.employee_id,
            PaymentPreference.default.is_(True),
            PaymentPreference.account_number.is_(None),
            PaymentPreference.routing_number.is_(None),
        )
        .first()
    )

    assert payment_preference_before.payment_method_id == payment_preference_after.payment_method_id
    assert payment_preference_after is not None
    assert payment_preference_after.account_number is None
    assert payment_preference_after.routing_number is None

    mock_info_logger.assert_called_once_with(
        f"Updated payment preference for employee {employee.employee_id} to Check"
    )


@patch("massgov.pfml.delegated_payments.sync_payment_preference_with_fineos_step.logger.info")
def test_prepaid_debit_card_upsert_payment_preference_updated(
    mock_info_logger, test_db_session, employee
):
    """
    Verify that the service will update the pyament preference for employee if it already exists
    but does not match the new payment preference.
    """

    payment_preference_response = PaymentPreferenceResource(
        paymentMethod=PaymentMethodResponse(
            domainId=101, domainName="PaymentMethodDomain", fullId=123, name="Check"
        )
    )

    payment_preference = PaymentPreference(
        payment_method_id=PaymentMethod.ACH.payment_method_id,
        employee_id=employee.employee_id,
        default=True,
    )

    test_db_session.add(payment_preference)
    test_db_session.commit()

    service = SyncPrepaidDebitPaymentPreferenceWithFineosStep(test_db_session, test_db_session)
    fineos_payment_preference = service.upsert_payment_preference(
        payment_preference_response, employee
    )

    assert isinstance(fineos_payment_preference, PaymentPreference)

    payment_preference_db = (
        test_db_session.query(PaymentPreference)
        .filter_by(
            employee_id=employee.employee_id,
        )
        .first()
    )

    mock_info_logger.assert_called_once_with(
        f"Updated payment preference for employee {employee.employee_id} to Check"
    )
    assert payment_preference_db.payment_method_id == PaymentMethod.CHECK.payment_method_id


@patch("massgov.pfml.delegated_payments.sync_payment_preference_with_fineos_step.logger.info")
def test_prepaid_debit_card_upsert_payment_preference_created(
    mock_info_logger, test_db_session, employee
):
    """
    Verify that the service will set the payment preference for the employee if it doesn't exists.
    """

    payment_preference_response = PaymentPreferenceResource(
        paymentMethod=PaymentMethodResponse(
            domainId=101, domainName="PaymentMethodDomain", fullId=123, name="Check"
        )
    )

    service = SyncPrepaidDebitPaymentPreferenceWithFineosStep(test_db_session, test_db_session)
    service.upsert_payment_preference(payment_preference_response, employee)

    payment_preference_db = (
        test_db_session.query(PaymentPreference)
        .filter_by(
            employee_id=employee.employee_id,
        )
        .first()
    )

    if payment_preference_db:
        assert payment_preference_db.payment_method_id == PaymentMethod.CHECK.payment_method_id


@patch("massgov.pfml.delegated_payments.sync_prepaid_debit_card_registration.logger.info")
def test_sync_prepaid_debit_card_registration_set_inactive(
    mock_info_logger, test_db_session, initialize_factories_session
):
    employee1 = EmployeeFactory.create()
    employee2 = EmployeeFactory.create()

    payment_preferences = [PrepaidRegistrationStatus.PENDING, PrepaidRegistrationStatus.ERROR]

    for employee in [employee1, employee2]:
        payment_preference = payment_preferences.pop()

        registration = ClaimantPrepaidRegistration(
            claimant_prepaid_registration_id=uuid.uuid4(),
            employee_id=employee.employee_id,
            prepaid_registration_status_id=payment_preference.prepaid_registration_status_id,
        )
        test_db_session.add(registration)
        test_db_session.commit()

        # Create payment preference with default as True but non PREPAID_CARD payment method
        payment_preference = PaymentPreference(
            payment_method_id=PaymentMethod.CHECK.payment_method_id,
            employee_id=employee.employee_id,
            default=True,
        )

        test_db_session.add(payment_preference)
        test_db_session.commit()

        # Create payment preference with default as False and PREPAID_CARD payment method
        payment_preference = PaymentPreference(
            payment_method_id=PaymentMethod.PREPAID_CARD.payment_method_id,
            employee_id=employee.employee_id,
            default=False,
        )

        test_db_session.add(payment_preference)
        test_db_session.commit()

    service = SyncPrepaidDebitCardRegistrationStep(test_db_session, test_db_session)
    service.sync_pending_prepaid_registrations()

    for employee in [employee1, employee2]:
        prepaid_registration = (
            test_db_session.query(ClaimantPrepaidRegistration)
            .filter_by(
                employee_id=employee.employee_id,
            )
            .first()
        )

        assert (
            prepaid_registration.prepaid_registration_status_id
            == PrepaidRegistrationStatus.INACTIVE.prepaid_registration_status_id
        )

    mock_info_logger.assert_has_calls(
        [
            call("Found 2 prepaid debit card registrations to sync"),
            call(
                f"Syncing prepaid debit card registration for employee {employee1.employee_id} status Inactive"
            ),
            call(
                f"Syncing prepaid debit card registration for employee {employee2.employee_id} status Inactive"
            ),
        ]
    )


@patch("massgov.pfml.delegated_payments.sync_prepaid_debit_card_registration.logger.info")
def test_sync_prepaid_debit_card_registration_set_inactive_with_no_default(
    mock_info_logger, test_db_session, initialize_factories_session
):
    """
    Verify that the service will not set the prepaid debit card registrations to INACTIVE even though
    the payment preference does exists but not marked as Default.
    """

    employee1 = EmployeeFactory.create()
    employee2 = EmployeeFactory.create()

    payment_preferences = [PrepaidRegistrationStatus.PENDING, PrepaidRegistrationStatus.ERROR]

    for employee in [employee1, employee2]:
        payment_preference = payment_preferences.pop()

        registration = ClaimantPrepaidRegistration(
            claimant_prepaid_registration_id=uuid.uuid4(),
            employee_id=employee.employee_id,
            prepaid_registration_status_id=payment_preference.prepaid_registration_status_id,
        )
        test_db_session.add(registration)
        test_db_session.commit()

        # Create payment preference with default as False and PREPAID_CARD payment method
        payment_preference = PaymentPreference(
            payment_method_id=PaymentMethod.CHECK.payment_method_id,
            employee_id=employee.employee_id,
            default=False,
        )

        test_db_session.add(payment_preference)
        test_db_session.commit()

    service = SyncPrepaidDebitCardRegistrationStep(test_db_session, test_db_session)
    service.sync_pending_prepaid_registrations()

    for employee in [employee1, employee2]:
        prepaid_registration = (
            test_db_session.query(ClaimantPrepaidRegistration)
            .filter_by(
                employee_id=employee.employee_id,
            )
            .first()
        )

        assert (
            prepaid_registration.prepaid_registration_status_id
            != PrepaidRegistrationStatus.INACTIVE.prepaid_registration_status_id
        )

    mock_info_logger.assert_has_calls(
        [
            call("Found 0 prepaid debit card registrations to sync"),
        ]
    )


@patch("massgov.pfml.delegated_payments.sync_prepaid_debit_card_registration.logger.info")
def test_sync_prepaid_debit_card_registration_no_pending_registration(
    mock_info_logger, test_db_session, employee
):

    registration = ClaimantPrepaidRegistration(
        claimant_prepaid_registration_id=uuid.uuid4(),
        employee_id=employee.employee_id,
        prepaid_registration_status_id=PrepaidRegistrationStatus.ACTIVE.prepaid_registration_status_id,
    )
    test_db_session.add(registration)
    test_db_session.commit()

    payment_preference = PaymentPreference(
        payment_method_id=PaymentMethod.PREPAID_CARD.payment_method_id,
        employee_id=employee.employee_id,
        default=True,
    )

    test_db_session.add(payment_preference)
    test_db_session.commit()

    service = SyncPrepaidDebitCardRegistrationStep(test_db_session, test_db_session)
    service.sync_pending_prepaid_registrations()

    prepaid_registration = (
        test_db_session.query(ClaimantPrepaidRegistration)
        .filter_by(
            employee_id=employee.employee_id,
        )
        .first()
    )

    assert (
        prepaid_registration.prepaid_registration_status_id
        == PrepaidRegistrationStatus.ACTIVE.prepaid_registration_status_id
    )

    mock_info_logger.assert_called_with("Found 0 prepaid debit card registrations to sync")


@patch("massgov.pfml.delegated_payments.sync_prepaid_debit_card_registration.logger.info")
def test_sync_prepaid_debit_card_registration_with_pending_prepaid_card(
    mock_info_logger, test_db_session, employee
):

    registration = ClaimantPrepaidRegistration(
        claimant_prepaid_registration_id=uuid.uuid4(),
        employee_id=employee.employee_id,
        prepaid_registration_status_id=PrepaidRegistrationStatus.PENDING.prepaid_registration_status_id,
    )
    test_db_session.add(registration)
    test_db_session.commit()

    # Create payment preference with default as True but without routing and account number
    payment_preference_1 = PaymentPreference(
        payment_method_id=PaymentMethod.PREPAID_CARD.payment_method_id,
        employee_id=employee.employee_id,
        default=True,
    )

    # Create payment preference with default as False and with routing and account number
    payment_preference_2 = PaymentPreference(
        payment_method_id=PaymentMethod.PREPAID_CARD.payment_method_id,
        employee_id=employee.employee_id,
        default=False,
        routing_number="*********",
        account_number="*********",
    )

    test_db_session.add(payment_preference_1)
    test_db_session.add(payment_preference_2)
    test_db_session.commit()

    service = SyncPrepaidDebitCardRegistrationStep(test_db_session, test_db_session)
    service.sync_pending_prepaid_registrations()

    prepaid_registration = (
        test_db_session.query(ClaimantPrepaidRegistration)
        .filter_by(
            employee_id=employee.employee_id,
        )
        .first()
    )

    assert (
        prepaid_registration.prepaid_registration_status_id
        == PrepaidRegistrationStatus.PENDING.prepaid_registration_status_id
    )

    mock_info_logger.assert_called_with("Found 0 prepaid debit card registrations to sync")


@patch("massgov.pfml.delegated_payments.sync_prepaid_debit_card_registration.logger.info")
def test_sync_already_registered_prepaid_debit_cards(mock_info_logger, test_db_session, employee):

    registration = ClaimantPrepaidRegistration(
        claimant_prepaid_registration_id=uuid.uuid4(),
        employee_id=employee.employee_id,
        prepaid_registration_status_id=PrepaidRegistrationStatus.PENDING.prepaid_registration_status_id,
    )
    test_db_session.add(registration)
    test_db_session.commit()

    payment_preference_1 = PaymentPreference(
        payment_method_id=PaymentMethod.PREPAID_CARD.payment_method_id,
        employee_id=employee.employee_id,
        default=True,
        routing_number="*********",
        account_number="*********",
    )

    payment_preference_2 = PaymentPreference(
        payment_method_id=PaymentMethod.PREPAID_CARD.payment_method_id,
        employee_id=employee.employee_id,
        default=False,
        routing_number="*********",
        account_number="*********",
    )

    test_db_session.add(payment_preference_1)
    test_db_session.add(payment_preference_2)
    test_db_session.commit()

    service = SyncPrepaidDebitCardRegistrationStep(test_db_session, test_db_session)
    service.sync_already_registered_prepaid_debit_cards()

    prepaid_registration = (
        test_db_session.query(ClaimantPrepaidRegistration)
        .filter_by(
            employee_id=employee.employee_id,
        )
        .first()
    )

    assert (
        prepaid_registration.prepaid_registration_status_id
        == PrepaidRegistrationStatus.INACTIVE.prepaid_registration_status_id
    )

    mock_info_logger.assert_any_call(
        "Found 1 registrations with defined account and routing numbers in payment preference but pending for registration"
    )
    mock_info_logger.assert_any_call(
        f"Syncing prepaid debit card registration for employee {employee.employee_id} status Inactive"
    )


@patch("massgov.pfml.delegated_payments.sync_prepaid_debit_card_registration.logger.info")
def test_sync_already_registered_prepaid_debit_cards_undefined_routing_account_number(
    mock_info_logger, test_db_session, employee
):

    registration = ClaimantPrepaidRegistration(
        claimant_prepaid_registration_id=uuid.uuid4(),
        employee_id=employee.employee_id,
        prepaid_registration_status_id=PrepaidRegistrationStatus.PENDING.prepaid_registration_status_id,
    )
    test_db_session.add(registration)
    test_db_session.commit()

    payment_preference = PaymentPreference(
        payment_method_id=PaymentMethod.PREPAID_CARD.payment_method_id,
        employee_id=employee.employee_id,
        default=True,
    )

    test_db_session.add(payment_preference)
    test_db_session.commit()

    service = SyncPrepaidDebitCardRegistrationStep(test_db_session, test_db_session)
    service.sync_already_registered_prepaid_debit_cards()

    prepaid_registration = (
        test_db_session.query(ClaimantPrepaidRegistration)
        .filter_by(
            employee_id=employee.employee_id,
        )
        .first()
    )

    assert (
        prepaid_registration.prepaid_registration_status_id
        == PrepaidRegistrationStatus.PENDING.prepaid_registration_status_id
    )

    mock_info_logger.assert_called_with(
        "Found 0 registrations with defined account and routing numbers in payment preference but pending for registration"
    )


@patch("massgov.pfml.delegated_payments.update_fineos_payment_preference_step.logger.error")
@patch("massgov.pfml.delegated_payments.update_fineos_payment_preference_step.logger.info")
def test_update_fineos_payment_preference_missing_routing_account_number(
    mock_info_logger, mock_error_logger, test_db_session, employee
):

    registration = ClaimantPrepaidRegistration(
        claimant_prepaid_registration_id=uuid.uuid4(),
        employee_id=employee.employee_id,
        prepaid_registration_status_id=PrepaidRegistrationStatus.UPDATE.prepaid_registration_status_id,
    )
    test_db_session.add(registration)
    test_db_session.commit()

    service = UpdateFineosPaymentPreferenceStep(test_db_session, test_db_session)
    service.update_fineos_payment_preference()

    mock_info_logger.assert_called_with(
        "Found 1 prepaid debit card registrations to update in Fineos"
    )

    mock_error_logger.assert_called_with(
        f"Prepaid debit card registration for employee {employee.employee_id} is missing account number or routing number"
    )


@patch("massgov.pfml.delegated_payments.update_fineos_payment_preference_step.logger.error")
@patch("massgov.pfml.delegated_payments.update_fineos_payment_preference_step.logger.info")
def test_update_fineos_payment_preference_read_update_from_prior_status(
    mock_info_logger, mock_error_logger, test_db_session, employee
):

    registration1 = ClaimantPrepaidRegistration(
        claimant_prepaid_registration_id=uuid.uuid4(),
        employee_id=employee.employee_id,
        prepaid_registration_status_id=PrepaidRegistrationStatus.UPDATE.prepaid_registration_status_id,
    )
    registration2 = ClaimantPrepaidRegistration(
        claimant_prepaid_registration_id=uuid.uuid4(),
        employee_id=employee.employee_id,
        prepaid_registration_status_id=PrepaidRegistrationStatus.UPDATE_FROM_PRIOR.prepaid_registration_status_id,
    )
    test_db_session.add(registration1)
    test_db_session.add(registration2)
    test_db_session.commit()

    service = UpdateFineosPaymentPreferenceStep(test_db_session, test_db_session)
    service.update_fineos_payment_preference()

    mock_info_logger.assert_called_with(
        "Found 2 prepaid debit card registrations to update in Fineos"
    )


@patch("massgov.pfml.delegated_payments.util.payment_preference_util.get_fineos_web_id")
@patch("massgov.pfml.delegated_payments.update_fineos_payment_preference_step.logger.error")
@patch("massgov.pfml.delegated_payments.update_fineos_payment_preference_step.logger.info")
def test_update_fineos_payment_preference_no_fineos_web_id(
    mock_info_logger, mock_error_logger, mock_get_fineos_web_id, test_db_session, employee
):

    registration = ClaimantPrepaidRegistration(
        claimant_prepaid_registration_id=uuid.uuid4(),
        employee_id=employee.employee_id,
        prepaid_registration_status_id=PrepaidRegistrationStatus.UPDATE.prepaid_registration_status_id,
        routing_number="*********",
        account_number="*********",
    )
    test_db_session.add(registration)
    test_db_session.commit()

    mock_get_fineos_web_id.return_value = None

    service = UpdateFineosPaymentPreferenceStep(test_db_session, test_db_session)
    service.update_fineos_payment_preference()

    mock_info_logger.assert_called_with(
        "Found 1 prepaid debit card registrations to update in Fineos"
    )
    mock_get_fineos_web_id.assert_called_once_with(test_db_session, employee.employee_id)
    mock_error_logger.assert_called_with(
        f"No FINEOS Web ID found for employee {employee.employee_id}, skipping"
    )


@patch(
    "massgov.pfml.delegated_payments.util.payment_preference_util.find_default_payment_preference"
)
@patch("massgov.pfml.delegated_payments.util.payment_preference_util.get_fineos_web_id")
@patch("massgov.pfml.delegated_payments.update_fineos_payment_preference_step.logger.error")
@patch("massgov.pfml.delegated_payments.update_fineos_payment_preference_step.logger.info")
def test_update_fineos_payment_preference_no_default_payment_preference(
    mock_info_logger,
    mock_error_logger,
    mock_get_fineos_web_id,
    mock_get_default_payment_preference_id,
    test_db_session,
    employee,
):

    registration = ClaimantPrepaidRegistration(
        claimant_prepaid_registration_id=uuid.uuid4(),
        employee_id=employee.employee_id,
        prepaid_registration_status_id=PrepaidRegistrationStatus.UPDATE.prepaid_registration_status_id,
        routing_number="*********",
        account_number="**********",
    )
    test_db_session.add(registration)
    test_db_session.commit()

    mock_get_fineos_web_id.return_value = "pfml_api_eb6809e8-fec2-474b-8795-45942fd9a084"
    mock_get_default_payment_preference_id.return_value = None

    service = UpdateFineosPaymentPreferenceStep(test_db_session, test_db_session)
    service.update_fineos_payment_preference()

    mock_error_logger.assert_called_with(
        f"No default payment preference found for employee {employee.employee_id} in fineos, skipping"
    )
    mock_get_fineos_web_id.assert_called_once_with(test_db_session, employee.employee_id)


@patch("massgov.pfml.delegated_payments.util.payment_preference_util.get_fineos_web_id")
@patch("massgov.pfml.delegated_payments.update_fineos_payment_preference_step.logger.error")
@patch("massgov.pfml.delegated_payments.update_fineos_payment_preference_step.logger.info")
def test_update_fineos_payment_preference(
    mock_info_logger, mock_error_logger, mock_get_fineos_web_id, test_db_session, employee
):

    registration = ClaimantPrepaidRegistration(
        claimant_prepaid_registration_id=uuid.uuid4(),
        employee_id=employee.employee_id,
        prepaid_registration_status_id=PrepaidRegistrationStatus.UPDATE.prepaid_registration_status_id,
        routing_number="*********",
        account_number="**********",
    )
    test_db_session.add(registration)
    test_db_session.commit()

    payment_preference = PaymentPreference(
        payment_method_id=PaymentMethod.PREPAID_CARD.payment_method_id,
        employee_id=employee.employee_id,
        default=True,
    )

    test_db_session.add(payment_preference)
    test_db_session.commit()

    mock_get_fineos_web_id.return_value = "pfml_api_eb6809e8-fec2-474b-8795-45942fd9a084"

    service = UpdateFineosPaymentPreferenceStep(test_db_session, test_db_session)
    service.update_fineos_payment_preference()

    mock_get_fineos_web_id.assert_called_once_with(test_db_session, employee.employee_id)
    mock_info_logger.assert_called_with(
        f"Successfully updated Registration record for employee {employee.employee_id}"
    )
    mock_error_logger.assert_not_called()

    # Verify the account and routing number is updated in the payment preference
    payment_preference_db = (
        test_db_session.query(PaymentPreference)
        .filter_by(
            employee_id=employee.employee_id,
            default=True,
        )
        .first()
    )
    assert payment_preference_db.routing_number == "*********"
    assert payment_preference_db.account_number == "**********"

    registration_after = (
        test_db_session.query(ClaimantPrepaidRegistration)
        .filter_by(
            employee_id=employee.employee_id,
        )
        .first()
    )
    assert (
        registration_after.prepaid_registration_status_id
        == PrepaidRegistrationStatus.ACTIVE.prepaid_registration_status_id
    )


@patch("massgov.pfml.delegated_payments.util.payment_preference_util.get_fineos_web_id")
@patch("massgov.pfml.delegated_payments.update_fineos_payment_preference_step.logger.error")
@patch("massgov.pfml.delegated_payments.update_fineos_payment_preference_step.logger.info")
def test_update_fineos_payment_preference_for_update_from_prior_status(
    mock_info_logger, mock_error_logger, mock_get_fineos_web_id, test_db_session, employee
):

    registration = ClaimantPrepaidRegistration(
        claimant_prepaid_registration_id=uuid.uuid4(),
        employee_id=employee.employee_id,
        prepaid_registration_status_id=PrepaidRegistrationStatus.UPDATE_FROM_PRIOR.prepaid_registration_status_id,
        routing_number="*********",
        account_number="**********",
    )
    test_db_session.add(registration)
    test_db_session.commit()

    payment_preference = PaymentPreference(
        payment_method_id=PaymentMethod.PREPAID_CARD.payment_method_id,
        employee_id=employee.employee_id,
        default=True,
    )

    test_db_session.add(payment_preference)
    test_db_session.commit()

    mock_get_fineos_web_id.return_value = "pfml_api_eb6809e8-fec2-474b-8795-45942fd9a084"

    service = UpdateFineosPaymentPreferenceStep(test_db_session, test_db_session)
    service.update_fineos_payment_preference()

    mock_get_fineos_web_id.assert_called_once_with(test_db_session, employee.employee_id)
    mock_info_logger.assert_called_with(
        f"Successfully updated Registration record for employee {employee.employee_id}"
    )
    mock_error_logger.assert_not_called()

    # Verify the account and routing number is updated in the payment preference
    payment_preference_db = (
        test_db_session.query(PaymentPreference)
        .filter_by(
            employee_id=employee.employee_id,
            default=True,
        )
        .first()
    )
    assert payment_preference_db.routing_number == "*********"
    assert payment_preference_db.account_number == "**********"

    registration_after = (
        test_db_session.query(ClaimantPrepaidRegistration)
        .filter_by(
            employee_id=employee.employee_id,
        )
        .first()
    )
    assert (
        registration_after.prepaid_registration_status_id
        == PrepaidRegistrationStatus.INACTIVE.prepaid_registration_status_id
    )


@patch("massgov.pfml.delegated_payments.util.payment_preference_util.get_fineos_web_id")
@patch(
    "massgov.pfml.delegated_payments.util.payment_preference_util.find_default_payment_preference"
)
@patch("massgov.pfml.delegated_payments.update_fineos_payment_preference_step.create_client")
@patch("massgov.pfml.delegated_payments.update_fineos_payment_preference_step.logger.error")
@patch("massgov.pfml.delegated_payments.update_fineos_payment_preference_step.logger.info")
def test_update_fineos_payment_preference_failed_in_fineos(
    mock_info_logger,
    mock_error_logger,
    mock_create_client,
    mock_payment_preference,
    mock_get_fineos_web_id,
    test_db_session,
    employee,
):
    """
    Verify that if the service fails to update the payment preference (account and routing number) in Fineos,
    the registration status will changed to UPDATE_ERROR
    """

    registration = ClaimantPrepaidRegistration(
        claimant_prepaid_registration_id=uuid.uuid4(),
        employee_id=employee.employee_id,
        prepaid_registration_status_id=PrepaidRegistrationStatus.UPDATE.prepaid_registration_status_id,
        routing_number="*********",
        account_number="**********",
    )
    test_db_session.add(registration)
    test_db_session.commit()

    payment_preference = PaymentPreference(
        payment_method_id=PaymentMethod.PREPAID_CARD.payment_method_id,
        employee_id=employee.employee_id,
        default=True,
    )

    test_db_session.add(payment_preference)
    test_db_session.commit()

    mock_client_obj = MagicMock()
    mock_create_client.return_value = mock_client_obj
    mock_client_obj.update_customer_payment_preference.side_effect = Exception(
        "Failed to update payment preference in Fineos"
    )

    mock_get_fineos_web_id.return_value = "pfml_api_eb6809e8-fec2-474b-8795-45942fd9a084"
    mock_payment_preference.id.return_value = "7706-964225"

    service = UpdateFineosPaymentPreferenceStep(test_db_session, test_db_session)
    service.update_fineos_payment_preference()

    registration_after = (
        test_db_session.query(ClaimantPrepaidRegistration)
        .filter_by(
            employee_id=employee.employee_id,
        )
        .first()
    )
    assert (
        registration_after.prepaid_registration_status_id
        == PrepaidRegistrationStatus.UPDATE_ERROR.prepaid_registration_status_id
    )


def test_prepaid_debit_card_get_pending_verified_registrations_with_unverified_record(
    test_db_session, create_registration_with_addresses
):
    """
    Verify that the service will not process or error a pending prepaid registrations
    if associated address is not verified by Experian.
    """
    create_registration_with_addresses(is_experian_verified=False)

    service = PrepaidDebitCardRegistrationStep(test_db_session, test_db_session)

    registrations = service.get_employees_pending_registration()

    assert (
        registrations[0][0].prepaid_registration_status.prepaid_registration_status_id
        == PrepaidRegistrationStatus.PENDING.prepaid_registration_status_id
    )


def test_prepaid_debit_card_verified_record_with_non_default_payment_preference(
    test_db_session, create_registration_with_addresses
):
    """
    Verify that the service will not return any registration records if the payment preference is not
    set to default.
    """
    create_registration_with_addresses(is_experian_verified=True)

    # Mark all payment preferences as non-default
    test_db_session.query(PaymentPreference).update({PaymentPreference.default: False})
    test_db_session.commit()

    service = PrepaidDebitCardRegistrationStep(test_db_session, test_db_session)
    registrations = service.get_employees_pending_registration()

    assert len(registrations) == 0


def test_prepaid_debit_card_get_pending_verified_registrations_with_verified_record(
    test_db_session, create_registration_with_addresses
):
    """
    Verify that the service will return a list of employees with pending status prepaid registrations
    if  associated address is verified by Experian.
    """
    create_registration_with_addresses(is_experian_verified=True)

    service = PrepaidDebitCardRegistrationStep(test_db_session, test_db_session)
    registrations = service.get_employees_pending_registration()

    assert len(registrations) == 1
    assert isinstance(registrations, list)

    for registration in registrations:
        assert isinstance(registration, Row)
        assert isinstance(registration[0], ClaimantPrepaidRegistration)
        assert isinstance(registration[1], Employee)
        assert isinstance(registration[2], ExperianAddressPair)


def test_prepaid_debit_card_get_error_verified_registrations_with_verified_record(
    test_db_session, create_registration_with_addresses
):
    """
    Verify that the service will return a list of employees with error status prepaid registrations
    if associated address is verified by Experian.
    """
    create_registration_with_addresses(
        is_experian_verified=True, registration_status=PrepaidRegistrationStatus.ERROR
    )

    service = PrepaidDebitCardRegistrationStep(test_db_session, test_db_session)
    registrations = service.get_employees_pending_registration()

    assert len(registrations) == 1
    assert isinstance(registrations, list)

    for registration in registrations:
        assert isinstance(registration, Row)
        assert isinstance(registration[0], ClaimantPrepaidRegistration)
        assert isinstance(registration[1], Employee)
        assert isinstance(registration[2], ExperianAddressPair)


@patch(
    "massgov.pfml.delegated_payments.prepaid_debit_card_registration_step.PrepaidDebitCardRegistrationStep.create_fineos_task"
)
def test_prepaid_debit_card_registrations_update_status(
    mock_fineos_task, test_db_session, create_registration_with_addresses
):
    """
    Verify that the service will update the status of the prepaid registration.
    Will not create a fineos task.
    """
    create_registration_with_addresses(is_experian_verified=True)

    employee = test_db_session.query(Employee).first()
    employee.date_of_birth = "1990-01-01"
    test_db_session.commit()

    prepaid_registration_before = (
        test_db_session.query(ClaimantPrepaidRegistration)
        .filter_by(
            employee_id=employee.employee_id,
        )
        .first()
    )

    assert (
        prepaid_registration_before.prepaid_registration_status_id
        == PrepaidRegistrationStatus.PENDING.prepaid_registration_status_id
    )

    service = PrepaidDebitCardRegistrationStep(test_db_session, test_db_session)
    service.prepaid_debit_card_registration()

    prepaid_registration_after = (
        test_db_session.query(ClaimantPrepaidRegistration)
        .filter_by(
            employee_id=employee.employee_id,
        )
        .first()
    )

    assert (
        prepaid_registration_after.prepaid_registration_status_id
        == PrepaidRegistrationStatus.UPDATE.prepaid_registration_status_id
    )

    assert mock_fineos_task.call_count == 0


def test_prepaid_debit_card_registrations_order_card_unsuccessful(
    test_db_session, create_registration_with_addresses, caplog
):
    """
    Verrify that the service will update the status of the prepaid registration to ERROR if the
    order card request is unsuccessful.
    """

    create_registration_with_addresses(is_experian_verified=True)

    employee = test_db_session.query(Employee).first()
    employee.date_of_birth = None
    test_db_session.commit()

    prepaid_registration_before = (
        test_db_session.query(ClaimantPrepaidRegistration)
        .filter_by(
            employee_id=employee.employee_id,
        )
        .first()
    )

    assert (
        prepaid_registration_before.prepaid_registration_status_id
        == PrepaidRegistrationStatus.PENDING.prepaid_registration_status_id
    )

    with caplog.at_level(logging.ERROR):
        service = PrepaidDebitCardRegistrationStep(test_db_session, test_db_session)
        service.prepaid_debit_card_registration()

        assert "Error registering prepaid debit card for employee" in caplog.text

    prepaid_registration_after = (
        test_db_session.query(ClaimantPrepaidRegistration)
        .filter_by(
            employee_id=employee.employee_id,
        )
        .first()
    )

    assert (
        prepaid_registration_after.prepaid_registration_status_id
        == PrepaidRegistrationStatus.ERROR.prepaid_registration_status_id
    )


@patch("massgov.pfml.delegated_payments.prepaid_debit_card_registration_step.fineos_create_client")
def test_prepaid_debit_card_registrations_create_fineos_task_successful(
    mock_fineos_create_client, test_db_session
):
    """Test successful task creation."""
    mock_fineos_client = MagicMock()
    mock_fineos_create_client.return_value = mock_fineos_client

    service = PrepaidDebitCardRegistrationStep(test_db_session, test_db_session)
    service.create_fineos_task(
        absence_case_id="NTN-001-001", subject="Test Subject", description="Test Description"
    )

    mock_fineos_client.create_task.assert_called_once()
    args, kwargs = mock_fineos_client.create_task.call_args

    assert args[0].Name == "Address Validation Error"  # WorkType
    assert args[1].CaseNumber == "NTN-001-001"  # CaseIdentifier
    assert args[2].GenericSubject == "Test Subject"  # ActivitySubjectIdentifier
    assert args[3] == "Test Description"


@patch("massgov.pfml.delegated_payments.prepaid_debit_card_registration_step.fineos_create_client")
def test_prepaid_debit_card_registrations_prevent_duplicate_task(
    mock_fineos_create_client,
    test_db_session,
    task_reference_file,
    create_registration_with_addresses,
):
    FineosExtractVbiTaskReportDeltaSomFactory.create(
        status=Constants.VBI_TASK_REPORT_STATUS_OPEN,
        tasktypename=FineosTaskType.ADDRESS_VALIDATION_ERROR.fineos_task_type_description,
        reference_file=task_reference_file,
        casenumber="NTN-001-001",
        creationdate="2025-02-01 00:00:00",
    )

    create_registration_with_addresses(is_experian_verified=False)

    employee = test_db_session.query(Employee).first()
    employee.date_of_birth = None
    test_db_session.commit()

    """Test task creation and preventing duplicates."""
    mock_fineos_client = MagicMock()
    mock_fineos_create_client.return_value = mock_fineos_client

    service = PrepaidDebitCardRegistrationStep(test_db_session, test_db_session)
    service.prepaid_debit_card_registration()

    mock_fineos_client.create_task.assert_not_called()


@patch("massgov.pfml.delegated_payments.prepaid_debit_card_registration_step.fineos_create_client")
def test_prepaid_debit_card_registrations_prevent_duplicate_task_closed(
    mock_fineos_create_client,
    test_db_session,
    task_reference_file,
    create_registration_with_addresses,
):
    FineosExtractVbiTaskReportDeltaSomFactory.create(
        status=Constants.VBI_TASK_REPORT_STATUS_CLOSED,
        tasktypename=FineosTaskType.ADDRESS_VALIDATION_ERROR.fineos_task_type_description,
        reference_file=task_reference_file,
        casenumber="NTN-001-001",
        creationdate="2025-02-01 00:00:00",
        closeddate=datetime.now() - timedelta(days=1),
    )

    create_registration_with_addresses(is_experian_verified=False)

    employee = test_db_session.query(Employee).first()
    employee.date_of_birth = None
    test_db_session.commit()

    """Test task creation and preventing duplicates."""
    mock_fineos_client = MagicMock()
    mock_fineos_create_client.return_value = mock_fineos_client

    service = PrepaidDebitCardRegistrationStep(test_db_session, test_db_session)
    service.prepaid_debit_card_registration()

    mock_fineos_client.create_task.assert_not_called()


@patch("massgov.pfml.delegated_payments.prepaid_debit_card_registration_step.fineos_create_client")
def test_prepaid_debit_card_registrations_create_task_closed_60plus(
    mock_fineos_create_client,
    test_db_session,
    task_reference_file,
    create_registration_with_addresses,
):
    FineosExtractVbiTaskReportDeltaSomFactory.create(
        status=Constants.VBI_TASK_REPORT_STATUS_CLOSED,
        tasktypename=FineosTaskType.ADDRESS_VALIDATION_ERROR.fineos_task_type_description,
        reference_file=task_reference_file,
        casenumber="NTN-001-001",
        creationdate="2025-01-01 00:00:00",
        closeddate=(datetime.now() - timedelta(days=61)).strftime("%Y-%m-%d %H:%M:%S"),
    )

    create_registration_with_addresses(is_experian_verified=False)

    employee = test_db_session.query(Employee).first()
    employee.date_of_birth = None
    test_db_session.commit()

    """Test task creation and preventing duplicates."""
    mock_fineos_client = MagicMock()
    mock_fineos_create_client.return_value = mock_fineos_client

    service = PrepaidDebitCardRegistrationStep(test_db_session, test_db_session)
    service.prepaid_debit_card_registration()

    mock_fineos_client.create_task.assert_called()


@patch("massgov.pfml.delegated_payments.prepaid_debit_card_registration_step.fineos_create_client")
def test_prepaid_debit_card_registrations_create_fineos_task_error(
    mock_fineos_create_client, test_db_session
):
    """Test successful task creation."""
    mock_fineos_client = MagicMock()
    mock_fineos_create_client.return_value = mock_fineos_client

    service = PrepaidDebitCardRegistrationStep(test_db_session, test_db_session)

    with pytest.raises(
        Exception, match="missing 1 required positional argument: 'absence_case_id'"
    ):
        service.create_fineos_task(subject="Test Subject", description="Test Description")

    mock_fineos_client.create_task.assert_not_called()
