import logging  # noqa: B1
from concurrent.futures import Thread<PERSON>oolExecutor
from datetime import date, datetime, timedelta
from unittest import mock
from unittest.mock import MagicMock

import defusedxml.ElementTree as ElementTree
import faker
import pytest
from freezegun import freeze_time

import massgov.pfml.fineos
import massgov.pfml.fineos.mock.field
import massgov.pfml.fineos.mock_client as fineos_mock
import massgov.pfml.services.applications as application_service
from massgov.pfml.api.constants.documents import OVERPAYMENT_DOCUMENT_TYPES
from massgov.pfml.api.models.claims.responses import AbsencePeriodResponse
from massgov.pfml.api.models.IntermittentLeaveEpisodes.common import IntermittentLeaveEpisode
from massgov.pfml.api.services import fineos_actions
from massgov.pfml.db.lookup_data.absences import AbsencePeriodType
from massgov.pfml.db.lookup_data.applications import (
    <PERSON><PERSON><PERSON><PERSON>eek,
    LeaveReason,
    LeaveReasonQualifier,
    RelationshipQualifier,
    RelationshipToCaregiver,
)
from massgov.pfml.db.lookup_data.change_request import ChangeRequestType
from massgov.pfml.db.lookup_data.documents import DocumentType
from massgov.pfml.db.lookup_data.employees import (
    AddressType,
    BankAccountType,
    LeaveRequestDecision,
    PaymentMethod,
)
from massgov.pfml.db.lookup_data.geo import Country
from massgov.pfml.db.lookup_data.language import Language
from massgov.pfml.db.models.applications import Application
from massgov.pfml.db.models.change_request import ChangeRequest, LkChangeRequestType
from massgov.pfml.db.models.employees import Employer, LeaveRequest
from massgov.pfml.db.models.factories import (
    AbsencePeriodFactory,
    AddressFactory,
    ApplicationFactory,
    CaringLeaveMetadataFactory,
    ClaimFactory,
    ContinuousLeavePeriodFactory,
    EmployerFactory,
    Generators,
    LeaveRequestFactory,
    PaymentPreferenceFactory,
    PreviousLeaveFactory,
    ReducedScheduleLeavePeriodFactory,
    TaxIdentifierFactory,
    WorkPatternFixedFactory,
)
from massgov.pfml.db.models.fineos_web_id import FINEOSWebIdExt
from massgov.pfml.db.queries.absence_periods import (
    convert_fineos_absence_period_to_claim_response_absence_period,
)
from massgov.pfml.features import FeaturesConfig
from massgov.pfml.fineos import FINEOSClient, exception
from massgov.pfml.fineos.exception import (
    FINEOSClientBadResponse,
    FINEOSClientError,
    FINEOSEntityNotFound,
    FINEOSForbidden,
)
from massgov.pfml.fineos.models import CreateOrUpdateEmployer, CreateOrUpdateServiceAgreement
from massgov.pfml.fineos.models.customer_api import AbsencePeriod as FineosAbsencePeriod
from massgov.pfml.fineos.models.customer_api import (
    AbsencePeriodDecision,
    ActualAbsencePeriodResources,
    Base64EncodedFileData,
    BenefitSummary,
    CountryRequest,
    CreateAddressCommand,
)
from massgov.pfml.fineos.models.customer_api import Document as FineosDocument
from massgov.pfml.fineos.models.customer_api import EmailAddress, ReadDisabilityBenefitResult
from tests.helpers.get_additional_data_value import get_additional_data_value
from tests.helpers.logging import get_mock_logger

mock_logger = get_mock_logger()


@pytest.fixture
def employer():
    return EmployerFactory.create()


@pytest.fixture
def claim(employer):
    return ClaimFactory.create(employer_id=employer.employer_id, fineos_absence_id="NTN-111-111")


@pytest.fixture
def application(user, claim):
    return ApplicationFactory.create(user=user, claim_id=claim.claim_id)


def test_register_employee_pass(test_db_session):
    fineos_client = massgov.pfml.fineos.MockFINEOSClient()

    employer_fein = "*********"
    employee_ssn = "*********"
    employee_external_id = fineos_actions.register_employee(
        fineos_client, employee_ssn, employer_fein, test_db_session
    )

    assert employee_external_id is not None


def test_using_existing_id(test_db_session):
    fineos_client = massgov.pfml.fineos.MockFINEOSClient()

    employer_fein = "*********"
    employee_ssn = "*********"
    employee_external_id_1 = fineos_actions.register_employee(
        fineos_client, employee_ssn, employer_fein, test_db_session
    )

    employee_external_id_2 = fineos_actions.register_employee(
        fineos_client, employee_ssn, employer_fein, test_db_session
    )

    assert employee_external_id_1 == employee_external_id_2


def test_create_different_id_for_other_employer(test_db_session):
    fineos_client = massgov.pfml.fineos.MockFINEOSClient()

    employer_fein = "*********"
    employee_ssn = "*********"
    employee_external_id_1 = fineos_actions.register_employee(
        fineos_client, employee_ssn, employer_fein, test_db_session
    )

    employer_fein = "179892897"
    employee_external_id_2 = fineos_actions.register_employee(
        fineos_client, employee_ssn, employer_fein, test_db_session
    )

    assert employee_external_id_1 != employee_external_id_2


def test_register_employee_bad_fein(test_db_session):
    fineos_client = massgov.pfml.fineos.MockFINEOSClient()

    employer_fein = "999999999"
    employee_ssn = "*********"

    try:
        fineos_actions.register_employee(
            fineos_client, employee_ssn, employer_fein, test_db_session
        )
    except FINEOSEntityNotFound:
        assert True


def test_register_employee_bad_ssn(test_db_session):
    fineos_client = massgov.pfml.fineos.MockFINEOSClient()

    employer_fein = "*********"
    employee_ssn = "999999999"

    try:
        fineos_actions.register_employee(
            fineos_client, employee_ssn, employer_fein, test_db_session
        )
    except FINEOSClientBadResponse:
        assert True


def test_determine_absence_period_status_cont(user, test_db_session):
    application = ApplicationFactory.create(user=user)
    continuous_leave_period = ContinuousLeavePeriodFactory.create()
    application.continuous_leave_periods.append(continuous_leave_period)

    status = fineos_actions.determine_absence_period_status(application)
    assert status == "known"


def test_determine_absence_period_status_reduced(user, test_db_session):
    today = date.today()
    tomorrow = today + timedelta(days=1)

    application = ApplicationFactory.create(
        user=user,
        child_birth_date=tomorrow,
        has_future_child_date=True,
        leave_reason_id=LeaveReason.CHILD_BONDING.leave_reason_id,
    )
    reduced_schedule_leave_period = ReducedScheduleLeavePeriodFactory.create()
    application.reduced_schedule_leave_periods.append(reduced_schedule_leave_period)

    status = fineos_actions.determine_absence_period_status(application)
    assert status == "estimated"


@pytest.fixture
def file():
    file = MagicMock()
    file.name = "test.png"
    file.content = b"abcdef"
    file.content_type = "image/png"

    return file


@pytest.fixture
def document():
    document = MagicMock()
    document.type = "Passport"
    document.description = "Test document upload description"

    return document


class TestUploadDocument:
    def test_success(self, user, document, file, test_db_session):
        application = ApplicationFactory.create(
            user=user, work_pattern=WorkPatternFixedFactory.create()
        )
        application.employer_fein = "*********"
        application.tax_identifier.tax_identifier = "*********"

        with ThreadPoolExecutor() as executor:
            application_service.submit(test_db_session, [application], user, executor)

        updated_application = test_db_session.get(Application, application.application_id)

        assert updated_application.claim.fineos_absence_id is not None

        fineos_document = fineos_actions.upload_document(
            updated_application,
            document.type,
            file.content,
            file.name,
            file.content_type,
            document.description,
            test_db_session,
        ).dict()

        assert fineos_document["caseId"] == application.claim.fineos_absence_id
        # See massgov/pfml/fineos/mock_client.py
        assert fineos_document["documentId"] == 3011
        assert fineos_document["name"] == document.type
        assert fineos_document["fileExtension"] == ".png"
        assert fineos_document["originalFilename"] == file.name
        assert fineos_document["description"] == document.description


class TestUploadDocumentWithClaim:
    # Run `initialize_factories_session` for all tests,
    # so that it doesn't need to be manually included
    @pytest.fixture(autouse=True)
    def setup_factories(self, initialize_factories_session):
        return

    @pytest.fixture
    def mock_fineos(self):
        mock_fineos = MagicMock()
        mock_fineos.find_employer.return_value = "1234"
        return mock_fineos

    def test_success(self, claim, document, file, test_db_session):
        fineos_document = fineos_actions.upload_document_with_claim(
            claim,
            document.type,
            file.content,
            file.name,
            file.content_type,
            document.description,
            test_db_session,
            with_multipart=False,
        ).dict()

        assert fineos_document["caseId"] == claim.fineos_absence_id
        # See massgov/pfml/fineos/mock_client.py
        assert fineos_document["documentId"] == 3011
        assert fineos_document["name"] == document.type
        assert fineos_document["fileExtension"] == ".png"
        assert fineos_document["originalFilename"] == file.name
        assert fineos_document["description"] == document.description

    @mock.patch("massgov.pfml.fineos.create_client")
    def test_uploads_to_fineos(
        self, mock_create_fineos, mock_fineos, claim, document, file, test_db_session
    ):
        mock_create_fineos.return_value = mock_fineos

        fineos_actions.upload_document_with_claim(
            claim,
            document.type,
            file.content,
            file.name,
            file.content_type,
            document.description,
            test_db_session,
            with_multipart=False,
        )

        mock_fineos.upload_document.assert_called_once_with(
            mock.ANY,
            claim.fineos_absence_id,
            document.type,
            file.content,
            file.name,
            file.content_type,
            document.description,
        )

    @mock.patch("massgov.pfml.fineos.create_client")
    def test_uploads_to_fineos_with_multipart(
        self, mock_create_fineos, mock_fineos, claim, document, file, test_db_session
    ):
        mock_create_fineos.return_value = mock_fineos

        fineos_actions.upload_document_with_claim(
            claim,
            document.type,
            file.content,
            file.name,
            file.content_type,
            document.description,
            test_db_session,
            with_multipart=True,
        )

        mock_fineos.upload_document_multipart.assert_called_once_with(
            mock.ANY,
            claim.fineos_absence_id,
            document.type,
            file.content,
            file.name,
            file.content_type,
            document.description,
        )


class TestUploadDocumentToDMS:
    # Run `initialize_factories_session` for all tests,
    # so that it doesn't need to be manually included
    @pytest.fixture(autouse=True)
    def setup_factories(self, initialize_factories_session):
        return

    @pytest.fixture
    def mock_fineos(self):
        mock_fineos = MagicMock()
        return mock_fineos

    @freeze_time("2021-12-09 12:00:00", tz_offset=5)
    @mock.patch("massgov.pfml.fineos.create_client")
    def test_uploads_to_fineos(
        self,
        mock_create_fineos,
        mock_fineos,
        claim,
        document,
        file,
        test_db_session,
    ):
        response = fineos_actions.upload_document_to_dms(
            file.name, file.content, document.type, max_retries=2, fineos=mock_fineos
        )

        assert response.ok

        doc_properties = {
            "title": file.name,
            "description": file.name,
            "receivedDate": str(datetime.now().isoformat()),
            "managedReqId": 0,
            "status": "Completed",
            "fineosDocType": document.type,
            "dmsDocType": document.type,
        }

        data = {
            "dmsDocId": file.name,
            "fileExtension": "." + file.name.split(".")[1],
            "docProperties": doc_properties,
        }

        mock_create_fineos.return_value = mock_fineos
        mock_fineos.upload_document_to_dms.assert_called_once_with(file.name, file.content, data)


def test_submit_direct_deposit_payment_preference(user, test_db_session):
    payment_pref = PaymentPreferenceFactory.create(
        payment_method_id=PaymentMethod.ACH.payment_method_id,
        account_number="*********",
        routing_number="*********",
        bank_account_type_id=BankAccountType.CHECKING.bank_account_type_id,
    )
    application = ApplicationFactory.create(user=user, payment_preference=payment_pref)
    fineos_response = fineos_actions.submit_customer_payment_preference(
        application, test_db_session
    )
    test_db_session.refresh(application)
    assert fineos_response is not None
    assert fineos_response.paymentMethod.name == PaymentMethod.ACH.payment_method_description
    assert fineos_response.accountDetail.accountNo == payment_pref.account_number
    assert fineos_response.accountDetail.routingNumber == payment_pref.routing_number
    assert (
        fineos_response.accountDetail.accountType.name
        == BankAccountType.CHECKING.bank_account_type_description
    )
    assert fineos_response.chequeDetail is None


def test_submit_direct_deposit_payment_pref_with_mailing_addr(user, test_db_session):
    payment_pref = PaymentPreferenceFactory.create(
        payment_method_id=PaymentMethod.ACH.payment_method_id,
        account_number="*********",
        routing_number="*********",
        bank_account_type_id=BankAccountType.CHECKING.bank_account_type_id,
    )
    mailing_address = AddressFactory.create(
        address_type_id=AddressType.MAILING.address_type_id,
        address_line_one="123 Main St",
        city="Cambridge",
        geo_state_id=1,  # Massachusetts
        zip_code="02139",
    )
    residential_address = AddressFactory.create(
        address_type_id=AddressType.RESIDENTIAL.address_type_id,
        address_line_one="321 South St",
        city="Somerville",
        geo_state_id=1,
        zip_code="02138",
    )
    application = ApplicationFactory.create(
        user=user,
        payment_preference=payment_pref,
        has_mailing_address=True,
        mailing_address=mailing_address,
        residential_address=residential_address,
    )
    fineos_mock.start_capture()
    fineos_response = fineos_actions.submit_customer_payment_preference(
        application, test_db_session
    )
    assert fineos_response is not None
    assert fineos_response.paymentMethod.name == PaymentMethod.ACH.payment_method_description

    capture = fineos_mock.get_capture()
    fineos_payment_calls = [
        method_call
        for method_call in capture
        if method_call[0] == "create_customer_payment_preference"
    ]
    assert len(fineos_payment_calls) == 1
    sent_new_payment_pref = fineos_payment_calls[0][2]["payment_preference"]
    assert sent_new_payment_pref.address == CreateAddressCommand(
        addressLine1=mailing_address.address_line_one,
        addressLine4=mailing_address.city,
        addressLine6="MA",
        country=CountryRequest(fullId=None, name="USA"),
        postCode="02139",
    )
    assert sent_new_payment_pref.overridePostalAddress is True


def test_submit_direct_deposit_payment_pref_without_mailing_addr(user, test_db_session):
    payment_pref = PaymentPreferenceFactory.create(
        payment_method_id=PaymentMethod.ACH.payment_method_id,
        account_number="*********",
        routing_number="*********",
        bank_account_type_id=BankAccountType.CHECKING.bank_account_type_id,
    )
    residential_address = AddressFactory.create(
        address_type_id=AddressType.RESIDENTIAL.address_type_id,
        address_line_one="321 South St",
        city="Somerville",
        geo_state_id=1,  # Massachusetts
        zip_code="02139",
    )
    application = ApplicationFactory.create(
        user=user, payment_preference=payment_pref, residential_address=residential_address
    )
    fineos_mock.start_capture()
    fineos_response = fineos_actions.submit_customer_payment_preference(
        application, test_db_session
    )
    test_db_session.refresh(application)
    assert fineos_response is not None
    assert fineos_response.paymentMethod.name == PaymentMethod.ACH.payment_method_description

    capture = fineos_mock.get_capture()
    fineos_payment_calls = [
        method_call
        for method_call in capture
        if method_call[0] == "create_customer_payment_preference"
    ]
    assert len(fineos_payment_calls) == 1
    sent_new_payment_pref = fineos_payment_calls[0][2]["payment_preference"]
    assert sent_new_payment_pref.address is None
    assert sent_new_payment_pref.overridePostalAddress is None


def test_submit_check_payment_pref_with_mailing_addr(user, test_db_session):
    payment_pref = PaymentPreferenceFactory.create(
        payment_method_id=PaymentMethod.CHECK.payment_method_id
    )
    mailing_address = AddressFactory.create(
        address_type_id=AddressType.MAILING.address_type_id,
        address_line_one="123 Main St",
        city="Cambridge",
        geo_state_id=1,  # Massachusetts
        zip_code="02138",
    )
    residential_address = AddressFactory.create(
        address_type_id=AddressType.RESIDENTIAL.address_type_id,
        address_line_one="321 South St",
        city="Somerville",
        geo_state_id=1,
        zip_code="02138",
    )
    application = ApplicationFactory.create(
        user=user,
        payment_preference=payment_pref,
        has_mailing_address=True,
        mailing_address=mailing_address,
        residential_address=residential_address,
    )
    fineos_mock.start_capture()
    fineos_response = fineos_actions.submit_customer_payment_preference(
        application, test_db_session
    )
    test_db_session.refresh(application)
    assert fineos_response is not None
    assert fineos_response.paymentMethod.name == PaymentMethod.CHECK.payment_method_description

    capture = fineos_mock.get_capture()
    fineos_payment_calls = [
        method_call
        for method_call in capture
        if method_call[0] == "create_customer_payment_preference"
    ]
    assert len(fineos_payment_calls) == 1
    sent_new_payment_pref = fineos_payment_calls[0][2]["payment_preference"]
    assert sent_new_payment_pref.address == CreateAddressCommand(
        addressLine1=mailing_address.address_line_one,
        addressLine4=mailing_address.city,
        addressLine6="MA",
        country=CountryRequest(fullId=None, name="USA"),
        postCode="02138",
    )
    assert sent_new_payment_pref.overridePostalAddress is True


def test_build_customer_payment_preference(user, test_db_session):
    payment_pref = PaymentPreferenceFactory.create(
        payment_method_id=PaymentMethod.CHECK.payment_method_id
    )
    residential_address = AddressFactory.create(
        address_type_id=AddressType.RESIDENTIAL.address_type_id,
        address_line_one="321 South St",
        city="Somerville",
        geo_state_id=1,
        zip_code="02138",
    )
    application = ApplicationFactory.create(
        user=user, payment_preference=payment_pref, residential_address=residential_address
    )
    payment_address = fineos_actions.build_fineos_customer_address(residential_address)

    assert payment_address.addressLine1 == residential_address.address_line_one

    fineos_payment_preference = fineos_actions.build_customer_payment_preference(
        application, payment_address
    )

    assert fineos_payment_preference.address == payment_address


def test_submit_check_payment_pref_without_mailing_addr(user, test_db_session):
    payment_pref = PaymentPreferenceFactory.create(
        payment_method_id=PaymentMethod.CHECK.payment_method_id
    )
    residential_address = AddressFactory.create(
        address_type_id=AddressType.RESIDENTIAL.address_type_id,
        address_line_one="321 South St",
        city="Somerville",
        geo_state_id=1,
        zip_code="02138",
    )
    application = ApplicationFactory.create(
        user=user, payment_preference=payment_pref, residential_address=residential_address
    )
    fineos_mock.start_capture()
    fineos_response = fineos_actions.submit_customer_payment_preference(
        application, test_db_session
    )
    test_db_session.refresh(application)
    assert fineos_response is not None
    assert fineos_response.paymentMethod.name == PaymentMethod.CHECK.payment_method_description

    capture = fineos_mock.get_capture()
    fineos_payment_calls = [
        method_call
        for method_call in capture
        if method_call[0] == "create_customer_payment_preference"
    ]
    assert len(fineos_payment_calls) == 1
    sent_new_payment_pref = fineos_payment_calls[0][2]["payment_preference"]
    assert sent_new_payment_pref.address is None
    assert sent_new_payment_pref.overridePostalAddress is None


def test_build_week_based_work_pattern(user, test_db_session):
    application = ApplicationFactory.create(user=user, work_pattern=WorkPatternFixedFactory())

    work_pattern = fineos_actions.build_week_based_work_pattern(application)

    assert work_pattern.workPatternType == "Fixed"
    assert work_pattern.workPatternDays == [
        massgov.pfml.fineos.models.customer_api.WorkPatternDay(
            dayOfWeek=day.day_of_week_description,
            weekNumber=1,
            hours=8,
            minutes=15,
        )
        # Order of days different between expected and actual.
        # Forcing to start on Sunday on expected to match actual.
        for day in (
            DayOfWeek.SUNDAY,
            DayOfWeek.MONDAY,
            DayOfWeek.TUESDAY,
            DayOfWeek.WEDNESDAY,
            DayOfWeek.THURSDAY,
            DayOfWeek.FRIDAY,
            DayOfWeek.SATURDAY,
        )
    ]


def test_create_employer_simple(test_db_session):
    fineos_client = massgov.pfml.fineos.MockFINEOSClient()

    employer = Employer()
    employer.employer_fein = "888447598"
    employer.employer_name = "Test Organization Name44"
    employer.employer_dba = "Test Organization DBA"
    test_db_session.add(employer)
    test_db_session.commit()

    assert employer.fineos_employer_id is None

    fineos_employer_id = fineos_actions.create_or_update_employer(fineos_client, employer)

    assert employer.fineos_employer_id == fineos_employer_id


def test_update_employer_simple(test_db_session):
    fineos_client = massgov.pfml.fineos.MockFINEOSClient()

    employer = Employer()
    employer.employer_fein = "888447576"
    employer.employer_name = "Test Organization Name"
    employer.employer_dba = "Test Organization DBA"
    employer.fineos_employer_id = massgov.pfml.fineos.mock.field.fake_customer_no(
        employer.employer_fein
    )
    test_db_session.add(employer)
    test_db_session.commit()

    fineos_employer_id = fineos_actions.create_or_update_employer(fineos_client, employer)

    assert employer.fineos_employer_id == fineos_employer_id


def test_create_employer_with_truncated_dba(test_db_session):
    fineos_employer_dba_max_length = 100
    fineos_client = massgov.pfml.fineos.MockFINEOSClient()

    employer = Employer()
    employer.employer_fein = "888447576"
    employer.employer_name = "Test Organization Name"
    employer.employer_dba = "Test Organization DBA; " * 5
    test_db_session.add(employer)
    test_db_session.commit()

    assert len(employer.employer_dba) > fineos_employer_dba_max_length
    assert employer.fineos_employer_id is None

    fineos_mock.start_capture()

    fineos_employer_id = fineos_actions.create_or_update_employer(fineos_client, employer)

    assert employer.fineos_employer_id == fineos_employer_id

    capture = fineos_mock.get_capture()
    fineos_payment_calls = [
        method_call for method_call in capture if method_call[0] == "create_or_update_employer"
    ]

    assert len(fineos_payment_calls) == 1
    assert (
        len(fineos_payment_calls[0][2]["employer_create_or_update"].employer_dba)
        == fineos_employer_dba_max_length
    )


def test_employer_creation_exception(test_db_session):
    fineos_client = massgov.pfml.fineos.MockFINEOSClient()

    employer = Employer()
    employer.employer_fein = "999999999"
    employer.employer_name = "Test Organization Dupe"
    employer.employer_dba = "Test Organization Dupe DBA"
    test_db_session.add(employer)
    test_db_session.commit()

    with pytest.raises(FINEOSClientError):
        fineos_actions.create_or_update_employer(fineos_client, employer)


def test_creating_request_payload():
    create_or_update_request = CreateOrUpdateEmployer(
        fineos_customer_nbr="pfml_test_payload",
        employer_fein="*********",
        employer_legal_name="Test Organization Name",
        employer_dba="Test Organization DBA",
    )
    payload = FINEOSClient._create_or_update_employer_payload(create_or_update_request)

    assert payload is not None
    assert payload.__contains__("<Name>Test Organization Name</Name>")
    assert payload.__contains__("<CustomerNo>pfml_test_payload</CustomerNo>")
    assert payload.__contains__("<CorporateTaxNumber>*********</CorporateTaxNumber>")
    assert payload.__contains__("<LegalBusinessName>Test Organization Name</LegalBusinessName>")
    assert payload.__contains__("<DoingBusinessAs>Test Organization DBA</DoingBusinessAs>")


def test_creating_request_payload_with_other_names():
    create_or_update_request = CreateOrUpdateEmployer(
        fineos_customer_nbr="pfml_test_payload",
        employer_fein="*********",
        employer_legal_name="Test Organization Name",
        employer_dba="Test Organization DBA",
    )
    payload = FINEOSClient._create_or_update_employer_payload(create_or_update_request)

    assert payload is not None
    assert payload.__contains__("<Name>Test Organization Name</Name>")
    assert payload.__contains__("<CustomerNo>pfml_test_payload</CustomerNo>")
    assert payload.__contains__("<CorporateTaxNumber>*********</CorporateTaxNumber>")
    assert payload.__contains__("<LegalBusinessName>Test Organization Name</LegalBusinessName>")
    assert payload.__contains__("<DoingBusinessAs>Test Organization DBA</DoingBusinessAs>")

    assert payload.__contains__("<ShortName>Test Org</ShortName>")
    assert payload.__contains__("<UpperName>TEST ORGANIZATION NAME</UpperName>")
    assert payload.__contains__("<UpperShortName>TEST ORG</UpperShortName>")


def test_build_bonding_date_reflexive_question_birth(user, app):
    with app.app.app_context():
        application = ApplicationFactory.create(user=user)
        application.child_birth_date = date(2021, 2, 9)
        application.leave_reason_qualifier_id = (
            LeaveReasonQualifier.NEWBORN.leave_reason_qualifier_id
        )
        reflexive_question = fineos_actions.build_bonding_date_reflexive_question(application)

        assert reflexive_question.reflexiveQuestionLevel == "reason"
        assert (
            reflexive_question.reflexiveQuestionDetails[0].fieldName
            == "FamilyMemberDetailsQuestionGroup.familyMemberDetailsQuestions.dateOfBirth"
        )
        assert reflexive_question.reflexiveQuestionDetails[0].dateValue == date(2021, 2, 9)


def test_build_bonding_date_reflexive_question_birth_finV24(user, app, fineos_v24_feature_config):

    with app.app.app_context():
        application = ApplicationFactory.create(user=user)
        application.child_birth_date = date(2021, 2, 9)
        application.leave_reason_qualifier_id = (
            LeaveReasonQualifier.NEWBORN.leave_reason_qualifier_id
        )
        reflexive_question = fineos_actions.build_bonding_date_reflexive_question(application)

        assert reflexive_question.reflexiveQuestionLevel == "reason"
        assert (
            reflexive_question.reflexiveQuestionDetails[0].fieldName
            == "NumberOfChildrenAndFamilyMemberDetailsQuestionGroup.familyMemberDetailsQuestions.dateOfBirth"
        )
        assert reflexive_question.reflexiveQuestionDetails[0].dateValue == date(2021, 2, 9)


def test_build_bonding_date_reflexive_question_adoption(user, app):
    with app.app.app_context():
        application = ApplicationFactory.create(user=user)
        application.child_placement_date = date(2021, 2, 9)
        application.leave_reason_qualifier_id = (
            LeaveReasonQualifier.ADOPTION.leave_reason_qualifier_id
        )
        reflexive_question = fineos_actions.build_bonding_date_reflexive_question(application)

        assert (
            reflexive_question.reflexiveQuestionDetails[0].fieldName
            == "PlacementQuestionGroup.placementQuestions.adoptionDate"
        )
        assert reflexive_question.reflexiveQuestionDetails[0].dateValue == date(2021, 2, 9)


def test_build_bonding_date_reflexive_question_adoption_finv24(
    user, app, fineos_v24_feature_config
):
    with app.app.app_context():
        application = ApplicationFactory.create(user=user)
        application.child_placement_date = date(2021, 2, 9)
        application.leave_reason_qualifier_id = (
            LeaveReasonQualifier.ADOPTION.leave_reason_qualifier_id
        )
        reflexive_question = fineos_actions.build_bonding_date_reflexive_question(application)

        assert (
            reflexive_question.reflexiveQuestionDetails[0].fieldName
            == "NumberOfChildrenAndPlacementQuestionGroup.placementQuestions.adoptionDate"
        )
        assert reflexive_question.reflexiveQuestionDetails[0].dateValue == date(2021, 2, 9)


def test_build_bonding_date_reflexive_question_foster(user, app):
    with app.app.app_context():
        application = ApplicationFactory.create(user=user)
        application.child_placement_date = date(2021, 2, 9)
        application.leave_reason_qualifier_id = (
            LeaveReasonQualifier.FOSTER_CARE.leave_reason_qualifier_id
        )
        reflexive_question = fineos_actions.build_bonding_date_reflexive_question(application)

        assert (
            reflexive_question.reflexiveQuestionDetails[0].fieldName
            == "PlacementQuestionGroup.placementQuestions.adoptionDate"
        )
        assert reflexive_question.reflexiveQuestionDetails[0].dateValue == date(2021, 2, 9)


def test_build_bonding_date_reflexive_question_foster_finv24(user, app, fineos_v24_feature_config):
    with app.app.app_context():
        application = ApplicationFactory.create(user=user)
        application.child_placement_date = date(2021, 2, 9)
        application.leave_reason_qualifier_id = (
            LeaveReasonQualifier.FOSTER_CARE.leave_reason_qualifier_id
        )
        reflexive_question = fineos_actions.build_bonding_date_reflexive_question(application)

        assert (
            reflexive_question.reflexiveQuestionDetails[0].fieldName
            == "NumberOfChildrenAndPlacementQuestionGroup.placementQuestions.adoptionDate"
        )
        assert reflexive_question.reflexiveQuestionDetails[0].dateValue == date(2021, 2, 9)


def test_build_caring_leave_reflexive_question_age_capacity(user):
    # Child relationship uses the "AgeCapacityFamilyMemberQuestionGroup.familyMemberDetailsQuestions" field name

    caring_leave_metadata = CaringLeaveMetadataFactory.create(
        relationship_to_caregiver_id=RelationshipToCaregiver.CHILD.relationship_to_caregiver_id
    )
    application = ApplicationFactory.create(user=user, caring_leave_metadata=caring_leave_metadata)
    application.leave_reason_id = LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id

    reflexive_question = fineos_actions.build_caring_leave_reflexive_question(application)

    assert (
        reflexive_question.reflexiveQuestionDetails[0].fieldName
        == "AgeCapacityFamilyMemberQuestionGroup.familyMemberDetailsQuestions.firstName"
    )
    assert (
        reflexive_question.reflexiveQuestionDetails[1].fieldName
        == "AgeCapacityFamilyMemberQuestionGroup.familyMemberDetailsQuestions.middleInitial"
    )
    assert (
        reflexive_question.reflexiveQuestionDetails[2].fieldName
        == "AgeCapacityFamilyMemberQuestionGroup.familyMemberDetailsQuestions.lastName"
    )
    assert (
        reflexive_question.reflexiveQuestionDetails[3].fieldName
        == "AgeCapacityFamilyMemberQuestionGroup.familyMemberDetailsQuestions.dateOfBirth"
    )
    assert (
        reflexive_question.reflexiveQuestionDetails[0].stringValue
        == caring_leave_metadata.family_member_first_name
    )
    assert (
        reflexive_question.reflexiveQuestionDetails[1].stringValue
        == caring_leave_metadata.family_member_middle_name
    )
    assert (
        reflexive_question.reflexiveQuestionDetails[2].stringValue
        == caring_leave_metadata.family_member_last_name
    )
    assert (
        reflexive_question.reflexiveQuestionDetails[3].dateValue
        == caring_leave_metadata.family_member_date_of_birth
    )


def test_build_caring_leave_reflexive_question_family_member_details(user):
    # Grandchild, Grandparent, Inlaw, Parent, and Spouse relationships use the "FamilyMemberDetailsQuestionGroup.familyMemberDetailsQuestions" field name
    relationship_ids = [
        RelationshipToCaregiver.GRANDCHILD.relationship_to_caregiver_id,
        RelationshipToCaregiver.GRANDPARENT.relationship_to_caregiver_id,
        RelationshipToCaregiver.INLAW.relationship_to_caregiver_id,
        RelationshipToCaregiver.PARENT.relationship_to_caregiver_id,
        RelationshipToCaregiver.SPOUSE.relationship_to_caregiver_id,
    ]

    for relationship_id in relationship_ids:
        caring_leave_metadata = CaringLeaveMetadataFactory.create(
            relationship_to_caregiver_id=relationship_id
        )
        application = ApplicationFactory.create(
            user=user, caring_leave_metadata=caring_leave_metadata
        )
        application.leave_reason_id = LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id

        reflexive_question = fineos_actions.build_caring_leave_reflexive_question(application)

        assert (
            reflexive_question.reflexiveQuestionDetails[0].fieldName
            == "FamilyMemberDetailsQuestionGroup.familyMemberDetailsQuestions.firstName"
        )
        assert (
            reflexive_question.reflexiveQuestionDetails[1].fieldName
            == "FamilyMemberDetailsQuestionGroup.familyMemberDetailsQuestions.middleInitial"
        )
        assert (
            reflexive_question.reflexiveQuestionDetails[2].fieldName
            == "FamilyMemberDetailsQuestionGroup.familyMemberDetailsQuestions.lastName"
        )
        assert (
            reflexive_question.reflexiveQuestionDetails[3].fieldName
            == "FamilyMemberDetailsQuestionGroup.familyMemberDetailsQuestions.dateOfBirth"
        )
        assert (
            reflexive_question.reflexiveQuestionDetails[0].stringValue
            == caring_leave_metadata.family_member_first_name
        )
        assert (
            reflexive_question.reflexiveQuestionDetails[1].stringValue
            == caring_leave_metadata.family_member_middle_name
        )
        assert (
            reflexive_question.reflexiveQuestionDetails[2].stringValue
            == caring_leave_metadata.family_member_last_name
        )
        assert (
            reflexive_question.reflexiveQuestionDetails[3].dateValue
            == caring_leave_metadata.family_member_date_of_birth
        )


def test_build_reflexive_question_v22(user):
    caring_leave_metadata = CaringLeaveMetadataFactory.create(
        relationship_to_caregiver_id=RelationshipToCaregiver.CHILD.relationship_to_caregiver_id
    )
    application = ApplicationFactory.create(user=user, caring_leave_metadata=caring_leave_metadata)
    application.leave_reason_id = LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id

    reflexive_question = fineos_actions.build_caring_leave_reflexive_question(application)

    assert (
        reflexive_question.reflexiveQuestionDetails[1].fieldName
        == "AgeCapacityFamilyMemberQuestionGroup.familyMemberDetailsQuestions.middleInitial"
    )


def test_build_caring_leave_reflexive_question_family_member_sibling(user):
    # Sibling relationship uses the "FamilyMemberSiblingDetailsQuestionGroup.familyMemberDetailsQuestions" field name

    caring_leave_metadata = CaringLeaveMetadataFactory.create(
        relationship_to_caregiver_id=RelationshipToCaregiver.SIBLING.relationship_to_caregiver_id
    )
    application = ApplicationFactory.create(user=user, caring_leave_metadata=caring_leave_metadata)
    application.leave_reason_id = LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id

    reflexive_question = fineos_actions.build_caring_leave_reflexive_question(application)

    assert (
        reflexive_question.reflexiveQuestionDetails[0].fieldName
        == "FamilyMemberSiblingDetailsQuestionGroup.familyMemberDetailsQuestions.firstName"
    )
    assert (
        reflexive_question.reflexiveQuestionDetails[1].fieldName
        == "FamilyMemberSiblingDetailsQuestionGroup.familyMemberDetailsQuestions.middleInitial"
    )
    assert (
        reflexive_question.reflexiveQuestionDetails[2].fieldName
        == "FamilyMemberSiblingDetailsQuestionGroup.familyMemberDetailsQuestions.lastName"
    )
    assert (
        reflexive_question.reflexiveQuestionDetails[3].fieldName
        == "FamilyMemberSiblingDetailsQuestionGroup.familyMemberDetailsQuestions.dateOfBirth"
    )
    assert (
        reflexive_question.reflexiveQuestionDetails[0].stringValue
        == caring_leave_metadata.family_member_first_name
    )
    assert (
        reflexive_question.reflexiveQuestionDetails[1].stringValue
        == caring_leave_metadata.family_member_middle_name
    )
    assert (
        reflexive_question.reflexiveQuestionDetails[2].stringValue
        == caring_leave_metadata.family_member_last_name
    )
    assert (
        reflexive_question.reflexiveQuestionDetails[3].dateValue
        == caring_leave_metadata.family_member_date_of_birth
    )


def test_build_customer_model_no_mass_id(user):
    application = ApplicationFactory.create(user=user)
    customer_model = fineos_actions.build_customer_model(application, user)

    assert application.mass_id is None

    assert customer_model.classExtensionInformation[0].name != "MassachusettsID"


def test_build_customer_address(user):
    residential_address = AddressFactory.create()
    application = ApplicationFactory.create(user=user, residential_address=residential_address)
    customer_address = fineos_actions.build_customer_address(application.residential_address)

    assert customer_address.address.addressLine1 == residential_address.address_line_one
    assert customer_address.address.addressLine2 == residential_address.address_line_two
    assert customer_address.address.addressLine4 == residential_address.city
    assert (
        customer_address.address.addressLine6 == residential_address.geo_state.geo_state_description
    )
    assert customer_address.address.postCode == residential_address.zip_code
    assert customer_address.address.country == Country.USA.country_description


def test_build_customer_language_preference(user, monkeypatch):
    application = ApplicationFactory.create(user=user, language_id=Language.PORTUGUESE.language_id)
    customer_model = fineos_actions.build_customer_model(application, user)

    assert customer_model.languages[0].languageEnum.domainName == "Language"
    assert (
        customer_model.languages[0].languageEnum.instanceValue
        == Language.PORTUGUESE.language_description
    )
    assert customer_model.languages[0].preferredLanguage
    assert customer_model.languages[0].written


def test_transform_chinese_customer_language_preference(user, monkeypatch):
    application = ApplicationFactory.create(
        user=user, language_id=Language.CHINESE_SIMPLIFIED.language_id
    )
    customer_model = fineos_actions.build_customer_model(application, user)

    assert customer_model.languages[0].languageEnum.domainName == "Language"
    assert (
        customer_model.languages[0].languageEnum.instanceValue
        != Language.CHINESE_SIMPLIFIED.language_description
    )
    assert customer_model.languages[0].languageEnum.instanceValue == "Chinese"
    assert customer_model.languages[0].preferredLanguage
    assert customer_model.languages[0].written


def test_build_customer_default_language_preference(user, monkeypatch):
    application = ApplicationFactory.create(user=user)
    customer_model = fineos_actions.build_customer_model(application, user)

    assert customer_model.languages[0].languageEnum.domainName == "Language"
    assert (
        customer_model.languages[0].languageEnum.instanceValue
        == Language.ENGLISH.language_description
    )
    assert customer_model.languages[0].preferredLanguage
    assert customer_model.languages[0].written


def test_create_service_agreement_for_employer_no_version(test_db_session):
    fineos_client = massgov.pfml.fineos.MockFINEOSClient()

    employer = Employer()
    employer.employer_fein = "888447598"
    employer.employer_name = "Test Organization Name"
    employer.employer_dba = "Test Organization DBA"
    test_db_session.add(employer)

    fineos_actions.create_or_update_employer(fineos_client, employer)


def test_create_service_agreement_payload():
    service_agreement_inputs = CreateOrUpdateServiceAgreement(
        leave_plans="MA PFML - Family, MA PFML - Military Care", unlink_leave_plans=True
    )

    payload = FINEOSClient._create_service_agreement_payload(123, service_agreement_inputs)
    assert payload is not None
    root = ElementTree.fromstring(payload)

    config_name = root.find("./config-name")
    assert config_name is not None
    assert config_name.text == "ServiceAgreementService"

    additional_data_set = root.find("./update-data/additional-data-set")
    assert additional_data_set is not None

    absence_management = get_additional_data_value("AbsenceManagement", additional_data_set)
    assert absence_management is None

    customer_number = get_additional_data_value("CustomerNumber", additional_data_set)
    assert customer_number is not None
    assert customer_number.text == "123"

    leave_plans = get_additional_data_value("LeavePlans", additional_data_set)
    assert leave_plans is not None
    leave_plan_set = set(leave_plans.text.split(", "))
    assert leave_plan_set == {"MA PFML - Family", "MA PFML - Military Care"}

    unlink_all_existing_leave_plans = get_additional_data_value(
        "UnlinkAllExistingLeavePlans", additional_data_set
    )
    assert unlink_all_existing_leave_plans is not None
    assert unlink_all_existing_leave_plans.text == "True"

    version = get_additional_data_value("Version", additional_data_set)
    assert version is not None
    assert version.text == "None"


def test_create_service_agreement_no_absence_management_payload():
    service_agreement_inputs = CreateOrUpdateServiceAgreement(
        absence_management_flag=False, unlink_leave_plans=True
    )

    payload = FINEOSClient._create_service_agreement_payload(123, service_agreement_inputs)
    assert payload is not None
    root = ElementTree.fromstring(payload)

    config_name = root.find("./config-name")
    assert config_name is not None
    assert config_name.text == "ServiceAgreementService"

    additional_data_set = root.find("./update-data/additional-data-set")
    assert additional_data_set is not None

    absence_management = get_additional_data_value("AbsenceManagement", additional_data_set)
    assert absence_management is not None
    assert absence_management.text == "False"

    customer_number = get_additional_data_value("CustomerNumber", additional_data_set)
    assert customer_number is not None
    assert customer_number.text == "123"

    leave_plans = get_additional_data_value("LeavePlans", additional_data_set)
    assert leave_plans is None

    unlink_all_existing_leave_plans = get_additional_data_value(
        "UnlinkAllExistingLeavePlans", additional_data_set
    )
    assert unlink_all_existing_leave_plans is not None
    assert unlink_all_existing_leave_plans.text == "True"

    version = get_additional_data_value("Version", additional_data_set)
    assert version is not None
    assert version.text == "None"


def test_determine_absence_notification_reason(user, test_db_session):
    application = ApplicationFactory.create(
        user=user,
        leave_reason_id=LeaveReason.CHILD_BONDING.leave_reason_id,
        leave_reason_qualifier_id=LeaveReasonQualifier.NEWBORN.leave_reason_qualifier_id,
    )

    absence_case: massgov.pfml.fineos.models.customer_api.AbsenceCase = (
        fineos_actions.build_absence_case(application)
    )
    assert (
        absence_case.notificationReason
        == fineos_actions.LeaveNotificationReason.BONDING_WITH_A_NEW_CHILD
    )

    application = ApplicationFactory.create(
        user=user,
        leave_reason_id=LeaveReason.PREGNANCY_MATERNITY.leave_reason_id,
        leave_reason_qualifier_id=LeaveReasonQualifier.NEWBORN.leave_reason_qualifier_id,
        pregnant_or_recent_birth=True,
    )

    absence_case: massgov.pfml.fineos.models.customer_api.AbsenceCase = (
        fineos_actions.build_absence_case(application)
    )
    assert (
        absence_case.notificationReason
        == fineos_actions.LeaveNotificationReason.PREGNANCY_BIRTH_OR_RELATED_MEDICAL_TREATMENT
    )

    application = ApplicationFactory.create(
        user=user,
        leave_reason_id=LeaveReason.SERIOUS_HEALTH_CONDITION_EMPLOYEE.leave_reason_id,
        leave_reason_qualifier_id=LeaveReasonQualifier.WORK_RELATED_ACCIDENT_INJURY.leave_reason_qualifier_id,
    )

    absence_case: massgov.pfml.fineos.models.customer_api.AbsenceCase = (
        fineos_actions.build_absence_case(application)
    )
    assert (
        absence_case.notificationReason
        == fineos_actions.LeaveNotificationReason.ACCIDENT_OR_TREATMENT_REQUIRED
    )

    application = ApplicationFactory.create(
        caring_leave_metadata=CaringLeaveMetadataFactory.create(
            relationship_to_caregiver_id=RelationshipToCaregiver.SIBLING.relationship_to_caregiver_id
        ),
        user=user,
        leave_reason_id=LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id,
        leave_reason_qualifier_id=LeaveReasonQualifier.SERIOUS_HEALTH_CONDITION.leave_reason_qualifier_id,
    )

    absence_case: massgov.pfml.fineos.models.customer_api.AbsenceCase = (
        fineos_actions.build_absence_case(application)
    )
    assert (
        absence_case.notificationReason
        == fineos_actions.LeaveNotificationReason.CARING_FOR_A_FAMILY_MEMBER
    )


def test_determine_relationship_qualifiers(user, test_db_session):
    # Relationships that use the BIOLOGICAL qualifier
    biological_qualifier_relationships = [
        RelationshipToCaregiver.PARENT,
        RelationshipToCaregiver.CHILD,
        RelationshipToCaregiver.GRANDPARENT,
        RelationshipToCaregiver.GRANDCHILD,
        RelationshipToCaregiver.SIBLING,
    ]

    for relationship in biological_qualifier_relationships:
        caring_leave_metatadata = CaringLeaveMetadataFactory.create(
            relationship_to_caregiver_id=relationship.relationship_to_caregiver_id
        )
        application = ApplicationFactory.create(
            caring_leave_metadata=caring_leave_metatadata,
            user=user,
            leave_reason_id=LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id,
            leave_reason_qualifier_id=LeaveReasonQualifier.SERIOUS_HEALTH_CONDITION.leave_reason_qualifier_id,
        )
        absence_case: massgov.pfml.fineos.models.customer_api.AbsenceCase = (
            fineos_actions.build_absence_case(application)
        )
        assert (
            absence_case.primaryRelationship == relationship.relationship_to_caregiver_description
        )
        assert (
            absence_case.primaryRelQualifier1
            == RelationshipQualifier.BIOLOGICAL.relationship_qualifier_description
        )
        assert absence_case.primaryRelQualifier2 is None

    # INLAW relationship
    caring_leave_metatadata = CaringLeaveMetadataFactory.create(
        relationship_to_caregiver_id=RelationshipToCaregiver.INLAW.relationship_to_caregiver_id
    )
    application = ApplicationFactory.create(
        caring_leave_metadata=caring_leave_metatadata,
        user=user,
        leave_reason_id=LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id,
        leave_reason_qualifier_id=LeaveReasonQualifier.SERIOUS_HEALTH_CONDITION.leave_reason_qualifier_id,
    )
    absence_case: massgov.pfml.fineos.models.customer_api.AbsenceCase = (
        fineos_actions.build_absence_case(application)
    )
    assert (
        absence_case.primaryRelationship
        == RelationshipToCaregiver.INLAW.relationship_to_caregiver_description
    )
    assert (
        absence_case.primaryRelQualifier1
        == RelationshipQualifier.PARENT_IN_LAW.relationship_qualifier_description
    )
    assert absence_case.primaryRelQualifier2 is None

    # SPOUSE relationship
    caring_leave_metatadata = CaringLeaveMetadataFactory.create(
        relationship_to_caregiver_id=RelationshipToCaregiver.SPOUSE.relationship_to_caregiver_id
    )
    application = ApplicationFactory.create(
        caring_leave_metadata=caring_leave_metatadata,
        user=user,
        leave_reason_id=LeaveReason.CARE_FOR_A_FAMILY_MEMBER.leave_reason_id,
        leave_reason_qualifier_id=LeaveReasonQualifier.SERIOUS_HEALTH_CONDITION.leave_reason_qualifier_id,
    )
    absence_case: massgov.pfml.fineos.models.customer_api.AbsenceCase = (
        fineos_actions.build_absence_case(application)
    )
    assert (
        absence_case.primaryRelationship
        == RelationshipToCaregiver.SPOUSE.relationship_to_caregiver_description
    )
    assert (
        absence_case.primaryRelQualifier1
        == RelationshipQualifier.LEGALLY_MARRIED.relationship_qualifier_description
    )
    assert (
        absence_case.primaryRelQualifier2
        == RelationshipQualifier.UNDISCLOSED.relationship_qualifier_description
    )


def test_format_previous_leaves_data_no_leaves(user, test_db_session):
    application: Application = ApplicationFactory.create(user=user)
    # Application without Other Leaves - No EForm generated.
    eform = fineos_actions.format_previous_leaves_data(application)
    assert eform is None


class TestFormatOtherLeavesEForm:
    def test_claimant_pto_ff_current_leave_and_other_reason(self, user):
        config = FeaturesConfig()

        application: Application = ApplicationFactory.create(user=user)
        application.previous_leaves = [
            PreviousLeaveFactory.create(
                application_id=application.application_id,
                is_for_current_employer=False,
                worked_per_week_minutes=2430,
                leave_minutes=3600,
            )
        ]

        eform = fineos_actions.format_previous_leaves_data(application, config)
        assert eform is not None

        # Confirm there are no PTO fields are in the data, V22PTO1, V22AccruedPaidLeave1, etc.
        assert eform.eformType == "Other Leaves - current version v2"
        expected_attributes = [
            {
                "enumValue": {
                    "domainName": "QualifyingReasons",
                    "instanceValue": "Pregnancy",
                },
                "name": "V22QualifyingReason1",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "No"},
                "name": "V22LeaveFromEmployer1",
            },
            {
                "dateValue": application.previous_leaves[0].leave_start_date.isoformat(),
                "name": "V22OtherLeavesPastLeaveStartDate1",
            },
            {
                "dateValue": application.previous_leaves[0].leave_end_date.isoformat(),
                "name": "V22OtherLeavesPastLeaveEndDate1",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "No"},
                "name": "V22ContinuousLeave1",
            },
            {"integerValue": 40, "name": "V22HoursWorked1"},
            {
                "enumValue": {"domainName": "15MinuteIncrements", "instanceValue": "30"},
                "name": "V22MinutesWorked1",
            },
            {"integerValue": 60, "name": "V22TotalHours1"},
            {
                "enumValue": {"domainName": "15MinuteIncrements", "instanceValue": "00"},
                "name": "V22TotalMinutes1",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "Yes"},
                "name": "V22Applies1",
            },
        ]

        assert eform.eformAttributes == expected_attributes

    def test_only_other_reason(self, user):
        application: Application = ApplicationFactory.create(user=user)
        application.previous_leaves = [
            PreviousLeaveFactory.create(
                application_id=application.application_id,
                is_for_current_employer=False,
                worked_per_week_minutes=2430,
                leave_minutes=3600,
            )
        ]
        application.has_previous_leaves = True

        eform = fineos_actions.format_previous_leaves_data(application)
        assert eform is not None
        assert eform.eformType == "Other Leaves - current version v2"
        expected_attributes = [
            {
                "enumValue": {"domainName": "QualifyingReasons", "instanceValue": "Pregnancy"},
                "name": "V22QualifyingReason1",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "No"},
                "name": "V22LeaveFromEmployer1",
            },
            {
                "dateValue": application.previous_leaves[0].leave_start_date.isoformat(),
                "name": "V22OtherLeavesPastLeaveStartDate1",
            },
            {
                "dateValue": application.previous_leaves[0].leave_end_date.isoformat(),
                "name": "V22OtherLeavesPastLeaveEndDate1",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "No"},
                "name": "V22ContinuousLeave1",
            },
            {"integerValue": 40, "name": "V22HoursWorked1"},
            {
                "enumValue": {"domainName": "15MinuteIncrements", "instanceValue": "30"},
                "name": "V22MinutesWorked1",
            },
            {"integerValue": 60, "name": "V22TotalHours1"},
            {
                "enumValue": {"domainName": "15MinuteIncrements", "instanceValue": "00"},
                "name": "V22TotalMinutes1",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "Yes"},
                "name": "V22Applies1",
            },
        ]

        assert eform.eformAttributes == expected_attributes

    def test_only_same_reason(self, user):
        application: Application = ApplicationFactory.create(user=user)
        application.previous_leaves = [
            PreviousLeaveFactory.create(
                application_id=application.application_id,
                is_for_current_employer=True,
                worked_per_week_minutes=2430,
                leave_minutes=3600,
            )
        ]
        application.has_previous_leaves = True

        eform = fineos_actions.format_previous_leaves_data(application)
        assert eform is not None
        assert eform.eformType == "Other Leaves - current version v2"
        expected_attributes = [
            {
                "enumValue": {
                    "domainName": "QualifyingReasons",
                    "instanceValue": "Pregnancy",
                },
                "name": "V22QualifyingReason1",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "Yes"},
                "name": "V22LeaveFromEmployer1",
            },
            {
                "dateValue": application.previous_leaves[0].leave_start_date.isoformat(),
                "name": "V22OtherLeavesPastLeaveStartDate1",
            },
            {
                "dateValue": application.previous_leaves[0].leave_end_date.isoformat(),
                "name": "V22OtherLeavesPastLeaveEndDate1",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "No"},
                "name": "V22ContinuousLeave1",
            },
            {"integerValue": 40, "name": "V22HoursWorked1"},
            {
                "enumValue": {"domainName": "15MinuteIncrements", "instanceValue": "30"},
                "name": "V22MinutesWorked1",
            },
            {"integerValue": 60, "name": "V22TotalHours1"},
            {
                "enumValue": {"domainName": "15MinuteIncrements", "instanceValue": "00"},
                "name": "V22TotalMinutes1",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "Yes"},
                "name": "V22Applies1",
            },
        ]

        assert eform.eformAttributes == expected_attributes

    def test_all_previous_leave_reasons_present(self, user):
        application: Application = ApplicationFactory.create(user=user)

        application.previous_leaves = [
            PreviousLeaveFactory.create(
                application_id=application.application_id,
                is_for_current_employer=True,
                worked_per_week_minutes=2430,
                leave_minutes=3600,
            ),
            PreviousLeaveFactory.create(
                application_id=application.application_id,
                is_for_current_employer=True,
                worked_per_week_minutes=2430,
                leave_minutes=3600,
            ),
            PreviousLeaveFactory.create(
                application_id=application.application_id,
                is_for_current_employer=True,
                worked_per_week_minutes=2430,
                leave_minutes=3600,
            ),
        ]

        application.has_previous_leaves_any_reason = True
        application.has_previous_leaves_other_reason = True
        application.has_previous_leaves_same_reason = True

        eform = fineos_actions.format_previous_leaves_data(application)
        assert eform is not None
        assert eform.eformType == "Other Leaves - current version v2"

        expected_attributes = [
            {
                "enumValue": {
                    "domainName": "QualifyingReasons",
                    "instanceValue": "Pregnancy",
                },
                "name": "V22QualifyingReason1",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "Yes"},
                "name": "V22LeaveFromEmployer1",
            },
            {
                "dateValue": application.previous_leaves[0].leave_start_date.isoformat(),
                "name": "V22OtherLeavesPastLeaveStartDate1",
            },
            {
                "dateValue": application.previous_leaves[0].leave_end_date.isoformat(),
                "name": "V22OtherLeavesPastLeaveEndDate1",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "No"},
                "name": "V22ContinuousLeave1",
            },
            {"integerValue": 40, "name": "V22HoursWorked1"},
            {
                "enumValue": {"domainName": "15MinuteIncrements", "instanceValue": "30"},
                "name": "V22MinutesWorked1",
            },
            {"integerValue": 60, "name": "V22TotalHours1"},
            {
                "enumValue": {"domainName": "15MinuteIncrements", "instanceValue": "00"},
                "name": "V22TotalMinutes1",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "Yes"},
                "name": "V22Applies1",
            },
            {
                "enumValue": {
                    "domainName": "QualifyingReasons",
                    "instanceValue": "Pregnancy",
                },
                "name": "V22QualifyingReason2",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "Yes"},
                "name": "V22LeaveFromEmployer2",
            },
            {
                "dateValue": application.previous_leaves[1].leave_start_date.isoformat(),
                "name": "V22OtherLeavesPastLeaveStartDate2",
            },
            {
                "dateValue": application.previous_leaves[1].leave_end_date.isoformat(),
                "name": "V22OtherLeavesPastLeaveEndDate2",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "No"},
                "name": "V22ContinuousLeave2",
            },
            {"integerValue": 40, "name": "V22HoursWorked2"},
            {
                "enumValue": {"domainName": "15MinuteIncrements", "instanceValue": "30"},
                "name": "V22MinutesWorked2",
            },
            {"integerValue": 60, "name": "V22TotalHours2"},
            {
                "enumValue": {"domainName": "15MinuteIncrements", "instanceValue": "00"},
                "name": "V22TotalMinutes2",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "Yes"},
                "name": "V22Applies2",
            },
            {
                "enumValue": {
                    "domainName": "QualifyingReasons",
                    "instanceValue": "Pregnancy",
                },
                "name": "V22QualifyingReason3",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "Yes"},
                "name": "V22LeaveFromEmployer3",
            },
            {
                "dateValue": application.previous_leaves[2].leave_start_date.isoformat(),
                "name": "V22OtherLeavesPastLeaveStartDate3",
            },
            {
                "dateValue": application.previous_leaves[2].leave_end_date.isoformat(),
                "name": "V22OtherLeavesPastLeaveEndDate3",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "No"},
                "name": "V22ContinuousLeave3",
            },
            {"integerValue": 40, "name": "V22HoursWorked3"},
            {
                "enumValue": {"domainName": "15MinuteIncrements", "instanceValue": "30"},
                "name": "V22MinutesWorked3",
            },
            {"integerValue": 60, "name": "V22TotalHours3"},
            {
                "enumValue": {"domainName": "15MinuteIncrements", "instanceValue": "00"},
                "name": "V22TotalMinutes3",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "Yes"},
                "name": "V22Applies3",
            },
        ]

        assert eform.eformAttributes == expected_attributes

    def test_all_data_present(self, user):
        application: Application = ApplicationFactory.create(user=user)
        application.previous_leaves = [
            PreviousLeaveFactory.create(
                application_id=application.application_id,
                is_for_current_employer=False,
                worked_per_week_minutes=2430,
                leave_minutes=3600,
            ),
            PreviousLeaveFactory.create(
                application_id=application.application_id,
                is_for_current_employer=False,
                worked_per_week_minutes=2430,
                leave_minutes=3600,
            ),
            PreviousLeaveFactory.create(
                application_id=application.application_id,
                is_for_current_employer=True,
                worked_per_week_minutes=2430,
                leave_minutes=3600,
            ),
            PreviousLeaveFactory.create(
                application_id=application.application_id,
                is_for_current_employer=True,
                worked_per_week_minutes=2430,
                leave_minutes=3600,
            ),
        ]
        application.has_previous_leaves = True

        eform = fineos_actions.format_previous_leaves_data(application)
        assert eform is not None
        assert eform.eformType == "Other Leaves - current version v2"

        expected_attributes = [
            {
                "enumValue": {"domainName": "QualifyingReasons", "instanceValue": "Pregnancy"},
                "name": "V22QualifyingReason1",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "No"},
                "name": "V22LeaveFromEmployer1",
            },
            {
                "dateValue": application.previous_leaves[0].leave_start_date.isoformat(),
                "name": "V22OtherLeavesPastLeaveStartDate1",
            },
            {
                "dateValue": application.previous_leaves[0].leave_end_date.isoformat(),
                "name": "V22OtherLeavesPastLeaveEndDate1",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "No"},
                "name": "V22ContinuousLeave1",
            },
            {"integerValue": 40, "name": "V22HoursWorked1"},
            {
                "enumValue": {"domainName": "15MinuteIncrements", "instanceValue": "30"},
                "name": "V22MinutesWorked1",
            },
            {"integerValue": 60, "name": "V22TotalHours1"},
            {
                "enumValue": {"domainName": "15MinuteIncrements", "instanceValue": "00"},
                "name": "V22TotalMinutes1",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "Yes"},
                "name": "V22Applies1",
            },
            {
                "enumValue": {"domainName": "QualifyingReasons", "instanceValue": "Pregnancy"},
                "name": "V22QualifyingReason2",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "No"},
                "name": "V22LeaveFromEmployer2",
            },
            {
                "dateValue": application.previous_leaves[1].leave_start_date.isoformat(),
                "name": "V22OtherLeavesPastLeaveStartDate2",
            },
            {
                "dateValue": application.previous_leaves[1].leave_end_date.isoformat(),
                "name": "V22OtherLeavesPastLeaveEndDate2",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "No"},
                "name": "V22ContinuousLeave2",
            },
            {"integerValue": 40, "name": "V22HoursWorked2"},
            {
                "enumValue": {"domainName": "15MinuteIncrements", "instanceValue": "30"},
                "name": "V22MinutesWorked2",
            },
            {"integerValue": 60, "name": "V22TotalHours2"},
            {
                "enumValue": {"domainName": "15MinuteIncrements", "instanceValue": "00"},
                "name": "V22TotalMinutes2",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "Yes"},
                "name": "V22Applies2",
            },
            {
                "enumValue": {
                    "domainName": "QualifyingReasons",
                    "instanceValue": "Pregnancy",
                },
                "name": "V22QualifyingReason3",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "Yes"},
                "name": "V22LeaveFromEmployer3",
            },
            {
                "dateValue": application.previous_leaves[2].leave_start_date.isoformat(),
                "name": "V22OtherLeavesPastLeaveStartDate3",
            },
            {
                "dateValue": application.previous_leaves[2].leave_end_date.isoformat(),
                "name": "V22OtherLeavesPastLeaveEndDate3",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "No"},
                "name": "V22ContinuousLeave3",
            },
            {"integerValue": 40, "name": "V22HoursWorked3"},
            {
                "enumValue": {"domainName": "15MinuteIncrements", "instanceValue": "30"},
                "name": "V22MinutesWorked3",
            },
            {"integerValue": 60, "name": "V22TotalHours3"},
            {
                "enumValue": {"domainName": "15MinuteIncrements", "instanceValue": "00"},
                "name": "V22TotalMinutes3",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "Yes"},
                "name": "V22Applies3",
            },
            {
                "enumValue": {
                    "domainName": "QualifyingReasons",
                    "instanceValue": "Pregnancy",
                },
                "name": "V22QualifyingReason4",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "Yes"},
                "name": "V22LeaveFromEmployer4",
            },
            {
                "dateValue": application.previous_leaves[3].leave_start_date.isoformat(),
                "name": "V22OtherLeavesPastLeaveStartDate4",
            },
            {
                "dateValue": application.previous_leaves[3].leave_end_date.isoformat(),
                "name": "V22OtherLeavesPastLeaveEndDate4",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "No"},
                "name": "V22ContinuousLeave4",
            },
            {"integerValue": 40, "name": "V22HoursWorked4"},
            {
                "enumValue": {"domainName": "15MinuteIncrements", "instanceValue": "30"},
                "name": "V22MinutesWorked4",
            },
            {"integerValue": 60, "name": "V22TotalHours4"},
            {
                "enumValue": {"domainName": "15MinuteIncrements", "instanceValue": "00"},
                "name": "V22TotalMinutes4",
            },
            {
                "enumValue": {"domainName": "PleaseSelectYesNo", "instanceValue": "Yes"},
                "name": "V22Applies4",
            },
        ]

        assert eform.eformAttributes == expected_attributes


class TestRegisterEmployeeWithClaim:
    # Run `initialize_factories_session` for all tests,
    # so that it doesn't need to be manually included
    @pytest.fixture(autouse=True)
    def setup_factories(self, initialize_factories_session):
        return

    @mock.patch("massgov.pfml.api.services.fineos_actions.register_employee")
    def test_success(self, mock_register, claim, test_db_session):
        fineos_client = MagicMock()
        fineos_actions.register_employee_with_claim(fineos_client, test_db_session, claim)

        ssn = claim.employee_tax_identifier
        fein = claim.employer_fein
        fineos_id = str(claim.employer.fineos_employer_id)

        mock_register.assert_called_once_with(fineos_client, ssn, fein, test_db_session, fineos_id)

    def test_no_employee_tax_id_exception(self, claim, test_db_session):
        claim.employee.tax_identifier = None

        with pytest.raises(Exception) as exc_info:
            fineos_actions.register_employee_with_claim(None, test_db_session, claim)

        error = exc_info.value
        assert type(error) == Exception

        expected = "Unable to register employee with FINEOS - No employee tax ID for claim"
        assert str(error) == expected

    def test_no_employer_fein_exception(self, claim, test_db_session):
        claim.employer = None

        with pytest.raises(Exception) as exc_info:
            fineos_actions.register_employee_with_claim(None, test_db_session, claim)

        error = exc_info.value
        assert type(error) == Exception

        expected = "Unable to register employee with FINEOS - No employer FEIN for claim"
        assert str(error) == expected


class TestGetAbsencePeriodsFromClaim:
    # Run `initialize_factories_session` for all tests,
    # so that it doesn't need to be manually included
    @pytest.fixture(autouse=True)
    def setup_factories(self, initialize_factories_session):
        return

    def test_no_absence_id_exception(self, claim, test_db_session):
        claim.fineos_absence_id = None

        with pytest.raises(Exception) as exc_info:
            fineos_actions.get_absence_periods_from_claim(claim, test_db_session)

        error = exc_info.value
        assert type(error) == Exception

        expected = "Can't get absence periods from FINEOS - No absence_id for claim"
        assert str(error) == expected

    @mock.patch("massgov.pfml.api.services.fineos_actions.register_employee")
    def test_success(self, mock_register, test_db_session, claim):
        mock_register.return_value = "web_id"

        # TODO (PORTAL-752): don't use magic string here
        claim.fineos_absence_id = "NTN-304363-ABS-01"

        fineos_absence_periods = fineos_actions.get_absence_periods_from_claim(
            claim, test_db_session
        )
        absence_periods = [
            convert_fineos_absence_period_to_claim_response_absence_period(
                fineos_absence_period, {}
            )
            for fineos_absence_period in fineos_absence_periods
        ]
        assert type(absence_periods[0]) == AbsencePeriodResponse
        assert absence_periods == [
            AbsencePeriodResponse(
                fineos_absence_period_id="PL-14449-0000002237",
                absence_period_start_date=date(2021, 1, 29),
                absence_period_end_date=date(2021, 1, 30),
                reason="Child Bonding",
                reason_qualifier_one="Foster Care",
                reason_qualifier_two=None,
                period_type="Continuous",
                request_decision="Pending",
                evidence_status=None,
            )
        ]

    def test_period_type_mapping(self):
        fineos_absence_periods = [
            FineosAbsencePeriod(
                absenceType=AbsencePeriodType.TIME_OFF_PERIOD.absence_period_type_description
            ),
            FineosAbsencePeriod(
                absenceType=AbsencePeriodType.EPISODIC.absence_period_type_description
            ),
        ]

        absence_periods = [
            convert_fineos_absence_period_to_claim_response_absence_period(
                fineos_absence_period, {}
            )
            for fineos_absence_period in fineos_absence_periods
        ]

        assert (
            absence_periods[0].period_type
            == AbsencePeriodType.CONTINUOUS.absence_period_type_description
        )
        assert (
            absence_periods[1].period_type
            == AbsencePeriodType.INTERMITTENT.absence_period_type_description
        )

    @mock.patch("massgov.pfml.api.services.fineos_actions.register_employee")
    def test_with_fineos_error(self, mock_register, test_db_session, claim, caplog):
        error = exception.FINEOSForbidden("get_absence", 200, 403, "Unable to get absence periods")
        mock_register.side_effect = error

        claim.fineos_absence_id = "NTN-304363-ABS-01"

        try:
            fineos_actions.get_absence_periods_from_claim(claim, test_db_session)
        except FINEOSForbidden:
            pass

        assert "Unable to get absence periods" in caplog.text


class TestGetAbsencePeriodDecisionsFromClaim:
    # Run `initialize_factories_session` for all tests,
    # so that it doesn't need to be manually included
    @pytest.fixture(autouse=True)
    def setup_factories(self, initialize_factories_session):
        return

    def test_no_absence_id_exception(self, claim, test_db_session):
        claim.fineos_absence_id = None

        with pytest.raises(Exception) as exc_info:
            fineos_actions.get_absence_period_decisions_from_claim(claim, test_db_session)

        error = exc_info.value
        assert type(error) == Exception

        expected = "Can't get absence period decisions from FINEOS - No absence_id for claim"
        assert str(error) == expected

    @mock.patch("massgov.pfml.api.services.fineos_actions.register_employee")
    def test_success(self, mock_register, test_db_session, claim):
        mock_register.return_value = "web_id"

        # TODO (PORTAL-752): don't use magic string here
        claim.fineos_absence_id = "NTN-304363-ABS-01"

        fineos_absence_periods = fineos_actions.get_absence_period_decisions_from_claim(
            claim, test_db_session
        )
        assert type(fineos_absence_periods[0]) == AbsencePeriodDecision

    @mock.patch("massgov.pfml.api.services.fineos_actions.register_employee")
    def test_with_fineos_error(self, mock_register, test_db_session, claim, caplog):
        error = exception.FINEOSForbidden(
            "get_customer_absence_period_decisions",
            200,
            403,
            "Unable to get absence period decisions",
        )
        mock_register.side_effect = error

        claim.fineos_absence_id = "NTN-304363-ABS-01"

        try:
            fineos_actions.get_absence_period_decisions_from_claim(claim, test_db_session)
        except FINEOSForbidden:
            pass

        assert "Unable to get absence period decisions" in caplog.text


class TestGetAbsencePeriods:
    # Run `initialize_factories_session` for all tests,
    # so that it doesn't need to be manually included
    @pytest.fixture(autouse=True)
    def setup_factories(self, initialize_factories_session):
        return

    @mock.patch("massgov.pfml.api.services.fineos_actions.register_employee")
    def test_success(self, mock_register, test_db_session, claim):
        mock_register.return_value = "web_id"

        # TODO (PORTAL-752): don't use magic string here
        fineos_absence_periods = fineos_actions.get_absence_periods(
            "NTN-304363-ABS-01", claim.employee_tax_identifier, claim.employer_fein, test_db_session
        )

        absence_periods = [
            convert_fineos_absence_period_to_claim_response_absence_period(
                fineos_absence_period, {}
            )
            for fineos_absence_period in fineos_absence_periods
        ]
        assert type(absence_periods[0]) == AbsencePeriodResponse
        assert absence_periods == [
            AbsencePeriodResponse(
                fineos_absence_period_id="PL-14449-0000002237",
                absence_period_start_date=date(2021, 1, 29),
                absence_period_end_date=date(2021, 1, 30),
                reason="Child Bonding",
                reason_qualifier_one="Foster Care",
                reason_qualifier_two=None,
                period_type="Continuous",
                request_decision="Pending",
                evidence_status=None,
            ),
        ]

    @mock.patch("massgov.pfml.api.services.fineos_actions.register_employee")
    def test_with_fineos_error(self, mock_register, test_db_session, claim, caplog):
        error = exception.FINEOSForbidden("get_absence", 200, 403, "Unable to get absence periods")
        mock_register.side_effect = error

        try:
            # TODO (PORTAL-752): don't use magic string here
            fineos_actions.get_absence_periods(
                "NTN-304363-ABS-01",
                claim.employee_tax_identifier,
                claim.employer_fein,
                test_db_session,
            )
        except FINEOSForbidden:
            pass

        assert "Unable to get absence periods" in caplog.text


class TestGetBenefitsInfo:
    # Run `initialize_factories_session` for all tests,
    # so that it doesn't need to be manually included
    @pytest.fixture(autouse=True)
    def setup_factories(self, initialize_factories_session):
        return

    @pytest.fixture
    def absence_paid_leave_case_number(self, claim):
        return "test-absence-paid-leave-case"

    @pytest.fixture
    def benefit_id(self, claim):
        return "PL ABS-479052-PL ABS-01"

    @mock.patch("massgov.pfml.api.services.fineos_actions.register_employee_with_claim")
    def test_get_claim_benefits(self, absence_paid_leave_case_number):
        fineos_mock_client = massgov.pfml.fineos.MockFINEOSClient()
        claim_benefits = fineos_mock_client.read_claim_benefit(
            claim, absence_paid_leave_case_number
        )
        assert type(claim_benefits[0]) == BenefitSummary

    @mock.patch("massgov.pfml.api.services.fineos_actions.register_employee_with_claim")
    @mock.patch("massgov.pfml.api.services.fineos_actions.get_claim_benefits")
    def test_get_disability_benefit_details(self, absence_paid_leave_case_number, benefit_id):
        fineos_mock_client = massgov.pfml.fineos.MockFINEOSClient()
        claim_benefits = fineos_mock_client.read_disability_benefit(
            claim, absence_paid_leave_case_number, benefit_id
        )
        assert type(claim_benefits) == ReadDisabilityBenefitResult


def test_send_tax_withholding_preference(application, claim):
    fineos_mock_client = massgov.pfml.fineos.MockFINEOSClient()
    fineos_mock.start_capture()
    fineos_actions.send_tax_withholding_preference(application, True, fineos_mock_client)
    capture = fineos_mock.get_capture()
    assert capture[0][2] == {"absence_id": claim.fineos_absence_id, "is_withholding_tax": True}


def test_tax_preference_payload():
    payload = FINEOSClient._create_tax_preference_payload("NTN-111-111", True)

    assert payload is not None
    assert payload.__contains__("<config-name>OptInSITFITService</config-name>")
    assert payload.__contains__("<name>AbsenceCaseNumber</name>")
    assert payload.__contains__("<value>NTN-111-111</value>")
    assert payload.__contains__("<name>FlagValue</name>")
    assert payload.__contains__("<value>True</value>")


@mock.patch("massgov.pfml.api.services.fineos_actions.logger", mock_logger)
@pytest.mark.parametrize(
    "existing_work_pattern, expected_fn_call,log_message",
    [
        (
            "Unknown",
            "add_week_based_work_pattern",
            "added work_pattern successfully for customer occupation %s",
        ),
        (
            "Week Based",
            "update_week_based_work_pattern",
            "updated work_pattern successfully for absence case %s",
        ),
        ("Other", [], "No work_pattern request for customer occupation %s"),
    ],
)
def test_upsert_week_based_work_pattern(
    existing_work_pattern, expected_fn_call, log_message, application, user
):
    application.work_pattern = WorkPatternFixedFactory.create()
    fineos_mock_client = massgov.pfml.fineos.MockFINEOSClient()
    fineos_mock.start_capture()
    fineos_actions.upsert_week_based_work_pattern(
        fineos_mock_client, user.user_id, application, 12345, existing_work_pattern
    )
    capture = fineos_mock.get_capture()

    if existing_work_pattern == "Other":
        assert capture == expected_fn_call
    else:
        assert capture[0][0] == expected_fn_call

    assert log_message in mock_logger.info.call_args.args[0]


@mock.patch(
    "massgov.pfml.api.services.fineos_actions.register_employee_with_claim", return_value="web_id"
)
class TestSubmitChangeRequest:
    # Run `initialize_factories_session` for all tests,
    # so that it doesn't need to be manually included
    @pytest.fixture(autouse=True)
    def setup_factories(self, initialize_factories_session):
        return

    @pytest.fixture
    def change_request(self, claim):
        return ChangeRequest(
            claim_id=claim.claim_id, change_request_type_instance=ChangeRequestType.WITHDRAWAL
        )

    @mock.patch("massgov.pfml.api.services.fineos_actions.convert_change_request_to_fineos_model")
    @mock.patch("massgov.pfml.fineos.create_client")
    def test_success(
        self, mock_create_fineos, mock_convert, change_request, claim, test_db_session
    ):
        mock_fineos = MagicMock()
        mock_create_fineos.return_value = mock_fineos
        mock_convert.return_value = {}

        fineos_actions.submit_change_request(change_request, claim, test_db_session)

        mock_convert.assert_called_with(change_request, claim)
        mock_fineos.create_or_update_leave_period_change_request.assert_called_with(
            "web_id", claim.fineos_absence_id, mock.ANY
        )
        assert change_request.submitted_time is not None


class TestConvertChangeRequestToFineosModel:
    # Run `initialize_factories_session` for all tests,
    # so that it doesn't need to be manually included
    @pytest.fixture(autouse=True)
    def setup_factories(self, initialize_factories_session):
        return

    @pytest.fixture
    def application(self):
        return ApplicationFactory.create(
            continuous_leave_periods=[
                ContinuousLeavePeriodFactory.create(
                    start_date=date(2022, 1, 1), end_date=date(2022, 2, 1)
                )
            ]
        )

    @pytest.fixture
    def claim(self, application):
        claim = ClaimFactory.create(
            application=application,
            claim_start_date=date(2022, 1, 1),
            claim_end_date=date(2022, 1, 10),
        )
        return claim

    @pytest.fixture
    def change_request(self, claim):
        return ChangeRequest(
            claim_id=claim.claim_id, change_request_type_instance=ChangeRequestType.EXTENSION
        )

    @pytest.fixture
    def pending_claim(self, claim):
        AbsencePeriodFactory.create(
            claim=claim,
            absence_period_start_date=claim.claim_start_date,
            absence_period_end_date=claim.claim_end_date,
            leave_request_decision_id=LeaveRequestDecision.PENDING.leave_request_decision_id,
        )
        return claim

    @pytest.fixture
    def leave_request_pending_claim(
        self,
        claim,
    ):
        leave_request: LeaveRequest = LeaveRequestFactory.create(
            claim_id=claim.claim_id,
            leave_approval_decision_id=LeaveRequestDecision.PENDING.leave_request_decision_id,
        )
        AbsencePeriodFactory.create(
            fineos_leave_request_id=leave_request.fineos_leave_request_id,
            claim=claim,
            absence_period_start_date=claim.claim_start_date,
            absence_period_end_date=claim.claim_end_date,
        )
        return claim

    def test_withdrawn_for_pending_claim(self, pending_claim, change_request):
        change_request.change_request_type_instance = ChangeRequestType.WITHDRAWAL

        fineos_change_request = fineos_actions.convert_change_request_to_fineos_model(
            change_request, pending_claim
        )

        assert fineos_change_request.reason.name == "Employee Request"
        assert fineos_change_request.additionalNotes == "Withdrawal"

        change_request_period = fineos_change_request.changeRequestPeriods[0]
        assert change_request_period.startDate == date(2022, 1, 1)
        assert change_request_period.endDate == date(2022, 1, 10)

    def test_withdrawn_for_pending_claim_on_leave_request(
        self, leave_request_pending_claim, change_request
    ):
        change_request.change_request_type_instance = ChangeRequestType.WITHDRAWAL

        fineos_change_request = fineos_actions.convert_change_request_to_fineos_model(
            change_request, leave_request_pending_claim
        )

        assert fineos_change_request.reason.name == "Employee Request"
        assert fineos_change_request.additionalNotes == "Withdrawal"

        change_request_period = fineos_change_request.changeRequestPeriods[0]
        assert change_request_period.startDate == date(2022, 1, 1)
        assert change_request_period.endDate == date(2022, 1, 10)

    def test_withdrawn_for_pending_claim_without_absence_periods(
        self, application, claim, change_request
    ):
        change_request.change_request_type_instance = ChangeRequestType.WITHDRAWAL

        claim.claim_start_date = None
        claim.claim_end_date = None

        fineos_change_request = fineos_actions.convert_change_request_to_fineos_model(
            change_request, claim
        )

        assert fineos_change_request.reason.name == "Employee Request"
        assert fineos_change_request.additionalNotes == "Withdrawal"

        change_request_period = fineos_change_request.changeRequestPeriods[0]
        assert change_request_period.startDate == date(2022, 1, 1)
        assert change_request_period.endDate == date(2022, 2, 1)

    def test_withdrawn_for_pending_claim_without_absence_periods_or_app(
        self, claim, change_request
    ):
        change_request.change_request_type_instance = ChangeRequestType.WITHDRAWAL

        claim.claim_start_date = None
        claim.claim_end_date = None
        claim.application = None

        with pytest.raises(ValueError) as exc_info:
            fineos_actions.convert_change_request_to_fineos_model(change_request, claim)

        error = exc_info.value
        assert "Claim does not have absence periods or an associated application" in str(error)

    def test_medical_to_bonding(self, claim, change_request):
        change_request.change_request_type_instance = ChangeRequestType.MEDICAL_TO_BONDING
        change_request.start_date = date(2022, 2, 2)
        change_request.end_date = date(2022, 2, 12)
        change_request.date_of_birth = date(2022, 1, 30)

        fineos_change_request = fineos_actions.convert_change_request_to_fineos_model(
            change_request, claim
        )

        assert fineos_change_request.reason.name == "Add time for different Absence Reason"
        assert (
            fineos_change_request.additionalNotes
            == "Medical to bonding transition. DOB - 01/30/2022"
        )

        change_request_period = fineos_change_request.changeRequestPeriods[0]
        assert change_request_period.startDate == date(2022, 2, 2)
        assert change_request_period.endDate == date(2022, 2, 12)

    def test_extension(self, claim, change_request):
        change_request.end_date = date(2022, 2, 12)

        fineos_change_request = fineos_actions.convert_change_request_to_fineos_model(
            change_request, claim
        )

        assert fineos_change_request.reason.name == "Add time for identical Absence Reason"
        assert fineos_change_request.additionalNotes == "Extension"

        change_request_period = fineos_change_request.changeRequestPeriods[0]
        assert change_request_period.startDate == date(2022, 1, 11)
        assert change_request_period.endDate == date(2022, 2, 12)

    def test_cancellation(self, claim, change_request):
        change_request.change_request_type_instance = ChangeRequestType.CANCELLATION
        change_request.start_date = date(2022, 1, 1)
        change_request.end_date = date(2022, 1, 5)

        fineos_change_request = fineos_actions.convert_change_request_to_fineos_model(
            change_request, claim
        )

        assert fineos_change_request.reason.name == "Employee Request"
        assert fineos_change_request.additionalNotes == "Cancellation"

        change_request_period = fineos_change_request.changeRequestPeriods[0]
        assert change_request_period.startDate == date(2022, 1, 6)
        assert change_request_period.endDate == date(2022, 1, 10)

    def test_unknown_type(self, claim, change_request):
        change_request.change_request_type_instance = LkChangeRequestType(123, "Foo")

        with pytest.raises(ValueError) as exc_info:
            fineos_actions.convert_change_request_to_fineos_model(change_request, claim)

        error = exc_info.value
        assert "Unknown type: Foo" in str(error)


class TestDownloadDocument:
    # Run `initialize_factories_session` for all tests,
    # so that it doesn't need to be manually included
    @pytest.fixture(autouse=True)
    def setup_factories(self, initialize_factories_session):
        return

    @pytest.fixture(autouse=True)
    def with_registered_employee(self, mocker):
        mocker.patch.object(
            fineos_actions, "get_or_register_employee_fineos_web_id", return_value="web_id"
        )

    @pytest.fixture(autouse=True)
    def with_mocked_fineos_calls(self, mocker, fineos_document):
        mocker.patch.object(
            massgov.pfml.fineos.mock_client.MockFINEOSClient,
            "get_documents",
            return_value=[fineos_document],
        )

    @pytest.fixture()
    def application(self, user, claim):
        claim = ClaimFactory.create(fineos_absence_id="NTN-123-ABS-01")

        return ApplicationFactory.create(
            user=user,
            claim=claim,
        )

    @pytest.fixture
    def fineos_document(self, user) -> FineosDocument:
        return FineosDocument(
            caseId="PL ABS-1121685-PL ABS-01-OP1124663",
            documentId="783912",
            name=DocumentType.OVERPAYMENT_PAYOFF_NOTICE.document_type_description,
            type="document",
            userId=user.user_id,
        )

    def test_approval_notice(
        self,
        application: Application,
        test_db_session,
    ):
        document_id = "123"
        document_type = DocumentType.APPROVAL_NOTICE.document_type_description

        fineos_mock.start_capture()
        fineos_actions.download_document(application, document_id, test_db_session, document_type)
        actions = fineos_mock.get_capture()
        action = actions[0]

        assert action[0] == "download_document"
        # download using the claim absence_id
        expected_params = {
            "fineos_document_id": document_id,
            "absence_id": application.fineos_absence_id,
        }
        assert action[2] == expected_params

    @pytest.mark.parametrize("document_type", OVERPAYMENT_DOCUMENT_TYPES)
    def test_overpayment_notices(
        self,
        application: Application,
        fineos_document: FineosDocument,
        document_type,
        test_db_session,
    ):
        document_id = str(fineos_document.documentId)
        document_case_id = fineos_document.caseId

        fineos_mock.start_capture()
        fineos_actions.download_document(application, document_id, test_db_session, document_type)
        actions = fineos_mock.get_capture()
        action = actions[0]

        assert action[0] == "download_document"
        # download using the document caseId (eg "PL ABS-1121685-PL ABS-01-OP1124663")
        # rather than the claim absence_id
        expected_params = {"fineos_document_id": document_id, "absence_id": document_case_id}
        assert action[2] == expected_params

    def test_dismissal_for_failure_to_attend_hearing(
        self,
        application: Application,
        fineos_document: FineosDocument,
        test_db_session,
    ):
        document_id = str(fineos_document.documentId)
        document_case_id = fineos_document.caseId
        document_type = (
            DocumentType.DISMISSAL_FOR_FAILURE_TO_ATTEND_HEARING.document_type_description
        )

        fineos_mock.start_capture()
        fineos_actions.download_document(application, document_id, test_db_session, document_type)
        actions = fineos_mock.get_capture()
        action = actions[0]

        assert action[0] == "download_document"
        # download using the document caseId (eg "PL ABS-1121685-PL ABS-01-OP1124663")
        # rather than the claim absence_id
        expected_params = {"fineos_document_id": document_id, "absence_id": document_case_id}
        assert action[2] == expected_params

    def test_notice_of_default(
        self,
        application: Application,
        fineos_document: FineosDocument,
        test_db_session,
    ):
        document_id = str(fineos_document.documentId)
        document_case_id = fineos_document.caseId
        document_type = DocumentType.NOTICE_OF_DEFAULT.document_type_description

        fineos_mock.start_capture()
        fineos_actions.download_document(application, document_id, test_db_session, document_type)
        actions = fineos_mock.get_capture()
        action = actions[0]

        assert action[0] == "download_document"
        # download using the document caseId (eg "PL ABS-1121685-PL ABS-01-OP1124663")
        # rather than the claim absence_id
        expected_params = {"fineos_document_id": document_id, "absence_id": document_case_id}
        assert action[2] == expected_params

    @pytest.mark.parametrize(
        "document_type",
        [
            DocumentType.W9_TAX_FORM.document_type_description,
            DocumentType.EFT_CHANGE_REQUEST.document_type_description,
            DocumentType.NOTICE_OF_CHILD_SUPPORT_WITHHOLDING.document_type_description,
        ],
    )
    def test_standard_documents(
        self,
        application: Application,
        fineos_document: FineosDocument,
        test_db_session,
        document_type,
    ):
        document_id = str(fineos_document.documentId)
        document_type = DocumentType.W9_TAX_FORM.document_type_description

        fineos_mock.start_capture()
        fineos_actions.download_document(application, document_id, test_db_session, document_type)
        actions = fineos_mock.get_capture()
        action = actions[0]

        assert action[0] == "download_document"
        # download using the document claim absence_id
        expected_params = {
            "fineos_document_id": document_id,
            "absence_id": application.claim.fineos_absence_id,
        }
        assert action[2] == expected_params


class TestDownloadCustomerDocument:
    # Run `initialize_factories_session` for all tests,
    # so that it doesn't need to be manually included
    @pytest.fixture(autouse=True)
    def setup_factories(self, initialize_factories_session):
        return

    @pytest.fixture()
    def user_with_application(self, user):
        ApplicationFactory.create(user=user, submitted_time=Generators.TransactionDateTime)
        return user

    @pytest.fixture
    def fineos_document_data(self) -> Base64EncodedFileData:
        return Base64EncodedFileData(
            base64EncodedFileContents="Zm9v",
            fileExtension="pdf",
            fileName=DocumentType.IRS_1099G_TAX_FORM_FOR_CLAIMANTS.document_type_description,
            fileSizeInBytes=10,
            description="9cd9da59-0590-4282-baf5-f197d8efa456.pdf",
        )

    @mock.patch("massgov.pfml.api.services.fineos_actions.register_employee")
    @mock.patch("massgov.pfml.fineos.create_client")
    def test_success(
        self,
        mock_create_fineos,
        mock_register,
        user_with_application,
        fineos_document_data,
        test_db_session,
    ):
        mock_fineos = MagicMock()
        mock_create_fineos.return_value = mock_fineos
        mock_register.return_value = "web_id"
        mock_fineos.download_document.return_value = fineos_document_data

        fineos_actions.download_customer_document(
            user_with_application, "document_id", test_db_session
        )

        mock_fineos.download_document.assert_called_with("web_id", "document_id")

    @mock.patch("massgov.pfml.fineos.create_client")
    def test_user_without_applications(self, mock_create_fineos, user, test_db_session):
        mock_fineos = MagicMock()
        mock_create_fineos.return_value = mock_fineos

        with pytest.raises(Exception) as exc_info:
            fineos_actions.download_customer_document(user, "document_id", test_db_session)

        error = exc_info.value
        assert (
            "User does not have any submitted applications, therefore could not establish a session"
            in str(error)
        )


class TestChangeEmail:
    @mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.update_customer_contact_details")
    def test_update_user_email_in_fineos_claimaint(
        self,
        mock_fineos_update,
        user,
        test_db_session,
        initialize_factories_session,
    ):
        new_email_address = faker.Faker().email(domain="example.com")
        user.email_address = new_email_address

        application = ApplicationFactory(user=user, submitted_time=date.today())
        fineos_web_id_ext = FINEOSWebIdExt()
        fineos_web_id_ext.employee_tax_identifier = application.tax_identifier.tax_identifier
        fineos_web_id_ext.employer_fein = application.employer_fein
        fineos_web_id_ext.fineos_web_id = "web_id"
        test_db_session.add(fineos_web_id_ext)

        fineos_actions.update_claimant_email_in_fineos(test_db_session, user, {})
        mock_fineos_update.assert_called_once()
        assert mock_fineos_update.mock_calls[0][1][0] == "web_id"
        assert mock_fineos_update.mock_calls[0][1][1].emailAddresses == [
            EmailAddress(emailAddress=new_email_address, emailAddressType="Email")
        ]

    @mock.patch("massgov.pfml.fineos.mock_client.MockFINEOSClient.update_customer_contact_details")
    def test_update_user_email_in_fineos_claimaint_multiple_application(
        self,
        mock_fineos_update,
        user,
        test_db_session,
        initialize_factories_session,
        caplog,
    ):
        new_email_address = faker.Faker().email(domain="example.com")
        user.email_address = new_email_address

        # To test that applications are grouped by tax_identifier
        tax_identifier = TaxIdentifierFactory(tax_identifier="*********")
        ApplicationFactory(user=user, tax_identifier=tax_identifier, submitted_time=date.today())
        ApplicationFactory(user=user, tax_identifier=tax_identifier, submitted_time=date.today())
        ApplicationFactory(user=user, submitted_time=date.today())
        # To test that only submitted applications are processed
        ApplicationFactory(user=user)

        caplog.set_level(logging.INFO)  # noqa: B1
        fineos_actions.update_claimant_email_in_fineos(test_db_session, user, {})

        assert mock_fineos_update.call_count == 2
        assert "Updating emails for 2 unique tax identifier found" in caplog.text
        assert "Executing claimant thread with 2 workers" in caplog.text


class TestLanguagePrefToFineos:
    def test_language_not_listed_to_english(self, user, test_db_session):
        application = ApplicationFactory.create(
            user=user,
            language_id=6,
        )
        preference = fineos_actions.language_pref_to_fineos(application.language)
        assert preference == "English"

    def test_chinese_simplified_to_chinese(self, user, test_db_session):
        application = ApplicationFactory.create(
            user=user,
            language_id=2,
        )
        preference = fineos_actions.language_pref_to_fineos(application.language)
        assert preference == "Chinese"

    def test_no_preference_to_english(self, user, test_db_session):
        application = ApplicationFactory.create(user=user, language_id=None)
        preference = fineos_actions.language_pref_to_fineos(application.language)
        assert preference == "English"


class TestSubmitIntermittentLeaveEpisode:
    # Run `initialize_factories_session` for all tests,
    # so that it doesn't need to be manually included
    @pytest.fixture(autouse=True)
    def setup_factories(self, initialize_factories_session):
        return

    @pytest.fixture
    def intermittent_leave_episode(self):
        return fineos_mock.mock_intermittent_leave_episode()

    def test_submit_intermittent_leave_episode_success(
        self, claim, test_db_session, intermittent_leave_episode: IntermittentLeaveEpisode
    ):
        actual_absence_period_list = fineos_actions.submit_intermittent_leave_episode(
            claim, "NTN-123456-ABS-01", intermittent_leave_episode, test_db_session
        )

        assert actual_absence_period_list[0].actualDate == intermittent_leave_episode.requested_date

        assert (
            actual_absence_period_list[0].episodePeriodBasis.name
            == intermittent_leave_episode.episodic_period_basis
        )
        assert (
            actual_absence_period_list[0].episodePeriodDuration
            == intermittent_leave_episode.episodic_period
        )

    def test_submit_intermittent_leave_episode_no_leave_episode(self, claim, test_db_session):
        intermittent_leave_episode = None

        with pytest.raises(ValueError) as exc_info:
            fineos_actions.submit_intermittent_leave_episode(
                claim, "NTN-123456-ABS-01", intermittent_leave_episode, test_db_session
            )

        error = exc_info.value
        assert "Episode is none" in str(error)

    def test_submit_intermittent_leave_episode_no_requested_date(
        self,
        claim,
        intermittent_leave_episode,
        test_db_session,
    ):
        intermittent_leave_episode.requested_date = None

        with pytest.raises(ValueError) as exc_info:
            fineos_actions.submit_intermittent_leave_episode(
                claim, "NTN-123456-ABS-01", intermittent_leave_episode, test_db_session
            )

        error = exc_info.value
        assert "Episode does not have a requested date" in str(error)

    def test_submit_intermittent_leave_episode_no_episode_period_duration(
        self,
        claim,
        intermittent_leave_episode,
        test_db_session,
    ):
        intermittent_leave_episode.episodic_period = 0

        with pytest.raises(ValueError) as exc_info:
            fineos_actions.submit_intermittent_leave_episode(
                claim, "NTN-123456-ABS-01", intermittent_leave_episode, test_db_session
            )

        error = exc_info.value
        assert "Episode period duration is invalid" in str(error)

    def test_submit_intermittent_leave_episode_no_episode_period_basis(
        self,
        claim,
        intermittent_leave_episode,
        test_db_session,
    ):
        intermittent_leave_episode.episodic_period_basis = None

        with pytest.raises(ValueError) as exc_info:
            fineos_actions.submit_intermittent_leave_episode(
                claim, "NTN-123456-ABS-01", intermittent_leave_episode, test_db_session
            )

        error = exc_info.value
        assert "Episode does not have a period basis" in str(error)


class TestGetActualAbsencePeriodResourcesForClaim:
    # Run `initialize_factories_session` for all tests,
    # so that it doesn't need to be manually included
    @pytest.fixture(autouse=True)
    def setup_factories(self, initialize_factories_session):
        return

    def test_success(self, claim, test_db_session):
        actual_absence_period_resources: ActualAbsencePeriodResources = (
            fineos_actions.get_actual_absence_period_resources_for_claim(claim, test_db_session)
        )

        actual_absence_period = actual_absence_period_resources.elements[0]

        # assert that object is parsed correctly
        assert actual_absence_period.actualDate == date(2023, 8, 22)
        assert actual_absence_period.episodePeriodDuration == 205
        assert actual_absence_period.episodePeriodBasis.name == "Minutes"
