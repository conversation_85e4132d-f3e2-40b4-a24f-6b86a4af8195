import copy
from datetime import date

import faker
import pytest

import massgov.pfml.api.services.employer_exemptions as employer_exemptions_service
from massgov.pfml.api.models.employer_exemptions.common import (
    SelfInsuredPlanDetails,
    SelfInsuredPlanQuestions,
)
from massgov.pfml.api.models.employer_exemptions.requests import (
    EmployerExemptionApplicationRequestBody,
    PurchasedPlanDetailsRequestBody,
)
from massgov.pfml.api.models.phones.common import Phone as api_models_phone
from massgov.pfml.api.models.phones.common import PhoneType as api_models_phone_type
from massgov.pfml.api.util.phone import convert_to_E164
from massgov.pfml.db.lookup_data.employer_exemptions import EmployerExemptionApplicationStatus
from massgov.pfml.db.lookup_data.phone import PhoneType as db_lookup_data_phone
from massgov.pfml.db.models.employer_exemptions import EmployerExemptionApplication, InsurancePlan
from massgov.pfml.db.models.factories import (
    EmployerExemptionApplicationFactory,
    UserLeaveAdministratorFactory,
)
from massgov.pfml.db.models.phone import Phone as db_models_phone
from massgov.pfml.util.sqlalchemy import get_or_404

fake = faker.Faker()


@pytest.fixture
def build_new_employer_exemption_application(initialize_factories_session):
    employer_exemption_application: EmployerExemptionApplication = (
        EmployerExemptionApplicationFactory.build()
    )
    return employer_exemption_application


@pytest.fixture
def create_new_employer_exemption_application(test_db_session, initialize_factories_session):
    employer_exemption_application: EmployerExemptionApplication = (
        EmployerExemptionApplicationFactory.create()
    )
    return employer_exemption_application


def test_create_new_employer_exemption_application(
    test_db_session, build_new_employer_exemption_application
):
    new_employer_exemption_application = build_new_employer_exemption_application
    employer_exemption_application = (
        employer_exemptions_service.create_new_employer_exemption_application(
            test_db_session, new_employer_exemption_application
        )
    )

    assert employer_exemption_application


def test_get_draft_employer_exemption_application(
    test_db_session, create_new_employer_exemption_application
):
    new_employer_exemption_application = create_new_employer_exemption_application
    employer_exemption_application = (
        employer_exemptions_service.get_draft_employer_exemption_application(
            test_db_session, new_employer_exemption_application.employer_id
        )
    )

    assert employer_exemption_application
    assert employer_exemption_application.created_by_user_id
    assert employer_exemption_application.employer_exemption_application_id
    assert (
        employer_exemption_application.employer_exemption_application_status_id
        == EmployerExemptionApplicationStatus.DRAFT.employer_exemption_application_status_id
    )
    assert (
        new_employer_exemption_application.employer_id == employer_exemption_application.employer_id
    )


def test_get_all_employer_exemption_applications(test_db_session, initialize_factories_session):
    leave_admin = UserLeaveAdministratorFactory.create()
    leave_admin2 = UserLeaveAdministratorFactory.create(user=leave_admin.user)

    # create exemption applications for each employer that is in the user's a leave admin for
    EmployerExemptionApplicationFactory.create(employer=leave_admin.employer)
    EmployerExemptionApplicationFactory.create(
        employer=leave_admin.employer, employer_exemption_application_status_id=2
    )
    EmployerExemptionApplicationFactory.create(employer=leave_admin2.employer)
    test_db_session.commit()
    leave_admin_exemptions = employer_exemptions_service.get_all_employer_exemption_applications(
        test_db_session, leave_admin.employer_id
    )
    assert len(leave_admin_exemptions) == 2

    leave_admin2_exemptions = employer_exemptions_service.get_all_employer_exemption_applications(
        test_db_session, leave_admin2.employer_id
    )
    assert len(leave_admin2_exemptions) == 1


def test_update_exemption_application_contact_details(
    test_db_session, initialize_factories_session, create_new_employer_exemption_application
):
    employer_exemption_application = create_new_employer_exemption_application

    # set the feilds to update contact details
    employer_exemption_application_request_body = EmployerExemptionApplicationRequestBody(
        contact_first_name="Jane",
        contact_last_name="Doe",
        contact_phone=api_models_phone(
            phone_number="************", phone_type=api_models_phone_type.Fax
        ),
        contact_title="Software Engineer",
        contact_email_address="<EMAIL>",
    )

    validate_update_exemption_application(
        test_db_session, employer_exemption_application_request_body, employer_exemption_application
    )


def test_update_exemption_application_organization_details(
    test_db_session, initialize_factories_session, create_new_employer_exemption_application
):
    employer_exemption_application = create_new_employer_exemption_application

    employer_exemption_application_request_body = EmployerExemptionApplicationRequestBody(
        should_workforce_count_include_1099_misc=fake.boolean(),
        average_workforce_count=fake.random_int(100, 9999),
    )

    validate_update_exemption_application(
        test_db_session, employer_exemption_application_request_body, employer_exemption_application
    )


def test_update_exemption_application_insurance_details(
    test_db_session, initialize_factories_session, create_new_employer_exemption_application
):
    employer_exemption_application = create_new_employer_exemption_application

    employer_exemption_application_request_body = EmployerExemptionApplicationRequestBody(
        has_family_exemption=fake.boolean(),
        has_medical_exemption=fake.boolean(),
        is_self_insured_plan=fake.boolean(),
    )

    validate_update_exemption_application(
        test_db_session, employer_exemption_application_request_body, employer_exemption_application
    )


def test_update_exemption_application_self_insured_plan(
    test_db_session, initialize_factories_session, create_new_employer_exemption_application
):
    employer_exemption_application = create_new_employer_exemption_application
    employer_exemption_application_request_body = EmployerExemptionApplicationRequestBody()

    self_insured_plan_question_fields = SelfInsuredPlanQuestions()

    for i in SelfInsuredPlanQuestions.__fields__:
        setattr(self_insured_plan_question_fields, i, fake.boolean())

    self_insured_plan_details_fields = {
        "has_obtained_surety_bond": fake.boolean(),
        "has_third_party_administrator": fake.boolean(),
        "insurance_plan_effective_at": date(date.today().year, 1, 1),
        "surety_company": "Surety Company",
        "surety_bond_amount": fake.random_number(digits=7, fix_len=True) / 100,
        "tpa_business_name": "TPA Business Name",
        "tpa_contact_email_address": "<EMAIL>",
        "tpa_contact_first_name": "First",
        "tpa_contact_last_name": "Last",
        "tpa_contact_title": "Title",
        "tpa_contact_phone": api_models_phone(
            phone_number="************", phone_type=api_models_phone_type.Fax
        ),
        "questions": self_insured_plan_question_fields,
    }

    employer_exemption_application_request_body.self_insured = SelfInsuredPlanDetails(
        **self_insured_plan_details_fields
    )

    validate_update_exemption_application(
        test_db_session, employer_exemption_application_request_body, employer_exemption_application
    )


def test_update_exemption_application_purchased_private_plan(
    test_db_session, initialize_factories_session, create_new_employer_exemption_application
):
    employer_exemption_application = create_new_employer_exemption_application
    employer_exemption_application_request_body = EmployerExemptionApplicationRequestBody()

    employer_exemption_application_request_body.purchased_plan = PurchasedPlanDetailsRequestBody(
        insurance_provider_id=3, insurance_plan_id=6
    )

    validate_update_exemption_application(
        test_db_session, employer_exemption_application_request_body, employer_exemption_application
    )


def validate_update_exemption_application(
    test_db_session,
    employer_exemption_application_request_body: EmployerExemptionApplicationRequestBody,
    employer_exemption_application: EmployerExemptionApplication,
):
    API_NAME_TO_DB_NAME_MAP = {
        "contact_phone": "contact_phone_id",
        "insurance_provider": "insurance_provider_id",
        "tpa_contact_phone": "tpa_contact_phone_id",
    }

    employer_exemptions_service.update_from_request(
        test_db_session, employer_exemption_application_request_body, employer_exemption_application
    )

    employer_exemption_application_request_body

    for request_field_name in employer_exemption_application_request_body.__fields_set__:
        columns_to_validate = {}
        request_value = getattr(employer_exemption_application_request_body, request_field_name)

        if isinstance(request_value, PurchasedPlanDetailsRequestBody):
            purchased_plan_details_request_body = request_value.__dict__
            columns_to_validate = {**purchased_plan_details_request_body}

        elif isinstance(request_value, SelfInsuredPlanDetails):
            self_insured_plan_details_request_body = request_value.__dict__

            for k in self_insured_plan_details_request_body.keys():
                if isinstance(self_insured_plan_details_request_body[k], SelfInsuredPlanQuestions):
                    self_insured_plan_questions_request_body = (
                        self_insured_plan_details_request_body[k].__dict__
                    )
                    columns_to_validate = {
                        **columns_to_validate,
                        **self_insured_plan_questions_request_body,
                    }
                else:
                    columns_to_validate[k] = self_insured_plan_details_request_body[k]
        else:
            columns_to_validate[request_field_name] = request_value

        for request_field_name, request_value in columns_to_validate.items():

            db_column_name = (
                request_field_name
                if request_field_name not in API_NAME_TO_DB_NAME_MAP
                else API_NAME_TO_DB_NAME_MAP[request_field_name]
            )

            db_value = getattr(employer_exemption_application, db_column_name)

            if EmployerExemptionApplication.is_phone_db_model_foreign_key(db_column_name):
                phone = (
                    test_db_session.query(db_models_phone)
                    .filter_by(phone_id=db_value)
                    .one_or_none()
                )

                request_phone_number = convert_to_E164(request_value.phone_number)
                request_phone_type_id = db_lookup_data_phone.get_id(request_value.phone_type)
                db_phone_number = phone.phone_number
                db_phone_type_id = phone.phone_type_id

                assert (
                    db_phone_number == request_phone_number
                ), f"{db_column_name}.phone_number validation failed; expected {request_phone_number}, received {db_phone_number}"
                assert (
                    db_phone_type_id == request_phone_type_id
                ), f"{db_column_name}.phone_type_id validation failed; expected {request_phone_type_id}, received {db_phone_type_id}"
            elif db_column_name in ["employer_id", "created_by_user_id"]:
                # employer_id and created_by_user_id should not be updated by the api
                # confirm the request fields do not match the db fields
                assert (
                    db_value != request_value
                ), f"{db_column_name} validation failed; expected {request_value}, received {db_value}"
            else:
                assert (
                    db_value == request_value
                ), f"{db_column_name} validation failed; expected {request_value}, received {db_value}"


def test_is_valid_exemption_application_purchased_private_insurance(
    test_db_session, initialize_factories_session
):
    class ExemptionStatus:
        _EXEMPT_FAMILY_AND_MEDICAL = 1
        _EXEMPT_FAMILY_ONLY = 2
        _EXEMPT_MEDICAL_ONLY = 3
        _NOT_EXEMPT = 4

        @staticmethod
        def get(has_family_exemption, has_medical_exemption):
            status = ExemptionStatus._NOT_EXEMPT

            if has_family_exemption and has_medical_exemption:
                status = ExemptionStatus._EXEMPT_FAMILY_AND_MEDICAL
            elif has_family_exemption and not has_medical_exemption:
                status = ExemptionStatus._EXEMPT_FAMILY_ONLY
            elif not has_family_exemption and has_medical_exemption:
                status = ExemptionStatus._EXEMPT_MEDICAL_ONLY

            return status

    insurance_plans = test_db_session.query(InsurancePlan)

    for plan in insurance_plans:
        employer_exemption_application = EmployerExemptionApplicationFactory(
            has_family_exemption=fake.boolean(),
            has_medical_exemption=fake.boolean(),
            insurance_provider_id=plan.insurance_provider_id,
            insurance_plan_id=plan.insurance_plan_id,
        )

        plan_exemption_status = ExemptionStatus.get(
            plan.has_family_exemption, plan.has_medical_exemption
        )
        application_exemption_status = ExemptionStatus.get(
            employer_exemption_application.has_family_exemption,
            employer_exemption_application.has_medical_exemption,
        )

        if application_exemption_status == ExemptionStatus._NOT_EXEMPT:
            assert (
                employer_exemptions_service.is_valid_purchased_private_insurance(
                    test_db_session, employer_exemption_application
                )
                is False
            )
        else:
            assert employer_exemptions_service.is_valid_purchased_private_insurance(
                test_db_session, employer_exemption_application
            ) == (plan_exemption_status == application_exemption_status)


def validate_columns_are_cleared(
    test_db_session,
    initialize_factories_session,
    employer_exemption_application: EmployerExemptionApplication,
    request_body: EmployerExemptionApplicationRequestBody,
    expected_null_col: list[str],
):
    employer_exemption_application_before_update = copy.copy(employer_exemption_application)
    for col in expected_null_col:
        val = getattr(employer_exemption_application, col)
        assert val is not None, f"{col} validation failed; expected not None, received None"

    employer_exemptions_service.update_from_request(
        test_db_session, request_body, employer_exemption_application
    )

    for col in expected_null_col:
        val = getattr(employer_exemption_application, col)
        assert val is None, f"{col} validation failed; expected None, received {val}"

        if EmployerExemptionApplication.is_phone_db_model_foreign_key(col):
            phone = employer_exemption_application.get_related_phone_object(col)
            assert phone is None

    # updated_at will be changed when the database is updated; skip this column
    columns_to_skip = expected_null_col + ["updated_at"]

    # confirm columns not specified in update request body remain unchanged
    for col in employer_exemption_application_before_update.__dict__:
        # validate database column only, skip relationship attributes (eg. tpa_contact_phone)
        if not EmployerExemptionApplication.is_db_column_name(col):
            continue

        expected = None
        received = None

        if col not in columns_to_skip:
            if col in request_body.__fields_set__:
                expected = getattr(request_body, col)
            else:
                expected = getattr(employer_exemption_application_before_update, col)

            received = getattr(employer_exemption_application, col)

            assert (
                expected == received
            ), f"{col} validation failed; expected {expected}, received {received}"


def test_columns_are_cleared_when_is_self_insured(test_db_session, initialize_factories_session):
    employer_exemption_application = EmployerExemptionApplicationFactory.create()
    employer_exemption_application_request_body = EmployerExemptionApplicationRequestBody(
        is_self_insured_plan=True
    )
    expected_null_col = ["insurance_provider_id", "insurance_plan_id"]

    validate_columns_are_cleared(
        test_db_session,
        initialize_factories_session,
        employer_exemption_application,
        employer_exemption_application_request_body,
        expected_null_col,
    )


def test_columns_are_cleared_when_is_self_insured_family(
    test_db_session, initialize_factories_session
):
    employer_exemption_application = EmployerExemptionApplicationFactory.create()
    employer_exemption_application_request_body = EmployerExemptionApplicationRequestBody(
        is_self_insured_plan=True,
        has_family_exemption=True,
        has_medical_exemption=False,
    )
    expected_null_col = [
        "does_plan_provide_enough_medical_leave",  # medical only question
        "insurance_plan_id",
        "insurance_provider_id",
    ]

    validate_columns_are_cleared(
        test_db_session,
        initialize_factories_session,
        employer_exemption_application,
        employer_exemption_application_request_body,
        expected_null_col,
    )


def test_columns_are_cleared_when_is_self_insured_medical(
    test_db_session, initialize_factories_session
):
    employer_exemption_application = EmployerExemptionApplicationFactory.create()
    employer_exemption_application_request_body = EmployerExemptionApplicationRequestBody(
        is_self_insured_plan=True, has_family_exemption=False, has_medical_exemption=True
    )
    expected_null_col = [
        "does_plan_provide_enough_armed_forces_illness_leave",  # family only question
        "does_plan_provide_enough_armed_forces_leave",  # family only question
        "does_plan_provide_enough_bonding_leave",  # family only question
        "does_plan_provide_enough_caring_leave",  # family only question
        "does_plan_provide_intermittent_armed_forces_leave",  # family only question
        "does_plan_provide_intermittent_bonding_leave",  # family only question
        "does_plan_provide_intermittent_caring_leave",  # family only question
        "insurance_plan_id",
        "insurance_provider_id",
    ]

    validate_columns_are_cleared(
        test_db_session,
        initialize_factories_session,
        employer_exemption_application,
        employer_exemption_application_request_body,
        expected_null_col,
    )


def test_columns_are_cleared_when_is_not_self_insured(
    test_db_session, initialize_factories_session
):
    employer_exemption_application = EmployerExemptionApplicationFactory.create()
    employer_exemption_application_request_body = EmployerExemptionApplicationRequestBody(
        is_self_insured_plan=False
    )

    expected_null_col = [
        "are_employer_withholdings_within_allowable_amount",
        "does_employer_withhold_premiums",
        "does_plan_cover_all_employees",
        "does_plan_cover_employee_contribution",
        "does_plan_cover_former_employees",
        "does_plan_favor_paid_leave_benefits",
        "does_plan_pay_enough_benefits",
        "does_plan_provide_enough_armed_forces_illness_leave",
        "does_plan_provide_enough_armed_forces_leave",
        "does_plan_provide_enough_bonding_leave",
        "does_plan_provide_enough_caring_leave",
        "does_plan_provide_enough_leave",
        "does_plan_provide_enough_medical_leave",
        "does_plan_provide_intermittent_armed_forces_leave",
        "does_plan_provide_intermittent_bonding_leave",
        "does_plan_provide_intermittent_caring_leave",
        "does_plan_provide_intermittent_medical_leave",
        "does_plan_provide_pfml_job_protection",
        "does_plan_provide_return_to_work_benefits",
        "has_obtained_surety_bond",
        "insurance_plan_expires_at",
        "surety_bond_amount",
        "surety_company",
    ]

    validate_columns_are_cleared(
        test_db_session,
        initialize_factories_session,
        employer_exemption_application,
        employer_exemption_application_request_body,
        expected_null_col,
    )


def test_employer_exemption_is_editable(initialize_factories_session):
    employer_exemption_application = EmployerExemptionApplicationFactory.create()

    assert (
        employer_exemption_application.employer_exemption_application_status_id
        == EmployerExemptionApplicationStatus.DRAFT.employer_exemption_application_status_id
    )
    assert (
        employer_exemptions_service.employer_exemption_application_is_editable(
            employer_exemption_application
        )
        is True
    )

    non_editable_statuses = [
        EmployerExemptionApplicationStatus.APPROVED.employer_exemption_application_status_id,
        EmployerExemptionApplicationStatus.DENIED.employer_exemption_application_status_id,
        EmployerExemptionApplicationStatus.IN_REVIEW.employer_exemption_application_status_id,
    ]

    for status_id in non_editable_statuses:
        employer_exemption_application.employer_exemption_application_status_id = status_id
        assert (
            employer_exemptions_service.employer_exemption_application_is_editable(
                employer_exemption_application
            )
            is False
        )


def test_columns_are_cleared_when_when_not_third_party_admin(
    test_db_session, initialize_factories_session
):
    employer_exemption_application = EmployerExemptionApplicationFactory.create()
    employer_exemption_application_request_body = EmployerExemptionApplicationRequestBody(
        has_third_party_administrator=False
    )

    expected_null_col = [
        "tpa_business_name",
        "tpa_contact_title",
        "tpa_contact_first_name",
        "tpa_contact_last_name",
        "tpa_contact_phone_id",
        "tpa_contact_email_address",
    ]

    validate_columns_are_cleared(
        test_db_session,
        initialize_factories_session,
        employer_exemption_application,
        employer_exemption_application_request_body,
        expected_null_col,
    )


def test_add_or_update_phone_valid(test_db_session, initialize_factories_session):
    employer_exemption_application = EmployerExemptionApplicationFactory.create()

    phone_dict = {
        "contact_phone_id": api_models_phone(
            int_code=None,
            phone_number="************",
            phone_type=None,
            e164=None,
            extension=None,
        ),
        "tpa_contact_phone_id": api_models_phone(
            int_code=None,
            phone_number="************",
            phone_type=None,
            e164=None,
            extension=None,
        ),
    }

    for db_column_name, phone in phone_dict.items():
        employer_exemptions_service.add_or_update_phone(
            db_session=test_db_session,
            db_column_name=db_column_name,
            new_phone=phone,
            employer_exemption_application=employer_exemption_application,
        )

    phone = get_or_404(
        test_db_session, db_models_phone, getattr(employer_exemption_application, db_column_name)
    )

    assert phone.phone_number == convert_to_E164(phone.phone_number)


def test_add_or_update_phone_invalid(test_db_session, initialize_factories_session):
    employer_exemption_application = EmployerExemptionApplicationFactory.create()

    phone_dict = {
        "contact_phone": api_models_phone(
            int_code=None,
            phone_number="************",
            phone_type=None,
            e164=None,
            extension=None,
        ),
        "tpa_contact_phone": api_models_phone(
            int_code=None,
            phone_number="************",
            phone_type=None,
            e164=None,
            extension=None,
        ),
    }

    for db_column_name, phone in phone_dict.items():
        try:
            employer_exemptions_service.add_or_update_phone(
                db_session=test_db_session,
                db_column_name=db_column_name,
                new_phone=phone,
                employer_exemption_application=employer_exemption_application,
            )

            raise AssertionError(f"Expected '{db_column_name}' to raise a KeyError exception")
        except KeyError as e:
            assert (
                e.args[0]
                == f"Unable to find column '{db_column_name}' in EmployerExemptionApplication."
            )
