import datetime
from unittest.mock import patch

import faker
import pytest

import massgov.pfml.api.services.employer_claims as employer_claims
from massgov.pfml.fineos.models.customer_api import ReadCustomerOccupation
from massgov.pfml.util import logging

fake = faker.Faker()


@pytest.fixture
def mock_read_customer_occupation() -> list[ReadCustomerOccupation]:
    mock_valid_customer_occupation = {
        "additionalEmploymentCategory": "Unknown",
        "codeId": None,
        "codeName": None,
        "dateJobBegan": datetime.date(2020, 1, 1),
        "dateJobEnded": None,
        "daysWorkedPerWeek": 0.0,
        "department": None,
        "employeeId": "cbb2578b-7198-4876-bacd-4087538f607e",
        "employer": "Transition Seamless Blockchains and Sons",
        "employmentCategory": "Unknown",
        "employmentLocation": "Unknown",
        "employmentStatus": "Active",
        "employmentTitle": "Unknown",
        "endEmploymentReason": "Unknown",
        "endPosReason": None,
        "extensionAttributes": [],
        "hoursWorkedPerWeek": 35.0,
        "hoursWorkedPerYear": 0,
        "jobDesc": None,
        "jobStrenuous": "Unknown",
        "jobTitle": "DEFAULT",
        "manager": None,
        "occupationId": 124221,
        "overrideDaysWorkedPerWeek": False,
        "remarks": None,
        "selfEmployed": False,
        "workCity": "Boston",
        "workPattern": "Fixed",
        "workPatternBasis": "Week Based",
        "workScheduleDescription": None,
        "workSite": None,
        "workState": "MA",
    }

    return [ReadCustomerOccupation.parse_obj(mock_valid_customer_occupation)]


class TestCustomerDetailsFromCustomerApi:

    @patch("massgov.pfml.db.init")
    @patch("massgov.pfml.db.models.employees.Employer")
    @patch("massgov.pfml.fineos.fineos_client")
    def test_get_hours_worked_per_week_if_occupation_available(
        self,
        mock_fineos_client,
        mock_employer,
        mock_db_session,
        mock_read_customer_occupation,
        caplog,
    ):
        caplog.set_level(logging.INFO)

        mock_fineos_client.get_customer_occupations_customer_api.return_value = (
            mock_read_customer_occupation
        )

        customer_details_from_customer_api = employer_claims.CustomerDetailsFromCustomerApi(
            mock_fineos_client,
            mock_db_session,
            fake.random_letters,
            mock_employer,
            fake.random_letters,
        )

        results = customer_details_from_customer_api.get_hours_worked_per_week()

        assert results == "35.0"
        assert "No customer occupations were returned" not in caplog.text

    @patch("massgov.pfml.db.init")
    @patch("massgov.pfml.db.models.employees.Employer")
    @patch("massgov.pfml.fineos.fineos_client")
    def test_none_if_no_occupation_available(
        self, mock_fineos_client, mock_employer, mock_db_session, caplog
    ):
        caplog.set_level(logging.INFO)

        mock_fineos_client.get_customer_occupations_customer_api.return_value = []

        customer_details_from_customer_api = employer_claims.CustomerDetailsFromCustomerApi(
            mock_fineos_client,
            mock_db_session,
            fake.random_letters,
            mock_employer,
            fake.random_letters,
        )

        results = customer_details_from_customer_api.get_hours_worked_per_week()

        assert results is None
        assert "No customer occupations were returned" in caplog.text
