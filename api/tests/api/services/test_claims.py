import copy
from datetime import date, datetime, timedelta
from unittest import mock

import pytest
from freezegun import freeze_time

import massgov.pfml.api.services.claims as service
import massgov.pfml.util.json as json
from massgov.pfml.api.eligibility.benefit import calculate_weekly_benefit_amount
from massgov.pfml.api.eligibility.benefit_year_dates import calculate_benefit_year_dates
from massgov.pfml.api.eligibility.eligibility_util import fetch_state_metric
from massgov.pfml.api.models.applications.common import PaymentScheduleType
from massgov.pfml.api.models.claims.responses import (
    AbsencePeriodDetailResponse,
    IntermittentLeaveEpisodeResponse,
    calculate_duration_in_minutes,
)
from massgov.pfml.api.models.IntermittentLeaveEpisodes.common import (
    EpisodePeriodBasis,
    IntermittentLeaveEpisode,
)
from massgov.pfml.api.services import fineos_actions
from massgov.pfml.api.services.claims import get_previous_absence_period_from_claim
from massgov.pfml.db.lookup_data.absences import (
    AbsencePeriodType,
    AbsenceReason,
    AbsenceReasonQualifierOne,
    AbsenceStatus,
)
from massgov.pfml.db.lookup_data.employees import LeaveRequestDecision, PaymentMethod
from massgov.pfml.db.models.absences import AbsencePeriod
from massgov.pfml.db.models.employees import Claim
from massgov.pfml.db.models.factories import (
    AbsencePaidLeaveCaseFactory,
    AbsencePeriodFactory,
    ApplicationFactory,
    BenefitsMetricsFactory,
    BenefitYearContributionFactory,
    BenefitYearFactory,
    ClaimFactory,
    ContinuousLeavePeriodFactory,
    EmployeeFactory,
    EmployerFactory,
    LeaveRequestFactory,
    ManagedRequirementFactory,
    PaymentPreferenceFactory,
)
from massgov.pfml.db.queries.claims import get_iaww_from_claim
from massgov.pfml.fineos import exception
from massgov.pfml.fineos.mock_client import MockFINEOSClient
from massgov.pfml.fineos.models.customer_api import AbsenceDetails
from massgov.pfml.fineos.models.customer_api import AbsencePeriod as FineosAbsencePeriod
from massgov.pfml.fineos.models.customer_api import (
    AbsencePeriodDecision,
    AbsencePeriodDecisions,
    EpisodicLeavePeriodDetail,
)
from massgov.pfml.services.claims import ClaimWithdrawnError
from massgov.pfml.util.pydantic.types import FEINFormattedStr


# Run `initialize_factories_session` for all tests,
# so that it doesn't need to be manually included
@pytest.fixture(autouse=True)
def setup_factories(initialize_factories_session):
    return


class TestGetClaimDetail:
    @pytest.fixture(autouse=True)
    def with_fineos_absence_periods(self, mocker, fineos_absence_period):
        # override the FINEOS mock to return the given absence period by default
        absence_details = AbsenceDetails(absencePeriods=[fineos_absence_period])
        mocker.patch.object(
            MockFINEOSClient,
            "get_absence",
            return_value=absence_details,
        )

    @pytest.fixture
    def claim(self, claim, linked_absence_periods, test_db_session):
        db_period: AbsencePeriod = linked_absence_periods[1]

        claim.absence_periods = [db_period]
        test_db_session.commit()

        return claim

    @pytest.fixture
    def approved_claim(self, claim: Claim):
        """
        An approved claim has a leave request and an absence paid leave case
        """

        leave_request = LeaveRequestFactory.create(claim=claim)

        absence_period = claim.absence_periods[0]
        absence_period.leave_request = leave_request

        AbsencePaidLeaveCaseFactory.create(
            leave_request=leave_request,
            start_date=absence_period.absence_period_start_date,
            end_date=absence_period.absence_period_end_date,
        )

        return claim

    @pytest.fixture
    def linked_absence_periods(self):
        """
        Create both a FINEOS and Mass PFML db representation of a single absence_period
        """

        # values needed on both objects, so that they can be associated with each other
        fineos_ap_class_id = 14449
        fineos_ap_index_id = 2237

        start_date = date(2021, 1, 29)
        end_date = date(2021, 1, 30)

        fineos_period = FineosAbsencePeriod(
            id=f"PL-{fineos_ap_class_id}-000000{fineos_ap_index_id}",
            absenceType="Continuous",
            reason="Child Bonding",
            reasonQualifier1="Foster Care",
            startDate=start_date,
            endDate=end_date,
            requestStatus="Approved",
        )

        db_period = AbsencePeriod(
            fineos_absence_period_class_id=fineos_ap_class_id,
            fineos_absence_period_index_id=fineos_ap_index_id,
            absence_period_type_id=AbsencePeriodType.CONTINUOUS.absence_period_type_id,
            absence_reason_id=AbsenceReason.CHILD_BONDING.absence_reason_id,
            absence_reason_qualifier_one_id=AbsenceReasonQualifierOne.FOSTER_CARE.absence_reason_qualifier_one_id,
            absence_period_start_date=start_date,
            absence_period_end_date=end_date,
        )

        return fineos_period, db_period

    @pytest.fixture
    def fineos_absence_period(self, linked_absence_periods):
        return linked_absence_periods[0]

    @pytest.fixture
    def fineos_absence_period_decision(self):
        return AbsencePeriodDecision(
            periodId="PL-14449-0000002237",
            startDate=date(2021, 1, 15),
            endDate=date(2021, 2, 15),
            absencePeriodStatus="Known",
        )

    @pytest.fixture
    def expected_absence_period_detail(self):
        return AbsencePeriodDetailResponse(
            fineos_leave_request_id=None,
            fineos_absence_period_id="PL-14449-0000002237",
            absence_period_start_date=date(2021, 1, 29),
            absence_period_end_date=date(2021, 1, 30),
            modified_start_date=None,
            modified_end_date=None,
            reason="Child Bonding",
            reason_qualifier_one="Foster Care",
            reason_qualifier_two=None,
            period_type="Continuous",
            request_decision="Approved",
            document_type_requirements=["Identification Proof", "Child bonding evidence form"],
            episodic_leave_period_detail=None,
            approved_start_date=None,
            approved_end_date=None,
            is_fully_approved=False,
        )

    @pytest.fixture
    def managed_requirements(self, claim):
        requirements = []

        for _ in range(2):
            requirement = ManagedRequirementFactory.create(claim=claim, claim_id=claim.claim_id)
            requirements.append(requirement)

        return requirements

    def test_withdrawn_claim_exception(self, mocker, claim, test_db_session):
        error = {
            "error": "User does not have permission to access the absence case.",
            "correlationId": "foo",
        }
        error_msg = json.dumps(error)

        fineos_error = exception.FINEOSForbidden("get_absence", 200, 403, error_msg)
        mocker.patch.object(
            MockFINEOSClient,
            "get_absence",
            side_effect=fineos_error,
        )

        with pytest.raises(Exception) as exc_info:
            service.get_claim_detail(claim, test_db_session)

        error = exc_info.value
        assert type(error) == ClaimWithdrawnError

    def test_no_absence_periods_exception(self, mocker, claim, test_db_session):
        absence_details = AbsenceDetails(absencePeriods=[])
        mocker.patch.object(
            MockFINEOSClient,
            "get_absence",
            return_value=absence_details,
        )

        with pytest.raises(Exception) as exc_info:
            service.get_claim_detail(claim, test_db_session)

        error_msg = str(exc_info.value)
        assert error_msg == "No absence periods found for claim"

    def test_success(
        self,
        claim,
        fineos_absence_period,
        test_db_session,
        expected_absence_period_detail,
    ):
        claim_detail = service.get_claim_detail(claim, test_db_session)

        assert claim_detail_matches_claim(claim_detail, claim)

        period = claim_detail.absence_periods[0]
        assert period == expected_absence_period_detail

        assert "Identification Proof" in period.document_type_requirements
        assert "Child bonding evidence form" in period.document_type_requirements

        # assert these values have been updated as part of the call
        assert claim.claim_start_date == fineos_absence_period.startDate
        assert claim.claim_end_date == fineos_absence_period.endDate

        assert claim_detail.approval_date is not None

        assert claim_detail.payment_schedule_type == PaymentScheduleType.LEAVE_START_BASED

    def test_no_card_arrival_date_when_not_prepaid(self, claim, test_db_session):
        claim_detail = service.get_claim_detail(claim, test_db_session)

        assert claim_detail.claim_payment_info.card_arrival_date is None

    @pytest.fixture
    def claim_with_prepaid_card(self, claim):
        payment_preference = PaymentPreferenceFactory.create(
            payment_method_id=PaymentMethod.PREPAID_CARD.payment_method_id
        )
        claim.application.payment_preference = payment_preference
        claim.created_at = datetime(2025, 4, 19)

        return claim

    def test_card_arrival_date_when_prepaid(self, claim_with_prepaid_card, test_db_session):
        claim_detail = service.get_claim_detail(claim_with_prepaid_card, test_db_session)

        assert claim_detail.claim_payment_info.card_arrival_date == datetime(2025, 5, 2).date()

    def test_success_no_required_document_types(
        self,
        mocker,
        claim,
        test_db_session,
        expected_absence_period_detail,
    ):
        mocker.patch(
            "massgov.pfml.services.absence_periods._get_absence_period_details.DocumentRequirementService.get_required_document_types_by_absence_reason",
            return_value=[],
        )

        expected_absence_period_detail.document_type_requirements = []

        claim_detail = service.get_claim_detail(claim, test_db_session)
        assert claim_detail_matches_claim(claim_detail, claim)

        period = claim_detail.absence_periods[0]
        assert period.reason == expected_absence_period_detail.reason
        assert period == expected_absence_period_detail
        assert period.document_type_requirements == []

    def test_success_multiple_periods(self, mocker, claim, fineos_absence_period, test_db_session):
        fineos_absence_two = copy.deepcopy(fineos_absence_period)
        fineos_absence_two.startDate = date(2021, 1, 31)
        fineos_absence_two.endDate = date(2021, 2, 28)

        fineos_absence_periods = [fineos_absence_period, fineos_absence_two]

        absence_details = AbsenceDetails(absencePeriods=fineos_absence_periods)
        mocker.patch.object(
            MockFINEOSClient,
            "get_absence",
            return_value=absence_details,
        )

        service.get_claim_detail(claim, test_db_session)

        # assert these values have been updated to the earliest and latest date for multiple periods
        assert claim.claim_start_date == fineos_absence_period.startDate
        assert claim.claim_end_date == fineos_absence_two.endDate

    def test_success_with_managed_requirements(
        self,
        claim,
        managed_requirements,
        test_db_session,
    ):
        claim_detail = service.get_claim_detail(claim, test_db_session)

        assert len(claim_detail.managed_requirements) == 2

        req = claim_detail.managed_requirements[0]
        expected_req = managed_requirements[0]

        assert managed_req_response_matches_managed_req(req, expected_req)

    def test_success_with_absence_period_decisions(
        self,
        mocker,
        claim,
        fineos_absence_period_decision,
        test_db_session,
    ):
        absence_decisions = AbsencePeriodDecisions(
            absencePeriodDecisions=[fineos_absence_period_decision]
        )
        mocker.patch.object(
            MockFINEOSClient,
            "get_customer_absence_period_decisions",
            return_value=absence_decisions,
        )

        claim_detail = service.get_claim_detail(claim, test_db_session)

        assert claim_detail_matches_claim(claim_detail, claim)

        period = claim_detail.absence_periods[0]

        # Check these fields were updated
        assert period.modified_start_date == fineos_absence_period_decision.startDate
        assert period.modified_end_date == fineos_absence_period_decision.endDate

    def test_success_with_intermittent_leave_episodes(self, mocker, claim, test_db_session):
        fineos_absence_period = FineosAbsencePeriod(
            id="PL-14449-0000002237",
            reason="Child Bonding",
            reasonQualifier1="Foster Care",
            reasonQualifier2="",
            startDate=date(2021, 1, 29),
            endDate=date(2021, 1, 30),
            absenceType="Intermittent",
            requestStatus="Approved",
            episodicLeavePeriodDetail=EpisodicLeavePeriodDetail(),
        )

        absence_details = AbsenceDetails(absencePeriods=[fineos_absence_period])
        mocker.patch.object(
            MockFINEOSClient,
            "get_absence",
            return_value=absence_details,
        )

        response = service.get_claim_detail(claim, test_db_session)

        assert response.intermittent_leave_episodes[0] == IntermittentLeaveEpisodeResponse(
            episode_id="14449-225227",
            episode_leave_request_id="14474-33098",
            date_of_leave=date(2023, 8, 22),
            duration_in_minutes=205,
            episode_type="Incapacity",
            status="Pending",
            date_reported="2023-10-27T16:45:11Z",
        )
        assert response.intermittent_leave_episodes[1] == IntermittentLeaveEpisodeResponse(
            episode_id="14449-95169",
            episode_leave_request_id="14474-33098",
            date_of_leave=date(2024, 6, 25),
            duration_in_minutes=120,
            episode_type="Incapacity",
            status="Approved",
            date_reported="2024-06-27T17:11:05Z",
        )
        assert response.intermittent_leave_episodes[2] == IntermittentLeaveEpisodeResponse(
            episode_id="14449-95171",
            episode_leave_request_id="14474-33098",
            date_of_leave=date(2024, 6, 25),
            duration_in_minutes=1440,
            episode_type="Incapacity",
            status="Approved",
            date_reported="2024-06-27T17:11:05Z",
        )

    def test_approved_dates(self, mocker, fineos_absence_period, approved_claim, test_db_session):
        absence_period = approved_claim.absence_periods[0]

        fineos_absence_period.startDate = absence_period.start_date
        fineos_absence_period.endDate = absence_period.end_date

        absence_details = AbsenceDetails(absencePeriods=[fineos_absence_period])
        mocker.patch.object(
            MockFINEOSClient,
            "get_absence",
            return_value=absence_details,
        )

        claim_detail = service.get_claim_detail(approved_claim, test_db_session)
        absence_period_detail = claim_detail.absence_periods[0]

        assert absence_period_detail.approved_start_date == absence_period.start_date
        assert absence_period_detail.approved_end_date == absence_period.end_date


def claim_detail_matches_claim(claim_detail, claim):
    if claim_detail.fineos_absence_id != claim.fineos_absence_id:
        return False
    if claim_detail.fineos_notification_id != claim.fineos_notification_id:
        return False
    if claim_detail.employer.employer_dba != claim.employer.employer_dba:
        return False
    if claim_detail.employer.employer_fein != FEINFormattedStr.validate_type(
        claim.employer.employer_fein
    ):
        return False
    if claim_detail.employer.employer_id != claim.employer.employer_id:
        return False
    if claim_detail.employee.first_name != claim.employee.first_name:
        return False
    if claim_detail.employee.middle_name != claim.employee.middle_name:
        return False
    if claim_detail.employee.last_name != claim.employee.last_name:
        return False
    if claim_detail.employee.other_name != claim.employee.other_name:
        return False
    if claim_detail.employer_review.is_reviewable != claim.is_reviewable:
        return False
    if claim_detail.employer_review.earliest_follow_up_date != claim.earliest_follow_up_date:
        return False
    if claim_detail.employer_review.latest_follow_up_date != claim.latest_follow_up_date:
        return False

    return True


def managed_req_response_matches_managed_req(req_response, req):
    if str(req_response.follow_up_date) != req.follow_up_date.strftime("%Y-%m-%d"):
        return False

    req_status = req.managed_requirement_status.managed_requirement_status_description
    if req_response.status != req_status:
        return False

    req_type = req.managed_requirement_type.managed_requirement_type_description
    if req_response.type != req_type:
        return False

    req_category = req.managed_requirement_category.managed_requirement_category_description
    if req_response.category != req_category:
        return False

    return True


class TestCalculatePaymentScheduleType:
    fineos_upgrade_date = date(2022, 5, 20)

    @pytest.fixture(autouse=True)
    def setup_env_vars(self, monkeypatch):
        fineos_upgrade_date_str = self.fineos_upgrade_date.strftime("%Y-%m-%d")
        monkeypatch.setenv("FINEOS_V21_UPGRADE_DATE", fineos_upgrade_date_str)

    def test_no_fineos_upgrade_date(self, monkeypatch):
        monkeypatch.setenv("FINEOS_V21_UPGRADE_DATE", "")
        approval_date = None

        payment_schedule_type = service.calculate_payment_schedule_type(approval_date)

        assert payment_schedule_type == PaymentScheduleType.LEAVE_START_BASED

    def test_no_approval_date(self):
        approval_date = None

        type = service.calculate_payment_schedule_type(approval_date)

        assert type is None

    def test_approval_date_after_fineos_upgrade(self):
        approval_date = self.fineos_upgrade_date + timedelta(days=1)

        type = service.calculate_payment_schedule_type(approval_date)

        assert type == PaymentScheduleType.SUNDAY_BASED

    def test_approval_date_on_fineos_upgrade(self):
        approval_date = self.fineos_upgrade_date

        type = service.calculate_payment_schedule_type(approval_date)

        assert type == PaymentScheduleType.LEAVE_START_BASED

    def test_approval_date_before_fineos_upgrade(self):
        approval_date = self.fineos_upgrade_date - timedelta(days=10)

        type = service.calculate_payment_schedule_type(approval_date)

        assert type == PaymentScheduleType.LEAVE_START_BASED


class TestGetPreviousAbsencePeriodFromClaim:
    @pytest.fixture
    def employee(self):
        return EmployeeFactory.create()

    @pytest.fixture
    def employer(self):
        return EmployerFactory.create()

    @pytest.fixture
    def employee_previous_claim_same_employer(self, employee, employer):
        claim = ClaimFactory.create_with_leave_request_and_absence_period(
            employer=employer,
            employee=employee,
        )

        AbsencePeriodFactory.create(
            modified_start_date=date(2022, 3, 20),
            modified_end_date=date(2022, 4, 20),
            absence_period_start_date=date(2022, 3, 2),
            absence_period_end_date=date(2022, 4, 20),
            claim=claim,
            absence_period_type_id=AbsencePeriodType.CONTINUOUS.absence_period_type_id,
            leave_request_decision_id=LeaveRequestDecision.APPROVED.leave_request_decision_id,
        )

        return claim

    @pytest.fixture
    def employee_previous_claim_other_employer(self, employee):
        claim = ClaimFactory.create_with_leave_request_and_absence_period(
            employer=EmployerFactory.create(),
            employee=employee,
        )

        AbsencePeriodFactory.create(
            modified_start_date=date(2022, 1, 20),
            modified_end_date=date(2022, 2, 20),
            absence_period_start_date=date(2022, 1, 2),
            absence_period_end_date=date(2022, 2, 20),
            claim=claim,
            absence_period_type_id=AbsencePeriodType.CONTINUOUS.absence_period_type_id,
            leave_request_decision_id=LeaveRequestDecision.APPROVED.leave_request_decision_id,
        )

        return claim

    @pytest.fixture
    def claim(self, employee, employer):
        return ClaimFactory.create(employer=employer, employee=employee)

    @pytest.fixture
    def application_with_no_claim(self, user, employee, employer):
        return ApplicationFactory.create(
            user=user,
            employer_fein=employer.employer_fein,
            tax_identifier=employee.tax_identifier,
        )

    def test_should_not_return_absence_periods_for_claims_of_other_employer(
        self,
        employee,
        employee_previous_claim_other_employer,
        claim,
    ):
        assert len(employee.claims) == 2

        previous_absence_periods = get_previous_absence_period_from_claim(claim)
        assert len(previous_absence_periods) == 0

    def test_should_return_absence_periods_for_claims_of_other_employer(
        self,
        employee,
        employee_previous_claim_same_employer,
        employee_previous_claim_other_employer,
        claim,
    ):
        assert len(employee.claims) == 3

        AbsencePeriodFactory.create(
            modified_start_date=date(2023, 1, 20),
            modified_end_date=date(2023, 2, 20),
            absence_period_start_date=date(2023, 1, 2),
            absence_period_end_date=date(2023, 2, 20),
            claim=claim,
            absence_period_type_id=AbsencePeriodType.CONTINUOUS.absence_period_type_id,
            leave_request_decision_id=LeaveRequestDecision.APPROVED.leave_request_decision_id,
        )

        previous_absence_periods = get_previous_absence_period_from_claim(
            claim, limit_to_claim_employer=False
        )

        assert previous_absence_periods[0].absence_period_start_date == date(2022, 3, 2)
        assert previous_absence_periods[0].absence_period_end_date == date(2022, 4, 20)
        assert previous_absence_periods[1].absence_period_start_date == date(2022, 1, 2)
        assert previous_absence_periods[1].absence_period_end_date == date(2022, 2, 20)

        assert len(previous_absence_periods) == 2

    def test_should_not_return_absence_periods_for_this_claim(
        self,
        employee,
        claim,
    ):
        assert len(employee.claims) == 1
        for _ in range(3):
            AbsencePeriodFactory.create(
                claim=claim,
                leave_request_decision_id=LeaveRequestDecision.APPROVED.leave_request_decision_id,
            )

        previous_absence_periods = get_previous_absence_period_from_claim(claim)
        assert len(previous_absence_periods) == 0

    @pytest.mark.parametrize(
        ("leave_request_decision_id", "should_absence_period_be_returned"),
        (
            (LeaveRequestDecision.PENDING.leave_request_decision_id, True),
            (LeaveRequestDecision.IN_REVIEW.leave_request_decision_id, True),
            (LeaveRequestDecision.APPROVED.leave_request_decision_id, True),
            (LeaveRequestDecision.DENIED.leave_request_decision_id, False),
            (LeaveRequestDecision.CANCELLED.leave_request_decision_id, False),
            (LeaveRequestDecision.WITHDRAWN.leave_request_decision_id, False),
            (LeaveRequestDecision.PROJECTED.leave_request_decision_id, False),
            (LeaveRequestDecision.VOIDED.leave_request_decision_id, False),
            (LeaveRequestDecision.UNKNOWN.leave_request_decision_id, False),
            (None, False),
        ),
    )
    def test_should_correctly_return_absence_periods_based_on_leave_request_decision(
        self,
        claim,
        employee,
        employer,
        leave_request_decision_id,
        should_absence_period_be_returned,
    ):
        absence_period = [
            AbsencePeriodFactory.create(
                modified_start_date=date(2022, 1, 1),
                modified_end_date=date(2022, 1, 15),
                absence_period_start_date=date(2022, 1, 1),
                absence_period_end_date=date(2022, 1, 15),
                claim=claim,
                absence_period_type_id=AbsencePeriodType.CONTINUOUS.absence_period_type_id,
                leave_request_decision_id=LeaveRequestDecision.PENDING.leave_request_decision_id,
            ),
        ]
        claim.absence_periods = absence_period

        previous_claim = ClaimFactory.create(
            employee=employee,
            employer=employer,
        )
        absence_period = AbsencePeriodFactory.create(
            modified_start_date=date(2021, 11, 1),
            modified_end_date=date(2021, 11, 15),
            absence_period_start_date=date(2021, 11, 1),
            absence_period_end_date=date(2021, 11, 15),
            claim=previous_claim,
            leave_request_decision_id=leave_request_decision_id,
        )
        previous_absence_periods = get_previous_absence_period_from_claim(claim)
        assert len(previous_absence_periods) == (1 if should_absence_period_be_returned else 0)
        assert (absence_period in previous_absence_periods) == should_absence_period_be_returned


class TestSubmitLeaveEpisode:
    @pytest.fixture
    def employee(self):
        return EmployeeFactory.create()

    @pytest.fixture
    def employer(self):
        return EmployerFactory.create()

    @pytest.fixture
    def claim(self, employee, employer):
        return ClaimFactory.create(employer=employer, employee=employee)

    @pytest.fixture
    def not_intermittent_fineos_absence_period(self):
        return FineosAbsencePeriod(
            id="PL-14449-0000002237",
            reason="Child Bonding",
            reasonQualifier1="Foster Care",
            reasonQualifier2="",
            startDate=date(2021, 1, 29),
            endDate=date(2021, 1, 30),
            absenceType="Continuous",
            requestStatus="Approved",
            episodicLeavePeriodDetail=EpisodicLeavePeriodDetail(),
        )

    @pytest.fixture
    def fineos_episodic_leave_period1(self):
        return EpisodicLeavePeriodDetail(
            duration=4,
            durationBasis="Hours",
            frequency=3,
            frequencyInterval=1,
            frequencyIntervalBasis="Week",
        )

    @pytest.fixture
    def fineos_episodic_leave_period2(self):
        return EpisodicLeavePeriodDetail(
            duration=2,
            durationBasis="Hours",
            frequency=4,
            frequencyInterval=1,
            frequencyIntervalBasis="Week",
        )

    @pytest.fixture
    def fineos_intermittent_absence_period1(self, fineos_episodic_leave_period1):
        return FineosAbsencePeriod(
            id="PL-14449-0000002238",
            reason="Child Bonding",
            reasonQualifier1="Foster Care",
            reasonQualifier2="",
            startDate=date(2021, 1, 1),
            endDate=date(2021, 12, 12),
            absenceType="Episodic",
            requestStatus="Approved",
            episodicLeavePeriodDetail=fineos_episodic_leave_period1,
        )

    @pytest.fixture
    def fineos_intermittent_absence_period2(self, fineos_episodic_leave_period2):
        return FineosAbsencePeriod(
            id="PL-14449-0000002239",
            reason="Child Bonding",
            reasonQualifier1="Foster Care",
            reasonQualifier2="",
            startDate=date(2021, 12, 13),
            endDate=date(2021, 12, 30),
            absenceType="Episodic",
            requestStatus="Approved",
            episodicLeavePeriodDetail=fineos_episodic_leave_period2,
        )

    @pytest.fixture
    def intermittent_leave_episode_to_submit(self):
        return IntermittentLeaveEpisode(
            requested_date="2023-10-10",
            episodic_period=120,
            episodic_period_basis=EpisodePeriodBasis.Minutes,
        )

    @mock.patch("massgov.pfml.api.services.claims.submit_intermittent_leave_episode")
    def test_submit_intermittent_leave_episode(
        self,
        mock_submit_intermittent_leave_episode,
        intermittent_leave_episode_to_submit,
        claim,
        test_db_session,
    ):
        mock_submit_intermittent_leave_episode.return_value = (
            fineos_actions.submit_intermittent_leave_episode(
                claim,
                "NTN-123456-ABS-01",
                intermittent_leave_episode_to_submit,
                test_db_session,
            )
        )

        response = service.submit_leave_episode(
            claim, "NTN-123456-ABS-01", intermittent_leave_episode_to_submit, test_db_session
        )

        assert len(response) == 1
        assert response[0].date_of_leave == intermittent_leave_episode_to_submit.requested_date
        assert response[0].duration_in_minutes == calculate_duration_in_minutes(
            intermittent_leave_episode_to_submit.episodic_period,
            intermittent_leave_episode_to_submit.episodic_period_basis,
        )
        assert response[0].status == "Success"


class TestGetClaimWagesBenefitsFromClaim:
    @pytest.fixture
    def employee(self):
        return EmployeeFactory.create()

    @pytest.fixture
    def employer(self):
        return EmployerFactory.create()

    @pytest.fixture
    def application_with_wages_and_benefits(self, employee, employer):
        return ApplicationFactory.create(employer_fein=employer.employer_fein)

    @pytest.fixture
    def claim(self, employee, employer):
        return ClaimFactory.create(
            employer=employer,
            employee=employee,
            employee_id=employee.employee_id,
            employer_id=employer.employer_id,
            fineos_absence_status_id=AbsenceStatus.APPROVED.absence_status_id,
        )

    @pytest.fixture
    def application(self, claim):
        return ApplicationFactory.create(claim_id=claim.claim_id)

    @pytest.fixture
    def benefits_year_contribution(self, claim):
        benefit_year_dates = calculate_benefit_year_dates(claim.application.start_date)
        benefit_year = BenefitYearFactory.create(
            start_date=benefit_year_dates.start_date,
            end_date=benefit_year_dates.end_date,
            employee_id=claim.employee.employee_id,
            employee=claim.employee,
        )

        return BenefitYearContributionFactory.create(
            benefit_year=benefit_year,
            benefit_year_id=benefit_year.benefit_year_id,
            employee_id=claim.employee.employee_id,
            employer_id=claim.employer.employer_id,
            average_weekly_wage=1270.0,
        )

    @pytest.fixture
    @freeze_time("2021-06-14")
    def leave_period(self, claim):
        return ContinuousLeavePeriodFactory.create(
            application_id=claim.application.application_id,
            start_date=datetime.now().date(),
        )

    @pytest.fixture
    @freeze_time("2021-06-14")
    def absence_period(self, claim):
        return AbsencePeriodFactory.create(
            claim=claim,
            claim_id=claim.claim_id,
        )

    @pytest.fixture
    @freeze_time("2021-06-14")
    def benefits_metrics(self):
        return BenefitsMetricsFactory.create(
            effective_date=datetime.now().date(),
        )

    @pytest.fixture
    def claim_with_wages_and_benefits(
        self,
        claim,
        application,
        leave_period,
        benefits_year_contribution,
        benefits_metrics,
        absence_period,
    ) -> Claim:
        return claim

    @pytest.fixture
    def claim_without_wages_and_benefits(self, claim, application, leave_period, absence_period):
        return claim

    def test_get_claim_wages_benefits_from_claim(
        self, test_db_session, claim_with_wages_and_benefits
    ):
        wages_and_benefits = service.get_iaww_and_weekly_benefit_amount_from_claim(
            test_db_session, claim_with_wages_and_benefits
        )
        assert wages_and_benefits.individual_average_weekly_wage == 1270.0
        assert wages_and_benefits.weekly_benefit_amount is not None

        leave_start_date = claim_with_wages_and_benefits.application.start_date

        sunday_on_or_before_leave_start_date = calculate_benefit_year_dates(
            leave_start_date
        ).start_date
        state_metrics = fetch_state_metric(test_db_session, sunday_on_or_before_leave_start_date)
        individual_average_weekly_wage = get_iaww_from_claim(
            test_db_session, claim=claim_with_wages_and_benefits, log_extra={}
        )

        # Calculate the weekly benefit amount using the formula to ensure it matches the value in
        # the get_iaww_and_weekly_benefit_amount_from_claim
        wba = calculate_weekly_benefit_amount(
            individual_average_weekly_wage,
            state_metrics.for_benefits.average_weekly_wage,
            state_metrics.for_benefits.maximum_weekly_benefit_amount,
        )

        assert wba == wages_and_benefits.weekly_benefit_amount

    def test_get_claim_wages_benefits_from_unapproved_claim(
        self, test_db_session, claim_with_wages_and_benefits
    ):
        claim_with_wages_and_benefits.absence_periods[0].leave_request_decision_id = (
            LeaveRequestDecision.PENDING.leave_request_decision_id
        )
        test_db_session.commit()
        test_db_session.refresh(claim_with_wages_and_benefits)

        wages_and_benefits = service.get_iaww_and_weekly_benefit_amount_from_claim(
            test_db_session, claim_with_wages_and_benefits
        )
        assert wages_and_benefits is None

    def test_get_claim_without_wages_and_benefits(
        self, test_db_session, claim_without_wages_and_benefits
    ):
        wages_and_benefits = service.get_iaww_and_weekly_benefit_amount_from_claim(
            test_db_session, claim_without_wages_and_benefits
        )

        assert wages_and_benefits is None
