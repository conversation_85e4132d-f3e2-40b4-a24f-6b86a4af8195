from datetime import date
from unittest import mock
from unittest.mock import <PERSON>Mock

import pytest
from werkzeug.datastructures import FileStorage
from werkzeug.exceptions import NotFound

import massgov.pfml.services.documents as documents_service
from massgov.pfml.api.models.change_request.common import ChangeRequest
from massgov.pfml.api.models.documents.requests import DocumentRequestBody
from massgov.pfml.api.services.change_requests import (
    claim_has_in_progress_requests,
    defer_change_request,
    get_change_requests_from_db,
    update_change_request_db,
)
from massgov.pfml.db.lookup_data.deferred_submission_item import DeferredSubmissionStatus
from massgov.pfml.db.lookup_data.documents import DocumentType
from massgov.pfml.db.models.deferred_submission_item import DeferredSubmissionItem
from massgov.pfml.db.models.factories import ClaimFactory


class TestDeferChangeRequest:
    def test_defer_change_request(self, change_request, test_db_session):
        deferred_submission_item = defer_change_request(change_request, test_db_session)
        test_db_session.commit()

        assert deferred_submission_item.change_request_id == change_request.change_request_id
        assert (
            deferred_submission_item.deferred_submission_status_id
            == DeferredSubmissionStatus.PENDING.deferred_submission_status_id
        )
        assert test_db_session.query(DeferredSubmissionItem).one()


class TestUploadDocument:
    # Run `initialize_factories_session` for all tests,
    # so that it doesn't need to be manually included
    @pytest.fixture(autouse=True)
    def setup_factories(self, initialize_factories_session):
        return

    @pytest.fixture
    def document_details(self) -> DocumentRequestBody:
        return DocumentRequestBody(document_type=DocumentType.PASSPORT)

    @pytest.fixture
    def file(self) -> FileStorage:
        return MagicMock()

    @mock.patch("massgov.pfml.services.documents.upload_document.upload_document_to_fineos")
    def test_success(self, mock_upload, change_request, document_details, file, test_db_session):
        mock_response = {"status_code": 200, "data": {}}
        mock_upload.return_value = mock_response

        response = documents_service.upload_change_request_document(
            change_request, document_details, file, test_db_session
        )
        assert response == mock_response

        mock_upload.assert_called_with(change_request.claim.application, document_details, file)

    @mock.patch("massgov.pfml.services.documents.upload_document.upload_document_to_fineos")
    def test_submitted_at_updated(
        self, mock_upload, change_request, document_details, file, test_db_session
    ):
        assert change_request.documents_submitted_at is None

        documents_service.upload_change_request_document(
            change_request, document_details, file, test_db_session
        )

        # expire uncommited changes, so that change_request represents only what's in the db
        test_db_session.expire(change_request)
        assert change_request.documents_submitted_at is not None

    def test_no_application(self, change_request, document_details, file, test_db_session):
        change_request.claim.application = None

        with pytest.raises(Exception) as exc_info:
            documents_service.upload_change_request_document(
                change_request, document_details, file, test_db_session
            )

        error = exc_info.value

        assert change_request.documents_submitted_at is None
        assert type(error) == NotFound
        assert "Could not find associated application for change request" in str(error)


class TestClaimHasInProgressRequests:
    def test_claim_has_change_request(
        self, initialize_factories_session, test_db_session, claim, change_request
    ):
        in_progress = claim_has_in_progress_requests(claim, test_db_session)
        assert in_progress

    def test_claim_does_not_have_change_request(
        self, initialize_factories_session, test_db_session
    ):
        claim = ClaimFactory.create()
        assert not claim_has_in_progress_requests(claim, test_db_session)


class TestGetChangeRequestsFromDB:
    def test_success(self, initialize_factories_session, test_db_session, claim, change_request):

        change_requests = get_change_requests_from_db(claim, test_db_session)

        assert change_requests[0].claim_id == claim.claim_id
        assert change_requests[0].is_extension

    def test_no_change_requests(self, initialize_factories_session, test_db_session, claim):
        change_requests = get_change_requests_from_db(claim, test_db_session)
        assert len(change_requests) == 0


class TestUpdateChangeRequestsDB:
    def test_success(self, initialize_factories_session, test_db_session, change_request):
        update_request = ChangeRequest(
            change_request_type="Medical To Bonding Transition",
            start_date="2022-05-01",
            date_of_birth="2022-04-30",
        )
        update_change_request_db(test_db_session, update_request, change_request)
        test_db_session.commit()
        test_db_session.expire(change_request)
        assert change_request.type == "Medical To Bonding Transition"
        assert change_request.start_date == date(2022, 5, 1)
        assert change_request.date_of_birth == date(2022, 4, 30)

    def test_no_change_request_type_specified(
        self, initialize_factories_session, test_db_session, change_request
    ):
        update_request = ChangeRequest(change_request_type=None, start_date="2022-05-01")
        update_change_request_db(test_db_session, update_request, change_request)
        test_db_session.commit()
        test_db_session.expire(change_request)

        # No updates made to type, update successfully made
        assert change_request.type == "Extension"
        assert change_request.start_date == date(2022, 5, 1)
