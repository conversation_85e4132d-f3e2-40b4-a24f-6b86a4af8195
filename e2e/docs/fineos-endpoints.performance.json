[{"FINEOSMethod": "POST", "FINEOSUrl": "%/customerapi/customer/absence/%/reflexive-questions"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/customerapi/customer/absence/absences"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/customerapi/customer/absence/absences/%"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/customerapi/customer/absence/absences/%/absence-period-decisions"}, {"FINEOSMethod": "POST", "FINEOSUrl": "%/customerapi/customer/absence/absences/%/leave-periods-change-requests"}, {"FINEOSMethod": "POST", "FINEOSUrl": "%/customerapi/customer/absence/notifications/%/complete-intake"}, {"FINEOSMethod": "POST", "FINEOSUrl": "%/customerapi/customer/absence/startAbsence"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/customerapi/customer/payment-preferences"}, {"FINEOSMethod": "POST", "FINEOSUrl": "%/customerapi/customer/payment-preferences"}, {"FINEOSMethod": "POST", "FINEOSUrl": "%/customerapi/customer/payment-preferences/%/edit"}, {"FINEOSMethod": "POST", "FINEOSUrl": "%/customerapi/customer/cases/%/addEForm/%"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/customerapi/customer/document/%/base64Download"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/customerapi/customer/cases/%/documents/%/base64Download"}, {"FINEOSMethod": "POST", "FINEOSUrl": "%/customerapi/customer/cases/%/documents/%/doc-received-for-outstanding-supporting-evidence"}, {"FINEOSMethod": "POST", "FINEOSUrl": "%/customerapi/customer/cases/%/documents/base64Upload/%"}, {"FINEOSMethod": "POST", "FINEOSUrl": "%/customerapi/customer/cases/%/documents/upload/%"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/customerapi/customer/cases/%/documents?includeChildCases=True"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/customerapi/customer/document-metas?%"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/customerapi/customer/document?%"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/customerapi/customer/cases/%/eForms"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/customerapi/customer/cases/%/occupations"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/customerapi/customer/cases/%/readEForm/%"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/customerapi/customer/occupations"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/customerapi/customer/occupations/%/week-based-work-pattern"}, {"FINEOSMethod": "POST", "FINEOSUrl": "%/customerapi/customer/occupations/%/week-based-work-pattern"}, {"FINEOSMethod": "POST", "FINEOSUrl": "%/customerapi/customer/occupations/%/week-based-work-pattern/replace"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/customerapi/customer/readCustomerContactDetails"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/customerapi/customer/readCustomerDetails"}, {"FINEOSMethod": "POST", "FINEOSUrl": "%/customerapi/customer/updateCustomerContactDetails"}, {"FINEOSMethod": "POST", "FINEOSUrl": "%/customerapi/customer/updateCustomerDetails"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/groupclientapi/groupClient/absences/absence-period-decisions?%"}, {"FINEOSMethod": "POST", "FINEOSUrl": "%/groupclientapi/groupClient/cases/%/addEForm/%"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/groupclientapi/groupClient/cases/%/documents/%/base64Download"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/groupclientapi/groupClient/cases/%/document-metas?_subcaseDocuments=true"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/groupclientapi/groupClient/cases/%/documents?_filter=includeChildCases"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/groupclientapi/groupClient/cases/%/eforms"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/groupclientapi/groupClient/cases/%/eforms/%/readEform"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/groupclientapi/groupClient/cases/%/managedRequirements"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/groupclientapi/groupClient/cases/%/outstanding-information"}, {"FINEOSMethod": "POST", "FINEOSUrl": "%/groupclientapi/groupClient/cases/%/outstanding-information-received"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/groupclientapi/groupClient/customers/%/customer-info"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/groupclientapi/groupClient/customers/%/customer-occupations"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/groupclientapi/groupClient/notifications/%"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/healthcheck"}, {"FINEOSMethod": "POST", "FINEOSUrl": "%/integration-services/api/v1/document/uploadAndIndexDocumentToFineosDMS"}, {"FINEOSMethod": "POST", "FINEOSUrl": "%/integration-services/rest/externalUserProvisioningService/createOrUpdateEmployerViewpointUser"}, {"FINEOSMethod": "POST", "FINEOSUrl": "%/integration-services/services/ActivityServices"}, {"FINEOSMethod": "POST", "FINEOSUrl": "%/integration-services/services/CaseServices"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/integration-services/wscomposer/COMReadPaidLeaveInstruction?param_str_casenumber=%&userid=%"}, {"FINEOSMethod": "GET", "FINEOSUrl": "%/integration-services/wscomposer/ReadEmployer?param_str_taxId=*********&userid=%"}, {"FINEOSMethod": "POST", "FINEOSUrl": "%/integration-services/wscomposer/webservice?config=EmployeeRegisterService&userid=%"}, {"FINEOSMethod": "POST", "FINEOSUrl": "%/integration-services/wscomposer/webservice?config=OccupationDetailUpdateService&userid=%"}, {"FINEOSMethod": "POST", "FINEOSUrl": "%/integration-services/wscomposer/webservice?config=OptInSITFITService&userid=%"}, {"FINEOSMethod": "POST", "FINEOSUrl": "%/integration-services/wscomposer/webservice?config=ServiceAgreementService&userid=%"}, {"FINEOSMethod": "POST", "FINEOSUrl": "%/integration-services/wscomposer/webservice?config=UpdateOrCreateParty&userid=%"}]