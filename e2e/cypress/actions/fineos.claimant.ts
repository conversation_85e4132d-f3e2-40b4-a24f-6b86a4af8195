import { format } from "date-fns";

import {
  AbsencePeriodResponse,
  Address,
  Phone,
  ReducedScheduleLeavePeriods,
} from "../../src/_api";
import config from "../../src/config";
import { LeaveReason } from "../../src/generation/Claim";
import {
  AbsenceReasonDescription,
  CustomerSpecificDetails,
  PersonalIdentificationDetails,
  RequireNotNull,
  TypeOfRequestOptions,
  ValidClaim,
} from "../../src/types";
import {
  dateToMMddyyyy,
  extractLeavePeriodType,
  getFineosPreferredLanguage,
  getLeavePeriod,
} from "../../src/util/claims";
import { generateAddress } from "../../src/util/pii";
import { assertValidClaim } from "../../src/util/typeUtils";
import { before } from "../actions/fineos";
import { fineos } from ".";
import {
  ensureAlertPopUpClosed,
  onTab,
  wait,
  waitForAjaxComplete,
} from "./fineos";
import {
  DocumentsPage,
  getAbsenceReasonDescription,
  reasonToRequestTypeMap,
} from "./fineos.pages";

interface CreateNotificationOptions {
  withholdingPreference?: boolean;
  employeeId?: string;
  newEmployer?: Employer;
  taskType?: string;
  absenceStatus?: AbsenceStatus;
}

export class ClaimantPage {
  private constructor() {}

  static visit(ssn: string): ClaimantPage {
    before();
    cy.get('a[aria-label="Parties"]').click();
    waitForAjaxComplete();
    // removed the cy.wait(500);
    return ClaimantPage.findClaimant(ssn);
  }

  /*
   * @param identifier String representing unique identifier for party
   */
  static findClaimant(identifier: string): ClaimantPage {
    identifier = identifier.replace(/-/g, "");
    cy.findByLabelText("Identification Number").type(identifier, {
      delay: 10,
    });
    cy.get('input[type="submit"][value="Search"]').click();
    waitForAjaxComplete();
    fineos.clickBottomWidgetButton("OK");

    return new ClaimantPage();
  }

  assertTaxIdentifier(
    ssn: string,
    options?: { shouldBeVisible: boolean }
  ): this {
    const shouldBeVisible = options?.shouldBeVisible ?? true;
    const expectedSsn = shouldBeVisible
      ? ssn
      : `xxx-xx-${ssn.substring(ssn.length - 4)}`;

    cy.get("[id*='obfuscatedIdNumber_WRAPPER']").should(
      "contain.text",
      expectedSsn
    );
    return this;
  }

  assertCanEditTaxIdentifier(canEdit: boolean): this {
    cy.get(`#personalIdentificationCardWidget`).findByTitle("Edit").click();
    cy.get(`#cardEditPopupWidget_PopupWidgetWrapper`).within(() => {
      const haveExpectedStatus = canEdit ? "be.enabled" : "be.disabled";
      cy.get("input[id*='obfuscatedIdNumber_0']").should(haveExpectedStatus);
      cy.get("input[id*='obfuscatedIdNumber_1']").should(haveExpectedStatus);
      cy.get("input[id*='obfuscatedIdNumber_2']").should(haveExpectedStatus);
      // Actually modifying the SSN will cause the data to no longer match the
      // E2E dataset, so we just hit cancel to make sure no changes are saved.
      cy.findByText("Cancel").click();
    });
    return this;
  }

  assertPreferredLanguage(
    language: NonNullable<DehydratedClaim["preferredLanguage"]>
  ) {
    cy.contains(
      '[id$="preferredLanguage"]',
      getFineosPreferredLanguage(language)
    );
  }

  setPreferredLanguage(
    language: GeneratedClaim["preferredLanguage"]
  ): ClaimantPage {
    cy.get("#languagesCardWidget [title='Edit']").click();
    const option = getFineosPreferredLanguage(language);
    cy.get("select[id^='languagesWidget']").first().select(option);
    cy.get(
      "div[id$='LanguagesPopupWidget_PopupWidgetWrapper'] input[title='OK']"
    ).click();
    return this;
  }

  /**
   * Changes the personal identification details of the claimant.
   * @param changes Object with one or more of properties to edit.
   */
  editPersonalIdentification(
    changes: Partial<PersonalIdentificationDetails & CustomerSpecificDetails>
  ): this {
    cy.get(`#personalIdentificationCardWidget`).findByTitle("Edit").click();
    cy.get(`#cardEditPopupWidget_PopupWidgetWrapper`).within(() => {
      if (changes.id_number_type) {
        cy.findByLabelText(`Identification number type`).select(
          changes.id_number_type
        );
      }

      if (changes.date_of_birth) {
        cy.findByLabelText(`Date of birth`)
          .focus()
          .type(`{selectAll}{backspace}${changes.date_of_birth}`);
      }

      if (changes.gender) {
        cy.findByLabelText(`Gender`).select(changes.gender);
      }

      if (changes.marital_status) {
        cy.findByLabelText(`Marital status`).select(changes.marital_status);
      }
      cy.findByText("OK").click();
    });
    if (changes.date_of_birth) {
      cy.get("[id$=dateOfBirth]").should("contain.text", changes.date_of_birth);
    }
    return this.editCustomerSpecific(changes);
  }

  /**
   * Changes the personal identification details of the claimant.
   * @param changes Object with one or more of properties to edit.
   */
  editCustomerSpecific(changes: Partial<CustomerSpecificDetails>): this {
    if (
      !changes.mass_id &&
      !changes.out_of_state_id &&
      !changes.race &&
      !changes.ethnicity
    ) {
      return this;
    }
    cy.get(`#classExtensionDetailsCardWidget`).findByTitle("Edit").click();
    cy.get(`#cardEditPopupWidget_PopupWidgetWrapper`).within(() => {
      if (changes.mass_id) {
        cy.findByLabelText(`Massachusetts ID`)
          .focus()
          .type(`{selectAll}{backspace}${changes.mass_id}`);
      }

      if (changes.out_of_state_id) {
        cy.findByLabelText(`Out of State ID`)
          .focus()
          .type(`{selectAll}{backspace}${changes.out_of_state_id}`);
      }

      if (changes.race) {
        cy.findByLabelText(`Race`).select(changes.race);
      }

      if (changes.ethnicity) {
        cy.findByLabelText(`Ethnicity`).select(changes.ethnicity);
      }

      cy.findByText("OK").click();
    });
    if (changes.mass_id) {
      cy.get("[id$=MassachusettsID]").should("contain.text", changes.mass_id);
    }
    return this;
  }

  documents(cb: (page: DocumentsPage) => unknown): this {
    onTab("Documents");
    cb(new DocumentsPage());
    return this;
  }

  addAddress(
    address: RequireNotNull<Address, "city" | "line_1" | "state" | "zip">
  ): this {
    cy.findByText(`+ Add address`).click();
    waitForAjaxComplete();
    cy.get(`#addressPopupWidget_PopupWidgetWrapper`).within(() => {
      cy.findByLabelText("Country").select("USA");
      cy.wait("@ajaxRender").wait(250);
      cy.findByLabelText(`Address line 1`)
        .type(`${address.line_1}`)
        .wait("@ajaxRender");
      if (address.line_2) {
        cy.findByLabelText(`Address line 2`).type(`${address.line_2}`);
      }
      cy.findByLabelText(`City`).type(`${address.city}`);
      cy.findByLabelText(`State`).select(`${address.state}`);
      cy.findByLabelText(`Zip code`).type(`${address.zip}`);
      cy.findByTitle("OK").click();
    });
    return this;
  }

  addAddressIfNone(
    address?: RequireNotNull<Address, "city" | "line_1" | "state" | "zip">
  ): this {
    cy.get("#addressesMultiPaint").then((element) => {
      if (element.children().length === 0) {
        this.addAddress(address ?? generateAddress());
      }
    });

    return this;
  }

  addEmailIfNone(email: string): this {
    cy.get("#contactDetailsMultiPaint").then((wrapp) => {
      if (
        wrapp.find(".card .header .header-title:contains('Email')").length === 0
      ) {
        cy.get("a[id^='newContactDetailsCard'][id$='addEmailContact']").click();
        waitForAjaxComplete();
        cy.get("div#addEmailPopupWidget_PopupWidgetWrapper").within(() => {
          cy.get("input[id$='_email']").clear();
          cy.get("input[id$='_email']").type(email);
          cy.get("input[id$='okButtonBean']").click();
        });
      }
    });
    return this;
  }

  /**
   * Gets the first entitlement period listed for the claimant.
   */
  getFirstEntitlementPeriod() {
    onTab("Leave Information");
    onTab("Entitlement Periods");

    const entitlementPeriod: {
      end: string;
      start: string;
      leavePlan: string;
    } = { end: "", start: "", leavePlan: "" };

    return cy
      .get('table[id$="_entitlementPeriodLeavePlanListviewWidget"]')
      .within(() => {
        cy.get("tr")
          .first()
          .within(() => {
            cy.get(`td[id$="LeavePlan0"]`).then(
              (td) => (entitlementPeriod.leavePlan = td.text())
            );
            cy.get(`td[id$="StartDate0"]`).then(
              (td) => (entitlementPeriod.start = td.text())
            );
            cy.get(`td[id$="EndDate0"]`).then(
              (td) => (entitlementPeriod.end = td.text())
            );
          });
      })
      .then(() => entitlementPeriod);
  }

  setPhoneNumber(
    phoneNumber: string,
    verified = true,
    phoneType: Exclude<Phone["phone_type"], null | undefined> = "Cell"
  ): this {
    const strippedPhoneNumber = phoneNumber.replace(/[^0-9]/g, "");
    if (![10, 11].includes(strippedPhoneNumber.length)) {
      throw new Error(`Invalid phone number: ${phoneNumber}`);
    }

    // Deletes existing contact, if it exists
    cy.get("div#contactDetailsFrame")
      .find("div.container.card span.header-title")
      .each((el) => {
        if (el.text() === phoneType) {
          cy.wrap(
            el.parent().parent().find("span.controls span[id$='deleteIcon']")
          ).click();
          cy.get("input[id$='Delete_Contact_yes']").click();
        }
      });

    cy.get("a[id^='newContactDetailsCard'][id$='addPhoneContact']").click();

    let internationalCode: string;
    let areaCode: string;
    let number: string;
    if (strippedPhoneNumber.length === 11) {
      internationalCode = strippedPhoneNumber[0];
      areaCode = strippedPhoneNumber.slice(1, 4);
      number = strippedPhoneNumber.slice(4);
    } else {
      internationalCode = "1";
      areaCode = strippedPhoneNumber.slice(0, 3);
      number = strippedPhoneNumber.slice(3);
    }

    cy.get("div#addPhonePopupWidget_PopupWidgetWrapper").within(() => {
      cy.get("select[id$='contactMethod']").select(phoneType);

      cy.get("input[id$='intCode']").clear();
      cy.get("input[id$='intCode']").type(internationalCode);

      cy.get("input[id$='areaCode']").clear();
      cy.get("input[id$='areaCode']").type(areaCode);

      cy.get("input[id$='telephoneNumber']").clear();
      cy.get("input[id$='telephoneNumber']").type(number);

      cy.contains(
        "span[id$='Label']",
        verified ? "Verified" : "Unverified"
      ).click();

      cy.get("input[id$='okButtonBean']").click();
    });

    return this;
  }

  /**
   * Goes through the claim intake process for a given claim.
   * Currently some of the options during the intake process are hard coded, since there are too many options to reasonably account for at this stage.
   * If you need more fine-grained control, use `ClaimantPage.startCreateNotification()`
   * @param claim Generated claim
   * @returns Fineos Absence Case number wrapped into `Cypress.Chainable` type.
   * @example
   * ClaimantPage.visit(claimantSSN)
   *  .createNotification(claim)
   *  .then(fineos_absence_id=>{
   *    //Further actions here...
   *  })
   */
  createNotification(
    claim: ValidClaim,
    options: CreateNotificationOptions = {}
  ): Cypress.Chainable<string> {
    const {
      withholdingPreference,
      employeeId,
      newEmployer,
      taskType,
      absenceStatus = "Known",
    } = options;

    if (!claim.leave_details.reason) {
      throw new Error(`Missing leave reason.`);
    }
    const reason = claim.leave_details.reason as NonNullable<LeaveReason>;
    // Start the intake process, if needed
    this.maybeClickCreateNotification();
    return this.startCreateNotification((notificationDetails) => {
      let nextStep: "nextStep" | "nextBottomStep" = "nextStep";
      if (taskType === "Document Indexing") {
        nextStep = "nextBottomStep";
        notificationDetails.setNotificationSource("Self-Service");
        this.addAddressIfNone();
        this.addEmailIfNone(config("PORTAL_USERNAME"));
      }
      return notificationDetails[nextStep]((occupationDetails) => {
        // "Occupation Details" step.
        if (employeeId && newEmployer) {
          // Asserting Masterplan & EmployeeID info
          occupationDetails.hasMasterPlanText();
          occupationDetails.hasEmployeeID(employeeId);

          // Add new Employer to masterplan
          occupationDetails.addNewEmployer(newEmployer);
        }
        if (taskType === "Document Indexing") {
          occupationDetails.maybePickExistingOccupation(claim.employer_fein);
        }

        // At this point, there's an alert blocking the next button. We'll go ahead and close it.
        ensureAlertPopUpClosed();
        // Only add hours per week if an occupation is selected
        if (claim.hours_worked_per_week) {
          occupationDetails.enterHoursWorkedPerWeek(
            claim.hours_worked_per_week
          );
        }

        // Set the now required occupation fields
        // There's not an easy way to determine the actual hire date for the claimant, so it's easier to just pick one.
        occupationDetails.enterHireDate("2020-01-01");
        occupationDetails.employmentStatus("Active");

        return occupationDetails.nextStep((notificationOptions) => {
          // Choose Request Type
          return notificationOptions
            .chooseTypeOfRequest(reasonToRequestTypeMap[reason])
            .nextStep((reasonOfAbsence) => {
              // Fill reason of absence depending on claim contents.
              const absenceReasonDescription = getAbsenceReasonDescription(
                reason,
                claim.leave_details.reason_qualifier
              );
              if (absenceReasonDescription) {
                reasonOfAbsence.fillAbsenceReason(absenceReasonDescription);
              }

              if (reason === "Care for a Family Member") {
                reasonOfAbsence.fillAbsenceRelationship({
                  relationship_to_employee: "Sibling - Brother/Sister",
                  qualifier_1: "Biological",
                });
              }

              return reasonOfAbsence.nextStep((datesOfAbsence) => {
                assertValidClaim(claim);
                // Add all available leave periods.
                const [startDate, endDate] = getLeavePeriod(
                  claim.leave_details
                );
                const leavePeriodType = extractLeavePeriodType(
                  claim.leave_details
                );
                datesOfAbsence.toggleLeaveScheduleSlider(leavePeriodType);
                if (claim.has_continuous_leave_periods) {
                  datesOfAbsence.addFixedTimeOffPeriod({
                    status: absenceStatus,
                    start: startDate,
                    end: endDate,
                  });
                }
                if (
                  claim.has_reduced_schedule_leave_periods &&
                  claim.leave_details.reduced_schedule_leave_periods
                ) {
                  datesOfAbsence.addReducedSchedulePeriod(
                    absenceStatus,
                    claim.leave_details.reduced_schedule_leave_periods[0]
                  );
                }
                if (
                  claim.has_intermittent_leave_periods &&
                  claim.leave_details.intermittent_leave_periods
                ) {
                  datesOfAbsence.addIntermittentLeavePeriod(startDate, endDate);
                }
                return datesOfAbsence.nextStep((absenceDetails) => {
                  if (!claim?.work_pattern?.work_pattern_type) {
                    throw new Error(`Missing work pattern`);
                  }
                  const workPatternType =
                    claim.work_pattern.work_pattern_type === "Rotating"
                      ? "2 weeks Rotating"
                      : claim.work_pattern.work_pattern_type;
                  if (config("HAS_FR25_1")) {
                    WorkPatternWidget.open()
                      .selectWorkPatternType(workPatternType)
                      .applyStandardWorkWeek(
                        new Date(startDate),
                        new Date(endDate)
                      )
                      .close();
                    absenceDetails.setWorkStateIfUndefined(claim.mass_id);
                  } else {
                    absenceDetails
                      .selectWorkPatternType(workPatternType)
                      .applyStandardWorkWeek()
                      .setWorkStateIfUndefined(claim.mass_id);
                  }
                  return absenceDetails.nextStep((wrapUp) => {
                    // tax withholdings
                    fineos.waitForAjaxComplete();
                    if (withholdingPreference) {
                      cy.get(
                        "input[type='checkbox'][name$='_somSITFITOptIn_CHECKBOX']"
                      ).click();
                    }
                    // must be selected to proceed
                    cy.get(
                      "input[type='checkbox'][name$='_somSITFITVerification_CHECKBOX']"
                    ).click();

                    fineos.waitForAjaxComplete();

                    // Fill military Caregiver description if needed.
                    if (reason === "Military Caregiver") {
                      absenceDetails.addMilitaryCaregiverDescription();
                    }
                    // Skip additional details step if needed
                    if (
                      reason === "Care for a Family Member" ||
                      reason === "Military Exigency Family" ||
                      reason === "Serious Health Condition - Employee" ||
                      reason === "Military Caregiver" ||
                      reason === "Child Bonding"
                    ) {
                      wrapUp.clickNext(20000);
                    }

                    return wrapUp.finishNotificationCreation();
                  });
                });
              });
            });
        });
      });
    });
  }

  maybeClickCreateNotification() {
    cy.get("span.LinkText").then((buttons) => {
      if (buttons.text().includes("Create Notification")) {
        cy.contains("span", "Create Notification").click();
      }
    });
    return this;
  }
  /**
   * Starts the Fineos intake process and executes the given callback once navigated to first meaningful step of the intake.
   * @param cb
   * @returns the return value of `cb`
   */
  startCreateNotification<T>(cb: (step: NotificationDetails) => T): T {
    return cb(new NotificationDetails());
  }

  occupationActiveReset(employmentStatus: string, dateJobEnded: boolean): this {
    cy.get('a[title="Edit Occupation"]').click();
    cy.findByLabelText("Employment Status").select(`${employmentStatus}`);
    if (dateJobEnded) {
      cy.get("input[type=text][id$='_DateJobEnded']")
        .type("{selectAll}{backspace}")
        .click();
    }
    cy.get("#footerButtonsBar input[value='OK']").click();
    return this;
  }

  paymentPreferences(): PaymentPreferencePage {
    onTab("Payment Preferences");
    waitForAjaxComplete();
    return new PaymentPreferencePage();
  }
}

class PaymentPreferencePage {
  add(enabled: boolean | null | undefined = true) {
    if (enabled) {
      cy.get('input[type="submit"][value="Add"]').should("not.be.disabled");
      cy.screenshot("Add button enabled", { capture: "runner" });
    } else {
      cy.get('input[type="submit"][value="Add"]').should("be.disabled");
      cy.screenshot("Add button disabled", { capture: "runner" });
    }
    return this;
  }

  edit(enabled: boolean | null | undefined = true): EditPaymentPreferences {
    if (enabled) {
      cy.get('input[type="submit"][value="Edit"]').click();
    } else {
      cy.get('input[type="submit"][value="Edit"]').should("be.disabled");
      cy.screenshot("Edit button disabled", { capture: "runner" });
    }
    return new EditPaymentPreferences();
  }

  getDefaultPaymentPreference() {
    return cy
      .get("table[id*=PaymentPreferencesForPartyWidget]")
      .contains("Yes") // td marking payment as default
      .parent() // parent row
      .find("td")
      .first()
      .then((cell) => cell.text()); // first column is payment method.
  }
}

class EditPaymentPreferences {
  checkBulkPayee(
    hasPermission: boolean,
    disabledEditButton: boolean | null | undefined = false
  ) {
    if (disabledEditButton) {
      return;
    } else {
      cy.get('input[type="checkbox"][id$="bulkPayee_CHECKBOX"]').then(($el) => {
        if (hasPermission) {
          cy.wrap($el).click();
        } else {
          cy.wrap($el).should("have.attr", "disabled");
        }
      });
    }
  }
}

/**Contains utilities used within multiple pages throughout the intake process */
abstract class CreateNotificationStep {
  /**
   * Submits the current part of the claim intake.
   * @param timeout
   */
  clickNext(timeout = Cypress.config("defaultCommandTimeout")) {
    waitForAjaxComplete();
    cy.get('#navButtons input[value="Next "]', { timeout }).first().click();
  }

  clickBottomNext(timeout = Cypress.config("defaultCommandTimeout")) {
    cy.get('#navButtons input[value="Next "]', { timeout }).last().click();
  }

  /**
   * Safely selects an option for a <select> tag with a given label
   * @param label
   * @param option
   */
  protected chooseSelectOption(label: string, option: string) {
    cy.findByLabelText(label)
      .should((el: JQuery<HTMLElement>) => {
        // Make sure the select has children and is loaded
        expect(el.children().length > 1 || el.children().first().text() !== "")
          .to.be.true;
      })
      .select(option);
    // Wait for ajax
    waitForAjaxComplete();
  }
}

type NotificationSource =
  | "Self-Service"
  | "Phone"
  | "Paper"
  | "Fax"
  | "Email"
  | "Mail/Post"
  | "Takeover File"
  | "Takeover Manual";

class NotificationDetails extends CreateNotificationStep {
  setNotificationSource(source: NotificationSource) {
    cy.findByLabelText("Notification source").select(source);
  }

  /**
   * Submits Notification Details step and navigates to Occupation Details step
   * @param cb
   * @returns the return value of `cb`
   */
  nextStep<T>(cb: (step: OccupationDetails) => T): T {
    this.clickNext();
    return cb(new OccupationDetails());
  }

  nextBottomStep<T>(cb: (step: OccupationDetails) => T): T {
    this.clickBottomNext();
    return cb(new OccupationDetails());
  }
}

export class OccupationDetails extends CreateNotificationStep {
  enterHoursWorkedPerWeek(hoursWorkedPerWeek: number) {
    cy.findByLabelText("Hours worked per week")
      .clear()
      .type(`${hoursWorkedPerWeek}`);
  }

  async addNewEmployer(newEmployer: Employer) {
    cy.findByTestId("employer-select").click();
    if (config("HAS_FR25_1")) {
      // Uses `force: true` because the dropdown collapses and obscures the button
      cy.findByTestId("add-employer-when-one-exists").click({
        force: true,
      });
    } else {
      cy.get("button[value=employerSearch]").click();
    }
    cy.findByLabelText("Identification Number").type(
      newEmployer?.fein?.replace("-", ""),
      {
        delay: 10,
      }
    );
    cy.get('input[type="submit"][value="Search"]').click();
    waitForAjaxComplete();
    fineos.clickBottomWidgetButton("OK");
    cy.get(
      `input[id^="verificationStatusWidget_"][id$="_Verified_GROUP"]`
    ).click();
  }

  hasMasterPlanText() {
    cy.contains("Member & Occupation", { timeout: 30000 }).should("exist");

    cy.get("#masterPlan").should((masterPlanElement) => {
      const text = masterPlanElement.text();
      const isMasterPlan = text.includes("Master Plan") || text.endsWith(" MP");
      expect(isMasterPlan, "Expected to find the master plan.").to.be.true;
    });
  }

  hasEmployeeID(employeeID: string) {
    cy.get(`div[id^="occupationDetailsWidget_"][id$="_employeeId"]`).should(
      (employeeIDElement) => {
        expect(
          employeeIDElement,
          `Expected to find the following employee by '${employeeID}'.`
        ).to.have.text(employeeID);
      }
    );
  }

  /**
   * Attempts to select one of the pre-existing occupations
   * when none was selected by default
   */
  async maybePickExistingOccupation(employerFein: string) {
    cy.wait("@reactRender", { timeout: 30000 });
    cy.findByTestId("employer-select").click();
    // Determine if a default occupation is selected
    cy.get("label").then((labels) => {
      const hasDefaultOccupation = labels
        .text()
        .includes("Hours worked per week");
      cy.log(`hasDefaultOccupation: ${hasDefaultOccupation}`);
      if (!hasDefaultOccupation) {
        // Find an occupation
        // Temporarily attempt to add new employer
        cy.get("button[value=employerSearch]").click();
        // However, use the fein of the existing employer
        cy.findByLabelText("Identification Number").type(
          employerFein.replace("-", ""),
          {
            delay: 10,
          }
        );
        // Search and wait for results
        cy.get('input[type="submit"][value="Search"]').click();
        waitForAjaxComplete();
        // After finding the name of the employer in the results table
        cy.get(`[id$="PartySearchResultListviewWidgetName0"]`).then(
          (employerNameEl) => {
            const employerName = employerNameEl.text();
            // Cancel out of "Adding new employer"
            fineos.clickBottomWidgetButton("Cancel");
            // Select matching existing employer using the fineos name we found
            cy.findByTestId("employer-select").click();
            cy.get(".ant-select-item-option-content")
              .contains(employerName)
              .click({ timeout: 10000 });
          }
        );
        // Verify selection
        this.hasMasterPlanText();
        // Wait
        cy.wait(500);
      }
    });
  }

  /**
   * Submits Occupation Details step and navigates to Notification Options step
   * @param cb
   * @returns the return value of `cb`
   */
  nextStep<T>(cb: (step: NotificationOptions) => T): T {
    this.clickNext();
    return cb(new NotificationOptions());
  }

  nextBottomStep<T>(cb: (step: NotificationOptions) => T): T {
    this.clickBottomNext();
    return cb(new NotificationOptions());
  }

  employmentStatus(status: string) {
    cy.findByLabelText("Employment status").select(`${status}`);
  }

  enterHireDate(hireDate: string) {
    cy.findByLabelText("Date of hire").type(
      `{selectAll}{backspace}${dateToMMddyyyy(hireDate)}{enter}`
    );
  }

  enterDateJobEnded(dateJobEnded: string) {
    cy.findByLabelText("Date job ended").type(
      `${dateToMMddyyyy(dateJobEnded)}{enter}`
    );
  }
}

class NotificationOptions extends CreateNotificationStep {
  chooseTypeOfRequest(type: TypeOfRequestOptions): this {
    cy.contains("div", type).prev().find("input").click();
    waitForAjaxComplete();
    cy.findByText("Request a Leave").should("be.visible");
    return this;
  }

  /**
   * Submits Notification Options step and navigates Reason Of Absence to step
   * @param cb
   * @returns the return value of `cb`
   */
  nextStep<T>(cb: (step: ReasonOfAbsence) => T): T {
    this.clickNext();
    return cb(new ReasonOfAbsence());
  }
}

/**
 * Maps to select inputs available to describe Primary Relationship
 * Add new options to discriminated unions as needed
 */
export type PrimaryRelationshipDescription = {
  relationship_to_employee?: "Sibling - Brother/Sister" | "Child";
  qualifier_1?: "Biological" | "Adopted" | "Foster";
  // @todo - currently unused, uncomment when you need to set
  // this field
  // qualifier_2?: "Equivalent Family Member" | "Opposite Sex" | "Same Sex";
};

class ReasonOfAbsence extends CreateNotificationStep {
  /**
   * Fills out the absence reason select fields with given data
   * @param desc
   */
  fillAbsenceReason(desc: AbsenceReasonDescription): this {
    if (desc.relates_to) {
      this.chooseSelectOption("Absence relates to", desc.relates_to);
    }
    if (desc.reason) {
      this.chooseSelectOption("Absence reason", desc.reason);
    }
    if (desc.qualifier_1) {
      this.chooseSelectOption("Qualifier 1", desc.qualifier_1);
    }
    if (desc.qualifier_2) {
      this.chooseSelectOption("Qualifier 2", desc.qualifier_2);
    }
    return this;
  }

  /**
   * Fills out the Absence Relationships select fields with given data
   * @param relationship
   */
  fillAbsenceRelationship(relationship: PrimaryRelationshipDescription): this {
    cy.get("#leaveRequestAbsenceRelationshipsWidget").within(() => {
      if (relationship.relationship_to_employee) {
        this.chooseSelectOption(
          "Primary Relationship to Employee",
          relationship.relationship_to_employee
        );
      }
      if (relationship.qualifier_1) {
        this.chooseSelectOption("Qualifier 1", relationship.qualifier_1);
      }
      // @todo - currently unused, uncomment when you need to set
      // this field
      // if (relationship.qualifier_2)
      //   this.chooseSelectOption("Qualifier 2", relationship.qualifier_2);
    });
    return this;
  }

  /**
   * Submits Reason Of Absence step and navigates Dates Of Absence to step
   * @param cb
   * @returns the return value of `cb`
   */
  nextStep<T>(cb: (step: DatesOfAbsence) => T): T {
    this.clickNext(5000);
    return cb(new DatesOfAbsence());
  }
}

type AbsenceStatus = "Known" | "Estimated" | "Please select";

type ContinuousLeavePeriod = {
  status: AbsenceStatus;
  /**MM/DD/YYYY */
  start: string;
  /**MM/DD/YYYY */
  end: string;
  /**MM/DD/YYYY */
  last_day_worked?: string;
  /**MM/DD/YYYY */
  return_to_work_date?: string;
};

class DatesOfAbsence extends CreateNotificationStep {
  /**
   * Toggles the control needed to render the Leave Period inputs according to `type`.
   * The control needs to be toggled before attempting to enter the leave period dates, but doesn't need to be toggled more than once.
   * @param type
   */
  toggleLeaveScheduleSlider(
    type: NonNullable<AbsencePeriodResponse["period_type"]>
  ): this {
    const scheduleSliderMap: Record<typeof type, string> = {
      Continuous: "One or more fixed time off periods",
      Intermittent: "Episodic / leave as needed",
      "Reduced Schedule": "Reduced work schedule",
    };
    cy.contains("div.toggle-guidance-row", scheduleSliderMap[type])
      .find("span.slider")
      .click();
    waitForAjaxComplete();
    return this;
  }

  addIntermittentLeavePeriod(start: string, end: string): this {
    // Since the widget also gets re-rendered from time to time, we need to re-query it frequently.
    const withinWidget = (cb: () => unknown) =>
      cy.get(`#captureEpisodicLeaveDetailsWidget`).within(cb);
    withinWidget(() => {
      cy.findByTitle("Add a new episodic absence period").click();
      waitForAjaxComplete();
    });
    withinWidget(() => {
      cy.findByLabelText("Valid from").type(`${dateToMMddyyyy(start)}{enter}`);
      waitForAjaxComplete();
    });
    withinWidget(() => {
      cy.findByLabelText("Valid to").type(`${dateToMMddyyyy(end)}{enter}`);
      waitForAjaxComplete();
    });
    return this;
  }

  addFixedTimeOffPeriod(period: ContinuousLeavePeriod): this {
    // Since the widget also gets re-rendered from time to time, we need to re-query it frequently.
    const withinWidget = (cb: () => unknown) =>
      cy.get(`#timeOffAbsencePeriodDetailsQuickAddWidget`).within(cb);

    // Enter absence status
    withinWidget(() => {
      cy.findByLabelText("Absence status").select(period.status);
      waitForAjaxComplete();
    });

    // Enter leave start and end dates
    withinWidget(() => {
      cy.findByLabelText("Absence start date").type(
        `${dateToMMddyyyy(period.start)}{enter}`
      );
      waitForAjaxComplete();
    });

    withinWidget(() => {
      cy.findByLabelText("Absence end date").type(
        `${dateToMMddyyyy(period.end)}{enter}`
      );
      waitForAjaxComplete();
    });

    // Enter work related dates if specified
    withinWidget(() => {
      if (period.last_day_worked) {
        cy.findByLabelText("Last day worked ").type(
          `${dateToMMddyyyy(period.last_day_worked)}{enter}`
        );
      }
      waitForAjaxComplete();
    });

    withinWidget(() => {
      if (period.return_to_work_date) {
        cy.findByLabelText("Return to work date").type(
          `${dateToMMddyyyy(period.return_to_work_date)}{enter}`
        );
      }
      waitForAjaxComplete();
    });

    // Add the period
    withinWidget(() => {
      cy.findByTitle(`Quick Add`).click();
      waitForAjaxComplete();
    });
    return this;
  }

  private enterReducedWorkHours(
    leave_details: ReducedScheduleLeavePeriods
  ): void {
    const hrs = (minutes: number | null | undefined) => {
      return minutes ? Math.round(minutes / 60) : 0;
    };
    const weekdayInfo = [
      { hours: hrs(leave_details.sunday_off_minutes) },
      { hours: hrs(leave_details.monday_off_minutes) },
      { hours: hrs(leave_details.tuesday_off_minutes) },
      { hours: hrs(leave_details.wednesday_off_minutes) },
      { hours: hrs(leave_details.thursday_off_minutes) },
      { hours: hrs(leave_details.friday_off_minutes) },
      { hours: hrs(leave_details.saturday_off_minutes) },
    ];

    cy.get("input[name*='_hours']").each((input, index) => {
      cy.wrap(input).type(weekdayInfo[index].hours.toString());
    });
  }

  addReducedSchedulePeriod(
    absenceStatus: AbsenceStatus,
    reducedLeavePeriod: ReducedScheduleLeavePeriods
  ): this {
    const withinWidget = (cb: () => unknown) =>
      cy.get(`#reducedScheduleAbsencePeriodDetailsQuickAddWidget`).within(cb);

    withinWidget(() => {
      // Enter absence status
      this.chooseSelectOption("Absence status", absenceStatus);
      waitForAjaxComplete();

      if (reducedLeavePeriod.start_date) {
        // Enter reduced schedule period start/end dates
        cy.findByLabelText("Absence start date")
          .type(`${dateToMMddyyyy(reducedLeavePeriod.start_date)}{enter}`)
          .then(waitForAjaxComplete);
      }
    });
    // After this the entire widget re-renders and we need to re-query for it.
    withinWidget(() => {
      if (reducedLeavePeriod.end_date) {
        cy.findByLabelText("Absence end date")
          .type(`${dateToMMddyyyy(reducedLeavePeriod.end_date)}{enter}`)
          .then(waitForAjaxComplete);
      }
    });
    withinWidget(() => {
      // Enter hours for each weekday
      this.enterReducedWorkHours(reducedLeavePeriod);
      waitForAjaxComplete();
    });
    withinWidget(() => {
      // Submit period
      cy.findByTitle(`Quick Add`).click();
      waitForAjaxComplete();
    });

    return this;
  }

  nextStep<T>(cb: (step: WorkAbsenceDetails) => T): T {
    this.clickNext(5000);
    return cb(new WorkAbsenceDetails());
  }
}

export class WorkPatternWidget extends CreateNotificationStep {
  private constructor() {
    super();
  }

  private static hasExistingWorkPattern() {
    return cy.findByTestId("create-work-pattern-button").then((btn) => {
      return btn.is(":disabled");
    });
  }

  static open() {
    // Wait for the page to finish loading.
    cy.get("div#work-schedule").within(() => {
      cy.contains("Loading...").should("not.exist");
      cy.get(".ant-spin-dot").should("not.exist");
    });
    waitForAjaxComplete();

    this.hasExistingWorkPattern().then((hasWorkPattern) => {
      if (hasWorkPattern) {
        // open the existing work pattern.
        cy.findByTestId("edit-work-pattern-button").click();
      } else {
        cy.findByTestId("create-work-pattern-button").click();
      }
      // This screenshot is a hack to get the work pattern modal to become visible. Without this, the modal is part of the DOM but remains invisible.
      // It's assumed this triggers some repaint/re-render. Raw waits or waiting on inflight requests do not work here.
      // This currently effects the e2e/cypress/specs/morning/scenario/portal_medical_continuous__userNotFound.ts test
      // https://lwd.atlassian.net/browse/PFMLPB-24944
      cy.get(".ant-modal-body").screenshot({
        log: false,
        clip: { x: 0, y: 0, width: 0, height: 0 },
      });
      cy.get(".ant-modal-body").should("be.visible");
    });

    return new WorkPatternWidget();
  }

  selectWorkPatternType(type: WorkPatternType) {
    // need to click this element to bring up a list, then click from the list.
    cy.get("input#workPatternType").click({ force: true }); // element is being hidden under the span containing the actual text.
    cy.contains(type).click();
    return this;
  }

  applyWorkingDayDuration(time: string, startDate: Date, endDate: Date) {
    cy.get('[id*="workingHourPattern"]').each((el) => {
      cy.wrap(el).type(`{selectAll}{backspace}${time}`);
    });
    this.enterDates(startDate, endDate);
    return this;
  }

  applyStandardWorkWeek(startDate: Date, endDate: Date) {
    this.enterDates(startDate, endDate);
    cy.findByTestId("apply-standard-work-week").click();
    return this;
  }

  close() {
    cy.get(".ant-modal-footer").within(() => {
      cy.findByRole("button", { name: "OK" }).click();
    });
    cy.get(".ant-modal-wrap").should("not.exist");
  }

  private enterDates(startDate: Date, endDate: Date) {
    const formattedStartDate = format(startDate, "MM/dd/yyyy");
    const formattedEndDate = format(endDate, "MM/dd/yyyy");
    cy.findByTestId("pattern-start-date")
      .focus()
      .type(`{selectAll}{backspace}${formattedStartDate}{enter}`);
    cy.findByTestId("pattern-end-date")
      .focus()
      .type(`{selectAll}{backspace}${formattedEndDate}{enter}`);
  }
}

type WorkPatternType =
  | "Unknown"
  | "Fixed"
  | "2 weeks Rotating"
  | "3 weeks Rotating"
  | "4 weeks Rotating"
  | "Variable";

class WorkAbsenceDetails extends CreateNotificationStep {
  selectWorkPatternType(type: WorkPatternType): this {
    this.chooseSelectOption("Work Pattern Type", type);
    wait();
    return this;
  }

  applyStandardWorkWeek(): this {
    cy.findByLabelText("Standard Work Week").click();
    wait();
    cy.get('input[value="Apply to Calendar"]').click();
    return this;
  }

  applyWorkingDayDuration(time: string): this {
    cy.get('table[class="workPattern"] td').each(($el) => {
      cy.wrap($el)
        .children()
        .then((els) => {
          [...els].forEach((e) => {
            cy.wrap(e).type(`{selectall}{backspace}${time}`);
          });
        });
    });
    cy.get('input[type="submit"][value="Apply to Calendar"]').click();
    return this;
  }

  addMilitaryCaregiverDescription(): this {
    cy.findByLabelText("Military Caregiver Description").type(
      "I am a parent military caregiver."
    );
    return this;
  }

  /**
   * Used by CPS testing for configuration check. We checking for Work Pattern must be
   * populated in the FINEOS intake before continuing the process.
   */
  checkWorkPatternPopulated(): this {
    // Needs to be Unknown work pattern type
    this.chooseSelectOption("Work Pattern Type", "Unknown");
    wait();
    // Click the next button to show the message below
    this.clickNext(20000);
    fineos.assertErrorMessage(
      "Work Pattern must be populated. Total hours per week in the Work Pattern must equal the Hours Worked Per Week field. Populate the Work Pattern and click Apply to Calendar before proceeding."
    );
    cy.screenshot("CPS-906-AA(CPS-2579) Work Pattern Message", {
      capture: "runner",
    });
    return this;
  }

  editEmploymentLeaveDetails<T>(
    cb: (employmentLeaveDetails: EmploymentLeaveDetails) => T
  ) {
    cy.get("[id$=editEmploymentLeaveDetailsLink]").click();
    waitForAjaxComplete();
    cb(new EmploymentLeaveDetails());
    fineos.clickBottomWidgetButton("OK");
  }

  setWorkStateIfUndefined(massId: ValidClaim["mass_id"]) {
    this.getWorkState().then((state) => {
      if (state === "Unknown") {
        this.editEmploymentLeaveDetails((employmentLeaveDetails) => {
          employmentLeaveDetails.chooseWorkState(massId ? "MA" : "CT");
        });
      }
    });
  }

  getWorkState(): Cypress.Chainable<string> {
    return cy.get("[id$=_employmentWorkState]").invoke("text");
  }

  nextStep<T>(cb: (step: WrapUp) => T): T {
    this.clickNext(20000);
    return cb(new WrapUp());
  }
}

class EmploymentLeaveDetails extends CreateNotificationStep {
  chooseWorkState(state: string) {
    this.chooseSelectOption("USA Work State", state);
  }
}
class WrapUp extends CreateNotificationStep {
  /**Looks for the Leave Case number in the Wrap Up step and returns it wrapped by Cypress. */
  private getLeaveCaseNumber() {
    const caseNumberMatcher = /NTN-[0-9]+-[A-Z]{3}-[0-9]{2}/g;
    return cy
      .findByText(/Absence Case - NTN-[0-9]+-[A-Z]{3}-[0-9]{2}/g)
      .then((el) => {
        const match = el.text().match(caseNumberMatcher);
        if (!match) {
          throw new Error(
            `Couldn't find the Case Number on intake Wrap Up page.`
          );
        }
        return cy.wrap(match[0]);
      });
  }

  /**Captures the Leave Case id number and exits the notification creation process */
  finishNotificationCreation(): Cypress.Chainable<string> {
    return this.getLeaveCaseNumber().then((absenceId) => {
      if (config("HAS_FR25_1")) {
        cy.get("input[title='Finish']").first().click();
        waitForAjaxComplete();
      } else {
        this.clickBottomNext(20_000);
      }
      cy.contains(absenceId);
      return cy.wrap(absenceId);
    });
  }

  selectWithholdingPreference(withholdingPreference?: boolean) {
    if (withholdingPreference) {
      cy.get(
        "input[type='checkbox'][name$='_somSITFITOptIn_CHECKBOX']"
      ).click();
    }
    cy.get(
      "input[type='checkbox'][name$='_somSITFITVerification_CHECKBOX']"
    ).click();
    return this;
  }
}
