import { addDays, differenceInCalendarDays, format } from "date-fns";

import { differenceInBusinessDays } from "../../../global.common";
import {
  FineosCloseTaskStep,
  FineosTask,
  FineosTaskCheck,
  FineosTasksStatus,
  SlaDefinition,
} from "../../../src/types";
import { fineos } from "..";
import { clickBottomWidgetButton, onTab, waitForAjaxComplete } from "../fineos";

export class TasksPage {
  /**
   * Called from the tasks page, asserts that a particular task is found.
   * @param name
   */
  assertTaskExists(name: FineosTask): this {
    cy.get("table[id*='TasksForCaseWidget']").should((table) => {
      expect(table, `Expected to find a "${name}" task`).to.have.descendants(
        `tr td:nth-child(6)[title="${name}"]`
      );
    });
    return this;
  }

  assertTaskStatus(name: FineosTask, status: FineosTasksStatus): this {
    this.all();
    cy.get("table[id*='TasksForCaseWidget']").should((table) => {
      expect(table, `Expected to find a "${name}" task`).to.have.descendants(
        `tr td:nth-child(7)[title="${status}"]`
      );
    });
    return this;
  }

  /**
   * Adds a task to a claim and asserts it has been assigned to DFML Program Integrity
   * @param name name of the task to be added
   */
  add(name: FineosTask): this {
    cy.findByTitle(`Add a task to this case`).click();
    // Search for the task type
    cy.findByLabelText(`Find Work Types Named`).type(`${name}{enter}`);
    // Create task
    cy.findByTitle(name).click();
    clickBottomWidgetButton("Next");
    return this;
  }

  assertTasksNotPresent(task: string[]): this {
    task.forEach((task) => {
      cy.findByTitle(`Add a task to this case`).click({ force: true });
      cy.findByLabelText(`Find Work Types Named`).type(`${task}{enter}`);
      waitForAjaxComplete();
      cy.get("#NameChooseWorkTypeWidget").should("not.contain.text", task);
      clickBottomWidgetButton("Cancel");
    });

    return this;
  }

  close(name: FineosTask): this {
    cy.contains("td", name).click();
    waitForAjaxComplete();
    cy.get('input[title="Close selected task"]').click();
    waitForAjaxComplete();
    return this;
  }

  checkAndCloseOverlappingAbsenceTask(): this {
    cy.get("#TasksForCaseListViewWidget").then((el) => {
      if (el.text().includes("Overlapping Absence Request Exists")) {
        this.close("Overlapping Absence Request Exists");
        this.all();
        this.assertTaskStatus("Overlapping Absence Request Exists", "Closed");
      }
    });
    return this;
  }

  select(task: string): this {
    // Find  task
    cy.contains("tbody", "This case and its subcases").within(() => {
      cy.findAllByText(task).first().click();
    });
    cy.get(
      `[id^="activitytabs"][id$="FINEOS.WorkManager.Activities.ViewActivityDialogue.Task"]`
    ).should("contain.text", task);
    return this;
  }

  /**
   * Asserts that Service Level Agreement days match provided definition.
   */
  checkSLADifference(definition: SlaDefinition): this {
    const { days, isInBusinessDays, task } = definition;
    this.select(task);

    cy.get(
      `[id^="activitytabs"][id$="FINEOS.WorkManager.Activities.ViewActivityDialogue.Task"]`
    ).then((el) => {
      const [creationDate, creationTime] = el
        .find(`[id^="BasicDetailsUsersDeptWidget"][id$="_CreationDate"]`)
        .text()
        .split(" ");

      const [targetDate] = el
        .find(`[id^="ActivityTargetDates"][id$="_TargetDate"]`)
        .text()
        .split(" ");

      if (isInBusinessDays) {
        const daysToAssert = creationTime >= "17:00" ? days + 1 : days;

        expect(
          differenceInBusinessDays(new Date(targetDate), new Date(creationDate))
        ).to.eq(daysToAssert);
      } else {
        expect(
          differenceInCalendarDays(new Date(targetDate), new Date(creationDate))
        ).to.eq(days);
      }
    });

    return this;
  }

  checkTasksAvailable(
    taskChecks: readonly FineosTaskCheck[],
    taskSection: string,
    isManual: boolean
  ): this {
    taskChecks.forEach((taskCheck) => {
      this.checkTaskAvailable(taskCheck, taskSection, isManual);
    });

    return this;
  }

  /**
   * This is being used for Configuration testing in FINEOS
   * @param taskSection - Used to document in a text file what has ran when manually running this method
   * @param isManual - If we are running it locally it would be manually. This has screenshots and text file with all tasks listed.
   * It will also not fail if task is missing so we need to manually check each screenshot.
   */
  private checkTaskAvailable(
    taskCheck: FineosTaskCheck,
    taskSection: string,
    isManual: boolean
  ): this {
    cy.findByTitle(`Add a task to this case`).click();

    // Search for the task type
    cy.findByLabelText(`Find Work Types Named`)
      .clear()
      .type(`${taskCheck.name}{enter}`);

    if (isManual) {
      this.screenshotTask(taskSection);
    } else {
      this.addTask(taskCheck);
    }

    return this;
  }

  private screenshotTask(taskSection: string) {
    cy.on("fail", (e) => {
      if (e.message.includes(`No results found`)) {
        cy.get(`span[id^="PageMessage1"]`).should(
          "contain.text",
          "No results found"
        );
      } else {
        throw e;
      }
    });

    const imageName = taskSection + " " + name;

    // Found in the root directory of e2e folder.
    const filename = "tasksname.txt";
    cy.writeFile(filename, imageName + "\n", { flag: "a+" });
    cy.screenshot(imageName, { capture: "runner" });
  }

  private addTask(taskCheck: FineosTaskCheck) {
    cy.findByTitle(taskCheck.name).click();

    if (taskCheck.beforeNext) {
      taskCheck.beforeNext();
    }

    clickBottomWidgetButton("Next");

    if (taskCheck.afterNext) {
      taskCheck.afterNext();
    }
  }

  assignToDepartment(task: FineosTask, department: string): this {
    this.select(task);
    cy.get("[id*=openTaskButton]").click();
    waitForAjaxComplete();
    cy.get('span[id="DROPDOWNMENU.CasesTransferDropdown"]').click();
    // force: true is required because some Cypress issue with parent element having display:none
    cy.contains("span", "Transfer to Department").click({ force: true });
    cy.contains(department).click();
    waitForAjaxComplete();
    cy.get("#footerButtonsBar input[value='OK']").click();
    waitForAjaxComplete();

    cy.get("[id$='_UserTaskTransferRecord']").within(() => {
      cy.get(".ActivityTransferResult").should(
        "contain.text",
        `Transferred to ${department}:`
      );
      cy.get(".popup_buttons > input[type='submit']").click();
    });

    cy.get("#footerButtonsBar input[value='Close']").click();
    waitForAjaxComplete();

    return this;
  }

  assertIsAssignedToUser(task: FineosTask, user: string): this {
    this.select(task);
    // Assert it's assigned to given user
    cy.get(`span[id^="BasicDetailsUsersDeptWidget"][id$="AssignedTo"]`).should(
      "contain.text",
      `${user}`
    );
    return this;
  }

  assertIsAssignedToDepartment(task: FineosTask, department: string): this {
    // Find  task
    this.select(task);
    // Assert it's in given department
    cy.get(`span[id^="BasicDetailsUsersDeptWidget"][id$="Department"]`).should(
      "contain.text",
      `${department}`
    );
    return this;
  }

  editActivityDescription(name: string, comment: string): this {
    cy.contains("td", name).click();
    cy.wait("@ajaxRender");
    cy.get('input[title="Edit this Activity"').click();
    cy.wait("@ajaxRender");
    cy.get("textarea[name$='Description']").type(comment);
    cy.get("#footerButtonsBar input[value='OK']").click();
    return this;
  }

  closeAppealReview(attemptNumber: number): this {
    attemptNumber > 0
      ? cy
          .findAllByText("Appeal", { selector: "a" })
          .first()
          .parent()
          .next()
          .click()
      : cy.findByText("Appeal", { selector: "a" }).click();
    waitForAjaxComplete();
    cy.contains("td", "Review Appeal").click();
    waitForAjaxComplete();
    cy.get('input[title="Close selected task"]').click();
    waitForAjaxComplete();
    fineos.clickBottomWidgetButton("OK");
    waitForAjaxComplete();
    return this;
  }

  closeConductHearing(attemptNumber: number): this {
    attemptNumber > 0
      ? cy
          .findAllByText("Appeal", { selector: "a" })
          .first()
          .parent()
          .next()
          .click()
      : cy.findByText("Appeal", { selector: "a" }).click();
    waitForAjaxComplete();
    cy.contains("td", "Make Decision").click();
    waitForAjaxComplete();
    cy.contains("td", "Closed - Claim Decision Changed").click();
    waitForAjaxComplete();
    fineos.clickBottomWidgetButton("OK");
    waitForAjaxComplete();
    return this;
  }

  closeWithAdditionalSelection(
    name: FineosTask,
    selection: FineosCloseTaskStep
  ): this {
    cy.get(`table[id*="TasksForCaseWidget"]`).findByText(name).click();
    waitForAjaxComplete();
    cy.get('input[title="Close selected task"]').click();
    waitForAjaxComplete();
    cy.contains("td", selection).click();
    waitForAjaxComplete();
    fineos.clickBottomWidgetButton("OK");
    waitForAjaxComplete();
    return this;
  }

  all(): this {
    cy.get("input[type='radio'][value$='_allTasks']").click();
    waitForAjaxComplete();
    return this;
  }

  returnSubTasksTab(): this {
    cy.get(
      'td[id$="_FINEOS.WorkManager.Activities.ViewTasks.AbsenceCase_TasksView_cell"]'
    ).click();
    return this;
  }

  putOnHold(name: FineosTask, offset: Date | string, reason: string): this {
    this.select(name);
    cy.get("[id*=openTaskButton]").click();
    waitForAjaxComplete();
    cy.findByText("Put On Hold").click();
    cy.findByLabelText("On Hold Until")
      .focus()
      .type(`{selectAll}{backspace}${offset}`);
    cy.get("select[id*='OnHoldReasonEnum']").select(reason);

    cy.get("#PageFooterWidget").contains("OK").click();
    cy.get("#PageFooterWidget").contains("Close").click();
    waitForAjaxComplete();
    return this;
  }

  takeOffHold(
    name: FineosTask,
    reason: string,
    offholdDescription: string
  ): this {
    onTab("Tasks");
    this.select(name);
    cy.get("[id*=openTaskButton]").click();
    waitForAjaxComplete();
    cy.findByText("Take Off Hold").click();
    cy.findByLabelText("Off Hold Reason").select(reason);
    cy.findByLabelText("Description").type(offholdDescription);
    cy.get("#PageFooterWidget").contains("OK").click();
    waitForAjaxComplete();
    cy.findByText("Edit").click();
    const targetDate = addDays(new Date(), 2);
    cy.findByLabelText("Target Date").type(
      `{selectAll}{backspace}${format(targetDate, "MM/dd/yyyy")}{enter}`
    );
    cy.get("#PageFooterWidget").contains("OK").click();
    waitForAjaxComplete();
    cy.get("#PageFooterWidget").contains("Close").click();
    waitForAjaxComplete();
    return this;
  }

  assertDoesNotExist(name: FineosTask): this {
    cy.get("table[id*='TasksForCaseWidget']").within(() => {
      cy.get(`tr td:nth-child(6)[title="${name}"]`).should("not.exist");
    });
    return this;
  }
}
