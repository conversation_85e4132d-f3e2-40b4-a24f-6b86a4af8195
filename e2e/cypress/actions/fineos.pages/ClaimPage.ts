import { format } from "date-fns";

import { DehydratedDocument } from "../../../src/generation/documents";
import {
  FineosCorrespondenceType,
  FineosRMVCheckStatus,
} from "../../../src/types";
import { fineos } from "..";
import {
  assertMissingPaymentInformationAlert,
  clickBottomWidgetButton,
  clickNext,
  ensureAlertPopUpClosed,
  isAlertPopUpOpen,
  onTab,
  visitClaim,
  wait,
  waitForAjaxComplete,
} from "../fineos";
import { AdjudicationPage } from "./AdjudicationPage";
import { AlertsPage } from "./AlertsPage";
import { BenefitsExtensionPage } from "./BenefitsExtensionPage";
import { CaseNotesBar } from "./CaseNotesBar";
import { DocumentsPage } from "./DocumentsPage";
import { HistoricalAbsence } from "./HistoricalAbsence";
import { LeaveDetailsPage } from "./LeaveDetailsPage";
import { NotesPage } from "./NotesPages";
import { OutstandingRequirementsPage } from "./OutstandingRequirementsPage";
import { PaidLeavePage } from "./PaidLeave";
import { RecordActualTime } from "./RecordActualTime";
import { RecordActualTimeCalendar } from "./RecordActualTimeCalendar";
import { TasksPage } from "./TasksPage";

type StatusCategory =
  | "Applicability"
  | "Eligibility"
  | "Evidence"
  | "Availability"
  | "Restriction"
  | "Protocols"
  | "PlanDecision";

export class ClaimPage {
  static visit(
    id: string,
    ignorePotentialConcurrentAbsenceAlert = false
  ): ClaimPage {
    visitClaim(id);
    ignorePotentialConcurrentAbsenceAlert && ensureAlertPopUpClosed();
    return new ClaimPage();
  }

  // The Preliminary Designation is triggered to resolve a race
  // condition where the one-minute time trigger on this notice can
  // result in an "Out of Date Data" error when attempting to edit the
  // leave period dates.
  static visitAndTriggerPreliminaryDesignation(id: string) {
    const claimPage = ClaimPage.visit(id);
    claimPage.triggerNotice("Preliminary Designation");
    claimPage.resetTasksTab();
    onTab("Absence Hub");
    return claimPage;
  }

  addHistoricalAbsenceCase(): HistoricalAbsence {
    return HistoricalAbsence.create();
  }

  assertRMVCheckStatus(status: FineosRMVCheckStatus): this {
    // Even though this element has a label, it's rendered as a div, which
    // isn't considered a labelable element per the HTML5 spec.
    // https://html.spec.whatwg.org/multipage/forms.html#category-label
    cy.get("[id*='_identificationStatus']").should(
      "contain.text",
      `Verification check ${status}`
    );
    return this;
  }

  recordActualLeave<T>(cb: (page: RecordActualTime) => T): T {
    // Start the submission process.
    cy.findByText("Record Actual").click();
    waitForAjaxComplete();
    return cb(new RecordActualTime());
  }

  recordActualLeaveCalendar<T>(cb: (page: RecordActualTimeCalendar) => T): T {
    waitForAjaxComplete();
    return cb(new RecordActualTimeCalendar());
  }

  shouldHaveEpisodicLeave(
    date: Date,
    hours: number,
    status: "Pending" | "Approved"
  ) {
    const latestLeaveEpisodesBlock = cy
      .contains("#episodicSummaryCardWidget label", "Latest leave episodes")
      .parent()
      .parent();

    // Ex: Sunday, January 14th
    const formattedDate = format(date, "eeee, MMMM do");
    // There are no suitable selectors for the date, hours, or status.
    const template = new RegExp(
      `${formattedDate} - ${hours} hours\\s+\\(${status}\\)`
    );
    latestLeaveEpisodesBlock.invoke("text").should("match", template);
  }

  paidLeave(cb: (page: PaidLeavePage) => unknown): this {
    cy.findAllByText("Absence Paid Leave Case", { selector: "a" })
      .first()
      .focus()
      .click();
    waitForAjaxComplete();
    cy.wait(200);
    cb(new PaidLeavePage());
    cy.findByText("Absence Case", { selector: "a" }).click();
    waitForAjaxComplete();
    cy.wait(1500);
    return this;
  }

  // FINEOS changed for In Review workflow after the April upgrade 2022.
  adjudicateInReview(cb: (page: AdjudicationPage) => unknown): this {
    cb(new AdjudicationPage());
    cy.get("#footerButtonsBar input[value='OK']").click();

    return this;
  }

  adjudicate(cb: (page: AdjudicationPage) => unknown): this {
    waitForAjaxComplete();
    cy.get('input[type="submit"][value="Adjudicate"]', { timeout: 15000 })
      .should("be.visible")
      .click();
    cb(new AdjudicationPage());
    waitForAjaxComplete();
    cy.get("#footerButtonsBar input[value='OK']").click();
    return this;
  }

  /**
   * Receives evidence, prefills certification periods, and optionally accepts
   * the leave plan.
   */
  completeAdjudication(
    evidenceDocuments: readonly DehydratedDocument[],
    options: { acceptLeavePlan?: boolean } = {}
  ): ClaimPage {
    return this.adjudicate((adjudication) => {
      adjudication
        .evidence((evidence) => {
          evidenceDocuments.forEach((document) => {
            evidence.receive(document.document_type);
          });
        })
        .certificationPeriods((certPeriods) => certPeriods.prefill())
        .acceptLeavePlan(options.acceptLeavePlan ?? true);
    });
  }

  viewRequest(cb: (page: AdjudicationPage) => unknown): this {
    cy.get('input[type="submit"][value="View Request"]').click();
    cb(new AdjudicationPage());
    cy.get("#footerButtonsBar input[value='Close']").click();
    return this;
  }

  tasks(cb: (page: TasksPage) => unknown): this {
    onTab("Tasks");
    cb(new TasksPage());
    onTab("Absence Hub");
    return this;
  }

  appealDocuments(cb: (page: DocumentsPage) => unknown): this {
    onTab("Documents");
    cb(new DocumentsPage());
    return this;
  }

  appealTasks(cb: (page: TasksPage) => unknown): this {
    onTab("Tasks");
    cb(new TasksPage());
    return this;
  }

  documents(cb: (page: DocumentsPage) => unknown): this {
    onTab("Documents");
    cb(new DocumentsPage());
    onTab("Absence Hub");
    return this;
  }

  alerts(cb: (page: AlertsPage) => unknown): this {
    onTab("Alerts");
    cb(new AlertsPage());
    onTab("Absence Hub");
    return this;
  }

  caseNotesBar(cb: (bar: CaseNotesBar) => void): this {
    cb(new CaseNotesBar());
    return this;
  }

  outstandingRequirements(
    cb: (page: OutstandingRequirementsPage) => unknown
  ): this {
    ensureAlertPopUpClosed();
    onTab("Outstanding Requirements");
    cb(new OutstandingRequirementsPage());
    onTab("Absence Hub");
    return this;
  }

  notes(cb: (page: NotesPage) => unknown): this {
    onTab("Notes");
    cb(new NotesPage());
    onTab("Absence Hub");
    return this;
  }

  leaveDetails(cb: (page: LeaveDetailsPage) => unknown): this {
    onTab("Leave Details");
    cb(new LeaveDetailsPage());
    return this;
  }

  benefitsExtension(cb: (page: BenefitsExtensionPage) => unknown): this {
    cy.findByText("Add Time").click();
    cb(new BenefitsExtensionPage());
    return this;
  }

  shouldHaveStatus(category: StatusCategory, expected: string): this {
    const selector =
      category === "PlanDecision"
        ? `.divListviewGrid .ListTable td[id*='ListviewWidget${category}0']`
        : `.divListviewGrid .ListTable td[id*='ListviewWidget${category}Status0']`;
    cy.get(selector).should((element) => {
      expect(
        element,
        `Expected claim's "${category}" to be "${expected}"`
      ).to.have.text(expected);
    });
    return this;
  }

  triggerNotice(
    type:
      | "Designation Notice"
      | "SOM Generate Legal Notice"
      | "Leave Request Declined"
      | "Leave Request Withdrawn"
      | "Review Approval Notice"
      | "Leave Cancellation Request"
      | "Preliminary Designation"
      | "SOM Auto Approve Leave Request"
      | "SOM Generate Appeals Notice"
      | "SOM Generate Editable Notice"
      | "Appeal Dismissed - Other"
      | "Appeal Dismissed - Exempt Employer"
      | "Appeal - Returned to Adjudication"
      | "Appeal Hearing Virtual Fillable"
      | "Appeal Withdrawn"
      | "Modify Decision"
      | "SOM Generate Employer Reimbursement Notice"
      | "Send Decision Notice"
      | "Review Denial Notice"
      | "Leave Allotment Change Notice"
      | "Intermittent Time Request Decision"
      | "Approval Notice Explanation of Wages"
      | "Denial Notice Explanation of Wages"
      | "Explanation of Wages"
  ): this {
    const docRegex = new RegExp(`^${type}`);
    onTab("Task");
    onTab("Processes");
    cy.contains(".TreeNodeElement", docRegex).click({
      force: true,
    });
    waitForAjaxComplete();
    cy.wait(250);
    // When we're firing time triggers, there's always the possibility that the trigger has already happened
    // by the time we get here. When this happens, the "Properties" button will be grayed out and unclickable.
    cy.get('input[type="submit"][value="Properties"]').then((el) => {
      if (el.is(":disabled")) {
        cy.log("Skipping trigger because this time trigger has already fired");
        return;
      }
      cy.wrap(el).click();
      waitForAjaxComplete();
      cy.get("body").then((element) => {
        if (element.find('input[type="submit"][value="Continue"]').length > 0) {
          cy.wait(1000);
          cy.get('input[type="submit"][value="Continue"]').first().click();
        } else {
          cy.get('input[type="submit"][value="Close"]').first().click();
        }
        waitForAjaxComplete();
        cy.wait(2000);
        if (type === "Preliminary Designation") {
          // It's possible to encounter an alert if the case has been updated
          // by the automatic trigger for the Preliminary Designation notice type
          // while Cypress manually triggers the task i.e. https://onenr.io/0qwLdLLkmw5
          // If this happens we can close the alert and page to assert
          // that the notice has been triggered
          isAlertPopUpOpen().then((alertPopUpIsOpen) => {
            if (alertPopUpIsOpen) {
              ensureAlertPopUpClosed();
              // Then click the close button
              cy.get('input[type="submit"][value="Close"]').first().click();
            }
          });
        }
      });
    });
    return this;
  }

  assertClaimStatus(expected: string, hasDeniedExtension = false) {
    const selector = hasDeniedExtension
      ? "label[id*='deniedLeaveDescription']"
      : ".key-info-bar .status dd";
    cy.get(selector).should("contain.text", expected);
    return this;
  }

  approve(status: "Approved" | "Completed" = "Approved"): this {
    cy.get('a[title="Approve the pending/in review leave request"]')
      .focus()
      .click();
    waitForAjaxComplete();
    cy.wait(500);
    cy.get("body").then(($body) => {
      if ($body.find("div[id*='page_messages_container']").length > 0) {
        assertMissingPaymentInformationAlert();
      }
    });
    ensureAlertPopUpClosed();
    this.assertClaimStatus(status);
    return this;
  }

  deny(reason: string, assertStatus = true, denyextension: boolean): this {
    cy.get("input[type='submit'][value='Adjudicate']").click();
    // Make sure the page is fully loaded by waiting for the leave plan to show up.
    cy.get("table[id*='selectedLeavePlans'] tr")
      .should("have.length", 1)
      .click();
    waitForAjaxComplete();
    // wait for buttons to be rendered before clicking
    cy.wait("@ajaxRender");
    cy.wait(500);
    cy.get("#button-reject").should("be.visible").click();
    cy.get("#dropdown-reject-reason").click();
    cy.get("[title='Employee not eligible']").click();
    cy.get("#moval-evaluate-ok").click().wait(250);
    waitForAjaxComplete();
    clickBottomWidgetButton("OK");
    const selector = 'a[title="Deny the pending/in review leave request"]';
    cy.get(selector).click();
    cy.get('span[id="leaveRequestDenialDetailsWidget"]')
      .find("select")
      .select(reason);
    cy.get('input[type="submit"][value="OK"]').click();
    // denying an extension for another reason will cause this assertion to fail
    assertStatus &&
      this.assertClaimStatus(
        denyextension ? "Previously denied" : "Declined",
        denyextension
      );
    return this;
  }

  denyAppeal(): this {
    cy.findByText("Absence Case", { selector: "a" }).click();
    waitForAjaxComplete();
    cy.get("#link_Options").siblings("a").contains("Options").click();
    cy.contains("Appeal Denial").focus().click();
    waitForAjaxComplete();
    this.assertClaimStatus("Adjudication");
    waitForAjaxComplete();
    cy.wait(500);

    return this;
  }

  addActivity(activity: string): this {
    cy.get("[id^=MENUBAR\\.CaseSubjectMenu]")
      .findByText("Add Activity")
      .click()
      .parents("li")
      .findByText(activity)
      .parent()
      .focus()
      .click();
    return this;
  }

  addCorrespondenceDocument(action: FineosCorrespondenceType): this {
    const document = new DocumentsPage();

    cy.get("[id^=MENUBAR\\.CaseSubjectMenu]")
      .findByText("Correspondence")
      .click()
      .wait(1000)
      .parents("li")
      .findByText(action)
      .parent()
      .focus()
      .dblclick()
      .then(wait);

    // These are automatically generated by FINEOS, and don't require an
    // additional file upload.
    const generatedDocuments: FineosCorrespondenceType[] = [
      "Leave Allotment Change Notice",
      "Employer Reimbursement Denial Notice",
      "Employer Reimbursement Approval Notice",
    ];

    if (generatedDocuments.includes(action)) {
      cy.get("#footerButtonsBar input[value='Next']").click();
    } else {
      document.uploadDocumentAlt(action);
    }
    return this;
  }

  // This will deny extended time in the Leave Details.
  // No assert ClaimStatus for Declined for the absence case
  // won't say "Declined".
  denyExtendedTime(reason: string): this {
    const selector = 'a[title="Deny the pending/in review leave request"]';
    waitForAjaxComplete();
    cy.get("table[id$='leaveRequestListviewWidget']").within(() => {
      cy.get("tr.ListRowSelected").click();
    });
    cy.get(selector).click();
    cy.get('span[id="leaveRequestDenialDetailsWidget"]')
      .find("select")
      .select(reason);
    cy.get('input[type="submit"][value="OK"]').click();
    return this;
  }

  addAppeal(): this {
    cy.get('a[title="Add Sub Case"]').click();
    waitForAjaxComplete();
    cy.wait(200);
    cy.get('a[title="Create Appeal"]').focus().click();
    waitForAjaxComplete();
    cy.wait(200);
    return this;
  }

  checkOrgUnitAdminGroup(adminGroup: string): this {
    fineos.onTab("General");
    cy.get(`div[id$="_adminGroup"]`).should((element) => {
      expect(
        element,
        "Organization Unit should have the `${adminGroup}`"
      ).to.have.text(`${adminGroup}`);
    });
    return this;
  }

  addEmployer(employer_fein: string): this {
    //Select employer from drop down menu
    cy.get("[id^=MENUBAR\\.CaseSubjectMenu]")
      .findByText("Add Participant")
      .click()
      .parents("li")
      .findByText("Employer")
      .parent()
      .focus()
      .click()
      .wait(250);

    //change radio to Organization
    cy.contains("label", "Organization").click();
    // input employer FEIN
    cy.get('input[type="text"][id$=_Social_Security_No\\._\\(SSN\\)]').type(
      employer_fein?.replace("-", "")
    );
    //Search
    cy.get('input[type="submit"][value="Search"]').click();
    waitForAjaxComplete();
    cy.get('input[type="submit"][value="OK"][id$=_searchPageOk]').click();
    return this;
  }

  withdraw(): this {
    cy.get('a[title="Withdraw the Pending Leave Request"').click();
    cy.get("#leaveRequestWithdrawPopupWidget_PopupWidgetWrapper").within(() => {
      cy.findByLabelText("Withdrawal Reason").select("Employee Withdrawal");
      cy.findByText("OK").click();
    });
    this.assertClaimStatus("Closed");
    return this;
  }

  reviewClaim(): this {
    onTab("Leave Details");
    cy.contains("td", "Approved").click();
    // Note in the new workflow for putting a claim into Review we are going directly to the Adjudication
    // instead of click on the Absence Hub. Then clicking the Adjudication button on the Absence Hub tab.
    cy.contains("button", "Review").click();
    cy.wait("@reactRender");
    cy.get(`.ant-modal`)
      .should("be.visible")
      .within(() => {
        cy.get('input[id="secondOption"][type="radio"]').click();
        cy.contains("button", "OK").click();
      });
    cy.get("body").should("not.contain", ".ant-modal");
    cy.url().should("contain", "editleaverequest", { timeout: 20000 });
    cy.contains(
      'span[id$="_openReviewDocumentLabel"]',
      "The leave request is now in review"
    );
    waitForAjaxComplete();
    cy.wait(500);
    return this;
  }

  recordCancellation(): this {
    const recordCancelledTime = () => {
      cy.contains("td", "Known").click();
      cy.get(
        'input[title="Record Cancelled Time for the selected absence period"]'
      ).click();
      waitForAjaxComplete();
      cy.get('input[type="submit"][value="OK"]').click();
      waitForAjaxComplete();
      clickNext();
      waitForAjaxComplete();
      fineos.clickNext();
      waitForAjaxComplete();
    };
    const additionalReportingInformation = () => {
      cy.get('select[id$="reportedBy"]').select("Employee");
      waitForAjaxComplete();
      cy.get('select[id$="receivedVia"]').select("Phone");
      waitForAjaxComplete();
      cy.get('select[id$="cancellationReason"]').select(
        "Employee Requested Cancellation"
      );
      waitForAjaxComplete();
      cy.get(
        'input[type="checkbox"][id$="MasterMultiSelectCB_CHECKBOX"]'
      ).click();
      cy.get('input[type="submit"][title="Apply"]').click();
      waitForAjaxComplete();
      fineos.clickNext();
      waitForAjaxComplete();
    };
    const makeDecision = () => {
      cy.get('select[id$="period-decision-status"]').select("Approved");
      waitForAjaxComplete();
      cy.get(
        'input[type="checkbox"][id$="MasterMultiSelectCB_CHECKBOX"]'
      ).click();
      waitForAjaxComplete();
      cy.get('input[type="submit"][title="Apply"]').click();
      waitForAjaxComplete();
      fineos.clickNext();
      waitForAjaxComplete();
    };

    cy.findByText("Record Cancellation", {
      selector: "span",
    }).click();
    // steps for cancelling time
    recordCancelledTime();
    additionalReportingInformation();
    makeDecision();
    return this;
  }

  ensureAlertsOpen() {
    cy.get(".alertsList-wrap").then((alerts) => {
      if (alerts.hasClass("hidden")) {
        cy.get(".alertsMenuButton").click();
      }
    });
  }

  /**
   * To test secure action task only with our current E2E test suite.
   * Suppress and remove suppression in the same task. Two options on how to remove the suppression when
   * clicking the Notification to remove the suppression in the pop-up widget is very flaky in the headless browser.
   * So the second option is to cancel the suppress notification instead.
   */
  removeSuppressCorrespondence(hasAction: boolean): this {
    waitForAjaxComplete();
    ensureAlertPopUpClosed();
    cy.contains("Options").click();
    if (hasAction) {
      cy.contains("Notifications").focus().click();
      cy.get("input[type='submit'][value='Suppress Notifications']").click();
      cy.contains(
        "Automatic Notifications and Correspondence have been suppressed."
      );
      cy.get("#alertsHeader").within(() => {
        this.ensureAlertsOpen();
        cy.contains("Open").click();
        waitForAjaxComplete();
      });
      cy.contains(
        "Automatic Notifications and Correspondence have been suppressed."
      );
      cy.contains("Close Task").click();
      cy.get("table.PopupBean")
        .first()
        .within(() => {
          cy.get("input[type='submit'][value='Yes']").click();
        });
      clickBottomWidgetButton("OK");
    } else {
      cy.contains(
        "span[title='Control is protected by a Secured Action.']",
        "Notifications"
      ).should("have.attr", "disabled");
    }
    return this;
  }

  reopen() {
    cy.get("a[title='Reopen the Closed Case']").focus().click();
    fineos.waitForAjaxComplete();
    cy.get('a[title*="Close the Case"]');
    return this;
  }

  createHistoricalDisabled() {
    cy.contains("Options").click();
    cy.contains("Add Historical Absence").focus().click();
    cy.contains("div", "timeOffHistoricalAbsencePeriodsListviewWidget")
      .find("input")
      .should("have.attr", "disabled");
    return this;
  }

  private resetTasksTab() {
    cy.contains(".TabStrip td", "Processes").prev().trigger("click");
  }
}
