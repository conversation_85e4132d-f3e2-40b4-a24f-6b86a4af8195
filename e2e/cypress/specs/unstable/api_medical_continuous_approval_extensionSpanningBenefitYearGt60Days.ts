/**
 * This test makes assertions against the state of a claim with an extension
 * that crosses between two benefit years, where the next benefit year is at
 * least sixty days in the future. This is significant because the portion of
 * leave for an extension that spans across a new benefit year is held to the
 * same sixty-day-in-advance restriction as submitting a brand new application.
 *
 * The lifecycle of this test spans four days. The messaging shown to the
 * claimant is dependent on absence paid leave cases (APLC) being present in
 * PFML. APLCs are communicated to PFML from FINEOS via extracts which are
 * typically processed by batch jobs the next day.
 *
 * A peculiarity of this test is that it creates a brand new claimant account
 * for each unique claimant. Messaging related to benefit years does not display
 * to the user if the account has used more than one social security number in
 * the application process.
 */

import { addDays, addWeeks, parseISO, subDays } from "date-fns";

import {
  formatDateAsText,
  formatDateUS,
  formatIsoDateStringAsText,
} from "../../../global.common";
import config from "../../../src/config";
import { DehydratedClaim } from "../../../src/generation/Claim";
import { DehydratedDocument } from "../../../src/generation/documents";
import { ContinuousLeavePeriod } from "../../../src/types";
import { calculateNextBenefitYear } from "../../../src/util/benefit";
import { extractLeavePeriod } from "../../../src/util/claims";
import { getClaimantTaxIdCredentials } from "../../../src/util/credentials";
import { punctuateFein, punctuateSsn } from "../../../src/util/pii";
import { assertValidClaim } from "../../../src/util/typeUtils";
import { fineos, portal } from "../../actions";
import { ClaimPage } from "../../actions/fineos.pages";
import { unstashMultipleKeys } from "../../util";

const environmentIsTested = config("RUN_BENEFIT_YEAR_TESTS");

describe("Claimant submits an extension that spans multiple benefit years, more than sixty days before the next benefit year", () => {
  beforeEach(function () {
    if (!environmentIsTested) {
      this.skip();
    }
  });

  describe("Establish current benefit year", () => {
    const claimSubmitted =
      it("Claimant submits claim that establishes current benefit year", () => {
        const claimGenData: ClaimGenData = {
          employeePoolFileName: config("BENEFIT_YEAR_EMPLOYEE_FILE"),
          scenario: "FORTY_WEEKS_AGO",
        };

        cy.task("generateClaimForEmployeeWithoutClaims", claimGenData).then(
          ({ claim, employee }) => {
            cy.stash("documents", claim.documents);
            cy.stash("claim", claim);
            cy.stash("employee", employee);
            assertValidClaim(claim.claim);
            const credentials = getClaimantTaxIdCredentials(
              claim.claim.tax_identifier
            );
            cy.task("registerClaimant", credentials);

            cy.task("submitClaimToAPI", { ...claim, credentials }).then(
              ({ fineos_absence_id }) => {
                cy.stash("absenceCaseId", fineos_absence_id);
              }
            );
          }
        );
      });

    const agentAdjudication =
      it("Agent adjudicates and approves claim that establishes current benefit year", () => {
        cy.dependsOnPreviousPass([claimSubmitted]);
        fineos.before();

        unstashMultipleKeys<{
          absenceCaseId: string;
          documents: DehydratedDocument[];
        }>(["absenceCaseId", "documents"]).then(
          ({ absenceCaseId, documents }) => {
            ClaimPage.visit(absenceCaseId)
              .completeAdjudication(documents)
              .approve("Completed");
          }
        );
      });

    it("[Scenario: FORTY_WEEKS_AGO] Leave admin reviews the leave allotment on the leave allotment page", () => {
      cy.dependsOnPreviousPass([agentAdjudication]);
      portal.before();
      unstashMultipleKeys<{
        claim: DehydratedClaim;
        absenceCaseId: string;
      }>(["claim", "absenceCaseId"]).then(({ claim, absenceCaseId }) => {
        cy.task<ContinuousLeavePeriod[] | null>(
          "queryDb",
          getClaimsFromEmployeeQuery(claim.claim.tax_identifier!, false)
        ).then((claims) => {
          assertValidClaim(claim.claim);
          const { employer_fein } = claim.claim;

          if (!claims) {
            throw new Error("Claims must be present");
          }

          portal.loginLeaveAdmin(employer_fein);
          portal.goToLeaveAllotmentForClaimantByAbsenceId(absenceCaseId);
          portal.verifyContinuousLeaveAllotmentForClaimant(claims);
        });
      });
    });
  });

  // Benefit year is reconciled between PFML and FINEOS after processing an
  // extract in batch jobs.

  describe("Submit claim near end of current benefit year with extension into next benefit year", () => {
    const findEmployee =
      it("Finds suitable employee with single benefit year", () => {
        cy.task("getEmployerFeins", config("BENEFIT_YEAR_EMPLOYER_FILE")).then(
          (feins) => {
            cy.task<EmployeeWithBenefitYear | null>(
              "queryDb",
              getEmployeeWithSingleBenefitYearQuery(feins)
            ).then((employee) => {
              if (employee === null) {
                throw new Error(
                  "Employee with single benefit year not found. " +
                    "This may be due to extracts, batch jobs, or the first " +
                    "part of this test not running successfully yesterday."
                );
              }

              cy.stash("employee", employee);
            });
          }
        );
      });

    const claimSubmitted =
      it("Claimant submits claim with leave dates towards end of current benefit year", () => {
        cy.dependsOnPreviousPass([findEmployee]);

        cy.unstash<EmployeeWithBenefitYear>("employee").then((employee) => {
          cy.task("generateClaim", {
            scenario: "BENEFIT_YEAR_GREATER_THAN_60_DAYS",
            employeePoolFileName: config("BENEFIT_YEAR_EMPLOYEE_FILE"),
          }).then((claim) => {
            const claimForEmployee = cloneClaimForEmployeeWithBenefitYear(
              claim,
              employee
            );
            cy.stash("secondClaim", claimForEmployee);
            const submissionOptions = {
              ...claimForEmployee,
              credentials: getCredentialsFromTaxIdentifier(
                claimForEmployee.claim.tax_identifier
              ),
            };

            cy.task("submitClaimToAPI", submissionOptions).then(
              ({ fineos_absence_id }) => {
                cy.stash("secondAbsenceCaseId", fineos_absence_id);
              }
            );
          });
        });
      });

    const claimApproved =
      it("Agent adjudicates and approves claim with leave dates towards end of current benefit year", () => {
        cy.dependsOnPreviousPass([claimSubmitted]);
        fineos.before();

        unstashMultipleKeys<{
          secondAbsenceCaseId: string;
          secondClaim: DehydratedClaim;
        }>(["secondAbsenceCaseId", "secondClaim"]).then(
          ({ secondAbsenceCaseId, secondClaim }) => {
            ClaimPage.visit(secondAbsenceCaseId)
              .completeAdjudication(secondClaim.documents)
              .approve("Approved");
          }
        );
      });

    const extensionRequest =
      it("Claimant submits an extension request that spans into next benefit year", () => {
        cy.dependsOnPreviousPass([claimApproved]);
        portal.before();

        cy.unstash<DehydratedClaim>("secondClaim").then((claim) => {
          const credentials = getCredentialsFromTaxIdentifier(
            claim.claim.tax_identifier
          );
          portal.loginClaimant(credentials);

          cy.unstash<EmployeeWithBenefitYear>("employee").then((employee) => {
            const { benefitYearEndDate, benefitYearStartDate } = employee;
            const benefitYear = {
              endDate: benefitYearEndDate,
              isCurrent: true,
              startDate: benefitYearStartDate,
            };
            assertBenefitYearDates(benefitYear);

            cy.unstash<string>("secondAbsenceCaseId").then((absenceCaseId) => {
              portal.claimantGoToClaimStatus(absenceCaseId);
              const [, extensionEndDate] = generateExtensionDates(claim);

              portal.requestModification(
                "extension",
                new Date(extensionEndDate),
                {
                  crossesBenefitYear: true,
                  currentBenefitYearDates: {
                    end: benefitYearEndDate,
                    start: benefitYearStartDate,
                  },
                  uploadDoc: true,
                }
              );
            });
          });
        });
      });

    const agentProcessesExtension =
      it("Agent processes extension request that spans into next benefit year", () => {
        cy.dependsOnPreviousPass([extensionRequest]);
        fineos.before();

        unstashMultipleKeys<{
          secondAbsenceCaseId: string;
          secondClaim: DehydratedClaim;
        }>(["secondAbsenceCaseId", "secondClaim"]).then(
          ({ secondAbsenceCaseId, secondClaim }) => {
            const [start, end] = generateExtensionDates(secondClaim);
            const claimPage = ClaimPage.visit(secondAbsenceCaseId);
            claimPage.benefitsExtension((benefitsExtension) =>
              benefitsExtension.extendLeave(start, end)
            );
          }
        );
      });

    it("[Scenario: BENEFIT_YEAR_GREATER_THAN_60_DAYS] Leave admin reviews the leave allotment on the leave allotment page", () => {
      cy.dependsOnPreviousPass([agentProcessesExtension]);
      unstashMultipleKeys<{
        secondClaim: DehydratedClaim;
        secondAbsenceCaseId: string;
      }>(["secondClaim", "secondAbsenceCaseId"]).then(
        ({ secondClaim, secondAbsenceCaseId }) => {
          cy.task<ContinuousLeavePeriod[]>(
            "queryDb",
            getClaimsFromEmployeeQuery(secondClaim.claim.tax_identifier!)
          ).then((claims) => {
            assertValidClaim(secondClaim.claim);

            const { employer_fein } = secondClaim.claim;

            cy.dependsOnPreviousPass([claimApproved]);
            portal.before();

            portal.loginLeaveAdmin(employer_fein);
            portal.goToLeaveAllotmentForClaimantByAbsenceId(
              secondAbsenceCaseId
            );

            portal.verifyContinuousLeaveAllotmentForClaimant(claims);
          });
        }
      );
    });
  });

  // Further testing depends on the absence paid leave case for the new claim
  // being reconciled between PFML and FINEOS by processing an extract in a
  // batch job.

  describe("View and approve extension request", () => {
    const foundClaim =
      it("Finds claim with pending extension request into next benefit year", () => {
        cy.task("getEmployerFeins", config("BENEFIT_YEAR_EMPLOYER_FILE")).then(
          (feins) => {
            cy.task<ExtensionData | null>(
              "queryDb",
              getExtensionDataQuery(feins)
            ).then((extensionData) => {
              if (extensionData === null) {
                throw new Error(
                  "Unused claim with extension not found. This may be due to " +
                    "extracts, batch jobs, or the second part of this test " +
                    "not running successfully yesterday."
                );
              }

              cy.stash("extensionData", extensionData);
            });
          }
        );
      });

    it("Claimant sees information about the extension request spanning multiple benefit years", () => {
      cy.dependsOnPreviousPass([foundClaim]);
      portal.before();

      cy.unstash<ExtensionData>("extensionData").then((data) => {
        const credentials = getCredentialsFromTaxIdentifier(data.ssn);
        portal.loginClaimant(credentials);
        portal.viewClaimStatus(data.absenceCaseId);
        cy.contains(
          "Your request for leave extends into your new benefit year."
        );
        assertBenefitYearDates({
          endDate: data.benefitYearEndDate,
          isCurrent: true,
          startDate: data.benefitYearStartDate,
        });
        cy.contains("Part of your leave cannot be reviewed.");
        assertNewBenefitYearCalloutIsPresent(data.benefitYearEndDate);
      });
    });

    const adjudication =
      it("Agent adjudicates extension request into next benefit year", () => {
        cy.dependsOnPreviousPass([foundClaim]);
        fineos.before();

        cy.unstash<ExtensionData>("extensionData").then(({ absenceCaseId }) => {
          // The file details do not matter for completing adjudication.
          // Defining these documents inline allows this test case to succeed
          // without being coupled to claim generation in an earlier step.
          const documents: DehydratedDocument[] = [
            { document_type: "Identification Proof", file: "" },
            {
              document_type: "Own serious health condition form",
              file: "",
            },
          ];

          ClaimPage.visit(absenceCaseId).completeAdjudication(documents);
        });
      });

    // Adjudication of the extension request triggers an outstanding requirement
    // for employer review.

    const employerReview =
      it("Leave admin approves extension request into next benefit year", () => {
        cy.dependsOnPreviousPass([adjudication]);

        cy.unstash<ExtensionData>("extensionData").then((data) => {
          cy.task("submitEmployerResponseToApi", {
            employerResponse: {
              employer_benefits: [],
              employer_decision: "Approve",
              fraud: "No",
              hours_worked_per_week: {
                hours_worked: 40,
                employer_changes: "Unchanged",
              },
              previous_leaves: [],
            },
            identifiers: {
              employerFein: data.employerFein,
              fineosAbsenceId: data.absenceCaseId,
            },
          });
        });
      });

    it("Agent approves extension request into next benefit year", () => {
      cy.dependsOnPreviousPass([employerReview]);
      fineos.before();

      cy.unstash<ExtensionData>("extensionData").then(({ absenceCaseId }) => {
        ClaimPage.visit(absenceCaseId).approve("Approved");
      });
    });
  });

  // Further testing depends on the absence paid leave case for the approved
  // extension being reconciled between PFML and FINEOS by processing an extract
  // in a batch job.

  describe("View approved extension", () => {
    const foundClaim =
      it("Finds fully-approved claim with cross-benefit-year extension", () => {
        cy.task("getEmployerFeins", config("BENEFIT_YEAR_EMPLOYER_FILE")).then(
          (feins) => {
            cy.task<ApprovedExtensionData | null>(
              "queryDb",
              getApprovedExtensionDataQuery(feins)
            ).then((data) => {
              if (data === null) {
                throw new Error(
                  "Fully-approved claim not found. This may be due to " +
                    "extracts, batch jobs, or the third part of this test " +
                    "not running successfully yesterday."
                );
              }

              cy.stash("approvedExtensionData", data);
            });
          }
        );
      });

    it("Leave admin reviews the leave allotment on the leave allotment page", () => {
      cy.dependsOnPreviousPass([foundClaim]);
      unstashMultipleKeys<{ approvedExtensionData: ApprovedExtensionData }>([
        "approvedExtensionData",
      ]).then(({ approvedExtensionData }) => {
        cy.task<ContinuousLeavePeriod[]>(
          "queryDb",
          getClaimsFromEmployeeQuery(approvedExtensionData.ssn)
        ).then((claims) => {
          const { employerFein, benefitYearEndDate, absenceCaseId } =
            approvedExtensionData;

          portal.before();

          portal.loginLeaveAdmin(employerFein);
          portal.goToLeaveAllotmentForClaimantByAbsenceId(absenceCaseId);

          portal.verifyContinuousLeaveAllotmentForClaimant(
            claims,
            benefitYearEndDate
          );
        });
      });
    });

    it("Claimant sees the approval for the extension request spanning multiple benefit years", () => {
      cy.dependsOnPreviousPass([foundClaim]);
      portal.before();

      cy.unstash<ApprovedExtensionData>("approvedExtensionData").then(
        (data) => {
          const credentials = getCredentialsFromTaxIdentifier(data.ssn);
          portal.loginClaimant(credentials);
          portal.viewClaimStatus(data.absenceCaseId);
          // Verify that only the approved portion of the extension (the portion
          // in the current benefit year) is displayed.
          const approvedStartDate = formatIsoDateStringAsText(
            data.absencePeriodStartDate
          );
          const approvedEndDate = formatIsoDateStringAsText(
            data.absencePeriodEndDate
          );

          cy.contains(`From ${approvedStartDate} to ${approvedEndDate}`);
          cy.contains(
            "Your leave dates in your new benefit year could not be approved."
          );
          assertNewBenefitYearCalloutIsPresent(data.benefitYearEndDate);
        }
      );
    });
  });
});

function assertBenefitYearDates(benefitYear: BenefitYear) {
  const { endDate, isCurrent, startDate } = benefitYear;
  const kind = isCurrent ? "Current" : "New";
  const start = formatIsoDateStringAsText(startDate);
  const end = formatIsoDateStringAsText(endDate);
  cy.contains(`${kind} benefit year: From ${start} to ${end}`);
}

interface BenefitYear {
  endDate: string;
  isCurrent: boolean;
  startDate: string;
}

function assertNewBenefitYearCalloutIsPresent(benefitYearEndDate: string) {
  const [nextStart] = calculateNextBenefitYear(benefitYearEndDate);
  const sixtyDaysBeforeNextBenefitYear = subDays(parseISO(nextStart), 60);
  const formattedDate = formatDateAsText(sixtyDaysBeforeNextBenefitYear);

  cy.contains(
    `On or after ${formattedDate}, submit a separate application to cover the part of your leave that is in your new benefit year.`
  );
}

function cloneClaimForEmployeeWithBenefitYear(
  claim: DehydratedClaim,
  employee: EmployeeWithBenefitYear
) {
  const { employerFein, ssn } = employee;
  const clone = structuredClone(claim);
  clone.claim.employer_fein = punctuateFein(employerFein);
  clone.claim.first_name = employee.firstName;
  clone.claim.last_name = employee.lastName;
  clone.claim.tax_identifier = punctuateSsn(ssn);
  return clone;
}

function formatFeinsForQuery(feins: readonly string[]) {
  return feins.map((fein) => `'${fein.replace("-", "")}'`).join(",");
}

function generateExtensionDates(claim: DehydratedClaim) {
  assertValidClaim(claim.claim);
  const [, leavePeriodEndDate] = extractLeavePeriod(claim.claim);
  const startDate = formatDateUS(addDays(leavePeriodEndDate, 1), "/");
  const endDate = formatDateUS(addWeeks(leavePeriodEndDate, 8), "/");
  return [startDate, endDate];
}

function getCredentialsFromTaxIdentifier(taxIdentifier?: string | null) {
  if (!taxIdentifier) {
    throw new Error(
      "taxIdentifier is required to getClaimantTaxIdCredentials."
    );
  }

  return getClaimantTaxIdCredentials(taxIdentifier);
}

/**
 * Retrieves the absence periods for an given employee tax identifier. It's non-discriminatory of the benefit year.
 */
function getClaimsFromEmployeeQuery(
  employeeTaxIdentifer: string,
  areApproved: boolean = true
) {
  const formattedTaxIdentifier = employeeTaxIdentifer.replace(/-/g, "");

  return `
      SELECT json_agg(
        json_build_object(
            'start', aplc.absence_period_start_date,
            'end', aplc.absence_period_end_date,
            'leave_type', ct.claim_type_description)
      ) 
        FROM tax_identifier ti
        JOIN employee e ON e.tax_identifier_id = ti.tax_identifier_id
        JOIN claim c ON  c.employee_id = e.employee_id
        JOIN application a ON a.claim_id = c.claim_id 
        JOIN absence_period aplc ON aplc.claim_id = c.claim_id
        JOIN lk_claim_type ct ON ct.claim_type_id = c.claim_type_id
        WHERE ti.tax_identifier = '${formattedTaxIdentifier}' ${
    areApproved ? "AND aplc.leave_request_decision_id = 3" : ""
  };
`;
}

interface EmployeeWithBenefitYear {
  benefitYearCreatedAt: string;
  benefitYearEndDate: string;
  benefitYearStartDate: string;
  employeeId: string;
  employerId: string;
  employerFein: string;
  firstName: string;
  lastName: string;
  ssn: string;
}

/**
 * Gets data for an employee and related entities suitable for this test, with
 * ample time left for the start of a new benefit year to be more than 60 days
 * in the future.
 *
 * The employee should meet the following criteria:
 *
 * - Works for an employer with one of the passed FEINs
 * - Has a single benefit year ending more than 70 days in the future
 * - Has a single claim
 *
 * Selects data with the oldest viable benefit year first.
 */
function getEmployeeWithSingleBenefitYearQuery(feins: readonly string[]) {
  const formattedFeins = formatFeinsForQuery(feins);

  return `
    SELECT json_build_object(
      'benefitYearCreatedAt', benefit_year.created_at,
      'benefitYearEndDate', benefit_year.end_date,
      'benefitYearStartDate', benefit_year.start_date,
      'employeeId', benefit_year.employee_id,
      'employerId', employer.employer_id,
      'employerFein', employer.employer_fein,
      'firstName', employee.first_name,
      'lastName', employee.last_name,
      'ssn', tax_identifier.tax_identifier
    )
    FROM employee
    JOIN benefit_year
      ON benefit_year.employee_id = employee.employee_id
    JOIN employee_occupation
      ON employee_occupation.employee_id = employee.employee_id
    JOIN employer
      ON employer.employer_id = employee_occupation.employer_id
    JOIN tax_identifier
      ON tax_identifier.tax_identifier_id = employee.tax_identifier_id
    WHERE employer.employer_fein IN (${formattedFeins})
      AND (
        SELECT count(*)
        FROM benefit_year
        WHERE benefit_year.employee_id = employee.employee_id
      ) = 1
      AND benefit_year.end_date::date > (CURRENT_DATE + INTERVAL '70 days')::date
      AND (
        SELECT count(*)
        FROM claim
        WHERE claim.employee_id = employee.employee_id
      ) = 1
    ORDER BY benefit_year.created_at ASC
    LIMIT 1;
  `;
}

interface ExtensionData {
  firstName: string;
  lastName: string;
  absenceCaseId: string;
  benefitYearEndDate: string;
  benefitYearStartDate: string;
  employerFein: string;
  ssn: string;
}

/**
 * Gets data for a claim and related entities representing an outstanding
 * extension request suitable for this test.
 *
 * The data should meet the following criteria:
 *
 * - The claim should have one absence paid leave case
 * - The claim should have a pending absence period (an extension request)
 * - The claim should have two associated "Employer Confirmation of Leave Data"
 *   notifications
 * - The claimant should have a single benefit year ending more than 67 days in
 *   the future
 * - The claimant should have two claims
 * - The employer should have one of the passed FEINs
 *
 * Selects data with the oldest viable claim first.
 *
 * More than two "Employer Confirmation of Leave Data" notifications indicates
 * that the extension has already been adjudicated, and will fail if attempted
 * again.
 */
function getExtensionDataQuery(feins: readonly string[]) {
  const formattedFeins = formatFeinsForQuery(feins);

  return `
    SELECT json_build_object(
      'firstName', employee.first_name,
      'lastName', employee.last_name,
      'absenceCaseId', claim.fineos_absence_id,
      'absencePeriodId', absence_period.absence_period_id,
      'benefitYearEndDate', benefit_year.end_date,
      'benefitYearStartDate', benefit_year.start_date,
      'employerFein', employer.employer_fein,
      'ssn', tax_identifier.tax_identifier
    )
    FROM claim
    JOIN absence_period
      ON absence_period.claim_id = claim.claim_id
    JOIN employee
      ON employee.employee_id = claim.employee_id
    JOIN benefit_year
      ON benefit_year.employee_id = employee.employee_id
    JOIN employer
      ON employer.employer_id = claim.employer_id
    JOIN lk_leave_request_decision
      ON lk_leave_request_decision.leave_request_decision_id = absence_period.leave_request_decision_id
    JOIN tax_identifier
      ON tax_identifier.tax_identifier_id = employee.tax_identifier_id
    WHERE employer.employer_fein IN (${formattedFeins})
      AND absence_period.absence_period_end_date > benefit_year.end_date
      AND (
        SELECT count(*)
        FROM absence_paid_leave_case
        WHERE absence_paid_leave_case.claim_id = claim.claim_id
      ) = 1
      AND (
        SELECT count(*)
        FROM benefit_year
        WHERE benefit_year.employee_id = employee.employee_id
      ) = 1
      AND benefit_year.end_date > (CURRENT_DATE + INTERVAL '67 days')::date
      AND (
        SELECT count(*)
        FROM claim
        WHERE claim.employee_id = employee.employee_id
      ) = 2
      AND lk_leave_request_decision.leave_request_decision_description = 'Pending'
      AND (
        SELECT count(*)
        FROM notification
        WHERE notification.fineos_absence_id = claim.fineos_absence_id
          AND notification.request_json::json->>'trigger' = 'Employer Confirmation of Leave Data'
      ) = 2
    ORDER BY claim.created_at ASC
    LIMIT 1;
  `;
}

interface ApprovedExtensionData {
  absenceCaseId: string;
  employerFein: string;
  employerId: string;
  firstName: string;
  lastName: string;
  employeeId: string;
  benefitYearEndDate: string;
  benefitYearStartDate: string;
  claimStartDate: string;
  absencePeriodStartDate: string;
  absencePeriodEndDate: string;
  ssn: string;
}

/**
 * Gets data for a claim and related entities representing an approved extension
 * request suitable for this test.
 *
 * The data should meet the following criteria:
 *
 * - The claim should have two absence paid leave cases
 * - The claim should have two approved absence periods
 * - The claimant should have a single benefit year ending more than 60 days in
 *   the future
 * - The employer should have one of the passed FEINs
 *
 * Selects data with the youngest viable claim first.
 */
function getApprovedExtensionDataQuery(feins: readonly string[]) {
  const formattedFeins = formatFeinsForQuery(feins);

  return `
    SELECT json_build_object(
      'absenceCaseId', claim.fineos_absence_id,
      'employerFein', employer.employer_fein,
      'employerId', employer.employer_id,
      'firstName', employee.first_name,
      'lastName', employee.last_name,
      'employeeId', employee.employee_id,
      'benefitYearEndDate', benefit_year.end_date,
      'benefitYearStartDate', benefit_year.start_date,
      'claimStartDate', claim.claim_start_date,
      'absencePeriodStartDate', absence_period.absence_period_start_date,
      'absencePeriodEndDate', absence_period.absence_period_end_date,
      'ssn', tax_identifier.tax_identifier
    )
    FROM claim
    JOIN employee
      ON employee.employee_id = claim.employee_id
    JOIN benefit_year
      ON benefit_year.employee_id = employee.employee_id
    JOIN employer
      ON employer.employer_id = claim.employer_id
    JOIN tax_identifier
      ON tax_identifier.tax_identifier_id = employee.tax_identifier_id
    JOIN absence_period 
      ON absence_period.claim_id = claim.claim_id 
    WHERE employer.employer_fein IN (${formattedFeins})
      AND (
        SELECT count(*)
        FROM absence_paid_leave_case
        WHERE absence_paid_leave_case.claim_id = claim.claim_id
      ) = 2
      AND (
        SELECT count(*)
        FROM absence_period
        JOIN lk_leave_request_decision
          ON lk_leave_request_decision.leave_request_decision_id = absence_period.leave_request_decision_id
        WHERE absence_period.claim_id = claim.claim_id
          AND lk_leave_request_decision.leave_request_decision_description = 'Approved'
      ) = 2
      AND (
        SELECT count(*)
        FROM benefit_year
        WHERE benefit_year.employee_id = employee.employee_id
      ) = 1
      AND benefit_year.end_date > (CURRENT_DATE + INTERVAL '60 days')::date
    ORDER BY claim.created_at DESC
    LIMIT 1;
  `;
}
