import config from "../../../src/config";
import { LeaveAdminContactInformation } from "../../../src/types";
import { generateFakeContactInformation } from "../../../src/util/pii";
import { createIntentionalSkipFlag } from "../../../src/util/skipFlag";
import { portal } from "../../actions";
import { unstashMultipleKeys } from "../../util";

const intentionalSkipFlag = createIntentionalSkipFlag(
  !config("HAS_LIVECHAT_SETUP")
);
describe("Verified leave admin live chat", () => {
  beforeEach(function () {
    if (!config("HAS_LIVECHAT_SETUP")) {
      this.skip();
    }
  });

  const register =
    it(`${intentionalSkipFlag}Should register, verify, login, a leave admin and assert the chat window is present`, () => {
      cy.task("pickEmployer", {
        withholdings: "non-exempt",
        metadata: { register_leave_admins: true },
      }).then(({ withholdings, fein }) => {
        cy.stash("fein", fein);
        cy.task("generateCredentials").then((credentials) => {
          cy.stash("credentials", credentials);
          cy.task("registerAndVerifyLeaveAdmin", {
            ...credentials,
            fein,
            withholdings,
          }).then(() => {
            portal.before();
            cy.stash("timestamp", Date.now());

            portal.loginLeaveAdmin(credentials);
            portal.assertLoggedIn();
            cy.wait("@benefitYearsSearch");
            cy.wait("@getApplications");
            const newContactInformation = generateFakeContactInformation();
            cy.stash("contactInformation", newContactInformation);
            portal.enterLeaveAdminContactInformation(newContactInformation);
            portal.assertLeaveAdminContactInformation(newContactInformation);
            cy.get('[data-selector="GUIDE_CHANNEL_BUTTON"]');
          });
        });
      });
    });
  it(`${intentionalSkipFlag}Should log in and verify live chat window is pre-populated and a chat can be started`, () => {
    cy.dependsOnPreviousPass([register]);
    portal.before();
    unstashMultipleKeys<{
      credentials: Credentials;
      contactInformation: LeaveAdminContactInformation;
      fein: string;
    }>(["credentials", "contactInformation", "fein"]).then(
      ({ credentials, contactInformation, fein }) => {
        portal.loginLeaveAdmin(credentials);
        cy.get('[data-selector="GUIDE_CHANNEL_BUTTON"]').click();

        // Verify that chat window labels exist
        cy.get('label[for="textLabel-name"]').should("exist");
        cy.get('label[for="textLabel-leave_lname"]').should("exist");
        cy.get('label[for="emailLabel-email"]').should("exist");
        cy.get('label[for="textLabel-ntn"]').should("exist");
        cy.get('label[for="textLabel-fein"]').should("exist");

        // Validate the pre-populated fields
        cy.get("input#textLabel-name").should(
          "have.value",
          contactInformation.firstName
        );
        cy.get("input#textLabel-leave_lname").should(
          "have.value",
          contactInformation.lastName
        );
        cy.get("input#textLabel-ntn").scrollIntoView();
        cy.get("input#emailLabel-email")
          .should("be.visible")
          .should("have.value", credentials.username);

        cy.get("input#textLabel-fein").should("have.value", fein);

        // Enter a random NTN number
        const ntnValue = "NTN-000-ABS-01";
        cy.get("input#textLabel-ntn")
          .should("be.visible")
          .click()
          .type(ntnValue)
          .should("have.value", ntnValue);

        // Select a random topic from the drop down
        cy.get(".chevron").click();
        cy.get(".tree-container").should("be.visible");
        cy.get(".tree-container .list-item").then(($options) => {
          const randomIndex = Math.floor(Math.random() * $options.length);
          cy.wrap($options[randomIndex]).click();
          cy.get(".dropdown-select-field")
            .invoke("val")
            .then((selectedText) => {
              cy.get(".tree-container .list-item.selected .item-name").should(
                "have.text",
                selectedText
              );
            });
        });

        // Start a chat and check the network requests
        cy.intercept(
          "GET",
          "**/chat/1.0/brand/1120/channel/**/availability"
        ).as("chatAvailability");
        cy.intercept("GET", "**/chat/1.0/brand/1120/channel/chat_*").as(
          "chatInit"
        );
        cy.intercept(
          "POST",
          "**/logger-public?brandId=1120&program=dfo-chat"
        ).as("chatLogger");

        cy.get('button[data-selector="BEGIN_CHAT"]')
          .should("be.visible")
          .click();

        // Verify chat availability request
        cy.wait("@chatAvailability", { timeout: 10000 })
          .its("response.statusCode")
          .should("eq", 200);

        // Verify chat initialization request
        cy.wait("@chatInit", { timeout: 10000 })
          .its("response.statusCode")
          .should("eq", 200);

        // Verify the chat logging request
        cy.wait("@chatLogger", { timeout: 10000 })
          .its("response.statusCode")
          .should("eq", 204);
      }
    );
  });
});

describe("Non Verified leave admin live chat", () => {
  beforeEach(function () {
    if (!config("HAS_LIVECHAT_SETUP")) {
      this.skip();
    }
  });
  it(`${intentionalSkipFlag}Should register, a but not verify leave admin and assert the chat window is not present`, () => {
    cy.task("pickEmployer", {
      withholdings: "non-exempt",
      metadata: { register_leave_admins: true },
    }).then(({ withholdings, fein }) => {
      cy.stash("employer", fein);
      cy.task("generateCredentials").then((credentials) => {
        cy.task("registerLeaveAdmin", {
          ...credentials,
          fein,
          withholdings,
        }).then(() => {
          portal.before();
          portal.loginLeaveAdmin(credentials);
          portal.assertLoggedIn();
          cy.wait("@benefitYearsSearch");
          cy.wait("@getApplications");
          cy.get('[data-selector="GUIDE_CHANNEL_BUTTON"]').should("not.exist");
        });
      });
    });
  });
});
