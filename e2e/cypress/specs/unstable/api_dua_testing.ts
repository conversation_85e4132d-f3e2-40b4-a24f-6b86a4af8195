import { formatISO, previousSunday } from "date-fns";

import { EligibilityRequest } from "../../../src/_api";
import config from "../../../src/config";
import { DehydratedClaim } from "../../../src/generation/Claim";
import { getClaimantTaxIdCredentials } from "../../../src/util/credentials";
import {
  assertIsNotNull,
  assertIsString,
  assertValidClaim,
} from "../../../src/util/typeUtils";

type TestScenario = {
  scenario: Scenarios;
  description: string;
  financiallyEligible: boolean;
  expectedWages: number;
};

type EmployeeRecord = {
  employeeWages: string;
  firstName: string;
  lastName: string;
};

const testScenarios: TestScenario[] = [
  {
    scenario: "DUA_SCENARIO_2",
    description:
      "Claimant can pass financial eligibility with non-stat-excluded wages present in DUA data only",
    financiallyEligible: true,
    expectedWages: 10000,
  },
  {
    scenario: "DUA_SCENARIO_3",
    description:
      "<PERSON><PERSON><PERSON><PERSON> with only stat-excluded wages present in DUA data fails financial eligibility",
    financiallyEligible: false,
    expectedWages: 5000,
  },
  {
    scenario: "DUA_SCENARIO_4",
    description:
      "Financial eligibility calculations use DFML wages when a claimant has both DFML and DUA wages",
    financiallyEligible: true,
    expectedWages: 8000,
  },
];

testScenarios.forEach((testScenario) => {
  describe(`${testScenario.description}`, () => {
    const approvedClaim =
      it(`[Scenario: ${testScenario.scenario}] Generates and submits a claim`, () => {
        const employeePoolFileName = config("DUA_EMPLOYEES_FILE");
        const claimGenData: ClaimGenData = {
          employeePoolFileName: employeePoolFileName,
          scenario: testScenario.scenario,
        };
        cy.task("generateClaimForEmployeeWithoutClaims", claimGenData).then(
          ({ claim }) => {
            cy.stash("claim", claim);
            assertValidClaim(claim.claim);
            const credentials = getClaimantTaxIdCredentials(
              claim.claim.tax_identifier
            );
            cy.task("registerClaimant", credentials);

            cy.task("submitClaimToAPI", { ...claim, credentials }).then(
              ({ fineos_absence_id }) => {
                cy.stash("absenceCaseId", fineos_absence_id);
              }
            );
          }
        );
      });
    it(`[Scenario: ${testScenario.scenario}] Wages are expected amount`, async () => {
      cy.dependsOnPreviousPass([approvedClaim]);
      cy.unstash<DehydratedClaim>("claim").then((claim) => {
        assertIsString(claim.claim.tax_identifier);
        const taxIdentifier = formatTaxIdentifier(claim.claim.tax_identifier);
        const query = checkEmployeeWagesQuery(taxIdentifier);
        cy.task<EmployeeRecord>("queryDb", query).then((record) => {
          expect(record.employeeWages).to.eq(testScenario.expectedWages);
        });
      });
    });

    it(`[Scenario: ${testScenario.scenario}] Check for correct Financial Eligibility status`, async () => {
      cy.dependsOnPreviousPass([approvedClaim]);
      cy.unstash<DehydratedClaim>("claim").then((claim) => {
        cy.unstash<string>("absenceCaseId").then((fineosAbsenceId) => {
          const startDate = claim.claim.leave_details?.continuous_leave_periods;
          assertIsNotNull(startDate);
          assertIsNotNull(startDate[0].start_date);
          const eligibilityRequest: EligibilityRequest = {
            tax_identifier: claim.claim.tax_identifier as string,
            employer_fein: claim.claim.employer_fein as string,
            leave_start_date: startDate[0].start_date,
            application_submitted_date: formatISO(new Date(), {
              representation: "date",
            }),
            employment_status: "Employed",
            entitlement_period_start_date: formatISO(
              previousSunday(new Date(startDate[0].start_date)),
              {
                representation: "date",
              }
            ),
            absence_case_number: fineosAbsenceId,
          };
          cy.task("checkFinancialEligibility", eligibilityRequest).then(
            (res) => {
              if (testScenario.financiallyEligible) {
                if (res.status !== 200) {
                  throw new Error(`Expected status 200 but got ${res.status}`);
                }
              } else {
                expect(res.status).to.eq(400);
              }
            }
          );
        });
      });
    });
  });
});

function checkEmployeeWagesQuery(taxIdentifier: string) {
  return `
SELECT json_build_object(
    'employeeWages', w.employee_qtr_wages,
    'firstName', e.first_name,
    'lastName', e.last_name
  )
FROM employee e
JOIN wages_and_contributions w ON e.employee_id = w.employee_id
JOIN tax_identifier t ON e.tax_identifier_id = t.tax_identifier_id
WHERE t.tax_identifier = CAST(${taxIdentifier} AS text)
LIMIT 1;
`;
}

function formatTaxIdentifier(taxIdentifier: string) {
  return taxIdentifier.replace(/-/g, "");
}
