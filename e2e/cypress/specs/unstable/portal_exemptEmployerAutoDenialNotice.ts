import config from "../../../src/config";
import { Submission } from "../../../src/types";
import { createIntentionalSkipFlag } from "../../../src/util/skipFlag";
import { claim, fineos, fineosPages, portal } from "../../actions";

describe("Submit a claim through API: Verify it creates an absence case in Fineos", () => {
  const skipFlag = createIntentionalSkipFlag(!config("HAS_FR25_1"));

  beforeEach(function () {
    if (!config("HAS_FR25_1")) {
      this.skip();
    }
  });

  it(`${skipFlag}Create and Submit Claim via API`, () => {
    portal.generateAndSubmitClaimToAPI({
      employerPoolFileName: config("EXEMPT_EMPLOYER_FILE"),
      employeePoolFileName: config("EMPLOYEES_FOR_EXEMPT_EMPLOYER_FILE"),
      logSubmissionToNewRelic: true,
      scenario: "MED_INTER_EL",
    });
  });

  it(`${skipFlag}Sumbits claim and triggers denial notice`, () => {
    fineos.before();
    cy.unstash<Submission>("submission").then((submission) => {
      const claimPage = fineosPages.ClaimPage.visit(
        submission.fineos_absence_id
      );
      claimPage.triggerNotice("SOM Generate Legal Notice");
    });
  });

  it(`${skipFlag}Verifies correct denial reason on notice`, () => {
    portal.before();
    portal.loginClaimant();
    cy.unstash<Submission>("submission").then((submission) => {
      console.log("Claim", claim);
      portal.claimantGoToClaimStatus(submission.fineos_absence_id);
      portal.checkNoticeForClaimant("Denial of Application (PDF)");
      portal.downloadNoticeAndAssertContent(
        "Denial of Application (PDF)",
        "there are no wages for you on file with the employer you listed to establish a Massachusetts PFML application or your employer does not participate in the program, is exempt, or offers a private plan with rights and benefits at least equal to those provided under PFML law."
      );
    });
  });
});
