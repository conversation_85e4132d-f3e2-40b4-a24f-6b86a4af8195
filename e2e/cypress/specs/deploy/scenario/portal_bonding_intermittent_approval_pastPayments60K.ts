import { addDays, format, isSunday, startOfWeek, subDays } from "date-fns";
import { convertToTimeZone } from "date-fns-timezone";

import config from "../../../../src/config";
import { Submission } from "../../../../src/types";
import {
  asFixed,
  calculateWeeklyBenefit,
  getIndividualAverageWeeklyWages,
} from "../../../../src/util/benefit";
import { assertValidClaim } from "../../../../src/util/typeUtils";
import { claim, fineos, fineosPages, portal } from "../../../actions";
import { ClaimantPage } from "../../../actions/fineos.claimant";
import { assertLeaveHoursDisplayInPortal } from "../../../actions/portal";
import { reduceToStepsOneTwoOfPartOne } from "../../../util";

const mostRecentSunday = startOfWeek(new Date());
const firstLeaveDay = subDays(mostRecentSunday, 20);
const secondLeaveDay = addDays(firstLeaveDay, 7);

const scenario = "BIAP60";

describe("Submit bonding application via the web portal: Adjudication Approval, recording actual hours & payment checking", () => {
  const submissionTest =
    it("As a claimant, I should be able to submit a intermittent bonding application through the portal", () => {
      portal.before();
      claim.generateClaim(scenario).then((claim) => {
        cy.stash("claim", claim);
        const partsOne = reduceToStepsOneTwoOfPartOne(claim.claim);
        cy.task<string | undefined>("submitStepsOneTwoOfPartOne", {
          application: partsOne,
        }).then((applicationId) => {
          if (!applicationId) {
            throw Error("Application did not create successfully");
          }
          const application = claim.claim;
          const paymentPreference = claim.paymentPreference;
          portal.skipLoadingClaimantApplications();
          portal.loginClaimant();
          portal.wrapUpPartOne(applicationId, application);
          portal.waitForClaimSubmission({ logSubmissionToNewRelic: true });
          portal.submitClaimPartsTwoThree(application, paymentPreference, {
            is_withholding_tax: claim.is_withholding_tax,
          });
        });
      });
    });

  const erApproval =
    it("Leave admin will submit ER approval for employee", () => {
      cy.dependsOnPreviousPass([submissionTest]);
      portal.before();
      cy.unstash<DehydratedClaim>("claim").then((claim) => {
        cy.unstash<Submission>("submission").then((submission) => {
          assertValidClaim(claim.claim);
          portal.loginLeaveAdmin(claim.claim.employer_fein);
          portal.selectClaimFromEmployerDashboard(submission.fineos_absence_id);
          portal.visitActionRequiredERFormPage(submission.fineos_absence_id);
          portal.respondToLeaveAdminRequest({
            approval: true,
            gaveNotice: true,
          });
        });
      });
    });

  const claimApproval =
    it("CSR rep will begin adjudication & approve intermittent bonding application", () => {
      cy.dependsOnPreviousPass([erApproval]);
      fineos.before();
      cy.unstash<DehydratedClaim>("claim").then((claim) => {
        cy.unstash<Submission>("submission").then(({ fineos_absence_id }) => {
          const claimPage = fineosPages.ClaimPage.visit(fineos_absence_id);
          claimPage.shouldHaveStatus("Eligibility", "Met");
          claimPage.adjudicate((adjudication) => {
            adjudication
              .evidence((evidence) => {
                claim.documents.forEach(({ document_type }) =>
                  evidence.receive(document_type)
                );
              })
              .certificationPeriods((certPeriods) => certPeriods.prefill())
              .paidBenefits((paidBenefits) => {
                paidBenefits.assertSitFitOptIn(claim.is_withholding_tax);
              })
              .acceptLeavePlan(!config("HAS_FR25_1"));
          });
          claimPage.shouldHaveStatus("Availability", "As Certified");
          if (config("HAS_FR25_1")) {
            claimPage.adjudicate((adjudication) => {
              adjudication.progressToFullyAdjudicated();
            });
          }
          const sunday = isSunday(
            convertToTimeZone(new Date(), {
              timeZone: "America/New_York",
            })
          );
          claimPage.approve(sunday ? "Approved" : "Completed");
        });
      });
    });

  const recordingHours =
    it("CSR rep or claimant will report intermittent leave hours", () => {
      cy.dependsOnPreviousPass([claimApproval]);
      cy.unstash<DehydratedClaim>("claim").then((claim) => {
        if (!claim.metadata || !claim.metadata.fineosIntermittentLeave) {
          throw Error("Intermittent leave hours are undefined.");
        }
        const fineosIntermittentLeaveHours =
          claim.metadata.fineosIntermittentLeave.spanHoursStart;
        //preserve this variable for use in later it block
        cy.stash("fineosIntermittentLeaveHours", fineosIntermittentLeaveHours);
        cy.unstash<Submission>("submission").then(({ fineos_absence_id }) => {
          portal.before();
          portal.loginClaimant();
          portal.claimantGoToClaimStatus(fineos_absence_id, {
            visitLink: true,
          });
          // Example: 5 days of leave 1 time per week from December 17, 2023 to January 14, 2024
          cy.contains("5 days of leave");
          portal.claimantGoToReportLeaveHours();
          // Should error.
          cy.contains("button", "Submit hours").click();
          cy.get(".usa-alert__heading").should(
            "have.text",
            "2 errors occurred"
          );

          // Reporting actual intermittent leave hours in the portal doesn't have a concept of start or end.
          // Instead, each leave is reported individually.
          // Should show as approved later in Fineos.
          portal.claimantReportLeaveHours(
            firstLeaveDay,
            fineosIntermittentLeaveHours
          );
          portal.claimantReportLeaveHours(
            secondLeaveDay,
            fineosIntermittentLeaveHours
          );
          // Should should as pending later in Fineos.
          portal.claimantReportLeaveHours(
            mostRecentSunday,
            fineosIntermittentLeaveHours
          );
        });
      });
    });

  const confirmEpisodicLeaveEpisodes =
    it("Will assert pending leaves and the task to review submitted time", () => {
      cy.dependsOnPreviousPass([recordingHours]);
      fineos.before();
      cy.unstash<Submission>("submission").then((submission) => {
        const claimPage = fineosPages.ClaimPage.visit(
          submission.fineos_absence_id
        );
        claimPage.shouldHaveEpisodicLeave(mostRecentSunday, 4, "Pending");
        claimPage.shouldHaveEpisodicLeave(secondLeaveDay, 4, "Approved");
        claimPage.shouldHaveEpisodicLeave(firstLeaveDay, 4, "Approved");
        claimPage.tasks((task) => {
          task.assertTaskExists(
            "Review and Make Decision on Actual Time Submitted"
          );
        });
      });
    });

  it(
    "Should be able to confirm the weekly payment amount for a intermittent schedule",
    { retries: 0 },
    () => {
      cy.dependsOnPreviousPass([confirmEpisodicLeaveEpisodes]);
      fineos.before();
      cy.unstash<DehydratedClaim>("claim").then((claim) => {
        cy.unstash<Submission>("submission").then((submission) => {
          assertValidClaim(claim.claim);
          ClaimantPage.visit(claim.claim.tax_identifier)
            .getFirstEntitlementPeriod()
            .then((period) => {
              fineosPages.ClaimPage.visit(
                submission.fineos_absence_id
              ).paidLeave((leaveCase) => {
                const rateYear = new Date(period.start).getFullYear();
                const weeklyBenefit = calculateWeeklyBenefit(
                  getIndividualAverageWeeklyWages(scenario),
                  rateYear
                );
                const netPaymentAmount = asFixed(weeklyBenefit / 5);
                // In FR25-1 the unpaid waiting period aligns with the first certified date of absence and not the leave start date
                const paymentInstances = config("HAS_FR25_1") ? 1 : 2;

                leaveCase.assertAmountsPending([
                  {
                    netPaymentAmount,
                    paymentInstances,
                  },
                ]);
              });
            });
        });
      });
    }
  );

  //Created based off the manually entered leave hours in above it block "CSR rep or claimant will report intermittent leave hours"
  it("should display leave days and their approval status in portal", function () {
    cy.unstash<Submission>("submission").then(({ fineos_absence_id }) => {
      portal.before();
      portal.loginClaimant();
      portal.claimantGoToClaimStatus(fineos_absence_id, {
        visitLink: true,
      });
      portal.claimantGoToReportLeaveHours();

      cy.unstash<DehydratedClaim>("claim").then((claim) => {
        if (!claim.metadata || !claim.metadata.fineosIntermittentLeave) {
          throw Error("Intermittent leave hours are undefined.");
        }
        const hours = claim.metadata.fineosIntermittentLeave.spanHoursStart;
        const leaveHoursDisplayAssertionArg = [
          // must be done in correct order. Order is based off the manually entered
          // leave hours in above it block "CSR rep or claimant will report
          // intermittent leave hours". Last entry appears top of list.
          createLeaveHoursDisplayArg(mostRecentSunday, hours, "Pending"),
          createLeaveHoursDisplayArg(secondLeaveDay, hours, "Approved"),
          createLeaveHoursDisplayArg(firstLeaveDay, hours, "Approved"),
        ];
        cy.get("table tr").each((tr, index) => {
          const rowText = tr.text();
          //skip first row because it is the table headers.
          if (index === 0) {
            return;
          }
          assertLeaveHoursDisplayInPortal(
            leaveHoursDisplayAssertionArg[index - 1],
            rowText
          );
        });
      });
    });
  });

  it("should not allow you to submit a date thats has already been reported for leave hours", () => {
    cy.unstash<Submission>("submission").then(({ fineos_absence_id }) => {
      cy.unstash<DehydratedClaim>("claim").then((claim) => {
        if (!claim.metadata || !claim.metadata.fineosIntermittentLeave) {
          throw Error("Intermittent leave hours are undefined.");
        }
        const fineosIntermittentLeaveHours =
          claim.metadata.fineosIntermittentLeave.spanHoursStart;

        portal.before();
        portal.loginClaimant();
        portal.claimantGoToClaimStatus(fineos_absence_id, {
          visitLink: true,
        });
        portal.claimantGoToReportLeaveHours();
        const formattedDate = format(firstLeaveDay, "MMMM d, yyyy");
        portal.claimantReportRedundantLeaveHours(
          formattedDate,
          fineosIntermittentLeaveHours
        );
      });
    });
  });

  it("CSR rep will override payment processing date to be scheduled for day of approval", () => {
    cy.dependsOnPreviousPass([recordingHours]);
    fineos.before();
    cy.unstash<Submission>("submission").then((submission) => {
      fineosPages.ClaimPage.visit(submission.fineos_absence_id).paidLeave(
        (paidLeavePage) => {
          paidLeavePage.editPaymentProcessingDate();
        }
      );
    });
  });
});

function createLeaveHoursDisplayArg(date: Date, hours: string, status: string) {
  const formattedDate = format(date, "MMMM d, yyyy");
  return [formattedDate, `${hours} hours`, status];
}
