import config from "../../../../src/config";
import { Credentials, Submission } from "../../../../src/types";
import { generateDateOfBirth } from "../../../../src/util/pii";
import { createIntentionalSkipFlag } from "../../../../src/util/skipFlag";
import { assertValidClaim } from "../../../../src/util/typeUtils";
import { claim, fineos, fineosPages, portal } from "../../../actions";
import { ClaimantPage } from "../../../actions/fineos.claimant";
import { unstashMultipleKeys } from "../../../util";

// This test is reliant on batch jobs that have been disabled in training
// environments.
const environmentIsExcluded = ["trn2", "training"].includes(
  config("ENVIRONMENT")
);

const intentionalSkipFlag = createIntentionalSkipFlag(environmentIsExcluded);

describe("Claimant Registration with Channel Switching", () => {
  beforeEach(function () {
    if (environmentIsExcluded) {
      this.skip();
    }
  });

  const fineosSubmission =
    it(`${intentionalSkipFlag}As a Call Center Representative, I should be able to submit a continuous bonding application through FINEOS`, () => {
      fineos.before();
      claim
        .generateClaim("BCAP90")
        .then((claim) => {
          cy.stash("claim", claim);
          assertValidClaim(claim.claim);
          const customer = ClaimantPage.visit(claim.claim.tax_identifier);
          customer.setPreferredLanguage(claim.preferredLanguage);
          customer.editPersonalIdentification({
            date_of_birth: generateDateOfBirth(),
            mass_id: claim.claim.mass_id,
          });
          customer.addAddressIfNone();
          customer.createNotification(claim.claim).then((fineos_absence_id) => {
            cy.log(fineos_absence_id);
            cy.stash(
              "submission",
              {
                fineos_absence_id: fineos_absence_id,
                timestamp_from: Date.now(),
              },
              { logToNewRelic: true }
            );
          });
        })
        .debug();
    });

  const portalSubmission =
    it(`${intentionalSkipFlag} As a newly-registered claimant, I should be able to resume the application via the web portal`, function () {
      cy.dependsOnPreviousPass([fineosSubmission]);
      cy.task("generateCredentials").then((credentials) => {
        cy.stash("credentials", credentials);
        cy.task("registerClaimant", credentials);
      });

      unstashMultipleKeys<{
        claim: DehydratedClaim;
        credentials: Credentials;
        submission: Submission;
      }>(["claim", "credentials", "submission"]).then(
        ({ claim, credentials, submission }) => {
          portal.before();
          portal.loginClaimant(credentials);

          if (!claim.claim.tax_identifier) {
            throw new Error(
              `Employee SSN missing for scenario: ${claim.scenario}`
            );
          }

          portal.resumeFineosApplication(
            claim.claim.tax_identifier,
            submission.fineos_absence_id
          );
          portal.assertClaimImportSuccess(submission.fineos_absence_id);
          portal.submitClaimPartThree(claim.claim, {
            is_withholding_tax: false,
          });
        }
      );
    });

  it(`${intentionalSkipFlag} Checks for the uploaded documents in FINEOS`, () => {
    cy.dependsOnPreviousPass([portalSubmission]);
    fineos.before();
    cy.unstash<Submission>("submission").then((submission) => {
      const claimPage = fineosPages.ClaimPage.visit(
        submission.fineos_absence_id
      );
      claimPage.documents((docsPage) => {
        docsPage.assertDocumentExists("Child bonding evidence form");
        docsPage.assertDocumentExists("Identification Proof");
      });
    });
  });
});
