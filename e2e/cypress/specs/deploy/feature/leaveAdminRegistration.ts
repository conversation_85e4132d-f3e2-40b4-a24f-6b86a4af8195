import { Credentials } from "../../../../src/types";
import { getMostRecentWithholding } from "../../../../src/util/employers";
import { generateFakeContactInformation } from "../../../../src/util/pii";
import { portal } from "../../../actions";

describe("Leave Admin Self-Registration", { taskTimeout: 300_000 }, () => {
  before(function () {
    const startTime = Date.now();
    cy.task("removeLeaveAdmin", false).then((result) => {
      console.log(
        `Removed leave admin in ${Date.now() - startTime}ms: ${result}`
      );
    });
  });

  const register =
    it("Leave administrators should be able to self-register on the portal.", () => {
      cy.task("pickEmployer", {
        withholdings: "non-exempt",
        metadata: { register_leave_admins: true },
      }).then((employer) => {
        cy.task("generateCredentials").then((credentials) => {
          portal.before();
          cy.task("registerLeaveAdmin", credentials);

          portal.loginLeaveAdmin(credentials);
          cy.get('button[type="submit"]')
            .should("contain.text", "Agree and continue")
            .get('button[type="submit"]')
            .click({ timeout: 20000 });
          // Added to resolve background request before clicking
          cy.wait("@benefitYearsSearch");
          cy.wait("@getApplications");
          // should fail without contact information
          portal.enterLeaveAdminContactInformation();
          let contactInformation = generateFakeContactInformation();
          // verified leave admin information is being collected
          portal.enterLeaveAdminContactInformation(contactInformation);
          portal.goToWelcomePage();
          portal.assertLeaveAdminContactInformation(contactInformation);
          // verify contact information can be edited
          contactInformation = generateFakeContactInformation();
          portal.enterLeaveAdminContactInformation(contactInformation, true);

          portal.goToEmployerApplicationsPage();
          portal.assertUnverifiedEmployerDashboard();
          const withholding = getMostRecentWithholding(employer.withholdings);
          if (!withholding) {
            throw new Error("This employer has no withholdings reported");
          }
          portal.goToOrganizationsTab();
          cy.wait(3000);
          portal.addOrganization(employer.fein, withholding);

          cy.stash("employer", employer.fein);
          cy.stash("withholdings", employer.withholdings);
          cy.stash("credentials", credentials);
        });
      });
    });

  it("Leave administrators should be able to register for a second organization", () => {
    cy.dependsOnPreviousPass([register]);
    portal.before();
    cy.unstash<string>("employer").then((fein) => {
      cy.unstash<Credentials>("credentials").then((credentials) => {
        portal.loginLeaveAdmin(credentials);
        portal.goToOrganizationsTab();
        // Pick a second employer from the dataset to register as an additional organization.
        cy.task("pickEmployer", {
          withholdings: "non-exempt",
          notFEIN: fein,
          metadata: { register_leave_admins: true },
        }).then((secondary) => {
          const secondaryWithholding = getMostRecentWithholding(
            secondary.withholdings
          );
          portal.addOrganization(secondary.fein, secondaryWithholding);
        });
        // Test that we can filter claims by organization.
        // This is currently the only place where we can test it.
        portal.goToEmployerApplicationsPage();
        portal.clickShowFilterButton();
        // Select the second option, as we only care we can filter at all.
        portal.selectOrgFilter({ organization: 1 });
        portal.clickApplyFilters();
      });
    });
  });

  const verifyWithMTC =
    it("Leave administrators can use mtc_number to verify with an organization whose withholdings equal 0", () => {
      cy.dependsOnPreviousPass([register]);
      portal.before();
      cy.unstash<Credentials>("credentials").then((credentials) => {
        portal.loginLeaveAdmin(credentials);
        portal.goToOrganizationsTab();
        cy.task("pickEmployer", {
          withholdings: [0, 0, 0, 0],
          metadata: { register_leave_admins: true },
        }).then((tertiary) => {
          const tertiaryWithholding = getMostRecentWithholding(
            tertiary.withholdings
          );

          portal.addOrganization(
            tertiary.fein,
            tertiaryWithholding,
            tertiary.mtc_number
          );

          cy.stash("tertiary", tertiary);
        });
      });
    });

  it("Leave administrators can remove themselves as a leave administrator from their organization", () => {
    cy.dependsOnPreviousPass([verifyWithMTC]);
    portal.before();
    cy.unstash<Credentials>("credentials").then((credentials) => {
      portal.loginLeaveAdmin(credentials);
      cy.unstash<Employer>("tertiary").then((tertiary) => {
        portal.goToOrganizationsTab();
        cy.contains("a", tertiary.name).click();
        portal.removeLeaveAdmin(credentials.username);
      });
    });
  });
});
