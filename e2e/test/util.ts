/*
  @Note: Parts of the test are commented out due to functionality not being ready
  for testing.  Once feature is ready for testing we'll remove comments.

  We can uncomment uploads when NAVA API team has completed implementation
  for the new API upload using multipart: https://lwd.atlassian.net/browse/PORTAL-1390

  This file also contains other util functions/variables specifically for integration tests
*/

import { describe as jestDescribe, it as jestIt } from "@jest/globals";
import fs from "fs";

import { RMVCheckRequest } from "../src/_api";
import config from "../src/config";
import NewRelicClient from "../src/NewRelicClient";
import { createIntentionalSkipFlag } from "../src/util/skipFlag";
import { assertIsTypedArray, isObjectType } from "../src/util/typeUtils";
import dataDirectory, {
  DataDirectory,
} from "./../src/generation/DataDirectory";

const originalEnvironment = { ...process.env };

export interface DocumentTestCase {
  description: string;
  filepath: string;
  statusCode: 200 | 413 | 400;
  filetype?: string;
}

type DocumentTestSpec =
  | Array<DocumentTestCase>
  | (() => Array<DocumentTestCase>);

/**
 *  Idea here is to test the file size limit (4.5MB) w/each accepted
 *  filetype: PDF/JPG/PNG in three different ways.
 *    - smaller than limit
 *    - right at limit ex. 4.4MB
 *    - larger than limit
 */
export const documentTests: Record<string, DocumentTestSpec> = {
  pdf: [
    {
      description:
        "Should submit a PDF document with file size less than 4.5MB successfully",
      filepath: "./cypress/fixtures/docTesting/small-150KB.pdf",
      statusCode: 200,
    },
    {
      description:
        "Should submit a PDF document with file size larger than 4.5MB (10MB) unsuccessfully and return API error",
      filepath: "./cypress/fixtures/docTesting/large-10MB.pdf",
      statusCode: 413,
    },
  ],
  jpg: [
    {
      description:
        "Should submit a JPG document with file size less than 4.5MB successfully",
      filepath: "./cypress/fixtures/docTesting/xsmall-220KB.jpg",
      statusCode: 200,
    },
    {
      description:
        "Should submit a JPG document with file size larger than 4.5MB (15.5MB) unsuccessfully and return API error",
      filepath: "./cypress/fixtures/docTesting/large-15.5MB.jpg",
      statusCode: 413,
    },
  ],
  png: [
    {
      description:
        "Should submit a PNG document with file size less than 4.5MB successfully",
      filepath: "./cypress/fixtures/docTesting/small-2.7MB.png",
      statusCode: 200,
    },
    {
      description:
        "Should submit a PNG document with file size larger than 4.5MB (15.5MB) unsuccessfully and return API error",
      filepath: "./cypress/fixtures/docTesting/large-14MB.png",
      statusCode: 413,
    },
  ],
  badFileTypes: [
    {
      description:
        "Should receive error when trying to submit an incorrect file type (.gif)",
      filepath: "./cypress/fixtures/docTesting/small-275KB.gif",
      filetype: "gif",
      statusCode: 400,
    },
  ],
  // Added logic to establish baseline of failures for Files right at limit
  rightAtLimit: () => {
    return [
      {
        description:
          "Should submit a PDF document with file size just under 10MB successfully",
        filepath: "./cypress/fixtures/docTesting/limit-9.4MB.pdf",
        statusCode: 200,
      },
      {
        description:
          "Should submit a JPG document with file size right at 4.5MB successfully",
        filepath: "./cypress/fixtures/docTesting/limit-4.5MB.jpg",
        statusCode: 200,
      },
      {
        description:
          "Should submit a PNG document with file size right at 4.5MB successfully",
        filepath: "./cypress/fixtures/docTesting/limit-4.5MB.png",
        statusCode: 200,
      },
    ];
  },
};

export type IDProofingSpecification = [
  string,
  RMVCheckRequest,
  string,
  boolean
];

export const id_proofing: IDProofingSpecification[] = [
  [
    "fraudulent",
    {
      absence_case_id: "z1pGihOsyBW4LBCqNyQr",
      first_name: "Willis",
      last_name: "Sierra",
      ssn_last_4: "9053",
      date_of_birth: "1975-06-02",
      mass_id_number: "SA2600200",
      residential_address_city: "Lynn",
      residential_address_line_1: "42 murray st",
      residential_address_zip_code: "01905",
    },
    "Verification failed because no record could be found for given ID information.",
    false,
  ],
  [
    "valid",
    {
      absence_case_id: "4LjXqd0kSJ1px8p6LF8P",
      first_name: "John",
      last_name: "Pinkham",
      ssn_last_4: "0105",
      date_of_birth: "1973-10-30",
      mass_id_number: "S46493908",
      residential_address_city: "Ashfield",
      residential_address_line_1: "83g bear mountain dr",
      residential_address_zip_code: "01330",
    },
    "Verification check passed.",
    true,
  ],
];

type ItSpecParameters = Parameters<typeof jestIt>;
export const itIf =
  (condition: boolean) =>
  (
    label: ItSpecParameters["0"],
    testFn: ItSpecParameters["1"],
    timeout?: ItSpecParameters["2"]
  ): void => {
    const intentionalSkip = createIntentionalSkipFlag(!condition);
    return (condition ? jestIt : jestIt.skip)(
      `${intentionalSkip}${label}`,
      testFn,
      timeout
    );
  };

export function describeIf(
  condition: boolean
): typeof jestDescribe | typeof jestDescribe.skip {
  return condition ? jestDescribe : jestDescribe.skip;
}

export const testStorage = () => {
  const storage: DataDirectory = dataDirectory("tmp", __dirname);
  const prepareStorage = async () => {
    await storage.prepare();
  };
  const removeStorage = async () => {
    await fs.promises.rm(storage.dir, { recursive: true });
  };
  return {
    storage,
    prepareStorage,
    removeStorage,
  };
};

interface NrqlEligFileLogs {
  number_of_employees: string;
  output_file: string;
  timestamp: number;
}

export async function logEligibilityFileInformation(
  start: number,
  end: number,
  employerIds?: string[]
) {
  const nrClient = new NewRelicClient(
    config("NEWRELIC_APIKEY"),
    config("NEWRELIC_ACCOUNTID")
  );
  const env = config("ENVIRONMENT");
  const whereClauses = [
    `aws.logGroup = 'service/pfml-api-${env}/ecs-tasks'`,
    "output_file LIKE 's3://%'",
    "name = 'massgov.pfml.fineos.eligibility_feed'",
  ];
  if (employerIds) {
    whereClauses.push(
      "internal_employer_id IN (" +
        employerIds.map((e) => "'" + e + "'").join(",") +
        ")"
    );
  }
  const query = `
    SELECT
    number_of_employees, output_file
    FROM Log
    WHERE ${whereClauses.join(" AND ")}
    SINCE ${start} UNTIL ${end}
    LIMIT MAX`;
  const data = await nrClient.nrql(query);
  assertIsTypedArray(
    data,
    isObjectType<NrqlEligFileLogs>({
      number_of_employees: "",
      output_file: "",
      timestamp: "",
    })
  );
  return data;
}

export function getPreviousQuarterStartDate(date: Date): Date {
  const month = date.getMonth(); // 0-11
  const prevQuarterMonth = month - (month % 3) - 3;
  const yearAdjustment = prevQuarterMonth < 0 ? -1 : 0;
  const adjustedYear = date.getFullYear() + yearAdjustment;
  const adjustedMonth =
    prevQuarterMonth < 0 ? 12 + prevQuarterMonth : prevQuarterMonth;
  return new Date(adjustedYear, adjustedMonth, 1);
}

export function generateQuarterlyDates(
  numberOfQuarters: number
): Array<string> {
  const today = new Date();
  let quarterStartDate = getPreviousQuarterStartDate(today);
  const result = [];
  for (let i = 0; i < numberOfQuarters; i++) {
    // Adjust the date to the end of the quarter
    const endOfQuarter = new Date(
      quarterStartDate.getFullYear(),
      quarterStartDate.getMonth() + 3,
      0
    );
    const key = endOfQuarter.toISOString().split("T")[0];
    result.push(key);
    quarterStartDate = getPreviousQuarterStartDate(quarterStartDate);
  }
  return result.sort();
}

export function setE2EEnvironmentVariables(env: {
  [key: string]: string | undefined;
}) {
  Object.entries(env).forEach(([key, value]) => {
    process.env[key] = value;
  });
}
export function resetE2EEnvironmentVariables() {
  process.env = originalEnvironment;
}
