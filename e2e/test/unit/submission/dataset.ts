import { expect, jest } from "@jest/globals";
import child_process, { ChildProcess } from "child_process";
import winston, { Logger } from "winston";
import yargs from "yargs";

import { DatasetArgs } from "../../../src/commands/dataset";
import { handler } from "../../../src/commands/dataset/upload";
import { Configuration } from "../../../src/config";
import { ParsedEnvironment } from "../../../src/EnvironmentVariables";
import storage from "../../../src/generation/DataDirectory";
import InfraClient from "../../../src/InfraClient";

// Mock modules that handlers are reliant on
jest.mock("../../../src/generation/DataDirectory");
jest.mock("../../../src/InfraClient");
jest.mock("winston");
jest.mock("child_process");
const getDefaultArgs = (args: yargs.ArgumentsCamelCase<DatasetArgs>) => {
  return {
    ...yargs.argv,
    ...args,
  };
};

const infraTest = {
  _client: new InfraClient(
    "foo",
    "bar",
    "baz",
    "test",
    "foo",
    "bar",
    "baz",
    "test",
    "foo",
    "bar",
    "baz",
    "test",
    "foo"
  ),
  get() {
    return this._client;
  },
};

const mockConfigGet = jest
  .fn(
    <K extends keyof Configuration>(_name: K) =>
      "" as ParsedEnvironment[typeof _name]
  )
  .mockReturnValueOnce("foo")
  .mockReturnValueOnce("bar")
  .mockReturnValueOnce("baz");

describe("Dataset upload handler", () => {
  let testInfraClient: InfraClient;
  const createInfraSpy = jest.spyOn(InfraClient, "create");
  const mockedLogger = jest.mocked(winston);
  const mockedChildProcess = jest.mocked(child_process["spawn"]);

  beforeAll(() => {
    // These modules only need to be mocked once
    const storageMock = jest.mocked(storage);
    mockedLogger.createLogger.mockImplementation(
      () =>
        ({
          info: jest.fn(),
        } as unknown as Logger)
    );
    mockedChildProcess.mockImplementation(
      // @ts-ignore - mock implementation will be completed as a part of this ticket https://lwd.atlassian.net/browse/PFMLPB-17379
      () =>
        ({
          stdout: {
            on: jest.fn(),
          },
          stderr: {
            on: jest.fn(),
          },
          on: jest.fn(),
        } as unknown as ChildProcess)
    );
    storageMock.mockReturnValue({
      claims: "",
      dir: "",
      documents: "",
      employees: "",
      employers: "",
      state: "",
      usedEmployees: "",
      getDORFiles: jest.fn((type: "DORDFML" | "DORCSE") =>
        type === "DORDFML"
          ? Promise.resolve(["foo", "bar"])
          : Promise.resolve(["foo"])
      ),
      join: jest.fn(() => ""),
      prepare: jest.fn(() => Promise.resolve()),
      dorFile: jest.fn(() => ""),
    });
  });

  beforeEach(() => {
    createInfraSpy.mockReturnValue(infraTest.get());
    // reassign testInfraClient to a new instance on InfraClient
    // this prevents multiple tests from referencing the same instance of InfraClient, causing call assertion counts to fail
    testInfraClient = infraTest.get();
    testInfraClient.getFineosExtracts = jest.fn(() => Promise.resolve([]));
    testInfraClient.runDorEtl = jest.fn(() => Promise.resolve());
    testInfraClient.uploadRevenueFiles = jest.fn(() => Promise.resolve());
    testInfraClient.getIAMUsername = jest.fn(() => Promise.resolve("foo"));
  });

  afterEach(() => {
    createInfraSpy.mockReset();
  });

  it("Run one given N number of environments, the script should run N times", async () => {
    const args: yargs.ArgumentsCamelCase<DatasetArgs> = {
      environments: ["tst2"],
      directory: "test",
      configs: {
        performance: mockConfigGet("ENVIRONMENT"),
        tst2: mockConfigGet("ENVIRONMENT"),
        uat: mockConfigGet("ENVIRONMENT"),
      },
      storage: storage("", ""),
      environment: "tst2",
      logger: mockedLogger.createLogger(),
      verbose: false,
      _: [],
      $0: "",
    };
    await handler(getDefaultArgs(args));
    expect(createInfraSpy).toHaveBeenCalledTimes(1);
    expect(testInfraClient.uploadRevenueFiles).toHaveBeenCalledTimes(2);
    expect(testInfraClient.runDorEtl).toHaveBeenCalledTimes(1);
  });

  it("Run two given N number of environments, the script should run N times", async () => {
    const args: yargs.ArgumentsCamelCase<DatasetArgs> = {
      environments: ["performance", "tst2", "uat"],
      directory: "tst2",
      configs: {
        performance: mockConfigGet("ENVIRONMENT"),
        tst2: mockConfigGet("ENVIRONMENT"),
        uat: mockConfigGet("ENVIRONMENT"),
      },
      storage: storage("", ""),
      environment: "devint,test,uat",
      logger: mockedLogger.createLogger(),
      verbose: false,
      _: [],
      $0: "",
    };
    await handler(getDefaultArgs(args));
    expect(createInfraSpy).toHaveBeenCalledTimes(3);
    expect(testInfraClient.uploadRevenueFiles).toHaveBeenCalledTimes(6);
    expect(testInfraClient.runDorEtl).toHaveBeenCalledTimes(3);
  });
});
