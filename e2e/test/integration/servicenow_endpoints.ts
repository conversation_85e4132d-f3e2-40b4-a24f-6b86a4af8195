import { beforeAll, describe, expect, jest, test } from "@jest/globals";

import {
  ClaimSearchRequest,
  ClaimSearchRequestTerms,
  EmployeeSearchRequest,
  EmployeeSearchRequestTerms,
  postClaimsSearch,
  postEmployeesSearch,
} from "../../src/_api";
import config from "../../src/config";
import { ClaimGenerator } from "../../src/generation/Claim";
import { ScenarioSpecification } from "../../src/generation/Scenario";
import {
  getAuthManager,
  getEmployeePool,
  getPortalSubmitter,
} from "../../src/util/common";
import {
  getApiCredentialsForSnow,
  getClaimantCredentials,
} from "../../src/util/credentials";

let token: string;

/**
 * @group stable
 */
describe("ServiceNow API Endpoint", () => {
  beforeAll(async () => {
    jest.retryTimes(3);
    const authenticator = getAuthManager();
    const apiCreds = getApiCredentialsForSnow();
    token = await authenticator.getAPIBearerToken(apiCreds);
  });

  test("Checking the Employee Search API endpoint available", async () => {
    const employee = await getEmployeePool();
    const claimant = employee.pick({ wages: "ineligible" });
    console.log(claimant.ssn);

    const terms: EmployeeSearchRequestTerms = {
      first_name: claimant.first_name,
      last_name: claimant.last_name,
      tax_identifier: claimant.ssn,
    };
    const request: EmployeeSearchRequest = {
      terms: terms,
    };
    const pmflApiOptions = {
      baseUrl: config("API_BASEURL"),
      headers: {
        Authorization: `Bearer ${token}`,
        "Mass-PFML-Agent-ID": `Integration test`,
        "User-Agent": `PFML Integration Testing (ServiceNow)`,
      },
    };
    const employeeSearch = await postEmployeesSearch(request, pmflApiOptions);
    expect(employeeSearch.status).toBe(200);
    expect(employeeSearch.data.data?.[0].tax_identifier).toContain(
      claimant.ssn
    );
    expect(employeeSearch.data.data?.[0].first_name).toContain(
      claimant.first_name
    );
    expect(employeeSearch.data.data?.[0].last_name).toContain(
      claimant.last_name
    );
  }, 60_000);

  test("Checking the Claim Search API endpoint available", async () => {
    const submitter = getPortalSubmitter();
    const CS_test: ScenarioSpecification = {
      employee: { mass_id: true, wages: "eligible" },
      claim: {
        label: "MHAP1",
        shortClaim: true,
        reason: "Serious Health Condition - Employee",
        docs: {
          HCP: {},
          MASSID: {},
        },
      },
    };

    const claim = ClaimGenerator.generate(
      await getEmployeePool(),
      CS_test.employee,
      CS_test.claim
    );

    const res = await submitter.submit(claim, getClaimantCredentials());
    console.log(res.fineos_absence_id);
    console.log("API submission completed successfully");

    const pmflApiOptions = {
      baseUrl: config("API_BASEURL"),
      headers: {
        Authorization: `Bearer ${token}`,
        "Mass-PFML-Agent-ID": `Integration test`,
        "User-Agent": `PFML Integration Testing (ServiceNow)`,
      },
    };

    const terms: ClaimSearchRequestTerms = {
      employee_id: res.employee_id as string,
      search: res.fineos_absence_id,
    };
    const request: ClaimSearchRequest = {
      terms: terms,
    };
    const claimSearch = await postClaimsSearch(request, pmflApiOptions);
    expect(claimSearch.status).toBe(200);
    expect(claimSearch.data.data?.[0].fineos_absence_id).toBe(
      res.fineos_absence_id
    );
  }, 60_000);
});
