/**
 * This test asserts that the PFML API is able to create payment validation
 * tasks in FINEOS.
 *
 * The FineosTaskAutomationStep of pub-payments-process-fineos reads from the
 * `payment_issue_resolution` table to create these tasks. Because the
 * FineosTaskAutomationStep runs at roughly the same time as this test during
 * the morning run, the payment validation tasks are sometimes created after
 * this test runs. To reduce flakiness around this temporal coupling, the test
 * looks back two weekdays instead of one when selecting a prepared absence case
 * for testing.
 *
 * The test will prepare a new absence case for future testing by adding payment
 * issue resolutions to one of its payments.
 */

import { afterAll, beforeAll, jest, test } from "@jest/globals";
import { format, startOfWeek, subBusinessDays, subWeeks } from "date-fns";
import { convertToTimeZone } from "date-fns-timezone";
import d from "debug";
import pRetry from "p-retry";
import { v4 as uuidv4 } from "uuid";

import config from "../../src/config";
import InfraClient from "../../src/InfraClient";
import { createIntentionalSkipFlag } from "../../src/util/skipFlag";
import { getTypedKeys } from "../../src/util/typeUtils";
// The `describeIf` import is renamed to `describe` so the reporter
// can determine test block names
import { describeIf as describe } from "../util";
import { ClaimPage, Fineos } from "./../../src/submission/fineos.pages";
import { PaymentValidationTask } from "./../../src/types";

jest.setTimeout(1000 * 300);

const isTrainingEnvironment = ["training", "trn2"].includes(
  config("ENVIRONMENT")
);

const paymentTasks: PaymentTaskValidationConfig = {
  "EFT Account Information Error": {
    payment_issue_resolution_scenario_config_id: 3,
    fineos_task_type_id: 14,
  },
  "Exempt Employer": {
    payment_issue_resolution_scenario_config_id: 4,
    fineos_task_type_id: 3,
  },
  "Bank Processing Error": {
    payment_issue_resolution_scenario_config_id: 9,
    fineos_task_type_id: 13,
  },
  "Address Validation Error": {
    payment_issue_resolution_scenario_config_id: 11,
    fineos_task_type_id: 12,
  },
  "Invalid Payment Waiting Week": {
    payment_issue_resolution_scenario_config_id: 12,
    fineos_task_type_id: 4,
  },
};

const paymentValidationTasks = getTypedKeys(paymentTasks);
const skipFlag = createIntentionalSkipFlag(isTrainingEnvironment);
/**
 * @group unstable
 */
describe(!isTrainingEnvironment)("Payment task validation", () => {
  let absenceCaseId: string;

  beforeAll(async () => {
    absenceCaseId = await pRetry(() => getAbsenceCaseForTesting(today), {
      retries: 3,
    });
  });

  test.each(paymentValidationTasks)(
    `${skipFlag}%s is created in FINEOS after a payment validation record is created the day prior.`,
    async (task) => {
      console.log(`Checking for "${task}" on absence case ${absenceCaseId}`);

      await Fineos.withBrowser(
        async (page) => {
          const claimPage = await ClaimPage.visit(page, absenceCaseId);
          await claimPage.tasks(async (tasksPage) => {
            await tasksPage.assertTaskExists(task);
            await tasksPage.clickMostRecentTask(task);
            await tasksPage.assertTaskDetails(
              {
                status: "Open",
                description: TASK_DESCRIPTION,
              },
              task
            );
          });
        },
        { debug: false }
      );
    },
    60_000
  );

  afterAll(async () => {
    await pRetry(
      () => prepareAbsenceCaseForFutureTesting(paymentTasks, today),
      {
        retries: 3,
        onFailedAttempt(error) {
          const debug = d("e2e:integration");
          debug(error);
        },
      }
    );
  });
});

async function getAbsenceCaseForTesting(today: Date) {
  // The absence case selected for testing is one prepared two weekdays in the
  // past because we cannot guarantee that the FineosTaskAutomationStep of
  // pub-payments-process-fineos will complete before this test runs in the
  // morning. The payment validation tasks in FINEOS may not be populated until
  // this step is completed.

  // subBusinessDays is not aware of holidays and only accounts for weekdays.
  const twoWeekdaysAgo = subBusinessDays(today, 2);
  const twoWeekdaysAgoString = format(twoWeekdaysAgo, "yyyy/MM/dd");

  const query = `
    SELECT fineos_absence_id
    FROM claim
    JOIN payment ON payment.claim_id = claim.claim_id
    WHERE payment_id = (
      SELECT payment_id
      FROM payment_issue_resolution
      WHERE created_at::date = '${twoWeekdaysAgoString}'::date
        AND description = '${TASK_DESCRIPTION}'
        AND task_processing_outcome = 'TASK_CREATION_SUCCEEDED'
      ORDER BY created_at DESC
      LIMIT 1
    )
    ORDER BY payment.created_at DESC
    LIMIT 1;
  `;

  const command = infraClient.buildRdsCommand(query, rdsCommandFlags);

  const absenceCaseId = (
    await infraClient.sendAndWaitForSSMShellCommand(command)
  ).StandardOutputContent?.trim();

  if (!absenceCaseId) {
    throw Error("absence case ID not found");
  }

  return absenceCaseId;
}

async function prepareAbsenceCaseForFutureTesting(
  paymentTasks: PaymentTaskValidationConfig,
  today: Date
) {
  const { absenceCaseId, paymentId } = await getPaymentForFutureTesting(today);
  const importLogId = await getImportLogId();
  await insertPaymentIssueResolutions(importLogId, paymentId, paymentTasks);

  console.log(
    `created payment validation records for ${absenceCaseId}, ` +
      `payment ${paymentId}`
  );
}

/**
 * Gets a payment for a claim that has no payments with any existing payment
 * issue resolutions.
 *
 * The related absence case should be no older than four Sundays ago.
 */
async function getPaymentForFutureTesting(today: Date) {
  const mostRecentSunday = startOfWeek(today);
  const fourSundaysAgo = subWeeks(mostRecentSunday, 3);
  const minClaimCreationDate = format(fourSundaysAgo, "yyyy/MM/dd");

  const query = `
    SELECT json_build_object(
      'absenceCaseId', claim.fineos_absence_id,
      'paymentId', payment.payment_id
    )
    FROM payment
    JOIN claim ON claim.claim_id = payment.claim_id
    WHERE claim.claim_id = (
      SELECT claim.claim_id
      FROM claim
      JOIN payment ON payment.claim_id = claim.claim_id
      LEFT JOIN payment_issue_resolution
        ON payment_issue_resolution.payment_id = payment.payment_id
      WHERE payment.absence_case_creation_date >= '${minClaimCreationDate}'::date
      GROUP BY claim.claim_id
      HAVING COUNT(payment.payment_id) > 0
        AND COUNT(payment_issue_resolution.payment_id) = 0
      ORDER BY claim.created_at ASC
      LIMIT 1
    )
    ORDER BY payment.created_at DESC
    LIMIT 1;
  `;

  const response = await infraClient.sendAndWaitForSSMShellCommand(
    infraClient.buildRdsCommand(query, rdsCommandFlags),
    { retries: 20, retryInterval: 1500 }
  );

  if (!response.StandardOutputContent) {
    throw Error("payment for future testing not found");
  }

  const rawText = response?.StandardOutputContent.substring(
    response?.StandardOutputContent.indexOf("{")
  );

  const payment: Payment = JSON.parse(rawText);
  return payment;
}

type Payment = { absenceCaseId: string; paymentId: string };

/**
 * Finds the latest PaymentExtractStep log ID that would have marked the payment
 * as having issues needing resolution.
 */
async function getImportLogId() {
  const query = `
    SELECT import_log_id 
    FROM import_log 
    WHERE source = 'PaymentExtractStep'
    ORDER BY created_at DESC
    LIMIT 1;
  `;

  const command = infraClient.buildRdsCommand(query, rdsCommandFlags);

  const importLogId = (
    await infraClient.sendAndWaitForSSMShellCommand(command)
  ).StandardOutputContent?.trim();

  if (!importLogId) {
    throw Error("import log ID not found");
  }

  return importLogId;
}

async function insertPaymentIssueResolutions(
  importLogId: string,
  paymentId: string,
  paymentTasks: PaymentTaskValidationConfig
) {
  const insertStatement = `
    INSERT INTO payment_issue_resolution(
      payment_issue_resolution_id, payment_id, description,
      payment_issue_resolution_scenario_config_id, fineos_task_type_id,
      import_log_id
    )
    VALUES
  `;

  const records = Object.values(paymentTasks).map(
    (config) => `
      (
        '${uuidv4()}', '${paymentId}', '${TASK_DESCRIPTION}',
        ${config.payment_issue_resolution_scenario_config_id},
        ${config.fineos_task_type_id}, '${importLogId}'
      )
    `
  );

  const joinedRecords = records.join(",");
  const query = insertStatement + joinedRecords + ";";
  const command = infraClient.buildRdsCommand(query);

  await infraClient.sendAndWaitForSSMShellCommand(command, {
    retries: 20,
    retryInterval: 1500,
  });
}

type PaymentTaskValidationConfig = Record<
  PaymentValidationTask,
  PaymentTaskValidationConfigEntries
>;

type PaymentTaskValidationConfigEntries = {
  payment_issue_resolution_scenario_config_id: number;
  fineos_task_type_id: number;
};

const TASK_DESCRIPTION =
  "PFML Integration Testing\nThis validation error was created for testing purposes.";

const infraClient = InfraClient.create(config);

/**
 * Strips out table formatting, returning only the value.
 */
const rdsCommandFlags = ["-t", "-A"];

const today = convertToTimeZone(new Date(), { timeZone: "America/New_York" });
