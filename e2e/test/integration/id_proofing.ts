import { beforeAll, expect, test } from "@jest/globals";

import { postRmvCheck, RMVCheckRequest } from "../../src/api";
import config from "../../src/config";
import { getAuthManager } from "../../src/util/common";
import { getApiCredentialsForFineos } from "../../src/util/credentials";
import { createIntentionalSkipFlag } from "../../src/util/skipFlag";
// The `describeIf` import is renamed to `describe` so the reporter
// can determine test block names
import { describeIf as describe, id_proofing } from "../util";

const rmvCheckIsFullyMocked = config("RMV_CHECK_IS_FULLY_MOCKED");

let token: string;
const skipFlag = createIntentionalSkipFlag(rmvCheckIsFullyMocked);

/**
 * @group stable
 */
describe(!rmvCheckIsFullyMocked)("ID Proofing Tests", () => {
  beforeAll(async () => {
    const authenticator = getAuthManager();
    const apiCreds = getApiCredentialsForFineos();
    token = await authenticator.getAPIBearerToken(apiCreds);
  });

  test.each(id_proofing)(
    `${skipFlag}Claimant RMV check should be %s`,
    async (
      description: string,
      rmvCheckRequest: RMVCheckRequest,
      message: string,
      verified: boolean
    ) => {
      const pmflApiOptions = {
        baseUrl: config("API_BASEURL"),
        headers: {
          Authorization: `Bearer ${token}`,
          "User-Agent": `PFML Integration Testing (RMV Check ${description})`,
        },
      };
      const res = await postRmvCheck(rmvCheckRequest, pmflApiOptions);
      expect(res.status).toBe(200);
      expect(res.data.data?.description).toBe(message);
      expect(res.data.data?.verified).toBe(verified);
    },
    60_000
  );
});
