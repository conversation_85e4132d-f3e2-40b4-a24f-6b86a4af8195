/**
 * This test asserts that the PFML API is able to match application forms uploaded
 * to existing claims for Claimants in FINEOS
 *
 * This test first finds an active claim to use for testing
 * It will then generate a presigned url from bucket "massgov-pfml-test-bucket" and object "TestDoc.pdf"
 * and then send a JSON payload with the url and claimant information to the API endpoint
 * The information will be matched to the claimant on FINEOS and the document will be uploaded
 * The test will then assert that this new document is visible on FINEOS
 */

import { beforeAll, describe, expect, test } from "@jest/globals";
import { convertToTimeZone } from "date-fns-timezone";
import d from "debug";
import pRetry from "p-retry";

import {
  CertificationDocumentRequest,
  postClaimsMatchCertDocToClaim,
} from "../../src/api";
import config from "../../src/config";
import InfraClient from "../../src/InfraClient";
import { ClaimPage, Fineos } from "../../src/submission/fineos.pages";
import { getAuthManager } from "../../src/util/common";
import { getApiCredentialsForFineos } from "../../src/util/credentials";

let token: string;
let activeClaim: ActiveClaimQueryResponse;
const infra = InfraClient.create(config);
const debug = d("e2e:integration");
const rdsCommandFlags = ["-t", "-A"];

/**
 * @group unstable
 */

describe("Document matching claimant test", () => {
  beforeAll(async () => {
    const authenticator = getAuthManager();
    const apiCreds = getApiCredentialsForFineos();
    token = await authenticator.getAPIBearerToken(apiCreds);
    activeClaim = await getSingleActiveClaimForTesting();
  });

  test("Document should be uploaded and visible in FINEOS", async () => {
    const res = await submitCertificationDocument();
    expect(res.status).toBe(201);
    const absenceId = activeClaim.absenceId;
    expect(res.data.message).toBe(
      `Successfully uploaded certification document to claim with absence id ${absenceId}`
    );
    await checkForUploadedDocument(activeClaim.absenceId);
  }, 60_000);
});

async function getSingleActiveClaimForTesting() {
  const query = `
  SELECT json_build_object('firstName',e.first_name,'lastName',e.last_name,'dateOfBirth',e.date_of_birth,'absenceId',c.fineos_absence_id,'taxIdentifier',t.tax_identifier)
  FROM (
    SELECT employee.*
    FROM employee
    JOIN claim c ON employee.employee_id = c.employee_id
    GROUP BY employee.employee_id
    HAVING COUNT(c.claim_id) = 1
  ) AS e
  JOIN claim c ON e.employee_id = c.employee_id
  JOIN tax_identifier t ON e.tax_identifier_id = t.tax_identifier_id
  WHERE c.fineos_absence_status_id = '1'
  LIMIT 1;
  `;
  const response = await infra.sendAndWaitForSSMShellCommand(
    infra.buildRdsCommand(query, rdsCommandFlags),
    { retries: 20, retryInterval: 1500 }
  );
  if (!response.StandardOutputContent) {
    throw Error("No active claims found");
  }
  const activeClaim: ActiveClaimQueryResponse = JSON.parse(
    response?.StandardOutputContent
  );
  return activeClaim;
}

async function submitCertificationDocument() {
  const pmflApiOptions = {
    baseUrl: config("API_BASEURL"),
    headers: {
      Authorization: `Bearer ${token}`,
      "User-Agent": `PFML Integration Testing Match CertificationDocument`,
    },
  };
  const certificationDocumentData = {
    first_name: activeClaim.firstName,
    last_name: activeClaim.lastName,
    date_of_birth: activeClaim.dateOfBirth,
    tax_identifier: activeClaim.taxIdentifier,
  };
  const bucketName = "massgov-pfml-test-bucket";
  const objectKey = "TestDoc.pdf";
  const s3BucketUrl = await infra.generatePresignedUrl(bucketName, objectKey);
  const certificationRequestBody: CertificationDocumentRequest = {
    document_type: "DFML_FAMILY_SERIOUS_HEALTH_COND",
    document_data: certificationDocumentData,
    s3_bucket_url: s3BucketUrl,
  };
  return await postClaimsMatchCertDocToClaim(
    certificationRequestBody,
    pmflApiOptions
  );
}

async function checkForUploadedDocument(fineosAbsenceId: string) {
  await pRetry(
    () =>
      Fineos.withBrowser(
        async (page) => {
          const claimPage = await ClaimPage.visit(page, fineosAbsenceId);
          const today = convertToTimeZone(new Date(), {
            timeZone: "America/New_York",
          });
          await claimPage.documents(async (documentsPage) => {
            await documentsPage.assertRecentDocumentExists(
              today,
              fineosAbsenceId
            );
          });
        },
        { debug: false }
      ),
    {
      retries: 2,
      onFailedAttempt(error) {
        debug(error);
      },
    }
  );
}

type ActiveClaimQueryResponse = {
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  absenceId: string;
  taxIdentifier: string;
};
