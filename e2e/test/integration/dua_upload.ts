import { afterAll, beforeAll, jest, test } from "@jest/globals";
import { format } from "date-fns";
import { formatToTimeZone } from "date-fns-timezone";
import * as fs from "fs";
import * as os from "os";
import * as path from "path";

import config from "../../src/config";
import dataDirectory from "../../src/generation/DataDirectory";
import EmployeePool from "../../src/generation/Employee";
import EmployerPool from "../../src/generation/Employer";
import DOR from "../../src/generation/writers/DOR";
import InfraClient from "../../src/InfraClient";
import { createIntentionalSkipFlag } from "../../src/util/skipFlag";
import { quarters } from "../../src/util/writers";
import { describeIf as describe } from "../util";

const env = config("ENVIRONMENT");

const isTraining = ["training", "trn2"].includes(env);

const MINUTES = 1_000 * 60;
jest.setTimeout(60 * MINUTES);

const today = new Date();

const infra = InfraClient.create(config);

const skipFlag = createIntentionalSkipFlag(isTraining);
/**
 * @group unstable
 */
describe(!isTraining)("DUA upload and service agreements", () => {
  let tempDir: string;
  beforeAll(async () => {
    const tempDirPrefix = path.join(os.tmpdir(), "dua");
    tempDir = await fs.promises.mkdtemp(tempDirPrefix);
  });

  afterAll(async () => {
    if (tempDir) {
      await fs.promises.rm(tempDir, { recursive: true });
    }
  });
  test(
    `${skipFlag}Generate, upload and process DUA files`,
    async () => {
      const storage = dataDirectory("initial", tempDir);
      await storage.prepare();
      const [employers, employees] = await generateDuaEmployersEmployees();
      const employerFile = storage.dorFile("DORDFMLEMP");
      const employeeFile = storage.dorFile("DORDFML");
      const duaFile = storage.dorFile("DORWAGE", "txt");
      await DOR.writeDuaWageFile(employers, employees, duaFile);
      await DOR.writeEmployersFile(employers, employerFile);
      await DOR.writeEmployeesFile(employers, employees, employeeFile);
      await uploadAndRunETL(
        employerFile,
        employeeFile,
        duaFile,
        infra,
        employers
      );
      // Save the employers.json file. The withholdings are used to verify
      // the leave admin.
      await employers.save(storage.employers);
    },
    15 * MINUTES
  );
  test("Check wages are populated correctly", async () => {
    const taxIdentifier = generateIdentifier(4, today);
    const rdsQuery = `
  SELECT  w.employee_qtr_wages
  FROM employee e
  JOIN wages_and_contributions w ON e.employee_id = w.employee_id
  JOIN tax_identifier t ON e.tax_identifier_id = t.tax_identifier_id
  WHERE t.tax_identifier = CAST(${taxIdentifier} AS text)
  LIMIT 1;
`;
    const response = await infra.sendAndWaitForSSMShellCommand(
      infra.buildRdsCommand(rdsQuery, ["-t", "-A"])
    );
    const output = response.StandardOutputContent?.trim();
    if (!output) {
      throw new Error("No wages found");
    }

    const formatOutput = parseInt(output).toString();

    expect(formatOutput).toBe("15000");
  });
});

/**
 * Generate employer and employee DOR files
 */
async function generateDuaEmployersEmployees(): Promise<
  [EmployerPool, EmployeePool]
> {
  // Using 4 as prefix here to avoid collisions with DOR integration test which uses 1-3
  const identifier = generateIdentifier(4, today);

  const employers = EmployerPool.generate(1);
  const basePeriodQuarters = quarters(new Date(), 6)
    .map((quarter) => format(quarter, "yyyy-MM-dd"))
    .reverse();
  const q1 = basePeriodQuarters[0];
  const q2 = basePeriodQuarters[1];
  const q3 = basePeriodQuarters[2];
  const q4 = basePeriodQuarters[3];
  const q5 = basePeriodQuarters[4];
  const q6 = basePeriodQuarters[5];
  const employees = EmployeePool.generateWithOccupations(1, {}, [
    {
      employer: employers.pick(),
      wages: [
        { quarter: q1, source: "ui", wages: 15_000 },
        { quarter: q2, source: "ui", wages: 15_000 },
        { quarter: q3, source: "ui", wages: 15_000 },
        { quarter: q4, source: "ui", wages: 15_000 },
        { quarter: q5, source: "ui", wages: 15_000 },
        { quarter: q6, source: "ui", wages: 15_000 },
      ],
    },
  ]);
  employees.pick().ssn = formatAsSSN(identifier);
  return [employers, employees];
}

function generateIdentifier(prefix: number, date?: Date) {
  const identifier =
    prefix.toString() +
    formatToTimeZone(date ?? today, "MMDDYYYY", {
      timeZone: "America/New_York",
    });
  return identifier;
}

function formatAsSSN(identifier: string) {
  const strippedIdentifier = identifier.replace(/-/g, "");
  const segmentA = strippedIdentifier.slice(0, 3);
  const segmentB = strippedIdentifier.slice(3, 5);
  const segmentC = strippedIdentifier.slice(5);
  return `${segmentA}-${segmentB}-${segmentC}`;
}

async function uploadAndRunETL(
  employerFile: string,
  employeeFile: string,
  duaWageFile: string,
  infra: InfraClient,
  employers: EmployerPool
) {
  await infra.uploadRevenueFiles([employerFile, employeeFile, duaWageFile]);
  const start = new Date().getTime();
  await infra.runEcsTaskAndWaitForCompletion("dua-wages-from-dor-import", 5, [
    { name: "DECRYPT", value: "false" },
  ]);
  await infra.runEcsTaskAndWaitForCompletion("dor-import", 5, [
    { name: "DECRYPT", value: "false" },
  ]);

  await infra.runEcsTaskAndWaitForCompletion("load-employers-to-fineos", 5, [
    { name: "EMPLOYER_LOAD_MODE", value: "updates" },
    {
      name: "SERVICE_AGREEMENT_LOAD",
      value: "true",
    },
  ]);

  const employerIds = await getEmployerIds(infra, employers);

  await infra.runEcsTaskAndWaitForCompletion(
    "load-service-agreements-to-fineos",
    5,
    [
      {
        name: "SERVICE_AGREEMENT_LOAD",
        value: "true",
      },
      { name: "ENABLE_SERVICE_AGREEMENT_VERSION_FINEOS_WRITE", value: "1" },
      { name: "EMPLOYER_SERVICE_AGREEMENT_VERSION_UPDATE_LIMIT", value: "3" },
      {
        name: "SERVICE_AGREEMENT_PRESELECTED_EMPLOYERS",
        value: employerIds.join(","),
      },
    ]
  );
  const end = new Date().getTime();
  return { start, end };
}

async function getEmployerIds(infra: InfraClient, employers: EmployerPool) {
  const feins = Array.from(employers)
    .map((employer) => `'${employer.fein.replace(/-/g, "")}'`)
    .join(",");
  const rdsQuery = `SELECT STRING_AGG(employer_id::text, ',') from employer WHERE employer_fein IN (${feins});`;
  const response = await infra.sendAndWaitForSSMShellCommand(
    infra.buildRdsCommand(rdsQuery, ["-t", "-A"])
  );
  const employerIdsCSV = response.StandardOutputContent?.trimEnd();
  if (!employerIdsCSV) {
    throw Error("No employer ids returned for feins.");
  }
  return employerIdsCSV.split(",");
}
