/**
 * This test will:
 *
 * 1) Query New Relic for TestResultInstances that contain payment fields for
 *    the current environment.
 * 2) Navigate to the the “Payments” tab in FINEOS for the absence case and
 *    confirm payment was made with the correct information. (For reference:
 *    https://lwd.atlassian.net/wiki/spaces/DD/pages/2673705122/Payment+Flow)
 *
 * Payments are reported by e2e tests, specifically tests that validate
 * scheduled payments have the correct information.
 */

import { beforeAll, expect, test } from "@jest/globals";
import { startOfDay } from "date-fns";
import d from "debug";
import pRetry from "p-retry";

import config from "../../src/config";
import NewRelicClient from "../../src/NewRelicClient";
import {
  ClaimPage,
  Fineos,
  PaidLeave,
} from "../../src/submission/fineos.pages";
import { PaymentMadeTableRow } from "../../src/types";
import { addTwoDollarAmountStrings } from "../../src/util/common";
import { createIntentionalSkipFlag } from "../../src/util/skipFlag";
// The `describeIf` import is renamed to `describe` so the reporter
// can determine test block names
import { describeIf as describe } from "../util";

const debug = d("e2e:integration");
const environment = config("ENVIRONMENT");

// Skip these environments due to weekly data restores
const skippedEnvs = new Set(["training", "trn2"]);
const isInSkippedEnv = skippedEnvs.has(environment);
const skipFlag = createIntentionalSkipFlag(isInSkippedEnv);

/**
 * @group morning
 */
describe(!isInSkippedEnv)("Payments are made with correct amounts", () => {
  let expectedPayments: Readonly<CategorizedExpectedPayments>;
  let madePaymentRecords: CategorizedMadePaymentRecords;

  beforeAll(async () => {
    expectedPayments = await getExpectedPayments();
    madePaymentRecords = await getMadePaymentRecords(
      expectedPayments.fineosAbsenceId
    );
  }, 1000 * 60 * 3);

  test(`${skipFlag}Expected payments were made`, () => {
    const madeFitPayments = getRelevantMadePayments(
      madePaymentRecords.fitPayments,
      expectedPayments.fitPayments.length
    );

    expect(madeFitPayments).toEqual(expectedPayments.fitPayments);

    const madeMainPayments = getRelevantMadePayments(
      madePaymentRecords.mainPayments,
      expectedPayments.mainPayments.length
    );

    expect(madeMainPayments).toEqual(expectedPayments.mainPayments);

    const madeSitPayments = getRelevantMadePayments(
      madePaymentRecords.sitPayments,
      expectedPayments.sitPayments.length
    );

    expect(madeSitPayments).toEqual(expectedPayments.sitPayments);
  });

  test(`${skipFlag}Made payments are active`, () => {
    expect(madePaymentRecords.fitPayments.every(isActivePayment)).toBe(true);
    expect(madePaymentRecords.sitPayments.every(isActivePayment)).toBe(true);
    expect(madePaymentRecords.mainPayments.every(isActivePayment)).toBe(true);
  });
});

interface CategorizedExpectedPayments {
  fineosAbsenceId: string;
  fitPayments: readonly Payment[];
  mainPayments: readonly Payment[];
  sitPayments: readonly Payment[];
}

interface Payment {
  readonly amount: string;
  readonly date: string;
  readonly payee: string;
}

interface CategorizedMadePaymentRecords {
  readonly fitPayments: readonly MadePaymentRecord[];
  readonly mainPayments: readonly MadePaymentRecord[];
  readonly sitPayments: readonly MadePaymentRecord[];
}

type MadePaymentRecord = Readonly<PaymentMadeTableRow>;

async function getExpectedPayments() {
  const nr = new NewRelicClient(
    config("NEWRELIC_APIKEY"),
    +config("NEWRELIC_ACCOUNTID")
  );

  const query = `
  SELECT
  fineosAbsenceId, mainPayments, sitPayments, fitPayments
  FROM TestResultInstance
  WHERE mainPayments IS NOT NULL
  AND sitPayments IS NOT NULL
  AND fitPayments IS NOT NULL
  AND environment = '${environment}'
  ORDER BY timestamp DESC
  SINCE 1 week ago
  LIMIT 30`;

  const expectedPaymentData = await pRetry(
    () => nr.nrql<ExpectedPaymentData>(query),
    {
      retries: 2,
      onFailedAttempt(error) {
        debug(error);
      },
    }
  );

  const candidates = expectedPaymentData.map(toCategorizedExpectedPayments);
  const expectedPayments =
    candidates.find(isEarliestMainPaymentBeforeToday) ?? null;

  if (!expectedPayments) {
    throw Error("Unable to find suitable expected payments for testing.");
  }

  expectedPayments.fitPayments =
    expectedPayments.fitPayments.filter(isEarlierThanToday);
  expectedPayments.mainPayments =
    expectedPayments.mainPayments.filter(isEarlierThanToday);
  expectedPayments.sitPayments =
    expectedPayments.sitPayments.filter(isEarlierThanToday);

  // Main payments made to the claimant that are scheduled on the same day are
  // combined into one.
  expectedPayments.mainPayments = combineSameDayPayments(
    expectedPayments.mainPayments
  );

  return expectedPayments;
}

interface ExpectedPaymentData {
  readonly fineosAbsenceId: string;
  readonly fitPayments: string;
  readonly mainPayments: string;
  readonly sitPayments: string;
  readonly timestamp: number;
}

function toCategorizedExpectedPayments(
  data: ExpectedPaymentData
): CategorizedExpectedPayments {
  const fitPaymentRecords: readonly ExpectedPaymentRecord[] = JSON.parse(
    data.fitPayments
  );

  const mainPaymentRecords: readonly ExpectedPaymentRecord[] = JSON.parse(
    data.mainPayments
  );

  const sitPaymentRecords: readonly ExpectedPaymentRecord[] = JSON.parse(
    data.sitPayments
  );

  return {
    fineosAbsenceId: data.fineosAbsenceId,
    fitPayments: fitPaymentRecords.map(toPayment),
    mainPayments: mainPaymentRecords.map(toPayment),
    sitPayments: sitPaymentRecords.map(toPayment),
  };
}

interface ExpectedPaymentRecord {
  readonly netAmount: string;
  readonly netPaymentAmount: string;
  readonly payee: string;
  readonly processingDate: string;
}

function toPayment(value: ExpectedPaymentRecord | MadePaymentRecord): Payment {
  if (isExpectedPaymentRecord(value)) {
    return {
      amount: value.netPaymentAmount,
      date: value.processingDate,
      // CPS-12839 adds a number 1-20 to the SIT/FIT payee names.
      // This value doesn't appear to be consistent as the claim
      // goes through payment processing, so it's just stripped out.
      payee: value.payee.replace(/[0-9]+$/, ""),
    };
  }

  return {
    amount: value.netPayment,
    date: value.effectiveDate,
    payee: value.payeeName.replace(/[0-9]+$/, ""),
  };
}

function isExpectedPaymentRecord(
  record: ExpectedPaymentRecord | MadePaymentRecord
): record is ExpectedPaymentRecord {
  return "payee" in record;
}

function isEarliestMainPaymentBeforeToday(
  records: Readonly<CategorizedExpectedPayments>
) {
  const mainPayments = [...records.mainPayments];
  mainPayments.sort(compareByDate);
  const resultDate = new Date(mainPayments[0].date);
  return resultDate < today;
}

function compareByDate(a: HasDate, b: HasDate) {
  return new Date(a.date).getTime() - new Date(b.date).getTime();
}

interface HasDate {
  readonly date: string;
}

const today = startOfDay(new Date());

async function getMadePaymentRecords(fineosAbsenceId: string) {
  const records = await pRetry(
    () =>
      Fineos.withBrowser(
        async (page) => {
          await ClaimPage.visit(page, fineosAbsenceId);
          const paidLeave = await PaidLeave.visit(page, fineosAbsenceId);
          return paidLeave.scrapePaymentsMade();
        },
        { debug: false }
      ),
    {
      retries: 2,
      onFailedAttempt(error) {
        debug(error);
      },
    }
  );

  debug(records);
  return categorizeMadePaymentRecords(records);
}

function categorizeMadePaymentRecords(
  payments: readonly MadePaymentRecord[]
): CategorizedMadePaymentRecords {
  return {
    fitPayments: payments.filter(isFitPayment),
    mainPayments: payments.filter(isMainPayment),
    sitPayments: payments.filter(isSitPayment),
  };
}

function isFitPayment(row: MadePaymentRecord) {
  return row.payeeName.includes("FIT");
}

function isMainPayment(record: MadePaymentRecord) {
  return !isFitPayment(record) && !isSitPayment(record);
}

function isSitPayment(record: MadePaymentRecord) {
  return record.payeeName.includes("SIT");
}

function isEarlierThanToday(value: HasDate) {
  return new Date(value.date) < today;
}

function combineSameDayPayments(payments: readonly Payment[]) {
  const combinedPayments: Payment[] = [];

  payments.forEach((payment) => {
    const idx = combinedPayments.findIndex((combinedPayment) =>
      isSameDay(combinedPayment, payment)
    );

    if (idx === -1) {
      combinedPayments.push(payment);
    } else {
      const sameDayPayment = combinedPayments[idx];
      combinedPayments[idx] = addPayments(sameDayPayment, payment);
    }
  });

  return combinedPayments;
}

function isSameDay(a: HasDate, b: HasDate) {
  return a.date === b.date;
}

/**
 * Returns the sum of two `Payment`s.
 *
 * Returned `Payment` has `date` and `payee` of `augend`.
 */
function addPayments(augend: Payment, addend: Payment): Payment {
  return {
    amount: addTwoDollarAmountStrings(augend.amount, addend.amount),
    date: augend.date,
    payee: augend.payee,
  };
}

/**
 * Creates `Payment`s from `MadePaymentRecord`s, returning only those that are
 * relevant for comparison with expected `Payment`s.
 */
function getRelevantMadePayments(
  madePaymentRecords: readonly MadePaymentRecord[],
  expectedPaymentCount: number
) {
  return madePaymentRecords
    .map(toPayment)
    .sort(compareByDate)
    .slice(0, expectedPaymentCount);
}

function isActivePayment(payment: MadePaymentRecord) {
  return payment.status === "Active";
}
