import { beforeAll, describe, expect, jest, test } from "@jest/globals";

import {
  EmployerClaimRequestBody,
  getUsersCurrent,
  UserResponse,
} from "../../src/_api";
import config from "../../src/config";
import { ClaimGenerator } from "../../src/generation/Claim";
import { Employer } from "../../src/generation/Employer";
import { ScenarioSpecification } from "../../src/generation/Scenario";
import AuthenticationManager from "../../src/submission/AuthenticationManager";
import { Credentials } from "../../src/types";
import {
  getAuthManager,
  getEmployeePool,
  getEmployerPool,
  getPortalSubmitter,
} from "../../src/util/common";
import {
  generateCredentials,
  getClaimantCredentials,
} from "../../src/util/credentials";
import { getLeaveAdminToken } from "../../src/util/myMassGov";

let authenticator: AuthenticationManager;
let leaveAdminCreds1: Credentials;
let leaveAdminCreds2: Credentials;
let employer: Employer;

jest.retryTimes(3);

/**
 * @group morning
 */
describe("Series of test that verifies LAs are properly registered in Fineos", () => {
  beforeAll(async () => {
    leaveAdminCreds1 = generateCredentials();
    leaveAdminCreds2 = generateCredentials();
    authenticator = getAuthManager();
    employer = (await getEmployerPool()).pick({
      withholdings: "non-exempt",
      metadata: { has_employees: true },
    });
  });

  test("Register Leave Admins", async () => {
    const fein = employer.fein;
    try {
      await authenticator.registerLeaveAdmin(leaveAdminCreds1, fein);
      await authenticator.registerLeaveAdmin(leaveAdminCreds2, fein);
      console.log("Both Leave Admins have been registered Successfully");
    } catch (e) {
      throw new Error(`Unable to register Leave Admins: ${e}`);
    }
  }, 90_000);

  test("Verify Leave Admin", async () => {
    const fein = employer.fein;
    try {
      console.log("Verifying Leave Admin");
      await authenticator.verifyLeaveAdmin(
        leaveAdminCreds1,
        employer.withholdings
      );
      console.log(
        `Leave Admin One successfully verified for ${fein}: ${leaveAdminCreds1.username}`
      );
    } catch (e) {
      throw new Error(`Unable to register/verify Leave Admins: ${e}`);
    }
  }, 90_000);

  test("Check LA user object for has_fineos_registration property", async () => {
    const token1 = await getLeaveAdminToken(leaveAdminCreds1);
    const token2 = await getLeaveAdminToken(leaveAdminCreds2);

    const pmflApiOptions1 = {
      baseUrl: config("API_BASEURL"),
      headers: {
        Authorization: `Bearer ${token1}`,
        "User-Agent": "PFML E2E Integration Testing",
      },
    };
    const pmflApiOptions2 = {
      baseUrl: config("API_BASEURL"),
      headers: {
        Authorization: `Bearer ${token2}`,
        "User-Agent": "PFML E2E Integration Testing",
      },
    };

    const leaveAdminInfo1 = (await getUsersCurrent(
      pmflApiOptions1
    )) as unknown as {
      data: { data: UserResponse };
    };
    const leaveAdminInfo2 = (await getUsersCurrent(
      pmflApiOptions2
    )) as unknown as {
      data: { data: UserResponse };
    };

    if (
      !leaveAdminInfo1.data.data.user_leave_administrators ||
      !leaveAdminInfo2.data.data.user_leave_administrators
    ) {
      throw new Error("No leave administrators found");
    }

    expect(
      leaveAdminInfo1.data.data.user_leave_administrators?.[0].verified
    ).toBe(true);
    expect(
      leaveAdminInfo1.data.data.user_leave_administrators?.[0]
        .has_fineos_registration
    ).toBe(true);
    expect(
      leaveAdminInfo2.data.data.user_leave_administrators?.[0].verified
    ).toBe(false);
    expect(
      leaveAdminInfo2.data.data.user_leave_administrators?.[0]
        .has_fineos_registration
    ).toBe(false);
  }, 60_000);

  test("Submit Claim and confirm the right LA can access the review page", async () => {
    const employeePool = await getEmployeePool();
    const submitter = getPortalSubmitter();
    // @TODO: What is RLAF? If you know what this acronym means, please make it
    //        clear.
    const rlafTest: ScenarioSpecification = {
      employee: { mass_id: true, wages: "eligible", fein: employer.fein },
      claim: {
        label: "MHAP1",
        shortClaim: true,
        reason: "Serious Health Condition - Employee",
        docs: {
          HCP: {},
          MASSID: {},
        },
      },
    };
    const employerResponseBody: EmployerClaimRequestBody = {
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
      fraud: "No",
      previous_leaves: [],
      employer_benefits: [],
    };
    const claim = ClaimGenerator.generate(
      employeePool,
      rlafTest.employee,
      rlafTest.claim
    );
    const res = await submitter.submit(claim, getClaimantCredentials());
    console.log(res.fineos_absence_id);
    console.log("API submission completed successfully");

    try {
      await submitter.submitEmployerResponse(
        leaveAdminCreds1,
        res.fineos_absence_id as string,
        employerResponseBody
      );
    } catch (e) {
      throw new Error(`Employer Response failed: ${e}`);
    }
    const employer2Response = submitter.submitEmployerResponse(
      leaveAdminCreds2,
      res.fineos_absence_id as string,
      employerResponseBody
    );
    await expect(employer2Response).rejects.toMatchObject({
      data: expect.objectContaining({
        message: "User has no leave administrator FINEOS ID",
        status_code: 403,
      }),
    });
  }, 60_000);
});
