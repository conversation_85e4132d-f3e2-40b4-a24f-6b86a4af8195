import { afterAll, beforeAll, expect, jest, test } from "@jest/globals";
import { format, subBusinessDays, subDays } from "date-fns";
import { formatToTimeZone } from "date-fns-timezone";
import d from "debug";
import * as fs from "fs";
import * as os from "os";
import pRetry from "p-retry";
import * as path from "path";

import config from "../../src/config";
import { <PERSON>laimGenerator } from "../../src/generation/Claim";
import dataDirectory from "../../src/generation/DataDirectory";
import EmployeePool from "../../src/generation/Employee";
import EmployerPool from "../../src/generation/Employer";
import DOR from "../../src/generation/writers/DOR";
import InfraClient from "../../src/InfraClient";
import { generateExemptionTestingLeaveScenario } from "../../src/scenarios/service_agreements";
import {
  ClaimantPage,
  EmployerPage,
  Fineos,
} from "../../src/submission/fineos.pages";
import { DorIntegrityReportRow } from "../../src/types";
import {
  getAuthManager,
  getEmployeePool,
  getPortalSubmitter,
  searchEmployeeBySSN,
} from "../../src/util/common";
import {
  generateCredentials,
  getClaimantCredentials,
} from "../../src/util/credentials";
import { createIntentionalSkipFlag } from "../../src/util/skipFlag";
import { assertValidClaim } from "../../src/util/typeUtils";
import {
  ExemptionStatus,
  Scenario,
  scenarios,
} from "../service_agreements_util";
import { describeIf as describe } from "../util";

const env = config("ENVIRONMENT");

const isTraining = ["training", "trn2"].includes(env);

const debug = d("e2e:integration");

const MINUTES = 1000 * 60;
jest.setTimeout(60 * MINUTES);

const today = new Date();

const submitter = getPortalSubmitter();
const infra = InfraClient.create(config);
const firstScenarioPrefix = scenarios[0].prefix;

const employeesWithClaimsSSNs: string[] = [];
const employeeWithoutClaimsSSNs: string[] = [];

let dorNameChangeReport: DorIntegrityReportRow[] = [];

const skipFlag = createIntentionalSkipFlag(isTraining);
/**
 * @group unstable
 */
describe(!isTraining)("DOR upload and service agreements", () => {
  let tempDir: string;
  let hasFirstStepFunctionInstanceFinished = false;
  beforeAll(async () => {
    const tempDirPrefix = path.join(os.tmpdir(), "dor");
    tempDir = await fs.promises.mkdtemp(tempDirPrefix);
  });

  afterAll(async () => {
    if (tempDir) {
      await fs.promises.rm(tempDir, { recursive: true });
    }
  });
  test(
    `${skipFlag}Generate, upload and process DOR files`,
    async () => {
      const storage = dataDirectory("initial", tempDir);
      await storage.prepare();
      const [employers, employees] = await generateDOREmployersEmployees(
        scenarios,
        "before"
      );

      // Generate employers and employees for the previous day. This is used to test the DOR Name Change report.
      const [previousEmployers, employeesWithClaims] =
        await generateDOREmployersEmployees(scenarios, "after", {
          generateEmployees: true,
          targetDate: subBusinessDays(today, 2),
        });

      // Generate employers and employees for the current day without claims. This is used to test the DOR Name Change report.
      const [_, employeesWithoutClaims] = await generateDOREmployersEmployees(
        scenarios,
        "after",
        {
          generateEmployees: true,
        }
      );

      // In order to test the DOR Name Change report, we need to import employees who were imported at least 2 days ago.
      employeesWithClaimsSSNs.push(
        ...Array.from(employeesWithClaims).map((employee) =>
          employee.ssn.replace(/-/g, "")
        )
      );

      employeeWithoutClaimsSSNs.push(
        ...Array.from(employeesWithoutClaims).map((employee) =>
          employee.ssn.replace(/-/g, "")
        )
      );

      const employerFile = storage.dorFile("DORDFMLEMP");
      const employeeFile = storage.dorFile("DORDFML");

      const combinedEmployers = [...employers, ...previousEmployers];
      const combinedEmployees = [
        ...employees,
        ...employeesWithClaims,
        ...employeesWithoutClaims,
      ];

      await DOR.writeEmployersFile(combinedEmployers, employerFile);
      await DOR.writeEmployeesFile(
        combinedEmployers,
        combinedEmployees,
        employeeFile
      );

      // Save the employers.json file. The withholdings are used to verify
      // the leave admin.
      employers.save(storage.employers);
      const employerPool = EmployerPool.merge(employers, previousEmployers);

      const dorImport = await uploadAndRunETL(
        employerFile,
        employeeFile,
        infra,
        employerPool
      );
      hasFirstStepFunctionInstanceFinished = true;

      /*
       * This retrieves the following DOR Name Change Report or Integrity Report to be used in the test cases.
       * Since we're performing several imports in other test suites in this file, we need to ensure that the
       * DOR Name Change Report is retrieved after the first import.
       */
      dorNameChangeReport = await infra.getLatestDorIntegrityReport(
        new Date(dorImport.start)
      );
    },
    15 * MINUTES
  );

  describe(!isTraining)("Employer and employees", () => {
    test(
      `${skipFlag}Finds the employee from the previous day`,
      async () => {
        await Fineos.withBrowser(
          async (page) => {
            await ClaimantPage.visit(
              page,
              formatAsSSN(
                generateIdentifier(
                  firstScenarioPrefix,
                  subBusinessDays(today, 1)
                )
              )
            );
          },
          { debug: false }
        );
      },
      1 * MINUTES
    );

    test(
      `${skipFlag}Register and verify leave admin`,
      async () => {
        const authenticator = getAuthManager();
        const fein = formatAsFEIN(generateIdentifier(firstScenarioPrefix));

        const credentials = generateCredentials();
        debug(
          `Register the following LA: ${credentials.username} for company FEIN ${fein}`
        );

        try {
          await authenticator.registerLeaveAdmin(credentials, fein);
        } catch (error) {
          // since we're generating random creds each run, it'd be pretty
          // surprising if we got a repeat email error. regardless, we don't
          // want to fail the whole test if it does happen
          if (error.code !== "UsernameExistsException") {
            throw error;
          }
        }

        const storage = dataDirectory("initial", tempDir);
        const pool = await EmployerPool.load(storage.employers);
        const employer = pool.pick({
          fein,
          size: "small",
        });

        await authenticator.verifyLeaveAdmin(
          credentials,
          employer.withholdings
        );
      },
      1.5 * MINUTES
    );

    test(
      `${skipFlag}Finds the employer master plan`,
      async () => {
        await pRetry(
          async () => {
            await Fineos.withBrowser(
              async (page) => {
                const creationDate = formatToTimeZone(today, "MM/DD/YYYY", {
                  timeZone: "America/New_York",
                });
                const fein = generateIdentifier(1, today);
                const employerPage = await EmployerPage.visit(page, fein, 5);

                const masterPlans = await employerPage.getMasterPlans();
                expect(masterPlans.length).toBe(1);

                const masterPlan = masterPlans[0];
                expect(masterPlan.creationDate).toBe(creationDate);
              },
              {
                debug: false,
                screenshots: path.join(
                  __dirname,
                  "..",
                  "playwright-screenshots"
                ),
              }
            );
          },
          {
            retries: 3,
          }
        );
      },
      1.5 * MINUTES
    );

    // This should be run after processing DOR files. When DOR files are processed
    // it triggers the fineos-import-employee-updates job which updates the
    // employee's fineos customer number.
    test(
      `${skipFlag}Finds the employee customer number from two days ago`,
      async () => {
        const dayOfWeek = format(today, "EEEE");
        const isStartOfWeek = ["Monday", "Tuesday"].includes(dayOfWeek);
        // Batch jobs don't run on weekends, so employees uploaded on Thursday or
        // Friday aren't included as part of the PFML batch jobs, and don't get a
        // customer number imported to check on Monday/Tuesday, respectively.
        if (isStartOfWeek) {
          return;
        }
        const id = generateIdentifier(
          firstScenarioPrefix,
          subBusinessDays(today, 2)
        );
        const employee = await searchEmployeeBySSN(formatAsSSN(id));
        expect(employee).not.toBeUndefined();
        const fineosCustomerNumber = employee.fineos_customer_number;
        expect(fineosCustomerNumber).not.toBeNull();
      },
      1 * MINUTES
    );
  });

  describe(!isTraining)("Service agreements", () => {
    test.each(scenarios)(
      `${skipFlag}Validate initial %s employer service agreements`,
      async (scenario: Scenario) => {
        const fein = generateIdentifier(scenario.prefix);
        // Validate the initial service agreements.
        await validateServiceAgreement(fein, scenario.before);
      },
      5 * MINUTES
    );

    test(
      `${skipFlag}Generate, upload and process DOR files for exemption changes`,
      async () => {
        if (hasFirstStepFunctionInstanceFinished !== true) {
          throw new Error(
            "The first jobs to process DOR files have not finished."
          );
        }
        const storage = dataDirectory("updated", tempDir);
        await storage.prepare();
        const [employers, employees] = await generateDOREmployersEmployees(
          scenarios,
          "after",
          { generateEmployees: false }
        );
        const employerFile = storage.dorFile("DORDFMLEMP");
        const employeeFile = storage.dorFile("DORDFML");
        await DOR.writeEmployersFile(employers, employerFile);
        await DOR.writeEmployeesFile(employers, employees, employeeFile);
        await uploadAndRunETL(employerFile, employeeFile, infra, employers);
      },
      15 * MINUTES
    );

    test.each(scenarios)(
      `${skipFlag}Validate updated %s employer service agreements`,
      async (scenario: Scenario) => {
        const fein = generateIdentifier(scenario.prefix);
        debug("SA: Validating updated SA for " + fein);
        // Validate the updated service agreements.
        await validateServiceAgreement(fein, scenario.after);
      },
      5 * MINUTES
    );

    test.each(scenarios)(
      `${skipFlag}Validate claim submission against %s employer from the previous day`,
      async (scenario: Scenario) => {
        // If today is Monday, look back to Friday instead of "Yesterday"
        const dayOfWeek = format(today, "EEEE");
        const isMonday = dayOfWeek === "Monday";
        const identifier = generateIdentifier(
          scenario.prefix,
          subDays(today, isMonday ? 3 : 1)
        );
        await submitAndValidateClaim(identifier, scenario.after);
      },
      5 * MINUTES
    );
  });

  describe(!isTraining)("DOR Name Change Report", () => {
    beforeAll(async () => {
      for (const ssn of employeesWithClaimsSSNs) {
        const employee = await searchEmployeeBySSN(formatAsSSN(ssn));
        if (employee === undefined) {
          throw new Error(`Employee with SSN ${ssn} not found.`);
        }

        const claimExists = await employeeHasExistingClaim(ssn);

        if (!claimExists) {
          throw new Error(`Claim for employee with SSN ${ssn} not found.`);
        }
      }
    });

    test(`${skipFlag}Name change employees who have associated claims appear in the DOR Name Change report`, async () => {
      const filterEntries = dorNameChangeReport.filter((entry) =>
        employeesWithClaimsSSNs.includes(entry.EmployeeSSN)
      );

      expect(filterEntries.length).toBe(employeesWithClaimsSSNs.length);
    });

    test(`${skipFlag}Name change employees who do not have associated claims do not appear in the DOR Name Change report`, async () => {
      const filterEntries = dorNameChangeReport.filter((entry) =>
        employeeWithoutClaimsSSNs.includes(entry.EmployeeSSN)
      );

      expect(filterEntries.length).toBe(0);
    });
  });
});

interface GenerateDorEmployersEmployeesOptions {
  generateEmployees?: boolean;
  employeesPerEmployer?: number;
  targetDate?: Date;
}

/**
 * Generate employer and and optionally employee DOR files for each exemption status.
 */
async function generateDOREmployersEmployees(
  scenarios: readonly Scenario[],
  state: "before" | "after",
  options: GenerateDorEmployersEmployeesOptions = {
    generateEmployees: true,
  }
): Promise<[EmployerPool, EmployeePool]> {
  const { generateEmployees, targetDate, employeesPerEmployer } = options;

  const employerPools: EmployerPool[] = [];
  const employeePools: EmployeePool[] = [];

  for (const scenario of scenarios) {
    const identifier = generateIdentifier(scenario.prefix, targetDate ?? today);

    const exemptionStatus = scenario[state];
    const employerPool = EmployerPool.generate(1, {
      fein: formatAsFEIN(identifier),
      family_exemption: exemptionStatus.familyExemption,
      medical_exemption: exemptionStatus.medicalExemption,
      exemption_commence_date: exemptionStatus.exemptionCommenceDate,
      exemption_cease_date: exemptionStatus.exemptionCeaseDate,
      size: "small",
    });
    employerPools.push(employerPool);

    if (generateEmployees) {
      const numberOfEmployees = employeesPerEmployer ?? 1;
      const employeePool = EmployeePool.generate(
        numberOfEmployees,
        employerPool,
        {}
      );
      employeePool.pick().ssn = formatAsSSN(identifier);
      employeePools.push(employeePool);
    }
  }

  // Merge all pools
  const employers = EmployerPool.merge(...employerPools);
  const employees = EmployeePool.merge(...employeePools);

  return [employers, employees];
}

async function getClaimRequestDecision(fineosAbsenceId: string) {
  const claim = await submitter.getClaim(fineosAbsenceId);
  const absencePeriods = claim.data.data?.absence_periods;
  if (!absencePeriods) {
    throw new Error(
      `Unable to find absence_periods for claim ${fineosAbsenceId}.`
    );
  }
  return absencePeriods[0].request_decision;
}

async function validateServiceAgreement(
  fein: string,
  exemptionStatus: ExemptionStatus
) {
  debug(`Validating employer ${fein}`);
  await Fineos.withBrowser(
    async (page) => {
      const employerPage = await EmployerPage.visit(page, fein, 2);
      await employerPage.visitServiceAgreement(
        async (serviceAgreementPage) =>
          await exemptionStatus.validate(serviceAgreementPage)
      );
    },
    {
      debug: false,
      screenshots: path.join(__dirname, "..", "playwright-screenshots"),
    }
  );
}

async function submitAndValidateClaim(
  identifier: string,
  exemptionStatus: ExemptionStatus
) {
  if (exemptionStatus.exemptionCeaseDate === undefined) {
    throw new Error("Received ExemptionStatus without an exemptionCeaseDate.");
  }

  const leaveScenario = generateExemptionTestingLeaveScenario(
    exemptionStatus.exemptionCeaseDate
  );
  debug(`Submitting a claim against employer ${identifier}`);
  const application = ClaimGenerator.generate(
    await getEmployeePool(),
    leaveScenario.employee,
    leaveScenario.claim
  );
  // The dataset generated contains a FEIN and SSN of the same value.
  // Override the application FEIN and SSN.
  application.claim.employer_fein = formatAsFEIN(identifier);
  application.claim.tax_identifier = formatAsSSN(identifier);
  assertValidClaim(application.claim);
  const apiResponse = await submitter.createApplicationAndSubmitPartOne(
    application.claim,
    getClaimantCredentials()
  );
  if (!apiResponse.fineos_absence_id) {
    throw new Error(
      `Did not receive fineos_absence_id for claim against employer ${identifier}.`
    );
  }
  const fineosAbsenceId = apiResponse.fineos_absence_id;

  debug(
    `Submitted ${fineosAbsenceId} against ${exemptionStatus} employer ${identifier}`
  );

  const decision = await getClaimRequestDecision(fineosAbsenceId);
  expect(decision).toBe(exemptionStatus.expectedRequestLeaveDecision);
}

function generateIdentifier(prefix: number, date?: Date) {
  // Uncomment to test generate feins/ssns that don't exist.
  // Tests that check for data from one or more days relative to this day will
  // still fail.

  // const additionalSubDays = 355;
  // if (date) {
  //   date = subDays(date, additionalSubDays);
  // } else {
  //   date = subDays(new Date(), additionalSubDays);
  // }

  const identifier =
    prefix.toString() +
    formatToTimeZone(date ?? today, "MMDDYYYY", {
      timeZone: "America/New_York",
    });
  return identifier;
}

function formatAsFEIN(identifier: string) {
  const strippedIdentifier = identifier.replace(/-/g, "");
  const segmentA = strippedIdentifier.slice(0, 2);
  const segmentB = strippedIdentifier.slice(2);
  return `${segmentA}-${segmentB}`;
}

function formatAsSSN(identifier: string) {
  const strippedIdentifier = identifier.replace(/-/g, "");
  const segmentA = strippedIdentifier.slice(0, 3);
  const segmentB = strippedIdentifier.slice(3, 5);
  const segmentC = strippedIdentifier.slice(5);
  return `${segmentA}-${segmentB}-${segmentC}`;
}

async function uploadAndRunETL(
  employerFile: string,
  employeeFile: string,
  infra: InfraClient,
  employers: EmployerPool
) {
  await infra.uploadRevenueFiles([employerFile, employeeFile]);
  const start = new Date().getTime();
  await infra.runEcsTaskAndWaitForCompletion("dor-import", 5, [
    { name: "DECRYPT", value: "false" },
  ]);

  await infra.runEcsTaskAndWaitForCompletion("load-employers-to-fineos", 5, [
    { name: "EMPLOYER_LOAD_MODE", value: "updates" },
    {
      name: "SERVICE_AGREEMENT_LOAD",
      value: "true",
    },
  ]);

  const employerIds = await getEmployerIds(infra, employers);

  await infra.runEcsTaskAndWaitForCompletion(
    "load-service-agreements-to-fineos",
    5,
    [
      {
        name: "SERVICE_AGREEMENT_LOAD",
        value: "true",
      },
      { name: "ENABLE_SERVICE_AGREEMENT_VERSION_FINEOS_WRITE", value: "1" },
      { name: "EMPLOYER_SERVICE_AGREEMENT_VERSION_UPDATE_LIMIT", value: "3" },
      {
        name: "SERVICE_AGREEMENT_PRESELECTED_EMPLOYERS",
        value: employerIds.join(","),
      },
    ]
  );
  const end = new Date().getTime();
  return { start, end };
}

async function getEmployerIds(infra: InfraClient, employers: EmployerPool) {
  const feins = Array.from(employers)
    .map((employer) => `'${employer.fein.replace(/-/g, "")}'`)
    .join(",");
  const rdsQuery = `SELECT STRING_AGG(employer_id::text, ',') from employer WHERE employer_fein IN (${feins});`;
  const response = await infra.sendAndWaitForSSMShellCommand(
    infra.buildRdsCommand(rdsQuery, ["-t", "-A"])
  );
  const employerIdsCSV = response.StandardOutputContent?.trimEnd();
  if (!employerIdsCSV) {
    throw Error("No employer ids returned for feins.");
  }
  return employerIdsCSV.split(",");
}

async function employeeHasExistingClaim(
  taxIdentifier: string
): Promise<boolean> {
  const rdsQuery = `
    SELECT
      c.claim_id
    FROM
      employee AS e
      JOIN tax_identifier AS ti ON ti.tax_identifier_id = e.tax_identifier_id
      JOIN claim AS c ON c.employee_id = e.employee_id
    WHERE
      ti.tax_identifier = '${taxIdentifier}';
  `;
  const response = await infra.sendAndWaitForSSMShellCommand(
    infra.buildRdsCommand(rdsQuery, ["-t", "-A"])
  );

  const output = response.StandardOutputContent?.trimEnd();

  return output ? output.trimEnd().length > 0 : false;
}
