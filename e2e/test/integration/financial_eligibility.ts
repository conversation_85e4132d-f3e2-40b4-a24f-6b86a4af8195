import { beforeAll, describe, expect, test } from "@jest/globals";
import { formatISO, previousSunday } from "date-fns";

import { EligibilityRequest, postFinancialEligibility } from "../../src/api";
import config from "../../src/config";
import { ClaimGenerator } from "../../src/generation/Claim";
import { ScenarioSpecification } from "../../src/generation/Scenario";
import * as scenarios from "../../src/scenarios";
import { getAuthManager, getEmployeePool } from "../../src/util/common";
import { getApiCredentialsForFineos } from "../../src/util/credentials";

let token: string;

/**
 * @group stable
 */
describe("Financial Eligibility", () => {
  beforeAll(async () => {
    const authenticator = getAuthManager();
    const apiCreds = getApiCredentialsForFineos();
    token = await authenticator.getAPIBearerToken(apiCreds);
  });

  const INEL: ScenarioSpecification = {
    ...scenarios.BHAP1,
    employee: { wages: "ineligible" },
  };

  const financial_eligibility: [string, ScenarioSpecification][] = [
    ["Eligible", scenarios.BHAP1],
    ["Ineligible", INEL],
  ];

  test.each(financial_eligibility)(
    "Claimaint should be Financially %s",
    async (description: string, scenarioSpec: ScenarioSpecification) => {
      const employeePool = await getEmployeePool();

      const { claim } = ClaimGenerator.generate(
        employeePool,
        scenarioSpec.employee,
        scenarioSpec.claim
      );

      if (!claim.leave_details?.continuous_leave_periods) {
        throw new Error("No Claim");
      }

      const start_date = claim.leave_details?.continuous_leave_periods[0]
        .start_date as string;

      const eligibilityRequest: EligibilityRequest = {
        tax_identifier: claim.tax_identifier as string,
        employer_fein: claim.employer_fein as string,
        leave_start_date: start_date,
        application_submitted_date: formatISO(new Date(), {
          representation: "date",
        }),
        employment_status: "Employed",
        // @Note: These two props (below) currently only validate it isn't Null,
        // setting it to the prev Sunday along w/a dummy NTN until prop
        // validation work is complete. Further setting these values don't cause
        // non Nov upgrade env request to fail.
        entitlement_period_start_date: formatISO(
          previousSunday(new Date(start_date)),
          {
            representation: "date",
          }
        ),
        absence_case_number: "NTN-0000-ABS-01",
      };
      const pmflApiOptions = {
        baseUrl: config("API_BASEURL"),
        headers: {
          Authorization: `Bearer ${token}`,
          "User-Agent": `PFML Integration Testing (Financially ${description})`,
        },
      };

      const res = await postFinancialEligibility(
        eligibilityRequest,
        pmflApiOptions
      );
      expect(res.status).toBe(200);
      expect(res.data.data?.financially_eligible).toBe(
        scenarioSpec.employee.wages === "eligible" ? true : false
      );
    },
    60_000
  );
});
