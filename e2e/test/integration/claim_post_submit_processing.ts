import { describe, expect, jest, test } from "@jest/globals";
import { collect, transform } from "streaming-iterables";

import { ClaimGenerator } from "../../src/generation/Claim";
import { ScenarioSpecification } from "../../src/generation/Scenario";
import { PostSubmitSpec } from "../../src/submission/PostSubmitAction";
import { dispatchPostSubmit } from "../../src/util/claimGenerator";
import { getEmployeePool, getPortalSubmitter } from "../../src/util/common";
import {
  getClaimantCredentials,
  getLeaveAdminCredentials,
} from "../../src/util/credentials";
import { random } from "../../src/util/random";
import { assertValidClaim } from "../../src/util/typeUtils";

// SETUP
//Set longer timeout as this takes a while.
jest.setTimeout(500 * 1000);
// Set up retries to mitigate 504's on claim submission
jest.retryTimes(1);
// Base scenario
const baseScenario: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "Post submit testing",
    reason: "Serious Health Condition - Employee",
    shortClaim: true,
    docs: {
      HCP: {},
      MASSID: {},
    },
    employerResponse: {
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
      fraud: "No",
    },
  },
};

// Using a mix of leave types to increase coverage of our post submission actions
const scenarios: ScenarioSpecification[] = [
  {
    ...baseScenario,
    claim: {
      ...baseScenario.claim,
      intermittent_leave_spec: true,
    },
  },
  {
    ...baseScenario,
    claim: {
      ...baseScenario.claim,
      reduced_leave_spec: "0,240,240,240,240,240,0",
    },
  },
  baseScenario,
];

const submitter = getPortalSubmitter();
const testing = transform<PostSubmitSpec, PostSubmitSpec>(
  3,
  async (postSubmitSpec) => {
    const scenario = scenarios[Math.floor(random() * scenarios.length)];
    const scenarioWithPostSubmit = {
      ...scenario,
      claim: {
        ...scenario.claim,
        metadata: {
          postSubmit: postSubmitSpec,
        },
      },
    };
    // Generate claim
    const application = ClaimGenerator.generate(
      await getEmployeePool(),
      scenarioWithPostSubmit.employee,
      scenarioWithPostSubmit.claim
    );
    console.info("Claim generated");
    assertValidClaim(application.claim);
    // Submit it to API
    const apiResponse = await submitter
      .submit(application, getClaimantCredentials(), {
        leaveAdminCredentials: getLeaveAdminCredentials(application.employer),
      })
      /**
       * @todo There's on opportunity here to preprocess errors for easier access in NR
       * We can split any errors coming from this test into "Submission" and "Post-processing" errors,
       * and enrich them with claim metadata for easier debugging.
       */
      // Catch submission errors and log them here.
      .catch((err) => {
        console.error("Failed to submit claim:", err.data);
        console.log(err);
        throw err;
      });
    console.info("Claim submitted");
    try {
      await dispatchPostSubmit(application, apiResponse);
      console.info(`${postSubmitSpec} post processing complete`);
    } catch (error) {
      // Catch post-processing errors and log them
      console.error(error);
      console.info(
        `${postSubmitSpec} error! Claim ID: ${apiResponse.fineos_absence_id}`
      );
      throw new Error(
        `${postSubmitSpec} error! Claim ID: ${apiResponse.fineos_absence_id}`
      );
    }
    return postSubmitSpec;
  }
);

/**
 * @group morning
 */
describe("Claim post-submit processing:", () => {
  // Generate a test for every post submit command
  // @todo We should add those post processing commands to metadata type for ScenarioSpecification
  test(`Can complete Post Submit Action:  on a submitted claim`, async () => {
    const scenarios: PostSubmitSpec[] = ["adjudicate", "approve", "deny"];
    const successfulScenarios = new Set(await collect(testing(scenarios)));
    const failed = scenarios.filter(
      (scenario) => !successfulScenarios.has(scenario)
    );
    expect(failed.length).toEqual(0);
  });
});
