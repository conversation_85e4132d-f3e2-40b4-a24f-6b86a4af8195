import { beforeAll, describe, expect, jest, test } from "@jest/globals";
import * as fs from "fs";
import pRetry from "p-retry";
import * as path from "path";

import {
  DocumentUploadRequest,
  postApplicationsByApplication_idDocuments,
  RequestOptions,
} from "../../src/api";
import config from "../../src/config";
import { ClaimGenerator } from "../../src/generation/Claim";
import * as scenarios from "../../src/scenarios";
import { getEmployeePool, getPortalSubmitter } from "../../src/util/common";
import { getClaimantCredentials } from "../../src/util/credentials";
import { getClaimantToken } from "../../src/util/myMassGov";
import { DocumentTestCase, documentTests } from "../util";

const defaultClaimantCredentials = getClaimantCredentials();
let application_id: string;
let pmflApiOptions: RequestOptions;

const getExpectedResponseText = (
  statusCode: DocumentTestCase["statusCode"],
  filetype?: string
): Partial<{
  statusText: string;
  message: string;
}> => {
  if (statusCode === 200) {
    return { message: "Successfully uploaded document" };
  }

  if (statusCode === 400) {
    return {
      message: `Incorrect file type: image/${
        filetype === "gif" ? "gif" : "svg+xml"
      }`,
    };
  }

  if (statusCode === 413) {
    return { statusText: "Request Entity Too Large" };
  }

  const exhaustiveCheck: never = statusCode;
  return exhaustiveCheck;
};

/**
 * @group stable
 */
describe("API Documents Test of various file sizes", () => {
  beforeAll(async () => {
    jest.retryTimes(2);
    const submitter = getPortalSubmitter();
    const employeePool = await getEmployeePool();
    const apiToken = await getClaimantToken(defaultClaimantCredentials);

    pmflApiOptions = {
      baseUrl: config("API_BASEURL"),
      headers: {
        Authorization: `Bearer ${apiToken}`,
        "User-Agent": "PFML Cypress Testing",
      },
    };

    // Before hooks will not be retried, so we have to handle our own retry logic here.
    application_id = await pRetry(
      async () => {
        const claim = ClaimGenerator.generate(
          employeePool,
          scenarios.BHAP1.employee,
          scenarios.BHAP1.claim
        );

        const res = await submitter.createApplicationAndSubmitPartOne(
          claim.claim,
          defaultClaimantCredentials
        );
        expect(res).toMatchObject({
          application_id: expect.any(String),
        });
        return res.application_id as string;
      },
      { retries: 3 }
    );

    console.log(
      `Documents are being submitted to this application_id: "${application_id}"`
    );
  }, 120_000);

  const tests: [string, DocumentTestCase][] = [];

  Object.values(documentTests).forEach((testSpec) => {
    const testCases = typeof testSpec === "function" ? testSpec() : testSpec;

    tests.push(
      ...testCases.map((testCase): [string, DocumentTestCase] => [
        testCase.description,
        testCase,
      ])
    );
  });

  test.each(tests)(
    "%s",
    async (description: string, testCase: DocumentTestCase) => {
      const { filepath, statusCode, filetype } = testCase;

      const document: DocumentUploadRequest = {
        document_type: "State managed Paid Leave Confirmation",
        description: description,
        file: fs.createReadStream(filepath),
        name: path.basename(filepath),
      };
      const docRes = await postApplicationsByApplication_idDocuments(
        { application_id: application_id },
        document,
        pmflApiOptions
      ).catch((err) => {
        return err;
      });

      if (docRes.errno) {
        throw new Error(
          `request to /applications/${application_id}/documents failed, reason: write EPIPE`
        );
      }

      if (docRes.status !== statusCode) {
        throw new Error(`Unable to add document: ${JSON.stringify(docRes)}`);
      }

      expect(docRes.status).toBe(statusCode);

      const { statusText, message } = getExpectedResponseText(
        statusCode,
        filetype
      );
      if (statusText) {
        expect(docRes.statusText).toBe(statusText);
      } else {
        expect(docRes.data.message).toBe(message);
      }
    },
    60_000
  );
});
