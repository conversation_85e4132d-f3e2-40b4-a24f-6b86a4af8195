import {
  addDays,
  addWeeks,
  endOfWeek,
  formatISO,
  lastDayOfQuarter,
  startOfWeek,
  subDays,
  subWeeks,
} from "date-fns";

import { generateLeaveDates } from "../generation/LeaveDetails";
import {
  BondingFosterClaim,
  CaringLeaveClaim,
  MilitaryCaregiverClaim,
  MilitaryExigencyClaim,
  ScenarioSpecification,
} from "../generation/Scenario";
import generateWorkPattern from "../generation/WorkPattern";
import { quarters } from "../util/writers";

const mostRecentSunday = startOfWeek(new Date());
const midweek = addDays(mostRecentSunday, 3);

/**
 * Cypress Testing Scenarios.
 *
 * 99% of our Cypress scenarios are short claims (1 day). If you add a new scenario here, please ensure it is a
 * short claim, or you explain why it is not.
 */
export const BHAP1: ScenarioSpecification<BondingFosterClaim> = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "BHAP1",
    shortClaim: true,
    reason: "Child Bonding",
    reason_qualifier: "Foster Care",
    work_pattern_spec: "0,720,0,720,0,720,0",
    docs: {
      MASSID: {},
      FOSTERPLACEMENT: {},
    },
  },
};

export const BHAP1_OOS: ScenarioSpecification<BondingFosterClaim> = {
  employee: { ...BHAP1.employee, mass_id: false },
  claim: {
    ...BHAP1.claim,
    docs: {
      OOSID: {},
      FOSTERPLACEMENT: {},
    },
    label: "BHAP1_OOS",
  },
};

export const BHAP1ER: ScenarioSpecification = {
  ...BHAP1,
  claim: {
    ...BHAP1.claim,
    employerResponse: {
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
      employer_decision: "Approve",
      fraud: "No",
    },
  },
};

export const BHAP1ERA: ScenarioSpecification = {
  ...BHAP1ER,
  claim: {
    ...BHAP1ER.claim,
    leave_dates: [addWeeks(new Date(), 2), addDays(addWeeks(new Date(), 2), 3)],
  },
};

export const BHAP1ERB: ScenarioSpecification = {
  ...BHAP1ER,
  claim: {
    ...BHAP1ER.claim,
    leave_dates: [subWeeks(new Date(), 2), addDays(subWeeks(new Date(), 2), 3)],
  },
};

export const REDUCED_ER: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "REDUCEDER",
    shortClaim: true,
    reason: "Child Bonding",
    reason_qualifier: "Foster Care",
    docs: {
      FOSTERPLACEMENT: {},
      MASSID: {},
    },
    employerResponse: {
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 20,
      },
      employer_decision: "Approve",
      fraud: "No",
    },
    reduced_leave_spec: "0,240,240,240,240,240,0",
  },
};

export const REDUCED_ER_MIDWEEK: ScenarioSpecification = {
  ...REDUCED_ER,
  claim: {
    ...REDUCED_ER.claim,
    label: "REDUCED_ER_MIDWEEK",
    shortClaim: false,
    leave_dates: [addWeeks(midweek, 1), addWeeks(midweek, 4)],
  },
};
export const MIL_RED: ScenarioSpecification<MilitaryCaregiverClaim> = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "MIL_RED",
    shortClaim: true,
    reason: "Military Caregiver",
    docs: {
      MASSID: {},
      COVERED_SERVICE_MEMBER_ID: {},
      CARING: {},
    },
    reduced_leave_spec: "0,240,240,240,240,240,0",
  },
};

export const MIL_EXI: ScenarioSpecification<MilitaryExigencyClaim> = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "Military Exigency claim",
    shortClaim: true,
    reason: "Military Exigency Family",
    docs: {
      MASSID: {},
      ACTIVE_SERVICE_PROOF: {},
      MILITARY_EXIGENCY_FORM: {},
    },
  },
};

export const MED_INTER_INEL: ScenarioSpecification = {
  employee: { mass_id: true, wages: "ineligible" },
  claim: {
    label: "MED_INTER_INEL",
    shortClaim: true,
    reason: "Serious Health Condition - Employee",
    docs: {
      HCP: {},
      MASSID: {},
    },
    intermittent_leave_spec: true,
  },
};

export const MED_INTER_INEL_0_WAGES: ScenarioSpecification = {
  employee: {
    mass_id: true,
    wages: 0,
  },
  claim: {
    label: "MED_INTER_INEL_0_WAGES",
    shortClaim: true,
    reason: "Serious Health Condition - Employee",
    docs: {
      HCP: {},
      MASSID: {},
    },
    intermittent_leave_spec: true,
  },
};

export const MCAP_NODOC: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "MCAP_NODOC",
    shortClaim: true,
    reason: "Serious Health Condition - Employee",
    work_pattern_spec: "0,315,315,315,315,315,0",
    docs: {
      // Missing HCP.
      MASSID: {},
    },
  },
};

// Only used in *ignored* spec MHAP4.ts
export const MHAP4: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "MHAP4",
    shortClaim: true,
    reason: "Pregnancy/Maternity",
    docs: {
      MASSID: {},
      PREGNANCY_MATERNITY_FORM: {},
    },
  },
};

export const MRAP30_PREPAID: ScenarioSpecification = {
  employee: {
    wages: 30_000,
    mass_id: true,
  },
  claim: {
    label: "MRAP30_PREPAID",
    reason: "Serious Health Condition - Employee",
    docs: {
      MASSID: {},
      HCP: {},
    },
    employer_benefits: [
      // Values of benefit_amount_dollars and benefit_amount_frequency are
      // insignificant. Needed for E2E validation.
      {
        benefit_amount_dollars: 1,
        benefit_amount_frequency: "Per Day",
        benefit_type: "Paid time off",
        benefit_start_date: formatISO(addWeeks(mostRecentSunday, 2), {
          representation: "date",
        }),
        benefit_end_date: formatISO(addWeeks(mostRecentSunday, 3), {
          representation: "date",
        }),
        is_full_salary_continuous: true,
      },
    ],
    payment: {
      payment_method: "Prepaid Card",
    },
    work_pattern_spec: "standard",
    reduced_leave_spec: "0,240,240,240,240,240,0",
    // This scenario requires a 2 week leave time for payment calculation purposes.
    leave_dates: [addWeeks(mostRecentSunday, 1), addWeeks(mostRecentSunday, 3)],
  },
};

export const BCAP90: ScenarioSpecification = {
  employee: {
    wages: 90000,
    mass_id: true,
  },
  claim: {
    label: "BCAP90",
    reason: "Child Bonding",
    reason_qualifier: "Foster Care",
    work_pattern_spec: "0,720,0,720,0,720,0",
    docs: {
      MASSID: {},
      FOSTERPLACEMENT: {},
    },
    // This scenario requires a 2 week leave time for payment calculation purposes.
    leave_dates: [subWeeks(mostRecentSunday, 1), addWeeks(mostRecentSunday, 1)],
  },
};

export const BIAP60: ScenarioSpecification = {
  employee: {
    wages: 60_000,
    mass_id: true,
  },
  claim: {
    label: "BIAP60",
    reason: "Child Bonding",
    reason_qualifier: "Newborn",
    bondingDate: subDays(subWeeks(mostRecentSunday, 4), 2),
    work_pattern_spec: "0,240,240,240,240,240,0",
    docs: {
      MASSID: {},
      BIRTHCERTIFICATE: {},
    },
    intermittent_leave_spec: {
      duration: 5,
      duration_basis: "Days",
    },
    is_withholding_tax: false,
    // This scenario requires a 4 week leave time for payment calculation purposes.
    leave_dates: [subWeeks(mostRecentSunday, 4), mostRecentSunday],
    metadata: {
      fineosIntermittentLeave: {
        spanHoursStart: "4",
        spanHoursEnd: "4",
        spanMinutesStart: "0",
        spanMinutesEnd: "0",
      },
    },
  },
};

export const BIAP60ER: ScenarioSpecification = {
  ...BIAP60,
  claim: {
    ...BIAP60.claim,
    label: "BIAP60ER",
    employerResponse: {
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 20,
      },
      employer_decision: "Approve",
      fraud: "No",
    },
  },
};

export const CCAP90: ScenarioSpecification = {
  employee: {
    wages: 90_000,
    mass_id: true,
  },
  claim: {
    label: "CCAP90",
    reason: "Care for a Family Member",
    work_pattern_spec: "0,720,0,720,0,720,0",
    docs: { MASSID: {}, CARING: {} },
    leave_dates: [subWeeks(mostRecentSunday, 3), mostRecentSunday],
  },
};

export const CCAP90ER: ScenarioSpecification = {
  ...CCAP90,
  claim: {
    ...CCAP90.claim,
    label: "CCAP90ER",
    employerResponse: {
      employer_decision: "Approve",
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
    },
  },
};

export const CCAP90ERSP: ScenarioSpecification = {
  ...CCAP90ER,
  claim: {
    ...CCAP90ER.claim,
    label: "CCAP90ERSP",
    preferredLanguage: "Spanish",
  },
};

export const CDENY2: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "CDENY2",
    shortClaim: true,
    has_continuous_leave_periods: true,
    reason: "Care for a Family Member",
    docs: {
      MASSID: {},
      CARING: {},
    },
  },
};

export const CDENY2ER: ScenarioSpecification = {
  ...CDENY2,
  claim: {
    ...CDENY2.claim,
    label: "CDENY2ER",
    employerResponse: {
      employer_decision: "Approve",
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
    },
  },
};

export const ORGUNIT: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "ORGUNIT",
    shortClaim: true,
    has_continuous_leave_periods: true,
    reason: "Care for a Family Member",
    docs: {
      MASSID: {},
      CARING: {},
    },
    metadata: {
      orgUnitData: {
        orgUnit: "Governor's Office",
        worksite: "COM_MASS-",
      },
    },
  },
};

export const NO_ORGUNIT: ScenarioSpecification = {
  ...ORGUNIT,
  claim: {
    ...ORGUNIT.claim,
    label: "NO_ORGUNIT",
    organization_unit_id: null,
    organization_unit_selection: "not_selected",
    is_withholding_tax: true,
    metadata: {
      orgUnitData: {
        orgUnit: "I'm not sure",
        worksite: "n/a",
      },
    },
  },
};

export const BLANK_ORGUNIT: ScenarioSpecification = {
  ...ORGUNIT,
  claim: {
    ...ORGUNIT.claim,
    label: "BLANK_ORGUNIT",
    organization_unit_id: null,
    organization_unit_selection: "not_selected",
    is_withholding_tax: true,
    metadata: {
      orgUnitData: {
        orgUnit: "Blank",
        worksite: "n/a",
      },
    },
  },
};

// only used in ignored `reductions.ts` spec
export const MED_OLB: ScenarioSpecification = {
  employee: { mass_id: true, wages: 90000 },
  claim: {
    label: "MED with Other Leaves & Benefits",
    leave_dates: [subWeeks(mostRecentSunday, 3), addWeeks(mostRecentSunday, 3)],
    reason: "Serious Health Condition - Employee",
    docs: {
      MASSID: {},
      HCP: {},
    },
    reduced_leave_spec: "0,240,240,240,240,240,0",
    employerResponse: {
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 20,
      },
      employer_decision: "Approve",
      fraud: "No",
    },
    other_incomes: [
      {
        income_type: "Earnings from another employment/self-employment",
        income_amount_dollars: 200,
        income_amount_frequency: "Per Week",
      },
    ],
    employer_benefits: [
      {
        benefit_amount_dollars: 1000,
        benefit_amount_frequency: "In Total",
        benefit_type: "Short-term disability insurance",
        is_full_salary_continuous: false,
      },
    ],
    previous_leaves: [
      {
        type: "other_reason",
        leave_reason: "Bonding with my child after birth or placement",
        is_for_current_employer: true,
        leave_minutes: 2400,
        worked_per_week_minutes: 1200,
      },
    ],
  },
};

export const BONDING_DISABILITY: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    bondingDate: "future",
    docs: { MASSID: {} },
    employer_benefits: [
      {
        benefit_amount_dollars: 500,
        benefit_amount_frequency: "Per Week",
        benefit_type: "Permanent disability insurance",
        is_full_salary_continuous: true,
      },
    ],
    label: "BONDING_DISABILITY",
    leave_dates: () => {
      const workPattern = generateWorkPattern("0,315,315,315,315,315,0");
      const earliestStartDate = addWeeks(new Date(), 3).toDateString();
      return generateLeaveDates(workPattern, { days: 8 }, earliestStartDate);
    },
    reason: "Child Bonding",
    reason_qualifier: "Newborn",
    work_pattern_spec: "0,315,315,315,315,315,0",
    preferredLanguage: "Language not listed",
  },
};

export const PREBIRTH: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "PREBIRTH",
    reason: "Pregnancy/Maternity",
    pregnant_or_recent_birth: undefined,
    shortClaim: true,
    docs: {
      MASSID: {},
      PREGNANCY_MATERNITY_FORM: {},
    },
  },
};

export const PREBIRTH_ER: ScenarioSpecification = {
  ...PREBIRTH,
  claim: {
    ...PREBIRTH.claim,
    label: "PREBIRTH_ER",
    employerResponse: {
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
      employer_decision: "Approve",
    },
  },
};

export const MED_RFI: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "MED_RFI",
    reason: "Serious Health Condition - Employee",
    docs: {
      MASSID: {},
      HCP: {},
    },
    employerResponse: {
      has_amendments: true,
      fraud: "Yes",
      employer_decision: "Deny",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
    },
  },
};

export const CONTINUOUS_MEDICAL_OLB: ScenarioSpecification = {
  employee: { mass_id: true, wages: 90000 },
  claim: {
    label: "CONTINUOUS MEDICAL LEAVE WITH OTHER LEAVES & BENEFITS",
    reason: "Serious Health Condition - Employee",
    docs: {
      MASSID: {},
      HCP: {},
    },
    work_pattern_spec: "standard",
    // Create a leave in progress, so we can check adjustments for both made and future payments.
    leave_dates: [subWeeks(mostRecentSunday, 3), addWeeks(mostRecentSunday, 3)],
    // Leave start & end dates here and in employer benefits empty so they match the leave dates automatically
    other_incomes: [
      {
        income_type: "Earnings from another employment/self-employment",
        income_amount_dollars: 200,
        income_amount_frequency: "Per Week",
      },
    ],
    employer_benefits: [
      {
        benefit_amount_dollars: 100,
        benefit_amount_frequency: "Per Week",
        benefit_type: "Short-term disability insurance",
        is_full_salary_continuous: false,
      },
    ],
    previous_leaves: [
      {
        type: "other_reason",
        leave_reason: "Bonding with my child after birth or placement",
        is_for_current_employer: true,
        leave_minutes: 2400,
        worked_per_week_minutes: 1200,
      },
    ],
  },
};

export const BHAP1_OLB: ScenarioSpecification = {
  employee: { mass_id: true, wages: 90000 },
  claim: {
    label: "BHAP1_OLB",
    reason: "Child Bonding",
    reason_qualifier: "Newborn",
    bondingDate: "past",
    docs: {
      MASSID: {},
      BIRTHCERTIFICATE: {},
    },
    // Create a leave in progress, so we can check adjustments for both made and future payments.
    leave_dates: [subWeeks(mostRecentSunday, 2), addWeeks(mostRecentSunday, 2)],
    // Leave start & end dates here and in employer benefits empty so they match the leave dates automatically
    other_incomes: [
      {
        income_type: "SSDI",
        income_amount_dollars: 200,
        income_amount_frequency: "Per Week",
      },
    ],
    employer_benefits: [
      {
        benefit_amount_dollars: 500,
        benefit_amount_frequency: "In Total",
        benefit_type: "Family or medical leave insurance",
        is_full_salary_continuous: true,
        employer_changes: "Added",
      },
    ],
    previous_leaves: [
      {
        type: "other_reason",
        leave_reason: "An illness or injury",
        is_continuous: false,
        is_for_current_employer: true,
        leave_minutes: 2400,
        worked_per_week_minutes: 1200,
      },
    ],
    is_withholding_tax: true,
  },
};

export const BHAP1_OLB_ER: ScenarioSpecification = {
  employee: { mass_id: true, wages: 90_000 },
  claim: {
    label: "BHAP1_OLB_ER",
    reason: "Child Bonding",
    reason_qualifier: "Newborn",
    bondingDate: "past",
    childSupportReductions: [
      {
        amountDollars: 100,
        endDate: formatISO(addWeeks(mostRecentSunday, 1), {
          representation: "date",
        }),
        startDate: formatISO(subWeeks(mostRecentSunday, 3), {
          representation: "date",
        }),
      },
    ],
    docs: {
      MASSID: {},
      BIRTHCERTIFICATE: {},
    },
    employerResponse: {
      employer_decision: "Approve",
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
      employer_benefits: [
        {
          benefit_amount_dollars: 500,
          benefit_amount_frequency: "In Total",
          benefit_type: "Family or medical leave insurance",
          is_full_salary_continuous: true,
          employer_changes: "Unchanged",
        },
      ],
    },
    other_incomes: [
      {
        income_type: "Earnings from another employment/self-employment",
        income_amount_dollars: 200,
        income_amount_frequency: "Per Week",
        income_end_date: formatISO(addWeeks(mostRecentSunday, 1), {
          representation: "date",
        }),
        income_start_date: formatISO(subWeeks(mostRecentSunday, 3), {
          representation: "date",
        }),
      },
    ],
    // Create a leave in progress, so we can check adjustments for both made and future payments.
    leave_dates: [subWeeks(mostRecentSunday, 3), addWeeks(mostRecentSunday, 1)],
    // Leave start & end dates here and in employer benefits empty so they match the leave dates automatically
    is_withholding_tax: true,
    address: {
      city: "Washington",
      line_1: "1600 Pennsylvania Avenue NW",
      state: "DC",
      zip: "20500",
    },
    payment: {
      payment_method: "Check",
    },
  },
};

// This needs a specific scenario to be used by a CPS test. Please do not change specs on this scenario.
export const MHAP1_OLB_ER: ScenarioSpecification = {
  employee: { wages: 30000, mass_id: true },
  claim: {
    label: "MHAP1_OLB_ER",
    reason: "Serious Health Condition - Employee",
    // Create a leave in progress, so we can check adjustments for both made and future payments.
    leave_dates: [subWeeks(mostRecentSunday, 3), addWeeks(mostRecentSunday, 1)],
    docs: {
      MASSID: {},
      HCP: {},
    },
    employerResponse: {
      employer_decision: "Approve",
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
    },
  },
};

export const CARE_ER_APPROVE: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "CARE_ER_APPROVE",
    reason: "Care for a Family Member",
    shortClaim: true,
    docs: {
      MASSID: {},
      CARING: {},
    },
    employerResponse: {
      employer_decision: "Approve",
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
    },
  },
};

export const WDCLAIM: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "WDCLAIM",
    shortClaim: true,
    reason: "Serious Health Condition - Employee",
    docs: {
      HCP: {},
      MASSID: {},
    },
  },
};

export const HIST_CASE: ScenarioSpecification<CaringLeaveClaim> = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "HIST_CASE",
    shortClaim: true,
    has_continuous_leave_periods: true,
    reason: "Care for a Family Member",
    employerResponse: {
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
      employer_decision: "Approve",
      fraud: "No",
    },
    docs: {
      MASSID: {},
      CARING: {},
    },
  },
};

// Leave start date change request
export const MED_LSDCR: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "MED_LSDCR",
    shortClaim: true,
    reason: "Serious Health Condition - Employee",
    leave_dates: [addWeeks(new Date(), 2), addWeeks(new Date(), 6)],
    employerResponse: {
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
      employer_decision: "Approve",
      fraud: "No",
    },
    docs: {
      HCP: {},
      MASSID: {},
    },
  },
};

export const MED_ERRE: ScenarioSpecification = {
  employee: {
    wages: 30_000,
    mass_id: true,
  },
  claim: {
    label: "MED_ERRE",
    reason: "Serious Health Condition - Employee",
    leave_dates: [subWeeks(mostRecentSunday, 2), addWeeks(mostRecentSunday, 2)],
    employerResponse: {
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
      employer_decision: "Approve",
      fraud: "No",
      employer_benefits: [],
    },
    docs: {
      HCP: {},
      MASSID: {},
    },
    metadata: {
      employerReimbursement: 100,
    },
  },
};

export const MED_ERRE_FULL_OFFSET: ScenarioSpecification = {
  ...MED_ERRE,
  claim: {
    ...MED_ERRE.claim,
    label: "MED_ERRE_FULL_OFFSET",
    leave_dates: [subWeeks(mostRecentSunday, 2), addWeeks(mostRecentSunday, 3)],
  },
};

export const CARE_TAXES: ScenarioSpecification<CaringLeaveClaim> = {
  employee: { mass_id: true, wages: 30000 },
  claim: {
    reason: "Care for a Family Member",
    label: "CARE_TAXES",
    leave_dates: [
      startOfWeek(subWeeks(new Date(), 2)),
      startOfWeek(addWeeks(new Date(), 2)),
    ],
    is_withholding_tax: true,
    work_pattern_spec: "standard",
    reduced_leave_spec: "0,240,240,240,240,240,0",
    docs: { CARING: {}, MASSID: {} },
  },
};

export const MED_CONT_ER_APPROVE: ScenarioSpecification = {
  employee: { mass_id: true, wages: 60000 },
  claim: {
    reason: "Serious Health Condition - Employee",
    label: "MED_CONT_ER_APPROVE",
    leave_dates: [
      startOfWeek(subWeeks(new Date(), 3)),
      startOfWeek(addWeeks(new Date(), 1)),
    ],
    address: {
      city: "Washington",
      line_1: "1600 Pennsylvania Avenue NW",
      state: "DC",
      zip: "20500",
    },
    work_pattern_spec: "standard",
    docs: { MASSID: {}, HCP: {} },
    payment: {
      payment_method: "Check",
    },
    employerResponse: {
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
      employer_decision: "Approve",
    },
  },
};

export const POSTBIRTH: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "POSTBIRTH",
    reason: "Pregnancy/Maternity",
    pregnant_or_recent_birth: true,
    docs: {
      MASSID: {},
      PREGNANCY_MATERNITY_FORM: {},
    },
    employerResponse: {
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
      employer_decision: "Approve",
    },
    leave_dates: [startOfWeek(subWeeks(new Date(), 1)), endOfWeek(new Date())],
  },
};

const fortyWeeksAgoStart = subWeeks(new Date(), 40);
const fortyWeeksAgoEnd = addWeeks(fortyWeeksAgoStart, 2);

export const FORTY_WEEKS_AGO: ScenarioSpecification = {
  claim: {
    docs: {
      HCP: {},
      MASSID: {},
    },
    employerResponse: {
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
    has_continuous_leave_periods: true,
    label: "FORTY_WEEKS_AGO",
    leave_dates: [fortyWeeksAgoStart, fortyWeeksAgoEnd],
    reason: "Serious Health Condition - Employee",
  },
  employee: {
    mass_id: true,
    wages: "eligible",
  },
};
const oneYearAgoStart = subWeeks(new Date(), 52);
const oneYearAgoEnd = addWeeks(oneYearAgoStart, 2);

export const ONE_YEAR_AGO: ScenarioSpecification = {
  claim: {
    docs: {
      HCP: {},
      MASSID: {},
    },
    employerResponse: {
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
    has_continuous_leave_periods: true,
    label: "ONE_YEAR_AGO",
    leave_dates: [oneYearAgoStart, oneYearAgoEnd],
    reason: "Serious Health Condition - Employee",
  },
  employee: {
    mass_id: true,
    wages: "eligible",
  },
};

const byGt60Start = addWeeks(new Date(), 5);
// This end date must be before the end of benefit year to support
// extensionSpanningBenefitYearGt60Days.ts
const byGt60End = addWeeks(byGt60Start, 2);

export const BENEFIT_YEAR_GREATER_THAN_60_DAYS: ScenarioSpecification = {
  claim: {
    docs: {
      HCP: {},
      MASSID: {},
    },
    employerResponse: {
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
    has_continuous_leave_periods: true,
    label: "BENEFIT_YEAR_GREATER_THAN_60_DAYS",
    leave_dates: [byGt60Start, byGt60End],
    reason: "Serious Health Condition - Employee",
  },
  employee: {
    mass_id: true,
    wages: "eligible",
  },
};

export const CONCURRENT_EMPLOYMENT_PORTAL: ScenarioSpecification = {
  employee: {
    metadata: {
      concurrentEmploymentScenario: 0,
    },
  },
  claim: {
    reason: "Serious Health Condition - Employee",
    label: "CONCURRENT_EMPLOYMENT_PORTAL",
    has_concurrent_employers: true,
    hours_worked_per_week_all_employers: 50,
    docs: {
      MASSID: {},
      HCP: {},
    },
    employerResponse: {
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
  },
};

const last7Quarters = quarters(new Date(), 7);
const lastDayOfCurrentQuarter = lastDayOfQuarter(new Date());

export const CONCURRENT_EMPLOYMENT_SCENARIO_1: ScenarioSpecification = {
  employee: {
    mass_id: true,
    metadata: {
      concurrentEmploymentScenario: 1,
    },
  },
  claim: {
    reason: "Serious Health Condition - Employee",
    label: "CONCURRENT_EMPLOYMENT_SCENARIO_1",
    has_concurrent_employers: false,
    leave_dates: [last7Quarters[4], addWeeks(last7Quarters[4], 2)],
    docs: {
      MASSID: {},
      HCP: {},
    },
    employerResponse: {
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
  },
  previousOccupationIndex: 0,
  currentOccupationIndex: 1,
};

export const CONCURRENT_EMPLOYMENT_SCENARIO_2: ScenarioSpecification = {
  employee: {
    mass_id: true,
    metadata: {
      concurrentEmploymentScenario: 2,
    },
  },
  claim: {
    reason: "Serious Health Condition - Employee",
    label: "CONCURRENT_EMPLOYMENT_SCENARIO_2",
    has_concurrent_employers: false,
    hours_worked_per_week_all_employers: 50,
    leave_dates: [
      subWeeks(lastDayOfCurrentQuarter, 9),
      subWeeks(lastDayOfCurrentQuarter, 7),
    ],
    docs: {
      MASSID: {},
      HCP: {},
    },
    employerResponse: {
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
  },
  currentOccupationIndex: 2,
};

export const CONCURRENT_EMPLOYMENT_SCENARIO_3: ScenarioSpecification = {
  employee: {
    mass_id: true,
    metadata: {
      concurrentEmploymentScenario: 3,
    },
  },
  claim: {
    reason: "Serious Health Condition - Employee",
    label: "CONCURRENT_EMPLOYMENT_SCENARIO_3",
    has_concurrent_employers: true,
    hours_worked_per_week_all_employers: 50,
    docs: {
      MASSID: {},
      HCP: {},
    },
    leave_dates: [subWeeks(last7Quarters[5], 2), last7Quarters[5]],
    employerResponse: {
      hours_worked_per_week: {
        hours_worked: 20,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
    work_pattern_spec: "0,360,360,360,360,360,0",
  },
  previousOccupationIndex: 0,
  currentOccupationIndex: 1,
  currentOccupationWorkPattern: "0,240,240,240,240,240,0",
};

export const CONCURRENT_EMPLOYMENT_SCENARIO_4: ScenarioSpecification = {
  employee: {
    mass_id: true,
    metadata: {
      concurrentEmploymentScenario: 4,
    },
  },
  claim: {
    reason: "Serious Health Condition - Employee",
    label: "CONCURRENT_EMPLOYMENT_SCENARIO_4",
    has_concurrent_employers: false,
    docs: {
      MASSID: {},
      HCP: {},
    },
    leave_dates: [subWeeks(last7Quarters[4], 2), last7Quarters[4]],
    employerResponse: {
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
  },
  currentOccupationIndex: 0,
};

export const CONCURRENT_EMPLOYMENT_SCENARIO_5: ScenarioSpecification = {
  employee: {
    mass_id: true,
    metadata: {
      concurrentEmploymentScenario: 5,
    },
  },
  claim: {
    reason: "Serious Health Condition - Employee",
    label: "CONCURRENT_EMPLOYMENT_SCENARIO_5",
    has_concurrent_employers: true,
    hours_worked_per_week_all_employers: 50,
    docs: {
      MASSID: {},
      HCP: {},
    },
    leave_dates: [
      subWeeks(lastDayOfCurrentQuarter, 9),
      subWeeks(lastDayOfCurrentQuarter, 7),
    ],
    employerResponse: {
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
  },
  currentOccupationIndex: 0,
};

export const UNF: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    reason: "Serious Health Condition - Employee",
    label: "UNF",
    has_continuous_leave_periods: true,
    shortClaim: true,
    payment: {
      payment_method: "Check",
    },
    is_withholding_tax: true,
    userNotFoundInformation: {
      currently_employed: true,
      date_of_hire: "2020-07-10",
      date_of_separation: null,
      employer_name: "PFML Testing UNF",
      recently_acquired_or_merged: null,
    },
    metadata: {
      quantity: 40,
    },
  },
};

export const MED_INTER_EL: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "MED_INTER_EL",
    shortClaim: true,
    reason: "Serious Health Condition - Employee",
    docs: {
      HCP: {},
      MASSID: {},
    },
    intermittent_leave_spec: true,
  },
};

// DUA_SCENARIO_2 uses the same employee defined in scenario 1 used in Integration testing
export const DUA_SCENARIO_2: ScenarioSpecification = {
  employee: {
    mass_id: true,
    metadata: {
      duaTestScenario: 1,
    },
  },
  claim: {
    reason: "Serious Health Condition - Employee",
    label: "DUA_SCENARIO_2",
    has_concurrent_employers: false,
    leave_dates: [last7Quarters[4], addWeeks(last7Quarters[4], 2)],
    docs: {
      MASSID: {},
      HCP: {},
    },
  },
};

export const DUA_SCENARIO_3: ScenarioSpecification = {
  employee: {
    mass_id: true,
    metadata: {
      duaTestScenario: 3,
    },
  },
  claim: {
    reason: "Serious Health Condition - Employee",
    label: "DUA_SCENARIO_3",
    has_concurrent_employers: false,
    leave_dates: [last7Quarters[4], addWeeks(last7Quarters[4], 2)],
    docs: {
      MASSID: {},
      HCP: {},
    },
  },
};

export const DUA_SCENARIO_4: ScenarioSpecification = {
  employee: {
    mass_id: true,
    metadata: {
      duaTestScenario: 4,
    },
  },
  claim: {
    reason: "Serious Health Condition - Employee",
    label: "DUA_SCENARIO_4",
    docs: {
      MASSID: {},
      HCP: {},
    },
  },
};
