import { parseISO } from "date-fns";

import { ScenarioSpecification } from "../generation/Scenario";

const retroactive: [Date, Date] = [
  parseISO("2025-03-03"),
  parseISO("2025-04-02"),
];

const retroactive16Weeks: [Date, Date] = [
  parseISO("2025-01-01"),
  parseISO("2025-05-01"),
];

const inProgress: [Date, Date] = [
  parseISO("2025-04-15"),
  parseISO("2025-05-15"),
];

const inProgress12Weeks: [Date, Date] = [
  parseISO("2025-03-15"),
  parseISO("2025-06-15"),
];

const inProgress16Weeks: [Date, Date] = [
  parseISO("2025-03-01"),
  parseISO("2025-07-01"),
];

export const PAYMENT_INFLIGHTS_1A: ScenarioSpecification = {
  employee: { mass_id: true, wages: 100_000, metadata: { scenario: "1a" } },
  claim: {
    label: "PAYMENT_INFLIGHTS_1A",
    reason: "Serious Health Condition - Employee",
    work_pattern_spec: "standard",
    has_continuous_leave_periods: true,
    leave_dates: retroactive,
    docs: {
      MASSID: {},
      HCP: {},
    },
    is_withholding_tax: true,
    metadata: {
      postSubmit: "approve",
      quantity: 30,
    },
  },
};

export const PAYMENT_INFLIGHTS_1B: ScenarioSpecification = {
  employee: { mass_id: true, wages: 100_000, metadata: { scenario: "1b" } },
  claim: {
    label: "PAYMENT_INFLIGHTS_1B",
    reason: "Serious Health Condition - Employee",
    has_continuous_leave_periods: true,
    work_pattern_spec: "standard",
    leave_dates: retroactive,
    docs: {
      MASSID: {},
      HCP: {},
    },
    is_withholding_tax: false,
    metadata: {
      postSubmit: "approve",
      quantity: 30,
      useFineosSubmitter: true,
    },
  },
};

export const PAYMENT_INFLIGHTS_2A_1: ScenarioSpecification = {
  employee: {
    mass_id: true,
    wages: 60_000,
    metadata: { scenario: "2a" },
  },
  claim: {
    label: "PAYMENT_INFLIGHTS_2A_1",
    reason: "Child Bonding",
    reason_qualifier: "Foster Care",
    has_continuous_leave_periods: true,
    is_withholding_tax: false,
    leave_dates: retroactive,
    work_pattern_spec: "standard",
    bondingDate: "past",
    docs: {
      MASSID: {},
      FOSTERPLACEMENT: {},
    },
    metadata: {
      useFineosSubmitter: true,
      quantity: 30,
    },
  },
};

export const PAYMENT_INFLIGHTS_2A_2: ScenarioSpecification = {
  employee: {
    mass_id: true,
    wages: 40_000,
    metadata: { scenario: "2a" },
  },
  claim: {
    label: "PAYMENT_INFLIGHTS_2A_2",
    reason: "Child Bonding",
    reason_qualifier: "Foster Care",
    has_continuous_leave_periods: true,
    is_withholding_tax: false,
    leave_dates: retroactive,
    bondingDate: "past",
    docs: {
      MASSID: {},
      FOSTERPLACEMENT: {},
    },
    metadata: {
      useFineosSubmitter: true,
      quantity: 30,
    },
  },
};

export const PAYMENT_INFLIGHTS_2B_1: ScenarioSpecification = {
  employee: { mass_id: true, wages: 60_000, metadata: { scenario: "2b" } },
  claim: {
    label: "PAYMENT_INFLIGHTS_2B_1",
    reason: "Child Bonding",
    reason_qualifier: "Foster Care",
    has_continuous_leave_periods: true,
    is_withholding_tax: false,
    leave_dates: retroactive,
    bondingDate: "past",
    docs: {
      MASSID: {},
      FOSTERPLACEMENT: {},
    },
    metadata: {
      postSubmit: "approve",
      quantity: 30,
    },
  },
};

export const PAYMENT_INFLIGHTS_2B_2: ScenarioSpecification = {
  employee: { mass_id: true, wages: 40_000, metadata: { scenario: "2b" } },
  claim: {
    label: "PAYMENT_INFLIGHTS_2B_2",
    reason: "Child Bonding",
    reason_qualifier: "Foster Care",
    has_continuous_leave_periods: true,
    is_withholding_tax: false,
    leave_dates: retroactive,
    bondingDate: "past",
    docs: {
      MASSID: {},
      FOSTERPLACEMENT: {},
    },
    metadata: {
      postSubmit: "approve",
      quantity: 30,
    },
  },
};

export const PAYMENT_INFLIGHTS_3A_1: ScenarioSpecification = {
  employee: {
    mass_id: true,
    wages: 60_000,
    metadata: { scenario: "3a" },
  },
  claim: {
    label: "PAYMENT_INFLIGHTS_3A_1",
    reason: "Serious Health Condition - Employee",
    work_pattern_spec: "standard",
    has_continuous_leave_periods: true,
    is_withholding_tax: false,
    leave_dates: retroactive,
    docs: {
      MASSID: {},
      HCP: {},
    },
    metadata: {
      postSubmit: "approve",
      quantity: 30,
    },
  },
};

export const PAYMENT_INFLIGHTS_3A_2: ScenarioSpecification = {
  employee: {
    mass_id: true,
    wages: 40_000,
    metadata: { scenario: "3a" },
  },
  claim: {
    label: "PAYMENT_INFLIGHTS_3A_2",
    reason: "Serious Health Condition - Employee",
    work_pattern_spec: "standard",
    has_continuous_leave_periods: true,
    is_withholding_tax: false,
    leave_dates: retroactive,
    docs: {
      MASSID: {},
      HCP: {},
    },
    metadata: {
      postSubmit: "approve",
      quantity: 30,
    },
  },
};

export const PAYMENT_INFLIGHTS_3B_1: ScenarioSpecification = {
  employee: {
    mass_id: true,
    wages: 60_000,
    metadata: { scenario: "3b" },
  },
  claim: {
    label: "PAYMENT_INFLIGHTS_3B_1",
    reason: "Serious Health Condition - Employee",
    work_pattern_spec: "standard",
    has_continuous_leave_periods: true,
    is_withholding_tax: false,
    leave_dates: retroactive,
    docs: {
      MASSID: {},
      HCP: {},
    },
    metadata: {
      postSubmit: "approve",
      useFineosSubmitter: true,
      quantity: 30,
    },
  },
};

export const PAYMENT_INFLIGHTS_3B_2: ScenarioSpecification = {
  employee: {
    mass_id: true,
    wages: 40_000,
    metadata: { scenario: "3b" },
  },
  claim: {
    label: "PAYMENT_INFLIGHTS_3B_2",
    reason: "Serious Health Condition - Employee",
    work_pattern_spec: "standard",
    has_continuous_leave_periods: true,
    is_withholding_tax: false,
    leave_dates: retroactive,
    docs: {
      MASSID: {},
      HCP: {},
    },
    metadata: {
      postSubmit: "approve",
      useFineosSubmitter: true,
      quantity: 30,
    },
  },
};

export const PAYMENT_INFLIGHTS_4A: ScenarioSpecification = {
  employee: { mass_id: true, wages: 40_000, metadata: { scenario: "4a" } },
  claim: {
    label: "PAYMENT_INFLIGHTS_4A",
    reason: "Care for a Family Member",
    work_pattern_spec: "standard",
    is_withholding_tax: false,
    has_continuous_leave_periods: true,
    leave_dates: inProgress,
    docs: {
      MASSID: {},
      CARING: {},
    },
    metadata: {
      quantity: 30,
    },
  },
};

export const PAYMENT_INFLIGHTS_4B: ScenarioSpecification = {
  employee: { mass_id: true, wages: 40_000, metadata: { scenario: "4b" } },
  claim: {
    label: "PAYMENT_INFLIGHTS_4B",
    reason: "Care for a Family Member",
    work_pattern_spec: "standard",
    has_continuous_leave_periods: true,
    is_withholding_tax: false,
    leave_dates: inProgress,
    docs: {
      MASSID: {},
      CARING: {},
    },
    metadata: {
      postSubmit: "approve",
      quantity: 30,
      useFineosSubmitter: true,
    },
  },
};

export const PAYMENT_INFLIGHTS_5A: ScenarioSpecification = {
  employee: { mass_id: true, wages: 60_000, metadata: { scenario: "5a" } },
  claim: {
    label: "PAYMENT_INFLIGHTS_5A",
    reason: "Child Bonding",
    reason_qualifier: "Newborn",
    has_continuous_leave_periods: true,
    is_withholding_tax: false,
    leave_dates: inProgress12Weeks,
    bondingDate: "past",
    docs: {
      MASSID: {},
      BIRTHCERTIFICATE: {},
    },
    metadata: {
      postSubmit: "approve",
      useFineosSubmitter: true,
      quantity: 30,
    },
  },
};

export const PAYMENT_INFLIGHTS_5B: ScenarioSpecification = {
  employee: { mass_id: true, wages: 60_000, metadata: { scenario: "5b" } },
  claim: {
    label: "PAYMENT_INFLIGHTS_5B",
    reason: "Child Bonding",
    reason_qualifier: "Newborn",
    work_pattern_spec: "standard",
    has_continuous_leave_periods: true,
    is_withholding_tax: false,
    leave_dates: inProgress12Weeks,
    bondingDate: "past",
    docs: {
      MASSID: {},
      BIRTHCERTIFICATE: {},
    },
    metadata: {
      quantity: 30,
    },
  },
};

export const PAYMENT_INFLIGHTS_6A: ScenarioSpecification = {
  employee: { mass_id: true, wages: 100_000, metadata: { scenario: "6a" } },
  claim: {
    label: "PAYMENT_INFLIGHTS_6A",
    reason: "Care for a Family Member",
    has_continuous_leave_periods: true,
    is_withholding_tax: true,
    leave_dates: retroactive,
    bondingDate: "past",
    docs: {
      MASSID: {},
      CARING: {},
    },
    metadata: {
      quantity: 30,
      useFineosSubmitter: true,
    },
  },
};

export const PAYMENT_INFLIGHTS_6B: ScenarioSpecification = {
  employee: { mass_id: true, wages: 100_000, metadata: { scenario: "6b" } },
  claim: {
    label: "PAYMENT_INFLIGHTS_6B",
    reason: "Care for a Family Member",
    has_continuous_leave_periods: true,
    is_withholding_tax: true,
    work_pattern_spec: "standard",
    leave_dates: retroactive,
    docs: {
      MASSID: {},
      CARING: {},
    },
    metadata: {
      postSubmit: "approve",
      quantity: 30,
    },
  },
};

export const PAYMENT_INFLIGHTS_7A: ScenarioSpecification = {
  employee: { mass_id: true, wages: 100_000, metadata: { scenario: "7a" } },
  claim: {
    label: "PAYMENT_INFLIGHTS_7A",
    reason: "Pregnancy/Maternity",
    work_pattern_spec: "standard",
    has_continuous_leave_periods: true,
    is_withholding_tax: false,
    leave_dates: retroactive,
    docs: {
      MASSID: {},
      PREGNANCY_MATERNITY_FORM: {},
    },
    metadata: {
      postSubmit: "approve",
      quantity: 30,
      useFineosSubmitter: true,
    },
  },
};

export const PAYMENT_INFLIGHTS_7B: ScenarioSpecification = {
  employee: { mass_id: true, wages: 100_000, metadata: { scenario: "7b" } },
  claim: {
    label: "PAYMENT_INFLIGHTS_7B",
    reason: "Pregnancy/Maternity",
    work_pattern_spec: "standard",
    has_continuous_leave_periods: true,
    is_withholding_tax: false,
    leave_dates: retroactive,
    docs: {
      MASSID: {},
      DIGITAL_MEDPREG_FORM: {},
    },
    metadata: {
      postSubmit: "approve",
      quantity: 30,
    },
  },
};

export const PAYMENT_INFLIGHTS_8A: ScenarioSpecification = {
  employee: { mass_id: true, wages: 80_000, metadata: { scenario: "8a" } },
  claim: {
    label: "PAYMENT_INFLIGHTS_8A",
    reason: "Military Caregiver",
    has_continuous_leave_periods: true,
    work_pattern_spec: "standard",
    is_withholding_tax: false,
    leave_dates: retroactive,
    docs: {
      MASSID: {},
      COVERED_SERVICE_MEMBER_ID: {},
      CARING: {},
    },
    metadata: {
      postSubmit: "approve",
      quantity: 30,
      useFineosSubmitter: true,
    },
  },
};

export const PAYMENT_INFLIGHTS_8B: ScenarioSpecification = {
  employee: { mass_id: true, wages: 80_000, metadata: { scenario: "8b" } },
  claim: {
    label: "PAYMENT_INFLIGHTS_8B",
    reason: "Pregnancy/Maternity",
    work_pattern_spec: "standard",
    is_withholding_tax: false,
    has_continuous_leave_periods: true,
    leave_dates: retroactive,
    docs: {
      MASSID: {},
      DIGITAL_MEDPREG_FORM: {},
    },
    metadata: {
      quantity: 30,
    },
  },
};

export const PAYMENT_INFLIGHTS_9A: ScenarioSpecification = {
  employee: { mass_id: true, wages: 80_000, metadata: { scenario: "9a" } },
  claim: {
    label: "PAYMENT_INFLIGHTS_9A",
    reason: "Pregnancy/Maternity",
    has_continuous_leave_periods: true,
    is_withholding_tax: false,
    work_pattern_spec: "standard",
    leave_dates: retroactive16Weeks,
    docs: {
      MASSID: {},
      PREGNANCY_MATERNITY_FORM: {},
    },
    metadata: {
      postSubmit: "approve",
      quantity: 30,
      useFineosSubmitter: true,
    },
  },
};

export const PAYMENT_INFLIGHTS_9B: ScenarioSpecification = {
  employee: { mass_id: true, wages: 80_000, metadata: { scenario: "9b" } },
  claim: {
    label: "PAYMENT_INFLIGHTS_9B",
    reason: "Pregnancy/Maternity",
    work_pattern_spec: "standard",
    has_continuous_leave_periods: true,
    is_withholding_tax: false,
    leave_dates: retroactive16Weeks,
    docs: {
      MASSID: {},
      PREGNANCY_MATERNITY_FORM: {},
    },
    metadata: {
      postSubmit: "approve",
      quantity: 30,
    },
  },
};

export const PAYMENT_INFLIGHTS_10A: ScenarioSpecification = {
  employee: { mass_id: true, wages: 80_000, metadata: { scenario: "10a" } },
  claim: {
    label: "PAYMENT_INFLIGHTS_10A",
    reason: "Military Caregiver",
    work_pattern_spec: "standard",
    has_continuous_leave_periods: true,
    is_withholding_tax: false,
    leave_dates: inProgress16Weeks,
    docs: {
      MASSID: {},
      COVERED_SERVICE_MEMBER_ID: {},
      CARING: {},
    },
    metadata: {
      postSubmit: "approve",
      quantity: 30,
      useFineosSubmitter: true,
    },
  },
};

export const PAYMENT_INFLIGHTS_10B: ScenarioSpecification = {
  employee: { mass_id: true, wages: 80_000, metadata: { scenario: "10b" } },
  claim: {
    label: "PAYMENT_INFLIGHTS_10B",
    reason: "Child Bonding",
    reason_qualifier: "Adoption",
    is_withholding_tax: true,
    work_pattern_spec: "standard",
    has_continuous_leave_periods: true,
    leave_dates: inProgress16Weeks,
    bondingDate: "past",
    docs: {
      MASSID: {},
      ADOPTIONCERT: {},
    },
    metadata: {
      postSubmit: "approve",
      quantity: 30,
    },
  },
};
