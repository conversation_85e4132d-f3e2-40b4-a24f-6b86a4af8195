/**
 * These are the scenarios used to generate claims used for training environment
 * refreshes.
 *
 * Scenarios are sometimes changed or retired, and occasionally brought back
 * from retirement.
 *
 * Historical scenarios may be retrieved from version control in this file or in
 * e2e/src/scenarios/training-refreshes-unused.ts.
 */

import { faker } from "@faker-js/faker";
import {
  addDays,
  addWeeks,
  differenceInMonths,
  min,
  parseISO,
  subDays,
} from "date-fns";
import { sample } from "lodash";

import { IntermittentLeavePeriods } from "../_api";
import { EmployeeGenerator } from "../generation/Employee";
import { ScenarioSpecification } from "../generation/Scenario";
import { random } from "../util/random";

const getRandomNumber = (min: number, max: number) => {
  return Math.floor(random() * (max - min + 1)) + min;
};

const randomWeeksLeave = (minimumWeeks = 4) => {
  return getRandomNumber(minimumWeeks, 12);
};

const earliestRandomDateString = "2025-05-14";
const latestRandomDateString = "2025-07-14";
const earliestRandomDate = parseISO(earliestRandomDateString);
// Guard against attempting to create a claim more than 60 days in advance.
const latestRandomDate = min([
  addDays(new Date(), 58),
  parseISO(latestRandomDateString),
]);

const generateRandomLeave = (minimumWeeks?: number): [Date, Date] => {
  const start = faker.date.between({
    from: earliestRandomDate,
    to: latestRandomDate,
  });
  const end = addWeeks(start, randomWeeksLeave(minimumWeeks));
  return [start, end];
};

const generateMaxLeave = (maxLeaveLength: number): [Date, Date] => {
  const start = faker.date.between({
    from: earliestRandomDate,
    to: addDays(new Date(), 58),
  });
  const end = subDays(addWeeks(start, maxLeaveLength), 1);
  return [start, end];
};

const formatDate = (date: Date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are zero-based
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const MINUTES_PER_HOUR = 60;

const GENERATE_LEAVE_DATES_DESCRIPTION = `Leave begins between ${earliestRandomDateString} - ${latestRandomDateString}\nEnds 4-12 weeks after start date`;

const FIRST_OF_MONTH_START_MAX_LEAVE_DESCRIPTION = `Leave begins between ${earliestRandomDateString} - ${latestRandomDateString}\nMaximum leave length`;

const intermittentLeaveSpecA: IntermittentLeavePeriods = {
  duration: 8,
  duration_basis: "Hours",
  frequency: 3,
  frequency_interval: 1,
  frequency_interval_basis: "Weeks",
};

/**
 * Leaves using this spec must have a minimum of at least five weeks leave. Four
 * weeks leave is less than one month, which will cause the application to fail
 * validation with the PFML API.
 */
const intermittentLeaveSpecB: IntermittentLeavePeriods = {
  duration: 8,
  duration_basis: "Hours",
  frequency: 5,
  frequency_interval: 1,
  frequency_interval_basis: "Months",
};

const intermittentLeaveSpecC: IntermittentLeavePeriods = {
  duration: 8,
  duration_basis: "Hours",
  frequency: 2,
  frequency_interval: 1,
  frequency_interval_basis: "Weeks",
};

const allIntermittentLeaveSpecs = [
  intermittentLeaveSpecA,
  intermittentLeaveSpecB,
  intermittentLeaveSpecC,
] as const;

function pickIntermittentLeaveSpec(
  leaveDates: readonly [Date, Date]
): IntermittentLeavePeriods {
  const [start, end] = leaveDates;
  const leaveIsAtLeastOneMonth = differenceInMonths(end, start) >= 1;
  const isSuitableSpec = (spec: IntermittentLeavePeriods) =>
    spec.frequency_interval_basis !== "Months" || leaveIsAtLeastOneMonth;
  const suitableSpecs = allIntermittentLeaveSpecs.filter(isSuitableSpec);
  const spec = sample(suitableSpecs);

  if (!spec || typeof spec !== "object") {
    throw new Error("Illegal state reached");
  }

  return spec;
}

// Deny claim for financial eligibility
export const TRNA: ScenarioSpecification = {
  employee: { mass_id: true, wages: "ineligible" },
  claim: {
    label: "TRNA",
    reason: "Serious Health Condition - Employee",
    work_pattern_spec: "standard",
    intermittent_leave_spec: pickIntermittentLeaveSpec,
    leave_dates: () => generateRandomLeave(4),
    docs: {
      MASSID: {},
      HCP: {},
    },
    is_withholding_tax: true,
    metadata: {
      postSubmit: "deny",
      leaveDescription: GENERATE_LEAVE_DATES_DESCRIPTION,
      quantity: 200,
    },
  },
};

// Prep for adjudication
export const TRNB: ScenarioSpecification = {
  employee: { mass_id: false, wages: "eligible" },
  claim: {
    label: "TRNB",
    reason: "Serious Health Condition - Employee",
    work_pattern_spec: "standard",
    has_continuous_leave_periods: true,
    leave_dates: () => generateRandomLeave(4),
    is_withholding_tax: true,
    docs: {
      // No docs.
    },
    metadata: {
      applicationIncomplete: true,
      leaveDescription: GENERATE_LEAVE_DATES_DESCRIPTION,
      quantity: 400,
      randomizePhoneNumber: true,
    },
  },
};

export const TRNC: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNC",
    reason: "Serious Health Condition - Employee",
    work_pattern_spec: "standard",
    has_continuous_leave_periods: true,
    leave_dates: () => generateRandomLeave(4),
    docs: {
      MASSID: {},
      HCP: {
        claimOverride: {
          get tax_identifier() {
            return EmployeeGenerator.generateSSN();
          },
        },
      },
    },
    metadata: {
      leaveDescription: GENERATE_LEAVE_DATES_DESCRIPTION,
      quantity: 200,
    },
  },
};

export const TRND: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRND",
    reason: "Serious Health Condition - Employee",
    has_continuous_leave_periods: true,
    docs: {
      MASSID: {},
      HCP: {},
    },
    employerResponse: {
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
    is_withholding_tax: true,
    metadata: {
      randomizePhoneNumber: true,
      postSubmit: "approve",
      leaveDescription: FIRST_OF_MONTH_START_MAX_LEAVE_DESCRIPTION,
      quantity: 200,
    },
    leave_dates: () => generateMaxLeave(20),
  },
};

export const TRNE: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNE",
    reason: "Serious Health Condition - Employee",
    has_continuous_leave_periods: true,
    docs: {
      MASSID: {},
      HCP: {},
    },
    employerResponse: {
      fraud: "No",
      leave_reason: "An illness or injury",
      comment: "EE works 35 hours per week. Otherwise claim is approved",
      hours_worked_per_week: {
        hours_worked: 35,
        employer_changes: "Amended",
      },
      employer_decision: "Approve",
    },
    is_withholding_tax: true,
    metadata: {
      leaveDescription: FIRST_OF_MONTH_START_MAX_LEAVE_DESCRIPTION,
      quantity: 200,
    },
    leave_dates: () => generateMaxLeave(20),
  },
};

export const TRNH: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNH",
    reason: "Serious Health Condition - Employee",
    has_continuous_leave_periods: true,
    leave_dates: () => generateRandomLeave(4),
    docs: {
      MASSID: {},
      HCP: {},
    },
    is_withholding_tax: true,
    metadata: {
      leaveDescription: GENERATE_LEAVE_DATES_DESCRIPTION,
      quantity: 300,
    },
  },
};

export const TRNM: ScenarioSpecification = {
  employee: { mass_id: false, wages: "eligible" },
  claim: {
    label: "TRNM",
    reason: "Serious Health Condition - Employee",
    reduced_leave_spec: "0,180,180,180,180,180,0",
    docs: {
      OOSID: {},
      HCP: {},
    },
    leave_dates: () => generateRandomLeave(4),
    metadata: {
      leaveDescription: GENERATE_LEAVE_DATES_DESCRIPTION,
      quantity: 400,
    },
  },
};

export const TRNO: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNO",
    reason: "Serious Health Condition - Employee",
    intermittent_leave_spec: pickIntermittentLeaveSpec,
    docs: {
      MASSID: {},
      HCP: {},
    },
    is_withholding_tax: true,
    employerResponse: {
      fraud: "No",
      employer_decision: "Approve",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
    },
    metadata: {
      postSubmit: "approve",
      leaveDescription: GENERATE_LEAVE_DATES_DESCRIPTION,
      quantity: 150,
    },
    leave_dates: () => generateRandomLeave(4),
  },
};

export const TRNP: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNP",
    reason: "Serious Health Condition - Employee",
    has_continuous_leave_periods: true,
    docs: {
      MASSID: {},
      HCP: {},
    },
    employerResponse: {
      fraud: "No",
      employer_decision: "Approve",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
    },
    leave_dates: () => generateRandomLeave(4),
    metadata: {
      leaveDescription: GENERATE_LEAVE_DATES_DESCRIPTION,
      quantity: 250,
    },
  },
};

export const TRNQ: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNQ",
    reason: "Serious Health Condition - Employee",
    intermittent_leave_spec: pickIntermittentLeaveSpec,
    docs: {
      MASSID: {},
      HCP: {},
    },
    employerResponse: {
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
    is_withholding_tax: true,
    metadata: {
      postSubmit: "approve",
      leaveDescription: FIRST_OF_MONTH_START_MAX_LEAVE_DESCRIPTION,
      quantity: 150,
    },
    leave_dates: () => generateMaxLeave(12),
  },
};

const TRNR_leave_dates = generateRandomLeave(4);

export const TRNR: ScenarioSpecification = {
  employee: { mass_id: true, wages: "ineligible" },
  claim: {
    label: "TRNR",
    reason: "Child Bonding",
    reason_qualifier: "Newborn",
    bondingDate: TRNR_leave_dates[0],
    docs: {
      MASSID: {},
      BIRTHCERTIFICATE: { dateOfBirth: formatDate(TRNR_leave_dates[0]) },
    },
    leave_dates: TRNR_leave_dates,
    metadata: {
      leaveDescription: GENERATE_LEAVE_DATES_DESCRIPTION,
      quantity: 100,
    },
  },
};

const TRNT_leave_dates = generateMaxLeave(12);

export const TRNT: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNT",
    reason: "Child Bonding",
    reason_qualifier: "Foster Care",
    bondingDate: TRNT_leave_dates[0],
    docs: {
      MASSID: {},
      FOSTERPLACEMENT: {},
    },
    employerResponse: {
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
    metadata: {
      postSubmit: "approve",
      leaveDescription: FIRST_OF_MONTH_START_MAX_LEAVE_DESCRIPTION,
      quantity: 200,
    },
    leave_dates: TRNT_leave_dates,
  },
};

export const TRNU: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNU",
    reason: "Child Bonding",
    bondingDate: parseISO("2025-06-14"),
    reason_qualifier: "Foster Care",
    docs: {
      MASSID: {},
      FOSTERPLACEMENT: {},
    },
    employerResponse: {
      fraud: "No",
      comment: " ", // We must have an empty space here because the ER Conflict task won't be created unless the comment field is filled with something
      employer_benefits: [
        {
          benefit_start_date: "2025-07-01",
          benefit_end_date: "2025-08-02",
          benefit_type: "Short-term disability insurance",
          employer_changes: "Added",
          is_full_salary_continuous: true,
        },
      ],
      employer_decision: "Approve",
      leave_reason: "An illness or injury",
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
    },
    leave_dates: [parseISO("2025-06-14"), parseISO("2025-09-14")],
    metadata: {
      leaveDescription: FIRST_OF_MONTH_START_MAX_LEAVE_DESCRIPTION,
      quantity: 150,
    },
  },
};

export const TRNV: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNV",
    reason: "Serious Health Condition - Employee",
    docs: {},
    leave_dates: () => generateMaxLeave(12),
    metadata: {
      applicationIncomplete: true,
      leaveDescription: FIRST_OF_MONTH_START_MAX_LEAVE_DESCRIPTION,
      quantity: 200,
    },
  },
};

const TRNY_bonding_date = generateRandomLeave(4);

export const TRNY: ScenarioSpecification = {
  employee: { mass_id: false, wages: "eligible" },
  claim: {
    label: "TRNY",
    reason: "Child Bonding",
    bondingDate: TRNY_bonding_date[0],
    reason_qualifier: "Foster Care",
    is_withholding_tax: true,
    has_continuous_leave_periods: true,
    docs: {
      OOSID: {},
      FOSTERPLACEMENT: {},
    },
    leave_dates: TRNY_bonding_date,
    metadata: {
      leaveDescription: GENERATE_LEAVE_DATES_DESCRIPTION,
      quantity: 400,
    },
  },
};

const TRNAA_leave_dates = generateMaxLeave(12);

export const TRNAA: ScenarioSpecification = {
  employee: { mass_id: false, wages: "eligible" },
  claim: {
    label: "TRNAA",
    reason: "Child Bonding",
    bondingDate: TRNAA_leave_dates[0],
    reason_qualifier: "Newborn",
    reduced_leave_spec: "0,180,180,180,180,180,0",
    docs: {
      BIRTHCERTIFICATE: { dateOfBirth: formatDate(TRNAA_leave_dates[0]) },
      OOSID: {},
    },
    employerResponse: {
      fraud: "No",
      leave_reason: "An illness or injury",
      comment: "EE works 35 hours per week. Otherwise claim is approved",
      employer_decision: "Approve",
      hours_worked_per_week: {
        employer_changes: "Amended",
        hours_worked: 35,
      },
    },
    leave_dates: TRNAA_leave_dates,
    metadata: {
      leaveDescription: FIRST_OF_MONTH_START_MAX_LEAVE_DESCRIPTION,
      quantity: 200,
    },
  },
};

export const TRNAB: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNAB",
    reason: "Pregnancy/Maternity",
    has_continuous_leave_periods: true,
    pregnant_or_recent_birth: true,
    docs: {
      MASSID: {},
      PREGNANCY_MATERNITY_FORM: {
        expectedDateOfBirth: "leaveStart",
        postnatalRecoveryWeeks: 8,
        prenatalCareWeeks: 0,
      },
    },
    employerResponse: {
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
    metadata: {
      postSubmit: "approve",
      leaveDescription: GENERATE_LEAVE_DATES_DESCRIPTION,
      quantity: 200,
    },
    leave_dates: () => generateMaxLeave(8),
  },
};

export const TRNAC: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNAC",
    reason: "Pregnancy/Maternity",
    has_continuous_leave_periods: true,
    is_withholding_tax: true,
    docs: {
      MASSID: {},
      PREGNANCY_MATERNITY_FORM: {
        expectedDateOfBirth: "leaveStart",
        postnatalRecoveryWeeks: 8,
        prenatalCareWeeks: 0,
      },
    },
    employerResponse: {
      employer_benefits: [],
      employer_decision: "Approve",
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      previous_leaves: [],
    },
    leave_dates: () => generateRandomLeave(4),
    metadata: {
      leaveDescription: GENERATE_LEAVE_DATES_DESCRIPTION,
      quantity: 200,
    },
  },
};

export const TRNAD: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNAD",
    reason: "Pregnancy/Maternity",
    work_pattern_spec: "standard",
    has_continuous_leave_periods: true,
    pregnant_or_recent_birth: true,
    docs: {
      MASSID: {},
      PREGNANCY_MATERNITY_FORM: {
        expectedDateOfBirth: "leaveEnd",
        postnatalRecoveryWeeks: 0,
        prenatalCareWeeks: 20,
      },
    },
    employerResponse: {
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
    metadata: {
      postSubmit: "approve",
      leaveDescription: FIRST_OF_MONTH_START_MAX_LEAVE_DESCRIPTION,
      quantity: 200,
    },
    leave_dates: () => generateMaxLeave(20),
  },
};

const TRNAE_leave_dates = generateRandomLeave(4);

function calculateWeeksBetweenDates(start: Date, end: Date): number {
  const msPerWeek = 1000 * 60 * 60 * 24 * 7;
  const diffInMs = end.getTime() - start.getTime();
  return Math.round(diffInMs / msPerWeek);
}

export const TRNAE: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNAE",
    reason: "Pregnancy/Maternity",
    work_pattern_spec: "standard",
    has_continuous_leave_periods: true,
    pregnant_or_recent_birth: true,
    docs: {
      MASSID: {},
      PREGNANCY_MATERNITY_FORM: {
        expectedDateOfBirth: "leaveEnd",
        postnatalRecoveryWeeks: 0,
        prenatalCareWeeks: calculateWeeksBetweenDates(
          TRNAE_leave_dates[0],
          TRNAE_leave_dates[1]
        ),
      },
    },
    employerResponse: {
      employer_benefits: [],
      employer_decision: "Approve",
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      previous_leaves: [],
    },
    leave_dates: TRNAE_leave_dates,
    metadata: {
      leaveDescription: GENERATE_LEAVE_DATES_DESCRIPTION,
      quantity: 200,
    },
  },
};

export const TRNAF: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNAF",
    reason: "Care for a Family Member",
    is_withholding_tax: true,
    work_pattern_spec: "standard",
    leave_dates: () => generateRandomLeave(4),
    docs: {
      CARING: {
        leaveStartDateBlank: true,
      },
      MASSID: {},
    },
    metadata: {
      leaveDescription: GENERATE_LEAVE_DATES_DESCRIPTION,
      quantity: 150,
    },
  },
};

export const TRNAG: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNAG",
    reason: "Care for a Family Member",
    is_withholding_tax: true,
    work_pattern_spec: "standard",
    docs: {
      MASSID: {},
      CARING: {},
    },
    employerResponse: {
      fraud: "No",
      employer_decision: "Approve",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
    },
    metadata: {
      postSubmit: "approve",
      leaveDescription: FIRST_OF_MONTH_START_MAX_LEAVE_DESCRIPTION,
      quantity: 200,
    },
    leave_dates: () => generateMaxLeave(12),
  },
};

export const TRNAH: ScenarioSpecification = {
  employee: { mass_id: false, wages: "eligible" },
  claim: {
    label: "TRNAH",
    reason: "Care for a Family Member",
    work_pattern_spec: "standard",
    docs: {
      CARING: {},
      OOSID: {},
    },
    employerResponse: {
      fraud: "No",
      employer_benefits: [
        {
          employer_changes: "Added",
          benefit_type: "Short-term disability insurance",
          benefit_start_date: "2025-06-15",
          benefit_end_date: "2025-06-26",
          is_full_salary_continuous: true,
        },
      ],
      comment: "", // Required by FINEOS, unvalidated by PFML API. Can be blank.
      has_amendments: true,
      employer_decision: "Approve",
      leave_reason: "An illness or injury",
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
    },
    leave_dates: () => generateRandomLeave(12),
    metadata: {
      leaveDescription: GENERATE_LEAVE_DATES_DESCRIPTION,
      quantity: 300,
    },
  },
};

export const TRNAL: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNAL",
    reason: "Care for a Family Member",
    leave_dates: () => generateRandomLeave(4),
    docs: {
      MASSID: {},
      CARING: {},
    },
    metadata: {
      leaveDescription: GENERATE_LEAVE_DATES_DESCRIPTION,
      quantity: 300,
    },
  },
};

export const TRNAM: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNAM",
    reason: "Care for a Family Member",
    reduced_leave_spec: "0,300,300,300,300,300,0",
    docs: {
      MASSID: {},
      CARING: {},
    },
    employerResponse: {
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
    metadata: {
      postSubmit: "approve",
      leaveDescription: FIRST_OF_MONTH_START_MAX_LEAVE_DESCRIPTION,
      quantity: 100,
    },
    leave_dates: () => generateRandomLeave(4),
  },
};

export const TRNAP: ScenarioSpecification = {
  employee: { mass_id: false, wages: "eligible" },
  claim: {
    label: "TRNAP",
    reason: "Care for a Family Member",
    reduced_leave_spec: "0,240,240,240,240,240,0",
    is_withholding_tax: true,
    leave_dates: () => generateRandomLeave(4),
    docs: {
      OOSID: {},
      CARING: {},
    },
    metadata: {
      leaveDescription: GENERATE_LEAVE_DATES_DESCRIPTION,
      quantity: 100,
    },
  },
};

export const TRNAQ1: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNAQ1",
    reason: "Care for a Family Member",
    leave_dates: () => generateRandomLeave(4),
    intermittent_leave_spec: pickIntermittentLeaveSpec,
    docs: {
      MASSID: {},
      CARING: {},
    },
    employerResponse: {
      employer_decision: "Approve",
      leave_reason: "An illness or injury",
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 35,
        employer_changes: "Amended",
      },
      comment: "EE works 35 hours per week. Otherwise claim is approved",
    },
    metadata: {
      quantity: 200,
      leaveDescription: GENERATE_LEAVE_DATES_DESCRIPTION,
    },
  },
};

export const TRNAS: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNAS",
    intermittent_leave_spec: pickIntermittentLeaveSpec,
    reason: "Care for a Family Member",
    docs: {
      MASSID: {},
      CARING: {},
    },
    employerResponse: {
      fraud: "No",
      employer_decision: "Approve",
      has_amendments: false,
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
    },
    metadata: {
      leaveDescription: GENERATE_LEAVE_DATES_DESCRIPTION,
      quantity: 100,
    },
    leave_dates: () => generateRandomLeave(4),
  },
};

export const TRNAT: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNAT",
    intermittent_leave_spec: pickIntermittentLeaveSpec,
    reason: "Care for a Family Member",
    docs: {
      MASSID: {},
      CARING: {},
    },
    employerResponse: {
      fraud: "No",
      employer_decision: "Approve",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
    },
    metadata: {
      postSubmit: "approve",
      leaveDescription: FIRST_OF_MONTH_START_MAX_LEAVE_DESCRIPTION,
      quantity: 100,
    },
    leave_dates: () => generateMaxLeave(12),
  },
};

export const TRNAU: ScenarioSpecification = {
  employee: { mass_id: false, wages: "eligible" },
  claim: {
    label: "TRNAU",
    reason: "Child Bonding",
    reason_qualifier: "Newborn",
    leave_dates: () => generateRandomLeave(4),
    bondingDate: earliestRandomDate,
    intermittent_leave_spec: pickIntermittentLeaveSpec,
    employerResponse: {
      fraud: "No",
      comment: "Denying leave per discussion with Employee",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      leave_reason: "An illness or injury",
      employer_decision: "Deny",
    },
    docs: {
      OOSID: {},
      BIRTHCERTIFICATE: {},
    },
    metadata: {
      leaveDescription: GENERATE_LEAVE_DATES_DESCRIPTION,
      quantity: 400,
    },
  },
};

export const TRNAX: ScenarioSpecification = {
  employee: { mass_id: false, wages: "eligible" },
  claim: {
    label: "TRNAX",
    reason: "Care for a Family Member",
    reduced_leave_spec: "0,240,240,240,240,240,0",
    docs: {
      OOSID: {},
    },
    leave_dates: () => generateRandomLeave(4),
    metadata: {
      leaveDescription: GENERATE_LEAVE_DATES_DESCRIPTION,
      quantity: 250,
    },
  },
};

export const TRNB1: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNB1",
    reason: "Serious Health Condition - Employee",
    intermittent_leave_spec: pickIntermittentLeaveSpec,
    docs: {
      FMLA_FORM: {},
      MASSID: {},
    },
    employerResponse: {
      employer_decision: "Approve",
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
    },
    work_pattern_spec: "standard",
    leave_dates: () => generateRandomLeave(4),
    metadata: {
      leaveDescription: GENERATE_LEAVE_DATES_DESCRIPTION,
      quantity: 150,
    },
  },
};

export const TRNB2: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNB2",
    reason: "Pregnancy/Maternity",
    docs: {
      DIGITAL_MEDPREG_FORM: { kind: "postnatal" },
      MASSID: {},
    },
    employerResponse: {
      employer_decision: "Approve",
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
    },
    has_continuous_leave_periods: true,
    work_pattern_spec: "standard",
    leave_dates: () => generateRandomLeave(4),
    metadata: {
      leaveDescription: GENERATE_LEAVE_DATES_DESCRIPTION,
      quantity: 200,
    },
  },
};

export const TRNB3: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNB3",
    reason: "Serious Health Condition - Employee",
    docs: {
      DIGITAL_MEDICAL_FORM: {},
      MASSID: {},
    },
    employerResponse: {
      employer_decision: "Approve",
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
    },
    has_continuous_leave_periods: true,
    work_pattern_spec: "standard",
    leave_dates: () => generateRandomLeave(4),
    metadata: {
      leaveDescription: GENERATE_LEAVE_DATES_DESCRIPTION,
      quantity: 150,
    },
  },
};

const OI_LEAVE_DATES: [Date, Date] = [
  parseISO("2025-06-14"),
  parseISO("2025-07-19"),
];

export const TRNOI1: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNOI1",
    reason: "Serious Health Condition - Employee",
    work_pattern_spec: "standard",
    leave_dates: OI_LEAVE_DATES,
    docs: {
      MASSID: {},
      HCP: {},
    },
    employerResponse: {
      employer_benefits: [
        {
          benefit_start_date: "2025-07-05",
          benefit_end_date: "2025-07-15",
          benefit_type: "Short-term disability insurance",
          employer_changes: "Added",
          is_full_salary_continuous: true,
        },
      ],
      employer_decision: "Approve",
      fraud: "No",
      has_amendments: true,
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      leave_reason: "An illness or injury",
    },
    metadata: {
      postSubmit: "approve",
      quantity: 50,
    },
  },
};

export const TRNOI2: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNOI2",
    reason: "Serious Health Condition - Employee",
    work_pattern_spec: "standard",
    leave_dates: OI_LEAVE_DATES,
    is_withholding_tax: true,
    docs: {
      MASSID: {},
      HCP: {},
    },
    employerResponse: {
      employer_benefits: [
        {
          benefit_type: "Short-term disability insurance",
          benefit_start_date: "2025-07-01",
          benefit_end_date: "2025-07-14",
          employer_changes: "Added",
          is_full_salary_continuous: true,
        },
      ],
      fraud: "No",
      has_amendments: true,
      employer_decision: "Approve",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
    },
    metadata: {
      postSubmit: "approve",
      quantity: 50,
    },
  },
};

export const TRNOI3: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNOI3",
    reason: "Pregnancy/Maternity",
    work_pattern_spec: "standard",
    is_withholding_tax: true,
    leave_dates: OI_LEAVE_DATES,
    docs: {
      MASSID: {},
      PREGNANCY_MATERNITY_FORM: {},
    },
    employerResponse: {
      employer_benefits: [
        {
          benefit_type: "Short-term disability insurance",
          benefit_start_date: "2025-07-01",
          benefit_end_date: "2025-07-14",
          employer_changes: "Added",
          benefit_amount_dollars: 500,
          benefit_amount_frequency: "Per Month",
          is_full_salary_continuous: false,
        },
      ],
      fraud: "No",
      has_amendments: true,
      employer_decision: "Approve",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
    },
    metadata: {
      postSubmit: "approve",
      quantity: 50,
    },
  },
};

export const TRNOI4: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNOI4",
    reason: "Pregnancy/Maternity",
    work_pattern_spec: "standard",
    leave_dates: OI_LEAVE_DATES,
    docs: {
      MASSID: {},
      PREGNANCY_MATERNITY_FORM: {},
    },
    is_withholding_tax: true,
    employerResponse: {
      employer_benefits: [
        {
          benefit_type: "Short-term disability insurance",
          benefit_start_date: "2025-07-01",
          benefit_end_date: "2025-07-14",
          is_full_salary_continuous: true,
          employer_changes: "Added",
        },
      ],
      fraud: "No",
      has_amendments: true,
      employer_decision: "Approve",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      previous_leaves: [],
    },
    metadata: {
      postSubmit: "approve",
      quantity: 50,
    },
  },
};

const OL_LEAVE_DATES: [Date, Date] = [
  parseISO("2025-06-14"),
  parseISO("2025-09-05"),
];

export const TRNOL1: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNOL1",
    reason: "Serious Health Condition - Employee",
    work_pattern_spec: "standard",
    leave_dates: OL_LEAVE_DATES,
    docs: {
      MASSID: {},
      HCP: {},
    },
    is_withholding_tax: true,
    employerResponse: {
      fraud: "No",
      has_amendments: false,
      employer_decision: "Approve",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      previous_leaves: [
        {
          employer_changes: "Unchanged",
          previous_leave_id: null,
        },
      ],
    },
    previous_leaves: [
      {
        type: "same_reason",
        leave_reason: "An illness or injury",
        is_continuous: true,
        is_for_current_employer: true,
        leave_start_date: "2025-01-15",
        leave_end_date: "2025-01-31",
      },
    ],
    metadata: {
      closeEmployerApprovalTaskInAdjudication: true,
      postSubmit: "adjudicate",
      quantity: 50,
    },
  },
};

export const TRNOL3: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNOL3",
    reason: "Child Bonding",
    bondingDate: OL_LEAVE_DATES[0],
    reason_qualifier: "Newborn",
    is_withholding_tax: true,
    work_pattern_spec: "standard",
    leave_dates: OL_LEAVE_DATES,
    docs: {
      MASSID: {},
      BIRTHCERTIFICATE: { dateOfBirth: formatDate(OL_LEAVE_DATES[0]) },
    },
    employerResponse: {
      fraud: "No",
      has_amendments: false,
      employer_decision: "Approve",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      previous_leaves: [
        {
          employer_changes: "Unchanged",
        },
      ],
    },
    previous_leaves: [
      {
        type: "same_reason",
        leave_reason: "Bonding with my child after birth or placement",
        is_continuous: true,
        is_for_current_employer: true,
        leave_start_date: "2025-01-01",
        leave_end_date: "2025-01-19",
      },
    ],
    metadata: {
      closeEmployerApprovalTaskInAdjudication: true,
      postSubmit: "adjudicate",
      quantity: 50,
    },
  },
};

const ER_LEAVE_DATES: [Date, Date] = [
  parseISO("2025-02-14"),
  parseISO("2025-04-19"),
];

export const TRNER1: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNER1",
    reason: "Serious Health Condition - Employee",
    work_pattern_spec: "standard",
    is_withholding_tax: true,
    leave_dates: ER_LEAVE_DATES,
    docs: {
      HCP: {},
      MASSID: {},
    },
    employerResponse: {
      fraud: "No",
      comment: "EE works 37.5 hours per week. Otherwise claim is approved",
      employer_decision: "Approve",
      has_amendments: true,
      hours_worked_per_week: {
        employer_changes: "Amended",
        hours_worked: 37.5,
      },
      leave_reason: "An illness or injury",
    },
    metadata: {
      postSubmit: "adjudicate",
      quantity: 150,
    },
  },
};

export const TRNER2: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNER2",
    is_withholding_tax: true,
    reason: "Serious Health Condition - Employee",
    work_pattern_spec: "standard",
    leave_dates: ER_LEAVE_DATES,
    docs: {
      HCP: {},
      MASSID: {},
    },
    employerResponse: {
      fraud: "No",
      comment:
        "Employee did not inform employer of leave with proper advanced notice.",
      employer_decision: "Deny",
      has_amendments: true,
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
      leave_reason: "An illness or injury",
    },
    metadata: {
      postSubmit: "adjudicate",
      quantity: 150,
    },
  },
};

export const TRNER3: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNER3",
    reason: "Serious Health Condition - Employee",
    work_pattern_spec: "standard",
    leave_dates: ER_LEAVE_DATES,
    docs: {
      MASSID: {},
      HCP: {},
    },
    is_withholding_tax: true,
    employerResponse: {
      comment: "Employee did not make request. Suggested fraud.",
      employer_decision: "Deny",
      fraud: "Yes",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      leave_reason: "An illness or injury",
    },
    metadata: {
      postSubmit: "adjudicate",
      quantity: 150,
    },
  },
};

export const TRNER4: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNER4",
    reason: "Pregnancy/Maternity",
    work_pattern_spec: "standard",
    leave_dates: ER_LEAVE_DATES,
    docs: {
      MASSID: {},
      PREGNANCY_MATERNITY_FORM: {},
    },
    is_withholding_tax: true,
    employerResponse: {
      has_amendments: true,
      comment: "Employee notified employer of leave in time. Approve request.",
      employer_decision: "Approve",
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      leave_reason: "An illness or injury",
    },
    metadata: {
      postSubmit: "adjudicate",
      quantity: 150,
    },
  },
};

const E_LEAVE_DATES: [Date, Date] = [
  parseISO("2025-06-14"),
  parseISO("2025-08-19"),
];

export const TRNE1: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    reason: "Serious Health Condition - Employee",
    label: "TRNE1",
    leave_dates: E_LEAVE_DATES,
    has_continuous_leave_periods: true,
    employerResponse: {
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
    docs: {
      MASSID: {},
      HCP: {},
    },
    metadata: {
      modifications: [
        {
          change_request_type: "Extension",
          end_date: "2025-09-19",
        },
      ],
      quantity: 100,
      postSubmit: "approveWithModifications",
    },
  },
};

export const TRNE2: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    reason: "Serious Health Condition - Employee",
    label: "TRNE2",
    leave_dates: E_LEAVE_DATES,
    has_continuous_leave_periods: true,
    employerResponse: {
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
    docs: {
      MASSID: {},
      HCP: {},
    },
    metadata: {
      modifications: [
        {
          change_request_type: "Extension",
          end_date: "2025-09-10",
        },
      ],
      quantity: 100,
      postSubmit: "approveWithModifications",
    },
  },
};

export const TRNE3: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    reason: "Serious Health Condition - Employee",
    label: "TRNE3",
    leave_dates: E_LEAVE_DATES,
    employerResponse: {
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
    reduced_leave_spec: "0,240,240,240,240,240,0",
    docs: {
      MASSID: {},
      HCP: {},
    },
    metadata: {
      modifications: [
        {
          change_request_type: "Extension",
          end_date: "2025-08-24",
        },
      ],
      quantity: 100,
      postSubmit: "approveWithModifications",
    },
  },
};

export const TRNE5: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    reason: "Pregnancy/Maternity",
    label: "TRNE5",
    leave_dates: E_LEAVE_DATES,
    employerResponse: {
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
    has_continuous_leave_periods: true,
    docs: {
      MASSID: {},
      PREGNANCY_MATERNITY_FORM: {},
    },
    metadata: {
      postSubmit: "approveWithModifications",
      modifications: [
        {
          change_request_type: "Medical To Bonding Transition",
          start_date: "2025-08-20",
          end_date: "2025-08-25",
          date_of_birth: "2025-05-14",
        },
      ],
      postApprovalDocuments: {
        BIRTHCERTIFICATE: {},
      },
      quantity: 100,
    },
  },
};

export const TRNC1: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    reason: "Serious Health Condition - Employee",
    label: "TRNC1",
    leave_dates: [parseISO("2025-06-14"), parseISO("2025-09-19")],
    has_continuous_leave_periods: true,
    employerResponse: {
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
    docs: {
      MASSID: {},
      HCP: {},
    },
    metadata: {
      modifications: [
        {
          change_request_type: "Cancellation",
          end_date: "2025-09-12",
        },
      ],
      quantity: 100,
      postSubmit: "approveWithModifications",
    },
  },
};

export const TRNC2: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    reason: "Serious Health Condition - Employee",
    label: "TRNC2",
    leave_dates: [parseISO("2025-04-06"), parseISO("2025-09-09")],
    reduced_leave_spec: "0,240,240,240,240,240,0",
    employerResponse: {
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
    docs: {
      MASSID: {},
      HCP: {},
    },
    metadata: {
      modifications: [
        {
          change_request_type: "Cancellation",
          end_date: "2025-09-02",
        },
      ],
      quantity: 100,
      postSubmit: "approveWithModifications",
    },
  },
};

export const TRNC3: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    reason: "Serious Health Condition - Employee",
    label: "TRNC3",
    leave_dates: [parseISO("2025-06-15"), parseISO("2025-09-15")],
    intermittent_leave_spec: {
      duration_basis: "Hours",
      duration: 8,
      frequency_interval_basis: "Weeks",
      frequency_interval: 1,
      frequency: 2,
    },
    employerResponse: {
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
    docs: {
      MASSID: {},
      HCP: {},
    },
    metadata: {
      quantity: 100,
      postSubmit: "approveWithModifications",
      modifications: [
        {
          change_request_type: "Cancellation",
          end_date: "2025-09-09",
        },
      ],
    },
  },
};

export const TRNC5: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    reason: "Child Bonding",
    label: "TRNC5",
    leave_dates: [parseISO("2025-06-08"), parseISO("2025-09-08")],
    employerResponse: {
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
    reason_qualifier: "Newborn",
    bondingDate: parseISO("2025-09-08"),
    reduced_leave_spec: "0,240,240,240,240,240,0",
    docs: {
      MASSID: {},
      BIRTHCERTIFICATE: { dateOfBirth: "2025-09-08" },
    },
    metadata: {
      postSubmit: "approveWithModifications",
      modifications: [
        {
          change_request_type: "Cancellation",
          end_date: "2025-09-01",
        },
      ],
      quantity: 100,
    },
  },
};

export const TRNC6: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    reason: "Serious Health Condition - Employee",
    label: "TRNC6",
    leave_dates: [parseISO("2025-05-09"), parseISO("2025-06-10")],
    employerResponse: {
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
    has_continuous_leave_periods: true,
    docs: {
      MASSID: {},
      HCP: {},
    },
    reduced_leave_spec: [
      {
        start_date: "2025-06-11",
        end_date: "2025-08-19",
        sunday_off_minutes: 0,
        monday_off_minutes: 4 * MINUTES_PER_HOUR,
        tuesday_off_minutes: 4 * MINUTES_PER_HOUR,
        wednesday_off_minutes: 4 * MINUTES_PER_HOUR,
        thursday_off_minutes: 4 * MINUTES_PER_HOUR,
        friday_off_minutes: 4 * MINUTES_PER_HOUR,
        saturday_off_minutes: 0,
      },
    ],
    metadata: {
      modifications: [
        {
          change_request_type: "Cancellation",
          end_date: "2025-08-09",
        },
      ],
      postSubmit: "approveWithModifications",
      quantity: 100,
    },
  },
};

export const TRNC7: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    reason: "Serious Health Condition - Employee",
    label: "TRNC7",
    has_continuous_leave_periods: true,
    leave_dates: [parseISO("2025-05-09"), parseISO("2025-06-13")],
    reduced_leave_spec: [
      {
        start_date: "2025-06-14",
        end_date: "2025-08-22",
        sunday_off_minutes: 0,
        monday_off_minutes: 4 * MINUTES_PER_HOUR,
        tuesday_off_minutes: 4 * MINUTES_PER_HOUR,
        wednesday_off_minutes: 4 * MINUTES_PER_HOUR,
        thursday_off_minutes: 4 * MINUTES_PER_HOUR,
        friday_off_minutes: 4 * MINUTES_PER_HOUR,
        saturday_off_minutes: 0,
      },
    ],
    employerResponse: {
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
    docs: {
      MASSID: {},
      HCP: {},
    },
    metadata: {
      modifications: [
        {
          change_request_type: "Cancellation",
          end_date: "2025-08-04",
        },
      ],
      quantity: 100,
      postSubmit: "approveWithModifications",
    },
  },
};

export const UNFEE: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    reason: "Serious Health Condition - Employee",
    label: "UNFEE",
    has_continuous_leave_periods: true,
    leave_dates: [parseISO("2025-06-14"), parseISO("2025-08-19")],
    payment: {
      payment_method: "Check",
    },
    is_withholding_tax: true,
    userNotFoundInformation: {
      currently_employed: true,
      date_of_hire: "2020-07-10",
      date_of_separation: null,
      // @TODO: Match employer name to FEIN. Should exist in FINEOS.
      employer_name: "Strategize Dynamic Technologies and Sons",
      recently_acquired_or_merged: null,
    },
    metadata: {
      quantity: 20,
      // @TODO: Match FEIN to employer name. Should exist in FINEOS.
      userNotFoundFEIN: "35-8148217",
      postSubmit: "ensureEmployeeNotPresent",
      // There should not be a matching SSN in FINEOS.
      randomizeSSN: true,
    },
  },
};

export const UNFER1: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    reason: "Child Bonding",
    reason_qualifier: "Adoption",
    bondingDate: parseISO("2025-06-14"),
    label: "UNFER1",
    has_continuous_leave_periods: true,
    leave_dates: [parseISO("2025-06-14"), parseISO("2025-08-19")],
    payment: {
      payment_method: "Check",
    },
    is_withholding_tax: true,
    userNotFoundInformation: {
      currently_employed: true,
      date_of_hire: "2020-07-12",
      date_of_separation: null,
      // @TODO: Match employer name to FEIN. Should exist in FINEOS.
      employer_name: "Strategize Dynamic Technologies and Sons",
      recently_acquired_or_merged: null,
    },
    metadata: {
      quantity: 20,
      // @TODO: Match FEIN to employer name. Should exist in FINEOS, but should
      //        not be the claimant’s actual employer.
      userNotFoundFEIN: "42-0392280",
    },
  },
};

export const UNFER2: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    reason: "Child Bonding",
    reason_qualifier: "Adoption",
    bondingDate: parseISO("2025-06-14"),
    label: "UNFER2",
    leave_dates: [parseISO("2025-06-14"), parseISO("2025-08-19")],
    reduced_leave_spec: "0,240,240,240,240,240,0",
    payment: {
      payment_method: "Check",
    },
    is_withholding_tax: true,
    userNotFoundInformation: {
      currently_employed: true,
      date_of_hire: "2020-07-12",
      date_of_separation: null,
      // @TODO: Match employer name to FEIN. Should exist in FINEOS.
      employer_name: "Strategize Dynamic Technologies and Sons",
      recently_acquired_or_merged: null,
    },
    metadata: {
      quantity: 20,
      // @TODO: Match FEIN to employer name. Should exist in FINEOS, but should
      //        not be the claimant’s actual employer.
      userNotFoundFEIN: "42-0392280",
    },
  },
};

export const UNFER3: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    reason: "Child Bonding",
    bondingDate: parseISO("2025-06-14"),
    reason_qualifier: "Adoption",
    label: "UNFER3",
    leave_dates: [parseISO("2025-06-14"), parseISO("2025-08-19")],
    intermittent_leave_spec: pickIntermittentLeaveSpec,
    payment: {
      payment_method: "Check",
    },
    is_withholding_tax: true,
    userNotFoundInformation: {
      currently_employed: true,
      date_of_hire: "2020-07-12",
      date_of_separation: null,
      // @TODO: Match employer name to FEIN. Should exist in FINEOS.
      employer_name: "Strategize Dynamic Technologies and Sons",
      recently_acquired_or_merged: null,
    },
    metadata: {
      quantity: 20,
      // @TODO: Match FEIN to employer name. Should exist in FINEOS, but should
      //        not be the claimant’s actual employer.
      userNotFoundFEIN: "42-0392280",
    },
  },
};

export const TRNMBE1: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    reason: "Pregnancy/Maternity",
    label: "TRNMBE1",
    leave_dates: [parseISO("2025-06-04"), parseISO("2025-08-01")],
    has_continuous_leave_periods: true,
    employerResponse: {
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
    docs: {
      MASSID: {},
      PREGNANCY_MATERNITY_FORM: {
        expectedDateOfBirth: "leaveEnd",
        postnatalRecoveryWeeks: 0,
        prenatalCareWeeks: 8,
      },
    },
    metadata: {
      postApprovalDocuments: {
        BIRTHCERTIFICATE: { dateOfBirth: "2025-08-03" },
      },
      postSubmit: "approve",
      quantity: 200,
    },
  },
};

export const TRNMBE2: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    reason: "Pregnancy/Maternity",
    label: "TRNMBE2",
    leave_dates: [parseISO("2025-06-14"), parseISO("2025-08-15")],
    has_continuous_leave_periods: true,
    employerResponse: {
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
    docs: {
      MASSID: {},
      PREGNANCY_MATERNITY_FORM: {
        expectedDateOfBirth: "leaveStart",
        postnatalRecoveryWeeks: 8,
        prenatalCareWeeks: 0,
      },
    },
    metadata: {
      postApprovalDocuments: {
        BIRTHCERTIFICATE: { dateOfBirth: "2025-08-18" },
      },
      postSubmit: "approve",
      quantity: 200,
    },
  },
};

export const TRNMBE3: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    reason: "Pregnancy/Maternity",
    label: "TRNMBE3",
    leave_dates: [parseISO("2025-06-14"), parseISO("2025-08-13")],
    has_continuous_leave_periods: true,
    employerResponse: {
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
    docs: {
      MASSID: {},
      PREGNANCY_MATERNITY_FORM: {
        expectedDateOfBirth: "leaveEnd",
        postnatalRecoveryWeeks: 0,
        prenatalCareWeeks: 8,
      },
    },
    metadata: {
      postApprovalDocuments: {
        BIRTHCERTIFICATE: { dateOfBirth: "2025-08-10" },
      },
      postSubmit: "approve",
      quantity: 200,
    },
  },
};

export const TRNMBE4: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    reason: "Pregnancy/Maternity",
    label: "TRNMBE4",
    leave_dates: [parseISO("2025-06-14"), parseISO("2025-08-03")],
    has_continuous_leave_periods: true,
    employerResponse: {
      fraud: "No",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
      employer_decision: "Approve",
    },
    docs: {
      MASSID: {},
      PREGNANCY_MATERNITY_FORM: {
        expectedDateOfBirth: "leaveStart",
        postnatalRecoveryWeeks: 8,
        prenatalCareWeeks: 0,
      },
    },
    metadata: {
      postApprovalDocuments: {
        BIRTHCERTIFICATE: { dateOfBirth: "2025-08-01" },
      },
      postSubmit: "approve",
      quantity: 200,
    },
  },
};
