/**
 * These are currently unused scenarios for generating claims used for training
 * environment refreshes.
 *
 * Scenarios are sometimes changed or retired, and occasionally brought back
 * from retirement.
 *
 * Historical scenarios may be retrieved from version control in this file or in
 * e2e/src/scenarios/training-refreshes.ts.
 */

import { parseISO } from "date-fns";

import { ScenarioSpecification } from "../generation/Scenario";

export const TRNOL2: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNOL2",
    reason: "Care for a Family Member",
    work_pattern_spec: "standard",
    leave_dates: [parseISO("2023-04-17"), parseISO("2023-08-18")],
    docs: {
      MASSID: {},
      CARING: {},
    },
    employerResponse: {
      fraud: "No",
      has_amendments: false,
      employer_decision: "Approve",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
    },
    metadata: {
      postSubmit: "adjudicate",
      quantity: 25,
    },
  },
};

export const TRNOL4: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNOL4",
    reason: "Pregnancy/Maternity",
    work_pattern_spec: "standard",
    leave_dates: [parseISO("2023-04-17"), parseISO("2023-08-18")],
    docs: {
      MASSID: {},
      PREGNANCY_MATERNITY_FORM: {},
    },
    employerResponse: {
      fraud: "No",
      has_amendments: false,
      employer_decision: "Approve",
      hours_worked_per_week: {
        hours_worked: 40,
        employer_changes: "Unchanged",
      },
    },
    metadata: {
      postSubmit: "adjudicate",
      quantity: 25,
    },
  },
};

export const TRNER5: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNER5",
    reason: "Serious Health Condition - Employee",
    work_pattern_spec: "standard",
    leave_dates: [parseISO("2024-04-15"), parseISO("2024-05-24")],
    docs: {
      MASSID: {},
      HCP: {},
    },
    is_withholding_tax: true,
    employerResponse: {
      fraud: "No",
      comment: "Approved; employee completed all steps with employer",
      employer_decision: "Approve",
      has_amendments: true,
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
      leave_reason: "An illness or injury",
      previous_leaves: [
        {
          employer_changes: "Added",
          leave_end_date: "2023-10-31",
          leave_reason: "An illness or injury",
          leave_start_date: "2023-09-17",
          type: "same_reason",
        },
      ],
    },
    metadata: {
      postSubmit: "adjudicate",
      quantity: 150,
    },
  },
};

export const TRNER6: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNER6",
    reason: "Care for a Family Member",
    work_pattern_spec: "standard",
    leave_dates: [parseISO("2024-04-15"), parseISO("2024-05-24")],
    docs: {
      MASSID: {},
      CARING: {},
    },
    is_withholding_tax: true,
    employerResponse: {
      believe_relationship_accurate: "Yes",
      employer_benefits: [
        {
          benefit_amount_dollars: 1500,
          benefit_amount_frequency: "Per Week",
          benefit_end_date: "2023-10-31",
          benefit_start_date: "2023-10-24",
          benefit_type: "Short-term disability insurance",
          employer_changes: "Added",
          is_full_salary_continuous: true,
        },
      ],
      employer_decision: "Approve",
      fraud: "No",
      leave_reason: "Caring for a family member with a serious health condtion",
      has_amendments: true,
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
    },
    metadata: {
      postSubmit: "approve",
      quantity: 150,
    },
  },
};

export const TRNER7: ScenarioSpecification = {
  employee: { mass_id: true, wages: "eligible" },
  claim: {
    label: "TRNER7",
    reason: "Child Bonding",
    reason_qualifier: "Newborn",
    bondingDate: parseISO("2023-05-11"),
    work_pattern_spec: "standard",
    leave_dates: [parseISO("2023-04-17"), parseISO("2023-06-23")],
    docs: {
      MASSID: {},
      BIRTHCERTIFICATE: {},
    },
    is_withholding_tax: true,
    employerResponse: {
      believe_relationship_accurate: "Yes",
      employer_decision: "Approve",
      fraud: "No",
      has_amendments: true,
      hours_worked_per_week: {
        employer_changes: "Amended",
        hours_worked: 36,
      },
      leave_reason:
        "Caring for a family member with a serious health condition",
    },
    metadata: {
      postSubmit: "approve",
      quantity: 150,
    },
  },
};
