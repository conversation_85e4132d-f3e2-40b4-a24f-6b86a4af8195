import { addDays, subYears } from "date-fns";
import fs from "fs/promises";
import path from "path";
import { collect } from "streaming-iterables";

import ClaimPool, { APIClaimSpec } from "../generation/Claim";
import dataDirectory, { DataDirectory } from "../generation/DataDirectory";
import EmployeePool, {
  Employee,
  EmployeeGenerationSpec,
} from "../generation/Employee";
import EmployerPool from "../generation/Employer";
import DOR from "../generation/writers/DOR";

// This script generates DOR files and claim data that can be used to create a
// set of employees with established entitlement periods in FINEOS. The
// variables below should be customized according to your needs.

/** Name of data directory to write to. */
const dataDirectoryName = "";
/** Count of employees to create. */
const employeeCount = 0;
/** Special details of employees. */
const employeeSpec: EmployeeGenerationSpec = {};
/** Count of employers to create. */
const employerCount = 0;
/** Date from which entitlement periods are created. */
const referenceDate = new Date();
/** Count of historical years to create data. */
const yearCount = 0;

main();

async function main() {
  const storage = dataDirectory(dataDirectoryName);
  await storage.prepare();

  const employerPool = await EmployerPool.load(
    storage.employers
  ).orGenerateAndSave(() => EmployerPool.generate(employerCount));

  const employeePool = await EmployeePool.load(
    storage.employees
  ).orGenerateAndSave(() =>
    EmployeePool.generate(employeeCount, employerPool, employeeSpec)
  );

  const employees = collect(employeePool);

  for (let index = 0; index < yearCount; ++index) {
    await ensureDataForYear(
      storage,
      subYears(referenceDate, index),
      employees,
      employerPool
    );
  }
}

async function ensureDataForYear(
  storage: DataDirectory,
  yearReferenceDate: Date,
  employees: Employee[],
  employerPool: EmployerPool
) {
  const dirPath = storage.join(yearReferenceDate.getFullYear().toString());
  const documentDirPath = path.join(dirPath, "documents");
  await fs.mkdir(documentDirPath, { recursive: true });
  const claimFilePath = path.join(dirPath, "claims.ndjson");

  try {
    await ClaimPool.load(claimFilePath, documentDirPath);
  } catch (exception) {
    if (exception?.code !== "ENOENT") {
      throw exception;
    }

    await createData(
      employees,
      yearReferenceDate,
      claimFilePath,
      documentDirPath,
      dirPath,
      storage,
      employerPool
    );
  }
}

async function createData(
  employees: Employee[],
  yearReferenceDate: Date,
  claimFilePath: string,
  documentDirPath: string,
  dirPath: string,
  storage: DataDirectory,
  employerPool: EmployerPool
) {
  const employeePool = new EmployeePool(employees);

  await ClaimPool.generate(
    employeePool,
    {},
    generateClaimSpec(yearReferenceDate),
    employees.length
  ).save(claimFilePath, documentDirPath);

  const employerDorFilePath = path.join(
    dirPath,
    path.basename(storage.dorFile("DORDFMLEMP"))
  );

  await DOR.writeEmployersFile(employerPool, employerDorFilePath);

  const employeeDorFilePath = path.join(
    dirPath,
    path.basename(storage.dorFile("DORDFML"))
  );

  await DOR.writeEmployeesFile(
    employerPool,
    employeePool,
    employeeDorFilePath,
    yearReferenceDate
  );

  // A brief pause prevents timestamp collisions in DOR file names.
  await resolveAfterTimeout(5000);
}

function generateClaimSpec(yearReferenceDate: Date): APIClaimSpec {
  return {
    docs: { HCP: {}, MASSID: {} },
    employerResponse: {
      employer_decision: "Approve",
      fraud: "No",
      has_amendments: false,
      hours_worked_per_week: {
        employer_changes: "Unchanged",
        hours_worked: 40,
      },
    },
    label: "ESTABLISH_ENTITLEMENT_PERIOD",
    leave_dates: [yearReferenceDate, addDays(yearReferenceDate, 3)],
    metadata: { postSubmit: "approve", useFineosSubmitter: true },
    reason: "Serious Health Condition - Employee",
  };
}

async function resolveAfterTimeout(milliseconds: number) {
  return new Promise<void>((resolve) => setTimeout(resolve, milliseconds));
}
