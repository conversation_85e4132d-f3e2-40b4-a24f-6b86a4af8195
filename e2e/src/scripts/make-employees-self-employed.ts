import { format, startOfWeek, subWeeks } from "date-fns";
import fs from "fs";
import path from "path";
import { Page } from "playwright-chromium";
import yargs from "yargs";

import { concurrencyWrapper as withConcurrency } from "../commands/dataset/util";
import { ClaimantPage, Fineos } from "../submission/fineos.pages";
import { waitForStablePage } from "../util/playwright";
import { random } from "../util/random";

main();

async function main() {
  try {
    await makeEmployeesSelfEmployed();
  } catch (exception) {
    console.error(exception);
  }
}

async function makeEmployeesSelfEmployed() {
  const config = getConfig();

  console.log(
    `Making employees in "${config.employeeFilePath}" self-employed…`
  );

  const employees = getEmployees(config.employeeFilePath);

  const requests: MakeEmployeeSelfEmployedRequest[] =
    config.employeesHaveJobEndDates
      ? createRequestsWithJobEndDates(employees)
      : createRequests(employees);

  const results: MakeEmployeeSelfEmployedResult[] = [];

  const makeSelfEmployedAndCollectResult = async (
    request: MakeEmployeeSelfEmployedRequest
  ) => {
    const result = await makeEmployeeSelfEmployed(
      request,
      config.isInDebugMode
    );

    results.push(result);
  };

  await withConcurrency(
    makeSelfEmployedAndCollectResult,
    requests,
    config.concurrency
  );

  writeResultFile(results);
}

function getConfig(): Config {
  const parsedArgv = parseArgv(process.argv);
  const isInDebugMode = parsedArgv.d;
  const concurrency = isInDebugMode ? 1 : parsedArgv.c;
  const employeeFilePath = path.resolve(parsedArgv._[0].toString());
  const employeesHaveJobEndDates = parsedArgv.e;

  return {
    concurrency,
    employeeFilePath,
    employeesHaveJobEndDates,
    isInDebugMode,
  };
}

interface Config {
  readonly concurrency: number;
  readonly employeeFilePath: string;
  readonly employeesHaveJobEndDates: boolean;
  readonly isInDebugMode: boolean;
}

function parseArgv(argv: readonly string[]) {
  return yargs(argv.slice(2))
    .alias("h", "help")
    .demandCommand(1, "Path to employees.json must be specified.")
    .help("h")
    .option("c", {
      alias: "concurrency",
      default: 1,
      description: "Number of employees to modify in parallel.",
      type: "number",
    })
    .option("d", {
      alias: "debug",
      default: false,
      description:
        "If the script should run in debug mode. Concurrency will be set to 1.",
      type: "boolean",
    })
    .option("e", {
      alias: "end-dates",
      default: false,
      description: "If some of the employees should have job end dates.",
      type: "boolean",
    })
    .usage(generateUsage())
    .version(false)
    .parseSync();
}

function generateUsage() {
  const fileName = path.basename(__filename);

  return `${fileName}

Usage:
  npx ts-node ${fileName} <employees.json>`;
}

function getEmployees(employeeFilePath: string): Employee[] {
  const readFileOptions = { encoding: "utf8" } as const;
  const employeeFileText = fs.readFileSync(employeeFilePath, readFileOptions);
  return JSON.parse(employeeFileText);
}

interface Employee {
  readonly ssn: string;
}

function createRequestsWithJobEndDates(employees: readonly Employee[]) {
  const requests = createRequests(employees);
  const mostRecentSunday = startOfWeek(new Date());

  return requests.map((request, index) =>
    addJobEndDate(request, index, mostRecentSunday)
  );
}

function createRequests(
  employees: readonly Employee[]
): MakeEmployeeSelfEmployedRequest[] {
  return employees.map((employee) => ({ employee }));
}

function addJobEndDate(
  request: MakeEmployeeSelfEmployedRequest,
  index: number,
  referenceDate: Date
): MakeEmployeeSelfEmployedRequest {
  switch (index % 3) {
    case 0: {
      const jobEndDate = createDateLessThan26WeeksAgo(referenceDate);
      return { ...request, jobEndDate };
    }
    case 1: {
      const jobEndDate = createDateMoreThan26WeeksAgo(referenceDate);
      return { ...request, jobEndDate };
    }
    case 2:
    default: {
      return { ...request, jobEndDate: null };
    }
  }
}

function createDateLessThan26WeeksAgo(referenceDate: Date) {
  const weeksAgo = Math.ceil(random() * 24);
  return subWeeks(referenceDate, weeksAgo);
}

function createDateMoreThan26WeeksAgo(referenceDate: Date) {
  const weeksAgo = Math.ceil(random() * 8) + 26;
  return subWeeks(referenceDate, weeksAgo);
}

async function makeEmployeeSelfEmployed(
  request: MakeEmployeeSelfEmployedRequest,
  debug: boolean
): Promise<MakeEmployeeSelfEmployedResult> {
  try {
    console.log(
      `Making employee with tax identifier "${request.employee.ssn}" self-employed…`
    );

    const editOccupationAndWaitForStability = async (page: Page) => {
      await editEmployeeOccupation(request, page);
      await waitForStablePage(page);
    };

    await Fineos.withBrowser(editOccupationAndWaitForStability, { debug });

    console.log(
      `Made employee with tax identifier "${request.employee.ssn}" self-employed.`
    );

    return { error: null, request };
  } catch (exception) {
    const errorHeader = `Failed to make employee with tax identifier "${request.employee.ssn}" self-employed:`;
    console.error(`${errorHeader}\n${exception}`);
    return { error: exception.toString(), request };
  }
}

interface MakeEmployeeSelfEmployedRequest {
  readonly employee: Employee;
  readonly jobEndDate?: Date | null;
}

interface MakeEmployeeSelfEmployedResult {
  readonly error: string | null;
  readonly request: MakeEmployeeSelfEmployedRequest;
}

async function editEmployeeOccupation(
  request: MakeEmployeeSelfEmployedRequest,
  page: Page
) {
  const claimantPage = await ClaimantPage.visit(page, request.employee.ssn);

  if (request.jobEndDate) {
    await claimantPage.editOccupation(request.jobEndDate);
  } else {
    await claimantPage.editOccupation();
  }
}

function writeResultFile(results: readonly MakeEmployeeSelfEmployedResult[]) {
  const filePath = getResultFilePath();
  const fileData = createResultFileData(results);
  fs.writeFileSync(filePath, fileData);
  console.log(`Results written to "${filePath}".`);
}

function getResultFilePath() {
  const timestamp = format(new Date(), "yyyyMMddhhmmss");
  const relativeResultFilePath = `./self-employed-employees-${timestamp}.csv`;
  return path.resolve(relativeResultFilePath);
}

function createResultFileData(
  results: readonly MakeEmployeeSelfEmployedResult[]
) {
  const header = "tax_identifier,job_end_date,error";
  const records = results.map(transformResultToRecord);
  const resultRows = [header, ...records];
  return resultRows.join("\n") + "\n";
}

function transformResultToRecord(result: MakeEmployeeSelfEmployedResult) {
  const error = result.error || "";

  const jobEndDate = result.request.jobEndDate
    ? format(result.request.jobEndDate, "yyyy-MM-dd")
    : "";

  const taxIdentifier = result.request.employee.ssn;
  return [taxIdentifier, jobEndDate, error].join(",");
}
