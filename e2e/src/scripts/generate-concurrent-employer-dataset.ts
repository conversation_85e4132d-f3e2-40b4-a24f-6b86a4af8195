/**
 * @file
 * Template for generating PFML test data specific to multiple employers for a single employee.
 */

import { format, isMatch, lastDayOfQuarter } from "date-fns";
import path from "path";
import yargs, { Arguments } from "yargs";

import dataDirectory from "../generation/DataDirectory";
import EmployeePool from "../generation/Employee";
import EmployerPool from "../generation/Employer";
import DOR from "../generation/writers/DOR";
import EmployeeIndex from "../generation/writers/EmployeeIndex";
import EmployerIndex from "../generation/writers/EmployerIndex";
import { quarters } from "../util/writers";

(async () => {
  const { d: date } = parseArgv(process.argv);

  const storage = dataDirectory(`${date}-multiple-employers-for-employee`);
  await storage.prepare();

  const employerPoolA = EmployerPool.generate(1);
  const employerPoolB = EmployerPool.generate(1);
  const employerPoolC = EmployerPool.generate(1);

  const firstEmployer = employerPoolA.pick();
  const secondEmployer = employerPoolB.pick();
  const thirdEmployer = employerPoolC.pick();

  // Part 1: Employer generation.
  const employerPool = await EmployerPool.load(
    storage.employers
  ).orGenerateAndSave(() => {
    return EmployerPool.merge(employerPoolA, employerPoolB, employerPoolC);
  });

  await EmployerIndex.write(employerPool, storage.dir + "/employers.csv");
  await DOR.writeEmployersFile(employerPool, storage.dorFile("DORDFMLEMP"));

  //TODO: write the DUA file

  const employeePool = await EmployeePool.load(
    storage.employees
  ).orGenerateAndSave(() => {
    const quartersFromReferenceDate = quarters(new Date(date), 7).map((date) =>
      format(date, "yyyy-MM-dd")
    );

    return EmployeePool.merge(
      // This data is used when testing the portal submission process
      EmployeePool.generateWithOccupations(
        400,
        {
          metadata: { concurrentEmploymentScenario: 0 },
        },
        [
          {
            employer: firstEmployer,
            wages: [
              {
                quarter: format(lastDayOfQuarter(new Date()), "yyyy-MM-dd"),
                wages: 10_000,
              },
            ],
          },
          {
            employer: secondEmployer,
            wages: [
              {
                quarter: format(lastDayOfQuarter(new Date()), "yyyy-MM-dd"),
                wages: 25_000,
              },
              {
                quarter: quartersFromReferenceDate[6],
                wages: 25_000,
              },
              {
                quarter: quartersFromReferenceDate[5],
                wages: 25_000,
              },
              {
                quarter: quartersFromReferenceDate[4],
                wages: 25_000,
              },
              {
                quarter: quartersFromReferenceDate[3],
                wages: 25_000,
              },
            ],
          },
        ]
      ),
      // Scenario 1: Claimant files from a new employer and they have an established base period
      EmployeePool.generateWithOccupations(
        400,
        {
          metadata: { concurrentEmploymentScenario: 1 },
        },
        [
          {
            employer: firstEmployer,
            wages: [
              { quarter: quartersFromReferenceDate[1], wages: 9500 },
              { quarter: quartersFromReferenceDate[2], wages: 10_500 },
              { quarter: quartersFromReferenceDate[3], wages: 9400 },
              { quarter: quartersFromReferenceDate[4], wages: 13_000 },
            ],
          },
          {
            employer: secondEmployer,
            wages: [
              { quarter: quartersFromReferenceDate[5], wages: 13_000 },
              { quarter: quartersFromReferenceDate[6], wages: 13_000 },
            ],
          },
        ]
      ),
      // Scenario 2: Claimant files from an employer they used to work for
      EmployeePool.generateWithOccupations(
        400,
        {
          wages: "eligible",
          metadata: { concurrentEmploymentScenario: 2 },
        },
        [
          {
            employer: firstEmployer,
            wages: [
              { quarter: quartersFromReferenceDate[3], wages: 10_000 },
              { quarter: quartersFromReferenceDate[4], wages: 9000 },
              { quarter: quartersFromReferenceDate[5], wages: 9500 },
              { quarter: quartersFromReferenceDate[6], wages: 6750 },
            ],
          },
          {
            employer: secondEmployer,
            wages: [{ quarter: quartersFromReferenceDate[5], wages: 5000 }],
          },
          {
            employer: thirdEmployer,
            wages: [{ quarter: quartersFromReferenceDate[2], wages: 9000 }],
          },
        ]
      ),
      // Scenario 3: Claimant has an established base period but files from a new employer while still working for their old employer
      EmployeePool.generateWithOccupations(
        400,
        {
          metadata: { concurrentEmploymentScenario: 3 },
        },
        [
          {
            employer: firstEmployer,
            wages: [
              { quarter: quartersFromReferenceDate[2], wages: 13_000 },
              { quarter: quartersFromReferenceDate[3], wages: 10_000 },
              { quarter: quartersFromReferenceDate[4], wages: 11_000 },
              { quarter: quartersFromReferenceDate[5], wages: 9500 },
              { quarter: quartersFromReferenceDate[6], wages: 10_000 },
            ],
          },
          {
            employer: secondEmployer,
            wages: [{ quarter: quartersFromReferenceDate[6], wages: 12_000 }],
          },
        ]
      ),
      // Scenario 4 (Control Case): Claimant has an established base period and files with the same employer
      EmployeePool.generateWithOccupations(
        400,
        {
          metadata: { concurrentEmploymentScenario: 4 },
        },
        [
          {
            employer: firstEmployer,
            wages: [
              { quarter: quartersFromReferenceDate[1], wages: 10_000 },
              { quarter: quartersFromReferenceDate[2], wages: 8500 },
              { quarter: quartersFromReferenceDate[3], wages: 9000 },
              { quarter: quartersFromReferenceDate[4], wages: 11_000 },
              { quarter: quartersFromReferenceDate[5], wages: 12_000 },
              { quarter: quartersFromReferenceDate[6], wages: 16_000 },
              {
                quarter: format(lastDayOfQuarter(new Date()), "yyyy-MM-dd"),
                wages: 15_500,
              },
            ],
          },
        ]
      ),
      // Scenario 5 (Control Case): Claimant files from an employer they worked concurrently with other employers in the base period
      EmployeePool.generateWithOccupations(
        400,
        {
          metadata: { concurrentEmploymentScenario: 5 },
        },
        [
          {
            employer: firstEmployer,
            wages: [
              { quarter: quartersFromReferenceDate[3], wages: 10_000 },
              { quarter: quartersFromReferenceDate[4], wages: 12_000 },
              { quarter: quartersFromReferenceDate[5], wages: 12_000 },
              { quarter: quartersFromReferenceDate[6], wages: 12_000 },
            ],
          },
          {
            employer: secondEmployer,
            wages: [{ quarter: quartersFromReferenceDate[5], wages: 2300 }],
          },
          {
            employer: thirdEmployer,
            wages: [
              { quarter: quartersFromReferenceDate[2], wages: 1750 },
              { quarter: quartersFromReferenceDate[4], wages: 2500 },
              { quarter: quartersFromReferenceDate[5], wages: 2500 },
              { quarter: quartersFromReferenceDate[6], wages: 2500 },
            ],
          },
        ]
      )
    );
  });

  // <!-- @default
  await DOR.writeEmployeesFile(
    employerPool,
    employeePool,
    storage.dorFile("DORDFML")
  );
  await EmployeeIndex.write(
    employeePool,
    path.join(storage.dir, "employees.csv")
  );

  await employerPool.save(
    storage.dir + `/../../employers/concurrent-employers-${date}.json`
  );

  await employeePool.save(
    storage.dir + `/../../employees/concurrent-employees-${date}.json`
  );
  // @default -->

  const used = process.memoryUsage().heapUsed / 1024 / 1024;
  console.log(
    `The script uses approximately ${Math.round(used * 100) / 100} MB`
  );
  // Catch and log any errors that bubble all the way up here.
})().catch((e) => {
  console.error(e);
  process.exit(1);
});

function parseArgv(argv: readonly string[]) {
  return yargs(argv.slice(2))
    .alias("h", "help")
    .help("help")
    .option("d", {
      alias: "date",
      demandOption: false,
      default: format(new Date(), "yyyy-MM-dd"),
      description: "Date used to determine the referenced calendar quarter",
      type: "string",
    })
    .check(validate)
    .usage("Usage: npx ts-node $0 [options]")
    .version(false)
    .parseSync();
}

function validate(argv: Arguments) {
  const date = argv.d as string;
  const pattern = "yyyy-MM-dd";

  if (!isMatch(date, pattern)) {
    throw new Error("The date must be a valid format: [yyyy-MM-dd]");
  }

  return true;
}
