import { format, lastDayOfQuarter } from "date-fns";
import path from "path";

import config from "../config";
import dataDirectory from "../generation/DataDirectory";
import EmployeePool from "../generation/Employee";
import EmployerPool from "../generation/Employer";
import DOR from "../generation/writers/DOR";
import EmployeeIndex from "../generation/writers/EmployeeIndex";
import EmployerIndex from "../generation/writers/EmployerIndex";
import InfraClient from "../InfraClient";
import { random } from "../util/random";
import { quarters } from "../util/writers";

(async () => {
  const todayFormatted = format(new Date(), "yyyy-MM-dd");
  const storage = dataDirectory(`${todayFormatted}-dua-dataset`);

  await storage.prepare();

  const currentStatExcludedEmployers = await statExcludedEmployers();
  const randomFeinIndex = Math.floor(
    random() * currentStatExcludedEmployers.length
  );
  const { fein, name } = currentStatExcludedEmployers[randomFeinIndex];

  const duaOnlyEmployerPool = EmployerPool.generate(1);
  const dorAndDuaEmployerPool = EmployerPool.generate(1);
  const dorOnlyEmployerPool = EmployerPool.generate(1);
  const duaStatExcludedEmployerPool = EmployerPool.generate(1, {
    fein: formatAsFEIN(fein),
    name,
    statutorily_excluded: true,
  });

  const duaOnlyEmployer = duaOnlyEmployerPool.pick();
  const dorAndDuaEmployer = dorAndDuaEmployerPool.pick();
  const dorOnlyEmployer = dorOnlyEmployerPool.pick();
  const duaStatExcludedEmployer = duaStatExcludedEmployerPool.pick();

  const duaEmployers = EmployerPool.merge(
    duaOnlyEmployerPool,
    duaStatExcludedEmployerPool
  );

  const dorEmployers = EmployerPool.merge(
    dorOnlyEmployerPool,
    dorAndDuaEmployerPool
  );

  const allEmployers = await EmployerPool.load(
    storage.employers
  ).orGenerateAndSave(() => {
    return EmployerPool.merge(duaEmployers, dorEmployers);
  });

  await EmployerIndex.write(allEmployers, storage.dir + "/employers.csv");
  await DOR.writeEmployersFile(dorEmployers, storage.dorFile("DORDFMLEMP"));

  const employeePool = await EmployeePool.load(
    storage.employees
  ).orGenerateAndSave(() => {
    const quartersFromCurrentDate = quarters(new Date(), 7).map((date) =>
      format(date, "yyyy-MM-dd")
    );
    return EmployeePool.merge(
      EmployeePool.generateWithOccupations(
        100,
        {
          metadata: {
            duaTestScenario: 3,
          },
        },
        [
          {
            employer: dorOnlyEmployer,
            wages: [
              {
                quarter: format(lastDayOfQuarter(new Date()), "yyyy-MM-dd"),
                wages: 5000,
                source: "dfml",
              },
            ],
          },
          {
            employer: duaStatExcludedEmployer,
            wages: [
              {
                quarter: format(
                  new Date(quartersFromCurrentDate[5]),
                  "yyyy-MM-dd"
                ),
                wages: 9000,
                source: "ui",
              },
            ],
          },
        ]
      ),
      EmployeePool.generateWithOccupations(
        100,
        {
          metadata: {
            duaTestScenario: 1,
          },
        },
        [
          {
            employer: duaOnlyEmployer,
            wages: [
              {
                quarter: format(lastDayOfQuarter(new Date()), "yyyy-MM-dd"),
                wages: 10_000,
                source: "ui",
              },
            ],
          },
        ]
      ),
      EmployeePool.generateWithOccupations(
        100,
        {
          metadata: {
            duaTestScenario: 4,
          },
        },
        [
          {
            employer: dorAndDuaEmployer,
            wages: [
              {
                quarter: format(lastDayOfQuarter(new Date()), "yyyy-MM-dd"),
                wages: 8000,
                source: "dfml",
              },
              {
                quarter: format(lastDayOfQuarter(new Date()), "yyyy-MM-dd"),
                wages: 7000,
                source: "ui",
              },
            ],
          },
        ]
      )
    );
  });

  await DOR.writeEmployeesFile(
    dorEmployers,
    employeePool,
    storage.dorFile("DORDFML"),
    new Date(),
    {
      skipOrphanedOccupations: true,
    }
  );

  await EmployeeIndex.write(
    employeePool,
    path.join(storage.dir, "employees.csv")
  );

  await DOR.writeDuaWageFile(
    duaEmployers,
    employeePool,
    storage.dorFile("DORWAGE") + ".txt"
  );

  await DOR.writeStatutoryExclusionFile(
    duaStatExcludedEmployerPool,
    storage.dorFile("STATUTORILY_EXCLUDED_EMPLOYERS") + ".csv"
  );

  await allEmployers.save(
    storage.dir + `/../../employers/dua-employers-${todayFormatted}.json`
  );

  await employeePool.save(
    storage.dir + `/../../employees/dua-employees-${todayFormatted}.json`
  );

  const used = process.memoryUsage().heapUsed / 1024 / 1024;
  console.log(
    `The script uses approximately ${Math.round(used * 100) / 100} MB`
  );
})().catch((e) => {
  console.error(e);
  process.exit(1);
});

type StatExcludedEmployer = {
  fein: string;
  name: string;
};

async function statExcludedEmployers(): Promise<StatExcludedEmployer[]> {
  const infra = InfraClient.create(config);
  const rdsQuery = `
     WITH
      stat_excluded_employers AS (
        select
          employer_fein,
          description
        FROM
          statutorily_excluded_employer see
        LIMIT
          10
      )
      select
          json_agg (
              json_build_object ('fein', employer_fein, 'name', description)
          )
      from stat_excluded_employers
  `;
  const response = await infra.sendAndWaitForSSMShellCommand(
    infra.buildRdsCommand(rdsQuery, ["-t", "-A"]),
    {
      retryInterval: 30,
      retries: 5,
    }
  );

  const output = response.StandardOutputContent?.trim();

  if (!output) {
    throw new Error("Failed to retrieve stat excluded employers");
  }

  return JSON.parse(output);
}

function formatAsFEIN(identifier: string) {
  const strippedIdentifier = identifier.replace(/-/g, "");
  const segmentA = strippedIdentifier.slice(0, 2);
  const segmentB = strippedIdentifier.slice(2);
  return `${segmentA}-${segmentB}`;
}
