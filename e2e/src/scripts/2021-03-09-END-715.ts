import stringify from "csv-stringify";
import fs from "fs";
import path from "path";
import { chain } from "stream-chain";
import { map, pipeline, writeToStream } from "streaming-iterables";

import dataDirectory from "../generation/DataDirectory";
import EmployeePool from "../generation/Employee";
import EmployerPool, { Employer } from "../generation/Employer";
import DOR from "../generation/writers/DOR";
import EmployeeIndex from "../generation/writers/EmployeeIndex";

/**
 * This is a data generation script.
 *
 * One important part of this script is that it is "idempotent" - if you run it multiple times, nothing bad happens.
 * Since we check to see if the file exists before creating it, we will be able to rerun this multiple times, and it
 * won't overwrite existing data.
 */
(async () => {
  const storage = dataDirectory("END-715-2");
  await storage.prepare();

  let employerPool: EmployerPool;
  let employeePool: EmployeePool;

  // Generate a pool of employers.
  try {
    employerPool = await EmployerPool.load(storage.employers);
  } catch (e) {
    if (e.code !== "ENOENT") {
      throw e;
    }
    employerPool = EmployerPool.merge(
      EmployerPool.generate(10, { size: "small" }),
      EmployerPool.generate(5, {
        size: "small",
        withholdings: [null, null, null, 0],
      }),
      EmployerPool.generate(5, {
        size: "small",
        withholdings: [0, 0, 0, 0],
      })
    );

    await employerPool.save(storage.employers);
    await DOR.writeEmployersFile(employerPool, storage.dorFile("DORDFMLEMP"));

    const buildEmployerIndexLine = (
      employer: Employer
    ): Record<string, string | number> => {
      if (!employer.withholdings) {
        throw new Error("No withholdings found for this employer");
      }

      const withholdingDates = Object.keys(employer.withholdings).sort(
        (a, b) => new Date(a).getTime() - new Date(b).getTime()
      );

      const withholdingsArray = withholdingDates.map(
        (date) => employer.withholdings[date]
      );

      return {
        fein: employer.fein,
        name: employer.name,
        q1: withholdingsArray[0],
        q2: withholdingsArray[1],
        q3: withholdingsArray[2],
        q4: withholdingsArray[3],
      };
    };

    const employerIndexStream = chain([
      stringify({
        header: true,
        columns: {
          fein: "FEIN",
          name: "Name",
          q1: "2020-03-31 Withholdings",
          q2: "2020-06-30 Withholdings",
          q3: "2020-09-30 Withholdings",
          q4: "2020-12-31 Withholdings",
        },
      }),
      fs.createWriteStream(storage.dir + "/employers.csv"),
    ]);

    await pipeline(
      () => employerPool,
      map(buildEmployerIndexLine),
      writeToStream(employerIndexStream)
    );
  }

  // Generate a pool of employees.
  try {
    employeePool = await EmployeePool.load(storage.employees);
  } catch (e) {
    if (e.code !== "ENOENT") {
      throw e;
    }
    // Define the kinds of employees we need to support. Each type of employee is generated as its own pool,
    // then we merge them all together.
    employeePool = EmployeePool.merge(
      EmployeePool.generate(150, employerPool, {
        wages: "ineligible",
      }),
      EmployeePool.generate(150, employerPool, {
        wages: 30_000,
      }),
      EmployeePool.generate(150, employerPool, {
        wages: 60_000,
      }),
      EmployeePool.generate(150, employerPool, {
        wages: 90_000,
      })
    );

    await employeePool.save(storage.employees);
    await DOR.writeEmployeesFile(
      employerPool,
      employeePool,
      storage.dorFile("DORDFML")
    );
    await EmployeeIndex.write(
      employeePool,
      path.join(storage.dir, "employees.csv")
    );
  }

  const used = process.memoryUsage().heapUsed / 1024 / 1024;
  console.log(
    `The script uses approximately ${Math.round(used * 100) / 100} MB`
  );

  //Make sure to catch and log any errors that bubble all the way up here.
})().catch((e) => {
  console.error(e);
  process.exit(1);
});
