/**
 * @file
 * Serves as a quick way to test interactions with e2e/src/submission.
 *
 * By default, claims are submitted using the standard E2E dataset as defined in e2e/config.json.
 */
import yargs from "yargs";

import { Scenarios } from "../types";
import {
  dispatchPostSubmit,
  generateClaims,
  submitClaim,
} from "../util/claimGenerator";
import { assertValidClaim } from "../util/typeUtils";

const argv = yargs(process.argv.slice(2))
  .usage("Usage: npx ts-node $0 [options]")
  .alias("h", "help")
  .help("help")
  .option("s", {
    alias: "scenarioID",
    demandOption: true,
    description: "Scenario ID to generate claims for",
    type: "string",
  })
  .option("c", {
    alias: "count",
    default: 1,
    description: "Number of claims to create",
    type: "number",
  })
  .option("e", {
    alias: "employee-pool",
    description: "Employee pool file name (defaults to environment default)",
    type: "string",
  })
  .version(false)
  .parseSync();

async function generateAndSubmitClaims(
  scenarioID: Scenarios,
  count: number,
  employeePoolFile?: string
) {
  const claims = await generateClaims(scenarioID, count, employeePoolFile);
  for (const claim of claims) {
    if (!claim.metadata) {
      claim.metadata = {};
    }
    const res = await submitClaim(claim);
    assertValidClaim(claim.claim);
    await dispatchPostSubmit(claim, res);
    console.log(`Claim submitted: ${res.fineos_absence_id}`);
  }
}

// Run script with CLI args
(async () => {
  await generateAndSubmitClaims(argv.s as Scenarios, argv.c, argv.e);
})();
