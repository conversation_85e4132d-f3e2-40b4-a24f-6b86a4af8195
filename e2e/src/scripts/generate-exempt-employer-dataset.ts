import { format } from "date-fns";
import path from "path";

import dataDirectory from "../generation/DataDirectory";
import EmployeePool from "../generation/Employee";
import EmployerPool from "../generation/Employer";
import DOR from "../generation/writers/DOR";
import EmployeeIndex from "../generation/writers/EmployeeIndex";
import EmployerIndex from "../generation/writers/EmployerIndex";

//TODO add 50-100 employees

(async () => {
  const today = format(new Date(), "yyyy-MM-dd");
  const storage = dataDirectory(`${today}-e2e`);
  await storage.prepare();

  // Generate 1 employer that is exempt for use in new_test
  const employerPool = EmployerPool.generate(1, {
    size: "small",
    //Todo: fields exemption commence and cease date exist, are they necessary here?
    family_exemption: true,
    medical_exemption: true,
    metadata: {
      register_leave_admins: true,
    },
  });

  // Save the employer pool to JSON (employers.json)
  await employerPool.save(storage.employers);
  // Write an employer DOR file.
  await DOR.writeEmployersFile(employerPool, storage.dorFile("DORDFMLEMP"));
  // Write an employer "index" file for human consumption.
  await EmployerIndex.write(
    employerPool,
    storage.dir + "/employers-exempt.csv"
  );

  // Generate 100 Employees

  const employeePool = EmployeePool.generate(100, employerPool, {
    wages: "eligible",
    mass_id: false,
  });

  await employeePool.save(storage.employees);

  await DOR.writeEmployeesFile(
    employerPool,
    employeePool,
    storage.dorFile("DORDFML")
  );

  await EmployeeIndex.write(
    employeePool,
    path.join(storage.dir, "employees-for-exempt-employer.csv")
  );

  // Additionally save the JSON files to the employers/employees directory at the top level.
  await employeePool.save(`employees/e2e-${today}-for-exempt-employer.json`);
  await employerPool.save(`employers/e2e-${today}-exempt.json`);

  const used = process.memoryUsage().heapUsed / 1024 / 1024;
  console.log(
    `The script uses approximately ${Math.round(used * 100) / 100} MB`
  );
  // Catch and log any errors that bubble all the way up here.
})().catch((e) => {
  console.error(e);
  process.exit(1);
});
