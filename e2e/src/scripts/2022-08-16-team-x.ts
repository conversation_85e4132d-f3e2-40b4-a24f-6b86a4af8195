import { format } from "date-fns";

import dataDirectory from "../generation/DataDirectory";
import EmployeePool from "../generation/Employee";
import EmployerPool from "../generation/Employer";
import DOR from "../generation/writers/DOR";
import EmployeeIndex from "../generation/writers/EmployeeIndex";
import EmployerIndex from "../generation/writers/EmployerIndex";

(async () => {
  const date = format(new Date(), "yyyy-MM-dd");

  // Prepare a "data directory" to save the generated data to disk.
  const storage = dataDirectory(`team-x-${date}`);
  await storage.prepare();

  const exemptionCommenceDate = new Date("2022-01-01");
  const exemptionCeaseDate = new Date("2022-12-31");

  // Generate various exempt scenarios since we're working on service agreements.
  const notExempt = EmployerPool.generate(50, {
    medical_exemption: false,
    family_exemption: false,
    metadata: { has_employees: true, register_leave_admins: true },
  });
  const medicalTrue = EmployerPool.generate(25, {
    medical_exemption: true,
    family_exemption: false,
    exemption_commence_date: exemptionCommenceDate,
    exemption_cease_date: exemptionCeaseDate,
    metadata: { has_employees: true, register_leave_admins: true },
  });
  const familyTrue = EmployerPool.generate(25, {
    medical_exemption: false,
    family_exemption: true,
    exemption_commence_date: exemptionCommenceDate,
    exemption_cease_date: exemptionCeaseDate,
    metadata: { has_employees: true, register_leave_admins: true },
  });
  const fullyExempt = EmployerPool.generate(25, {
    medical_exemption: true,
    family_exemption: true,
    exemption_commence_date: exemptionCommenceDate,
    exemption_cease_date: exemptionCeaseDate,
    metadata: { has_employees: true, register_leave_admins: true },
  });

  const employerPool = EmployerPool.merge(
    notExempt,
    medicalTrue,
    familyTrue,
    fullyExempt
  );

  // Save the employer pool to JSON (employers.json)
  await employerPool.save(storage.employers);
  // Write an employer DOR file.
  await DOR.writeEmployersFile(employerPool, storage.dorFile("DORDFMLEMP"));
  // Write an employer "index" file for human consumption.
  await EmployerIndex.write(employerPool, storage.dir + "/employers.csv");

  // At the moment we don't care about employees that much. Create a set of financially eligible employees.
  const employeePool = EmployeePool.generate(500, employerPool, {
    wages: 60_000,
  });

  // Write the employee pool to disk (employees.json).
  await employeePool.save(storage.employees);
  // Write an employees DOR file to disk.
  await DOR.writeEmployeesFile(
    employerPool,
    employeePool,
    storage.dorFile("DORDFML")
  );
  // Write an employee "index" file for human consumption.
  await EmployeeIndex.write(employeePool, storage.dir + "/employees.csv");

  // Additionally save the JSON files to the employers/employees directory at the top level.
  await employeePool.save(`employees/team-x-${date}.json`);
  await employerPool.save(`employers/team-x-${date}.json`);
})().catch((e) => {
  console.error(e);
  process.exit(1);
});
