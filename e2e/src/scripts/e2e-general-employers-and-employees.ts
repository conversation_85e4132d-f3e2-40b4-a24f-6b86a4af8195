/**
 * Generates a dataset (ee/er only) for general e2e usage in all environments.
 *
 * Mostly copied from the 2023-12-11-e2e-employees script,
 * removing employees with child support obligations and adding
 * 5000 extra employees across the other sub-groups.
 */

import { format } from "date-fns";
import path from "path";

import dataDirectory from "../generation/DataDirectory";
import EmployeePool from "../generation/Employee";
import EmployerPool from "../generation/Employer";
import DOR from "../generation/writers/DOR";
import EmployeeIndex from "../generation/writers/EmployeeIndex";
import EmployerIndex from "../generation/writers/EmployerIndex";

(async () => {
  const today = format(new Date(), "yyyy-MM-dd");
  const storage = dataDirectory(`${today}-e2e`);
  await storage.prepare();

  // Generate 12 employers that will have employees assigned to them.
  const withEmployees = EmployerPool.generate(12, {
    size: "small",
    metadata: { has_employees: true },
  });

  // Generate 1 employer with 0 withholdings for use in LA verification.
  const noWithholdings = EmployerPool.generate(1, {
    size: "small",
    withholdings: [0, 0, 0, 0],
    metadata: { register_leave_admins: true },
  });

  // Generate 2 employers with withholdings for use in LA verification.
  const withholdings = EmployerPool.generate(2, {
    size: "small",
    metadata: { register_leave_admins: true },
  });

  const employerPool = EmployerPool.merge(
    withEmployees,
    noWithholdings,
    withholdings
  );

  // Save the employer pool to JSON (employers.json)
  await employerPool.save(storage.employers);
  // Write an employer DOR file.
  await DOR.writeEmployersFile(employerPool, storage.dorFile("DORDFMLEMP"));
  // Write an employer "index" file for human consumption.
  await EmployerIndex.write(employerPool, storage.dir + "/employers.csv");

  // Total employees = 28,500
  const employeePool = EmployeePool.merge(
    EmployeePool.generate(6367, withEmployees, {
      wages: "ineligible",
    }),
    EmployeePool.generate(606, withEmployees, {
      wages: "eligible",
      mass_id: false,
    }),
    EmployeePool.generate(6367, withEmployees, { wages: 30_000 }),
    EmployeePool.generate(6367, withEmployees, { wages: 60_000 }),
    EmployeePool.generate(6367, withEmployees, { wages: 90_000 }),
    EmployeePool.generate(2426, withEmployees, { wages: 0 })
  );

  await employeePool.save(storage.employees);

  await DOR.writeEmployeesFile(
    employerPool,
    employeePool,
    storage.dorFile("DORDFML")
  );

  await EmployeeIndex.write(
    employeePool,
    path.join(storage.dir, "employees.csv")
  );

  // Additionally save the JSON files to the employers/employees directory at the top level.
  await employeePool.save(`employees/e2e-${today}.json`);
  await employerPool.save(`employers/e2e-${today}.json`);

  const used = process.memoryUsage().heapUsed / 1024 / 1024;
  console.log(
    `The script uses approximately ${Math.round(used * 100) / 100} MB`
  );
  // Catch and log any errors that bubble all the way up here.
})().catch((e) => {
  console.error(e);
  process.exit(1);
});
