import { format } from "date-fns";
import path from "path";

import dataDirectory from "../generation/DataDirectory";
import EmployeePool from "../generation/Employee";
import EmployerPool from "../generation/Employer";
import DOR from "../generation/writers/DOR";
import EmployeeIndex from "../generation/writers/EmployeeIndex";
import EmployerIndex from "../generation/writers/EmployerIndex";

(async () => {
  const todayFormatted = format(new Date(), "yyyy-MM-dd");

  // @note: This script functions similarly to the dataGen.tmpl.ts script,
  // meaning that it will produce no new data if the folder already exists.
  // If you need to create multiple data sets on the same day, update the folder name.
  const storage = dataDirectory(`cps-${todayFormatted}`);
  await storage.prepare();

  const employerPool = await EmployerPool.load(
    storage.employers
  ).orGenerateAndSave(() => {
    return EmployerPool.generate(8, { size: "medium" });
  });

  await EmployerIndex.write(employerPool, storage.dir + "/employers.csv");
  await DOR.writeEmployersFile(employerPool, storage.dorFile("DORDFMLEMP"));

  const employeePool = await EmployeePool.load(
    storage.employees
  ).orGenerateAndSave(() => {
    return EmployeePool.merge(
      EmployeePool.generate(200, employerPool, { wages: "ineligible" }),
      EmployeePool.generate(3800, employerPool, { wages: "eligible" }),
      EmployeePool.generate(2000, employerPool, { wages: 30_000 }),
      EmployeePool.generate(2000, employerPool, { wages: 60_000 }),
      EmployeePool.generate(2000, employerPool, { wages: 90_000 })
    );
  });

  await DOR.writeEmployeesFile(
    employerPool,
    employeePool,
    storage.dorFile("DORDFML")
  );
  await EmployeeIndex.write(
    employeePool,
    path.join(storage.dir, "employees.csv")
  );

  // Save generated files under employees and employers directories
  // @note: The same warning regarding name collision for the storage variable applies here as well.
  await employerPool.save(
    storage.dir + `/../../employers/cps-${todayFormatted}.json`
  );
  await employeePool.save(
    storage.dir + `/../../employees/cps-${todayFormatted}.json`
  );

  const used = process.memoryUsage().heapUsed / 1024 / 1024;
  console.log(
    `The script uses approximately ${Math.round(used * 100) / 100} MB`
  );
  // Catch and log any errors that bubble all the way up here.
})().catch((e) => {
  console.error(e);
  process.exit(1);
});
