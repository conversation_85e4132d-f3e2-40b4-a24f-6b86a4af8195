import { format } from "date-fns";
import path from "path";

import dataDirectory from "../generation/DataDirectory";
import EmployeePool from "../generation/Employee";
import EmployerPool from "../generation/Employer";
import DOR from "../generation/writers/DOR";
import EmployeeIndex from "../generation/writers/EmployeeIndex";
import EmployerIndex from "../generation/writers/EmployerIndex";

/**
 * @summary
 *
 * This script is used to create a new LST data set when the current one ages out of use.
 */
(async () => {
  const today = new Date();
  const outputFile = `lst-${format(today, "yyyy-MM-dd")}`;

  const storage = dataDirectory(outputFile);

  await storage.prepare();

  const employerPool = EmployerPool.generate(3);
  await employerPool.save(storage.employers);
  await EmployerIndex.write(employerPool, storage.dir + "/employers.csv");
  await DOR.writeEmployersFile(employerPool, storage.dorFile("DORDFMLEMP"));

  const employeePool = EmployeePool.merge(
    EmployeePool.generate(15_000, employerPool, {
      wages: "eligible",
      mass_id: true,
    })
  );
  await employeePool.save(storage.employees);
  await DOR.writeEmployeesFile(
    employerPool,
    employeePool,
    storage.dorFile("DORDFML")
  );
  await EmployeeIndex.write(
    employeePool,
    path.join(storage.dir, "employees.csv")
  );

  // Additionally save the JSON files to the employers/employees directory at the top level.
  await employeePool.save(`employees/${outputFile}.json`);
  await employerPool.save(`employers/${outputFile}.json`);

  const used = process.memoryUsage().heapUsed / 1024 / 1024;
  console.log(
    `The script uses approximately ${Math.round(used * 100) / 100} MB`
  );
  // Catch and log any errors that bubble all the way up here.
})().catch((e) => {
  console.error(e);
  process.exit(1);
});
