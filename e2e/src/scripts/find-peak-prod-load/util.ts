import { addDays, format, isWeekend } from "date-fns";
import { collect, transform } from "streaming-iterables";

import NewRelicClient from "../../NewRelicClient";
import { Environment } from "../../types";
import {
  DailyRequests,
  RequestData,
  RequestsForPeriod,
  TrackingOptions,
  UserInput,
} from "./types";

const MILLIS_PER_SECOND = 1000;

/**
 * Pulls traffic data from New Relic, sorted chronologically.
 */
export async function scrapeNewRelic(input: UserInput, client: NewRelicClient) {
  const options = toTrackingOptions(input);

  const [startDate, endDate] = options.period;

  const allRequests: string[] = [];

  for (
    let referenceDate = startDate;
    referenceDate <= endDate;
    referenceDate = addDays(referenceDate, 1)
  ) {
    if (!options.includeWeekends && isWeekend(referenceDate)) {
      continue;
    }

    allRequests.push(...requestsForDay(options, referenceDate));
  }

  const iterator = transform<string, RequestData[]>(
    input.concurrency,
    (request) => client.nrql<RequestData>(request)
  );

  const results = await collect(iterator(allRequests));

  return results
    .sort(([a], [b]) => a.beginTimeSeconds - b.beginTimeSeconds)
    .flat();
}

/**
 * Used to cleanly separate the results of `scrapeNewRelic` into labelled per-day results.
 * @param data sorted data points to be categorized
 * @returns Mapping of yyyy-MM-dd formatted dates to the corresponding traffic results from `data`
 */
export function mapToDay(data: RequestsForPeriod) {
  const mapping: DailyRequests = {};

  for (const point of data) {
    const key = format(
      point.beginTimeSeconds * MILLIS_PER_SECOND,
      "yyyy-MM-dd"
    );

    if (mapping[key]) {
      mapping[key].push(point);
    } else {
      mapping[key] = [point];
    }
  }

  return mapping;
}

function toTrackingOptions(input: UserInput): TrackingOptions {
  return {
    dailyHourRange: [input.hourDayStarts, input.hourDayEnds],
    environment: input.environment as Environment,
    includeWeekends: input.includeWeekends,
    period: [new Date(input.startDate), new Date(input.endDate)],
    urlRule: input.urlRuleWildcard,
  };
}

function requestsForDay(options: TrackingOptions, day: Date) {
  const windows = computeWindowsForRange(options.dailyHourRange);

  const formattedDate = format(day, "yyyy-MM-dd");

  return windows
    .filter(([s, e]) => s !== e)
    .map(([start, end]) => {
      const windowStart = start.toString().padStart(2, "0");
      const windowEnd = end.toString().padStart(2, "0");
      return `SELECT uniqueCount(request_id) AS 'uniqueRequests'
      FROM Log
        WHERE aws.logGroup = 'service/pfml-api-${options.environment}'
          AND request.path LIKE '%${options.urlRule}%'
        SINCE '${formattedDate} ${windowStart}:00:00'
        UNTIL '${formattedDate} ${windowEnd}:59:59'
      TIMESERIES 1 minute`;
    });
}

function computeWindowsForRange(hours: [number, number], windowSize = 6) {
  const [start, end] = hours;

  let idx = end;

  const periods = [];
  while (idx > start + windowSize) {
    periods.push([idx - windowSize + 1, idx]);
    idx -= windowSize;
  }

  periods.push([start, idx]);
  return periods.reverse();
}
