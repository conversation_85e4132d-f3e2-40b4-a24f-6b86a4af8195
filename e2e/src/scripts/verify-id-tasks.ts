/**
 * Verifies ID tasks for scenarios involving RMV auto-approval (closedScenarios),
 * as well as those that use Out of State IDs that should not auto-approve (openScenarios).
 *
 * Outputs 'failed-id-task-verification.csv' in the directory specified by DIRECTORY_NAME.
 *
 * Determining open and closed scenarios:
 *  These quotations correspond to text in scenario refresh spreadsheet(s)
 *  They are arbitrary and may change or be inconsistent
 *
 *  closed-scenarios: "ID doc and task (RMV auto approve)" -- or "auto-approval"
 *      Ideally for closed scenarios there should not be an "ID Review Task" created with the auto-approval.
        The ID Verification this task is often still there, if there are not enough claims without the ID Review task 
        the training team is ok with using those claims as long as the ID Review task status is (Closed) NOT (Open)
 *
 *  open-scenarios: "Use Out of State ID. ID Proofing task should not be automatically closed."
 * 
 *  
 *
 */

import csv from "csv-parser";
import fs from "fs";
import fsp from "fs/promises";
import path from "path";
import { Page } from "playwright-chromium";
import yargs from "yargs";

import { concurrencyWrapper } from "../commands/dataset/util";
import { ClaimPage, Fineos } from "../submission/fineos.pages";

const argv = yargs(process.argv.slice(2))
  .alias("h", "help")
  .help("help")
  .option("c", {
    alias: "concurrency",
    default: 1,
    demandOption: true,
    description:
      "Number of streaming-iterables to simultaneously run - not true concurrency.",
    type: "number",
  })
  .option("d", {
    alias: "directory-name",
    demandOption: true,
    description: "Folder containing submitted.csv",
    type: "string",
  })
  .option("cs", {
    alias: "closed-scenarios",
    default: [],
    describe:
      "A list of scenarios where the RMV auto approve task should automatically close.",
    string: true,
    type: "array",
  })
  .option("os", {
    alias: "open-scenarios",
    default: [],
    describe:
      "A list of scenarios where the RMV auto approve task should remain open.",
    string: true,
    type: "array",
  })
  .version(false)
  .parseSync();

const CONCURRENCY = argv.c;
const DIRECTORY_NAME = argv.d;
const CLOSED_SCENARIOS: string[] = argv.cs;
const OPEN_SCENARIOS: string[] = argv.os;

const filePath = path.join(
  __dirname,
  "../../data",
  DIRECTORY_NAME,
  "submitted.csv"
);

enum CsvHeader {
  Scenario = "Scenario",
  FineosID = "Fineos ID",
  IDReviewTaskStatus = "ID Review Task Status",
}

const failures: CsvRow[] = [];
const errored: CsvRow[] = [];

async function main() {
  let filteredValues;
  try {
    filteredValues = await filterCSV(filePath, [
      ...CLOSED_SCENARIOS,
      ...OPEN_SCENARIOS,
    ]);
  } catch (e) {
    console.error(e);
    process.exit(1);
  }

  if (!filteredValues) {
    console.log("No claims were found that match provided scenarios.");
    process.exit(1);
  }

  const filteredClaims = filteredValues.filter(
    (claim) => claim["Fineos ID"] !== ""
  );

  const total = filteredClaims.length;

  let verified = 0;
  let errors = 0;

  await concurrencyWrapper(
    async (claim) => {
      try {
        await Fineos.withBrowser(
          async (page): Promise<void> => {
            const passed = await verifyIdTasks(page, claim);
            if (!passed) {
              console.info("Failure: ", claim["Fineos ID"]);
              failures.push(claim);
            }

            verified++;
            console.info(
              `Verified ${verified} of ${total} claims with ${errors} errors (${
                (errors / verified) * 100
              }%).`
            );
          },
          {
            debug: false,
            //slowMo: 1000,
          }
        );
      } catch (e) {
        console.log(e);
        errored.push(claim);
        errors++;
      }
    },
    filteredClaims,
    CONCURRENCY
  );

  await maybeWriteFile("failed", failures);
  await maybeWriteFile("errored", errored);
}

interface CsvRow {
  [CsvHeader.Scenario]: string;
  [CsvHeader.FineosID]: string;
  [CsvHeader.IDReviewTaskStatus]?: string;
}

function filterCSV(
  filePath: string,
  scenarioValues: string[]
): Promise<CsvRow[]> {
  return new Promise((resolve, reject) => {
    const results: CsvRow[] = [];
    fs.createReadStream(filePath)
      .pipe(csv())
      .on("data", (data: Record<string, string>) => {
        if (scenarioValues.includes(data[CsvHeader.Scenario])) {
          results.push({
            [CsvHeader.Scenario]: data[CsvHeader.Scenario],
            [CsvHeader.FineosID]: data[CsvHeader.FineosID],
          });
        }
      })
      .on("end", () => resolve(results))
      .on("error", (err) => reject(err));
  });
}

async function maybeWriteFile(status: string, collection: CsvRow[]) {
  if (collection.length === 0) {
    return;
  }

  const fileName = `${status}-id-task-verification.csv`;

  const headers = [
    CsvHeader.Scenario,
    CsvHeader.FineosID,
    CsvHeader.IDReviewTaskStatus,
  ];

  const rows = collection.map((r) => headers.map((h) => r[h] || "").join(","));

  await fsp.writeFile(
    path.join(__dirname, "../../data", DIRECTORY_NAME, fileName),
    [headers.join(","), ...rows].join("\r\n")
  );

  console.info(`Created ${fileName}`);
}

const verifyIdTasks = async (page: Page, claim: CsvRow): Promise<boolean> => {
  console.log(
    `Verifying id tasks for ${claim["Fineos ID"]}, scenario ${claim["Scenario"]}`
  );

  const claimPage = await ClaimPage.visit(page, claim["Fineos ID"]);
  let verified = false;

  await claimPage.tasks(async (tasks) => {
    try {
      if (OPEN_SCENARIOS.includes(claim[CsvHeader.Scenario])) {
        await tasks.assertTaskExists("ID Review");
        await tasks.assertTaskStatus("Get ID Proof [AUTO]", [
          "Cancelled",
          "Closed",
        ]);
      } else {
        let idReviewExists = false;
        try {
          await tasks.assertDoesNotExist("ID Review");
        } catch {
          idReviewExists = true;
        }
        if (idReviewExists) {
          const status = await tasks.getTaskStatus("ID Review");
          claim[CsvHeader.IDReviewTaskStatus] = `ID Review (${status})`;
          return;
        }
      }

      verified = true;
    } catch (e) {
      console.error(e);
      verified = false;
    }
  });

  return verified;
};

main();
