import { format } from "date-fns";
import * as fs from "fs";
import path from "path";
import { pipeline } from "stream";
import { promisify } from "util";

import ClaimPool from "../generation/Claim";
import dataDirectory from "../generation/DataDirectory";
import EmployeePool from "../generation/Employee";
import EmployerPool from "../generation/Employer";
import { ScenarioSpecification } from "../generation/Scenario";
import DOR from "../generation/writers/DOR";
import EmployeeIndex from "../generation/writers/EmployeeIndex";
import EmployerIndex from "../generation/writers/EmployerIndex";
import * as scenarios from "../scenarios/training-refreshes";
import describe from "../specification/describe";
import { generateEmailAddress } from "../util/pii";

const today = new Date();

const pipelineP = promisify(pipeline);
const DIRECTORY_NAME = `${format(today, "yyyy-MM-dd")}-training`;
(async () => {
  const storage = dataDirectory(DIRECTORY_NAME);
  await storage.prepare();

  const employerPool = await EmployerPool.load(
    storage.employers
  ).orGenerateAndSave(() =>
    EmployerPool.generate(5, {
      size: "small",
      get leave_admin_email() {
        return generateEmailAddress();
      },
    })
  );
  await DOR.writeEmployersFile(employerPool, storage.dorFile("DORDFMLEMP"));
  await EmployerIndex.write(
    employerPool,
    path.join(storage.dir, "employers.csv")
  );

  const employeePool = await EmployeePool.load(
    storage.employees
  ).orGenerateAndSave(() =>
    EmployeePool.merge(
      EmployeePool.generate(7010 * 1.5 * 3, employerPool, {
        mass_id: true,
        wages: "eligible",
      }),
      EmployeePool.generate(2550 * 1.5 * 3, employerPool, {
        mass_id: false,
        wages: "eligible",
      }),
      EmployeePool.generate(300 * 1.5 * 3, employerPool, {
        wages: "ineligible",
      })
    )
  );
  await DOR.writeEmployeesFile(
    employerPool,
    employeePool,
    storage.dorFile("DORDFML")
  );
  await EmployeeIndex.write(
    employeePool,
    path.join(storage.dir, "employees.csv")
  );

  const generateClaims = (spec: ScenarioSpecification, count: number) =>
    ClaimPool.generate(employeePool, spec.employee, spec.claim, count);
  await ClaimPool.load(storage.claims, storage.documents).orGenerateAndSave(
    () =>
      ClaimPool.merge(
        ...Object.values(scenarios).map((spec) => {
          if (typeof spec?.claim.metadata?.quantity !== "number") {
            throw new Error("Missing 'quantity' value for scenario");
          }
          return generateClaims(spec, spec.claim.metadata.quantity * 1.5);
        })
      )
  );
  // Write a CSV description of the scenarios we're using for human consumption.
  await pipelineP(
    describe(Object.values(scenarios)),
    fs.createWriteStream(storage.dir + "/scenarios.csv")
  );

  // Training team request: 1,000 claimants with no data and no claims
  // used to generate employees without any demographics
  const blankClaimantsStorage = dataDirectory(
    DIRECTORY_NAME + "-blank-claimants"
  );
  await blankClaimantsStorage.prepare();

  // Generate a pool of employers for employees without any demographics.
  const blankClaimantsEmployerPool: EmployerPool = await EmployerPool.load(
    blankClaimantsStorage.employers
  ).orGenerateAndSave(() =>
    EmployerPool.merge(
      EmployerPool.generate(1, {
        size: "large",
        get leave_admin_email() {
          return generateEmailAddress();
        },
      })
    )
  );
  await DOR.writeEmployersFile(
    blankClaimantsEmployerPool,
    blankClaimantsStorage.dorFile("DORDFMLEMP")
  );
  await EmployerIndex.write(
    blankClaimantsEmployerPool,
    path.join(blankClaimantsStorage.dir, "employers.csv")
  );

  // Generate a pool of employees without any demographics.
  const blankClaimantsEmployeePool: EmployeePool = await EmployeePool.load(
    blankClaimantsStorage.employees
  ).orGenerateAndSave(() =>
    EmployeePool.generate(1000, blankClaimantsEmployerPool, {
      wages: "eligible",
    })
  );
  await DOR.writeEmployeesFile(
    blankClaimantsEmployerPool,
    blankClaimantsEmployeePool,
    blankClaimantsStorage.dorFile("DORDFML")
  );
  await EmployeeIndex.write(
    blankClaimantsEmployeePool,
    path.join(blankClaimantsStorage.dir, "employees.csv")
  );

  // Training team request: 400 claimants with party record data but no claims
  // Used for mass ID employees with pre-populated demographics
  // Demographics can be populated using the set_party_data.ts script
  const populatedMassIdClaimantsStorage = dataDirectory(
    DIRECTORY_NAME + "-populated-mass-id-claimants"
  );
  await populatedMassIdClaimantsStorage.prepare();

  // Generate a pool of employers for mass ID employees with pre-populated demographics.
  const populatedMassIdClaimantsEmployerPool: EmployerPool =
    await EmployerPool.load(
      populatedMassIdClaimantsStorage.employers
    ).orGenerateAndSave(() =>
      EmployerPool.merge(
        EmployerPool.generate(1, {
          size: "large",
          get leave_admin_email() {
            return generateEmailAddress();
          },
        })
      )
    );
  await DOR.writeEmployersFile(
    populatedMassIdClaimantsEmployerPool,
    populatedMassIdClaimantsStorage.dorFile("DORDFMLEMP")
  );
  await EmployerIndex.write(
    populatedMassIdClaimantsEmployerPool,
    path.join(populatedMassIdClaimantsStorage.dir, "employers.csv")
  );

  // Generate a pool of mass ID employees for pre-populated demographics.
  const populatedMassIdClaimantsEmployeePool: EmployeePool =
    await EmployeePool.load(
      populatedMassIdClaimantsStorage.employees
    ).orGenerateAndSave(() =>
      EmployeePool.generate(400, populatedMassIdClaimantsEmployerPool, {
        mass_id: true,
        wages: "eligible",
      })
    );
  await DOR.writeEmployeesFile(
    populatedMassIdClaimantsEmployerPool,
    populatedMassIdClaimantsEmployeePool,
    populatedMassIdClaimantsStorage.dorFile("DORDFML")
  );
  await EmployeeIndex.write(
    populatedMassIdClaimantsEmployeePool,
    path.join(populatedMassIdClaimantsStorage.dir, "employees.csv")
  );

  // Training team request: 1 exempt employer with 30 employees
  const exemptClaimantsStorage = dataDirectory(
    DIRECTORY_NAME + "-exempt-claimants"
  );
  await exemptClaimantsStorage.prepare();

  // Generate a an exempt employer in a pool.
  const exemptClaimantsEmployerPool: EmployerPool = await EmployerPool.load(
    exemptClaimantsStorage.employers
  ).orGenerateAndSave(() =>
    EmployerPool.merge(
      EmployerPool.generate(1, {
        size: "small",
        family_exemption: true,
        medical_exemption: true,
        get leave_admin_email() {
          return generateEmailAddress();
        },
      })
    )
  );
  await DOR.writeEmployersFile(
    exemptClaimantsEmployerPool,
    exemptClaimantsStorage.dorFile("DORDFMLEMP")
  );
  await EmployerIndex.write(
    exemptClaimantsEmployerPool,
    path.join(exemptClaimantsStorage.dir, "employers.csv")
  );

  // Generate a pool of employees for the exempt employer.
  const exemptClaimantsEmployeePool: EmployeePool = await EmployeePool.load(
    exemptClaimantsStorage.employees
  ).orGenerateAndSave(() =>
    EmployeePool.generate(30, exemptClaimantsEmployerPool, {
      mass_id: true,
      wages: "eligible",
    })
  );
  await DOR.writeEmployeesFile(
    exemptClaimantsEmployerPool,
    exemptClaimantsEmployeePool,
    exemptClaimantsStorage.dorFile("DORDFML")
  );
  await EmployeeIndex.write(
    exemptClaimantsEmployeePool,
    path.join(exemptClaimantsStorage.dir, "employees.csv")
  );

  // Used for employees to have claims created manually
  const manualClaimantsStorage = dataDirectory(
    DIRECTORY_NAME + "-manual-claimants"
  );
  await manualClaimantsStorage.prepare();

  // Training team request: Employees for scenarios labelled as "Claimants only"
  // Generate a pool of employers for manual claim submission.
  const manualClaimantsEmployerPool: EmployerPool = await EmployerPool.load(
    manualClaimantsStorage.employers
  ).orGenerateAndSave(() =>
    EmployerPool.merge(
      EmployerPool.generate(1, {
        size: "large",
        get leave_admin_email() {
          return generateEmailAddress();
        },
      })
    )
  );
  await DOR.writeEmployersFile(
    manualClaimantsEmployerPool,
    manualClaimantsStorage.dorFile("DORDFMLEMP")
  );
  await EmployerIndex.write(
    manualClaimantsEmployerPool,
    path.join(manualClaimantsStorage.dir, "employers.csv")
  );

  // Generate a pool of employees for manual claim submission.
  const manualClaimantsEmployeePool: EmployeePool = await EmployeePool.load(
    manualClaimantsStorage.employees
  ).orGenerateAndSave(() =>
    EmployeePool.generate(800, manualClaimantsEmployerPool, {
      wages: "eligible",
    })
  );
  await DOR.writeEmployeesFile(
    manualClaimantsEmployerPool,
    manualClaimantsEmployeePool,
    manualClaimantsStorage.dorFile("DORDFML")
  );
  await EmployeeIndex.write(
    manualClaimantsEmployeePool,
    path.join(manualClaimantsStorage.dir, "employees.csv")
  );

  // Generate an employeer with Org Units with fein 04-6002284 and name COMM OF MASS COMPTROLLER'S OFFICE
  // And a pool of 150 employees associated with that employer
  const orgUnitsClaimantsStorage = dataDirectory(
    DIRECTORY_NAME + "-orgUnit-claimants"
  );
  await orgUnitsClaimantsStorage.prepare();
  const orgUnitsEmployerPool: EmployerPool = await EmployerPool.load(
    orgUnitsClaimantsStorage.employers
  ).orGenerateAndSave(() =>
    EmployerPool.merge(
      EmployerPool.generate(1, {
        fein: "04-6002284",
        name: "COMM OF MASS COMPTROLLER'S OFFICE",
      })
    )
  );

  const orgUnitsEmployeePool: EmployeePool = await EmployeePool.load(
    orgUnitsClaimantsStorage.employees
  ).orGenerateAndSave(() =>
    EmployeePool.generate(150, orgUnitsEmployerPool, {
      mass_id: true,
      wages: "eligible",
    })
  );
  await DOR.writeEmployersFile(
    orgUnitsEmployerPool,
    orgUnitsClaimantsStorage.dorFile("DORDFMLEMP")
  );

  await EmployerIndex.write(
    orgUnitsEmployerPool,
    path.join(orgUnitsClaimantsStorage.dir, "employers.csv")
  );

  await DOR.writeEmployeesFile(
    orgUnitsEmployerPool,
    orgUnitsEmployeePool,
    orgUnitsClaimantsStorage.dorFile("DORDFML")
  );

  await EmployeeIndex.write(
    orgUnitsEmployeePool,
    path.join(orgUnitsClaimantsStorage.dir, "employees.csv")
  );

  // Generate and employer with fein 00-0000000 and name "EMPLOYER NOT FOUND"

  const notFoundClaimantsStorage = dataDirectory(
    DIRECTORY_NAME + "-notFound-claimants"
  );
  await notFoundClaimantsStorage.prepare();
  const notFoundEmployerPool: EmployerPool = await EmployerPool.load(
    notFoundClaimantsStorage.employers
  ).orGenerateAndSave(() =>
    EmployerPool.merge(
      EmployerPool.generate(1, {
        fein: "00-0000000",
        name: "EMPLOYER NOT FOUND",
      })
    )
  );
  await DOR.writeEmployersFile(
    notFoundEmployerPool,
    notFoundClaimantsStorage.dorFile("DORDFMLEMP")
  );
  await EmployerIndex.write(
    notFoundEmployerPool,
    path.join(notFoundClaimantsStorage.dir, "employers.csv")
  );

  const used = process.memoryUsage().heapUsed / 1024 / 1024;
  console.log(
    `The script uses approximately ${Math.round(used * 100) / 100} MB`
  );
})().catch((e) => {
  console.error(e);
  process.exit(1);
});
