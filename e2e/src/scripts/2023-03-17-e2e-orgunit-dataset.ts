import { format } from "date-fns";

import config from "../config";
import dataDirectory from "../generation/DataDirectory";
import EmployeePool from "../generation/Employee";
import EmployerPool from "../generation/Employer";
import DOR from "../generation/writers/DOR";
import EmployeeIndex from "../generation/writers/EmployeeIndex";

const environment = config("ENVIRONMENT");
const employersFile = config("ORGUNIT_EMPLOYERS_FILE");
const hasOrgUnitSetup = config("HAS_ORGUNITS_SETUP");

(async () => {
  if (!hasOrgUnitSetup) {
    throw new Error(
      `The '${environment}' environment does not have Organization Units setup!`
    );
  }

  // Prepare a "data directory" to save the generated data to disk.
  const date = format(new Date(), "yyyy-MM-dd");
  const storage = dataDirectory(`e2e-${date}-${environment}`);
  await storage.prepare();

  // Read employers directly from the currently selected environment
  // So that the generated DOR files are actually valid
  // An environment can be selected by modifying the local `.env` file
  // ie.: loads file E2E_ORGUNIT_EMPLOYERS_FILE for `E2E_ENVIRONMENT=uat`
  const employerPool = await EmployerPool.load(employersFile);

  // Define the kinds of employees we need to support.
  // Each type of employee is generated as its own pool, then we merge them all together.
  const employeePool = EmployeePool.merge(
    EmployeePool.generate(5, employerPool, {
      wages: "ineligible",
    }),
    EmployeePool.generate(5, employerPool, { wages: 30_000 }),
    EmployeePool.generate(5, employerPool, { wages: 60_000 }),
    EmployeePool.generate(5, employerPool, { wages: 90_000 })
  );
  // Write the employee pool to disk (employees.json).
  await employeePool.save(storage.employees);
  // Write an employees DOR file to disk.
  await DOR.writeEmployeesFile(
    employerPool,
    employeePool,
    storage.dorFile("DORDFML")
  );
  // Write an employee "index" file for human consumption.
  await EmployeeIndex.write(employeePool, storage.dir + "/employees.csv");

  // Additionally save the JSON files to the employers/employees directory at the top level.
  await employeePool.save(`employees/e2e-${date}-${environment}-orgunits.json`);
})().catch((e) => {
  console.error(e);
  process.exit(1);
});
