{"name": "pfml-artillery", "description": "This faux-package contains dependencies we cannot bundle.", "main": "processor.js", "dependencies": {"@influxdata/influxdb-client": "^1.18.0", "artillery": "^2.0.19", "playwright-chromium": "^1.12.2"}, "overrides": {"@aws-sdk/client-cloudwatch": "^3.621.0", "@aws-sdk/credential-providers": "^3.621.0", "brace-expansion": "2.0.2", "axios": "^1.8.2", "artillery": {"posthog-node": "3.1.3"}, "jsonpath-plus": "10.3.0", "ip": "https://registry.npmjs.org/@seal-security/ip/-/ip-2.0.0-sp-1.tgz"}, "scripts": {"artillery": "artillery run -e test dist/spec.yaml"}, "volta": {"node": "20.9.0", "npm": "10.1.0"}}