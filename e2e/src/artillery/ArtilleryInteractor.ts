import { faker } from "@faker-js/faker";
import { EventEmitter } from "events";
import winston from "winston";

import config from "../../src/config";
import { EmployeesResponse } from "../_api";
import FineosClient from "../FineosClient";
import { ClaimGenerator, GeneratedClaim } from "../generation/Claim";
import EmployeePool, { Employee } from "../generation/Employee";
import * as scenarios from "../scenarios/lst";
import AuthenticationManager from "../submission/AuthenticationManager";
import { Credentials, OAuthCreds } from "../types";
import { dispatchPostSubmit } from "../util/claimGenerator";
import { getAuthManager, getFineosClient } from "../util/common";
import {
  getApiCredentialsForSnow,
  getClaimantCredentials,
} from "../util/credentials";
import { findScenarioByName } from "../util/scenarios";
import documentDownloadPool from "./1099.json";
import getArtillerySubmitter from "./ArtilleryClaimSubmitter";
import { getDataFromClaim, UsefulClaimData } from "./util";

/**
 * This is the interaction layer.
 *
 * Each public method on this interactor is a step that can be performed in a load test.
 */
export default class ArtilleryPFMLInteractor {
  private employeePool?: EmployeePool;
  private authManager: AuthenticationManager;
  private fineosClient?: FineosClient;
  constructor() {
    this.authManager = getAuthManager();
  }

  private async employees(): Promise<EmployeePool> {
    if (!this.employeePool) {
      this.employeePool = await EmployeePool.load(config("LST_EMPLOYEES_FILE"));
    }
    return this.employeePool;
  }
  generateClaim(
    ee: EventEmitter,
    scenario: Scenarios,
    logger: winston.Logger
  ): Promise<GeneratedClaim> {
    return timed(ee, logger, "generateClaim", async () => {
      const pool = await this.employees();
      if (!(scenario in scenarios)) {
        throw new Error("Invalid or missing scenario");
      }
      const scenarioObj = findScenarioByName(scenario);
      const claim = ClaimGenerator.generate(
        pool,
        scenarioObj.employee,
        scenarioObj.claim
      );
      if (claim.employerResponse) {
        claim.employerResponse.fraud = claim.employerResponse.fraud || "No";
      }
      logger.debug("Fully generated claim", claim.claim);
      return claim;
    });
  }

  submitClaim(
    claim: GeneratedClaim,
    ee: EventEmitter,
    logger: winston.Logger,
    creds?: Credentials
  ): Promise<ApplicationSubmissionResponse> {
    return timed(ee, logger, "submitClaim", async () => {
      const claimantCredentials = creds ?? getClaimantCredentials();
      const leaveAdminCredentials = getLeaveAdminCredentials(
        claim.claim.employer_fein as string
      );
      const submitter = getArtillerySubmitter();
      const submission = await submitter.lstSubmit(
        claim,
        claimantCredentials,
        leaveAdminCredentials,
        logger
      );
      return submission;
    });
  }

  postProcessClaim(
    claim: GeneratedClaim,
    submission: ApplicationSubmissionResponse,
    ee: EventEmitter,
    logger: winston.Logger
  ): Promise<UsefulClaimData> {
    return timed(ee, logger, "postProcessClaim", async () => {
      await dispatchPostSubmit(claim, submission);
      return getDataFromClaim(claim);
    });
  }

  getFirstValidEmployee(data: EmployeesResponse | undefined) {
    if (data) {
      for (const datum of data) {
        if (datum.employee_id) {
          return datum.employee_id;
        }
      }
    }

    return null;
  }

  async getEmployeeFromPool(): Promise<Employee> {
    const pool = await this.employees();
    const employee = pool.shuffle().slice(0, 1).getFirst();

    if (
      employee.first_name.length < 3 ||
      employee.last_name.length < 3 ||
      !/^[a-zA-Z0-9]*$/.test(employee.last_name)
    ) {
      return await this.getEmployeeFromPool();
    } else {
      return employee;
    }
  }

  async getAPIToken(creds?: OAuthCreds) {
    const apiCreds = creds ?? getApiCredentialsForSnow();
    return await this.authManager.getAPIBearerToken(apiCreds);
  }

  searchEmployee(
    employee: Employee,
    ee: EventEmitter,
    logger: winston.Logger,
    token: string
  ) {
    return timed(ee, logger, "SearchEmployee", async () => {
      logger.debug(
        `Search For Employee Name ${employee.first_name} ${employee.last_name}`
      );
      if (!employee.phone_number) {
        throw Error(
          `Missing employee phone number for employee ${employee.ssn}`
        );
      }
      const submitter = getArtillerySubmitter();
      const {
        data: { data: searchResult },
      } = await submitter.lstSearch(
        {
          phone_number: employee.phone_number.replace(/-/g, ""),
        },
        token
      );
      logger.debug(`Employees Found (phone search): ${searchResult?.length}`);
      if (searchResult && searchResult?.length > 0) {
        return this.getFirstValidEmployee(searchResult);
      }
      const fallbackSearchResult = await submitter.lstSearch(
        {
          first_name: employee.first_name,
          last_name: employee.last_name,
        },
        token
      );
      logger.debug(
        `Employees Found (name search): ${fallbackSearchResult.data.data?.length}`
      );
      return this.getFirstValidEmployee(fallbackSearchResult.data.data);
    });
  }

  searchClaimant(
    employeeId: string,
    ee: EventEmitter,
    logger: winston.Logger,
    token: string
  ) {
    return timed(ee, logger, "SearchClaims", async () => {
      const submitter = getArtillerySubmitter();
      const claim = await submitter.claimSearch(employeeId, token);
      logger.debug(`Claims Found: ${claim.data.data?.length}`);
    });
  }

  getDocumentsFromFineos(
    args: Parameters<FineosClient["getDocuments"]>,
    ee: EventEmitter,
    logger: winston.Logger
  ): Promise<Record<string, unknown>[]> {
    return timed(ee, logger, "GetDocumentsFromFineos", async () => {
      if (!this.fineosClient) {
        this.fineosClient = await getFineosClient();
      }
      return await this.fineosClient.getDocuments(...args);
    });
  }
  downloadDocumentFromFineos(
    args: Parameters<FineosClient["downloadDocument"]>,
    ee: EventEmitter,
    logger: winston.Logger
  ) {
    return timed(ee, logger, "DownloadDocument", async () => {
      if (!this.fineosClient) {
        this.fineosClient = await getFineosClient();
      }
      await this.fineosClient.downloadDocument(...args);
    });
  }
  selectRandomDocDownload() {
    return faker.helpers.arrayElement(documentDownloadPool);
  }
}

async function timed<T>(
  ee: EventEmitter,
  logger: winston.Logger,
  name: string,
  cb: () => Promise<T>
): Promise<T> {
  logger.info(`Starting ${name}`);
  const startedAt = process.hrtime();
  ee.emit("counter", `${name}.count.started`, 1);
  try {
    const res = await cb();
    ee.emit("counter", `${name}.count.completed`, 1);
    logger.info(`Completed ${name}`);
    return res;
  } catch (e) {
    ee.emit("counter", `${name}.count.error`, 1);
    logger.info(`Completed ${name} with error.`);
    throw e;
  } finally {
    const endedAt = process.hrtime(startedAt);
    const delta = endedAt[0] * 1e9 + endedAt[1];
    // Convert to milliseconds.
    ee.emit("histogram", `${name}.time`, delta / 1e6);
  }
}

function getLeaveAdminCredentials(fein: string): Credentials {
  const password = config("EMPLOYER_PORTAL_PASSWORD");
  if (!password) {
    throw new Error(
      `You must set the E2E_EMPLOYER_PORTAL_PASSWORD environment variable.`
    );
  }
  return {
    username: `gqzap.employer.${fein.replace("-", "")}@inbox.testmail.app`,
    password,
  };
}
