import { LSTScenarios, NonEmptyArray } from "../types";
import { pick } from "../util/random";
import {
  ArtilleryDocSize,
  DocRangesToScenarios,
  DocumentRange,
  ScenarioPool,
} from "./types";

export default class ArtilleryScenarioPool {
  private scenarioWheel: ScenarioPool[];
  private documentSizeWheel: ArtilleryDocSize[];

  constructor(
    scenarioWheel: NonEmptyArray<ScenarioPool>,
    range: DocumentRange
  ) {
    this.scenarioWheel = scenarioWheel;
    this.documentSizeWheel = generateDocumentSizeWeights(range);
  }

  getScenario(): LSTScenarios {
    const range = pick(this.documentSizeWheel);
    const pool = pick(this.scenarioWheel);
    const possibleScenarios = scenarioMap[pool][range];
    return pick(possibleScenarios);
  }
}

function generateDocumentSizeWeights(range: DocumentRange): ArtilleryDocSize[] {
  switch (range) {
    case "small": {
      return ["small"];
    }
    case "large": {
      return [...Array(70).fill("med"), ...Array(30).fill("large")];
    }
    case "full_range": {
      return [
        ...Array(85).fill("small"),
        ...Array(10).fill("med"),
        ...Array(5).fill("large"),
      ];
    }
  }
}

const scenarioMap: Record<ScenarioPool, DocRangesToScenarios> = {
  default: {
    small: ["LSTOLB1_150KB", "LSTBHAP_1MB", "LSTCHAP1_2MB", "LSTOLB1_4MB"],
    med: ["LSTBHAP_5MB", "LSTCHAP1_6MB", "LSTOLB1_7MB"],
    large: ["LSTBHAP_8MB", "LSTCHAP1_9MB"],
  },
  episodicLeave: {
    small: ["LST_INTERMITTENT_150KB"],
    med: ["LST_INTERMITTENT_5MB"],
    large: ["LST_INTERMITTENT_8MB"],
  },
};
