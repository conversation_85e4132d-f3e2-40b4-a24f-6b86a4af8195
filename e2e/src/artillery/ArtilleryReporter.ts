import {
  CloudWatchLogsClient,
  GetQueryResultsCommand,
  ResultField,
  StartQueryCommand,
} from "@aws-sdk/client-cloudwatch-logs";
import { endOfDay, startOfDay } from "date-fns";
import delay from "delay";
import pRetry from "p-retry";

type MessageField = {
  uid: string;
  run_id: string;
  instance_id: string;
  type: string;
  level: string;
  message?: string;
  file_size?: string;
};

type APICategorizationRecord = {
  errorCount: number;
  identifier: RegExp | null;
  messages: string[];
  fileSizeFailures?: Record<string, number>;
};

type ErrorCategory =
  | "review"
  | "documents"
  | "submit-application"
  | "submit-payment-preference"
  | "submit-tax-withholding-preference"
  | "complete-application"
  | "applications"
  | "postProcessing"
  | "claims"
  | "payments"
  | "unknown";

const generateCategory = (
  identifier: APICategorizationRecord["identifier"]
): APICategorizationRecord => {
  return { errorCount: 0, identifier, messages: [] };
};

type ErrorCategories = Record<ErrorCategory, APICategorizationRecord>;
export default class ArtilleryReporter {
  private cloudWatchClient: CloudWatchLogsClient;
  private readonly runId: string;
  private readonly errorCategories: ErrorCategories;
  private claimSubmitAttempts?: number;
  private postProcessingAttempts?: number;
  private parsedErrorLogs?: MessageField[];

  constructor(runId: string) {
    this.cloudWatchClient = new CloudWatchLogsClient({});
    this.runId = runId;
    this.errorCategories = {
      documents: { ...generateCategory(/\/documents/) },
      review: generateCategory(/\/review/),
      ["submit-application"]: generateCategory(/\/submit-application/),
      ["submit-payment-preference"]: generateCategory(
        /\/submit-payment-preference/
      ),
      ["submit-tax-withholding-preference"]: generateCategory(
        /(\/submit-tax-withholding-preference)/
      ),
      ["complete-application"]: generateCategory(/\/complete-application/),
      applications: generateCategory(
        /(applications(\/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})?\s+)/
      ),
      claims: generateCategory(/claims\/NTN-[0-9]+-[A-Z]{3}-[0-9]{2}\s+/),
      payments: generateCategory(/payments/),
      postProcessing: generateCategory(
        /(page\.|waiting for (selector|navigation|stable page)|)/
      ),
      unknown: generateCategory(null),
    };
  }

  async retrieveAndProcessResults() {
    const errorLogs = this.getLogs(this.getCloudWatchQueries("errors"));
    const claimSubmissionAttempts = this.getLogs(
      this.getCloudWatchQueries("claimSubmissionCount")
    );
    const postProcessingAttempts = this.getLogs(
      this.getCloudWatchQueries("postProcessingCount")
    );
    console.log(`Fetching LST results - this could take a few seconds.\n`);
    const [eLogs, claimLogs, postProcessingLogs] = await Promise.all([
      errorLogs,
      claimSubmissionAttempts,
      postProcessingAttempts,
    ]);
    this.parseRawLogs(eLogs);
    if (!claimLogs[0][0].value || !postProcessingLogs[0][0].value) {
      throw Error(
        "Could not determine total claim submission or post processing attempts"
      );
    }
    this.claimSubmitAttempts = Number(claimLogs[0][0].value);
    this.postProcessingAttempts = Number(postProcessingLogs[0][0].value);
    this.categorizeErrors();
    this.countDocumentFailures();
  }

  private categorizeErrors() {
    if (this.parsedErrorLogs) {
      for (const log of this.parsedErrorLogs) {
        this.categorizeError(log.message);
      }
    }
  }

  private async getLogs(query: string) {
    const match = this.runId.match(/[0-9]{2}-[0-9]{2}-[0-9]{4}/);
    if (!match) {
      throw Error("Could not determine lst date from run id");
    }
    const date = new Date(match[0]);
    const { queryId } = await this.cloudWatchClient.send(
      new StartQueryCommand({
        queryString: query,
        logGroupName: "e2e-lst-logs",
        startTime: startOfDay(date).getTime(),
        endTime: endOfDay(date).getTime(),
      })
    );
    return await pRetry(
      async () => {
        await delay(1000 * 3);
        const logs = await this.cloudWatchClient.send(
          new GetQueryResultsCommand({ queryId })
        );
        if (!logs.results) {
          throw Error("No results for CloudWatch query");
        }
        return logs.results;
      },
      { retries: 3 }
    );
  }

  private parseRawLogs(rawLogs: ResultField[][]) {
    if (!rawLogs) {
      throw Error("No logs to parse at the time of invoking parseRawLogs()");
    }
    const results = [];
    for (const log of rawLogs) {
      if (this.isErrorLog(log)) {
        results.push(this.extractMessage(log));
      }
    }
    this.parsedErrorLogs = results;
  }

  private isErrorLog(log: ResultField[]) {
    return log.some(
      (entry) => entry.field == "level" && entry.value == "error"
    );
  }

  private extractMessage(log: ResultField[]) {
    const messageField = log.find((entry) => entry.field === "@message");
    if (!messageField || !messageField.value) {
      throw Error("Cloudwatch log missing @message field");
    }
    const result = JSON.parse(messageField.value) as MessageField;
    return result;
  }

  private categorizeError(error: string | undefined) {
    let key: ErrorCategory;
    for (key in this.errorCategories) {
      // handles empty or undefined error messages
      if (!error) {
        this.errorCategories["unknown"].errorCount += 1;
        return;
      }
      if (this.errorCategories[key].identifier?.test(error)) {
        this.errorCategories[key].errorCount += 1;
        return;
      }
    }
    // handle unknown error messages
    this.errorCategories["unknown"].errorCount += 1;
  }

  private countDocumentFailures() {
    if (this.parsedErrorLogs) {
      const failedDocumentFileSizes = this.parsedErrorLogs
        .filter((log) => {
          return (
            log.message &&
            log.file_size &&
            this.errorCategories["documents"].identifier?.test(log.message)
          );
        })
        .map((record) => record.file_size as string);
      for (const fileSize of failedDocumentFileSizes) {
        let obj = this.errorCategories["documents"].fileSizeFailures;
        if (!obj) {
          obj = {};
          this.errorCategories["documents"].fileSizeFailures = obj;
        }
        if (!obj) {
          throw Error("Property fileSizeFailures");
        }
        if (obj[fileSize] !== undefined) {
          obj[fileSize] += 1;
        } else {
          obj[fileSize] = 1;
        }
      }
    }
  }

  logResults() {
    let results =
      "Copy and paste (w/ match style) the results below into an lst report \n--------------\n";
    results += "### Error Categorization\n\n";
    for (const [key, value] of Object.entries(this.errorCategories)) {
      if (value.errorCount) {
        results += this.formatLine(key as ErrorCategory, value.errorCount);
      }
    }
    console.log(results);
  }

  private formatLine(
    key: keyof typeof this.errorCategories,
    errorCount: number
  ) {
    if (!this.claimSubmitAttempts || !this.postProcessingAttempts) {
      throw Error("claimSubmitAttempts or postProcessingAttempts is undefined");
    }
    let result = "";
    if (key == "postProcessing" || key == "unknown") {
      result += `* ${key}\n`;
    } else {
      result += `* /${key}\n`;
    }
    const isApiOrUnknownError = key !== "postProcessing";
    const percentage = (
      (errorCount /
        (isApiOrUnknownError
          ? this.claimSubmitAttempts
          : this.postProcessingAttempts)) *
      100
    ).toFixed(2);
    result += `** Failed - ${errorCount} total (${percentage}% of ${
      isApiOrUnknownError ? "claim submission" : "post processing"
    } attempts)\n`;
    if (key === "documents") {
      Object.entries(
        this.errorCategories["documents"].fileSizeFailures ?? {}
      ).map(([fileName, errorCount]) => {
        result += `*** ${fileName} - ${errorCount} failures\n`;
      });
    }
    return result;
  }

  private getCloudWatchQueries(
    type: "errors" | "postProcessingCount" | "claimSubmissionCount"
  ) {
    switch (type) {
      case "errors": {
        return `fields @timestamp, uid, level, message, @message
        | filter run_id = "${this.runId}"
        | filter level like "error"
        | limit 1000`;
      }

      case "postProcessingCount": {
        return `fields uid, message
        | filter run_id = "${this.runId}"
        | filter message like 'Starting postProcessClaim'
        | stats count_distinct(uid)`;
      }

      case "claimSubmissionCount": {
        return `fields uid, message
        | filter run_id = "${this.runId}"
        | filter message like 'Starting submitClaim'
        | stats count_distinct(uid)`;
      }
      default: {
        throw new Error("No query type provided");
      }
    }
  }
}
