import path from "path";
import { CommandModule } from "yargs";

import { SystemWideArgs } from "../../cli";
import ClaimPool from "../../generation/Claim";
import dataDirectory from "../../generation/DataDirectory";
import { submit } from "../../scripts/util";
import ClaimSubmissionTracker from "../../submission/ClaimStateTracker";
import SubmittedClaimIndex from "../../submission/writers/SubmittedClaimIndex";
import { dispatchPostSubmit } from "../../util/claimGenerator";

type SubmissionArgs = {
  claimDirectory: string;
  completionDelay?: number;
  concurrency?: number;
  cooldownAfter?: number;
  errorLimit?: number;
} & SystemWideArgs;

const cmd: CommandModule<SystemWideArgs, SubmissionArgs> = {
  command: "submit <claimDirectory>",
  describe: "Registers a new leave admin account for a given employer.",
  builder: (yargs) => {
    return yargs
      .positional("claimDirectory", {
        type: "string",
        description: "Directory that contains claim pool to be submitted",
        demandOption: true,
      })
      .options({
        completionDelay: {
          description:
            "Millisecond delay between document upload and application completion",
          number: true,
          default: 0,
          alias: "cd",
        },
        concurrency: {
          description: "Concurrent amount of claim submissions",
          number: true,
          default: 3,
          alias: "cc",
        },
        cooldownAfter: {
          description:
            "Delay claim submission after the given amount of consecutive errors",
          number: true,
          default: 0,
          alias: "ca",
        },
        errorLimit: {
          description: "Amount of consecutive errors before exiting program",
          number: true,
          default: 3,
        },
      });
  },
  async handler(args) {
    try {
      const storage = dataDirectory(
        args.claimDirectory,
        path.join(__dirname, "..", "..", "..")
      );
      const tracker = new ClaimSubmissionTracker(storage.state);
      const claimPool: ClaimPool = await ClaimPool.load(
        storage.claims,
        storage.documents
      );

      args.logger.info(
        `Claim submission beginning with a concurrency of ${args.concurrency}`
      );
      const completionDelay = args?.completionDelay ?? 0;
      const concurrency = args?.concurrency || 1;
      const cooldownAfter = args?.cooldownAfter || 0;
      const errorLimit = args?.errorLimit || 3;
      if (cooldownAfter > 0) {
        if (cooldownAfter >= errorLimit) {
          throw Error(
            "To run in cooldown mode, cooldownAfter must be less than errorLimit"
          );
        }
        args.logger.info(
          `Claim submission will run in cooldown mode after ${cooldownAfter} consecutive errors`
        );
      }

      await submit(
        {
          claims: claimPool,
          completionDelay,
          concurrency,
          cooldownAfter,
          maxConsecErrors: errorLimit,
          tracker,
        },
        { postSubmit: dispatchPostSubmit }
      );
      await SubmittedClaimIndex.write(
        path.join(storage.dir, "submitted.csv"),
        await ClaimPool.load(storage.claims, storage.documents),
        tracker
      );

      const used = process.memoryUsage().heapUsed / 1024 / 1024;
      console.log(
        `The script uses approximately ${Math.round(used * 100) / 100} MB`
      );
    } catch (e) {
      if (e.code !== "ENOENT") {
        console.log(e);
      } else {
        console.log(`Invalid path to claim directory: ${e.path}`);
      }
      process.exit(1);
    }
  },
};

const { command, describe, builder, handler } = cmd;
export { builder, command, describe, handler };
