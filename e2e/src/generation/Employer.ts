import { faker } from "@faker-js/faker";
import { format, formatISO } from "date-fns";
import fs from "fs";
import { pipeline, Readable } from "stream";
import { promisify } from "util";

import * as JSONStream from "../stream/json";
import { generateDefaultLeaveAdminUsername } from "../util/credentials";
import { generateFein } from "../util/employers";
import { random, shuffle } from "../util/random";
import { quarters } from "../util/writers";
import { Address } from "./../_api";
import unique from "./unique";

const pipelineP = promisify(pipeline);

type EmployerSize = "small" | "medium" | "large";

export interface GeneratePromise<T> extends Promise<T> {
  orGenerateAndSave(generationCb: () => T): Promise<T>;
}

export type LoadOrGeneratePromise = GeneratePromise<EmployerPool>;

export type Employer = {
  accountKey: string;
  name: string;
  fein: string;
  street: string;
  city: string;
  state: string;
  zip: string;
  dba: string;
  family_exemption: boolean;
  medical_exemption: boolean;
  exemption_commence_date?: Date | string;
  exemption_cease_date?: Date | string;
  updated_date?: Date | string;
  size?: EmployerSize;
  withholdings: Record<string, number>;
  leave_admin_email: string;
  activity_key: string;
  metadata?: Record<string, unknown>;
  mtc_number: string;
  is_statutorily_excluded: boolean;
};

export type MasterPlanData = {
  newEmployer: Employer;
  employeeId: string;
};

// Roulette wheel algorithm.
// Will generate 50x small employers and 10x medium employers for every large employer.
const employerSizeWheel = [
  ...new Array(50).fill("small"),
  ...new Array(10).fill("medium"),
  ...new Array(1).fill("large"),
];

export type EmployerGenerationSpec = {
  fein?: Employer["fein"];
  size?: Employer["size"];
  withholdings?: (number | null)[]; // quarters with 0 or null withholding amounts
  family_exemption?: boolean;
  medical_exemption?: boolean;
  exemption_commence_date?: Date;
  exemption_cease_date?: Date;
  address?: Address;
  leave_admin_email?: string;
  metadata?: Record<string, unknown>;
  name?: string;
  statutorily_excluded?: boolean;
};

/**
 * Older datasets may not have the leave_admin_email, withholdings, or
 * is_statutorily_excluded fields properly set.
 * @TODO (PFMLPB-17536): Remove this handling once we can safely ensure no other
 * datasets use outdated format.
 */
export type DehydratedEmployer = Omit<
  Employer,
  "is_statutorily_excluded" | "leave_admin_email" | "withholdings"
> & {
  is_statutorily_excluded?: boolean;
  leave_admin_email?: string;
  withholdings: Record<string, number> | number[];
};

export class EmployerGenerator {
  static hydrate(employer: DehydratedEmployer): Employer {
    let withholdings: Record<string, number>;
    if (Array.isArray(employer.withholdings)) {
      const withholdingValues = employer.withholdings;
      if (!employer.updated_date) {
        throw new Error(
          "Unable to hydrate employer without period start date or updated date"
        );
      }
      const periods = quarters(new Date(employer.updated_date)).map((period) =>
        format(period, "yyyy-MM-dd")
      );
      withholdings = Object.fromEntries(
        periods.map((period, index) => {
          const value = withholdingValues[index];
          return [
            period,
            value ?? getDefaultEmployerWithholding(employer.fein),
          ];
        })
      );
    } else {
      withholdings = employer.withholdings;
    }
    return {
      ...employer,
      is_statutorily_excluded: employer.is_statutorily_excluded ?? false,
      withholdings,
      leave_admin_email:
        employer.leave_admin_email ??
        generateDefaultLeaveAdminUsername(employer.fein),
    };
  }

  private generateAccountKey = unique(() => faker.string.numeric(11));
  private generateActivityKey = unique(() =>
    faker.number.int({ min: 100_000_000, max: 9_999_999_999 }).toString()
  );

  private generateCompanyName = unique(() => {
    const name = faker.company
      .buzzPhrase()
      // Capitalize first letter of each word.
      .replace(/(^|\s|-)\w/g, (char) => char.toUpperCase());
    const suffix = faker.helpers.arrayElement([
      "and Sons",
      "LLC",
      "Inc",
      "Group",
    ]);
    return `${name} ${suffix}`;
  });
  private generateFEIN = unique(generateFein);
  private generateMTCNumber = unique(() => `PFM${faker.string.numeric(11)}`);
  private generateSize() {
    return employerSizeWheel[Math.floor(random() * employerSizeWheel.length)];
  }

  private generateWithholding(
    fein: string,
    spec: (number | null)[] = [null, null, null, null],
    periods: string[],
    metadata: Record<string, unknown>
  ): Employer["withholdings"] {
    const defaultWithholding = getDefaultEmployerWithholding(fein);
    if (metadata.randomSingleQuarter) {
      spec.fill(0);
      const randomIndex = Math.floor(random() * 4);
      spec[randomIndex] = null;
    }
    return Object.fromEntries(
      periods.map((period, index) => {
        const value = spec[index];
        return [period, value ?? defaultWithholding];
      })
    );
  }

  generate(spec: EmployerGenerationSpec = {}): Employer {
    // dba and name should be the same

    const name = spec.name ?? this.generateCompanyName();
    const fein = spec.fein ?? this.generateFEIN();
    const periods = quarters(new Date()).map((period) =>
      format(period, "yyyy-MM-dd")
    );
    const withholdings = this.generateWithholding(
      fein,
      spec.withholdings,
      periods,
      spec.metadata ?? {}
    );
    const family_exemption = spec.family_exemption || false;
    const medical_exemption = spec.medical_exemption || false;
    const activity_key =
      family_exemption || medical_exemption ? this.generateActivityKey() : "0";
    const mtc_number = this.generateMTCNumber();

    return {
      accountKey: this.generateAccountKey(),
      name,
      fein,
      street: spec.address?.line_1 ?? faker.location.streetAddress(),
      city: spec.address?.city ?? faker.location.city(),
      state: spec.address?.state ?? "MA",
      zip: spec.address?.zip ?? faker.location.zipCode("#####-####"),
      dba: name,
      family_exemption,
      medical_exemption,
      exemption_commence_date: spec.exemption_commence_date,
      exemption_cease_date: spec.exemption_cease_date,
      updated_date: formatISO(new Date()),
      size: spec.size ?? this.generateSize(),
      withholdings,
      leave_admin_email:
        spec.leave_admin_email ?? generateDefaultLeaveAdminUsername(fein),
      activity_key,
      metadata: spec.metadata ?? {},
      mtc_number,
      is_statutorily_excluded: spec.statutorily_excluded ?? false,
    };
  }
}

export function getDefaultEmployerWithholding(fein: string) {
  const formattedFein = fein.replace(/-/, "");
  return parseInt(formattedFein.slice(0, 6)) / 100;
}

export type EmployerPickSpec = {
  size?: EmployerSize;
  withholdings?: number[] | "exempt" | "non-exempt";
  notFEIN?: string;
  metadata?: Record<string, unknown>;
  fein?: string;
  statutorilyExcluded?: boolean;
};

export default class EmployerPool implements Iterable<Employer> {
  private wheel?: EmployerSize[];

  /**
   * Load a pool from a JSON file.
   */
  static load(filename: string): LoadOrGeneratePromise {
    const employerProm = (async () => {
      const raw = await fs.promises.readFile(filename, "utf-8");
      const employers: DehydratedEmployer[] = JSON.parse(raw);
      return new this(employers.map(EmployerGenerator.hydrate));
    })();
    return Object.assign(employerProm, {
      orGenerateAndSave: async (generator: () => EmployerPool) => {
        return employerProm.catch(async (e) => {
          if (e.code !== "ENOENT") {
            throw e;
          }
          const employerPool = generator();
          await employerPool.save(filename);
          return employerPool;
        });
      },
    });
  }

  /**
   * Generate a new pool.
   */
  static generate(
    count: number,
    spec: EmployerGenerationSpec = {}
  ): EmployerPool {
    const generator = new EmployerGenerator();
    const employers = [...new Array(count)].map(() => generator.generate(spec));
    return new this(employers);
  }

  constructor(private employers: Employer[]) {}
  [Symbol.iterator](): Iterator<Employer> {
    return this.employers[Symbol.iterator]();
  }

  /**
   * Save the pool to JSON format.
   *
   * Uses streams for memory efficiency.
   */
  async save(filename: string): Promise<void> {
    await pipelineP(
      Readable.from(this.employers),
      JSONStream.stringify(),
      fs.createWriteStream(filename)
    );
  }

  /**
   * Merge one or more pools together into a megapool.
   *
   * @param pools
   */
  static merge(...pools: EmployerPool[]): EmployerPool {
    return new this(pools.map((p) => p.employers).flat(1));
  }

  /**
   * Get an employer from pool by fein, bypassing pick logic.
   */

  get(fein: string): Employer | undefined {
    return this.employers.find((e) => e.fein === fein);
  }

  /**
   * Pick a single employer from the pool.
   *
   * Favors larger employers, returning them more often.
   */
  pick(spec: EmployerPickSpec = {}): Employer {
    // Filter the pool down based on spec. If an explicit size hasn't been given,
    // pick a size according to the chance defined by weightedSize().
    const filter = buildPickFilter({ size: this.weightedSize(), ...spec });
    const applicable = this.employers.filter(filter);
    const employer = shuffle(applicable).pop();
    if (!employer) {
      throw new Error(
        `No employers match the specification: ${JSON.stringify(spec)}`
      );
    }
    return employer;
  }

  /**
   * Use the roulette wheel algorithm to pick a particular size of employer to select from the pool.
   *
   * This function considers the sizes of the employers in the pool (it will not return "large" if there are no "large"
   * employers in the pool. Beyond that, it returns larger sizes more often.
   */
  private weightedSize(): EmployerSize {
    if (this.wheel === undefined) {
      // Pick out the distinct sizes of employers in the pool.
      const activeSizes = this.employers.reduce(
        (sizes, employer) => sizes.add(employer.size),
        new Set()
      );
      // Build a roulette wheel containing the sizes.
      const wheel: EmployerSize[] = [];
      activeSizes.forEach((size) => {
        switch (size) {
          case "small": {
            // 1x chance of small.
            wheel.push(...new Array(1).fill("small"));
            break;
          }
          case "medium": {
            // 3x chance of medium.
            wheel.push(...new Array(3).fill("medium"));
            break;
          }
          case "large": {
            // 10x chance of large.
            wheel.push(...new Array(10).fill("large"));
            break;
          }
        }
      });
      this.wheel = wheel;
    }
    return this.wheel[Math.floor(random() * this.wheel.length)];
  }
}

function buildPickFilter(
  spec: EmployerPickSpec
): (employer: Employer) => boolean {
  return (employer: Employer) => {
    if (spec.notFEIN && spec.notFEIN === employer.fein) {
      return false;
    }

    if (spec.size && spec.size !== employer.size) {
      return false;
    }

    if (spec.fein && spec.fein !== employer.fein) {
      return false;
    }

    if (
      spec.withholdings === "exempt" &&
      Object.values(employer.withholdings).some((amt) => amt > 0)
    ) {
      return false;
    }

    if (
      spec.withholdings === "non-exempt" &&
      Object.values(employer.withholdings).some((amt) => amt === 0)
    ) {
      return false;
    }

    // Cheap test for shallow array equality.
    if (
      Array.isArray(spec.withholdings) &&
      JSON.stringify(Object.values(employer.withholdings)) !==
        JSON.stringify(spec.withholdings)
    ) {
      return false;
    }

    if (
      spec.statutorilyExcluded !== undefined &&
      spec.statutorilyExcluded !== employer.is_statutorily_excluded
    ) {
      return false;
    }

    if (spec.metadata) {
      for (const [k, v] of Object.entries(spec.metadata)) {
        if (!employer.metadata || employer.metadata[k] !== v) {
          return false;
        }
      }
    }

    return true;
  };
}
