import { differenceInWeeks, format, parseISO } from "date-fns";
import { snakeCase } from "lodash";

import { ApplicationLeaveDetails, ApplicationRequestBody } from "../../api";
import {
  extractEarliestEndDate,
  extractEarliestStartDate,
  extractLeavePeriod,
  extractLeavePeriodType,
  formatIntermittentDetails,
  formatStartAndEndDate,
  getAvailableLeaveScheduleTypes,
  getPeriodFromLeaveDetails,
  sumTotalHoursOff,
} from "../../util/claims";
import {
  AbstractDocumentGenerator,
  PDFFormData,
} from "./AbstractDocumentGenerator";

export type HealthCareProviderFormConfig = {
  /**
   * Overrides values derived from the submitted claim that would be used to
   * fill fields in the form. This allows for "incorrect" values to be populated
   * in the form.
   */
  claimOverride?: ApplicationRequestBody;
  expectedDateOfBirth?: Date | "leaveEnd" | "leaveStart";
  invalid?: boolean;
  postnatalRecoveryWeeks?: number;
  prenatalCareWeeks?: number;
};

export default class HealthCareProviderForm extends AbstractDocumentGenerator<HealthCareProviderFormConfig> {
  documentSource(): string {
    return this.path("hcp-v5.pdf");
  }
  getFormData(
    claimToSubmit: ApplicationRequestBody,
    config: HealthCareProviderFormConfig
  ): PDFFormData {
    const claim = {
      ...claimToSubmit,
      ...(config.claimOverride ?? {}),
    };

    if (
      !claim.first_name ||
      !claim.last_name ||
      !claim.date_of_birth ||
      !claim.leave_details
    ) {
      throw new Error("Unable to generate document due to missing properties");
    }
    // Note: To debug this PDF's fields, follow this: https://stackoverflow.com/a/38257183
    const dob = parseISO(claim.date_of_birth);
    const isPregnancy = !!claim.leave_details.pregnant_or_recent_birth;
    const childBirthDate = resolveChildBirthDate(
      claim.leave_details,
      config.expectedDateOfBirth
    );
    const type = snakeCase(
      `${extractLeavePeriodType(claim.leave_details)} leave periods`
    );
    // @ts-ignore
    const [start, end] = extractLeavePeriod(claim, type);
    const data: PDFFormData = {
      // Employee Info - Section 1
      "Employee Name 2": `${claim.first_name} ${claim.last_name}`,
      "Employee first name": `${claim.first_name}`,
      "Employee Last name": `${claim.last_name}`,
      "Emp. DOB mm": format(dob, "MM"),
      "Emp. DOB dd": format(dob, "dd"),
      "Emp. DOB yyyy": config.invalid ? "" : format(dob, "yyyy"),
      "Emp. SSI last 4": `${
        config.invalid ? "" : claim.tax_identifier?.slice(7)
      }`,
      Occupation: "[assume filled]",

      // Health Condition - Section 2
      "Employee Name 3": `${claim.first_name} ${claim.last_name}`,
      "Requires or did require": "On",
      "Requires multiple treatments and": "On",
      "Provide appropriate medical facts 1":
        "[Assume applicant has filled out this section]",
      "one essesntial job function 1":
        "[Assume applicant has filled out this section]",
      "Job-Related?": "No",
      "Pregnancy?": isPregnancy ? "Yes" : "No",
      "Weeks preg": isPregnancy
        ? resolvePregnancyCareWeeks(
            config.prenatalCareWeeks ??
              differenceInWeeks(end, start, { roundingMethod: "round" })
          )
        : "",
      "Weeks birth": isPregnancy
        ? resolvePregnancyCareWeeks(config.postnatalRecoveryWeeks)
        : "",
      "Delivery mm": isPregnancy ? format(childBirthDate, "MM") : "",
      "Delivery dd": isPregnancy ? format(childBirthDate, "dd") : "",
      "Delivery yyyy": isPregnancy ? format(childBirthDate, "yyyy") : "",

      // // Section 3 #
      Continuous: claim.has_continuous_leave_periods ? "On" : false,
      Reduced: claim.has_reduced_schedule_leave_periods ? "On" : false,
      Intermittent: claim.has_intermittent_leave_periods ? "On" : false,

      // Section 4 - Practitioner data.
      "Employee Name 4": `${claim.first_name} ${claim.last_name}`,
      "Medical Provider Signature_es_:signer:signature": "[assume signed]",
      "Provider name": "Theodore Cure",
      "Provider Title": "MD",
      License: "[assume license is valid]",
      "License state": "MA",
      "Area of practice": "General Medicine",
      "Practice name": "[assume business is valid]",
      "Office Phone 1": "555",
      "Office Phone 2": "555",
      "Office phone 3": "5555",
    };
    const leaveDetails = claim.leave_details;

    if (!leaveDetails) {
      throw new Error("No leave details available");
    }

    const availableLeaveTypes = getAvailableLeaveScheduleTypes(leaveDetails);

    for (const leaveType of availableLeaveTypes) {
      switch (leaveType) {
        case "continuous": {
          const period = getPeriodFromLeaveDetails(
            "continuous_leave_periods",
            leaveDetails
          );
          const { startDate, endDate } = formatStartAndEndDate(period);
          // Leave Period start/end dates
          data["Cont date 1"] = startDate;
          data["Cont date 2"] = endDate;
          break;
        }
        case "intermittent": {
          const period = getPeriodFromLeaveDetails(
            "intermittent_leave_periods",
            leaveDetails
          );
          const { startDate, endDate } = formatStartAndEndDate(period);
          const { frequency, frequencyType, duration, durationType } =
            formatIntermittentDetails(period);
          // Question 15
          data["Inter date 1"] = startDate;
          data["Inter date 2"] = endDate;
          if (frequency) {
            data["Number of times"] = frequency;
          }
          // Number of hours represents all kinds of duration length
          if (duration) {
            data["Number of hours"] = duration;
          }
          if (frequencyType) {
            data[frequencyType] = "On";
          }
          if (durationType) {
            data[durationType] = "On";
          }
          break;
        }
        case "reduced_schedule": {
          const period = getPeriodFromLeaveDetails(
            "reduced_schedule_leave_periods",
            leaveDetails
          );
          const { startDate, endDate } = formatStartAndEndDate(period);
          data["Reduced date 1"] = startDate;
          data["Reduced date 2"] = endDate;
          // Hours off.
          data["Reduced hours"] = `${sumTotalHoursOff(period)} hrs a week`;
          break;
        }
      }
    }
    return data;
  }
}

function resolveChildBirthDate(
  leaveDetails: ApplicationLeaveDetails,
  expectedDateOfBirth?: Date | "leaveEnd" | "leaveStart"
) {
  switch (expectedDateOfBirth) {
    case "leaveEnd": {
      return extractEarliestEndDate(leaveDetails);
    }
    case "leaveStart": {
      return extractEarliestStartDate(leaveDetails);
    }
    default: {
      // Equivalent to "leaveEnd" if date is not provided.
      return expectedDateOfBirth ?? extractEarliestEndDate(leaveDetails);
    }
  }
}

function resolvePregnancyCareWeeks(careWeeks = 0) {
  return careWeeks.toString();
}
