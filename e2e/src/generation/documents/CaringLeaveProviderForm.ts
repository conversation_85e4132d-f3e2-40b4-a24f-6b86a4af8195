import { faker } from "@faker-js/faker";
import { differenceInWeeks, format, parseISO, subYears } from "date-fns";

import {
  ApplicationRequestBody,
  ContinuousLeavePeriods,
  IntermittentLeavePeriods,
  ReducedScheduleLeavePeriods,
} from "../../api";
import {
  formatIntermittentDetails,
  getAvailableLeaveScheduleTypes,
  getPeriodFromLeaveDetails,
  leaveScheduleTypes,
  sumTotalHoursOff,
} from "../../util/claims";
import {
  AbstractDocumentGenerator,
  PDFFormData,
} from "./AbstractDocumentGenerator";

type CaringLeaveProviderFormConfig = {
  invalid?: boolean;
  leaveStartDateBlank?: boolean;
};

export default class CaringLeaveProviderForm extends AbstractDocumentGenerator<CaringLeaveProviderFormConfig> {
  documentSource(): string {
    return this.path("caring-v1.1.2.pdf");
  }
  getFormData(
    claim: ApplicationRequestBody,
    config: CaringLeaveProviderFormConfig
  ): PDFFormData {
    if (!claim.first_name || !claim.last_name || !claim.date_of_birth) {
      throw new Error("Unable to generate document due to missing properties");
    }
    // Note: To debug this PDF's fields, follow this: https://stackoverflow.com/a/38257183
    const dob = parseISO(claim.date_of_birth);
    const familyMemberDOB = parseISO(
      claim.leave_details?.caring_leave_metadata
        ?.family_member_date_of_birth as string
    );
    const conditionStart = subYears(new Date(), 2);
    const data: PDFFormData = {
      // Employee Info - Section 1
      "Employee Name": `${claim.first_name} ${claim.last_name}`, // employee at top of pages
      "Employee name": `${claim.first_name} ${claim.last_name}`, // employee at top of page (different FN)
      "Employee first name": `${claim.first_name}`,
      "Employee Last name": `${claim.last_name}`,
      "Emp. DOB mm": format(dob, "MM"),
      "Emp. DOB dd": format(dob, "dd"),
      "Emp. DOB yyyy": config.invalid ? "" : format(dob, "yyyy"),
      "Emp. SSI last 4": `${
        config.invalid ? "" : claim.tax_identifier?.slice(7)
      }`,
      "Why are you applying for leave?":
        "To care for a family member with a serious health condition",

      // Family member information - Section 2
      "The family member who is experiencing a serious health condition is my:":
        "Sibling",
      "Family member name: First": claim.leave_details?.caring_leave_metadata
        ?.family_member_first_name as string,
      "Family member name: Last": claim.leave_details?.caring_leave_metadata
        ?.family_member_last_name as string,
      "Family member address: Street:": faker.location.streetAddress(),
      "Family member address: City:": faker.location.city(),
      "Family member address: State:": faker.location.state({
        abbreviated: true,
      }),
      "Family member address: Zipcode:": faker.location.zipCode("#####"),
      "Family member address: Country:": "United States",
      "Family member's date of birth: MM": format(familyMemberDOB, "MM"),
      "Family member's date of birth: DD": format(familyMemberDOB, "dd"),
      "Family member's date of birth: yyyy": format(familyMemberDOB, "yyyy"),
      "Employee signature date: MM": format(new Date(), "MM"),
      "Employee signature date: DD": format(new Date(), "dd"),
      "Employee signature date: yyyy": format(new Date(), "yyyy"),

      // Health Condition - Section 3
      // "Does the family member (your patient) have a serious health condition": "Yes",
      // "Does the family member have serious health condition": "Yes",
      "Does the family member (your patient) have a serious health condition as defined by the criteria on page 2?":
        "Yes",
      "Requires, or did require inpatient care": "Yes",
      "Requires one medical visit, plus a regimen of care": "Yes",
      "Condition start mm": format(conditionStart, "MM"),
      "Condition start dd": format(conditionStart, "dd"),
      "Conditiion start yyyy": format(conditionStart, "yyyy"),
      "Is this health condition related to the patient's military service?":
        "No",
      "Medical facts": "Patient has limited mobility and needs assistance.",
      "Describe the kinds of care related to the patient's condition that the employee will provide":
        "Patient has limited mobility and needs assistance.",
      "Will the employee be required to take leave to care for the patient?":
        "Yes",

      // Estimate Leave Details - Section 4
      "Continuous leave": claim.has_continuous_leave_periods ? "On" : false,
      "Reduced leave schedule": claim.has_reduced_schedule_leave_periods
        ? "On"
        : false,
      "Intermittent leave": claim.has_intermittent_leave_periods ? "On" : false,

      // Practitioner data.
      "Provider name": "Theodore Cure",
      "Provider Title": "MD",
      License: "[assume license is valid]",
      "Area of practice": "General Medicine",
      "Practice name": "[assume business is valid]",
      "Office Phone 1": "555",
      "Office Phone 2": "555",
      "Office phone 3": "5555",
      // On multiple pages
      "Healthcare Provider Initials": "TC",
    };

    const leaveDetails = claim.leave_details;

    if (!leaveDetails) {
      throw new Error("No leave details available");
    }

    const availableScheduleTypes = getAvailableLeaveScheduleTypes(leaveDetails);
    for (const leaveType of availableScheduleTypes) {
      switch (leaveType) {
        case "continuous": {
          const period = getPeriodFromLeaveDetails(
            "continuous_leave_periods",
            leaveDetails
          );

          const { startDate, endDate } = getStartAndEndDate(period);

          // Section 4A
          data["Weeks of continuous leave"] = differenceInWeeks(
            endDate,
            startDate
          ).toString();
          // Leave Period start/end dates
          if (!config.leaveStartDateBlank) {
            data["Continuous start mm"] = format(startDate, "MM");
            data["Continuous start dd"] = format(startDate, "dd");
            data["Continuous start yyyy"] = format(startDate, "yyyy");
          }

          data["Continuous end mm"] = format(endDate, "MM");
          data["Continuous end dd"] = format(endDate, "dd");
          data["Continuous end yyyy"] = format(endDate, "yyyy");
          break;
        }
        case "intermittent": {
          const period = getPeriodFromLeaveDetails(
            "intermittent_leave_periods",
            leaveDetails
          );

          const { startDate, endDate } = getStartAndEndDate(period);

          // Section 4C
          const { duration, durationType, frequency, frequencyType } =
            formatIntermittentDetails(period);
          if (!config.leaveStartDateBlank) {
            data["Intermittent start mm"] = format(startDate, "MM");
            data["Intermittent start dd"] = format(startDate, "dd");
            data["Intermittent start yyyy"] = format(startDate, "yyyy");
          }

          data["Intermittent end mm"] = format(endDate, "MM");
          data["Intermittent end dd"] = format(endDate, "dd");
          data["Intermittent end yyyy"] = format(endDate, "yyyy");
          // Question 28
          if (
            frequency &&
            (frequencyType === "Month" ||
              frequencyType === "Day" ||
              frequencyType === "Week")
          ) {
            setFrequency(data, frequency, frequencyType);
          }
          // Question 29 - How long will a single absence last?
          if (
            duration &&
            (durationType === "hours" || durationType === "days")
          ) {
            setDuration(data, duration, durationType);
          }
          break;
        }
        case "reduced_schedule": {
          const period = getPeriodFromLeaveDetails(
            "reduced_schedule_leave_periods",
            leaveDetails
          );

          const { startDate, endDate } = getStartAndEndDate(period);

          // Section 4B
          // Weeks of reduced schedule.
          data["Weeks of a reduced leave schedule"] = differenceInWeeks(
            endDate,
            startDate
          ).toString();

          if (!config.leaveStartDateBlank) {
            data["Reduced start mm"] = format(startDate, "MM");
            data["Reduced start dd"] = format(startDate, "dd");
            data["Reduced start yyyy"] = format(startDate, "yyyy");
          }

          data["Reduced end mm"] = format(endDate, "MM");
          data["Reduced end dd"] = format(endDate, "dd");
          data["Reduced end yyyy"] = format(endDate, "yyyy");

          // Hours off.
          data["Hours of reduced leave schedule"] = sumTotalHoursOff(period);
          break;
        }
      }
    }

    const missingScheduleTypes = leaveScheduleTypes.filter(
      (leaveType) => !availableScheduleTypes.includes(leaveType)
    );

    for (const leaveType of missingScheduleTypes) {
      switch (leaveType) {
        case "continuous": {
          data["No continuous leave needed"] = "On";
          break;
        }
        case "intermittent": {
          data["Absence length"] = "N/A";
          break;
        }
        case "reduced_schedule": {
          data["no reduced leave schedule needed"] = "Yes";
          data["No reduced leave schedule needed"] = "On";
          break;
        }
      }
    }
    return data;
  }
}

function setFrequency(
  data: PDFFormData,
  frequency: string,
  frequencyIntervalBasis: "Month" | "Week" | "Day"
) {
  switch (frequencyIntervalBasis) {
    case "Month": {
      data["Absences"] = "Once per month";
      data["Times per month"] = frequency;
      break;
    }
    case "Week":
    default: {
      data["Absences"] = "Once per week";
      data["Times per week"] = frequency;
    }
  }
}

function setDuration(
  data: PDFFormData,
  duration: string,
  durationBasis: "hours" | "days"
) {
  switch (durationBasis) {
    case "hours": {
      // "More than 1 day" does not make sense with the field name
      // "Absence length: Less than one full work day, up to", but it is the
      // correct value for this field in the PDF.
      data["Absence length: Less than one full work day, up to"] =
        "More than 1 day";
      data["Hours"] = duration;
      break;
    }
    case "days": {
      // "Less than 1 day" does not make sense with the field name
      // "Absence length: At least one day, up to", but it is the correct value
      // for this field in the PDF.
      data["Absence length: At least one day, up to"] = "Less than 1 day";
      data["Days"] = duration;
      break;
    }
  }
}

function getStartAndEndDate(
  period:
    | ContinuousLeavePeriods
    | IntermittentLeavePeriods
    | ReducedScheduleLeavePeriods
) {
  if (!period.start_date) {
    throw new Error(`Leave period does not have a start date`);
  }

  if (!period.end_date) {
    throw new Error(`Leave period does not have a end date`);
  }

  return {
    startDate: parseISO(period.start_date),
    endDate: parseISO(period.end_date),
  };
}
