import { faker } from "@faker-js/faker";
import {
  add,
  addDays,
  differenceInDays,
  format as formatDate,
  formatISO,
  max as maxDate,
  min as minDate,
  parseISO,
  subDays,
  subMonths,
} from "date-fns";

import {
  ApplicationLeaveDetails,
  ContinuousLeavePeriods,
  DayOfWeek,
  IntermittentLeavePeriods,
  ReducedScheduleLeavePeriods,
  WorkPattern,
} from "../api";
import { ClaimSpecification, LeaveReason } from "./Claim";

export default function generateLeaveDetails(
  config: ClaimSpecification,
  work_pattern: WorkPattern
): ApplicationLeaveDetails {
  const { reason, reason_qualifier } = config;
  const has_continuous_leave_periods =
    config.has_continuous_leave_periods ??
    (!config.reduced_leave_spec && !config.intermittent_leave_spec);

  const leaveDates = resolveLeaveDates(
    config.leave_dates ?? null,
    work_pattern,
    {
      isIntermittentLeave: !!config.intermittent_leave_spec,
      isShortClaim: config.shortClaim,
    }
  );

  const details: ApplicationLeaveDetails = {
    continuous_leave_periods: has_continuous_leave_periods
      ? generateContinuousLeavePeriods(leaveDates)
      : [],
    reduced_schedule_leave_periods: config.reduced_leave_spec
      ? generateReducedLeavePeriods(config.reduced_leave_spec, leaveDates)
      : [],
    intermittent_leave_periods: config.intermittent_leave_spec
      ? generateIntermittentLeavePeriods(
          config.intermittent_leave_spec instanceof Function
            ? config.intermittent_leave_spec(leaveDates)
            : config.intermittent_leave_spec,
          leaveDates
        )
      : [],
    pregnant_or_recent_birth: !!config.pregnant_or_recent_birth,
    employer_notified: true,
    reason,
    reason_qualifier: reason_qualifier ?? null,
    caring_leave_metadata:
      reason === "Care for a Family Member" ||
      (reason as NonNullable<LeaveReason>) === "Military Caregiver"
        ? {
            relationship_to_caregiver: "Sibling - Brother/Sister",
            family_member_first_name: faker.person.firstName(),
            family_member_last_name: faker.person.lastName(),
            family_member_date_of_birth: formatISO(
              faker.date.birthdate({ mode: "age", min: 10, max: 85 }),
              { representation: "date" }
            ),
          }
        : undefined,
  };

  const earliestStartDate = getEarliestStartDate(details);
  const latestEndDate = getLatestEndDate(details);

  details.employer_notification_date = formatISO(
    generateNotificationDate(earliestStartDate, !!config.shortNotice),
    { representation: "date" }
  );

  switch (reason as NonNullable<LeaveReason>) {
    case "Serious Health Condition - Employee": {
      // Do nothing else.
      break;
    }
    case "Care for a Family Member": {
      // Do nothing else.
      break;
    }
    case "Pregnancy/Maternity": {
      details.pregnant_or_recent_birth = true;
      // Used for generating birth certificate if requested

      const dateOfBirthModification = config.metadata?.modifications?.find(
        (modification) => modification.date_of_birth
      );

      if (dateOfBirthModification !== undefined) {
        details.child_birth_date = dateOfBirthModification.date_of_birth;
      }

      break;
    }
    case "Child Bonding": {
      switch (reason_qualifier) {
        case "Newborn": {
          details.child_birth_date = makeChildPlacementDate(
            config.bondingDate,
            earliestStartDate,
            subDays(new Date(), 2)
          );
          details.pregnant_or_recent_birth = true;
          break;
        }
        case "Adoption": {
          details.child_placement_date = makeChildPlacementDate(
            config.bondingDate ?? "past",
            earliestStartDate,
            latestEndDate
          );
          break;
        }
        case "Foster Care": {
          details.child_placement_date = makeChildPlacementDate(
            config.bondingDate ?? "past",
            earliestStartDate,
            latestEndDate
          );
          break;
        }
        default: {
          throw new Error(`Invalid reason_qualifier for Child Bonding`);
        }
      }
      break;
    }
    //Fineos leave reasons
    case "Military Caregiver": {
      break;
    }
    case "Military Exigency Family": {
      break;
    }
    default: {
      throw new Error(`Invalid reason given`);
    }
  }
  return details;
}

const formatISODate = (date: Date) =>
  formatISO(date, { representation: "date" });

/**
 * Generate start and end dates for a leave request.
 *
 * Generated dates meet the following conditions:
 *   * Start date > max(2021-01-01, today) and < today + 60 days
 *   * End date < Start Date + 20 weeks.
 *   * Start date will always fall on a work day for this employee.
 */
export function generateLeaveDates(
  workPattern: WorkPattern,
  duration?: Duration,
  earliestStart = "2021-01-01"
): [Date, Date] {
  // Start date must be greater than max(2021-01-01 or today).
  const minStartDate = maxDate([parseISO(earliestStart), new Date()]);
  // Start date must be < 60 days from the application date (now).
  const maxStartDate = addDays(new Date(), 60);

  const workingDays = (workPattern.work_pattern_days || [])
    .filter((day) => (day.minutes ?? 0) > 0)
    .map((day) => day.day_of_week);

  let i = 0;
  while (i++ < 100) {
    const startDate = faker.date.between({
      from: minStartDate,
      to: maxStartDate,
    });
    // Leave dates MUST involve at least one day that falls in the work pattern.
    if (workingDays.includes(formatDate(startDate, "iiii") as DayOfWeek)) {
      // Allow duration overrides.
      const addition = duration ?? {
        weeks: faker.number.int({ min: 1, max: 11 }),
      };
      return [startDate, add(startDate, addition)];
    }
  }
  throw new Error("Unable to generate leave dates for this employee");
}

function generateContinuousLeavePeriods(
  leave_dates: readonly [Date, Date]
): ContinuousLeavePeriods[] {
  const [startDate, endDate] = leave_dates;
  return [
    {
      start_date: formatISO(startDate, { representation: "date" }),
      end_date: formatISO(endDate, { representation: "date" }),
    },
  ];
}

type IntermittentLeaveSpec =
  | Partial<IntermittentLeavePeriods>
  | Partial<IntermittentLeavePeriods>[]
  | true;

function generateIntermittentLeavePeriods(
  periods: IntermittentLeaveSpec,
  leave_dates: readonly [Date, Date]
): IntermittentLeavePeriods[] {
  const [startDate, endDate] = leave_dates;
  const diffInDays = differenceInDays(endDate, startDate);
  const defaults: IntermittentLeavePeriods = {
    start_date: formatISO(startDate, { representation: "date" }),
    end_date: formatISO(endDate, { representation: "date" }),
    duration: faker.number.int({ min: 1, max: Math.min(diffInDays, 7) }),
    duration_basis: "Days",
    frequency: 1,
    frequency_interval: 1,
    frequency_interval_basis: "Weeks",
  };
  if (periods === true) {
    return [defaults];
  } else if (!Array.isArray(periods)) {
    return [{ ...defaults, ...periods }];
  } else {
    return periods.map((period) => ({ ...defaults, ...period }));
  }
}

function generateReducedLeavePeriods(
  spec: string | ReducedScheduleLeavePeriods[],
  leave_dates: readonly [Date, Date]
): ReducedScheduleLeavePeriods[] {
  if (typeof spec === "string") {
    const [startDate, endDate] = leave_dates;

    const minsByDay = spec
      // Split the spec into weeks (even though we only care about 1 week at a time here).
      .split(";")
      // Split the week into days and parse the minutes off.
      .map((weekSpec) => weekSpec.split(",").map((n) => parseInt(n)))
      // Only look at the first wek.
      .pop();

    if (!minsByDay || minsByDay.find(isNaN)) {
      throw new Error(`Invalid reduced leave specification: ${spec}`);
    }

    const getDayOffMinutes = (dayNumber: number) =>
      dayNumber in minsByDay ? minsByDay[dayNumber] : 0;

    return [
      {
        start_date: formatISO(startDate, { representation: "date" }),
        end_date: formatISO(endDate, { representation: "date" }),
        sunday_off_minutes: getDayOffMinutes(0),
        monday_off_minutes: getDayOffMinutes(1),
        tuesday_off_minutes: getDayOffMinutes(2),
        wednesday_off_minutes: getDayOffMinutes(3),
        thursday_off_minutes: getDayOffMinutes(4),
        friday_off_minutes: getDayOffMinutes(5),
        saturday_off_minutes: getDayOffMinutes(6),
      },
    ];
  } else {
    return spec;
  }
}

function getEarliestStartDate(details: ApplicationLeaveDetails): Date {
  const leaveDates: Date[] = [];

  const leavePeriods = [
    details.continuous_leave_periods as ContinuousLeavePeriods[],
    details.reduced_schedule_leave_periods as ReducedScheduleLeavePeriods[],
    details.intermittent_leave_periods as IntermittentLeavePeriods[],
  ];
  leavePeriods.forEach((period) => {
    if (period === undefined || period.length < 1) {
      return;
    }
    leaveDates.push(parseISO(period[0].start_date as string));
  });
  if (leaveDates.length < 1) {
    throw new Error("No leave dates have been specified");
  }
  return minDate(leaveDates);
}

function getLatestEndDate(details: ApplicationLeaveDetails): Date {
  const leaveDates: Date[] = [];

  const leavePeriods = [
    details.continuous_leave_periods as ContinuousLeavePeriods[],
    details.reduced_schedule_leave_periods as ReducedScheduleLeavePeriods[],
    details.intermittent_leave_periods as IntermittentLeavePeriods[],
  ];
  leavePeriods.forEach((period) => {
    if (period === undefined || period.length < 1) {
      return;
    }
    leaveDates.push(parseISO(period[0].end_date as string));
  });
  if (leaveDates.length < 1) {
    throw new Error("No leave dates have been specified");
  }
  return maxDate(leaveDates);
}

function makeChildPlacementDate(
  spec: ClaimSpecification["bondingDate"],
  leaveStart: Date,
  leaveEnd: Date
): string {
  if (spec instanceof Date) {
    return formatISODate(spec);
  }
  switch (spec) {
    case "far-past": {
      return formatISODate(
        faker.date.between({
          from: subMonths(new Date(), 12),
          to: subMonths(new Date(), 11),
        })
      );
    }
    case "past": {
      // Recent birth date.
      return formatISODate(
        faker.date.between({
          from: subMonths(leaveEnd, 1),
          to: subDays(leaveEnd, 1),
        })
      );
    }
    case "future": {
      // A date after 01-02-2021, but no more than startDate
      return formatISODate(
        faker.date.between({ from: new Date(), to: leaveStart })
      );
    }
    default: {
      throw new Error(
        `Invalid bondingDate property given. You must set this property with one of "far-past", "past", "future", or Date `
      );
    }
  }
}

// Generate an employer notification date based on the claim start date.
// Optionally, generate a "short notice" date.
function generateNotificationDate(startDate: Date, shortNotice: boolean) {
  return add(startDate, {
    days: shortNotice ? -1 : -60,
  });
}

function resolveLeaveDates(
  leaveDates: (() => [Date, Date]) | [Date, Date] | null,
  workPattern: WorkPattern,
  options: ResolveLeaveDatesOptions = {}
) {
  if (leaveDates instanceof Function) {
    return leaveDates();
  }

  if (leaveDates) {
    return leaveDates;
  }

  const { isShortClaim, isIntermittentLeave } = options;
  return generateLeaveDates(
    workPattern,
    isShortClaim ? { days: isIntermittentLeave ? 7 : 1 } : undefined
  );
}

interface ResolveLeaveDatesOptions {
  isIntermittentLeave?: boolean;
  isShortClaim?: boolean;
}
