import { faker } from "@faker-js/faker";
import { add, formatISO, subQuarters } from "date-fns";
import fs from "fs";
import { pipeline, Readable } from "stream";
import { collect, concat } from "streaming-iterables";
import { promisify } from "util";

import * as JSONStream from "../stream/json";
import { getMinimumEligibleWages } from "../util/benefit";
import { generateSsn, generateValidPhoneNumber } from "../util/pii";
import { shuffle } from "../util/random";
import { quarters } from "../util/writers";
import EmployerPool, { Employer, EmployerGenerator } from "./Employer";
import unique from "./unique";

const pipelineP = promisify(pipeline);
interface PromiseWithOptionalGeneration<T> extends Promise<T> {
  orGenerateAndSave(gen: () => T): Promise<T>;
}

export type EmployeeOccupation = {
  employer: Employer;
  wages: QuarterlyWages;
};

export type Employee = {
  first_name: string;
  last_name: string;
  ssn: string;
  date_of_birth: string;
  occupations: EmployeeOccupation[];
  mass_id?: string | null;
  metadata?: Record<string, unknown>;
  phone_number?: string;
  child_support_obligation?: number;
};

export type PotentiallyConsumedEmployee = Employee & { used: string };

export type WageSpecification = FlatWageSpecification | QuarterlyWages;
type FlatWageSpecification = Wages | YearlyWageSpecification;

type Wages = "high" | "medium" | "low" | "eligible" | "ineligible" | number;

/**
 * The "ui" WageSource is also referred to as "DUA". They both represent wages
 * provided by the Department of Unemployment Assistance.
 */
type WageSource = "dfml" | "ui";

export type QuarterlyWages = {
  quarter: string;
  source?: WageSource;
  statutorilyExcluded?: boolean;
  wages: number;
}[];

interface YearlyWageSpecification {
  wages: WageSpecification;
  year: number;
}

const currentYear = new Date().getFullYear();

function getWageBounds(spec: FlatWageSpecification) {
  return getWageBoundsUtil(spec);
}

function sortQuarters(quarters: QuarterlyWages) {
  return [...quarters].sort((q1, q2) => q1.quarter.localeCompare(q2.quarter));
}

// Generate the min/max bounds for yearly wages.
function getWageBoundsUtil(spec: FlatWageSpecification) {
  if (typeof spec === "number") {
    return [spec, spec];
  }

  const isYearlyWageSpec = typeof spec === "object";
  const wages = isYearlyWageSpec ? spec.wages : spec;
  const year = isYearlyWageSpec ? spec.year : currentYear;
  const minimumEligibleWages = getMinimumEligibleWages(year);

  switch (wages) {
    case "ineligible":
      return [2000, minimumEligibleWages - 1];
    case "eligible":
      return [minimumEligibleWages, 100000];
    case "high":
      return [90000, 100000];
    case "medium":
      return [30000, 90000];
    case "low":
      return [minimumEligibleWages, 30000];
    default:
      throw new Error(`Invalid wage specification: ${JSON.stringify(spec)}`);
  }
}

export function toAnnualWages(quarters: QuarterlyWages) {
  const sortedQuarters = sortQuarters(quarters);
  const latestQuarter = sortedQuarters[sortedQuarters.length - 1];
  const newestInvalidQuarter = subQuarters(new Date(latestQuarter.quarter), 4);
  const validQuarters = sortedQuarters.filter(
    (q) => new Date(q.quarter) > newestInvalidQuarter
  );
  return validQuarters.reduce((agg, quarter) => agg + quarter.wages, 0);
}

function toQuarterlyWages(wages: number) {
  return quarters().map((q) => {
    return {
      wages: wages / 4,
      quarter: formatISO(q, { representation: "date" }),
    };
  });
}

function wagesInBounds(wages: number, spec: FlatWageSpecification): boolean {
  const [min, max] = getWageBounds(spec);
  return wages >= min && wages <= max;
}

export type EmployeeGenerationSpec = {
  // Defaults to true.
  mass_id?: boolean;
  wages?: WageSpecification;
  child_support_obligation?: number;
  metadata?: Record<string, unknown>;
};

export class EmployeeGenerator {
  static generate(
    employerPool: EmployerPool,
    spec: EmployeeGenerationSpec
  ): Employee {
    const occupations = this.generateOccupations(employerPool, spec.wages);
    return this.generateWithOccupations(occupations, spec);
  }

  static generateWithOccupations(
    occupations: EmployeeOccupation[],
    spec: EmployeeGenerationSpec
  ): Employee {
    const occupationsWithSortedWages = occupations.map((occupation) => ({
      ...occupation,
      wages: sortQuarters(occupation.wages),
    }));

    return {
      first_name: faker.person.firstName(),
      last_name: faker.person.lastName(),
      ssn: this.generateSSN(),
      date_of_birth: this.generateDateOfBirth(),
      occupations: occupationsWithSortedWages,
      mass_id: spec.mass_id ?? true ? this.generateMassId() : null,
      metadata: spec.metadata,
      phone_number: this.generatePhoneNumber(),
      child_support_obligation: spec.child_support_obligation,
    };
  }

  public static generateSSN = unique(generateSsn);

  private static generateOccupations(
    employerPool: EmployerPool,
    wages?: EmployeeGenerationSpec["wages"]
  ): EmployeeOccupation[] {
    if (Array.isArray(wages)) {
      const occupations: EmployeeOccupation[] = [];

      const standardWages = wages.filter((wage) => !wage.statutorilyExcluded);

      if (standardWages.length > 0) {
        occupations.push({
          employer: employerPool.pick({ statutorilyExcluded: false }),
          wages: standardWages,
        });
      }

      const excludedWages = wages.filter((wage) => wage.statutorilyExcluded);

      if (excludedWages.length > 0) {
        occupations.push({
          employer: employerPool.pick({ statutorilyExcluded: true }),
          wages: excludedWages,
        });
      }

      return occupations;
    }

    return [
      {
        employer: employerPool.pick(),
        wages: this.generateAllWages(wages),
      },
    ];
  }

  static generateAllWages(
    spec: EmployeeGenerationSpec["wages"] = "eligible"
  ): QuarterlyWages {
    if (spec instanceof Array) {
      return spec;
    }

    const annualWages = this.generateWages(spec);
    return toQuarterlyWages(annualWages);
  }

  private static generateWages(spec: FlatWageSpecification) {
    if (typeof spec === "number") {
      return spec;
    }
    const [min, max] = getWageBounds(spec);
    return faker.number.int({ min, max });
  }

  private static generateDateOfBirth(): string {
    // Generate a birthdate > 65 years ago and < 18 years ago.
    const dob = faker.date.between({
      from: add(new Date(), { years: -65 }),
      to: add(new Date(), { years: -18 }),
    });
    return formatISO(dob, { representation: "date" });
  }

  // Generate a Mass ID number.
  static generateMassId(): string {
    return faker.helpers.arrayElement([
      "S" + faker.string.numeric(8),
      "SA" + faker.string.numeric(7),
    ]);
  }

  static generatePhoneNumber(): string {
    const areaCodes = ["800", "888"];
    const randomArea = faker.helpers.arrayElement(areaCodes);
    return generateValidPhoneNumber(`${randomArea}-!##-####`);
  }
}

export type EmployeePickSpec = {
  // Whether or not the employee has a massachussetts ID #.
  mass_id?: boolean;
  // A specific wage specification that needs to be matched (# or level name).
  wages?: WageSpecification;
  fein?: string;
  metadata?: Record<string, unknown>;
};

export interface LoadOptions {
  employerFilePath?: string;
  usedFilePath?: string;
}

export default class EmployeePool implements Iterable<Employee> {
  used: Set<string>;
  /**
   * Generate a new pool.
   */
  static generate(
    count: number,
    employers: EmployerPool,
    spec: EmployeeGenerationSpec
  ): EmployeePool {
    const employees = [...new Array(count)].map(() =>
      EmployeeGenerator.generate(employers, spec)
    );
    return new this(employees);
  }

  static generateWithOccupations(
    count: number,
    spec: EmployeeGenerationSpec,
    occupations: EmployeeOccupation[]
  ) {
    const employees = [...new Array(count)].map(() =>
      EmployeeGenerator.generateWithOccupations(occupations, spec)
    );
    return new this(employees);
  }

  /**
   * Load a pool from a JSON file.
   */
  static load(
    employeeFilePath: string,
    options: LoadOptions = {}
  ): PromiseWithOptionalGeneration<EmployeePool> {
    // Do not use streams here. Interestingly, the perf/memory usage of this was tested,
    // and raw read/parse is faster and more efficient than the streams equivalent. ¯\_(ツ)_/¯
    const loadPromise = (async () => {
      const rawEmployees = await fs.promises.readFile(
        employeeFilePath,
        "utf-8"
      );
      const employees: DehydratedEmployee[] = JSON.parse(rawEmployees);
      const employers = await getRelatedEmployers(
        employeeFilePath,
        options.employerFilePath
      );
      const used: string[] = options.usedFilePath
        ? JSON.parse(await fs.promises.readFile(options.usedFilePath, "utf-8"))
        : [];
      return new this(hydrateEmployees(employees, employers), used);
    })();

    return Object.assign(loadPromise, {
      orGenerateAndSave: async (generator: () => EmployeePool) => {
        // When orGenerateAndSave() is called, return a new promise that adds a catch() to
        // the load promise to regenerate and save the data _only if the initial load fails_
        return loadPromise.catch(async (e) => {
          if (e.code !== "ENOENT") {
            return Promise.reject(e);
          }
          // Invoke the generator here, and save the pool it returns to `filename`
          const pool = generator();
          await pool.save(employeeFilePath, options.usedFilePath);
          return await EmployeePool.load(employeeFilePath, options);
        });
      },
    });
  }

  /**
   * Merge one or more pools together into a megapool.
   *
   * @param pools
   */
  static merge(...pools: Iterable<Employee>[]): EmployeePool {
    return new this(collect(concat(...pools)));
  }

  constructor(private employees: Employee[], used?: string[]) {
    this.used = new Set<string>(used);
  }

  /**
   * Save the pool to JSON format.
   */
  async save(filename: string, usedFileName?: string): Promise<void> {
    // Use streams for memory efficiency.
    await pipelineP(
      Readable.from(dehydrateEmployees(this.employees)),
      JSONStream.stringify(),
      fs.createWriteStream(filename)
    );

    if (usedFileName) {
      await pipelineP(
        Readable.from(this.used),
        JSONStream.stringify(),
        fs.createWriteStream(usedFileName)
      );
    }
  }

  *consumptionStatus(): Iterable<PotentiallyConsumedEmployee> {
    for (const e of this.employees) {
      yield { ...e, used: this.used.has(e.ssn) ? "Yes" : "No" };
    }
  }

  [Symbol.iterator](): Iterator<Employee> {
    return this.employees[Symbol.iterator]();
  }

  private _createFinder(
    spec: EmployeePickSpec
  ): (employee: Employee) => boolean {
    return (e) => {
      const wageSpec = spec.wages;
      const { metadata } = spec;
      // For each condition in the pick spec, return false if it is not met for this employee.

      if (wageSpec !== undefined) {
        const allWages = e.occupations.flatMap(
          (occupation) => occupation.wages
        );

        if (Array.isArray(wageSpec)) {
          const specDoesNotHaveCorrespondingWage = (
            spec: QuarterlyWages[number]
          ) => !allWages.some((wage) => areEqualWages(wage, spec));

          const wageDoesNotHaveCorrespondingSpec = (
            wage: QuarterlyWages[number]
          ) => !wageSpec.some((spec) => areEqualWages(spec, wage));

          if (
            wageSpec.some(specDoesNotHaveCorrespondingWage) ||
            allWages.some(wageDoesNotHaveCorrespondingSpec)
          ) {
            return false;
          }
        } else if (!wagesInBounds(toAnnualWages(allWages), wageSpec)) {
          return false;
        }
      }

      if (spec.fein && spec.fein !== e.occupations[0].employer.fein) {
        return false;
      }

      if (spec.mass_id && !e.mass_id) {
        return false;
      }

      if (spec.mass_id === false && e.mass_id) {
        return false;
      }

      if (metadata) {
        for (const [k, v] of Object.entries(metadata)) {
          if (!e.metadata || e.metadata[k] !== v) {
            return false;
          }
        }
      }
      // If we've met all conditions, this is a matching employee.
      return true;
    };
  }

  /**
   * Filter the pool to a set that matches the specification.
   *
   * This will not modify the source pool, but will return a new "sub pool" containing only the matching employees.
   */
  filter(spec: EmployeePickSpec): EmployeePool {
    return new EmployeePool(this.employees.filter(this._createFinder(spec)));
  }

  /**
   * Shuffle the pool, randomizing the order.
   */
  shuffle(): EmployeePool {
    return new EmployeePool(shuffle(this.employees));
  }

  /**
   * Slice a number of Employees out of the pool.
   *
   * This will not remove these employees from the source pool, but will return a new "sub pool" containing only
   * those employees.
   */
  slice(start?: number, end?: number): EmployeePool {
    return new EmployeePool(this.employees.slice(start, end));
  }

  /**
   * Find one employee matching the specification.
   *
   * @param spec
   */
  find(spec: EmployeePickSpec): Employee | undefined {
    return this.employees.find(this._createFinder(spec));
  }

  getFirst(): Employee {
    return this.employees[0];
  }
  /**
   * Pick a single employee from the pool.
   *
   * Note: this method tracks employee usage to avoid picking the same employee twice in a single data
   * generation session.
   */
  pick(spec: EmployeePickSpec = {}): Employee {
    const finder = this._createFinder(spec);
    const employee = shuffle(this.employees).find((employee) => {
      return !this.used.has(employee.ssn) && finder(employee);
    });
    // Track employee usage.
    if (employee) {
      // Track the employee's usage.
      this.used.add(employee.ssn);
      return employee;
    }
    throw new Error(
      `No employee is left matching the specification: ${JSON.stringify(spec)}`
    );
  }

  getTaxIdentifers(): string[] {
    return this.employees.map((employee) => employee.ssn);
  }
}

function areEqualWages(
  wageA: QuarterlyWages[number],
  wageB: QuarterlyWages[number]
) {
  return (
    wageA.quarter === wageB.quarter &&
    wageA.source === wageB.source &&
    wageA.statutorilyExcluded === wageB.statutorilyExcluded &&
    wageA.wages === wageB.wages
  );
}

type DehydratedEmployee = Omit<Employee, "occupations"> & {
  occupations: DehydratedEmployeeOccupation[];
};

type DehydratedEmployeeOccupation = Omit<EmployeeOccupation, "employer"> & {
  fein: string;
  wages: number | QuarterlyWages;
};

function dehydrateEmployees(
  employees: readonly Employee[]
): DehydratedEmployee[] {
  return employees.map((employee) => ({
    ...employee,
    occupations: employee.occupations.map(dehydrateEmployeeOccupation),
  }));
}

function dehydrateEmployeeOccupation(
  occupation: EmployeeOccupation
): DehydratedEmployeeOccupation {
  return {
    fein: occupation.employer.fein,
    wages: occupation.wages,
  };
}

/**
 * Gets the most likely employer file path based on the employee file path.
 */
function getInferredEmployerFilePath(employeeFilePath: string) {
  if (employeeFilePath.includes("employees")) {
    return employeeFilePath.replace(/employees/g, "employers");
  }

  throw new Error(
    `Unable to infer employer file path for "${employeeFilePath}".`
  );
}

async function getRelatedEmployers(
  employeeFilePath: string,
  employerFilePath?: string
) {
  const resolvedFilePath =
    employerFilePath ?? getInferredEmployerFilePath(employeeFilePath);
  const rawEmployers = await tryToGetRawEmployers(resolvedFilePath);
  return JSON.parse(rawEmployers).map(EmployerGenerator.hydrate) as Employer[];
}

function hydrateEmployees(
  employees: readonly DehydratedEmployee[],
  employers: readonly Employer[]
): Employee[] {
  return employees.map((employee) => ({
    ...employee,
    occupations: employee.occupations.map((occupation) =>
      hydrateEmployeeOccupation(occupation, employers)
    ),
  }));
}

function hydrateEmployeeOccupation(
  occupation: DehydratedEmployeeOccupation,
  employers: readonly Employer[]
): EmployeeOccupation {
  const employer = employers.find(
    (employer) => employer.fein === occupation.fein
  );

  if (!employer) {
    throw new Error(`Unable to find Employer with FEIN "${occupation.fein}".`);
  }

  // Employee occupation records created before PFMLPB-22223 was merged define wages as an integer instead of by quarter.
  const wages =
    occupation.wages instanceof Array
      ? occupation.wages
      : toQuarterlyWages(occupation.wages);

  return { employer, wages };
}

async function tryToGetRawEmployers(filePath: string) {
  try {
    return await fs.promises.readFile(filePath, "utf-8");
  } catch (exception: unknown) {
    throw new Error(
      `Unable to read Employers from "${filePath}". This path may have been ` +
        "inferred. If so, try passing an explicit employer file path to " +
        `EmployeePool.load(). ${exception}`
    );
  }
}
