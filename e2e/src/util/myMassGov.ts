import { addSeconds, secondsToMilliseconds, toDate } from "date-fns";
import { chromium, Page } from "playwright-chromium";

import { authorizeIdvPage } from "../../playwright/utils/idv";
import { Address, ApplicationRequestBody } from "../_api";
import config from "../config";
import portalFeatureFlags from "../portalFeatureFlags";
import TestMailVerificationFetcher from "../submission/TestMailVerificationFetcher";
import { Credentials } from "../types";
import { setAuthenticatorKey } from "./authenticatorKeys";
import AutoCache from "./AutoCache";
import {
  extractMmgAuthenticatorKey,
  getAuthenticatorCode,
} from "./credentials";
import { generateName } from "./pii";
import { assertIsNotNull, assertIsString, assertValidClaim } from "./typeUtils";

/**
 * Gets serialized Portal OAuth state for a claimant.
 *
 * OAuth state contains an API access token and other data about the claimant’s
 * authentication status.
 *
 * This is a useful workaround for the limitations of Cypress with MyMassGov. It
 * must be called from the Cypress backend and propagated to the frontend
 * through inter-process communication.
 */
export async function getClaimantOAuthState(credentials: Credentials) {
  const key: CacheKey = { credentials, user: "Employee" };
  return getOAuthStateFromCache(key);
}

/**
 * Gets a Portal API access token for a claimant.
 */
export async function getClaimantToken(credentials: Credentials) {
  const key: CacheKey = { credentials, user: "Employee" };
  return getTokenFromCache(key);
}

/**
 * Gets serialized Portal OAuth state for a leave administrator.
 *
 * OAuth state contains an API access token and other data about the leave
 * administrator’s authentication status.
 *
 * This is a useful workaround for the limitations of Cypress with MyMassGov. It
 * must be called from the Cypress backend and propagated to the frontend
 * through inter-process communication.
 */
export async function getLeaveAdminOAuthState(credentials: Credentials) {
  const key: CacheKey = { credentials, user: "Leave Administrator" };
  return getOAuthStateFromCache(key);
}

/**
 * Gets a Portal API access token for a leave administrator.
 */
export async function getLeaveAdminToken(credentials: Credentials) {
  const key: CacheKey = { credentials, user: "Leave Administrator" };
  return getTokenFromCache(key);
}

/**
 * Registers a claimant.
 *
 * Will link an existing MyMassGov account to PFML in the current environment or
 * perform initial MyMassGov registration if the account does not exist.
 */
export async function registerClaimant(
  credentials: Credentials,
  options: RegistrationOptions = {}
) {
  return linkOrRegister(credentials, "Employee", options);
}

/**
 * Registers a leave admin.
 *
 * Will link an existing MyMassGov account to PFML in the current environment or
 * perform initial MyMassGov registration if the account does not exist.
 */
export async function registerLeaveAdmin(
  credentials: Credentials,
  options: RegistrationOptions = {}
) {
  return linkOrRegister(credentials, "Leave Administrator", options);
}

export interface RegistrationOptions {
  /**
   * When `true`, captures the authenticator key for this account on initial
   * MyMassGov registration to allow for future multi-factor authentication.
   *
   * Generally, if this account is intended for long-term use or testing
   * integrations with account-level features of MyMassGov, this should be set
   * to `true`.
   *
   * Does nothing if the account is already registered with MyMassGov and only
   * needs to link in a new PFML environment.
   *
   * Defaults to `false`.
   */
  captureAuthenticatorKey?: boolean;
  /**
   * The name used by MyMassGov, unrelated to PFML.
   *
   * Does nothing if the account is already registered with MyMassGov and only
   * needs to link in a new PFML environment.
   *
   * Defaults to a randomly generated name.
   */
  name?: Name;
}

async function cacheOAuthState(page: Page, key: CacheKey) {
  const oAuthState = await extractOAuthState(page);
  const data: OAuthStateData = {
    parsed: parseOAuthState(oAuthState),
    raw: oAuthState,
  };
  oAuthStateCache.set(key, data);
}

async function completeSetup(page: Page, username: string) {
  const askedToConsentToProfileSharing = await isAskedToConsentToProfileSharing(
    page
  );

  if (askedToConsentToProfileSharing) {
    await consentToProfileSharing(page);
    await waitForProcessing(page);
  }

  if (askedToConsentToProfileSharing && (await isSetupComplete(page))) {
    return;
  }

  await verifyEmail(page, username, false);

  await setUpMfa(page, username, {
    // It is not possible to know if the original client intended for this key
    // to be captured or not. Out of an abundance of caution, it is captured for
    // future use.
    captureAuthenticatorKey: true,
    fromIncomplete: true,
  });
}

function consentToProfileSharing(page: Page) {
  return page.getByLabel("Continue").click();
}

async function enterName(page: Page, name = generateName()) {
  await page.getByLabel("First Name").fill(name.first);
  await page.getByLabel("Last Name").fill(name.last);
  await page.getByLabel("Continue").click();
}

async function enterPassword(page: Page, password: string) {
  await page.locator("#newPassword").fill(password, { timeout: 30_000 });
  await page.locator("#reenterPassword").fill(password);
  await page.locator("#continue").click();
}

async function extractOAuthState(page: Page) {
  const storage = await page.context().storageState();
  const portalStorage = storage.origins.find(
    (item) => item.origin === portalBaseUrl
  );

  if (portalStorage === undefined) {
    throw new Error(`Could not find local storage for "${portalBaseUrl}".`);
  }

  const oauthState = portalStorage.localStorage.find(
    (item) => item.name === "OAUTH_STATE"
  );

  if (oauthState === undefined) {
    throw new Error(
      `Could not find OAUTH_STATE in local storage for "${portalBaseUrl}".`
    );
  }

  return oauthState.value;
}

async function fetchOAuthState(key: CacheKey) {
  const { credentials, user } = key;
  return withPortalPage(async (page) => {
    await navigateToMyMassGov(page, user);
    await logIn(page, credentials, user);
    const oAuthState = await extractOAuthState(page);
    const data: OAuthStateData = {
      parsed: parseOAuthState(oAuthState),
      raw: oAuthState,
    };
    return data;
  });
}

async function getOAuthStateFromCache(key: CacheKey) {
  const { raw } = await oAuthStateCache.get(key);
  return raw;
}

async function getPortalPage() {
  const browser = await chromium.launch({
    executablePath: process.env.PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH,
  });

  try {
    const page = await browser.newPage({
      viewport: { width: 1_200, height: 1_000 },
    });
    await page.context().addCookies([
      {
        name: "_ff",
        url: portalBaseUrl,
        value: JSON.stringify(portalFeatureFlags),
      },
    ]);
    return page;
  } catch (exception: unknown) {
    browser.close();
    throw exception;
  }
}

function getTokenExpiration(data: OAuthStateData) {
  const { id_token_expires_in, not_before } = data.parsed.token_data;
  const validStartDate = toDate(secondsToMilliseconds(+not_before));
  return addSeconds(validStartDate, +id_token_expires_in);
}

async function getTokenFromCache(key: CacheKey) {
  const { parsed } = await oAuthStateCache.get(key);
  return parsed.token_data.id_token;
}

async function isAskedToConsentToProfileSharing(page: Page) {
  // The content of the page renders later than the page itself. Waiting for the
  // common heading that appears on each page to be visible allows checking for
  // the profile sharing consent prompt to happen at the right time, without
  // throwing an error if this is a different MyMassGov page.
  await page
    .getByRole("heading", { name: "MyMassGov", level: 1 })
    .waitFor({ state: "visible" });
  return page.getByText("Consent to share MyMassGov").isVisible();
}

async function isEmailOrPasswordIncorrect(page: Page, user: User) {
  const logInRequestUrl =
    user === "Employee"
      ? `${config("MMG_PERSONAL_BASE_URL")}/**/SelfAsserted*`
      : `${config("MMG_BUSINESS_BASE_URL")}/**/SelfAsserted*`;
  const response = await page.waitForResponse(logInRequestUrl);

  try {
    // MyMassGov returns a 200 status code even when there is a failure. The
    // body must be parsed to inspect the outcome.
    const parsedResponse: { message: string; status: string } =
      await response.json();

    return (
      parsedResponse.status === "400" &&
      parsedResponse.message.includes("The email or password is incorrect.")
    );
  } catch (exception: unknown) {
    // If there is no JSON to parse, then there is no message that the email or
    // password were incorrect.
    return !(
      exception instanceof Error &&
      exception.message.startsWith("response.json")
    );
  }
}

async function isSetupComplete(page: Page): Promise<boolean> {
  // After logging in, the user is either redirected back to the PFML
  // application (/oauth-return), or are still in MMG with pending account setup
  // (/CombinedSignInAndSignUp). If it does land in the PFML application, the
  // application will immediately redirect to the main application page.
  await page.waitForURL(/(oauth-return|CombinedSignInAndSignUp)/i);
  return page.url().includes(portalBaseUrl);
}

async function linkOrRegister(
  credentials: Credentials,
  user: User,
  options: RegistrationOptions
) {
  return withPortalPage(async (page) => {
    await navigateToMyMassGov(page, user);
    const key: CacheKey = { credentials, user };

    try {
      // If the user is already registered with MyMassGov, logging in will link
      // the existing MyMassGov account with PFML in the current environment.
      await logIn(page, credentials, user);
      await cacheOAuthState(page, key);
    } catch (exception: unknown) {
      if (
        exception instanceof Error &&
        exception.message.includes(MAY_NOT_BE_REGISTERED)
      ) {
        await register(page, credentials, options);
        await cacheOAuthState(page, key);
      } else {
        throw exception;
      }
    }
  });
}

async function logIn(page: Page, credentials: Credentials, user: User) {
  const { username, password } = credentials;
  await page
    .locator(user === "Employee" ? "#email" : "#signInName")
    .fill(username);
  await page.locator("#password").fill(password);
  await page.locator("button", { hasText: "Log in" }).click();

  if (await isEmailOrPasswordIncorrect(page, user)) {
    throw new Error(
      `Email or password is incorrect. ${MAY_NOT_BE_REGISTERED}.`
    );
  }

  // It’s possible that the now logged-in account still has some setup left.
  if (!(await isSetupComplete(page))) {
    await completeSetup(page, username);
  }

  await page.waitForURL(`${portalBaseUrl}/**`);
  await page.waitForLoadState("domcontentloaded");
  await page
    .getByRole("heading", {
      name: "Completing paid leave login from MyMassGov",
    })
    .waitFor({ state: "detached" });
}

export async function startAppAndVerifyIdentity(
  claim: ApplicationRequestBody,
  credentials: Credentials
) {
  return withPortalPage(async (page) => {
    const user = "Employee";
    await navigateToMyMassGov(page, user);
    await logIn(page, credentials, user);
    await startApplication(page);
    await verifyIdentity(page, claim);
    return await getClaimId(page);
  });
}

async function startApplication(page: Page) {
  await page.waitForLoadState("domcontentloaded");
  await page.getByRole("link", { name: "Start a new application" }).click();
  await page.getByRole("link", { name: "Create an application" }).click();
  await page.getByText("I understand and agree").click();
  await authorizeIdvPage(page);
  await page.getByLabel("Verify your identification").click();
  await page.waitForLoadState("domcontentloaded");
  await page
    .locator("div")
    .filter({ hasText: /^Edit and reverify Continue applying$/ })
    .getByRole("link")
    .click();
}

async function verifyIdentity(page: Page, claim: ApplicationRequestBody) {
  assertValidClaim(claim);
  assertIsNotNull(claim.mailing_address);
  assertIsNotNull(claim.phone?.phone_number);
  await page.getByRole("button", { name: "Not this time" }).click();
  await fillName(page, claim.first_name, claim.last_name);
  await fillDob(page, claim.date_of_birth);
  await fillPhone(page, claim.phone.phone_number);
  await fillAddress(page, claim.mailing_address);
  await fillSsn(page, claim.tax_identifier);
  if (claim.has_state_id) {
    assertIsString(claim.mass_id);
    await page.getByText("Yes", { exact: true }).click();
    await page.locator('input[name="mass_id"]').fill(claim.mass_id);
  } else {
    await page.getByText("No", { exact: true }).click();
  }
  await page.getByRole("button", { name: "Save and continue" }).click();
}

async function fillName(page: Page, firstName: string, lastName: string) {
  await page.getByRole("textbox", { name: "First name" }).fill(firstName);
  await page.getByRole("textbox", { name: "Last name" }).fill(lastName);
  await page.getByRole("button", { name: "Save and continue" }).click();
}

async function fillDob(page: Page, dateOfBirth: string) {
  const dob = new Date(dateOfBirth);
  await page.getByLabel("Month").fill((dob.getMonth() + 1).toString());
  await page.getByLabel("Day").fill(dob.getUTCDate().toString());
  await page.getByLabel("Year").fill(dob.getUTCFullYear().toString());
  await page.getByRole("button", { name: "Save and continue" }).click();
}

async function fillPhone(page: Page, phoneNumber: string) {
  await page.getByLabel("Phone number").fill(phoneNumber);
  await page.getByText("Landline").click();
  await page.getByRole("button", { name: "Save and continue" }).click();
}

async function fillAddress(page: Page, address: Address) {
  assertIsString(address.line_1);
  assertIsString(address.city);
  assertIsString(address.zip);
  assertIsString(address.state);
  await page
    .getByLabel("Address", {
      exact: true,
    })
    .fill(address.line_1);
  await page.getByLabel("City").fill(address.city);
  await page.getByLabel("ZIP").fill(address.zip);
  await page.getByLabel("State").selectOption(address.state);
  await page.getByText("Yes").click();
  await page
    .getByRole("button", {
      name: "Validate Address",
    })
    .click();

  await page
    .getByLabel("Address", {
      exact: true,
    })
    .waitFor({ state: "detached" });
  await validateAddress(page, address.line_1);
}

async function validateAddress(page: Page, addressLine1: string) {
  if (await page.getByText("Which address do you want to").isVisible()) {
    await page.getByText(addressLine1).click();
    await page.getByRole("button", { name: "Save and continue" }).click();
  } else {
    await page.getByRole("button", { name: "Continue anyway" }).click();
  }
}

async function fillSsn(page: Page, ssn: string) {
  await page
    .getByRole("textbox", { name: "What’s your Social Security" })
    .fill(ssn);
  await page.getByRole("button", { name: "Save and continue" }).click();
}

async function getClaimId(page: Page) {
  const url = page.url();
  const urlObject = new URL(url);
  const claim_id = urlObject.searchParams.get("claim_id");
  return claim_id;
}

async function navigateToMyMassGov(page: Page, user: User) {
  await page.goto(`${portalBaseUrl}/oauth-start/`);
  await page
    .locator("a", { hasText: `Log in with MyMassGov — ${user}` })
    .click();
}

function parseOAuthState(oAuthState: string) {
  const state: OAuthState = JSON.parse(oAuthState);
  return state;
}

async function register(
  page: Page,
  credentials: Credentials,
  options: RegistrationOptions
) {
  const { username, password } = credentials;
  const { name } = options;
  await page.locator("a", { hasText: "Create an Account" }).click();
  await consentToProfileSharing(page);
  await verifyEmail(page, username);
  await enterName(page, name);
  await enterPassword(page, password);
  await page.locator("button", { hasText: "Continue" }).click();
}

async function setUpMfa(
  page: Page,
  emailAddress: string,
  options: SetUpMfaOptions = {}
) {
  const { captureAuthenticatorKey, fromIncomplete } = options;

  // We don't need the additional navigation step for incomplete users.
  if (!fromIncomplete) {
    await page.locator("button", { hasText: "Set Up MFA" }).click();
  }

  await page.getByText("Authenticator App", { exact: true }).click();
  await page.getByLabel("Continue").click();
  const authenticatorDetails = await page
    .getByText("enter this information into your authenticator app manually")
    .locator("..")
    .textContent();
  assertIsNotNull(authenticatorDetails);
  const authenticatorKey = extractMmgAuthenticatorKey(authenticatorDetails);
  const authCode = getAuthenticatorCode(authenticatorKey);
  await page.getByPlaceholder("Enter your code").fill(authCode);
  await page.locator("#continue").click();

  if (captureAuthenticatorKey) {
    await setAuthenticatorKey(emailAddress, authenticatorKey);
  }
}

interface SetUpMfaOptions {
  captureAuthenticatorKey?: boolean;
  fromIncomplete?: boolean;
}

async function verifyEmail(page: Page, email: string, isForNewAccount = true) {
  if (isForNewAccount) {
    await page.getByLabel("Email address you use to log").fill(email);
  }

  await page.locator("button", { hasText: "Send verification code" }).click();
  const emailCode = await verificationFetcher.getVerificationCodeForUser(
    // MyMassGov sends the verification email to the address with all characters
    // converted to lowercase. Testmail is case-sensitive.
    email.toLowerCase()
  );
  await page.getByPlaceholder("Verification code").fill(emailCode);
  await page.locator("button", { hasText: "Verify" }).click();
}

function waitForProcessing(page: Page) {
  return page
    .getByText("Please wait while we process")
    .waitFor({ state: "detached" });
}

/**
 * Higher Order Function (HOC) that provides a Playwright Page, navigates to the
 * PFML Portal, and cleans up after itself on success or failure.
 *
 * This is useful for reducing the scope of other functions that desire this
 * behavior, and keeping this logic in one place.
 */
async function withPortalPage<T>(usePortalPage: (page: Page) => Promise<T>) {
  const page = await getPortalPage();

  try {
    // usePortalPage must be awaited for otherwise the browser will close before
    // it is used.
    return await usePortalPage(page);
  } finally {
    page.context().browser()?.close();
  }
}

interface CacheKey {
  credentials: Credentials;
  user: User;
}

interface Name {
  first: string;
  last: string;
}

interface OAuthState {
  outcomes: string[];
  token_data: {
    id_token_expires_in: string;
    id_token: string;
    not_before: string;
    profile_info: string;
    scope: string;
    token_type: string;
  };
}

interface OAuthStateData {
  parsed: OAuthState;
  raw: string;
}

type User = "Employee" | "Leave Administrator";

const MAY_NOT_BE_REGISTERED = "The user may not be registered";

const oAuthStateCache = new AutoCache({
  fetchValue: fetchOAuthState,
  getExpirationDate: getTokenExpiration,
});
const portalBaseUrl = config("PORTAL_BASEURL");
const verificationFetcher = new TestMailVerificationFetcher(
  config("TESTMAIL_APIKEY"),
  config("TESTMAIL_NAMESPACE")
);
