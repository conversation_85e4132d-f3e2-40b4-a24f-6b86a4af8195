import { addDays, format, isWithinInterval, min, parseISO } from "date-fns";

import {
  AbsencePeriodResponse,
  ApplicationLeaveDetails,
  ApplicationRequestBody,
  ApplicationResponse,
  ContinuousLeavePeriods,
  IntermittentLeavePeriods,
  Language,
  ReducedScheduleLeavePeriods,
} from "../_api";
import { DehydratedClaim } from "../generation/Claim";
import { ScenarioSpecification } from "../generation/Scenario";
import {
  ApplicationSubmissionResponse,
  LeavePeriods,
  Submission,
} from "../types";
import { getTypedKeys } from "./typeUtils";

export function extractEmployerReimbursement(
  claim: DehydratedClaim | ScenarioSpecification["claim"]
) {
  if (!claim.metadata?.employerReimbursement) {
    throw new Error("Claim does not have a defined employer reimbursement");
  }

  return claim.metadata.employerReimbursement;
}

export function extractOrgUnitData(claim: DehydratedClaim) {
  if (!claim.metadata?.orgUnitData) {
    throw new Error("Claim is missing orgUnit metadata field.");
  }

  return claim.metadata.orgUnitData;
}

export function extractTimeDeduction(claim: DehydratedClaim) {
  if (!claim.metadata?.timeDeduction) {
    throw new Error("Claim has no timeDeduction field to extract.");
  }

  return claim.metadata.timeDeduction;
}

export function asLeaveCaseDuration(timeDeduction: string) {
  const hours = +timeDeduction;
  const referenceDate = new Date(0, 0);
  referenceDate.setMinutes(hours * 60);

  // format uses single quotes as escape characters.
  let formatString = "h 'Hours'";
  if (referenceDate.getMinutes() !== 0) {
    formatString += " m 'Minutes'";
  }
  return format(referenceDate, formatString);
}

export function extractLeavePeriod(
  { leave_details }: ApplicationRequestBody | NonNullable<ApplicationResponse>,
  leaveType: keyof LeavePeriods = "continuous_leave_periods"
): [Date, Date] {
  if (leave_details && (leave_details?.[leaveType]?.length ?? 0) > 0) {
    return getLeavePeriodFromLeaveDetails(leave_details, leaveType);
  } else {
    throw new Error("Missing leave details");
  }
}

export function getHoursNormallyWorkedPerWeek(claim: DehydratedClaim) {
  const hoursWorkedPerWeek = claim.claim.hours_worked_per_week;

  if (!hoursWorkedPerWeek) {
    throw new Error("Claim does not define hours_worked_per_week");
  }

  return hoursWorkedPerWeek;
}

export function extractHoursOffFromReducedLeave(
  claim: DehydratedClaim["claim"]
) {
  if (!claim.leave_details) {
    throw new Error("Claim does not have defined leave details");
  }

  const period = getPeriodFromLeaveDetails(
    "reduced_schedule_leave_periods",
    claim.leave_details
  );

  return sumHoursForReducedPeriod(period);
}

/**
 * Extracts relevant submission data from an API response.
 * @param apiResponse API response coming from the PMFL API
 * @returns submission object with current timestamp
 */
export function getSubmissionFromApiResponse(
  apiResponse: ApplicationSubmissionResponse
): Submission {
  return {
    application_id: apiResponse.application_id,
    fineos_absence_id: apiResponse.fineos_absence_id,
    timestamp_from: Date.now(),
  };
}
/**
 * Converts an amount of minutes to hours + minutes, throws an error if the remainder of minutes is not a multiple of increment
 * @param totalMinutes
 * @param increment 15 by default
 * @returns hours and minutes as a tuple
 * @example <caption>With default increment of 15</caption>
 * minutesToHoursAndMinutes(225) => [3, 45]
 * @example <caption>With specified increment of 20</caption>
 * minutesToHoursAndMinutes(200, 20) => [3, 20]
 */
export function minutesToHoursAndMinutes(
  totalMinutes: number,
  increment = 15
): [number, number] {
  const hours = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  if (minutes % increment !== 0) {
    throw new Error(`Amount of minutes is not a multiple of ${increment}`);
  }
  return [hours, minutes];
}

export function dateToMMddyyyy(date: string): string {
  const dateObj = parseISO(date);
  return format(dateObj, "MM/dd/yyyy");
}

type DateRange = [Date, Date];
/**
 * Takes 2 date ranges in the form of a tuple [startDate, endDate]. Returns true if these ranges overlap, false otherwise.
 * @example
 * const range1 = [parseISO("01/01/1990"),parseISO("01/01/2010")]
 * const range2 = [parseISO("01/01/2000"),parseISO("01/01/2020")]
 * checkDateRangesIntersect(range1, range2) // true
 */
export function checkDateRangesIntersect(
  range1: DateRange,
  range2: DateRange
): boolean {
  const [startsEarlier, startsLater] =
    range1[0] <= range2[0] ? [range1, range2] : [range2, range1];
  return startsLater[0] <= startsEarlier[1];
}

export function getLeavePeriodFromLeaveDetails(
  leaveDetails: ApplicationLeaveDetails,
  leaveType: keyof LeavePeriods = "continuous_leave_periods"
): [Date, Date] {
  const period = leaveDetails[leaveType]?.[0];
  if (!period || !period.start_date || !period.end_date) {
    throw new Error("No leave period given");
  }

  return [parseISO(period.start_date), parseISO(period.end_date)];
}

export function getLeavePeriod(
  leave_details: ApplicationLeaveDetails
): [string, string] {
  let period;
  if (leave_details.continuous_leave_periods?.length) {
    period = leave_details.continuous_leave_periods[0];
  }
  if (leave_details.intermittent_leave_periods?.length) {
    period = leave_details.intermittent_leave_periods[0];
  }
  if (leave_details.reduced_schedule_leave_periods?.length) {
    period = leave_details.reduced_schedule_leave_periods[0];
  }
  if (period?.start_date && period.end_date) {
    return [period.start_date, period.end_date];
  } else {
    throw new Error("Claim missing leave periods");
  }
}

/**
 * Returns the start year of the earliest leave period, or `null` if it cannot
 * be determined.
 *
 * The start year of an intermittent leave period cannot be determined until an
 * actual is recorded.
 */
export function getLeaveStartYear(leaveDetails: ApplicationLeaveDetails) {
  const leavePeriods = [
    ...(leaveDetails.continuous_leave_periods ?? []),
    ...(leaveDetails.reduced_schedule_leave_periods ?? []),
  ];

  if (leavePeriods.length === 0) {
    return null;
  }

  const dates = leavePeriods
    .filter(hasStartDate)
    .map((period) => parseISO(period.start_date));
  const earliestDate = min(dates);
  return earliestDate.getFullYear();
}

function hasStartDate(value: unknown): value is StartDateBearer {
  return typeof value === "object" && value !== null && "start_date" in value;
}

interface StartDateBearer {
  start_date: string;
}

export function getWaitingPeriod(
  leaveDetails: ApplicationLeaveDetails
): [Date, Date] {
  const [leavePeriodStart] = getLeavePeriod(leaveDetails);
  const waitingPeriodStart = parseISO(leavePeriodStart);
  const waitingPeriodEnd = addDays(waitingPeriodStart, 6);
  return [waitingPeriodStart, waitingPeriodEnd];
}

export function shorterThanWaitingPeriod(
  leaveDetails: ApplicationLeaveDetails
) {
  const [waitStart, waitEnd] = getWaitingPeriod(leaveDetails);
  const [, endDate] = getLeavePeriod(leaveDetails);
  return isWithinInterval(parseISO(endDate), {
    start: waitStart,
    end: waitEnd,
  });
}

/**
 * Returns `[Date, Date]` representing the range of dates that are reportable as
 * a benefit according to PFML validation, or `null` if the benefit is not
 * reportable.
 */
export function getReportableBenefitPeriod(
  benefitDates: readonly [string, string],
  leaveDetails: ApplicationLeaveDetails
): [Date, Date] | null {
  const benefitStart = parseISO(benefitDates[0]);
  const leaveEnd = parseISO(getLeavePeriod(leaveDetails)[1]);
  const benefitEnd = parseISO(benefitDates[1]);
  const waitingPeriodEnd = getWaitingPeriod(leaveDetails)[1];
  const benefitIsReportable =
    benefitStart <= leaveEnd && benefitEnd > waitingPeriodEnd;

  if (benefitIsReportable) {
    const dayAfterWaitingPeriod = addDays(waitingPeriodEnd, 1);
    const reportableStart =
      benefitStart <= waitingPeriodEnd ? dayAfterWaitingPeriod : benefitStart;
    const reportableEnd = benefitEnd > leaveEnd ? leaveEnd : benefitEnd;
    return [reportableStart, reportableEnd];
  }

  return null;
}

export function extractEarliestStartDate(
  leave_details: ApplicationLeaveDetails
): Date {
  const dates = [
    ...(leave_details.continuous_leave_periods ?? []),
    ...(leave_details.reduced_schedule_leave_periods ?? []),
    ...(leave_details.intermittent_leave_periods ?? []),
  ]
    .filter((period) => !!period.start_date)
    .map((period) => period.start_date as string)
    .map((dateStr) => parseISO(dateStr));

  return min(dates);
}

export function extractEarliestEndDate(
  leave_details: ApplicationLeaveDetails
): Date {
  const dates = [
    ...(leave_details.continuous_leave_periods ?? []),
    ...(leave_details.reduced_schedule_leave_periods ?? []),
    ...(leave_details.intermittent_leave_periods ?? []),
  ]
    .filter((period) => !!period.end_date)
    .map((period) => period.end_date as string)
    .map((dateStr) => parseISO(dateStr));

  return min(dates);
}

export function extractLeavePeriodType(
  leaveDetails: ApplicationLeaveDetails | ApplicationResponse["leave_details"]
): NonNullable<AbsencePeriodResponse["period_type"]> {
  if (!leaveDetails) {
    throw Error("Generated claim missing leave details");
  }
  const leavePeriodTypes = {
    intermittent_leave_periods: "Intermittent",
    continuous_leave_periods: "Continuous",
    reduced_schedule_leave_periods: "Reduced Schedule",
  } as const;
  let key: keyof typeof leavePeriodTypes;
  for (key in leavePeriodTypes) {
    const leavePeriodField = leaveDetails[key];
    if (leavePeriodField && leavePeriodField.length > 0) {
      return leavePeriodTypes[key];
    }
  }
  throw Error("Couldn't determine leave period type");
}

/**
 * Format an ISO date to an "M/d/yyyy" format, used in the LA portal.
 * @param date
 * @returns
 */
export function dateToReviewFormat(date: string): string {
  return format(parseISO(date), "M/d/yyyy");
}

export function getFineosPreferredLanguage(language: Language) {
  switch (language) {
    case "Chinese (simplified)": {
      return "Chinese";
    }
    case "Language not listed": {
      return "Other";
    }
    default: {
      return language ?? "English";
    }
  }
}

export function formatStartAndEndDate(
  period:
    | ContinuousLeavePeriods
    | IntermittentLeavePeriods
    | ReducedScheduleLeavePeriods
) {
  if (!period.start_date || !period.end_date) {
    throw new Error(`Leave period does not have a start or end date`);
  }
  return {
    startDate: format(parseISO(period.start_date), "MM/dd/yyyy"),
    endDate: format(parseISO(period.end_date), "MM/dd/yyyy"),
  };
}

export function formatIntermittentDetails(period: IntermittentLeavePeriods) {
  return {
    duration: period.duration?.toString() ?? null,
    frequency: period.frequency?.toString() ?? null,
    frequencyType: getFrequencyIntervalBasisKey(
      period.frequency_interval_basis
    ),
    durationType: getDurationBasisKey(period.duration_basis),
  };
}

/**
 * Calculates the total hours off for a week
 * @param period
 * @returns Total off hours as a rounded string.
 */
export function sumTotalHoursOff(period: ReducedScheduleLeavePeriods) {
  const totalHours = sumHoursForReducedPeriod(period);
  return Math.round(totalHours).toString();
}

function sumHoursForReducedPeriod(period: ReducedScheduleLeavePeriods) {
  const totalMinutes = [
    period.monday_off_minutes,
    period.tuesday_off_minutes,
    period.wednesday_off_minutes,
    period.thursday_off_minutes,
    period.friday_off_minutes,
    period.saturday_off_minutes,
    period.sunday_off_minutes,
  ].reduce<number>((sum, dayMinutes) => sum + (dayMinutes ?? 0), 0);
  return totalMinutes / 60;
}

export function getFrequencyIntervalBasisKey(
  frequencyIntervalBasis: IntermittentLeavePeriods["frequency_interval_basis"]
) {
  switch (frequencyIntervalBasis) {
    case "Months": {
      return "Month";
    }
    case "Days": {
      return "Day";
    }
    case "Weeks": {
      return "Week";
    }
    default: {
      return null;
    }
  }
}

export function getDurationBasisKey(
  durationBasis: IntermittentLeavePeriods["duration_basis"]
) {
  switch (durationBasis) {
    case "Minutes": {
      throw new Error("Minutes are not supported as a duration basis.");
    }
    case "Hours": {
      return "hours";
    }
    case "Days": {
      return "days";
    }
    default: {
      return null;
    }
  }
}

export function getPeriodFromLeaveDetails(
  key:
    | "intermittent_leave_periods"
    | "reduced_schedule_leave_periods"
    | "continuous_leave_periods",
  leaveDetails: ApplicationLeaveDetails
) {
  const periods = leaveDetails[key];
  if (!periods || periods.length === 0) {
    throw new Error(`leaveDetails does not have key: ${key}`);
  }
  return periods[0];
}

const leaveTypeDetails = {
  continuous: "continuous_leave_periods",
  reduced_schedule: "reduced_schedule_leave_periods",
  intermittent: "intermittent_leave_periods",
} as const;

export const leaveScheduleTypes = getTypedKeys(leaveTypeDetails);

export function getAvailableLeaveScheduleTypes(
  leaveDetails: ApplicationLeaveDetails
) {
  if (!leaveDetails) {
    throw new Error("No leave details available");
  }

  return leaveScheduleTypes.filter((leaveType) => {
    const detailKey = leaveTypeDetails[leaveType];
    const periods = leaveDetails[detailKey];
    return Array.isArray(periods) && periods.length > 0;
  });
}
