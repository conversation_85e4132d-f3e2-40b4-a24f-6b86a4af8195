import { ApplicationResponse } from "../_api";
import {
  APIClaimSpec,
  ClaimGenerator,
  GeneratedClaim,
} from "../generation/Claim";
import * as scenarios from "../scenarios";
import FineosSubmitter from "../submission/FineosSubmitter";
import {
  ApplicationSubmissionResponse,
  Credentials,
  Scenarios,
} from "../types";
import { getEmployeePool, getPortalSubmitter } from "./common";
import {
  getClaimantCredentials,
  getLeaveAdminCredentials,
} from "./credentials";

export function dispatchPostSubmit(
  claim: GeneratedClaim,
  response: ApplicationResponse
) {
  const postSubmit = claim.metadata?.postSubmit;

  if (postSubmit) {
    return postSubmit.execute({
      claim,
      fineosAbsenceId: response.fineos_absence_id || undefined,
      applicationId: response.application_id,
    });
  }

  return Promise.resolve();
}

export async function submitClaim(
  application: GeneratedClaim & {
    credentials?: Credentials;
    employerCredentials?: Credentials;
  }
): Promise<ApplicationSubmissionResponse> {
  if (!application.claim) {
    throw new Error("Application missing!");
  }
  const { credentials, employerCredentials, ...claim } = application;
  const { employer_fein } = application.claim;
  if (!employer_fein) {
    throw new Error("Application is missing employer FEIN");
  }
  if (claim.metadata?.useFineosSubmitter) {
    return new FineosSubmitter().submit(claim).catch((err) => {
      console.error("Failed to submit claim:", err.data);
      throw new Error(err);
    });
  } else {
    return getPortalSubmitter()
      .submit(claim, credentials ?? getClaimantCredentials(), {
        leaveAdminCredentials:
          employerCredentials ?? getLeaveAdminCredentials(application.employer),
      })
      .catch((err) => {
        console.error("Failed to submit claim:", err.data);
        throw new Error(err);
      });
  }
}

export async function generateClaims(
  scenarioID: Scenarios,
  count: number,
  employeePoolFile?: string
): Promise<GeneratedClaim[]> {
  if (!(scenarioID in scenarios)) {
    throw new Error(`Invalid scenario: ${scenarioID}`);
  }
  const scenario = scenarios[scenarioID];
  const employeePool = await getEmployeePool(employeePoolFile);

  return Array.from({ length: count }, () =>
    ClaimGenerator.generate(
      employeePool,
      scenario.employee,
      scenario.claim as APIClaimSpec
    )
  );
}
