import { addDays, addWeeks, parseISO, subDays } from "date-fns";

import { formatDateAsIsoExt } from "../../global.common";
import { Scenarios } from "../types";
import { findScenarioByName } from "./scenarios";

const WEEKS_PER_YEAR = 52;

/**
 * Calculates the next benefit year from the end date of a previous benefit
 * year.
 *
 * Accepts and returns values in `yyyy-MM-dd` format.
 */
export function calculateNextBenefitYear(currentBenefitYearEnd: string) {
  const nextStart = addDays(parseISO(currentBenefitYearEnd), 1);
  const nextEnd = subDays(addWeeks(nextStart, WEEKS_PER_YEAR), 1);
  const nextDates: [string, string] = [
    formatDateAsIsoExt(nextStart),
    formatDateAsIsoExt(nextEnd),
  ];
  return nextDates;
}

/**
 * Returns expected weekly benefit based on IAWW and program year.
 *
 * Does not account for eligibility.
 *
 * Performs floating point operations and rounding.
 *
 * Floating point rounding in JavaScript is perilous. This function has proved
 * to be "accurate enough" for tests using program years 2021, 2022, and 2023.
 * A different algorithm may be more appropriate should errors appear.
 *
 * @param individualAverageWeeklyWages - rounded to two-decimal precision
 * @param year - program year for wage calculations
 */
export function calculateWeeklyBenefit(
  individualAverageWeeklyWages: number,
  year: ProgramYear
) {
  const stateAverageWeeklyWages = getStateAverageWeeklyWages(year);
  const halfStateAverageWeeklyWages = 0.5 * stateAverageWeeklyWages;

  /** 80% of IAWW that is equal to or less than 50% of the SAWW. */
  const portionA =
    0.8 * Math.min(individualAverageWeeklyWages, halfStateAverageWeeklyWages);

  /** 50% of IAWW that is more than 50% of the SAWW. */
  const portionB =
    0.5 *
    Math.max(individualAverageWeeklyWages - halfStateAverageWeeklyWages, 0);

  const maximumWeeklyBenefit = getMaximumWeeklyBenefit(year);
  const actualBenefit = Math.min(portionA + portionB, maximumWeeklyBenefit);

  return asFixed(actualBenefit);
}
/**
 * Returns the minimal annual wage needed to satisfy the maximum benefit. The annual wage is rounded to the next hundred.
 * @param year - program year for wage calculations
 */
export function getAnnualWageSatisfyingMaximumBenefit(year: ProgramYear) {
  const stateAverageWeeklyWages = getStateAverageWeeklyWages(year);

  return (
    Math.ceil(asFixed(stateAverageWeeklyWages * WEEKS_PER_YEAR) / 100) * 100
  );
}

// More information for why this is required can be found here:
// https://www.mass.gov/info-details/how-pfml-weekly-benefit-amounts-are-calculated-andor-changed#other-factors-
export function adjustBenefitForTimeOff(
  benefit: number,
  hoursOffPerWeek: number,
  hoursNormallyWorkedPerWeek: number
) {
  const adjustment = hoursOffPerWeek / hoursNormallyWorkedPerWeek;
  return asFixed(benefit * adjustment);
}

// The maximum weekly benefit applies to a claimant benefit year and is updated
// each year. The minimum eligible wages amount also gets updated each year.
// More information for both can be found at
// https://lwd.atlassian.net/wiki/spaces/DD/pages/2704867444/MA+Rates+updates
export function getMaximumWeeklyBenefit(year: ProgramYear) {
  const result = maximumWeeklyBenefit[year];

  if (result === undefined) {
    throw new Error(`Maximum weekly benefit for year ${year} is undefined.`);
  }

  return result;
}

const maximumWeeklyBenefit: Record<ProgramYear, number> = {
  "2021": 850,
  "2022": 1084.31,
  "2023": 1129.82,
  "2024": 1149.9,
  "2025": 1170.64,
};

export const eligibleProgramYears =
  Object.keys(maximumWeeklyBenefit).map(Number);

export function getMinimumEligibleWages(year: ProgramYear) {
  const result = minimumEligibleWages[year];

  if (result === undefined) {
    throw new Error(`Minimum eligible wages for year ${year} are undefined.`);
  }

  return result;
}

const minimumEligibleWages: Record<ProgramYear, number> = {
  "2021": 5400,
  "2022": 5700,
  "2023": 6000,
  "2024": 6300,
  "2025": 6300,
};

export function getStateAverageWeeklyWages(year: ProgramYear) {
  const result = stateAverageWeeklyWages[year];

  if (result === undefined) {
    throw new Error(
      `State average weekly wages for year ${year} are undefined.`
    );
  }

  return result;
}

const stateAverageWeeklyWages: Record<ProgramYear, number> = {
  "2021": 1487.78,
  "2022": 1694.24,
  "2023": 1765.34,
  "2024": 1796.72,
  "2025": 1829.13,
};

type ProgramYear = number;

export function getIndividualAverageWeeklyWages(scenarioDescriptor: Scenarios) {
  const scenario = findScenarioByName(scenarioDescriptor);
  const wages = scenario.employee.wages;
  if (typeof wages !== "number") {
    throw new Error(
      "Can only determine individual average weekly wages for a an employee spec with an explicit numerical wage value."
    );
  }

  const iaww = Math.ceil(wages / WEEKS_PER_YEAR);

  return asFixed(iaww);
}

export function computeTaxes(amount: number) {
  const sit = asFixed(amount * 0.05);
  const fit = asFixed(amount * 0.1);
  return {
    sit,
    fit,
    netPayment: amount - sit - fit,
  };
}

export function asFixed(amount: number, precision = 2) {
  return +amount.toFixed(precision);
}

// TODO: Add function to apply reductions from claim.

export const maxAvailableLeaveTimeInWeeks = {
  total: 26,
  medical: 20,
  family: 12,
} as const;
