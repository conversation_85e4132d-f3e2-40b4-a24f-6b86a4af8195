import {
  ApplicationLeaveDetails,
  ApplicationRequestBody,
  ApplicationResponse,
  DocumentResponse,
  DocumentType,
  DocumentUploadRequest,
  EmployerBenefit,
  EmployerClaimReviewRequestEmployerBenefit,
  OtherIncome,
  PreviousLeave,
  ReasonQualifierOne,
} from "./api";
import { DehydratedClaim } from "./generation/Claim";
import { Employee } from "./generation/Employee";
import { WorkPatternSpec } from "./generation/WorkPattern";
import * as scenarios from "./scenarios";
import * as lstScenarios from "./scenarios/lst";

export type Credentials = {
  username: string;
  password: string;
};
export type CredentialsPair = {
  claimant: Credentials;
  leaveadmin: Credentials;
};

export type OAuthCreds = {
  clientId: string;
  clientSecret: string;
};

/**
 * Types of correspondence that can be added via the "Add Correspondence" button
 * from the claim page.
 */
export type FineosCorrespondenceType =
  | "Employer Reimbursement Formstack"
  | "Employer Reimbursement Policy"
  | "Employer Reimbursement Denial Notice"
  | "Employer Reimbursement Approval Notice"
  | "Overpayment Notice - Full Balance Recovery"
  | "Leave Allotment Change Notice";

/**
 * Valid document types in Fineos. Contains documents that can be uploaded via
 * the API, as well as a few that can only be uploaded through the Fineos UI.
 */
export type FineosDocumentType =
  | DocumentUploadRequest["document_type"]
  | NonNullable<DocumentResponse["document_type"]>
  | "Employee Not Found Information"
  | "Employer Reimbursement Formstack"
  | "Employer Reimbursement Policy"
  | "Overpayment Notice - Full Balance Recovery"
  | "W9 Tax Form"
  | "Pregnancy and Maternity form"
  | "EFT Change Request";
export type LeavePeriods = Pick<
  ApplicationLeaveDetails,
  | "reduced_schedule_leave_periods"
  | "continuous_leave_periods"
  | "intermittent_leave_periods"
>;

export type Submission = {
  /**Claim id given to it by the PFML API */
  application_id: string;
  /**Absence Case number from fineos */
  fineos_absence_id: string;
  /**Time when the claim was submitted. Used to find the right emails within the testmail. */
  timestamp_from: number;
};

export type ApplicationSubmissionResponse = RequireNotNull<
  ApplicationResponse,
  "fineos_absence_id" | "application_id" | "first_name" | "last_name"
>;

export type SubjectOptions =
  | "application started"
  | "employer response"
  | "denial (employer)"
  | "approval (employer)"
  | "denial (claimant)"
  | "appeal (employer)"
  | "approval (claimant)"
  | "appeal (claimant)"
  | "appeal dismissed"
  | "review leave hours"
  | "request for additional info"
  | "extension of benefits"
  | "employee withdrawal"
  | "employee record intermittent hours"
  | "overpayment payoff"
  | "user not found claim linked"
  | "child support notice";
export type ScenarioSpecs = typeof scenarios;
export type Scenarios = keyof ScenarioSpecs;
export type LSTScenarios = keyof typeof lstScenarios;
export type ClaimGenData = {
  scenario: Scenarios;
  employeePoolFileName?: string;
  employerPoolFileName?: string;
  logSubmissionToNewRelic?: boolean;
  occupationIndex?: number;
  workPattern?: WorkPatternSpec;
  absenceStatus?: AbsenceStatus;
};
export type EmployeeClaimGenData = {
  employee?: Employee;
  scenario: Scenarios;
};
export type EmployeeClaimGenResponse = {
  claim: DehydratedClaim;
  employee: Employee;
};

export type PersonalIdentificationDetails = {
  id_number_type: "Social Security Number" | "ID" | "ITIN";
  date_of_birth: string;
  gender: NonNullable<ApplicationRequestBody["gender"]>;
  marital_status:
    | "Unknown"
    | "Single"
    | "Married"
    | "Divorced"
    | "Widowed"
    | "Separated";
};

export type FolderStructureDetails = {
  "Inbound Documents": string[];
  "Outbound Documents": string[];
  eForms?: string[];
  "Certification Documents"?: string[];
};

export type CustomerSpecificDetails = {
  consent_to_share_data?: boolean;
  mass_id?: string | null;
  out_of_state_id?: string | null;
  race?: string | null;
  ethnicity?: string | null;
};

/**
 * @FINEOS_TYPES
 */

/**
 * Tasks associated with employer response to a claim.
 */
export type ERTask =
  | "Employer Approval Received"
  | "Employer Conflict Reported"
  | "Escalate employer reported accrued paid leave (PTO)"
  | "Escalate Employer Reported Fraud"
  | "Escalate Employer Reported Other Income"
  | "Escalate employer reported past leave";
/**
 * Tasks associated with reviewing evidence.
 * Separated because they are use to get the tasks assicoated with document review.
 */
export type DocumentReviewTask =
  | "Bonding Certification Review"
  | "Medical Certification Review"
  | "ID Review"
  | "Certification Review"
  | "Caring Certification Review"
  | "Medical Pregnancy Certification Review";
/**Other fineos tasks, expand as needed. */
export type OtherTask =
  | "1099-G Address Change"
  | "1099-G Payment Dispute"
  | "Absence Paid Leave Case Creation Failure"
  | "Absence Paid Leave Payments Failure"
  | "Accounts Receivable System Inaccessible"
  | "Action Outstanding Requirement"
  | "Action Requested"
  | "Add Appeal Case"
  | "Additional Information Overdue Notification Task"
  | "Additional Information Received"
  | "Adjudicate Absence"
  | "Agency Reported Additional Income"
  | "Anonymize Batch Error Task"
  | "Appeal Follow Up"
  | "Approved Leave Start Date Change"
  | "Auto Balancing Error Task"
  | "Auto Evaluate Case"
  | "Autopay After Appeal Reminder"
  | "Benefit Payment Waiting Period Change"
  | "Bill Published without an Email Address Defined"
  | "Bill Regenerated without an Email Address Defined"
  | "Billing Account Synchronisation Issue"
  | "CANNOT CREATE CLAIM PAYMENT INTERFACE"
  | "Case File Notification Task"
  | "Case File Producer Task"
  | "Census Processed for Ready To Pay Bill without an Email Address defined"
  | "Census Rollover Task"
  | "Census Updates for Ready To Pay Bill without an Email Address defined"
  | "Certification Overdue Notification Task"
  | "Claim Lodgement Notification Task"
  | "Complete Notification Intake"
  | "Complete Plan Selection"
  | "Conduct Hearing"
  | "Confirm Employee Return to Work"
  | "Confirm Employment"
  | "DI Claims Activity Period Change Notification"
  | "DO NOT USE Autopay After Appeal Reminder"
  | "Document Indexing"
  | "Due Recalculated by Batch needing Manual Approval"
  | "Employee Communication Failed"
  | "Employee Email Missing"
  | "Employee File Upload Notification Task"
  | "Employee Reported Other Income"
  | "Employee Reported Other Leave"
  | "Employee Upload Manual Intervention Task"
  | "Employee reported accrued paid leave (PTO)"
  | "Employer Communication Failed"
  | "Employer Confirmation not Received within 10 days"
  | "Employer Email Missing"
  | "Employer Reimbursement"
  | "Employer Reimbursement Adjustment"
  | "Evidence Review"
  | "Exceeds 26 weeks of total leave"
  | "Failed Invoice Payment Processing Task"
  | "Failed To Open New Billing Period"
  | "Follow-up Reminder"
  | "Fraud Report Received"
  | "General Enquiry Submitted"
  | "Get ID Proof [AUTO]"
  | "Incoming Email - Case Not Found"
  | "Incoming Email - Party Not Found"
  | "Insufficient Amount for Deduction Notification"
  | "Invalid Payment Leave Dates Change"
  | "Invalid Payment Name Mismatch"
  | "Invalid Payment Paid Date"
  | "Invalid Payment Pay Adjustment"
  | "Invalid Payment Waiting Week"
  | "Leave Request In Review"
  | "Main Process Due Approval Failure"
  | "Make Decision"
  | "Manual Intervention required to Approve Payments"
  | "Manual Intervention required to Approve Periods"
  | "Manual Request for Information"
  | "Mark Anonymize Batch Error Task"
  | "Military Certification Review"
  | "Military ID Review"
  | "Move Census to Next Period"
  | "New Web Message Received"
  | "Notification of Cost Transfer"
  | "Notification of Cost Transfer Removal or Reversal"
  | "NotificationRequest"
  | "Offline Bill Generated without an Email Address Defined"
  | "Offset Recovery Frequency Change Notification"
  | "Online Payment for Bill Unconfirmed"
  | "Organisation Structure Warning"
  | "Outstanding Requirement Received"
  | "Overlapping Absence Request Exists"
  | "Overpayment Letter Follow-up"
  | "Overpayment Mgt New Underpayment Notification"
  | "PEI Writeback Failure Due to Incorrect Status Notification"
  | "PEI Writeback Failure Due to Locked Records Notification"
  | "Payment Audit Error"
  | "Payment Change Request Received"
  | "Payment Failure Due to Locked Records Notification"
  | "Payment Failure Due to Orphaned Records"
  | "Payment Validation Error"
  | "Print and Mail Correspondence"
  | "Quality Review: Absence Adjudication"
  | "Ready to Approve"
  | "Recalculation Error Task"
  | "Recertification Overdue Notification Task"
  | "Recommend Service Profile Change"
  | "Record Additional Time Reported"
  | "Record Leave Period Removal"
  | "Register New Absence Request"
  | "Register New Accommodation Request"
  | "Release Notice"
  | "Reminder - Additional Outreach Attempt Needed"
  | "Reminder Task"
  | "Reminder to remove waived fee"
  | "ReplyToEmployerPartyMessage"
  | "Request Fitness for Duty Certification"
  | "Returned Payment Received"
  | "Review Appeal"
  | "Review Create Claim Recovery"
  | "Review Negative Cover Code"
  | "Review New Viewpoint Claim"
  | "Review Reserve"
  | "Review Tax Adjustments"
  | "Review Updated Pregnancy Details"
  | "Review and Decision Cancel Time Submitted"
  | "Review and Make Decision on Actual Time Submitted"
  | "Review bill paid based on census containing errors"
  | "Review receivables - cash received, auto-clear off"
  | "Review receivables - cash received, auto-clear off; further instalments may be paid"
  | "Review receivables - cash received, outside of auto-clear tolerance"
  | "Review receivables - cash received, outside of auto-clear tolerance; further instalments may be paid"
  | "ReviewClientGoalChangeTask"
  | "SOM Autopay After Appeal Reminder"
  | "Schedule Hearing"
  | "Scheduled billing method change"
  | "Send Decision Notice"
  | "Send Overpayment Letter to Claimant (Payee)"
  | "Service Agreement Revision"
  | "Start Of Birth Claim Reminder"
  | "Start of Birth Disability Reminder"
  | "Successful Recalc and Auto Balancing Task"
  | "Successful Recalculation Task"
  | "System Task to Upgrade the Delinquency Process"
  | "TASK_ClaimsByLossType"
  | "TASK_ClaimsByMedicalCode"
  | "TASK_ClaimsByMedicalCodeSet"
  | "TASK_ReserveRecoveries"
  | "TASK_ReserveRecoveryDetails"
  | "TASK_ReserveTotalsByCaseTypeReserveType"
  | "TASK_ReserveTrendsByCaseType"
  | "Temporary Address Verification"
  | "Termination Confirmation Due"
  | "Termination Warning Due"
  | "Update Paid Leave Case"
  | "User ReRegistration Notification"
  | "Viewpoint User Registration Task";
export type PaymentValidationTask =
  | "EFT Account Information Error"
  | "Exempt Employer"
  | "Bank Processing Error"
  | "Address Validation Error"
  | "Invalid Payment Waiting Week";

type FineosPaymentValidationTask =
  | PaymentValidationTask
  | "Max Weekly Benefits Exceeded";

/**Tasks available in fineos */
export type FineosTask =
  | DocumentReviewTask
  | ERTask
  | FineosPaymentValidationTask
  | OtherTask;

export type FineosProcess =
  | DocumentType
  | "Designation Notice"
  | "SOM Generate Legal Notice"
  | "Leave Request Declined"
  | "Leave Request Withdrawn"
  | "Leave Request Not Applicable"
  | "Review Approval Notice"
  | "Leave Cancellation Request"
  | "Preliminary Designation"
  | "SOM Auto Approve Leave Request"
  | "SOM Generate Appeals Notice"
  | "SOM Generate Editable Notice"
  | "SOM Generate Employer Reimbursement Notice"
  | "Send Decision Notice"
  | "Review Denial Notice"
  | "Intermittent Time Request Decision";

export interface ReceiveEvidenceOptions {
  decision?: "Pending" | "Satisfied" | "Not Satisfied" | "Waived";
  reason?: string;
  receipt?: "Pending" | "Received" | "Not Received";
}

export type FineosDepartment =
  | "SL - Back Office"
  | "SL - Bonding Leave"
  | "SL - Caring Leave"
  | "SL - Medical Leave"
  | "SL - Medical Pregnancy Leave"
  | "SL - Military Leave"
  | "DFML Program Integrity"
  | "DFML Appeals";

type SavilinxRole =
  | "SL - Back Office"
  | "SL - Bonding Leave"
  | "SL - Caring Leave"
  | "SL - Medical Leave"
  | "SL - Medical Pregnancy Leave"
  | "SL - Military Leave";

type DFMLAppealsRole =
  | "Appeal Administrator"
  | "Review Examiner I"
  | "Review Examiner II";

type DFMLProgramIntegrityRole =
  | "Claim Examiner"
  | "Claim Supervisor"
  | "Compliance Analyst"
  | "Compliance Supervisor";

export type FineosRole =
  | SavilinxRole
  | DFMLAppealsRole
  | DFMLProgramIntegrityRole;

export interface FineosTaskCheck {
  readonly afterNext?: () => void;
  readonly beforeNext?: () => void;
  readonly name: FineosTask;
}

export type FineosTasksStatus = "Open" | "Closed" | "Cancelled";

export type FineosRMVCheckStatus = "passed" | "failed";

export type FineosCloseTaskStep =
  | "Reimbursement Approved"
  | "Reimbursement Denied";

export type ClaimStatus =
  | "Adjudication"
  | "Approved"
  | "Completed"
  | "Declined";

/**
 * @note UTILITY TYPES
 */

/**
 * Require properties in P to be neither null nor undefined within T
 */
export type RequireNotNull<T, P extends keyof T> = AllNotNull<Pick<T, P>> &
  Pick<T, Exclude<keyof T, P>>;

/**
 * Exclude null and undefined from all of the properties of the given type T
 */
export type AllNotNull<T> = Required<{
  [P in keyof T]: NonNullable<T[P]>;
}>;

/**Get a union of non-optional keys of type */
export type RequiredKeys<T> = {
  [k in keyof T]-?: undefined extends T[k] ? never : k;
}[keyof T];

/**
 * Require a typed array to have at least one element.
 */
export type NonEmptyArray<T> = [T, ...T[]];

/**
 * @note Following types are used within typeguards and to limit the amount of property checks & type casts.
 */

// Two options are in the API but are not used in the portal.
type PreviousLeaveOmittedReasons = Omit<PreviousLeave, "leave_reason"> & {
  leave_reason: Exclude<
    PreviousLeave["leave_reason"],
    | "A health condition during pregnancy"
    | "An illness or injury that required hospitalization"
  >;
};

/**
 * Has all the properties required to submit a previous leave.
 */
export type ValidPreviousLeave = RequireNotNull<
  PreviousLeaveOmittedReasons,
  Exclude<keyof PreviousLeave, "previous_leave_id" | "employer_changes">
>;

/**
 * Has all the properties required to submit a child support adjustment.
 */
export interface ChildSupport {
  startDate: string;
  endDate: string;
  amountDollars: number;
}

/**
 * Has all the properties required to submit an other income.
 */
export type ValidOtherIncome = RequireNotNull<
  OtherIncome,
  Exclude<keyof OtherIncome, "other_income_id" | "employer_changes">
>;

/**
 * Has all the properties required to submit an employer benefit.
 */
export type ValidEmployerBenefit = RequireNotNull<
  EmployerClaimReviewRequestEmployerBenefit,
  Exclude<keyof EmployerBenefit, "employer_benefit_id" | "employer_changes">
>;

/**
 * Used to assert the existence of following properties on a generated claim.
 */
export type ValidClaim = RequireNotNull<
  DehydratedClaim["claim"],
  | "first_name"
  | "last_name"
  | "tax_identifier"
  | "employer_fein"
  | "date_of_birth"
  | "language"
  | "leave_details"
>;

const environments = [
  "breakfix",
  "performance",
  "training",
  "trn2",
  "tst1",
  "tst2",
  "tst3",
  "uat",
] as const;

export type Environment = (typeof environments)[number];

export function assertValidEnvironment(e: unknown): asserts e is Environment {
  if (!(environments as readonly unknown[]).includes(e)) {
    throw new Error(`${e} is not a valid environment.`);
  }
}

//String enum used for mapping between AbsencePeriodResponse["period_type"] and ApplicationLeaveDetails types
export enum LeavePeriodTypes {
  "Continuous" = "continuous_leave_periods",
  "Intermittent" = "intermittent_leave_periods",
  "Reduced Schedule" = "reduced_schedule_leave_periods",
}

export type LeaveAdminContactInformation = {
  firstName: string;
  lastName: string;
  phoneNumber: string;
};

export type PaymentRecord = {
  fineos_absence_id: string;
  payment_id: string;
  fineos_extract_import_log_id: number;
  created_at: string;
  payment_date: string;
};

export type TypeOfRequestOptions =
  | "Accident or treatment required for an injury"
  | "Sickness, treatment required for a medical condition or any other medical procedure"
  | "Pregnancy, birth or related medical treatment"
  | "Bonding with a new child (adoption/ foster care/ newborn)"
  | "Caring for a family member"
  | "Out of work for another reason";

/**
 * Maps to select inputs available to describe Absence Reason
 * Add new options to discriminated unions as needed
 */
export type AbsenceReasonDescription = {
  relates_to?: "Employee" | "Family";
  reason?:
    | "Serious Health Condition - Employee"
    | "Care for a Family Member"
    | "Pregnancy/Maternity"
    | "Child Bonding"
    | "Military Exigency Family"
    | "Military Caregiver";
  qualifier_1?: ReasonQualifierOne;
  qualifier_2?: "Sickness" | "Accident / Injury";
  typeOfRequest?: TypeOfRequestOptions;
};

/*
 * Represents new relic query log results
 * for applications successfully linked
 */
export interface NrqlUNFLog {
  application_id: string;
  claim_id: string;
  submitted_time: string;
  completed_time?: string | null;
  unf_submitted_time: string;
  message: string;
  updated_at: string;
  fineos_absence_id: string;
  status?: "Started" | "In Manual Review" | "Submitted" | "Completed";
}

export type FineosRoles =
  | "DFML Program Integrity"
  | "DFML Appeals"
  | "SaviLinx"
  | "Post-Prod Admin(sec)"
  | "DFML IT";
export type FineosSecurityGroups =
  | "DFML Claims Examiners(sec)"
  | "DFML Claims Supervisors(sec)"
  | "DFML Compliance Analyst(sec)"
  | "DFML Compliance Supervisors(sec)"
  | "DFML Appeals Administrator(sec)"
  | "DFML Appeals Examiner I(sec)"
  | "DFML Appeals Examiner II(sec)"
  | "SaviLinx Agents (sec)"
  | "SaviLinx Secured Agents(sec)"
  | "SaviLinx Supervisors(sec)"
  | "SaviLinx Back Office Agents(sec)"
  | "DFML IT(sec)"
  | "Post-Prod Admin(sec)";

export type FineosPhoneNumberType =
  | "Cell"
  | "Fax"
  | "Home Phone"
  | "Phone"
  | "Work Phone";

export type AbsenceStatus = "Known" | "Estimated" | "Please select";

export type ContinuousLeavePeriod = {
  status: AbsenceStatus;
  /**MM/DD/YYYY */
  start: string;
  /**MM/DD/YYYY */
  end: string;
  /**MM/DD/YYYY */
  last_day_worked?: string;
  /**MM/DD/YYYY */
  return_to_work_date?: string;
  leave_type?: string;
};

export type AdditionalDetails = {
  reported_by?: "Employee" | "Employee Manager" | "Employer Representative";
  received_via?:
    | "Unknown"
    | "Phone"
    | "E-Mail"
    | "Paper"
    | "Fax"
    | "Mail/Post"
    | "Self Service";
  reported_date?: string;
  accepted?: "Yes" | "No" | "Unknown";
  additional_notes?: string;
  reporting_party?: string;
};

export type PaymentMadeTableRow = {
  effectiveDate: string;
  periodStartDate: string;
  periodEndDate: string;
  status: string;
  netPayment: string;
  payeeName: string;
};

export type EpisodicLeavePeriodDescription = {
  startDate: string;
  endDate: string;
  timeSpanHoursStart: string;
  timeSpanHoursEnd: string;
  timeSpanMinutesStart: string;
  timeSpanMinutesEnd: string;
};

export type FixedAbsenceDateDescription = {
  date: string;
  all_day?: true;
  hours?: string;
  minutes?: string;
};

export type FixedTimeOffPeriodDescription = {
  start: FixedAbsenceDateDescription;
  end: FixedAbsenceDateDescription;
};
export type NoteType = "Leave Request Review";

export type EvidenceStatus = {
  evidenceType: string;
  receipt?: "Pending" | "Received" | "Not Received";
  decision?: "Pending" | "Satisfied" | "Not Satisfied" | "Waived";
  reason?: string;
};

export interface SlaDefinition {
  days: number;
  isInBusinessDays: boolean;
  task: FineosTask;
}

export type PaidLeaveDocumentStatus = "Unknown" | "Completed" | "Draft";

export interface PaymentMade {
  readonly dateEffective: Date;
  readonly datePeriodStart: Date;
  readonly datePeriodEnd: Date;
  readonly type: string;
  readonly paymentType: string;
  readonly status: string;
  readonly netPaymentAmount: string;
  readonly payeeName?: string;
  readonly crossCasePayment?: boolean;
  readonly statusReason?: string;
}

export type PaymentPeriod = {
  datePeriodStart: Date;
  datePeriodEnd: Date;
};

export type CompletedPaymentPeriod = Pick<PaymentMade, "dateEffective"> &
  PaymentPeriod & {
    periods: PaymentPeriod[];
  };

export type DorIntegrityReportRow = Record<
  DorIntegrityReportColumnName,
  string
>;

export const dorIntegrityReportColumnNames = [
  "EmployeeSSN",
  "DifferenceMeasure",
  "CurrentPfmlFirstNameValue",
  "CurrentPfmlLastNameValue",
  "NewFirstNameValue",
  "NewLastNameValue",
] as const;

export type DorIntegrityReportColumnName =
  (typeof dorIntegrityReportColumnNames)[number];

export type PaymentAuditReportMap = Map<PaymentId, PaymentAuditReportRow>;

type PaymentId = string;

export type PaymentAuditReportRow = Record<
  PaymentAuditReportColumnName,
  string
>;

export type PaymentAuditReportColumnName =
  (typeof paymentAuditReportColumnNames)[number];

export const paymentAuditReportColumnNames = [
  "PFML Payment Id",
  "Leave type",
  "Customer Number",
  "First Name",
  "Last Name",
  "DOR First Name",
  "DOR Last Name",
  "DOR FINEOS Name Mismatch",
  "Address Line 1",
  "Address Line 2",
  "City",
  "State",
  "Zip",
  "Address Verified",
  "Employer ID",
  "Employer Payee Name",
  "Employer Address Line 1",
  "Employer Address Line 2",
  "Employer City",
  "Employer State",
  "Employer Zip",
  "Employer Address Verified",
  "Payment Preference",
  "Scheduled Payment Date",
  "Payment Period Start",
  "Payment Period End",
  "Payment Period Weeks",
  "Gross Payment Amount",
  "Payment Amount",
  "Federal Withholding Amount",
  "State Withholding Amount",
  "Employer Reimbursement Amount",
  "Child Support Amount",
  "Absence Case Number",
  "C Value",
  "I Value",
  "Federal Withholding I Value",
  "State Withholding I Value",
  "Employer Reimbursement I Value",
  "Child Support I Value",
  "Absence Case Creation Date",
  "Absence Case Start Date",
  "Absence Case End Date",
  "Case Status",
  "Leave Request Decision",
  "Check Memo",
  "First Time Payment",
  "Previously Errored Payment Count",
  "Previously Rejected Payment Count",
  "Previously Skipped Payment Count",
  "DUA Additional Income",
  "DIA Additional Income",
  "Outstanding Overpayments",
  "Reject",
  "Skip",
  "Reject Notes",
  "Previously Paid Payment Count",
  "List of Previously Paid Payments",
  "Has Payment in prior batch MMARS/ PUB",
  "Payment Date Mismatch",
  "Is Pre-approved",
  "Pre-approval Issues",
  "Waiting Week",
] as const;

export type PaymentAuditPaymentField =
  | "Payment Amount"
  | "Gross Payment Amount"
  | "Federal Withholding Amount"
  | "State Withholding Amount"
  | "DUA Additional Income"
  | "DIA Additional Income";

export type ApplicationLeaveDetailsReason =
  | "Pregnancy/Maternity"
  | "Child Bonding"
  | "Serious Health Condition - Employee"
  | "Care for a Family Member"
  | "Military Caregiver"
  | "Military Exigency Family";
