import fileConfiguration from "../config.json";

export type EnvironmentVariableName = keyof typeof environmentVariableTypes;

export type RawEnvironment = {
  [K in EnvironmentVariableName]: string | undefined;
};

export type ParsedEnvironment = {
  [K in EnvironmentVariableName]: StringLiteralToPrimitive<
    (typeof environmentVariableTypes)[K]
  >;
};

// prettier-ignore
export type StringLiteralToPrimitive<T> =
  T extends "string" ? string :
  T extends "number" ? number :
  T extends "boolean" ? boolean :
  never;

const environmentVariableTypes = {
  ENVIRONMENT: "string",
  PORTAL_BASEURL: "string",
  PORTAL_PASSWORD: "string",
  PORTAL_USERNAME: "string",
  EMPLOYER_PORTAL_PASSWORD: "string",

  API_BASEURL: "string",
  API_FINEOS_CLIENT_ID: "string",
  API_FINEOS_CLIENT_SECRET: "string",
  API_MMG_FINEOS_CLIENT_ID: "string",
  API_MMG_FINEOS_CLIENT_SECRET: "string",

  API_SNOW_CLIENT_ID: "string",
  API_SNOW_CLIENT_SECRET: "string",
  API_MMG_SNOW_CLIENT_ID: "string",
  API_MMG_SNOW_CLIENT_SECRET: "string",

  FINEOS_BASEURL: "string",
  FINEOS_USERNAME: "string",
  FINEOS_PASSWORD: "string",
  FINEOS_DEPT_USER_PASSWORD: "string",
  FINEOS_USERS: "string",

  SSO_USERNAME: "string",
  SSO_PASSWORD: "string",
  SSO2_USERNAME: "string",
  SSO2_PASSWORD: "string",

  TESTMAIL_APIKEY: "string",
  TESTMAIL_NAMESPACE: "string",

  EMPLOYEES_FILE: "string",
  EMPLOYERS_FILE: "string",
  EMPLOYERS_MP_FILE: "string",
  EXEMPT_EMPLOYER_FILE: "string",
  EMPLOYEES_FOR_EXEMPT_EMPLOYER_FILE: "string",

  BENEFIT_YEAR_EMPLOYEE_FILE: "string",
  BENEFIT_YEAR_EMPLOYER_FILE: "string",
  RUN_BENEFIT_YEAR_TESTS: "boolean",

  ORGUNIT_EMPLOYEES_FILE: "string",
  ORGUNIT_EMPLOYERS_FILE: "string",
  NO_ORGUNIT_EMPLOYEES_FILE: "string",
  DEFAULT_ORGUNIT_EMPLOYEES_FILE: "string",

  CPS_EMPLOYEES_FILE: "string",
  CPS_EMPLOYERS_FILE: "string",

  DUA_EMPLOYEES_FILE: "string",
  DUA_EMPLOYERS_FILE: "string",

  CONCURRENT_EMPLOYMENT_EMPLOYERS_FILE: "string",
  CONCURRENT_EMPLOYMENT_EMPLOYEES_FILE: "string",

  HAS_ORGUNITS_SETUP: "boolean",
  HAS_DEFAULT_ORGUNITS_SETUP: "boolean",
  HAS_LIVECHAT_SETUP: "boolean",

  FINEOS_AWS_IAM_ROLE_ARN: "string",

  LAP2_EC2_INSTANCEID: "string",

  LST_EMPLOYEES_FILE: "string",
  LST_EMPLOYERS_FILE: "string",
  LST_PAYMENTS_FILE: "string",

  NEWRELIC_APIKEY: "string",
  NEWRELIC_ACCOUNTID: "number",
  NEWRELIC_INGEST_KEY: "string",

  DOR_IMPORT_URI: "string",
  DOR_ETL_ARN: "string",

  LST_FILE_RANGE: "string",
  LST_SCENARIOS: "string",
  S3_INTELLIGENCE_TOOL_BUCKET: "string",
  S3_MASSGOV_PFML_REPORTS_BUCKET: "string",
  S3_MASSGOV_PFML_AGENCY_TRANSFER_BUCKET: "string",

  FINEOS_AWS_IAM_ROLE_EXTERNAL_ID: "string",
  FINEOS_DATA_EXPORTS_BUCKET: "string",
  DB_HOSTNAME: "string",
  DB_NAME: "string",
  DB_PW: "string",
  FINEOS_CLIENT_ID: "string",
  FINEOS_CLIENT_SECRET: "string",
  FINEOS_API_BASEURL: "string",
  HAS_FINEOS_DEPARTMENTS_SET_UP: "boolean",
  HAS_GOOGLE_ANALYTICS: "boolean",
  GOOGLE_ANALYTICS_PATTERN: "string",
  USE_SERVICE_AGREEMENT_VERSIONS: "boolean",
  USE_MMG_SSO: "boolean",
  SAVE_EMAIL_URLS: "boolean",
  MMG_PERSONAL_BASE_URL: "string",
  MMG_BUSINESS_BASE_URL: "string",
  RMV_CHECK_IS_FULLY_MOCKED: "boolean",
  AUTHENTICATOR_KEY_TABLE: "string",
  AWS_REGION: "string",
  LOG_TEST_RESULTS: "boolean",

  USE_LATEST_RFI_NOTIFICATION_CONTENT: "boolean",

  HAS_PROFILE_IDV_FEATURE: "boolean",
  HAS_FINEOS_SSO_LANDING_PAGE: "boolean",
  MMG_IDV_PASSWORD: "string",
  HAS_FR25_1: "boolean",
} as const;

export function getRawEnvironment() {
  const entries = Object.keys(environmentVariableTypes).map((key) => [
    key,
    process.env[`E2E_${key}`],
  ]);

  return Object.fromEntries(entries) as RawEnvironment;
}

export function getParsedEnvironment(rawEnvironment: RawEnvironment) {
  const entries = parseEntries(rawEnvironment);

  return Object.fromEntries(entries) as ParsedEnvironment;
}

export function getRawFileEnvironment(env: string) {
  if (!(env in fileConfiguration)) {
    throw new Error(
      `Requested config for nonexistent environment: ${env}. Make sure this environment is defined in config.json`
    );
  }
  // The file layer is the configuration defined in config.json for this environment.
  return env in fileConfiguration
    ? (fileConfiguration[
        env as keyof typeof fileConfiguration
      ] as RawEnvironment)
    : ({} as RawEnvironment);
}

function parseEntries(file: RawEnvironment) {
  return Object.entries(file).map(([key, value]) => [
    key as EnvironmentVariableName,
    parseValue(key as EnvironmentVariableName, value),
  ]);
}

export function validateEnvironmentVariables(
  configuration: ParsedEnvironment
): string[] {
  return Object.entries(configuration)
    .filter(([_key, value]) => {
      return value === undefined || (typeof value === "number" && isNaN(value));
    })
    .map(([key]) => key);
}

function parseValue(key: EnvironmentVariableName, value: string | undefined) {
  const environmentVariableType = environmentVariableTypes[key];
  if (value === undefined) {
    return undefined;
  }

  switch (environmentVariableType) {
    case "number":
      if (Number.isNaN(+value)) {
        throw new Error(
          `The value of the environment variable ${key} is not a number: ${value}`
        );
      }

      return +value;
    case "boolean":
      if (value.toLowerCase() === "true") {
        return true;
      }
      if (value.toLowerCase() === "false") {
        return false;
      }

      throw new Error(
        `The value of the environment variable ${key} is not a boolean: "${value}". Please use "true" or "false"`
      );
    case "string":
      return value;
    default:
      // If there are any new types added to environmentVariableTypes, this will throw a compile-time error.
      // Make sure to include the new types as cases in this switch statement.
      throwOnUnknownType(key, environmentVariableType);
  }
}

function throwOnUnknownType(key: string, valueType: never) {
  throw new Error(
    `Unrecognized configuration value type for environment variable ${key}: ${valueType}`
  );
}
