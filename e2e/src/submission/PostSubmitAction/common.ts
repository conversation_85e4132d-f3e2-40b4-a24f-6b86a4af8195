import { Page } from "playwright-chromium";

import config from "../../config";
import { GeneratedClaim, GeneratedClaimMetadata } from "../../generation/Claim";
import { ClaimPage } from "../fineos.pages";

export async function receivePostModificationDocuments(
  claimPage: ClaimPage,
  metadata: GeneratedClaimMetadata
) {
  const { documentsToReceivePostModification } = metadata;

  if (documentsToReceivePostModification) {
    await claimPage.adjudicate(async (adjudication) =>
      adjudication.evidence(async (evidence) => {
        await evidence.receiveAll(documentsToReceivePostModification);
      })
    );
  }
}

export async function approveClaim(
  page: Page,
  claim: GeneratedClaim,
  fineosAbsenceId: string,
  isPostProcessing?: boolean
): Promise<void> {
  const claimPage = await ClaimPage.visit(page, fineosAbsenceId);
  await claimPage.adjudicate(async (adjudication) => {
    await adjudication.completeAdjudication(claim.documents, {
      acceptLeavePlan: !config("HAS_FR25_1"),
    });
  });

  const closeAlertsAndOutstandingRequirements = async () => {
    await claimPage.closeAlertsIfShown();

    await claimPage.outstandingRequirements(async (outstandingRequirements) => {
      if (!(await outstandingRequirements.areRequirementsComplete())) {
        await outstandingRequirements.complete();
      }
    });
  };

  if (
    !claim.metadata?.useFineosSubmitter &&
    !claim.employerResponse?.has_amendments &&
    claim.employerResponse?.employer_decision === "Approve"
  ) {
    await claimPage.tasks(async (tasks) => {
      await tasks.all();
      await tasks.close("Employer Approval Received");
    });

    if (isPostProcessing) {
      await closeAlertsAndOutstandingRequirements();
    }
  } else {
    await closeAlertsAndOutstandingRequirements();
  }
  if (config("HAS_FR25_1")) {
    await claimPage.adjudicate(async (adjudication) => {
      await adjudication.progressToFullyAdjudicated();
    });
  } else {
    await claimPage.approve();
  }
}

export async function postProcessModification(
  postProcess: string,
  page: Page,
  claimPage: ClaimPage,
  claim: GeneratedClaim,
  fineosAbsenceId: string
) {
  switch (postProcess) {
    case "adjudicate": {
      await claimPage.adjudicate(async (adjudication) => {
        await adjudication.completeAdjudication(claim.documents, {
          acceptLeavePlan: true,
        });
      });

      break;
    }

    case "approve": {
      await approveClaim(page, claim, fineosAbsenceId, true);
      break;
    }
  }
}
