import { addDays, format, parseISO } from "date-fns";
import { Page } from "playwright-chromium";

import { GeneratedClaim } from "../../generation/Claim";
import { getPortalSubmitter } from "../../util/common";
import { ClaimPage } from "../fineos.pages";
import { findLeaveEnd } from "../writers/SubmittedClaimIndex";
import {
  approveClaim,
  postProcessModification,
  receivePostModificationDocuments,
} from "./common";
import { PostSubmitAction } from "./types";
import wrapFineosPostSubmitFunction from "./wrapFineosPostSubmitAction";

export const approveWithModifications: PostSubmitAction = {
  serializedName: "approveWithModifications",
  claimStatusOnSuccess: "Approved",
  execute: ({ claim, fineosAbsenceId }) =>
    wrapFineosPostSubmitFunction(
      (page, fineosAbsenceId) =>
        approveClaimWithModifications(page, claim, fineosAbsenceId),
      fineosAbsenceId
    ),
};

async function approveClaimWithModifications(
  page: Page,
  claim: GeneratedClaim,
  fineosAbsenceId: string
) {
  await approveClaim(page, claim, fineosAbsenceId);
  const claimPage = await ClaimPage.visit(page, fineosAbsenceId);
  await claimPage.tasks(async (tasksPage) => {
    await tasksPage.triggerNotice("Designation Notice");
  });

  if (!claim.metadata?.modifications) {
    throw Error(
      "Unable to submit modification request due to missing property: 'metadata.modifications'"
    );
  }

  const submitter = getPortalSubmitter();

  for (const modification of claim.metadata.modifications) {
    await submitter.submitModification(fineosAbsenceId, modification, claim);

    const postProcess = modification?.postProcess;
    const [_, endDate] = format(findLeaveEnd(claim.claim), "P");
    if (!modification.end_date) {
      throw Error(
        "Unable to submit modification request due to missing property: 'end_date'"
      );
    }

    const extensionEndDate = format(
      parseISO(modification.end_date),
      "MM/dd/yyyy"
    );

    const cancellationDate = format(
      addDays(parseISO(modification.end_date), 1),
      "MM/dd/yyyy"
    );

    const extensionStartDate = format(
      addDays(new Date(endDate), 1),
      "MM/dd/yyyy"
    );

    const changeRequestType = modification.change_request_type;

    switch (changeRequestType) {
      case "Extension": {
        const benefitsExtensionPage = await claimPage.visitBenefitsPage();
        await benefitsExtensionPage.extendLeave({
          startDate: extensionStartDate,
          endDate: extensionEndDate,
        });

        break;
      }

      case "Cancellation": {
        const cancelPage = await claimPage.visitCancelClaimPage();
        await cancelPage.cancelClaim({
          cancellationStartDate: cancellationDate,
        });
        continue;
      }

      default: {
        throw Error(
          `Unable to submit modification request due to unsupported change request type: ${changeRequestType}`
        );
      }
    }

    if (postProcess) {
      await postProcessModification(
        postProcess,
        page,
        claimPage,
        claim,
        fineosAbsenceId
      );
    }

    await receivePostModificationDocuments(claimPage, claim.metadata);
  }
}
