import { faker } from "@faker-js/faker";
import { add, format } from "date-fns";
import delay from "delay";
import { <PERSON><PERSON><PERSON>, <PERSON> } from "playwright-chromium";

import {
  AbsencePeriodResponse,
  Address,
  ReasonQualifierOne,
  ReducedScheduleLeavePeriods,
} from "../../_api";
import config from "../../config";
import { GeneratedClaim, LeaveReason } from "../../generation/Claim";
import EmployerPool from "../../generation/Employer";
import {
  AbsenceReasonDescription,
  AbsenceStatus,
  ContinuousLeavePeriod,
  PersonalIdentificationDetails,
  RequireNotNull,
  TypeOfRequestOptions,
  ValidClaim,
} from "../../types";
import {
  dateToMMddyyyy,
  extractLeavePeriod,
  extractLeavePeriodType,
  getFineosPreferredLanguage,
  getLeavePeriod,
} from "../../util/claims";
import * as util from "../../util/playwright";
import { ClaimantPage } from "./ClaimantPage";
import { FineosPage } from "./FineosPage";

export const reasonToRequestTypeMap: Record<
  NonNullable<LeaveReason>,
  TypeOfRequestOptions
> = {
  "Care for a Family Member": "Caring for a family member",
  "Child Bonding": "Bonding with a new child (adoption/ foster care/ newborn)",
  "Military Caregiver": "Out of work for another reason",
  "Military Exigency Family": "Out of work for another reason",
  "Pregnancy/Maternity": "Pregnancy, birth or related medical treatment",
  "Serious Health Condition - Employee":
    "Accident or treatment required for an injury",
};

export function getAbsenceReasonDescription(
  reason: string,
  reasonQualifier: ReasonQualifierOne | undefined | null
): AbsenceReasonDescription {
  switch (reason) {
    case "Care for a Family Member": {
      return {
        qualifier_1: "Serious Health Condition",
      };
    }
    case "Child Bonding": {
      if (reasonQualifier) {
        return {
          qualifier_1: reasonQualifier,
        };
      }

      break;
    }
    case "Serious Health Condition - Employee": {
      return {
        relates_to: "Employee",
        reason: "Serious Health Condition - Employee",
        qualifier_1: "Not Work Related",
        qualifier_2: "Accident / Injury",
      };
    }
    case "Pregnancy/Maternity": {
      return {
        relates_to: "Employee",
        reason: "Pregnancy/Maternity",
        qualifier_1: "Prenatal Care",
      };
    }
    case "Military Caregiver": {
      return {
        relates_to: "Family",
        reason: "Military Caregiver",
      };
    }
    case "Military Exigency Family": {
      return {
        relates_to: "Family",
        reason: "Military Exigency Family",
        qualifier_1: "Other Additional Activities",
      };
    }
  }

  throw new Error(`Invalid leave reason.`);
}

export class CreateNotificationPage extends FineosPage {
  constructor(page: Page) {
    super(page);
  }

  async visitClaimantPage(ssn: string) {
    await ClaimantPage.visit(this.page, ssn);
  }

  async addAddress(
    address: RequireNotNull<Address, "city" | "line_1" | "state" | "zip">
  ) {
    await this.page.waitForLoadState("domcontentloaded");

    const hasAddress = await this.page.$(
      "#addressesMultiPaint:has-text('Mailing address')"
    );
    if (hasAddress) {
      return;
    }

    await this.page.click("a:has-text('+ Add address')");
    const popup = await this.page
      .locator("#addressPopupWidget_PopupWidgetWrapper")
      .first();

    await this.fillInputWithLabel(popup, "Address line 1", address.line_1);
    await this.fillInputWithLabel(popup, "City", address.city);
    await this.chooseSelectOption(popup, "State", address.state);
    await this.fillInputWithLabel(popup, "Zip code", address.zip);
    await this.page.click(
      '#addressPopupWidget_PopupWidgetWrapper input[title="OK"]'
    );
  }

  async editPersonalInformation(
    changes: Partial<PersonalIdentificationDetails>
  ) {
    await this.page.click("#personalIdentificationCardWidget [title='Edit']");
    const popup = await this.page
      .locator("#cardEditPopupWidget_PopupWidgetWrapper")
      .first();

    if (changes.date_of_birth) {
      await this.fillInputWithLabel(
        popup,
        "Date of birth",
        changes.date_of_birth
      );
    }

    await this.page.click(
      '#cardEditPopupWidget_PopupWidgetWrapper input[title="OK"]'
    );
  }

  async startCreateNotification() {
    // Start the process
    await this.page.click("span:has-text('Create Notification')");
  }

  async fillOccupationDetailsStep(claim: ValidClaim) {
    if (claim.employer_fein) {
      const employerSelect = await this.page.locator(
        '[data-test-id="employer-select"], [data-testid="employer-select"]' // Pretty sure I've seen FINEOS return both.
      );

      if (employerSelect) {
        // This is not ideal. We should be able to specify the employers file. As of now, you need to manually copy in employers.
        const employers = await EmployerPool.load(config("EMPLOYERS_FILE"));
        const employer = employers.get(claim.employer_fein);
        if (!employer) {
          throw new Error(
            `${claim.employer_fein} not found in config("EMPLOYERS_FILE")`
          );
        }

        await employerSelect.click({ force: true });
        const option = await this.page.locator(
          `div.ant-select-item-option-content:has-text("${employer.name}")`
        );

        await option.click({ force: true });
      }
    }

    if (claim.hours_worked_per_week) {
      await this.fillInputWithLabel(
        this.page,
        "Hours worked per week",
        claim.hours_worked_per_week.toString()
      );
    }
  }

  async fillNotificationOptionsStep(reason: NonNullable<LeaveReason>) {
    await this.setLeaveReason(reasonToRequestTypeMap[reason]);
  }

  /**
   * Fills out the Absence Relationships select fields with given data
   * @param relationshipToEmployee
   * @param qualifier1
   */
  async fillAbsenceRelationship(
    relationshipToEmployee: string,
    qualifier1: string | null
  ) {
    const widget = await this.page
      .locator("#leaveRequestAbsenceRelationshipsWidget")
      .first();

    await this.chooseSelectOption(
      widget,
      "Primary Relationship to Employee",
      relationshipToEmployee
    );

    if (qualifier1) {
      await this.chooseSelectOption(widget, "Qualifier 1", qualifier1);
    }
  }

  async fillReasonsForAbsenceStep(
    claim: ValidClaim,
    reason: NonNullable<LeaveReason>
  ) {
    // Fill reason of absence depending on claim contents.
    const absenceReasonDescription = getAbsenceReasonDescription(
      reason,
      claim.leave_details?.reason_qualifier
    );

    await this.fillAbsenceReason(absenceReasonDescription);

    if (reason === "Child Bonding") {
      await delay(250);

      if (absenceReasonDescription.qualifier_1 === "Foster Care") {
        await this.fillAbsenceRelationship("Child", "Foster");
      } else if (absenceReasonDescription.qualifier_1 === "Adoption") {
        await this.fillAbsenceRelationship("Child", "Adopted");
      } else if (absenceReasonDescription.qualifier_1 === "Newborn") {
        await this.fillAbsenceRelationship("Child", "Biological");
      }
    }

    if (
      reason === "Care for a Family Member" ||
      reason === "Military Caregiver"
    ) {
      await delay(250);
      await this.fillAbsenceRelationship(
        "Sibling - Brother/Sister",
        "Biological"
      );
    }
  }

  async addIntermittentLeavePeriod(start: string, end: string) {
    // Since the widget also gets re-rendered from time to time, we need to re-query it frequently.
    const widget = this.page
      .locator(`#captureEpisodicLeaveDetailsWidget`)
      .first();
    await this.page.click("input[title='Add a new episodic absence period']");
    await this.fillInputWithLabel(widget, "Valid from", dateToMMddyyyy(start));
    await util.waitForStablePage(this.page);
    await delay(250);
    await this.fillInputWithLabel(widget, "Valid to", dateToMMddyyyy(end));
    await util.waitForStablePage(this.page);
    await delay(250);
  }

  async fillDatesOfAbsenceStep(claim: ValidClaim) {
    if (claim.leave_details) {
      const [startDate, endDate] = getLeavePeriod(claim.leave_details);
      const leavePeriodType = extractLeavePeriodType(claim.leave_details);

      await this.toggleLeaveScheduleSlider(leavePeriodType);

      if (claim.has_continuous_leave_periods) {
        await this.addFixedTimeOffPeriod({
          status: "Known",
          start: startDate,
          end: endDate,
        });

        await this.page.waitForSelector(
          "#timeOffAbsencePeriodsListviewQuickAddWidget label:has-text('1-1 of 1')"
        );
      }

      if (leavePeriodType === "Intermittent") {
        await this.addIntermittentLeavePeriod(startDate, endDate);
      }

      if (
        claim.has_reduced_schedule_leave_periods &&
        claim.leave_details.reduced_schedule_leave_periods
      ) {
        await this.addReducedSchedulePeriod(
          "Known",
          claim.leave_details.reduced_schedule_leave_periods[0]
        );

        await this.page.waitForSelector(
          "#reducedScheduleAbsencePeriodDetailsContainerWidget label:has-text('1-1 of 1')"
        );
      }
    }
  }

  async fillWorkAbsenceDetailsStep(claim: ValidClaim) {
    if (!claim.work_pattern?.work_pattern_type) {
      throw new Error("Missing work pattern");
    }

    const workPatternType =
      claim.work_pattern.work_pattern_type === "Rotating"
        ? "2 weeks Rotating"
        : claim.work_pattern.work_pattern_type;

    if (config("HAS_FR25_1")) {
      const workPatternWidget = await WorkPatternWidget.open(this.page);
      await workPatternWidget.selectWorkPatternType(workPatternType);
      const [start, end] = extractLeavePeriod(claim);
      await workPatternWidget.applyStandardWorkWeek(start, end);
    } else {
      await this.selectWorkPatternType(workPatternType);
      await this.applyStandardWorkWeek();
    }
  }

  async fillAdditionalAbsenceDetailsStep(
    _claim: ValidClaim,
    reason: NonNullable<LeaveReason>,
    isWithholdingTax: boolean
  ) {
    // tax withholdings
    if (isWithholdingTax) {
      await this.setWitholdTax();
    }

    await this.setSITFITVerification();

    if (reason === "Child Bonding") {
      await this.page.fill(
        'label:text-is("What is your Family Member\'s Date of Birth?")',
        format(
          faker.date.between({
            from: add(new Date(), { months: -1 }),
            to: add(new Date(), { months: 0 }),
          }),
          "MM/dd/yyyy"
        )
      );
    }

    // Fill military Caregiver description if needed.
    if (reason === "Military Caregiver") {
      await this.page.fill(
        "label:text-is('Military Caregiver Description')",
        "I am a parent military caregiver."
      );
    }
  }

  async setPreferredLanguage(language: GeneratedClaim["preferredLanguage"]) {
    await this.page.click("#languagesCardWidget [title='Edit']");
    const optionLabel = getFineosPreferredLanguage(language);
    await this.page
      .locator("select[id^='languagesWidget']")
      .first()
      .selectOption({ label: optionLabel });
    await this.page
      .locator(
        "div[id$='LanguagesPopupWidget_PopupWidgetWrapper'] input[title='OK']"
      )
      .click();
  }

  private async setLeaveReason(reason: string): Promise<void> {
    await this.page
      .locator(`[type=radio]:left-of(label:has-text('${reason}'))`)
      .first()
      .click();
    await delay(2000);
  }

  async clickNext(): Promise<void> {
    await this.page.click('#navButtons input[value="Next "]');
    await util.waitForStablePage(this.page);
  }

  private async fillAbsenceReason(desc: AbsenceReasonDescription) {
    if (desc.relates_to) {
      await this.chooseSelectOption(
        this.page,
        "Absence relates to",
        desc.relates_to
      );
    }
    if (desc.reason) {
      await this.chooseSelectOption(this.page, "Absence reason", desc.reason);
    }
    if (desc.qualifier_1) {
      await this.chooseSelectOption(this.page, "Qualifier 1", desc.qualifier_1);
    }
    if (desc.qualifier_2) {
      await this.chooseSelectOption(this.page, "Qualifier 2", desc.qualifier_2);
    }
  }

  private async toggleLeaveScheduleSlider(
    type: NonNullable<AbsencePeriodResponse["period_type"]>
  ): Promise<void> {
    const scheduleSliderMap: Record<typeof type, string> = {
      Continuous: "One or more fixed time off periods",
      Intermittent: "Episodic / leave as needed",
      "Reduced Schedule": "Reduced work schedule",
    };
    await this.page
      .locator(".toggle-guidance-row", {
        has: this.page.locator(`text='${scheduleSliderMap[type]}'`),
      })
      .locator(".toggle-guidance-toggle-column")
      .click();
    await delay(1000);
  }

  private async addFixedTimeOffPeriod(period: ContinuousLeavePeriod) {
    await this.chooseSelectOption(this.page, "Absence status", period.status);
    const startDate = dateToMMddyyyy(period.start);
    const endDate = dateToMMddyyyy(period.end);
    await this.fillDateWithLabel("Absence start date", startDate);
    await this.fillDateWithLabel("Absence end date", endDate);

    if (period.last_day_worked) {
      await this.fillDateWithLabel(
        "Last day worked ",
        dateToMMddyyyy(period.last_day_worked)
      );
    }

    if (period.return_to_work_date) {
      await this.fillDateWithLabel(
        "Return to work date",
        dateToMMddyyyy(period.return_to_work_date)
      );
    }

    await util.waitForStablePage(this.page);
    await delay(500);
    await this.page.locator('input[title="Quick Add"]').first().click();
    await delay(1000);
  }

  private async enterReducedWorkHours(
    locator: Locator | Page,
    leave_details: ReducedScheduleLeavePeriods
  ) {
    const hrs = (minutes: number | null | undefined) => {
      return minutes ? Math.round(minutes / 60) : 0;
    };
    const weekdayInfo = [
      { hours: hrs(leave_details.sunday_off_minutes) },
      { hours: hrs(leave_details.monday_off_minutes) },
      { hours: hrs(leave_details.tuesday_off_minutes) },
      { hours: hrs(leave_details.wednesday_off_minutes) },
      { hours: hrs(leave_details.thursday_off_minutes) },
      { hours: hrs(leave_details.friday_off_minutes) },
      { hours: hrs(leave_details.saturday_off_minutes) },
    ];
    const fields = await locator.locator("input[name*='_hours']");
    const count = await fields.count();
    for (let index = 0; index < count; index++) {
      await fields.nth(index).fill(weekdayInfo[index].hours.toString());
    }
  }

  async addReducedSchedulePeriod(
    absenceStatus: AbsenceStatus,
    reducedLeavePeriod: ReducedScheduleLeavePeriods
  ) {
    const wrapper = this.page
      .locator("#reducedScheduleAbsencePeriodDetailsQuickAddWidget")
      .first();

    // Enter absence status
    await this.chooseSelectOption(wrapper, "Absence status", absenceStatus);

    if (reducedLeavePeriod.start_date) {
      await this.fillInputWithLabel(
        wrapper,
        "Absence start date",
        dateToMMddyyyy(reducedLeavePeriod.start_date)
      );
    }

    // After this the entire widget re-renders, and we need to re-query for it.
    if (reducedLeavePeriod.end_date) {
      await this.fillInputWithLabel(
        wrapper,
        "Absence end date",
        dateToMMddyyyy(reducedLeavePeriod.end_date)
      );
    }

    // Enter hours for each weekday
    await this.enterReducedWorkHours(wrapper, reducedLeavePeriod);
    // Submit period
    await wrapper.locator('input[title="Quick Add"]').first().click();
  }

  private async selectWorkPatternType(
    type:
      | "Unknown"
      | "Fixed"
      | "2 weeks Rotating"
      | "3 weeks Rotating"
      | "4 weeks Rotating"
      | "Variable"
  ): Promise<string[]> {
    const selectId = await this.page
      .locator('label:has-text("Work Pattern Type")')
      .first()
      .getAttribute("for");
    return await this.page
      .locator(`select[name='${selectId}']`)
      .first()
      .selectOption({ label: type });
  }

  private async applyStandardWorkWeek() {
    await this.page.click("label:has-text('Standard Work Week')");
    await this.page.click('input[value="Apply to Calendar"]');
  }

  private async setWitholdTax() {
    await this.page.click(
      "input[type='checkbox'][name$='_somSITFITOptIn_CHECKBOX']"
    );
  }

  private async setSITFITVerification() {
    await this.page.click(
      "input[type='checkbox'][name$='_somSITFITVerification_CHECKBOX']"
    );
  }

  /** Looks for the Leave Case number in the Wrap Up step and returns it wrapped by Cypress. */
  async getLeaveCaseNumber() {
    await this.page.waitForLoadState("domcontentloaded");
    const caseNumberMatcher = /NTN-[0-9]+-[A-Z]{3}-[0-9]{2}/g;
    const pageText = await this.page.content();
    const match = pageText?.match(caseNumberMatcher);

    if (!match) {
      throw new Error(`Couldn't find the Case Number on intake Wrap Up page.`);
    }

    return match[0];
  }
}

class WorkPatternWidget extends FineosPage {
  private constructor(page: Page) {
    super(page);
  }

  static async open(page: Page) {
    const createWorkPatternButton = page.getByTestId(
      "create-work-pattern-button"
    );
    const editWorkPatternButton = page.getByTestId("edit-work-pattern-button");

    const spinner = page.locator(".ant-spin-dot");
    await spinner.waitFor({ state: "detached" });

    if (await createWorkPatternButton.isEnabled()) {
      await createWorkPatternButton.click();
    } else {
      await editWorkPatternButton.click();
    }

    return new WorkPatternWidget(page);
  }

  async selectWorkPatternType(
    type:
      | "Unknown"
      | "Fixed"
      | "2 weeks Rotating"
      | "3 weeks Rotating"
      | "4 weeks Rotating"
      | "Variable"
  ) {
    // need to click this element to bring up a list, then click from the list.
    await this.workPatternTypeDropdown.click({ force: true }); // element is being hidden under the span containing the actual text.
    await this.page.getByTestId("work-pattern-type").getByText(type).click();
  }

  async applyStandardWorkWeek(startDate: Date, endDate: Date) {
    const formattedStartDate = format(startDate, "MM/dd/yyyy");
    const formattedEndDate = format(endDate, "MM/dd/yyyy");

    await this.fillInput(this.patternStartDate, formattedStartDate);
    await this.fillInput(this.patternEndDate, formattedEndDate);
    await this.applyStandardWorkWeekButton.click({ force: true });
    await this.close();
  }

  async close() {
    await this.closeButton.click();
  }

  private async fillInput(locator: Locator, content: string) {
    await locator.selectText();
    await locator.press("Backspace");
    await locator.fill(content);
    await locator.press("Enter");
  }

  private workPatternTypeDropdown = this.page.locator("input#workPatternType");
  private patternStartDate = this.page.getByTestId("pattern-start-date");
  private patternEndDate = this.page.getByTestId("pattern-end-date");
  private applyStandardWorkWeekButton = this.page.getByTestId(
    "apply-standard-work-week"
  );
  private closeButton = this.page.getByRole("button", { name: "OK" }).first();
}
