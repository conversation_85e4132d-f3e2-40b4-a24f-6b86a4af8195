import delay from "delay";
import pRetry from "p-retry";
import path from "path";
import { chromium, Page } from "playwright-chromium";
import { v4 as uuid } from "uuid";
import winston from "winston";

import defaultConfig, { ConfigFunction } from "../../config";
import { Credentials } from "../../types";
import * as util from "../../util/playwright";

export * from "./AdjudicationPage";
export * from "./CertificationPeriodsPage";
export * from "./ClaimantPage";
export * from "./ClaimPage";
export * from "./ConfigPage";
export * from "./CreateNotificationPage";
export * from "./EmployerAddressPage";
export * from "./EmployerPage";
export * from "./EvidencePage";
export * from "./FineosPage";
export * from "./PaidLeave";
export * from "./RequestDetailsPage";
export * from "./RolesPage";
export * from "./ServiceAgreementPage";

export type FineosBrowserOptions = {
  credentials?: Credentials;
  debug: boolean;
  screenshots?: string;
  slowMo?: number;
  logger?: winston.Logger;
  config?: ConfigFunction;
};
export class Fineos {
  static async withBrowser<T>(
    next: (page: Page) => Promise<T>,
    {
      debug = false,
      screenshots,
      slowMo,
      credentials,
      logger,
      config: configOverride,
    }: FineosBrowserOptions
  ): Promise<T> {
    const config = configOverride ?? defaultConfig;
    const isSSO =
      config("ENVIRONMENT") === "uat" ||
      config("ENVIRONMENT") === "breakfix" ||
      config("HAS_FR25_1");
    const browser = await chromium.launch({
      headless: !debug,
      slowMo: debug ? slowMo ?? 100 : slowMo,
      executablePath: process.env.PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH,
    });
    const httpCredentials = isSSO
      ? undefined
      : credentials ?? {
          username: config("FINEOS_USERNAME"),
          password: config("FINEOS_PASSWORD"),
        };
    const page = await browser.newPage({
      viewport: { width: 1200, height: 1000 },
      httpCredentials,
    });
    page.on("dialog", async (dialog) => {
      await delay(2000);
      await dialog.dismiss().catch(() => {
        //intentional no-op on error.
      });
    });

    const debugError = async (e: Error) => {
      // If we're in debug mode, pause the page wherever the error was thrown.
      if (debug) {
        console.error(
          "Caught error - holding browser window open for debugging.",
          e
        );
        await page.pause();
      }
      if (screenshots) {
        const filename = path.join(screenshots, `${uuid()}.jpg`);
        await page
          .screenshot({
            fullPage: true,
            path: filename,
          })
          .then(() => console.log(`Saved screenshot of error to ${filename}`))
          .catch((err) =>
            console.error("An error was caught during screenshot capture", err)
          );
      }
      return Promise.reject(e);
    };
    const start = async () => {
      // We have to wait for network idle here because the SAML redirect happens via JS, which is only triggered after
      // the initial load. So we can't determine whether we've been redirect to SSO unless we wait for network activity
      // to stop.
      await page.goto(config("FINEOS_BASEURL"), { timeout: 60000 });
      await page.waitForLoadState("domcontentloaded");

      if (isSSO) {
        const ssoCredentials = credentials ?? {
          username: config("SSO_USERNAME"),
          password: config("SSO_PASSWORD"),
        };

        if (config("HAS_FR25_1") && config("HAS_FINEOS_SSO_LANDING_PAGE")) {
          await page
            .getByText("Sign in with your corporate ID")
            .locator("xpath=following-sibling::*[1]")
            .nth(1)
            .click();
        }
        await page.fill('input[name="loginfmt"]', ssoCredentials.username);
        await page.click("input[value='Next']");
        await page.waitForSelector("a[id*='ForgotPassword']");
        await page.fill(
          "input[type='password'][name='passwd']",
          ssoCredentials.password
        );
        await page.click("input[value='Sign in']");
        // Sometimes we end up with a "Do you want to stay logged in" question.
        // This seems inconsistent, so we only look for it if we haven't already found ourselves
        // in Fineos.
        if (/login\.microsoftonline\.com/.test(page.url())) {
          await page.click("input[value='No']");
        }
        await page.waitForLoadState("domcontentloaded");
      }
      await page.waitForSelector("body.PageBody");
      /* @Note: Added for extra Stability
      ----------------------------------------------
      * Post the most recent Fineos Upgrade (May/June 2022)
      * During LST an increase in PP failures occur when
      * initially loading the Web App.  The Check below ensures
      * the page returned is clickable prior to any PP actions
      */
      process.env.IS_ECS === "true" && (await delay(1000));
      try {
        await pRetry(
          async () => {
            await Promise.all([
              util.waitForStablePage(page),
              util.stableFineosHomepage(page),
            ]);
          },
          {
            onFailedAttempt: async (e) => {
              logger?.warn(
                `Attempt #${e.attemptNumber} to determine Fineos homepage stability. Waiting 1 second before retry`,
                { retry_error_message: e }
              );
              await delay(1000);
            },
            forever: true,
            maxRetryTime: 60000,
            minTimeout: 3000,
          }
        );
      } catch (e) {
        throw new Error(
          `Fineos web app didnt load successfully within 60s: \n${e}`
        );
      }
      return page;
    };

    return start()
      .then(next)
      .catch(debugError)
      .finally(async () => {
        // Note: For whatever reason, we get sporadic crashes when calling browser.close() without page.close().
        // This sporadic crash is characterized by an ERR_IPC_CHANNEL_CLOSED error. We believe the issue is similar
        // to https://github.com/microsoft/playwright/issues/5327.
        await page.close();
        await browser.close();
      });
  }
}
