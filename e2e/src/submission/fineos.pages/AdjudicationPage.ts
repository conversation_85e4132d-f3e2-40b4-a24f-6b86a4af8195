import delay from "delay";
import { Page } from "playwright-chromium";

import config from "../../config";
import { DocumentToReceive } from "../../generation/Claim";
import * as util from "../../util/playwright";
import { CertificationPeriodsPage, EvidencePage, RequestDetailsPage } from ".";
import { FineosPage, FineosPageCallback } from "./FineosPage";

type AdjudicationOptions = {
  acceptLeavePlan: boolean;
};

export class AdjudicationPage extends FineosPage {
  constructor(page: Page) {
    super(page);
  }

  async completeAdjudication(
    documents: DocumentToReceive[],
    options: AdjudicationOptions
  ) {
    const acceptLeavePlan = options?.acceptLeavePlan || false;

    if (config("HAS_FR25_1")) {
      const managePlansButton = this.page.locator(
        'button:has-text("Manage Plans")'
      );
      await managePlansButton.waitFor({ state: "visible" });
      await managePlansButton.click();

      await this.evidence(async (evidence) => {
        await evidence.receiveAll(documents);
      });

      await this.certificationPeriods(async (certification) => {
        await certification.prefill();
      });
    } else {
      await this.evidence(async (evidence) => {
        await evidence.receiveAll(documents);
      });
      await this.certificationPeriods(async (certification) => {
        await certification.prefill();
      });

      if (acceptLeavePlan) {
        await this.acceptLeavePlan();
      }
    }
  }

  async progressToFullyAdjudicated(): Promise<void> {
    await this.page
      .locator('button[data-testid="leave-request-secondary-actions"]')
      .click();
    await util.waitForStablePage(this.page);
    await this.page
      .locator('li[data-testid="leave-request-bulk-accept"]')
      .click();
    await this.page.locator('button:has-text("Progress Plans")').click();
    await util.waitForStablePage(this.page);
    this.page.locator("#leave-request-progress-window-ok-button").click();
    await util.waitForStablePage(this.page);
    await this.page.waitForSelector(".ant-modal-wrap", { state: "detached" });

    await this.page.waitForSelector(".ant-spin-text", {
      state: "detached",
      timeout: 40_000,
    });
    await this.page.click('role=link[name="Absence Case"]');
  }

  async evidence(cb: FineosPageCallback<EvidencePage>): Promise<void> {
    if (config("HAS_FR25_1")) {
      await this.clickAntTab("Evidence");
    } else {
      await this.onTab("Evidence");
    }
    await cb(new EvidencePage(this.page));
  }
  async certificationPeriods(
    cb: FineosPageCallback<CertificationPeriodsPage>
  ): Promise<void> {
    if (config("HAS_FR25_1")) {
      await this.clickAntTab("Certification Periods");
    } else {
      await this.onTab("Evidence", "Certification Periods");
    }
    await cb(new CertificationPeriodsPage(this.page));
  }

  async requestDetails(
    cb: FineosPageCallback<RequestDetailsPage>
  ): Promise<void> {
    await this.onTab("Request Information", "Request Details");
    await cb(new RequestDetailsPage(this.page));
  }

  async denyLeavePlan(): Promise<void> {
    await this.onTab("Manage Request");
    await this.page.locator('button:has-text("Reject")').click();
    await this.page.click('input[type="search"]');
    await this.page
      .locator("#dropdown-reject-reason")
      .type("Employee not eligible");
    await this.page.click('[title="Employee not eligible"]');
    await this.page.locator('button:has-text("OK")').click();
    await delay(150);
  }

  async acceptLeavePlan(): Promise<void> {
    await this.onTab(`Manage Request`);
    await util.waitForStablePage(this.page);
    await delay(2000);
    await this.page.click('span:text-is("Accept")');
    await delay(1000);
    await util.waitForStablePage(this.page);
  }
}
