import { Page } from "playwright-chromium";

import * as util from "../../util/playwright";
import { FineosPage } from "./FineosPage";

export class CancelPage extends FineosPage {
  constructor(page: Page) {
    super(page);
  }

  async cancelClaim({
    cancellationStartDate,
  }: {
    cancellationStartDate: string;
  }) {
    await this.recordCancelledTime(cancellationStartDate);
    await util.waitForStablePage(this.page);
    await this.addAdditionalInformation();
    await util.waitForStablePage(this.page);
    await this.makeFinalDecision();
    return this;
  }

  async recordCancelledTime(cancellationStartDate: string) {
    await this.page.getByRole("cell", { name: "Known", exact: true }).click();
    await this.page.click(
      'input[title="Record Cancelled Time for the selected absence period"]'
    );
    await this.page.fill('input[id$="_startDate"]', cancellationStartDate);
    await this.clickContinuationBtn("input:has-text('Ok')");
    await this.clickContinuationBtn("input:has-text('Next')");
  }

  private async addAdditionalInformation() {
    await this.page.selectOption('select[id$="reportedBy"]', "Employee");
    await util.waitForStablePage(this.page);
    await this.page.waitForSelector('select[id$="receivedVia"]', {
      state: "visible",
      timeout: 5000,
    });
    await this.page.selectOption('select[id$="receivedVia"]', {
      label: "Phone",
    });
    await this.page.selectOption(
      'select[id$="cancellationReason"]',
      "Employee Requested Cancellation"
    );

    await this.page.click(
      'input[type="checkbox"][id$="MasterMultiSelectCB_CHECKBOX"]'
    );
    await this.page.click('input[type="submit"][title="Apply"]');
    await this.clickContinuationBtn("input:has-text('Next')");
  }

  private async makeFinalDecision() {
    const dropdown = await this.page.waitForSelector(
      '//select[contains(@id, "period-decision-status")]',
      { timeout: 30_000 }
    ); // Wait for the drop down to render
    await dropdown.selectOption("Approved");

    await this.page.click(
      'input[type="checkbox"][id$="MasterMultiSelectCB_CHECKBOX"]'
    );
    await this.page.click('input[type="submit"][title="Apply"]');
    await this.clickContinuationBtn("input:has-text('Next')");
  }

  private async clickContinuationBtn(selector: string) {
    await util.waitForStablePage(this.page);
    await this.page.click(selector);
  }
}
