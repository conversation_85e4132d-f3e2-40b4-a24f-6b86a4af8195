import { codeFrameColumns } from "@babel/code-frame";
import { getTestNames as getCypressTestNames, Results } from "find-test-names";
import fs from "fs";
import glob from "glob";
import {
  DescribeBlock,
  ItBlock,
  parse as getJestTestNames,
} from "jest-editor-support";
import {
  getStackTraceLines,
  getTopFrame,
  separateMessageFromStack,
} from "jest-message-util";
import yaml from "js-yaml";
import { camelCase } from "lodash";
import { Suite as MochaSuite } from "mocha";
import fetch from "node-fetch";
import os from "os";
import pRetry from "p-retry";
import path from "path";
import { parseSync } from "yargs";

import config from "../config";
import { getPostRunMetadataLocation } from "../util/metadata";
import {
  CypressBeforeRunDetails,
  CypressMochaSuite,
  CypressMochaTest,
  CypressPreRunMetadata,
  ErrorCategory,
  NRExpectedTestsPerRunEvent,
  NRTestRunEvent,
  ParsedErrorMessage,
  PostRunMetadata,
  TestRunMetadata,
} from "./types";

const tempDir = os.tmpdir();

/**
 * Send an Event to New Relic
 */
export async function pushEventsToNewRelic<T extends NRTestRunEvent[]>(
  events: T,
  isRunningLocally = false
) {
  if (config("LOG_TEST_RESULTS")) {
    console.error(events);
  }

  if (isRunningLocally) {
    return events;
  }

  // Get NR credentials from env
  const accountId = config("NEWRELIC_ACCOUNTID");
  const apiKey = config("NEWRELIC_INGEST_KEY");
  const formattedEvents = events.map(formatEventForNewRelic);

  try {
    // Push new event to New Relic
    const sendEventToNewRelic = async () => {
      const res = await fetch(
        `https://insights-collector.newrelic.com/v1/accounts/${accountId}/events`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Api-Key": apiKey,
          },
          body: JSON.stringify(formattedEvents),
        }
      );
      if (!res.ok) {
        throw new Error(
          `New Relic responded with status ${res.status} (${res.statusText})`
        );
      }
      return res;
    };
    // Retry sending the event to New Relic
    await pRetry(sendEventToNewRelic, {
      onFailedAttempt: (error) => {
        console.error(`Attempt ${error.attemptNumber} failed.`);
      },
      retries: 3,
    });
    // If successful return events
    return events;
  } catch (e) {
    console.error(
      `An error occurred while reporting to New Relic: ${e.message || e}`
    );
    return Promise.reject(
      `The reporter received an error while attempting to report a result: ${e}`
    );
  }
}

function formatEventForNewRelic(event: NRTestRunEvent): NRTestRunEvent {
  const formattedEntries = Object.entries(event).map(([key, value]) => {
    const formattedValue =
      typeof value === "string"
        ? truncateNewRelicCustomAttributeValue(value)
        : value;

    return [key, formattedValue];
  });

  return Object.fromEntries(formattedEntries);
}

export async function beforeRunCollectMetadata(
  details: CypressBeforeRunDetails
): Promise<void> {
  // Assert that required data is present
  const isRunningLocally = Object.values(
    details.config.reporterOptions ?? {}
  ).some((o) => (typeof o === "object" ? o.isRunningLocally : false));

  if (isRunningLocally) {
    details.runUrl = details.runUrl ?? UNKNOWN;
    details.tag = details.tag ?? [UNKNOWN];
    details.group = details.group ?? UNKNOWN;
  } else {
    if (
      details.runUrl === undefined ||
      details.tag === undefined ||
      details.group === undefined
    ) {
      throw new Error(
        "Cypress on(before:run) was unable to retrieve required data!"
      );
    }
  }

  // Save global run metadata in json file
  const metadataPath = path.join(os.tmpdir(), "cypress-run-meta.json");
  const runMetadata: Omit<CypressPreRunMetadata, "ciBuildId"> = {
    runUrl: details.runUrl,
    cypressVersion: details.cypressVersion,
    tag: Array.isArray(details.tag) ? details.tag : [details.tag],
    specGroup: details.group,
  };
  await fs.promises.writeFile(metadataPath, JSON.stringify(runMetadata));

  // Save a list of tests that are expected to run to completion
  const expectedTests: Partial<NRExpectedTestsPerRunEvent>[] =
    details.specs?.map((spec) => ({
      eventType: "ExpectedTestsPerRun",
      schemaVersion: 0.1,
      file: spec.name,
    })) ?? [];
  const expectedSpecsToRun = path.join(os.tmpdir(), "expected-tests.json");
  await fs.promises.writeFile(
    expectedSpecsToRun,
    JSON.stringify(expectedTests)
  );
}

export function getCypressPostRunMetadata(
  test: CypressMochaTest
): PostRunMetadata {
  const testId = path.parse(
    test.parent?.invocationDetails.relativeFile ?? ""
  ).base;
  const stashedData: PostRunMetadata = {};
  const filepath = getPostRunMetadataLocation(testId);

  try {
    const rawData = JSON.parse(fs.readFileSync(filepath, "utf-8"));
    // Add the parsed raw data to the stashedData variable
    Object.entries(rawData).reduce((obj, [key, value]) => {
      obj[camelCase(key)] = value;
      return obj;
    }, stashedData);
  } catch (err) {
    if (err.code !== "ENOENT") {
      // If the file doesn't exist, that just means there's no metadata to collect.
      // All other errors are just re-thrown.
      throw err;
    }
  }

  return stashedData;
}

/**
 * Gets the `ci-build-id` from the process arguments
 * Checkout `CypressPreRunMetadata` type for more info.
 */
export async function getCypressPreRunMetadata() {
  const metadataPath = path.join(os.tmpdir(), "cypress-run-meta.json");
  let cachedMetadata = "{}";
  let error = "";
  try {
    cachedMetadata = await fs.promises.readFile(metadataPath, "utf-8");
  } catch (e) {
    error = `Failed to read run metadata from ${metadataPath}: ${e}`;
    console.error(error);
  }

  const parsedCache: Omit<CypressPreRunMetadata, "ciBuildId"> =
    JSON.parse(cachedMetadata);

  const args = parseSync(process.argv.filter((a) => a !== "--"));
  const ciBuildId = (args.ciBuildId ?? UNKNOWN) as string;
  if (ciBuildId === UNKNOWN) {
    // TODO: How do we surface errors that cannot be thrown?
    error = `Failed to retrieve ciBuildId!\n${error}`;
  }
  const preRunData: CypressPreRunMetadata = {
    ...parsedCache,
    ciBuildId,
    error,
  };

  return preRunData;
}

export async function getCypressExpectedTests(runId: string) {
  const expectedSpecsToRun = path.join(os.tmpdir(), "expected-tests.json");

  let detailSpecs: NRExpectedTestsPerRunEvent[];
  try {
    const cache = await fs.promises.readFile(expectedSpecsToRun, "utf-8");
    detailSpecs = JSON.parse(cache);
  } catch (e) {
    const error = `Failed to read contents of 'expected-tests.json':\n${e}`;
    console.error(error);
    return;
  }

  const expectedTestsPerRun = detailSpecs.map(async (spec) => {
    let specInfo: Results;
    const event: NRExpectedTestsPerRunEvent = {
      ...spec,
      runId,
    };
    try {
      const text = await fs.promises.readFile(spec.file, {
        encoding: "utf-8",
      });
      specInfo = getCypressTestNames(text);
    } catch (e) {
      const error = `Failed to get Cypress test names for '${path.basename(
        spec.file
      )}':\n${e}`;
      console.error(error);
      // return early, without information on the test blocks
      // but still expects the file as a whole to run
      return spec;
    }
    event.suites = specInfo.suiteNames.join("|");
    event.tests = specInfo.testNames.join("|");
    return event;
  });

  return await Promise.all(expectedTestsPerRun);
}

export async function getJestExpectedTests(
  runId: string,
  group: string,
  spec?: string
) {
  // Helper that takes a test file absolute path
  // and generates the New Relic event
  async function fileToEvent(
    file: string
  ): Promise<NRExpectedTestsPerRunEvent> {
    const { describeBlocks, itBlocks } = getJestTestNames(file);
    return {
      eventType: "ExpectedTestsPerRun",
      schemaVersion: 0.1,
      runId,
      file: getSpecRelativeFilePath(file),
      suites: getJestSpecBlockNames(describeBlocks),
      tests: getJestSpecBlockNames(itBlocks),
    };
  }

  // Read the current jest configuration
  const { testMatch }: { testMatch: string[] } = JSON.parse(
    await fs.promises.readFile("jest.integration.json", {
      encoding: "utf-8",
    })
  );

  const expectedTests: NRExpectedTestsPerRunEvent[] = [];
  // For each of the configured test match patterns, find tests
  for (const pattern of testMatch) {
    const shortPattern = pattern.replace(/\*\*\/test\//g, "");
    const isInTestFolder = pattern !== shortPattern;
    // if we're looking specifically in the test folder
    // then no need to search the full root directory
    const filesPerPattern = isInTestFolder
      ? glob.sync(shortPattern, {
          absolute: true,
          cwd: path.join(process.cwd(), "test"),
        })
      : glob.sync(pattern);

    // If running just one particular spec
    if (spec) {
      // Find it
      const filePath = filesPerPattern.find((file) => file.includes(spec));
      // If it's missing, continue to next test match pattern.
      // However, jest automatically checks for this before it runs
      if (!filePath) {
        continue;
      }
      // Otherwise, create the NR event
      const event = await fileToEvent(filePath);
      expectedTests.push(event);
      // Stop looking - ignore other patterns
      break;
    }

    // Find out which test files belong to the passed `group` argument
    for (const filePath of filesPerPattern) {
      const text = await fs.promises.readFile(filePath, { encoding: "utf-8" });
      const comments = text.match(/@group ([a-zA-Z\d]{0,})/g);
      // If there's at least one describe block with this group
      const belongsToGroup =
        comments !== null &&
        comments.some((comment) => comment.includes(group));
      if (belongsToGroup) {
        // Create the NR event
        const event = await fileToEvent(filePath);
        expectedTests.push(event);
      }
    }
  }

  return expectedTests;
}

/* Helper */
export function stripEnvironmentTags(tags: string[] | undefined): string {
  return tags
    ? tags.filter((tag) => !tag.includes("Env-")).join(",")
    : "Deploy,Deploy-API";
}

/* Helper
 * Legacy recursive function used to get suite info
 * currently this data is added to metadata and solely used
 * for the CypressTestResult bucket, which is now defunct.
 * Leaving function here in the event we want to add the
 * suite.title back to our data collection.
 */
export function getSuite(
  testOrSuite: CypressMochaTest | CypressMochaSuite
): CypressMochaSuite | CypressMochaTest {
  if (testOrSuite instanceof MochaSuite) {
    return testOrSuite;
  }
  if (testOrSuite.parent) {
    return getSuite(testOrSuite.parent);
  }
  console.warn("Unable to determine parent suite");
  return testOrSuite;
}

/* Helper */
export function getSuiteFile(
  suite: CypressMochaSuite | CypressMochaTest
): string {
  if (suite.file) {
    return suite.file;
  }
  if (suite.parent) {
    return getSuiteFile(suite.parent as CypressMochaSuite);
  }
  console.warn("Unable to find file for suite");
  return UNKNOWN;
}

/* Helper */
export function matchRuleExpl(str: string, rule: string) {
  // for this solution to work on any string, no matter what characters it has
  const escapeRegex = (ruleStr: string) =>
    ruleStr.replace(/([.*+?^=!:${}()|[\]/\\])/g, "\\$1");

  // Remove all whitespace from string being evaluated
  str = str.replace(/(\r\n|\n|\r)/gm, " ");

  // "([\s\S]*?)" => Matches any string that contains zero or more characters
  rule = rule.split("*").map(escapeRegex).join("([\\s\\S]*?)");

  // "^" => Matches any string with this rule at the beginning of it
  // "$" => Matches any string with this rule at the end of it
  rule = "^" + rule + "$";

  // Create a regular expression object for matching string
  const regex = new RegExp(rule);

  // Returns true if it finds a match, otherwise it returns false
  return regex.test(str);
}

/* Helper */
export function getErrorAnonymized(message: string): string {
  // Regex pattern for anonymizing temp directories used by cy.stash().
  const regexPattern = new RegExp(`\\/${tempDir}\\/\\d+-`, "g");
  const anonymized = message
    // Take off the "Timed out after retrying..." prefix. It's not helpful.
    .replace(/Timed out retrying after \d+ms: /g, "")
    // Anonymize UUIDs.
    .replace(
      /[\da-f]{8}-[\da-f]{4}-[\da-f]{4}-[\da-f]{4}-[\da-f]{12}/g,
      "XXXXXXX-XXXX-XXXX-XXXX-XXXXXXXXXXXX"
    )
    // Anonymize domains for portal, API and fineos.
    .replace(/paidleave-api-([a-z-]+)(\.dfml)?(\.eol)?\.mass\.gov/g, "api")
    .replace(/[a-z0-9]+\.execute-api\.us-east-1\.amazonaws\.com/g, "api")
    .replace(/\/api\/performance\/api/g, "/api/api")
    .replace(/paidleave-([a-z-]+)(\.eol)?\.mass\.gov/g, "portal")
    .replace(/[a-z0-9-]+-claims-webapp\.masspfml\.fineos\.com/g, "fineos")
    // Anonymize NTNs, preserving the ABS/AP suffixes without their digits.
    .replace(/NTN-\d+/g, "NTN-XXX")
    .replace(/-(ABS|AP)-\d+/g, "-$1-XX")
    // Anonymize dates and times.
    .replace(/\d{2}\/\d{2}\/\d{4}/g, "XX/XX/XXXX") // Raw dates
    .replace(/\d{2}\\\/\d{2}\\\/\d{4}/g, "XX\\/XX\\/XXXX") // Regex formatted dates.
    .replace(/\d+ms/g, "Xms") // Milliseconds.
    .replace(/\d+ seconds/g, "X seconds")
    // Anonymize Fineos element selectors, which are prone to change (eg: SomethingWidget_un19_Something).
    .replace(/_un(\d+)_/g, "_unXX_")
    .replace(/_PL_\d+_\d+_/g, "_PL_X_X_")
    .replace(/__TE:\d+:\d+_/g, "__TE:X:X_")
    // Anonymize temp directories used by cy.stash().
    .replace(regexPattern, `${tempDir}/XXX`)
    // Drop excess Ajax request waiting data.
    .replace(/(In-flight Ajax requests should be 0).*/g, "$1")
    // Drop debug information...
    .split("Here are the matching elements:")[0]
    .split("Debug information")[0]
    .split("Debug Information")[0]
    .trim();

  // The maximum length allowed by New Relic is 4,096, but limiting further helps
  // deduplicate messages from Cypress testing library functions.
  return anonymized.slice(0, 1024);
}

/**
 * Returns the overarching group of tags to which a test tag belongs.
 */
export function getTagGroup(tag: string) {
  if (tag === "") {
    return tag;
  }

  const groups = ["Deploy", "PR", "Targeted", "Morning Run"];

  for (const group of groups) {
    if (tag.includes(group)) {
      return group;
    }
  }

  if (tag.includes("Fineos Trigger")) {
    return "Fineos";
  }

  return "Manual";
}

// New Relic claims that it supports 4,096 characters, but using exactly 4,096
// characters results in the value being defined as null. More than 4,096
// characters results in a NrIntegrationError event.
const MAX_NR_CUSTOM_FIELD_LENGTH = 4095;

/**
 * Returns `value` truncated to the maximum length allowed by New Relic for
 * custom attribute values
 */
export function truncateNewRelicCustomAttributeValue(value: string) {
  return value.slice(0, MAX_NR_CUSTOM_FIELD_LENGTH);
}

/* Helper */
export function formatErrorMessages(
  failureMessages: string[]
): ParsedErrorMessage {
  const errorWithStack = failureMessages.join("|");
  // Attempt to prettify error output
  const { message, stack } = separateMessageFromStack(errorWithStack);
  const lines = getStackTraceLines(stack);
  const frame = getTopFrame(lines);
  const fallback = { message: errorWithStack, stack };
  if (!frame) {
    return fallback;
  }
  // Attempt to retrieve the actual code where error was thrown
  try {
    const { line, column, file } = frame;
    const result = codeFrameColumns(
      fs.readFileSync(file, "utf8"),
      { start: { line: line ?? -1, column } },
      { highlightCode: true }
    );
    return {
      message: `\n${message}\n\n${result}\n${stack}\n`,
      stack,
      line,
      file: getSpecRelativeFilePath(file) ?? file,
    };
  } catch (_error) {
    return fallback;
  }
}

/* Helper */
export function getSpecRelativeFilePath(filePath: string) {
  const [_, relativePath] = filePath
    .replace(/\\\\/g, path.sep)
    .replace(/[/\\]/g, path.sep)
    .split(`e2e${path.sep}`);
  return relativePath;
}

/* Helper */
export function getGroup(filePath: string) {
  const group = filePath.split(path.sep)[2];
  return group.charAt(0).toUpperCase() + group.slice(1);
}

/**
 * Reads a YAML file that contains error categories and their associated rules from disk
 * @returns An object that maps error categories and subcategories to rules along
 * with other metadata for future data analysis
 */
export function getKnownIssues(): ErrorCategory {
  const filePath = path.resolve(
    __dirname,
    path.relative(__dirname, "docs/known-issues.yml")
  );
  const raw = fs.readFileSync(filePath, "utf-8");
  return yaml.load(raw) as ErrorCategory;
}

/**
 * Used when output is `undefined` or `unpredictable`
 */
export const UNKNOWN = "UNKNOWN";
export const NOT_CATEGORIZED = "NOT-CATEGORIZED";

function getJestSpecBlockNames(blocks: DescribeBlock[] | ItBlock[]) {
  return blocks
    .reduce((titles, { name }) => {
      titles.push(name.replace("${skipFlag}", "[Intentionally Skipped] "));
      return titles;
    }, [] as string[])
    .join("|");
}

export function parseArgs(args: string[]) {
  const config: Record<string, string> = {};
  let prevKey = "";
  args.forEach((arg) => {
    const [key, value] = arg.split("=");
    const parsedValue = value || "1";
    if (key.startsWith("--")) {
      const keyName = key.replace(/^--/, "");
      if (!(keyName in config)) {
        config[keyName] = parsedValue;
      } else {
        config[keyName] += `,${parsedValue}`;
      }
      prevKey = keyName;
    } else if (prevKey) {
      config[prevKey] += ` ${key}`;
    }
  });

  return config;
}

export function getRunMetadata(
  specGroup: string,
  tag = process.env.E2E_TAG ?? UNKNOWN,
  runId = process.env.TEST_RUN_ID ?? UNKNOWN,
  environment = process.env.E2E_ENVIRONMENT ?? UNKNOWN,
  branchRef = process.env.GITHUB_REF ?? ""
): TestRunMetadata {
  return {
    runId,
    runUrl: getTestResultRunUrl(),
    environment,
    branch: path.relative("refs/heads", branchRef),
    tag,
    tagGroup: getTagGroup(tag),
    specGroup,
  };
}

function getTestResultRunUrl() {
  if (
    !process.env.GITHUB_SERVER_URL ||
    !process.env.GITHUB_REPOSITORY ||
    !process.env.GITHUB_RUN_ID
  ) {
    return UNKNOWN;
  }
  return `${process.env.GITHUB_SERVER_URL}/${process.env.GITHUB_REPOSITORY}/actions/runs/${process.env.GITHUB_RUN_ID}?check_suite_focus=true`;
}
