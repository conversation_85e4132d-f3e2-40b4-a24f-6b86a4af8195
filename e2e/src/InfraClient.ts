import {
  DescribeSecurityGroupsCommand,
  DescribeSubnetsCommand,
  EC2Client,
} from "@aws-sdk/client-ec2";
import {
  ContainerOverride,
  DescribeTasksCommand,
  ECSClient,
  RunTaskCommand,
  RunTaskCommandInput,
  waitUntilTasksStopped,
} from "@aws-sdk/client-ecs";
import {
  GetObjectCommand,
  ListObjectsV2Command,
  ListObjectsV2CommandInput,
  ListObjectsV2CommandOutput,
  PutObjectCommandInput,
  S3Client,
} from "@aws-sdk/client-s3";
import {
  GetExecutionHistoryCommand,
  HistoryEvent,
  SFNClient,
  StartExecutionCommand,
} from "@aws-sdk/client-sfn";
import {
  GetCommandInvocationCommand,
  SendCommandCommand,
  SendCommandCommandOutput,
  SSMClient,
} from "@aws-sdk/client-ssm";
import {
  AssumeRoleCommand,
  GetCallerIdentityCommand,
  STSClient,
} from "@aws-sdk/client-sts";
import { Upload } from "@aws-sdk/lib-storage";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import { parse } from "@aws-sdk/util-arn-parser";
import assert from "assert";
import chalk from "chalk";
import { parse as csvParse } from "csv-parse/sync";
import { format } from "date-fns";
import delay from "delay";
import { createReadStream } from "fs";
import pRetry from "p-retry";
import * as path from "path";
import { URL } from "url";

import config, { ConfigFunction } from "./config";
import {
  dorIntegrityReportColumnNames,
  DorIntegrityReportRow,
  paymentAuditReportColumnNames,
  PaymentAuditReportMap,
  PaymentAuditReportRow,
} from "./types";
import { assertIsNotNull } from "./util/typeUtils";
import { convertToPosixPath } from "./util/urls";

type SfnEventName =
  | " dor_import "
  | "load_employers_to_fineos"
  | "fineos_eligibility_feed_export"
  | "success";

type SSMCommandWaitOpts = { retryInterval?: number; retries?: number };
type ExtractInterval = "daily" | "weekly" | "monthly";
const ExtractIntervalDirectoryMap: Record<ExtractInterval, string> = {
  daily: "dataexports",
  weekly: "weeklyExtracts",
  monthly: "monthlyExtracts",
};

const region = config("AWS_REGION");

const ec2Client = new EC2Client({ region });
const ecsClient = new ECSClient({ region });
const s3Client = new S3Client({ region });
const sfnClient = new SFNClient({ region });
const ssmClient = new SSMClient({ region });
const stsClient = new STSClient({ region });

const cache: Map<string, InfraClient> = new Map();

export default class InfraClient {
  static create(config: ConfigFunction) {
    const parameters = [
      config("ENVIRONMENT"),
      config("DOR_IMPORT_URI"),
      config("DOR_ETL_ARN"),
      config("FINEOS_AWS_IAM_ROLE_ARN"),
      config("FINEOS_AWS_IAM_ROLE_EXTERNAL_ID"),
      config("FINEOS_DATA_EXPORTS_BUCKET"),
      config("LAP2_EC2_INSTANCEID"),
      config("S3_MASSGOV_PFML_AGENCY_TRANSFER_BUCKET"),
      config("S3_INTELLIGENCE_TOOL_BUCKET"),
      config("S3_MASSGOV_PFML_REPORTS_BUCKET"),
      config("DB_HOSTNAME"),
      config("DB_NAME"),
      config("DB_PW"),
    ] as const;

    const key = JSON.stringify(parameters);
    if (cache.has(key)) {
      const cached = cache.get(key);
      if (cached !== undefined) {
        return cached;
      }
    }
    const newInstance = new InfraClient(...parameters);
    cache.set(key, newInstance);
    return newInstance;
  }

  constructor(
    private environment: string,
    private dor_import_uri: string,
    private dor_etl_arn: string,
    private fineos_aws_iam_role_arn: string,
    private fineos_aws_iam_role_external_id: string,
    private fineos_data_exports_bucket: string,
    private lap2_ec2_instance_id: string,
    private s3_massgov_pfml_agency_transfer_bucket: string,
    private s3_intelligence_tool_bucket: string,
    private s3_massgov_pfm_reports_bucket: string,
    private db_hostname: string,
    private db_name: string,
    private db_pw: string
  ) {}

  buildRdsCommand(query: string, additionalCommandArgs?: string[]) {
    const commands = [
      `PGPASSWORD='${this.db_pw}'`,
      `PGSSLMODE=require`,
      `psql -h ${this.db_hostname} -W -U pfml ${this.db_name}`,
      "--command",
      `"${query}"`,
    ];
    if (additionalCommandArgs) {
      commands.push(...additionalCommandArgs);
    }
    return commands.join(" ");
  }

  async checkForEcsTaskErrors(taskArn: string): Promise<string[]> {
    const command = new DescribeTasksCommand({
      cluster: this.environment,
      tasks: [taskArn],
    });
    const response = await ecsClient.send(command);
    const failures = response.failures || [];
    if (failures.length !== 0) {
      throw new Error(
        `Error describing ECS Tasks: ${JSON.stringify(failures)}`
      );
    }
    const tasks = response.tasks || [];

    if (tasks.length !== 1) {
      throw new Error(
        `Expected describe tasks response to contain one task, found ${tasks.length}`
      );
    }
    const containers = tasks[0].containers || [];

    const errors = containers
      .filter((c) => c.exitCode !== 0)
      .map((c) => `${c.name} (${c.exitCode}): ${c.reason}`);

    return errors;
  }

  async getEcsTaskUrl(taskArn: string) {
    const resource = parse(taskArn).resource;
    const [_, cluster, id] = resource.split("/");

    return `https://console.aws.amazon.com/ecs/v2/clusters/${cluster}/tasks/${id}`;
  }

  async generatePresignedUrl(
    bucketName: string,
    objectKey: string,
    expiresIn: number = 5000
  ) {
    try {
      const command = new GetObjectCommand({
        Bucket: bucketName,
        Key: objectKey,
      });
      return getSignedUrl(s3Client, command, { expiresIn });
    } catch (error) {
      throw Error(`Error generating presigned URL: ${error}`);
    }
  }

  async getFineosExtracts(date: Date, interval: ExtractInterval) {
    const subDirectory = ExtractIntervalDirectoryMap[interval];
    const s3URI = `${this.fineos_data_exports_bucket}/${subDirectory}/${format(
      date,
      "yyyy-MM-dd"
    )}`;
    const assumeRoleCommand = await stsClient.send(
      new AssumeRoleCommand({
        RoleArn: this.fineos_aws_iam_role_arn,
        RoleSessionName: "E2E.INFRA.CLIENT",
        ExternalId: this.fineos_aws_iam_role_external_id,
      })
    );
    if (!assumeRoleCommand.Credentials) {
      throw Error("Missing errors while assuming role");
    }
    const { Credentials } = assumeRoleCommand;
    const sentCommand = await ssmClient.send(
      new SendCommandCommand({
        DocumentName: "AWS-RunShellScript",
        InstanceIds: [this.lap2_ec2_instance_id],
        Parameters: {
          commands: [
            `AWS_ACCESS_KEY_ID=${Credentials.AccessKeyId} AWS_SECRET_ACCESS_KEY=${Credentials.SecretAccessKey} AWS_SESSION_TOKEN=${Credentials.SessionToken} aws s3 ls ${s3URI}`,
          ],
        },
      })
    );
    return await pRetry(async () => {
      await delay(1000);
      const commandResponse = await ssmClient.send(
        new GetCommandInvocationCommand({
          CommandId: sentCommand.Command?.CommandId,
          InstanceId: this.lap2_ec2_instance_id,
        })
      );
      // In the case that fineos extracts failed to produce extracts for the specified date, this command will fail
      // so return an empty array.
      if (commandResponse.Status === "Failed") {
        console.log(
          `FINEOS S3 command failed:\n ${commandResponse.StandardErrorContent}`
        );
        return [];
      }
      if (
        !commandResponse.StandardOutputContent ||
        commandResponse.Status !== "Success"
      ) {
        throw Error(
          `SSM command not successful. Command response status: ${commandResponse.Status}`
        );
      }
      const response = commandResponse.StandardOutputContent;
      return response.split("\n").reduce<string[]>((extracts, row) => {
        const result = row.match(/\d{4}(-\d{2}){5}[a-zA-Z-_\d]*(.csv|OK)/);
        if (result) {
          extracts.push(result[0]);
        }
        return extracts;
      }, []);
    });
  }

  async getLatestDorIntegrityReport(
    date: Date
  ): Promise<DorIntegrityReportRow[]> {
    const objects = await this.getS3Objects({
      Bucket: this.s3_massgov_pfm_reports_bucket,
      Prefix: `dor-report/${format(date, "yyyy-MM-dd")}`,
    });

    if (!objects.Contents) {
      throw Error(
        `DOR integrity reports for ${format(
          date,
          "yyyy-MM-dd"
        )} not found in S3 bucket "${
          this.s3_massgov_pfm_reports_bucket
        }/dor_reports"`
      );
    }

    const closestReport = objects.Contents.reduce((closest, current) => {
      const closestDiff = Math.abs(
        date.getTime() - new Date(closest.LastModified!).getTime()
      );
      const currentDiff = Math.abs(
        date.getTime() - new Date(current.LastModified!).getTime()
      );

      return currentDiff < closestDiff ? current : closest;
    });

    try {
      const command = new GetObjectCommand({
        Bucket: this.s3_massgov_pfm_reports_bucket,
        Key: closestReport?.Key,
      });

      const response = await s3Client.send(command);

      assertIsNotNull(response.Body);

      const csvContent: string = await response.Body.transformToString("utf-8");

      const report = this.parseDorIntegrityReport(csvContent);

      return report;
    } catch (error) {
      throw new Error(`Error fetching DOR integrity report: ${error}`);
    }
  }

  parseDorIntegrityReport(csvContent: string): DorIntegrityReportRow[] {
    const rows = csvContent.split("\r\n").filter((row) => row.length > 0);

    const parsedHeaders = rows.shift()?.split(",");

    assertIsNotNull(parsedHeaders);
    assert(dorIntegrityReportColumnNames.length === parsedHeaders.length);
    for (const [
      index,
      expectedHeader,
    ] of dorIntegrityReportColumnNames.entries()) {
      assert(expectedHeader === parsedHeaders[index]);
    }

    const output = [];

    for (const row of rows) {
      const values = row.split(",");
      assert(values.length === dorIntegrityReportColumnNames.length);

      const entries = values.map((value, index) => {
        const header = dorIntegrityReportColumnNames[index];
        return [header, value];
      });

      const rowObject = Object.fromEntries(entries) as DorIntegrityReportRow;

      output.push(rowObject);
    }

    return output;
  }

  async getPaymentAuditFile(date: Date): Promise<PaymentAuditReportMap> {
    const todayReports = await this.getS3Objects({
      Bucket: this.s3_massgov_pfml_agency_transfer_bucket,
      Prefix: `reports/sent/${format(date, "yyyy-MM-dd")}`,
    });

    if (!todayReports.Contents) {
      throw Error(
        `No reports for ${format(date, "yyyy-MM-dd")} found in S3 bucket "${
          this.s3_massgov_pfml_agency_transfer_bucket
        }/reports/sent"`
      );
    }

    const paymentReport = todayReports.Contents.filter((report) =>
      report.Key?.includes("Payment-Audit-Report")
    );

    if (paymentReport.length === 0) {
      throw Error(
        `Payment Audit Log for ${format(
          date,
          "yyyy-MM-dd"
        )} not found in S3 bucket "${
          this.s3_massgov_pfml_agency_transfer_bucket
        }/reports/sent"`
      );
    }
    try {
      const command = new GetObjectCommand({
        Bucket: this.s3_massgov_pfml_agency_transfer_bucket,
        Key: paymentReport[0]?.Key,
      });

      const response = await s3Client.send(command);

      assertIsNotNull(response.Body);

      const csvContent: string = await response.Body.transformToString("utf-8");
      return this.parsePaymentAuditFile(csvContent);
    } catch (error) {
      throw new Error(`Error fetching Payment Audit Report: ${error}`);
    }
  }

  async parsePaymentAuditFile(
    csvContent: string
  ): Promise<PaymentAuditReportMap> {
    const rows = csvContent.split("\r\n").filter((row) => row.length > 0);
    const parsedHeaders = rows
      .shift()
      ?.split(",")
      .map((header) => header.replace(/"/g, ""));

    assertIsNotNull(parsedHeaders);
    assert(paymentAuditReportColumnNames.length === parsedHeaders.length);
    for (const [
      index,
      expectedHeader,
    ] of paymentAuditReportColumnNames.entries()) {
      assert(expectedHeader === parsedHeaders[index]);
    }
    const outputMap = new Map();
    for (const row of rows) {
      const values = csvParse(row)[0] as string[];
      assert(values.length === paymentAuditReportColumnNames.length);

      const entries = values.map((value, index) => {
        const header = paymentAuditReportColumnNames[index];
        return [header, value];
      });

      const rowObject = Object.fromEntries(entries) as PaymentAuditReportRow;
      const paymentId = rowObject["PFML Payment Id"];
      outputMap.set(paymentId, rowObject);
    }

    return outputMap;
  }

  async getFineosExtractsCopies(date: Date) {
    const objects = await this.getS3Objects({
      Bucket: this.s3_intelligence_tool_bucket,
      Prefix: `fineos/dataexports/${format(date, "yyyy-MM-dd")}`,
      Delimiter: "/",
    });
    // Ignore non folder types that match - some envs will experience seemingly random .csv files at this level
    if (!objects.CommonPrefixes) {
      throw Error(
        `FINEOS extract data for ${format(
          date,
          "yyyy-MM-dd"
        )} not found in S3 bucket "${
          this.s3_intelligence_tool_bucket
        }/fineos/dataexports"`
      );
    }
    // grab objects only in folder for the date's extracts
    const extractFolderPath = objects.CommonPrefixes[0].Prefix as string;
    const [timestamp] = extractFolderPath.split("/").slice(-2, -1);
    const { Contents } = await this.getS3Objects({
      Bucket: this.s3_intelligence_tool_bucket,
      Prefix: extractFolderPath + timestamp,
    });
    return Contents;
  }

  async getIAMUsername() {
    const result = await stsClient.send(new GetCallerIdentityCommand({}));
    // User ID format is 'ACCCCCCCCCCC7EHS5WO7:<EMAIL>'
    const userID = result.UserId?.match(/(?<=:).*(?=@)/);
    if (userID?.length) {
      return userID.pop();
    }
    throw Error(`Couldn't determine username for ${userID}`);
  }

  /**
   * Query a database and see the raw/unparsed results.
   *
   * This is intended to be a utility function. You can use it to write
   * temporary scripts for the purposes of debugging. It's not used in the
   * rest of the code base.
   *
   * @param query The query/SQL string
   * @returns The unparsed result from standard output.
   */
  async queryRdsRaw(query: string) {
    const response = await this.sendAndWaitForSSMShellCommand(
      this.buildRdsCommand(query, ["-t", "-A"]),
      { retries: 5, retryInterval: 1500 }
    );
    if (response.ResponseCode !== 0) {
      throw Error("Did not receive successful response for SSM command");
    }
    const ssmOutput = response?.StandardOutputContent;
    if (!ssmOutput) {
      return;
    }
    return ssmOutput;
  }

  // "exitEvent" determines at which step we want to stop monitoring etl progress
  // i.e if we want to register leave admins, we only need to monitor progress up until "fineos_eligibility_feed_export" begins
  async runDorEtl(
    exitEvent: SfnEventName = "fineos_eligibility_feed_export"
  ): Promise<boolean | void> {
    const { executionArn } = await sfnClient.send(
      new StartExecutionCommand({
        stateMachineArn: this.dor_etl_arn,
      })
    );
    console.log(
      `${chalk.blue(
        this.environment.toUpperCase() + " etl execution started"
      )} - waiting on "${exitEvent}" task completion`
    );
    // wait 3 minutes before checking 'load_employers_to_fineos' completion
    await delay(1000 * 60 * 3);
    // reduces amount of repeat logs
    let log = true;
    return pRetry(
      async () => {
        log &&
          console.log(
            `${chalk.blue(
              this.environment.toUpperCase()
            )} - checking for "${exitEvent}" task completion`
          );
        const logs = await sfnClient.send(
          new GetExecutionHistoryCommand({
            executionArn,
          })
        );
        if (!logs.events) {
          return;
        }
        const getStartedEventName = (event: HistoryEvent) => {
          if (
            event.type === "TaskStateEntered" ||
            event.type === "SucceedStateEntered"
          ) {
            return event.stateEnteredEventDetails?.name;
          }
        };
        for (const event of logs.events) {
          const eventName = getStartedEventName(event);
          if (eventName === "failure_notification") {
            throw new Error(
              `StepFunction has failed: ${JSON.stringify(
                // These properties are always defined for the "failure_notification" event
                JSON.parse(event.stateEnteredEventDetails?.input as string)
                  .task_failure_details,
                null,
                2
              )}`
            );
          }
          if (eventName === exitEvent) {
            if (eventName === "fineos_eligibility_feed_export") {
              console.log(
                chalk.green(
                  `${this.environment.toUpperCase()} load_employers_to_fineos successful!`
                )
              );
            } else {
              console.log(`"${exitEvent}" event started`);
            }
            return true;
          }
        }
        await delay(1000 * 15);
        log = !log;
        throw new Error(`Unable to verify completion of "${exitEvent}"`);
      },
      // 100 retries should equal around 28 minutes.
      // 100 * 15 / 60 = 25, plus the three minute wait above.
      { retries: 100, maxTimeout: 100, minTimeout: 0 }
    );
  }

  /**
   * Run an ECS task using fargate.
   *
   * For simplicitly, this doesn't allow overriding the default command.
   *
   * @param taskName The name of the task. For example, dor-import.
   *
   * @return string The task ARN. The task ID is the last portion of the ARN.
   */
  async runEcsTask(
    taskName: string,
    environmentVariables: ContainerOverride["environment"] = [],
    command: ContainerOverride["command"] = []
  ) {
    const taskDefinition = `pfml-api-${this.environment}-${taskName}`;
    const subnetIds = await this.getPrivateNonProdSubnetIds();
    const securityGroupId = await this.getEnvironmentSecurityGroupId();
    const containerOverride: ContainerOverride = {
      name: taskName, // Required when other ContainerOverride properties are set.
    };
    if (environmentVariables) {
      containerOverride["environment"] = environmentVariables;
    }
    if (command.length > 0) {
      containerOverride["command"] = command;
    }
    const input: RunTaskCommandInput = {
      cluster: this.environment,
      launchType: "FARGATE",
      platformVersion: "1.4.0", // The same as bin/run-ecs-task/run-task.sh for consistency.
      startedBy: "E2E",
      taskDefinition,
      overrides: { containerOverrides: [containerOverride] },
      networkConfiguration: {
        awsvpcConfiguration: {
          assignPublicIp: "DISABLED", // This is disabled by default, but is being set to be explicit.
          securityGroups: [securityGroupId],
          subnets: subnetIds,
        },
      },
    };
    const runTaskCommand = new RunTaskCommand(input);
    // If task definition doesn't exist, this will throw an exception.
    const response = await ecsClient.send(runTaskCommand);
    if (!response.tasks) {
      throw new Error("ECS tasks array is missing or empty");
    }
    const tasks = response.tasks;

    const taskArn = tasks[0].taskArn;
    if (!taskArn) {
      throw new Error("ECS task ARN is missing or empty");
    }
    return taskArn;
  }

  async runEcsTaskAndWaitForCompletion(
    taskName: string,
    maxWaitTimeMinutes: number,
    environmentVariables: ContainerOverride["environment"] = [],
    command: ContainerOverride["command"] = []
  ) {
    const taskArn = await this.runEcsTask(
      taskName,
      environmentVariables,
      command
    );
    try {
      const taskResult = await waitUntilTasksStopped(
        { client: ecsClient, maxWaitTime: maxWaitTimeMinutes * 60 },
        {
          cluster: this.environment,
          tasks: [taskArn],
        }
      );
      if (taskResult.state !== "SUCCESS") {
        const errors = await this.checkForEcsTaskErrors(taskArn);
        throw new Error(
          `ECS task at ${this.getEcsTaskUrl(
            taskArn
          )} exited with errors: ${JSON.stringify(errors)}`
        );
      }
    } catch (error) {
      error.message = `Error running ${taskName}: ${error.message}`;
      throw error;
    }
    return taskArn;
  }

  async sendAndWaitForSSMShellCommand(
    command: string,
    waitOpts?: SSMCommandWaitOpts
  ) {
    const sentCommand = await this.sendSSMShellScriptCommand(command);
    const response = await this.waitForSSMCommandSuccess(sentCommand, waitOpts);
    if (response instanceof Array) {
      throw Error("Did not receive successful response for SSM command");
    }
    return response;
  }

  async uploadRevenueFiles(files: string[]): Promise<void> {
    const uploads = files.map((file) => {
      const upload = new Upload({
        params: this.getS3UploadParams(file),
        client: s3Client,
      });
      return upload
        .done()
        .then(() =>
          console.log(
            `${this.environment.toUpperCase()} - Completed upload of ${file}`
          )
        );
    });
    await Promise.all(uploads);
  }

  private async getS3Objects(
    commandInput: ListObjectsV2CommandInput
  ): Promise<ListObjectsV2CommandOutput> {
    return await s3Client.send(new ListObjectsV2Command(commandInput));
  }

  private getS3UploadParams(filename: string): PutObjectCommandInput {
    const {
      host: bucket,
      pathname: key,
      protocol,
    } = new URL(this.dor_import_uri);
    if (protocol !== "s3:") {
      throw new Error(`Invalid protocol for DOR_IMPORT_URI: ${protocol}`);
    }

    return {
      Bucket: bucket,
      Key: path.posix.join(
        key.slice(1),
        path.posix.basename(convertToPosixPath(filename))
      ),
      Body: createReadStream(filename),
    };
  }

  private async getEnvironmentSecurityGroupId() {
    const describeSecurityGroupsInput = {
      Filters: [
        {
          Name: "group-name",
          Values: [`pfml-api-${this.environment}-tasks`],
        },
      ],
    };
    const command = new DescribeSecurityGroupsCommand(
      describeSecurityGroupsInput
    );

    const response = await ec2Client.send(command);
    if (!response?.SecurityGroups) {
      throw new Error("No security groups found");
    }
    const securityGroups = response.SecurityGroups;
    if (securityGroups.length !== 1) {
      throw new Error("Expected to find one security group");
    }
    const securityGroup = securityGroups[0];
    if (!securityGroup.GroupId) {
      throw new Error("No security group ID found on security group");
    }
    return securityGroup.GroupId;
  }

  /**
   * Get the non-prod private subnets. This should be the same for all non-prod/lower environments.
   */
  private async getPrivateNonProdSubnetIds() {
    const input = {
      Filters: [
        {
          Name: "tag:Name",
          Values: ["*-Private*"],
        },
        {
          Name: "tag:Environment",
          Values: ["NonProd"],
        },
      ],
    };
    const command = new DescribeSubnetsCommand(input);

    const response = await ec2Client.send(command);
    if (!response.Subnets || response.Subnets.length < 1) {
      throw new Error("No subnets found");
    }

    const subnetIds = response.Subnets.map((subnet) => {
      if (!subnet.SubnetId) {
        throw new Error("Subnet ID missing");
      }
      return subnet.SubnetId;
    });
    return subnetIds;
  }

  private async sendSSMShellScriptCommand(
    command: string
  ): Promise<SendCommandCommandOutput> {
    return await ssmClient.send(
      new SendCommandCommand({
        DocumentName: "AWS-RunShellScript",
        InstanceIds: [this.lap2_ec2_instance_id],
        Parameters: {
          commands: [command],
        },
      })
    );
  }

  private async waitForSSMCommandSuccess(
    sentCommand: SendCommandCommandOutput,
    waitOpts?: SSMCommandWaitOpts
  ) {
    if (waitOpts?.retries && waitOpts?.retries > 20) {
      throw Error(
        "Request limit for checking SSM command success is 20. Try increasing 'retryInterval' if more time is needed for command to complete"
      );
    }
    return await pRetry(
      async () => {
        await delay(waitOpts?.retryInterval || 1000);
        const commandResponse = await ssmClient.send(
          new GetCommandInvocationCommand({
            CommandId: sentCommand.Command?.CommandId,
            InstanceId: this.lap2_ec2_instance_id,
          })
        );
        if (commandResponse.Status === "Failed") {
          console.log(
            `SSM command failed:\n ${commandResponse.StandardErrorContent}`
          );
          return [];
        }
        if (commandResponse.Status !== "Success") {
          throw Error(
            `SSM command not successful. Command response status: ${commandResponse.Status}`
          );
        }
        return commandResponse;
      },
      { retries: waitOpts?.retries }
    );
  }
}
