import {
  BrowserContext,
  BrowserContextOptions,
  Page,
  test as baseTest,
} from "@playwright/test";

import config from "../../src/config";
import portalFeatureFlags from "../../src/portalFeatureFlags";
import {
  encodeMmgCredentials,
  getClaimantCredentials,
  getMmgLeaveAdminCredentials,
} from "../../src/util/credentials";
import { expect } from "../extensions/portal";
import {
  FineosFixtures,
  fineosFixtures,
  FineosOptions,
} from "../fixtures/fineos";
import PortalOAuthStartPage from "../pages/portal/oauth-start";
import PortalUserApplicationStatusPage from "../pages/portal/user/application-status";
import PortalUserApplicationsPage from "../pages/portal/user/applications";

export { expect } from "@playwright/test";

const idvHostName = "https://my.test-next.tss.mass.gov";

type PortalFixtures = {
  loggedInLeaveAdminPage: Page;
  loggedInClaimantPage: Page;
  portalAuthorizeIdv: boolean;
  portalBrowserContext: BrowserContext;
  portalClaimantCredentials: {
    username: string;
    password: string;
  };
  portalPage: Page;
  portalUserApplicationsPage: PortalUserApplicationsPage;
  portalUserApplicationStatusPage: PortalUserApplicationStatusPage;
};

/**
 * Playwright `test` function extended for use with tests that visit the PFML
 * Portal.
 */
export const test = baseTest.extend<
  FineosFixtures & FineosOptions & PortalFixtures
>({
  loggedInLeaveAdminPage: async ({ portalPage }, use) => {
    await setup(portalPage);
    const oAuthStartPage = await PortalOAuthStartPage.goto(portalPage);
    const myMassGovPage = await oAuthStartPage.logInAsLeaveAdmin();
    const logInCredentials = getMmgLeaveAdminCredentials();
    await myMassGovPage.logIn(logInCredentials);
    const portalUrl = config("PORTAL_BASEURL");
    expect(portalPage.url()).toContain(portalUrl);
    await use(portalPage);
  },
  loggedInClaimantPage: async (
    { portalClaimantCredentials, portalPage },
    use
  ) => {
    await setup(portalPage);
    const oAuthStartPage = await PortalOAuthStartPage.goto(portalPage);
    const myMassGovPage = await oAuthStartPage.logInAsClaimant();
    await myMassGovPage.logIn(portalClaimantCredentials);
    const portalUrl = config("PORTAL_BASEURL");
    expect(portalPage.url()).toContain(portalUrl);
    await use(portalPage);
  },
  portalBrowserContext: async ({ browser }, use) => {
    const options: BrowserContextOptions = {};
    const context = await browser.newContext(options);

    await use(context);
    await context.close();
  },
  portalPage: async ({ portalAuthorizeIdv, portalBrowserContext }, use) => {
    const page = await portalBrowserContext.newPage();
    if (portalAuthorizeIdv) {
      await page.route(`${idvHostName}/**/*`, async (idvRoute) => {
        const request = idvRoute.request();
        await idvRoute.continue({
          headers: {
            ...request.headers(),
            Authorization: "Basic " + encodeMmgCredentials(),
          },
        });
      });
    }
    await setup(page);
    await use(page);
  },
  portalUserApplicationsPage: async ({ loggedInClaimantPage }, use) => {
    // Use the logged-in claimant page to get the PortalUserApplicationsPage
    const portalUserApplicationsPage = await PortalUserApplicationsPage.get(
      loggedInClaimantPage
    );
    await use(portalUserApplicationsPage);
  },
  portalUserApplicationStatusPage: async (
    { portalUserApplicationsPage, fineosClaimSubmissions },
    use
  ) => {
    // Use the portalUserApplicationsPage to navigate to the application status page
    const portalUserApplicationStatusPage =
      await portalUserApplicationsPage.navigateToApplicationStatus(
        Object.values(fineosClaimSubmissions)[0].submission.fineos_absence_id
      );
    await use(portalUserApplicationStatusPage);
  },
  portalAuthorizeIdv: [false, { option: true }],
  portalClaimantCredentials: [getClaimantCredentials(), { option: true }],
  ...fineosFixtures,
});

function setup(page: Page) {
  return Promise.all([
    setFeatureFlags(page, portalFeatureFlags),
    stubGoogleAnalytics(page),
  ]);
}

function setFeatureFlags(page: Page, featureFlags: Record<string, boolean>) {
  return page.context().addCookies([
    {
      name: "_ff",
      url: config("PORTAL_BASEURL"),
      value: JSON.stringify(featureFlags),
    },
  ]);
}

function stubGoogleAnalytics(page: Page) {
  return page.route(config("GOOGLE_ANALYTICS_PATTERN"), (route, request) => {
    if (request.method() === "POST") {
      route.fulfill({ status: 200 });
    } else {
      route.continue();
    }
  });
}
