import { expect, Page, Response } from "@playwright/test";

import { assertIsString } from "../../../../src/util/typeUtils";
import PageObjectModel from "../../PageObjectModel";

export default class PortalEmployerApplicationsPage extends PageObjectModel {
  static async get(page: Page) {
    const currentUrl = page.url();
    expect(currentUrl).toContain(`employers/applications`);
    return new PortalEmployerApplicationsPage(page);
  }

  static async clickApplyFilters(page: Page) {
    const applyFilters = page.getByRole("button", { name: "Apply filters" });
    await applyFilters.waitFor({ state: "attached", timeout: 5000 });
    await applyFilters.click({ force: true });
    await page
      .locator('span[class="c-spinner"]')
      .waitFor({ state: "detached", timeout: 15000 });
    await page
      .locator("div table")
      .waitFor({ state: "attached", timeout: 30000 });
  }

  static async assertClaimsNeedsReview(
    page: Page,
    status: string
  ): Promise<void> {
    await page.locator("table").waitFor({ state: "attached", timeout: 30000 });
    const rows = page.locator(
      'table tbody tr td[data-label="Review due date"] a'
    );
    const texts = await rows.allTextContents();
    texts.forEach((text) => {
      expect(text).toContain(status);
    });
  }

  static async assertClaimsDontNeedReview(page: Page): Promise<void> {
    await page.locator("table").waitFor({ state: "attached", timeout: 5000 });
    const count = await page
      .locator('table tbody tr td[data-label="Review due date"] a')
      .count();
    expect(count).toBe(0);
  }

  async searchByID(
    searchText: string,
    page: Page,
    applicationsPage: PortalEmployerApplicationsPage,
    responsePromise?: Promise<Response>
  ) {
    const firstApplicationIDCheck = await applicationsPage.applicationIDs
      .first()
      .textContent();

    await this.searchInput.fill(searchText);
    await page.getByRole("button", { name: "Search" }).click();
    await page
      .locator("div table")
      .waitFor({ state: "attached", timeout: 5000 });

    if (responsePromise) {
      await responsePromise;
    }

    const caseCount = await page.locator("div table").count();
    expect(caseCount).toBe(1);
    expect(searchText).toBe(firstApplicationIDCheck);
  }

  async searchByName(
    searchText: string,
    page: Page,
    applicationsPage: PortalEmployerApplicationsPage,
    responsePromise?: Promise<Response>
  ) {
    await this.searchInput.fill(searchText);
    await page.getByRole("button", { name: "Search" }).click({ force: true });
    await page
      .locator("div table")
      .waitFor({ state: "attached", timeout: 5000 });
    if (responsePromise) {
      await responsePromise;
    }

    const firstApplicationName = await applicationsPage.applicationNames
      .first()
      .textContent();
    assertIsString(firstApplicationName);
    const { formattedName } =
      PortalEmployerApplicationsPage.formatName(firstApplicationName);

    expect(searchText).toBe(formattedName);
  }

  static formatName(inputString: string) {
    const parts = inputString.split(" ");
    const firstName = parts[0];
    let lastName = parts[1];
    if (lastName.includes("View")) {
      lastName = lastName.replace("View", "");
    }
    return {
      formattedName: `${firstName} ${lastName}`,
      name: { firstName, lastName },
    };
  }

  get applicationIDs() {
    return this.page.locator('table tbody tr td[data-label="Application ID"]');
  }

  get applicationNames() {
    return this.page.locator('table tbody tr th[data-label="Employee"]');
  }

  async showFilters() {
    await this.showFiltersButton.click();
  }

  private readonly showFiltersButton = this.page.getByRole("button", {
    name: "Show filters",
  });

  private readonly searchInput = this.page.getByLabel(
    "Search for employee name or application ID"
  );
}
