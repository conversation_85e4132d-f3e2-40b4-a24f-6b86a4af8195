import { expect, Page } from "@playwright/test";

import { DocumentWithPromisedFile } from "../../../src/generation/documents";
import PageObjectModel from "../PageObjectModel";
import FineosClaimPage from "./claim";
import FineosClaimAdjudicationEvidencePage from "./claim-adjudication-evidence";

export default class FineosClaimAdjudicationPage extends PageObjectModel {
  static async get(page: Page, id: string) {
    const title = page.getByRole("heading", {
      name: new RegExp("Adjudicate Request"),
    });
    await expect(title).toBeVisible();
    return new FineosClaimAdjudicationPage(page, id);
  }

  constructor(page: Page, claimId: string) {
    super(page);
    this.claimId = claimId;
  }

  async acceptLeavePlan(): Promise<void> {
    await this.acceptButton.click();
    await this.page.waitForLoadState("networkidle");
  }

  async gotoEvidence(): Promise<FineosClaimAdjudicationEvidencePage> {
    await this.evidenceTab.click();
    await this.page.waitForLoadState("networkidle");
    return FineosClaimAdjudicationEvidencePage.get(this.page);
  }

  async gotoManageRequest(): Promise<FineosClaimAdjudicationPage> {
    await this.manageRequestTab.click();
    await this.page.waitForLoadState("networkidle");
    return this;
  }

  async gotoPlanEvidence(): Promise<FineosClaimAdjudicationEvidencePage> {
    await this.planEvidenceTab.click();
    await this.page.waitForLoadState("networkidle");
    return FineosClaimAdjudicationEvidencePage.get(this.page);
  }

  async receiveAllEvidence(
    documents: DocumentWithPromisedFile[]
  ): Promise<FineosClaimAdjudicationEvidencePage> {
    const fineosClaimAdjudicationEvidencePage = await this.gotoEvidence();
    for (const document of documents) {
      await fineosClaimAdjudicationEvidencePage.receiveEvidence(
        document.document_type
      );
    }
    return fineosClaimAdjudicationEvidencePage;
  }

  async saveChanges() {
    // this will ensure changes are saved and navigate back to the claim page
    await this.footerOkButton.click();
    await this.page.waitForLoadState("networkidle");
    return FineosClaimPage.get(this.page, this.claimId);
  }

  private readonly claimId: string;

  private readonly acceptButton = this.page.getByRole("button", {
    name: "Accept",
  });

  private readonly evidenceTab = this.page.locator("table.TabStrip td", {
    hasText: "Evidence",
  });

  private readonly footerOkButton = this.page
    .locator("#footerButtonsBar")
    .getByRole("button", { name: "OK" });

  private readonly manageRequestTab = this.page.locator("table.TabStrip td", {
    hasText: "Manage Request",
  });

  private readonly planEvidenceTab = this.page.locator("table.TabStrip td", {
    hasText: "Plan Evidence",
  });
}
