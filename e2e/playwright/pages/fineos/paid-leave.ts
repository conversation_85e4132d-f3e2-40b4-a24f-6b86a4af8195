import { expect, Page } from "@playwright/test";

import PageObjectModel from "../PageObjectModel";
import FineosCasesPage from "./cases";

interface PaymentVerifyOptions {
  preApproval?: string;
  sitAmount?: string;
  fitAmount?: string;
  duaIncome?: string;
  diaIncome?: string;
  overPayment?: string;
  waitingWeek?: string;
}

export default class FineosPaidLeavePage extends PageObjectModel {
  static async get(page: Page) {
    const title = page.getByRole("heading", {
      name: new RegExp("Absence Paid Leave Case"),
    });
    await expect(title).toBeVisible();
    return new FineosPaidLeavePage(page);
  }

  async viewPaymentDetails() {}

  async verifyName(name: string) {
    const nameTitle = this.page.getByRole("link", { name });
    await expect(nameTitle).toBeVisible();
  }

  async verifyPaymentInformation(
    paymentAmount: string,
    options: PaymentVerifyOptions = {}
  ) {
    await this.page.getByText("Financials").click();
    await this.page.getByText("Payment History").click();
    await this.page.waitForLoadState("networkidle");

    const payment = this.page.getByRole("cell", { name: paymentAmount }).nth(0);

    // Depending on which reductions and information has been reported to audit file, checks for that information in FINEOS

    await expect(payment).toBeVisible();
    if (options.waitingWeek) {
      await this.page.getByText("Amounts Pending").click();
      await expect(payment).toBeVisible();
      await this.page.getByText("Payments Made").nth(0).click();
    }
    if (options.fitAmount) {
      await this.page.getByText("Payments Summary Info").click();
      const fitPayment = this.page
        .getByRole("cell", { name: options.fitAmount })
        .nth(0);
      await expect(fitPayment).toBeVisible();
    }
    if (options.sitAmount) {
      await this.page.getByText("Payments Summary Info").click();
      const sitPayment = this.page
        .getByRole("cell", { name: options.sitAmount })
        .nth(0);
      await expect(sitPayment).toBeVisible();
    }
    if (options.diaIncome) {
      await this.page.getByText("Payments Summary Info").click();
      const diaIncome = this.page
        .getByRole("cell", { name: options.diaIncome })
        .nth(0);
      await expect(diaIncome).toBeVisible();
    }
    if (options.duaIncome) {
      await this.page.getByText("Payments Summary Info").click();
      const duaIncome = this.page
        .getByRole("cell", { name: options.duaIncome })
        .nth(0);
      await expect(duaIncome).toBeVisible();
    }
    if (options.overPayment) {
      const overPaymentTab = this.page.getByText("Overpayment Summary");
      await overPaymentTab.click();
      const overPaymentAmount = this.page.getByText(options.overPayment);
      await expect(overPaymentAmount).toBeVisible();
    }
    if (options.preApproval) {
      const caseDetailsTab = this.page.getByText("Case Details");
      await caseDetailsTab.click();
      const approvalStatus = this.page.locator("#CaseDetails_un41_CaseStatus");
      await expect(approvalStatus).toHaveText("Approved");
    }
  }

  async returnToCases() {
    await this.page.getByRole("link", { name: "Cases" }).click();
    await this.page.waitForLoadState("networkidle");
    return FineosCasesPage.get(this.page);
  }
}
