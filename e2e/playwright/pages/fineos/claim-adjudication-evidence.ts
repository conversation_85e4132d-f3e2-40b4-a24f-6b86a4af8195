import { expect, Page } from "@playwright/test";

import { DocumentUploadRequest } from "../../../src/_api";
import PageObjectModel from "../PageObjectModel";
import FineosClaimAdjudicationCertificationPeriodsPage from "./claim-adjudication-certification-periods";

export default class FineosClaimAdjudicationEvidencePage extends PageObjectModel {
  static async get(page: Page) {
    const title = page.getByRole("heading", {
      name: new RegExp("Plan Evidence"),
    });
    await expect(title).toBeVisible();
    return new FineosClaimAdjudicationEvidencePage(page);
  }

  async gotoCertificationPeriods(): Promise<FineosClaimAdjudicationCertificationPeriodsPage> {
    await this.certificationPeriodsTab.click();
    await this.page.waitForLoadState("networkidle");
    return await FineosClaimAdjudicationCertificationPeriodsPage.get(this.page);
  }

  async receiveEvidence(documentType: DocumentUploadRequest["document_type"]) {
    await this.page.getByRole("cell", { name: documentType }).click();
    await this.manageEvidenceButton.click();
    await this.page.waitForLoadState("networkidle");
    await this.evidenceReceipt.selectOption("Received");
    await this.evidenceDecision.selectOption("Satisfied");
    await this.evidenceDecisionReason.fill(
      "Evidence has been reviewed and approved"
    );
    await this.manageEvidenceOkButton.click();
    await this.page.waitForLoadState("networkidle");
    const row = this.page.getByRole("row").filter({
      has: this.page.getByRole("cell", { name: documentType }),
    });
    const satisfiedCell = row.getByRole("cell", {
      name: "Satisfied",
      exact: true,
    });
    await expect(satisfiedCell).toBeVisible();
    const receivedCell = row.getByRole("cell", {
      name: "Received",
      exact: true,
    });
    await expect(receivedCell).toBeVisible();
    const reasonCell = row.getByRole("cell", {
      name: "Evidence has been reviewed and approved",
      exact: true,
    });
    await expect(reasonCell).toBeVisible();
    await this.page.waitForLoadState("networkidle");
  }

  private readonly certificationPeriodsTab = this.page.locator(
    "table.TabStrip td",
    {
      // use regex to match the text "Certification Periods" exactly because strings are case insenstive
      hasText: /Certification Periods/,
    }
  );

  private readonly evidenceDecision = this.page.getByLabel(
    "Evidence Decision",
    {
      exact: true,
    }
  );

  private readonly evidenceDecisionReason = this.page.getByRole("textbox", {
    name: "Evidence Decision Reason",
  });

  private readonly evidenceReceipt = this.page.getByLabel("Evidence Receipt", {
    exact: true,
  });

  private readonly manageEvidenceButton = this.page.getByRole("button", {
    name: "Manage Evidence",
  });

  private readonly manageEvidenceOkButton = this.page.getByTitle("OK", {
    exact: true,
  });
}
