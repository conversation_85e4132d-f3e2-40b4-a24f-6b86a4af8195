import { expect, Page } from "@playwright/test";

import PageObjectModel from "../PageObjectModel";

export default class FineosClaimAdjudicationCertificationPeriodsPage extends PageObjectModel {
  static async get(page: Page) {
    const certificationPeriodsTab = page.locator("table.TabStrip td", {
      // use regex to match the text "Certification Periods" exactly because strings are case insenstive
      hasText: /Certification Periods/,
    });
    await expect(certificationPeriodsTab).toHaveClass("TabOn");
    return new FineosClaimAdjudicationCertificationPeriodsPage(page);
  }

  async prefill(): Promise<void> {
    await this.prefillButton.click();
    await this.yesButton.click();
    await this.page.waitForLoadState("networkidle");
  }

  private readonly prefillButton = this.page.getByRole("button", {
    name: "Prefill with Requested",
  });
  private readonly yesButton = this.page.getByRole("button", { name: "Yes" });
}
