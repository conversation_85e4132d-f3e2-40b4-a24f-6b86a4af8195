import { expect, Page } from "@playwright/test";
import { format, parseISO } from "date-fns";

import { GeneratedClaim } from "../../../src/generation/Claim";
import PageObjectModel from "../PageObjectModel";
import FineosClaimAdjudicationPage from "./claim-adjudication";
import FineosClaimAppealPage from "./claim-appeal";
import FineosClaimDocumentsPage from "./claim-documents";
import FineosPaidLeavePage from "./paid-leave";

interface PaidLeaveOptions {
  startDate?: string;
  endDate?: string;
}

export default class FineosClaimPage extends PageObjectModel {
  static async get(page: Page, id: string) {
    const title = await page.textContent(".case_pageheader_title");
    expect(title).toMatch(id);
    return new FineosClaimPage(page, id);
  }

  constructor(page: Page, claimId: string) {
    super(page);
    this.claimId = claimId;
  }

  async addAppeal(): Promise<FineosClaimAppealPage> {
    await this.addSubCaseButton.click();
    await this.addAppealButton.click();
    await this.page.waitForLoadState("networkidle");
    return FineosClaimAppealPage.get(this.page, this.claimId);
  }

  async approveLeaveRequest() {
    await this.approveLeaveRequestLink.click();
    await this.page.waitForLoadState("networkidle");
  }

  async completeAdjudication(claim: GeneratedClaim): Promise<void> {
    const fineosClaimAdjudicationPage = await this.gotoAdjudicate();
    await fineosClaimAdjudicationPage.receiveAllEvidence(claim.documents);
    await fineosClaimAdjudicationPage.saveChanges();
    // need to navigate back to adjudication page after saving
    await this.gotoAdjudicate();
    const fineosClaimAdjudicationEvidencePage =
      await fineosClaimAdjudicationPage.gotoEvidence();
    const fineosClaimAdjudicationCertificationPeriodsPage =
      await fineosClaimAdjudicationEvidencePage.gotoCertificationPeriods();
    await fineosClaimAdjudicationCertificationPeriodsPage.prefill();
    await fineosClaimAdjudicationPage.saveChanges();
    // need to navigate back to adjudication page after saving
    await this.gotoAdjudicate();
    await fineosClaimAdjudicationPage.acceptLeavePlan();
    // saving will naivigate back to the claim page
    await fineosClaimAdjudicationPage.saveChanges();
  }

  async gotoAdjudicate(): Promise<FineosClaimAdjudicationPage> {
    await this.adjudicateButton.click();
    await this.page.waitForLoadState("networkidle");
    return FineosClaimAdjudicationPage.get(this.page, this.claimId);
  }

  async gotoDocuments(): Promise<FineosClaimDocumentsPage> {
    await this.documentsTab.click();
    await this.page.waitForLoadState();
    return FineosClaimDocumentsPage.get(this.page);
  }

  async gotoPaidLeave(
    options: PaidLeaveOptions = {}
  ): Promise<FineosPaidLeavePage> {
    const paidLeaveCases = await this.page
      .getByRole("link", { name: "Absence Paid Leave Case" })
      .all();
    if (paidLeaveCases.length === 1) {
      await paidLeaveCases[0].click();
      await this.page.waitForLoadState();
      return FineosPaidLeavePage.get(this.page);
    }
    if (options.startDate && options.endDate) {
      for (const paidLeaveCase of paidLeaveCases) {
        await paidLeaveCase.click();
        await this.page.waitForLoadState();
        await this.page.getByText("Financials").click();
        const startDateField = await this.page
          .locator("#BenefitApprovalWidget_un43_Approval_Start_Date")
          .textContent();
        const endDateField = await this.page
          .locator("#BenefitApprovalWidget_un43_Approval_End_Date")
          .textContent();
        const formattedStartDate = format(
          parseISO(options.startDate),
          "MM/dd/yyyy"
        );
        const formattedEndDate = format(
          parseISO(options.endDate),
          "MM/dd/yyyy"
        );
        if (
          startDateField?.includes(formattedStartDate) &&
          endDateField?.includes(formattedEndDate)
        ) {
          return FineosPaidLeavePage.get(this.page);
        }
      }
      throw Error("Payment with dates provided not found");
    } else {
      await this.page
        .getByRole("link", { name: "Absence Paid Leave Case" })
        .nth(0)
        .click();
      return FineosPaidLeavePage.get(this.page);
    }
  }

  private readonly claimId: string;

  private readonly addSubCaseButton = this.page.getByRole("link", {
    name: "Add Sub Case",
  });

  private readonly adjudicateButton = this.page.getByRole("button", {
    name: "Adjudicate",
  });

  private readonly addAppealButton = this.page.getByRole("link", {
    name: "Appeal",
  });

  private readonly approveLeaveRequestLink = this.page.getByRole("link", {
    name: "Approve",
    exact: true,
  });

  private readonly documentsTab = this.page.locator("table.TabStrip td", {
    hasText: "Documents",
  });
}
