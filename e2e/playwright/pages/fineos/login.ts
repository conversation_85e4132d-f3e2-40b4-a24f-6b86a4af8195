import { chromium, expect, Page } from "@playwright/test";

import config from "../../../src/config";
import PageObjectModel from "../PageObjectModel";
import FineosDashboardPage from ".";

const environment = config("ENVIRONMENT");
const isSSO =
  config("HAS_FR25_1") || environment === "uat" || environment === "breakfix";
const url = config("FINEOS_BASEURL");
const ssoUsername = config("SSO_USERNAME");
const ssoPassword = config("SSO_PASSWORD");
const basicUsername = config("FINEOS_USERNAME");
const basicPassword = config("FINEOS_PASSWORD");

export default class FineosLoginPage extends PageObjectModel {
  static async get(page: Page) {
    await page.waitForURL(url);
    return new FineosLoginPage(page);
  }

  static async goto(page: Page) {
    const response = await page.goto(url);
    if (response?.status() != null) {
      const status = response.status();
      const badStatusCodes = [403, 404, 500, 504];
      if (badStatusCodes.includes(status)) {
        throw new Error(`Fineos Login page returned status code ${status}.`);
      }
    }
    await page.waitForLoadState();
    return new FineosLoginPage(page);
  }

  async logIn() {
    if (isSSO) {
      return await this.loginWithSSO();
    } else {
      return FineosDashboardPage.get(this.page);
    }
  }

  async loginWithSSO() {
    if (await this.corporateIdButton.isVisible()) {
      await this.corporateIdButton.click();
      await this.page.waitForLoadState();
    }
    await this.emailField.fill(ssoUsername);
    await this.nextButton.click();
    await this.page.waitForLoadState();
    await expect(this.passwordHeading).toBeVisible();
    await this.passwordField.fill(ssoPassword);
    await this.signInButton.click();
    await this.page.waitForLoadState();
    await this.declineStaySignedInButton.click();
    await this.page.waitForLoadState();
    return FineosDashboardPage.get(this.page);
  }

  async logInWithHttpCredentials() {
    const browser = await chromium.launch({
      executablePath: process.env.PLAYWRIGHT_CHROMIUM_EXECUTABLE_PATH,
    });

    const httpCredentials = {
      username: basicUsername,
      password: basicPassword,
    };
    const page = await browser.newPage({
      viewport: { width: 1200, height: 1000 },
      httpCredentials,
    });

    await page.goto(url);
    await page.waitForLoadState("networkidle");

    return FineosDashboardPage.get(this.page);
  }

  private readonly corporateIdButton = this.page.getByRole("button", {
    name: "somvrf-IDT3",
  });

  private readonly emailField = this.page.locator("input[type='email']");

  private readonly passwordHeading = this.page.getByRole("heading", {
    name: "Enter password",
  });

  private readonly passwordField = this.page.locator('input[type="password"]');

  private readonly nextButton = this.page.getByRole("button", {
    name: "Next",
  });

  private readonly signInButton = this.page.getByRole("button", {
    name: "Sign in",
  });

  private declineStaySignedInButton = this.page.getByRole("button", {
    name: "No",
  });
}
