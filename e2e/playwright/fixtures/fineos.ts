import {
  <PERSON><PERSON><PERSON><PERSON>ontext,
  Browser<PERSON>ontextOptions,
  Fixtures,
  Page,
  PlaywrightTestArgs,
  PlaywrightWorkerArgs,
} from "@playwright/test";

import config from "../../src/config";
import { GeneratedClaim } from "../../src/generation/Claim";
import { ApplicationSubmissionResponse, Scenarios } from "../../src/types";
import {
  dispatchPostSubmit,
  generateClaims,
  submitClaim,
} from "../../src/util/claimGenerator";
import { assertValidClaim } from "../../src/util/typeUtils";
import FineosDashboardPage from "../pages/fineos";
import FineosCasesPage from "../pages/fineos/cases";
import FineosClaimPage from "../pages/fineos/claim";
import FineosClaimAppealPage from "../pages/fineos/claim-appeal";
import FineosClaimDocumentsPage from "../pages/fineos/claim-documents";
import FineosLoginPage from "../pages/fineos/login";
import FineosPaidLeavePage from "../pages/fineos/paid-leave";

const environment = config("ENVIRONMENT");
const isSSO =
  config("HAS_FR25_1") || environment === "uat" || environment === "breakfix";
const basicUsername = config("FINEOS_USERNAME");
const basicPassword = config("FINEOS_PASSWORD");

export type FineosOptions = {
  scenarioIds?: Scenarios[];
};

interface ClaimSubmission {
  claim: GeneratedClaim;
  submission: ApplicationSubmissionResponse;
}

const generateAndSubmitClaim = async (
  scenarioId: Scenarios,
  attempt: number = 0
): Promise<ClaimSubmission> => {
  console.log("generating claim for scenarioId:", scenarioId);
  try {
    const claim = (await generateClaims(scenarioId, 1))[0];
    if (!claim.metadata) {
      claim.metadata = {};
    }
    console.log("submitting claim:", scenarioId);
    const res = await submitClaim(claim);
    assertValidClaim(claim.claim);
    console.log("claim submitted:", scenarioId);
    return { claim, submission: res };
  } catch (error) {
    console.error(
      `Error generating or submitting claim for scenarioId ${scenarioId}:`,
      error
    );
    console.log(
      `Delaying and then will retry claim generation and submission for scenarioId ${scenarioId}...`
    );
    await new Promise((resolve) => setTimeout(resolve, 5_000));
    if (attempt < 3) {
      console.log(
        `Retrying claim generation and submission for scenarioId ${scenarioId}...`
      );
      return generateAndSubmitClaim(scenarioId, attempt + 1);
    } else {
      console.error(
        `Failed to generate or submit claim for scenarioId ${scenarioId} after 3 attempts`
      );
      throw error;
    }
  }
};

const claimSubmissionPostSubmit = async (
  scenarioId: Scenarios,
  claimSubmission: ClaimSubmission,
  attempt: number = 0
): Promise<void> => {
  console.log("dispatchPostSubmit:", scenarioId);
  try {
    await dispatchPostSubmit(claimSubmission.claim, claimSubmission.submission);
    console.log("claim submission dispatched:", scenarioId);
  } catch (error) {
    console.error(
      `Error dispatching post submit for scenarioId ${scenarioId}:`,
      error
    );
    console.log(
      `Delaying and then will retry dispatchPostSubmit for scenarioId ${scenarioId}...`
    );
    await new Promise((resolve) => setTimeout(resolve, 30_000));
    if (attempt < 3) {
      console.log(
        `Retrying claim dispatchPostSubmit for scenarioId ${scenarioId}...`
      );
      await claimSubmissionPostSubmit(scenarioId, claimSubmission, attempt + 1);
    } else {
      console.error(
        `Failed to dispatch post submit for scenarioId ${scenarioId} after 3 attempts`
      );
      throw error;
    }
  }
};

export type FineosFixtures = {
  fineosBrowserContext: BrowserContext;
  fineosCasesPage: FineosCasesPage;
  fineosClaimAppealPage: FineosClaimAppealPage;
  fineosClaimDocumentsPage: FineosClaimDocumentsPage;
  fineosClaimPage: FineosClaimPage;
  fineosClaimSubmissions: Record<Scenarios, ClaimSubmission>;
  fineosPaidLeavePage: FineosPaidLeavePage;
  fineosPage: Page;
  loggedInFineosPage: FineosDashboardPage;
};

export const fineosFixtures: Fixtures<
  FineosFixtures & FineosOptions,
  object,
  PlaywrightTestArgs,
  PlaywrightWorkerArgs
> = {
  fineosBrowserContext: async ({ browser }, use) => {
    const options: BrowserContextOptions = {};
    if (!isSSO) {
      // If not in SSO environment, set the basic auth for the context
      options.httpCredentials = {
        username: basicUsername,
        password: basicPassword,
      };
    }
    const context = await browser.newContext(options);

    await use(context);
    await context.close();
  },
  fineosClaimSubmissions: [
    async ({ scenarioIds }, use) => {
      if (!scenarioIds) {
        // If no scenarioId is provided, throw an error
        throw new Error("ScenarioId is required for generating claims");
      }

      const claimSubmissions: Record<Scenarios, ClaimSubmission> = {} as Record<
        Scenarios,
        ClaimSubmission
      >;
      const claimSubmissionPromises = scenarioIds.map(async (scenarioId) => {
        const claimSubmission = await generateAndSubmitClaim(scenarioId);
        claimSubmissions[scenarioId] = claimSubmission;
      });

      await Promise.all(claimSubmissionPromises);

      const fineosClaimSubmissions = Object.entries(claimSubmissions);
      for (const fineosClaimSubmission of fineosClaimSubmissions) {
        const scenarioId = fineosClaimSubmission[0] as Scenarios;
        const claimSubmission = fineosClaimSubmission[1];
        claimSubmissionPostSubmit(scenarioId, claimSubmission);
      }

      console.log("FINEOS Claim Submissions:", claimSubmissions);

      await use(claimSubmissions); // Return the FINEOS absence ID for further use in tests
    },
    { timeout: 0 },
  ],
  fineosCasesPage: async ({ loggedInFineosPage }, use) => {
    // Use the logged-in FINEOS page to navigate to cases
    const fineosCasesPage = await loggedInFineosPage.naviagteToCases();
    // Return the cases page for further use in tests
    await use(fineosCasesPage);
  },
  fineosClaimPage: async ({ fineosCasesPage, fineosClaimSubmissions }, use) => {
    // Navigate to the specific case using the FINEOS absence ID
    const fineosClaimPage = await fineosCasesPage.gotoCase(
      Object.values(fineosClaimSubmissions)[0].submission.fineos_absence_id
    );
    // Return the claim page for further use in tests
    await use(fineosClaimPage);
  },
  fineosClaimAppealPage: async ({ fineosClaimPage }, use) => {
    // Navigate to the appeal page from the claim page
    const fineosClaimAppealPage = await fineosClaimPage.addAppeal();
    // Return the appeal page for further use in tests
    await use(fineosClaimAppealPage);
  },
  fineosClaimDocumentsPage: async ({ fineosClaimPage }, use) => {
    // Navigate to the claim documents page from the claim page
    const fineosClaimDocumentsPage = await fineosClaimPage.gotoDocuments();
    // Return the claim documents page for further use in tests
    await use(fineosClaimDocumentsPage);
  },
  fineosPaidLeavePage: async ({ fineosClaimPage }, use) => {
    // Navigate to the paid leave page from the claim page
    const fineosPaidLeavePage = await fineosClaimPage.gotoPaidLeave();
    // Return the paid leave page for further use in tests
    await use(fineosPaidLeavePage);
  },
  loggedInFineosPage: async ({ fineosPage }, use) => {
    const fineosLoginPage = await FineosLoginPage.goto(fineosPage);
    const fineosDashboardPage = await fineosLoginPage.logIn();
    await use(fineosDashboardPage);
  },
  fineosPage: async ({ fineosBrowserContext }, use) => {
    const page = await fineosBrowserContext.newPage();
    await use(page);
  },
  scenarioIds: [undefined, { option: true }],
};
