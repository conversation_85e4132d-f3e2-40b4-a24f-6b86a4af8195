import { Page } from "@playwright/test";

export async function waitForAccountLoad(page: Page) {
  const loadingSpinner = page.locator('span[aria-label="Loading account"]');
  await loadingSpinner.waitFor();
  await loadingSpinner.waitFor({ state: "hidden" });
}

export async function consentToProfileSharingIfPrompted(page: Page) {
  if (await isAskedToConsentToProfileSharing(page)) {
    await consentToProfileSharing(page);
  }
}

// TODO(PFMLPB-19963): This is just a verbatim copy of the functions of the same
// name from myMassGov.ts since @playwright/test::Page is distinct from
// playwright::chromium/test. Ideally, we'd have a unified interface for both.
async function isAskedToConsentToProfileSharing(page: Page) {
  // The content of the page renders later than the page itself. Waiting for the
  // common heading that appears on each page to be visible allows checking for
  // the profile sharing consent prompt to happen at the right time, without
  // throwing an error if this is a different MyMassGov page.
  await page
    .getByRole("heading", { name: "MyMassGov", level: 1 })
    .waitFor({ state: "visible" });
  return page.getByText("Consent to share MyMassGov").isVisible();
}

function consentToProfileSharing(page: Page) {
  return page.getByLabel("Continue").click();
}
