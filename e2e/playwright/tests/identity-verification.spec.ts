import { ApplicationRequestBody } from "../../src/_api";
import config from "../../src/config";
import { ClaimGenerator } from "../../src/generation/Claim";
import * as scenarios from "../../src/scenarios";
import { Credentials } from "../../src/types";
import { getEmployeePool } from "../../src/util/common";
import { generateCredentials } from "../../src/util/credentials";
import { registerClaimant } from "../../src/util/myMassGov";
import { createIntentionalSkipFlag } from "../../src/util/skipFlag";
import { assertValidClaim } from "../../src/util/typeUtils";
import { test } from "../extensions/portal";
import {
  logInAndSetUp,
  submitApplicationPartOne,
  submitApplicationPartThree,
  submitApplicationPartTwo,
} from "../utils/application";

const VERIFIED_STATUS_MESSAGE = "Identity verified";
const REJECTED_STATUS_MESSAGE = "Your verification status is pending";
const REFERRED_STATUS_MESSAGE = "We need more information";

const intentionalSkipFlag = createIntentionalSkipFlag(
  !config("HAS_PROFILE_IDV_FEATURE")
);

// 2 minutes
test.setTimeout(120_000);

test.describe("IDV tests", () => {
  let claimantCredentials: Credentials;
  let application: ApplicationRequestBody;
  let applicationID: string;

  test.beforeEach(async () => {
    const employeePool = await getEmployeePool();

    application = ClaimGenerator.generate(
      employeePool,
      scenarios.MED_CONT_ER_APPROVE.employee,
      scenarios.MED_CONT_ER_APPROVE.claim
    ).claim;
    claimantCredentials = generateCredentials();
    claimantCredentials.username = claimantCredentials.username.toLowerCase();
    await registerClaimant(claimantCredentials, {
      captureAuthenticatorKey: true,
      name: {
        first: application.first_name as string,
        last: application.last_name as string,
      },
    });
  });

  // These specific ssn's will return the expected status for each test case
  const testCases = [
    {
      status: "Verified",
      testSsn: "*********",
      statusMessage: VERIFIED_STATUS_MESSAGE,
      licenseNumber: "*********",
      makeChange: false,
    },
    {
      status: "Rejected",
      testSsn: "777777777",
      statusMessage: REJECTED_STATUS_MESSAGE,
      licenseNumber: "*********",
      makeChange: false,
    },
    {
      status: "Referred",
      testSsn: "222222222",
      statusMessage: REFERRED_STATUS_MESSAGE,
      licenseNumber: "*********",
      makeChange: false,
    },
    {
      status: "Verified and made changes",
      testSsn: "*********",
      statusMessage: VERIFIED_STATUS_MESSAGE,
      licenseNumber: "*********",
      makeChange: true,
    },
  ] as const;

  testCases.forEach(({ status, ...testCases }) => {
    test.skip(
      !config("HAS_PROFILE_IDV_FEATURE"),
      "MyMassGov Identity Verification testing is only enabled in certain environments."
    );

    test.use({
      // specifying this will set add credentials to the context when routing to idv pages
      portalAuthorizeIdv: [true, { scope: "test" }],
      // specifying this will set which credentials to use when creating the loggedInClaimantPage
      portalClaimantCredentials: [claimantCredentials, { scope: "test" }],
    });

    test(`${intentionalSkipFlag}Claimant receives ${status} status after completing IDV process and displays appropriate status in FINEOS`, async ({
      portalPage,
      loggedInFineosPage,
    }) => {
      const { testSsn, statusMessage, licenseNumber, makeChange } = testCases;
      assertValidClaim(application);
      const applicationPage = await logInAndSetUp(
        portalPage,
        claimantCredentials
      );
      const finishedPartOne = await submitApplicationPartOne(
        portalPage,
        applicationPage,
        application,
        { testSsn, statusMessage, licenseNumber, makeChange }
      );
      const finishedPartTwo = await submitApplicationPartTwo(finishedPartOne);
      if (testCases.makeChange) {
        status = "Rejected";
      }
      const reviewApplicationPage = await submitApplicationPartThree(
        finishedPartTwo,
        { needUploadId: status !== "Verified", skipCertUpload: true }
      );
      applicationID = await reviewApplicationPage.getApplicationID();

      // Appropriate RMV Status should appear in FINEOS
      const fineosCasePage = await loggedInFineosPage.searchForApplication(
        applicationID
      );
      const isVerified = status === "Verified";
      await fineosCasePage.checkVerificationStatus(isVerified);
      await fineosCasePage.checkMmgIdvVerified(isVerified);
      await fineosCasePage.checkEvidence(isVerified);
    });
  });
});
