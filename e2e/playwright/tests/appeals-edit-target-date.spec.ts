import { test } from "../extensions/fineos";

test.describe("Claim Appeals edit Target Date", async () => {
  test.describe.configure({ retries: 2 });

  test.use({
    // specifying this scenario will generate a claim for the claimant to use in the test
    scenarioIds: [["CCAP90ERSP"], { scope: "test" }],
  });

  test("Edit the target date of an appeal", async ({
    fineosClaimAppealPage,
  }) => {
    const fineosAppealDetailsPage =
      await fineosClaimAppealPage.openAppealDetails();
    await fineosAppealDetailsPage.editAppeal();
    await fineosAppealDetailsPage.editTargetDate();
  });
});
