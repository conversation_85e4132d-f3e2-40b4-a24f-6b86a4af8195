import { ClaimGenerator, GeneratedClaim } from "../../src/generation/Claim";
import * as scenarios from "../../src/scenarios";
import { getEmployeePool } from "../../src/util/common";
import { getLeaveAdminCredentials } from "../../src/util/credentials";
import { assertIsString } from "../../src/util/typeUtils";
import { expect, test } from "../extensions/portal";
import PortalEmployerIndexPage from "../pages/portal/admin";
import PortalEmployerApplicationsPage from "../pages/portal/admin/applications";
import PortalOAuthStartPage from "../pages/portal/oauth-start";

const claimantStatusFilters = [
  "Approved",
  "Denied",
  "Withdrawn",
  "Pending",
  "Cancelled",
];

test.describe("dashboard filters", () => {
  test.describe.configure({ retries: 2 });

  let claim: GeneratedClaim;
  test.beforeAll(async () => {
    const employeePool = await getEmployeePool();
    claim = ClaimGenerator.generate(
      employeePool,
      scenarios.MED_INTER_INEL.employee,
      scenarios.MED_INTER_INEL.claim
    );
  });

  let applicationsPage: PortalEmployerApplicationsPage;

  test.beforeEach(async ({ portalPage }) => {
    test.setTimeout(30000);
    const myMassGovPage = await PortalOAuthStartPage.goto(portalPage);
    const MmgBusinessIndexPage = await myMassGovPage.logInAsLeaveAdmin();
    const credentials = getLeaveAdminCredentials(claim.employer);
    const portalEmployerIndexPage = (await MmgBusinessIndexPage.logIn(
      credentials
    )) as PortalEmployerIndexPage;
    applicationsPage = await portalEmployerIndexPage.navigateToApplications();
    await applicationsPage.showFilters();
    await expect(portalPage.locator("div#filters")).toBeVisible();
  });

  test("it should list all claims with a review requested", async ({
    portalPage,
  }) => {
    const targetUrl = /.*\/claims\?.*is_reviewable=yes/;
    const responsePromise = portalPage.waitForResponse(targetUrl, {
      timeout: 30000,
    });

    await portalPage
      .getByText("Yes, review requested")
      .waitFor({ state: "visible" });
    await portalPage.getByText("Yes, review requested").click();
    await PortalEmployerApplicationsPage.clickApplyFilters(portalPage);

    await responsePromise;
    await PortalEmployerApplicationsPage.assertClaimsNeedsReview(
      portalPage,
      "Review Application"
    );
  });

  test("it should list all claims that don't require a review", async ({
    portalPage,
  }) => {
    const targetUrl = /.*\/claims\?.*is_reviewable=no/;

    const responsePromise = portalPage.waitForResponse(targetUrl, {
      timeout: 0,
    });

    await portalPage
      .getByText("No, review not needed")
      .waitFor({ state: "visible" });
    await portalPage.getByText("No, review not needed").click({ force: true });
    await PortalEmployerApplicationsPage.clickApplyFilters(portalPage);

    await responsePromise;

    await PortalEmployerApplicationsPage.assertClaimsDontNeedReview(portalPage);
  });

  test("Apply Filters button should be disabled when both 'show all' filters are selected", async ({
    portalPage,
  }) => {
    const showAllfirst = portalPage.getByText("Show all").first();
    const showAllSecond = portalPage.getByText("Show all").nth(1);

    await showAllfirst.waitFor({ state: "visible" });
    await showAllfirst.click();
    await expect(showAllfirst).toBeChecked();
    await expect(showAllSecond).toBeChecked();

    await expect(
      portalPage.getByRole("button", { name: "Apply filters" })
    ).toBeDisabled();
  });

  test("it should filter and display claims properly", async ({
    portalPage,
  }) => {
    for (const filter of claimantStatusFilters) {
      const targetUrl = `**/*request_decision=${filter.toLowerCase()}*`;

      const filterOption = portalPage.locator("label", { hasText: filter });
      await filterOption.waitFor({ state: "visible" });
      await filterOption.click({ force: true });

      await PortalEmployerApplicationsPage.clickApplyFilters(portalPage);

      await portalPage.waitForURL(targetUrl);

      const filters = portalPage.locator('div[data-testid="filters-menu"]');
      await expect(filters).toContainText(filter);

      const cases = portalPage.locator(
        'table tbody tr td[data-label="Leave details"]'
      );
      const caseElements = await cases.elementHandles();

      for (const singleCase of caseElements) {
        const text = await singleCase.innerText();
        expect(text).toContain(filter.toUpperCase());
      }

      await portalPage.getByText("Show filters").click();
    }
  });

  test("it should use search bar to search for claims with claim ID", async ({
    portalPage,
  }) => {
    const firstApplicationID = await applicationsPage.applicationIDs
      .first()
      .textContent();
    assertIsString(firstApplicationID);

    const targetUrl = new RegExp(
      `.*\\/claims\\?.*search=${firstApplicationID}`,
      "i"
    );
    const responsePromise = portalPage.waitForResponse(targetUrl, {
      timeout: 0,
    });

    await applicationsPage.searchByID(
      firstApplicationID,
      portalPage,
      applicationsPage,
      responsePromise
    );
  });

  test("it should use search bar to search for claims with Employee name", async ({
    portalPage,
  }) => {
    const firstApplicationName = await applicationsPage.applicationNames
      .first()
      .textContent();
    ``;
    assertIsString(firstApplicationName);

    const { formattedName, name } =
      PortalEmployerApplicationsPage.formatName(firstApplicationName);

    const targetUrl = new RegExp(
      `.*\\/claims\\?.*search=${name.firstName}\\+${name.lastName}`,
      "i"
    );

    const responsePromise = portalPage.waitForResponse(targetUrl, {
      timeout: 0,
    });

    await applicationsPage.searchByName(
      formattedName,
      portalPage,
      applicationsPage,
      responsePromise
    );
  });
});
