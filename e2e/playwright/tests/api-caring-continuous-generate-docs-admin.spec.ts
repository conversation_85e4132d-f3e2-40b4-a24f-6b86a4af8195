import { test } from "../extensions/fineos";

test.describe("Care for Family Member Generate Documents - Admin", async () => {
  test.describe.configure({ retries: 2 });

  test.use({
    // specifying this scenario will generate a claim for the claimant to use in the test
    scenarioIds: [["CCAP90ERSP"], { scope: "test" }],
  });

  test("Generate W9 for claim", async ({ fineosClaimDocumentsPage }) => {
    await fineosClaimDocumentsPage.generateDocument("W9 Tax Form");
  });

  test("Generates the EFT Change Request document in Spanish", async ({
    fineosClaimDocumentsPage,
  }) => {
    await fineosClaimDocumentsPage.generateDocument("EFT Change Request");
  });
});
