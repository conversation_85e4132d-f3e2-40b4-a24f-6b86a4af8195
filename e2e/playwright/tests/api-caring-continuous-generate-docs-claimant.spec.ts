import { test } from "../extensions/portal";

test.describe("Care for Family Member Generate Documents - Claimant", async () => {
  test.describe.configure({ retries: 2 });

  test.use({
    // specifying this scenario will generate a claim for the claimant to use in the test
    scenarioIds: [["CCAP90ERSP"], { scope: "test" }],
  });

  test.beforeEach(async ({ fineosClaimDocumentsPage }) => {
    await fineosClaimDocumentsPage.generateDocument("EFT Change Request");
  });

  test("checks portal for EFT Change Request notice and asserts for Spanish", async ({
    portalUserApplicationStatusPage,
  }) => {
    await portalUserApplicationStatusPage.checktNoticeForClaimant(
      "EFT Change Request (PDF)"
    );
    await portalUserApplicationStatusPage.downloadNoticeAndAssertContent(
      "EFT Change Request (PDF)",
      "Formulario EFT"
    );
  });
});
