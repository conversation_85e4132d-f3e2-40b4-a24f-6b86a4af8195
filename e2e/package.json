{"name": "pfml-automation", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "tsc", "watch": "tsc -w", "cypress": "cypress", "cypress:open": "cypress open --e2e", "cypress:open:local": "E2E_ENVIRONMENT=local cypress open", "cypress:open:local:portal": "E2E_ENVIRONMENT=local cypress open --e2e --config specPattern=cypress/specs/deploy/**", "cypress:run": "cypress run --e2e", "cypress:run:video": "cypress run --e2e --config video=true", "playwright": "playwright", "playwright:codegen": "playwright codegen", "playwright:open": "playwright test --ui", "playwright:run": "playwright test", "lint": "npm run lint:js -- --fix && npm run lint:ts", "lint:ci": "npm run lint:js -- --max-warnings=0 && npm run lint:ts", "lint:ts": "tsc --noEmit && tsc --project ./cypress/tsconfig.json --noEmit && tsc --project ./test/tsconfig.json --noEmit && tsc --project ./playwright/tsconfig.json --noEmit && tsc --project ./nerdpack/tsconfig.json", "lint:js": "eslint src/ cypress/ playwright/ test/ nerdpack/", "lint:staged": "eslint --cache --fix src/ cypress/ playwright/ test/ nerdpack/", "format": "prettier --write", "format:all": "npm run format -- \"**/*.{js,json,md,mdx,ts,tsx,scss,yaml,yml}\"", "format-check": "prettier --check", "format-check:all": "npm run format-check -- \"**/*.{js,json,md,mdx,ts,tsx,scss,yaml,yml}\"", "test:unit": "jest --config=jest.unit.json", "test:integration": "jest --testPathIgnorePatterns=./test/integration/dor_upload_and_service_agreements.[jt]s --config=jest.integration.json", "test:integration:ci": "jest --config=jest.integration.json --reporters=default --reporters=./dist/src/reporter/jest.js", "cli": "ts-node src/cli.ts", "generate:api": "npx @spec2ts/openapi-client ../api/openapi.yaml -o src/_api.ts", "artillery:dev": "npm run artillery:bundle && artillery run -e basic dist/development.yml", "artillery:run": "E2E_ENVIRONMENT=performance npm run artillery:bundle && artillery run -e performance dist/development.yml", "artillery:bundle": "rm -rf ./dist && webpack --config=webpack.artillery.config.ts", "newrelic:generate-fineos-dashboards": "ts-node src/scripts/2021-07-19-generate-nr-api-dashboards.ts", "newrelic:generate-fineos-endpoints": "ts-node src/cli.ts newrelic generate-fineos-endpoints"}, "author": "", "license": "ISC", "devDependencies": {"@babel/core": "^7.26.10", "@babel/plugin-transform-class-properties": "^7.25.9", "@babel/preset-env": "^7.26.9", "@babel/preset-typescript": "^7.26.0", "@cypress/grep": "^4.1.0", "@eslint/compat": "^1.2.1", "@eslint/eslintrc": "^3.1.0", "@eslint/js": "^9.13.0", "@testing-library/cypress": "^10.0.3", "@types/babel__code-frame": "^7.0.6", "@types/copy-webpack-plugin": "^6.4.2", "@types/glob": "^8.1.0", "@types/google-libphonenumber": "^7.4.23", "@types/jest": "^29.5.12", "@types/js-yaml": "^4.0.0", "@types/ndjson": "^2.0.0", "@types/node": "^20.9.0", "@types/node-fetch": "^2.5.12", "@types/pdf-parse": "^1.1.0", "@types/stream-json": "^1.7.2", "@types/uuid": "^8.3.0", "@types/webpack": "^5.28.0", "@types/zip-webpack-plugin": "^3.0.2", "@typescript-eslint/eslint-plugin": "^8.9.0", "@typescript-eslint/parser": "^8.9.0", "babel-loader": "^10.0.0", "copy-webpack-plugin": "^12.0.2", "eslint": "^9.12.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^15.11.0", "google-libphonenumber": "^3.2.32", "jest": "^29.7.0", "path-browserify": "^1.0.1", "playwright-chromium": "^1.44.1", "prettier": "^2.3.2", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.5.4", "webpack": "^5.94.0", "webpack-cli": "^4.9.2"}, "dependencies": {"@actions/core": "^v1.10.0", "@aws-sdk/client-cloudwatch-logs": "^3.621.0", "@aws-sdk/client-dynamodb": "^3.621.0", "@aws-sdk/client-ec2": "^3.621.0", "@aws-sdk/client-ecs": "^3.621.0", "@aws-sdk/client-s3": "^3.621.0", "@aws-sdk/client-sfn": "^3.621.0", "@aws-sdk/client-sqs": "^3.621.0", "@aws-sdk/client-ssm": "^3.621.0", "@aws-sdk/client-sts": "^3.621.0", "@aws-sdk/credential-provider-sso": "^3.621.0", "@aws-sdk/lib-storage": "^3.621.0", "@aws-sdk/s3-request-presigner": "^3.621.0", "@aws-sdk/util-arn-parser": "^3.568.0", "@faker-js/faker": "^8.4.1", "@influxdata/influxdb-client": "^1.18.0", "@playwright/test": "^1.44.0", "@types/debug": "^4.1.7", "@types/find-test-names": "^1.24.0", "@types/lodash": "^4.14.184", "@types/tinymce": "^4.6.5", "abort-controller": "^3.0.0", "artillery": "^2.0.19", "csv": "^5.5.0", "csv-parse": "^5.6.0", "csv-parser": "^3.0.0", "csv-stringify": "^5.6.2", "cypress": "^14.2.0", "cypress-iframe": "^1.0.1", "cypress-multi-reporters": "^2.0.5", "date-fns": "^2.29.3", "date-fns-timezone": "^0.1.4", "delay": "^4.3.0", "dotenv": "^8.2.0", "encoding": "^0.1.13", "enquirer": "^2.3.6", "find-test-names": "^1.28.6", "form-data": "^4.0.0", "glob": "^7.2.0", "graphql": "^15.4.0", "graphql-request": "^3.7.0", "jest-editor-support": "^31.1.1", "jest-message-util": "^27.5.1", "jest-runner-groups": "^2.1.0", "js-yaml": "^4.0.0", "lodash": "^4.17.21", "mocha": "^10.8.2", "node-fetch": "^2.6.7", "otpauth": "^9.2.4", "p-retry": "^4.2.0", "pdf-lib": "^1.11.1", "pdf-parse": "^1.1.1", "ssn": "^1.0.3", "stream-json": "^1.7.3", "streaming-iterables": "^5.0.4", "uuid": "^8.3.1", "winston": "^3.3.3", "yargs": "^17.0.1"}, "overrides": {"@babel/traverse": "^7.26.10", "axios": "^1.8.2", "yargs-parser": "21.1.1", "glob-parent": "6.0.2", "artillery": {"posthog-node": "3.1.3"}, "ip": "https://registry.npmjs.org/@seal-security/ip/-/ip-2.0.0-sp-1.tgz", "cross-spawn": "7.0.5", "jsonpath-plus": "10.3.0"}, "volta": {"node": "20.19.0", "npm": "10.1.0"}}