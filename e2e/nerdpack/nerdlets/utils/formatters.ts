import { intervalToDuration } from "date-fns";

import { TestResultInstanceDataPoint } from "./run-status";

/**
 * Formats a component name to a readable label.
 * Currently only maps "api" to "API", otherwise capitalizes the first letter
 * Used for displaying component labels in New Relic dashboard views.
 */
export const labelComponent = (name: string) => {
  if (!name) {
    return name;
  }
  switch (name) {
    case "api":
      return "API";
    default:
      return name.charAt(0).toUpperCase() + name.substring(1);
  }
};

/**
 * Maps environment codes to friendly display names.
 * These values are used throughout PFML dashboards in New Relic to label
 * environments clearly for users (e.g., "tst1" → "TST1/IDT1")
 *  */
export const labelEnv = (name: string) => {
  name = name.toLowerCase();
  switch (name) {
    case "breakfix":
      return "Breakfix/PFX";
    case "uat":
      return "UAT";
    case "training":
      return "Training/TRN";
    case "trn2":
      return "Training2/TRN2";
    case "tst1":
      return "TST1/IDT1";
    case "tst2":
      return "TST2/IDT2";
    case "tst3":
      return "TST3/IDT3";
    default:
      return name.charAt(0).toUpperCase() + name.substring(1);
  }
};

/**
 * Converts a duration in milliseconds into a human-readable time string
 * in the format "00h 00m 00s".
 */
export function msToTime(duration: number): string {
  const { hours, minutes, seconds } = intervalToDuration({
    start: 0,
    end: duration,
  });

  return `${hours}h ${minutes}m ${seconds}s`;
}

/**
 * Converts a single test result (facet) into a formatted multiline string.
 * Used to populate failure tooltips in the "Spec Overview" New Relic panel.
 *
 * @param facet - test result data point for a specific environment + file
 * @returns a string like:
 *
 * Affected Environment: tst1
 * Affected E2E Spec: path/to/spec.cy.js
 * Block Title: should do something
 * ...
 */
export function fillEdm(facet: TestResultInstanceDataPoint): string {
  return [
    `Affected Environment: ${facet.environment}`,
    `Affected E2E Spec: ${facet.file}`,
    `Block Title: ${facet.blockTitle}`,
    `Spec Group: ${facet.specGroup}`,
    `PFML Application ID: ${facet.applicationId ?? ""}`,
    `FINEOS Absence ID: ${facet.fineosAbsenceId ?? ""}`,
    `Run URL: ${facet.runUrl}`,
    `Error Category: ${facet.category ?? ""}`,
    `Error Subcategory: ${facet.subCategory ?? ""}`,
  ].join("\n");
}
