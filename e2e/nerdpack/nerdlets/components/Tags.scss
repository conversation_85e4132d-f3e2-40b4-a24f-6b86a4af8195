.E2E-tag {
  padding: 2px 12px;
  border: 0.1em #aaa solid;
  border-radius: 5px;
  margin-right: 5px;
  color: #aaa;
  font-size: 10px;

  &.branch,
  &.pr {
    color: #333;
    border-color: #333;
    background: #dfe4f5;
    font-weight: bold;
  }

  &.deploy {
    color: #6a8de1;
    border-color: #6a8de1;
    background: #dfe4f5;
    font-weight: bold;
  }

  &.morning {
    color: #986ae1;
    border-color: #986ae1;
    background: #f0e6f5;
    font-weight: bold;
  }
}

.tag {
  font-family: monospace;
  display: inline-block;
  padding: 2px 6px;
  background: $TAG_BG;
  border: 0.1em $TAG_COLOR solid;
  color: $TAG_COLOR;
  border-radius: 5px;
  margin-right: 5px;
  font-size: 8px;
  word-break: break-word;
  text-wrap: none;
  line-height: 8px;
  height: fit-content;
  max-width: 100px;
  white-space: normal;

  &.branch {
    background: $TAG_BG_BRANCH;
    color: $TAG_COLOR_BRANCH;
    border-color: $TAG_COLOR_BRANCH;
  }

  &.pr {
    color: $TAG_COLOR_PR;
    border-color: $TAG_COLOR_PR;
    background: $TAG_BG_PR;
    font-weight: bold;
  }

  &.deploy,
  &.deployment {
    color: $TAG_COLOR_DEPLOY;
    border-color: $TAG_COLOR_DEPLOY;
    background: $TAG_BG_DEPLOY;
    font-weight: bold;
  }

  &.morning {
    color: $TAG_COLOR_MORNING;
    border-color: $TAG_COLOR_MORNING;
    background: $TAG_BG_MORNING;
    font-weight: bold;
  }
}
