import { Spinner } from "nr1";
import React from "react";

import { DAO } from "../DAO";
import {
  Facet,
  facet,
  fillEdm,
  shallowArrayEquals,
  TestResultInstanceDataPoint,
} from "../utils";
import { E2EQuery } from "./E2EQuery";
import { PopoverDetails } from "./PopoverDetails";

const HIGH_FAILURE_THRESHOLD = 10;

type SpecOverviewProps = {
  since: string;
  envs: readonly string[];
  tags: readonly string[];
};

type SpecOverviewState = {
  data: TestResultInstanceDataPoint[];
  pending: boolean;
};

export default function SpecOverview(props: SpecOverviewProps) {
  return (
    <div className="SpecOverview">
      <SpecOverviewHeader />
      <SpecOverviewBody
        since={props.since}
        envs={props.envs}
        tags={props.tags}
      />
    </div>
  );
}

function SpecOverviewHeader() {
  return <h2>Spec Overview</h2>;
}

class SpecOverviewBody extends React.Component<
  SpecOverviewProps,
  SpecOverviewState
> {
  constructor(props: SpecOverviewProps) {
    super(props);
    this.state = { data: [], pending: true };
  }

  override componentDidUpdate(prevProps: SpecOverviewProps) {
    if (
      !shallowArrayEquals(this.props.tags, prevProps.tags) ||
      !shallowArrayEquals(this.props.envs, prevProps.envs) ||
      prevProps.since !== this.props.since
    ) {
      this.setState({ pending: true }, () => this.fetchData());
    }
  }

  override componentDidMount() {
    this.fetchData();
  }

  async fetchData() {
    // Manually triggered single integration tests are reported as having no tag/tagGroup.
    const where = `tagGroup IN ('${this.props.tags.join("','")}','')`;
    const results = await Promise.all(
      this.props.envs.map((env) =>
        E2EQuery.query(
          DAO.SpecReport()
            .where(where + ` AND environment = '${env}'`)
            .since(this.props.since)
        )
      )
    );
    this.setState({ data: results.flat(), pending: false });
  }

  override render() {
    if (this.state.pending) {
      return <Spinner />;
    }

    const [highFailureEnvironments, filteredTestResults] =
      filterHighFailureEnvironments(this.state.data);
    const dataByGroup = facet(filteredTestResults, "specGroup").sort(
      specGroupCompare
    );
    return (
      <>
        {dataByGroup.map(({ key: { specGroup }, facets }) => {
          return (
            <FailedGroup key={specGroup} group={specGroup} data={facets} />
          );
        })}
        <br />
        <HighFailureEnvironments envs={highFailureEnvironments} />
      </>
    );
  }
}

function specGroupCompare(
  a: Facet<TestResultInstanceDataPoint, ["specGroup"]>,
  b: Facet<TestResultInstanceDataPoint, ["specGroup"]>
) {
  function value(group: string): number {
    switch (group) {
      case "Deploy":
        return 0;
      case "Morning":
        return 1;
      case "Integration":
        return 2;
      case "Unstable":
        return 3;
      default:
        return 4;
    }
  }

  return value(a.key.specGroup) - value(b.key.specGroup);
}

type FailedGroupProps = { group: string; data: TestResultInstanceDataPoint[] };

function FailedGroup(props: FailedGroupProps) {
  const failures = facet(props.data, "file");
  return (
    <>
      <br />
      <b className="failureLabel">{props.group}</b>
      {failures.map(({ key: { file }, facets }) => (
        <FailedSpec key={file} file={file} facets={facets} />
      ))}
    </>
  );
}

function FailedSpec(props: {
  file: string;
  facets: TestResultInstanceDataPoint[];
}) {
  // Attempt to shorten filename by trimming leading path.
  const { facets } = props;
  let { file } = props;
  const path = file.split("/");
  file = path[path.length - 1] || file;
  return (
    <ul className="failureLabel">
      <li>{file}</li>
      <ul>
        {facets.map((facet) => {
          const { environment, runUrl } = facet;
          const details = fillEdm(facet);
          return (
            <li key={environment}>
              <PopoverDetails openOnHover={true}>
                <a target="_blank" href={runUrl}>
                  {environment}
                </a>
                <pre>
                  <span className="edmDetails">{details}</span>
                </pre>
              </PopoverDetails>
            </li>
          );
        })}
      </ul>
    </ul>
  );
}

function HighFailureEnvironments(props: { envs: string[] }) {
  if (props.envs.length > 0) {
    return (
      <>
        <h3>Environments Needing Further Investigation</h3>
        <ul>
          {props.envs.map((env) => (
            <li key={env} className="failureLabel">
              {env}
            </li>
          ))}
        </ul>
      </>
    );
  }
  return <></>;
}

function filterHighFailureEnvironments(
  data: TestResultInstanceDataPoint[]
): [string[], TestResultInstanceDataPoint[]] {
  // We only care about failures in the deploy specGroup.
  const deployData = data.filter(({ specGroup }) => specGroup === "Deploy");

  const perEnvironmentFailureCounter: Record<string, number> = {};

  deployData.forEach(({ environment }) => {
    perEnvironmentFailureCounter[environment] =
      (perEnvironmentFailureCounter[environment] || 0) + 1;
  });

  const highFailureEnvironments: string[] = [];

  for (const [environment, failures] of Object.entries(
    perEnvironmentFailureCounter
  )) {
    if (failures > HIGH_FAILURE_THRESHOLD) {
      highFailureEnvironments.push(environment);
    }
  }

  const filteredResults = data.filter(
    ({ environment }) => !highFailureEnvironments.includes(environment)
  );

  return [highFailureEnvironments, filteredResults];
}
