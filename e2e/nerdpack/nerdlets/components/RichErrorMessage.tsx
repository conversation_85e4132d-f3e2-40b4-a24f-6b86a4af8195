import ansiRegex from "ansi-regex";
import React, { ReactNode } from "react";

const httpUrlRegex = new RegExp(
  /(https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b[-a-zA-Z0-9()@:%_\\+.~#?&//=]*)/
);

const ansiRegexCompiled = ansiRegex();

export default function RichErrorMessage({
  children,
}: {
  children: ReactNode;
}) {
  return (
    <>
      {React.Children.map(children, (errorMessage: unknown) => {
        if (typeof errorMessage !== "string") {
          return errorMessage;
        }
        return errorMessage.split(httpUrlRegex).map((substring) => {
          if (httpUrlRegex.test(substring)) {
            return (
              <a
                href={encodeURI(substring)}
                target="_blank"
                rel="noopener noreferrer"
              >
                {substring}
              </a>
            );
          }
          return substring
            ? substring.replace(ansiRegexCompiled, "")
            : substring;
        });
      })}
    </>
  );
}
