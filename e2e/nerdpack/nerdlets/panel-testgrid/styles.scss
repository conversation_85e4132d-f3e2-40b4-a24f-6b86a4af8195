@import "../scss/index";

@mixin tryMessage($color, $bg) {
  & {
    td {
      border-color: $bg;
      background-color: $color;
    }

    .tryNumber,
    .status {
      background-color: $bg;
      color: $color;
    }
  }
}

.colorKeyContainer {
  display: flex;
  align-items: center;
  justify-content: left;
  background-color: aliceblue;
  padding: 15px 25px;
  gap: 10px;

  .colorKey {
    align-items: center;
    display: flex;
    justify-content: space-around;
    gap: 3px;

    .colorBox {
      display: flex;
      align-items: center;
      height: 1px;
      width: 1px;
      font-weight: 700;
      border-radius: 75px;
      padding: 5px;
    }

    .colorFlake {
      background-color: $COLOR_FLAKE;
    }

    .colorNA {
      background-color: $COLOR_NA;
    }

    .colorFail {
      background-color: $COLOR_FAIL;
    }

    .colorSkip {
      background-color: $COLOR_WARN;
    }

    .colorPass {
      background-color: $COLOR_PASS;
    }

    .colorPartialPass {
      background-color: $COLOR_PARTIAL_PASS;
    }
  }
}

.testgrid {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: 12px;

  &-wrapper {
    height: 100px;
    overflow: auto;
    flex: 1;
  }

  &-table {
    &__file-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    td {
      height: auto;
    }

    min-width: 900px;

    td {
      padding: 0 10px;
    }

    .clickable {
      cursor: pointer;
    }

    .colProgress {
      min-width: 150px;
    }

    thead th {
      font-size: 14px;
      font-weight: bold;
      border-bottom: 1px #ddd solid;

      .TestGridHeader {
        .runNumber {
          display: flex;
        }

        .tag {
          margin-left: 5px;
        }

        .date {
          display: block;
          font-size: 10px;
          font-weight: normal;
        }
      }
    }

    tbody {
      tr.highlight {
        cursor: pointer;
        &:hover {
          td {
            background: #eee;
          }
        }
      }

      .filename {
        border-left: 5px solid;

        &__content {
          display: flex;
          align-items: center;
          gap: 4px;
        }

        &.na {
          border-left-color: $COLOR_NA;
        }

        &.fail {
          border-left-color: $COLOR_FAIL;
        }

        &.skip {
          border-left-color: $COLOR_WARN;
        }

        &.flake {
          border-left-color: $COLOR_FLAKE;
        }

        &.pass {
          border-left-color: $COLOR_PASS;
        }

        text-wrap: none;
        white-space: nowrap;
        word-break: keep-all;
      }

      td.subTable {
        padding: 0;
      }
    }

    table.runDetails {
      width: 100%;
      border-bottom: 2px #ccc solid;
      margin-bottom: 5px;

      thead th {
        border-bottom: 10px solid;

        &.fail {
          border-bottom-color: $COLOR_FAIL;
        }

        &.skip {
          border-bottom-color: $COLOR_WARN;
        }

        &.flake {
          border-bottom-color: $COLOR_FLAKE;
        }

        &.pass {
          border-bottom-color: $COLOR_PASS;
        }
      }

      td {
        vertical-align: top;
        padding: 3px;
        background: #fff;

        &.testCaseName {
          position: relative;
          vertical-align: middle;
          padding: 5px 0;
          background: #f7f7f7;
        }
      }

      td.TestGridRowDetails {
        padding: 0;
      }

      table.TestGridRowDetails {
        width: 100%;

        tr td {
          padding: 3px;
          border-top: 5px solid #fff;

          &.tryNumber {
            width: 20px;
            padding: 10px;
            font-weight: bold;
            font-size: 12px;
          }
        }

        .try {
          .status {
            vertical-align: middle;
            width: 15px;

            span {
              font-weight: bold;
              -ms-writing-mode: tb-rl;
              -webkit-writing-mode: vertical-rl;
              writing-mode: vertical-rl;
              transform: rotate(180deg);
              white-space: nowrap;
              text-transform: uppercase;
            }

            &.fail {
              border-top-color: $COLOR_FAIL;
              background-color: $COLOR_FAIL;
              color: #fff;
              width: 20px;
            }
          }
        }

        .try_1 {
          @include tryMessage($TRY1_COLOR, $TRY1_BG);
        }

        .pass.try_1 {
          @include tryMessage($COLOR_PASS_LITE, $COLOR_PASS);
        }

        .try_2 {
          @include tryMessage($TRY2_COLOR, $TRY2_BG);
        }
      }
    }
  }
}
