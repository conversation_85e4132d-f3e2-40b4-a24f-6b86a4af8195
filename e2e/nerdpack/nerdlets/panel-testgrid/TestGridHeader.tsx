import { format } from "date-fns";
import { AdditionalInfoLink, Tooltip } from "nr1";
import React from "react";

import { Tags, TagsProps } from "../components/Tags";
import { labelEnv, msToTime } from "../utils";

export interface TestGridHeaderProps {
  readonly computeTimeMs: number;
  readonly dashboardUrl?: string | undefined;
  readonly endTime: Date;
  readonly environment: string;
  readonly runId: string;
  readonly runNumber: number;
  readonly showDate?: boolean | undefined;
  readonly startTime: Date;
  readonly tags: TagsProps["tags"];
}

export default function TestGridHeader(props: TestGridHeaderProps) {
  const additionalInfoLink = getAdditionalInfoLink(props.dashboardUrl);
  const formattedStartTime = formatDateForDisplay(props.startTime);
  const formattedEndTime = formatDateForDisplay(props.endTime);
  const formattedComputeTime = msToTime(props.computeTimeMs);

  const toolTipText = `${formattedStartTime} - ${formattedEndTime}

    Run ID: ${props.runId}
    Environment: ${labelEnv(props.environment)}
    Total Compute Time: ${formattedComputeTime}
  `;

  return (
    <Tooltip
      additionalInfoLink={additionalInfoLink}
      placementType={Tooltip.PLACEMENT_TYPE.BOTTOM}
      text={toolTipText}
    >
      <span className="TestGridHeader">
        <span className="runNumber">
          {props.runNumber}
          <Tags tags={props.tags} />
        </span>
        {props.showDate && <span className="date">{formattedStartTime}</span>}
      </span>
    </Tooltip>
  );
}

function formatDateForDisplay(date: Date) {
  return format(date, "PPp");
}

function getAdditionalInfoLink(
  url: TestGridHeaderProps["dashboardUrl"]
): AdditionalInfoLink | undefined {
  if (url === undefined) {
    return undefined;
  }

  return {
    label: getUrlLabel(url),
    to: url,
  };
}

function getUrlLabel(url: string) {
  if (url.includes("cypress")) {
    return "View in Cypress";
  }

  if (url.includes("github")) {
    return "View in GitHub Actions";
  }

  return "View in Dashboard";
}
