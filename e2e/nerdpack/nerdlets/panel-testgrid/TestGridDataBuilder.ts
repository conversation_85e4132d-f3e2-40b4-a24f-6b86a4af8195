import { Mutable } from "../utils";
import { TestGridRowProps } from "./TestGridRow";

/**
 * # Example
 * ```typescript
 * {
 *   anonymizedMessage: "`cy.task('submitClaimToAPI')` failed with the following error:\n\n",
 *   blockTitle: "Given a fully approved claim",
 *   branch: "main",
 *   category: "infrastructure",
 *   duration: 36343,
 *   environment: "breakfix",
 *   fail: true,
 *   file: "cypress/specs/stable/feature/api_caring_continuous_approval_extensionNotification+O_RButtons.ts",
 *   group: "Deploy",
 *   pass: false,
 *   runId: "EOLWD/pfml-1898273937-1",
 *   runUrl: "https://dashboard.cypress.io/projects/wjoxhr/runs/10752",
 *   schemaVersion: 0.2,
 *   skip: false,
 *   status: "failed",
 *   subCategory: "failure-500",
 *   tag: "Morning Run,Env-breakfix",
 *   timestamp: 1645787200206,
 *   tryNumber: 2,
 * }
 * ```
 */
export interface TestResultInstance {
  readonly anonymizedMessage: string;
  readonly blockTitle: string;
  readonly branch: string;
  readonly category?: string;
  readonly duration: number;
  readonly environment: string;
  readonly fail: boolean;
  readonly file: string;
  readonly group: string;
  readonly specGroup: string;
  readonly pass: boolean;
  readonly runId: string;
  readonly runUrl: string;
  readonly schemaVersion: number;
  readonly skip: boolean;
  readonly status: string;
  readonly subCategory?: string;
  readonly tag: string;
  readonly tagGroup: string;
  readonly timestamp: number;
  readonly tryNumber: number;
}

export interface ExpectedTestsPerRun {
  readonly file: string;
  readonly runId: string;
  readonly schemaVersion: number;
  readonly timestamp: number;
  readonly suites: string;
  readonly tests: string;
}

type TestGroup = Record<string, TestFile>;
type TestFile = TestGridRowProps["testFile"];
type TestCase = TestFile["testCases"][string];
type TestCaseRun = TestCase["runs"][number];
type TestSuiteRun = TestGridRowProps["testSuiteRuns"][number];

export default class TestGridDataBuilder {
  private isUsed = false;
  private readonly groups: Record<string, Mutable<TestGroup>> = {};
  private readonly seenTestCaseRuns = new Set<TestCaseRun>();
  private readonly testCaseRunTries = new Map<TestCaseRun, number>();
  private readonly testResultInstances: readonly TestResultInstance[];
  private readonly testSuiteRuns: Mutable<TestSuiteRun[]> = [];

  constructor(testResultInstances: readonly TestResultInstance[]) {
    this.testResultInstances = testResultInstances;
  }

  build() {
    if (this.isUsed) {
      throw new Error("TestGridDataBuilder instance can only be used once.");
    }

    this.isUsed = true;

    this.testResultInstances.forEach((instance) =>
      this.processTestResultInstance(instance)
    );

    this.testSuiteRuns.forEach((run) =>
      this.resolveTestFileStatusesForRun(run)
    );

    this.testSuiteRuns.sort(compareTestSuiteRunsByStartTimeDescending);

    return {
      groups: this.groups as Record<string, TestGroup>,
      testSuiteRuns: this.testSuiteRuns as TestSuiteRun[],
    };
  }

  private processTestResultInstance(testResultInstance: TestResultInstance) {
    const fileName = testResultInstance.file.replace("cypress/specs/", "");

    this.updateTestSuiteRun(testResultInstance, testResultInstance.specGroup);
    this.updateTestFileAndCaseRun(
      testResultInstance,
      testResultInstance.specGroup,
      fileName
    );
  }

  private updateTestSuiteRun(
    testResultInstance: TestResultInstance,
    groupName: string
  ) {
    const run = this.getTestSuiteRun(testResultInstance);
    run.computeTimeMs += testResultInstance.duration ?? 0;
    run.dashboardUrls[groupName] = testResultInstance.runUrl;

    if (testResultInstance.tag) {
      run.tags = testResultInstance.tag
        .split(",")
        .filter((tag) => !tag.includes("Env-"));
    }
  }

  private getTestSuiteRun(testResultInstance: TestResultInstance) {
    let run = this.testSuiteRuns.find(
      (run) => run.id === testResultInstance.runId
    );

    const time = new Date(testResultInstance.timestamp);

    if (run === undefined) {
      run = createTestSuiteRun(testResultInstance, time);
      this.testSuiteRuns.push(run);
    } else {
      updateTestSuiteRunTime(run, time);
    }

    return run;
  }

  private updateTestFileAndCaseRun(
    testResultInstance: TestResultInstance,
    groupName: string,
    fileName: string
  ) {
    const testFile = this.getTestFile(groupName, fileName);

    const testCaseRun = getTestCaseRun(
      testFile,
      testResultInstance.blockTitle,
      testResultInstance.runId
    );

    this.updateTestCaseRun(testCaseRun, testResultInstance);
    this.updateTestFileRun(testFile, testCaseRun, testResultInstance);
  }

  private getTestFile(groupName: string, fileName: string) {
    const group = this.getGroup(groupName);
    let testFile = group[fileName];

    if (testFile === undefined) {
      testFile = { runs: {}, testCases: {} };
      group[fileName] = testFile;
    }

    return testFile;
  }

  private getGroup(groupName: string) {
    let group = this.groups[groupName];

    if (group === undefined) {
      group = {};
      this.groups[groupName] = group;
    }

    return group;
  }

  private updateTestCaseRun(
    testCaseRun: Mutable<TestCaseRun>,
    testResultInstance: TestResultInstance
  ) {
    if (testResultInstance.anonymizedMessage) {
      testCaseRun.messages.push(createErrorMessage(testResultInstance));
    }

    const triesForTestCase = this.testCaseRunTries.get(testCaseRun);

    if (
      triesForTestCase === undefined ||
      triesForTestCase < testResultInstance.tryNumber
    ) {
      this.testCaseRunTries.set(testCaseRun, testResultInstance.tryNumber);
      testCaseRun.status = getTestCaseRunStatus(testResultInstance);
    }
  }

  private updateTestFileRun(
    testFile: Mutable<TestFile>,
    testCaseRun: TestCaseRun,
    testResultInstance: TestResultInstance
  ) {
    const testFileRun = getTestFileRun(testFile, testResultInstance.runId);
    ++testFileRun.tryCount;

    if (testResultInstance.fail) {
      ++testFileRun.fail;
    }

    if (testResultInstance.pass) {
      ++testFileRun.pass;
    }

    if (testResultInstance.skip) {
      ++testFileRun.skip;
    }

    if (!this.seenTestCaseRuns.has(testCaseRun)) {
      ++testFileRun.testCaseCount;
      this.seenTestCaseRuns.add(testCaseRun);
    }
  }

  private resolveTestFileStatusesForRun(run: Mutable<TestSuiteRun>) {
    Object.values(this.groups).forEach((group) =>
      Object.values(group).forEach((testFile) =>
        resolveTestFileStatus(testFile, run.id)
      )
    );
  }
}

function createTestSuiteRun(
  testResultInstance: TestResultInstance,
  time: Date
) {
  return {
    computeTimeMs: 0,
    dashboardUrls: {},
    endTime: time,
    environment: testResultInstance.environment,
    id: testResultInstance.runId,
    startTime: time,
    tags: [],
  };
}

function updateTestSuiteRunTime(run: Mutable<TestSuiteRun>, time: Date) {
  if (run.startTime > time) {
    run.startTime = time;
  }

  if (run.endTime < time) {
    run.endTime = time;
  }
}

function getTestCaseRun(
  testFile: Mutable<TestFile>,
  testCaseName: string,
  runId: string
) {
  const testCase = getTestCase(testFile, testCaseName);
  let testCaseRun = testCase.runs[runId];

  if (testCaseRun === undefined) {
    testCaseRun = { messages: [], status: null };
    testCase.runs[runId] = testCaseRun;
  }

  return testCaseRun;
}

function getTestCase(testFile: Mutable<TestFile>, testCaseName: string) {
  let testCase = testFile.testCases[testCaseName];

  if (testCase === undefined) {
    testCase = { order: Object.keys(testFile.testCases).length, runs: {} };
    testFile.testCases[testCaseName] = testCase;
  }

  return testCase;
}

function createErrorMessage(testResultInstance: TestResultInstance) {
  return {
    category: testResultInstance.category ?? "",
    subCategory: testResultInstance.subCategory ?? "",
    text: testResultInstance.anonymizedMessage,
    tryNumber: testResultInstance.tryNumber,
  };
}

function getTestCaseRunStatus(testResultInstance: TestResultInstance) {
  if (testResultInstance.pass) {
    return "pass";
  }

  if (testResultInstance.fail) {
    return "fail";
  }

  if (testResultInstance.skip) {
    return "skip";
  }

  return null;
}

function getTestFileRun(testFile: Mutable<TestFile>, runId: string) {
  let testFileRun = testFile.runs[runId];

  if (testFileRun === undefined) {
    testFileRun = createTestFileRun();
    testFile.runs[runId] = testFileRun;
  }

  return testFileRun;
}

function createTestFileRun() {
  return {
    fail: 0,
    pass: 0,
    percent: 0,
    skip: 0,
    status: null,
    testCaseCount: 0,
    tryCount: 0,
  };
}

function resolveTestFileStatus(
  testFile: Mutable<TestGridRowProps["testFile"]>,
  testSuiteRunId: string
) {
  const testFileRun = testFile.runs[testSuiteRunId];

  if (testFileRun === undefined) {
    return;
  }

  testFileRun.percent = calculatePercent(testFile.runs, testSuiteRunId);
  testFileRun.status = resolveStatus(testFile.testCases, testSuiteRunId);
}

/**
 * Calculates the percentage of passing test cases in a run of a test suite
 * against the largest set of test cases among all runs of the suite. A rerun
 * may unexpectedly stop running. This results in the rerun having a smaller
 * set of test cases than the original run and an inaccurate measure of
 * success. Comparing across all runs guarantees that each run is held to the
 * same standard.
 */
function calculatePercent(
  testFileRuns: TestFile["runs"],
  testSuiteRunId: string
) {
  const testCaseCountForAllRuns = Object.values(testFileRuns).map(
    (run) => run.testCaseCount
  );

  const maxTestCaseCount = Math.max(...testCaseCountForAllRuns);
  const testFileRun = testFileRuns[testSuiteRunId];

  if (testFileRun === undefined || maxTestCaseCount === 0) {
    return 0;
  }

  return Math.round((testFileRun.pass / maxTestCaseCount) * 100);
}

function resolveStatus(
  testCases: Mutable<TestGridRowProps["testFile"]["testCases"]>,
  testSuiteRunId: string
) {
  return Object.values(testCases)
    .map((testCase) => testCase.runs[testSuiteRunId]?.status ?? null)
    .reduce((testFileRunStatus, testCaseRunStatus) => {
      if (testCaseRunStatus === "fail") {
        return testCaseRunStatus;
      }

      if (testFileRunStatus === "skip" && testCaseRunStatus === "pass") {
        return testCaseRunStatus;
      }

      return testFileRunStatus;
    });
}

function compareTestSuiteRunsByStartTimeDescending(
  runA: Mutable<TestSuiteRun>,
  runB: Mutable<TestSuiteRun>
) {
  return +runB.startTime - +runA.startTime;
}
