@import "../scss/variables";

html,
body,
#root {
  height: 100%;
  margin: 0;
  width: 100%;
  bottom: 0;
  left: 0;
  overflow: auto;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  padding: 20px;
  gap: 20px;
}

a {
  color: $LINK_COLOR;

  &:hover {
    color: $LINK_COLOR_HOVER;
  }
}

.closed {
  display: none;
}

.pill {
  padding: 2px 6px;
  border-radius: 5px;
  border: 1px $PILL_DEFAULT_COLOR solid;
  font-weight: bold;
  font-size: 9px;
  margin-right: 5px;
  background: $PILL_DEFAULT_FILL;
  color: $PILL_DEFAULT_COLOR;

  &.PASS {
    background: $COLOR_PASS_LITE;
    color: $COLOR_PASS;
    border-color: $COLOR_PASS;
  }

  &.SKIPPED,
  &.SKIP {
    background: $COLOR_WARN;
    color: #000;
  }

  &.EDM {
    background: #ffeffd;
    color: #ff00e0;
    border-color: #ff00e0;
  }

  &.HIGH,
  &.FAIL,
  &.connection {
    background: $COLOR_FAIL_LITE;
    color: $COLOR_FAIL;
    border-color: $COLOR_FAIL;
  }

  &.MEDIUM {
    background: $COLOR_WARN_MEDIUM_LITE;
    color: $COLOR_WARN_MEDIUM;
    border-color: $COLOR_WARN_MEDIUM;
  }

  &.LOW {
    background: $COLOR_WARN_LOW_LITE;
    color: $COLOR_WARN_LOW;
    border-color: $COLOR_WARN_LOW;
  }
}

.runProgress {
  background: $PROGRESS_DEFAULT_FILL;
  width: 100%;
  position: relative;
  border-radius: 15px;
  height: 30px;
  min-width: 50px;
  margin: 3px;

  &.fail {
    background: $PROGRESS_ERROR_FILL;
  }

  .progress {
    left: 0;
    position: absolute;
    height: 30px;
    border-radius: 15px;
    text-align: center;
    color: #000 !important;
    line-height: 30px !important;
    font-weight: bold;
    user-select: none;

    &.fail {
      @include COLOR_FILTER();
      background: $COLOR_FAIL;
    }

    &.skip,
    &.warning {
      background: $COLOR_WARN;
    }

    &.flake {
      @include COLOR_FILTER();
      background: $COLOR_FLAKE;
    }

    &.pass {
      @include COLOR_FILTER();
      background: $COLOR_PASS;
    }

    &.partialPass {
      @include COLOR_FILTER();
      background: $COLOR_PARTIAL_PASS;
    }
  }
}

[role="tooltip"] {
  color: #fff;
}

details summary {
  cursor: pointer;

  span {
    padding-left: 6px;
  }
}
