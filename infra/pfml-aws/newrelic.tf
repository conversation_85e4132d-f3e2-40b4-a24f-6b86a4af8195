data "aws_ssm_parameter" "newrelic_license_key" {
  name = "/service/pfml-api/common/newrelic-license-key"
}

data "aws_sns_topic" "lambda_failures" {
  name = module.constants.infra_low_priority_cloudwatch_alerts
}

module "newrelic_log_ingestion" {
  source = "github.com/newrelic/aws-log-ingestion"

  nr_license_key     = data.aws_ssm_parameter.newrelic_license_key.value
  nr_logging_enabled = true
  nr_infra_logging   = true
  nr_tags            = ""
  memory_size        = 128
  timeout            = 30
}

resource "aws_cloudwatch_metric_alarm" "error" {
  alarm_name          = module.constants.newrelic_log_ingestion_function_name
  alarm_actions       = [data.aws_sns_topic.lambda_failures.arn]
  alarm_description   = "Lambda function '${module.constants.newrelic_log_ingestion_function_name}' received an error. View Logs: https://us-east-1.console.aws.amazon.com/cloudwatch/home?region=us-east-1#logsV2:log-groups/log-group/$252Faws$252Flambda$252F${module.constants.newrelic_log_ingestion_function_name}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "60"
  metric_name         = "Errors"
  namespace           = "AWS/Lambda"
  period              = 60
  statistic           = "Sum"
  threshold           = "50"
  treat_missing_data  = "notBreaching"
  datapoints_to_alarm = 60

  dimensions = {
    FunctionName = module.constants.newrelic_log_ingestion_function_name
  }
}

#----------------------------------------------------------------------------------------------------------------------------
# These EC2 instances host New Relic agents for monitoring RDS instance performance data.
# - performance: i-0624c72103a380d7c (massgov-pfml-api-performance-newrelic-rds-agent)
# - prod       : i-0e3707a46dd667b2e (massgov-pfml-api-prod-newrelic-rds-agent)
#
# CAMS manages the patching, updating, backing up, and monitoring of these instances.
# CAMS POCs: John Quarles and Leesa Smith
#----------------------------------------------------------------------------------------------------------------------------

locals {
  newrelic_agent_environments = toset(["performance", "prod"])
}

resource "aws_instance" "newrelic_rds_agent" {
  for_each                    = local.newrelic_agent_environments
  ami                         = data.aws_ami.amazon_linux_2023.id
  instance_type               = "t3.medium"
  subnet_id                   = each.key == "prod" ? data.aws_subnets.app_private["prod"].ids[0] : data.aws_subnets.app_private["nonprod"].ids[1]
  iam_instance_profile        = aws_iam_instance_profile.ec2_newrelic_rds_agent_role[each.key].name
  vpc_security_group_ids      = [aws_security_group.ec2_newrelic_rds_agent_sg[each.key].id]
  associate_public_ip_address = false
  enable_primary_ipv6         = false
  ebs_optimized               = true
  key_name                    = data.aws_key_pair.ec2[each.key].key_name
  disable_api_termination     = true
  metadata_options {
    http_endpoint = "enabled"
    http_tokens   = "required"
  }
  tags = {
    Name         = "massgov-pfml-api-${each.key}-newrelic-rds-agent"
    environment  = module.constants.environment_tags[each.key]
    "SMX:Asset"  = "v1:massgov-pfml-api-${each.key}-newrelic-rds-agent:${module.constants.smartronix_environment_tags[each.key]}:EC2:PFML:Advanced:None"
    contact      = "<EMAIL>, <EMAIL>"
    "smx:backup" = each.key == "prod" ? "true-prod" : "true-nonprod"
  }
  lifecycle {
    ignore_changes = [
      ami
    ]
  }
}

# Latest Amazon Linux 2023 AMI
data "aws_ami" "amazon_linux_2023" {
  most_recent = true
  owners      = ["amazon"]

  filter {
    name   = "name"
    values = ["al2023-ami-2023*-x86_64"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}

data "aws_key_pair" "ec2" {
  for_each = local.newrelic_agent_environments
  key_name = each.key == "prod" ? "massgov-pfml-newrelic-postgres-agent-${each.key}" : "massgov-pfml-newrelic-postgres-agent-poc"
}

resource "aws_iam_instance_profile" "ec2_newrelic_rds_agent_role" {
  for_each = local.newrelic_agent_environments
  name     = "massgov-pfml-api-${each.key}-newrelic-rds-agent"
  role     = aws_iam_role.ec2_newrelic_rds_agent_role[each.key].name
}

resource "aws_iam_role" "ec2_newrelic_rds_agent_role" {
  for_each           = local.newrelic_agent_environments
  name               = "massgov-pfml-api-${each.key}-newrelic-rds-agent-role"
  assume_role_policy = data.aws_iam_policy_document.ec2_assume_role_policy.json
  tags = {
    environment = module.constants.environment_tags[each.key]
  }
}

resource "aws_iam_role_policy_attachments_exclusive" "ec2_kms_policy_attachment" {
  for_each  = local.newrelic_agent_environments
  role_name = aws_iam_role.ec2_newrelic_rds_agent_role[each.key].name
  policy_arns = [
    data.aws_iam_policy.CloudWatchAgentServerPolicy.arn,
    data.aws_iam_policy.CMS-SMX-CWAgentDocRun.arn,
    data.aws_iam_policy.EOTSSEC2PolicyforSSM.arn,
    data.aws_iam_policy.smx-cms-access-policy.arn,
    aws_iam_policy.ec2_kms_policy[each.key].arn,
    "arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore"
  ]
}

data "aws_iam_policy_document" "ec2_assume_role_policy" {
  statement {
    sid     = "AllowEC2AssumeRole"
    effect  = "Allow"
    actions = ["sts:AssumeRole"]
    principals {
      type        = "Service"
      identifiers = ["ec2.amazonaws.com"]
    }
  }
}

# Below policies are needed by CAMS and EOTSS.
data "aws_iam_policy" "CloudWatchAgentServerPolicy" {
  name = "CloudWatchAgentServerPolicy"
}
data "aws_iam_policy" "CMS-SMX-CWAgentDocRun" {
  name = "CMS-SMX-CWAgentDocRun"
}
data "aws_iam_policy" "smx-cms-access-policy" {
  name = "smx-cms-access-policy"
}
data "aws_iam_policy" "EOTSSEC2PolicyforSSM" {
  name = "EOTSSEC2PolicyforSSM"
}

resource "aws_iam_policy" "ec2_kms_policy" {
  for_each = local.newrelic_agent_environments
  name     = "massgov-pfml-api-${each.key}-newrelic-rds-agent-kms-policy"
  policy   = data.aws_iam_policy_document.ec2_kms_policy_document[each.key].json
  tags = {
    environment = module.constants.environment_tags[each.key]
  }
}

# If a new instance is created, a new KMS key will need to be created via the EC2 instance. Temporarily update this statement to "kms:*" for "*" resources.
# Once the key is created, you can change the policy back to it's least privilege state.
data "aws_iam_policy_document" "ec2_kms_policy_document" {
  for_each = local.newrelic_agent_environments
  statement {
    sid    = "AllowKMS"
    effect = "Allow"
    actions = [
      "kms:DescribeKey",
      "kms:Encrypt",
      "kms:Decrypt",
      "kms:ReEncrypt*",
      "kms:GenerateDataKey*",
    ]
    resources = [data.aws_kms_alias.ec2_newrelic_rds_agent_kms_key[each.key].target_key_arn]
  }
}

# KMS keys created for encrypting the RDS telemetry data sent to New Relic
# The keys are created programatically on the EC2 instance. So if a new env is added to the newrelic_agent_environments local var list, you'll have to temporarily
# comment out this data block as the new env's key won't exist yet. Once the key exists, you can uncomment this block.
data "aws_kms_alias" "ec2_newrelic_rds_agent_kms_key" {
  for_each = local.newrelic_agent_environments
  name     = "alias/massgov-pfml-api-${each.key}-newrelic-rds-agent"
}

data "aws_security_group" "rds" {
  for_each = local.newrelic_agent_environments
  name     = "pfml-api-rds-${each.key}"
}

# Security Group for New Relic Agent EC2 instance
resource "aws_security_group" "ec2_newrelic_rds_agent_sg" {
  for_each = local.newrelic_agent_environments
  name     = "massgov-pfml-api-${each.key}-newrelic-rds-agent"
  vpc_id   = each.key == "prod" ? data.aws_vpc.vpcs["prod"].id : data.aws_vpc.vpcs["nonprod"].id
  ingress {
    description = "Allow all inbound from CAMS"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["***********/32"]
  }
  egress {
    description = "Allow all 443 outbound"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  egress {
    description = "Allow all 80 outbound"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  egress {
    description     = "PostgreSQL to ${each.key} RDS"
    from_port       = 5432
    to_port         = 5432
    protocol        = "tcp"
    security_groups = [data.aws_security_group.rds[each.key].id]
  }
  egress {
    description     = "TCP DNS out to govt domain controllers"
    from_port       = 53
    to_port         = 53
    protocol        = "tcp"
    prefix_list_ids = ["pl-08da094e006b16321"]
  }
  egress {
    description     = "UDP DNS out to govt domain controllers"
    from_port       = 53
    to_port         = 53
    protocol        = "udp"
    prefix_list_ids = ["pl-08da094e006b16321"]
  }
  egress {
    description = "Required by CAMS"
    from_port   = 4119
    to_port     = 4119
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  egress {
    description = "Required by CAMS"
    from_port   = 8080
    to_port     = 8080
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
  egress {
    description = "Required by CAMS"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["***********/32"]
  }
  tags = {
    environment = module.constants.environment_tags[each.key]
  }
}

# Security Group Rule for prod and performance RDS instances to allow ingress from New Relic Agent EC2 instances
resource "aws_security_group_rule" "rds_postgresql_ingress_from_ec2" {
  for_each                 = local.newrelic_agent_environments
  type                     = "ingress"
  description              = "PostgreSQL from ${each.key} New Relic Agent EC2"
  from_port                = 5432
  to_port                  = 5432
  protocol                 = "tcp"
  source_security_group_id = aws_security_group.ec2_newrelic_rds_agent_sg[each.key].id
  security_group_id        = data.aws_security_group.rds[each.key].id
}

# S3 bucket for CAMS Tenable scan results. One bucket for both prod and performance.
# CAMS SecOps will handle the cross-account priviliges for scanner to access the bucket.
module "terraform_s3_bucket" {
  source = "../modules/terraform_s3_bucket"

  bucket_name        = "cams-tenable-scans-results"
  bucket_name_prefix = "massgov-pfml"
  environment        = "prod"
  kms_key_arn        = aws_kms_key.main_kms_key.arn
}

