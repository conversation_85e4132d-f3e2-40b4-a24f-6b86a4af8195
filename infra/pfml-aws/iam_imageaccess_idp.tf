# IAM users (prod and nonprod) to act as service accts for ImageAccessCorp interact with S3 as a part of Intelligent Document Processing (IDP)
# ImageAccess POCs: <PERSON> <N<PERSON>ider<PERSON>@imageaccesscorp.com> <PERSON>, <PERSON> (EOL) <<PERSON><PERSON><PERSON><PERSON>@mass.gov>

resource "aws_iam_user" "idp" {
  for_each = toset(local.vpcs)
  name     = "pfml-idp-${each.key}"

  tags = {
    poc         = "<EMAIL>"
    environment = each.key
  }
}

resource "aws_iam_user_policy_attachments_exclusive" "idp" {
  for_each    = toset(local.vpcs)
  user_name   = aws_iam_user.idp[each.key].name
  policy_arns = [aws_iam_policy.idp[each.key].arn]
}

data "aws_iam_policy_document" "idp" {
  for_each = toset(local.vpcs)
  statement {
    sid    = "AllowS3Access"
    effect = "Allow"
    actions = [
      "s3:Get*",
      "s3:PutObject",
      "s3:ListBucket",
      "s3:DeleteObject",
      "s3:AbortMultipartUpload",
    ]
    resources = [
      module.idp_s3_bucket[each.key].s3_bucket_arn,
      "${module.idp_s3_bucket[each.key].s3_bucket_arn}/*"
    ]
  }
}

resource "aws_iam_policy" "idp" {
  for_each = toset(local.vpcs)
  name     = "idp-s3-access-policy-${each.key}"
  policy   = data.aws_iam_policy_document.idp[each.key].json
  tags = {
    environment = each.key
  }
}

module "idp_s3_bucket" {
  for_each = toset(local.vpcs)
  source   = "../modules/terraform_s3_bucket"

  bucket_name        = "idp"
  bucket_name_prefix = "massgov-pfml-${each.key}"
  environment        = each.key
}