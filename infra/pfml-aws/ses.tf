# See /docs/api/ses.tf for full details on configuring SES permissions for applications.
#
locals {
  email_identities = {
    "noreplypfml"            = aws_ses_email_identity.noreply.arn,
    "pfmldonotreply-massgov" = aws_ses_email_identity.pfml_donotreply.arn,
    "pfmldonotreply-statema" = aws_ses_email_identity.pfml_donotreply_state.arn,
  }
}

# Deprecated email identity
resource "aws_ses_email_identity" "noreply" {
  email = "<EMAIL>"
}

# New email domain and identity -- see https://lwd.atlassian.net/browse/PFMLPB-640.
#
resource "aws_ses_domain_identity" "eol" {
  domain = "eol.mass.gov"
}

resource "aws_ses_domain_dkim" "eol" {
  domain = aws_ses_domain_identity.eol.domain
}

resource "aws_ses_email_identity" "pfml_donotreply" {
  email = "<EMAIL>"
}

# Deprecated email alias
resource "aws_ses_domain_identity" "eol_state" {
  domain = "eol.state.ma.us"
}

resource "aws_ses_domain_dkim" "eol_state" {
  domain = aws_ses_domain_identity.eol_state.domain
}

resource "aws_ses_email_identity" "pfml_donotreply_state" {
  email = "<EMAIL>"
}

resource "aws_ses_template" "add_or_remove_leave_admin_template" {
  name    = "ManageLeaveAdminAll"
  subject = "{{subject}}"
  html    = file("../portal/template/emails/manage-leave-admin-all.html")
}

resource "aws_ses_template" "deactivate_leave_admin_template" {
  name    = "ManageLeaveAdminDeactivate"
  subject = "{{subject}}"
  html    = file("../portal/template/emails/manage-leave-admin-deactivate.html")
}

resource "aws_ses_template" "add_leave_admin_template" {
  name    = "ManageLeaveAdminAdd"
  subject = "{{subject}}"
  html    = file("../portal/template/emails/manage-leave-admin-add.html")
}

resource "aws_ses_template" "change_email_leave_admin_all" {
  name    = "ChangeEmailLeaveAdminAll"
  subject = "{{subject}}"
  html    = file("../portal/template/emails/change-email-leave-admin-all.html")
}

resource "aws_ses_template" "payment_audit_file_uploaded" {
  name    = "PaymentAuditFileUploaded"
  subject = "Payment Audit File Upload to S3"
  html    = file("../portal/template/emails/payment-audit-file-uploaded.html")
}

resource "aws_ses_template" "payment_audit_file_available" {
  name    = "PaymentAuditFileAvailable"
  subject = "PFML Audit File Available (EOL-PFML-Payments)"
  html    = file("../portal/template/emails/payment-audit-file-available.html")
}

# Create an IAM policy that only allows payments tasks to send emails from the email address
data "aws_iam_policy_document" "restrict_ses_senders" {
  for_each = local.email_identities

  statement {
    effect = "Deny"
    actions = [
      "ses:SendEmail",
      "ses:SendRawEmail",
      "ses:SendTemplatedEmail"
    ]

    resources = [each.value]

    principals {
      type        = "AWS"
      identifiers = ["*"]
    }

    condition {
      test     = "ArnNotLike"
      variable = "aws:PrincipalArn"
      values = [

        # ECS tasks - API service & batch jobs
        "arn:aws:sts::${data.aws_caller_identity.current.account_id}:assumed-role/pfml-api-*-api-service/*",
        "arn:aws:sts::${data.aws_caller_identity.current.account_id}:assumed-role/pfml-api-*-ecs-tasks-pub-payments-process-fineos/*",
        "arn:aws:sts::${data.aws_caller_identity.current.account_id}:assumed-role/pfml-api-*-pub-payments-create-pub-files/*",
        "arn:aws:sts::${data.aws_caller_identity.current.account_id}:assumed-role/pfml-api-*-pub-payments-process-pub-returns/*",
        "arn:aws:sts::${data.aws_caller_identity.current.account_id}:assumed-role/pfml-api-*-ecs-tasks-reductions-workflow/*",
        "arn:aws:sts::${data.aws_caller_identity.current.account_id}:assumed-role/pfml-api-*-ecs-tasks-dfml-fines-and-repayments/*",
        "arn:aws:sts::${data.aws_caller_identity.current.account_id}:assumed-role/pfml-api-*-ecs-tasks-dor-import-task-role/*",
        "arn:aws:sts::${data.aws_caller_identity.current.account_id}:assumed-role/pfml-api-*-ecs-tasks-process-overpayment-task-role/*",
        "arn:aws:sts::${data.aws_caller_identity.current.account_id}:assumed-role/pfml-api-*-db-user-pfml-migrate-role/*",
        "arn:aws:sts::${data.aws_caller_identity.current.account_id}:assumed-role/ci-run-deploys-oidc/GitHubActions/*",
        "arn:aws:sts::${data.aws_caller_identity.current.account_id}:assumed-role/massgov_pfml_workspaces_usage_report_role/*",

        # Duplicate the roles above but in the normal IAM format.
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/pfml-api-*-api-service",
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/pfml-api-*-ecs-tasks-pub-payments-process-fineos",
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/pfml-api-*-pub-payments-create-pub-files",
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/pfml-api-*-pub-payments-process-pub-returns",
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/pfml-api-*-ecs-tasks-reductions-workflow",
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/pfml-api-*-ecs-tasks-dfml-fines-and-repayments",
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/pfml-api-*-ecs-tasks-dor-import-task-role",
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/pfml-api-*-ecs-tasks-process-overpayment-task-role",
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/pfml-api-*-db-user-pfml-migrate-role",
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/ci-run-deploys-oidc",
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:role/massgov_pfml_workspaces_usage_report_role",

        # Add in existing AWS users with SES in their name, as of April 8, 2021.
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:user/pfml-ses-savilinx-sn-prod-user",
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:user/pfml-ses-third-party-smtp-user",
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:user/pfml-ses-savilinx-sn-test-user",
        "arn:aws:iam::${data.aws_caller_identity.current.account_id}:user/pfml-ses-mailchimp-test-user"
      ]
    }
  }
}

# Set the SES Sending Authorization policy
# See: https://docs.aws.amazon.com/ses/latest/DeveloperGuide/sending-authorization-policies.html
resource "aws_ses_identity_policy" "restrict_ses_senders" {
  for_each = local.email_identities

  identity = each.value
  name     = "massgov-pfml-restrict-ses-send-email-${each.key}"
  policy   = data.aws_iam_policy_document.restrict_ses_senders[each.key].json
}
