module "constants" {
  source = "../../constants"
}

locals {
  function_name = "${module.constants.prefix}${var.function_name}"
  layer_arns = concat(
    ["arn:aws:lambda:${data.aws_region.current.name}:017000801446:layer:AWSLambdaPowertoolsPythonV2:42"],
    var.layer_arns
  )
  cloudwatch_base_url           = "https://us-east-1.console.aws.amazon.com/cloudwatch/home?region=us-east-1#logsV2:log-groups/log-group/$252Faws$252Flambda$252F"
  newrelic_log_ingestion_lambda = data.aws_lambda_function.newrelic_log_ingestion.arn
  lambda_package_directory      = "${var.source_directory}/${var.function_name}"
  lambda_package                = "${var.source_directory}/${var.function_name}/${var.function_name}.zip"
}

data "aws_region" "current" {}
data "aws_caller_identity" "current" {}

data "aws_sns_topic" "lambda_failures" {
  name = module.constants.infra_low_priority_cloudwatch_alerts
}

data "aws_lambda_function" "newrelic_log_ingestion" {
  function_name = module.constants.newrelic_log_ingestion_function_name
}

data "archive_file" "lambda_package" {
  type        = "zip"
  source_dir  = local.lambda_package_directory
  output_path = local.lambda_package
  excludes = [
    local.lambda_package,
    "${var.function_name}.zip",
    ".zip",
    "tests",
    "__pycache__",
    ".venv",
    ".env",
  ]
}

resource "aws_lambda_function" "lambda_function" {
  architectures                  = ["x86_64"]
  description                    = var.description != null ? var.description : var.function_name
  filename                       = data.archive_file.lambda_package.output_path
  function_name                  = local.function_name
  handler                        = "${var.function_name}.handler"
  memory_size                    = var.memory_size
  reserved_concurrent_executions = var.concurrency
  role                           = aws_iam_role.lambda_function_role.arn
  runtime                        = var.runtime == null ? module.constants.python_runtime : var.runtime
  source_code_hash               = data.archive_file.lambda_package.output_base64sha256
  timeout                        = var.timeout
  layers                         = var.include_layers ? local.layer_arns : null
  publish                        = var.publish

  dynamic "vpc_config" {
    for_each = length(var.subnet_ids) > 0 && length(var.security_group_ids) > 0 ? [1] : []

    content {
      subnet_ids         = var.subnet_ids
      security_group_ids = var.security_group_ids
    }
  }

  dynamic "environment" {
    for_each = var.environment_variables[*]

    content {
      variables = environment.value
    }
  }

  depends_on = [
    aws_cloudwatch_log_group.lambda_function_logs
  ]

  provisioner "local-exec" {
    command = "rm ${local.lambda_package}"
  }
}


resource "aws_lambda_function_url" "lambda_function_url" {
  count              = var.lambda_function_url ? 1 : 0
  function_name      = aws_lambda_function.lambda_function.function_name
  authorization_type = "NONE"
}
