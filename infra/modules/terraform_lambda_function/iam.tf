data "aws_iam_role" "scheduler_role" {
  name = module.constants.eventbridge_scheduler_name
}

data "aws_iam_policy_document" "lambda_function_trust_policy" {
  statement {
    actions = ["sts:AssumeRole"]
    effect  = "Allow"
    principals {
      type = "Service"
      identifiers = [
        "lambda.amazonaws.com",
        "edgelambda.amazonaws.com"
      ]
    }
  }
}

resource "aws_iam_role" "lambda_function_role" {
  name               = "${local.function_name}_role"
  assume_role_policy = data.aws_iam_policy_document.lambda_function_trust_policy.json
}

resource "aws_iam_role_policy_attachment" "lambda_function_default_policy" {
  role       = aws_iam_role.lambda_function_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

resource "aws_iam_role_policy_attachment" "lambda_function_vpc_access" {
  count      = length(var.subnet_ids) > 0 && length(var.security_group_ids) > 0 ? 1 : 0
  role       = aws_iam_role.lambda_function_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaVPCAccessExecutionRole"
}

resource "aws_iam_role_policy" "scheduler_policy" {
  role = data.aws_iam_role.scheduler_role.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "lambda:InvokeFunction",
        ]
        Effect   = "Allow"
        Resource = aws_lambda_function.lambda_function.arn
      },
    ]
  })
}