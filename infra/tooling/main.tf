provider "aws" {
  region = "us-east-1"
  default_tags {
    tags = module.constants.common_tags
  }
}

provider "pagerduty" {
  token = data.aws_ssm_parameter.pagerduty_api_key.value
}

provider "newrelic" {
  region     = "US"
  account_id = "2837112"
  api_key    = data.aws_ssm_parameter.newrelic-api-key.value
}

terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "5.81.0"
    }
    archive = {
      source  = "hashicorp/archive"
      version = "2.4.1"
    }

    newrelic = {
      source  = "newrelic/newrelic"
      version = "~> 3.53.0"
    }

    pagerduty = {
      source  = "pagerduty/pagerduty"
      version = "~> 3.18.0"
    }

  }
  backend "s3" {
    bucket         = "massgov-pfml-aws-account-mgmt"
    key            = "terraform/infra-tooling.tfstate"
    region         = "us-east-1"
    dynamodb_table = "terraform_locks"
    encrypt        = "true"
  }
}

locals {
  infrastructure_email_addresses = [
    "<EMAIL>"
  ]
  lambda_directory = "lambda_functions"
}

# add a resource to handle moving existing file for lambda_layers
resource "aws_lambda_layer_version" "this" {
  for_each = toset(["python_requests_310", "python_PyGithub_310", "python_boto3_1_37_30"])

  filename                 = "lambda_layers/${each.key}.zip"
  layer_name               = each.key
  compatible_runtimes      = [module.constants.python_runtime]
  compatible_architectures = ["x86_64"]
}

module "constants" {
  source = "../constants"
}

module "aws_auditor" {
  source = "./aws_auditor"

  prefix            = "${module.constants.prefix}audit_"
  aws_region        = data.aws_region.current.name
  lambda_directory  = local.lambda_directory
  schedule          = "rate(1 day)"
  auditors_filename = "aws_auditors.json"
}

module "trigger_rds_iam_sync" {
  source = "./trigger_rds_iam_sync"

  github_api_key     = data.aws_ssm_parameter.github_api_key.name
  github_api_key_arn = data.aws_ssm_parameter.github_api_key.arn
  aws_region         = data.aws_region.current.name
  lambda_directory   = local.lambda_directory
  delay              = 900
}

module "ghas_code_scanning" {
  source = "./ghas_code_scanning"

  lambda_directory                      = local.lambda_directory
  layer_directory                       = "lambda_layers"
  jira_api_key                          = data.aws_ssm_parameter.jira_api_key.name
  jira_api_key_arn                      = data.aws_ssm_parameter.jira_api_key.arn
  jira_username                         = data.aws_ssm_parameter.jira_username.name
  jira_username_arn                     = data.aws_ssm_parameter.jira_username.arn
  ghas_code_scanning_webhook_secret_arn = data.aws_ssm_parameter.ghas_code_scanning_webhook_secret.arn
  ghas_code_scanning_webhook_secret     = data.aws_ssm_parameter.ghas_code_scanning_webhook_secret.name
  jira_parent_id                        = "PFMLPB-18137"
  jira_project_id                       = "PFMLPB"
}

module "ghas_dependabot" {
  source = "./ghas_dependabot"

  lambda_directory                   = local.lambda_directory
  layer_directory                    = "lambda_layers"
  jira_api_key                       = data.aws_ssm_parameter.jira_api_key.name
  jira_api_key_arn                   = data.aws_ssm_parameter.jira_api_key.arn
  jira_username                      = data.aws_ssm_parameter.jira_username.name
  jira_username_arn                  = data.aws_ssm_parameter.jira_username.arn
  ghas_dependabot_webhook_secret_arn = data.aws_ssm_parameter.ghas_dependabot_webhook_secret.arn
  ghas_dependabot_webhook_secret     = data.aws_ssm_parameter.ghas_dependabot_webhook_secret.name
  jira_parent_id                     = "PFMLPB-18137"
  jira_project_id                    = "PFMLPB"
}

module "ghas_secret_scanning" {
  source = "./ghas_secret_scanning"

  lambda_directory                        = local.lambda_directory
  layer_directory                         = "lambda_layers"
  jira_api_key                            = data.aws_ssm_parameter.jira_api_key.name
  jira_api_key_arn                        = data.aws_ssm_parameter.jira_api_key.arn
  jira_username                           = data.aws_ssm_parameter.jira_username.name
  jira_username_arn                       = data.aws_ssm_parameter.jira_username.arn
  ghas_secret_scanning_webhook_secret_arn = data.aws_ssm_parameter.ghas_secret_scanning_webhook_secret.arn
  ghas_secret_scanning_webhook_secret     = data.aws_ssm_parameter.ghas_secret_scanning_webhook_secret.name
  jira_parent_id                          = "PFMLPB-18137"
  jira_project_id                         = "PFMLPB"
}

module "ses_suppression_list" {
  source = "./ses_suppression_list"

  prefix           = "${module.constants.prefix}ses_suppression_list"
  tags             = module.constants.common_tags
  lambda_directory = local.lambda_directory
  schedule         = "rate(1 hour)"
  bucket_name      = "massgov-pfml-ses-logging"
  bucket_prefix    = "aws-account-suppression-list"
  output_filename  = "suppressed-destinations-list"
}

module "auto_resolve_security_hub" {
  source = "./auto_resolve_security_hub"

  lambda_directory = local.lambda_directory
  schedule         = "cron(0 12 * * ? *)" # Run at 12 Noon UTC
}

module "tenable_image_scanning" {
  source = "./tenable_image_scanning"

  lambda_directory  = local.lambda_directory
  schedule          = "cron(0 12-21 ? * MON-FRI *)" # Run every hour between 1200-2100 UTC Mon-Fri
  jira_api_key_name = data.aws_ssm_parameter.jira_api_key.name
  jira_api_key_arn  = data.aws_ssm_parameter.jira_api_key.arn
  jira_username     = data.aws_ssm_parameter.jira_username.name
  jira_username_arn = data.aws_ssm_parameter.jira_username.arn
  main_kms_key_arn  = data.aws_kms_alias.main_kms_key.arn
}
module "security_hub_ticket_generator" {
  source = "./security_hub_ticket_generator"

  lambda_directory  = "lambda_functions"
  jira_api_key_name = data.aws_ssm_parameter.jira_api_key.name

  jira_api_key_arn  = data.aws_ssm_parameter.jira_api_key.arn
  jira_username_arn = data.aws_ssm_parameter.jira_username.arn
  jira_username     = data.aws_ssm_parameter.jira_username.name
  jira_project_id   = "INFRA"
  jira_parent_id    = "INFRA-2840"
}

module "guard_duty_ticket_generator" {
  source = "./guard_duty_ticket_generator"

  lambda_directory  = "lambda_functions"
  jira_api_key_name = data.aws_ssm_parameter.jira_api_key.name

  jira_api_key_arn  = data.aws_ssm_parameter.jira_api_key.arn
  jira_username_arn = data.aws_ssm_parameter.jira_username.arn
  jira_username     = data.aws_ssm_parameter.jira_username.name
  jira_project_id   = "INFRA"
  jira_parent_id    = "INFRA-2840"
}

module "security_hub_summary" {
  source = "./security_hub_summary"

  kms_key_id       = "alias/massgov-pfml-main-kms-key"
  lambda_directory = "lambda_functions"
  report_footer    = ""
  schedule         = "cron(0 12 ? * MON *)"
  email_addresses  = [module.constants.infrastructure_email_address]
}

module "ip_72hour_block" {
  source = "./ip_72hour_block"
}

module "cost_anomaly_monitor" {
  source = "./cost_anomaly_monitor"

  email_addresses             = [module.constants.infrastructure_email_address]
  weekly_threshold_percentage = 10
  immediate_threshold_amount  = 200
}

locals {
  timezone = "America/New_York"
}

module "start_stop_services" {
  source = "./start_stop_services"

  lambda_directory = local.lambda_directory
  schedules = {
    service-stop-schedule = {
      # Stop should be 7 days a week so if someone starts an
      # environment on Saturday it doesn’t stay running until Monday night.
      # Stop will run twice (5 min after) to catch any service that may have not been fully stopped.
      expression = "cron(0,5 21 ? * * *)"
      timezone   = local.timezone
      payload = jsonencode({
        Action       = "STOP"
        Environments = ["infra-test", "shadow", "performance", "tst2"]
      })
    },
    service-start-schedule = {
      expression = "cron(15 06 ? * MON-FRI *)"
      timezone   = local.timezone
      payload = jsonencode({
        Action       = "START"
        Environments = ["infra-test"]
      })
    },
    service-start-schedule-2 = {
      expression = "cron(15 05 ? * * *)"
      timezone   = local.timezone
      payload = jsonencode({
        Action       = "START"
        Environments = ["tst2", "performance"]
      })
    }
  }
}

module "dns_check" {
  source = "./dns_check"

  schedules = {
    dns_check_schedule = {
      expression = "rate(10 minutes)"
      timezone   = null
      payload    = null
    }
  }
}

module "auto_delete_shadow_instances" {
  source = "./auto_delete_shadow_instances"

  lambda_directory = local.lambda_directory
  schedules = {
    auto_delete_shadow_instances_schedule = {
      expression = "rate(1 day)"
      timezone   = null
      payload    = null
    }
  }
}


module "monitor_rds_eventbridge" {
  source = "./monitor_rds_eventbridge"

  lambda_directory = local.lambda_directory
  filtered_roles   = ["massgov_pfml_auto_delete_shadow_instances_role"]
}

module "workspaces_usage_report" {
  source = "./workspaces_usage_report"

  lambda_directory = local.lambda_directory
  schedules = {
    workspaces_usage_report_schedule = {
      expression = "cron(0 3 1 * ? *)" # Run at 3 AM ET on the first day of every month
      timezone   = local.timezone
      payload    = null
    }
  }
}