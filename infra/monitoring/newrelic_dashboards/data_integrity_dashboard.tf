locals {
  data_integrity_queries = yamldecode(file("${path.module}/data_integrity_queries.yaml"))
}

resource "newrelic_one_dashboard" "data_integrity" {
  name        = "Data Integrity"
  permissions = "public_read_write"

  page {
    name = "Employee Data Integrity"

    widget_line {
      column = 1
      row    = 1
      width  = 4
      height = 3

      title = "Data Integrity Errors per day"

      nrql_query {
        query = <<-EOF
            ${local.data_integrity_queries.SyncEmployeeStep.agg_select}
            ${local.data_integrity_queries.SyncEmployeeStep.query} 
            SINCE 2 weeks ago TIMESERIES 1 day
            EOF
      }
      nrql_query {
        query = <<-EOF
            ${local.data_integrity_queries.ImportFineosEmployeeUpdatesStep.agg_select}
            ${local.data_integrity_queries.ImportFineosEmployeeUpdatesStep.query} 
            SINCE 2 weeks ago TIMESERIES 1 day
            EOF
      }
      nrql_query {
        query = <<-EOF
            ${local.data_integrity_queries.LinkClaimToApplicationStep.agg_select}
            ${local.data_integrity_queries.LinkClaimToApplicationStep.query} 
            SINCE 2 weeks ago TIMESERIES 1 day
            EOF
      }
    }
    widget_billboard {
      column = 5
      row    = 1
      width  = 8
      height = 3

      title = "Data Integrity Errors - last 24 hours"
      nrql_query {
        query = <<-EOF
            ${local.data_integrity_queries.SyncEmployeeStep.agg_select}
            ${local.data_integrity_queries.SyncEmployeeStep.query} 
            TIMESERIES 1 day
            SINCE 1 days ago
            EOF
      }
      nrql_query {
        query = <<-EOF
            ${local.data_integrity_queries.ImportFineosEmployeeUpdatesStep.agg_select}
            ${local.data_integrity_queries.ImportFineosEmployeeUpdatesStep.query} 
            TIMESERIES 1 day
            SINCE 1 days ago
            EOF
      }
      nrql_query {
        query = <<-EOF
            ${local.data_integrity_queries.LinkClaimToApplicationStep.agg_select}
            ${local.data_integrity_queries.LinkClaimToApplicationStep.query} 
            TIMESERIES 1 day
            SINCE 1 days ago
            EOF
      }
    }
    widget_markdown {
      column = 1
      row    = 4
      width  = 4
      height = 3

      title = ""
      text  = local.data_integrity_queries.SyncEmployeeStep.description
    }
    widget_log_table {
      column = 5
      row    = 4
      width  = 8
      height = 3
      title  = local.data_integrity_queries.SyncEmployeeStep.name
      nrql_query {
        query = <<-EOF
            ${local.data_integrity_queries.SyncEmployeeStep.detail_select}
            ${local.data_integrity_queries.SyncEmployeeStep.query} 
            SINCE 1 day ago 
            EOF
      }
    }

    widget_markdown {
      column = 1
      row    = 7
      width  = 4
      height = 3

      title = ""
      text  = local.data_integrity_queries.ImportFineosEmployeeUpdatesStep.description
    }
    widget_log_table {
      column = 5
      row    = 7
      width  = 8
      height = 3
      title  = local.data_integrity_queries.ImportFineosEmployeeUpdatesStep.name
      nrql_query {
        query = <<-EOF
            ${local.data_integrity_queries.ImportFineosEmployeeUpdatesStep.detail_select}
            ${local.data_integrity_queries.ImportFineosEmployeeUpdatesStep.query} 
            SINCE 1 day ago 
            EOF
      }
    }
    widget_markdown {
      column = 1
      row    = 10
      width  = 4
      height = 3

      title = ""
      text  = local.data_integrity_queries.LinkClaimToApplicationStep.description
    }
    widget_line {
      column = 5
      row    = 10
      width  = 3
      height = 3

      title = ""

      nrql_query {
        query = <<-EOF
            ${local.data_integrity_queries.LinkClaimToApplicationStep.agg_select}
            ${local.data_integrity_queries.LinkClaimToApplicationStep.query} 
            SINCE 2 weeks ago TIMESERIES 1 day
            FACET message
            EOF
      }
    }
    widget_log_table {
      column = 8
      row    = 10
      width  = 5
      height = 3
      title  = local.data_integrity_queries.LinkClaimToApplicationStep.name
      nrql_query {
        query = <<-EOF
            ${local.data_integrity_queries.LinkClaimToApplicationStep.detail_select}
            ${local.data_integrity_queries.LinkClaimToApplicationStep.query} 
            SINCE 1 day ago 
            EOF
      }
    }
  }
}

