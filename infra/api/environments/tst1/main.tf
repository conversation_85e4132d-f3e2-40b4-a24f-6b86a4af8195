# This file was originally generated from the following command:
#
#   bin/bootstrap-env.sh tst1 api
#
# If adding new variables, it's recommended to update the bootstrap
# templates so there's less manual work in creating new envs.
#

locals {
  environment_name = "tst1"
}

provider "aws" {
  region = "us-east-1"
}

terraform {
  backend "s3" {
    bucket         = "massgov-pfml-tst1-env-mgmt"
    key            = "terraform/api.tfstate"
    region         = "us-east-1"
    dynamodb_table = "terraform_locks"
  }
}

data "aws_ecs_cluster" "tst1" {
  cluster_name = "tst1"
}

data "aws_secretsmanager_secret" "rmv_client_certificate" {
  name = "/service/pfml-api-tst1/rmv_client_certificate"
}

data "aws_secretsmanager_secret" "usbank_client_certificate" {
  name = "/service/pfml-api/${local.environment_name}/usbank_client_certificate"
}

module "api" {
  source = "../../template"

  environment_name                = local.environment_name
  service_app_count               = 1
  service_max_app_count           = 10
  service_docker_tag              = local.service_docker_tag
  service_ecs_cluster_arn         = data.aws_ecs_cluster.tst1.arn
  vpc_id                          = data.aws_vpc.vpc.id
  vpc_app_subnet_ids              = data.aws_subnets.vpc_app.ids
  vpc_db_subnet_ids               = data.aws_subnets.vpc_db.ids
  vpc_name                        = local.vpc
  postgres_version                = "14.13"
  postgres_parameter_group_family = "postgres14"
  lb_port                         = 3507
  cors_origins = [
    "https://5edqb9tcsa.execute-api.us-east-1.amazonaws.com",
    "https://d3p328bz52vfwn.cloudfront.net",
    "https://paidleave-tst1.dfml.eol.mass.gov",
    "https://paidleave-api-tst1.dfml.eol.mass.gov",
    # Allow requests from the Admin Portal
    "https://paidleave-admin-tst1.dfml.eol.mass.gov",

    # We're also going to allow requests from Portal developer's machines for now, so they
    # can test certain features without deploying to the test environment. This is not
    # really that secure since anyone can spin up a local server on port 3000 and hit our
    # API, but we're not heavily using the tst1 environment right now so it's fine.
    "http://localhost:3000",
  ]

  # Portal Preview Environments (PPE) use TST1 API. Since PPE domains are dynamically generated,
  # we need to use a regex pattern for the subdomain (Pull request number i.e. pr-12345)
  # https://www.starlette.io/middleware/#corsmiddleware
  cors_origin_regex = ".*\\\\.pfml\\\\.eol\\\\.comacloud\\\\.net"

  enable_application_fraud_check = "0"
  release_version                = var.release_version

  portal_base_url = "https://paidleave-tst1.dfml.eol.mass.gov"

  logging_level = "massgov.pfml.fineos.fineos_client=DEBUG"

  # TODO: Connect to an RMV endpoint if desired. All nonprod environments are connected to the staging API
  #       in either a fully-mocked or partially-mocked setting.
  rmv_client_base_url               = "https://atlas-staging-gateway.massdot.state.ma.us/vs"
  rmv_client_certificate_binary_arn = data.aws_secretsmanager_secret.rmv_client_certificate.arn
  rmv_api_behavior                  = "fully_mocked"
  rmv_check_mock_success            = "1"

  usbank_client_base_url               = "https://alpha-apip2-prepaid.usbank.com"
  usbank_client_certificate_binary_arn = data.aws_secretsmanager_secret.usbank_client_certificate.arn

  # TODO: These values are provided by FINEOS.
  fineos_client_integration_services_api_url          = "https://idt1-api.masspfml.fineos.com/integration-services/"
  fineos_client_customer_api_url                      = "https://idt1-api.masspfml.fineos.com/customerapi/"
  fineos_client_group_client_api_url                  = "https://idt1-api.masspfml.fineos.com/groupclientapi/"
  fineos_client_wscomposer_api_url                    = "https://idt1-api.masspfml.fineos.com/integration-services/wscomposer/"
  fineos_client_oauth2_url                            = "https://idt1-api.masspfml.fineos.com/oauth2/token"
  fineos_import_employee_updates_input_directory_path = "s3://fin-somvrf-data-export/IDT1/dataexports"
  fineos_aws_iam_role_arn                             = "arn:aws:iam::************:role/somvrf-IAMRoles-CustomerAccountAccessRole-VsNw727kJDoq"
  fineos_aws_iam_role_external_id                     = "12345"

  # TODO: This value is provided by FINEOS over Interchange.

  fineos_v21_upgrade_date = "2022-04-04"

  # TODO: Connect to ServiceNow.
  service_now_base_url  = "https://savilinxtest.servicenowservices.com"
  admin_portal_base_url = "https://paidleave-admin-tst1.dfml.eol.mass.gov"

  azure_ad_authority_domain = "login.microsoftonline.com"
  azure_ad_client_id        = "ecc75e15-cd60-4e28-b62f-d1bf80e05d4d"
  azure_ad_parent_group     = "TSS-SG-PFML_ADMIN_PORTAL_NON_PROD"
  azure_ad_tenant_id        = "3e861d16-48b7-4a0e-9806-8c04d81b7b2a"

  enable_child_support_automation                            = "0"
  enable_document_multipart_upload                           = "1"
  channel_switch_unsupported_claims                          = "1"
  limit_ssn_fein_max_attempts                                = "5"
  claim_status_v2_employer_review                            = "0"
  enable_email_change                                        = "1"
  enable_service_agreement_versions_for_existing_automations = "1"
  enable_system_message_translation                          = "0"

  # @TODO(PFMLPB-23195): Remove enable_mark_applications_ready_for_review
  enable_mark_applications_ready_for_review = "1"

  enable_prepaid_impact_payments = "1"
  enable_sync_payment_preference = "1"

  # Med to Bonding
  # @TODO(PFMLPB-24292): Remove feature flag for Med to Bonding BPC
  enable_delayed_submission_of_modifications = "0"

  # @TODO(PFMLPB-24796): Remove enable_re_notification
  enable_re_notification = "0"

  # Following provided by BI Team
  snowflake_snowpipe_user_arn    = "arn:aws:iam::************:user/oonb0000-s"
  snowflake_snowpipe_external_id = "RBB97437_SFCRole=2_jMU92eFIHlR0swcz8RMxtbzMGEI="
  snowflake_snowpipe_queue_arn   = "arn:aws:sqs:us-east-1:************:sf-snowpipe-AIDAV4GCTVNJFCGQ6GYHW-CYElAWK0-AARR55ZuFKXUA"

  # PDF API Service Variables
  pdf_api_lb_port               = 3607
  pdf_api_service_app_count     = 1
  pdf_api_service_max_app_count = 3

  # US Bank Client settings
  usbank_client_oauth2_url = "https://stage-apip.prepaidgateway.com/oauth/oauth20/token"
}
