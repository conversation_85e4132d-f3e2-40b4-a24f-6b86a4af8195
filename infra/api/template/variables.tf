variable "environment_name" {
  description = "Name of the environment"
  type        = string
}

variable "service_app_count" {
  description = "Number of application containers to run"
  type        = number
}

variable "service_max_app_count" {
  description = "Maximum number of application containers to run (under auto scaling)."
  type        = number
}

variable "service_docker_tag" {
  description = "Tag of the docker image to run"
  type        = string
}

variable "service_ecs_cluster_arn" {
  description = "ARN of the ECS cluster used to schedule app containers."
  type        = string
}

variable "vpc_id" {
  description = "The VPC ID."
  type        = string
}

variable "vpc_app_subnet_ids" {
  description = "A list of app-level subnets within the VPC."
  type        = list(string)
}

##################################################################################################
### API Service RDS Configuration Variables
##################################################################################################


variable "vpc_db_subnet_ids" {
  description = "A list of db-level subnets within the VPC."
  type        = list(string)
}

variable "postgres_version" {
  description = "The version of the postgres database."
  type        = string
  default     = "14.13"
}

variable "postgres_parameter_group_family" {
  description = "The parameter group family for the postgres database."
  type        = string
  default     = "postgres14"
}

variable "db_allocated_storage" {
  description = "The allocated storage in gibibytes."
  type        = number
  default     = 20
}

variable "db_max_allocated_storage" {
  description = "The upper limit for automatically scaled storage in gibibytes."
  type        = number
  default     = 100
}

variable "db_instance_class" {
  description = "The instance class of the database (RDS)."
  type        = string
  default     = "db.r5.xlarge" # For now, this must be changed in AWS Console. Modifications to this field will yield no result.
}

variable "db_iops" {
  description = "The amount of provisioned IOPS."
  type        = number
  default     = null
}

variable "db_storage_type" {
  description = "Storage type, one of gp3 or io2."
  type        = string
  default     = "gp3" # For now, this must be changed in AWS Console. Modifications to this field will yield no result.
}

variable "db_multi_az" {
  description = "Specifies if the RDS instance is multi-AZ."
  type        = bool
  default     = false
}

variable "backup_window" {
  description = "The daily time range (in UTC) during which automated backups are created if they are enabled. Example: '09:46-10:16'. Must not overlap with maintenance_window"
  type        = string
  default     = null
}

variable "maintenance_window" {
  description = "The window to perform maintenance in. Syntax: 'ddd:hh24:mi-ddd:hh24:mi'. Eg: 'Mon:00:00-Mon:03:00'"
  type        = string
  default     = null
}

### Valid values are: 7, 31, 62, 93, 124, 155, 186, 217, 248, 279, 310, 341, 372, 403, 434, 465, 496, 527, 558, 589, 620, 651, 682, 713, 731
variable "performance_insights_retention_period" {
  description = "The amount of days to retain Performance Insights logs"
  type        = number
  default     = 93
}

variable "vpc_name" {
  description = "Used to grab the correct LB name"
  type        = string
}

variable "lb_port" {
  description = "Port of the load balancer that has been reserved within the API Gateway."
  type        = string
}

variable "enable_full_error_logs" {
  description = "Enable logging of full request and response on errors"
  type        = string
  default     = "0"
}

variable "enable_alarm_api_cpu" {
  description = "Enable Cloudwatch alarms for API CPU Usage"
  type        = bool
  default     = true
}

variable "enable_alarm_api_ram" {
  description = "Enable Cloudwatch alarms for API RAM Usage"
  type        = bool
  default     = true
}

variable "enable_document_multipart_upload" {
  description = "Enable document uploads through the FINEOS multipart endpoint"
  type        = string
  default     = "1"
}

variable "cors_origins" {
  description = "A list of origins to allow CORS requests from."
  type        = list(string)
}

variable "cors_origin_regex" {
  description = "Portal Preview Environment CORS Origin Regex"
  type        = string
  default     = ""
}

variable "logging_level" {
  description = "Override default logging levels for certain Python modules."
  type        = string
  default     = ""
}

variable "rmv_client_base_url" {
  description = "The base URL for the Registry of Motor Vehicles (RMV) API."
  type        = string
  default     = ""
}

variable "rmv_client_certificate_binary_arn" {
  description = "The secretsmanager ARN for the Registry of Motor Vehicles (RMV) certificate."
  type        = string
  default     = ""
}

variable "usbank_client_base_url" {
  description = "The base URL for the US Bank API client certificate."
  type        = string
  default     = ""
}

variable "usbank_client_certificate_binary_arn" {
  description = "The secretsmanager ARN for the US Bank API client certificate."
  type        = string
  default     = ""
}

variable "rmv_api_behavior" {
  description = "Specifies if the RMV response is mocked"
  type        = string
  default     = "fully_mocked"
}

variable "rmv_check_mock_success" {
  description = "Specifies if RMV check mock response always passes. '1' always passes id proofing, '0' always fails id proofing."
  type        = string
  default     = "1"
}

variable "fineos_client_customer_api_url" {
  description = "URL of the FINEOS Customer API"
  type        = string
  default     = ""
}

variable "fineos_client_group_client_api_url" {
  description = "URL of the FINEOS Group Client API"
  type        = string
  default     = ""
}

variable "fineos_client_integration_services_api_url" {
  description = "URL of the FINEOS Integration Services API"
  type        = string
  default     = ""
}

variable "fineos_client_wscomposer_api_url" {
  description = "URL of the FINEOS Web Services Composer API"
  type        = string
  default     = ""
}

variable "fineos_client_wscomposer_user_id" {
  description = "User id for FINEOS Web Services Composer API"
  type        = string
  default     = "CONTENT"
}

variable "fineos_client_soap_user_id" {
  description = "User id for FINEOS SOAP API endpoints"
  type        = string
  default     = "CONTENT"
}

variable "fineos_client_oauth2_url" {
  description = "URL of the FINEOS OAuth2 token endpoint."
  type        = string
  default     = ""
}

variable "pfml_fineos_eligibility_feed_output_directory_path" {
  description = "Location the Eligibility Feed Lambda should write output to"
  type        = string
  default     = null
}

variable "fineos_eligibility_feed_arhive_directory_path" {
  description = "Location the Eligibility Feed Lambda should archive to"
  type        = string
  default     = null
}

variable "fineos_import_employee_updates_input_directory_path" {
  description = "Location of the FINEOS extract to process into our DB."
  type        = string
  default     = null
}

variable "fineos_aws_iam_role_arn" {
  description = "ARN for role in the FINEOS AWS account that must be used to access resources inside of it"
  type        = string
  default     = null
}

variable "fineos_aws_iam_role_external_id" {
  description = "ExternalId paramter for assuming role specified by fineos_aws_iam_role_arn"
  type        = string
  default     = null
}

variable "fineos_v21_upgrade_date" {
  description = "The date that the FINEOS 21.3 upgrade was deployed to the environment, eg 2022-05-20"
  type        = string
  default     = ""
}

variable "fineos_is_running_v24" {
  description = "Whether the environment is running Fineos V24+"
  type        = string
  default     = "0"
}

variable "service_now_base_url" {
  description = "URL for Service Now post requests"
  type        = string
  default     = ""
}

variable "portal_base_url" {
  description = "Portal base URL to use when creating links"
  type        = string
  default     = ""
}

variable "admin_portal_base_url" {
  description = "Admin Portal base URL for Azure log in and redirect URLs."
  type        = string
  default     = ""
}

variable "azure_ad_authority_domain" {
  description = "The main azure domain used for public keys and logging out."
  type        = string
  default     = ""
}

variable "azure_ad_client_id" {
  description = "Used to associate with the Azure AD application."
  type        = string
  default     = ""
}

variable "azure_ad_client_secret" {
  description = "Used to generate the access token."
  type        = string
  default     = ""
}

variable "azure_ad_parent_group" {
  description = "The main authentication group, which represents either prod or non-prod."
  type        = string
  default     = ""
}

variable "azure_ad_tenant_id" {
  description = "Identifies the Azure AD tenant to use for authentication. AKA directory ID."
  type        = string
  default     = ""
}

variable "enable_application_fraud_check" {
  description = "Enable the fraud check for application submission"
  type        = string
}

# @TODO(PFMLPB-23195): Remove enable_mark_applications_ready_for_review
variable "enable_mark_applications_ready_for_review" {
  description = "Enable marking applications ready for review"
  type        = string
  default     = "0"
}

variable "enable_re_notification" {
  description = "Feature flag to enable re-notification for RFI"
  type        = string
  default     = "0"
}

variable "release_version" {
  description = "API release version"
  type        = string
  default     = ""
}

variable "new_plan_proofs_active_at" {
  description = "ISO 8601 formatted date string, should explicitly set UTC offset (+00:00)"
  type        = string
  default     = "2021-06-21 00:00:00+00:00"
}

variable "limit_ssn_fein_max_attempts" {
  description = "Whether or not to limit failed SSS/FEIN combinations"
  type        = string
  default     = "5"
}

variable "channel_switch_unsupported_claims" {
  description = "Allow claimants to import unsupported claims"
  type        = string
  default     = "1"
}

variable "enable_verification_limits" {
  description = "Enable daily limits on leave admin verification"
  type        = string
  default     = "1"
}

variable "enable_2025_state_metrics" {
  description = "Insert 2025 state metric records into the PFML database"
  type        = string
  default     = "1"
}

variable "claim_status_v2_employer_review" {
  description = "Enable V2 code for employer review"
  type        = string
  default     = "0"
}

variable "enable_email_change" {
  description = "Enable feature for users to change their email address"
  type        = string
  default     = "0"
}

variable "enable_service_agreement_versions_for_existing_automations" {
  description = "Enable/Disable the functionality to send service agreement versions to FINEOS"
  type        = string
  default     = "0"
}

variable "enable_system_message_translation" {
  description = "Enable or disable system messages translation"
  type        = string
  default     = "0"
}

##################################################################################################
### PDF API Service
##################################################################################################

variable "pdf_api_service_app_count" {
  description = "Number of application containers to run"
  type        = number
}

variable "pdf_api_service_max_app_count" {
  description = "Max number of application containers to run"
  type        = number
}

variable "pdf_api_lb_port" {
  description = "Port of the load balancer reserved for PDF API target groups"
  type        = number
}

variable "generate_1099_max_files" {
  description = "Maximum number of 1099s to generate"
  default     = "1000"
}

variable "upload_max_files_to_fineos" {
  description = "max number of 1099 documents to upload to Fineos API"
  default     = "10"
}

variable "enable_1099_testfile_generation" {
  description = "Enable IRS 1099 test file generation"
  default     = "0"
}

variable "irs_1099_tax_year" {
  description = "Declares the tax year for the 1099 batch"
  default     = "2000"
}

variable "irs_1099_correction_ind" {
  description = "Declares if the 1099 batch should be a correction run"
  default     = "0"
}

variable "enable_offset_get_1099" {
  description = "Declares to process the new logic to upload 1099 documents"
  default     = "0"
}

variable "upload_1099_doc_batch_start" {
  description = "Declares the start of the 1099 records to upload the 1099 document in a batch run"
  default     = "1"
}

variable "upload_1099_doc_batch_end" {
  description = "Declares the end of the 1099 records to upload the 1099 document in a batch run"
  default     = "10"
}

variable "enable_image_access_document_upload" {
  description = "If the API supports document uploads from Image Access"
  type        = string
  default     = "false"
}

##################################################################################################
### LWD CData access for snowflake, this is per enviornment
### default is for dev/non-prod
##################################################################################################
variable "lwd_cdata_ip_addresses" {
  description = "Specifies IP addresses for RDS access from LWD Account"
  type        = list(string)
  default     = ["**************/32"]
}

variable "s3_access_logging_enabled" {
  description = "Enables or disables S3 access logging per environment"
  type        = bool
  default     = false
}

variable "bucket_replication_enabled" {
  description = "Enables bucket replication per environment"
  type        = bool
  default     = false
}

variable "enable_child_support_automation" {
  description = "Enable Child Support Automation"
  default     = "0"
}
##################################################################################################
### Business Intelligence
##################################################################################################

variable "snowflake_snowpipe_user_arn" {
  description = "snowflake_user_arn"
  type        = string
  default     = ""
}

variable "snowflake_snowpipe_external_id" {
  description = "snowflake_external_id"
  type        = string
  default     = ""
}

variable "snowflake_snowpipe_queue_arn" {
  description = "snowflake sqs queue arn"
  type        = string
  default     = ""
}

##################################################################################################
### Login.mass.gov Configuration Variables
##################################################################################################
# LMG PERSONAL
variable "lmg_personal_base_url" {
  description = "URL of the LMG PERSONAL tenant"
  type        = string
  default     = "https://personal.login.test.tss.mass.gov/0f521299-8041-4527-b1d0-5ebebacc91cc"
}
variable "lmg_personal_application_id" {
  description = "OAuth Application ID of the LMG PERSONAL tenant"
  type        = string
  default     = "f51dbf6d-c5c3-4419-ac95-6546f8f797e5"
}
variable "lmg_personal_policy_sisu" {
  description = "LMG action policy for personal users to sign-in/sign-up"
  type        = string
  default     = "B2C_1A_CITIZEN_SISU"
}
variable "lmg_personal_policy_change_email" {
  description = "LMG action policy for personal users to change email address (username) on their account"
  type        = string
  default     = "B2C_1A_CITIZEN_CHANGEEMAIL"
}
variable "lmg_personal_policy_change_mfa" {
  description = "LMG action policy for personal users to change MFA settings on their account"
  type        = string
  default     = "B2C_1A_CITIZEN_CHANGEMFA"
}
variable "lmg_personal_policy_change_name" {
  description = "LMG action policy for personal users to change the name on their account"
  type        = string
  default     = "B2C_1A_CITIZEN_CHANGENAME"
}
variable "lmg_personal_policy_change_pw" {
  description = "LMG action policy for personal users to change the password on their account"
  type        = string
  default     = "B2C_1A_CITIZEN_CHANGEPASSWORD"
}
# LMG BUSINESS
variable "lmg_business_base_url" {
  description = "URL of the LMG BUSINESS tenant"
  type        = string
  default     = "https://admin.login.test.tss.mass.gov/ed96e6cd-0036-4043-bb11-3900ecd6c71c"
}
variable "lmg_business_application_id" {
  description = "OAuth Application ID of the LMG BUSINESS tenant"
  type        = string
  default     = "272590a9-930e-45d4-b8ad-e32b394031e5"
}
### LMG operation policies
### https://massgov.sharepoint.com/sites/TSS-LoginDevGuide/SitePages/Attributes.aspx?csf=1&web=1&e=fzQwaS
variable "lmg_business_policy_sisu" {
  description = "LMG action policy for business users to sign-in/sign-up"
  type        = string
  default     = "B2C_1A_PARTNER_SISU"
}
variable "lmg_business_policy_change_email" {
  description = "LMG action policy for business users to change email address (username) on their account"
  type        = string
  default     = "B2C_1A_PARTNER_CHANGEEMAIL"
}
variable "lmg_business_policy_change_mfa" {
  description = "LMG action policy for business users to change MFA settings on their account"
  type        = string
  default     = "B2C_1A_PARTNER_CHANGEMFA"
}
variable "lmg_business_policy_change_name" {
  description = "LMG action policy for business users to change the name on their account"
  type        = string
  default     = "B2C_1A_PARTNER_CHANGENAME"
}
variable "lmg_business_policy_change_pw" {
  description = "LMG action policy for business users to change the password on their account"
  type        = string
  default     = "B2C_1A_PARTNER_CHANGEPASSWORD"
}

# LMG secrets name fragment vars
### Valid values are: non_prod, prod
variable "idp_ssm_env" {
  description = "SSM name fragment: LMG host"
  type        = string
  default     = "non_prod"
}

### Valid values are: mfa_on, mfa_off
variable "lmg_ssm_mfa_state" {
  description = "SSM name fragment: LMG app type (MFA/NOMFA)"
  type        = string
  default     = "mfa_on"
}

variable "enable_fineos_api_logging_to_db" {
  description = "Feature Flag to enable FINEOS API logging to the PFML DB"
  default     = "1"
}

variable "enable_leave_admin_mtc_verification" {
  description = "A feature flag is needed to enable leave admins to verify using their MTC number."
  default     = "0"
}

variable "enable_prepaid_impact_payments" {
  description = "Feature flag to protect the existing payments processing to facilitate development around Prepaid debit cards"
  default     = "0"
}

variable "enable_sync_payment_preference" {
  description = "Feature flag to enable sync of payment preference"
  default     = "0"
}

variable "mymassgov_api_personal_application_id_uri" {
  description = "MMG API app ID urls for personal scopes"
  type        = string
  default     = "https://TestMassGovB2C.onmicrosoft.com/cc7f9e56-2b0e-4836-8cf5-1f7522620038"
}

variable "mymassgov_api_business_application_id_uri" {
  description = "MMG API app ID urls for business scopes"
  type        = string
  default     = "https://PartnerTestMassGovB2C.onmicrosoft.com/bbce28f8-bd8e-4902-b22b-f324da2531bf"
}

variable "my_mass_gov_api_client_base_url" {
  description = "MMG API base URL"
  type        = string
  default     = "https://api.my.test-next.tss.mass.gov"
}

variable "enable_document_upload_optional" {
  description = "Feature Flag to make document uploads optional for claimants"
  default     = "0"
}

##################################################################################################
### MED TO BONDING
##################################################################################################

variable "enable_delayed_submission_of_modifications" {
  description = "Feature flag for enabling and disabling the delayed processing of modifications backend pipeline component (BPC) of Med to Bonding"
  type        = string
  default     = "0"
}

##################################################################################################
### USBANK API
##################################################################################################

variable "usbank_client_oauth2_url" {
  description = "URL of the USBANK OAuth2 token endpoint."
  type        = string
  default     = ""
}
