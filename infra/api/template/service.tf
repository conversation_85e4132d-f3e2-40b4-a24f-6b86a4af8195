# Terraform configuration for running applications on ECS Fargate. Applications are run
# in a private subnet behind a network load balancer.
#
data "aws_ecr_repository" "app" {
  name = local.app_name
}

resource "aws_ecs_service" "app" {
  name                          = "${local.app_name}-${var.environment_name}"
  task_definition               = aws_ecs_task_definition.app.arn
  cluster                       = var.service_ecs_cluster_arn
  launch_type                   = "FARGATE"
  platform_version              = "1.4.0"
  desired_count                 = var.service_app_count
  availability_zone_rebalancing = var.environment_name == "prod" ? "DISABLED" : "ENABLED"

  # WORKAROUND: Increase health check grace period to 5 minutes to account for
  # lag time in ALB starting to send requests to new tasks.
  health_check_grace_period_seconds = 300

  # Allow changes to the desired_count without differences in terraform plan.
  # This allows autoscaling to manage the desired count for us.
  lifecycle {
    ignore_changes = [desired_count]
  }

  network_configuration {
    assign_public_ip = false
    subnets          = data.aws_subnet.app.*.id
    security_groups  = [aws_security_group.app.id]
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.app.id
    container_name   = local.app_name
    container_port   = 1550
  }

  depends_on = [
    aws_lb_listener.listener,
    aws_iam_role_policy.task_executor,
  ]
}

# @TODO(PFMLPB-23195): Remove enable_mark_applications_ready_for_review below
resource "aws_ecs_task_definition" "app" {
  family             = "${local.app_name}-${var.environment_name}-server"
  execution_role_arn = aws_iam_role.task_executor.arn
  task_role_arn      = aws_iam_role.api_service.arn
  container_definitions = templatefile(
    "${path.module}/container_definitions.json",
    {
      app_name                                                   = local.app_name
      cpu                                                        = "2048"
      memory                                                     = module.constants.api_ram_size
      db_host                                                    = aws_db_instance.default.address
      db_name                                                    = aws_db_instance.default.db_name
      db_username                                                = "pfml_api"
      docker_image                                               = "${data.aws_ecr_repository.app.repository_url}:${var.service_docker_tag}"
      environment_name                                           = var.environment_name
      enable_full_error_logs                                     = var.enable_full_error_logs
      cloudwatch_logs_group_name                                 = aws_cloudwatch_log_group.service_logs.name
      aws_region                                                 = data.aws_region.current.name
      cors_origins                                               = join(",", var.cors_origins)
      cors_origin_regex                                          = var.cors_origin_regex
      logging_level                                              = var.logging_level
      rmv_client_certificate_binary_arn                          = var.rmv_client_certificate_binary_arn
      rmv_client_base_url                                        = var.rmv_client_base_url
      usbank_client_certificate_binary_arn                       = var.usbank_client_certificate_binary_arn
      usbank_client_base_url                                     = var.usbank_client_base_url
      rmv_api_behavior                                           = var.rmv_api_behavior
      rmv_check_mock_success                                     = var.rmv_check_mock_success
      fineos_client_customer_api_url                             = var.fineos_client_customer_api_url
      fineos_client_integration_services_api_url                 = var.fineos_client_integration_services_api_url
      fineos_client_group_client_api_url                         = var.fineos_client_group_client_api_url
      fineos_client_wscomposer_api_url                           = var.fineos_client_wscomposer_api_url
      fineos_client_wscomposer_user_id                           = var.fineos_client_wscomposer_user_id
      fineos_client_soap_user_id                                 = var.fineos_client_soap_user_id
      fineos_client_oauth2_url                                   = var.fineos_client_oauth2_url
      fineos_client_oauth2_client_id                             = data.aws_ssm_parameter.fineos_oauth2_client_id.value
      fineos_v21_upgrade_date                                    = var.fineos_v21_upgrade_date
      fineos_is_running_v24                                      = var.fineos_is_running_v24
      pfml_email_address                                         = data.aws_ses_email_identity.bounce_forwarding_email.email
      bounce_forwarding_email_address                            = data.aws_ses_email_identity.bounce_forwarding_email.email
      bounce_forwarding_email_address_arn                        = data.aws_ses_email_identity.bounce_forwarding_email.arn
      service_now_base_url                                       = var.service_now_base_url
      portal_base_url                                            = var.portal_base_url
      admin_portal_base_url                                      = var.admin_portal_base_url
      azure_ad_authority_domain                                  = var.azure_ad_authority_domain
      azure_ad_client_id                                         = var.azure_ad_client_id
      azure_ad_parent_group                                      = var.azure_ad_parent_group
      azure_ad_tenant_id                                         = var.azure_ad_tenant_id
      enable_application_fraud_check                             = var.enable_application_fraud_check
      enable_mark_applications_ready_for_review                  = var.enable_mark_applications_ready_for_review
      enable_re_notification                                     = var.enable_re_notification
      release_version                                            = var.release_version
      new_plan_proofs_active_at                                  = var.new_plan_proofs_active_at
      enable_document_multipart_upload                           = var.enable_document_multipart_upload
      limit_ssn_fein_max_attempts                                = var.limit_ssn_fein_max_attempts
      channel_switch_unsupported_claims                          = var.channel_switch_unsupported_claims
      pdf_api_host                                               = "https://${var.vpc_name}-alb.pfml.eol.comacloud.net:${var.pdf_api_lb_port}"
      generate_1099_max_files                                    = var.generate_1099_max_files
      upload_max_files_to_fineos                                 = var.upload_max_files_to_fineos
      enable_1099_testfile_generation                            = var.enable_1099_testfile_generation
      irs_1099_correction_ind                                    = var.irs_1099_correction_ind
      irs_1099_tax_year                                          = var.irs_1099_tax_year
      enable_verification_limits                                 = var.enable_verification_limits
      enable_image_access_document_upload                        = var.enable_image_access_document_upload
      claim_status_v2_employer_review                            = var.claim_status_v2_employer_review
      enable_email_change                                        = var.enable_email_change
      enable_child_support_automation                            = var.enable_child_support_automation
      enable_service_agreement_versions_for_existing_automations = var.enable_service_agreement_versions_for_existing_automations
      experian_auth_token                                        = data.aws_ssm_parameter.experian_auth_token.value
      enable_offset_get_1099                                     = var.enable_offset_get_1099
      upload_1099_doc_batch_start                                = var.upload_1099_doc_batch_start
      upload_1099_doc_batch_end                                  = var.upload_1099_doc_batch_end
      enable_system_message_translation                          = var.enable_system_message_translation
      lmg_personal_base_url                                      = var.lmg_personal_base_url
      lmg_personal_application_id                                = var.lmg_personal_application_id
      lmg_personal_policy_sisu                                   = var.lmg_personal_policy_sisu
      lmg_personal_policy_change_email                           = var.lmg_personal_policy_change_email
      lmg_personal_policy_change_mfa                             = var.lmg_personal_policy_change_mfa
      lmg_personal_policy_change_name                            = var.lmg_personal_policy_change_name
      lmg_personal_policy_change_pw                              = var.lmg_personal_policy_change_pw
      lmg_business_base_url                                      = var.lmg_business_base_url
      lmg_business_application_id                                = var.lmg_business_application_id
      lmg_business_policy_sisu                                   = var.lmg_business_policy_sisu
      lmg_business_policy_change_email                           = var.lmg_business_policy_change_email
      lmg_business_policy_change_mfa                             = var.lmg_business_policy_change_mfa
      lmg_business_policy_change_name                            = var.lmg_business_policy_change_name
      lmg_business_policy_change_pw                              = var.lmg_business_policy_change_pw
      idp_ssm_env                                                = var.idp_ssm_env
      lmg_ssm_mfa_state                                          = var.lmg_ssm_mfa_state
      enable_fineos_api_logging_to_db                            = var.enable_fineos_api_logging_to_db
      enable_leave_admin_mtc_verification                        = var.enable_leave_admin_mtc_verification
      enable_document_upload_optional                            = var.enable_document_upload_optional
      enable_prepaid_impact_payments                             = var.enable_prepaid_impact_payments
      enable_sync_payment_preference                             = var.enable_sync_payment_preference
      mymassgov_api_personal_application_id_uri                  = var.mymassgov_api_personal_application_id_uri
      mymassgov_api_business_application_id_uri                  = var.mymassgov_api_business_application_id_uri
      my_mass_gov_api_client_base_url                            = var.my_mass_gov_api_client_base_url
      enable_2025_state_metrics                                  = var.enable_2025_state_metrics
      usbank_client_oauth2_url                                   = var.usbank_client_oauth2_url
      usbank_client_oauth2_client_id                             = data.aws_ssm_parameter.usbank_oauth2_client_id.value
      oauth2_kms_asymmetric_key_alias                            = data.aws_kms_alias.oauth2_kms_asymmetric_key_alias.name
      access_token_expiration_minutes                            = "30"
      refresh_token_expiration_minutes                           = "240"
      api_domain                                                 = "https://${lookup(module.constants.api_domains, var.environment_name)}"
      enable_delayed_submission_of_modifications                 = var.enable_delayed_submission_of_modifications
    }
  )

  cpu                      = "2048"
  memory                   = module.constants.api_ram_size
  requires_compatibilities = ["FARGATE"]
  network_mode             = "awsvpc"
}

##################################################################################################
### PDF API Service
##################################################################################################

# Previously, PDF API would only run when the 1099 ECS Task was ran (infra/ecs-tasks/tasks_1099.tf)
# Now, it will run as a persistent service to handle the "User Not Found" portal flow
# mirroring the API Service while utilizing the same resources

data "aws_ecr_repository" "pdf_api" {
  name = "pfml-pdf-api"
}

resource "aws_ecs_service" "pdf_api" {
  name                              = "pfml-pdf-api-${var.environment_name}"
  task_definition                   = aws_ecs_task_definition.pdf_api.arn
  cluster                           = var.service_ecs_cluster_arn
  launch_type                       = "FARGATE"
  platform_version                  = "1.4.0"
  desired_count                     = var.pdf_api_service_app_count
  health_check_grace_period_seconds = 300
  availability_zone_rebalancing     = var.environment_name == "prod" ? "DISABLED" : "ENABLED"
  lifecycle {
    ignore_changes = [desired_count]
  }
  network_configuration {
    assign_public_ip = false
    subnets          = data.aws_subnet.app.*.id
    security_groups  = [aws_security_group.app.id]
  }
  load_balancer {
    target_group_arn = aws_lb_target_group.pdf_api_target_group.id
    container_name   = "pfml-pdf-api"
    container_port   = 5000
  }
  depends_on = [
    aws_lb_listener.pdf_api_listener,
    aws_iam_role_policy.task_executor,
  ]
}

resource "aws_ecs_task_definition" "pdf_api" {
  family                   = "pfml-pdf-api-${var.environment_name}-server"
  execution_role_arn       = aws_iam_role.task_executor.arn
  task_role_arn            = aws_iam_role.api_service.arn
  cpu                      = "2048"
  memory                   = module.constants.api_ram_size
  requires_compatibilities = ["FARGATE"]
  network_mode             = "awsvpc"
  container_definitions = jsonencode([
    {
      name                   = "pfml-pdf-api",
      image                  = format("%s:%s", data.aws_ecr_repository.pdf_api.repository_url, var.service_docker_tag),
      command                = ["dotnet", "PfmlPdfApi.dll"],
      cpu                    = 2048,
      memory                 = module.constants.api_ram_size,
      networkMode            = "awsvpc",
      essential              = true,
      readonlyRootFilesystem = false, # False by default; some tasks write local files.
      linuxParameters = {
        capabilities = {
          drop = ["ALL"]
        },
        initProcessEnabled = true
      },
      portMappings = [
        {
          containerPort = 5000,
          hostPort      = 5000
        }
      ],
      logConfiguration = {
        logDriver = "awslogs",
        options = {
          "awslogs-group"         = aws_cloudwatch_log_group.pdf_api_service_logs.name,
          "awslogs-region"        = data.aws_region.current.name,
          "awslogs-stream-prefix" = var.environment_name
        }
      },

      environment = [
        { name : "ASPNETCORE_ENVIRONMENT", value : module.constants.env_var_mappings[var.environment_name] },
        { name : "AWS_DEFAULT_REGION", value : data.aws_region.current.name }
      ]
      secrets = []
    }
  ])
}
