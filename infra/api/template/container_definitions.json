[
  {
    "name": "${app_name}",
    "image": "${docker_image}",
    "cpu": ${cpu},
    "memory": ${memory},
    "networkMode": "awsvpc",
    "essential": true,
    "readonlyRootFilesystem": false,
    "portMappings": [
      {
        "containerPort": 1550,
        "hostPort": 1550
      }
    ],
    "linuxParameters": {
      "capabilities": {
        "drop": ["ALL"]
      },
      "initProcessEnabled": true
    },
    "logConfiguration": {
      "logDriver": "awslogs",
      "options": {
        "awslogs-group": "${cloudwatch_logs_group_name}",
        "awslogs-region": "${aws_region}",
        "awslogs-stream-prefix": "${environment_name}"
      }
    },
    "environment": [
      { "name": "ENVIRONMENT", "value": "${environment_name}" },
      { "name": "AWS_DEFAULT_REGION", "value": "${aws_region}"},
      { "name": "ENABLE_FULL_ERROR_LOGS", "value": "${enable_full_error_logs}" },
      { "name": "DB_HOST", "value": "${db_host}" },
      { "name": "DB_NAME", "value": "${db_name}" },
      { "name": "DB_USERNAME", "value": "${db_username}" },
      { "name": "CORS_ORIGINS", "value": "${cors_origins}" },
      { "name": "CORS_ORIGIN_REGEX", "value": "${cors_origin_regex}" },
      { "name": "LOGGING_LEVEL", "value": "${logging_level}" },
      { "name": "RMV_CLIENT_BASE_URL", "value": "${rmv_client_base_url}" },
      { "name": "USBANK_CLIENT_BASE_URL", "value": "${usbank_client_base_url}" },
      { "name": "RMV_CLIENT_CERTIFICATE_BINARY_ARN", "value": "${rmv_client_certificate_binary_arn}" },
      { "name": "USBANK_CLIENT_CERTIFICATE_BINARY_ARN", "value": "${usbank_client_certificate_binary_arn}" },
      { "name": "RMV_API_BEHAVIOR", "value": "${rmv_api_behavior}" },
      { "name": "RMV_CHECK_MOCK_SUCCESS", "value": "${rmv_check_mock_success}" },
      { "name": "FINEOS_CLIENT_CUSTOMER_API_URL", "value": "${fineos_client_customer_api_url}" },
      { "name": "FINEOS_CLIENT_INTEGRATION_SERVICES_API_URL", "value": "${fineos_client_integration_services_api_url}" },
      { "name": "FINEOS_CLIENT_GROUP_CLIENT_API_URL", "value": "${fineos_client_group_client_api_url}" },
      { "name": "FINEOS_CLIENT_WSCOMPOSER_API_URL", "value": "${fineos_client_wscomposer_api_url}" },
      { "name": "FINEOS_CLIENT_WSCOMPOSER_USER_ID", "value": "${fineos_client_wscomposer_user_id}" },
      { "name": "FINEOS_CLIENT_SOAP_USER_ID", "value": "${fineos_client_soap_user_id}" },
      { "name": "FINEOS_CLIENT_OAUTH2_URL", "value": "${fineos_client_oauth2_url}" },
      { "name": "FINEOS_CLIENT_OAUTH2_CLIENT_ID", "value": "${fineos_client_oauth2_client_id}" },
      { "name": "FINEOS_V21_UPGRADE_DATE", "value": "${fineos_v21_upgrade_date}" },
      { "name": "FINEOS_IS_RUNNING_V24", "value": "${fineos_is_running_v24}" },
      { "name": "ENABLE_APPLICATION_FRAUD_CHECK", "value": "${enable_application_fraud_check}" },
      { "name": "ENABLE_DOCUMENT_MANAGEMENT_OPTIONAL", "value": "${enable_document_upload_optional}" },
      { "name": "PFML_EMAIL_ADDRESS", "value": "${pfml_email_address}" },
      { "name": "BOUNCE_FORWARDING_EMAIL_ADDRESS", "value": "${bounce_forwarding_email_address}" },
      { "name": "BOUNCE_FORWARDING_EMAIL_ADDRESS_ARN", "value": "${bounce_forwarding_email_address_arn}" },
      { "name": "SERVICE_NOW_BASE_URL", "value": "${service_now_base_url}" },
      { "name": "PORTAL_BASE_URL", "value": "${portal_base_url}" },
      { "name": "ADMIN_PORTAL_BASE_URL", "value": "${admin_portal_base_url}" },
      { "name": "AZURE_AD_AUTHORITY_DOMAIN", "value": "${azure_ad_authority_domain}" },
      { "name": "AZURE_AD_CLIENT_ID", "value": "${azure_ad_client_id}" },
      { "name": "AZURE_AD_PARENT_GROUP", "value": "${azure_ad_parent_group}" },
      { "name": "AZURE_AD_TENANT_ID", "value": "${azure_ad_tenant_id}" },
      { "name": "RELEASE_VERSION", "value": "${release_version}" },
      { "name": "FEATURES_FILE_PATH", "value": "s3://massgov-pfml-${environment_name}-feature-gate/features.yaml"},
      { "name": "NEW_PLAN_PROOFS_ACTIVE_AT", "value": "${new_plan_proofs_active_at}" },
      { "name": "ENABLE_DOCUMENT_MULTIPART_UPLOAD", "value": "${enable_document_multipart_upload}" },
      { "name": "LIMIT_SSN_FEIN_MAX_ATTEMPTS", "value": "${limit_ssn_fein_max_attempts}" },
      { "name": "CHANNEL_SWITCH_UNSUPPORTED_CLAIMS", "value": "${channel_switch_unsupported_claims}" },
      { "name": "PDF_API_HOST", "value": "${pdf_api_host}"},
      { "name": "GENERATE_1099_MAX_FILES", "value": "${generate_1099_max_files}" },
      { "name": "UPLOAD_MAX_FILES_TO_FINEOS", "value": "${upload_max_files_to_fineos}" },
      { "name": "TEST_FILE_GENERATION_1099", "value": "${enable_1099_testfile_generation}" },
      { "name": "IRS_1099_CORRECTION_IND", "value": "${irs_1099_correction_ind}" },
      { "name": "IRS_1099_TAX_YEAR", "value": "${irs_1099_tax_year}" },
      { "name": "ENABLE_VERIFICATION_LIMITS", "value": "${enable_verification_limits}" },
      { "name": "CLAIM_STATUS_V2_EMPLOYER_REVIEW", "value": "${claim_status_v2_employer_review}" },
      { "name": "IMAGE_ACCESS_ENABLE_DOCUMENT_UPLOAD", "value": "${enable_image_access_document_upload}" },
      { "name": "ENABLE_EMAIL_CHANGE", "value": "${enable_email_change}" },
      { "name": "ENABLE_CHILD_SUPPORT_AUTOMATION", "value": "${enable_child_support_automation}" },
      { "name": "EXPERIAN_AUTH_TOKEN", "value": "${experian_auth_token}" },
      { "name": "ENABLE_SERVICE_AGREEMENT_VERSIONS_FOR_EXISTING_AUTOMATIONS", "value": "${enable_service_agreement_versions_for_existing_automations}"},
      { "name": "ENABLE_OFFSET_GET_1099", "value": "${enable_offset_get_1099}" },
      { "name": "UPLOAD_1099_DOC_BATCH_START", "value": "${upload_1099_doc_batch_start}" },
      { "name": "UPLOAD_1099_DOC_BATCH_END", "value": "${upload_1099_doc_batch_end }" },
      { "name": "ENABLE_SYSTEM_MESSAGE_TRANSLATION", "value": "${enable_system_message_translation}" },
      { "name": "LMG_PERSONAL_BASE_URL", "value": "${lmg_personal_base_url}" },
      { "name": "LMG_PERSONAL_APPLICATION_ID", "value": "${lmg_personal_application_id}" },
      { "name": "LMG_PERSONAL_POLICY_SISU", "value": "${lmg_personal_policy_sisu}" },
      { "name": "LMG_PERSONAL_POLICY_CHANGE_EMAIL", "value": "${lmg_personal_policy_change_email}" },
      { "name": "LMG_PERSONAL_POLICY_CHANGE_MFA", "value": "${lmg_personal_policy_change_mfa}" },
      { "name": "LMG_PERSONAL_POLICY_CHANGE_NAME", "value": "${lmg_personal_policy_change_name}" },
      { "name": "LMG_PERSONAL_POLICY_CHANGE_PW", "value": "${lmg_personal_policy_change_pw}" },
      { "name": "LMG_BUSINESS_BASE_URL", "value": "${lmg_business_base_url}" },
      { "name": "LMG_BUSINESS_APPLICATION_ID", "value": "${lmg_business_application_id}" },
      { "name": "LMG_BUSINESS_POLICY_SISU", "value": "${lmg_business_policy_sisu}" },
      { "name": "LMG_BUSINESS_POLICY_CHANGE_EMAIL", "value": "${lmg_business_policy_change_email}" },
      { "name": "LMG_BUSINESS_POLICY_CHANGE_MFA", "value": "${lmg_business_policy_change_mfa}" },
      { "name": "LMG_BUSINESS_POLICY_CHANGE_NAME", "value": "${lmg_business_policy_change_name}" },
      { "name": "LMG_BUSINESS_POLICY_CHANGE_PW", "value": "${lmg_business_policy_change_pw}" },
      { "name": "ENABLE_FINEOS_API_LOGGING_TO_DB", "value": "${enable_fineos_api_logging_to_db}"},
      { "name": "ENABLE_PREPAID_IMPACT_PAYMENTS", "value": "${enable_prepaid_impact_payments}"},
      { "name": "ENABLE_SYNC_PAYMENT_PREFERENCE", "value": "${enable_sync_payment_preference}"},
      { "name": "MYMASSGOV_API_PERSONAL_APPLICATION_ID_URI", "value": "${mymassgov_api_personal_application_id_uri }"},
      { "name": "MYMASSGOV_API_BUSINESS_APPLICATION_ID_URI", "value": "${mymassgov_api_business_application_id_uri}"},
      { "name": "MY_MASS_GOV_API_CLIENT_BASE_URL", "value": "${my_mass_gov_api_client_base_url}"},
      { "name": "ENABLE_2025_STATE_METRICS", "value": "${enable_2025_state_metrics}"},
      { "name": "USBANK_CLIENT_OAUTH2_URL", "value": "${usbank_client_oauth2_url}" },
      { "name": "OAUTH2_KMS_ASYMMETRIC_KEY_ALIAS", "value": "${oauth2_kms_asymmetric_key_alias}" },
      { "name": "ENABLE_MARK_APPLICATIONS_READY_FOR_REVIEW", "value": "${enable_mark_applications_ready_for_review}" },
      { "name": "ENABLE_RE_NOTIFICATION", "value": "${enable_re_notification}" },
      { "name": "ACCESS_TOKEN_EXPIRATION_MINUTES", "value": "${access_token_expiration_minutes}" },
      { "name": "REFRESH_TOKEN_EXPIRATION_MINUTES", "value": "${refresh_token_expiration_minutes}" },
      { "name": "API_DOMAIN", "value": "${api_domain}" },
      { "name": "ENABLE_DELAYED_SUBMISSION_OF_MODIFICATIONS", "value": "${enable_delayed_submission_of_modifications}" }
    ],
    "secrets": [
      { "name": "NEW_RELIC_LICENSE_KEY", "valueFrom": "/service/${app_name}/common/newrelic-license-key"},
      { "name": "RMV_CLIENT_CERTIFICATE_PASSWORD", "valueFrom": "/service/${app_name}/${environment_name}/rmv_client_certificate_password" },
      { "name": "USBANK_CLIENT_CERTIFICATE_PASSWORD", "valueFrom": "/service/${app_name}/${environment_name}/usbank_client_certificate_password" },
      { "name": "FINEOS_CLIENT_SOAP_PASSWORD", "valueFrom": "/service/${app_name}/${environment_name}/fineos_client_soap_password" },
      { "name": "FINEOS_CLIENT_OAUTH2_CLIENT_SECRET", "valueFrom": "/service/${app_name}/${environment_name}/fineos_oauth2_client_secret" },
      { "name": "SERVICE_NOW_USERNAME", "valueFrom": "/service/${app_name}/${environment_name}/service_now_username" },
      { "name": "SERVICE_NOW_PASSWORD", "valueFrom": "/service/${app_name}/${environment_name}/service_now_password" },
      { "name": "AZURE_AD_CLIENT_SECRET", "valueFrom": "/service/${app_name}/${environment_name}/azure_ad_client_secret" },
      { "name": "DASHBOARD_PASSWORD", "valueFrom": "/service/${app_name}/${environment_name}/dashboard_password" },
      { "name": "LMG_PERSONAL_OAUTH_CLIENT_SECRET", "valueFrom": "/service/${app_name}/${idp_ssm_env}/lmg_personal/${lmg_ssm_mfa_state}/client_secret" },
      { "name": "LMG_BUSINESS_OAUTH_CLIENT_SECRET", "valueFrom": "/service/${app_name}/${idp_ssm_env}/lmg_business/${lmg_ssm_mfa_state}/client_secret" },
      { "name": "USBANK_CLIENT_OAUTH2_CLIENT_SECRET", "valueFrom": "/service/${app_name}/${environment_name}/usbank_oauth2_client_secret" }
    ]
  }
]
