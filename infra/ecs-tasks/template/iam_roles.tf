# IAM roles for ECS tasks.
### IAM Role Pattern, managed poilicies and inline policies
### Review documentation before updating roles
### https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/iam_role
############################################
# resource "aws_iam_role" "db_user_pfml_batch" {
#   name               = "${local.app_name}-${var.environment_name}-db-user-pfml-batch-role-mbaker-test"
#   assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
#   managed_policy_arns = [
#     "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole",
#     aws_iam_policy.db_user_pfml_batch.arn
#   ]
#   inline_policy {
#     name   = "AllowRDSAccess"
#     policy = data.aws_iam_policy_document.db_user_pfml_batch.json
#   }
#   # this will be used for fineos iam role after moving to inline policies
#   dynamic "inline_policy" {
#     for_each = var.fineos_aws_iam_role_arn != "" ? [1] : []
#     content {
#       policy = data.aws_iam_policy_document.assume_fineos_iam_policy_doc[0].json
#     }
#   }
# }


# Task runtime role. This role is used by the task after it has started up -- i.e. for application-level access to AWS resources.
resource "aws_iam_role" "ecs_tasks" {
  name               = "${local.app_name}-${var.environment_name}-ecs-tasks"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  inline_policy {}
}

resource "aws_iam_role" "db_user_pfml_batch" {
  name               = "${local.app_name}-${var.environment_name}-db-user-pfml-batch-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  inline_policy {}
}

resource "aws_iam_policy" "db_user_pfml_batch" {
  name        = "${local.app_name}-${var.environment_name}-db-user-pfml-batch"
  description = "Allow Access To RDS using pfm admin user"
  policy      = data.aws_iam_policy_document.db_user_pfml_batch.json
}

resource "aws_iam_role" "db_user_pfml_admin" {
  name               = "${local.app_name}-${var.environment_name}-db-user-pfml-admin-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_admin.arn
  ]
  inline_policy {}
}

resource "aws_iam_policy" "db_user_pfml_admin" {
  name        = "${local.app_name}-${var.environment_name}-db-user-pfml-admin"
  description = "Allow Access To RDS using pfm admin user"
  policy      = data.aws_iam_policy_document.db_user_pfml_admin.json
}

resource "aws_iam_policy" "db_user_pfml_migrate" {
  name        = "${local.app_name}-${var.environment_name}-db-user-pfml-migrate"
  description = "Allow Access To RDS using pfml_migrate user"
  policy      = data.aws_iam_policy_document.db_user_pfml_migrate.json
}

resource "aws_iam_role" "db_user_pfml_migrate" {
  name               = "${local.app_name}-${var.environment_name}-db-user-pfml-migrate-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_migrate.arn
  ]
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-dfml-db-migration-send-ses"
    policy = data.aws_iam_policy_document.dfml_db_migration_send_ses.json
  }
}

# Task execution role. This role is used by ECS during startup to pull container images from
# ECR and access secrets from Systems Manager Parameter Store before running the application.
#
# See: https://docs.aws.amazon.com/AmazonECS/latest/developerguide/task_execution_IAM_role.html
resource "aws_iam_role" "task_executor" {
  name               = "${local.app_name}-${var.environment_name}-ecs-tasks-execution-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-ecs-tasks-execution-role"
    policy = data.aws_iam_policy_document.task_executor.json
  }
}

resource "aws_iam_role" "task_execute_sql_task_role_batch_user" {
  name               = "${local.app_name}-${var.environment_name}-ecs-tasks-execute-sql-task-batch-user-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-ecs-tasks-execute-sql-s3"
    policy = data.aws_iam_policy_document.task_sql_export_s3_policy_doc.json
  }
}

# ------------------------------------------------------------------------------------------------------
# Register Leave Admins with FINEOS
# ------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "register_admins_task_role" {
  name               = "${local.app_name}-${var.environment_name}-ecs-tasks-register-admins-task-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    # aws_iam_policy.register_admins_task_policy.arn,
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-ecs-tasks-register-admins-task"
    policy = data.aws_iam_policy_document.register_admins_task_role_policy_document.json
  }
}

# ------------------------------------------------------------------------------------------------------
# DOR Import task stuff
# ------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "dor_import_task_role" {
  name               = "${local.app_name}-${var.environment_name}-ecs-tasks-dor-import-task-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-ecs-tasks-dor-import-ecs"
    policy = data.aws_iam_policy_document.dor_import_task_role_extras.json
  }
}

# - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
resource "aws_iam_role" "dor_import_execution_role" {
  name               = "${local.app_name}-${var.environment_name}-ecs-tasks-dor-import-execution-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.dor_import_execution_role_extras.arn
  ]
  inline_policy {}
}

resource "aws_iam_policy" "dor_import_execution_role_extras" {
  name        = "${local.app_name}-${var.environment_name}-ecs-tasks-dor-import-execution"
  description = "A clone of the standard execution role with extra SSM permissions for DOR Import's decryption keys."
  policy      = data.aws_iam_policy_document.dor_import_execution_role_extras.json
}

# ----------------------------------------------------------------------------------------------------------------------
# FINEOS Updates Role
# ----------------------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "fineos_updates_task_role" {
  name               = "${local.app_name}-${var.environment_name}-ecs-tasks-fineos-updates"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  # We may not always have a value for `fineos_aws_iam_role_arn`
  dynamic "inline_policy" {
    for_each = var.fineos_aws_iam_role_arn != "" ? [1] : []
    content {
      name   = "${local.app_name}-${var.environment_name}-ecs-tasks-fineos-eligibility-feed-export-fineos"
      policy = data.aws_iam_policy_document.assume_fineos_iam_policy_doc[0].json
    }
  }
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-ecs-tasks-fineos-update-list-bucket"
    policy = data.aws_iam_policy_document.pub_payments_s3_allow_list_bucket.json
  }
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-ecs-tasks-fineos-updates"
    policy = data.aws_iam_policy_document.agency_transfer_and_reports_s3_bucket_dupe_7.json
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# fineos-eligibility-feed-export
# ----------------------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "fineos_eligibility_feed_export_task_role" {
  name               = "${local.app_name}-${var.environment_name}-fineos-eligibility-feed-export-task-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  dynamic "inline_policy" {
    for_each = var.fineos_aws_iam_role_arn != "" ? [1] : []
    content {
      name   = "${local.app_name}-${var.environment_name}-ecs-tasks-fineos-eligibility-feed-export-fineos"
      policy = data.aws_iam_policy_document.assume_fineos_iam_policy_doc[0].json
    }
  }
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-fineos-eligibility-feed-export-task-role"
    policy = data.aws_iam_policy_document.fineos_eligibility_feed_export.json
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# pub-payments-process-fineos
# ----------------------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "pub_payments_process_fineos_task_role" {
  name               = "${local.app_name}-${var.environment_name}-ecs-tasks-pub-payments-process-fineos"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  # We may not always have a value for `fineos_aws_iam_role_arn`
  dynamic "inline_policy" {
    for_each = var.fineos_aws_iam_role_arn != "" ? [1] : []
    content {
      name   = "${local.app_name}-${var.environment_name}-ecs-tasks-pub-payments-process-fineos-assume"
      policy = data.aws_iam_policy_document.assume_fineos_iam_policy_doc[0].json
    }
  }
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-ecs-tasks-pub-payments-list-bucket"
    policy = data.aws_iam_policy_document.pub_payments_s3_allow_list_bucket.json
  }
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-ecs-tasks-pub-payments-process-fineos"
    policy = data.aws_iam_policy_document.agency_transfer_and_reports_s3_bucket_dupe_3.json
  }
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-pub-payments-send-ses"
    policy = data.aws_iam_policy_document.pub_payments_send_ses.json
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# pub-payments-create-pub-files
# (Use default task_executor execution role)
# ----------------------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "pub_payments_create_pub_files_task_role" {
  name               = "${local.app_name}-${var.environment_name}-pub-payments-create-pub-files"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  # We may not always have a value for `fineos_aws_iam_role_arn`
  dynamic "inline_policy" {
    for_each = var.fineos_aws_iam_role_arn != "" ? [1] : []
    content {
      name   = "${local.app_name}-${var.environment_name}-ecs-tasks-pub-payments-create-pub-files-fineos-assume"
      policy = data.aws_iam_policy_document.assume_fineos_iam_policy_doc[0].json
    }
  }
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-pub-payments-list-bucket"
    policy = data.aws_iam_policy_document.pub_payments_s3_allow_list_bucket.json
  }
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-pub-payments-create-pub-files"
    policy = data.aws_iam_policy_document.agency_transfer_and_reports_s3_bucket_dupe_1.json
  }
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-pub-payments-send-ses"
    policy = data.aws_iam_policy_document.pub_payments_send_ses.json
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# pub-payments-process-pub-returns
# (Use default task_executor execution role)
# ----------------------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "pub_payments_process_pub_returns_task_role" {
  name               = "${local.app_name}-${var.environment_name}-pub-payments-process-pub-returns"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  # We may not always have a value for `fineos_aws_iam_role_arn`
  dynamic "inline_policy" {
    for_each = var.fineos_aws_iam_role_arn != "" ? [1] : []
    content {
      name   = "${local.app_name}-${var.environment_name}-ecs-tasks-pub-payments-process-pub-returns-fineos-assume"
      policy = data.aws_iam_policy_document.assume_fineos_iam_policy_doc[0].json
    }
  }
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-pub-payments-list-bucket"
    policy = data.aws_iam_policy_document.pub_payments_s3_allow_list_bucket.json
  }
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-pub-payments-process-pub-returns"
    policy = data.aws_iam_policy_document.agency_transfer_and_reports_s3_bucket_dupe_2.json
  }
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-pub-payments-send-ses"
    policy = data.aws_iam_policy_document.pub_payments_send_ses.json
  }
}

#####
resource "aws_iam_role" "pub_payments_copy_audit_report_task_role" {
  name               = "${local.app_name}-${var.environment_name}-pub-payments-copy-audit-report"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-pub-payments-copy-audit-report-list-bucket"
    policy = data.aws_iam_policy_document.pub_payments_s3_allow_list_bucket.json
  }
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-pub-payments-copy-audit-report"
    policy = data.aws_iam_policy_document.agency_transfer_and_reports_s3_bucket_dupe_4.json
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# mock-fineos-writeback-status-for-payments
# ----------------------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "mock_fineos_writeback_status_for_payments_role" {
  name               = "${local.app_name}-${var.environment_name}-mock-fineos-writeback-status-for-payments"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  # We may not always have a value for `fineos_aws_iam_role_arn`
  dynamic "inline_policy" {
    for_each = var.fineos_aws_iam_role_arn != "" ? [1] : []
    content {
      name   = "${local.app_name}-${var.environment_name}-mock-fineos-writeback-status-for-payments-fineos-assume"
      policy = data.aws_iam_policy_document.assume_fineos_iam_policy_doc[0].json
    }
  }
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-mock-fineos-writeback-status-for-payments-processing-list-bucket"
    policy = data.aws_iam_policy_document.pub_payments_s3_allow_list_bucket.json
  }
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-mock-fineos-writeback-status-for-payments-processing"
    policy = data.aws_iam_policy_document.agency_transfer_and_reports_s3_bucket_dupe_5.json
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# pub-payments-process-1099-documents
# ----------------------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "pub_payments_process_1099_task_role" {
  name               = "${local.app_name}-${var.environment_name}-ecs-tasks-pub-payments-process-1099"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  # We may not always have a value for `fineos_aws_iam_role_arn`
  dynamic "inline_policy" {
    for_each = var.fineos_aws_iam_role_arn != "" ? [1] : []
    content {
      name   = "${local.app_name}-${var.environment_name}-ecs-tasks-pub-payments-process-1099-fineos-assume"
      policy = data.aws_iam_policy_document.assume_fineos_iam_policy_doc[0].json
    }
  }
  dynamic "inline_policy" {
    for_each = var.fineos_aws_iam_role_arn != "" ? [1] : []
    content {
      name   = "${local.app_name}-${var.environment_name}-ecs-tasks-pub-payments-process-1099-s3_access"
      policy = data.aws_iam_policy_document.pub_payments_process_1099_role_s3_access_policy.json
    }
  }
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-ecs-tasks-pub-payments-process-1099-list-bucket"
    policy = data.aws_iam_policy_document.pub_payments_s3_allow_list_bucket.json
  }
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-ecs-tasks-pub-payments-process-1099"
    policy = data.aws_iam_policy_document.agency_transfer_and_reports_s3_bucket_dupe_6.json
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# fineos-import-iaww
# ----------------------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "fineos_import_iaww_task_role" {
  name               = "${local.app_name}-${var.environment_name}-ecs-tasks-fineos-import-iaww"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  # We may not always have a value for `fineos_aws_iam_role_arn`
  dynamic "inline_policy" {
    for_each = var.fineos_aws_iam_role_arn != "" ? [1] : []
    content {
      name   = "${local.app_name}-${var.environment_name}-ecs-tasks-fineos-import-iaww-fineos-assume"
      policy = data.aws_iam_policy_document.assume_fineos_iam_policy_doc[0].json
    }
  }
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-ecs-tasks-pub-payments-list-bucket"
    policy = data.aws_iam_policy_document.pub_payments_s3_allow_list_bucket.json
  }
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-ecs-tasks-pub-payments-process-snapshot"
    policy = data.aws_iam_policy_document.agency_transfer_and_reports_s3_bucket.json
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# reductions-workflow
# ----------------------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "reductions_workflow_task_role" {
  name               = "${local.app_name}-${var.environment_name}-ecs-tasks-reductions-workflow"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-ecs-tasks-reductions-workflow"
    policy = data.aws_iam_policy_document.reductions_workflow_task_role_extras.json
  }
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-ecs-tasks-pub-payments-process-fineos"
    policy = data.aws_iam_policy_document.agency_transfer_and_reports_s3_bucket_dupe_3.json
  }
}

resource "aws_iam_role" "reductions_workflow_execution_role" {
  name               = "${local.app_name}-${var.environment_name}-ecs-tasks-reductions-wrkflw-execution-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.reductions_workflow_execution_role_extras.arn
  ]
  inline_policy {}
}

resource "aws_iam_policy" "reductions_workflow_execution_role_extras" {
  name        = "${local.app_name}-${var.environment_name}-ecs-tasks-reductions-workflow-execution"
  description = "A clone of the standard execution role with extra SSM permissions for Reductions Workflow decryption keys."
  policy      = data.aws_iam_policy_document.reductions_workflow_execution_role_extras.json
}

# ----------------------------------------------------------------------------------------------------------------------
# S3 buckets for business intelligence (BI) data extracts
# ----------------------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "fineos_bucket_tool_role" {
  name               = "${local.app_name}-${var.environment_name}-ecs-tasks-fineos-bucket-tool"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  # We may not always have a value for `fineos_aws_iam_role_arn`
  dynamic "inline_policy" {
    for_each = var.fineos_aws_iam_role_arn != "" ? [1] : []
    content {
      name   = "${local.app_name}-${var.environment_name}-ecs-tasks-fineos-bucket-tool-assume-role"
      policy = data.aws_iam_policy_document.assume_fineos_iam_policy_doc[0].json
    }
  }
  dynamic "inline_policy" {
    for_each = var.fineos_aws_iam_role_arn != "" ? [1] : []
    content {
      name   = "${local.app_name}-${var.environment_name}-ecs-tasks-fineos-bucket-tool-role"
      policy = data.aws_iam_policy_document.fineos_bucket_tool_task_policy_document.json
    }
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# pub-process-weekly-reports
# (Use default task_executor execution role)
# ----------------------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "pub_process_weekly_reports_task_role" {
  name               = "${local.app_name}-${var.environment_name}-ecs-tasks-pub-process-weekly-reports"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  # We may not always have a value for `fineos_aws_iam_role_arn`
  dynamic "inline_policy" {
    for_each = var.fineos_aws_iam_role_arn != "" ? [1] : []
    content {
      name   = "${local.app_name}-${var.environment_name}-ecs-tasks-pub-process-weekly-reports-fineos-assume"
      policy = data.aws_iam_policy_document.assume_fineos_iam_policy_doc[0].json
    }
  }
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-ecs-tasks-pub-process-list-bucket"
    policy = data.aws_iam_policy_document.pub_payments_s3_allow_list_bucket.json
  }
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-ecs-tasks-pub-process-weekly-reports"
    policy = data.aws_iam_policy_document.agency_transfer_and_reports_s3_bucket_dupe_7.json
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# child-support-dor-import
# (Use default task_executor execution role)
# ----------------------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "child_support_dor_import_task_role" {
  name               = "${local.app_name}-${var.environment_name}-ecs-tasks-child-support-dor-import"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  # We may not always have a value for `fineos_aws_iam_role_arn`
  dynamic "inline_policy" {
    for_each = var.fineos_aws_iam_role_arn != "" ? [1] : []
    content {
      name   = "${local.app_name}-${var.environment_name}-ecs-tasks-child-support-dor-import-fineos-assume"
      policy = data.aws_iam_policy_document.assume_fineos_iam_policy_doc[0].json
    }
  }
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-ecs-tasks-agency-transfer-full-access"
    policy = data.aws_iam_policy_document.agency_transfer_and_reports_s3_bucket_dupe_8.json
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# S3 buckets for SFTP (agency transfer) tool
# ----------------------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "sftp_tool_role" {
  name               = "${local.app_name}-${var.environment_name}-ecs-tasks-sftp-tool"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-ecs-tasks-sftp-tool-role"
    policy = data.aws_iam_policy_document.agency_transfer_and_reports_s3_bucket_dupe_8.json
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# cps-errors-crawler
# ----------------------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "cps_errors_crawler_task_role" {
  name               = "${local.app_name}-${var.environment_name}-cps-errors-crawler-task-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-cps-errors-crawler-execution-role"
    policy = data.aws_iam_policy_document.cps_errors_crawler_role_policy_document.json
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# update-gender-data-from-rmv
# ----------------------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "update_gender_data_from_rmv_task_role" {
  name               = "${local.app_name}-${var.environment_name}-update-gender-data-from-rmv-task-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-update-gender-data-from-rmv-role"
    policy = data.aws_iam_policy_document.update_gender_data_from_rmv.json
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# process-prepaid-debit-registration
# ----------------------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "process_prepaid_debit_registration_task_role" {
  name               = "${local.app_name}-${var.environment_name}-process-prepaid-debit-reg-task-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-process-prepaid-debit-registration-role"
    policy = data.aws_iam_policy_document.process_prepaid_debit_registration.json
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# appeals-import-extract
# ----------------------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "appeals_import_extract_task_role" {
  name               = "${local.app_name}-${var.environment_name}-appeals-import-extract-task-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  dynamic "inline_policy" {
    for_each = var.fineos_aws_iam_role_arn != "" ? [1] : []
    content {
      name   = "${local.app_name}-${var.environment_name}-ecs-tasks-fineos-eligibility-feed-export-fineos"
      policy = data.aws_iam_policy_document.assume_fineos_iam_policy_doc[0].json
    }
  }
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-appeals-import-extract-task-role"
    policy = data.aws_iam_policy_document.appeals_import_extract.json
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# overpayments-backfill-data
# ----------------------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "overpayments_backfill_data_task_role" {
  name               = "${local.app_name}-${var.environment_name}-ecs-tasks-overpayments-backfill-data"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-ecs-tasks-overpayments-backfill-data"
    policy = data.aws_iam_policy_document.overpayments_backfill_data_task_role_extras.json
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# dfml-fines-and-repayments
#  ----------------------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "dfml_fines_and_repayments_task_role" {
  name               = "${local.app_name}-${var.environment_name}-ecs-tasks-dfml-fines-and-repayments"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-ecs-tasks-dfml-fines-and-repayments"
    policy = data.aws_iam_policy_document.dfml_fines_and_repayments_task_role_extras.json
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# process-new-relic-dashboard-recovery
# ----------------------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "process_new_relic_dashboard_task_role" {
  name               = "${local.app_name}-${var.environment_name}-ecs-tasks-process-nr-dash-task-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-ecs-tasks-process-new-relic-dashboard-access"
    policy = data.aws_iam_policy_document.process_new_relic_dashboard_task_role.json
  }
}
resource "aws_iam_role" "process_new_relic_dashboard_task_exe_role" {
  name               = "${local.app_name}-${var.environment_name}-ecs-tasks-process-nr-dash-exe-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-ecs-tasks-process-new-relic-dashboard-access"
    policy = data.aws_iam_policy_document.process_new_relic_dashboard_exe_role.json
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# process-overpayment-referrals
# ----------------------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "process_overpayment_referrals_task_role" {
  name               = "${local.app_name}-${var.environment_name}-ecs-tasks-process-overpayment-task-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-ecs-tasks-process-overpayment-referrals"
    policy = data.aws_iam_policy_document.process_overpayment_referrals_extras.json
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# process-mmars-response-file
# ----------------------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "process_mmars_response_file_role" {
  name               = "${local.app_name}-${var.environment_name}-ecs-tasks-process-mmars-response-file-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
  managed_policy_arns = [
    aws_iam_policy.db_user_pfml_batch.arn
  ]
  inline_policy {
    name   = "${local.app_name}-${var.environment_name}-ecs-tasks-agency-transfer-full-access"
    policy = data.aws_iam_policy_document.agency_transfer_and_reports_s3_bucket_dupe_8.json
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# submit-deferred-items
# ----------------------------------------------------------------------------------------------------------------------
resource "aws_iam_role" "submit_deferred_items_task_role" {
  name               = "${local.app_name}-${var.environment_name}-ecs-tasks-submit-deferred-items-role"
  assume_role_policy = data.aws_iam_policy_document.ecs_tasks_assume_role_policy.json
}