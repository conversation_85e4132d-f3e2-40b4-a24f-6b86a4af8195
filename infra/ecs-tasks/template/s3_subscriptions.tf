# NOTE: There can only be one S3 bucket notification resource per bucket.
#
# This file sets up triggers to run ECS tasks based off of S3 events. However,
# this is not natively possible, so this actually happens in two steps:
#
# 1. A lambda is created using the s3_ecs_trigger module
# 2. S3 Bucket notifications are set up to trigger the lambda based off specified events
#
# The lambda will run the specified ECS task when triggered.
#

module "trigger_sharepoint_pub" {
  source = "./s3_ecs_trigger"

  environment_name   = var.environment_name
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  task_name                  = "pub-payments-create-pub-files"
  ecs_task_definition_family = aws_ecs_task_definition.ecs_tasks["pub-payments-create-pub-files"].family
  ecs_task_executor_role     = aws_iam_role.task_executor.arn
  ecs_task_role              = aws_iam_role.pub_payments_create_pub_files_task_role.arn
  s3_bucket_arn              = data.aws_s3_bucket.reports.arn
}

module "trigger_agency_transfer_pub" {
  source = "./s3_ecs_trigger"

  environment_name   = var.environment_name
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  task_name                  = "pub-payments-process-pub-returns"
  ecs_task_definition_family = aws_ecs_task_definition.ecs_tasks["pub-payments-process-pub-returns"].family
  ecs_task_executor_role     = aws_iam_role.task_executor.arn
  ecs_task_role              = aws_iam_role.pub_payments_process_pub_returns_task_role.arn
  s3_bucket_arn              = data.aws_s3_bucket.agency_transfer.arn
}

module "trigger_fineos_import_iaww" {
  source = "./s3_ecs_trigger"

  environment_name   = var.environment_name
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  task_name                  = "fineos-import-iaww"
  ecs_task_definition_family = aws_ecs_task_definition.ecs_tasks["fineos-import-iaww"].family
  ecs_task_executor_role     = aws_iam_role.task_executor.arn
  ecs_task_role              = aws_iam_role.fineos_import_iaww_task_role.arn
  s3_bucket_arn              = data.aws_s3_bucket.agency_transfer.arn
}

module "trigger_sync_fineos_extracts_to_pfml_models" {
  source = "./s3_ecs_trigger"

  environment_name   = var.environment_name
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  task_name                  = "sync-fineos-extracts-to-pfml-models"
  ecs_task_definition_family = aws_ecs_task_definition.ecs_tasks["sync-fineos-extracts-to-pfml-models"].family
  ecs_task_executor_role     = aws_iam_role.task_executor.arn
  ecs_task_role              = aws_iam_role.fineos_import_iaww_task_role.arn
  s3_bucket_arn              = data.aws_s3_bucket.agency_transfer.arn
}

module "trigger_dfml_fines_and_repayments" {
  source = "./s3_ecs_trigger"

  environment_name   = var.environment_name
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  task_name                  = "dfml-fines-and-repayments"
  ecs_task_definition_family = aws_ecs_task_definition.ecs_tasks["dfml-fines-and-repayments"].family
  ecs_task_executor_role     = aws_iam_role.task_executor.arn
  ecs_task_role              = aws_iam_role.dfml_fines_and_repayments_task_role.arn
  s3_bucket_arn              = data.aws_s3_bucket.agency_transfer.arn
}

module "trigger_submit_deferred_items" {
  source = "./s3_ecs_trigger"

  environment_name   = var.environment_name
  app_subnet_ids     = var.app_subnet_ids
  security_group_ids = [aws_security_group.tasks.id]

  task_name                  = "submit-deferred-items"
  ecs_task_definition_family = aws_ecs_task_definition.ecs_tasks["submit-deferred-items"].family
  ecs_task_executor_role     = aws_iam_role.task_executor.arn
  ecs_task_role              = aws_iam_role.submit_deferred_items_task_role.arn
  s3_bucket_arn              = data.aws_s3_bucket.agency_transfer.arn
}

# Be aware that the agency-transfer and reports buckets defined in infra/pfml-aws/s3.tf
# and infra/env-shared/template/s3.tf use the below lambda triggers and any additional S3 bucket
# notification resources defined outside this file will overwrite this configuration
# effectively breaking a number of ECS tasks.

resource "aws_s3_bucket_notification" "sharepoint_pub_notifications" {
  count  = var.enable_pub_automation_create_pub_files ? 1 : 0
  bucket = data.aws_s3_bucket.reports.id

  lambda_function {
    lambda_function_arn = module.trigger_sharepoint_pub.lambda_arn
    events              = ["s3:ObjectCreated:*"]
    filter_prefix       = "dfml-responses/Payment-Audit-Report"
  }
}

resource "aws_s3_bucket_notification" "agency_transfer_notifications" {
  count  = (var.enable_pub_automation_process_returns || var.enable_fineos_import_iaww || var.enable_sync_fineos_extracts_to_pfml_models || var.enable_fines_and_repayments_automation || var.enable_delayed_submission_of_modifications) ? 1 : 0
  bucket = data.aws_s3_bucket.agency_transfer.id

  dynamic "lambda_function" {
    for_each = var.enable_pub_automation_process_returns ? [1] : []
    content {
      lambda_function_arn = module.trigger_agency_transfer_pub.lambda_arn
      events              = ["s3:ObjectCreated:*"]
      filter_prefix       = "pub/inbound/"
      filter_suffix       = ".OK"
    }
  }

  dynamic "lambda_function" {
    for_each = var.enable_fineos_import_iaww ? [1] : []
    content {
      lambda_function_arn = module.trigger_fineos_import_iaww.lambda_arn
      events              = ["s3:ObjectCreated:*"]
      filter_prefix       = "reports/processed/"
      filter_suffix       = "pub-payments-process-fineos.SUCCESS"
    }
  }

  dynamic "lambda_function" {
    for_each = var.enable_sync_fineos_extracts_to_pfml_models ? [1] : []
    content {
      lambda_function_arn = module.trigger_sync_fineos_extracts_to_pfml_models.lambda_arn
      events              = ["s3:ObjectCreated:*"]
      filter_prefix       = "reports/processed/"
      filter_suffix       = "fineos-import-iaww.SUCCESS"
    }
  }


  dynamic "lambda_function" {
    for_each = var.enable_fines_and_repayments_automation ? [1] : []
    content {
      lambda_function_arn = module.trigger_dfml_fines_and_repayments.lambda_arn
      events              = ["s3:ObjectCreated:*"]
      filter_prefix       = "bofa/dfml-fines-and-repayment/received"
      filter_suffix       = ".pdf"
    }
  }

  dynamic "lambda_function" {
    for_each = var.enable_delayed_submission_of_modifications ? [1] : []
    content {
      lambda_function_arn = module.trigger_submit_deferred_items.lambda_arn
      events              = ["s3:ObjectCreated:*"]
      filter_prefix       = "reports/processed/"
      filter_suffix       = "sync-fineos-extracts-to-pfml-models.SUCCESS"
    }
  }
}