{
  "Comment": "DOR FINEOS ETL step function",
  "StartAt": "${first_step}",

  "States": {
    %{if first_step == "dor_generate"}
    "dor_generate": {
      "Comment": "Generate fake DOR data to S3 (non-prod)",
      "Type": "Task",
      "Resource": "arn:aws:states:::ecs:runTask.sync",
      "TimeoutSeconds": 28800,
      "Parameters": {
        "LaunchType": "FARGATE",
        "PlatformVersion": "1.4.0",
        "Cluster": "${cluster_arn}",
        "TaskDefinition": "pfml-api-${environment_name}-dor-import",
        "Overrides": {
          "ContainerOverrides": [
            {
              "Name": "dor-import",
              "Command": [
                "dor-generate",
                "--folder=s3://massgov-pfml-${environment_name}-agency-transfer/dor/received",
                "--count=1000"
              ]
            }
          ]
        },
        "NetworkConfiguration": {
          "AwsvpcConfiguration": {
            "SecurityGroups": ["${security_group}"],
            "Subnets": ["${subnet_1}", "${subnet_2}"],
            "AssignPublicIp": "DISABLED"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": ["States.TaskFailed"],
          "IntervalSeconds": 1,
          "MaxAttempts": 2,
          "BackoffRate": 2.0
        }
      ],
      "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "dor_generate_notify_failure"}],
      "Next": "fineos_import_employee_updates"
    },

    "dor_generate_notify_failure": {
      "Type": "Pass",
      "Result": {
        "task_name": "dor-generate",
        "subject": "Task 'dor-generate' failed in ${environment_name}"
      },
      "ResultPath": "$.task_failure_details",
      "Next": "failure_notification"
    },
    %{endif}

    "fineos_import_employee_updates": {
      "Type": "Task",
      "Comment": "FINEOS employee updates import to RDS",
      "Resource": "arn:aws:states:::ecs:runTask.sync",
      "TimeoutSeconds": 28800,
      "Parameters": {
        "LaunchType": "FARGATE",
        "PlatformVersion": "1.4.0",
        "Cluster": "${cluster_arn}",
        "TaskDefinition": "pfml-api-${environment_name}-fineos-import-employee-updates",
        "Overrides": {
          "ContainerOverrides": [
            {
              "Name": "fineos-import-employee-updates",
              "Environment": [
                  {
                    "Name": "SFN_EXECUTION_ID", "Value.$": "$$.Execution.Id"
                  }
                ]
            }
          ]
        },
        "NetworkConfiguration": {
          "AwsvpcConfiguration": {
            "SecurityGroups": ["${security_group}"],
            "Subnets": ["${subnet_1}", "${subnet_2}"],
            "AssignPublicIp": "DISABLED"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": ["States.TaskFailed"],
          "IntervalSeconds": 1,
          "MaxAttempts": 2,
          "BackoffRate": 2.0
        }
      ],
      "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "fineos_import_employee_updates_notify_failure"}],
      "Next": "dor_import"
    },

    "fineos_import_employee_updates_notify_failure": {
      "Type": "Pass",
      "Result": {
        "task_name": "fineos-import-employee-updates",
        "subject": "Task 'fineos-import-employee-updates' failed in ${environment_name}"
      },
      "ResultPath": "$.task_failure_details",
      "Next": "parallel_failure_notification"
    },

    "dor_import": {
      "Comment": "DOR import to RDS",
      "Type": "Task",
      "Resource": "arn:aws:states:::ecs:runTask.sync",
      "TimeoutSeconds": 10800,
      "Parameters": {
        "LaunchType": "FARGATE",
        "PlatformVersion": "1.4.0",
        "Cluster": "${cluster_arn}",
        "TaskDefinition": "pfml-api-${environment_name}-dor-import",
        "Overrides": {
          "ContainerOverrides": [
            {
              "Name": "dor-import",
              "Environment": [
                ${st_decrypt_value},
                { "Name": "SFN_EXECUTION_ID", "Value.$": "$$.Execution.Id" }
              ]
            }
          ]
        },
        "NetworkConfiguration": {
          "AwsvpcConfiguration": {
            "SecurityGroups": ["${security_group}"],
            "Subnets": ["${subnet_1}", "${subnet_2}"],
            "AssignPublicIp": "DISABLED"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": ["States.TaskFailed"],
          "IntervalSeconds": 1,
          "MaxAttempts": 2,
          "BackoffRate": 2.0
        }
      ],
      "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "dor_import_notify_failure"}],
      "Next": "dor_import_exempt"
    },

    "dor_import_exempt": {
      "Comment": "DOR import exempt to RDS",
      "Type": "Task",
      "Resource": "arn:aws:states:::ecs:runTask.sync",
      "TimeoutSeconds": 28800,
      "Parameters": {
        "LaunchType": "FARGATE",
        "PlatformVersion": "1.4.0",
        "Cluster": "${cluster_arn}",
        "TaskDefinition": "pfml-api-${environment_name}-dor-import-exempt",
        "Overrides": {
          "ContainerOverrides": [
            {
              "Name": "dor-import-exempt",
              "Environment": [
                ${st_decrypt_value},
                { "Name": "SFN_EXECUTION_ID", "Value.$": "$$.Execution.Id" }
              ]
            }
          ]
        },
        "NetworkConfiguration": {
          "AwsvpcConfiguration": {
            "SecurityGroups": ["${security_group}"],
            "Subnets": ["${subnet_1}", "${subnet_2}"],
            "AssignPublicIp": "DISABLED"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": ["States.TaskFailed"],
          "IntervalSeconds": 1,
          "MaxAttempts": 2,
          "BackoffRate": 2.0
        }
      ],
      "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "failure_notification_dor_import_exempt"}],
      "Next": "load_employers_to_fineos_init"
    },

    "failure_notification_dor_import_exempt": {
      %{if task_failure_notification_enabled}
        "Type": "Task",
        "Resource": "arn:aws:states:::sns:publish",
        "Parameters": {
          "TopicArn": "${sns_failure_topic_arn}",
          "Subject": "Task 'dor-import-exempt' failed in ${environment_name}",
          "Message": {
            "task_name": "dor-import-exempt",
            "environment": "${environment_name}",
            "error.$": "$.Error",
            "cause.$": "$.Cause"
          }
        },
      %{else}
        "Type": "Pass",
      %{endif}
        "End": true
      },

    "dor_import_notify_failure": {
      "Type": "Pass",
      "Result": {
        "task_name": "dor-import",
        "subject": "Task 'dor-import' failed in ${environment_name}"
      },
      "ResultPath": "$.task_failure_details",
      "Next": "parallel_failure_notification"
    },
    "parallel_failure_notification": {
      "Type": "Pass",
      "Next": "parallel_failure"
    },
    "parallel_failure": {
      "Comment": "Failure end state",
      "Type": "Fail"
    },

    "load_employers_to_fineos_init": {
      "Comment": "Set number of load_employers_to_fineos tasks",
      "Type": "Pass",
      "Parameters": {
        "parallel": [
          ["load-employers-to-fineos", "--process-id=1"],
          ["load-employers-to-fineos", "--process-id=2"],
          ["load-employers-to-fineos", "--process-id=3"]
        ]
      },
      "ResultPath": "$",
      "OutputPath": "$",
      "Next": "load_employers_to_fineos_map"
    },

    "load_employers_to_fineos_map": {
      "Comment": "Create employers in FINEOS using API calls",
      "Type": "Map",
      "ItemsPath": "$.parallel",

      "Iterator": {
        "StartAt": "load_employers_to_fineos",
        "States": {
          "load_employers_to_fineos": {
            "Type": "Task",
            "Resource": "arn:aws:states:::ecs:runTask.sync",
            "TimeoutSeconds": 28800,
            "Parameters": {
              "LaunchType": "FARGATE",
              "PlatformVersion": "1.4.0",
              "Cluster": "${cluster_arn}",
              "TaskDefinition": "pfml-api-${environment_name}-load-employers-to-fineos",
              "Overrides": {
                "ContainerOverrides": [
                  {
                    "Name": "load-employers-to-fineos",
                    "Environment": [
                      { "Name": "EMPLOYER_UPDATE_LIMIT", "Value": "${st_employer_update_limit}" },
                      { "Name": "ENABLE_SERVICE_AGREEMENT_VERSION_FINEOS_WRITE", "Value": "1" },
                      { "Name": "EMPLOYER_LOAD_MODE", "Value": "updates" },
                      { "Name": "SFN_EXECUTION_ID", "Value.$": "$$.Execution.Id" },
                      { "Name" : "SERVICE_AGREEMENT_LOAD", "Value" : "1" }
                    ],
                    "Command.$": "$"
                  }
                ]
              },
              "NetworkConfiguration": {
                "AwsvpcConfiguration": {
                  "SecurityGroups": ["${security_group}"],
                  "Subnets": ["${subnet_1}", "${subnet_2}"],
                  "AssignPublicIp": "DISABLED"
                }
              }
            },
            "End": true
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": ["States.TaskFailed"],
          "IntervalSeconds": 1,
          "MaxAttempts": 2,
          "BackoffRate": 2.0
        }
      ],
      "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "load_employers_to_fineos_notify_failure"}],
      "Next": "fineos_import_service_agreements"
    },

    "load_employers_to_fineos_notify_failure": {
      "Type": "Pass",
      "Result": {
        "task_name": "load-employers-to-fineos",
        "subject": "Task 'load-employers-to-fineos' failed in ${environment_name}"
      },
      "ResultPath": "$.task_failure_details",
      "Next": "failure_notification"
    },

    "fineos_import_service_agreements": {
      "Type": "Task",
      "Comment": "FINEOS service agreements import to RDS",
      "Resource": "arn:aws:states:::ecs:runTask.sync",
      "TimeoutSeconds": 28800,
      "Parameters": {
        "LaunchType": "FARGATE",
        "PlatformVersion": "1.4.0",
        "Cluster": "${cluster_arn}",
        "TaskDefinition": "pfml-api-${environment_name}-fineos-import-service-agreements",
        "Overrides": {
          "ContainerOverrides": [
            {
              "Name": "fineos-import-service-agreements",
              "Memory": 61440,
              "Cpu": 8192,
              "Environment": [
                {
                  "Name": "SFN_EXECUTION_ID",
                  "Value.$": "$$.Execution.Id"
                },
                {
                  "Name" : "FINEOS_SERVICE_AGREEMENT_EXTRACT_MAX_HISTORY_DATE",
                  "Value" : "2024-06-01"
                }
              ]
            }
          ]
        },
        "NetworkConfiguration": {
          "AwsvpcConfiguration": {
            "SecurityGroups": ["${security_group}"],
            "Subnets": ["${subnet_1}", "${subnet_2}"],
            "AssignPublicIp": "DISABLED"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": ["States.TaskFailed"],
          "IntervalSeconds": 1,
          "MaxAttempts": 2,
          "BackoffRate": 2.0
        }
      ],
      "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "fineos_import_service_agreements_notify_failure"}],
      "Next": "load_service_agreements_to_fineos"
    },

    "fineos_import_service_agreements_notify_failure": {
      "Type": "Pass",
      "Result": {
        "task_name": "fineos-import-service-agreements",
        "subject": "Task 'fineos-import-service-agreements' failed in ${environment_name}"
      },
      "ResultPath": "$.task_failure_details",
      "Next": "import_service_agreements_failure_notification"
    },

    "import_service_agreements_failure_notification": {
      "Type": "Pass",
      "Next": "import_service_agreements_failure"
    },
    "import_service_agreements_failure": {
      "Comment": "Failure end state",
      "Type": "Fail"
    },

    "load_service_agreements_to_fineos": {
      "Comment": "Load service agreements to FINEOS using API calls",
      "Type": "Task",
      "Resource": "arn:aws:states:::ecs:runTask.sync",
      "TimeoutSeconds": 28800,
      "Parameters": {
        "LaunchType": "FARGATE",
        "PlatformVersion": "1.4.0",
        "Cluster": "${cluster_arn}",
        "TaskDefinition": "pfml-api-${environment_name}-load-service-agreements-to-fineos",
        "Overrides": {
          "ContainerOverrides": [
            {
              "Name": "load-service-agreements-to-fineos",
              "Environment": [
                { "Name" : "SERVICE_AGREEMENT_LOAD", "Value" : "1" },
                { "Name" : "ENABLE_FINEOS_API_LOGGING_TO_DB", "Value" : "1" },
                { "Name" : "EMPLOYER_SERVICE_AGREEMENT_VERSION_UPDATE_LIMIT", "Value" : "${employer_service_agreement_version_update_limit}" }
              ]
            }
          ]
        },
        "NetworkConfiguration": {
          "AwsvpcConfiguration": {
            "SecurityGroups": ["${security_group}"],
            "Subnets": ["${subnet_1}", "${subnet_2}"],
            "AssignPublicIp": "DISABLED"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": ["States.TaskFailed"],
          "IntervalSeconds": 1,
          "MaxAttempts": 2,
          "BackoffRate": 2.0
        }
      ],
      "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "load_service_agreements_to_fineos_notify_failure"}],
      "Next": "fineos_eligibility_feed_export"
    },

    "load_service_agreements_to_fineos_notify_failure": {
      "Type": "Pass",
      "Result": {
        "task_name": "load-service-agreements-to-fineos",
        "subject": "Task 'load-service-agreements-to-fineos' failed in ${environment_name}"
      },
      "ResultPath": "$.task_failure_details",
      "Next": "failure_notification"
    },

    "fineos_eligibility_feed_export": {
      "Comment": "Generate eligibility files containing employees for FINEOS",
      "Type": "Task",
      "Resource": "arn:aws:states:::ecs:runTask.sync",
      "TimeoutSeconds": 28800,
      "Parameters": {
        "LaunchType": "FARGATE",
        "PlatformVersion": "1.4.0",
        "Cluster": "${cluster_arn}",
        "TaskDefinition": "pfml-api-${environment_name}-fineos-eligibility-feed-export",
        "Overrides": {
          "ContainerOverrides": [
            {
              "Name": "fineos-eligibility-feed-export",
              "Environment": [
                { "Name": "ELIGIBILITY_FEED_MODE", "Value": "updates" },
                %{if st_employee_export_limit_specified}
                { "Name": "ELIGIBILITY_FEED_EXPORT_EMPLOYEE_LIMIT", "Value": "100000" },
                %{endif}
                %{if st_file_limit_specified}
                { "Name": "ELIGIBILITY_FEED_EXPORT_FILE_NUMBER_LIMIT", "Value": "25000" },
                %{endif}
                { "Name": "SFN_EXECUTION_ID", "Value.$": "$$.Execution.Id" }
              ]
            }
          ]
        },
        "NetworkConfiguration": {
          "AwsvpcConfiguration": {
            "SecurityGroups": ["${security_group}"],
            "Subnets": ["${subnet_1}", "${subnet_2}"],
            "AssignPublicIp": "DISABLED"
          }
        }
      },
      "Retry": [
        {
          "ErrorEquals": ["States.TaskFailed"],
          "IntervalSeconds": 1,
          "MaxAttempts": 2,
          "BackoffRate": 2.0
        }
      ],
      "Catch": [{"ErrorEquals": ["States.ALL"], "Next": "fineos_eligibility_feed_export_notify_failure"}],
      "Next": "success"
    },

    "fineos_eligibility_feed_export_notify_failure": {
      "Type": "Pass",
      "Result": {
        "task_name": "fineos-eligibility-feed-export",
        "subject": "Task 'fineos-eligibility-feed-export' failed in ${environment_name}"
      },
      "ResultPath": "$.task_failure_details",
      "Next": "failure_notification"
    },

    "success": {
      "Comment": "Successful end state",
      "Type": "Succeed"
    },

    "failure_notification": {
    %{if task_failure_notification_enabled}
      "Type": "Task",
      "Resource": "arn:aws:states:::sns:publish",
      "Parameters": {
        "TopicArn": "${sns_failure_topic_arn}",
        "Subject.$": "$.task_failure_details.subject",
        "Message": {
          "task_name.$": "$.task_failure_details.task_name",
          "environment": "${environment_name}",
          "error.$": "$.Error",
          "cause.$": "$.Cause"
        }
      },
    %{else}
      "Type": "Pass",
    %{endif}
      "Next": "failure"
    },

    "failure": {
      "Comment": "Failure end state",
      "Type": "Fail"
    }
  }
}
