# IAM policy documents for ECS tasks.
# The default is effect = "Allow"

locals {
  ssm_arn_prefix         = "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:parameter/service"
  iam_db_user_arn_prefix = "arn:aws:rds-db:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:dbuser:${data.aws_db_instance.default.resource_id}"
}

data "aws_iam_policy_document" "ecs_tasks_assume_role_policy" {
  statement {
    actions = [
      "sts:AssumeRole"
    ]
    principals {
      type        = "Service"
      identifiers = ["ecs-tasks.amazonaws.com"]
    }
  }
}

# Task runtime role. This role is used by the task after it has started up
# This role is used to allow the task access to RDS pfml_batch users.
data "aws_iam_policy_document" "db_user_pfml_batch" {
  # Policy to allow connection to RDS via IAM database authentication as pfml_api user
  # https://docs.aws.amazon.com/AmazonRDS/latest/UserGuide/UsingWithRDS.IAMDBAuth.IAMPolicy.html
  statement {
    sid = "AllowAccessToRdsUsingPfmlBatchUser"
    actions = [
      "rds-db:connect"
    ]
    resources = [
      "${local.iam_db_user_arn_prefix}/pfml_batch"
    ]
  }
}

# This role is used to allow the task access to RDS pfml_admin users.
data "aws_iam_policy_document" "db_user_pfml_admin" {
  statement {
    sid = "AllowAccessToRdsUsingPfmlAdminUser"
    actions = [
      "rds-db:connect"
    ]
    resources = [
      "${local.iam_db_user_arn_prefix}/pfml_admin"
    ]
  }
}

data "aws_iam_policy_document" "db_user_pfml_migrate" {
  statement {
    sid = "AllowAccessToRdsUsingPfmlMigrate"
    actions = [
      "rds-db:connect"
    ]
    resources = [
      "${local.iam_db_user_arn_prefix}/pfml_migrate"
    ]
  }

  statement {
    actions = [
      "s3:PutObject",
      "s3:AbortMultipartUpload"
    ]
    resources = [
      "${data.aws_s3_bucket.agency_transfer.arn}/edm/pfml-schema-changes",
      "${data.aws_s3_bucket.agency_transfer.arn}/edm/pfml-schema-changes/*",
    ]
  }
}

data "aws_iam_policy_document" "task_executor" {
  # Allow ECS to log to Cloudwatch.
  statement {
    actions = [
      "logs:CreateLogStream",
      "logs:PutLogEvents",
      "logs:DescribeLogStreams"
    ]
    resources = [
      "${aws_cloudwatch_log_group.ecs_tasks.arn}:*"
    ]
  }

  # Allow ECS to authenticate with ECR and download images.
  statement {
    actions = [
      "ecr:GetAuthorizationToken",
      "ecr:BatchCheckLayerAvailability",
      "ecr:GetDownloadUrlForLayer",
      "ecr:BatchGetImage",
    ]
    # ECS Fargate doesn't like it when you restrict the access to a single
    # repository. Instead, it needs access to all of them.
    resources = ["*"]
  }

  # Allow ECS to access secrets from parameter store.
  statement {
    actions = [
      "ssm:GetParameters"
    ]
    resources = [
      "${local.ssm_arn_prefix}/${local.app_name}/common/*",
      "${local.ssm_arn_prefix}/${local.app_name}/${var.environment_name}/*",
      "arn:aws:ssm:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:parameter/admin/*"
    ]
  }

  # Allow only nonprod envs to access nonprod MMG parameters (prod is handled in above statement)
  dynamic "statement" {
    for_each = var.environment_name != "prod" ? [1] : []
    content {
      actions = [
        "ssm:GetParameters"
      ]
      resources = [
        "${local.ssm_arn_prefix}/${local.app_name}/non_prod/*",
      ]
    }
  }
}

data "aws_iam_policy_document" "assume_fineos_iam_policy_doc" {
  count = var.fineos_aws_iam_role_arn != "" ? 1 : 0
  statement {
    actions = [
      "sts:AssumeRole",
    ]
    resources = [
      var.fineos_aws_iam_role_arn,
    ]
  }
}

# ------------------------------------------------------------------------------------------------------
# Execute SQL Export Task Stuff
# ------------------------------------------------------------------------------------------------------
data "aws_iam_policy_document" "task_sql_export_s3_policy_doc" {
  # Allow Execute SQL task access to WRITE S3 files
  statement {
    actions = [
      "s3:PutObject",
      "s3:AbortMultipartUpload"
    ]
    resources = [
      "${data.aws_s3_bucket.reports.arn}/*",
      "${aws_s3_bucket.execute_sql_export.arn}/*",
      "${data.aws_s3_bucket.business_intelligence_tool.arn}/api_db/accounts_created/*",
    ]
  }
}

# ------------------------------------------------------------------------------------------------------
# Register Leave Admins with FINEOS
# ------------------------------------------------------------------------------------------------------
data "aws_iam_policy_document" "register_admins_task_role_policy_document" {
  source_policy_documents = [
    data.aws_iam_policy_document.task_executor.json,
  ]
  statement {
    actions = [
      "s3:GetObject",
      "s3:ListBucket"
    ]
    resources = [
      "arn:aws:s3:::massgov-pfml-${var.environment_name}-feature-gate",
      "arn:aws:s3:::massgov-pfml-${var.environment_name}-feature-gate/*"
    ]
  }
}

# ------------------------------------------------------------------------------------------------------
# DOR Import task stuff
# ------------------------------------------------------------------------------------------------------
data "aws_iam_policy_document" "dor_import_task_role_extras" {
  statement {
    actions = [
      "s3:PutObject",
      "s3:GetObject",
      "s3:ListBucket",
      "s3:DeleteObject"
    ]
    resources = [
      data.aws_s3_bucket.agency_transfer.arn,
      "${data.aws_s3_bucket.agency_transfer.arn}/*",
      data.aws_s3_bucket.reports.arn,
      "${data.aws_s3_bucket.reports.arn}/dor-report/*"
    ]
  }
  statement {
    sid = "AllowSESSendEmail"
    actions = [
      "ses:SendEmail",
      "ses:SendRawEmail"
    ]
    condition {
      test     = "ForAllValues:StringLike"
      variable = "ses:Recipients"
      values = [
        var.dor_employee_integrity_email_address
      ]
    }
    resources = ["*"]
    effect    = "Allow"
  }
}

# - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
data "aws_iam_policy_document" "dor_import_execution_role_extras" {
  source_policy_documents = [
    data.aws_iam_policy_document.task_executor.json,
  ]

  # Allow ECS to access secrets from parameter store.
  statement {
    actions = [
      "ssm:GetParameter",
      "ssm:GetParameters",
    ]
    resources = [
      "${local.ssm_arn_prefix}/${local.app_name}-dor-import/${var.environment_name}/*",
    ]
  }

  statement {
    actions = [
      "ssm:GetParametersByPath",
    ]
    resources = [
      "${local.ssm_arn_prefix}/${local.app_name}-dor-import/${var.environment_name}",
    ]
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# IAM policy for fineos-eligibility-feed-export
# ----------------------------------------------------------------------------------------------------------------------
data "aws_iam_policy_document" "fineos_eligibility_feed_export" {
  statement {
    sid = "GetListPutDeleteToAgencyTransferBucket"
    actions = [
      "s3:Get*",
      "s3:List*",
      "s3:PutObject",
      "s3:DeleteObject",
      "s3:AbortMultipartUpload"
    ]
    resources = [
      "${data.aws_s3_bucket.agency_transfer.arn}/cps/eligibility-feed-to-cps",
      "${data.aws_s3_bucket.agency_transfer.arn}/cps/eligibility-feed-to-cps/*",
    ]
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# IAM policy for pub-payments s3 allow list bucket
# ----------------------------------------------------------------------------------------------------------------------
data "aws_iam_policy_document" "pub_payments_s3_allow_list_bucket" {
  statement {
    sid = "AllowListingOfBucket"
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      data.aws_s3_bucket.agency_transfer.arn,
      "${data.aws_s3_bucket.agency_transfer.arn}/*",
      data.aws_s3_bucket.reports.arn,
      "${data.aws_s3_bucket.reports.arn}/*"
    ]
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# IAM policy to send emails via SES
# ----------------------------------------------------------------------------------------------------------------------
# See /docs/api/ses.tf for full details on configuring SES permissions.
data "aws_iam_policy_document" "pub_payments_send_ses" {
  statement {
    sid = "AllowSESSendEmail"
    actions = [
      "ses:SendEmail",
      "ses:SendRawEmail",
      "ses:SendTemplatedEmail"
    ]
    condition {
      test     = "ForAllValues:StringLike"
      variable = "ses:Recipients"
      values = distinct(concat(flatten([
        var.dfml_project_manager_email_address,
        var.dfml_business_operations_email_address,
        var.payment_audit_report_upload_notification_email_address,
        var.payment_audit_report_upload_notification_cc_email_address,
        var.payment_audit_report_available_notification_email_address,
        var.payment_audit_report_available_notification_cc_email_address
      ])))
    }
    resources = ["*"]
    effect    = "Allow"
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# IAM policy for agency transfer and reports s3 allow list bucket
# ----------------------------------------------------------------------------------------------------------------------
data "aws_iam_policy_document" "agency_transfer_and_reports_s3_bucket" {
  statement {
    sid = "ReadWriteAccessToAgencyTransferBucket"
    actions = [
      "s3:Get*",
      "s3:List*",
      "s3:PutObject",
      "s3:DeleteObject",
      "s3:AbortMultipartUpload"
    ]
    resources = [
      "${data.aws_s3_bucket.agency_transfer.arn}/cps",
      "${data.aws_s3_bucket.agency_transfer.arn}/cps/*",
      "${data.aws_s3_bucket.agency_transfer.arn}/reports",
      "${data.aws_s3_bucket.agency_transfer.arn}/reports/*",
      "${data.aws_s3_bucket.reports.arn}/dfml-reports",
      "${data.aws_s3_bucket.reports.arn}/dfml-reports/*"
    ]
  }
}

data "aws_iam_policy_document" "agency_transfer_and_reports_s3_bucket_dupe_1" {
  statement {
    sid = "ReadWriteAccessToAgencyTransferBucket"
    actions = [
      "s3:Get*",
      "s3:List*",
      "s3:PutObject",
      "s3:DeleteObject",
      "s3:AbortMultipartUpload"
    ]
    resources = [
      "${data.aws_s3_bucket.agency_transfer.arn}/pub",
      "${data.aws_s3_bucket.agency_transfer.arn}/pub/*",
      "${data.aws_s3_bucket.agency_transfer.arn}/reports",
      "${data.aws_s3_bucket.agency_transfer.arn}/reports/*",
      "${data.aws_s3_bucket.agency_transfer.arn}/audit",
      "${data.aws_s3_bucket.agency_transfer.arn}/audit/*",
      "${data.aws_s3_bucket.agency_transfer.arn}/cps",
      "${data.aws_s3_bucket.agency_transfer.arn}/cps/*",
      "${data.aws_s3_bucket.reports.arn}/dfml-reports",
      "${data.aws_s3_bucket.reports.arn}/dfml-reports/*",
      "${data.aws_s3_bucket.reports.arn}/dfml-responses",
      "${data.aws_s3_bucket.reports.arn}/dfml-responses/*"
    ]
  }
}

data "aws_iam_policy_document" "agency_transfer_and_reports_s3_bucket_dupe_2" {
  statement {
    sid = "ReadWriteAccessToAgencyTransferBucket"
    actions = [
      "s3:Get*",
      "s3:List*",
      "s3:PutObject",
      "s3:DeleteObject",
      "s3:AbortMultipartUpload"
    ]
    resources = [
      "${data.aws_s3_bucket.agency_transfer.arn}/pub",
      "${data.aws_s3_bucket.agency_transfer.arn}/pub/*",
      "${data.aws_s3_bucket.agency_transfer.arn}/reports",
      "${data.aws_s3_bucket.agency_transfer.arn}/reports/*",
      "${data.aws_s3_bucket.agency_transfer.arn}/cps",
      "${data.aws_s3_bucket.agency_transfer.arn}/cps/*",
      "${data.aws_s3_bucket.reports.arn}/dfml-reports",
      "${data.aws_s3_bucket.reports.arn}/dfml-reports/*",
      "${data.aws_s3_bucket.reports.arn}/dfml-responses",
      "${data.aws_s3_bucket.reports.arn}/dfml-responses/*"
    ]
  }
}

data "aws_iam_policy_document" "agency_transfer_and_reports_s3_bucket_dupe_3" {
  statement {
    sid = "ReadWriteAccessToAgencyTransferBucket"
    actions = [
      "s3:Get*",
      "s3:List*",
      "s3:PutObject",
      "s3:DeleteObject",
      "s3:AbortMultipartUpload"
    ]
    resources = [
      "${data.aws_s3_bucket.agency_transfer.arn}/cps",
      "${data.aws_s3_bucket.agency_transfer.arn}/cps/*",
      "${data.aws_s3_bucket.agency_transfer.arn}/reports",
      "${data.aws_s3_bucket.agency_transfer.arn}/reports/*",
      "${data.aws_s3_bucket.agency_transfer.arn}/audit",
      "${data.aws_s3_bucket.agency_transfer.arn}/audit/*",
      "${data.aws_s3_bucket.reports.arn}/dfml-reports",
      "${data.aws_s3_bucket.reports.arn}/dfml-reports/*"
    ]
  }
}

data "aws_iam_policy_document" "agency_transfer_and_reports_s3_bucket_dupe_4" {
  statement {
    sid = "ReadWriteAccessToAgencyTransferBucket"
    actions = [
      "s3:Get*",
      "s3:List*",
      "s3:PutObject",
      "s3:DeleteObject",
      "s3:AbortMultipartUpload"
    ]
    resources = [
      "${data.aws_s3_bucket.agency_transfer.arn}/reports",
      "${data.aws_s3_bucket.agency_transfer.arn}/reports/*",
      "${data.aws_s3_bucket.reports.arn}/dfml-responses",
      "${data.aws_s3_bucket.reports.arn}/dfml-responses/*"
    ]
  }
}

data "aws_iam_policy_document" "agency_transfer_and_reports_s3_bucket_dupe_5" {
  statement {
    sid = "ReadWriteAccessToAgencyTransferBucket"
    actions = [
      "s3:Get*",
      "s3:List*",
      "s3:PutObject",
      "s3:DeleteObject",
      "s3:AbortMultipartUpload"
    ]
    resources = [
      "${data.aws_s3_bucket.agency_transfer.arn}/reports",
      "${data.aws_s3_bucket.agency_transfer.arn}/reports/*",
      "${data.aws_s3_bucket.agency_transfer.arn}/cps",
      "${data.aws_s3_bucket.agency_transfer.arn}/cps/*",
    ]
  }
}

data "aws_iam_policy_document" "agency_transfer_and_reports_s3_bucket_dupe_6" {
  statement {
    sid = "ReadWriteAccessToAgencyTransferBucket"
    actions = [
      "s3:Get*",
      "s3:List*",
      "s3:PutObject",
      "s3:DeleteObject",
      "s3:AbortMultipartUpload"
    ]
    resources = [
      "${data.aws_s3_bucket.agency_transfer.arn}/pub",
      "${data.aws_s3_bucket.agency_transfer.arn}/pub/*",
      "${data.aws_s3_bucket.agency_transfer.arn}/reports",
      "${data.aws_s3_bucket.agency_transfer.arn}/reports/*",
      "${data.aws_s3_bucket.agency_transfer.arn}/audit",
      "${data.aws_s3_bucket.agency_transfer.arn}/audit/*",
      "${data.aws_s3_bucket.agency_transfer.arn}/cps",
      "${data.aws_s3_bucket.agency_transfer.arn}/cps/*",
      "${data.aws_s3_bucket.reports.arn}/dfml-reports",
      "${data.aws_s3_bucket.reports.arn}/dfml-reports/*",
      "${data.aws_s3_bucket.reports.arn}/dfml-responses",
      "${data.aws_s3_bucket.reports.arn}/dfml-responses/*",
      "${data.aws_s3_bucket.agency_transfer.arn}/printshop",
      "${data.aws_s3_bucket.agency_transfer.arn}/printshop/*"
    ]
  }
}

data "aws_iam_policy_document" "agency_transfer_and_reports_s3_bucket_dupe_7" {
  statement {
    sid = "ReadWriteAccessToAgencyTransferBucket"
    actions = [
      "s3:Get*",
      "s3:List*",
      "s3:PutObject",
      "s3:DeleteObject",
      "s3:AbortMultipartUpload"
    ]
    resources = [
      "${data.aws_s3_bucket.agency_transfer.arn}/reports",
      "${data.aws_s3_bucket.agency_transfer.arn}/reports/*",
      "${data.aws_s3_bucket.reports.arn}/dfml-reports",
      "${data.aws_s3_bucket.reports.arn}/dfml-reports/*",
      "${data.aws_s3_bucket.reports.arn}/dfml-responses",
      "${data.aws_s3_bucket.reports.arn}/dfml-responses/*"
    ]
  }
}

data "aws_iam_policy_document" "agency_transfer_and_reports_s3_bucket_dupe_8" {
  statement {
    sid = "ReadWriteAccessToAgencyTransferBucket"
    actions = [
      "s3:Get*",
      "s3:List*",
      "s3:PutObject",
      "s3:DeleteObject",
      "s3:AbortMultipartUpload"
    ]
    resources = [
      data.aws_s3_bucket.agency_transfer.arn,
      "${data.aws_s3_bucket.agency_transfer.arn}/*",
    ]
  }
}

data "aws_iam_policy_document" "pub_payments_process_1099_role_s3_access_policy" {
  statement {
    actions = [
      "s3:Get*",
      "s3:List*",
      "s3:PutObject",
      "ssm:GetParameter"
    ]
    resources = [
      aws_s3_bucket.pdf_generator.arn,
      "${aws_s3_bucket.pdf_generator.arn}/*",
      data.aws_ssm_parameter.itext_key.arn
    ]
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# IAM role and policies for reductions-workflow
# ----------------------------------------------------------------------------------------------------------------------
data "aws_iam_policy_document" "reductions_workflow_task_role_extras" {
  source_policy_documents = [
    data.aws_iam_policy_document.agency_transfer_and_reports_s3_bucket_dupe_8.json,
  ]

  statement {
    sid = "AllowSESSendEmail"
    actions = [
      "ses:SendEmail",
      "ses:SendRawEmail"
    ]
    condition {
      test     = "ForAllValues:StringLike"
      variable = "ses:Recipients"
      values = [
        var.dfml_project_manager_email_address,
        var.dfml_business_operations_email_address,
        var.agency_reductions_email_address,
        var.agency_service_desk_email_address,
      ]
    }
    resources = ["*"]
  }
}

data "aws_iam_policy_document" "reductions_workflow_execution_role_extras" {
  source_policy_documents = [
    data.aws_iam_policy_document.task_executor.json,
  ]

  # Allow ECS to access secrets from parameter store.
  statement {
    actions = [
      "ssm:GetParameter",
      "ssm:GetParameters",
    ]
    resources = [
      "${local.ssm_arn_prefix}/${local.app_name}-comptroller/${var.environment_name}/*"
    ]
  }

  statement {
    actions = [
      "ssm:GetParametersByPath",
    ]
    resources = [
      "${local.ssm_arn_prefix}/${local.app_name}-comptroller/${var.environment_name}"
    ]
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# IAM role and policies for S3 buckets for business intelligence (BI) data extracts
# ----------------------------------------------------------------------------------------------------------------------
data "aws_s3_bucket" "business_intelligence_tool" {
  bucket = "massgov-pfml-${var.environment_name}-business-intelligence-tool"
}

data "aws_iam_policy_document" "fineos_bucket_tool_task_policy_document" {
  statement {
    sid = "AllowS3ReadWriteOnBucket"
    actions = [
      "s3:Get*",
      "s3:List*",
      "s3:PutObject",
      "s3:DeleteObject",
      "s3:AbortMultipartUpload"
    ]
    resources = [
      data.aws_s3_bucket.agency_transfer.arn,
      "${data.aws_s3_bucket.agency_transfer.arn}/*",
      data.aws_s3_bucket.business_intelligence_tool.arn,
      "${data.aws_s3_bucket.business_intelligence_tool.arn}/*"
    ]
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# IAM role and policies for cps-errors-crawler
# ----------------------------------------------------------------------------------------------------------------------
data "aws_iam_policy_document" "cps_errors_crawler_role_policy_document" {
  statement {
    sid = "AllowListingOfBucket"
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      data.aws_s3_bucket.agency_transfer.arn,
      "${data.aws_s3_bucket.agency_transfer.arn}/*"
    ]
    condition {
      test     = "StringLike"
      variable = "s3:prefix"
      values = [
        "cps-errors/received/",
        "cps-errors/received/*"
      ]
    }
  }

  statement {
    sid = "AllowS3ReadDeleteOnBucket"
    actions = [
      "s3:Get*",
      "s3:List*",
      "s3:DeleteObject"
    ]
    resources = [
      "${data.aws_s3_bucket.agency_transfer.arn}/cps-errors/received",
      "${data.aws_s3_bucket.agency_transfer.arn}/cps-errors/received/*"
    ]
  }

  statement {
    sid = "AllowS3WriteOnBucket"
    actions = [
      "s3:List*",
      "s3:PutObject",
      "s3:AbortMultipartUpload"
    ]
    resources = [
      "${data.aws_s3_bucket.agency_transfer.arn}/cps-errors/processed",
      "${data.aws_s3_bucket.agency_transfer.arn}/cps-errors/processed/*"
    ]
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# IAM role and policies for update-gender-data-from-rmv
# ----------------------------------------------------------------------------------------------------------------------
data "aws_iam_policy_document" "update_gender_data_from_rmv" {
  statement {
    actions = [
      "secretsmanager:GetSecretValue",
    ]
    resources = [
      var.rmv_client_certificate_binary_arn,
    ]
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# IAM role and policies for process-prepaid-debit-registration
# ----------------------------------------------------------------------------------------------------------------------
data "aws_iam_policy_document" "process_prepaid_debit_registration" {
  statement {
    actions = [
      "secretsmanager:GetSecretValue",
    ]
    resources = [
      var.usbank_client_certificate_binary_arn,
    ]
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# IAM role and policies for appeals-import-extract
# ----------------------------------------------------------------------------------------------------------------------
data "aws_iam_policy_document" "appeals_import_extract" {
  # Allow ECS task to read extract files from S3.
  statement {
    actions = [
      "s3:GetObject",
      "s3:ListBucket",
    ]
    resources = [
      data.aws_s3_bucket.agency_transfer.arn,
      "${data.aws_s3_bucket.agency_transfer.arn}/appeals/extracts/*",
    ]
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# IAM role and policies for overpayments-backfill-data
# ----------------------------------------------------------------------------------------------------------------------
data "aws_iam_policy_document" "overpayments_backfill_data_task_role_extras" {
  source_policy_documents = [
    data.aws_iam_policy_document.agency_transfer_and_reports_s3_bucket_dupe_8.json,
  ]
  statement {
    sid = "ReadAccessToBusinessIntelligenceBucket"
    actions = [
      "s3:Get*",
      "s3:List*"
    ]
    resources = [
      data.aws_s3_bucket.business_intelligence_tool.arn,
      "${data.aws_s3_bucket.business_intelligence_tool.arn}/*"
    ]
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# IAM role and policies for dfml-fines-repayments
# ----------------------------------------------------------------------------------------------------------------------
data "aws_iam_policy_document" "dfml_fines_and_repayments_task_role_extras" {
  source_policy_documents = [
    data.aws_iam_policy_document.agency_transfer_and_reports_s3_bucket_dupe_8.json,
  ]

  statement {
    sid = "AllowSESSendEmail"
    actions = [
      "ses:SendEmail",
      "ses:SendRawEmail"
    ]
    condition {
      test     = "ForAllValues:StringLike"
      variable = "ses:Recipients"
      values = [
        var.dfml_fines_repayments_email_address,
        var.dfml_fines_repayments_cc_email_address,
      ]
    }
    resources = ["*"]
    effect    = "Allow"
  }
}

# ------------------------------------------------------------------------------------------------------
# New Relic Access
# ------------------------------------------------------------------------------------------------------
data "aws_iam_policy_document" "process_new_relic_dashboard_exe_role" {
  source_policy_documents = [
    data.aws_iam_policy_document.task_executor.json,
  ]
  statement {
    actions   = ["ssm:GetParameters"]
    effect    = "Allow"
    resources = [data.aws_ssm_parameter.newrelic-api-key.arn]
  }
}

data "aws_iam_policy_document" "process_new_relic_dashboard_task_role" {
  statement {
    actions = [
      "s3:Get*",
      "s3:List*",
      "s3:PutObject",
      "s3:DeleteObject",
      "s3:ListBucketMultipartUploads",
      "s3:AbortMultipartUpload",
      "s3:PutObjectVersionAcl",
      "s3:PutObjectAcl",
      "s3:ListMultipartUploadParts"
    ]
    resources = [
      data.aws_s3_bucket.new_relic_dashboard.arn,
      "${data.aws_s3_bucket.new_relic_dashboard.arn}/*"
    ]
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# IAM role and policies for process-overpayment-referrals
# ----------------------------------------------------------------------------------------------------------------------
data "aws_iam_policy_document" "process_overpayment_referrals_extras" {
  source_policy_documents = [
    data.aws_iam_policy_document.agency_transfer_and_reports_s3_bucket_dupe_8.json,
  ]

  statement {
    sid = "AllowSESSendEmail"
    actions = [
      "ses:SendEmail",
      "ses:SendRawEmail"
    ]
    condition {
      test     = "ForAllValues:StringLike"
      variable = "ses:Recipients"
      values = [
        var.mmars_overpayment_email_address,
        var.mmars_overpayment_bcc_email_address,
      ]
    }
    resources = ["*"]
    effect    = "Allow"
  }
}

# ----------------------------------------------------------------------------------------------------------------------
# IAM policy to send emails via SES
# ----------------------------------------------------------------------------------------------------------------------
# See /docs/api/ses.tf for full details on configuring SES permissions.
data "aws_iam_policy_document" "dfml_db_migration_send_ses" {
  statement {
    sid = "AllowSESSendEmail"
    actions = [
      "ses:SendEmail",
      "ses:SendRawEmail"
    ]
    condition {
      test     = "ForAllValues:StringLike"
      variable = "ses:Recipients"
      values = [
        var.dfml_db_migration_email_address,
        var.dfml_db_migration_cc_email_address,
      ]
    }
    resources = ["*"]
    effect    = "Allow"
  }
}