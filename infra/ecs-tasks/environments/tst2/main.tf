locals {
  environment_name = "tst2"
}

provider "aws" {
  region = "us-east-1"
}

terraform {
  backend "s3" {
    bucket         = "massgov-pfml-tst2-env-mgmt"
    key            = "terraform/ecs-tasks.tfstate"
    region         = "us-east-1"
    dynamodb_table = "terraform_locks"
  }
}

data "aws_caller_identity" "current" {}
data "aws_region" "current" {}

data "aws_secretsmanager_secret" "rmv_client_certificate" {
  name = "/service/pfml-api-tst2/rmv_client_certificate"
}

data "aws_secretsmanager_secret" "usbank_client_certificate" {
  name = "/service/pfml-api/${local.environment_name}/usbank_client_certificate"
}

module "tasks" {
  source = "../../template"

  environment_name              = "tst2"
  st_use_mock_dor_data          = true
  st_decrypt_dor_data           = false
  st_file_limit_specified       = false
  st_employer_update_limit      = 1500
  service_docker_tag            = local.service_docker_tag
  vpc_id                        = data.aws_vpc.vpc.id
  vpc_name                      = local.vpc
  app_subnet_ids                = data.aws_subnets.vpc_app.ids
  enforce_execute_sql_read_only = false

  # TODO: These values are provided by FINEOS.
  fineos_client_integration_services_api_url = "https://idt2-api.masspfml.fineos.com/integration-services/"
  fineos_client_customer_api_url             = "https://idt2-api.masspfml.fineos.com/customerapi/"
  fineos_client_group_client_api_url         = "https://idt2-api.masspfml.fineos.com/groupclientapi/"
  fineos_client_wscomposer_api_url           = "https://idt2-api.masspfml.fineos.com/integration-services/wscomposer/"
  fineos_client_oauth2_url                   = "https://idt2-api.masspfml.fineos.com/oauth2/token"

  fineos_aws_iam_role_arn         = "arn:aws:iam::************:role/somvrf-IAMRoles-CustomerAccountAccessRole-VsNw727kJDoq"
  fineos_aws_iam_role_external_id = "12345"

  fineos_eligibility_feed_output_directory_path       = "s3://fin-somvrf-data-import/IDT2"
  fineos_import_employee_updates_input_directory_path = "s3://fin-somvrf-data-export/IDT2/dataexports"
  logging_level                                       = "massgov.pfml.fineos.fineos_client=DEBUG"

  # These can be kept blank.
  eolwd_moveit_sftp_uri   = "sftp://<EMAIL>"
  pfml_error_reports_path = "s3://massgov-pfml-tst2-agency-transfer/error-reports/outbound"

  dfml_project_manager_email_address     = "<EMAIL>"
  dfml_business_operations_email_address = "<EMAIL>"
  dfml_fines_repayments_email_address    = "<EMAIL>"
  dfml_fines_repayments_cc_email_address = "<EMAIL>"
  dor_employee_integrity_email_address   = "<EMAIL>"
  agency_reductions_email_address        = "<EMAIL>"
  agency_service_desk_email_address      = "<EMAIL>"
  mmars_overpayment_email_address        = "<EMAIL>"
  mmars_overpayment_bcc_email_address    = "<EMAIL>"
  dfml_db_migration_email_address        = "<EMAIL>"
  dfml_db_migration_cc_email_address     = "<EMAIL>"

  # TODO: Values from FINEOS.
  fineos_data_export_path         = "s3://fin-somvrf-data-export/IDT2/dataexports"
  fineos_monthly_data_export_path = "s3://fin-somvrf-data-export/IDT2/monthlyExtracts"
  fineos_adhoc_data_export_path   = "s3://fin-somvrf-data-export/IDT2/dataExtracts/AdHocExtract"
  fineos_data_import_path         = "s3://fin-somvrf-data-import/IDT2/peiupdate"
  fineos_error_export_path        = "s3://fin-somvrf-data-export/IDT2/errorExtracts"
  fineos_report_export_path       = "s3://fin-somvrf-data-export/IDT2/reportExtract"

  pfml_fineos_inbound_path                            = "s3://massgov-pfml-tst2-agency-transfer/cps/inbound"
  pfml_fineos_outbound_path                           = "s3://massgov-pfml-tst2-agency-transfer/cps/outbound"
  pfml_fineos_eligibility_feed_archive_directory_path = "s3://massgov-pfml-tst2-agency-transfer/cps/eligibility-feed-to-cps"
  pfml_fineos_eligibility_feed_temp_directory_path    = "/tmp"
  pfml_bi_tool_extract_path                           = "s3://massgov-pfml-tst2-business-intelligence-tool/fineos/dataexports"

  payment_audit_report_outbound_folder_path = "s3://massgov-pfml-tst2-agency-transfer/audit/outbound"
  payment_audit_report_sent_folder_path     = "s3://massgov-pfml-tst2-agency-transfer/audit/sent"

  # PaymentAuditReport Feature Flags
  dia_dua_annotations_enabled                           = "0"
  enable_to_use_feed_aggregate_table                    = "1"
  sync_employee_feed_aggregate_step_enable              = "1"
  enable_sync_address_using_aggregate_table             = "1"
  enable_sync_eft_using_aggregate_table                 = "1"
  enable_sync_payment_preferences_using_aggregate_table = "0"

  enable_register_admins_job = true

  enable_pub_automation_create_pub_files     = true
  enable_pub_automation_process_returns      = false
  enable_fineos_import_iaww                  = true
  enable_sync_fineos_extracts_to_pfml_models = true
  enable_fines_and_repayments_automation     = true

  enable_mock_edm_repayment_response = "0"

  fineos_overpayment_extract_max_history_date = "2024-10-01"
  enable_overpayment_adjustment_processing    = "1"

  rmv_client_base_url               = "https://atlas-staging-gateway.massdot.state.ma.us/vs"
  rmv_client_certificate_binary_arn = data.aws_secretsmanager_secret.rmv_client_certificate.arn

  usbank_client_base_url               = "https://alpha-apip2-prepaid.usbank.com"
  usbank_client_soap_base_url          = "https://stage-apip.prepaidgateway.com"
  usbank_client_certificate_binary_arn = data.aws_secretsmanager_secret.usbank_client_certificate.arn
  usbank_client_oauth2_url             = "https://stage-apip.prepaidgateway.com/oauth/oauth20/token"
  usbank_transId                       = "3491"
  usbank_certCardId                    = "**********"
  usbank_certCardPasscode              = "3770"
  usbank_fundingCardId                 = "**********"
  usbank_cardType                      = "2"

  task_failure_email_address_list = ["<EMAIL>"]

  # FineOS ETL Variables, cron expressions are in ET timezone
  dor_fineos_etl_schedule_enabled    = true
  dor_fineos_etl_schedule_expression = "cron(30 19 ? * * *)" # 7:30 PM ET every day

  # Used for 1099 only
  localhost_pdf_api_host = "http://localhost:5000"
  # Used for all other PDF generation
  generate_1099_max_files         = "10000"
  upload_max_files_to_fineos      = "10000"
  enable_1099_testfile_generation = "0"
  irs_1099_correction_ind         = "0"
  irs_1099_tax_year               = "2024"

  enable_offset_get_1099      = "0"
  upload_1099_doc_batch_start = "1"
  upload_1099_doc_batch_end   = "10000"

  # PDF API Service Variables
  pdf_api_lb_port = 3605

  # EDM API Sevice Variables
  edm_client_api_base_url = "https://coma-lwd-dev.privatelink.snowflakecomputing.com/api/v2/statements"
  edm_client_oauth2_url   = "https://coma-lwd-dev.privatelink.snowflakecomputing.com/oauth/token-request"


  use_overpayment_table_for_over_payments_max_weekly_benefit = "0"

  enable_pub_undeliverable_checks_file_processing = "1"
  enable_employer_overpayment                     = "0"
  enable_email_overpayment_referrals_inf_file     = "1"

  pub_payment_starting_check_number = "106"

  enable_sync_claims_step                   = "1"
  enable_sync_employees_step                = "1"
  enable_sync_efts_step                     = "1"
  enable_sync_absence_periods_step          = "1"
  enable_sync_absence_paid_leave_cases_step = "1"
  enable_sync_leave_requests_step           = "1"
  enable_sync_c_i_values                    = "1"
  enable_sync_claimant_address_step         = "1"

  enable_payment_reject_issue_resolutions_milestone1 = "1"
  enable_notify_edm_team_of_pfml_schema_changes      = "1"

  # @TODO(PFMLPB-23195): Remove enable_mark_applications_ready_for_review
  enable_mark_applications_ready_for_review = "1"
  # @TODO(PFMLPB-24796): Remove enable_re_notification
  enable_re_notification = "0"

  # Allow link_claim_to_application step to send notifications
  portal_base_url      = "https://paidleave-tst2.dfml.eol.mass.gov"
  service_now_base_url = "https://savilinxtest.servicenowservices.com"

  release_version = var.release_version

  enable_prepaid_impact_payments = "1"
  enable_sync_payment_preference = "1"

  enable_overpayment_vcmt_automation = "1"

  # Med to Bonding
  # @TODO(PFMLPB-24292): Remove feature flag for Med to Bonding BPC
  enable_delayed_submission_of_modifications = "1"

  ####  Cloudwatch ecs schedules

  appeals_generate_intake_pdfs_schedule_expression = "cron(30 6-20 ? * * *)"
  enable_appeals_generate_intake_pdfs              = true

  appeals_import_extract_schedule_expression = "cron(30 11 ? * * *)"
  enable_appeals_import_extract              = true

  child_support_dor_import_schedule_expression = "cron(0 9 ? * FRI *)"
  enable_child_support_automation              = "0"

  cps_errors_crawler_schedule_expression = "cron(0 10 ? * * *)"
  enable_cps_errors_crawler              = true

  export_60_day_comms_report_schedule_expression = "cron(0 8 ? * WED *)"
  enable_export_60_day_comms_report              = true

  export_leave_admins_created_schedule_expression = "cron(0 7 ? * * *)"
  enable_export_leave_admins_created              = true

  export_psd_report_schedule_expression = "cron(0 8 ? * * *)"
  enable_export_psd_report              = true

  fineos_data_export_tool_schedule_expression = "cron(0 11 ? * * *)"
  enable_fineos_data_export_tool              = true

  fineos_error_extract_tool_schedule_expression = "cron(0 9 ? * * *)"
  enable_fineos_error_extract_tool              = true

  fineos_import_employee_updates_schedule_expression = "cron(30 13 ? * * *)"
  enable_standalone_fineos_import_employee_updates   = true

  fineos_import_la_units_schedule_expression = "cron(0 7 ? * MON-FRI *)"
  enable_fineos_import_la_units              = true

  fineos_monthly_extract_scheduler_schedule_expression = "cron(0 11 ? * * *)"
  enable_fineos_monthly_extract_scheduler              = true

  fineos_report_extract_tool_schedule_expression = "cron(30 12,16 ? * * *)"
  enable_fineos_report_extracts_tool             = true

  fineos_snapshot_extract_schedule_expression = "cron(0 10 ? * * *)"
  enable_fineos_snapshot_extract_tool         = true

  import_fineos_to_warehouse_schedule_expression = "cron(0 9 ? * * *)"
  enable_import_fineos_to_warehouse              = true

  # If you are updating "enable_1099_generator" from "false" to "true", you must also update
  # pub_payments_process_1099_documents_schedule's "start_date" in eventbridge.tf since its
  # current value is older than what EventBridge allows. It has to run every other Thursday
  # at 13:00 UTC since "2024-02-01T13:00:00Z". Reach out to Infra Team for additional help.

  # we are switching to the cron scheduler to schedule the job evry business day. This will be
  # revert back to the rate(14 days) in future. That time we need to uncomment the rate
  # and remove the cron scheduler below
  # pub_payments_process_1099_documents_schedule_expression = "rate(14 days)"
  pub_payments_process_1099_documents_schedule_expression = "cron(0 12 ? * WED,FRI *)"
  enable_1099_generator                                   = true

  pub_payments_copy_audit_report_schedule_expression = "cron(0 14 ? * MON-FRI *)"
  enable_pub_payments_copy_audit_report_schedule     = true


  pub_payments_process_fineos_schedule_expression        = "cron(0 7 ? * TUE-FRI *)"
  pub_payments_process_fineos_monday_schedule_expression = "cron(0 7 ? * MON *)"

  pub_payments_process_snapshot_schedule_expression      = "cron(0 14 ? * WED *)"
  pub_payments_verify_fineos_extract_schedule_expression = "cron(0 14 ? * MON-FRI *)"
  enable_pub_automation_fineos                           = true
  enable_pub_payments_process_snapshot_fineos            = true
  enable_pub_automation_fineos_monday                    = true

  pub_process_weekly_reports_schedule_expression = "cron(0 9 ? * TUE *)"
  enable_pub_process_weekly_reports              = true

  reductions_dia_send_claimant_lists_schedule_expression     = "cron(20 9 ? * MON-FRI *)"
  reductions_dua_send_claimant_lists_schedule_expression     = "cron(20 9 ? * MON-FRI *)"
  enable_reductions_send_claimant_lists_to_agencies_schedule = true

  reductions_process_agency_data_lists_schedule_expression = "cron(0/15 6-19 ? * MON-FRI *)"
  enable_reductions_process_agency_data_schedule           = false

  process_prepaid_debit_registration_schedule_expression = "cron(0 14 ? * MON-FRI *)"
  enable_process_prepaid_debit_registration              = true # true or false

  process_overpayment_referrals_schedule_expression = "cron(0 9 ? * THU *)"
  enable_process_overpayment_referrals              = true

  process_mmars_response_file_schedule_expression = "cron(30 8 ? * MON-FRI *)"
  enable_process_mmars_response_file              = true # true or false

  process_overpayment_collections_schedule_expression = "cron(0 14 ? * MON-FRI *)"
  enable_process_overpayment_collections              = true # true or false

  dua_wages_from_dor_import_schedule_expression = "cron(30 10 * * ? *)"
  enable_dua_wages_from_dor_import_schedule     = true

  # Weekend Jobs

  weekend_fineos_import_la_units_schedule_expression = "cron(0 10 ? * SAT-SUN *)"
  enable_weekend_fineos_import_la_units              = true

  sunday_pub_payments_process_fineos_schedule_expression = "cron(0 11 ? * SUN *)"
  enable_sunday_pub_payments_process_fineos_schedule     = true

  saturday_pub_payments_process_fineos_schedule_expression = "cron(0 11 ? * SAT *)"
  enable_saturday_pub_payments_process_fineos_schedule     = true

  enable_address_extract_schedule     = true
  address_extract_schedule_expression = "cron(0 0 1 * ? *)" # Run at midnight on the first day of the month
}
