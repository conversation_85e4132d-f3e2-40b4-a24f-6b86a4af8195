import sys
import boto3
import psycopg2
import logging
import argparse
import random
import string
import emoji

PARSER = argparse.ArgumentParser()
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)


SSM = boto3.client("ssm")
SES = boto3.client("ses")


def get_input_arguments():
    PARSER.add_argument(
        "--email",
        required=True,
        help="Automatically passed in from GitHub Actions. The username is created from this, replacing `.` with `_` and removing the @mass.gov",
    )
    return PARSER.parse_args()


ARGS = get_input_arguments()
ENVS = [
    "infra-test",
    "breakfix",
    "performance",
    "training",
    "trn2",
    "tst1",
    "tst2",
    "tst3",
    "uat",
]


def get_ssm_parameter(env=None):
    return SSM.get_parameter(
        Name=f"/service/pfml-api/{env}/db-password", WithDecryption=True
    )["Parameter"]["Value"]


def create_password():
    chars = string.ascii_letters + string.digits + "!@#$%^&*"
    return "".join(random.choice(chars) for i in range(10))


def send_email(username=None, password=None):
    db_hosts = [
        f"massgov-pfml-{env}.c6icrkacncoz.us-east-1.rds.amazonaws.com" for env in ENVS
    ]
    body_text = (
f"""
Your database credentials:
--------------------------------------------------------------------------------
Username: {username}
Password: {password}
Database endpoints:
"""
    + "\n".join(f"- {host}" for host in db_hosts)
    + """
--------------------------------------------------------------------------------
"""
    )

    try:
        SES.send_email(
            Source="<EMAIL>",
            Destination={"ToAddresses": [f"{ARGS.email}"]},
            Message={
                "Subject": {
                    "Data": "Connection information for PFML Nonprod Databases"
                },
                "Body": {"Text": {"Data": body_text}},
            },
        )
        logging.info(f"Email sent to {ARGS.email}")
    except Exception as e:
        logging.error(f"Failed to send email: {e}")


def main():
    error = False
    email = ARGS.email
    username = email.replace(".", "_").replace("-", "_").split("@")[0]
    user_password = create_password()
    for env in ENVS:
        db_name = f"massgov_pfml_{env.replace('-', '_')}"
        pfml_password = get_ssm_parameter(env=env)
        db_host = f"massgov-pfml-{env}.c6icrkacncoz.us-east-1.rds.amazonaws.com"

        try:
            connection = psycopg2.connect(
                dbname=db_name,
                user="pfml",
                password=pfml_password,
                host=db_host,
                port=5432,
                sslmode="require",
            )
            cursor = connection.cursor()
            cursor.execute(
                f"""
                DO $$
                BEGIN
                    IF EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = '{username}') THEN
                    ALTER USER {username} WITH PASSWORD '{user_password}' VALID UNTIL 'infinity';
                    ELSE
                        CREATE USER {username} WITH PASSWORD '{user_password}' VALID UNTIL 'infinity';
                    END IF;
                    GRANT app, pfml_owner TO {username};
                END
                $$;
                """
            )
            connection.commit()
            logging.info(f"{env} {emoji.emojize(':check_mark_button:')})")
        except Exception as e:
            logging.error(f"{env} {emoji.emojize(':cross_mark:')})")
            logging.error(e)
            error = True
        finally:
            cursor.close()
            connection.close()
    sys.exit(1) if error else send_email(username=username, password=user_password)


if __name__ == "__main__":
    main()
