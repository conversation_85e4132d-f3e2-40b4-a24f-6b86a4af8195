# Create DB User - Nonprods
Automates creating a user with write privileges to the PFML nonprod databases. Created for developer self-service via GitHub Actions (GHA) - [Infra Create DB User - Nonprods](https://github.com/EOLWD/pfml/actions/workflows/infra-create-db-user-nonprods.yml)

### How it works
The GHA runners are self-hosted in the LWD AWS account. The VPC that hosts the runners is peered to the PFML Nonprod VPC (see [diagram](https://lwd.atlassian.net/wiki/x/FQB3_/)), allowing a private route from the runners to the nonprod RDS instances. The workflow runs the python directly so that no intermediary services/technologies are needed to interact with AWS or proxy the code execution. Looping through a list of nonprod environments, `psycopg2` is used to connect to each instance then execute the SQL statements required to create the user and grant the needed permissions.

Once the user is provisioned in each DB, SES is used to email the user his or her username, password, and DB endpoint connection strings. The email is extracted from the GitHub context using GraphQL and the username is assembled automatically from the user's email i.e. `<EMAIL>` would be `benjamin_d_lake`.

### DB permissions
The point of this automation, aside from automating a manual DBA process, is to give developers write access to the Nonprod DBs so they no longer have to use the root user `pfml`. The user is granted `app` and `pfml_owner` roles so that the user has sufficient permissions to write to and create tables, columns, etc. The `pfml` user will be used to create the users.

### Alerting
If any errors are encountered during the user creation process, no SES email will be sent to the user. They will instead receive an email from GitHub that their workflow run failed. 

A low priority PD alert will also be created for the Infra team containing the error. Infra will be able to manually provision the user if necessary while the issue is addressed.

### Password resets
The first SQL statement ran checks if the user already exists. If it does, then instead of creating a new user, it simply resets the password, (re)applies the grants, then emails the user. So, if you ever forget your PW, just run the job again.

### Password expiration
Because this is nonprod, the password will not expire. Infra team can reset if needed, but the easiest/quickest thing to do if you can't log in is to just rerun the job - that'll reset the password.

