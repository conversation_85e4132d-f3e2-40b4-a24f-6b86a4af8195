HEAD_REVISION_FILEPATH=massgov/pfml/db/migrations/head_revision.txt

lint-staged --shell

# Check for added, modified, or deleted migrations in the api directory
CHANGED_MIGRATIONS=$(git diff --cached --name-only --diff-filter=AMD -- api/massgov/pfml/db/migrations/versions)

if [ -n "$CHANGED_MIGRATIONS" ]; then
  echo "Changes detected in api/massgov/pfml/db/migrations, updating head_revision.txt"

  # Output a new head revision if there are any changes to migrations
  pushd api || {
    echo "Error: Could not find api directory"
    exit 1
  }
  HEAD=$(make -s db-migrate-heads | cut -d ' ' -f 1) || {
    echo "Error: Failed to get migration heads using 'make db-migrate-heads'"
    exit 1
  }
  echo "$HEAD" > $HEAD_REVISION_FILEPATH
  git add $HEAD_REVISION_FILEPATH

  # Check if there are multiple heads
  HEAD_COUNT=$(echo "$HEAD" | wc -l)
  if [ "$HEAD_COUNT" -gt 1 ]; then
    echo "Error: Multiple head revisions found"
    echo "$HEAD"
    exit 1
  fi
  popd || {
    echo "Error: Unexpected failure during cleanup"
    exit 1
  }
fi