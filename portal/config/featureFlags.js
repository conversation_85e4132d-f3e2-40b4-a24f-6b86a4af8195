// @ts-check

/**
 * Feature flags. A feature flag can be enabled/disabled at the environment level.
 * Its value will either be true or false. Environments can override this value.
 * See: ../../docs/portal/feature-flags.md
 * @type {Record<string, Record<string, boolean>>}
 */
const flagsConfig = {
  // Define a default or all feature flags here.
  // Environments will fallback to these default values.
  defaults: {
    // When this flag true, you can BYPASS maintenance pages that are currently present.
    // See docs/portal/maintenance-pages.md for more details.
    noMaintenance: false,

    // When this flag is enabled, the user can see the site.
    // To immediately hide the site from people who previously overrode this
    // flag in a cookie, you can rename this flag to something else (and also
    // update the reference to it in _app.js), but try to keep it prefixed with pfml.
    // https://lwd.atlassian.net/browse/CP-459
    pfmlTerriyay: false,

    // When this feature flag is enabled, show content related to benefit years potentially being
    // out of sync - a known side effect of the FINEOS 22.5 upgrade
    // See: https://lwd.atlassian.net/browse/PFMLPB-9154
    // TODO (PFMLPB-9152) Remove flag
    showBenefitYearOutOfSyncContent: true,

    // When this feature flag is enabled, prevent users from being able to create and submit
    // applications with overlapping benefit years.
    // See: https://lwd.atlassian.net/browse/PFMLPB-9137
    // TODO (PFMLPB-9153) Remove flag
    disableOverlappingBenefitYearClaimCreation: true,

    // TODO (PFMLPB-14806): Remove flag
    // When this flag is enabled, employer PTO top off legislation functionality is enabled
    // See more: https://lwd.atlassian.net/wiki/spaces/DD/pages/2898919555/PTO+Top+Off+legislation+change
    employerPtoTopOff: true,

    // TODO (PFMLPB-16850): Remove flag
    // When enabled claimants are reminded they need to upload their certification document(s) post application submit
    // and are able to upload these documents from the claim status page.
    enableClaimStatusCertDocUpload: false,

    // When this feature flag is ON, it will enable and display a new option to the user to select prepaid debit card as payment preference
    enablePrepaidDebitSelection: true,

    // When this feature flag is ON, it will enable address validation
    enableAddressValidation: true,

    // When this FF is on, the claimant will be able to ‘submit’ an application without uploading verification documents.
    documentUploadOptional: false,

    // When feature flag is on it will enable delegated return detection and redirection when detected.
    enableDelegatedOAuthDetection: true,

    // When feature flag is on it will force the MMG Auth flow to launch as a delegated return.
    forceDelegatedOAuthReturn: false,

    // TODO (PFMLPB-19806): Remove flag
    // When feature flag is on, employers can access the exemption portal
    enableEmployerExemptionsPortal: false,

    // When this feature flag is on, employers can access a live chat support widget
    enableLiveChat: true,

    // TODO (PFMLPB-21397): Remove flag
    // When this feature flag is off, the IDV Universal Profile enhancements are disabled.
    // When this feature flag is on, the IDV Universal Profile enhancements are enabled.
    enableUniversalProfileIDV: true,

    // TODO (PFMLPB-21400): Remove flag
    // When this feature flag is on, the MMG IDV Pilot enhancements are enabled
    enableMmgIDV: true,

    // TODO (PFMLPB-22297): Cleanup feature flag
    // When this feature flag is on, employers can export leave admin data to CSV
    enableSKICSVExport: false,

    // TODO (PFMLPB-22429): Remove flag
    // When this feature flag is on, employers can view new SKI filters for the employers application table
    enableSKIApplicationsTable: false,

    // TODO (PFMLPB-22982): Remove flag
    // When this feature flag is on, employers can view new SKI information in the application table
    enableSKIApplicationsFilter: false,

    // TODO (PFMLPB-22584) Remove this flag when SKI is fully deployed
    // When this feature flag is on, leave admins can view IAWW & weekly benefit wage information in the application status page.
    enableSKIWages: false,

    // TODO (PFMLPB-23916) Remove this flag when side nav v2 is stable and
    // has been deployed for several sprints
    enableClaimantPortalSideNavigationV2: false,

    // TODO (PFMLPB-23608) Remove feature flag
    // When this feature flag is on, enhancements to provide employer name in portal application are enabled.
    enableEmployerNameContent: false,

    // TODO (PFMLPB-24322) Remove ODC feature flag
    // When this feature flag is on, occupational data collection is enabled.
    enableOccupationDataCollection: false,

    // TODO (PFMLPB-24883) Remove backend IDV invalidation feature flag
    // When this feature flag is on, backend IDV invalidation is enabled.
    enableBackendInvalidation: false,
  },
  // Environments can optionally override a default feature flag below.
  // The environment keys should use the same envName defined in
  // environment config files.
  prod: {
    pfmlTerriyay: true,
    enableDelegatedOAuthDetection: false,
  },
  local: {
    pfmlTerriyay: true,
  },
  "local-api": {
    pfmlTerriyay: true,
  },
  performance: {},
  uat: {},
  tst1: {},
  trn2: {},
  tst2: {
    enableLiveChat: true,
  },
  tst3: {},
  breakfix: {},
  training: {},
};

/**
 * Merges the default feature flags with any environment-specific overrides
 * @param {string} env - Environment name
 * @returns {string} Stringified JSON representation of the feature flags for the given environment
 */
function featureFlags(env) {
  const envFlags = Object.assign({}, flagsConfig.defaults, flagsConfig[env]);

  // This gets passed into the app as an environment variable, which *must* be a string.
  return JSON.stringify(envFlags);
}

module.exports = featureFlags;
