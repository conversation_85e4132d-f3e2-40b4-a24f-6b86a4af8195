{"name": "mass_pfml", "version": "1.0.0", "description": "Mass PFML Portal", "main": "index.js", "engines": {"node": ">=22", "npm": ">=10"}, "scripts": {"analyze-bundle": "ANALYZE_BUNDLE=true next build", "dev": "NEXT_TELEMETRY_DISABLED=1 next", "dev:local": "NEXT_TELEMETRY_DISABLED=1 BUILD_ENV=local-api next", "docs": "storybook dev -p 6006 -c ./pfml-storybook", "docs:build": "storybook build -c ./pfml-storybook -o out-storybook", "docs:smoke-test": "npm run docs -- --ci --smoke-test --quiet", "build": "NEXT_TELEMETRY_DISABLED=1 next build", "lint": "npm run lint:ci -- --fix", "lint:ci": "eslint --cache --ext .js,.ts,.tsx --max-warnings 0 ./", "format": "prettier --cache --cache-location='./.prettier-cache' --write '**/*.{js,json,md,mdx,ts,tsx,scss,yaml,yml}'", "format-check": "prettier --cache --cache-location='./.prettier-cache' --check '**/*.{js,json,md,mdx,ts,tsx,scss,yaml,yml}'", "format-imports": "import-sort --write '!**/*.json'", "format-some": "prettier --cache --cache-location='./.prettier-cache' --write", "generate-claims-page-stories": "node ./bin/generate-claims-page-stories.js", "start": "next start", "test": "NODE_ENV=test jest", "test:ci:src": "npm test -- --ci --coverage --testPathIgnorePatterns=tests/storybook/", "test:ci:storybook": "npm test -- --ci tests/storybook/", "pretest:ci:storybook": "npm run generate-claims-page-stories", "test:update-snapshots": "npm test -- -u", "test:watch": "npm test -- --watch", "ts:check": "tsc --noEmit", "upload-new-relic-source-maps": "node ./bin/newRelicSourceMapUpload.js", "delete-new-relic-source-maps": "node ./bin/deleteNewRelicSourceMaps.js", "predocs": "npm run generate-claims-page-stories", "predocs:build": "npm run generate-claims-page-stories"}, "dependencies": {"@dword-design/eslint-plugin-import-alias": "^5.1.1", "@massds/mayflower-assets": "^14.0.0", "@uswds/uswds": "^3.0.2", "assert": "^2.0.0", "classnames": "^2.3.1", "compressorjs": "^1.2.1", "date-fns": "^4.1.0", "dayjs": "^1.10.7", "dayjs-business-time": "^1.0.4", "downloadjs": "^1.4.7", "fast-loops": "^1.1.4", "i18next": "^24.2.3", "js-cookie": "^3.0.0", "loader-utils": "^3.2.1", "lodash": "^4.17.21", "mockdate": "^3.0.5", "postcss": "^8.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-helmet": "^6.1.0", "react-i18next": "^15.4.1", "react-use": "^17.6.0", "ts-jest": "^29.2.5", "xstate": "^4.38.1"}, "devDependencies": {"@babel/core": "^7.23.2", "@babel/eslint-parser": "^7.17.0", "@faker-js/faker": "^9.3.0", "@newrelic/publish-sourcemap": "^5.1.0", "@next/bundle-analyzer": "^15.1.3", "@storybook/addon-a11y": "8.6.4", "@storybook/addon-docs": "8.6.4", "@storybook/addon-essentials": "8.6.4", "@storybook/addon-interactions": "8.6.4", "@storybook/addon-links": "8.6.4", "@storybook/addon-onboarding": "8.6.4", "@storybook/blocks": "8.6.4", "@storybook/cli": "8.6.4", "@storybook/components": "8.6.4", "@storybook/manager-api": "8.6.4", "@storybook/nextjs": "8.6.4", "@storybook/preview-api": "8.6.4", "@storybook/react": "8.6.4", "@storybook/test": "8.6.4", "@storybook/theming": "8.6.4", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.4.3", "@types/jest-axe": "^3.5.5", "@types/lodash": "^4.14.172", "@types/new-relic-browser": "^1.230.4", "@types/react": "^19.0.8", "@types/react-helmet": "^6.1.6", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.31.1", "@typescript-eslint/parser": "^8.31.1", "@uswds/compile": "^1.2.0", "babel-jest": "^29.7.0", "babel-loader": "^9.1.3", "babel-plugin-lodash": "^3.3.4", "default-browser-id": "5.0.0", "eslint": "^8.56.0", "eslint-config-nava": "^13.0.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-import": "^2.25.4", "eslint-plugin-jest": "^28.11.0", "eslint-plugin-jest-dom": "^5.5.0", "eslint-plugin-jsdoc": "^50.6.1", "eslint-plugin-jsx-a11y": "^6.5.1", "eslint-plugin-lodash": "^7.4.0", "eslint-plugin-react": "^7.29.2", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-storybook": "^0.5.12", "eslint-plugin-testing-library": "^7.1.1", "eslint-plugin-todo-plz": "^1.2.1", "eslint-plugin-you-dont-need-lodash-underscore": "^6.12.0", "import-sort-cli": "^6.0.0", "jest": "^29.7.0", "jest-axe": "^10.0.0", "jest-environment-jsdom": "^29.7.0", "mermaid": "^11.4.1", "next": "^15.2.4", "next-images": "^1.8.2", "next-transpile-modules": "10.0.1", "postcss-preset-env": "^10.1.5", "prettier": "^3.5.3", "react-is": "^19.1.0", "readdirp": "^3.6.0", "recursive-readdir-sync": "^1.0.6", "sass": "^1.51.0", "sass-loader": "^14.2.1", "storybook": "8.6.4", "typescript": "^5.5.4", "uuid": "^11.0.3", "webpack": "^5.94.0"}, "overrides": {"yargs-parser": "21.1.1", "semver": "7.5.2", "glob-parent": "6.0.2", "word-wrap": "1.2.4", "ejs": "3.1.10", "@massds/mayflower-assets": {"sass": "$sass"}, "ws": "8.17.1", "braces": "3.0.3", "micromatch": "4.0.8", "esbuild": "0.25.0"}, "browserslist": ["> 5%", "Last 2 versions"]}