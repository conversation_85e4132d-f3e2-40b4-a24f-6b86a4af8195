import {
  ClaimReviewStatusTypes,
  ClaimStatusTypes,
} from "src/models/WithClaimProperties";
import {
  ManagedRequirement,
  getLatestFollowUpDate,
  getSoonestReviewableManagedRequirement,
  mostRecentlyCreatedManagedRequirement,
} from "src/models/ManagedRequirement";

import { AbsencePeriod } from "src/models/AbsencePeriod";
import AbsencePeriodStatusTag from "src/components/AbsencePeriodStatusTag";
import ApiResourceCollection from "src/models/ApiResourceCollection";
import ButtonLink from "src/components/ButtonLink";
import Claim from "src/models/Claim";
import LeaveReason from "src/models/LeaveReason";
import Link from "next/link";
import { PageQueryParam } from "./SortDropdown";
import PaginationMeta from "src/models/PaginationMeta";
import PaginationNavigation from "src/components/PaginationNavigation";
import PaginationSummary from "src/components/PaginationSummary";
import { PortalFlow } from "src/hooks/usePortalFlow";
import React from "react";
import Table from "src/components/core/Table";
import { Trans } from "react-i18next";
import { WithClaimsProps } from "src/hoc/withClaims";
import classNames from "classnames";
import { compact } from "lodash";
import findKeyByValue from "src/utils/findKeyByValue";
import formatDate from "src/utils/formatDate";
import { formatDateRange } from "src/utils/formatDateRange";
import { isFeatureEnabled } from "src/services/featureFlags";
import { useTranslation } from "src/locales/i18n";

export interface PaginatedClaimsTableProps extends WithClaimsProps {
  claims: ApiResourceCollection<Claim>;
  getRouteFor: PortalFlow["getRouteFor"];
  hasOnlyUnverifiedEmployers: boolean;
  paginationMeta: PaginationMeta;
  updatePageQuery: (params: PageQueryParam[]) => void;
  showEmployer: boolean;
  /** Pass in the SortDropdown so it can be rendered in the expected inline UI position */
  sort: React.ReactNode;
}

type TableColumnKey =
  | "employee"
  | "case"
  | "employer_fein"
  | "employer"
  | "leave_details"
  | "review_status"
  | "timeline"
  | "todo";

const PaginatedClaimsTable = (props: PaginatedClaimsTableProps) => {
  const { claims, paginationMeta } = props;
  const { t } = useTranslation();
  // TODO (PFMLPB-22429): Remove this feature flag check once the feature is fully enabled
  const isSKIApplicationsTableEnabled = isFeatureEnabled(
    "enableSKIApplicationsTable"
  );
  /**
   * Columns rendered in the table.
   * Used as i18n context for rendering headers, and determining
   * what content to render in each column.
   */
  const visibleTableColumns: TableColumnKey[] = compact([
    "employee",
    "case",
    props.showEmployer && !isSKIApplicationsTableEnabled
      ? "employer_fein"
      : undefined, // TODO (PFMLPB-22429): Remove deprecated code after feature flag is removed
    props.showEmployer && isSKIApplicationsTableEnabled
      ? "employer"
      : undefined,
    "leave_details",
    !isSKIApplicationsTableEnabled ? "review_status" : undefined,
    isSKIApplicationsTableEnabled ? "timeline" : undefined,
    isSKIApplicationsTableEnabled ? "todo" : undefined,
  ]);

  /** Helper for determining what to display in our table body. Keeps conditions simpler in our render section */
  const getTableBodyState = () => {
    if (props.hasOnlyUnverifiedEmployers) return "no_verifications";
    return claims.isEmpty ? "empty" : "show_claims";
  };
  const tableBodyState = getTableBodyState();

  const handlePaginationNavigationClick = (pageOffset: number) => {
    props.updatePageQuery([
      {
        name: "page_offset",
        value: pageOffset,
      },
    ]);
  };

  return (
    <React.Fragment>
      <div className="margin-y-2 grid-row grid-gap flex-align-center">
        <div className="grid-col grid-col-12 margin-bottom-2 mobile-lg:grid-col-fill mobile-lg:margin-bottom-0">
          <PaginationSummary
            pageOffset={paginationMeta.page_offset}
            pageSize={paginationMeta.page_size}
            totalRecords={paginationMeta.total_records}
          />
        </div>
        <div className="grid-col grid-col-auto">{props.sort}</div>
      </div>
      <Table className="width-full" responsive scrollable>
        <thead>
          <tr>
            {visibleTableColumns.map((columnKey) => (
              <th key={columnKey} scope="col">
                {t("pages.employersApplications.tableColHeading", {
                  context: columnKey,
                })}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {tableBodyState === "no_verifications" && (
            <tr data-test="verification-instructions-row">
              <td colSpan={visibleTableColumns.length}>
                <Trans
                  i18nKey="pages.employersApplications.verificationInstructions"
                  components={{
                    "your-organizations-link": (
                      <a href={props.getRouteFor("VERIFY_ORG")} />
                    ),
                  }}
                />
              </td>
            </tr>
          )}
          {tableBodyState === "empty" && (
            <tr>
              <td colSpan={visibleTableColumns.length}>
                {t("pages.employersApplications.noClaimResults")}
              </td>
            </tr>
          )}
          {tableBodyState === "show_claims" &&
            claims.items.map((claim) => (
              <ClaimTableRow
                key={claim.fineos_absence_id}
                claim={claim}
                href={props.getRouteFor(
                  "VIEW_CLAIM",
                  {},
                  { absence_id: claim.fineos_absence_id }
                )}
                leaveAllotmentHref={props.getRouteFor(
                  "VIEW_LEAVE_ALLOTMENT",
                  {},
                  {
                    employer_id: claim.employer.employer_id,
                    employee_id: claim.employee?.employee_id,
                  }
                )}
                visibleTableColumns={visibleTableColumns}
              />
            ))}
        </tbody>
      </Table>
      {paginationMeta.total_pages > 1 && (
        <PaginationNavigation
          pageOffset={paginationMeta.page_offset}
          totalPages={paginationMeta.total_pages}
          onClick={handlePaginationNavigationClick}
        />
      )}
    </React.Fragment>
  );
};

interface ClaimTableRowProps {
  claim: Claim;
  href: string;
  leaveAllotmentHref: string;
  visibleTableColumns: TableColumnKey[];
}

const ClaimTableRow = (props: ClaimTableRowProps) => {
  const { claim, visibleTableColumns } = props;
  const { t } = useTranslation();
  // TODO (PFMLPB-22429): Remove this feature flag check once the feature is fully enabled
  const isSKIApplicationsTableEnabled = isFeatureEnabled(
    "enableSKIApplicationsTable"
  );

  const getColumnContents = (
    columnKey: (typeof visibleTableColumns)[number]
  ) => {
    switch (columnKey) {
      case "employee":
        return (
          <div className="text-medium">
            <div>
              <Trans
                i18nKey="data.user.any"
                values={{
                  value: claim.employee?.fullName || "--",
                }}
              />
            </div>
            <Link href={props.leaveAllotmentHref} className="text-no-wrap">
              <Trans i18nKey="pages.employersApplications.viewLeaveAllotmentLink" />
            </Link>
          </div>
        );
      case "case":
        return isSKIApplicationsTableEnabled ? (
          <div className="text-medium">
            <div>
              <Trans
                i18nKey="data.user.any"
                values={{
                  value: claim.fineos_absence_id,
                }}
              />
            </div>
            <Link href={props.href}>
              <Trans i18nKey="pages.employersApplications.applicationLink" />
            </Link>
          </div>
        ) : (
          <Link href={props.href}>
            <Trans
              i18nKey="data.user.any"
              values={{
                value: claim.fineos_absence_id,
              }}
            />
          </Link>
        );
      case "employer_fein":
      case "employer":
        return (
          <div className="text-wrap">
            <Trans
              i18nKey="data.user.any"
              values={{
                value: claim.employer.employer_dba,
              }}
            />
            <div className="font-body-2xs text-base-dark">
              <Trans
                i18nKey="data.user.any"
                values={{
                  value: claim.employer.employer_fein,
                }}
              />
            </div>
          </div>
        );
      case "leave_details":
        return (
          <LeaveDetailsCell
            absence_periods={claim.absence_periods}
            isSKIApplicationsTableEnabled={isSKIApplicationsTableEnabled}
          />
        );
      case "review_status":
        return <ReviewStatusCell href={props.href} claim={claim} />;
      case "timeline":
        return <TimelineCell claim={claim} />;
      case "todo":
        return <ToDoCell href={props.href} claim={claim} />;
    }
  };

  return (
    <tr className="text-top">
      <th
        scope="row"
        data-label={t("pages.employersApplications.tableColHeading", {
          context: visibleTableColumns[0],
        })}
      >
        {getColumnContents(visibleTableColumns[0])}
      </th>
      {visibleTableColumns.slice(1).map((columnKey) => (
        <td
          key={columnKey}
          data-label={t("pages.employersApplications.tableColHeading", {
            context: columnKey,
          })}
        >
          {getColumnContents(columnKey)}
        </td>
      ))}
    </tr>
  );
};

const LeaveDetailsCell = (props: {
  absence_periods: Claim["absence_periods"];
  isSKIApplicationsTableEnabled: boolean;
}) => {
  const { absence_periods } = props;
  const { t } = useTranslation();
  const maxPeriodsShown = 2;
  const additionalPeriods = absence_periods.length - maxPeriodsShown;

  return (
    <React.Fragment>
      {AbsencePeriod.sortNewToOld(absence_periods)
        .slice(0, maxPeriodsShown)
        .map((period, index) => (
          <div
            key={index}
            className={classNames(
              "grid-row grid-gap-1",
              // Put space between the cell's heading when stacked
              "margin-top-1 mobile-lg:margin-top-0",
              {
                // Put space between the absence periods
                "padding-top-2": index > 0,
              }
            )}
            data-testid="absence-period"
          >
            {props.isSKIApplicationsTableEnabled ? (
              <div className="desktop:grid-col-auto">
                {/* 2px top margin to vertically align text when side-by-side */}
                <AbsencePeriodStatusTag
                  className="minw-15 margin-top-2px margin-bottom-05"
                  request_decision={period.request_decision}
                />
                <div className="text-wrap">
                  <div className="text-medium">
                    {t("pages.employersApplications.absencePeriodReason", {
                      context: findKeyByValue(LeaveReason, period.reason),
                    })}
                  </div>

                  {formatDateRange(period.startDate, period.endDate)}

                  <div className="font-body-2xs">
                    {period.period_type &&
                      t("pages.employersApplications.absencePeriodType", {
                        context: period.period_type,
                      })}
                  </div>
                </div>
              </div>
            ) : (
              // TODO (PFMLPB-22429): Remove deprecated code after feature flag is removed
              <React.Fragment>
                <div className="desktop:grid-col-auto">
                  {/* 2px top margin to vertically align text when side-by-side */}
                  <AbsencePeriodStatusTag
                    className="minw-15 margin-top-2px margin-bottom-05"
                    request_decision={period.request_decision}
                  />
                </div>
                <div className="desktop:grid-col-fill text-wrap">
                  <div className="text-medium">
                    {t("pages.employersApplications.absencePeriodReason", {
                      context: findKeyByValue(LeaveReason, period.reason),
                    })}
                  </div>

                  {formatDateRange(period.startDate, period.endDate)}

                  <div className="font-body-2xs">
                    {period.period_type &&
                      t("pages.employersApplications.absencePeriodType", {
                        context: period.period_type,
                      })}
                  </div>
                </div>
              </React.Fragment>
            )}
          </div>
        ))}
      {absence_periods.length > maxPeriodsShown && (
        <div className="grid-row grid-gap padding-top-1">
          <div className="desktop:grid-col-auto minw-15"></div>
          <div className="desktop:grid-col-fill">
            <div className="padding-left-1 text-base-dark">
              <Trans
                i18nKey="pages.employersApplications.numAbsencePeriodsHidden"
                values={{
                  hiddenCount: additionalPeriods,
                }}
              />
            </div>
          </div>
        </div>
      )}
    </React.Fragment>
  );
};

const TimelineCell = (props: { claim: Claim }) => {
  const { claim } = props;
  const appealFiled = claim.appealFiled;
  const applicationStatus = claim.applicationStatus;
  const applicationStatusDate = claim.applicationStatusDate;
  const employerReviewStatusCompleted =
    claim.employerReviewStatus === ClaimReviewStatusTypes.completed;
  const employerReviewStatusDue =
    claim.employerReviewStatus === ClaimReviewStatusTypes.missed ||
    claim.employerReviewStatus === ClaimReviewStatusTypes.pending;
  const recentAbsencePeriod = AbsencePeriod.getNewestAbsencePeriod(
    claim.absence_periods
  );

  return (
    <React.Fragment>
      <div
        className={classNames("grid-row grid-gap-1", "font-body-2xs")}
        data-testid="timeline-period"
      >
        <div>
          {appealFiled && (
            <div
              title="appeal status"
              className="desktop:grid-col-fill padding-bottom-1"
            >
              <Trans i18nKey="pages.employersApplications.appealFiled" />
            </div>
          )}
          {recentAbsencePeriod &&
            getTimelineDFMLDecision(
              recentAbsencePeriod.request_decision,
              claim
            )}
          {recentAbsencePeriod?.request_decision ===
            ClaimStatusTypes.withdrawn &&
            !employerReviewStatusCompleted && (
              <Trans i18nKey="pages.employersApplications.applicationWithdrawn" />
            )}
          {employerReviewStatusCompleted &&
            getEmployerReviewCompleted(claim.managed_requirements)}
          {employerReviewStatusDue &&
            getEmployerReviewDataDue(claim.managed_requirements)}
          {applicationStatus &&
            getApplicationStatus(applicationStatus, applicationStatusDate)}
        </div>
      </div>
    </React.Fragment>
  );
};

const ToDoCell = (props: { href: string; claim: Claim }) => {
  const { t } = useTranslation();
  const { claim } = props;
  const managedRequirements = claim.managed_requirements;
  const isReviewable = claim.isReviewable;
  const employerReviewStatus = claim.employerReviewStatus;

  const reviewableManagedRequirement =
    getSoonestReviewableManagedRequirement(managedRequirements);

  if (managedRequirements.length === 0) {
    return (
      <React.Fragment>
        <div className="font-body-2xs">
          <Trans i18nKey="pages.employersApplications.noActionRequired" />
        </div>
      </React.Fragment>
    );
  }

  if (isReviewable) {
    return (
      <React.Fragment>
        <ButtonLink className="margin-bottom-05" href={props.href}>
          {t("pages.employersApplications.reviewAction")}
        </ButtonLink>
        <br />
        <div title="employers applications due" className="font-body-2xs">
          <Trans
            i18nKey="pages.employersApplications.dueBy"
            values={{
              date: formatDate(
                reviewableManagedRequirement?.follow_up_date
              ).short(),
            }}
          />
        </div>
      </React.Fragment>
    );
  }

  return (
    <React.Fragment>
      <div className="font-body-2xs">
        <Trans i18nKey="pages.employersApplications.noActionRequired" />
      </div>
      {employerReviewStatus && (
        <div className="font-body-2xs">
          {employerReviewStatus === ClaimReviewStatusTypes.completed &&
            claim.hasPendingStatus && (
              <Trans i18nKey="pages.employersApplications.employerReviewCompleted" />
            )}
          {employerReviewStatus === ClaimReviewStatusTypes.missed && (
            <Trans i18nKey="pages.employersApplications.employerReviewDeadlineMissed" />
          )}
        </div>
      )}
    </React.Fragment>
  );
};

// TODO (PFMLPB-22429): Remove this code once the feature is fully enabled
const ReviewStatusCell = (props: { href: string; claim: Claim }) => {
  const { t } = useTranslation();
  const { claim } = props;
  const managedRequirements = claim.managed_requirements;
  const isReviewable = claim.isReviewable;

  const reviewableManagedRequirement =
    getSoonestReviewableManagedRequirement(managedRequirements);

  if (managedRequirements.length === 0) {
    return <React.Fragment>--</React.Fragment>;
  }

  if (isReviewable) {
    return (
      <React.Fragment>
        <ButtonLink className="margin-bottom-05" href={props.href}>
          {t("pages.employersApplications.reviewAction")}
        </ButtonLink>
        <br />
        <div title="employers applications respond by">
          <Trans
            i18nKey="pages.employersApplications.respondBy"
            values={{
              date: formatDate(
                reviewableManagedRequirement?.follow_up_date
              ).short(),
            }}
          />
        </div>
      </React.Fragment>
    );
  }

  return (
    <React.Fragment>
      {formatDate(getLatestFollowUpDate(managedRequirements)).short()}
    </React.Fragment>
  );
};

const getApplicationStatus = (
  applicationStatus: string,
  applicationStatusDate: string | null
) => {
  return (
    <div title="employers application status" className="desktop:grid-col-fill">
      <Trans
        i18nKey="pages.employersApplications.applicationStatus"
        values={{
          status: applicationStatus,
          date: formatDate(applicationStatusDate).short(),
        }}
      />
    </div>
  );
};

const getTimelineDFMLDecision = (
  requestDecision: string | null,
  claim: Claim
) => {
  const reviewableManagedRequirement = mostRecentlyCreatedManagedRequirement(
    claim.managed_requirements
  );
  const dfmlDecisionDate = reviewableManagedRequirement?.dfml_decision_date;
  if (
    claim.employerReviewStatus === ClaimReviewStatusTypes.pending &&
    claim.hasPendingStatus
  ) {
    return;
  }
  if (
    (claim.employerReviewStatus === ClaimReviewStatusTypes.completed ||
      claim.employerReviewStatus === ClaimReviewStatusTypes.missed) &&
    claim.hasPendingStatus
  ) {
    return (
      <div className="desktop:grid-col-fill padding-bottom-1">
        <Trans
          i18nKey="pages.employersApplications.dfmlDecision"
          values={{
            date: formatDate(dfmlDecisionDate).short(),
          }}
        />
      </div>
    );
  }
  return (
    <React.Fragment>
      {requestDecision === ClaimStatusTypes.withdrawn && (
        <div className="desktop:grid-col-fill padding-bottom-1">
          <Trans i18nKey="pages.employersApplications.applicationWithdrawn" />
        </div>
      )}
      {requestDecision === ClaimStatusTypes.cancelled && (
        <div className="desktop:grid-col-fill padding-bottom-1">
          <Trans i18nKey="pages.employersApplications.applicationCancelled" />
        </div>
      )}
      {claim.hasNoPendingWithdrawnCancelledStatus && (
        <div className="desktop:grid-col-fill padding-bottom-1">
          <Trans i18nKey="pages.employersApplications.dfmlDecisionReached" />
        </div>
      )}
    </React.Fragment>
  );
};

const getEmployerReviewCompleted = (
  managedRequirements: ManagedRequirement[]
) => {
  const recentManagedRequirement =
    mostRecentlyCreatedManagedRequirement(managedRequirements);
  return (
    <div
      title="employers employer review"
      className="desktop:grid-col-fill padding-bottom-1"
    >
      <Trans
        i18nKey="pages.employersApplications.employerReviewCompletedBy"
        values={{
          date: formatDate(recentManagedRequirement?.responded_at).short(),
          firstName: recentManagedRequirement?.responded_user_first_name,
          lastName: recentManagedRequirement?.responded_user_last_name,
        }}
      />
    </div>
  );
};

const getEmployerReviewDataDue = (
  managedRequirements: ManagedRequirement[]
) => {
  return (
    <div
      title="employers employer review"
      className="desktop:grid-col-fill padding-bottom-1"
    >
      <Trans
        i18nKey="pages.employersApplications.employerReviewDueBy"
        values={{
          date: formatDate(getLatestFollowUpDate(managedRequirements)).short(),
        }}
      />
    </div>
  );
};

export default PaginatedClaimsTable;
