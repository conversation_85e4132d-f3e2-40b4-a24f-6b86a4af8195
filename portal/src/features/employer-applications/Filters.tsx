import React, {
  FormEvent,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";

import Button from "src/components/core/Button";
import Dropdown from "src/components/core/Dropdown";
import { GetClaimsParams } from "src/api/ClaimsApi";
import Icon from "src/components/core/Icon";
import InputChoiceGroup from "src/components/core/InputChoiceGroup";
import { PageQueryParam } from "./SortDropdown";
import { Trans } from "react-i18next";
import { UserLeaveAdministrator } from "src/models/User";
import { isEqual } from "lodash";
import { isFeatureEnabled } from "src/services/featureFlags";
import useFormState from "src/hooks/useFormState";
import useFunctionalInputProps from "src/hooks/useFunctionalInputProps";
import { useTranslation } from "src/locales/i18n";

/**
 * UI variables
 */
const FILTERS_CONTAINER_ID = "filters";
const ARRAY_FILTERS: Array<keyof ClaimsFilterParams> = [
  "to_do",
  "application_status",
];

export type ClaimsFilterParams = Omit<
  GetClaimsParams,
  | "employee_id"
  | "allow_hrd"
  | "search"
  | "order_direction"
  | "page_offset"
  | "order_by"
>;

export type FilterFormState = {
  [Property in keyof ClaimsFilterParams]: Exclude<
    ClaimsFilterParams[Property],
    undefined
  > extends Array<infer T>
    ? T[]
    : "" | ClaimsFilterParams[Property];
};

export interface FiltersProps {
  params: GetClaimsParams;
  updatePageQuery: (params: PageQueryParam[]) => void;
  verifiedEmployers: UserLeaveAdministrator[];
}

const Filters = ({
  updatePageQuery,
  verifiedEmployers,
  ...props
}: FiltersProps) => {
  // TODO (PFMLPB-22982): Remove this feature flag check once the enableSKIApplicationsFilter feature is fully enabled
  const isSKIApplicationsFilterEnabled = isFeatureEnabled(
    "enableSKIApplicationsFilter"
  );
  const { t } = useTranslation();

  /**
   * Returns all filter fields with their values set based on
   * what's currently being applied to the API requests
   */
  const submittedFormState = useMemo(() => {
    const {
      employer_id,
      // Empty strings represent the default "Show all" option.
      // We use empty strings to avoid passing a param to the API
      // when these default options are selected, since they really mean:
      // "no filter selected".
      // TODO (PFMLPB-22982): Remove is_reviewable when the SKI filter is enabled for all users
      is_reviewable = "",
      // TODO (PFMLPB-22982): Remove request_decision when the SKI filter is enabled for all users
      request_decision = "",
      to_do = [],
      application_status = [],
    } = props.params;
    return {
      employer_id,
      is_reviewable,
      request_decision,
      to_do,
      application_status,
    } satisfies FilterFormState;
  }, [props.params]);
  // Throwaway init object for form due to form mutating reference objects.
  // Use structuredClone instead when availble.
  const initFormState = useMemo(
    () => JSON.parse(JSON.stringify(submittedFormState)),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  /**
   * Form visibility and state management
   */
  const [showFilters, setShowFilters] = useState(false);
  const { formState, updateFields } = useFormState(initFormState);
  const getFunctionalInputProps = useFunctionalInputProps({
    formState,
    updateFields,
  });

  // Flatten multi-selects and then gather active
  const activeFilters = Object.entries(submittedFormState)
    .flatMap(([key, value]) => {
      if (Array.isArray(value)) return value.map((v) => [key, v]);
      return [[key, value]];
    })
    .filter(([_, value]) => {
      return (
        value != null &&
        value !== "" &&
        (!Array.isArray(value) || value.length > 0)
      );
    });
  const areFiltersActive = activeFilters.length > 0;
  const changedFilters = Object.entries(submittedFormState).filter(
    ([name, value]) => {
      const formStateValue = formState[name];
      return !isEqual(value, formStateValue);
    }
  );
  const areFiltersChanged = changedFilters.length > 0;
  const isResetAllowed = areFiltersActive || areFiltersChanged;

  /**
   * Watch the router query for changes, and update the selected form fields
   * anytime those change. This is handy since a filter might get removed
   * outside of this component, and it also saves us from calling
   * updateFields every time we call updatePageQuery. Instead, we treat
   * the query string as the source of truth, and react to its changes.
   */
  useEffect(() => {
    // replace with structuredClone when available
    const slowCopy = JSON.parse(JSON.stringify(submittedFormState));
    // pass a copy so original object doesn't get mutated
    updateFields(slowCopy);
  }, [submittedFormState, updateFields]);

  /**
   * Event handler for when the user applies their status and
   * organization filter selections
   */
  const handleSubmit = useCallback(
    (evt: FormEvent) => {
      evt.preventDefault();
      const params = Object.entries(formState).map(([k, v]) => ({
        name: k,
        value: v,
      }));

      updatePageQuery([
        ...params,
        {
          // Reset the page to 1 since filters affect what shows on the first page
          name: "page_offset",
          value: "1",
        },
      ]);

      setShowFilters(false);
    },
    [formState, updatePageQuery]
  );

  /**
   * Event handler for the "Reset filters" action
   */
  const handleFilterReset = useCallback(() => {
    const emptiedEntries = Object.entries(formState).map(([k]) => [k, ""]);
    const emptiedParams = emptiedEntries.map(([k, v]) => ({
      name: k,
      value: v,
    }));
    const emptiedObject = Object.fromEntries(emptiedEntries);

    // Reset ui
    updateFields(emptiedObject);

    updatePageQuery([
      ...emptiedParams,
      {
        // Reset the page to 1 since filters affect what shows on the first page
        name: "page_offset",
        value: "1",
      },
    ]);
  }, [formState, updateFields, updatePageQuery]);

  /**
   * Click event handler for an individual filter's removal button.
   * @param name - Filter query param name
   * @param value - Leave empty to remove filter, or pass in the updated
   *  value if the filter is a checkbox field
   */
  const handleRemoveFilterClick = useCallback(
    (name: keyof FilterFormState, value: string = "") => {
      const updatedPageQueryParams: PageQueryParam[] = [
        {
          // Reset the page to 1 since filters affect what shows on the first page
          name: "page_offset",
          value: "1",
        },
      ];

      if (ARRAY_FILTERS.includes(name)) {
        const currValue = submittedFormState[name] as string[];
        // Remove the specific value from the array
        const updatedValue = currValue.filter((item) => item !== value) ?? [
          value,
        ];
        updatedPageQueryParams.push({
          name,
          value: updatedValue, // Update `to_do` with the filtered array
        });
      } else {
        // Handle other filters
        updatedPageQueryParams.push({
          name,
          value, // Update `to_do` with the filtered array
        });
      }

      updatePageQuery(updatedPageQueryParams);
    },
    [submittedFormState, updatePageQuery]
  );

  const handleFilterToggleClick = useCallback(() => {
    setShowFilters(!showFilters);
  }, [showFilters]);

  return (
    <React.Fragment>
      <div className="padding-bottom-3 bg-base-lightest padding-x-3">
        <Button
          title="filters show with count"
          aria-controls={FILTERS_CONTAINER_ID}
          aria-expanded={showFilters ? "true" : "false"}
          onClick={handleFilterToggleClick}
          variation="outline"
        >
          {areFiltersActive && !showFilters ? (
            <Trans
              i18nKey="pages.employersApplications.filtersShowWithCount"
              values={{ count: activeFilters.length }}
            />
          ) : (
            t("pages.employersApplications.filtersToggle", {
              context: showFilters ? "expanded" : undefined,
            })
          )}
        </Button>
      </div>

      {/* `hidden` is set on div, instead of the form, to workaround a quirk in E2E scripts (https://lwd.atlassian.net/browse/PORTAL-1592) */}
      <div hidden={!showFilters} id={FILTERS_CONTAINER_ID}>
        <form
          className="bg-primary-lighter padding-x-3 padding-top-1px padding-bottom-3 usa-form maxw-none"
          onSubmit={handleSubmit}
          name="filters"
        >
          {verifiedEmployers.length > 1 && (
            <Dropdown
              autocomplete
              {...getFunctionalInputProps("employer_id")}
              choices={[
                ...verifiedEmployers.map((employer) => ({
                  label: `${employer.employer_dba} (${employer.employer_fein})`,
                  value: employer.employer_id,
                })),
              ]}
              label={
                /* TODO (PFMLPB-22982): Remove this feature flag check once the enableSKIApplicationsFilter feature is fully enabled */
                t(
                  isSKIApplicationsFilterEnabled
                    ? "pages.employersApplications.filterOrgOrEinLabel"
                    : "pages.employersApplications.filterOrgsLabel"
                )
              }
              smallLabel
            />
          )}
          {isSKIApplicationsFilterEnabled ? (
            <React.Fragment>
              <div className="grid-row grid-gap-lg tablet:padding-y-2">
                <div className="desktop:grid-col-2 tablet:grid-col-4">
                  <ApplicationStatusFilter
                    {...getFunctionalInputProps("application_status")}
                  />
                </div>
                <div className="desktop:grid-col-3 tablet:grid-col-4">
                  <TodoFilter {...getFunctionalInputProps("to_do")} />
                </div>
              </div>
            </React.Fragment>
          ) : (
            <React.Fragment>
              <ReviewableFilter {...getFunctionalInputProps("is_reviewable")} />
              <RequestDecisionFilter
                {...getFunctionalInputProps("request_decision")}
              />
            </React.Fragment>
          )}

          <Button type="submit" disabled={!areFiltersChanged}>
            {t("pages.employersApplications.filtersApply")}
          </Button>

          <Button
            type="submit"
            variation="outline"
            onClick={handleFilterReset}
            hidden={!isResetAllowed}
          >
            {t("pages.employersApplications.filtersReset")}
          </Button>
        </form>
      </div>

      {areFiltersActive && (
        <div
          className="margin-top-1 margin-bottom-4"
          data-testid="filters-menu"
        >
          <strong className="margin-right-2 display-inline-block">
            {t("pages.employersApplications.filterNavLabel")}
          </strong>
          {submittedFormState.employer_id && (
            <FilterMenuButton
              onClick={() => handleRemoveFilterClick("employer_id")}
            >
              {
                verifiedEmployers.find(
                  ({ employer_id }) =>
                    employer_id === submittedFormState.employer_id
                )?.employer_dba
              }
            </FilterMenuButton>
          )}

          {submittedFormState.is_reviewable && (
            <FilterMenuButton
              onClick={() => handleRemoveFilterClick("is_reviewable")}
            >
              {t("pages.employersApplications.filterReviewable", {
                context: submittedFormState.is_reviewable,
              })}
            </FilterMenuButton>
          )}

          {submittedFormState.to_do.map((toDo: string) => (
            <FilterMenuButton
              key={toDo}
              onClick={() => handleRemoveFilterClick("to_do", toDo)}
            >
              {t("pages.employersApplications.filterToDo", {
                context: toDo,
              })}
            </FilterMenuButton>
          ))}

          {submittedFormState.application_status.map((status: string) => (
            <FilterMenuButton
              key={status}
              onClick={() =>
                handleRemoveFilterClick("application_status", status)
              }
            >
              {t("pages.employersApplications.filterApplicationStatus", {
                context: status,
              })}
            </FilterMenuButton>
          ))}

          {submittedFormState.request_decision && (
            <FilterMenuButton
              onClick={() => handleRemoveFilterClick("request_decision")}
            >
              {t("pages.employersApplications.filterRequestDecision", {
                context: submittedFormState.request_decision,
              })}
            </FilterMenuButton>
          )}
        </div>
      )}
    </React.Fragment>
  );
};

interface FilterMenuButtonProps {
  children: React.ReactNode;
  onClick: React.MouseEventHandler<HTMLButtonElement>;
}

const FilterMenuButton = (props: FilterMenuButtonProps) => {
  const { t } = useTranslation();

  return (
    <Button
      className="text-bold text-no-underline hover:text-underline margin-right-2"
      onClick={props.onClick}
      type="button"
      variation="unstyled"
    >
      <Icon
        name="cancel"
        size={3}
        className="text-ttop margin-top-neg-1px margin-right-05"
        fill="currentColor"
      />
      <span className="usa-sr-only">
        {t("pages.employersApplications.filterRemove")}
      </span>
      {props.children}
    </Button>
  );
};

// TODO (PFMLPB-22982): Remove this entry once the enableSKIApplicationsFilter feature is fully enabled
function RequestDecisionFilter(props: {
  onChange: React.ChangeEventHandler<HTMLInputElement>;
  name: string;
  value: string;
}) {
  const { t } = useTranslation();
  const choices: Array<{
    label: string;
    value: "" | Required<ClaimsFilterParams>["request_decision"];
  }> = [
    {
      label: t("pages.employersApplications.filterRequestDecision", {
        context: "approved",
      }),
      value: "approved",
    },
    {
      label: t("pages.employersApplications.filterRequestDecision", {
        context: "cancelled",
      }),
      value: "cancelled",
    },
    {
      label: t("pages.employersApplications.filterRequestDecision", {
        context: "denied",
      }),
      value: "denied",
    },
    {
      label: t("pages.employersApplications.filterRequestDecision", {
        context: "pending",
      }),
      value: "pending",
    },
    {
      label: t("pages.employersApplications.filterRequestDecision", {
        context: "withdrawn",
      }),
      value: "withdrawn",
    },
    {
      label: t("pages.employersApplications.filterRequestDecision", {
        context: "all",
      }),
      value: "",
    },
  ];

  return (
    <InputChoiceGroup
      name={props.name}
      onChange={props.onChange}
      choices={choices.map((choice) => ({
        checked: props.value === choice.value,
        ...choice,
      }))}
      label={t("pages.employersApplications.filterRequestDecisionLabel")}
      type="radio"
      smallLabel
    />
  );
}

// TODO (PFMLPB-22982): Remove once the enableSKIApplicationsFilter feature is fully enabled
function ReviewableFilter(props: {
  onChange: React.ChangeEventHandler<HTMLInputElement>;
  name: string;
  value: string;
}) {
  const { t } = useTranslation();
  const choices: Array<{
    label: string;
    value: "" | Required<ClaimsFilterParams>["is_reviewable"];
  }> = [
    {
      label: t("pages.employersApplications.filterReviewable", {
        context: "yes",
      }),
      value: "yes",
    },
    {
      label: t("pages.employersApplications.filterReviewable", {
        context: "no",
      }),
      value: "no",
    },
    {
      label: t("pages.employersApplications.filterReviewable", {
        context: "all",
      }),
      value: "",
    },
  ];

  return (
    <InputChoiceGroup
      name={props.name}
      onChange={props.onChange}
      choices={choices.map((choice) => ({
        checked: props.value === choice.value,
        ...choice,
      }))}
      label={t("pages.employersApplications.filterReviewableLabel")}
      type="radio"
      smallLabel
    />
  );
}

function TodoFilter(props: {
  onChange: React.ChangeEventHandler<HTMLInputElement>;
  name: string;
  value: string[];
}) {
  const { t } = useTranslation();
  const choices = [
    {
      label: t("pages.employersApplications.filterToDo", {
        context: "review_due",
      }),
      value: "review_due",
    },
    {
      label: t("pages.employersApplications.filterToDo", {
        context: "no_action",
      }),
      value: "no_action",
    },
  ];

  return (
    <InputChoiceGroup
      name={props.name}
      onChange={props.onChange}
      choices={choices.map((choice) => ({
        checked: props.value.includes(choice.value),
        ...choice,
      }))}
      label={t("pages.employersApplications.filterToDoLabel")}
      type="checkbox"
      smallLabel
    />
  );
}

function ApplicationStatusFilter(props: {
  onChange: React.ChangeEventHandler<HTMLInputElement>;
  name: string;
  value: string[];
}) {
  const { t } = useTranslation();
  const choices = [
    {
      label: t("pages.employersApplications.filterApplicationStatus", {
        context: "approved",
      }),
      value: "approved",
    },
    {
      label: t("pages.employersApplications.filterApplicationStatus", {
        context: "cancelled",
      }),
      value: "cancelled",
    },
    {
      label: t("pages.employersApplications.filterApplicationStatus", {
        context: "denied",
      }),
      value: "denied",
    },
    {
      label: t("pages.employersApplications.filterApplicationStatus", {
        context: "pending",
      }),
      value: "pending",
    },
    {
      label: t("pages.employersApplications.filterApplicationStatus", {
        context: "withdrawn",
      }),
      value: "withdrawn",
    },
  ];

  return (
    <div className="desktop tablet:border-right-1px border-base-light">
      <InputChoiceGroup
        name={props.name}
        onChange={props.onChange}
        choices={choices.map((choice) => ({
          checked: props.value.includes(choice.value),
          ...choice,
        }))}
        label={t("pages.employersApplications.filterApplicationStatusLabel")}
        type="checkbox"
        smallLabel
      />
    </div>
  );
}

export default Filters;
