import InputDate from "src/components/core/InputDate";
import { InputQuarterlyDate } from "src/components/core/InputQuarterlyDate";
import React from "react";
import { useTranslation } from "src/locales/i18n";

interface InsurancePlanInputDateProps {
  labelTranslationKey: string;
  inputProps: {
    errorMsg: React.JSX.Element | undefined;
    name: string;
    onChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
    value: unknown;
  };
  isQuarterly?: boolean;
  value: string | undefined;
}

const InsurancePlanInputDate: React.FC<InsurancePlanInputDateProps> = ({
  labelTranslationKey,
  inputProps,
  value,
  isQuarterly,
}) => {
  const { t } = useTranslation();
  const bt = (translationKey: string) => <strong>{t(translationKey)}</strong>;

  if (isQuarterly) {
    return (
      <InputQuarterlyDate
        {...inputProps}
        smallLabel
        label={t(
          `pages.employersExemptionsInsuranceDetails.${labelTranslationKey}`
        )}
        quarterLabel={t("components.form.dateInputQuarterLabel")}
        yearLabel={t("components.form.dateInputYearLabel")}
        value={value}
        hint
        dropdownDefault="pages.employersExemptionsInsuranceDetails.dropdownDefault"
      />
    );
  }
  return (
    <InputDate
      {...inputProps}
      smallLabel
      label={t(
        `pages.employersExemptionsInsuranceDetails.${labelTranslationKey}`
      )}
      example={t("components.form.dateInputExample")}
      dayLabel={bt("components.form.dateInputDayLabel")}
      monthLabel={bt("components.form.dateInputMonthLabel")}
      yearLabel={bt("components.form.dateInputYearLabel")}
      value={value}
      hint
    />
  );
};

export default InsurancePlanInputDate;
