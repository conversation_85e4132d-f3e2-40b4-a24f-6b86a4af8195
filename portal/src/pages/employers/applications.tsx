import React, { FormEvent, useCallback, useMemo, useRef } from "react";
import SortDropdown, {
  PageQueryParam,
} from "src/features/employer-applications/SortDropdown";
import withUser, { WithUserProps } from "src/hoc/withUser";

import Button from "src/components/core/Button";
import EmployerDownloadCSV from "src/components/EmployerDownloadCSV";
import EmployerNavigationTabs from "src/components/EmployerNavigationTabs";
import Filters from "src/features/employer-applications/Filters";
import InputText from "src/components/core/InputText";
import NoOrganizationsAlert from "src/components/NoOrganizationsAlert";
import type { PageProps } from "types/common";
import PaginatedClaimsTable from "src/features/employer-applications/PaginatedClaimsTable";
import Title from "src/components/core/Title";
import UnverifiedOrganizationAlert from "src/components/UnverifiedOrganizationAlert";
import { get } from "lodash";
import { getEmployersApplicationsPageQuery } from "src/utils/transformRouterQuery";
import isBlank from "src/utils/isBlank";
import { isFeatureEnabled } from "src/services/featureFlags";
import { useChatWidget } from "src/hooks/useChatWidget";
import useFormState from "src/hooks/useFormState";
import useFunctionalInputProps from "src/hooks/useFunctionalInputProps";
import { useTranslation } from "src/locales/i18n";
import withClaims from "src/hoc/withClaims";

// Set to prevent reaching the 30 second timeout for large CSV downloads.
const MAX_CSV_EXPORT_CLAIMS_LIMIT = 50000;

export interface EmployerApplicationsPageProps
  extends PageProps,
    WithUserProps {}

export const Applications = (props: EmployerApplicationsPageProps) => {
  useChatWidget(props.user, props.appLogic.chatWidget);
  const { t } = useTranslation();
  const introElementRef = useRef<HTMLElement>(null);
  // Use query-safe version for withClaims
  const [origParams, routerQueryParams] = useMemo(
    () => getEmployersApplicationsPageQuery(props.query),
    [props.query]
  );

  // TODO (PFMLPB-22297): Cleanup feature flag
  const leaveAdminCSVExportEnabled = isFeatureEnabled("enableSKICSVExport");

  /**
   * Update the page's query string, to load a different page number,
   * or change the filter/sort of the loaded claims. The name/value
   * are merged with the existing query string.
   */
  const updatePageQuery = useCallback(
    (paramsToUpdate: PageQueryParam[]) => {
      const params = Object.fromEntries(Object.entries(props.query));

      paramsToUpdate.forEach(({ name, value }) => {
        if (
          isBlank(value) ||
          (typeof value !== "number" && value.length === 0)
        ) {
          // Remove param if its value is null, undefined, empty string, or empty array
          delete params[name];
        } else {
          params[name] = value.toString();
        }
      });

      // Our withClaims component watches the query string and
      // will trigger an API request when it changes.
      props.appLogic.portalFlow.updateQuery(params);

      // Scroll user back to top of the table actions
      if (introElementRef.current) introElementRef.current.scrollIntoView();
    },
    [props.appLogic.portalFlow, props.query]
  );

  const PaginatedClaimsTableWithClaims = useMemo(
    () => withClaims(PaginatedClaimsTable, origParams),
    [origParams]
  );
  const claimsTableProps = {
    updatePageQuery,
    getRouteFor: props.appLogic.portalFlow.getRouteFor,
    hasOnlyUnverifiedEmployers: props.user.hasOnlyUnverifiedEmployers,
    showEmployer: props.user.user_leave_administrators.length > 1,
    sort: (
      <SortDropdown
        order_by={routerQueryParams.order_by}
        order_direction={routerQueryParams.order_direction}
        updatePageQuery={updatePageQuery}
      />
    ),
  };

  const totalClaims = props?.appLogic?.claims?.paginationMeta?.total_records;
  const hasValidNumberOfClaims =
    totalClaims > 0 && totalClaims < MAX_CSV_EXPORT_CLAIMS_LIMIT;
  return (
    <React.Fragment>
      <EmployerNavigationTabs activePath={props.appLogic.portalFlow.pathname} />
      <Title>{t("pages.employersApplications.title")}</Title>

      <div className="measure-6">
        {!props.user.hasAssociatedOrgs && <NoOrganizationsAlert />}
        {props.user.hasVerifiableEmployer && <UnverifiedOrganizationAlert />}
      </div>

      <section ref={introElementRef} className="margin-top-2">
        <Search
          initialValue={get(routerQueryParams, "search", "")}
          updatePageQuery={updatePageQuery}
        />
      </section>
      <Filters
        params={routerQueryParams}
        updatePageQuery={updatePageQuery}
        verifiedEmployers={props.user.verifiedEmployers}
      />
      {leaveAdminCSVExportEnabled && hasValidNumberOfClaims && (
        <EmployerDownloadCSV appLogic={props.appLogic} />
      )}
      <PaginatedClaimsTableWithClaims
        appLogic={props.appLogic}
        {...claimsTableProps}
      />
    </React.Fragment>
  );
};

interface SearchProps {
  /** The current search value */
  initialValue?: string;
  updatePageQuery: (params: PageQueryParam[]) => void;
}

const Search = (props: SearchProps) => {
  const { initialValue, updatePageQuery } = props;
  const { t } = useTranslation();

  const { formState, updateFields } = useFormState({ search: initialValue });
  const getFunctionalInputProps = useFunctionalInputProps({
    formState,
    updateFields,
  });

  const handleSubmit = (evt: FormEvent) => {
    evt.preventDefault();

    updatePageQuery([
      {
        name: "search",
        value: get(formState, "search", ""),
      },
      {
        // Reset the page to 1 since search affects what shows on the first page
        name: "page_offset",
        value: "1",
      },
    ]);
  };

  return (
    <div className="bg-base-lightest padding-x-3 padding-top-1px padding-bottom-2">
      <form className="usa-form grid-row" onSubmit={handleSubmit}>
        <div className="grid-col-fill tablet:grid-col-auto">
          <InputText
            {...getFunctionalInputProps("search")}
            label={t("pages.employersApplications.searchLabel")}
            smallLabel
          />
        </div>
        <div className="grid-col-auto flex-align-self-end">
          <Button className="width-auto" type="submit">
            {t("pages.employersApplications.searchSubmit")}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default withUser(Applications);
