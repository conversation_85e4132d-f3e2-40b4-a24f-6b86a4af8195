import { DocumentType, findDocumentsByTypes } from "src/models/Document";
import EmployerBenefit, {
  EmployerBenefitType,
} from "src/models/EmployerBenefit";
import {
  EmploymentStatus,
  Ethnicity,
  Gender,
  IndustrySector,
  Race,
  ReasonQualifier,
  ReducedScheduleLeavePeriod,
  RelationshipToCaregiver,
  WorkPattern,
  WorkPatternType,
} from "src/models/BenefitsApplication";
import OtherIncome, {
  OtherIncomeFrequency,
  OtherIncomeType,
} from "src/models/OtherIncome";
import PreviousLeave, { PreviousLeaveReason } from "src/models/PreviousLeave";
import React, { useEffect, useState } from "react";
import Step, { ClaimSteps } from "src/models/Step";
import { compact, get } from "lodash";
import withBenefitsApplication, {
  WithBenefitsApplicationProps,
} from "src/hoc/withBenefitsApplication";
import withClaimDocuments, {
  WithClaimDocumentsProps,
} from "src/hoc/withClaimDocuments";

import Address from "src/models/Address";
import Alert from "src/components/core/Alert";
import BackButton from "src/components/BackButton";
import BenefitYearsSpanAlert from "src/features/benefits-applications/BenefitYearsSpanAlert";
import Heading from "src/components/core/Heading";
import HeadingPrefix from "src/components/core/HeadingPrefix";
import Lead from "src/components/core/Lead";
import LeaveReason from "src/models/LeaveReason";
import { Localize } from "types/common";
import Noun from "src/utils/Noun";
import { NullableQueryParams } from "src/utils/routeWithParams";
import { PhoneType } from "src/models/Phone";
import ReviewCertificationUploads from "src/features/benefits-applications/ReviewCertificationUploads";
import ReviewHeading from "src/components/ReviewHeading";
import ReviewRow from "src/components/ReviewRow";
import ReviewTaxAndPaymentPreferences from "src/features/benefits-applications/ReviewTaxAndPaymentPreferences";
import Spinner from "src/components/core/Spinner";
import ThrottledButton from "src/components/ThrottledButton";
import Title from "src/components/core/Title";
import { Trans } from "react-i18next";
import WeeklyTimeTable from "src/components/WeeklyTimeTable";
import claimantConfigs from "src/flows/claimant";
import convertHoursToMinutesHours from "src/utils/convertHoursToMinutesHours";
import convertMinutesToHours from "src/utils/convertMinutesToHours";
import findKeyByValue from "src/utils/findKeyByValue";
import formatDate from "src/utils/formatDate";
import { formatDateRange } from "src/utils/formatDateRange";
import getI18nContextForIntermittentFrequencyDuration from "src/utils/getI18nContextForIntermittentFrequencyDuration";
import getMissingRequiredFields from "src/utils/getMissingRequiredFields";
import hasDocumentsLoadError from "src/utils/hasDocumentsLoadError";
import isBlank from "src/utils/isBlank";
import { isFeatureEnabled } from "src/services/featureFlags";
import tracker from "src/services/tracker";
import transformLanguage from "src/utils/transformLanguage";
import { useTranslation } from "src/locales/i18n";

/**
 * Format an address onto a single line, or return undefined if the address
 * is empty.
 */
function formatAddress(address: Partial<Address> | null) {
  let formatted = compact([
    get(address, "line_1"),
    get(address, "line_2"),
    get(address, "city"),
    get(address, "state"),
  ]).join(", ");

  const zip = get(address, "zip");
  if (zip) formatted += " " + zip;

  return formatted;
}

declare global {
  interface Window {
    Localize?: Localize;
  }
}

/**
 * Application review page, allowing a user to review the info
 * they've entered before they submit it.
 */
export const Review = (
  props: WithClaimDocumentsProps & WithBenefitsApplicationProps
) => {
  const { t } = useTranslation();
  const { appLogic, claim, documents, isLoadingDocuments, user } = props;

  const { errors, clearRequiredFieldErrors } = appLogic;
  const { hasDeferredCertDoc, setHasDeferredCertDoc } = appLogic.documents;
  const hasLoadingDocumentsError = hasDocumentsLoadError(
    errors,
    claim.application_id
  );

  const idDocuments = findDocumentsByTypes(documents, [
    DocumentType.identityVerification,
  ]);

  const reasonQualifier = get(claim, "leave_details.reason_qualifier");
  const hasFutureChildDate = get(claim, "leave_details.has_future_child_date");
  const reducedLeavePeriod = new ReducedScheduleLeavePeriod(
    get(claim, "leave_details.reduced_schedule_leave_periods[0]")
  );
  const workPattern = new WorkPattern(get(claim, "work_pattern") || {});
  const gender = get(claim, "gender");
  const {
    race,
    race_custom,
    ethnicity,
    additional_user_not_found_info,
    industry_sector,
  } = claim;
  const isEmployed =
    get(claim, "employment_status") === EmploymentStatus.employed;
  const willSubmitToManualReview =
    appLogic.benefitsApplications.hasUserNotFoundError(claim.application_id) &&
    additional_user_not_found_info != null;

  const steps = Step.createClaimStepsFromMachine(claimantConfigs, {
    claim: props.claim,
  });

  const usePartOneReview = !claim.isSubmitted;

  const getStepEditHref = (
    name: string,
    additionalParams?: NullableQueryParams
  ) => {
    const step = steps.find((s) => s.name === name);

    if (step && step.editable) {
      return appLogic.portalFlow.getRouteFor(
        step.name,
        { claim },
        {
          claim_id: claim.application_id,
          ...additionalParams,
        }
      );
    }
  };

  const handleSubmit = async () => {
    const w: Window = window;
    setShowNewFieldError(false);
    if (usePartOneReview) {
      if (typeof w.Localize !== "undefined") {
        tracker.trackEvent("User completed part 1", {
          claim_language: `${claim.language}`,
        });
      }
      await appLogic.benefitsApplications.submit(claim.application_id);
      return;
    }

    if (typeof w.Localize !== "undefined") {
      tracker.trackEvent("User completed application", {
        user_id: user.user_id,
        language: w.Localize.getLanguage(),
        claim_language: `${claim.language}`,
      });
    }

    let isBondingLeaveWithFutureDOB = false;
    if (claim.isBondingLeave && claim.leave_details.has_future_child_date) {
      // for bonding leave with future child birthdate, claimant does not yet upload certificate document
      isBondingLeaveWithFutureDOB = true;
      setHasDeferredCertDoc(isBondingLeaveWithFutureDOB);
    }

    await appLogic.benefitsApplications.complete(
      claim.application_id,
      isBondingLeaveWithFutureDOB || hasDeferredCertDoc
    );
  };

  // Adjust heading levels depending on if there's a "Part 1" heading at the top of the page or not
  const reviewHeadingLevel = usePartOneReview ? "3" : "2";
  const reviewRowLevel = usePartOneReview ? "4" : "3";

  // If there are any required field errors then display a custom error message and clear the required
  // field errors so they don't render in the default error boundary. This can happen if a user reached
  // the review page just before we a deployed a new question or page, or if the user somehow skipped  a
  // page with required fields.
  const [showNewFieldError, setShowNewFieldError] = useState(false);
  useEffect(() => {
    const missingFields = getMissingRequiredFields(errors);
    if (missingFields.length) {
      tracker.trackEvent("Missing required fields", {
        missingFields: JSON.stringify(missingFields),
      });

      clearRequiredFieldErrors();
      if (!showNewFieldError) {
        // Display a custom error alert with a link back to the checklist
        setShowNewFieldError(true);
      }
    }
  }, [
    errors,
    showNewFieldError,
    setShowNewFieldError,
    clearRequiredFieldErrors,
  ]);

  const showEmployerNotNotifiedWarning = !claim.leave_details.employer_notified;

  const usedProfileData =
    isFeatureEnabled("enableUniversalProfileIDV") &&
    !!claim.fields_to_use_from_user_profile &&
    claim.fields_to_use_from_user_profile.length > 0;

  // TODO (PFMLPB-24322):  https://lwd.atlassian.net/browse/PFMLPB-24322 Remove FF
  const isOccupationDataCollectionEnabled = isFeatureEnabled(
    "enableOccupationDataCollection"
  );

  // Pluralizing frequency and duration nouns for Frequency of Intermittent Leave description
  const absenceNoun = new Noun({ singular: "absence" });
  const dayNoun = new Noun({ singular: "day" });
  const hourNoun = new Noun({ singular: "hour" });

  return (
    <div className="measure-6">
      <BackButton />

      {showEmployerNotNotifiedWarning && (
        <Alert className="margin-bottom-3" state="warning">
          <Trans i18nKey="pages.claimsReview.employerNotNotifiedWarning" />
        </Alert>
      )}

      {showNewFieldError && (
        <Alert className="margin-bottom-3">
          <Trans
            i18nKey="pages.claimsReview.missingRequiredFieldError"
            components={{
              "checklist-link": (
                <a
                  href={appLogic.portalFlow.getRouteFor(
                    "CHECKLIST",
                    { claim },
                    {
                      claim_id: claim.application_id,
                    }
                  )}
                />
              ),
            }}
          />
        </Alert>
      )}

      {usePartOneReview && usedProfileData && (
        <Alert
          className="margin-bottom-3"
          heading={t("pages.claimsReview.autofilledAlertTitle")}
          state="info"
        >
          {t("pages.claimsReview.autofilledAlertBody")}
        </Alert>
      )}

      {usePartOneReview && (
        <Title hidden>{t("pages.claimsReview.title_part1")}</Title>
      )}
      {!usePartOneReview && (
        <Title marginBottom="6">{t("pages.claimsReview.title_final")}</Title>
      )}

      <Heading className="margin-top-0" level="2">
        <HeadingPrefix>
          <Trans
            i18nKey="pages.claimsReview.partHeadingPrefix"
            values={{ number: 1 }}
          />
        </HeadingPrefix>
        {t("pages.claimsReview.partHeading_1")}
      </Heading>

      {!usePartOneReview && (
        <Lead>
          <Trans
            i18nKey="pages.claimsReview.partDescription"
            values={{ absence_id: claim.fineos_absence_id, step: 1 }}
            components={{
              "contact-center-phone-link": (
                <a href={`tel:${t("shared.contactCenterPhoneNumber")}`} />
              ),
            }}
          />
        </Lead>
      )}

      {usePartOneReview && usedProfileData && (
        <p className="margin-top-3" aria-hidden>
          <Trans i18nKey="pages.claimsReview.autofilledLegend" />
        </p>
      )}

      {/* EMPLOYEE IDENTITY */}
      <ReviewHeading
        editHref={getStepEditHref(ClaimSteps.verifyId)}
        editText={t("pages.claimsReview.editLink")}
        level={reviewHeadingLevel}
      >
        {t("pages.claimsReview.stepHeading", { context: "verifyId" })}
      </ReviewHeading>

      <ReviewRow
        level={reviewRowLevel}
        label={t("pages.claimsReview.userNameLabel")}
        autofilled={
          usedProfileData &&
          usePartOneReview &&
          (!!claim.fields_to_use_from_user_profile?.includes("firstname") ||
            !!claim.fields_to_use_from_user_profile?.includes("lastname"))
        }
      >
        <Trans
          i18nKey="data.user.any"
          values={{
            value: [
              get(claim, "first_name"),
              get(claim, "middle_name"),
              get(claim, "last_name"),
            ].join(" "),
          }}
        />
      </ReviewRow>

      <ReviewRow
        level={reviewRowLevel}
        label={t("pages.claimsReview.userDateOfBirthLabel")}
        autofilled={
          usedProfileData &&
          usePartOneReview &&
          !!claim.fields_to_use_from_user_profile?.includes("dateOfBirth")
        }
      >
        <Trans
          i18nKey="shared.date"
          values={{ date: get(claim, "date_of_birth") }}
        />
      </ReviewRow>

      <ReviewRow
        level={reviewRowLevel}
        label={t("pages.claimsReview.phoneLabel")}
        autofilled={
          usedProfileData &&
          usePartOneReview &&
          !!claim.fields_to_use_from_user_profile?.includes("phones")
        }
      >
        {t("pages.claimsReview.phoneType", {
          context: findKeyByValue(PhoneType, get(claim, "phone.phone_type")),
        })}
        <br />
        <Trans
          i18nKey="data.user.any"
          values={{
            value: get(claim, "phone.phone_number"),
          }}
        />
      </ReviewRow>

      <ReviewRow
        level={reviewRowLevel}
        label={t("pages.claimsReview.residentialAddressLabel")}
        autofilled={
          usedProfileData &&
          usePartOneReview &&
          !!claim.fields_to_use_from_user_profile?.includes("addresses")
        }
      >
        <Trans
          i18nKey="data.user.any"
          values={{
            value: formatAddress(get(claim, "residential_address")),
          }}
        />
      </ReviewRow>

      {!isFeatureEnabled("enableMmgIDV") && (
        <ReviewRow
          level={reviewRowLevel}
          label={t("pages.claimsReview.languageLabel")}
        >
          <Trans
            i18nKey="data.user.any"
            values={{
              value: transformLanguage(get(claim, "language")),
            }}
          />
        </ReviewRow>
      )}

      {claim.has_mailing_address && (
        <ReviewRow
          label={t("pages.claimsReview.mailingAddressLabel")}
          level={reviewRowLevel}
        >
          <Trans
            i18nKey="data.user.any"
            values={{
              value: formatAddress(get(claim, "mailing_address")),
            }}
          />
        </ReviewRow>
      )}

      {claim.has_state_id && (
        <ReviewRow
          level={reviewRowLevel}
          label={t("pages.claimsReview.userStateIdLabel")}
        >
          <Trans
            i18nKey="data.user.any"
            values={{
              value: claim.mass_id,
            }}
          />
        </ReviewRow>
      )}

      <ReviewRow
        level={reviewRowLevel}
        label={t("pages.claimsReview.userTaxIdLabel")}
      >
        <Trans
          i18nKey="data.user.any"
          values={{
            value: get(claim, "tax_identifier"),
          }}
        />
      </ReviewRow>

      {gender && !isFeatureEnabled("enableMmgIDV") && (
        <ReviewRow
          level={reviewRowLevel}
          label={t("pages.claimsReview.userGenderLabel")}
        >
          {t("pages.claimsReview.genderValue", {
            context: findKeyByValue(Gender, gender),
          })}
        </ReviewRow>
      )}

      {ethnicity && !isFeatureEnabled("enableMmgIDV") && (
        <ReviewRow
          level={reviewRowLevel}
          label={t("pages.claimsReview.userEthnicityLabel")}
        >
          {t("pages.claimsReview.ethnicity", {
            context: findKeyByValue(Ethnicity, ethnicity),
          })}
        </ReviewRow>
      )}

      {race && !isFeatureEnabled("enableMmgIDV") && (
        <ReviewRow
          level={reviewRowLevel}
          label={t("pages.claimsReview.userRaceLabel")}
        >
          {t("pages.claimsReview.race", {
            context: findKeyByValue(Race, race),
          })}
          {race === Race.anotherRaceNotListedAbove && (
            <React.Fragment>: {race_custom}</React.Fragment>
          )}
        </ReviewRow>
      )}

      {/* EMPLOYMENT INFO */}
      <ReviewHeading
        editHref={getStepEditHref(ClaimSteps.employerInformation)}
        editText={t("pages.claimsReview.editLink")}
        level={reviewHeadingLevel}
      >
        {t("pages.claimsReview.stepHeading", {
          context: "employerInformation",
        })}
      </ReviewHeading>

      {isEmployed && ( // only display this if the claimant is Employed
        <ReviewRow
          level={reviewRowLevel}
          label={t("pages.claimsReview.employerFeinLabel")}
        >
          <Trans
            i18nKey="data.user.any"
            values={{
              value: get(claim, "employer_fein"),
            }}
          />
        </ReviewRow>
      )}

      {/* TODO (PFMLPB-24322):  https://lwd.atlassian.net/browse/PFMLPB-24322 Remove FF */}
      {isEmployed && isOccupationDataCollectionEnabled && (
        <ReviewRow
          level={reviewRowLevel}
          label={t("pages.claimsReview.jobTypeLabel")}
        >
          {t("shared.industrySector", {
            context: industry_sector
              ? findKeyByValue(IndustrySector, industry_sector)
              : "notAnswered",
          })}
        </ReviewRow>
      )}

      {/* ADDITIONAL USER NOT FOUND INFO */}
      {willSubmitToManualReview && (
        <React.Fragment>
          {additional_user_not_found_info?.employer_name && (
            <ReviewRow
              level={reviewRowLevel}
              label={t("pages.claimsReview.employerNameLabel")}
            >
              <Trans
                i18nKey="data.user.any"
                values={{
                  value: additional_user_not_found_info.employer_name,
                }}
              />
            </ReviewRow>
          )}
          {additional_user_not_found_info?.date_of_hire && (
            <ReviewRow
              level={reviewRowLevel}
              label={t("pages.claimsReview.dateOfHireLabel")}
            >
              <Trans
                i18nKey="data.user.any"
                values={{
                  value: formatDate(
                    additional_user_not_found_info.date_of_hire
                  ).short(),
                }}
              />
            </ReviewRow>
          )}
          {additional_user_not_found_info?.currently_employed != null && (
            <ReviewRow
              level={reviewRowLevel}
              label={t("pages.claimsReview.currentlyWorkAtEmployerLabel")}
            >
              {additional_user_not_found_info.currently_employed
                ? t("shared.choiceYes")
                : t("shared.choiceNo")}
            </ReviewRow>
          )}
          {additional_user_not_found_info?.date_of_separation && (
            <ReviewRow
              level={reviewRowLevel}
              label={t("pages.claimsReview.dateOfSeparationLabel")}
            >
              <Trans
                i18nKey="data.user.any"
                values={{
                  value: formatDate(
                    additional_user_not_found_info.date_of_separation
                  ).short(),
                }}
              />
            </ReviewRow>
          )}
          {additional_user_not_found_info?.recently_acquired_or_merged !=
            null && (
            <ReviewRow
              level={reviewRowLevel}
              label={t("pages.claimsReview.recentlyAcquiredOrMergedLabel")}
            >
              {additional_user_not_found_info.recently_acquired_or_merged
                ? t("shared.choiceYes")
                : t("shared.choiceNo")}
            </ReviewRow>
          )}
        </React.Fragment>
      )}

      {/* CONTINUED EMPLOYMENT INFO */}
      {get(claim, "organization_unit") && ( // only displays this if the claimant is Employed
        <ReviewRow
          level={reviewRowLevel}
          label={t("pages.claimsReview.employeeOrganizationUnit")}
        >
          <Trans
            i18nKey="data.user.any"
            values={{
              value: get(claim, "organization_unit.name"),
            }}
          />
        </ReviewRow>
      )}

      {isEmployed &&
        get(claim, "leave_details.employer_notification_date") && ( // only display this if the claimant is Employed and date is set
          <ReviewRow
            level={reviewRowLevel}
            label={t("pages.claimsReview.employerNotifiedLabel")}
          >
            <p title="claims review employer notified value">
              <Trans
                i18nKey="pages.claimsReview.employerNotifiedValue"
                values={{
                  context: (!!get(
                    claim,
                    "leave_details.employer_notified"
                  )).toString(),
                  date: formatDate(
                    get(claim, "leave_details.employer_notification_date")
                  ).short(),
                }}
              />
            </p>
          </ReviewRow>
        )}

      {workPattern.work_pattern_type && (
        <ReviewRow
          level={reviewRowLevel}
          label={t("pages.claimsReview.workPatternTypeLabel")}
        >
          {t("pages.claimsReview.workPatternTypeValue", {
            context: findKeyByValue(
              WorkPatternType,
              get(claim, "work_pattern.work_pattern_type")
            ),
          })}
        </ReviewRow>
      )}

      {workPattern.work_pattern_days &&
        workPattern.work_pattern_type === WorkPatternType.fixed &&
        workPattern.minutesWorkedPerWeek !== null && (
          <ReviewRow
            level={reviewRowLevel}
            label={t("pages.claimsReview.workPatternDaysFixedLabel")}
          >
            <WeeklyTimeTable days={workPattern.work_pattern_days} />
          </ReviewRow>
        )}

      {workPattern.work_pattern_type === WorkPatternType.variable && (
        <ReviewRow
          level={reviewRowLevel}
          label={t("pages.claimsReview.workPatternDaysVariableLabel")}
        >
          {!isBlank(workPattern.minutesWorkedPerWeek) && (
            <Trans
              i18nKey="pages.claimsReview.workPatternVariableTime"
              values={{
                context:
                  convertMinutesToHours(workPattern.minutesWorkedPerWeek)
                    .minutes === 0
                    ? "noMinutes"
                    : null,
                ...convertMinutesToHours(workPattern.minutesWorkedPerWeek),
              }}
            />
          )}
        </ReviewRow>
      )}

      {/* Conditional concurrent employer content */}
      {claim.has_concurrent_employers !== null && (
        <ReviewRow
          level={reviewRowLevel}
          label={t("pages.claimsReview.additionalEmployersLabel")}
        >
          {claim.has_concurrent_employers
            ? t("shared.choiceYes")
            : t("shared.choiceNo")}
        </ReviewRow>
      )}

      {/* Conditional concurrent employer content */}
      {claim.has_concurrent_employers &&
        !isBlank(claim.hours_worked_per_week_all_employers) && (
          <ReviewRow
            level={reviewRowLevel}
            label={t("pages.claimsReview.additionalEmployersTotalHoursLabel")}
          >
            <Trans
              i18nKey="pages.claimsReview.workPatternVariableTime"
              values={{
                context:
                  convertHoursToMinutesHours(
                    claim.hours_worked_per_week_all_employers
                  ).minutes === 0
                    ? "noMinutes"
                    : null,
                ...convertHoursToMinutesHours(
                  claim.hours_worked_per_week_all_employers
                ),
              }}
            />
          </ReviewRow>
        )}

      {/* LEAVE DETAILS */}
      <ReviewHeading
        editHref={getStepEditHref(ClaimSteps.leaveDetails)}
        editText={t("pages.claimsReview.editLink")}
        level={reviewHeadingLevel}
      >
        {t("pages.claimsReview.stepHeading", { context: "leaveDetails" })}
      </ReviewHeading>

      {claim.hasLeaveSpanningBenefitYears() &&
        claim.isLeaveInSecondBenefitYearReviewableToday && (
          <React.Fragment>
            <BenefitYearsSpanAlert claim={claim} />
            <br />
          </React.Fragment>
        )}

      <ReviewRow
        level={reviewRowLevel}
        label={t("pages.claimsReview.leaveReasonLabel")}
      >
        {t("pages.claimsReview.leaveReasonValue", {
          context: findKeyByValue(
            LeaveReason,
            get(claim, "leave_details.reason")
          ),
        })}
      </ReviewRow>

      {claim.isMedicalOrPregnancyLeave && (
        <ReviewRow
          level={reviewRowLevel}
          label={t("pages.claimsReview.pregnancyOrRecentBirthLabel")}
        >
          {t("pages.claimsReview.pregnancyOrRecentBirth", {
            context: get(claim, "leave_details.pregnant_or_recent_birth")
              ? "yes"
              : "no",
          })}
        </ReviewRow>
      )}

      {claim.isBondingLeave && (
        <ReviewRow
          level={reviewRowLevel}
          label={t("pages.claimsReview.familyLeaveTypeLabel")}
        >
          {t("pages.claimsReview.familyLeaveTypeValue", {
            context: findKeyByValue(ReasonQualifier, reasonQualifier),
          })}
        </ReviewRow>
      )}

      {claim.isBondingLeave &&
        reasonQualifier === ReasonQualifier.newBorn &&
        claim.leave_details.child_birth_date && (
          <ReviewRow
            level={reviewRowLevel}
            label={t("pages.claimsReview.childBirthDateLabel")}
          >
            <Trans
              i18nKey="data.user.any"
              values={{
                value: formatDate(claim.leave_details.child_birth_date).short(),
              }}
            />
          </ReviewRow>
        )}

      {claim.isBondingLeave &&
        [ReasonQualifier.adoption, ReasonQualifier.fosterCare].includes(
          reasonQualifier
        ) &&
        claim.leave_details.child_placement_date && (
          <ReviewRow
            level={reviewRowLevel}
            label={t("pages.claimsReview.childPlacementDateLabel")}
          >
            <Trans
              i18nKey="data.user.any"
              values={{
                value: formatDate(
                  claim.leave_details.child_placement_date
                ).short(),
              }}
            />
          </ReviewRow>
        )}

      {claim.isCaringLeave && claim.hasCaringLeaveMetadata && (
        <React.Fragment>
          <ReviewRow
            level={reviewRowLevel}
            label={t("pages.claimsReview.familyMemberRelationshipLabel")}
          >
            {t("pages.claimsReview.familyMemberRelationship", {
              context: findKeyByValue(
                RelationshipToCaregiver,
                get(
                  claim,
                  "leave_details.caring_leave_metadata.relationship_to_caregiver"
                )
              ),
            })}
          </ReviewRow>
          <ReviewRow
            level={reviewRowLevel}
            label={t("pages.claimsReview.familyMemberNameLabel")}
          >
            <Trans
              i18nKey="data.user.any"
              values={{
                value: [
                  get(
                    claim,
                    "leave_details.caring_leave_metadata.family_member_first_name"
                  ),
                  get(
                    claim,
                    "leave_details.caring_leave_metadata.family_member_middle_name"
                  ),
                  get(
                    claim,
                    "leave_details.caring_leave_metadata.family_member_last_name"
                  ),
                ].join(" "),
              }}
            />
          </ReviewRow>
          <ReviewRow
            level={reviewRowLevel}
            label={t("pages.claimsReview.familyMemberDateOfBirthLabel")}
          >
            <Trans
              i18nKey="data.user.any"
              values={{
                value: formatDate(
                  get(
                    claim,
                    "leave_details.caring_leave_metadata.family_member_date_of_birth"
                  )
                ).short(),
              }}
            />
          </ReviewRow>
        </React.Fragment>
      )}

      <ReviewRow
        level={reviewRowLevel}
        label={t("pages.claimsReview.leavePeriodLabel", {
          context: "continuous",
        })}
      >
        {claim.isContinuous
          ? claim.continuousLeaveDateRange()
          : t("pages.claimsReview.leavePeriodNotSelected")}
      </ReviewRow>

      <ReviewRow
        level={reviewRowLevel}
        label={t("pages.claimsReview.leavePeriodLabel", {
          context: "reduced",
        })}
      >
        {claim.isReducedSchedule
          ? claim.reducedLeaveDateRange()
          : t("pages.claimsReview.leavePeriodNotSelected")}
      </ReviewRow>
      {/* Only hide the border when we're rendering a WeeklyTimeTable */}
      {claim.isReducedSchedule && (
        <ReviewRow
          level={reviewRowLevel}
          label={t("pages.claimsReview.reducedLeaveScheduleLabel")}
          noBorder={workPattern.work_pattern_type === WorkPatternType.fixed}
        >
          {workPattern.work_pattern_type === WorkPatternType.fixed && (
            <WeeklyTimeTable
              className="margin-bottom-0"
              days={reducedLeavePeriod.days}
            />
          )}
          {workPattern.work_pattern_type === WorkPatternType.variable && (
            <Trans
              i18nKey="pages.claimsReview.reducedLeaveScheduleWeeklyTotal"
              values={{
                context:
                  convertMinutesToHours(reducedLeavePeriod.totalMinutesOff)
                    .minutes === 0
                    ? "noMinutes"
                    : null,
                ...convertMinutesToHours(reducedLeavePeriod.totalMinutesOff),
              }}
            />
          )}
        </ReviewRow>
      )}

      <ReviewRow
        level={reviewRowLevel}
        label={t("pages.claimsReview.leavePeriodLabel", {
          context: "intermittent",
        })}
      >
        {claim.isIntermittent
          ? claim.intermittentLeaveDateRange()
          : t("pages.claimsReview.leavePeriodNotSelected")}
      </ReviewRow>

      {claim.hasIntermittentLeaveFrequency && (
        <ReviewRow
          level={reviewRowLevel}
          label={t("pages.claimsReview.intermittentFrequencyDurationLabel")}
        >
          <Trans
            i18nKey="pages.claimsReview.intermittentFrequencyDuration"
            context={getI18nContextForIntermittentFrequencyDuration(
              get(claim, "leave_details.intermittent_leave_periods[0]")
            )}
            values={{
              duration: get(
                claim,
                "leave_details.intermittent_leave_periods[0].duration"
              ),
              frequency: get(
                claim,
                "leave_details.intermittent_leave_periods[0].frequency"
              ),
              absenceWord: absenceNoun.getFormForCount(
                get(
                  claim,
                  "leave_details.intermittent_leave_periods[0].frequency"
                )
              ),
              dayWord: dayNoun.getFormForCount(
                get(
                  claim,
                  "leave_details.intermittent_leave_periods[0].duration"
                )
              ),
              hourWord: hourNoun.getFormForCount(
                get(
                  claim,
                  "leave_details.intermittent_leave_periods[0].duration"
                )
              ),
            }}
          />
        </ReviewRow>
      )}

      {/* OTHER LEAVE */}
      {/* Conditionally showing this section since it was added after launch, so some claims may not have this section yet. */}
      {(get(claim, "has_employer_benefits") !== null ||
        get(claim, "has_other_incomes") !== null ||
        get(claim, "has_previous_leaves_same_reason") !== null ||
        get(claim, "has_previous_leaves_other_reason") !== null ||
        get(claim, "has_previous_leaves") !== null) && (
        <div>
          <ReviewHeading
            editHref={getStepEditHref(ClaimSteps.otherLeave)}
            editText={t("pages.claimsReview.editLink")}
            level={reviewHeadingLevel}
          >
            {t("pages.claimsReview.stepHeading", { context: "otherLeave" })}
          </ReviewHeading>
          <ReviewRow
            level={reviewRowLevel}
            label={t("pages.claimsReview.previousLeaveHasPreviousLeavesLabel")}
          >
            {claim.hasAnyPreviousLeaves
              ? t("pages.claimsReview.otherLeaveChoiceYes")
              : t("pages.claimsReview.otherLeaveChoiceNo")}
          </ReviewRow>

          <PreviousLeaveList
            entries={claim.previous_leaves}
            type="anyReason"
            startIndex={0}
            reviewRowLevel={reviewRowLevel}
          />

          <ReviewRow
            level={reviewRowLevel}
            label={t("pages.claimsReview.employerBenefitLabel")}
          >
            {get(claim, "has_employer_benefits") === true
              ? t("pages.claimsReview.otherLeaveChoiceYes")
              : t("pages.claimsReview.otherLeaveChoiceNo")}
          </ReviewRow>

          {get(claim, "has_employer_benefits") && (
            <EmployerBenefitList
              entries={get(claim, "employer_benefits")}
              reviewRowLevel={reviewRowLevel}
            />
          )}

          <ReviewRow
            level={reviewRowLevel}
            label={t("pages.claimsReview.otherIncomeLabel")}
          >
            {get(claim, "has_other_incomes") === true &&
              t("pages.claimsReview.otherLeaveChoiceYes")}
            {get(claim, "has_other_incomes") === false &&
              t("pages.claimsReview.otherLeaveChoiceNo")}
          </ReviewRow>

          {get(claim, "has_other_incomes") && (
            <OtherIncomeList
              entries={get(claim, "other_incomes")}
              reviewRowLevel={reviewRowLevel}
            />
          )}
        </div>
      )}

      {/* DEMOGRAPHIC DETAILS */}
      {isFeatureEnabled("enableMmgIDV") && (
        <ReviewHeading
          editHref={getStepEditHref(ClaimSteps.confirmDemographics)}
          editText={t("pages.claimsReview.editLink")}
          level={reviewHeadingLevel}
        >
          {t("pages.claimsReview.stepHeading", {
            context: "demographics",
          })}
        </ReviewHeading>
      )}
      {gender && isFeatureEnabled("enableMmgIDV") && (
        <ReviewRow
          level={reviewRowLevel}
          label={t("pages.claimsReview.userGenderLabel")}
          autofilled={
            usedProfileData &&
            usePartOneReview &&
            !!claim.fields_to_use_from_user_profile?.includes("gender")
          }
        >
          {t("pages.claimsReview.genderValue", {
            context: findKeyByValue(Gender, gender),
          })}
        </ReviewRow>
      )}

      {ethnicity && isFeatureEnabled("enableMmgIDV") && (
        <ReviewRow
          level={reviewRowLevel}
          label={t("pages.claimsReview.userEthnicityLabel")}
          autofilled={
            usedProfileData &&
            usePartOneReview &&
            !!claim.fields_to_use_from_user_profile?.includes("raceEthnicity")
          }
        >
          {t("pages.claimsReview.ethnicity", {
            context: findKeyByValue(Ethnicity, ethnicity),
          })}
        </ReviewRow>
      )}

      {race && isFeatureEnabled("enableMmgIDV") && (
        <ReviewRow
          level={reviewRowLevel}
          label={t("pages.claimsReview.userRaceLabel")}
          autofilled={
            usedProfileData &&
            usePartOneReview &&
            !!claim.fields_to_use_from_user_profile?.includes("raceEthnicity")
          }
        >
          {t("pages.claimsReview.race", {
            context: findKeyByValue(Race, race),
          })}
          {race === Race.anotherRaceNotListedAbove && (
            <React.Fragment>: {race_custom}</React.Fragment>
          )}
        </ReviewRow>
      )}

      {isFeatureEnabled("enableMmgIDV") && (
        <ReviewRow
          level={reviewRowLevel}
          label={t("pages.claimsReview.languageLabel")}
        >
          <Trans
            i18nKey="data.user.any"
            values={{
              value: transformLanguage(get(claim, "language")),
            }}
          />
        </ReviewRow>
      )}

      {usePartOneReview ? (
        <React.Fragment>
          {/* ADDITIONAL USER NOT FOUND INFO */}
          {willSubmitToManualReview && (
            <ReviewTaxAndPaymentPreferences
              claim={claim}
              reviewHeadingLevel={reviewHeadingLevel}
              reviewRowLevel={reviewRowLevel}
              getStepEditHref={getStepEditHref}
            />
          )}
          {/* OTHER */}
          <div className="margin-top-6 margin-bottom-2">
            {claim.hasLeaveSpanningBenefitYears() &&
            !claim.isLeaveInSecondBenefitYearReviewableToday ? (
              <BenefitYearsSpanAlert claim={claim} />
            ) : (
              <Trans
                i18nKey="pages.claimsReview.partOneNextSteps"
                components={{
                  "contact-center-phone-link": (
                    <a href={`tel:${t("shared.contactCenterPhoneNumber")}`} />
                  ),
                }}
                context={willSubmitToManualReview ? "manualReview" : ""}
              />
            )}
          </div>
        </React.Fragment>
      ) : (
        <React.Fragment>
          <Heading level="2">
            <HeadingPrefix>
              <Trans
                i18nKey="pages.claimsReview.partHeadingPrefix"
                values={{ number: 2 }}
              />
            </HeadingPrefix>
            {t("pages.claimsReview.partHeading", {
              context: "2",
            })}
          </Heading>
          <Lead>
            <Trans
              i18nKey="pages.claimsReview.partDescription"
              values={{ absence_id: claim.fineos_absence_id, step: 2 }}
              components={{
                "contact-center-phone-link": (
                  <a href={`tel:${t("shared.contactCenterPhoneNumber")}`} />
                ),
              }}
            />
          </Lead>
          <ReviewTaxAndPaymentPreferences
            claim={claim}
            reviewHeadingLevel={reviewHeadingLevel}
            reviewRowLevel={reviewRowLevel}
            getStepEditHref={getStepEditHref}
          />
          <Heading level="2">
            <HeadingPrefix>
              <Trans
                i18nKey="pages.claimsReview.partHeadingPrefix"
                values={{ number: 3 }}
              />
            </HeadingPrefix>
            {t("pages.claimsReview.partHeading", {
              context: "3",
            })}
          </Heading>
          {hasLoadingDocumentsError && (
            <Alert className="margin-bottom-3" noIcon>
              <Trans
                i18nKey="pages.claimsReview.documentsLoadError"
                components={{
                  "contact-center-phone-link": (
                    <a href={`tel:${t("shared.contactCenterPhoneNumber")}`} />
                  ),
                }}
              />
            </Alert>
          )}
          {isLoadingDocuments && !hasLoadingDocumentsError && (
            <div className="margin-top-8 text-center">
              <Spinner aria-label={t("components.spinner.label")} />
            </div>
          )}
          {!isLoadingDocuments && !hasLoadingDocumentsError && (
            <React.Fragment>
              {(claim.mmg_idv_status !== "Verified" ||
                claim.has_state_id === false) && (
                <React.Fragment>
                  <ReviewHeading
                    editHref={getStepEditHref(ClaimSteps.uploadId)}
                    editText={t("pages.claimsReview.editLink")}
                    level={reviewHeadingLevel}
                  >
                    {t("pages.claimsReview.stepHeading", {
                      context: "uploadId",
                    })}
                  </ReviewHeading>
                  <ReviewRow
                    label={t("pages.claimsReview.numberOfFilesLabel")}
                    level={reviewRowLevel}
                  >
                    <Trans
                      i18nKey="data.user.any"
                      values={{
                        value: idDocuments.length,
                      }}
                    />
                  </ReviewRow>
                </React.Fragment>
              )}
              {!hasFutureChildDate && (
                <React.Fragment>
                  <ReviewCertificationUploads
                    getStepEditHref={getStepEditHref}
                    documents={documents}
                    reviewHeadingLevel={reviewHeadingLevel}
                    reviewRowLevel={reviewRowLevel}
                    claimType={get(claim, "leave_details.reason")}
                  />
                </React.Fragment>
              )}
            </React.Fragment>
          )}
          <Trans i18nKey="pages.claimsReview.employerWarning" />
        </React.Fragment>
      )}

      <ThrottledButton
        className="margin-top-3"
        onClick={handleSubmit}
        type="button"
        loadingMessage={t("pages.claimsReview.submitLoadingMessage")}
      >
        {t("pages.claimsReview.submitAction", {
          context: usePartOneReview ? "part1" : "final",
        })}
      </ThrottledButton>
    </div>
  );
};

interface PreviousLeaveListProps {
  type: "anyReason" | "sameReason" | "otherReason";
  entries: PreviousLeave[];
  /** start index for previous leave label */
  startIndex: number;
  reviewRowLevel: "2" | "3" | "4" | "5" | "6";
}

export const PreviousLeaveList = (props: PreviousLeaveListProps) => {
  const { t } = useTranslation();
  if (!props.entries.length) return null;

  const rows = props.entries.map((entry, index) => {
    return (
      <ReviewRow
        level={props.reviewRowLevel}
        key={`${props.type}-${index}`}
        label={
          <Trans
            i18nKey="pages.claimsReview.previousLeaveEntryLabel"
            values={{ count: props.startIndex + index + 1 }}
          />
        }
      >
        <p className="text-base-darker margin-top-1">
          {formatDateRange(entry.leave_start_date, entry.leave_end_date)}
        </p>
        <ul className="usa-list margin-top-1">
          {props.type !== "anyReason" && (
            <li>
              {t("pages.claimsReview.previousLeaveType", {
                context: props.type,
              })}
            </li>
          )}
          <li>
            {t("pages.claimsReview.isForCurrentEmployer", {
              context: String(get(entry, "is_for_current_employer")),
            })}
          </li>
          {entry.leave_reason &&
            ["otherReason", "anyReason"].includes(props.type) && (
              <li>
                {t("pages.claimsReview.previousLeaveReason", {
                  context: findKeyByValue(
                    PreviousLeaveReason,
                    get(entry, "leave_reason")
                  ),
                })}
              </li>
            )}
          {entry.is_continuous && (
            <li>{t("pages.claimsReview.isContinuous")}</li>
          )}
        </ul>
      </ReviewRow>
    );
  });

  return <React.Fragment>{rows}</React.Fragment>;
};

interface EmployerBenefitListProps {
  entries: EmployerBenefit[];
  reviewRowLevel: "2" | "3" | "4" | "5" | "6";
}

/*
 * Helper component for rendering an array of EmployerBenefit
 * objects.
 */
export const EmployerBenefitList = (props: EmployerBenefitListProps) => {
  const { t } = useTranslation();
  const { entries, reviewRowLevel } = props;

  const rows = entries.map((entry, index) => {
    const label = (
      <Trans
        i18nKey="pages.claimsReview.employerBenefitEntryLabel"
        values={{ count: index + 1 }}
      />
    );

    const type = t("pages.claimsReview.employerBenefitType", {
      context: findKeyByValue(EmployerBenefitType, entry.benefit_type),
    });

    let dates;

    if (entry.is_full_salary_continuous) {
      dates = formatDateRange(entry.benefit_start_date, entry.benefit_end_date);
    }

    return (
      <OtherLeaveEntry
        key={index}
        label={label}
        type={type}
        dates={dates}
        amount={null}
        reviewRowLevel={reviewRowLevel}
      />
    );
  });

  return <React.Fragment>{rows}</React.Fragment>;
};

interface OtherIncomeListProps {
  entries: OtherIncome[];
  reviewRowLevel: "2" | "3" | "4" | "5" | "6";
}

/*
 * Helper component for rendering an array of OtherIncome
 * objects.
 */
export const OtherIncomeList = (props: OtherIncomeListProps) => {
  const { t } = useTranslation();
  const { entries, reviewRowLevel } = props;

  const rows = entries.map((entry, index) => {
    const label = (
      <Trans
        i18nKey="pages.claimsReview.otherIncomeEntryLabel"
        values={{ count: index + 1 }}
      />
    );

    const type = t("pages.claimsReview.otherIncomeType", {
      context: findKeyByValue(OtherIncomeType, entry.income_type),
    });

    const dates = formatDateRange(
      entry.income_start_date,
      entry.income_end_date
    );

    const amount = !isBlank(entry.income_amount_dollars) ? (
      <Trans
        i18nKey="pages.claimsReview.amountPerFrequency"
        values={{
          context: findKeyByValue(
            OtherIncomeFrequency,
            entry.income_amount_frequency
          ),
          amount: entry.income_amount_dollars,
        }}
      />
    ) : null;

    return (
      <OtherLeaveEntry
        key={index}
        label={label}
        type={type}
        dates={dates}
        amount={amount}
        reviewRowLevel={reviewRowLevel}
      />
    );
  });

  return <React.Fragment>{rows}</React.Fragment>;
};

interface OtherLeaveEntryProps {
  amount?: string | React.ReactElement | null;
  dates?: string | React.ReactElement;
  label: string | React.ReactElement;
  reviewRowLevel: "2" | "3" | "4" | "5" | "6";
  type?: string;
}

/*
 * Helper component for rendering a single other leave entry. This will
 * render a ReviewRow with the specified label, date string,
 * and an optional type string and amount string
 */
export const OtherLeaveEntry = (props: OtherLeaveEntryProps) => {
  const { amount, dates, label, reviewRowLevel, type } = props;

  return (
    <ReviewRow level={reviewRowLevel} label={label}>
      <p className="text-base-darker margin-top-1">{dates}</p>
      <ul className="usa-list margin-top-1">
        {type && <li>{type}</li>}
        {amount != null && <li>{amount}</li>}
      </ul>
    </ReviewRow>
  );
};

export default withBenefitsApplication(withClaimDocuments(Review));
