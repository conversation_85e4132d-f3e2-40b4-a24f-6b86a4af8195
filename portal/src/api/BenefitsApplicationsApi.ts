import ApiResourceCollection from "src/models/ApiResourceCollection";
import BaseApi from "./BaseApi";
import BenefitsApplication from "src/models/BenefitsApplication";
import PaymentPreference from "src/models/PaymentPreference";
import TaxWithholdingPreference from "src/models/TaxWithholdingPreference";
import routes from "src/routes";

export default class BenefitsApplicationsApi extends BaseApi {
  get basePath() {
    return routes.api.applications;
  }

  get namespace() {
    return "applications";
  }

  get headers() {
    return [
      { featureFlag: "disableOverlappingBenefitYearClaimCreation" },
      { featureFlag: "enableUniversalProfileIDV" },
      { featureFlag: "enableMmgIDV" },
      { featureFlag: "enableOccupationDataCollection" },
      { featureFlag: "enableBackendInvalidation" },
      {
        name: "X-FF-Disable-Application-Split-On-Benefit-Year-During-Submit",
        value: true,
      },
    ];
  }

  getClaim = async (application_id: string) => {
    const { data, warnings } = await this.request<BenefitsApplication>(
      "GET",
      application_id
    );

    return {
      claim: new BenefitsApplication(data),
      warnings,
    };
  };

  /**
   * Fetches the list of claims for a user
   */
  getClaims = async (pageOffset: string | number = 1) => {
    const { data, meta } = await this.request<BenefitsApplication[]>(
      "GET",
      "",
      {
        order_by: "created_at",
        order_direction: "descending",
        page_offset: pageOffset,
      }
    );

    const claims = data.map((claimData) => new BenefitsApplication(claimData));

    return {
      claims: new ApiResourceCollection<BenefitsApplication>(
        "application_id",
        claims
      ),
      paginationMeta: meta?.paging ?? {},
    };
  };

  /**
   * Signal the data entry is complete and application is ready
   * for intake to be marked as complete in the claims processing system.
   */
  completeClaim = async (
    applicationId: string,
    certificateDocumentDeferred: boolean = false
  ) => {
    const requestBody = {
      certificate_document_deferred: certificateDocumentDeferred,
    };
    const { data } = await this.request<BenefitsApplication>(
      "POST",
      `${applicationId}/complete-application`,
      requestBody
    );

    return {
      claim: new BenefitsApplication(data),
    };
  };

  createClaim = async () => {
    const { data } = await this.request<BenefitsApplication>("POST", "");

    return {
      claim: new BenefitsApplication(data),
    };
  };

  updateClaim = async (
    application_id: string,
    patchData: Partial<BenefitsApplication>
  ) => {
    const { data, warnings } = await this.request<BenefitsApplication>(
      "PATCH",
      application_id,
      patchData
    );

    return {
      claim: new BenefitsApplication(data),
      warnings,
    };
  };

  /**
   * Signal data entry for Part 1 is complete and ready
   * to be submitted to the claims processing system.
   */
  submitClaim = async (application_id: string) => {
    const { data } = await this.request<BenefitsApplication>(
      "POST",
      `${application_id}/submit-application`
    );

    return {
      claim: new BenefitsApplication(data),
    };
  };

  submitCustomerPaymentPreference = async (
    application_id: string,
    paymentPreferenceData: Partial<PaymentPreference>
  ) => {
    const { data, warnings } = await this.request<BenefitsApplication>(
      "POST",
      `${application_id}/submit-customer-payment-preference`,
      paymentPreferenceData
    );

    return {
      claim: new BenefitsApplication(data),
      warnings,
    };
  };

  submitTaxWithholdingPreference = async (
    application_id: string,
    preferenceData: Partial<TaxWithholdingPreference>
  ) => {
    const { data, warnings } = await this.request<BenefitsApplication>(
      "POST",
      `${application_id}/submit-tax-withholding-preference`,
      preferenceData
    );

    return {
      claim: new BenefitsApplication(data),
      warnings,
    };
  };
}
