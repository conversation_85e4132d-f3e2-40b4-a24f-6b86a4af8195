import Dropdown from "./Dropdown";
import Fieldset from "./Fieldset";
import FormLabel from "./FormLabel";
import InputText from "./InputText";
import React from "react";
import classnames from "classnames";
import isBlank from "src/utils/isBlank";
import { parseDateParts } from "./InputDate";
import { useTranslation } from "src/locales/i18n";

interface InputQuarterlyDateProps {
  /**
   * Localized error message. Setting this enables the error state styling.
   */
  errorMsg?: React.ReactNode;
  /**
   * Localized example text for the entire fieldset
   */
  example?: string;
  /**
   * Localized hint text for the entire fieldset
   */
  hint?: React.ReactNode;
  /**
   * Localized label for the entire fieldset
   */
  label: React.ReactNode;
  /**
   * Localized label for the quarter field
   */
  quarterLabel: React.ReactNode;
  /**
   * Apply error styling to the quarter dropdown
   */
  quarterInvalid?: boolean;
  /**
   * A name for the full date string. This is used for generating the individual field names
   * and is the name used as the onChange event's `target.name`
   */
  name: string;
  /**
   * Called when any of the fields' value changes. The event `target` will
   * include the formatted ISO 8601 date as its `value`
   */
  onChange?: React.ChangeEventHandler<HTMLInputElement>;
  /**
   * Localized text indicating this field is optional
   */
  optionalText?: React.ReactNode;
  /**
   * Enable the smaller label variant
   */
  smallLabel?: boolean;
  /**
   * The full ISO 8601 date (`YYYY-MM-DD`). If a date part is not yet set, leave
   * it blank (i.e `2019--10`). Use this prop in combination with `onChange`
   * for a controlled component.
   */
  value?: string;
  /**
   * Apply error styling to the year `input`
   */
  yearInvalid?: boolean;
  /**
   * Localized label for the year `input` field
   */
  yearLabel: React.ReactNode;
  /**
   * Localized string defining what the user sees in the quarter dropdown if they haven't selected anything yet
   */
  dropdownDefault?: string;
}

const quarterLabelPrefix = "components.form.dateInputQuarterLabel_";
const quarterValues = ["01", "04", "07", "10"];

export const InputQuarterlyDate = (props: InputQuarterlyDateProps) => {
  const hasError = !isBlank(props.errorMsg);
  const values = parseDateParts(props.value);
  const { t } = useTranslation();
  const quarterChoices = quarterValues.map((value: string) => {
    return {
      label: t(`${quarterLabelPrefix}${value}`),
      value,
    };
  });
  const inputClassNames = {
    quarter: classnames({
      "usa-input--error": props.quarterInvalid,
    }),
    year: classnames("usa-input--inline", {
      "usa-input--error": props.yearInvalid,
    }),
  };

  const formGroupClasses = classnames("usa-form-group", {
    "usa-form-group--error": hasError,
  });

  const yearFieldName = `${props.name}_year`;
  const quarterFieldName = `${props.name}_quarter`;

  const handleYearChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    // Read in the value for the year (ignoring any non-numerical characters)
    values.year = event.target.value.replace(/\D/g, "");
    dispatchOnChange();
  };

  const handleQuarterChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    // Set the date to either the first day of the quarter or empty string based-on if the field is empty or not
    values.day = event.target.value ? "01" : "";
    values.month = event.target.value ? event.target.value : "";

    dispatchOnChange();
  };

  /**
   * Call props.onChange with an argument value in a shape resembling Event so
   * that our form event handlers can manage this field's state just like
   * it does with other fields like InputText. We also include the original
   * event, but only for debugging purposes.
   */
  function dispatchOnChange() {
    const fullDateString = `${values.year}-${values.month}-${values.day}`;
    const target = document.createElement("input");
    target.setAttribute("name", props.name);
    target.setAttribute("value", fullDateString);
    target.setAttribute("data-value-type", "integer");
    target.name = props.name;
    target.value = fullDateString;

    if (props.onChange) {
      props.onChange({
        target,
      } as React.ChangeEvent<HTMLInputElement>);
    }
  }

  const dropdownDefault =
    props.dropdownDefault ?? "components.dropdown.emptyChoiceLabel";
  return (
    <Fieldset className={formGroupClasses}>
      <FormLabel
        component="legend"
        errorMsg={props.errorMsg}
        hint={props.hint}
        example={props.example}
        optionalText={props.optionalText}
        small={props.smallLabel}
      >
        {props.label}
      </FormLabel>
      <div className="usa-memorable-date">
        <Dropdown
          formGroupClassName="margin-right-1"
          selectClassName={inputClassNames.quarter}
          choices={quarterChoices}
          smallLabel
          label={props.quarterLabel}
          name={quarterFieldName}
          onChange={handleQuarterChange}
          value={values.month}
          labelClassName=""
          emptyChoiceLabel={t(dropdownDefault)}
        />
        <InputText
          formGroupClassName="usa-form-group--year"
          inputClassName={inputClassNames.year}
          inputMode="numeric"
          label={props.yearLabel}
          labelClassName="margin-top-0"
          maxLength={4}
          name={yearFieldName}
          onBlur={handleYearChange}
          onChange={handleYearChange}
          smallLabel
          value={values.year}
        />
      </div>
    </Fieldset>
  );
};

export default InputQuarterlyDate;
