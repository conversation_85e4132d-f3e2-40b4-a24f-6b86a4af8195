import DocumentRequirements from "src/components/DocumentRequirements";
import { FileCardListProps } from "src/components/FileCardList";
import FileUploadDetails from "src/components/FileUploadDetails";
import { InputFileProps } from "src/components/InputFile";
import MultipleInputFileGroup from "src/components/MultipleInputFileGroup";
import React from "react";
import { defaultAllowedFileTypes } from "src/hooks/useFilesLogic";

const acceptedFileTypes = defaultAllowedFileTypes.join();

export interface ExemptionsDocumentUploadProps {
  readonly files: FileCardListProps["files"];
  readonly fileErrors: FileCardListProps["fileErrors"];
  readonly handleFiles: InputFileProps["onChange"];
  readonly removeFile: FileCardListProps["removeFile"];
}

export const ExemptionsDocumentUploadPrompt = (
  props: ExemptionsDocumentUploadProps
) => {
  const { files, fileErrors, handleFiles, removeFile } = props;
  return (
    <React.Fragment>
      <DocumentRequirements type={"employerExemption"} />
      <FileUploadDetails />
      <MultipleInputFileGroup
        accept={acceptedFileTypes}
        files={files}
        fileErrors={fileErrors}
        headingLevel="2"
        headingSize="1"
        removeFile={removeFile}
        onChange={handleFiles}
      />
      <br />
    </React.Fragment>
  );
};
