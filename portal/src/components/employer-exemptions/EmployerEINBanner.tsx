import React from "react";
import { Trans } from "react-i18next";
import { WithEmployerExemptionsApplicationProps } from "src/hoc/withEmployerExemptionsApplication";

export const createEmployerEINBanner = (
  props: WithEmployerExemptionsApplicationProps
) => {
  const { user } = props;
  const { user_leave_administrators } = user;

  return (
    <Trans
      i18nKey="shared.employerExemptions.employerEIN"
      values={{
        employer_fein: user_leave_administrators[0].employer_fein,
      }}
    />
  );
};
