import BetaBanner from "src/components/BetaBanner";
import D<PERSON>LLogo from "src/components/DFMLLogo.svg";
import Icon from "src/components/core/Icon";
import MayflowerIcon from "./MayflowerIcon";
import PFMLLogo from "src/components/PFMLLogo.svg";
import React from "react";
import SiteLogo from "./SiteLogo";
import { Trans } from "react-i18next";
import User from "src/models/User";
import routes from "src/routes";
import { useTranslation } from "src/locales/i18n";

interface FooterProps {
  user?: User;
}

const SiteLogos = () => {
  const { t } = useTranslation();

  return (
    <React.Fragment>
      <SiteLogo
        url={routes.external.massgov.dfml}
        image={{
          src: DFMLLogo,
          alt: t("components.siteLogo.dfmlAlt"),
          width: 165,
          height: 45,
        }}
        title={t("components.footer.logoTitleDFML")}
      />
      <SiteLogo
        url={routes.external.massgov.pfml}
        image={{
          src: PFMLLogo,
          alt: t("components.siteLogo.pfmlAlt"),
          width: 118,
          height: 45,
        }}
        title={t("components.footer.logoTitlePFML")}
      />
    </React.Fragment>
  );
};

/**
 * Global page footer, displayed at the bottom of every page.
 */
const Footer = (props: FooterProps) => {
  const { t } = useTranslation();

  const isLoggedIn = props.user;
  const feedbackUrl =
    props.user && props.user.hasEmployerRole
      ? routes.external.massgov.feedbackEmployer
      : routes.external.massgov.feedbackClaimant;

  const contact = [
    {
      icon: <Icon name="location_on" size={3} />,
      component: (
        <span className="ma__address">
          <div className="ma__address__address">
            {t("components.footer.orgAddress")}
          </div>
        </span>
      ),
    },
    {
      icon: <MayflowerIcon name="laptop" />,
      component: (
        <a href={routes.external.massgov.dfml}>
          {t("components.footer.orgName")}
        </a>
      ),
    },
    {
      icon: <MayflowerIcon name="phone" />,
      component: (
        <div>
          <span className="ma__phone-number">
            <a
              className="ma__phone-number__number"
              href={`tel:${t("components.footer.orgPhoneNumber")}`}
            >
              {t("components.footer.orgPhoneNumber")}
            </a>
            <p className="ma__contact__details">
              <span>
                <Trans
                  i18nKey="components.footer.customerService_english"
                  components={{
                    strong: <strong />,
                  }}
                />
              </span>
            </p>
          </span>
        </div>
      ),
    },
  ];
  const today = new Date();
  const year = today.getFullYear();
  const links = [
    {
      href: routes.external.massgov.privacyPolicy,
      title: t("components.footer.privacyPolicy"),
    },
    {
      href: routes.external.massgov.consentAgreement,
      title: t("components.footer.dataSharingAgreement"),
    },
  ];

  return (
    <React.Fragment>
      {isLoggedIn && <BetaBanner feedbackUrl={feedbackUrl} />}
      <footer className="ma__footer-slim" id="footer">
        <div className="ma__footer-slim__container ma__container ma__footer-slim__container--stacked">
          <div className="ma__footer-slim__container__logos ma__footer-slim__container__logos--stacked">
            <SiteLogos />
          </div>
          <div className="ma__footer-slim__container__inner ma__footer-slim__container__inner--stacked">
            <div className="ma__footer-slim__info">
              <div className="ma__footer-slim__title">
                {t("components.footer.title")}
              </div>
              <p>{t("components.footer.description")}</p>
              <p className="ma__footer-slim__copyright">
                &copy; {`${year} Commonwealth of Massachusetts.`}
              </p>
            </div>
            <div className="ma__footer-slim__details">
              <div className="ma__footer-slim__links">
                {links.map((link, linkIndex) => (
                  /* eslint-disable-next-line react/no-array-index-key */
                  <a href={link.href} key={`footslimlinks-${linkIndex}`}>
                    {link.title}
                  </a>
                ))}
              </div>
              <address className="ma__footer-slim__contact">
                {contact.map((field, i) => (
                  /* eslint-disable-next-line react/no-array-index-key */
                  <div
                    className="ma__footer-slim__contact__item currentColor"
                    key={`filterbox-field-${i}`}
                  >
                    {React.cloneElement(field.icon, {
                      width: 20,
                      height: 20,
                    })}
                    {field.component}
                  </div>
                ))}
              </address>
            </div>
          </div>
        </div>
      </footer>
    </React.Fragment>
  );
};

export default Footer;
