export default function getNestedObjectValue(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  object: { [index: string]: any },
  field: string
) {
  const arr = field.split(".");
  let nestedValue = object;

  for (const str of arr) {
    if (
      typeof nestedValue === "object" &&
      nestedValue !== null &&
      nestedValue.hasOwnProperty(str)
    ) {
      nestedValue = nestedValue[str];
    } else {
      return undefined;
    }
  }

  return nestedValue;
}
