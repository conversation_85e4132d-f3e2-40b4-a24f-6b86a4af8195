import Language from "src/models/LanguageEnum";
import { MockBenefitsApplicationBuilder } from "lib/mock-helpers/mock-model-builder";
import { PreviousLeaveReason } from "src/models/PreviousLeave";
import dayjs from "dayjs";

/**
 * Storybook argTypes providing granular controls for
 * different aspects of an Application
 */
export const claimArgTypes = {
  Status: {
    control: {
      type: "radio",
    },
    options: [
      "Part 1 not submitted - User found flow",
      "User not found flow ready for submission",
      "Part 1 and 2 are submitted",
    ],
  },
  "Has state ID": {
    control: {
      type: "boolean",
    },
  },
  "Employer notified": {
    control: {
      type: "boolean",
    },
  },
  "Work pattern": {
    control: {
      type: "radio",
      options: ["fixed", "variable"],
    },
  },
  "Leave reason": {
    control: {
      type: "radio",
      options: [
        "Medical",
        "Family - Birth",
        "Family - Adoption",
        "Family - Foster care",
        "Caring",
        "Pregnancy",
      ],
    },
  },
  "Leave period": {
    control: {
      type: "radio",
      options: ["continuous", "reduced", "intermittent", "hybrid"],
    },
  },
  "Previous leaves same reason": {
    control: {
      type: "radio",
      options: ["None", "From current employer", "From other employer"],
    },
  },
  "Previous leaves other reason": {
    control: {
      type: "radio",
      options: [
        "None",
        "Medical leave from current employer",
        "Pregnancy leave from other employer",
      ],
    },
  },

  "Employer-sponsored benefit": {
    control: {
      type: "radio",
      options: ["None", "Yes (array)"],
    },
  },
  "Other income": {
    control: {
      type: "radio",
      options: ["None", "From other employer"],
    },
  },
  Payment: {
    control: {
      type: "radio",
      options: ["Deposit", "Check"],
    },
  },
  "Withhold taxes": {
    control: {
      type: "boolean",
    },
  },
  Language: {
    control: {
      type: "radio",
      options: Language,
    },
  },
  "IDV status": {
    options: ["None", "Verified", "Unverified"],
    control: {
      type: "select",
    },
  },
  "Occupation selected": {
    control: {
      type: "boolean",
    },
  },
};

export const claimArgs = {
  Status: "Part 1 not submitted - User found flow",
  "Has state ID": true,
  "Employer notified": true,
  "Work pattern": "fixed",
  "Leave reason": "Medical",
  "Leave period": "continuous",
  "Previous leaves same reason": "None",
  "Previous leaves other reason": "None",
  "Employer-sponsored benefit": "None",
  "Other income": "None",
  "IDV status": "None",
  Language: Language.english,
  Payment: "Deposit",
};

/**
 * Create a claim using the args passed into a Story. The args
 * are based on the claimArgTypes.
 */
export function createClaimFromArgs(args: {
  [key in keyof typeof claimArgTypes]: unknown;
}) {
  let claim = new MockBenefitsApplicationBuilder();

  claim = claim.languagePreference(String(args.Language));

  if (args.Status === "User not found flow ready for submission") {
    claim = claim.additionalUserNotFoundInfo();
  } else if (args.Status === "Part 1 and 2 are submitted") {
    claim = claim.submitted();
  }

  // Set defaults for aspects of a claim that will always be present.
  // These can be overridden by custom args below.
  claim = claim.verifiedId().employed();

  if (args["Has state ID"]) {
    claim = claim.hasStateId();
  } else {
    claim = claim.hasOtherId();
  }

  if (args["Employer notified"]) {
    claim = claim.notifiedEmployer();
  } else {
    claim = claim.notNotifiedEmployer();
  }

  if (args["Work pattern"] === "fixed") {
    claim = claim.fixedWorkPattern();
  } else {
    claim = claim.variableWorkPattern();
  }

  if (args["Withhold taxes"] === true) {
    claim = claim.taxPrefSubmitted(true);
  } else {
    claim = claim.taxPrefSubmitted(false);
  }

  switch (args["Leave reason"]) {
    case "Medical":
      claim = claim.medicalLeaveReason();
      break;
    case "Family - Birth":
      claim = claim.bondingBirthLeaveReason();
      break;
    case "Family - Adoption":
      claim = claim.bondingAdoptionLeaveReason();
      break;
    case "Family - Foster care":
      claim = claim.bondingFosterCareLeaveReason();
      break;
    case "Caring":
      claim.caringLeaveReason();
      break;
    case "Pregnancy":
      claim.pregnancyLeaveReason();
      break;
  }

  switch (args["Leave period"]) {
    case "continuous":
      claim = claim.continuous();
      break;
    case "reduced":
      claim = claim.reducedSchedule();
      break;
    case "intermittent":
      claim = claim.intermittent();
      break;
    case "hybrid":
      claim = claim.continuous().reducedSchedule();
      break;
  }

  switch (args["Previous leaves same reason"]) {
    case "From current employer":
      claim = claim.previousLeavesSameReason([
        {
          is_for_current_employer: true,
          leave_start_date: "2021-05-01",
          leave_end_date: "2021-07-01",
          leave_minutes: 5000,
          worked_per_week_minutes: 10000,
        },
      ]);
      break;
    case "From other employer":
      claim = claim.previousLeavesSameReason([
        {
          is_for_current_employer: false,
          leave_start_date: "2021-05-01",
          leave_end_date: "2021-07-01",
          leave_minutes: 5000,
          worked_per_week_minutes: 10000,
        },
      ]);
      break;
  }

  switch (args["Previous leaves other reason"]) {
    case "Medical leave from current employer":
      claim = claim.previousLeavesOtherReason([
        {
          is_for_current_employer: true,
          leave_reason: PreviousLeaveReason.medical,
          leave_start_date: "2021-05-01",
          leave_end_date: "2021-07-01",
          leave_minutes: 5000,
          worked_per_week_minutes: 10000,
        },
      ]);
      break;
    case "Pregnancy leave from other employer":
      claim = claim.previousLeavesOtherReason([
        {
          is_for_current_employer: false,
          leave_reason: PreviousLeaveReason.pregnancy,
          leave_start_date: "2021-05-01",
          leave_end_date: "2021-07-01",
          leave_minutes: 5000,
          worked_per_week_minutes: 10000,
        },
      ]);
      break;
  }

  switch (args["Employer-sponsored benefit"]) {
    case "Yes (array)":
      claim = claim.employerBenefit();
      break;
  }

  switch (args["Other income"]) {
    case "From other employer":
      claim = claim.otherIncome();
      break;
    default:
      claim = claim.noOtherIncomes();
      break;
  }

  switch (args["IDV status"]) {
    case "Unverified":
      claim = claim.isMmgUnverified();
      break;
    case "Verified":
      claim = claim.isMmgVerified();
      break;
    default:
      claim = claim.setMmgIdvStatus("None");
      break;
  }

  if (args.Payment === "Deposit") {
    claim = claim.directDeposit();
  } else {
    claim = claim.check();
  }

  if (args["Occupation selected"]) {
    claim.occupationDataCollection();
  } else {
    claim.occupationDataCollection("None");
  }

  return claim.create();
}

/**
 * Setup warnings based on claim Status passed into a Story. The args
 * are based on the claimArgTypes.
 */
export function createUserNotFoundClaimFromArgs(args: {
  [key in keyof typeof claimArgTypes]: unknown;
}): boolean {
  if (args.Status === "User not found flow ready for submission") {
    return true;
  }
  return false;
}

export function createMockClaimsByApplicationType() {
  const monthToAdd = 1; // workaround for https://github.com/storybookjs/storybook/issues/12208
  const futureDate = dayjs().add(monthToAdd, "month").format("YYYY-MM-DD");

  return {
    "Medical (Not pregnant)": new MockBenefitsApplicationBuilder()
      .continuous({ start_date: "2022-02-15" })
      .medicalLeaveReason()
      .absenceId()
      .create(),
    "Medical (Pregnant)": new MockBenefitsApplicationBuilder()
      .continuous({ start_date: "2022-02-15" })
      .medicalLeaveReason()
      .pregnant()
      .absenceId()
      .create(),
    "Medical (Pregnant, applying in advance)":
      new MockBenefitsApplicationBuilder()
        .continuous({ start_date: futureDate })
        .medicalLeaveReason()
        .pregnant()
        .absenceId()
        .create(),
    "Family (Bonding Newborn)": new MockBenefitsApplicationBuilder()
      .continuous({ start_date: "2022-02-15" })
      .bondingBirthLeaveReason()
      .absenceId()
      .create(),
    "Family (Bonding Future Newborn)": new MockBenefitsApplicationBuilder()
      .continuous({ start_date: futureDate })
      .hasFutureChild()
      .bondingBirthLeaveReason(futureDate)
      .absenceId()
      .create(),
    "Family (Bonding Adoption)": new MockBenefitsApplicationBuilder()
      .continuous({ start_date: "2022-02-15" })
      .bondingAdoptionLeaveReason()
      .absenceId()
      .create(),
    "Family (Bonding Future Adoption)": new MockBenefitsApplicationBuilder()
      .continuous({ start_date: futureDate })
      .hasFutureChild()
      .bondingAdoptionLeaveReason(futureDate)
      .absenceId()
      .create(),
    "Caring Leave": new MockBenefitsApplicationBuilder()
      .continuous({ start_date: "2022-02-15" })
      .caringLeaveReason()
      .absenceId()
      .create(),
  };
}
