import React, { useState } from "react";

import { InputQuarterlyDate } from "src/components/core/InputQuarterlyDate";
import { Props } from "types/common";

export default {
  title: "Core Components/Forms/InputQuarterlyDate",
  component: InputQuarterlyDate,
};

export const ControlledField = (args: Props<typeof InputQuarterlyDate>) => {
  // Setup super simple state management for the change handler and this controlled form component
  const [value, setFieldValue] = useState("2020-04-01");

  const handleOnChange = (evt: React.ChangeEvent<HTMLInputElement>) => {
    setFieldValue(evt.target.value);
  };

  // Render the form component!
  return (
    <form className="usa-form">
      <InputQuarterlyDate onChange={handleOnChange} value={value} {...args} />

      <code className="display-block margin-top-3">value: {value}</code>
    </form>
  );
};

const defaultProps = {
  label: "When does your policy renew?",
  name: "renewalDate",
  quarterLabel: "Quarter",
  yearLabel: "Year",
};

ControlledField.args = {
  ...defaultProps,
};

export const WithEmptyQuarterError = () => {
  return (
    <form className="usa-form">
      <InputQuarterlyDate
        {...defaultProps}
        errorMsg="You must select a quarter"
        quarterInvalid
        value="2020--"
      />
    </form>
  );
};

export const WithEmptyYearError = () => {
  // TODO (PFMLPB-24566) - Add story for invalid/out-of-range dates
  return (
    <form className="usa-form">
      <InputQuarterlyDate
        {...defaultProps}
        errorMsg="You must select a year"
        yearInvalid
        value="-04-01"
      />
    </form>
  );
};
