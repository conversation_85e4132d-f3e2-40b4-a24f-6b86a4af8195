import type { Met<PERSON>, StoryObj } from "@storybook/react";

import { MockBenefitsApplicationBuilder } from "lib/mock-helpers/mock-model-builder";
import generateClaimPageStory from "pfml-storybook/utils/generateClaimPageStory";

const mockClaims = {
  empty: new MockBenefitsApplicationBuilder().create(),
  "employed with DBA": new MockBenefitsApplicationBuilder()
    .employed()
    .employerDba()
    .create(),
  "employed without DBA": new MockBenefitsApplicationBuilder()
    .employed()
    .create(),
  "no employer": new MockBenefitsApplicationBuilder().create(),
};

const { config, DefaultStory } = generateClaimPageStory(
  "occupation",
  mockClaims
);

const meta: Meta<typeof DefaultStory> = {
  title: "Pages/Applications/Occupation",
  component: config.component,
};
export default meta;

type Story = StoryObj<typeof DefaultStory>;
export const Default: Story = DefaultStory;
