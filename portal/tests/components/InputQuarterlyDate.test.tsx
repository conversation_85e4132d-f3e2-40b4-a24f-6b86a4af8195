/* eslint-disable @typescript-eslint/no-explicit-any */
import { fireEvent, render, screen } from "@testing-library/react";

import InputQuarterlyDate from "src/components/core/InputQuarterlyDate";
import React from "react";
import userEvent from "@testing-library/user-event";

function setup(customProps = {}) {
  const props: any = Object.assign(
    {
      quarterLabel: "Quarter",
      name: "field-name",
      yearLabel: "Year",
    },
    customProps
  );

  // Setup state management so we can test event handlers
  const InputQuarterlyDateWithState = () => {
    const [value, setValue] = React.useState(props.value);
    const handleChange = (event: any) => {
      setValue(event.target.value);
      props.onChange(event);
    };

    return (
      <InputQuarterlyDate {...props} value={value} onChange={handleChange} />
    );
  };

  return render(<InputQuarterlyDateWithState />);
}
const getYearTextBox = () => screen.getByRole("textbox", { name: "Year" });
const getQuarterDropdown = () =>
  screen.getByRole("combobox", { name: "Quarter" });

describe("InputQuarterlyDate", () => {
  // TODO (PFMLPB-23216) - Come-up with a way to standardize the way we write single-component tests
  // TODO (PFMLPB-24549) - Tests for Warning states
  // TODO (PFMLPB-24566) - Tests for Error states

  it("renders component", () => {
    const { container } = setup({});

    expect(getYearTextBox()).toBeInTheDocument();
    expect(getQuarterDropdown()).toBeInTheDocument();
    expect(container.firstChild).toMatchSnapshot();
  });

  it("renders a fieldset with a legend", () => {
    setup({ label: "Bond renewal date" });

    expect(
      screen.getByRole("group", { name: "Bond renewal date" })
    ).toBeInTheDocument();
  });

  it("year input ignores non-numerical characters", async () => {
    const onChange = jest.fn();
    setup({ onChange });
    const yearInput = getYearTextBox();
    await userEvent.type(yearInput, "123abc4");
    expect(yearInput).toHaveValue("1234");
  });

  it("year input stops at 4 characters", async () => {
    const onChange = jest.fn();
    setup({ onChange });
    const yearInput = getYearTextBox();
    await userEvent.type(yearInput, "123456");
    expect(yearInput).toHaveValue("1234");
  });

  it("quarter dropdown calls onChange for each of the valid options", async () => {
    const onChange = jest.fn();
    setup({ onChange });
    const options = ["01", "04", "07", "10"];
    const optionPromises = options.map(async (option) => {
      await userEvent.selectOptions(getQuarterDropdown(), [option]);
    });
    await Promise.all(optionPromises);
    expect(onChange).toHaveBeenCalledTimes(options.length);
  });

  it("year input calls onChange with each keystroke", async () => {
    const onChange = jest.fn();
    setup({ onChange });
    const keystrokes = "2020".split("");
    const typingPromises = keystrokes.map(async (stroke) => {
      await userEvent.type(getYearTextBox(), stroke);
    });
    await Promise.all(typingPromises);
    expect(onChange).toHaveBeenCalledTimes(keystrokes.length);
  });

  it("year input updates value when blurred", async () => {
    const onChange = jest.fn();
    setup({
      onChange,
      value: "202-04-01",
    });

    const getLastChangeEventValue = () => {
      // Access the event argument this way instead of toHaveBeenCalledWith()
      // to workaround warnings related to https://fb.me/react-event-pooling
      return onChange.mock.calls[onChange.mock.calls.length - 1][0].target
        .value;
    };

    const yearInput = getYearTextBox();
    await userEvent.type(yearInput, "0");
    expect(getLastChangeEventValue()).toBe("2020-04-01");
    fireEvent.blur(yearInput);
    expect(onChange).toHaveBeenCalled();
  });
});
