// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`PageWrapper renders Spinner when isLoading is true 1`] = `
<main
  class="l-main grid-container margin-top-5 margin-bottom-8"
  id="main"
>
  <div
    class="grid-row"
  >
    <div
      class="grid-col-fill"
    >
      <section
        class="margin-top-8 text-center"
        id="page"
      >
        <span
          aria-label="Loading"
          class="c-spinner"
          role="progressbar"
        />
      </section>
    </div>
  </div>
</main>
`;

exports[`PageWrapper renders the children as the page's body 1`] = `
<main
  class="l-main grid-container margin-top-5 margin-bottom-8"
  id="main"
>
  <div
    class="grid-row"
  >
    <div
      class="grid-col-fill"
    >
      <section
        id="page"
      >
        <div>
          Page
        </div>
      </section>
    </div>
  </div>
</main>
`;

exports[`PageWrapper sets description meta tag 1`] = `
<div
  class="l-container"
>
  <div
    data-testid="Header"
  >
    <a
      class="usa-skipnav"
      href="#main"
    >
      Skip to main content
    </a>
    <div
      class="ma__brand-banner ma__brand-banner--c-primary-bg-light"
      data-testid="mayflower_brand_banner"
    >
      <button
        aria-controls="ma__brand-banner-content"
        aria-expanded="false"
        class="ma__brand-banner-container"
        type="button"
      >
        <img
          alt="Massachusetts State Seal"
          class="ma__brand-banner-logo"
          src="test-file-stub"
        />
        <span
          class="ma__brand-banner-text"
        >
          <span>
            An official website of the Commonwealth of Massachusetts
          </span>
          <span>
               
          </span>
          <span
            class="ma__brand-banner-button ma__button-icon ma__icon-small ma__button-icon--quaternary ma__button-icon--c-primary"
          >
            <span>
              Here’s how you know
            </span>
            <svg
              aria-hidden="true"
              fill="currentColor"
              height="1em"
              viewBox="0 0 59 38"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M29.414,37.657 L0.344,8.586 L8.828,0.102 L29.414,20.686 L50,0.1 L58.484,8.585 L29.414,37.657"
              />
            </svg>
          </span>
        </span>
      </button>
      <ul
        class="ma__brand-banner-expansion"
        id="ma__brand-banner-content"
      >
        <li
          class="ma__brand-banner-expansion-item"
        >
          <span
            class="text-primary"
          >
            <svg
              aria-hidden="true"
              fill="currentColor"
              height="30"
              viewBox="0 0 16 16"
              width="30"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M1169.73 14.5924L1169.73 13L1168.27 13L1168.27 14.5924C1166.21 14.9558 1164.6399999999999 16.8309 1164.6399999999999 19.0952L1173.36 19.0952C1173.36 16.8309 1171.79 14.955799999999998 1169.7299999999998 14.592399999999998ZM1176.27 24.4286L1174.09 24.4286L1174.09 21.381C1174.09 20.9604 1173.77 20.619 1173.36 20.619L1164.6399999999999 20.619C1164.2299999999998 20.619 1163.9099999999999 20.9604 1163.9099999999999 21.381L1163.9099999999999 24.4286L1161.7299999999998 24.4286C1161.3299999999997 24.4286 1160.9999999999998 24.7699 1160.9999999999998 25.1905L1160.9999999999998 28.2381C1160.9999999999998 28.659399999999998 1161.3299999999997 29 1161.7299999999998 29L1176.2699999999998 29C1176.6699999999998 29 1176.9999999999998 28.6594 1176.9999999999998 28.2381L1176.9999999999998 25.1905C1176.9999999999998 24.7699 1176.6699999999998 24.4286 1176.2699999999998 24.4286ZM1163.91 27.4762L1162.45 27.4762L1162.45 25.952399999999997L1163.91 25.952399999999997ZM1166.82 27.4762L1165.36 27.4762L1165.36 25.952399999999997L1166.82 25.952399999999997ZM1166.82 23.6667L1165.36 23.6667L1165.36 22.142899999999997L1166.82 22.142899999999997ZM1169.73 27.4762L1168.27 27.4762L1168.27 25.952399999999997L1169.73 25.952399999999997ZM1169.73 23.6667L1168.27 23.6667L1168.27 22.142899999999997L1169.73 22.142899999999997ZM1172.64 27.4762L1171.18 27.4762L1171.18 25.952399999999997L1172.64 25.952399999999997ZM1172.64 23.6667L1171.18 23.6667L1171.18 22.142899999999997L1172.64 22.142899999999997ZM1175.55 27.4762L1174.09 27.4762L1174.09 25.952399999999997L1175.55 25.952399999999997Z "
                transform="matrix(1,0,0,1,-1161,-13)"
              />
            </svg>
          </span>
          <div
            class="ma__brand-banner-expansion-item-content"
          >
            <p>
              Official websites use .mass.gov
            </p>
            <p>
              A .mass.gov website belongs to an official government organization in Massachusetts.
            </p>
          </div>
        </li>
        <li
          class="ma__brand-banner-expansion-item"
        >
          <span
            class="text-secondary"
          >
            <svg
              aria-hidden="true"
              fill="currentColor"
              height="30"
              viewBox="0 0 21 25"
              width="30"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M10.399 0a6.491 6.491 0 016.487 6.258l.004.233-.001 3.853h2.077c.952 0 1.724.773 1.724 1.725v11.207c0 .952-.772 1.724-1.724 1.724H1.724A1.724 1.724 0 010 23.276V12.069c0-.952.772-1.724 1.724-1.724l2.184-.001V6.491l.004-.233A6.491 6.491 0 0110.4 0zm0 1.517A4.974 4.974 0 005.43 6.275l-.005.216v3.853h9.947V6.491l-.004-.216a4.974 4.974 0 00-4.97-4.758z"
                fill-rule="evenodd"
              />
            </svg>
          </span>
          <div
            class="ma__brand-banner-expansion-item-content"
          >
            <p>
              Secure websites use HTTPS certificate
            </p>
            <p>
              A lock icon (
              <svg
                aria-hidden="true"
                fill="currentColor"
                height="12"
                viewBox="0 0 21 25"
                width="12"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M10.399 0a6.491 6.491 0 016.487 6.258l.004.233-.001 3.853h2.077c.952 0 1.724.773 1.724 1.725v11.207c0 .952-.772 1.724-1.724 1.724H1.724A1.724 1.724 0 010 23.276V12.069c0-.952.772-1.724 1.724-1.724l2.184-.001V6.491l.004-.233A6.491 6.491 0 0110.4 0zm0 1.517A4.974 4.974 0 005.43 6.275l-.005.216v3.853h9.947V6.491l-.004-.216a4.974 4.974 0 00-4.97-4.758z"
                  fill-rule="evenodd"
                />
              </svg>
              ) or https:// means you’ve safely connected to the official website. Share sensitive information only on official, secure websites.
            </p>
          </div>
        </li>
      </ul>
    </div>
    <div
      class="ma__header_slim"
    >
      <div
        class="ma__header_slim__utility"
      >
        <div
          class="ma__header_slim__utility-container ma__container"
        >
          <div
            class="flex-fill margin-y-1 margin-left-2"
          >
            <div
              class="grid-row grid-gap"
            >
              <div
                class="grid-col"
              >
                <a
                  class="usa-button width-auto usa-button--unstyled usa-button--inverse usa-button--outline"
                  href="https://mass.gov/PaidLeave"
                >
                  Back to Mass.gov
                </a>
              </div>
              <div
                class="grid-col text-right"
              >
                <span
                  class="display-inline-block"
                  data-testid="language_selector"
                >
                  <div
                    class="usa-language-container usa-language--small"
                  >
                    <ul
                      class="usa-language__primary usa-accordion"
                    >
                      <li
                        class="usa-language__primary-item"
                      >
                        <button
                          aria-controls="language-options"
                          aria-expanded="false"
                          class="usa-button width-auto usa-button--unstyled usa-button--inverse usa-button--outline margin-right-2 usa-language__link"
                          data-testid="language-selector-toggle"
                          type="button"
                        >
                          <svg
                            aria-hidden="true"
                            class="usa-icon usa-icon--size-3 margin-right-05 position-relative text-middle"
                            focusable="false"
                            role="img"
                          >
                            <use
                              xlink:href="/img/sprite.svg#language"
                            />
                          </svg>
                          Languages
                        </button>
                        <ul
                          class="usa-language__submenu"
                          data-testid="language-selector-menu"
                          hidden=""
                          id="language-options"
                        >
                          <li
                            class="usa-language__submenu-item "
                          >
                            <button
                              class="usa-button width-full text-left"
                              data-testid="language-selector-en"
                              tabindex="0"
                              title="English"
                              type="button"
                            >
                              <span
                                class="pretranslated-multilanguage-content"
                                lang="en"
                              >
                                English
                              </span>
                            </button>
                          </li>
                          <li
                            class="usa-language__submenu-item "
                          >
                            <button
                              class="usa-button width-full text-left"
                              data-testid="language-selector-es"
                              tabindex="0"
                              title="Español"
                              type="button"
                            >
                              <span
                                class="pretranslated-multilanguage-content"
                                lang="es"
                              >
                                Español
                              </span>
                            </button>
                          </li>
                          <li
                            class="usa-language__submenu-item "
                          >
                            <button
                              class="usa-button width-full text-left"
                              data-testid="language-selector-ht"
                              tabindex="0"
                              title="Kreyòl Ayisyen"
                              type="button"
                            >
                              <span
                                class="pretranslated-multilanguage-content"
                                lang="ht"
                              >
                                Kreyòl Ayisyen
                              </span>
                            </button>
                          </li>
                          <li
                            class="usa-language__submenu-item "
                          >
                            <button
                              class="usa-button width-full text-left"
                              data-testid="language-selector-pt-BR"
                              tabindex="0"
                              title="Português"
                              type="button"
                            >
                              <span
                                class="pretranslated-multilanguage-content"
                                lang="pt-BR"
                              >
                                Português
                              </span>
                            </button>
                          </li>
                          <li
                            class="usa-language__submenu-item "
                          >
                            <button
                              class="usa-button width-full text-left"
                              data-testid="language-selector-zh-Hans"
                              tabindex="0"
                              title="中文"
                              type="button"
                            >
                              <span
                                class="pretranslated-multilanguage-content"
                                lang="zh-Hans"
                              >
                                中文
                              </span>
                            </button>
                          </li>
                          <li
                            class="usa-language__submenu-item "
                          >
                            <button
                              class="usa-button width-full text-left"
                              data-testid="language-selector-vi"
                              tabindex="0"
                              title="Tiếng Việt"
                              type="button"
                            >
                              <span
                                class="pretranslated-multilanguage-content"
                                lang="vi"
                              >
                                Tiếng Việt
                              </span>
                            </button>
                          </li>
                          <li
                            class="usa-language__submenu-item "
                          >
                            <button
                              class="usa-button width-full text-left"
                              data-testid="language-selector-fr"
                              tabindex="0"
                              title="Français"
                              type="button"
                            >
                              <span
                                class="pretranslated-multilanguage-content"
                                lang="fr"
                              >
                                Français
                              </span>
                            </button>
                          </li>
                        </ul>
                      </li>
                    </ul>
                  </div>
                </span>
                <a
                  class="usa-button width-auto margin-right-2 usa-button--unstyled usa-button--inverse usa-button--outline"
                  href="/oauth-start"
                >
                  <svg
                    aria-hidden="true"
                    class="usa-icon usa-icon--size-3 margin-right-05 position-relative text-middle"
                    focusable="false"
                    role="img"
                  >
                    <use
                      xlink:href="/img/sprite.svg#login"
                    />
                  </svg>
                  Log in
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
      <header
        class="ma__header_slim__header"
        id="header"
      >
        <div
          class="ma__header_slim__header-container ma__container"
        >
          <div
            class="ma__header_slim__logo"
          >
            <div
              class="ma__site-logo"
            >
              <a
                href="/"
              >
                <img
                  alt="Massachusetts State Seal"
                  class="ma__image"
                  height="45"
                  src="test-file-stub"
                  width="45"
                />
                <span>
                  Paid Family and Medical Leave
                </span>
              </a>
            </div>
          </div>
        </div>
      </header>
    </div>
  </div>
  <main
    class="l-main grid-container margin-top-5 margin-bottom-8"
    id="main"
  >
    <div
      class="grid-row"
    >
      <div
        class="grid-col-fill"
      >
        <section
          id="page"
        >
          <div>
            Page
          </div>
        </section>
      </div>
    </div>
  </main>
  <footer
    class="ma__footer-slim"
    id="footer"
  >
    <div
      class="ma__footer-slim__container ma__container ma__footer-slim__container--stacked"
    >
      <div
        class="ma__footer-slim__container__logos ma__footer-slim__container__logos--stacked"
      >
        <div
          class="ma__site-logo"
        >
          <a
            href="https://mass.gov/dfml"
            title="Go to DFML homepage"
          >
            <img
              alt="Department of Family and Medical Leave logo"
              class="ma__image"
              height="45"
              src="test-file-stub"
              width="165"
            />
          </a>
        </div>
        <div
          class="ma__site-logo"
        >
          <a
            href="https://www.mass.gov/pfml"
            title="Go to PFML homepage"
          >
            <img
              alt="Paid Family and Medical Leave program logo"
              class="ma__image"
              height="45"
              src="test-file-stub"
              width="118"
            />
          </a>
        </div>
      </div>
      <div
        class="ma__footer-slim__container__inner ma__footer-slim__container__inner--stacked"
      >
        <div
          class="ma__footer-slim__info"
        >
          <div
            class="ma__footer-slim__title"
          >
            Paid Family and Medical Leave (PFML)
          </div>
          <p>
            Paid Family and Medical Leave is a state-offered benefit for anyone who works in Massachusetts and is eligible to take up to 26 weeks of paid leave for medical or family reasons.
          </p>
          <p
            class="ma__footer-slim__copyright"
          >
            © 
            2025 Commonwealth of Massachusetts.
          </p>
        </div>
        <div
          class="ma__footer-slim__details"
        >
          <div
            class="ma__footer-slim__links"
          >
            <a
              href="https://www.mass.gov/privacypolicy"
            >
              Privacy Policy
            </a>
            <a
              href="https://mass.gov/paidleave-informedconsent"
            >
              Data Sharing Agreement
            </a>
          </div>
          <address
            class="ma__footer-slim__contact"
          >
            <div
              class="ma__footer-slim__contact__item currentColor"
            >
              <svg
                aria-hidden="true"
                class="usa-icon usa-icon--size-3"
                focusable="false"
                role="img"
              >
                <use
                  xlink:href="/img/sprite.svg#location_on"
                />
              </svg>
              <span
                class="ma__address"
              >
                <div
                  class="ma__address__address"
                >
                  PO Box 838 Lawrence, MA 01842
                </div>
              </span>
            </div>
            <div
              class="ma__footer-slim__contact__item currentColor"
            >
              <svg
                aria-hidden="true"
                fill="currentColor"
                height="20"
                viewBox="0 0 36 30"
                width="20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M424 1674L394 1674L394 1693.5L424 1693.5ZM421 1690.5L397 1690.5L397 1677L421 1677ZM424 1695L394 1695L391 1704L427 1704ZM405.658 1702.5L406.357 1701L411.64300000000003 1701L412.34200000000004 1702.5Z"
                  transform="translate(-391 -1674)"
                />
              </svg>
              <a
                href="https://mass.gov/dfml"
              >
                Department of Family and Medical Leave (DFML)
              </a>
            </div>
            <div
              class="ma__footer-slim__contact__item currentColor"
            >
              <svg
                aria-hidden="true"
                fill="currentColor"
                height="20"
                viewBox="0 0 33 37"
                width="20"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M412.841 2040.51C401.798 2045.57 384.657 2012.19 395.451 2006.56L398.60900000000004 2005L403.84400000000005 2015.23L400.72300000000007 2016.77C397.44200000000006 2018.54 404.27600000000007 2031.91 407.63200000000006 2030.28C407.7680000000001 2030.22 410.7150000000001 2028.77 410.72600000000006 2028.76L416.0040000000001 2038.96C415.9920000000001 2038.97 413.02200000000005 2040.43 412.84100000000007 2040.51ZM413.24 2011.28C415.564 2011.98 417.62600000000003 2013.56 418.87 2015.86C420.115 2018.1699999999998 420.309 2020.76 419.616 2023.09L416.926 2022.29C417.413 2020.6499999999999 417.275 2018.83 416.39799999999997 2017.2C415.52399999999994 2015.5800000000002 414.073 2014.47 412.436 2013.98ZM411.838 2016C412.956 2016.33 413.95000000000005 2017.1 414.548 2018.21C415.148 2019.32 415.242 2020.56 414.908 2021.69L410.535 2020.38ZM413.841 2009.26C416.681 2010.11 419.2 2012.04 420.722 2014.86C422.24199999999996 2017.6799999999998 ************.85 421.63399999999996 2023.6899999999998L424.39199999999994 2024.5199999999998C425.44899999999996 2020.9599999999998 425.15399999999994 2017.0099999999998 423.2509999999999 2013.4899999999998C421.3539999999999 2009.9699999999998 418.20899999999995 2007.5599999999997 414.66399999999993 2006.4999999999998Z "
                  transform="matrix(1,0,0,1,-392,-2005)"
                />
              </svg>
              <div>
                <span
                  class="ma__phone-number"
                >
                  <a
                    class="ma__phone-number__number"
                    href="tel:(*************"
                  >
                    (*************
                  </a>
                  <p
                    class="ma__contact__details"
                  >
                    <span>
                      Our Customer Service Representatives can answer your questions in 
                      <strong>
                        English
                      </strong>
                      , 
                      <strong>
                        Spanish
                      </strong>
                       and 
                      <strong>
                        Portuguese
                      </strong>
                      . Translation services for up to 
                      <strong>
                        240+ languages
                      </strong>
                       are also available to better serve employees, employers and medical providers.
                    </span>
                  </p>
                </span>
              </div>
            </div>
          </address>
        </div>
      </div>
    </div>
  </footer>
</div>
`;
