// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`DocumentRequirements when rendering Mass ID content should render correctly 1`] = `
<div>
  <div
    class="usa-summary-box margin-bottom-2"
    role="complementary"
  >
    <div
      class="usa-summary-box__body grid-row grid-gap"
    >
      <div
        class="grid-col"
      >
        <h3
          class="usa-summary-box__heading font-heading-xs text-bold"
        >
          Document requirements
        </h3>
        <div
          class="usa-summary-box__text"
        >
          <ul
            class="usa-list"
          >
            <li>
              The image of the card must be in full color
            </li>
            <li>
              The text must be clear and readable
            </li>
            <li>
              The front and back must be uploaded
            </li>
            <li>
              Each file must be smaller than 4.5 MB
            </li>
          </ul>
        </div>
      </div>
      <div
        class="tablet:grid-col margin-top-2 tablet:margin-top-0"
      >
        <h4
          class="usa-summary-box__heading font-heading-xs text-bold"
        >
          Example
        </h4>
        <div
          class="grid-row grid-gap"
        >
          <div
            class="grid-col"
          >
            <figure
              class="margin-0"
            >
              <img
                alt="A blurry image of the front side of a Massachusetts ID."
                src="test-file-stub"
              />
              <figcaption
                class="font-body-3xs"
              >
                Must not be blurry
              </figcaption>
            </figure>
          </div>
          <div
            class="grid-col"
          >
            <figure
              class="margin-0"
            >
              <img
                alt="A clear image of both sides of a Massachusetts ID."
                src="test-file-stub"
              />
              <figcaption
                class="font-body-3xs"
              >
                Must upload both sides
              </figcaption>
            </figure>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`DocumentRequirements when rendering appeal support content should render correctly 1`] = `
<div>
  <div
    class="usa-summary-box margin-bottom-2"
    role="complementary"
  >
    <div
      class="usa-summary-box__body grid-row grid-gap"
    >
      <div
        class="grid-col"
      >
        <h3
          class="usa-summary-box__heading font-heading-xs text-bold"
        >
          Document requirements
        </h3>
        <div
          class="usa-summary-box__text"
        >
          <ul
            class="usa-list"
          >
            <li>
              The document must be valid and unexpired
            </li>
            <li>
              Any ID document must be in full color
            </li>
            <li>
              The text must be clear and readable
            </li>
            <li>
              The document can be in one file, or in separate files
            </li>
            <li>
              Each file must be smaller than 4.5 MB
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`DocumentRequirements when rendering certification content should render correctly 1`] = `
<div>
  <div
    class="usa-summary-box margin-bottom-2"
    role="complementary"
  >
    <div
      class="usa-summary-box__body grid-row grid-gap"
    >
      <div
        class="grid-col"
      >
        <h3
          class="usa-summary-box__heading font-heading-xs text-bold"
        >
          Document requirements
        </h3>
        <div
          class="usa-summary-box__text"
        >
          <ul
            class="usa-list"
          >
            <li>
              The text must be clear and readable
            </li>
            <li>
              Upload every page of the document where you or your health care provider have entered information
            </li>
            <li>
              Each file you upload must be smaller than 4.5 MB
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`DocumentRequirements when rendering employer exemptions content should render correctly 1`] = `
<div>
  <div
    class="usa-summary-box margin-bottom-2"
    role="complementary"
  >
    <div
      class="usa-summary-box__body grid-row grid-gap"
    >
      <div
        class="grid-col"
      >
        <h3
          class="usa-summary-box__heading font-heading-xs text-bold"
        >
          Document requirements
        </h3>
        <div
          class="usa-summary-box__text"
        >
          <ul
            class="usa-list"
          >
            <li>
              The text must be clear and readable
            </li>
            <li>
              Any file you upload must be smaller than 4.5 mb
            </li>
            <li>
              Any file you upload must be a PDF, JPG, JPEG, or PNG file 
              <strong>
                (PDF files are preferred)
              </strong>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`DocumentRequirements when rendering health certification content should render correctly 1`] = `
<div>
  <div
    class="usa-summary-box margin-bottom-2"
    role="complementary"
  >
    <div
      class="usa-summary-box__body grid-row grid-gap"
    >
      <div
        class="grid-col"
      >
        <h3
          class="usa-summary-box__heading font-heading-xs text-bold"
        >
          Document requirements
        </h3>
        <div
          class="usa-summary-box__text"
        >
          <ul
            class="usa-list"
          >
            <li>
              The text must be clear and readable
            </li>
            <li>
              Any file you upload must be smaller than 4.5 MB
            </li>
          </ul>
          <p>
            To review an application we need all the pages of your certification document. Upload every page even if it does not require information from you or your health care provider. If there are pages you have already submitted, you don’t have to send those pages again unless they have been updated. For any updates, upload both the updated pages and the page with your provider’s information and signature.
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
`;

exports[`DocumentRequirements when rendering other ID content should render correctly 1`] = `
<div>
  <div
    class="usa-summary-box margin-bottom-2"
    role="complementary"
  >
    <div
      class="usa-summary-box__body grid-row grid-gap"
    >
      <div
        class="grid-col"
      >
        <h3
          class="usa-summary-box__heading font-heading-xs text-bold"
        >
          Document requirements
        </h3>
        <div
          class="usa-summary-box__text"
        >
          <ul
            class="usa-list"
          >
            <li>
              The document must be valid and unexpired
            </li>
            <li>
              The image of the document must be in full color
            </li>
            <li>
              The text must be clear and readable
            </li>
            <li>
              Each file must be smaller than 4.5 MB
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
`;
