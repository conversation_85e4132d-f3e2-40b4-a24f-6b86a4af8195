// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`InputQuarterlyDate renders component 1`] = `
<fieldset
  class="usa-fieldset margin-top-3 usa-form-group"
>
  <legend
    class="usa-label text-bold usa-legend font-heading-lg line-height-sans-3 margin-bottom-1 maxw-tablet"
  />
  <div
    class="usa-memorable-date"
  >
    <div
      class="usa-form-group margin-right-1"
    >
      <label
        class="usa-label  font-heading-xs measure-5"
        for="Dropdown1"
        id="Dropdown1_label"
      >
        Quarter
      </label>
      <div
        class=""
        data-default-value=""
      >
        <select
          aria-labelledby="Dropdown1_label Dropdown1_hint Dropdown1_error"
          class="usa-select maxw-mobile-lg"
          id="Dropdown1"
          name="field-name_quarter"
        >
          <option
            value=""
          >
            - Select an answer -
          </option>
          <option
            value="01"
          >
            January 1st
          </option>
          <option
            value="04"
          >
            April 1st
          </option>
          <option
            value="07"
          >
            July 1st
          </option>
          <option
            value="10"
          >
            October 1st
          </option>
        </select>
      </div>
    </div>
    <div
      class="usa-form-group usa-form-group--year"
    >
      <label
        class="usa-label margin-top-0 font-heading-xs measure-5"
        for="InputText«r0»"
        id="InputText«r0»_label"
      >
        Year
      </label>
      <input
        aria-labelledby="InputText«r0»_label InputText«r0»_hint InputText«r0»_error"
        class="usa-input usa-input--inline maxw-mobile-lg"
        id="InputText«r0»"
        inputmode="numeric"
        maxlength="4"
        name="field-name_year"
        type="text"
        value=""
      />
    </div>
  </div>
</fieldset>
`;
