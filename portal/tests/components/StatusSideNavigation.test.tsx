import { render, screen } from "@testing-library/react";

import ClaimDetail from "src/models/ClaimDetail";
import LeaveReason from "src/models/LeaveReason";
import React from "react";
import StatusSideNavigation from "src/features/status/StatusSideNavigation";
import { createAbsencePeriod } from "tests/test-utils";
import routes from "src/routes";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";

// getRouteFor in StatusSideNavigation will return "STATUS" or "VIEW_PAYMENTS" context
const mockGetRouteFor = (context: string) => {
  if (context === "STATUS") return routes.applications.status.claim;
  if (context === "VIEW_PAYMENTS") return routes.applications.status.payments;
  if (context === "REPORT_INTERMITTENT_LEAVE")
    return routes.applications.status.reportIntermittentLeave;
  return "";
};

const CLAIM_DETAIL = new ClaimDetail({
  fineos_absence_id: "fineos-abence-id",
  absence_periods: [
    createAbsencePeriod({
      period_type: "Reduced Schedule",
      absence_period_start_date: "2021-06-01",
      absence_period_end_date: "2021-06-08",
      request_decision: "Approved",
      fineos_leave_request_id: "PL-14432-0000002026",
      reason: LeaveReason.bonding,
      reason_qualifier_one: "Newborn",
    }),
  ],
});

describe("StatusSideNavigation", () => {
  it("renders the component", () => {
    render(
      <StatusSideNavigation
        activePath={routes.applications.status.payments}
        getRouteFor={mockGetRouteFor}
        claimDetail={CLAIM_DETAIL}
      />
    );
    expect(screen.getByRole("link", { name: "Payments" })).toBeInTheDocument();
    expect(
      screen.getByRole("link", { name: "Application" })
    ).toBeInTheDocument();
  });

  it("renders the report leave tab if isIntermittent", () => {
    const INTERMITTENT_CLAIM_DETAIL = new ClaimDetail({
      fineos_absence_id: "fineos-abence-id",
      absence_periods: [
        createAbsencePeriod({
          period_type: "Intermittent",
          absence_period_start_date: "2021-06-01",
          absence_period_end_date: "2021-06-08",
          request_decision: "Approved",
          fineos_leave_request_id: "PL-14432-0000002026",
          reason: LeaveReason.bonding,
          reason_qualifier_one: "Newborn",
        }),
      ],
    });
    render(
      <StatusSideNavigation
        activePath={routes.applications.status.reportIntermittentLeave}
        getRouteFor={mockGetRouteFor}
        claimDetail={INTERMITTENT_CLAIM_DETAIL}
      />
    );
    expect(
      screen.getByRole("link", { name: "View and report leave hours" })
    ).toBeInTheDocument();
  });

  describe("with enableClaimantPortalSideNavigationV2 enabled", () => {
    beforeEach(() => {
      setFeatureFlags({ enableClaimantPortalSideNavigationV2: true });
    });

    it("renders a sticky side nav when page width is >= 1053", () => {
      Object.defineProperty(window, "innerWidth", { value: 1053 });

      render(
        <StatusSideNavigation
          activePath={routes.applications.status.payments}
          getRouteFor={mockGetRouteFor}
          claimDetail={CLAIM_DETAIL}
        />
      );

      const sideNav = screen.getByTestId("status-side-navigation");
      expect(sideNav).toHaveClass("position-sticky");
    });

    it("doesn't render a sticky side nav when page width is < 1053", () => {
      Object.defineProperty(window, "innerWidth", { value: 1052 });

      render(
        <StatusSideNavigation
          activePath={routes.applications.status.payments}
          getRouteFor={mockGetRouteFor}
          claimDetail={CLAIM_DETAIL}
        />
      );

      const sideNav = screen.getByTestId("status-side-navigation");
      expect(sideNav).not.toHaveClass("position-sticky");
    });
  });

  describe("with enableClaimantPortalSideNavigationV2 disabled", () => {
    beforeEach(() => {
      setFeatureFlags({ enableClaimantPortalSideNavigationV2: false });
    });

    it("doesn't render a sticky side nav when page width is >= 1053", () => {
      Object.defineProperty(window, "innerWidth", { value: 1053 });

      render(
        <StatusSideNavigation
          activePath={routes.applications.status.payments}
          getRouteFor={mockGetRouteFor}
          claimDetail={CLAIM_DETAIL}
        />
      );

      const sideNav = screen.getByTestId("status-side-navigation");
      expect(sideNav).not.toHaveClass("position-sticky");
    });

    it("doesn't render a sticky side nav when page width is < 1053", () => {
      Object.defineProperty(window, "innerWidth", { value: 1052 });

      render(
        <StatusSideNavigation
          activePath={routes.applications.status.payments}
          getRouteFor={mockGetRouteFor}
          claimDetail={CLAIM_DETAIL}
        />
      );

      const sideNav = screen.getByTestId("status-side-navigation");
      expect(sideNav).not.toHaveClass("position-sticky");
    });
  });
});
