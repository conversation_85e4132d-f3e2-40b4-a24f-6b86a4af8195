import DocumentRequirements, {
  DocumentRequirementsProps,
} from "src/components/DocumentRequirements";
import { render, screen } from "@testing-library/react";

import React from "react";

interface Scenario {
  readonly description: string;
  readonly type: DocumentRequirementsProps["type"];
}

const scenarios: readonly Scenario[] = [
  { type: "appeal_support", description: "appeal support" },
  { type: "certification", description: "certification" },
  { type: "healthCertification", description: "health certification" },
  { type: "massId", description: "Mass ID" },
  { type: "otherId", description: "other ID" },
  // TODO (PFMLPB-21077): Add additional scenarios for each of the individual emptions-related document types
  { type: "employerExemption", description: "employer exemptions" },
];

describe("DocumentRequirements", () => {
  describe.each(scenarios)(
    "when rendering $description content",
    ({ type }) => {
      it("should render correctly", () => {
        const view = render(<DocumentRequirements type={type} />);
        expect(view.container).toMatchSnapshot();
      });

      it("should display an accessible heading", () => {
        render(<DocumentRequirements type={type} />);
        const heading = screen.getByRole("heading", { level: 3 });
        expect(heading).toHaveAccessibleName("Document requirements");
      });
    }
  );
});
