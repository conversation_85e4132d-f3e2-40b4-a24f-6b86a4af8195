import getNestedObjectValue from "src/utils/getNestedObjectValue";

describe("getNestedObjectValue", () => {
  it("returns undefined if object parameter is empty", () => {
    expect(getNestedObjectValue({}, "field")).toBe(undefined);
  });

  it("returns undefined if object parameter is null", () => {
    // @ts-expect-error TS2345
    expect(getNestedObjectValue(null, "field")).toBe(undefined);
  });

  it("returns undefined if object parameter is undefined", () => {
    // @ts-expect-error TS2345
    expect(getNestedObjectValue(undefined, "field")).toBe(undefined);
  });

  it("returns undefined if object parameter is not an object", () => {
    // @ts-expect-error TS2345
    expect(getNestedObjectValue("field", "field")).toBe(undefined);
  });

  it("returns undefined if field not in object parameter", () => {
    const obj = {
      p1: "p1",
    };
    expect(getNestedObjectValue(obj, "p2")).toBe(undefined);
  });

  it("returns nested value", () => {
    const obj = {
      level1: {
        level2: {
          level3: {
            p1: "v1",
            p2: "v2",
          },
          p3: "v3",
        },
        p4: "v4",
        level2_2: {
          level3_2: {
            p5: "v5",
          },
        },
      },
      level1_2: {
        p6: "v6",
      },
    };

    expect(getNestedObjectValue(obj, "level1.level2.level3.p1")).toBe("v1");
    expect(getNestedObjectValue(obj, "level1.level2.level3.p2")).toBe("v2");
    expect(getNestedObjectValue(obj, "level1.level2.p3")).toBe("v3");
    expect(getNestedObjectValue(obj, "level1.p4")).toBe("v4");
    expect(getNestedObjectValue(obj, "level1.level2_2.level3_2.p5")).toBe("v5");
    expect(getNestedObjectValue(obj, "level1_2.p6")).toBe("v6");
  });
});
