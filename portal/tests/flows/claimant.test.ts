import claimantFlow, { guards } from "src/flows/claimant";

import { ClaimSteps } from "src/models/Step";
import { EmploymentStatus } from "src/models/BenefitsApplication";
import { MockBenefitsApplicationBuilder } from "tests/test-utils";
import { fields as departmentFields } from "src/pages/applications/department";
import { fields as employmentStatusFields } from "src/pages/applications/employment-status";
import routes from "src/routes";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";

describe("Claimant flow", () => {
  describe("Guards", () => {
    const mockEvent = { type: "TEST" };
    const mockMeta = { cond: {} };

    describe("hasEmployerWithDepartments", () => {
      it("returns true when employed and employer_organization_units is not empty", () => {
        const context = {
          claim: new MockBenefitsApplicationBuilder()
            .employed()
            .employerOrganizationUnits([
              { organization_unit_id: "1", name: "Department 1" },
            ])
            .create(),
        };
        expect(
          guards.hasEmployerWithDepartments(context, mockEvent, mockMeta)
        ).toBe(true);
      });

      it("returns false when employed and employer_organization_units is empty", () => {
        const context = {
          claim: new MockBenefitsApplicationBuilder().employed().create(),
        };
        expect(
          guards.hasEmployerWithDepartments(context, mockEvent, mockMeta)
        ).toBe(false);
      });

      it("returns false when not employed", () => {
        const context = {
          claim: new MockBenefitsApplicationBuilder().create(),
        };
        expect(
          guards.hasEmployerWithDepartments(context, mockEvent, mockMeta)
        ).toBe(false);
      });
    });

    describe("hasUserNotFoundError", () => {
      it("returns true when warnings has the require_contributing_employer rule", () => {
        const context = {
          warnings: [
            { rule: "require_contributing_employer", namespace: "test" },
          ],
        };
        expect(guards.hasUserNotFoundError(context, mockEvent, mockMeta)).toBe(
          true
        );
      });
      it("returns true when warnings has the require_employee rule", () => {
        const context = {
          warnings: [{ rule: "require_employee", namespace: "test" }],
        };
        expect(guards.hasUserNotFoundError(context, mockEvent, mockMeta)).toBe(
          true
        );
      });
      it("returns true when warnings has the require_employee rule and the require_contributing_employer", () => {
        const context = {
          warnings: [
            { rule: "require_employee", namespace: "test" },
            { rule: "require_contributing_employer", namespace: "test" },
          ],
        };
        expect(guards.hasUserNotFoundError(context, mockEvent, mockMeta)).toBe(
          true
        );
      });
      it("returns false when warnings is empty", () => {
        const context = {
          warnings: [],
        };
        expect(guards.hasUserNotFoundError(context, mockEvent, mockMeta)).toBe(
          false
        );
      });
      it("returns false when warnings has other rules", () => {
        const context = {
          warnings: [{ rule: "other_rule", namespace: "test" }],
        };
        expect(guards.hasUserNotFoundError(context, mockEvent, mockMeta)).toBe(
          false
        );
      });
      it("returns false when no warnings are found", () => {
        const context = {};
        expect(guards.hasUserNotFoundError(context, mockEvent, mockMeta)).toBe(
          false
        );
      });
    });

    describe("isEmployed", () => {
      it("returns true when employment status is employed", () => {
        const context = {
          claim: new MockBenefitsApplicationBuilder().employed().create(),
        };
        expect(guards.isEmployed(context, mockEvent, mockMeta)).toBe(true);
      });

      it("returns false when employment status is not employed", () => {
        const mockClaim = new MockBenefitsApplicationBuilder().create();
        mockClaim.employment_status = EmploymentStatus.unemployed;

        const context = {
          claim: mockClaim,
        };
        expect(guards.isEmployed(context, mockEvent, mockMeta)).toBe(false);
      });

      it("returns false when employment status is null", () => {
        const context = {
          claim: new MockBenefitsApplicationBuilder().create(),
        };
        expect(guards.isEmployed(context, mockEvent, mockMeta)).toBe(false);
      });

      it("returns false when claim is undefined", () => {
        const context = {};
        expect(guards.isEmployed(context, mockEvent, mockMeta)).toBe(false);
      });
    });

    describe("isOccupationDataCollectionEnabled", () => {
      it("returns true when the enableOccupationDataCollection feature flag is enabled", () => {
        setFeatureFlags({
          enableOccupationDataCollection: true,
        });

        expect(
          guards.isOccupationDataCollectionEnabled({}, mockEvent, mockMeta)
        ).toBe(true);
      });

      it("returns false when the enableOccupationDataCollection feature flag is disabled", () => {
        setFeatureFlags({
          enableOccupationDataCollection: false,
        });

        expect(
          guards.isOccupationDataCollectionEnabled({}, mockEvent, mockMeta)
        ).toBe(false);
      });
    });
  });

  describe("Flow states", () => {
    describe("employmentStatus state", () => {
      const state = claimantFlow.states[routes.applications.employmentStatus];

      it("has correct meta data", () => {
        expect(state.meta).toEqual({
          applicableRules: [],
          step: ClaimSteps.employerInformation,
          fields: employmentStatusFields,
        });
      });

      describe("CONTINUE transitions", () => {
        const transitions = state.on.CONTINUE;

        it("transitions to noMatchFound when hasUserNotFoundError is true", () => {
          const transition = transitions[0];
          expect(transition).toEqual({
            target: routes.applications.noMatchFound,
            cond: "hasUserNotFoundError",
          });
        });

        it("transitions to department when hasEmployerWithDepartments is true", () => {
          const transition = transitions[1];
          expect(transition).toEqual({
            target: routes.applications.department,
            cond: "hasEmployerWithDepartments",
          });
        });

        it("transitions to occupation when isEmployed is true and the occupation data collection feature is enabled", () => {
          const transition = transitions[2];
          expect(transition).toEqual({
            target: routes.applications.occupation,
            cond: {
              type: "every",
              guardKeys: ["isOccupationDataCollectionEnabled", "isEmployed"],
            },
          });
        });

        it("transitions to notifiedEmployer when isEmployed is true", () => {
          const transition = transitions[3];
          expect(transition).toEqual({
            target: routes.applications.notifiedEmployer,
            cond: "isEmployed",
          });
        });

        it("transitions to checklist as default", () => {
          const transition = transitions[4];
          expect(transition).toEqual({
            target: routes.applications.checklist,
          });
        });
      });
    });

    describe("occupation state", () => {
      const state = claimantFlow.states[routes.applications.occupation];

      it("transitions to notifiedEmployer when isEmployed is true", () => {
        const transition = state.on.CONTINUE[0];
        expect(transition).toEqual({
          target: routes.applications.notifiedEmployer,
          cond: "isEmployed",
        });
      });

      it("transitions to checklist as default", () => {
        const transition = state.on.CONTINUE[1];
        expect(transition).toEqual({
          target: routes.applications.checklist,
        });
      });
    });

    describe("noMatchFound state", () => {
      const state = claimantFlow.states[routes.applications.noMatchFound];

      it("has correct meta data", () => {
        expect(state.meta).toEqual({
          step: ClaimSteps.employerInformation,
        });
      });

      describe("CONTINUE transitions", () => {
        const transitions = state.on.CONTINUE;

        it("transitions to beginAdditionalUserNotFoundInfo when hasUserNotFoundError is true", () => {
          const transition = transitions[0];
          expect(transition).toEqual({
            target: routes.applications.beginAdditionalUserNotFoundInfo,
            cond: "hasUserNotFoundError",
          });
        });

        it("transitions to department when hasEmployerWithDepartments is true", () => {
          const transition = transitions[1];
          expect(transition).toEqual({
            target: routes.applications.department,
            cond: "hasEmployerWithDepartments",
          });
        });

        it("transitions to occupation when isEmployed is true and the occupation data collection feature is enabled", () => {
          const transition = transitions[2];
          expect(transition).toEqual({
            target: routes.applications.occupation,
            cond: {
              type: "every",
              guardKeys: ["isOccupationDataCollectionEnabled", "isEmployed"],
            },
          });
        });

        it("transitions to notifiedEmployer when isEmployed is true", () => {
          const transition = transitions[3];
          expect(transition).toEqual({
            target: routes.applications.notifiedEmployer,
            cond: "isEmployed",
          });
        });

        it("transitions to checklist as default", () => {
          const transition = transitions[4];
          expect(transition).toEqual({
            target: routes.applications.checklist,
          });
        });
      });
    });

    describe("department state", () => {
      const state = claimantFlow.states[routes.applications.department];

      it("has correct meta data", () => {
        expect(state.meta).toEqual({
          step: ClaimSteps.employerInformation,
          fields: departmentFields,
        });
      });

      describe("CONTINUE transitions", () => {
        const transitions = state.on.CONTINUE;

        it("transitions to occupation when isEmployed is true and the occupation data collection feature is enabled", () => {
          const transition = transitions[0];
          expect(transition).toEqual({
            target: routes.applications.occupation,
            cond: {
              type: "every",
              guardKeys: ["isOccupationDataCollectionEnabled", "isEmployed"],
            },
          });
        });

        it("transitions to notifiedEmployer when employed", () => {
          const transition = transitions[1];
          expect(transition).toEqual({
            target: routes.applications.notifiedEmployer,
            cond: "isEmployed",
          });
        });

        it("transitions to checklist as default", () => {
          const transition = transitions[2];
          expect(transition).toEqual({
            target: routes.applications.checklist,
          });
        });
      });
    });
  });
});
