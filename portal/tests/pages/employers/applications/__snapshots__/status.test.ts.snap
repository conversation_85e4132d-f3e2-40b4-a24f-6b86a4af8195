// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Status Payments renders the payment header, table and FAQ 1`] = `
<section
  class="margin-y-5"
  data-testid="payment-information"
>
  <h2
    class="margin-bottom-3 font-heading-md text-bold"
  >
    Payment information
  </h2>
  <div
    class="usa-accordion usa-accordion--bordered"
  >
    <div
      id="delays_accordion"
    >
      <h2
        class="usa-accordion__heading margin-y-2"
      >
        <button
          aria-controls="accordion«r2m»"
          aria-expanded="false"
          class="usa-accordion__button"
          type="button"
        >
          What may cause a delayed or cancelled payment
        </button>
      </h2>
      <div
        class="usa-accordion__content usa-prose"
        data-testid="accordion«r2m»"
        hidden=""
        id="accordion«r2m»"
      >
        <strong>
          What may cause a delayed payment
        </strong>
        <p>
          The exact day a payment is issued can be delayed because of:
        </p>
        <ul>
          <li>
            State and federal holidays
          </li>
          <li>
            Pay periods that end on weekends
          </li>
          <li>
            Bank processes
          </li>
          <li>
            Changes to the leave start and end dates
          </li>
          <li>
            Changes to the benefit amount due to additional income or benefits reported
          </li>
          <li>
            Other processing issues such as issues with bank account information or mailing addresses
          </li>
        </ul>
        <p>
          <strong>
            What may cause a cancelled payment
          </strong>
        </p>
        <p>
          Payments can be cancelled because of:
        </p>
        <ul>
          <li>
            Changes to the leave start and end dates
          </li>
          <li>
            Changes to the benefit amount due to additional income or benefits reported
          </li>
          <li>
            Hours worked outside of the schedule set up with the employer or during reported intermittent hours
          </li>
        </ul>
      </div>
    </div>
    <h2
      class="usa-accordion__heading margin-y-2"
    >
      <button
        aria-controls="accordion«r2n»"
        aria-expanded="false"
        class="usa-accordion__button"
        type="button"
      >
        Why a payment may be less than the weekly benefit amount
      </button>
    </h2>
    <div
      class="usa-accordion__content usa-prose"
      data-testid="accordion«r2n»"
      hidden=""
      id="accordion«r2n»"
    >
      <p>
        To see the weekly benefit amount, view the most recent 
        <a
          href="#view_notices"
        >
          approval or change notice
        </a>
        .
      </p>
      <p>
        The payment amount seen on this page may be less than the weekly benefit amount due to 
        <a
          href="https://mass.gov/pfml-benefit-reductions"
          rel="noopener noreferrer"
          target="_blank"
        >
          other leave, income and benefits
        </a>
         reported. Another notice will be processed if we reduce the benefit amount for any reason other than tax withholding. Please note that tax withholding status cannot be changed at this point, since the application has been approved.
      </p>
      <p>
        <strong>
          Other scenarios that may change the received payment amount:
        </strong>
      </p>
      <ul>
        <li>
          Other leave, income, and benefits reported
        </li>
        <li>
          The maximum total amount of receivable PFML benefits has been reached across multiple applications
        </li>
        <li>
          A payment for a pay period less than a full week was processed and received
        </li>
        <li>
          Elected to have taxes withheld
        </li>
        <li>
          Received an overpayment
        </li>
      </ul>
    </div>
    <h2
      class="usa-accordion__heading margin-y-2"
    >
      <button
        aria-controls="accordion«r2p»"
        aria-expanded="false"
        class="usa-accordion__button"
        type="button"
      >
        Weekly benefit amount
      </button>
    </h2>
    <div
      class="usa-accordion__content usa-prose"
      data-testid="accordion«r2p»"
      hidden=""
      id="accordion«r2p»"
    >
      To see the weekly benefit amount, view the most recent 
      <a
        href="#view_notices"
      >
        approval or change notice.
      </a>
      <p>
        A new notice will be sent if the benefit amount is changed for any reason other than tax withholding. Learn more about 
        <a
          href="https://www.mass.gov/info-details/how-pfml-weekly-benefit-amounts-are-calculated-andor-changed"
          rel="noreferrer"
          target="_blank"
        >
          how PFML weekly benefit amounts are calculated and/or changed
        </a>
      </p>
    </div>
    <h2
      class="usa-accordion__heading margin-y-2"
    >
      <button
        aria-controls="accordion«r2q»"
        aria-expanded="false"
        class="usa-accordion__button"
        type="button"
      >
        How to appeal a reduced or cancelled payment
      </button>
    </h2>
    <div
      class="usa-accordion__content usa-prose"
      data-testid="accordion«r2q»"
      hidden=""
      id="accordion«r2q»"
    >
      If it appears that we’ve reduced or cancelled payments for an incorrect reason, 
      <a
        href="https://mass.gov/PaidLeaveDecisionAppeal"
        rel="noreferrer noopener"
        target="_blank"
      >
        the DFML decision can be appealed.
      </a>
    </div>
    <h2
      class="usa-accordion__heading margin-y-2"
    >
      <button
        aria-controls="accordion«r2r»"
        aria-expanded="false"
        class="usa-accordion__button"
        type="button"
      >
        Change payment preferences
      </button>
    </h2>
    <div
      class="usa-accordion__content usa-prose"
      data-testid="accordion«r2r»"
      hidden=""
      id="accordion«r2r»"
    >
      <p>
        To make changes to the payment method or banking information, call the Contact Center at 
        <a
          href="tel:(*************"
        >
          (833) 344‑7365
        </a>
        .
      </p>
    </div>
  </div>
</section>
`;

exports[`Status Payments renders the payment header, table and FAQ 2`] = `
<table
  class="usa-table usa-table--borderless usa-table--stacked"
>
  <thead>
    <tr>
      <th
        scope="col"
      >
        Pay period
      </th>
      <th
        scope="col"
      >
        Amount
      </th>
      <th
        scope="col"
      >
        Status
      </th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td
        class="tablet:width-card-lg"
        data-label="Pay period"
      >
        <var>
          11/8/2021
        </var>
         to 
        <var>
          12/15/2021
        </var>
      </td>
      <td
        data-label="Amount"
      >
        <var>
          $100.00
        </var>
      </td>
      <td
        data-label="Status"
      >
        <div
          title="table payment status"
        >
          Check was mailed on 
          <var>
            November 22, 2021
          </var>
          .
        </div>
      </td>
    </tr>
    <tr>
      <td
        class="tablet:width-card-lg"
        data-label="Pay period"
      >
        <var>
          11/8/2021
        </var>
         to 
        <var>
          12/15/2021
        </var>
      </td>
      <td
        data-label="Amount"
      >
        <var>
          $100.00
        </var>
      </td>
      <td
        data-label="Status"
      >
        <div
          title="table payment status"
        >
          Check was mailed on 
          <var>
            November 22, 2021
          </var>
          .
        </div>
      </td>
    </tr>
    <tr>
      <td
        data-label="Waiting period"
      >
        <div>
          <b>
            Unpaid Waiting Period
          </b>
        </div>
        <div>
          <var>
            1/1/2022
          </var>
           to 
          <var>
            1/7/2022
          </var>
        </div>
      </td>
      <td
        data-label="Amount"
        data-testid="waiting-period"
      >
        $0.00
      </td>
      <td>
        There is an unpaid 7-day waiting period at the start of this leave. Payments are not scheduled during this time. Job protection will be afforded. Learn more about the 
        <a
          href="https://www.mass.gov/getting-paid"
          rel="noopener noreferrer"
          target="_blank"
        >
          7-day waiting period
        </a>
        .
      </td>
    </tr>
  </tbody>
</table>
`;

exports[`Status renders the page for a claim without documents or a status 1`] = `
<div>
  <span
    class="display-block font-heading-2xs margin-bottom-2 text-base-dark text-bold"
  >
    <div
      title="employers claims review absence id label"
    >
      Application ID: 
      <var>
        NTN-111-ABS-01
      </var>
    </div>
  </span>
  <h1
    class="js-title margin-top-0 margin-bottom-2 font-heading-lg line-height-sans-2"
    tabindex="-1"
    title="employers claims status title"
  >
    Application status for 
    <var>
      Jane Doe
    </var>
  </h1>
  <p
    class="usa-intro"
  >
    No action is required of you. You can view this page at any time to understand how to supplement your employee’s PFML payments with paid time off (PTO), download notices, see decisions, or access leave details for this application. Your employee has the right to appeal decisions under Massachusetts regulations (
    <a
      href="https://mass.gov/pfml/regulations"
      rel="noopener noreferrer"
      target="_blank"
    >
      458 CMR 2.14
    </a>
    ).
  </p>
  <div
    class="display-flex flex-align-end margin-top-6 margin-bottom-3"
  >
    <h2
      class="flex-fill margin-right-1 font-heading-md text-bold"
    >
      Employee information
    </h2>
  </div>
  <div
    class="margin-bottom-2 padding-bottom-2 display-flex flex-justify border-bottom-2px border-base-lighter"
    data-testid="ReviewRow"
  >
    <div
      class="margin-right-2"
      data-testid="ReviewRowContainer"
    >
      <h3
        class="margin-bottom-1 font-heading-xs text-bold"
      >
        Employee name
      </h3>
      <var>
        Jane Doe
      </var>
    </div>
  </div>
  <div
    class="margin-bottom-2 padding-bottom-2 display-flex flex-justify border-bottom-2px border-base-lighter"
    data-testid="ReviewRow"
  >
    <div
      class="margin-right-2"
      data-testid="ReviewRowContainer"
    >
      <h3
        class="margin-bottom-1 font-heading-xs text-bold"
      >
        Organization
      </h3>
      <var>
        Work Inc.
      </var>
    </div>
  </div>
  <div
    class="margin-bottom-2 padding-bottom-2 display-flex flex-justify border-bottom-2px border-base-lighter"
    data-testid="ReviewRow"
  >
    <div
      class="margin-right-2"
      data-testid="ReviewRowContainer"
    >
      <h3
        class="margin-bottom-1 font-heading-xs text-bold"
      >
        Employer ID number (EIN)
      </h3>
      <var>
        12-3456789
      </var>
    </div>
  </div>
  <div
    class="margin-bottom-2 padding-bottom-2 display-flex flex-justify border-bottom-2px border-base-lighter"
    data-testid="ReviewRow"
  >
    <div
      class="margin-right-2"
      data-testid="ReviewRowContainer"
    >
      <h3
        class="margin-bottom-1 font-heading-xs text-bold"
      >
        Mailing address
      </h3>
      <span
        class="residential-address"
        data-testid="residential-address"
      >
        <var>
          1234 My St.
          <br />
          Boston
          , 
          MA
           
          00000
        </var>
      </span>
    </div>
  </div>
  <div
    class="margin-bottom-2 padding-bottom-2 display-flex flex-justify border-bottom-2px border-base-lighter"
    data-testid="ReviewRow"
  >
    <div
      class="margin-right-2"
      data-testid="ReviewRowContainer"
    >
      <h3
        class="margin-bottom-1 font-heading-xs text-bold"
      >
        Social Security Number or Individual Taxpayer Identification Number
      </h3>
      <var>
        ***-**-1234
      </var>
    </div>
  </div>
  <div
    class="margin-bottom-2 padding-bottom-2 display-flex flex-justify border-bottom-2px border-base-lighter"
    data-testid="ReviewRow"
  >
    <div
      class="margin-right-2"
      data-testid="ReviewRowContainer"
    >
      <h3
        class="margin-bottom-1 font-heading-xs text-bold"
      >
        Date of birth
      </h3>
      <var>
        7/17/****
      </var>
    </div>
  </div>
  <div
    class="display-flex flex-align-end margin-top-6 margin-bottom-3"
  >
    <h2
      class="flex-fill margin-right-1 font-heading-md text-bold"
    >
      Leave details
    </h2>
  </div>
  <div
    class="margin-top-6"
    data-testid="absence periods"
  >
    <h3
      class="font-heading-sm text-bold"
    >
      Leave to manage their serious health condition
    </h3>
    <table
      class="usa-table usa-table--borderless width-full usa-table--stacked"
    >
      <thead>
        <tr>
          <th
            scope="col"
          >
            Leave duration
          </th>
          <th
            scope="col"
          >
            Leave frequency
          </th>
          <th
            scope="col"
          >
            Status
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          data-testid="1-0"
        >
          <th
            class="tablet:width-card-lg"
            data-label="Leave duration"
            scope="row"
          >
            <var>
              5/1/2022
            </var>
             to 
            <var>
              7/1/2022
            </var>
          </th>
          <td
            class="tablet:width-card-lg"
            data-label="Leave frequency"
          >
            Reduced leave schedule
          </td>
          <td
            data-label="Status"
          >
            <span
              class="usa-tag display-inline-block text-bold text-middle text-center text-no-wrap text-base-darkest bg-warning-lighter"
            >
              Pending
            </span>
          </td>
        </tr>
        <tr
          data-testid="1-1"
        >
          <th
            class="tablet:width-card-lg"
            data-label="Leave duration"
            scope="row"
          >
            <var>
              1/1/2022
            </var>
             to 
            <var>
              4/1/2022
            </var>
          </th>
          <td
            class="tablet:width-card-lg"
            data-label="Leave frequency"
          >
            Continuous leave
          </td>
          <td
            data-label="Status"
          >
            <span
              class="usa-tag display-inline-block text-bold text-middle text-center text-no-wrap text-base-darkest bg-warning-lighter"
            >
              Pending
            </span>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <section
    class="margin-y-5"
    data-testid="your-payments"
  >
    <h2
      class="margin-bottom-3 font-heading-md text-bold"
    >
      Payments
    </h2>
    <section
      data-testid="your-payments-intro"
    >
      Payments are processed to be paid each week of leave. Check back weekly to see when the next payment will be processed.
    </section>
    <table
      class="usa-table usa-table--borderless usa-table--stacked"
    >
      <thead>
        <tr>
          <th
            scope="col"
          >
            Pay period
          </th>
          <th
            scope="col"
          >
            Amount
          </th>
          <th
            scope="col"
          >
            Status
          </th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td
            class="tablet:width-card-lg"
            data-label="Pay period"
          >
            <var>
              11/8/2021
            </var>
             to 
            <var>
              12/15/2021
            </var>
          </td>
          <td
            data-label="Amount"
          >
            <var>
              $100.00
            </var>
          </td>
          <td
            data-label="Status"
          >
            <div
              title="table payment status"
            >
              Check was mailed on 
              <var>
                November 22, 2021
              </var>
              .
            </div>
          </td>
        </tr>
        <tr>
          <td
            class="tablet:width-card-lg"
            data-label="Pay period"
          >
            <var>
              11/8/2021
            </var>
             to 
            <var>
              12/15/2021
            </var>
          </td>
          <td
            data-label="Amount"
          >
            <var>
              $100.00
            </var>
          </td>
          <td
            data-label="Status"
          >
            <div
              title="table payment status"
            >
              Check was mailed on 
              <var>
                November 22, 2021
              </var>
              .
            </div>
          </td>
        </tr>
        <tr>
          <td
            data-label="Waiting period"
          >
            <div>
              <b>
                Unpaid Waiting Period
              </b>
            </div>
            <div>
              <var>
                1/1/2022
              </var>
               to 
              <var>
                1/7/2022
              </var>
            </div>
          </td>
          <td
            data-label="Amount"
            data-testid="waiting-period"
          >
            $0.00
          </td>
          <td>
            There is an unpaid 7-day waiting period at the start of this leave. Payments are not scheduled during this time. Job protection will be afforded. Learn more about the 
            <a
              href="https://www.mass.gov/getting-paid"
              rel="noopener noreferrer"
              target="_blank"
            >
              7-day waiting period
            </a>
            .
          </td>
        </tr>
      </tbody>
    </table>
  </section>
  <h3>
    Employees can supplement PFML payments with Paid Time Off
  </h3>
  <br />
  Employees' PFML payments can be supplemented with employer Paid Time Off (PTO). This includes vacation days, sick days, and personal time, as long as the combined total benefit amount is not greater than the Individual Average Weekly Wage (IAWW). 
  <ul
    class="usa-list"
  >
    <li
      class="maxw-full"
    >
      The employer is responsible for providing any supplemental payments
    </li>
    <li
      class="maxw-full"
    >
      Employers cannot require employees to use PTO during PFML leave
    </li>
    <li
      class="maxw-full"
    >
      Employers can continue to use temporary disability or paid family and medical leave plans to supplement PFML payments.
    </li>
  </ul>
  Learn more about 
  <a
    href="https://www.mass.gov/info-details/how-other-leave-and-benefits-can-affect-your-paid-family-and-medical-leave#paid-time-off-through-your-employer-"
    rel="noopener noreferrer"
    target="_blank"
  >
    using Paid Time Off during PFML leave.
  </a>
  <br />
  <br />
  <div
    class="text-bold"
  >
    How to calculate the maximum supplemental payment amount
  </div>
  <br />
  Individual Average Weekly Wage - Weekly Benefit Amount = Maximum Supplemental Payment amount. For example: 
  <ul
    class="usa-list"
  >
    <li
      class="maxw-full"
    >
      Mary has to take PFML leave for 12 weeks. Their Individual Average Weekly Wage is $1,300, and they receive a weekly benefit amount of $1,100 from PFML.
    </li>
    <li
      class="maxw-full"
    >
      Mary’s employer can provide $200 of PTO ($1,300 - $1,100.)
    </li>
  </ul>
  To see the employee's weekly benefit amount and Individual Average Weekly Wage, view their most recent approval or change notice.
  <section
    class="margin-y-5"
    data-testid="payment-information"
  >
    <h2
      class="margin-bottom-3 font-heading-md text-bold"
    >
      Payment information
    </h2>
    <div
      class="usa-accordion usa-accordion--bordered"
    >
      <div
        id="delays_accordion"
      >
        <h2
          class="usa-accordion__heading margin-y-2"
        >
          <button
            aria-controls="accordion«rc»"
            aria-expanded="false"
            class="usa-accordion__button"
            type="button"
          >
            What may cause a delayed or cancelled payment
          </button>
        </h2>
        <div
          class="usa-accordion__content usa-prose"
          data-testid="accordion«rc»"
          hidden=""
          id="accordion«rc»"
        >
          <strong>
            What may cause a delayed payment
          </strong>
          <p>
            The exact day a payment is issued can be delayed because of:
          </p>
          <ul>
            <li>
              State and federal holidays
            </li>
            <li>
              Pay periods that end on weekends
            </li>
            <li>
              Bank processes
            </li>
            <li>
              Changes to the leave start and end dates
            </li>
            <li>
              Changes to the benefit amount due to additional income or benefits reported
            </li>
            <li>
              Other processing issues such as issues with bank account information or mailing addresses
            </li>
          </ul>
          <p>
            <strong>
              What may cause a cancelled payment
            </strong>
          </p>
          <p>
            Payments can be cancelled because of:
          </p>
          <ul>
            <li>
              Changes to the leave start and end dates
            </li>
            <li>
              Changes to the benefit amount due to additional income or benefits reported
            </li>
            <li>
              Hours worked outside of the schedule set up with the employer or during reported intermittent hours
            </li>
          </ul>
        </div>
      </div>
      <h2
        class="usa-accordion__heading margin-y-2"
      >
        <button
          aria-controls="accordion«rd»"
          aria-expanded="false"
          class="usa-accordion__button"
          type="button"
        >
          Why a payment may be less than the weekly benefit amount
        </button>
      </h2>
      <div
        class="usa-accordion__content usa-prose"
        data-testid="accordion«rd»"
        hidden=""
        id="accordion«rd»"
      >
        <p>
          To see the weekly benefit amount, view the most recent 
          <a
            href="#view_notices"
          >
            approval or change notice
          </a>
          .
        </p>
        <p>
          The payment amount seen on this page may be less than the weekly benefit amount due to 
          <a
            href="https://mass.gov/pfml-benefit-reductions"
            rel="noopener noreferrer"
            target="_blank"
          >
            other leave, income and benefits
          </a>
           reported. Another notice will be processed if we reduce the benefit amount for any reason other than tax withholding. Please note that tax withholding status cannot be changed at this point, since the application has been approved.
        </p>
        <p>
          <strong>
            Other scenarios that may change the received payment amount:
          </strong>
        </p>
        <ul>
          <li>
            Other leave, income, and benefits reported
          </li>
          <li>
            The maximum total amount of receivable PFML benefits has been reached across multiple applications
          </li>
          <li>
            A payment for a pay period less than a full week was processed and received
          </li>
          <li>
            Elected to have taxes withheld
          </li>
          <li>
            Received an overpayment
          </li>
        </ul>
      </div>
      <h2
        class="usa-accordion__heading margin-y-2"
      >
        <button
          aria-controls="accordion«rf»"
          aria-expanded="false"
          class="usa-accordion__button"
          type="button"
        >
          Weekly benefit amount
        </button>
      </h2>
      <div
        class="usa-accordion__content usa-prose"
        data-testid="accordion«rf»"
        hidden=""
        id="accordion«rf»"
      >
        To see the weekly benefit amount, view the most recent 
        <a
          href="#view_notices"
        >
          approval or change notice.
        </a>
        <p>
          A new notice will be sent if the benefit amount is changed for any reason other than tax withholding. Learn more about 
          <a
            href="https://www.mass.gov/info-details/how-pfml-weekly-benefit-amounts-are-calculated-andor-changed"
            rel="noreferrer"
            target="_blank"
          >
            how PFML weekly benefit amounts are calculated and/or changed
          </a>
        </p>
      </div>
      <h2
        class="usa-accordion__heading margin-y-2"
      >
        <button
          aria-controls="accordion«rg»"
          aria-expanded="false"
          class="usa-accordion__button"
          type="button"
        >
          How to appeal a reduced or cancelled payment
        </button>
      </h2>
      <div
        class="usa-accordion__content usa-prose"
        data-testid="accordion«rg»"
        hidden=""
        id="accordion«rg»"
      >
        If it appears that we’ve reduced or cancelled payments for an incorrect reason, 
        <a
          href="https://mass.gov/PaidLeaveDecisionAppeal"
          rel="noreferrer noopener"
          target="_blank"
        >
          the DFML decision can be appealed.
        </a>
      </div>
      <h2
        class="usa-accordion__heading margin-y-2"
      >
        <button
          aria-controls="accordion«rh»"
          aria-expanded="false"
          class="usa-accordion__button"
          type="button"
        >
          Change payment preferences
        </button>
      </h2>
      <div
        class="usa-accordion__content usa-prose"
        data-testid="accordion«rh»"
        hidden=""
        id="accordion«rh»"
      >
        <p>
          To make changes to the payment method or banking information, call the Contact Center at 
          <a
            href="tel:(*************"
          >
            (833) 344‑7365
          </a>
          .
        </p>
      </div>
    </div>
  </section>
</div>
`;

exports[`Status renders the page for a completed claim 1`] = `
<div>
  <span
    class="display-block font-heading-2xs margin-bottom-2 text-base-dark text-bold"
  >
    <div
      title="employers claims review absence id label"
    >
      Application ID: 
      <var>
        NTN-111-ABS-01
      </var>
    </div>
  </span>
  <h1
    class="js-title margin-top-0 margin-bottom-2 font-heading-lg line-height-sans-2"
    tabindex="-1"
    title="employers claims status title"
  >
    Application status for 
    <var>
      Jane Doe
    </var>
  </h1>
  <p
    class="usa-intro"
  >
    No action is required of you. You can view this page at any time to understand how to supplement your employee’s PFML payments with paid time off (PTO), download notices, see decisions, or access leave details for this application. Your employee has the right to appeal decisions under Massachusetts regulations (
    <a
      href="https://mass.gov/pfml/regulations"
      rel="noopener noreferrer"
      target="_blank"
    >
      458 CMR 2.14
    </a>
    ).
  </p>
  <div
    class="display-flex flex-align-end margin-top-6 margin-bottom-3"
  >
    <h2
      class="flex-fill margin-right-1 font-heading-md text-bold"
    >
      Employee information
    </h2>
  </div>
  <div
    class="margin-bottom-2 padding-bottom-2 display-flex flex-justify border-bottom-2px border-base-lighter"
    data-testid="ReviewRow"
  >
    <div
      class="margin-right-2"
      data-testid="ReviewRowContainer"
    >
      <h3
        class="margin-bottom-1 font-heading-xs text-bold"
      >
        Employee name
      </h3>
      <var>
        Jane Doe
      </var>
    </div>
  </div>
  <div
    class="margin-bottom-2 padding-bottom-2 display-flex flex-justify border-bottom-2px border-base-lighter"
    data-testid="ReviewRow"
  >
    <div
      class="margin-right-2"
      data-testid="ReviewRowContainer"
    >
      <h3
        class="margin-bottom-1 font-heading-xs text-bold"
      >
        Organization
      </h3>
      <var>
        Work Inc.
      </var>
    </div>
  </div>
  <div
    class="margin-bottom-2 padding-bottom-2 display-flex flex-justify border-bottom-2px border-base-lighter"
    data-testid="ReviewRow"
  >
    <div
      class="margin-right-2"
      data-testid="ReviewRowContainer"
    >
      <h3
        class="margin-bottom-1 font-heading-xs text-bold"
      >
        Employer ID number (EIN)
      </h3>
      <var>
        12-3456789
      </var>
    </div>
  </div>
  <div
    class="margin-bottom-2 padding-bottom-2 display-flex flex-justify border-bottom-2px border-base-lighter"
    data-testid="ReviewRow"
  >
    <div
      class="margin-right-2"
      data-testid="ReviewRowContainer"
    >
      <h3
        class="margin-bottom-1 font-heading-xs text-bold"
      >
        Mailing address
      </h3>
      <span
        class="residential-address"
        data-testid="residential-address"
      >
        <var>
          1234 My St.
          <br />
          Boston
          , 
          MA
           
          00000
        </var>
      </span>
    </div>
  </div>
  <div
    class="margin-bottom-2 padding-bottom-2 display-flex flex-justify border-bottom-2px border-base-lighter"
    data-testid="ReviewRow"
  >
    <div
      class="margin-right-2"
      data-testid="ReviewRowContainer"
    >
      <h3
        class="margin-bottom-1 font-heading-xs text-bold"
      >
        Social Security Number or Individual Taxpayer Identification Number
      </h3>
      <var>
        ***-**-1234
      </var>
    </div>
  </div>
  <div
    class="margin-bottom-2 padding-bottom-2 display-flex flex-justify border-bottom-2px border-base-lighter"
    data-testid="ReviewRow"
  >
    <div
      class="margin-right-2"
      data-testid="ReviewRowContainer"
    >
      <h3
        class="margin-bottom-1 font-heading-xs text-bold"
      >
        Date of birth
      </h3>
      <var>
        7/17/****
      </var>
    </div>
  </div>
  <div
    class="display-flex flex-align-end margin-top-6 margin-bottom-3"
  >
    <h2
      class="flex-fill margin-right-1 font-heading-md text-bold"
    >
      Leave details
    </h2>
  </div>
  <div
    class="margin-top-6"
    data-testid="absence periods"
  >
    <h3
      class="font-heading-sm text-bold"
    >
      Leave to manage their serious health condition
    </h3>
    <table
      class="usa-table usa-table--borderless width-full usa-table--stacked"
    >
      <thead>
        <tr>
          <th
            scope="col"
          >
            Leave duration
          </th>
          <th
            scope="col"
          >
            Leave frequency
          </th>
          <th
            scope="col"
          >
            Status
          </th>
        </tr>
      </thead>
      <tbody>
        <tr
          data-testid="1-0"
        >
          <th
            class="tablet:width-card-lg"
            data-label="Leave duration"
            scope="row"
          >
            <var>
              5/1/2022
            </var>
             to 
            <var>
              7/1/2022
            </var>
          </th>
          <td
            class="tablet:width-card-lg"
            data-label="Leave frequency"
          >
            Reduced leave schedule
          </td>
          <td
            data-label="Status"
          >
            <span
              class="usa-tag display-inline-block text-bold text-middle text-center text-no-wrap text-base-darkest bg-warning-lighter"
            >
              Pending
            </span>
          </td>
        </tr>
        <tr
          data-testid="1-1"
        >
          <th
            class="tablet:width-card-lg"
            data-label="Leave duration"
            scope="row"
          >
            <var>
              1/1/2022
            </var>
             to 
            <var>
              4/1/2022
            </var>
          </th>
          <td
            class="tablet:width-card-lg"
            data-label="Leave frequency"
          >
            Continuous leave
          </td>
          <td
            data-label="Status"
          >
            <span
              class="usa-tag display-inline-block text-bold text-middle text-center text-no-wrap text-base-darkest bg-warning-lighter"
            >
              Pending
            </span>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <div
    class="padding-top-2"
  >
    <h2
      class="font-heading-md text-bold"
      id="view_notices"
    >
      Notices
    </h2>
    <p>
      The notice(s) below may be in your employee’s preferred language. Scroll to the bottom of the document to view the English translation of the notice.
    </p>
    <ul
      class="usa-list usa-list--unstyled margin-top-2"
      data-testid="documents"
    >
      <li
        class="margin-bottom-2"
      >
        <div
          class="usa-prose"
        >
          <button
            class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
            type="button"
          >
            Approval notice (PDF)
          </button>
          <div
            class="text-base-dark text-normal"
            title="downloadable document created date"
          >
            Posted 
            <var>
              1/2/2021
            </var>
          </div>
        </div>
      </li>
    </ul>
  </div>
  <section
    class="margin-y-5"
    data-testid="your-payments"
  >
    <h2
      class="margin-bottom-3 font-heading-md text-bold"
    >
      Payments
    </h2>
    <section
      data-testid="your-payments-intro"
    >
      Payments are processed to be paid each week of leave. Check back weekly to see when the next payment will be processed.
    </section>
    <table
      class="usa-table usa-table--borderless usa-table--stacked"
    >
      <thead>
        <tr>
          <th
            scope="col"
          >
            Pay period
          </th>
          <th
            scope="col"
          >
            Amount
          </th>
          <th
            scope="col"
          >
            Status
          </th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td
            class="tablet:width-card-lg"
            data-label="Pay period"
          >
            <var>
              11/8/2021
            </var>
             to 
            <var>
              12/15/2021
            </var>
          </td>
          <td
            data-label="Amount"
          >
            <var>
              $100.00
            </var>
          </td>
          <td
            data-label="Status"
          >
            <div
              title="table payment status"
            >
              Check was mailed on 
              <var>
                November 22, 2021
              </var>
              .
            </div>
          </td>
        </tr>
        <tr>
          <td
            class="tablet:width-card-lg"
            data-label="Pay period"
          >
            <var>
              11/8/2021
            </var>
             to 
            <var>
              12/15/2021
            </var>
          </td>
          <td
            data-label="Amount"
          >
            <var>
              $100.00
            </var>
          </td>
          <td
            data-label="Status"
          >
            <div
              title="table payment status"
            >
              Check was mailed on 
              <var>
                November 22, 2021
              </var>
              .
            </div>
          </td>
        </tr>
        <tr>
          <td
            data-label="Waiting period"
          >
            <div>
              <b>
                Unpaid Waiting Period
              </b>
            </div>
            <div>
              <var>
                1/1/2022
              </var>
               to 
              <var>
                1/7/2022
              </var>
            </div>
          </td>
          <td
            data-label="Amount"
            data-testid="waiting-period"
          >
            $0.00
          </td>
          <td>
            There is an unpaid 7-day waiting period at the start of this leave. Payments are not scheduled during this time. Job protection will be afforded. Learn more about the 
            <a
              href="https://www.mass.gov/getting-paid"
              rel="noopener noreferrer"
              target="_blank"
            >
              7-day waiting period
            </a>
            .
          </td>
        </tr>
      </tbody>
    </table>
  </section>
  <h3>
    Employees can supplement PFML payments with Paid Time Off
  </h3>
  <br />
  Employees' PFML payments can be supplemented with employer Paid Time Off (PTO). This includes vacation days, sick days, and personal time, as long as the combined total benefit amount is not greater than the Individual Average Weekly Wage (IAWW). 
  <ul
    class="usa-list"
  >
    <li
      class="maxw-full"
    >
      The employer is responsible for providing any supplemental payments
    </li>
    <li
      class="maxw-full"
    >
      Employers cannot require employees to use PTO during PFML leave
    </li>
    <li
      class="maxw-full"
    >
      Employers can continue to use temporary disability or paid family and medical leave plans to supplement PFML payments.
    </li>
  </ul>
  Learn more about 
  <a
    href="https://www.mass.gov/info-details/how-other-leave-and-benefits-can-affect-your-paid-family-and-medical-leave#paid-time-off-through-your-employer-"
    rel="noopener noreferrer"
    target="_blank"
  >
    using Paid Time Off during PFML leave.
  </a>
  <br />
  <br />
  <div
    class="text-bold"
  >
    How to calculate the maximum supplemental payment amount
  </div>
  <br />
  Individual Average Weekly Wage - Weekly Benefit Amount = Maximum Supplemental Payment amount. For example: 
  <ul
    class="usa-list"
  >
    <li
      class="maxw-full"
    >
      Mary has to take PFML leave for 12 weeks. Their Individual Average Weekly Wage is $1,300, and they receive a weekly benefit amount of $1,100 from PFML.
    </li>
    <li
      class="maxw-full"
    >
      Mary’s employer can provide $200 of PTO ($1,300 - $1,100.)
    </li>
  </ul>
  To see the employee's weekly benefit amount and Individual Average Weekly Wage, view their most recent approval or change notice.
  <section
    class="margin-y-5"
    data-testid="payment-information"
  >
    <h2
      class="margin-bottom-3 font-heading-md text-bold"
    >
      Payment information
    </h2>
    <div
      class="usa-accordion usa-accordion--bordered"
    >
      <div
        id="delays_accordion"
      >
        <h2
          class="usa-accordion__heading margin-y-2"
        >
          <button
            aria-controls="accordion«r3»"
            aria-expanded="false"
            class="usa-accordion__button"
            type="button"
          >
            What may cause a delayed or cancelled payment
          </button>
        </h2>
        <div
          class="usa-accordion__content usa-prose"
          data-testid="accordion«r3»"
          hidden=""
          id="accordion«r3»"
        >
          <strong>
            What may cause a delayed payment
          </strong>
          <p>
            The exact day a payment is issued can be delayed because of:
          </p>
          <ul>
            <li>
              State and federal holidays
            </li>
            <li>
              Pay periods that end on weekends
            </li>
            <li>
              Bank processes
            </li>
            <li>
              Changes to the leave start and end dates
            </li>
            <li>
              Changes to the benefit amount due to additional income or benefits reported
            </li>
            <li>
              Other processing issues such as issues with bank account information or mailing addresses
            </li>
          </ul>
          <p>
            <strong>
              What may cause a cancelled payment
            </strong>
          </p>
          <p>
            Payments can be cancelled because of:
          </p>
          <ul>
            <li>
              Changes to the leave start and end dates
            </li>
            <li>
              Changes to the benefit amount due to additional income or benefits reported
            </li>
            <li>
              Hours worked outside of the schedule set up with the employer or during reported intermittent hours
            </li>
          </ul>
        </div>
      </div>
      <h2
        class="usa-accordion__heading margin-y-2"
      >
        <button
          aria-controls="accordion«r4»"
          aria-expanded="false"
          class="usa-accordion__button"
          type="button"
        >
          Why a payment may be less than the weekly benefit amount
        </button>
      </h2>
      <div
        class="usa-accordion__content usa-prose"
        data-testid="accordion«r4»"
        hidden=""
        id="accordion«r4»"
      >
        <p>
          To see the weekly benefit amount, view the most recent 
          <a
            href="#view_notices"
          >
            approval or change notice
          </a>
          .
        </p>
        <p>
          The payment amount seen on this page may be less than the weekly benefit amount due to 
          <a
            href="https://mass.gov/pfml-benefit-reductions"
            rel="noopener noreferrer"
            target="_blank"
          >
            other leave, income and benefits
          </a>
           reported. Another notice will be processed if we reduce the benefit amount for any reason other than tax withholding. Please note that tax withholding status cannot be changed at this point, since the application has been approved.
        </p>
        <p>
          <strong>
            Other scenarios that may change the received payment amount:
          </strong>
        </p>
        <ul>
          <li>
            Other leave, income, and benefits reported
          </li>
          <li>
            The maximum total amount of receivable PFML benefits has been reached across multiple applications
          </li>
          <li>
            A payment for a pay period less than a full week was processed and received
          </li>
          <li>
            Elected to have taxes withheld
          </li>
          <li>
            Received an overpayment
          </li>
        </ul>
      </div>
      <h2
        class="usa-accordion__heading margin-y-2"
      >
        <button
          aria-controls="accordion«r6»"
          aria-expanded="false"
          class="usa-accordion__button"
          type="button"
        >
          Weekly benefit amount
        </button>
      </h2>
      <div
        class="usa-accordion__content usa-prose"
        data-testid="accordion«r6»"
        hidden=""
        id="accordion«r6»"
      >
        To see the weekly benefit amount, view the most recent 
        <a
          href="#view_notices"
        >
          approval or change notice.
        </a>
        <p>
          A new notice will be sent if the benefit amount is changed for any reason other than tax withholding. Learn more about 
          <a
            href="https://www.mass.gov/info-details/how-pfml-weekly-benefit-amounts-are-calculated-andor-changed"
            rel="noreferrer"
            target="_blank"
          >
            how PFML weekly benefit amounts are calculated and/or changed
          </a>
        </p>
      </div>
      <h2
        class="usa-accordion__heading margin-y-2"
      >
        <button
          aria-controls="accordion«r7»"
          aria-expanded="false"
          class="usa-accordion__button"
          type="button"
        >
          How to appeal a reduced or cancelled payment
        </button>
      </h2>
      <div
        class="usa-accordion__content usa-prose"
        data-testid="accordion«r7»"
        hidden=""
        id="accordion«r7»"
      >
        If it appears that we’ve reduced or cancelled payments for an incorrect reason, 
        <a
          href="https://mass.gov/PaidLeaveDecisionAppeal"
          rel="noreferrer noopener"
          target="_blank"
        >
          the DFML decision can be appealed.
        </a>
      </div>
      <h2
        class="usa-accordion__heading margin-y-2"
      >
        <button
          aria-controls="accordion«r8»"
          aria-expanded="false"
          class="usa-accordion__button"
          type="button"
        >
          Change payment preferences
        </button>
      </h2>
      <div
        class="usa-accordion__content usa-prose"
        data-testid="accordion«r8»"
        hidden=""
        id="accordion«r8»"
      >
        <p>
          To make changes to the payment method or banking information, call the Contact Center at 
          <a
            href="tel:(*************"
          >
            (833) 344‑7365
          </a>
          .
        </p>
      </div>
    </div>
  </section>
</div>
`;

exports[`Status shows only legal documents 1`] = `
<ul
  class="usa-list usa-list--unstyled margin-top-2"
  data-testid="documents"
>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Appeal Acknowledgment (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          1/13/2021
        </var>
      </div>
    </div>
  </li>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Appeal Approved (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          1/14/2021
        </var>
      </div>
    </div>
  </li>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Appeal Dismissed - Exempt Employer (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          1/15/2021
        </var>
      </div>
    </div>
  </li>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Appeal Dismissed - Other (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          1/16/2021
        </var>
      </div>
    </div>
  </li>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Appeal Hearing Virtual Fillable (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          1/17/2021
        </var>
      </div>
    </div>
  </li>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Modify Decision (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          1/18/2021
        </var>
      </div>
    </div>
  </li>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Appeal RFI (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          1/22/2021
        </var>
      </div>
    </div>
  </li>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Appeal - Returned To Adjudication (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          1/25/2021
        </var>
      </div>
    </div>
  </li>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Appeal Withdrawn (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          1/26/2021
        </var>
      </div>
    </div>
  </li>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Approval notice (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          1/28/2021
        </var>
      </div>
    </div>
  </li>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Approval of Application Change (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          1/30/2021
        </var>
      </div>
    </div>
  </li>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Approved Leave Dates Cancelled (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          1/31/2021
        </var>
      </div>
    </div>
  </li>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Approved Time Cancelled (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          2/1/2021
        </var>
      </div>
    </div>
  </li>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Benefit Amount Change Notice (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          2/2/2021
        </var>
      </div>
    </div>
  </li>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Change Request Approved (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          2/3/2021
        </var>
      </div>
    </div>
  </li>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Change Request Denied (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          2/4/2021
        </var>
      </div>
    </div>
  </li>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Denial Notice (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          2/6/2021
        </var>
      </div>
    </div>
  </li>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Denial of Application (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          2/8/2021
        </var>
      </div>
    </div>
  </li>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Denial of Application Change (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          2/9/2021
        </var>
      </div>
    </div>
  </li>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Intermittent Time Approved Notice (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          2/14/2021
        </var>
      </div>
    </div>
  </li>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Intermittent Time Reported (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          2/15/2021
        </var>
      </div>
    </div>
  </li>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Leave Allotment Change Notice (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          2/16/2021
        </var>
      </div>
    </div>
  </li>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Maximum Weekly Benefit Change Notice (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          2/17/2021
        </var>
      </div>
    </div>
  </li>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Request for More Information (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          3/19/2021
        </var>
      </div>
    </div>
  </li>
  <li
    class="margin-bottom-2"
  >
    <div
      class="usa-prose"
    >
      <button
        class="usa-button position-relative text-bold margin-top-0 usa-button--unstyled"
        type="button"
      >
        Pending Application Withdrawn (PDF)
      </button>
      <div
        class="text-base-dark text-normal"
        title="downloadable document created date"
      >
        Posted 
        <var>
          3/25/2021
        </var>
      </div>
    </div>
  </li>
</ul>
`;
