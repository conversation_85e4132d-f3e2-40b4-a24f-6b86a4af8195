import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  assertI<PERSON><PERSON><PERSON><PERSON><PERSON>,
  assertItemsShown,
  assertRadioQuestionWithOptions,
  clickSaveAndContinueButton,
  genericTestDescriptions,
  getComboBox,
  getRadioButton,
  selectRadioButton,
  selectRadioOptionAtRandom,
  setEnableEmployerExemptionsPortal,
  verifiedAdministrator,
  waitForDataToLoad,
} from "tests/pages/employers/employer-exemptions/employer-exemptions-test-utils";
/* eslint-disable jest/expect-expect */
import { InsurancePlan, InsuranceProvider } from "src/models/InsuranceProvider";
import User, { UserLeaveAdministrator } from "src/models/User";

import ApiResourceCollection from "src/models/ApiResourceCollection";
import EmployerExemptionsApplication from "src/models/EmployerExemptionsApplication";
import EmployerExemptionsInsuranceDetails from "src/pages/employers/employer-exemptions/insurance-details";
import InsuranceProvidersApi from "src/api/InsuranceProvidersApi";
import { ValidationError } from "src/errors";
import { createMockEmployerExemptionsApplication } from "lib/mock-helpers/createMockEmployerExemptionsApplication";
import { renderPage } from "tests/test-utils";
import routes from "src/routes";
import userEvent from "@testing-library/user-event";
import { waitFor } from "@testing-library/react";

jest.mock("src/api/EmployerExemptionsApi");
jest.mock("src/api/InsuranceProvidersApi");
const insuranceProvidersApi = new InsuranceProvidersApi();

// TODO (PFMLPB-23216): Genericize this boilerplate and pull parts into a util file
const setup = async (params: {
  purchasedPlanInsuranceProviderId?: number | undefined;
  props?: { [key: string]: unknown };
  errors?: ValidationError[];
}) => {
  const employerExemptionApplication: EmployerExemptionsApplication =
    createMockEmployerExemptionsApplication();
  employerExemptionApplication.employer_id = verifiedAdministrator.employer_id;
  let insuranceProviders: InsuranceProvider[] = [];
  let insurancePlans: InsurancePlan[] = [];

  if (
    params.purchasedPlanInsuranceProviderId !== undefined &&
    params.purchasedPlanInsuranceProviderId > 0
  ) {
    insuranceProviders = await insuranceProvidersApi.getInsuranceProviders();
    insurancePlans = await insuranceProvidersApi.getInsuranceProviderPlans(
      params.purchasedPlanInsuranceProviderId
    );
  }

  const utils = renderPage(
    EmployerExemptionsInsuranceDetails,
    {
      pathname: routes.employers.employerExemptions.insuranceDetails,
      addCustomSetup: (appLogic) => {
        appLogic.users.user = new User({
          consented_to_data_sharing: true,
          user_leave_administrators: [verifiedAdministrator],
        });
        appLogic.leaveAdmins.isLoadingLeaveAdmins = false;
        appLogic.leaveAdmins.leaveAdmins =
          new ApiResourceCollection<UserLeaveAdministrator>("id", [
            verifiedAdministrator,
          ]);
        appLogic.employerExemptionsApplication.employerExemptionsApplication =
          new ApiResourceCollection<EmployerExemptionsApplication>(
            "employer_exemption_application_id",
            [employerExemptionApplication]
          );
        appLogic.insuranceProviders.insuranceProviders = insuranceProviders;
        appLogic.insuranceProviders.insuranceProviderPlans = insurancePlans;
        appLogic.errors = params.errors ?? [];
      },
    },
    {
      query: {
        employer_exemption_application_id:
          employerExemptionApplication.employer_exemption_application_id,
      },
      ...params.props,
    }
  );

  await waitForDataToLoad();
  return utils;
};

const setupPageWithDefaults = async () => {
  return (await setup({})).container;
};

const testErrorsForPlanType = async (planType: RegExp, errors: ErrorList) => {
  const { container } = await setup({
    errors: errors.map((e) => {
      return new ValidationError([
        {
          field: e.field,
          message: `.${e.field} is required`,
          type: "required",
          namespace: "employer_exemptions",
        },
      ]);
    }),
  });

  // Click into the correct plan type then brute-force submit
  await userEvent.click(getRadioButton(planType));
  await selectRadioOptionAtRandom(leaveTypeOptions);
  await clickSaveAndContinueButton();

  await waitFor(() => {
    errors.forEach((v) => expect(container.innerHTML).toContain(v.message));
  });
};

const RadioButtonName = Object.freeze({
  PurchasedPrivatePlan: /Purchased Private Plan/i,
  SelfInsuredPlan: /Self-Insured Private Plan/i,
  DualLeave: /Family and medical/,
  FamilyOnlyLeave: /Family only/,
  MedicalOnlyLeave: /Medical only/,
});

const ComboboxName = Object.freeze({
  PrivatePlanProvider: /Private Plan Provider/i,
  PrivatePlanNumber: /Private Plan Number/i,
});

const selectPlanType = (planType: RegExp) => {
  return userEvent.click(getRadioButton(planType));
};

const selectInsuranceProvider = (insuranceProviderId: string) => {
  return userEvent.selectOptions(
    getComboBox(ComboboxName.PrivatePlanProvider),
    insuranceProviderId
  );
};

const pageName = "EmployerExemptionsInsuranceDetails";

const sectionLabel = "Tell us about your private plan";
const sectionLabelHint =
  "There are two types of private paid leave plans, purchased private paid leave plans and self-insured private paid leave plans. To learn more about minimum requirements per plan type, visit benefit plan requirements for a more detailed overview regarding each plan.";

const planTypeQuestion = "Select your insurance plan";
const planTypeOptions = [
  RadioButtonName.PurchasedPrivatePlan,
  RadioButtonName.SelfInsuredPlan,
];
const leaveTypeQuestion = "Choose your paid leave plan type";
const leaveTypeOptions = [
  RadioButtonName.DualLeave,
  RadioButtonName.FamilyOnlyLeave,
  RadioButtonName.MedicalOnlyLeave,
];

const purchasedPlanContent = [
  ComboboxName.PrivatePlanProvider,
  ComboboxName.PrivatePlanNumber,
  "Enter details about your insurance provider",
  "Enter your private plan number",
  "This number can be found on your completed Massachusetts Paid Family and Medical Leave Confirmation of Insurance form or on the bottom left hand corner of the first page of your new policy. Incorrect information may delay your request.",
  "Enter private plan coverage dates",
  "Provide the start date for your insurance coverage.",
  "Date coverage begins",
];

const selfInsuredContent = [
  "If your private plan is in the form of self-insurance, you must furnish a bond running to the commonwealth with a surety company authorized to transact business in the commonwealth. For information about bond calculation and remittance, please visit Bond Requirements for Approved Self-Insured Plans.",
  "Have you obtained a bond per the self-insurance requirements",
  "Enter surety company",
  "Enter surety bond effective date",
  "Enter bond amount",
  "Enter policy anniversary date",
];

const matchingFieldsTestName =
  "the page displays only those fields matching the plan type";
const matchingErrorsTestName =
  "the page displays error messages matching the plan type";

describe(pageName, () => {
  describe(genericTestDescriptions.featureFlagOff, () => {
    it(genericTestDescriptions.testCase404, async () => {
      setEnableEmployerExemptionsPortal(false);
      const container = await setupPageWithDefaults();
      expect(container).toHaveTextContent(genericTestDescriptions.notFoundText);
    });

    it(genericTestDescriptions.matchesSnapshot, async () => {
      const container = await setupPageWithDefaults();
      expect(container).toMatchSnapshot();
    });
  });

  describe(genericTestDescriptions.featureFlagOn, () => {
    beforeEach(() => {
      setEnableEmployerExemptionsPortal(true);
    });

    it("the page displays the section label + hint", async () => {
      const container = await setupPageWithDefaults();
      assertItemsShown(container, [sectionLabel, sectionLabelHint]);
    });

    it("the page initially hides all questions/fields except the plan type", async () => {
      const container = await setupPageWithDefaults();
      assertItemsHidden(container, [
        leaveTypeQuestion,
        ...leaveTypeOptions,
        ...selfInsuredContent,
        ...purchasedPlanContent,
      ]);
      assertRadioQuestionWithOptions(planTypeQuestion, planTypeOptions);
    });

    it("the page updates to show the leave type question after a plan type is selected", async () => {
      const container = await setupPageWithDefaults();
      assertItemsHidden(container, [leaveTypeQuestion, ...leaveTypeOptions]);
      await selectRadioOptionAtRandom(planTypeOptions);
      assertRadioQuestionWithOptions(leaveTypeQuestion, leaveTypeOptions);
    });

    describe("and puchased private plan is selected", () => {
      it(matchingFieldsTestName, async () => {
        const container = await setupPageWithDefaults();
        await selectPlanType(RadioButtonName.PurchasedPrivatePlan);
        await selectRadioOptionAtRandom(leaveTypeOptions);
        assertItemsHidden(container, selfInsuredContent);
        assertItemsShown(container, purchasedPlanContent);
      });

      it(matchingErrorsTestName, async () => {
        await testErrorsForPlanType(RadioButtonName.PurchasedPrivatePlan, [
          {
            field: "purchased_plan.insurance_plan_id",
            message: "Select an insurance plan number.",
          },
          {
            field: "purchased_plan.insurance_provider_id",
            message: "Select an insurance provider.",
          },
        ]);
      });

      describe("the page displays the correct insurance plans for each provider", () => {
        const providers = [
          {
            id: 1,
            name: "American Fidelity Assurance Company",
            expected_plan_count: 2,
          },
          {
            id: 2,
            name: "Anthem Life Insurance Company",
            expected_plan_count: 2,
          },
        ];

        for (let i = 0; i < providers.length; i++) {
          const provider = providers[i];
          it(`provider: ${provider.name} (id = ${provider.id}, # of plans = ${provider.expected_plan_count})`, async () => {
            await setup({
              purchasedPlanInsuranceProviderId: provider.id,
            });
            await selectPlanType(RadioButtonName.PurchasedPrivatePlan);
            await selectRadioButton(leaveTypeOptions[0]);
            await selectInsuranceProvider(provider.id.toString());

            const insuranceProviderDropdown = getComboBox(
              ComboboxName.PrivatePlanProvider
            );
            const insurancePlanDropdown = getComboBox(
              ComboboxName.PrivatePlanNumber
            );

            expect(insuranceProviderDropdown).toHaveValue(
              provider.id.toString()
            );

            // add 1 for -- select -- option
            expect(insurancePlanDropdown).toHaveLength(
              provider.expected_plan_count + 1
            );
          });
        }
      });
    });

    describe("and self-insured plan is selected", () => {
      it(matchingFieldsTestName, async () => {
        const container = await setupPageWithDefaults();
        await selectPlanType(RadioButtonName.SelfInsuredPlan);
        await selectRadioOptionAtRandom(leaveTypeOptions);
        assertItemsShown(container, selfInsuredContent);
        assertItemsHidden(container, purchasedPlanContent);
      });

      it(matchingErrorsTestName, async () => {
        await testErrorsForPlanType(RadioButtonName.SelfInsuredPlan, [
          {
            field: "self_insured.has_obtained_surety_bond",
            message:
              "Have you obtained a bond per the self-insurance requirements?",
          },
          {
            field: "self_insured.surety_company",
            message: "Provide a surety company.",
          },
          {
            field: "insurance_plan_effective_at",
            message: "Provide the start date for your insurance coverage.",
          },
          {
            field: "insurance_plan_expires_at",
            message:
              "Provide the anniversary date for your insurance coverage.",
          },
          {
            field: "self_insured.surety_bond_amount",
            message: "Provide a bond amount.",
          },
        ]);
      });
    });
  });
});
