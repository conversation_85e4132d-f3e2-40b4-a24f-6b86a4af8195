/*
    A list of standard tests we want to run on (close to) every exemptions page
*/

import { InputDefinition, clickSaveAndContinueButton } from "./input-utils";

import { ValidationError } from "src/errors";
import { assertInputsWithOrder } from "./assert-utils";
import { createValidationErrorsForRequiredInputs } from "./errors-utils";
import { exemptionsPageSetup } from "./page-setup-utils";
import { setEnableEmployerExemptionsPortal } from "tests/pages/employers/employer-exemptions/employer-exemptions-test-utils";
import { waitFor } from "@testing-library/react";

// TODO (PFMLPB-23216) - Investigate moving into a project-level util file
export interface PageDefinition {
  name: string;
  component: ExemptionsPage;
  path: string;
  baseContent: Array<string | RegExp>;
  inputs: InputDefinition[];
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type ExemptionsPage = React.ComponentType<any>;
export const testDescriptions = {
  featureFlagOff:
    "when the enableEmployerExemptionsPortal feature flag is disabled",
  featureFlagOn:
    "when the enableEmployerExemptionsPortal feature flag is enabled",
  testCase404: "page not found is displayed",
  matchesSnapshot: "page content matches snapshot",
  displaysCorrectLinks: "page displays links with the correct urls",
  displaysInputsInOrder:
    "page displays the correct inputs in the correct order",
  displaysPageContent:
    "page displays the correct base content (e.g. title, section label, hint etc)",
  displaysAppropriateErrors:
    "page displays the correct errors when all fields are empty and save and continue is clicked",
  displaysFileUploadPrompt:
    "page displays the standard exemptions file upload prompt",
};

export const testConstants = {
  textNotFound: "Page not found",
};

export class TestDefinition {
  name: string;
  handler: () => Promise<void>;

  constructor(name: string, handler: () => Promise<void>) {
    this.name = name;
    this.handler = handler;
  }
}

export type TestGenerator = (_: PageDefinition) => TestDefinition;

// TODO (PFMLPB-23216) - Investigate refactoring our existing tests to use this + creating a project-level variant
export const createFeatureFlagTest = (pageDef: PageDefinition) =>
  new TestDefinition(testDescriptions.testCase404, async () => {
    setEnableEmployerExemptionsPortal(false);
    const { container } = await exemptionsPageSetup(
      pageDef.component,
      pageDef.path
    );
    expect(container).toHaveTextContent(testConstants.textNotFound);
  });

// TODO (PFMLPB-23216) - Investigate refactoring our existing tests to use this + creating a project-level variant
export const createSnapshotTest = (pageDef: PageDefinition) =>
  new TestDefinition(testDescriptions.matchesSnapshot, async () => {
    const { container } = await exemptionsPageSetup(
      pageDef.component,
      pageDef.path
    );
    expect(container).toMatchSnapshot();
  });

// TODO (PFMLPB-23216) - Investigate refactoring our existing tests to use this + creating a project-level variant
export const createBaseContentTest = (pageDef: PageDefinition) =>
  new TestDefinition(testDescriptions.displaysPageContent, async () => {
    const { container } = await exemptionsPageSetup(
      pageDef.component,
      pageDef.path
    );
    pageDef.baseContent.forEach((element) => {
      expect(container).toHaveTextContent(element);
    });
  });

export const createFilePromptDisplayedTest = (pageDef: PageDefinition) =>
  new TestDefinition(testDescriptions.displaysFileUploadPrompt, async () => {
    const { container } = await exemptionsPageSetup(
      pageDef.component,
      pageDef.path
    );
    const requirementsText = [
      "Document requirements",
      "The text must be clear and readable",
      "Any file you upload must be smaller than 4.5 mb",
      "Any file you upload must be a PDF, JPG, JPEG, or PNG file (PDF files are preferred)",
    ];
    requirementsText.forEach((blurb) => {
      expect(container).toHaveTextContent(blurb);
    });

    const fileUploadText = [
      "Tips for uploading images or PDFs",
      "New documents you’re uploading",
    ];
    fileUploadText.forEach((blurb) => {
      expect(container).toHaveTextContent(blurb);
    });
  });

// TODO (PFMLPB-23216) - Investigate refactoring our existing tests to use this + implementng new tests for each relevant page
export const createInputOrderAndTypesTest = (pageDef: PageDefinition) =>
  new TestDefinition(testDescriptions.displaysInputsInOrder, async () => {
    const { container } = await exemptionsPageSetup(
      pageDef.component,
      pageDef.path
    );
    assertInputsWithOrder(container, pageDef.inputs);
  });

// TODO (PFMLPB-23216) - Investigate adding a similar test to all of our relevant pages
export const createValidationErrorsTest = (
  component: ExemptionsPage,
  pathname: string | undefined,
  validationErrors: ValidationError[],
  updatePage?: (container: HTMLElement) => Promise<void>
) =>
  new TestDefinition(testDescriptions.displaysAppropriateErrors, async () => {
    const { container } = await exemptionsPageSetup(
      component,
      pathname,
      {},
      validationErrors
    );

    await updatePage?.(container);

    await waitFor(() => {
      validationErrors.forEach((v) =>
        expect(container.innerHTML).toContain(v.message)
      );
    });
  });

// TODO (PFMLPB-23216) - Investigate adding a similar test to all of our relevant pages
export const createRequiredFieldsTest = (pageDef: PageDefinition) =>
  createValidationErrorsTest(
    pageDef.component,
    pageDef.path,
    createValidationErrorsForRequiredInputs(pageDef.inputs),
    clickSaveAndContinueButton
  );

export const standardStubTests = [createFeatureFlagTest, createSnapshotTest];

export const standardContentAndDesignTests = [
  createBaseContentTest,
  createInputOrderAndTypesTest,
  createRequiredFieldsTest,
];
