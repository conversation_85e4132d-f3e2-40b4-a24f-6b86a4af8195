import {
  TestGenerator,
  createBaseContentTest,
  createFilePromptDisplayedTest,
  standardStubTests,
  testDescriptions,
} from "tests/pages/employers/employer-exemptions/exemptions-utils/standard-test-handlers";

import EmployerExemptionsSelfInsuredProofOfBenefitsDocumentUpload from "src/pages/employers/employer-exemptions/document-upload/self-insured-proof-of-benefits";
import routes from "src/routes";
import { setEnableEmployerExemptionsPortal } from "tests/pages/employers/employer-exemptions/exemptions-utils/page-setup-utils";

/* eslint-disable jest/expect-expect */
jest.mock("src/api/EmployerExemptionsApi");

const pageDef = {
  name: "EmployerExemptionsSelfInsuredProofOfBenefitsDocumentUpload",
  component: EmployerExemptionsSelfInsuredProofOfBenefitsDocumentUpload,
  path: routes.employers.employerExemptions
    .selfInsuredProofOfBenefitsDocumentUpload,
  baseContent: [
    "Upload documentation | Part 3 of 3",
    "Upload proof of paid leave benefits",
    "Provide documentation of your paid leave benefit for the type of leave (i.e., family, medical or both) you are requesting to be exempt from. This document must include:",
    "Benefit details including benefit amount and weeks of leave offered",
    "Employee payroll contributions",
    "Labor protections",
    "Document requirements",
  ],
  inputs: [],
};
const runTests = (generators: TestGenerator[]) =>
  generators.forEach((generator) => {
    const testDef = generator(pageDef);
    it(testDef.name, testDef.handler);
  });

describe(pageDef.name, () => {
  beforeEach(() => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date(2022, 1, 1));
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe(testDescriptions.featureFlagOff, () => {
    runTests(standardStubTests);
  });

  describe(testDescriptions.featureFlagOn, () => {
    beforeEach(() => setEnableEmployerExemptionsPortal(true));
    runTests([createBaseContentTest, createFilePromptDisplayedTest]);
  });
});
