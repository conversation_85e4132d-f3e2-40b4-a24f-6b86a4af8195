import {
  TestGenerator,
  createBaseContentTest,
  createFilePromptDisplayedTest,
  standardStubTests,
  testDescriptions,
} from "tests/pages/employers/employer-exemptions/exemptions-utils/standard-test-handlers";

import EmployerExemptionsSelfInsuredSuretyBondDocumentUpload from "src/pages/employers/employer-exemptions/document-upload/self-insured-surety-bond";
import routes from "src/routes";
import { setEnableEmployerExemptionsPortal } from "tests/pages/employers/employer-exemptions/exemptions-utils/page-setup-utils";

/* eslint-disable jest/expect-expect */
jest.mock("src/api/EmployerExemptionsApi");

const pageDef = {
  name: "EmployerExemptionsSelfInsuredSuretyBondDocumentUpload",
  component: EmployerExemptionsSelfInsuredSuretyBondDocumentUpload,
  path: routes.employers.employerExemptions.selfInsuredSuretyBondDocumentUpload,
  baseContent: [
    "Upload documentation | Part 2 of 3",
    "Upload surety bond",
    "Provide a copy of your signed and notarized surety bond. For more information about what is required, visit requirements for self-insured private paid leave plans.",
  ],
  inputs: [],
};
const runTests = (generators: TestGenerator[]) =>
  generators.forEach((generator) => {
    const testDef = generator(pageDef);
    it(testDef.name, testDef.handler);
  });

describe(pageDef.name, () => {
  beforeEach(() => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date(2022, 1, 1));
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe(testDescriptions.featureFlagOff, () => {
    runTests(standardStubTests);
  });

  describe(testDescriptions.featureFlagOn, () => {
    beforeEach(() => setEnableEmployerExemptionsPortal(true));
    runTests([createBaseContentTest, createFilePromptDisplayedTest]);
  });
});
