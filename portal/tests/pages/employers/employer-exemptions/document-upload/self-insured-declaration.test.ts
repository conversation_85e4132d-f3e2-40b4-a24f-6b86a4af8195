import {
  TestGenerator,
  createBaseContentTest,
  createFilePromptDisplayedTest,
  standardStubTests,
  testDescriptions,
} from "tests/pages/employers/employer-exemptions/exemptions-utils/standard-test-handlers";

import EmployerExemptionsSelfInsuredDeclarationDocumentUpload from "src/pages/employers/employer-exemptions/document-upload/self-insured-declaration";
import routes from "src/routes";
import { setEnableEmployerExemptionsPortal } from "tests/pages/employers/employer-exemptions/exemptions-utils/page-setup-utils";

/* eslint-disable jest/expect-expect */
jest.mock("src/api/EmployerExemptionsApi");

const pageDef = {
  name: "EmployerExemptionsSelfInsuredDeclarationDocumentUpload",
  component: EmployerExemptionsSelfInsuredDeclarationDocumentUpload,
  path: routes.employers.employerExemptions
    .selfInsuredDeclarationDocumentUpload,
  baseContent: [
    "Upload documentation | Part 1 of 3",
    "Upload self-insured insurance declaration document",
    "Provide a completed self-insured insurance declaration document as is required for self-insured private paid leave exemptions. For more information about what is required, visit requirements for self-insured private paid leave plans.",
  ],
  inputs: [],
};
const runTests = (generators: TestGenerator[]) =>
  generators.forEach((generator) => {
    const testDef = generator(pageDef);
    it(testDef.name, testDef.handler);
  });

describe(pageDef.name, () => {
  beforeEach(() => {
    jest.useFakeTimers();
    jest.setSystemTime(new Date(2022, 1, 1));
  });

  afterAll(() => {
    jest.useRealTimers();
  });

  describe(testDescriptions.featureFlagOff, () => {
    runTests(standardStubTests);
  });

  describe(testDescriptions.featureFlagOn, () => {
    beforeEach(() => setEnableEmployerExemptionsPortal(true));
    runTests([createBaseContentTest, createFilePromptDisplayedTest]);
  });
});
