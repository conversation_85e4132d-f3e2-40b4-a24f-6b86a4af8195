import {
  TestGenerator,
  standardContentAndDesignTests,
  standardStubTests,
  testDescriptions,
} from "./exemptions-utils/standard-test-handlers";

import EmployerExemptionsOrganizationDetails from "src/pages/employers/employer-exemptions/organization-details";
import { inputsForYesNoQuestion } from "./exemptions-utils/input-utils";
import routes from "src/routes";
import { setEnableEmployerExemptionsPortal } from "./exemptions-utils/page-setup-utils";

/* eslint-disable jest/expect-expect */
jest.mock("src/api/EmployerExemptionsApi");

const pageDef = {
  name: "EmployerExemptionsOrganizationDetails",
  component: EmployerExemptionsOrganizationDetails,
  path: routes.employers.employerExemptions.organizationDetails,
  baseContent: [
    "For guidance on calculating your total workforce, visit counting your total workforce for PFML contributions.",
    "What is the average size of your Massachusetts workforce?",
    "No",
    "Yes",
    "Was more than half of your Massachusetts workforce in the last calendar year paid through the 1099-MISC form?",
    "Tell us more about your organization",
    "Provide details about your organization's workforce.",
    "Organization details",
  ],
  inputs: [
    ...inputsForYesNoQuestion({
      fieldName: "should_workforce_count_include_1099_misc",
      errorMsg:
        "Select if 1099-MISC workers should be included in your workforce count.",
    }),
    {
      type: "text",
      name: "average_workforce_count",
      label: "What is the average size of your Massachusetts workforce?",
      errorMsg: "Enter the average number of employees.",
    },
  ],
};

const runTests = (generators: TestGenerator[]) =>
  generators.forEach((generator) => {
    const testDef = generator(pageDef);
    it(testDef.name, testDef.handler);
  });

describe(pageDef.name, () => {
  describe(testDescriptions.featureFlagOff, () => {
    runTests(standardStubTests);
  });

  describe(testDescriptions.featureFlagOn, () => {
    beforeEach(() => setEnableEmployerExemptionsPortal(true));
    runTests(standardContentAndDesignTests);
  });
});
