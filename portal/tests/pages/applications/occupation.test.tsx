import { BasePortalError, ValidationError } from "src/errors";
import { MockBenefitsApplicationBuilder, renderPage } from "tests/test-utils";
import { screen, waitFor } from "@testing-library/react";

import { Occupation } from "src/pages/applications/occupation";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";
import { setupBenefitsApplications } from "tests/test-utils/helpers";
import userEvent from "@testing-library/user-event";

const mockUpdate = jest.fn(() => Promise.resolve());

const render = (
  claim = new MockBenefitsApplicationBuilder().create(),
  errors: BasePortalError[] = []
) => {
  return renderPage(
    Occupation,
    {
      addCustomSetup: (appLogic) => {
        appLogic.errors = errors;
        setupBenefitsApplications(appLogic, [claim]);
        appLogic.benefitsApplications.update = mockUpdate;
      },
    },
    { claim }
  );
};

describe("Occupation", () => {
  describe("with the enableOccupationDataCollection FF disabled", () => {
    beforeEach(() => {
      setFeatureFlags({
        enableOccupationDataCollection: false,
      });
    });

    it("renders PageNotFound", () => {
      render();
      expect(
        screen.getByRole("heading", { name: "Page not found" })
      ).toBeInTheDocument();
    });
  });

  describe("with the enableOccupationDataCollection FF enabled", () => {
    beforeEach(() => {
      setFeatureFlags({
        enableOccupationDataCollection: true,
      });

      mockUpdate.mockClear();
    });

    it("renders the occupation page", () => {
      render();

      expect(
        screen.getByRole("heading", { name: "Employment information" })
      ).toBeInTheDocument();

      expect(
        screen.getByRole("heading", {
          name: /this information helps us understand the people and industries that use pfml to continue improving our website\. /i,
        })
      ).toBeInTheDocument();
      expect(
        screen.getByRole("heading", {
          name: /your benefits won't be impacted by your response\. /i,
        })
      ).toBeInTheDocument();
      expect(
        screen.getByRole("heading", {
          name: /you can select an option that best matches what you do for work\./i,
        })
      ).toBeInTheDocument();
    });

    it("renders a combobox", () => {
      render();

      expect(
        screen.getByRole("combobox", {
          name: /select an option/i,
        })
      ).toBeInTheDocument();
    });

    it("displays the correct label for the dropdown", () => {
      render();

      expect(screen.getByLabelText(/select an option/i)).toBeInTheDocument();
    });

    it("shows the correct options when interacted with", async () => {
      render();
      const expectedValues = [
        "Accommodation and Food Services",
        "Administrative and Support and Waste Management Remediation Services",
        "Agriculture, Forestry, Fishing, and Hunting",
        "Arts, Entertainment, and Recreation",
        "Construction",
        "Educational Services",
        "Finance and Insurance",
        "Health Care and Social Assistance",
        "Information",
        "Management of Companies and Enterprises",
        "Manufacturing",
        "Other Services (except Public Administration)",
        "Professional, Scientific, and Technical Services",
        "Public Administration",
        "Real Estate Rental and Leasing",
        "Retail Trade",
        "Transportation and Warehousing",
        "Utilities",
        "Wholesale Trade",
      ];
      await userEvent.click(screen.getByRole("combobox"));
      const options = screen.getAllByRole("option");

      expectedValues.forEach((expectedValue) => {
        expect(
          screen.getByRole("option", { name: expectedValue })
        ).toBeInTheDocument();
      });

      expect(options.length).toBe(expectedValues.length);
    });

    it("allows selecting an industry sector", async () => {
      const claim = new MockBenefitsApplicationBuilder().create();
      render(claim);

      const combobox = screen.getByRole("combobox", {
        name: /select an option/i,
      });

      await userEvent.click(combobox);
      await userEvent.click(
        screen.getByRole("option", { name: "Manufacturing" })
      );

      expect(combobox).toHaveValue("Manufacturing");
    });

    it("updates the form state when a selection is made", async () => {
      const claim = new MockBenefitsApplicationBuilder().create();
      render(claim);

      await userEvent.click(
        screen.getByRole("combobox", {
          name: /select an option/i,
        })
      );
      await userEvent.click(
        screen.getByRole("option", {
          name: "Health Care and Social Assistance",
        })
      );
      await userEvent.click(
        screen.getByRole("button", { name: "Save and continue" })
      );

      await waitFor(() => {
        expect(mockUpdate).toHaveBeenCalledWith(claim.application_id, {
          industry_sector: "Health Care and Social Assistance",
        });
      });
    });

    it("shows an existing value on the claim as selected on page load", () => {
      const claim = new MockBenefitsApplicationBuilder()
        .occupationDataCollection("Retail Trade")
        .create();

      render(claim);

      const combobox = screen.getByRole("combobox", {
        name: /select an option/i,
      });

      expect(combobox).toHaveValue("Retail Trade");
    });

    it("shows the correct error message when industry_sector is left empty", () => {
      const errors = [
        new ValidationError([
          {
            field: "industry_sector",
            message: "industry_sector is required",
            type: "required",
            rule: "conditional",
            namespace: "applications",
          },
        ]),
      ];

      const claim = new MockBenefitsApplicationBuilder().create();

      render(claim, errors);

      expect(
        screen.getByText("Select an option that best matches your job.")
      ).toBeInTheDocument();
    });

    describe("includes employer info in the title question when present in the claim", () => {
      const dba = "TEST COMPANY";
      const fein = "12-3456789";

      describe("when the enableEmployerNameContent FF is disabled", () => {
        beforeEach(() => {
          setFeatureFlags({ enableEmployerNameContent: false });
        });

        it("displays the default title question", () => {
          const claim = new MockBenefitsApplicationBuilder().create();
          render(claim);
          expect(
            screen.getByText(/What do you do for work/i)
          ).toBeInTheDocument();
          expect(screen.queryByText(dba)).not.toBeInTheDocument();
          expect(screen.queryByText(fein)).not.toBeInTheDocument();
        });
      });

      describe("when the enableEmployerNameContent FF is enabled", () => {
        beforeEach(() => {
          setFeatureFlags({ enableEmployerNameContent: true });
        });
        it("displays employer-specific question when FEIN is valid", () => {
          const claim = new MockBenefitsApplicationBuilder()
            .employed()
            .create();
          render(claim);

          expect(
            screen.getByText(/What do you do for work/i)
          ).toBeInTheDocument();
          expect(screen.getByText(fein)).toBeInTheDocument();
          expect(screen.queryByText(dba)).not.toBeInTheDocument();
        });

        it("displays employer-specific question when DBA and FEIN are valid", () => {
          const claim = new MockBenefitsApplicationBuilder()
            .employed()
            .employerDba(dba)
            .create();
          render(claim);

          expect(
            screen.getByText(/What do you do for work/i)
          ).toBeInTheDocument();
          expect(screen.getByText(dba)).toBeInTheDocument();
          expect(screen.getByText(fein)).toBeInTheDocument();
        });

        it("displays the default title question when employer info is missing", () => {
          const claim = new MockBenefitsApplicationBuilder().create();
          render(claim);
          expect(
            screen.getByText(/What do you do for work at your employer/i)
          ).toBeInTheDocument();
          expect(screen.queryByText(dba)).not.toBeInTheDocument();
          expect(screen.queryByText(fein)).not.toBeInTheDocument();
        });

        it("displays the default title question when FEIN is invalid", () => {
          const invalid_fein = "000000000"; // Invalid FEIN
          const claim = new MockBenefitsApplicationBuilder()
            .employed()
            .employerDba(dba)
            .create();
          claim.employer_fein = invalid_fein;
          render(claim);
          expect(
            screen.getByText(/What do you do for work at your employer/i)
          ).toBeInTheDocument();
          expect(screen.queryByText(dba)).not.toBeInTheDocument();
          expect(screen.queryByText(fein)).not.toBeInTheDocument();
        });
      });
    });
  });
});
