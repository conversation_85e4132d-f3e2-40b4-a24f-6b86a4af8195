import BenefitsApplication, {
  AdditionalUserNotFoundInfo,
  DurationBasis,
  EmploymentStatus,
  FrequencyIntervalBasis,
  IndustrySector,
  IntermittentLeavePeriod,
} from "src/models/BenefitsApplication";
import {
  BenefitsApplicationDocument,
  DocumentType,
  MilitaryDocumentType,
} from "src/models/Document";
import { DocumentsLoadError, ValidationError } from "src/errors";
import EmployerBenefit, {
  EmployerBenefitType,
} from "src/models/EmployerBenefit";
import { MockBenefitsApplicationBuilder, renderPage } from "tests/test-utils";
import OtherIncome, {
  OtherIncomeFrequency,
  OtherIncomeType,
} from "src/models/OtherIncome";
import PreviousLeave, { PreviousLeaveReason } from "src/models/PreviousLeave";
import React, { useEffect } from "react";
import Review, {
  EmployerBenefitList,
  OtherIncomeList,
  OtherLeaveEntry,
  PreviousLeaveList,
} from "src/pages/applications/review";
/* eslint-disable testing-library/no-manual-cleanup */
import {
  cleanup,
  render,
  screen,
  waitFor,
  within,
} from "@testing-library/react";

import ApiResourceCollection from "src/models/ApiResourceCollection";
import { AppLogic } from "src/hooks/useAppLogic";
import Language from "src/models/LanguageEnum";
import { LeaveReasonType } from "src/models/LeaveReason";
import { createMockBenefitsApplicationDocument } from "lib/mock-helpers/createMockDocument";
import dayjs from "dayjs";
import { get } from "lodash";
import routes from "src/routes";
import setFeatureFlags from "tests/test-utils/setFeatureFlags";
import { setupBenefitsApplications } from "tests/test-utils/helpers";
import userEvent from "@testing-library/user-event";

/* eslint-enable testing-library/no-manual-cleanup */

const setup = ({
  errors,
  claim = new MockBenefitsApplicationBuilder().part1Complete().create(),
  documents,
  addCustomSetup,
}: {
  errors?: Error[];
  claim?: BenefitsApplication;
  documents?: BenefitsApplicationDocument[];
  addCustomSetup?: (appLogic: AppLogic) => void;
} = {}) => {
  let completeSpy, submitSpy;
  const utils = renderPage(
    Review,
    {
      pathname: routes.applications.review,
      addCustomSetup: (appLogic) => {
        submitSpy = jest.spyOn(appLogic.benefitsApplications, "submit");
        completeSpy = jest.spyOn(appLogic.benefitsApplications, "complete");

        // Mock the Application
        setupBenefitsApplications(appLogic, [claim]);

        // Mock the Application's documents
        appLogic.documents.documents =
          new ApiResourceCollection<BenefitsApplicationDocument>(
            "fineos_document_id",
            documents
          );
        jest
          .spyOn(appLogic.documents, "hasLoadedClaimDocuments")
          .mockReturnValue(true);

        if (errors) {
          // Rather than mutating the errors, we set them as they would
          // when the API request completes. This fixes an issue where mutating
          // appLogic.errors was causing an infinite loop in our tests.
          // eslint-disable-next-line react-hooks/rules-of-hooks
          useEffect(() => {
            appLogic.setErrors(errors);
          });
        }

        if (addCustomSetup) {
          addCustomSetup(appLogic);
        }
      },
    },
    { query: { claim_id: claim.application_id } }
  );

  return { completeSpy, submitSpy, ...utils };
};

describe("Review Page", () => {
  it("renders Review page with warning AND explantory text for not notifying employer of leave", () => {
    setup({
      claim: new MockBenefitsApplicationBuilder().create(),
    });

    // Warning when user is on application review page and has not notified their employer
    expect(
      screen.queryByText(
        /communication with your employer is not a substitute for you notifying your employer about the leave/i
      )
    ).toBeInTheDocument();

    // Explanatory text before submitting part 1 when user is on application review page and has not notified their employer
    expect(
      screen.queryByText(
        /Paid Family and Medical Leave. Your leave request may be delayed or denied if you do not notify your employer/i
      )
    ).toBeInTheDocument();
  });

  it("renders Review page with Part 1 content and edit links when application hasn't been submitted yet when IDV is disabled", () => {
    setFeatureFlags({ enableMmgIDV: false, enableUniversalProfileIDV: false });
    const { container } = setup({
      claim: new MockBenefitsApplicationBuilder()
        .part1Complete()
        .mailingAddress()
        .fixedWorkPattern()
        .previousLeavesSameReason()
        .previousLeavesOtherReason()
        .otherIncome()
        .employerBenefit()
        .create(),
    });

    expect(container).toMatchSnapshot();

    // Safeguard to ensure we're passing in all the required data into our test.
    // There shouldn't be any missing content strings.
    expect(screen.queryByText(/pages\.claimsReview/i)).not.toBeInTheDocument();
  });

  it("renders Review page with Part 1 content and edit links when application hasn't been submitted yet when IDV isenabled", () => {
    setFeatureFlags({ enableMmgIDV: true, enableUniversalProfileIDV: true });
    const { container } = setup({
      claim: new MockBenefitsApplicationBuilder()
        .part1Complete()
        .mailingAddress()
        .fixedWorkPattern()
        .previousLeavesSameReason()
        .previousLeavesOtherReason()
        .otherIncome()
        .employerBenefit()
        .create(),
    });

    expect(container).toMatchSnapshot();

    // Safeguard to ensure we're passing in all the required data into our test.
    // There shouldn't be any missing content strings.
    expect(screen.queryByText(/pages\.claimsReview/i)).not.toBeInTheDocument();
  });

  it("renders Review page with all sections and only edit links for Part 3 sections when Part 1 has already been submitted when IDV is disabled", () => {
    setFeatureFlags({ enableMmgIDV: false, enableUniversalProfileIDV: false });
    const { container } = setup({
      claim: new MockBenefitsApplicationBuilder().complete().create(),
    });

    expect(container).toMatchSnapshot();

    // Safeguard to ensure we're passing in all the required data into our test.
    // There shouldn't be any missing content strings.
    expect(screen.queryByText(/pages\.claimsReview/i)).not.toBeInTheDocument();
  });

  it("renders Review page with all sections and only edit links for Part 3 sections when Part 1 has already been submitted, when IDV is enabled", () => {
    setFeatureFlags({ enableMmgIDV: true, enableUniversalProfileIDV: true });
    const { container } = setup({
      claim: new MockBenefitsApplicationBuilder().complete().create(),
    });

    expect(container).toMatchSnapshot();

    // Safeguard to ensure we're passing in all the required data into our test.
    // There shouldn't be any missing content strings.
    expect(screen.queryByText(/pages\.claimsReview/i)).not.toBeInTheDocument();
  });

  it("renders Review page with previous leaves Yes", () => {
    const { container } = setup({
      claim: new MockBenefitsApplicationBuilder()
        .part1Complete()
        .previousLeaves([
          {
            is_for_current_employer: true,
            leave_minutes: 2400,
            leave_reason: PreviousLeaveReason.serviceMemberFamily,
            leave_start_date: "2020-03-01",
            leave_end_date: "2020-03-06",
            previous_leave_id: 0,
            type: "any_reason",
            worked_per_week_minutes: 1440,
          },
        ])
        .create(),
    });

    expect(container.innerHTML).toContain("Previous leave?</h4>Yes</div>");
  });

  it("submits the application when the user clicks Submit for a Part 1 review", async () => {
    const { completeSpy, submitSpy } = setup();

    await userEvent.click(screen.getByRole("button", { name: /submit/i }));

    await waitFor(() => {
      expect(submitSpy).toHaveBeenCalledWith("mock_application_id");
    });

    expect(completeSpy).not.toHaveBeenCalled();
  });

  it("doesn't display tax information for review when boolean is not set", () => {
    setup({
      claim: new MockBenefitsApplicationBuilder()
        .submitted()
        .paymentPrefSubmitted()
        .create(),
    });

    expect(screen.queryByText(/Tax withholding/)).not.toBeInTheDocument();
  });

  it("conditionally renders employer notification date", () => {
    // Applications imported from Fineos as part of Channel Switching won't have this field
    const textMatch = /Notified employer/;
    const claim = new MockBenefitsApplicationBuilder()
      .part1Complete()
      .notifiedEmployer()
      .create();

    setup({ claim });
    expect(screen.queryAllByText(textMatch)).toHaveLength(2);

    cleanup();

    claim.leave_details.employer_notified = null;
    claim.leave_details.employer_notification_date = null;
    setup({ claim });
    expect(screen.queryByText(textMatch)).not.toBeInTheDocument();
  });

  it("displays tax information for review when tax Pref is set", () => {
    setup({
      claim: new MockBenefitsApplicationBuilder()
        .complete()
        .taxPrefSubmitted(true)
        .create(),
    });

    const label = screen.getByRole("heading", {
      name: "Withhold state and federal taxes?",
    });
    expect(label.nextSibling).toHaveTextContent(/Yes/);
    expect(screen.getAllByText("No")).toHaveLength(6);
  });

  it("displays tax information for review, including No if no selected", () => {
    setup({
      claim: new MockBenefitsApplicationBuilder()
        .complete()
        .taxPrefSubmitted(false)
        .create(),
    });
    expect(screen.getByText(/Tax withholding/)).toBeInTheDocument();
    expect(screen.queryByText("Yes")).not.toBeInTheDocument();
    expect(screen.getAllByText("No")).toHaveLength(7);
  });

  it("completes the application when the user clicks Submit for a Part 3 review with certificate document", async () => {
    const { completeSpy, submitSpy } = setup({
      claim: new MockBenefitsApplicationBuilder().complete().create(),
    });

    await userEvent.click(screen.getByRole("button", { name: /submit/i }));

    await waitFor(() => {
      expect(completeSpy).toHaveBeenCalledWith("mock_application_id", false);
    });

    expect(submitSpy).not.toHaveBeenCalled();
  });

  it("completes the application when the user clicks Submit for a Part 3 review with deferred certificate document for bonding leave", async () => {
    const futureDate = dayjs().add(1, "month").format("YYYY-MM-DD");
    const mockClaimBondingLeave = new MockBenefitsApplicationBuilder()
      .complete()
      .bondingBirthLeaveReason(futureDate)
      .hasFutureChild()
      .create();

    const { completeSpy, submitSpy } = setup({
      claim: mockClaimBondingLeave,
    });

    await userEvent.click(screen.getByRole("button", { name: /submit/i }));

    await waitFor(() => {
      expect(completeSpy).toHaveBeenCalledWith("mock_application_id", true);
    });

    expect(submitSpy).not.toHaveBeenCalled();
  });

  it("completes the application when the user clicks Submit for a Part 3 review with deferred certificate document for medical leave", async () => {
    setFeatureFlags({ documentUploadOptional: true });

    const mockClaimMedicalLeave = new MockBenefitsApplicationBuilder()
      .medicalLeaveReason()
      .complete()
      .create();

    const { completeSpy, submitSpy } = setup({
      claim: mockClaimMedicalLeave,
      addCustomSetup: (appLogic) => {
        appLogic.documents.setHasDeferredCertDoc = jest
          .fn()
          .mockReturnValue(true);
        appLogic.documents.hasDeferredCertDoc = true;
      },
    });

    await userEvent.click(screen.getByRole("button", { name: /submit/i }));

    await waitFor(() => {
      expect(completeSpy).toHaveBeenCalledWith("mock_application_id", true);
    });

    expect(submitSpy).not.toHaveBeenCalled();
  });

  it("renders a Alert when there are required field errors", () => {
    const errors = [
      new ValidationError([
        { type: "required", field: "someField", namespace: "applications" },
      ]),
    ];

    setup({ errors });

    expect(
      screen.getByText(/We’ve added some new questions/i).parentNode
    ).toMatchSnapshot();
  });

  it("does not render a custom Alert when there are required errors not associated to a specific field", () => {
    const errors = [
      new ValidationError([
        {
          type: "required",
          rule: "require_employer_notified",
          namespace: "applications",
        },
      ]),
    ];

    setup({ errors });

    expect(
      screen.queryByText(/We’ve added some new questions/i)
    ).not.toBeInTheDocument();
  });

  it("renders Alert when there is an error for loading documents", () => {
    const claim = new MockBenefitsApplicationBuilder().complete().create();

    setup({
      errors: [
        new DocumentsLoadError({ application_id: claim.application_id }),
      ],
      claim,
    });

    expect(
      screen.getByText(
        /error was encountered while checking your application for documents/i
      )
    ).toBeInTheDocument();
  });

  it("renders the number of uploaded documents, filtering by document type and leave reason", () => {
    const claim = new MockBenefitsApplicationBuilder()
      .complete()
      .pregnancyLeaveReason()
      .create();

    setup({
      claim,
      documents: [
        createMockBenefitsApplicationDocument({
          application_id: claim.application_id,
          fineos_document_id: "1",
          document_type:
            DocumentType.certification[
              claim.leave_details.reason as LeaveReasonType
            ],
        }),
        createMockBenefitsApplicationDocument({
          application_id: claim.application_id,
          fineos_document_id: "12",
          document_type:
            DocumentType.certification[
              claim.leave_details.reason as LeaveReasonType
            ],
        }),
        createMockBenefitsApplicationDocument({
          application_id: claim.application_id,
          fineos_document_id: "3",
          document_type: DocumentType.identityVerification,
        }),
      ],
    });

    expect(
      screen.getByRole("heading", { name: /Upload identity document/i })
        .parentNode?.nextSibling
    ).toMatchSnapshot();
    expect(
      screen.getByRole("heading", { name: /Upload certification document/i })
        .parentNode?.nextSibling
    ).toMatchSnapshot();
  });

  const militaryUploadSections: Array<[string, string]> = [
    ["Military Exigency Family", "Upload miliary exigency document"],
    [
      "Military Exigency Family",
      "Upload family member active duty service proof",
    ],
    ["Military Caregiver", "Upload Care for a Family Member form"],
    ["Military Caregiver", "Upload covered service member ID"],
  ];
  it.each(militaryUploadSections)(
    "renders military-specific review steps with the correct heading and number of uploads",
    (reason, heading) => {
      const claim = new MockBenefitsApplicationBuilder()
        .complete()
        .useGivenLeaveReason(reason)
        .create();

      setup({
        claim,
        documents: [
          createMockBenefitsApplicationDocument({
            application_id: claim.application_id,
            fineos_document_id: "1",
            document_type: MilitaryDocumentType.activeDutyFamily,
          }),
          createMockBenefitsApplicationDocument({
            application_id: claim.application_id,
            fineos_document_id: "12",
            document_type:
              MilitaryDocumentType.familyMemberActiveDutyServiceProof,
          }),
          createMockBenefitsApplicationDocument({
            application_id: claim.application_id,
            fineos_document_id: "3",
            document_type: DocumentType.identityVerification,
          }),
          createMockBenefitsApplicationDocument({
            application_id: claim.application_id,
            fineos_document_id: "4",
            document_type: MilitaryDocumentType.serviceMemberFamily,
          }),
          createMockBenefitsApplicationDocument({
            application_id: claim.application_id,
            fineos_document_id: "8",
            document_type: MilitaryDocumentType.coveredServiceMemberProof,
          }),
        ],
      });

      expect(
        screen.getByRole("heading", { name: heading }).parentNode?.nextSibling
      ).toMatchSnapshot();
    }
  );

  it("does not render certification document row when claim is for future bonding leave", () => {
    const futureDate = dayjs().add(1, "month").format("YYYY-MM-DD");

    setup({
      claim: new MockBenefitsApplicationBuilder()
        .complete()
        .bondingBirthLeaveReason(futureDate)
        .hasFutureChild()
        .create(),
    });

    expect(screen.queryByText(/Upload certification/i)).not.toBeInTheDocument();
  });

  it("renders Other Leave section when the application has answers for that section", () => {
    const headingTextMatch = /Other leave, benefits, and income/i;
    const claim = new MockBenefitsApplicationBuilder().complete().create();
    claim.has_other_incomes = false;
    claim.has_employer_benefits = false;

    // Renders when falsy
    setup({ claim });

    expect(
      screen.getByRole("heading", { name: headingTextMatch })
    ).toBeInTheDocument();

    // But doesn't render when null
    cleanup();
    claim.has_other_incomes = null;
    claim.has_employer_benefits = null;
    claim.has_previous_leaves = null;
    claim.has_previous_leaves_same_reason = null;
    claim.has_previous_leaves_other_reason = null;
    setup({ claim });

    expect(
      screen.queryByRole("heading", { name: headingTextMatch })
    ).not.toBeInTheDocument();
  });

  describe("User Not Found Info", () => {
    const userNotFoundQuestion: Array<
      [string, keyof AdditionalUserNotFoundInfo]
    > = [
      ["What is your employer name?", "employer_name"],
      ["What was your start date at this employer?", "date_of_hire"],
      ["Do you currently work at this employer?", "currently_employed"],
      ["When was your last day at this employer?", "date_of_separation"],
      [
        "Was your employer recently merged with or acquired by another company?",
        "recently_acquired_or_merged",
      ],
    ];

    it.each(userNotFoundQuestion)(
      "conditionally renders expected review row for '%s' when set",
      (question, field) => {
        const claim = new MockBenefitsApplicationBuilder()
          .part1Complete()
          .additionalUserNotFoundInfo()
          .create();

        setup({
          claim,
          addCustomSetup: (appLogic) => {
            appLogic.benefitsApplications.hasUserNotFoundError = jest
              .fn()
              .mockReturnValue(true);
          },
        });

        if (claim?.additional_user_not_found_info?.[field] != null) {
          expect(
            screen.queryByText(new RegExp(question, "i"))
          ).toBeInTheDocument();
        } else {
          expect(
            screen.queryByText(new RegExp(question, "i"))
          ).not.toBeInTheDocument();
        }
      }
    );

    it.each(userNotFoundQuestion)(
      "does not render review row for '%s' when no UNF info",
      (question, _) => {
        // do not configure with call to .additionalUserNotFoundInfo()
        const claim = new MockBenefitsApplicationBuilder()
          .part1Complete()
          .create();

        setup({
          claim,
          addCustomSetup: (appLogic) => {
            appLogic.benefitsApplications.hasUserNotFoundError = jest
              .fn()
              .mockReturnValue(true);
          },
        });

        expect(
          screen.queryByText(new RegExp(question, "i"))
        ).not.toBeInTheDocument();
      }
    );

    const userNotFoundPreferences: Array<[string, string]> = [
      ["Payment method", "payment_preference.payment_method"],
      ["Tax withholding", "is_withholding_tax"],
    ];
    it.each(userNotFoundPreferences)(
      "conditionally renders '%s' section when set",
      (section, field) => {
        const claim = new MockBenefitsApplicationBuilder()
          .part1Complete()
          .additionalUserNotFoundInfo()
          .create();

        setup({
          claim,
          addCustomSetup: (appLogic) => {
            appLogic.benefitsApplications.hasUserNotFoundError = jest
              .fn()
              .mockReturnValue(true);
          },
        });

        expect(screen.getByText(new RegExp(section, "i"))).toBeInTheDocument();
        expect(get(claim, field)).not.toBe(null);
      }
    );
  });

  describe("Work patterns", () => {
    it("displays correct work pattern details when pattern type is Fixed", () => {
      setup({
        claim: new MockBenefitsApplicationBuilder()
          .part1Complete()
          .fixedWorkPattern()
          .create(),
      });

      expect(
        screen.getByText(/Each week I work the same number/i)
      ).toBeInTheDocument();

      expect(
        screen.getByText(/Weekly work hours/i).nextElementSibling
      ).toMatchSnapshot();
    });

    it("displays correct work pattern details when pattern type is Variable", () => {
      setup({
        claim: new MockBenefitsApplicationBuilder()
          .part1Complete()
          .variableWorkPattern()
          .create(),
      });

      expect(
        screen.getByText(/My schedule is not consistent from week to week/i)
      ).toBeInTheDocument();

      expect(
        screen.getByText(/Average weekly hours/i).nextSibling
      ).toHaveTextContent("40");

      expect(
        screen.getByText(/Average weekly hours/i).nextSibling?.nextSibling
      ).toHaveTextContent("h");
    });

    it("conditionally renders work pattern type", () => {
      const textMatch = /Work schedule type/;
      const claim = new MockBenefitsApplicationBuilder()
        .part1Complete()
        .fixedWorkPattern()
        .create();

      setup({ claim });
      expect(
        screen.queryAllByRole("heading", { name: textMatch })
      ).not.toHaveLength(0);

      cleanup();

      claim.work_pattern = null;
      setup({ claim });
      expect(
        screen.queryByRole("heading", { name: textMatch })
      ).not.toBeInTheDocument();
    });
  });

  it("does not render ACH content when payment method is Check", () => {
    const textMatch = /Routing number/i;

    setup({
      claim: new MockBenefitsApplicationBuilder().complete().check().create(),
    });

    expect(screen.queryByText(textMatch)).not.toBeInTheDocument();

    cleanup();
    setup({
      claim: new MockBenefitsApplicationBuilder()
        .complete()
        .directDeposit()
        .create(),
    });

    expect(screen.queryByText(textMatch)).toBeInTheDocument();
  });

  it("does not render Employer rows when Claimant is unemployed", () => {
    const einTextMatch = "Employer’s EIN";
    const notifyTextMatch = "Notified employer";
    const claim = new MockBenefitsApplicationBuilder()
      .complete()
      .employed()
      .create();

    setup({ claim });
    expect(screen.queryByText(einTextMatch)).toBeInTheDocument();
    expect(screen.queryByText(notifyTextMatch)).toBeInTheDocument();

    cleanup();
    claim.employment_status = EmploymentStatus.unemployed;
    setup({ claim });

    expect(screen.queryByText(einTextMatch)).not.toBeInTheDocument();
    expect(screen.queryByText(notifyTextMatch)).not.toBeInTheDocument();
  });

  it.each([
    [
      "medical",
      new MockBenefitsApplicationBuilder().part1Complete().medicalLeaveReason(),
    ],
    [
      "family",
      new MockBenefitsApplicationBuilder()
        .part1Complete()
        .bondingBirthLeaveReason(),
    ],
    [
      "caring",
      new MockBenefitsApplicationBuilder().part1Complete().caringLeaveReason(),
    ],
  ])("renders expected review row for %s leave reason", (reason, claim) => {
    const textMatch =
      {
        medical: "Leave for pregnancy or recovery from childbirth",
        family: "Family leave type",
        caring: "Family member's relationship",
      }[reason] || "no match";

    setup({ claim: claim.create() });

    expect(screen.queryByText(textMatch)).toBeInTheDocument();
  });

  it("conditionally renders Family Member information depending on if caring_leave_metadata is set", () => {
    const textMatch = /Family member's/;
    const claim = new MockBenefitsApplicationBuilder()
      .part1Complete()
      .caringLeaveReason()
      .create();

    setup({ claim });
    expect(
      screen.queryAllByRole("heading", { name: textMatch })
    ).not.toHaveLength(0);

    cleanup();

    claim.leave_details.caring_leave_metadata = null;
    setup({ claim });
    expect(
      screen.queryByRole("heading", { name: textMatch })
    ).not.toBeInTheDocument();
  });

  it("conditionally renders child birth date", () => {
    // Applications imported from Fineos as part of Channel Switching won't have this field
    const textMatch = /Child’s date of birth/;
    const claim = new MockBenefitsApplicationBuilder()
      .part1Complete()
      .bondingBirthLeaveReason()
      .create();

    setup({ claim });
    expect(
      screen.queryAllByRole("heading", { name: textMatch })
    ).not.toHaveLength(0);

    cleanup();

    claim.leave_details.child_birth_date = null;
    setup({ claim });
    expect(
      screen.queryByRole("heading", { name: textMatch })
    ).not.toBeInTheDocument();
  });

  it("conditionally renders child placement date", () => {
    // Applications imported from Fineos as part of Channel Switching won't have this field
    const textMatch = /Child’s placement date/;
    const claim = new MockBenefitsApplicationBuilder()
      .part1Complete()
      .bondingAdoptionLeaveReason()
      .create();

    setup({ claim });
    expect(
      screen.queryAllByRole("heading", { name: textMatch })
    ).not.toHaveLength(0);

    cleanup();

    claim.leave_details.child_placement_date = null;
    setup({ claim });
    expect(
      screen.queryByRole("heading", { name: textMatch })
    ).not.toBeInTheDocument();
  });

  it("conditionally renders intermittent leave frequency depending on if it is set", () => {
    const textMatch = /Frequency of intermittent/;
    const claim = new MockBenefitsApplicationBuilder()
      .part1Complete()
      .intermittent()
      .create();

    setup({ claim });
    expect(
      screen.queryAllByRole("heading", { name: textMatch })
    ).not.toHaveLength(0);

    cleanup();

    claim.leave_details.intermittent_leave_periods[0].frequency = null;
    setup({ claim });
    expect(
      screen.queryByRole("heading", { name: textMatch })
    ).not.toBeInTheDocument();
  });

  it("renders WeeklyTimeTable for the reduced leave period when work pattern is Fixed", () => {
    const claim = new MockBenefitsApplicationBuilder()
      .part1Complete()
      .fixedWorkPattern()
      .reducedSchedule()
      .create();

    setup({ claim });

    expect(screen.getByText("Hours off per week").parentNode).toMatchSnapshot();
  });

  it("renders total time for the reduced leave period when work pattern is Variable", () => {
    const claim = new MockBenefitsApplicationBuilder()
      .part1Complete()
      .variableWorkPattern()
      .reducedSchedule()
      .create();

    setup({ claim });

    expect(screen.getByText("Hours off per week").parentNode).toMatchSnapshot();
  });

  it("renders 'has concurrent employment' question section", () => {
    const claim = new MockBenefitsApplicationBuilder().part1Complete().create();
    claim.has_concurrent_employers = true;

    setup({ claim });

    expect(
      screen.getByText("Do you currently work for any additional employers?")
    ).toBeInTheDocument();
  });

  it("renders 'total hours across all employers' question section when applicant has concurrent employers and reported hours", () => {
    const claim = new MockBenefitsApplicationBuilder().part1Complete().create();

    claim.has_concurrent_employers = true;
    claim.hours_worked_per_week_all_employers = 40;

    setup({ claim });

    expect(
      screen.getByText(
        "How many total hours do you work each week on average across all of your employers?"
      )
    ).toBeInTheDocument();
  });

  it("doesn't render 'total hours across all employers' question section when applicant has no concurrent employers", () => {
    const claim = new MockBenefitsApplicationBuilder().part1Complete().create();

    claim.has_concurrent_employers = false;

    setup({ claim });

    expect(
      screen.queryByText(
        "How many total hours do you work each week on average across all of your employers?"
      )
    ).not.toBeInTheDocument();
  });

  it("doesn't render 'total hours across all employers' question section when the applicant has not answered the concurrent employment questions", () => {
    const claim = new MockBenefitsApplicationBuilder().part1Complete().create();

    claim.has_concurrent_employers = null;

    setup({ claim });

    expect(
      screen.queryByText(
        "How many total hours do you work each week on average across all of your employers?"
      )
    ).not.toBeInTheDocument();
  });

  it("renders the leave frequency and duration in plain language", () => {
    // Generate all possible combinations of Duration/Frequency for an Intermittent Leave Period:
    const durations = Object.values(DurationBasis);
    const frequencyIntervals = Object.values(FrequencyIntervalBasis);
    const intermittentLeavePeriodPermutations: IntermittentLeavePeriod[] = [];

    durations.forEach((duration_basis) => {
      frequencyIntervals.forEach((frequency_interval_basis) => {
        intermittentLeavePeriodPermutations.push(
          new IntermittentLeavePeriod({
            duration_basis,
            frequency_interval_basis,
            frequency: 2,
            duration: 3,
          })
        );
      });

      intermittentLeavePeriodPermutations.push(
        new IntermittentLeavePeriod({
          duration_basis,
          frequency_interval: 6, // irregular over 6 months
          frequency_interval_basis: FrequencyIntervalBasis.months,
          frequency: 2,
          duration: 3,
        })
      );
    });

    intermittentLeavePeriodPermutations.forEach((intermittentLeavePeriod) => {
      const claim = new MockBenefitsApplicationBuilder()
        .part1Complete()
        .create();
      claim.leave_details.intermittent_leave_periods = [
        intermittentLeavePeriod,
      ];

      setup({ claim });

      expect(
        screen.queryByText(/Estimated [0-9]+ absences/i)
      ).toMatchSnapshot();

      cleanup();
    });
  });

  it.each([
    {
      form: "singularized",
      frequency: 1,
      duration: 1,
      duration_basis: DurationBasis.days,
      expectedAbsenceWord: "absence",
      expectedDurationWord: "day",
    },
    {
      form: "pluralized",
      frequency: 3,
      duration: 3,
      duration_basis: DurationBasis.days,
      expectedAbsenceWord: "absences",
      expectedDurationWord: "days",
    },
    {
      form: "singularized",
      frequency: 1,
      duration: 1,
      duration_basis: DurationBasis.hours,
      expectedAbsenceWord: "absence",
      expectedDurationWord: "hour",
    },
    {
      form: "pluralized",
      frequency: 2,
      duration: 2,
      duration_basis: DurationBasis.hours,
      expectedAbsenceWord: "absences",
      expectedDurationWord: "hours",
    },
  ])(
    "renders $form form of '$expectedAbsenceWord' and '$expectedDurationWord' for frequency = $frequency, and duration = $duration)",
    ({
      frequency,
      duration,
      duration_basis,
      expectedAbsenceWord,
      expectedDurationWord,
    }) => {
      const claim = new MockBenefitsApplicationBuilder()
        .part1Complete()
        .intermittent()
        .create();

      claim.leave_details.intermittent_leave_periods = [
        new IntermittentLeavePeriod({
          duration_basis,
          frequency_interval: 6,
          frequency_interval_basis: FrequencyIntervalBasis.months,
          frequency,
          duration,
        }),
      ];

      setup({ claim });

      const reviewRows = screen.getAllByTestId("ReviewRow");
      const targetRow = reviewRows.find((row) =>
        within(row).queryByText("Frequency of intermittent leave")
      );

      expect(targetRow).toBeDefined();
      expect(targetRow!).toHaveTextContent(
        new RegExp(
          `Estimated ${frequency} ${expectedAbsenceWord}.*${duration} ${expectedDurationWord}`,
          "i"
        )
      );

      cleanup();
    }
  );

  describe("EmployerBenefitList", () => {
    const entries = [
      new EmployerBenefit({
        benefit_type: EmployerBenefitType.paidLeave,
        is_full_salary_continuous: true,
      }),
      new EmployerBenefit({
        benefit_type: EmployerBenefitType.shortTermDisability,
        is_full_salary_continuous: true,
      }),
      new EmployerBenefit({
        benefit_end_date: "2021-12-30",
        benefit_start_date: "2021-08-12",
        benefit_type: EmployerBenefitType.permanentDisability,
        is_full_salary_continuous: true,
      }),
      new EmployerBenefit({
        benefit_end_date: "2021-12-30",
        benefit_start_date: "2021-08-12",
        benefit_type: EmployerBenefitType.familyOrMedicalLeave,
        is_full_salary_continuous: true,
      }),
    ];

    it("renders all data fields when all data are present", () => {
      const { container } = render(
        <EmployerBenefitList entries={entries} reviewRowLevel="4" />
      );

      expect(container).toMatchSnapshot();
    });

    it("doesn't render missing data when amount fields are missing", () => {
      const entries = [
        new EmployerBenefit({
          benefit_end_date: "2021-12-30",
          benefit_start_date: "2021-08-12",
          benefit_type: EmployerBenefitType.permanentDisability,
        }),
      ];
      const { container } = render(
        <EmployerBenefitList entries={entries} reviewRowLevel="4" />
      );

      expect(container).toMatchSnapshot();
    });
  });

  describe("PreviousLeaveList", () => {
    it("renders other reason list", () => {
      const entries = [
        new PreviousLeave({
          is_for_current_employer: true,
          leave_end_date: "2021-05-01",
          leave_start_date: "2021-07-01",
          leave_minutes: 20 * 60,
          leave_reason: PreviousLeaveReason.care,
          worked_per_week_minutes: 40 * 60,
        }),
        new PreviousLeave({
          is_for_current_employer: false,
          leave_end_date: "2021-05-01",
          leave_start_date: "2021-07-01",
          leave_minutes: 20 * 60,
          leave_reason: PreviousLeaveReason.bonding,
          worked_per_week_minutes: 40 * 60,
        }),
      ];

      const { container } = render(
        <PreviousLeaveList
          entries={entries}
          type="otherReason"
          startIndex={1}
          reviewRowLevel="4"
        />
      );

      expect(container).toMatchSnapshot();
    });

    it("renders same reason list", () => {
      const entries = [
        new PreviousLeave({
          is_for_current_employer: true,
          leave_end_date: "2021-05-01",
          leave_start_date: "2021-07-01",
          leave_minutes: 20 * 60,
          leave_reason: null,
          worked_per_week_minutes: 40 * 60,
        }),
        new PreviousLeave({
          is_for_current_employer: false,
          leave_end_date: "2021-05-01",
          leave_start_date: "2021-07-01",
          leave_minutes: 20 * 60,
          leave_reason: null,
          worked_per_week_minutes: 40 * 60,
        }),
      ];

      const { container } = render(
        <PreviousLeaveList
          entries={entries}
          type="sameReason"
          startIndex={1}
          reviewRowLevel="4"
        />
      );

      expect(container).toMatchSnapshot();
    });

    it("renders any reason list", () => {
      const entries = [
        new PreviousLeave({
          is_for_current_employer: true,
          leave_end_date: "2021-05-01",
          leave_start_date: "2021-07-01",
          leave_minutes: 20 * 60,
          leave_reason: PreviousLeaveReason.medical,
          type: "any_reason",
          worked_per_week_minutes: 40 * 60,
        }),
        new PreviousLeave({
          is_for_current_employer: false,
          leave_end_date: "2021-05-01",
          leave_start_date: "2021-07-01",
          leave_minutes: 20 * 60,
          leave_reason: PreviousLeaveReason.bonding,
          type: "any_reason",
          worked_per_week_minutes: 40 * 60,
        }),
      ];

      const { container } = render(
        <PreviousLeaveList
          entries={entries}
          type="anyReason"
          startIndex={1}
          reviewRowLevel="4"
        />
      );

      expect(container).toMatchSnapshot();
    });
  });

  describe("OtherIncomeList", () => {
    it("renders all data fields when all data are present", () => {
      const entries = [
        new OtherIncome({
          income_amount_dollars: 250,
          income_amount_frequency: OtherIncomeFrequency.monthly,
          income_end_date: "2021-12-30",
          income_start_date: "2021-08-12",
          income_type: OtherIncomeType.workersCompensation,
        }),
        new OtherIncome({
          income_amount_dollars: 250,
          income_amount_frequency: OtherIncomeFrequency.monthly,
          income_end_date: "2021-12-30",
          income_start_date: "2021-08-12",
          income_type: OtherIncomeType.unemployment,
        }),
        new OtherIncome({
          income_amount_dollars: 250,
          income_amount_frequency: OtherIncomeFrequency.monthly,
          income_end_date: "2021-12-30",
          income_start_date: "2021-08-12",
          income_type: OtherIncomeType.ssdi,
        }),
        new OtherIncome({
          income_amount_dollars: 250,
          income_amount_frequency: OtherIncomeFrequency.monthly,
          income_end_date: "2021-12-30",
          income_start_date: "2021-08-12",
          income_type: OtherIncomeType.retirementDisability,
        }),
        new OtherIncome({
          income_amount_dollars: 250,
          income_amount_frequency: OtherIncomeFrequency.monthly,
          income_end_date: "2021-12-30",
          income_start_date: "2021-08-12",
          income_type: OtherIncomeType.jonesAct,
        }),
        new OtherIncome({
          income_amount_dollars: 250,
          income_amount_frequency: OtherIncomeFrequency.monthly,
          income_end_date: "2021-12-30",
          income_start_date: "2021-08-12",
          income_type: OtherIncomeType.railroadRetirement,
        }),
        new OtherIncome({
          income_amount_dollars: 250,
          income_amount_frequency: OtherIncomeFrequency.monthly,
          income_end_date: "2021-12-30",
          income_start_date: "2021-08-12",
          income_type: OtherIncomeType.otherEmployer,
        }),
      ];
      const { container } = render(
        <OtherIncomeList entries={entries} reviewRowLevel="4" />
      );

      expect(container).toMatchSnapshot();
    });

    it("doesn't render missing data when amount fields are missing", () => {
      const entries = [
        new OtherIncome({
          income_end_date: "2021-12-30",
          income_start_date: "2021-08-12",
          income_type: OtherIncomeType.otherEmployer,
        }),
      ];
      const { container } = render(
        <OtherIncomeList entries={entries} reviewRowLevel="4" />
      );

      expect(container).toMatchSnapshot();
    });
  });

  describe("OtherLeaveEntry", () => {
    it("renders all data fields when all data are present", () => {
      const label = "Benefit 1";
      const type = "Medical or family leave";
      const dates = "07-22-2020 - 09-22-2020";
      const amount = "$250 per month";
      const { container } = render(
        <OtherLeaveEntry
          label={label}
          type={type}
          dates={dates}
          amount={amount}
          reviewRowLevel="4"
        />
      );

      expect(container).toMatchSnapshot();
    });

    it("doesn't render missing data when amount is missing", () => {
      const label = "Benefit 1";
      const type = "Medical or family leave";
      const dates = "07-22-2020 - 09-22-2020";
      const { container } = render(
        <OtherLeaveEntry
          label={label}
          type={type}
          dates={dates}
          amount={null}
          reviewRowLevel="4"
        />
      );

      expect(container).toMatchSnapshot();
    });

    it.each([
      { label: "English", value: Language.english },
      { label: "Español", value: Language.spanish },
      { label: "Kreyòl Ayisyen", value: Language.haitian_creole },
      { label: "Português (Brasil)", value: Language.portuguese },
      { label: "中文", value: Language.chinese_simplified },
      { label: "Tiếng Việt", value: Language.vietnamese },
      { label: "Français", value: Language.french },
      {
        label: "English",
        value: Language.not_listed,
      },
    ])(
      "renders Review page with Part 1 language preference '$label' in document when '$value' is selected when IDV is disabled.",
      ({ label, value }) => {
        setFeatureFlags({
          enableMmgIDV: false,
          enableUniversalProfileIDV: false,
        });
        const { container } = setup({
          claim: new MockBenefitsApplicationBuilder()
            .part1Complete()
            .languagePreference(value)
            .create(),
        });

        expect(container).toMatchSnapshot();

        expect(
          screen.queryByText(/Language for written communications/i)
        ).toBeInTheDocument();

        expect(screen.queryByText(label)).toBeInTheDocument();
      }
    );

    it.each([
      { label: "English", value: Language.english },
      { label: "Español", value: Language.spanish },
      { label: "Kreyòl Ayisyen", value: Language.haitian_creole },
      { label: "Português (Brasil)", value: Language.portuguese },
      { label: "中文", value: Language.chinese_simplified },
      { label: "Tiếng Việt", value: Language.vietnamese },
      { label: "Français", value: Language.french },
      {
        label: "English",
        value: Language.not_listed,
      },
    ])(
      "renders Review page with Part 1 language preference '$label' in document when '$value' is selected when IDV is enabled.",
      ({ label, value }) => {
        setFeatureFlags({
          enableMmgIDV: true,
          enableUniversalProfileIDV: true,
        });
        const { container } = setup({
          claim: new MockBenefitsApplicationBuilder()
            .part1Complete()
            .languagePreference(value)
            .create(),
        });

        expect(container).toMatchSnapshot();

        expect(
          screen.queryByText(/Language for written communications/i)
        ).toBeInTheDocument();

        expect(screen.queryByText(label)).toBeInTheDocument();
      }
    );
  });

  describe("when enableMmgIDV is enabled", () => {
    beforeEach(() => {
      setFeatureFlags({
        enableMmgIDV: true,
      });
    });

    it("renders 'Demographic details and preference' section", () => {
      const claim = new MockBenefitsApplicationBuilder().create();

      setup({ claim });

      expect(
        screen.getByText(/Demographic details and preferences/i)
      ).toBeInTheDocument();
    });

    describe("when IDV status is 'Verified'", () => {
      it("does not render the upload id summary section when applicant has a state ID", () => {
        const claim = new MockBenefitsApplicationBuilder()
          .complete()
          .isMmgVerified()
          .hasStateId()
          .create();

        setup({ claim });

        expect(
          screen.queryByText(/Upload identity document/i)
        ).not.toBeInTheDocument();
      });

      it("renders the upload id summary section when the applicant does not have a state ID", () => {
        const claim = new MockBenefitsApplicationBuilder()
          .complete()
          .isMmgVerified()
          .hasOtherId()
          .create();

        setup({ claim });

        expect(
          screen.queryByText(/Upload identity document/i)
        ).toBeInTheDocument();
      });
    });
  });

  describe("when enableMmgIDV is disabled", () => {
    beforeEach(() => {
      setFeatureFlags({
        enableMmgIDV: false,
      });
    });

    it("does not render 'Demographic details and preference' question section", () => {
      const claim = new MockBenefitsApplicationBuilder().create();

      setup({ claim });

      expect(
        screen.queryByText(/Demographic details and preferences/i)
      ).not.toBeInTheDocument();
    });
  });

  describe("when enableUniversalProfileIDV is enabled", () => {
    beforeEach(() => {
      setFeatureFlags({
        enableUniversalProfileIDV: true,
      });
    });

    describe("and profile data has been autofilled", () => {
      it("renders the autofill alert banner", () => {
        const claim = new MockBenefitsApplicationBuilder()
          .part1Complete()
          .populateNameFromMMG()
          .create();

        setup({ claim });

        expect(
          screen.getByRole("heading", { name: "Review autofilled details" })
        ).toBeInTheDocument();

        expect(
          screen.getByText(
            /Your MyMassGov profile may have been used to autofill some of these details/
          )
        ).toBeInTheDocument();
      });

      it("renders the autofill legend", () => {
        const claim = new MockBenefitsApplicationBuilder()
          .part1Complete()
          .populateNameFromMMG()
          .create();

        setup({ claim });

        const legend = screen
          .getByText(/Autofilled fields are marked with/)
          .closest("p");

        expect(legend).toHaveTextContent(
          "Autofilled fields are marked with: •"
        );
        expect(legend).toHaveAttribute("aria-hidden");
      });

      it("renders the autofill indicator on the relevant field", () => {
        const claim = new MockBenefitsApplicationBuilder()
          .part1Complete()
          .populateNameFromMMG()
          .create();

        setup({ claim });

        const heading = screen.getByRole("heading", {
          name: /Name \(autofilled with MyMassGov\)/,
        });

        expect(heading).toBeInTheDocument();

        const bullet = within(heading).getByText("•");
        expect(bullet.tagName).toBe("STRONG");
        expect(bullet).toHaveAttribute("aria-hidden", "true");
      });
    });

    it("does not render autofill indicators when part 1 has been submitted", () => {
      const claim = new MockBenefitsApplicationBuilder()
        .submitted()
        .populateNameFromMMG()
        .create();

      setup({ claim });

      expect(
        screen.queryByRole("heading", { name: "Review autofilled details" })
      ).not.toBeInTheDocument();
      expect(
        screen.queryByText(/Autofilled fields are marked with/)
      ).not.toBeInTheDocument();

      const heading = screen.getByRole("heading", {
        name: /Name/,
      });
      expect(heading).not.toHaveAccessibleName(/autofilled with MyMassGov/);
      expect(screen.queryByText("•")).not.toBeInTheDocument();
    });

    describe("and profile data has not been autofilled", () => {
      it("does not render the autofill alert banner", () => {
        const claim = new MockBenefitsApplicationBuilder()
          .part1Complete()
          .create();

        setup({ claim });

        expect(
          screen.queryByRole("heading", { name: "Review autofilled details" })
        ).not.toBeInTheDocument();
      });

      it("does not render the autofill legend", () => {
        const claim = new MockBenefitsApplicationBuilder()
          .part1Complete()
          .create();

        setup({ claim });

        expect(
          screen.queryByText(/Autofilled fields are marked with/)
        ).not.toBeInTheDocument();
      });

      it("does not render the autofill indicator on the relevant field", () => {
        const claim = new MockBenefitsApplicationBuilder()
          .part1Complete()
          .create();

        setup({ claim });

        const heading = screen.getByRole("heading", {
          name: /Name/,
        });

        expect(heading).not.toHaveAccessibleName(/autofilled with MyMassGov/);
        expect(screen.queryByText("•")).not.toBeInTheDocument();
      });
    });
  });

  describe("when enableUniversalProfileIDV is disabled and profile data has been autofilled", () => {
    beforeEach(() => {
      setFeatureFlags({
        enableUniversalProfileIDV: false,
      });
    });

    it("does not render the autofill alert banner", () => {
      const claim = new MockBenefitsApplicationBuilder()
        .part1Complete()
        .populateNameFromMMG()
        .create();

      setup({ claim });

      expect(
        screen.queryByRole("heading", { name: "Review autofilled details" })
      ).not.toBeInTheDocument();
    });

    it("does not render the autofill legend", () => {
      const claim = new MockBenefitsApplicationBuilder()
        .part1Complete()
        .populateNameFromMMG()
        .create();

      setup({ claim });

      expect(
        screen.queryByText(/Autofilled fields are marked with/)
      ).not.toBeInTheDocument();
    });

    it("does not render the autofill indicator on the relevant field", () => {
      const claim = new MockBenefitsApplicationBuilder()
        .part1Complete()
        .populateNameFromMMG()
        .create();

      setup({ claim });

      const heading = screen.getByRole("heading", {
        name: /Name/,
      });

      expect(heading).not.toHaveAccessibleName(/autofilled with MyMassGov/);
      expect(screen.queryByText("•")).not.toBeInTheDocument();
    });
  });

  // TODO (PFMLPB-24322):  https://lwd.atlassian.net/browse/PFMLPB-24322 Remove FF
  describe("when enableOccupationDataCollection is enabled", () => {
    beforeEach(() => {
      setFeatureFlags({
        enableOccupationDataCollection: true,
      });
    });

    describe("occupation 'Job type' section", () => {
      it("displays not answered when no industry_sector is set", () => {
        const claim = new MockBenefitsApplicationBuilder()
          .part1Complete()
          .occupationDataCollection("None")
          .create();
        setup({ claim });
        expect(screen.getByText(/Job type/)).toBeInTheDocument();
        expect(screen.getByText(/Not answered/)).toBeInTheDocument();
      });

      it("displays correct value when industry_sector is set", () => {
        const claim = new MockBenefitsApplicationBuilder()
          .part1Complete()
          .occupationDataCollection(IndustrySector.accommodationAndFoodServices)
          .create();
        setup({ claim });
        expect(screen.getByText(/Job type/)).toBeInTheDocument();
        expect(
          screen.getByText(IndustrySector.accommodationAndFoodServices)
        ).toBeInTheDocument();
      });
    });
  });

  // TODO (PFMLPB-24322):  https://lwd.atlassian.net/browse/PFMLPB-24322 Remove FF
  describe("when enableOccupationDataCollection is disabled", () => {
    beforeEach(() => {
      setFeatureFlags({
        enableOccupationDataCollection: false,
      });
    });

    it("occupation 'Job type' section not displayed", () => {
      const claim = new MockBenefitsApplicationBuilder()
        .part1Complete()
        .occupationDataCollection("None")
        .create();
      setup({ claim });
      expect(screen.queryByText(/Job type/)).not.toBeInTheDocument();
    });
  });
});
