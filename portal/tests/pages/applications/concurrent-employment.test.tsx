import { BasePortalError, ValidationError } from "src/errors";
import { MockBenefitsApplicationBuilder, renderPage } from "tests/test-utils";
import { screen, waitFor } from "@testing-library/react";

import BenefitsApplication from "src/models/BenefitsApplication";
import ConcurrentEmployment from "src/pages/applications/concurrent-employment";
import { setupBenefitsApplications } from "tests/test-utils/helpers";
import tracker from "src/services/tracker";
import userEvent from "@testing-library/user-event";

jest.mock("src/services/tracker");

const defaultClaim = new MockBenefitsApplicationBuilder().create();

const updateClaim = jest.fn(() => {
  return Promise.resolve();
});

const setup = (claim = defaultClaim, errors: BasePortalError[] = []) => {
  return renderPage(
    ConcurrentEmployment,
    {
      addCustomSetup: (appLogic) => {
        appLogic.errors = errors;
        setupBenefitsApplications(appLogic, [claim]);
        appLogic.benefitsApplications.update = updateClaim;
      },
    },
    { query: { claim_id: "mock_application_id" } }
  );
};

describe("ConcurrentEmployment", () => {
  it("renders page", () => {
    setup();
    expect(screen.getByText("Employment information")).toBeInTheDocument();
  });

  it("displays has concurrent employment question", () => {
    setup();
    expect(
      screen.getByText("Do you currently work for any additional employers?")
    ).toBeInTheDocument();
    expect(screen.getByText("Yes")).toBeInTheDocument();
    expect(screen.getByText("No")).toBeInTheDocument();
  });

  it("displays an error when concurrent employment question is not answered", () => {
    const errors = [
      new ValidationError([
        {
          field: "has_concurrent_employers",
          message: "has_concurrent_employers is required",
          type: "required",
          rule: "conditional",
          namespace: "applications",
        },
      ]),
    ];
    setup(defaultClaim, errors);

    expect(
      screen.queryByText(
        "Select yes if you currently work for any additional employers. Select no if you only work for one employer."
      )
    ).toBeInTheDocument();
  });

  it("calls updateClaim with updated has_concurrent_employer", async () => {
    setup();
    await userEvent.click(
      screen.getByRole("radio", {
        name: "Yes",
      })
    );

    await userEvent.click(
      screen.getByRole("button", { name: "Save and continue" })
    );

    await waitFor(() => {
      expect(updateClaim).toHaveBeenCalledWith("mock_application_id", {
        has_concurrent_employers: true,
        hours_worked_per_week_all_employers: null,
      });
    });
  });

  it("doesn't show the hours worked per week all employers question if the claimant doesn't have concurrent employers", () => {
    setup();

    expect(
      screen.queryByText(
        "How many total hours do you work each week on average across all of your employers?"
      )
    ).not.toBeInTheDocument();
    expect(
      screen.queryByText(
        "applying for paid family or medical leave if you have multiple employers."
      )
    ).not.toBeInTheDocument();
    expect(screen.queryByText("Hours")).not.toBeInTheDocument();
    expect(screen.queryByText("Minutes")).not.toBeInTheDocument();
  });

  it("shows the hours worked per week all employers question", async () => {
    setup();
    await userEvent.click(
      screen.getByRole("radio", {
        name: "Yes",
      })
    );

    expect(
      screen.getByText(
        "How many total hours do you work each week on average across all of your employers?"
      )
    ).toBeInTheDocument();
    expect(
      screen.getByRole("link", {
        name: "applying for paid family or medical leave if you have multiple employers.",
      })
    ).toHaveAttribute("href");
    expect(screen.getByText("Hours")).toBeInTheDocument();
    expect(screen.getByText("Minutes")).toBeInTheDocument();
  });

  it("updates hours worked per week all employers", async () => {
    setup();
    await userEvent.click(
      screen.getByRole("radio", {
        name: "Yes",
      })
    );

    await userEvent.type(screen.getByLabelText("Hours"), "42");

    await userEvent.selectOptions(screen.getByLabelText("Minutes"), "30");

    await userEvent.click(
      screen.getByRole("button", { name: "Save and continue" })
    );

    await waitFor(() => {
      expect(updateClaim).toHaveBeenCalledWith("mock_application_id", {
        has_concurrent_employers: true,
        hours_worked_per_week_all_employers: 42.5,
      });
    });
  });

  it("persists hours worked per week input", () => {
    setup({
      ...defaultClaim,
      hours_worked_per_week_all_employers: 45.75,
      has_concurrent_employers: true,
    } as BenefitsApplication);

    expect(screen.getByLabelText("Hours")).toHaveValue("45");
    expect(screen.getByLabelText("Minutes")).toHaveValue("45");
  });

  it("handles clearing the hours worked per week input", async () => {
    setup();
    await userEvent.click(
      screen.getByRole("radio", {
        name: "Yes",
      })
    );

    await userEvent.type(screen.getByLabelText("Hours"), "42");
    await userEvent.clear(screen.getByLabelText("Hours"));

    await userEvent.click(
      screen.getByRole("button", { name: "Save and continue" })
    );

    await waitFor(() => {
      expect(updateClaim).toHaveBeenCalledWith("mock_application_id", {
        has_concurrent_employers: true,
        hours_worked_per_week_all_employers: null,
      });
    });
  });

  it("updates hours worked per week all employers with null when previous value is deleted", async () => {
    setup();

    await userEvent.click(
      screen.getByRole("radio", {
        name: "Yes",
      })
    );

    await userEvent.type(screen.getByLabelText("Hours"), "40");

    await userEvent.click(
      screen.getByRole("button", { name: "Save and continue" })
    );

    await waitFor(() => {
      expect(updateClaim).toHaveBeenCalledWith("mock_application_id", {
        has_concurrent_employers: true,
        hours_worked_per_week_all_employers: 40,
      });
    });

    await userEvent.clear(screen.getByLabelText("Hours"));

    await userEvent.click(
      screen.getByRole("button", { name: "Save and continue" })
    );

    await waitFor(() => {
      expect(updateClaim).toHaveBeenCalledWith("mock_application_id", {
        has_concurrent_employers: true,
        hours_worked_per_week_all_employers: null,
      });
    });
  });

  it("shows the correct error message when hours_worked_per_week_all_employers is more than 168", () => {
    const errors = [
      new ValidationError([
        {
          field: "hours_worked_per_week_all_employers",
          message: "hours_worked_per_week_all_employers must be 168 or fewer",
          type: "maximum",
          namespace: "applications",
        },
      ]),
    ];
    setup(
      {
        ...defaultClaim,
        has_concurrent_employers: true,
      } as BenefitsApplication,
      errors
    );

    expect(
      screen.getByText(
        "Number of hours you work each week must be 168 or fewer."
      )
    ).toBeInTheDocument();
  });

  describe("shows the correct error message when hours_worked_per_week_all_employers is less than hours_worked_per_week", () => {
    it("when only the employer FEIN is avaialble", () => {
      const employer_fein = "12-1234567";
      const hours_worked_per_week = 30;
      const errors = [
        new ValidationError([
          {
            field: "hours_worked_per_week_all_employers",
            message:
              "hours_worked_per_week_all_employers must be greater than the hours of the main employer on the claim",
            type: "minimum_ein",
            namespace: "applications",
            extra: { employer_fein, hours_worked_per_week },
          },
        ]),
      ];
      setup(
        {
          ...defaultClaim,
          has_concurrent_employers: true,
          hours_worked_per_week,
          employer_fein,
        } as BenefitsApplication,
        errors
      );

      const alert = screen.getByRole("alert");
      expect(alert).toBeInTheDocument();
      expect(alert.innerHTML).toBe(
        "The average weekly hours across all your employers must exceed <var>30</var> hours, the amount you have indicated you work at your employer (EIN #<var>12‑1234567</var>)."
      );
    });

    it("when both the employer DBA & FEIN is avaialble", () => {
      const employer_dba = "Acme Corp";
      const employer_fein = "12-1234567";
      const hours_worked_per_week = 30;
      const errors = [
        new ValidationError([
          {
            field: "hours_worked_per_week_all_employers",
            message:
              "hours_worked_per_week_all_employers must be greater than the hours of the main employer on the claim",
            type: "minimum_dba",
            namespace: "applications",
            extra: { employer_dba, employer_fein, hours_worked_per_week },
          },
        ]),
      ];
      setup(
        {
          ...defaultClaim,
          has_concurrent_employers: true,
          hours_worked_per_week,
          employer_fein,
        } as BenefitsApplication,
        errors
      );

      const alert = screen.getByRole("alert");
      expect(alert).toBeInTheDocument();
      expect(alert.innerHTML).toBe(
        "The average weekly hours across all your employers must exceed <var>30</var> hours, the amount you have indicated you work at <var>Acme Corp</var> (EIN #<var>12‑1234567</var>)."
      );
    });

    it("when only the employer DBA & FEIN are invalid", () => {
      const employer_fein = "000000000";
      const hours_worked_per_week = 30;
      const errors = [
        new ValidationError([
          {
            field: "hours_worked_per_week_all_employers",
            message:
              "hours_worked_per_week_all_employers must be greater than the hours of the main employer on the claim",
            type: "minimum",
            namespace: "applications",
            extra: { hours_worked_per_week },
          },
        ]),
      ];
      setup(
        {
          ...defaultClaim,
          has_concurrent_employers: true,
          hours_worked_per_week,
          employer_fein,
        } as BenefitsApplication,
        errors
      );

      const alert = screen.getByRole("alert");
      expect(alert).toBeInTheDocument();
      expect(alert.innerHTML).toBe(
        "The average weekly hours across all your employers must exceed <var>30</var> hours, the amount you have indicated you work at your employer."
      );
    });
  });

  it("shows the correct error message when hours_worked_per_week_all_employers is left empty", () => {
    const errors = [
      new ValidationError([
        {
          field: "hours_worked_per_week_all_employers",
          message: "hours_worked_per_week_all_employers is required",
          type: "required",
          namespace: "applications",
        },
      ]),
    ];
    setup(
      {
        ...defaultClaim,
        has_concurrent_employers: true,
      } as BenefitsApplication,
      errors
    );

    expect(
      screen.getByText("Enter the hours you work across all of your employers.")
    ).toBeInTheDocument();
  });

  it("tracks when a user clicks submit", async () => {
    setup();

    await userEvent.click(
      screen.getByRole("button", { name: "Save and continue" })
    );

    await waitFor(() => {
      expect(tracker.trackEvent).toHaveBeenCalledTimes(1);
      expect(tracker.trackEvent).toHaveBeenCalledWith(
        "Concurrent employment submit clicked",
        { hasConcurrentEmployers: null, percentThisEmployer: 100 }
      );
    });
  });

  it("tracks when a user clicks submit and they have concurrent employers", async () => {
    setup();
    await userEvent.click(
      screen.getByRole("radio", {
        name: "Yes",
      })
    );
    await userEvent.click(
      screen.getByRole("button", { name: "Save and continue" })
    );

    await waitFor(() => {
      expect(tracker.trackEvent).toHaveBeenCalledTimes(1);
      expect(tracker.trackEvent).toHaveBeenCalledWith(
        "Concurrent employment submit clicked",
        { hasConcurrentEmployers: true, percentThisEmployer: 100 }
      );
    });
  });

  it("tracks when a user clicks submit and they don't have concurrent employers", async () => {
    setup();
    await userEvent.click(
      screen.getByRole("radio", {
        name: "No",
      })
    );
    await userEvent.click(
      screen.getByRole("button", { name: "Save and continue" })
    );

    await waitFor(() => {
      expect(tracker.trackEvent).toHaveBeenCalledTimes(1);
      expect(tracker.trackEvent).toHaveBeenCalledWith(
        "Concurrent employment submit clicked",
        { hasConcurrentEmployers: false, percentThisEmployer: 100 }
      );
    });
  });

  it("tracks the percent of time a user worked for the employer they're applying with", async () => {
    const targetPercent = 50;
    setup({
      ...defaultClaim,
      hours_worked_per_week: 20,
    } as BenefitsApplication);

    await userEvent.click(
      screen.getByRole("radio", {
        name: "Yes",
      })
    );
    await userEvent.type(screen.getByLabelText("Hours"), "40");
    await userEvent.click(
      screen.getByRole("button", { name: "Save and continue" })
    );

    await waitFor(() => {
      expect(tracker.trackEvent).toHaveBeenCalledTimes(1);
      expect(tracker.trackEvent).toHaveBeenCalledWith(
        "Concurrent employment submit clicked",
        {
          hasConcurrentEmployers: true,
          percentThisEmployer: targetPercent,
        }
      );
    });
  });

  it("clears hours_worked_per_week_all_employers when has_concurrent_employers is set from yes to no", async () => {
    setup({
      ...defaultClaim,
      hours_worked_per_week: 20,
      has_concurrent_employers: true,
      hours_worked_per_week_all_employers: 55,
    } as BenefitsApplication);

    await userEvent.click(screen.getByRole("radio", { name: "No" }));
    await userEvent.click(
      screen.getByRole("button", { name: "Save and continue" })
    );

    await waitFor(() => {
      expect(updateClaim).toHaveBeenCalledWith("mock_application_id", {
        has_concurrent_employers: false,
        hours_worked_per_week_all_employers: null,
      });
    });
  });

  it("tracks 100% this employer when has_concurrent_employers is set from yes to no", async () => {
    setup({
      ...defaultClaim,
      hours_worked_per_week: 20,
      has_concurrent_employers: true,
      hours_worked_per_week_all_employers: 55,
    } as BenefitsApplication);

    await userEvent.click(screen.getByRole("radio", { name: "No" }));
    await userEvent.click(
      screen.getByRole("button", { name: "Save and continue" })
    );

    await waitFor(() => {
      expect(tracker.trackEvent).toHaveBeenCalledTimes(1);
      expect(tracker.trackEvent).toHaveBeenCalledWith(
        "Concurrent employment submit clicked",
        {
          hasConcurrentEmployers: false,
          percentThisEmployer: 100,
        }
      );
    });
  });
});
