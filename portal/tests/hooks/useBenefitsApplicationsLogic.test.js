import { BadRequestError, NotFoundError } from "src/errors";
import BenefitsApplication, {
  BenefitsApplicationStatus,
} from "src/models/BenefitsApplication";
import { act, renderHook, waitFor } from "@testing-library/react";
import {
  completeClaimMock,
  createClaimMock,
  getClaimMock,
  getClaimMockApplicationId,
  getClaimsMock,
  submitClaimMock,
  submitCustomerPaymentPreferenceMock,
  updateClaimMock,
} from "src/api/BenefitsApplicationsApi";

import ApiResourceCollection from "src/models/ApiResourceCollection";
import { MockBenefitsApplicationBuilder } from "tests/test-utils";
import { UserProfileUpdate } from "src/models/User";
import { isFeatureEnabled } from "src/services/featureFlags";
import { mockRouter } from "next/router";
import { mockUpdateUserProfile } from "src/api/UsersApi";
import routes from "src/routes";
import tracker from "src/services/tracker";
import useBenefitsApplicationsLogic from "src/hooks/useBenefitsApplicationsLogic";
import useErrorsLogic from "src/hooks/useErrorsLogic";
import usePortalFlow from "src/hooks/usePortalFlow";
import useUsersLogic from "src/hooks/useUsersLogic";

jest.mock("../../src/api/BenefitsApplicationsApi");
jest.mock("../../src/services/tracker");
jest.mock("../../src/api/UsersApi");
jest.mock("src/services/featureFlags", () => ({
  isFeatureEnabled: jest.fn(),
}));

describe("useBenefitsApplicationsLogic", () => {
  let applicationId, claimsLogic, errorsLogic, portalFlow, usersLogic;

  function setup() {
    renderHook(() => {
      portalFlow = usePortalFlow();
      errorsLogic = useErrorsLogic({ portalFlow });
      usersLogic = useUsersLogic({ errorsLogic, isLoggedIn: true, portalFlow });
      claimsLogic = useBenefitsApplicationsLogic({
        errorsLogic,
        portalFlow,
        usersLogic,
      });
    });
  }

  beforeEach(() => {
    applicationId = "mock-application-id";
    mockRouter.pathname = routes.getReady;
  });

  afterEach(() => {
    errorsLogic = null;
    claimsLogic = null;
    portalFlow = null;
  });

  it("sets initial claims data to empty collection", () => {
    setup();

    expect(claimsLogic.benefitsApplications).toBeInstanceOf(
      ApiResourceCollection
    );
    expect(claimsLogic.benefitsApplications.items).toHaveLength(0);
  });

  describe("hasLoadedBenefitsApplicationAndWarnings", () => {
    beforeEach(() => {
      // Make sure the ID we're loading matches what the API will return to us so caching works as
      applicationId = getClaimMockApplicationId;

      setup();
    });

    it("returns true when a claim and its warnings are loaded", async () => {
      expect(
        claimsLogic.hasLoadedBenefitsApplicationAndWarnings(applicationId)
      ).toBe(false);

      await act(async () => {
        await claimsLogic.load(applicationId);
      });

      expect(
        claimsLogic.hasLoadedBenefitsApplicationAndWarnings(applicationId)
      ).toBe(true);
    });
  });

  describe("load", () => {
    beforeEach(() => {
      // Make sure the ID we're loading matches what the API will return to us so caching works as
      applicationId = getClaimMockApplicationId;

      setup();
    });

    it("asynchronously fetches a claim and adds it to claims collection", async () => {
      await act(async () => {
        await claimsLogic.load(applicationId);
      });

      const claims = claimsLogic.benefitsApplications.items;

      expect(claims).toHaveLength(1);
      expect(claims[0]).toBeInstanceOf(BenefitsApplication);
      expect(getClaimMock).toHaveBeenCalledTimes(1);
    });

    it("stores the claim's warnings in warningsLists", async () => {
      await act(async () => {
        await claimsLogic.load(applicationId);
      });

      expect(claimsLogic.warningsLists).toEqual({
        [applicationId]: [],
      });
    });

    it("only makes api request if claim has not been loaded", async () => {
      await act(async () => {
        await claimsLogic.load(applicationId);
      });
      await act(async () => {
        await claimsLogic.load(applicationId);
      });

      expect(getClaimMock).toHaveBeenCalledTimes(1);
    });

    it("makes API request when claim is loaded but its warnings haven't been stored in warningsLists", async () => {
      await act(async () => {
        await claimsLogic.loadPage();
      });
      await act(async () => {
        await claimsLogic.load(applicationId);
      });

      expect(getClaimMock).toHaveBeenCalledTimes(1);
    });

    it("clears prior errors", async () => {
      act(() => {
        errorsLogic.setErrors([new Error()]);
      });

      await act(async () => {
        await claimsLogic.load(applicationId);
      });

      expect(errorsLogic.errors).toHaveLength(0);
    });

    describe("when request is unsuccessful", () => {
      beforeEach(() => {
        jest.spyOn(console, "error").mockImplementationOnce(jest.fn());
      });

      it("redirects to /applications page if claim wasn't found", async () => {
        getClaimMock.mockImplementationOnce(() => {
          throw new NotFoundError();
        });

        await act(async () => {
          await claimsLogic.load(applicationId);
        });

        expect(mockRouter.push).toHaveBeenCalledWith(routes.applications.index);
      });

      it("catches exceptions thrown from the API module", async () => {
        getClaimMock.mockImplementationOnce(() => {
          throw new BadRequestError();
        });

        await act(async () => {
          await claimsLogic.load(applicationId);
        });

        expect(errorsLogic.errors[0].name).toEqual("BadRequestError");
      });
    });
  });

  describe("loadPage", () => {
    beforeEach(() => {
      setup();
    });

    it("asynchronously fetches all claims and adds to claims collection", async () => {
      await act(async () => {
        await claimsLogic.loadPage();
      });

      expect(claimsLogic.benefitsApplications.items[0]).toBeInstanceOf(
        BenefitsApplication
      );
      expect(getClaimsMock).toHaveBeenCalled();
    });

    it("only makes api request if all claims have not been loaded", async () => {
      await act(() => {
        const claim = new BenefitsApplication({
          application_id: getClaimMockApplicationId,
        });
        getClaimsMock.mockImplementationOnce(() => {
          return {
            claim,
            paginationMeta: {
              page_offset: 1,
            },
          };
        });
      });

      await act(async () => {
        // this should make an API request since ALL claims haven't been loaded yet
        await claimsLogic.loadPage();
      });

      await act(async () => {
        // but this shouldn't, since we've already loaded all claims
        await claimsLogic.loadPage();
      });

      expect(getClaimsMock).toHaveBeenCalledTimes(1);
    });

    it("clears prior errors", async () => {
      act(() => {
        errorsLogic.setErrors([new Error()]);
      });

      await act(async () => {
        await claimsLogic.loadPage();
      });

      expect(errorsLogic.errors).toHaveLength(0);
    });

    it("makes api request when page_offset is changed", async () => {
      await act(() => {
        const claim = new BenefitsApplication({
          application_id: getClaimMockApplicationId,
        });
        getClaimsMock.mockImplementationOnce(() => {
          return {
            claim,
            paginationMeta: {
              page_offset: 1,
            },
          };
        });
      });

      await act(async () => {
        // makes initial api request
        await claimsLogic.loadPage();
      });

      await act(async () => {
        // does not make this call since page_offset is the same
        await claimsLogic.loadPage();
      });

      await act(async () => {
        // makes api call due to page_offset changing
        await claimsLogic.loadPage(2);
      });

      await waitFor(async () => {
        await expect(getClaimsMock).toHaveBeenCalledTimes(2);
      });
    });

    describe("when request is unsuccessful", () => {
      beforeEach(() => {
        jest.spyOn(console, "error").mockImplementationOnce(jest.fn());
      });

      it("catches exceptions thrown from the API module", async () => {
        getClaimsMock.mockImplementationOnce(() => {
          throw new BadRequestError();
        });

        await act(async () => {
          await claimsLogic.loadPage();
        });

        expect(errorsLogic.errors[0].name).toEqual("BadRequestError");
        expect(mockRouter.push).not.toHaveBeenCalled();
      });
    });
  });

  describe("create", () => {
    beforeEach(() => {
      mockRouter.pathname = routes.applications.start;
      setup();
    });

    it("sends API request", async () => {
      await act(async () => {
        await claimsLogic.create();
      });

      expect(createClaimMock).toHaveBeenCalled();
    });

    it("routes to claim checklist page when the request succeeds", async () => {
      await act(async () => {
        await claimsLogic.create();
      });

      expect(mockRouter.push).toHaveBeenCalledWith(
        expect.stringContaining(
          `${routes.applications.checklist}?claim_id=mock`
        )
      );
    });

    it("clears prior errors", async () => {
      act(() => {
        errorsLogic.setErrors([new Error()]);
      });

      await act(async () => {
        await claimsLogic.create();
      });

      expect(errorsLogic.errors).toHaveLength(0);
    });

    it("catches exceptions thrown from the API module", async () => {
      jest.spyOn(console, "error").mockImplementationOnce(jest.fn());

      createClaimMock.mockImplementationOnce(() => {
        throw new BadRequestError();
      });

      await act(async () => {
        await claimsLogic.create();
      });

      expect(errorsLogic.errors[0].name).toEqual("BadRequestError");
      expect(mockRouter.push).not.toHaveBeenCalled();
    });

    it("updates state to force a new API request when needed", async () => {
      const claim = new BenefitsApplication({ application_id: "12345" });
      createClaimMock.mockResolvedValueOnce({
        claim,
        success: true,
      });
      await act(async () => {
        await claimsLogic.loadPage();
      });
      expect(getClaimsMock).toHaveBeenCalledTimes(1);

      expect(claimsLogic.isLoadingClaims).toBe(false);
      await act(async () => {
        await claimsLogic.create();
      });
      expect(claimsLogic.isLoadingClaims).toBeUndefined();

      await act(async () => {
        await claimsLogic.loadPage();
      });

      expect(getClaimsMock).toHaveBeenCalledTimes(2);
    });

    describe("when claims have previously been loaded", () => {
      let claim, existingClaims;

      beforeEach(async () => {
        existingClaims = new ApiResourceCollection("application_id", [
          new BenefitsApplication({ application_id: "1" }),
          new BenefitsApplication({ application_id: "2" }),
        ]);

        getClaimsMock.mockImplementationOnce(() => {
          return {
            success: true,
            claims: existingClaims,
          };
        });

        claim = new BenefitsApplication({ application_id: "12345" });

        createClaimMock.mockResolvedValueOnce({
          claim,
          success: true,
        });

        await act(async () => {
          await claimsLogic.loadPage();
          await claimsLogic.create();
        });
      });

      it("doesn't affect existing claims", () => {
        expect.assertions(existingClaims.items.length);
        existingClaims.items.forEach((existingClaim) => {
          expect(claimsLogic.benefitsApplications.items).toContain(
            existingClaim
          );
        });
      });
    });
  });

  describe("when claims have been loaded or a new claim was created", () => {
    describe("complete", () => {
      beforeEach(async () => {
        mockRouter.pathname = routes.applications.review;
        setup();
        const claim = new BenefitsApplication({
          application_id: applicationId,
        });
        createClaimMock.mockResolvedValueOnce({
          claim,
          success: true,
        });

        await act(async () => {
          await claimsLogic.create();
        });

        mockRouter.push.mockClear();
      });

      it("completes the claim", async () => {
        await act(async () => {
          await claimsLogic.complete(applicationId);
        });

        const claim = claimsLogic.benefitsApplications.getItem(applicationId);

        expect(completeClaimMock).toHaveBeenCalledWith(applicationId, false);
        expect(claim.status).toBe(BenefitsApplicationStatus.completed);
      });

      it("clears prior errors", async () => {
        act(() => {
          errorsLogic.setErrors([new Error()]);
        });

        await act(async () => {
          await claimsLogic.complete(applicationId);
        });

        expect(errorsLogic.errors).toHaveLength(0);
      });

      it("routes to claim success page when the request succeeds", async () => {
        await act(async () => {
          await claimsLogic.complete(applicationId);
        });

        expect(mockRouter.push).toHaveBeenCalledWith(
          expect.stringContaining(
            `${routes.applications.success}?claim_id=${applicationId}`
          )
        );
      });

      it("catches exceptions thrown from the API module", async () => {
        jest.spyOn(console, "error").mockImplementationOnce(jest.fn());

        completeClaimMock.mockImplementationOnce(() => {
          throw new BadRequestError();
        });

        await act(async () => {
          await claimsLogic.complete(applicationId);
        });

        expect(errorsLogic.errors[0].name).toEqual("BadRequestError");
        expect(mockRouter.push).not.toHaveBeenCalled();
      });
    });

    describe("update", () => {
      const patchData = {
        first_name: "Bud",
        last_name: null,
      };

      beforeEach(() => {
        const claim = new BenefitsApplication({
          application_id: applicationId,
        });
        createClaimMock.mockResolvedValueOnce({
          claim,
          success: true,
        });
      });

      it("updates claim and redirects to nextPage when successful", async () => {
        mockRouter.pathname = routes.applications.race;

        setup();

        await act(async () => {
          await claimsLogic.create();
        });

        await act(async () => {
          await claimsLogic.update(applicationId, patchData);
        });

        const claim = claimsLogic.benefitsApplications.getItem(applicationId);

        expect(claim).toBeInstanceOf(BenefitsApplication);
        expect(claim).toEqual(expect.objectContaining(patchData));
        expect(updateClaimMock).toHaveBeenCalled();
        expect(mockRouter.push).toHaveBeenCalledWith(
          expect.stringContaining(
            `${routes.applications.checklist}?claim_id=${claim.application_id}`
          )
        );
      });

      it("clears prior errors", async () => {
        mockRouter.pathname = routes.applications.name;
        setup();

        await act(async () => {
          await claimsLogic.create();

          errorsLogic.setErrors([new Error()]);
        });

        await act(async () => {
          await claimsLogic.update(applicationId, patchData);
        });

        expect(errorsLogic.errors).toHaveLength(0);
      });

      it("does NOT proceed to the next page when goToNext is false and enableMmgIDV is on", async () => {
        const patchData = { first_name: "NewFirstName" };

        setup();

        jest
          .spyOn(claimsLogic.benefitsApplications, "getItem")
          .mockReturnValue({
            application_id: applicationId,
            first_name: "OldFirstName",
            mmg_idv_status: "Verified",
          });

        isFeatureEnabled.mockImplementation((flag) => flag === "enableMmgIDV");

        const goToNextPageMock = jest.spyOn(portalFlow, "goToNextPage");

        await act(async () => {
          await claimsLogic.update(applicationId, patchData, false); // goToNext = false
        });

        expect(goToNextPageMock).not.toHaveBeenCalled(); // Ensure navigation is skipped
      });

      describe("updateAndIgnoreErrors", () => {
        const patchData = {
          first_name: "Bud",
          last_name: null,
        };
        const applicationId = "mock-application-id";
        let claim;

        beforeEach(() => {
          claim = new BenefitsApplication({
            application_id: applicationId,
          });

          createClaimMock.mockResolvedValueOnce({
            claim,
            success: true,
          });
        });

        it("updates claim and redirects to nextPage when selects yes", async () => {
          mockRouter.pathname = routes.applications.useProfile;

          setup();

          await act(async () => {
            await claimsLogic.create();
          });

          await act(async () => {
            await claimsLogic.updateAndIgnoreErrors(
              applicationId,
              patchData,
              claim
            );
          });

          const updatedClaim =
            claimsLogic.benefitsApplications.getItem(applicationId);

          expect(updatedClaim).toBeInstanceOf(BenefitsApplication);
          expect(updatedClaim).toEqual(expect.objectContaining(patchData));
          expect(updateClaimMock).toHaveBeenCalled();
          expect(mockRouter.push).toHaveBeenCalledWith(
            expect.stringContaining(
              `${routes.applications.checklist}?claim_id=${updatedClaim.application_id}`
            )
          );
        });
      });

      describe("when request is unsuccessful", () => {
        beforeEach(() => {
          jest.spyOn(console, "error").mockImplementationOnce(jest.fn());
        });

        it("updates the local claim and warningsList if response only included warnings", async () => {
          mockRouter.pathname = routes.applications.name;
          const claimResponse = new MockBenefitsApplicationBuilder()
            .id(applicationId)
            .create();
          const last_name = "Updated from API";

          setup();

          await act(async () => {
            await claimsLogic.create();
          });
          mockRouter.push.mockClear();

          updateClaimMock.mockResolvedValueOnce({
            claim: { ...claimResponse, last_name },
            errors: [],
            warnings: [{ field: "first_name", type: "required" }],
            // Responses with only warnings receive a 200 status
            success: true,
          });

          await act(async () => {
            await claimsLogic.update(applicationId, patchData);
          });

          const claim = claimsLogic.benefitsApplications.getItem(applicationId);

          expect(claim.last_name).toBe(last_name);
          expect(claimsLogic.warningsLists[applicationId]).toEqual([
            { field: "first_name", type: "required" },
          ]);
        });

        it("reports warnings for fields on the Name page", async () => {
          mockRouter.pathname = routes.applications.name;

          setup();

          await act(async () => {
            await claimsLogic.create();
          });

          updateClaimMock.mockResolvedValueOnce({
            claim: new MockBenefitsApplicationBuilder()
              .id(applicationId)
              .create(),
            errors: [],
            warnings: [
              { field: "first_name", type: "required" },
              { field: "last_name", type: "required" },
              { field: "date_of_birth", type: "required" },
            ],
            // Responses with only warnings receive a 200 status
            success: true,
          });

          await act(async () => {
            await claimsLogic.update(applicationId, patchData);
          });

          const errors = errorsLogic.errors;
          const issues = errors[0].issues;
          const errorFields = issues.map((error) => error.field);

          expect(issues).toHaveLength(2);
          expect(errorFields).toContain("first_name");
          expect(errorFields).toContain("last_name");
        });

        it("reports warnings for applicable rules on the Intermittent Leave page", async () => {
          mockRouter.pathname = routes.applications.leavePeriodIntermittent;
          setup();

          await act(async () => {
            await claimsLogic.create();
          });

          updateClaimMock.mockResolvedValueOnce({
            claim: new MockBenefitsApplicationBuilder()
              .id(applicationId)
              .create(),
            errors: [],
            warnings: [
              { rule: "disallow_hybrid_intermittent_leave" },
              { field: "tax_identifier", type: "required" },
            ],
            // Responses with only warnings receive a 200 status
            success: true,
          });

          await act(async () => {
            await claimsLogic.update(applicationId, patchData);
          });

          const errors = errorsLogic.errors;

          expect(errors).toHaveLength(1);
          expect(errors[0].issues[0].rule).toBe(
            "disallow_hybrid_intermittent_leave"
          );
        });

        it("catches exceptions thrown from the API module", async () => {
          mockRouter.pathname = routes.applications.name;
          setup();

          await act(async () => {
            await claimsLogic.create();
          });
          mockRouter.push.mockClear();

          updateClaimMock.mockImplementationOnce(() => {
            throw new BadRequestError();
          });

          await act(async () => {
            await claimsLogic.update(applicationId, patchData);
          });

          expect(errorsLogic.errors[0].name).toEqual("BadRequestError");
          expect(mockRouter.push).not.toHaveBeenCalled();
        });
      });

      describe("For the IDV Pilot, update mmg_idv_status for any changes to MMG identity proofed fields", () => {
        describe("name fields or DOB are changed", () => {
          beforeEach(() => {
            jest.clearAllMocks();
            isFeatureEnabled.mockImplementation(
              (flag) => flag === "enableMmgIDV"
            );
            setup();
          });
          it("updates mmg_idv_status to 'Unverified' when first name is edited", async () => {
            const patchData = { first_name: "NewFirstName" };

            jest
              .spyOn(claimsLogic.benefitsApplications, "getItem")
              .mockReturnValue({
                application_id: applicationId,
                first_name: "OldFirstName", // Existing value
                mmg_idv_status: "Verified",
              });

            await act(async () => {
              await claimsLogic.update(applicationId, patchData);
            });

            expect(updateClaimMock).toHaveBeenCalledWith(applicationId, {
              ...patchData,
              mmg_idv_status: "Unverified",
            });
          });

          it("does NOT update mmg_idv_status if first name remains the same", async () => {
            const patchData = { first_name: "SameFirstName" };

            jest
              .spyOn(claimsLogic.benefitsApplications, "getItem")
              .mockReturnValue({
                application_id: applicationId,
                first_name: "SameFirstName", // Same value as patchData
                mmg_idv_status: "Verified",
              });

            await act(async () => {
              await claimsLogic.update(applicationId, patchData);
            });

            expect(updateClaimMock).toHaveBeenCalledWith(
              applicationId,
              patchData
            );
            expect(patchData).not.toHaveProperty("mmg_idv_status"); // No change
          });

          it("updates mmg_idv_status to 'Unverified' when last name is edited", async () => {
            const patchData = { last_name: "NewLastName" };

            jest
              .spyOn(claimsLogic.benefitsApplications, "getItem")
              .mockReturnValue({
                application_id: applicationId,
                last_name: "OldLastName", // Existing value
                mmg_idv_status: "Verified",
              });

            await act(async () => {
              await claimsLogic.update(applicationId, patchData);
            });

            expect(updateClaimMock).toHaveBeenCalledWith(applicationId, {
              ...patchData,
              mmg_idv_status: "Unverified",
            });
          });

          it("does NOT update mmg_idv_status if last name remains the same", async () => {
            const patchData = { last_name: "SameLastName" };

            jest
              .spyOn(claimsLogic.benefitsApplications, "getItem")
              .mockReturnValue({
                application_id: applicationId,
                last_name: "SameLastName", // Same as patchData
                mmg_idv_status: "Verified",
              });

            await act(async () => {
              await claimsLogic.update(applicationId, patchData);
            });

            expect(updateClaimMock).toHaveBeenCalledWith(
              applicationId,
              patchData
            );
            expect(patchData).not.toHaveProperty("mmg_idv_status"); // No change
          });

          it("does NOT update mmg_idv_status if middle name is changed", async () => {
            const patchData = { middle_name: "NewMiddleName" };

            jest
              .spyOn(claimsLogic.benefitsApplications, "getItem")
              .mockReturnValue({
                application_id: applicationId,
                middle_name: "OldMiddleName", // Existing value
                first_name: "SameFirstName",
                last_name: "SameLastName",
                date_of_birth: "1985-01-01",
                mmg_idv_status: "Verified",
              });

            await act(async () => {
              await claimsLogic.update(applicationId, patchData);
            });

            expect(updateClaimMock).toHaveBeenCalledWith(
              applicationId,
              patchData
            );
            expect(patchData).not.toHaveProperty("mmg_idv_status"); // No change
          });

          it("updates mmg_idv_status to 'Unverified' when date of birth is edited", async () => {
            const patchData = { date_of_birth: "2000-01-01" };

            jest
              .spyOn(claimsLogic.benefitsApplications, "getItem")
              .mockReturnValue({
                application_id: applicationId,
                date_of_birth: "1985-01-01", // Existing value
                mmg_idv_status: "Verified",
              });

            await act(async () => {
              await claimsLogic.update(applicationId, patchData);
            });

            expect(updateClaimMock).toHaveBeenCalledWith(applicationId, {
              ...patchData,
              mmg_idv_status: "Unverified",
            });
          });

          it("does NOT update mmg_idv_status if date of birth remains the same", async () => {
            const patchData = { date_of_birth: "1985-01-01" };

            jest
              .spyOn(claimsLogic.benefitsApplications, "getItem")
              .mockReturnValue({
                application_id: applicationId,
                date_of_birth: "1985-01-01", // Same as patchData
                mmg_idv_status: "Verified",
              });

            await act(async () => {
              await claimsLogic.update(applicationId, patchData);
            });

            expect(updateClaimMock).toHaveBeenCalledWith(
              applicationId,
              patchData
            );
            expect(patchData).not.toHaveProperty("mmg_idv_status"); // No change
          });

          it("updates mmg_idv_status to 'Unverified' when all fields are changed in a full patchData object", async () => {
            const patchData = {
              first_name: "NewFirstName",
              middle_name: "OldMiddleName",
              last_name: "NewLastName",
            };

            jest
              .spyOn(claimsLogic.benefitsApplications, "getItem")
              .mockReturnValue({
                application_id: applicationId,
                first_name: "OldFirstName",
                middle_name: "OldMiddleName",
                last_name: "OldLastName",
                mmg_idv_status: "Verified",
              });

            await act(async () => {
              await claimsLogic.update(applicationId, patchData);
            });

            expect(updateClaimMock).toHaveBeenCalledWith(applicationId, {
              ...patchData,
              mmg_idv_status: "Unverified",
            });
          });

          it("does NOT update mmg_idv_status if NO fields are changed in a full patchData object", async () => {
            const patchData = {
              first_name: "SameFirstName",
              middle_name: "SameMiddleName",
              last_name: "SameLastName",
            };

            jest
              .spyOn(claimsLogic.benefitsApplications, "getItem")
              .mockReturnValue({
                application_id: applicationId,
                first_name: "SameFirstName",
                middle_name: "SameMiddleName",
                last_name: "SameLastName",
                mmg_idv_status: "Verified",
              });

            await act(async () => {
              await claimsLogic.update(applicationId, patchData);
            });

            expect(updateClaimMock).toHaveBeenCalledWith(
              applicationId,
              patchData
            );
            expect(patchData).not.toHaveProperty("mmg_idv_status"); // No change
          });

          it("does NOT update mmg_idv_status if only middle name changes in a full patchData object", async () => {
            const patchData = {
              first_name: "SameFirstName",
              middle_name: "NewMiddleName",
              last_name: "SameLastName",
            };

            jest
              .spyOn(claimsLogic.benefitsApplications, "getItem")
              .mockReturnValue({
                application_id: applicationId,
                first_name: "SameFirstName",
                middle_name: "OldMiddleName",
                last_name: "SameLastName",
                mmg_idv_status: "Verified",
              });

            await act(async () => {
              await claimsLogic.update(applicationId, patchData);
            });

            expect(updateClaimMock).toHaveBeenCalledWith(
              applicationId,
              patchData
            );
            expect(patchData).not.toHaveProperty("mmg_idv_status"); // No change
          });

          it("does NOT update mmg_idv_status if feature flag is disabled", async () => {
            isFeatureEnabled.mockImplementation(
              (flag) => flag !== "enableMmgIDV"
            );
            const patchData = { first_name: "AnotherName" };

            claimsLogic.benefitsApplications.getItem = jest
              .fn()
              .mockReturnValue({
                application_id: applicationId,
                mmg_idv_status: "Verified",
              });

            await act(async () => {
              await claimsLogic.update(applicationId, patchData);
            });

            expect(updateClaimMock).toHaveBeenCalledWith(
              applicationId,
              patchData
            );
            expect(patchData).not.toHaveProperty("mmg_idv_status");
          });

          it("does NOT update mmg_idv_status if application is already 'Unverified'", async () => {
            const patchData = { first_name: "NewFirstName" };

            // Mock current application state as "Unverified"
            claimsLogic.benefitsApplications.getItem = jest
              .fn()
              .mockReturnValue({
                application_id: applicationId,
                first_name: "OldFirstName",
                mmg_idv_status: "Unverified", // Already Unverified
              });

            await act(async () => {
              await claimsLogic.update(applicationId, patchData);
            });

            expect(updateClaimMock).toHaveBeenCalledWith(
              applicationId,
              patchData
            );
            expect(patchData).not.toHaveProperty("mmg_idv_status"); // Ensure no status change
          });
        });

        describe("phone number changes", () => {
          beforeEach(() => {
            jest.clearAllMocks();
            isFeatureEnabled.mockImplementation(
              (flag) => flag === "enableMmgIDV"
            );
            setup();
          });

          it("does NOT update mmg_idv_status when phone number is changed", async () => {
            const patchData = { phone: { phone_number: "************" } };

            jest
              .spyOn(claimsLogic.benefitsApplications, "getItem")
              .mockReturnValue({
                application_id: applicationId,
                phone: { phone_number: "************" }, // Existing phone number
                mmg_idv_status: "Verified",
              });

            await act(async () => {
              await claimsLogic.update(applicationId, patchData);
            });

            expect(updateClaimMock).toHaveBeenCalledWith(
              applicationId,
              patchData
            );
            expect(patchData).not.toHaveProperty("mmg_idv_status"); // No change
          });
        });

        describe("address fileds are changed", () => {
          beforeEach(() => {
            jest.clearAllMocks();
            isFeatureEnabled.mockImplementation(
              (flag) => flag === "enableMmgIDV"
            );
            setup();
          });
          it("does NOT update mmg_idv_status if address is changed via Experian suggestion only", async () => {
            const patchData = {
              residential_address: { line_1: "123 New St", city: "NewCity" },
              is_residential_address_validated: true, // Ensuring it does not block the update
            };

            jest
              .spyOn(claimsLogic.benefitsApplications, "getItem")
              .mockReturnValue({
                application_id: applicationId,
                residential_address: { line_1: "456 Old St", city: "OldCity" }, // Old values
                mmg_idv_status: "Verified", // Ensure it's "Verified"
              });

            await act(async () => {
              await claimsLogic.update(applicationId, patchData);
            });

            expect(updateClaimMock).toHaveBeenCalledWith(
              applicationId,
              patchData
            );
            expect(patchData).not.toHaveProperty("mmg_idv_status"); // No change
          });
          it("updates mmg_idv_status to 'Unverified' when residential_address.line_1 is edited", async () => {
            const patchData = {
              residential_address: { line_1: "123 New St" },
              is_residential_address_validated: undefined, // Ensuring it does not block the update
            };

            jest
              .spyOn(claimsLogic.benefitsApplications, "getItem")
              .mockReturnValue({
                application_id: applicationId,
                residential_address: { line_1: "456 Old St" }, // Existing value
                mmg_idv_status: "Verified", // Ensure it's "Verified"
              });

            await act(async () => {
              await claimsLogic.update(applicationId, patchData);
            });

            expect(updateClaimMock).toHaveBeenCalledWith(applicationId, {
              ...patchData,
              mmg_idv_status: "Unverified",
            });
          });
          it("updates mmg_idv_status to 'Unverified' when residential_address.city is edited", async () => {
            const patchData = {
              residential_address: { city: "NewCity" },
              is_residential_address_validated: undefined, // Ensuring it does not block the update
            };

            jest
              .spyOn(claimsLogic.benefitsApplications, "getItem")
              .mockReturnValue({
                application_id: applicationId,
                residential_address: { city: "OldCity" }, // Existing value
                mmg_idv_status: "Verified", // Ensure it's "Verified"
              });

            await act(async () => {
              await claimsLogic.update(applicationId, patchData);
            });

            expect(updateClaimMock).toHaveBeenCalledWith(applicationId, {
              ...patchData,
              mmg_idv_status: "Unverified",
            });
          });
          it("updates mmg_idv_status to 'Unverified' when residential_address.state is edited", async () => {
            const patchData = {
              residential_address: { state: "NY" },
              is_residential_address_validated: undefined, // Ensuring it does not block the update
            };

            jest
              .spyOn(claimsLogic.benefitsApplications, "getItem")
              .mockReturnValue({
                application_id: applicationId,
                residential_address: { state: "MA" }, // Existing value
                mmg_idv_status: "Verified", // Ensure it's "Verified"
              });

            await act(async () => {
              await claimsLogic.update(applicationId, patchData);
            });

            expect(updateClaimMock).toHaveBeenCalledWith(applicationId, {
              ...patchData,
              mmg_idv_status: "Unverified",
            });
          });
          it("updates mmg_idv_status to 'Unverified' when residential_address.zip is edited", async () => {
            const patchData = {
              residential_address: { zip: "90001" },
              is_residential_address_validated: undefined, // Ensuring it does not block the update
            };

            jest
              .spyOn(claimsLogic.benefitsApplications, "getItem")
              .mockReturnValue({
                application_id: applicationId,
                residential_address: { zip: "10001" }, // Existing value
                mmg_idv_status: "Verified", // Ensure it's "Verified"
              });

            await act(async () => {
              await claimsLogic.update(applicationId, patchData);
            });

            expect(updateClaimMock).toHaveBeenCalledWith(applicationId, {
              ...patchData,
              mmg_idv_status: "Unverified",
            });
          });
          it("does NOT update mmg_idv_status when residential_address.line_2 is edited", async () => {
            const patchData = {
              residential_address: { line_2: "Apt 5" },
              is_residential_address_validated: undefined, // Ensuring it does not block the update
            };

            jest
              .spyOn(claimsLogic.benefitsApplications, "getItem")
              .mockReturnValue({
                application_id: applicationId,
                residential_address: { line_2: "Apt 4" },
                mmg_idv_status: "Verified", // Ensure it's "Verified"
              });

            await act(async () => {
              await claimsLogic.update(applicationId, patchData);
            });

            expect(updateClaimMock).toHaveBeenCalledWith(
              applicationId,
              patchData
            );
            expect(patchData).not.toHaveProperty("mmg_idv_status"); // No change
          });
        });

        describe("when applicant prompted to supply MA ID in Step 1", () => {
          beforeEach(() => {
            jest.clearAllMocks();
            isFeatureEnabled.mockImplementation(
              (flag) => flag === "enableMmgIDV"
            );
            setup();
          });

          it("updates mmg_idv_status to 'Unverified' when the applicant has no MA ID", async () => {
            const patchData = { has_state_id: false, mass_id: null };

            jest
              .spyOn(claimsLogic.benefitsApplications, "getItem")
              .mockReturnValue({
                application_id: applicationId,
                has_state_id: false, // Existing value
                mmg_idv_status: "Verified",
              });

            await act(async () => {
              await claimsLogic.update(applicationId, patchData);
            });

            expect(updateClaimMock).toHaveBeenCalledWith(applicationId, {
              ...patchData,
              mmg_idv_status: "Unverified",
            });
          });

          it("does NOT update mmg_idv_status when the applicant has supplied a MA ID", async () => {
            const patchData = { has_state_id: true, mass_id: "SA1234567" };

            jest
              .spyOn(claimsLogic.benefitsApplications, "getItem")
              .mockReturnValue({
                application_id: applicationId,
                has_state_id: true, // Same as patchData
                mmg_idv_status: "Verified",
              });

            await act(async () => {
              await claimsLogic.update(applicationId, patchData);
            });

            expect(updateClaimMock).toHaveBeenCalledWith(
              applicationId,
              patchData
            );
            expect(patchData).not.toHaveProperty("mmg_idv_status"); // No change
          });
        });
      });
    });

    describe("validate address fields", () => {
      beforeEach(async () => {
        mockRouter.pathname = routes.applications.review;
        setup(routes.applications);

        const claim = new BenefitsApplication({
          application_id: applicationId,
        });
        createClaimMock.mockResolvedValueOnce({
          claim,
          success: true,
        });

        await act(async () => {
          await claimsLogic.create();
        });

        mockRouter.push.mockClear();
      });

      const validResidentialAddress = {
        line_1: "123 Main St",
        city: "Springfield",
        state: "IL",
        zip: "62704",
      };

      const incompleteResidentialAddress = {
        line_1: "",
        city: "",
        state: "IL",
        zip: "",
      };

      const validMailingAddress = {
        line_1: "456 Elm St",
        city: "Springfield",
        state: "IL",
        zip: "62704",
      };

      const incompleteMailingAddress = {
        line_1: "",
        city: "",
        state: "",
        zip: "",
      };

      it("validate address fields with incomplete residential address", async () => {
        const issues = await claimsLogic.validateAddressFields(
          incompleteResidentialAddress,
          validMailingAddress,
          true
        );
        expect(issues).toHaveLength(3);
      });

      it("validate address fields with incomplete mailing address", async () => {
        const issues = await claimsLogic.validateAddressFields(
          validResidentialAddress,
          incompleteMailingAddress,
          true
        );
        expect(issues).toHaveLength(4);
      });

      it("validate address fields with valid residential and mailing address", async () => {
        const issues = await claimsLogic.validateAddressFields(
          validResidentialAddress,
          validMailingAddress,
          true
        );
        expect(issues).toHaveLength(0);
      });

      it("validate address fields with invalid residential and mailing address", async () => {
        const issues = await claimsLogic.validateAddressFields(
          incompleteResidentialAddress,
          incompleteMailingAddress,
          true
        );
        expect(issues).toHaveLength(7);
      });
    });

    describe("submit", () => {
      beforeEach(async () => {
        mockRouter.pathname = routes.applications.review;
        setup(routes.applications);

        const claim = new BenefitsApplication({
          application_id: applicationId,
        });
        createClaimMock.mockResolvedValueOnce({
          claim,
          success: true,
        });

        await act(async () => {
          await claimsLogic.create();
        });

        mockRouter.push.mockClear();
      });

      it("submits the claim", async () => {
        await act(async () => {
          await claimsLogic.submit(applicationId);
        });

        const claim = claimsLogic.benefitsApplications.getItem(applicationId);

        expect(submitClaimMock).toHaveBeenCalledWith(applicationId);
        expect(claim.status).toBe(BenefitsApplicationStatus.submitted);
      });

      it("clears prior errors", async () => {
        act(() => {
          errorsLogic.setErrors([new Error()]);
        });

        await act(async () => {
          await claimsLogic.submit(applicationId);
        });

        expect(errorsLogic.errors).toHaveLength(0);
      });

      it("routes to claim checklist page when the request succeeds", async () => {
        await act(async () => {
          await claimsLogic.submit(applicationId);
        });

        expect(mockRouter.push).toHaveBeenCalledWith(
          expect.stringContaining(
            `${routes.applications.checklist}?claim_id=${applicationId}`
          )
        );
      });

      it("passes part-one-submitted into the route when the request succeeds", async () => {
        await act(async () => {
          await claimsLogic.submit(applicationId);
        });

        expect(mockRouter.push).toHaveBeenCalledWith(
          expect.stringContaining(`&part-one-submitted=true`)
        );
      });

      it("catches exceptions thrown from the API module", async () => {
        jest.spyOn(console, "error").mockImplementationOnce(jest.fn());
        submitClaimMock.mockImplementationOnce(() => {
          throw new BadRequestError();
        });

        await act(async () => {
          await claimsLogic.submit(applicationId);
        });

        expect(errorsLogic.errors[0].name).toEqual("BadRequestError");
        expect(mockRouter.push).not.toHaveBeenCalled();
      });

      it("routes to the application index page when the submitted application was split", async () => {
        const splitClaim = new BenefitsApplication({
          application_id: applicationId,
          split_into_application_id: "split_application_id",
          status: BenefitsApplicationStatus.submitted,
        });
        createClaimMock.mockResolvedValueOnce({
          claim: splitClaim,
          success: true,
        });

        submitClaimMock.mockResolvedValueOnce({
          success: true,
          status: 201,
          claim: splitClaim,
          warnings: [],
        });

        await act(async () => {
          await claimsLogic.create();
        });

        await act(async () => {
          await claimsLogic.submit(applicationId);
        });

        expect(mockRouter.push).toHaveBeenCalledWith(
          expect.stringContaining(
            `${routes.applications.index}?claim_id=${applicationId}&part-one-submitted=true`
          )
        );
      });

      it("invalidates the applications cache when the submitted application was split", async () => {
        const splitClaim = new BenefitsApplication({
          application_id: applicationId,
          split_into_application_id: "split_application_id",
          status: BenefitsApplicationStatus.submitted,
        });
        createClaimMock.mockResolvedValueOnce({
          claim: splitClaim,
          success: true,
        });

        submitClaimMock.mockResolvedValueOnce({
          success: true,
          status: 201,
          claim: splitClaim,
          warnings: [],
        });

        await act(async () => {
          await claimsLogic.create();
        });

        await act(async () => {
          await claimsLogic.submit(applicationId);
        });

        expect(claimsLogic.isLoadingClaims).toBeUndefined();

        await act(async () => {
          await claimsLogic.loadPage();
        });

        expect(getClaimsMock).toHaveBeenCalledTimes(1);
      });
    });

    describe("submitCustomerPaymentPreference", () => {
      const paymentData = {
        payment_preference: new MockBenefitsApplicationBuilder()
          .directDeposit()
          .create().payment_preference,
      };

      beforeEach(async () => {
        mockRouter.pathname = routes.applications.paymentMethod;
        setup(routes.applications);

        const claim = new BenefitsApplication({
          application_id: applicationId,
        });
        createClaimMock.mockResolvedValueOnce({
          claim,
          success: true,
        });

        await act(async () => {
          await claimsLogic.create();
        });

        mockRouter.push.mockClear();
      });

      it("submits the payment preference", async () => {
        await act(async () => {
          await claimsLogic.submitCustomerPaymentPreference(
            applicationId,
            paymentData
          );
        });

        const claim = claimsLogic.benefitsApplications.getItem(applicationId);

        expect(submitCustomerPaymentPreferenceMock).toHaveBeenCalledWith(
          applicationId,
          paymentData
        );
        expect(claim.has_submitted_payment_preference).toBe(true);
      });

      it("clears prior errors", async () => {
        act(() => {
          errorsLogic.setErrors([new Error()]);
        });

        await act(async () => {
          await claimsLogic.submitCustomerPaymentPreference(applicationId);
        });

        expect(errorsLogic.errors).toHaveLength(0);
      });

      it("routes to claim checklist page when the request succeeds", async () => {
        await act(async () => {
          await claimsLogic.submitCustomerPaymentPreference(applicationId);
        });

        expect(mockRouter.push).toHaveBeenCalledWith(
          expect.stringContaining(
            `${routes.applications.checklist}?claim_id=${applicationId}`
          )
        );
      });

      it("passes payment-pref-submitted into the route when the request succeeds", async () => {
        await act(async () => {
          await claimsLogic.submitCustomerPaymentPreference(
            applicationId,
            paymentData
          );
        });

        expect(mockRouter.push).toHaveBeenCalledWith(
          expect.stringContaining(`&payment-pref-submitted=true`)
        );
      });

      it("catches exceptions thrown from the API module", async () => {
        jest.spyOn(console, "error").mockImplementationOnce(jest.fn());
        submitCustomerPaymentPreferenceMock.mockImplementationOnce(() => {
          throw new BadRequestError();
        });

        await act(async () => {
          await claimsLogic.submitCustomerPaymentPreference(applicationId);
        });

        expect(errorsLogic.errors[0].name).toEqual("BadRequestError");
        expect(mockRouter.push).not.toHaveBeenCalled();
      });
    });
  });

  describe("hasNoEmployeeFoundError", () => {
    beforeEach(() => {
      mockRouter.pathname = routes.applications.employmentStatus;
      // Make sure the ID we're loading matches what the API will return to us so caching works as
      applicationId = getClaimMockApplicationId;
      setup();
    });

    it("is true when application has require_employee warning", async () => {
      getClaimMock.mockResolvedValueOnce({
        success: true,
        status: 200,
        claim: new BenefitsApplication({
          application_id: getClaimMockApplicationId,
          status: BenefitsApplicationStatus.started,
        }),
        warnings: [{ rule: "require_employee" }],
      });

      expect(
        claimsLogic.hasLoadedBenefitsApplicationAndWarnings(applicationId)
      ).toBe(false);

      await act(async () => {
        await claimsLogic.load(applicationId);
      });

      expect(
        claimsLogic.hasLoadedBenefitsApplicationAndWarnings(applicationId)
      ).toBe(true);

      await act(async () => {
        const response =
          await claimsLogic.hasNoEmployeeFoundError(applicationId);
        expect(response).toBe(true);
      });
    });

    it("is false when application doesn't have require_employee warning", async () => {
      expect(
        claimsLogic.hasLoadedBenefitsApplicationAndWarnings(applicationId)
      ).toBe(false);

      await act(async () => {
        await claimsLogic.load(applicationId);
      });

      expect(
        claimsLogic.hasLoadedBenefitsApplicationAndWarnings(applicationId)
      ).toBe(true);

      await act(async () => {
        const response =
          await claimsLogic.hasNoEmployeeFoundError(applicationId);
        expect(response).toBe(false);
      });
    });
  });

  describe("updateUserProfileWithApplicationSkipErrors", () => {
    const userId = "mock_user_id";

    const claim = new BenefitsApplication({
      application_id: applicationId,
    });

    const patchData = new UserProfileUpdate({
      from_application: claim.application_id,
    });

    beforeEach(() => {
      mockRouter.pathname = routes.applications.saveProfile;

      setup();
    });

    it("calls the API", async () => {
      await act(async () => {
        await claimsLogic.updateUserProfileWithApplicationSkipErrors(
          userId,
          patchData,
          claim
        );
      });

      expect(mockUpdateUserProfile).toHaveBeenCalledWith(userId, patchData);
    });

    it("routes to next page", async () => {
      await act(async () => {
        await claimsLogic.updateUserProfileWithApplicationSkipErrors(
          userId,
          patchData,
          claim
        );
      });

      expect(mockRouter.push).toHaveBeenCalledWith(
        routes.applications.checklist
      );
    });

    describe("eats exceptions thrown from the API module", () => {
      beforeEach(() => {
        mockUpdateUserProfile.mockImplementationOnce(() => {
          throw new BadRequestError({}, "bad request");
        });
      });

      it("without query params", async () => {
        await act(async () => {
          await claimsLogic.updateUserProfileWithApplicationSkipErrors(
            userId,
            patchData,
            claim
          );
        });

        // the error is still tracked
        expect(tracker.trackEvent).toHaveBeenCalledWith("BadRequestError", {
          errorMessage: "bad request",
          errorName: "BadRequestError",
        });
        // but what drives UI errors is empty/no error will be shown
        // to user
        expect(errorsLogic.errors).toEqual([]);

        // and we still navigate to next page
        expect(mockRouter.push).toHaveBeenCalledWith(
          routes.applications.checklist
        );
      });

      it("with query params", async () => {
        const params = { foo: "bar" };

        await act(async () => {
          await claimsLogic.updateUserProfileWithApplicationSkipErrors(
            userId,
            patchData,
            claim,
            params
          );
        });

        expect(mockRouter.push).toHaveBeenCalledWith(
          expect.stringContaining("?foo=bar")
        );
      });
    });
  });
});
