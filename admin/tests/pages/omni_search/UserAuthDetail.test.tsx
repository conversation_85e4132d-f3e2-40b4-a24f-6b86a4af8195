import "@testing-library/jest-dom";

import { render, screen, waitFor } from "@testing-library/react";

import React, { act } from "react";
import * as api from "../../../src/_api";
import UserAuthDetail from "../../../src/pages/omni_search/UserAuthDetail";

const mockRouteChangeHandler = jest.fn();
let mockQuery = {};

jest.mock("next/router", () => ({
  useRouter: () => ({
    push: mockRouteChangeHandler,
    query: mockQuery,
    isReady: true,
  }),
}));

const leaveAdminUserDetailsResponse = {
  data: {
    application_names: [],
    auth_id: "8a39d6c8-eac1-4638-9b64-9d1983a8646e",
    consented_to_data_sharing: true,
    consented_to_view_tax_documents: null,
    email_address: "<EMAIL>",
    first_name: "First",
    has_multiple_tax_identifiers: false,
    language_preference: "English",
    last_name: "Last",
    phone_number: {
      extension: null,
      int_code: "1",
      phone_number: "***-***-7258",
      phone_type: null,
    },
    roles: [
      {
        role_description: "Employer",
        role_id: 3,
      },
    ],
    user_id: "b7b869e1-c027-4c27-8063-15b721bbeeb9",
    user_leave_administrators: [],
  },
  message: "Get user detail completed.",
  status_code: 200,
};

const verifiedLeaveAdminUserDetailsResponse = {
  data: {
    ...leaveAdminUserDetailsResponse.data,
    user_leave_administrators: [
      {
        email_address: "<EMAIL>",
        employer_dba: "Benchmark One-To-One Systems LLC",
        employer_fein: "88-2530111",
        employer_id: "daecdb3b-7be8-412a-af04-bfd3c57ce5d1",
        has_fineos_registration: true,
        has_verification_data: false,
        user_leave_administrator_id: "d94a669d-eb96-44e8-92c0-4d5fcb94f4be",
        verified: true,
      },
    ],
  },
  message: "Get user detail completed.",
  status_code: 200,
};

const authLogsResponse = {
  data: [
    {
      completed_at: "2025-01-10T18:26:35.520956+00:00",
      meta_data: {
        origin: "start",
        outcomes: ["account_found"],
      },
      oauth_operation: "Authenticate",
      oauth_operation_id: 1,
      started_at: "2025-01-10T18:25:59.607299+00:00",
      user_id: "b7b869e1-c027-4c27-8063-15b721bbeeb9",
    },
    {
      completed_at: "2025-01-10T17:42:46.962210+00:00",
      meta_data: {
        origin: "start",
        outcomes: ["account_found"],
      },
      oauth_operation: "Authenticate",
      oauth_operation_id: 1,
      started_at: "2025-01-10T17:42:42.371082+00:00",
      user_id: "b7b869e1-c027-4c27-8063-15b721bbeeb9",
    },
    {
      completed_at: "2025-01-10T17:29:36.113205+00:00",
      meta_data: {
        origin: "start",
        outcomes: ["account_found"],
      },
      oauth_operation: "Authenticate",
      oauth_operation_id: 1,
      started_at: "2025-01-10T17:29:16.319559+00:00",
      user_id: "b7b869e1-c027-4c27-8063-15b721bbeeb9",
    },
  ],
  message: "Get user detail completed.",
  status_code: 200,
};

const auditLogsResponse = {
  data: [],
  message: "Successfully retrieved audit logs",
  status_code: 200,
};

describe("UserAuthDetail", () => {
  beforeEach(() => {
    mockQuery = {};
  });

  test("renders the page", async () => {
    api.http.fetchJson = jest.fn().mockResolvedValue({
      response: {
        data: {},
      },
    });

    const component = render(<UserAuthDetail />).container;

    await waitFor(() => {
      expect(component).toMatchSnapshot();
    });
  });

  test("displays the 'convert user to claimant' button when an LA doesn't have any verified employers", async () => {
    mockQuery = {
      user_id: "b7b869e1-c027-4c27-8063-15b721bbeeb9",
    };

    const initialPageLoadMock = jest
      .fn()
      .mockResolvedValueOnce(leaveAdminUserDetailsResponse)
      .mockResolvedValueOnce(authLogsResponse)
      .mockResolvedValueOnce(auditLogsResponse);

    api.http.fetchJson = initialPageLoadMock;

    await act(() => {
      render(<UserAuthDetail />).container;
    });

    await waitFor(() => {
      expect(
        screen.getByRole("button", {
          name: /convert user to claimant/i,
        }),
      ).toBeInTheDocument();
    });
  });

  test("does not display the 'convert user to claimant' button when an LA has verified employers", async () => {
    mockQuery = {
      user_id: "b7b869e1-c027-4c27-8063-15b721bbeeb9",
    };

    const initialPageLoadMock = jest
      .fn()
      .mockResolvedValueOnce(verifiedLeaveAdminUserDetailsResponse)
      .mockResolvedValueOnce(authLogsResponse)
      .mockResolvedValueOnce(auditLogsResponse);

    api.http.fetchJson = initialPageLoadMock;

    await act(() => {
      render(<UserAuthDetail />).container;
    });

    screen.logTestingPlaygroundURL();

    await waitFor(() => {
      expect(
        screen.queryByRole("button", {
          name: /convert user to claimant/i,
        }),
      ).not.toBeInTheDocument();

      expect(
        screen.getByText(
          /unable to convert leave administrator with verified employers/i,
        ),
      ).toBeInTheDocument();
    });
  });
});
