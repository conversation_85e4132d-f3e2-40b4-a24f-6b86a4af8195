import "@testing-library/jest-dom";

import { render, screen, waitFor } from "@testing-library/react";

import React from "react";
import userEvent from "@testing-library/user-event";
import mockFetch from "../test-utils/mockFetch";
import * as api from "../../src/_api";
import Address from "../../src/pages/address";

const mockRouteChangeHandler = jest.fn();

jest.mock("next/router", () => ({
  useRouter: () => ({
    push: mockRouteChangeHandler,
  }),
}));

describe("Address", () => {
  test("renders the page", () => {
    const component = render(<Address />).container;

    expect(component).toMatchSnapshot();
  });

  test("displays an error message when too few address fields are provided", async () => {
    render(<Address />).container;

    await userEvent.type(
      screen.getByRole("textbox", {
        name: /city/i,
      }),
      "Boston",
    );

    await userEvent.type(
      screen.getByRole("textbox", {
        name: /state/i,
      }),
      "MA",
    );

    api.http.fetchJson = mockFetch({
      response: {
        data: [
          {
            picklist: [],
            valid_address: [],
            verify_level: "None",
          },
        ],
        message: "",
        status_code: 200,
      },
    });

    await userEvent.click(
      screen.getByRole("button", {
        name: /submit/i,
      }),
    );

    await waitFor(() => {
      expect(api.http.fetchJson).toHaveBeenCalledWith(
        "/admin/validate-address?address=" +
          encodeURIComponent(" , Boston MA "),
        {},
      );

      expect(screen.getByText(/incomplete address/i)).toBeInTheDocument();
    });
  });

  test("displays a verified address when an exact address is provided", async () => {
    render(<Address />).container;

    await userEvent.type(
      screen.getByRole("textbox", {
        name: /address_line_1/i,
      }),
      "123 Main St",
    );

    await userEvent.type(
      screen.getByRole("textbox", {
        name: /address_line_2/i,
      }),
      "Unit 1",
    );

    await userEvent.type(
      screen.getByRole("textbox", {
        name: /city/i,
      }),
      "Boston",
    );

    await userEvent.type(
      screen.getByRole("textbox", {
        name: /state/i,
      }),
      "MA",
    );

    api.http.fetchJson = mockFetch({
      response: {
        data: [
          {
            picklist: [],
            valid_address: [
              {
                "Address Line 1": "123 Main St",
                "Address Line 2": "Unit 1",
                City: "Charlestown",
                State: "MA",
                "Zip+4": "02129-3533",
              },
            ],
            verify_level: "Verified",
          },
        ],
        message: "",
        status_code: 200,
      },
    });

    await userEvent.click(
      screen.getByRole("button", {
        name: /submit/i,
      }),
    );

    await waitFor(() => {
      expect(api.http.fetchJson).toHaveBeenCalledWith(
        "/admin/validate-address?address=" +
          encodeURIComponent("123 Main St Unit 1, Boston MA "),
        {},
      );

      expect(
        screen.getByRole("heading", {
          name: /verified address:/i,
        }),
      ).toBeInTheDocument();

      expect(
        screen.getByText(/address line 1: 123 main st/i),
      ).toBeInTheDocument();

      expect(screen.getByText(/address line 2: unit 1/i)).toBeInTheDocument();

      expect(screen.getByText(/city: charlestown/i)).toBeInTheDocument();

      expect(screen.getByText(/state: ma/i)).toBeInTheDocument();

      expect(screen.getByText(/zip\+4: 02129\-3533/i)).toBeInTheDocument();
    });
  });

  test("displays a list of address when a partial address is provided", async () => {
    render(<Address />).container;

    await userEvent.type(
      screen.getByRole("textbox", {
        name: /address_line_1/i,
      }),
      "123 Parkway Place",
    );

    await userEvent.type(
      screen.getByRole("textbox", {
        name: /city/i,
      }),
      "Boston",
    );

    await userEvent.type(
      screen.getByRole("textbox", {
        name: /state/i,
      }),
      "MA",
    );

    await userEvent.type(
      screen.getByRole("textbox", {
        name: /zip\-code/i,
      }),
      "02124",
    );

    api.http.fetchJson = mockFetch({
      response: {
        data: [
          {
            picklist: [
              "1 ... 99 Parkway St, Dorchester Center MA   [odd],02124-1504",
              "2 ... 98 Parkway St, Dorchester Center MA   [even],02124-1505",
            ],
            valid_address: [],
            verify_level: "StreetPartial",
          },
        ],
        message: "",
        status_code: 200,
      },
    });

    await userEvent.click(
      screen.getByRole("button", {
        name: /submit/i,
      }),
    );

    await waitFor(() => {
      expect(api.http.fetchJson).toHaveBeenCalledWith(
        "/admin/validate-address?address=" +
          encodeURIComponent("123 Parkway Place , Boston MA 02124"),
        {},
      );

      expect(
        screen.getByText(/address is partially correct/i),
      ).toBeInTheDocument();

      expect(
        screen.getByRole("columnheader", {
          name: /suggestions/i,
        }),
      ).toBeInTheDocument();

      expect(
        screen.getByRole("cell", {
          name: /1 \.\.\. 99 parkway st, dorchester center ma \[odd\],02124\-1504/i,
        }),
      ).toBeInTheDocument();

      expect(
        screen.getByRole("cell", {
          name: /2 \.\.\. 98 parkway st, dorchester center ma \[even\],02124\-1505/i,
        }),
      ).toBeInTheDocument();
    });
  });
});
