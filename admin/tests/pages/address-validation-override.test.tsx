import "@testing-library/jest-dom";

import { render, screen, waitFor, within } from "@testing-library/react";

import React from "react";
import userEvent from "@testing-library/user-event";
import mockFetch from "../test-utils/mockFetch";
import * as api from "../../src/_api";
import AddressValidationOverride from "../../src/pages/address-validation-override";
import { HttpError } from "../../src/_api";

const mockRouteChangeHandler = jest.fn();

jest.mock("next/router", () => ({
  useRouter: () => ({
    push: mockRouteChangeHandler,
  }),
}));

describe("AddressValidationOverride", () => {
  test("renders the page", () => {
    const component = render(<AddressValidationOverride />).container;

    expect(component).toMatchSnapshot();
  });

  test("routes to the application root when the cancel button is clicked", async () => {
    render(<AddressValidationOverride />).container;

    await userEvent.click(screen.getByRole("button", { name: /cancel/i }));

    expect(mockRouteChangeHandler).toHaveBeenCalledWith("/");
  });

  test("displays an error message for an absence case ID that can't have its address validation overridden", async () => {
    render(<AddressValidationOverride />).container;

    await userEvent.type(screen.getByRole("textbox"), "NTN-1517966-ABS-01");

    api.http.fetchJson = jest.fn().mockImplementationOnce(() => {
      const headers = new Headers();
      headers.append("Content-Type", "application/json");
      headers.append("Authorization", "Bearer test-token");

      throw new HttpError(404, "Not Found", "", headers);
    });

    await userEvent.click(
      screen.getByRole("button", {
        name: /check/i,
      }),
    );

    await waitFor(() => {
      expect(
        screen.getByText(
          /the absence case id doesn't exist or the validated address is already confirmed\./i,
        ),
      ).toBeInTheDocument();
    });

    // Close the alert
    await userEvent.click(
      within(screen.getByTestId("alert-container")).getByRole("button"),
    );

    await waitFor(() => {
      expect(
        screen.queryByText(
          /the absence case id doesn't exist or the validated address is already confirmed\./i,
        ),
      ).not.toBeInTheDocument();
    });
  });

  test("displays a success message for an absence case ID after its address validation is overridden", async () => {
    render(<AddressValidationOverride />).container;

    await userEvent.type(screen.getByRole("textbox"), "NTN-1517966-ABS-01");

    api.http.fetchJson = mockFetch({
      response: {
        data: {
          address_line_one: "96629 Cronin Passage",
          address_line_two: null,
          city: "North Arjun",
          state: "MA",
          zip_code: "62811-9963",
        },
      },
    });

    await userEvent.click(
      screen.getByRole("button", {
        name: /check/i,
      }),
    );

    await waitFor(() => {
      expect(screen.getByText(/96629 cronin passage/i)).toBeInTheDocument();

      expect(
        screen.getByText(/north arjun, ma 62811\-9963/i),
      ).toBeInTheDocument();

      expect(
        screen.getByRole("button", {
          name: /continue/i,
        }),
      ).toBeInTheDocument();
    });

    api.http.fetchJson = mockFetch({
      response: {
        data: {},
        message: "Success",
      },
    });

    await userEvent.click(
      screen.getByRole("button", {
        name: /continue/i,
      }),
    );

    await waitFor(() => {
      expect(
        screen.getByText(
          /address validation was successfully overridden for ntn\-1517966\-abs\-01\./i,
        ),
      ).toBeInTheDocument();
    });

    // Close the alert
    await userEvent.click(
      within(screen.getByTestId("alert-container")).getByRole("button"),
    );

    await waitFor(() => {
      expect(
        screen.queryByText(
          /address validation was successfully overridden for ntn\-1517966\-abs\-01\./i,
        ),
      ).not.toBeInTheDocument();
    });
  });

  test("hides the confirmation modal when the cancel button is clicked", async () => {
    render(<AddressValidationOverride />).container;

    await userEvent.type(screen.getByRole("textbox"), "NTN-1517966-ABS-01");

    api.http.fetchJson = mockFetch({
      response: {
        data: {
          address_line_one: "96629 Cronin Passage",
          address_line_two: null,
          city: "North Arjun",
          state: "MA",
          zip_code: "62811-9963",
        },
      },
    });

    await userEvent.click(
      screen.getByRole("button", {
        name: /check/i,
      }),
    );

    await waitFor(() => {
      expect(screen.getByText(/96629 cronin passage/i)).toBeInTheDocument();

      expect(
        screen.getByText(/north arjun, ma 62811\-9963/i),
      ).toBeInTheDocument();

      expect(
        within(screen.getByRole("dialog")).getByRole("button", {
          name: /cancel/i,
        }),
      ).toBeInTheDocument();
    });

    await userEvent.click(
      within(screen.getByRole("dialog")).getByRole("button", {
        name: /cancel/i,
      }),
    );

    await waitFor(() => {
      expect(screen.queryByRole("dialog")).not.toBeInTheDocument();
    });
  });
});
