// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`AddressValidationOverride renders the page 1`] = `
<div>
  <h1>
    Address Validation Override
  </h1>
  <div
    class="maintenance-configure"
  >
    <div
      class="maintenance-configure__description"
    >
      Steps:
      <ul>
        <li>
          Enter an absence case ID and click 'Check' to confirm if the FINEOS address can be set as the verified mailing address.
        </li>
        <li>
          Review the FINEOS address that will be set and click 'Continue' to complete the override.
        </li>
      </ul>
    </div>
    <form
      autocomplete="off"
      class="maintenance-configure__form"
    >
      <label
        class="maintenance-configure__label"
        for="absence_case_id"
      >
        Absence Case ID
        <span
          style="color: red;"
        >
          *
        </span>
      </label>
      <input
        class="maintenance-configure__input"
        label="Absence Case ID"
        name="absence_case_id"
        pattern="NTN-[0-9]+-[A-Z]{3}-[0-9]+"
        placeholder="e.g. NTN-1517966-ABS-01"
        required=""
        type="text"
        value=""
      />
      <fieldset
        class="maintenance-configure__fieldset maintenance-configure__buttons"
      >
        <button
          class="maintenance-configure__btn maintenance-configure__btn--submit btn"
          type="submit"
        >
          Check
        </button>
        <button
          class="maintenance-configure__btn maintenance-configure__btn--cancel btn btn--cancel"
          type="button"
        >
          Cancel
        </button>
      </fieldset>
    </form>
  </div>
</div>
`;
