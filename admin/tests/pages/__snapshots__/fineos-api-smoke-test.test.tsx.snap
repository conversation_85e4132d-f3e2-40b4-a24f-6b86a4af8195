// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`FineosApiSmokeTest renders the page 1`] = `
<div>
  <h1>
    FINEOS API Smoke Test
  </h1>
  <div
    class="maintenance-configure"
  >
    <div
      class="maintenance-configure__description"
    >
      Use this page to confirm FINEOS APIs are available. The tests confirm that a service is reachable and that it returns an expected status code.
    </div>
    <form
      autocomplete="off"
      class="maintenance-configure__form"
    >
      <div
        class="fineos-api-smoke-test__container"
      >
        <div
          class="fineos-api-smoke-test__container-header"
        >
          API
        </div>
        <div
          class="fineos-api-smoke-test__container-header"
        />
        <div
          class="fineos-api-smoke-test__container-header"
        >
          Status
        </div>
        <div
          class="fineos-api-smoke-test__container-header"
        >
          Last Checked
        </div>
        <div
          class="fineos-api-smoke-test__container-header"
        >
          Recent Logs
        </div>
        <div>
          Customer API
        </div>
        <div>
          <button
            aria-label="Customer API"
            class="maintenance-configure__btn maintenance-configure__btn--submit btn"
            type="button"
          >
            Check
          </button>
        </div>
        <div
          aria-label="Customer API Status"
          class="fineos-api-smoke-test__status-icon"
        />
        <div
          aria-label="Customer API Last Checked"
        />
        <div />
        <div>
          Integration Services API
        </div>
        <div>
          <button
            aria-label="Integration Services API"
            class="maintenance-configure__btn maintenance-configure__btn--submit btn"
            type="button"
          >
            Check
          </button>
        </div>
        <div
          aria-label="Integration Services API Status"
          class="fineos-api-smoke-test__status-icon"
        />
        <div
          aria-label="Integration Services API Last Checked"
        />
        <div />
        <div>
          Group Client API
        </div>
        <div>
          <button
            aria-label="Group Client API"
            class="maintenance-configure__btn maintenance-configure__btn--submit btn"
            type="button"
          >
            Check
          </button>
        </div>
        <div
          aria-label="Group Client API Status"
          class="fineos-api-smoke-test__status-icon"
        />
        <div
          aria-label="Group Client API Last Checked"
        />
        <div />
      </div>
      <fieldset
        class="maintenance-configure__fieldset maintenance-configure__buttons"
      >
        <button
          class="maintenance-configure__btn maintenance-configure__btn--cancel btn btn--cancel"
          type="button"
        >
          Cancel
        </button>
      </fieldset>
    </form>
  </div>
</div>
`;
