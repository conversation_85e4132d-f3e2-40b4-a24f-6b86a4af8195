// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Maintenance renders the page 1`] = `
<div>
  <h1>
    Maintenance
  </h1>
  <div
    class="maintenance"
  >
    <div
      class="maintenance-info"
    >
      <div
        class="maintenance-info__text"
      >
        <h2
          class="maintenance-info__title"
        >
          Maintenance
        </h2>
        <span
          class="toggle toggle--off"
          data-testid="toggle"
        >
          OFF
        </span>
        <p
          class="maintenance-info__body"
        >
          When maintenance is scheduled for the future, a maintenance banner is displayed to users in the portal on the top of the page. When maintenance is currently enabled, a maintenance page is displayed to users instead of the normal page content.
        </p>
      </div>
      <div
        class="action-menu"
      >
        <ul
          class="action-menu__list"
        >
          <li
            class="action-menu__item"
          >
            <a
              class="action-menu__link"
              href="/maintenance/add?action=Configure+New+Maintenance"
            >
              Configure
            </a>
          </li>
        </ul>
      </div>
    </div>
  </div>
  <h2>
    History
  </h2>
  <p>
    No results found
  </p>
</div>
`;
