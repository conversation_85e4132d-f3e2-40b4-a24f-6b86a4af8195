import "@testing-library/jest-dom";

import {
  fireEvent,
  render,
  screen,
  waitFor,
  within,
} from "@testing-library/react";

import React, { act } from "react";
import userEvent from "@testing-library/user-event";
import mockFetch from "../../test-utils/mockFetch";
import * as api from "../../../src/_api";
import Maintenance from "../../../src/pages/maintenance/add";
import dayjs from "dayjs";

const mockRouteChangeHandler = jest.fn();
let mockQuery = {};

jest.mock("next/router", () => ({
  useRouter: () => ({
    push: mockRouteChangeHandler,
    query: mockQuery,
  }),
}));

const selectStartDateTime = async (
  nextNthAvailableDay: number,
  time: string,
) => {
  await userEvent.click(
    screen.getByRole("radio", {
      name: /schedule start datetime/i,
    }),
  );

  await userEvent.click(
    screen.getByRole("textbox", {
      name: /start_datetime/i,
    }),
  );

  let availableStartDays = screen.getAllByRole("option", {
    name: /choose/i,
  });

  if (availableStartDays.length < nextNthAvailableDay) {
    await userEvent.click(screen.getByText(/next month/i));

    availableStartDays = screen.getAllByRole("option", {
      name: /choose/i,
    });
  }

  const secondAvailableStartDay = availableStartDays[nextNthAvailableDay];

  await userEvent.click(secondAvailableStartDay);

  const startCalendarDialog = screen.getByRole("dialog", {
    name: /choose date and time/i,
  });

  const startTime = within(startCalendarDialog).getByPlaceholderText(/time/i);

  fireEvent.change(startTime, { target: { value: time } });
};

const selectEndDateTime = async (nextNthAvailableDay: number, time: string) => {
  await userEvent.click(
    screen.getByRole("radio", {
      name: /schedule end datetime/i,
    }),
  );

  await userEvent.click(
    screen.getByRole("textbox", {
      name: /end_datetime/i,
    }),
  );

  let availableEndDays = screen.getAllByRole("option", { name: /choose/i });

  if (availableEndDays.length < nextNthAvailableDay) {
    await userEvent.click(screen.getByText(/next month/i));

    availableEndDays = screen.getAllByRole("option", {
      name: /choose/i,
    });
  }

  const secondAvailableEndDay = availableEndDays[nextNthAvailableDay];

  await userEvent.click(secondAvailableEndDay);

  const endCalendarDialog = screen.getByRole("dialog", {
    name: /choose date and time/i,
  });

  const endTime = within(endCalendarDialog).getByPlaceholderText(/time/i);

  fireEvent.change(endTime, { target: { value: time } });
};

describe("Maintenance", () => {
  beforeEach(() => {
    mockQuery = {};
  });

  test("renders the page", async () => {
    api.http.fetchJson = mockFetch({
      response: {},
    });

    let component;

    await act(() => {
      component = render(<Maintenance />).container;
    });

    expect(component).toMatchSnapshot();
  });

  test("routes to the maintenance overview when the cancel button is clicked", async () => {
    render(<Maintenance />).container;

    await userEvent.click(screen.getByRole("button", { name: /cancel/i }));

    expect(mockRouteChangeHandler).toHaveBeenCalledWith("/maintenance/");
  });

  test("loads a maintenance window for editing", async () => {
    mockQuery = {
      name: "Test",
      start: "2025-04-23 12:00:00 EDT",
      end: "2025-04-23 15:00:00 EDT",
      action: "Edit Maintenance",
    };

    render(<Maintenance />).container;

    await waitFor(() => {
      expect(
        screen.getByPlaceholderText(/enter type of maintenance/i),
      ).toHaveValue("Test");

      expect(
        screen.getByRole("textbox", {
          name: /start_datetime/i,
        }),
      ).toHaveValue("2025-04-23 12:00PM EDT");

      expect(
        screen.getByRole("textbox", {
          name: /end_datetime/i,
        }),
      ).toHaveValue("2025-04-23 3:00PM EDT");
    });
  });

  test("loads an old maintenance window for cloning", async () => {
    mockQuery = {
      name: "Test",
      action: "Clone Test",
    };

    render(<Maintenance />).container;

    await waitFor(() => {
      expect(
        screen.getByPlaceholderText(/enter type of maintenance/i),
      ).toHaveValue("Test");

      expect(
        screen.getByRole("textbox", {
          name: /start_datetime/i,
        }),
      ).toHaveValue("");

      expect(
        screen.getByRole("textbox", {
          name: /end_datetime/i,
        }),
      ).toHaveValue("");
    });
  });

  test("schedules a 'start now' and 'no end date' maintenance window", async () => {
    render(<Maintenance />).container;

    api.http.fetchJson = mockFetch({
      response: {
        data: "success",
      },
    });

    await userEvent.type(
      screen.getByPlaceholderText(/enter type of maintenance/i),
      "Test",
    );

    await userEvent.click(screen.getByRole("radio", { name: /start now/i }));

    await userEvent.click(screen.getByRole("radio", { name: /no end date/i }));

    await userEvent.click(screen.getByRole("button", { name: /save/i }));

    await waitFor(() => {
      expect(api.http.fetchJson).toHaveBeenCalledWith(
        "/admin/flags/maintenance",
        {
          body: '{"enabled":true,"start":null,"end":null,"options":{"name":"Test"}}',
          headers: {
            "Content-Type": "application/json",
          },
          method: "PATCH",
        },
      );
    });
  });

  test("schedules a start and end date/time maintenance window for tomorrow", async () => {
    const tomorrow = dayjs().add(1, "day").format("YYYY-MM-DD");

    render(<Maintenance />).container;

    api.http.fetchJson = mockFetch({
      response: {
        data: "success",
      },
    });

    await userEvent.type(
      screen.getByPlaceholderText(/enter type of maintenance/i),
      "Test",
    );

    await selectStartDateTime(1, "15:15");

    await selectEndDateTime(1, "15:45");

    await waitFor(() => {
      expect(
        screen.getByRole("textbox", { name: /start_datetime/i }),
      ).toHaveValue(`${tomorrow} 3:15PM EDT`);

      expect(
        screen.getByRole("textbox", { name: /end_datetime/i }),
      ).toHaveValue(`${tomorrow} 3:45PM EDT`);
    });

    await userEvent.click(screen.getByRole("button", { name: /save/i }));

    await waitFor(() => {
      expect(api.http.fetchJson).toHaveBeenCalledWith(
        "/admin/flags/maintenance",
        {
          body: `{"enabled":true,"start":"${tomorrow}T15:15:00-04:00","end":"${tomorrow}T15:45:00-04:00","options":{"name":"Test"}}`,
          headers: {
            "Content-Type": "application/json",
          },
          method: "PATCH",
        },
      );
    });
  });

  test("schedules a start and end date/time maintenance window for next week", async () => {
    const nextWeek = dayjs().add(7, "day").format("YYYY-MM-DD");

    render(<Maintenance />).container;

    api.http.fetchJson = mockFetch({
      response: {
        data: "success",
      },
    });

    await userEvent.type(
      screen.getByPlaceholderText(/enter type of maintenance/i),
      "Test",
    );

    await selectStartDateTime(7, "15:15");

    await selectEndDateTime(7, "15:45");

    await waitFor(() => {
      expect(
        screen.getByRole("textbox", { name: /start_datetime/i }),
      ).toHaveValue(`${nextWeek} 3:15PM EDT`);

      expect(
        screen.getByRole("textbox", { name: /end_datetime/i }),
      ).toHaveValue(`${nextWeek} 3:45PM EDT`);
    });

    await userEvent.click(screen.getByRole("button", { name: /save/i }));

    await waitFor(() => {
      expect(api.http.fetchJson).toHaveBeenCalledWith(
        "/admin/flags/maintenance",
        {
          body: `{"enabled":true,"start":"${nextWeek}T15:15:00-04:00","end":"${nextWeek}T15:45:00-04:00","options":{"name":"Test"}}`,
          headers: {
            "Content-Type": "application/json",
          },
          method: "PATCH",
        },
      );
    });
  });

  test("schedules a start and end date/time maintenance window on different days", async () => {
    const tomorrow = dayjs().add(1, "day").format("YYYY-MM-DD");
    const dayAfterTomorrow = dayjs().add(2, "day").format("YYYY-MM-DD");

    render(<Maintenance />).container;

    api.http.fetchJson = mockFetch({
      response: {
        data: "success",
      },
    });

    await userEvent.type(
      screen.getByPlaceholderText(/enter type of maintenance/i),
      "Test",
    );

    await selectStartDateTime(1, "15:15");

    await selectEndDateTime(2, "15:45");

    await waitFor(() => {
      expect(
        screen.getByRole("textbox", { name: /start_datetime/i }),
      ).toHaveValue(`${tomorrow} 3:15PM EDT`);

      expect(
        screen.getByRole("textbox", { name: /end_datetime/i }),
      ).toHaveValue(`${dayAfterTomorrow} 3:45PM EDT`);
    });

    await userEvent.click(screen.getByRole("button", { name: /save/i }));

    await waitFor(() => {
      expect(api.http.fetchJson).toHaveBeenCalledWith(
        "/admin/flags/maintenance",
        {
          body: `{"enabled":true,"start":"${tomorrow}T15:15:00-04:00","end":"${dayAfterTomorrow}T15:45:00-04:00","options":{"name":"Test"}}`,
          headers: {
            "Content-Type": "application/json",
          },
          method: "PATCH",
        },
      );
    });
  });

  test("edits an active maintenance window", async () => {
    const tomorrow = dayjs().add(1, "day").format("YYYY-MM-DD");

    mockQuery = {
      name: "Test",
      start: `${tomorrow} 12:00:00 EDT`,
      end: `${tomorrow} 15:00:00 EDT`,
      action: "Edit Maintenance",
    };

    render(<Maintenance />).container;

    await waitFor(() => {
      expect(
        screen.getByRole("textbox", {
          name: /start_datetime/i,
        }),
      ).toHaveValue(`${tomorrow} 12:00PM EDT`);

      expect(
        screen.getByRole("textbox", {
          name: /end_datetime/i,
        }),
      ).toHaveValue(`${tomorrow} 3:00PM EDT`);
    });

    await selectStartDateTime(1, "15:15");

    await selectEndDateTime(1, "15:45");

    await userEvent.click(screen.getByRole("button", { name: /save/i }));

    await waitFor(() => {
      expect(api.http.fetchJson).toHaveBeenCalledWith(
        "/admin/flags/maintenance",
        {
          body: `{"enabled":true,"start":"${tomorrow}T15:15:00-04:00","end":"${tomorrow}T15:45:00-04:00","options":{"name":"Test"}}`,
          headers: {
            "Content-Type": "application/json",
          },
          method: "PATCH",
        },
      );
    });
  });

  test("validates that end date cannot be before start date", async () => {
    render(<Maintenance />).container;

    api.http.fetchJson = mockFetch({
      response: {
        data: "success",
      },
    });

    await userEvent.type(
      screen.getByPlaceholderText(/enter type of maintenance/i),
      "Test",
    );

    await selectStartDateTime(1, "15:45");

    await selectEndDateTime(1, "15:15");

    await waitFor(() => {
      expect(
        screen.getByText(/end cannot be before start/i),
      ).toBeInTheDocument();
    });
  });

  test("validates that end date cannot be in the past", async () => {
    mockQuery = {
      name: "Test",
      start: "2024-04-23 12:00:00 EDT",
      end: "2024-04-23 15:00:00 EDT",
      action: "Edit Maintenance",
    };

    render(<Maintenance />).container;

    await userEvent.click(screen.getByRole("button", { name: /save/i }));

    await waitFor(() => {
      expect(
        screen.getByText(/end cannot be in the past/i),
      ).toBeInTheDocument();
    });
  });
});
