// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Maintenance renders the page 1`] = `
<div>
  <div
    class="breadcrumb"
  >
    <svg
      aria-hidden="true"
      class="breadcrumb__icon"
      data-slot="icon"
      fill="currentColor"
      viewBox="0 0 24 24"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        clip-rule="evenodd"
        d="M7.72 12.53a.75.75 0 0 1 0-1.06l7.5-7.5a.75.75 0 1 1 1.06 1.06L9.31 12l6.97 6.97a.75.75 0 1 1-1.06 1.06l-7.5-7.5Z"
        fill-rule="evenodd"
      />
    </svg>
    <a
      class="breadcrumb__link"
      href="/maintenance"
    >
      Back to Maintenance
    </a>
  </div>
  <h1>
    Configure New Maintenance
  </h1>
  <div
    class="maintenance-configure"
  >
    <div
      class="maintenance-configure__description"
    >
      <p>
        The Portal includes the ability to have maintenance pages that we can turn on in case we need to shut down all or part of the website. When the times are set, they will be displayed to the user using their timezone and localization preferences.
      </p>
    </div>
    <form
      autocomplete="off"
      class="maintenance-configure__form"
    >
      <label
        class="maintenance-configure__label"
        for="name"
      >
        Name
        <span
          style="color: red;"
        >
          *
        </span>
      </label>
      <input
        class="maintenance-configure__input"
        label="Name"
        name="name"
        pattern="[a-zA-Z0-9 _\\-,]+"
        placeholder="Enter type of maintenance"
        required=""
        type="text"
        value=""
      />
      <div
        class="maintenance-configure__datetimes-wrapper"
      >
        <fieldset
          class="maintenance-configure__datetime-wrapper maintenance-configure__fieldset"
        >
          <legend>
            Start Date/Time
            <span
              style="color: red;"
            >
              *
            </span>
          </legend>
          <label
            class="maintenance-configure__radio-label maintenance-configure__label"
          >
            <input
              name="start_use_datetime"
              required=""
              type="radio"
              value="false"
            />
            Start Now
          </label>
          <label
            class="maintenance-configure__radio-label"
          >
            <input
              aria-label="schedule start datetime"
              name="start_use_datetime"
              type="radio"
              value="true"
            />
            Schedule
          </label>
          <div
            class="react-datepicker-wrapper"
          >
            <div
              class="react-datepicker__input-container"
            >
              <input
                aria-label="start_datetime"
                class="maintenance-configure__datetime maintenance-configure__input"
                placeholder="Choose Date/Time"
                readonly=""
                type="text"
                value=""
              />
              <svg
                aria-hidden="true"
                class="maintenance-configure__calendar-icon"
                data-slot="icon"
                fill="none"
                stroke="currentColor"
                stroke-width="1.5"
                tabindex="-1"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
          </div>
        </fieldset>
        <fieldset
          class="maintenance-configure__datetime-wrapper maintenance-configure__fieldset"
        >
          <legend>
            End Date/Time
            <span
              style="color: red;"
            >
              *
            </span>
          </legend>
          <label
            class="maintenance-configure__radio-label maintenance-configure__label"
          >
            <input
              name="end_use_datetime"
              required=""
              type="radio"
              value="false"
            />
            No End Date
          </label>
          <label
            class="maintenance-configure__radio-label maintenance-configure__label"
          >
            <input
              aria-label="schedule end datetime"
              name="end_use_datetime"
              type="radio"
              value="true"
            />
            Schedule
          </label>
          <div
            class="react-datepicker-wrapper"
          >
            <div
              class="react-datepicker__input-container"
            >
              <input
                aria-label="end_datetime"
                class="maintenance-configure__datetime maintenance-configure__input"
                placeholder="Choose Date/Time"
                readonly=""
                type="text"
                value=""
              />
              <svg
                aria-hidden="true"
                class="maintenance-configure__calendar-icon"
                data-slot="icon"
                fill="none"
                stroke="currentColor"
                stroke-width="1.5"
                tabindex="-1"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </div>
          </div>
        </fieldset>
      </div>
      <fieldset
        class="maintenance-configure__fieldset maintenance-configure__buttons"
      >
        <button
          class="maintenance-configure__btn maintenance-configure__btn--cancel btn btn--cancel"
          type="button"
        >
          Cancel
        </button>
        <button
          class="maintenance-configure__btn maintenance-configure__btn--submit btn"
          type="submit"
        >
          Save
        </button>
      </fieldset>
    </form>
  </div>
</div>
`;
