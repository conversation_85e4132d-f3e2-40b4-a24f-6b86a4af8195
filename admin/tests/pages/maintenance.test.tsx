import "@testing-library/jest-dom";

import { render, screen, waitFor, within } from "@testing-library/react";

import React, { act } from "react";
import userEvent from "@testing-library/user-event";
import mockFetch from "../test-utils/mockFetch";
import * as api from "../../src/_api";
import Maintenance from "../../src/pages/maintenance";

const mockRouteChangeHandler = jest.fn();

jest.mock("next/router", () => ({
  useRouter: () => ({
    push: mockRouteChangeHandler,
  }),
}));

describe("Maintenance", () => {
  beforeEach(() => {
    api.http.fetchJson = mockFetch({
      response: {
        data: [
          {
            enabled: true,
            end: "2025-04-23T07:00:00+00:00",
            first_name: "First",
            last_name: "Last",
            name: "maintenance",
            options: {
              name: "Test",
            },
            start: "2025-04-22T21:00:00+00:00",
            updated_at: "2025-04-22T11:30:18.575662+00:00",
          },
        ],
      },
    });
  });

  test("renders the page", async () => {
    api.http.fetchJson = mockFetch({
      response: {},
    });

    let component;

    await act(() => {
      component = render(<Maintenance />).container;
    });

    expect(component).toMatchSnapshot();
  });

  test("renders with no history", async () => {
    api.http.fetchJson = mockFetch({
      response: {},
    });

    await act(() => {
      render(<Maintenance />).container;
    });

    await waitFor(() => {
      expect(
        screen.getByRole("heading", { name: /history/i }),
      ).toBeInTheDocument();

      expect(screen.getByText(/no results found/i)).toBeInTheDocument();
    });
  });

  test("renders a link to configure a maintenance window", async () => {
    api.http.fetchJson = mockFetch({
      response: {},
    });

    await act(() => {
      render(<Maintenance />).container;
    });

    const configureLink = screen.getByRole("link", { name: /configure/i });

    await waitFor(() => {
      expect(configureLink).toHaveAttribute(
        "href",
        "/maintenance/add?action=Configure+New+Maintenance",
      );
    });
  });

  test("renders an enabled maintenance window", async () => {
    await act(() => {
      render(<Maintenance />).container;
    });

    await waitFor(() => {
      expect(screen.getByTestId("toggle")).toHaveTextContent(/on/i);

      expect(
        screen.getByRole("link", {
          name: /edit/i,
        }),
      ).toBeInTheDocument();

      expect(
        screen.getByRole("button", {
          name: /turn off/i,
        }),
      ).toBeInTheDocument();

      expect(
        screen.getByLabelText(/enabled maintenance name/i),
      ).toHaveTextContent(/test/i);

      expect(
        screen.getByLabelText(/enabled maintenance created by/i),
      ).toHaveTextContent(/first last/i);

      expect(
        screen.getByLabelText(/enabled maintenance duration/i),
      ).toHaveTextContent(
        /april 22, 2025 5:00pm edt to april 23, 2025 3:00am edt/i,
      );
    });
  });

  test("renders a history of maintenance windows", async () => {
    api.http.fetchJson = mockFetch({
      response: {
        data: [
          {
            enabled: true,
            end: "2025-04-23T07:00:00+00:00",
            first_name: "First",
            last_name: "Last",
            name: "maintenance",
            options: {
              name: "Test 4",
            },
            start: "2025-04-22T21:00:00+00:00",
            updated_at: "2025-04-22T11:30:18.575662+00:00",
          },
          {
            enabled: false,
            end: "2025-04-02T07:00:00+00:00",
            first_name: "First",
            last_name: "Last",
            name: "maintenance",
            options: {
              name: "Test 3",
            },
            start: "2025-04-01T21:00:00+00:00",
            updated_at: "2025-04-22T11:30:18.575662+00:00",
          },
          {
            enabled: false,
            end: "2025-04-04T07:00:00+00:00",
            first_name: "First",
            last_name: "Last",
            name: "maintenance",
            options: {
              name: "Test 2",
            },
            start: "2025-04-03T21:00:00+00:00",
            updated_at: "2025-04-22T11:30:18.575662+00:00",
          },
          {
            enabled: false,
            end: "2025-04-09T07:00:00+00:00",
            first_name: "First",
            last_name: "Last",
            name: "maintenance",
            options: {
              name: "Test 1",
            },
            start: "2025-04-08T21:00:00+00:00",
            updated_at: "2025-04-22T11:30:18.575662+00:00",
          },
        ],
      },
    });

    await act(() => {
      render(<Maintenance />).container;
    });

    await waitFor(() => {
      const rows = screen.getAllByRole("row", {
        name: /test \d/i,
      });

      expect(rows).toHaveLength(3);

      expect(
        within(rows[0]).getByRole("link", { name: /clone/i }),
      ).toHaveAttribute(
        "href",
        "/maintenance/add?name=Test+3&action=Clone+Test+3",
      );

      expect(
        within(rows[1]).getByRole("link", { name: /clone/i }),
      ).toHaveAttribute(
        "href",
        "/maintenance/add?name=Test+2&action=Clone+Test+2",
      );

      expect(
        within(rows[2]).getByRole("link", { name: /clone/i }),
      ).toHaveAttribute(
        "href",
        "/maintenance/add?name=Test+1&action=Clone+Test+1",
      );
    });
  });

  describe("Confirmation modal", () => {
    test("renders when the turn off button is clicked", async () => {
      await act(() => {
        render(<Maintenance />).container;
      });

      await userEvent.click(
        screen.getByRole("button", {
          name: /turn off/i,
        }),
      );

      await waitFor(() => {
        expect(
          screen.getByRole("heading", { name: /turn off maintenance\?/i }),
        ).toBeInTheDocument();

        expect(
          screen.getByRole("button", {
            name: /continue/i,
          }),
        ).toBeInTheDocument();

        expect(
          screen.getByRole("button", { name: /cancel/i }),
        ).toBeInTheDocument();
      });
    });

    test("is hidden when the cancel button is clicked", async () => {
      await act(() => {
        render(<Maintenance />).container;
      });

      await userEvent.click(
        screen.getByRole("button", {
          name: /turn off/i,
        }),
      );

      await waitFor(() => {
        expect(
          screen.getByRole("button", { name: /cancel/i }),
        ).toBeInTheDocument();
      });

      await userEvent.click(screen.getByRole("button", { name: /cancel/i }));

      await waitFor(() => {
        expect(
          screen.queryByRole("button", { name: /cancel/i }),
        ).not.toBeInTheDocument();
      });
    });

    test("calls patch API when the continue button is clicked", async () => {
      await act(() => {
        render(<Maintenance />).container;
      });

      await userEvent.click(
        screen.getByRole("button", {
          name: /turn off/i,
        }),
      );

      await userEvent.click(screen.getByRole("button", { name: /continue/i }));

      await waitFor(() => {
        expect(api.http.fetchJson).toHaveBeenCalledWith(
          "/admin/flags/maintenance",
          {
            body: '{"enabled":false}',
            headers: { "Content-Type": "application/json" },
            method: "PATCH",
          },
        );
      });
    });
  });
});
