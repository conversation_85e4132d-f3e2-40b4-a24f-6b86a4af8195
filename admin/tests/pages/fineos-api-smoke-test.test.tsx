import "@testing-library/jest-dom";

import { render, screen, waitFor, within } from "@testing-library/react";

import React from "react";
import userEvent from "@testing-library/user-event";
import FineosApiSmokeTest from "../../src/pages/fineos-api-smoke-test";
import mockFetch from "../test-utils/mockFetch";
import * as api from "../../src/_api";

const mockRouteChangeHandler = jest.fn();

jest.mock("next/router", () => ({
  useRouter: () => ({
    push: mockRouteChangeHandler,
  }),
}));

const dateTimeFormat =
  /^\d{1,2}\/\d{1,2}\/\d{4}, \d{1,2}:\d{1,2}:\d{1,2} (AM|PM)$/;
const fineosEndpoint = [
  ["Customer API", "/admin/smoke-test-fineos-customer-api"],
  [
    "Integration Services API",
    "/admin/smoke-test-fineos-integration-services-api",
  ],
  ["Group Client API", "/admin/smoke-test-fineos-group-client-api"],
];

describe("FineosApiSmokeTest", () => {
  test("renders the page", () => {
    const component = render(<FineosApiSmokeTest />).container;

    expect(component).toMatchSnapshot();
  });

  test("routes to the application root when the cancel button is clicked", async () => {
    render(<FineosApiSmokeTest />).container;

    await userEvent.click(screen.getByRole("button", { name: /cancel/i }));

    expect(mockRouteChangeHandler).toHaveBeenCalledWith("/");
  });

  describe("when the FINEOS API is available", () => {
    test.each([...fineosEndpoint])(
      "clicking the check button for the %s indicates success",
      async (apiName, apiEndpoint) => {
        render(<FineosApiSmokeTest />).container;

        api.http.fetchJson = mockFetch({
          response: {
            data: "success",
          },
        });

        expect(
          within(screen.getByLabelText(`${apiName} Status`)).queryByTitle(
            "Success Icon",
          ),
        ).not.toBeInTheDocument();

        await userEvent.click(
          screen.getByRole("button", {
            name: apiName,
          }),
        );

        await waitFor(() => {
          expect(api.http.fetchJson).toHaveBeenCalledWith(apiEndpoint, {});

          expect(
            within(screen.getByLabelText(`${apiName} Status`)).getByTitle(
              "Success Icon",
            ),
          ).toBeInTheDocument();

          expect(
            screen.getByLabelText(`${apiName} Last Checked`),
          ).toHaveTextContent(dateTimeFormat);

          expect(
            screen.getByRole("link", {
              name: `${apiName} View Logs`,
            }),
          ).toBeInTheDocument();
        });
      },
    );
  });
  describe("when the FINEOS API is not available", () => {
    test.each([...fineosEndpoint])(
      "clicking the check button for the %s indicates failure",
      async (apiName, apiEndpoint) => {
        render(<FineosApiSmokeTest />).container;

        api.http.fetchJson = jest.fn().mockImplementation(() => {
          throw new Error("Something went wrong!");
        });

        expect(
          within(screen.getByLabelText(`${apiName} Status`)).queryByTitle(
            "Failure Icon",
          ),
        ).not.toBeInTheDocument();

        await userEvent.click(
          screen.getByRole("button", {
            name: apiName,
          }),
        );

        await waitFor(() => {
          expect(api.http.fetchJson).toHaveBeenCalledWith(apiEndpoint, {});

          expect(
            within(screen.getByLabelText(`${apiName} Status`)).getByTitle(
              "Failure Icon",
            ),
          ).toBeInTheDocument();

          expect(
            screen.getByLabelText(`${apiName} Last Checked`),
          ).toHaveTextContent(dateTimeFormat);

          expect(
            screen.getByRole("link", {
              name: `${apiName} View Logs`,
            }),
          ).toBeInTheDocument();
        });
      },
    );
  });
});
