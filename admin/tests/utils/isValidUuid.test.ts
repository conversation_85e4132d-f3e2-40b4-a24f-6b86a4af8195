import isValidUuid from "../../src/utils/isValidUuid";

describe("isValidUuid", () => {
  it("returns false when the value is an empty string", () => {
    expect(isValidUuid("")).toBe(false);
  });

  it("returns false when the value is whitespace", () => {
    expect(isValidUuid("    ")).toBe(false);
  });

  it("returns false when the value is only alphanumeric", () => {
    expect(isValidUuid("abc123")).toBe(false);
  });

  it("returns false when the value is alphanumeric with dashes", () => {
    expect(isValidUuid("abc-123-ABC")).toBe(false);
  });

  it("returns true when provided a valid UUID V4", () => {
    expect(isValidUuid("d7cf59cd-3dde-48ee-a672-68ada47b30ef")).toBe(true);
    expect(isValidUuid("f66eeefe-dd7a-4f68-ab34-3df604c30444")).toBe(true);
    expect(isValidUuid("42e048d9-4dca-4dd4-8b68-eb924385ef7a")).toBe(true);
    expect(isValidUuid("D7CF59CD-3DDE-48EE-A672-68ADA47B30EF")).toBe(true);
    expect(isValidUuid("F66EEEFE-DD7A-4F68-AB34-3DF604C30444")).toBe(true);
    expect(isValidUuid("42E048D9-4DCA-4DD4-8B68-EB924385EF7A")).toBe(true);
  });
});
