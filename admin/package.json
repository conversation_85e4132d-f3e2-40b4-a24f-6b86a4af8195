{"name": "admin", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3001", "build": "NEXT_TELEMETRY_DISABLED=1 next build", "build:breakfix": "NEXT_PUBLIC_BUILD_ENV=breakfix npm run build", "build:infra-test": "NEXT_PUBLIC_BUILD_ENV=infra-test npm run build", "build:performance": "NEXT_PUBLIC_BUILD_ENV=performance npm run build", "build:prod": "NEXT_PUBLIC_BUILD_ENV=prod npm run build", "build:training": "NEXT_PUBLIC_BUILD_ENV=training npm run build", "build:trn2": "NEXT_PUBLIC_BUILD_ENV=trn2 npm run build", "build:uat": "NEXT_PUBLIC_BUILD_ENV=uat npm run build", "build:tst3": "NEXT_PUBLIC_BUILD_ENV=tst3 npm run build", "build:tst2": "NEXT_PUBLIC_BUILD_ENV=tst2 npm run build", "build:tst1": "NEXT_PUBLIC_BUILD_ENV=tst1 npm run build", "start": "next start", "generate:api": "npx @spec2ts/openapi-client ../api/openapi.yaml -o src/_api.ts", "type-check": "tsc --pretty --noEmit", "format": "prettier --write . && stylelint --fix styles/*.scss src/components/*.scss", "format-check:ci": "prettier --check .", "test": "jest", "test:coverage": "jest --coverage", "test:update-snapshots": "jest --updateSnapshot", "lint": "tsc --pretty --noEmit && next lint && stylelint styles/*.scss src/components/*.scss"}, "dependencies": {"@heroicons/react": "^2.2.0", "classnames": "^2.3.1", "dayjs": "^1.10.7", "formik": "^2.4.6", "next": "^15.2.4", "react": "^19.0.0", "react-datepicker": "^7.6.0", "react-dom": "^19.0.0", "react-helmet-async": "^2.0.5"}, "devDependencies": {"@babel/preset-typescript": "^7.26.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.4.6", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/classnames": "^2.3.1", "@types/jest": "^29.5.12", "@types/lodash": "^4.17.7", "@types/react": "^19.0.8", "@types/react-test-renderer": "^19.0.0", "babel-jest": "^29.7.0", "eslint-config-next": "^15.0.3", "jest-environment-jsdom": "^29.7.0", "next-router-mock": "^0.9.13", "prettier": "^3.4.2", "react-test-renderer": "^19.0.0", "sass": "^1.35.1", "stylelint": "^16.16.0", "stylelint-config-sass-guidelines": "^12.1.0", "stylelint-selector-bem-pattern": "^4.0.1", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.7.2"}, "overrides": {"semver": "7.5.2", "word-wrap": "1.2.4", "react": "^19.0.0", "@babel/helpers": "7.26.10", "@babel/runtime": "7.26.10"}}