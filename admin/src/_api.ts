/**
 * DO NOT MODIFY
 * Generated using @spec2ts/openapi-client.
 * See https://www.npmjs.com/package/@spec2ts/openapi-client
 */

/* eslint-disable */

export const defaults: RequestOptions = {
  baseUrl: "/v1",
};
export const servers = {
  developmentServer: "/v1",
};
export type RequestOptions = {
  baseUrl?: string;
  fetch?: typeof fetch;
  headers?: Record<string, string | undefined>;
} & Omit<RequestInit, "body" | "headers">;
export type ApiResponse<T> = {
  status: number;
  statusText: string;
  headers: Record<string, string>;
  data: T;
};
type Encoders = Array<(s: string) => string>;
type TagFunction = (strings: TemplateStringsArray, ...values: any[]) => string;
type FetchRequestOptions = RequestOptions & {
  body?: string | FormData;
};
type JsonRequestOptions = RequestOptions & {
  body: unknown;
};
type FormRequestOptions<T extends Record<string, unknown>> = RequestOptions & {
  body: T;
};
type MultipartRequestOptions = RequestOptions & {
  body: Record<string, any>; // string | Blob
};
/** Utilities functions */
export const _ = {
  // Encode param names and values as URIComponent
  encodeReserved: [encodeURI, encodeURIComponent],
  allowReserved: [encodeURI, encodeURI],
  /** Deeply remove all properties with undefined values. */
  stripUndefined<T extends Record<string, U | undefined>, U>(
    obj?: T,
  ): Record<string, U> | undefined {
    return obj && JSON.parse(JSON.stringify(obj));
  },
  isEmpty(v: unknown): boolean {
    return typeof v === "object" && !!v
      ? Object.keys(v).length === 0 && v.constructor === Object
      : v === undefined;
  },
  /** Creates a tag-function to encode template strings with the given encoders. */
  encode(encoders: Encoders, delimiter = ","): TagFunction {
    return (strings: TemplateStringsArray, ...values: any[]) => {
      return strings.reduce(
        (prev, s, i) => `${prev}${s}${q(values[i] ?? "", i)}`,
        "",
      );
    };
    function q(v: any, i: number): string {
      const encoder = encoders[i % encoders.length];
      if (typeof v === "object") {
        if (Array.isArray(v)) {
          return v.map(encoder).join(delimiter);
        }
        const flat = Object.entries(v).reduce(
          (flat, entry) => [...flat, ...entry],
          [] as any,
        );
        return flat.map(encoder).join(delimiter);
      }
      return encoder(String(v));
    }
  },
  /** Separate array values by the given delimiter. */
  delimited(
    delimiter = ",",
  ): (params: Record<string, any>, encoders?: Encoders) => string {
    return (params: Record<string, any>, encoders = _.encodeReserved) =>
      Object.entries(params)
        .filter(([, value]) => !_.isEmpty(value))
        .map(([name, value]) => _.encode(encoders, delimiter)`${name}=${value}`)
        .join("&");
  },
  /** Join URLs parts. */
  joinUrl(...parts: Array<string | undefined>): string {
    return parts
      .filter(Boolean)
      .join("/")
      .replace(/([^:]\/)\/+/, "$1");
  },
};
/** Functions to serialize query parameters in different styles. */
export const QS = {
  /** Join params using an ampersand and prepends a questionmark if not empty. */
  query(...params: string[]): string {
    const s = params.filter((p) => !!p).join("&");
    return s && `?${s}`;
  },
  /**
   * Serializes nested objects according to the `deepObject` style specified in
   * https://github.com/OAI/OpenAPI-Specification/blob/master/versions/3.0.0.md#style-values
   */
  deep(params: Record<string, any>, [k, v] = _.encodeReserved): string {
    const qk = _.encode([(s) => s, k]);
    const qv = _.encode([(s) => s, v]);
    // don't add index to arrays
    // https://github.com/expressjs/body-parser/issues/289
    const visit = (obj: any, prefix = ""): string =>
      Object.entries(obj)
        .filter(([, v]) => !_.isEmpty(v))
        .map(([prop, v]) => {
          const isValueObject = typeof v === "object";
          const index = Array.isArray(obj) && !isValueObject ? "" : prop;
          const key = prefix ? qk`${prefix}[${index}]` : prop;
          if (isValueObject) {
            return visit(v, key);
          }
          return qv`${key}=${v}`;
        })
        .join("&");
    return visit(params);
  },
  /**
   * Property values of type array or object generate separate parameters
   * for each value of the array, or key-value-pair of the map.
   * For other types of properties this property has no effect.
   * See https://github.com/OAI/OpenAPI-Specification/blob/master/versions/3.0.0.md#encoding-object
   */
  explode(params: Record<string, any>, encoders = _.encodeReserved): string {
    const q = _.encode(encoders);
    return Object.entries(params)
      .filter(([, value]) => typeof value !== "undefined")
      .map(([name, value]) => {
        if (Array.isArray(value)) {
          return value.map((v) => q`${name}=${v}`).join("&");
        }
        if (typeof value === "object") {
          return QS.explode(value, encoders);
        }
        return q`${name}=${value}`;
      })
      .join("&");
  },
  form: _.delimited(),
  pipe: _.delimited("|"),
  space: _.delimited("%20"),
};
/** Http request base methods. */
export const http = {
  async fetch(
    url: string,
    req?: FetchRequestOptions,
  ): Promise<ApiResponse<string | undefined>> {
    const {
      baseUrl,
      headers,
      fetch: customFetch,
      ...init
    } = { ...defaults, ...req };
    const href = _.joinUrl(baseUrl, url);
    const res = await (customFetch || fetch)(href, {
      ...init,
      headers: _.stripUndefined({ ...defaults.headers, ...headers }),
    });
    let text: string | undefined;
    try {
      text = await res.text();
    } catch (err) {
      /* ok */
    }
    if (!res.ok) {
      throw new HttpError(res.status, res.statusText, href, res.headers, text);
    }
    return {
      status: res.status,
      statusText: res.statusText,
      headers: http.headers(res.headers),
      data: text,
    };
  },
  async fetchJson(
    url: string,
    req: FetchRequestOptions = {},
  ): Promise<ApiResponse<any>> {
    const res = await http.fetch(url, {
      ...req,
      headers: {
        ...req.headers,
        Accept: "application/json",
      },
    });
    res.data = res.data && JSON.parse(res.data);
    return res;
  },
  async fetchVoid(
    url: string,
    req: FetchRequestOptions = {},
  ): Promise<ApiResponse<undefined>> {
    const res = await http.fetch(url, {
      ...req,
      headers: {
        ...req.headers,
        Accept: "application/json",
      },
    });
    return res as ApiResponse<undefined>;
  },
  json({ body, headers, ...req }: JsonRequestOptions): FetchRequestOptions {
    return {
      ...req,
      body: JSON.stringify(body),
      headers: {
        ...headers,
        "Content-Type": "application/json",
      },
    };
  },
  form<T extends Record<string, unknown>>({
    body,
    headers,
    ...req
  }: FormRequestOptions<T>): FetchRequestOptions {
    return {
      ...req,
      body: QS.form(body),
      headers: {
        ...headers,
        "Content-Type": "application/x-www-form-urlencoded",
      },
    };
  },
  multipart({ body, ...req }: MultipartRequestOptions): FetchRequestOptions {
    const data = new FormData();
    Object.entries(body).forEach(([name, value]) => {
      data.append(name, value);
    });
    return {
      ...req,
      body: data,
    };
  },
  headers(headers: Headers): Record<string, string> {
    const res: Record<string, string> = {};
    headers.forEach((value, key) => (res[key] = value));
    return res;
  },
};
export class HttpError extends Error {
  status: number;
  statusText: string;
  headers: Record<string, string>;
  data?: Record<string, unknown>;
  constructor(
    status: number,
    statusText: string,
    url: string,
    headers: Headers,
    text?: string,
  ) {
    super(`${url} - ${statusText} (${status})`);
    this.status = status;
    this.statusText = statusText;
    this.headers = http.headers(headers);
    if (text) {
      try {
        this.data = JSON.parse(text);
      } catch (err) {
        /* ok */
      }
    }
  }
}
/** Utility Type to extract returns type from a method. */
export type ApiResult<Fn> = Fn extends (
  ...args: any
) => Promise<ApiResponse<infer T>>
  ? T
  : never;
export interface Meta {
  resource: string;
  method: string;
  query?: object;
  paging?: {
    page_offset?: number;
    page_size?: number;
    total_records?: number;
    total_pages?: number;
    order_by?: string;
    order_direction?: string;
  };
}
export interface ValidationErrorDetail {
  type?: string;
  message?: string;
  rule?: (string | number | number | boolean | any | object) | null;
  field?: string | null;
  extra?: {
    [key: string]: string;
  };
}
export interface SuccessfulResponse {
  status_code: number;
  message?: string;
  meta?: Meta;
  data?: any | object;
  warnings?: ValidationErrorDetail[];
}
export interface ErrorResponse {
  status_code: number;
  message?: string;
  meta?: Meta;
  data?: any | object;
  warnings?: ValidationErrorDetail[];
  errors: ValidationErrorDetail[];
}
export interface Flag {
  start?: string | null;
  end?: string | null;
  name?: string;
  options?: object | null;
  enabled?: boolean;
}
export interface GETFlagsResponse {
  data?: Flag[];
}
export interface GETFlagsByNameResponse {
  data?: Flag;
}
export type Fein = string;
export interface UserCreateRequest {
  email_address?: string | null;
  password?: string | null;
  role?: {
    role_description?: "Claimant" | "Employer";
  };
  user_leave_administrator?: {
    employer_fein?: Fein | null;
  } | null;
}
export type MaskedPhoneNumber = string;
export interface MaskedPhone {
  int_code?: string | null;
  phone_number?: MaskedPhoneNumber | null;
  phone_type?: ("Cell" | "Fax" | "Phone") | null;
}
export interface RoleResponse {
  role_id?: number;
  role_description?: string;
}
export type AddedByUserResponse = {
  email_address?: string;
  first_name?: string | null;
  last_name?: string | null;
} | null;
export interface UserLeaveAdminResponse {
  email_address?: string;
  employer_id?: string;
  employer_fein?: string;
  employer_dba?: string | null;
  has_fineos_registration?: boolean;
  has_verification_data?: boolean;
  verified?: boolean;
  verification_type?: string | null;
  verified_at?: string | null;
  added_at?: string | null;
  added_by?: AddedByUserResponse | null;
  first_name?: string | null;
  last_name?: string | null;
  phone_number?: MaskedPhoneNumber | null;
  phone_extension?: string | null;
  organization_units?: any | null;
  user_leave_administrator_id?: string | null;
  user_leave_administrator_action_id?: string | null;
}
export interface UserResponse {
  user_id?: string;
  auth_id?: string;
  first_name?: string | null;
  last_name?: string | null;
  email_address?: string;
  application_names?: {
    first_name?: string | null;
    middle_name?: string | null;
    last_name?: string | null;
  }[];
  consented_to_data_sharing?: boolean;
  roles?: RoleResponse[];
  user_leave_administrators?: UserLeaveAdminResponse[];
  has_multiple_tax_identifiers?: boolean;
  consented_to_view_tax_documents?: boolean | null;
}
export interface RoleUserDeleteRequest {
  role: {
    role_description: string;
  };
  user_id: string;
}
export interface EmployerAddFeinRequestBody {
  employer_fein?: Fein | null;
}
export interface Phone {
  int_code?: string | null;
  phone_number?: string | null;
  e164?: string | null;
  phone_type?: ("Cell" | "Fax" | "Phone") | null;
  extension?: string | null;
}
export interface UserUpdateRequest {
  consented_to_data_sharing?: boolean;
  consented_to_view_tax_documents?: boolean;
  first_name?: string | null;
  last_name?: string | null;
  phone?: Phone;
}
export type DocumentType =
  | "Approval Notice"
  | "Denial Notice"
  | "Passport"
  | "Driver's License Mass"
  | "Driver's License Other State"
  | "Identification Proof"
  | "Absence Certification"
  | "State managed Paid Leave Confirmation"
  | "Own serious health condition form"
  | "Pregnancy/Maternity form"
  | "Child bonding evidence form"
  | "Care for a family member form"
  | "Military exigency form"
  | "Pending Application Withdrawn"
  | "Appeal Acknowledgment"
  | "Appeal Form"
  | "Appeal Notice - Claim Decision Affirmed"
  | "Appeal Notice - Claim Decision Changed"
  | "Appeals Supporting Documentation"
  | "Hearing Scheduled Notice"
  | "Maximum Weekly Benefit Change Notice"
  | "Benefit Amount Change Notice"
  | "Leave Allotment Change Notice"
  | "Approved Time Cancelled"
  | "Change Notice"
  | "Change Request Approved"
  | "Change Request Denied"
  | "Received Correspondence"
  | "Leave Request Review"
  | "Request for more Information"
  | "Additional Evidence Information eForm"
  | "Other Case Documentation"
  | "Other Income - current version"
  | "Other Leaves - current version"
  | "Inbound Payment Preference Modification Form"
  | "Outbound Payment Preference Modification Form"
  | "Returned Payment"
  | "Employer Response Additional Documentation"
  | "Employer Reimbursement Approval Notice"
  | "Employer Reimbursement Denial Notice"
  | " Employer Reimbursement Formstack"
  | "Overpayment Payoff Notice"
  | "Covered Service Member Identification Proof"
  | "Family Member Active Duty Service Proof"
  | "Pre-Review Summary Report"
  | "Payment Received-Updated Overpayment Balance"
  | "Reimbursement Request Form"
  | "Payment Plan Agreement"
  | "1099G Tax Form for Claimants"
  | "Direct Deposit/EFT form - GD"
  | "Bounced Payment"
  | "Overpayment Notice-Full Balance Demand";
export interface DocumentResponse {
  user_id: string;
  application_id?: string | null;
  appeal_id?: string | null;
  created_at: any;
  document_type: DocumentType | null;
  content_type?: string | null;
  fineos_document_id: string;
  name: string;
  description: string;
}
export interface GETUsersByUserIdDocumentsResponse extends SuccessfulResponse {
  data?: DocumentResponse[];
}
export interface UserChangeEmailRequest {
  email_address?: string;
}
export interface UserChangeEmailVerificationRequest {
  verification_code?: string;
}
export type SsnItin = string;
export type MassId = string;
export type MaskedDate = string;
export interface EmployeeResponse {
  employee_id: string;
  first_name?: string;
  middle_name?: string | null;
  other_name?: string | null;
  email_address?: string | null;
  last_name?: string;
  phone_numbers?: (Phone | null)[];
  tax_identifier_last4?: string | null;
  tax_identifier?: SsnItin | null;
  fineos_customer_number?: string | null;
  mass_id_number?: MassId | null;
  out_of_state_id_number?: string | null;
  date_of_birth?: MaskedDate | null;
  created_at?: any;
}
export interface GETEmployeesByEmployeeIdResponse extends SuccessfulResponse {
  data?: EmployeeResponse;
}
export type EmployeeErrorResponse = object;
export type NonMaskedSsnItin = string;
export interface EmployeeSearchRequestTerms {
  first_name?: string | null;
  last_name?: string | null;
  email_address?: string | null;
  phone_number?: string | null;
  tax_identifier?: NonMaskedSsnItin | null;
  fineos_customer_number?: string | null;
}
export interface EmployeeSearchRequestOrder {
  by?: "created_at" | "first_name" | "last_name" | "email_address";
  direction?: "ascending" | "descending";
}
export interface SearchRequestPaging {
  offset?: number;
  size?: number;
}
export interface EmployeeSearchRequest {
  terms: EmployeeSearchRequestTerms;
  order?: EmployeeSearchRequestOrder;
  paging?: SearchRequestPaging;
}
export type EmployeesResponse = EmployeeResponse[];
export interface POSTEmployeesSearchResponse extends SuccessfulResponse {
  data?: EmployeesResponse;
}
export interface EmployerResponse {
  employer_id?: string;
  employer_fein?: string;
  employer_dba?: string | null;
}
export interface POSTEmployersAddResponse extends SuccessfulResponse {
  data?: EmployerResponse;
}
export interface POSTEmployersAddResponse402 extends ErrorResponse {}
export interface POSTEmployersAddResponse409 extends ErrorResponse {}
export interface POSTEmployersAddResponse503 extends ErrorResponse {}
export interface WithholdingResponse {
  filing_period?: any;
}
export interface GETEmployersWithholdingByEmployerIdResponse
  extends SuccessfulResponse {
  data?: WithholdingResponse;
}
export interface EmployeeBasicResponse {
  employee_id: string;
  fineos_customer_number?: string | null;
  first_name?: string;
  middle_name?: string | null;
  last_name?: string;
  other_name?: string | null;
}
export type ReasonQualifierOne =
  | "Not Work Related"
  | "Work Related"
  | "Blood"
  | "Blood Stem Cell"
  | "Bone Marrow"
  | "Organ"
  | "Other"
  | "Postnatal Disability"
  | "Prenatal Care"
  | "Prenatal Disability"
  | "Adoption"
  | "Foster Care"
  | "Newborn"
  | "Pregnancy Related"
  | "Right to Leave"
  | "Serious Health Condition"
  | "Sickness - Non-Serious Health Condition"
  | "Childcare"
  | "Counseling"
  | "Financial & Legal Arrangements"
  | "Military Events & Related Activities"
  | "Other Additional Activities"
  | "Post Deployment Activities - Including Bereavement"
  | "Rest & Recuperation"
  | "Short Notice Deployment"
  | "Closure of School/Childcare"
  | "Quarantine/Isolation - Not Sick"
  | "Birth Disability"
  | "Childcare and School Activities"
  | "Parental Care"
  | "Post Deployment Activities";
export interface AbsencePeriodResponse {
  absence_period_start_date?: string;
  absence_period_end_date?: string;
  modified_start_date?: string | null;
  modified_end_date?: string | null;
  reason?:
    | (
        | "Care for a Family Member"
        | "Pregnancy/Maternity"
        | "Child Bonding"
        | "Serious Health Condition - Employee"
        | "Sickness - Non-Serious Health Condition - Employee"
        | "Military Caregiver"
        | "Military Exigency Family"
        | "Medical Donation - Employee"
        | "Preventative Care - Employee"
        | "Military - Employee"
        | "Personal - Employee"
      )
    | null;
  reason_qualifier_one?: ReasonQualifierOne | null;
  reason_qualifier_two?: string | null;
  period_type?: ("Continuous" | "Intermittent" | "Reduced Schedule") | null;
  request_decision?:
    | (
        | "Pending"
        | "In Review"
        | "Approved"
        | "Denied"
        | "Cancelled"
        | "Withdrawn"
        | "Projected"
        | "Voided"
      )
    | null;
  fineos_leave_request_id?: number | null;
  document_type_requirements?: DocumentType[] | null;
}

export interface EmployerReview {
  is_reviewable?: boolean;
  latest_follow_up_date?: string | null;
  earliest_follow_up_date?: string | null;
}
export interface DetailedClaimResponse {
  employer?: EmployerResponse | null;
  employee?: EmployeeBasicResponse | null;
  application_id?: string | null;
  fineos_absence_id?: string | null;
  fineos_notification_id?: string | null;
  created_at?: string | null;
  approval_date?: string | null;
  absence_periods?: AbsencePeriodResponse[] | null;
  employer_review?: EmployerReview;
  has_paid_payments?: boolean;
  payment_schedule_type?: ("Leave Start-Based" | "Sunday-Based") | null;
  document_requirements?:
    | {
        document_type?: DocumentType;
        upload_date?: string | null;
      }[]
    | null;
}
export interface GETClaimsByFineosAbsenceIdResponse extends SuccessfulResponse {
  data?: DetailedClaimResponse;
}
export type AbsenceCaseStatus =
  | "Adjudication"
  | "Approved"
  | "Closed"
  | "Completed"
  | "Declined"
  | "In Review"
  | "Intake In Progress";
export interface ManagedRequirementResponse {
  follow_up_date?: any;
  responded_at?: any;
  status?: string | null;
  category?: string | null;
  type?: string | null;
  created_at?: any;
}
export interface ClaimResponse {
  employer?: EmployerResponse | null;
  employee?: EmployeeBasicResponse | null;
  fineos_absence_id?: string | null;
  fineos_notification_id?: string | null;
  absence_period_start_date?: string | null;
  absence_period_end_date?: string | null;
  claim_status?: AbsenceCaseStatus | null;
  claim_type_description?: string | null;
  organization_unit_id?: string | null;
  created_at?: string | null;
  managed_requirements?: ManagedRequirementResponse[] | null;
  absence_periods?: AbsencePeriodResponse[] | null;
  has_paid_payments?: boolean;
  outstanding_evidence?: EmployerReview;
}
export type ClaimsResponse = ClaimResponse[];
export interface GETClaimsResponse extends SuccessfulResponse {
  data?: ClaimsResponse;
}
export interface ClaimSearchRequestTerms {
  employer_id?: string | string[];
  employee_id?: string | string[];
  search?: string;
  is_reviewable?: boolean | ("yes" | "no");
  request_decision?:
    | "approved"
    | "denied"
    | "withdrawn"
    | "pending"
    | "cancelled";
}
export interface ClaimSearchRequestOrder {
  by?: "created_at" | "employee" | "latest_follow_up_date";
  direction?: "ascending" | "descending";
}
export interface ClaimSearchRequest {
  terms: ClaimSearchRequestTerms;
  order?: ClaimSearchRequestOrder;
  paging?: SearchRequestPaging;
}
export interface POSTClaimsSearchResponse extends SuccessfulResponse {
  data?: ClaimsResponse;
}
export type Date = string;
export interface ChangeRequest {
  change_request_type?:
    | (
        | "Modification"
        | "Withdrawal"
        | "Medical To Bonding Transition"
        | "Cancellation"
        | "Extension"
      )
    | null;
  start_date?: Date | null;
  end_date?: Date | null;
  date_of_birth?: Date | null;
}
export interface ChangeRequestResponse {
  change_request_id?: string;
  fineos_absence_id?: string;
  change_request_type?:
    | (
        | "Modification"
        | "Withdrawal"
        | "Medical To Bonding Transition"
        | "Cancellation"
        | "Extension"
      )
    | null;
  start_date?: Date | null;
  end_date?: Date | null;
  date_of_birth?: Date | null;
  submitted_time?: string | null;
  documents_submitted_at?: string | null;
}
export interface POSTChangeRequestResponse extends SuccessfulResponse {
  data?: ChangeRequestResponse;
}
export type ChangeRequestsResponse = ChangeRequestResponse[];
export interface GETChangeRequestResponse extends SuccessfulResponse {
  data?: {
    absence_case_id?: string;
    change_requests?: ChangeRequestsResponse;
  };
}
export interface PATCHChangeRequestByChangeRequestIdResponse
  extends SuccessfulResponse {
  data?: ChangeRequestResponse;
}
export interface POSTChangeRequestByChangeRequestIdSubmitResponse
  extends SuccessfulResponse {
  data?: ChangeRequestResponse;
}
export interface DocumentUploadRequest {
  document_type:
    | "Passport"
    | "Driver's License Mass"
    | "Driver's License Other State"
    | "Identification Proof"
    | "State managed Paid Leave Confirmation"
    | "Approval Notice"
    | "Request for more Information"
    | "Denial Notice"
    | "Own serious health condition form"
    | "Pregnancy/Maternity form"
    | "Child bonding evidence form"
    | "Care for a family member form"
    | "Military exigency form"
    | "Pending Application Withdrawn"
    | "Appeal Acknowledgment"
    | "Maximum Weekly Benefit Change Notice"
    | "Benefit Amount Change Notice"
    | "Leave Allotment Change Notice"
    | "Approved Time Cancelled"
    | "Change Request Approved"
    | "Change Request Denied"
    | "Certification Form"
    | "Appeals Supporting Documentation"
    | "Covered Service Member Identification Proof"
    | "Family Member Active Duty Service Proof";
  name?: string;
  description?: string;
  file: Blob;
}
export interface POSTChangeRequestByChangeRequestIdDocumentsResponse
  extends SuccessfulResponse {
  data?: DocumentResponse;
}
export interface PaymentLineResponse {
  payment_line_id?: string;
  amount?: number;
  line_type?: string;
  line_type_category?:
    | "Other leave, income and benefits"
    | "Adjustments"
    | "Gross payment amount"
    | "Tax withholding"
    | "Net payment amount"
    | "Overpayment"
    | "Employer reimbursements"
    | "Child support";
}
export interface PaymentDetailsResponse {
  payment_detail_id?: string;
  period_start_date?: string | null;
  period_end_date?: string | null;
  amount?: number;
  business_net_amount?: number;
  payment_lines?: PaymentLineResponse[];
}
export interface PaymentResponse {
  payment_id?: string | null;
  period_start_date?: any;
  period_end_date?: any;
  amount?: number | null;
  sent_to_bank_date?: string | null;
  payment_method?: ("Elec Funds Transfer" | "Check") | null;
  expected_send_date_start?: string | null;
  expected_send_date_end?: string | null;
  cancellation_date?: string | null;
  status?: "Sent to bank" | "Pending" | "Cancelled" | "Delayed";
  writeback_transaction_status?: string | null;
  transaction_date?: string | null;
  transaction_date_could_change?: boolean;
  num_days_to_process_delay?: number | null;
  payment_details?: PaymentDetailsResponse[];
}
export interface PaymentsResponse {
  absence_case_id?: string;
  payments?: PaymentResponse[];
}
export interface GETPaymentsResponse extends SuccessfulResponse {
  data?: PaymentsResponse;
}
export interface ClaimDocumentResponse {
  created_at: string | null;
  document_type:
    | "State managed Paid Leave Confirmation"
    | "Approval Notice"
    | "Request for more Information"
    | "Denial Notice"
    | "Employer Response Additional Documentation"
    | "Own serious health condition form"
    | "Pregnancy/Maternity form"
    | "Child bonding evidence form"
    | "Care for a family member form"
    | "Military exigency form"
    | "Pending Application Withdrawn"
    | "Appeal Acknowledgment"
    | "Maximum Weekly Benefit Change Notice"
    | "Benefit Amount Change Notice"
    | "Leave Allotment Change Notice"
    | "Approved Time Cancelled"
    | "Change Request Approved"
    | "Change Request Denied"
    | "Covered Service Member Identification Proof"
    | "Family Member Active Duty Service Proof";
  content_type: string | null;
  fineos_document_id: string;
  name: string | null;
  description: string | null;
}
export interface GETEmployersClaimsByFineosAbsenceIdDocumentsResponse
  extends SuccessfulResponse {
  data?: ClaimDocumentResponse[];
}
export interface ConcurrentLeave {
  concurrent_leave_id?: string | null;
  is_for_current_employer?: boolean | null;
  leave_end_date?: Date | null;
  leave_start_date?: Date | null;
}
export interface EmployerBenefit {
  employer_benefit_id?: string | null;
  benefit_start_date?: Date | null;
  benefit_end_date?: Date | null;
  benefit_amount_dollars?: number | null;
  benefit_amount_frequency?:
    | ("Per Day" | "Per Week" | "Per Month" | "In Total" | "Unknown")
    | null;
  benefit_type?:
    | (
        | "Accrued paid leave"
        | "Short-term disability insurance"
        | "Permanent disability insurance"
        | "Family or medical leave insurance"
        | "Unknown"
      )
    | null;
  is_full_salary_continuous?: boolean | null;
}
export interface PreviousLeave {
  previous_leave_id?: string | null;
  is_for_current_employer?: boolean | null;
  leave_end_date?: Date | null;
  leave_start_date?: Date | null;
  leave_reason?:
    | (
        | "Pregnancy"
        | "Caring for a family member with a serious health condition"
        | "Bonding with my child after birth or placement"
        | "Caring for a family member who serves in the armed forces"
        | "Managing family affairs while a family member is on active duty in the armed forces"
        | "Unknown"
        | "An illness or injury"
      )
    | null;
  worked_per_week_minutes?: number | null;
  leave_minutes?: number | null;
  type?: ("other_reason" | "same_reason") | null;
}
export interface Address {
  city?: string | null;
  line_1?: string | null;
  line_2?: string | null;
  state?: string | null;
  zip?: string | null;
}
export type MaskedSsnItin = string;
export interface ComputedStartDates {
  other_reason?: string | null;
  same_reason?: string | null;
}
export interface ClaimReviewResponse {
  concurrent_leave?: ConcurrentLeave | null;
  date_of_birth?: MaskedDate;
  employer_benefits: EmployerBenefit[];
  employer_dba: string | null;
  employer_id: string;
  employer_fein: Fein;
  fineos_absence_id: string;
  first_name?: string;
  hours_worked_per_week?: number;
  last_name?: string;
  middle_name?: string | null;
  previous_leaves: PreviousLeave[];
  residential_address: Address;
  tax_identifier?: MaskedSsnItin;
  absence_periods: AbsencePeriodResponse[];
  managed_requirements?: ManagedRequirementResponse[];
  computed_start_dates?: ComputedStartDates;
  has_paid_payments?: boolean;
  payment_schedule_type?: ("Leave Start-Based" | "Sunday-Based") | null;
  approval_date?: string | null;
}
export interface GETEmployersClaimsByFineosAbsenceIdReviewResponse
  extends SuccessfulResponse {
  data?: ClaimReviewResponse;
}
export interface EmployerClaimReviewRequestEmployerBenefit
  extends EmployerBenefit {
  employer_changes?: "Added" | "Amended" | "Unchanged";
}
export interface EmployerClaimReviewRequestPreviousLeave extends PreviousLeave {
  employer_changes?: "Added" | "Amended" | "Unchanged";
}
export interface HoursWorkedPerWeek {
  hours_worked: number;
  employer_changes: "Added" | "Amended" | "Unchanged";
}
export interface EmployerClaimRequestBody {
  employer_benefits: EmployerClaimReviewRequestEmployerBenefit[];
  concurrent_leave?: {
    concurrent_leave_id?: string | null;
    is_for_current_employer?: boolean | null;
    leave_end_date?: Date | null;
    leave_start_date?: Date | null;
    employer_changes?: ("Added" | "Amended" | "Unchanged") | null;
  } | null;
  previous_leaves: EmployerClaimReviewRequestPreviousLeave[];
  has_amendments?: boolean;
  hours_worked_per_week: HoursWorkedPerWeek;
  employer_decision?: "Approve" | "Deny" | "Requires More Information";
  fraud?: "Yes" | "No";
  comment?: string;
  leave_reason?: string | null;
  believe_relationship_accurate?: ("Yes" | "No" | "Unknown") | null;
  relationship_inaccurate_reason?: string | null;
}
export interface UpdateClaimReviewResponse {
  claim_id: string;
}
export interface PATCHEmployersClaimsByFineosAbsenceIdReviewResponse
  extends SuccessfulResponse {
  data?: UpdateClaimReviewResponse;
}
export interface ApplicationImportRequestBody {
  absence_case_id?: string | null;
  tax_identifier?: SsnItin | null;
}
export interface OrganizationUnit {
  organization_unit_id: string;
  name: string;
}
export interface ReducedScheduleLeavePeriods {
  leave_period_id?: string | null;
  start_date?: Date | null;
  end_date?: Date | null;
  thursday_off_minutes?: number | null;
  friday_off_minutes?: number | null;
  saturday_off_minutes?: number | null;
  sunday_off_minutes?: number | null;
  monday_off_minutes?: number | null;
  tuesday_off_minutes?: number | null;
  wednesday_off_minutes?: number | null;
}
export interface ContinuousLeavePeriods {
  leave_period_id?: string | null;
  start_date?: Date | null;
  end_date?: Date | null;
  last_day_worked?: Date | null;
  expected_return_to_work_date?: Date | null;
  start_date_full_day?: boolean | null;
  start_date_off_hours?: number | null;
  start_date_off_minutes?: number | null;
  end_date_off_hours?: number | null;
  end_date_off_minutes?: number | null;
  end_date_full_day?: boolean | null;
}
export interface IntermittentLeavePeriods {
  leave_period_id?: string | null;
  start_date?: Date | null;
  end_date?: Date | null;
  frequency?: number | null;
  frequency_interval?: number | null;
  frequency_interval_basis?: ("Days" | "Weeks" | "Months") | null;
  duration?: number | null;
  duration_basis?: ("Minutes" | "Hours" | "Days") | null;
}
export type DateOrMaskedDate = string;
export interface CaringLeaveMetadata {
  family_member_first_name?: string | null;
  family_member_middle_name?: string | null;
  family_member_last_name?: string | null;
  family_member_date_of_birth?: DateOrMaskedDate | null;
  relationship_to_caregiver?:
    | (
        | "Parent"
        | "Child"
        | "Grandparent"
        | "Grandchild"
        | "Other Family Member"
        | "Service Member"
        | "Inlaw"
        | "Sibling - Brother/Sister"
        | "Other"
        | "Spouse"
      )
    | null;
}
export interface ApplicationLeaveDetails {
  reason?:
    | (
        | "Pregnancy/Maternity"
        | "Child Bonding"
        | "Serious Health Condition - Employee"
        | "Care for a Family Member"
        | "Military Caregiver"
        | "Military Exigency Family"
      )
    | null;
  reason_qualifier?: ReasonQualifierOne | null;
  reduced_schedule_leave_periods?: ReducedScheduleLeavePeriods[] | null;
  continuous_leave_periods?: ContinuousLeavePeriods[] | null;
  intermittent_leave_periods?: IntermittentLeavePeriods[] | null;
  caring_leave_metadata?: CaringLeaveMetadata | null;
  pregnant_or_recent_birth?: boolean | null;
  child_birth_date?: DateOrMaskedDate | null;
  child_placement_date?: DateOrMaskedDate | null;
  has_future_child_date?: boolean | null;
  employer_notified?: boolean | null;
  employer_notification_date?: Date | null;
  employer_notification_method?:
    | ("In Writing" | "In Person" | "By Telephone" | "Other")
    | null;
}
export type RoutingNbr = string;
export interface PaymentPreference {
  payment_method?: ("Elec Funds Transfer" | "Check") | null;
  account_number?: string | null;
  routing_number?: RoutingNbr | null;
  bank_account_type?: ("Checking" | "Savings") | null;
}
export type DayOfWeek =
  | "Sunday"
  | "Monday"
  | "Tuesday"
  | "Wednesday"
  | "Thursday"
  | "Friday"
  | "Saturday";
export interface WorkPatternDay {
  day_of_week?: DayOfWeek;
  week_number?: number;
  minutes?: number | null;
}
export interface WorkPattern {
  work_pattern_type?: ("Fixed" | "Rotating" | "Variable") | null;
  work_pattern_days?: WorkPatternDay[] | null;
}
export interface OtherIncome {
  other_income_id?: string | null;
  income_start_date?: Date | null;
  income_end_date?: Date | null;
  income_amount_dollars?: number | null;
  income_amount_frequency?:
    | ("Per Day" | "Per Week" | "Per Month" | "In Total" | "Unknown")
    | null;
  income_type?:
    | (
        | "Workers Compensation"
        | "Unemployment Insurance"
        | "SSDI"
        | "Disability benefits under Gov't retirement plan"
        | "Jones Act benefits"
        | "Railroad Retirement benefits"
        | "Earnings from another employment/self-employment"
        | "Unknown"
      )
    | null;
}
export interface BenefitYearResponse {
  employee_id?: string;
  benefit_year_start_date?: Date;
  benefit_year_end_date?: Date;
  current_benefit_year?: boolean;
  invalid_benefit_years_since?: Date | null;
}
export interface StartEndDates {
  start_date?: string;
  end_date?: string;
}
export interface ComputedApplicationSplit {
  crossed_benefit_year?: BenefitYearResponse;
  application_dates_in_benefit_year?: StartEndDates;
  application_dates_outside_benefit_year?: StartEndDates;
  application_outside_benefit_year_submittable_on?: string;
}
export interface ApplicationResponse {
  organization_unit_id?: string | null;
  application_id?: string;
  fineos_absence_id?: string | null;
  fineos_absence_status?: string | null;
  tax_identifier?: MaskedSsnItin | null;
  employee_id?: string | null;
  employer_id?: string | null;
  employer_fein?: string | null;
  first_name?: string | null;
  middle_name?: string | null;
  last_name?: string | null;
  date_of_birth?: MaskedDate | null;
  has_continuous_leave_periods?: boolean | null;
  has_employer_benefits?: boolean | null;
  has_intermittent_leave_periods?: boolean | null;
  has_reduced_schedule_leave_periods?: boolean | null;
  has_other_incomes?: boolean | null;
  has_submitted_payment_preference?: boolean | null;
  has_state_id?: boolean | null;
  has_mailing_address?: boolean | null;
  mailing_address?: Address | null;
  residential_address?: Address | null;
  mass_id?: string | null;
  employment_status?:
    | ("Employed" | "Unemployed" | "Self-Employed" | "Retired" | "Unknown")
    | null;
  occupation?:
    | ("Sales Clerk" | "Administrative" | "Engineer" | "Health Care")
    | null;
  employee_organization_units?: OrganizationUnit[];
  employer_organization_units?: OrganizationUnit[];
  organization_unit?: OrganizationUnit | null;
  organization_unit_selection?: ("not_listed" | "not_selected") | null;
  gender?:
    | (
        | "Woman"
        | "Man"
        | "Non-binary"
        | "Gender not listed"
        | "Prefer not to answer"
      )
    | null;
  race?:
    | (
        | "American Indian/Alaska Native"
        | "Asian/Asian American"
        | "Black/African American"
        | "Native Hawaiian/Other Pacific Islander"
        | "White"
        | "Prefer not to answer"
        | "Another race not listed above"
        | "Multiracial"
      )
    | null;
  race_custom?: string | null;
  ethnicity?:
    | ("Prefer not to answer" | "Hispanic or Latino" | "Not Hispanic or Latino")
    | null;
  hours_worked_per_week?: number | null;
  leave_details?: ApplicationLeaveDetails | null;
  payment_preference?: PaymentPreference[] | null;
  work_pattern?: WorkPattern | null;
  employer_benefits?: EmployerBenefit[] | null;
  other_incomes?: OtherIncome[] | null;
  imported_from_fineos_at?: string | null;
  updated_at?: string;
  updated_time?: string;
  status?: "Started" | "In Manual Review" | "Submitted" | "Completed";
  phone?: MaskedPhone | null;
  has_previous_leaves_other_reason?: boolean | null;
  has_previous_leaves_same_reason?: boolean | null;
  has_concurrent_leave?: boolean | null;
  previous_leaves_other_reason?: PreviousLeave[];
  previous_leaves_same_reason?: PreviousLeave[];
  concurrent_leave?: ConcurrentLeave | null;
  is_withholding_tax?: boolean | null;
  computed_start_dates?: ComputedStartDates;
  split_from_application_id?: string | null;
  split_into_application_id?: string | null;
  computed_earliest_submission_date?: string | null;
  computed_application_split?: ComputedApplicationSplit | null;
  computed_leave_details_is_editable?: boolean;
  computed_has_passed_manual_review?: boolean;
}
export interface POSTApplicationImportsResponse extends SuccessfulResponse {
  data?: ApplicationResponse;
}
export interface POSTApplicationImportsResponse503 extends ErrorResponse {
  data?: ApplicationResponse;
}
export interface POSTApplicationsResponse extends SuccessfulResponse {
  data?: ApplicationResponse;
}
export type ApplicationSearchResults = ApplicationResponse[];
export interface GETApplicationsResponse extends SuccessfulResponse {
  data?: ApplicationSearchResults;
}
export interface ApplicationUserNotFoundInfo {
  currently_employed?: boolean | null;
  date_of_hire?: Date | null;
  date_of_separation?: Date | null;
  employer_name?: string | null;
  recently_acquired_or_merged?: boolean | null;
}
export interface ApplicationRequestBody {
  organization_unit_selection?: ("not_listed" | "not_selected") | null;
  organization_unit_id?: string | null;
  tax_identifier?: SsnItin | null;
  employer_fein?: Fein | null;
  hours_worked_per_week?: number | null;
  first_name?: string | null;
  middle_name?: string | null;
  last_name?: string | null;
  date_of_birth?: DateOrMaskedDate | null;
  has_mailing_address?: boolean | null;
  mailing_address?: Address | null;
  residential_address?: Address | null;
  has_continuous_leave_periods?: boolean | null;
  has_employer_benefits?: boolean | null;
  has_intermittent_leave_periods?: boolean | null;
  has_other_incomes?: boolean | null;
  has_reduced_schedule_leave_periods?: boolean | null;
  has_state_id?: boolean | null;
  mass_id?: MassId | null;
  employment_status?:
    | ("Employed" | "Unemployed" | "Self-Employed" | "Retired" | "Unknown")
    | null;
  occupation?:
    | ("Sales Clerk" | "Administrative" | "Engineer" | "Health Care")
    | null;
  gender?:
    | (
        | "Woman"
        | "Man"
        | "Non-binary"
        | "Gender not listed"
        | "Prefer not to answer"
      )
    | null;
  race?:
    | (
        | "American Indian/Alaska Native"
        | "Asian/Asian American"
        | "Black/African American"
        | "Native Hawaiian/Other Pacific Islander"
        | "White"
        | "Prefer not to answer"
        | "Another race not listed above"
        | "Multiracial"
      )
    | null;
  race_custom?: string | null;
  ethnicity?:
    | ("Prefer not to answer" | "Hispanic or Latino" | "Not Hispanic or Latino")
    | null;
  leave_details?: ApplicationLeaveDetails;
  work_pattern?: WorkPattern | null;
  employer_benefits?: EmployerBenefit[] | null;
  other_incomes?: OtherIncome[] | null;
  phone?: Phone;
  has_previous_leaves_other_reason?: boolean | null;
  has_previous_leaves_same_reason?: boolean | null;
  has_concurrent_leave?: boolean | null;
  previous_leaves_other_reason?: PreviousLeave[] | null;
  previous_leaves_same_reason?: PreviousLeave[] | null;
  concurrent_leave?: ConcurrentLeave | null;
  additional_user_not_found_info?: ApplicationUserNotFoundInfo | null;
  industry_sector?:
    | "Accommodation and Food Services"
    | "Administrative and Support and Waste Management Remediation Services"
    | "Agriculture, Forestry, Fishing, and Hunting"
    | "Arts, Entertainment, and Recreation"
    | "Construction"
    | "Educational Services"
    | "Finance and Insurance"
    | "Health Care and Social Assistance"
    | "Information"
    | "Management of Companies and Enterprises"
    | "Manufacturing"
    | "Other Services (except Public Administration)"
    | "Public Administration"
    | "Professional, Scientific, and Technical Services"
    | "Real Estate Rental and Leasing"
    | "Retail Trade"
    | "Transportation and Warehousing"
    | "Utilities"
    | "Wholesale Trade";
}
export interface PATCHApplicationsByApplicationIdResponse
  extends SuccessfulResponse {
  data?: ApplicationResponse;
}
export interface POSTApplicationsByApplicationIdSubmitApplicationResponse
  extends SuccessfulResponse {
  data?: ApplicationResponse;
}
export interface POSTApplicationsByApplicationIdSubmitApplicationResponse503
  extends ErrorResponse {
  data?: ApplicationResponse;
}
export interface POSTApplicationsByApplicationIdCompleteApplicationResponse
  extends SuccessfulResponse {
  data?: ApplicationResponse;
}
export interface POSTApplicationsByApplicationIdCompleteApplicationResponse503
  extends ErrorResponse {
  data?: ApplicationResponse;
}
export interface GETApplicationsByApplicationIdDocumentsResponse
  extends SuccessfulResponse {
  data?: DocumentResponse[];
}
export interface POSTApplicationsByApplicationIdDocumentsResponse
  extends SuccessfulResponse {
  data?: DocumentResponse;
}
export interface PaymentPreferenceRequestBody {
  payment_preference?: PaymentPreference;
}
export interface POSTApplicationsByApplicationIdSubmitPaymentPreferenceResponse
  extends SuccessfulResponse {
  data?: ApplicationResponse;
}
export interface TaxWithholdingPreferenceRequestBody {
  is_withholding_tax?: boolean | null;
}
export interface POSTApplicationsByApplicationIdSubmitTaxWithholdingPreferenceResponse
  extends SuccessfulResponse {
  data?: ApplicationResponse;
}
export interface AppealCreateRequest {
  fineos_absence_id?: string;
  appeal_reason?: string | null;
}
export interface AppealResponse {
  appeal_id?: string;
  fineos_appeal_id?: string | null;
  fineos_absence_id?: string;
  appeal_phone_number?: MaskedPhone | null;
  appeal_reason?: string | null;
  for_private_insurance?: boolean | null;
  had_preventing_circumstances?: boolean | null;
  has_read_notices?: boolean | null;
  needs_interpreter?: boolean | null;
  interpreter_language_requested?: string | null;
  appeal_representative_option?: ("Yes" | "No" | "Unknown") | null;
  appeal_status?:
    | (
        | "Closed - Claim Decision Changed"
        | "Closed - Claim Decision Affirmed"
        | "Trigger Appeal Started Notification"
        | "Open - Conduct Hearing"
        | "Open - Schedule Hearing"
        | "Add Employer Participant Role"
      )
    | null;
  originally_decided_at?: string | null;
  originally_decided_at_reason_for_past_due?: string | null;
  other_interpreter_language_requested?: string | null;
  reason_for_not_reading_notices?: string | null;
  computed_is_more_than_ten_days_past_decision?: boolean | null;
  is_generated_from_extract?: boolean;
  submitted_at?: string | null;
  created_at?: string;
  updated_at?: string;
}
export interface POSTAppealsResponse extends SuccessfulResponse {
  data?: AppealResponse;
}
export interface GETAppealsByAppealIdResponse extends SuccessfulResponse {
  data?: AppealResponse;
}
export interface AppealUpdateRequest {
  appeal_phone_number?: Phone | null;
  appeal_reason?: string | null;
  for_private_insurance?: boolean | null;
  needs_interpreter?: boolean | null;
  interpreter_language_requested?: string | null;
  appeal_representative_option?: ("Yes" | "No" | "Unknown") | null;
  originally_decided_at?: string | null;
  originally_decided_at_reason_for_past_due?: string | null;
}
export interface PATCHAppealsByAppealIdResponse extends SuccessfulResponse {
  data?: AppealResponse;
}
export interface POSTAppealsByAppealIdCompleteResponse
  extends SuccessfulResponse {
  data?: AppealResponse;
}
export interface POSTAppealsByAppealIdCompleteResponse503
  extends ErrorResponse {
  data?: AppealResponse;
}
export interface AppealsSearchTerms {
  appeal_id?: string | null;
  fineos_appeal_id?: string | null;
  fineos_absence_id?: string | null;
  appeal_phone_number?: Phone | null;
}
export interface AppealsSearchRequest {
  terms?: AppealsSearchTerms | null;
}
export type AppealsResponse = AppealResponse[];
export interface POSTAppealsSearchResponse extends SuccessfulResponse {
  data?: AppealsResponse;
}
export interface GETAppealsByAppealIdDocumentsResponse
  extends SuccessfulResponse {
  data?: DocumentResponse[];
}
export interface POSTAppealsByAppealIdDocumentsResponse
  extends SuccessfulResponse {
  data?: DocumentResponse;
}
export interface ConfirmDocumentsRequest {
  fineos_document_ids?: string[] | null;
}
export interface POSTAppealsByAppealIdConfirmDocumentsResponse
  extends SuccessfulResponse {
  data?: AppealResponse;
}
export interface EligibilityRequest {
  tax_identifier: SsnItin;
  employer_fein: Fein;
  leave_start_date: Date;
  application_submitted_date: Date;
  employment_status:
    | "Employed"
    | "Unemployed"
    | "Self-Employed"
    | "Unknown"
    | "Retired";
  entitlement_period_start_date?: Date;
  absence_case_number?: string;
}
export interface EligibilityResponse {
  financially_eligible: boolean;
  description: string;
  total_wages?: number | null;
  state_average_weekly_wage?: number | null;
  unemployment_minimum?: number | null;
  employer_average_weekly_wage?: number | null;
}
export interface POSTFinancialEligibilityResponse extends SuccessfulResponse {
  data?: EligibilityResponse;
}
export interface RMVCheckRequest {
  absence_case_id: string;
  date_of_birth: Date;
  first_name: string;
  last_name: string;
  mass_id_number: MassId;
  residential_address_city: string;
  residential_address_line_1: string;
  residential_address_line_2?: string | null;
  residential_address_zip_code: string;
  ssn_last_4: string;
}
export interface RMVCheckResponse {
  verified: boolean;
  description: string;
}
export interface POSTRmvCheckResponse extends SuccessfulResponse {
  data?: RMVCheckResponse;
}
export interface NotificationRecipient {
  first_name?: string;
  last_name?: string;
  full_name?: string;
  contact_id?: string;
  email_address?: string;
}
export interface NotificationClaimant {
  first_name?: string;
  last_name?: string;
  date_of_birth?: Date;
  customer_id?: string;
}
export interface NotificationRequest {
  absence_case_id: string;
  fein: string;
  organization_name: string;
  document_type?: string;
  trigger: string;
  source: "Self-Service" | "Call Center";
  recipient_type: "Claimant" | "Leave Administrator";
  recipients: NotificationRecipient[];
  claimant_info: NotificationClaimant;
}
export interface POSTNotificationsResponse extends SuccessfulResponse {}
export interface VerificationRequest {
  employer_id: string;
  withholding_amount: number;
  withholding_quarter: string;
}
export interface BenefitYearsSearchTerms {
  employee_id?: string;
  current?: boolean;
  end_date_within?: string[];
}
export interface SearchRequestOrder {
  by?: "created_at";
  direction?: "ascending" | "descending";
}
export interface BenefitYearsSearchRequest {
  terms?: BenefitYearsSearchTerms | null;
  order?: SearchRequestOrder;
  paging?: SearchRequestPaging;
}
export interface POSTBenefitYearsSearchResponse extends SuccessfulResponse {
  data?: BenefitYearResponse[];
}
export type HolidaysSearchResponse = {
  name?: string;
  date?: Date;
}[];
export interface POSTHolidaysSearchResponse extends SuccessfulResponse {
  data?: HolidaysSearchResponse;
}
export interface AuthURIResponse {
  auth_uri?: string;
  claims_challenge?: string | null;
  code_verifier?: string;
  nonce?: string;
  redirect_uri?: string;
  scope?: string[];
  state?: string;
}
export interface AuthCodeResponse {
  code?: string;
  session_state?: string;
  state?: string;
}
export interface AdminTokenRequest {
  auth_uri_res?: AuthURIResponse;
  auth_code_res?: AuthCodeResponse;
}
export interface AdminTokenResponse {
  access_token?: string;
  refresh_token?: string;
  id_token?: string;
}
export interface AdminUserResponse {
  sub_id?: string;
  first_name?: string;
  last_name?: string;
  email_address?: string;
  groups?: string[];
  permissions?: string[];
}
export interface AdminLogoutResponse {
  logout_uri?: string;
}
export interface GETAdminUsersResponse {
  data?: UserResponse[];
}
export interface FlagWithLog extends Flag {
  first_name?: string;
  last_name?: string;
  updated_at?: string;
}
export interface GETAdminFlagLogsByNameResponse {
  data?: FlagWithLog[];
}
export interface GETCheckAddressValidationOverrideResponse {
  address_line_one: string;
  address_line_two: string | null;
  city: string;
  state: string;
  zip_code: string;
}
export interface ExperianReturn {
  verify_level?: string;
  picklist?: any | null;
  valid_address?: any | null;
}
export interface GETAdminValidateAddressResponse {
  data?: ExperianReturn[];
}
export interface OverpaymentCustomerDetailsResponse {
  vendor_customer_code?: string | null;
  first_name?: string | null;
  last_name?: string | null;
  legal_name?: string | null;
  address?: Address;
}
export interface VCMComparisonResponse {
  fineos_customer_number?: string;
  employee_id?: string;
  customer_active_status_name?: string | null;
  customer_approval_status_name?: string | null;
  pfml?: OverpaymentCustomerDetailsResponse;
  edm?: OverpaymentCustomerDetailsResponse;
}
export interface GETAdminOverpaymentVcmReportResponse {
  data?: VCMComparisonResponse[];
}
export interface OverpaymentSearchRequest {
  fineos_customer_number?: string;
}
export interface MmarsEventResponse {
  mmars_event_id: string;
  mmars_event_type_id: number;
  mmars_status_type_id: number;
  is_holdable: boolean;
  is_releasable: boolean;
  created_at?: string;
  updated_at?: string;
}
export interface OverpaymentCaseResponse {
  overpayment_id?: string;
  overpayment_casenumber: string;
  amount?: number;
  recovered_to_date_amount?: number;
  outstanding_amount?: number;
  overpayment_case_creation_date?: string;
  overpayment_adjustment_amount?: number;
  agreed_recovery_amount?: number;
  period_start_date?: string;
  period_end_date?: string;
  adjustment_description?: string;
  referral_date?: string;
  referral_status?: string;
  mmars_events?: MmarsEventResponse[];
}
export interface OverpaymentSearchResponse {
  employee_firstname?: string;
  employee_lastname?: string;
  fineos_customer_number?: string;
  overpayment_cases?: OverpaymentCaseResponse[];
  employee_address?: Address;
  prevent_referral?: boolean;
  referral_prevention_reason?: string;
}
export interface POSTAdminOverpaymentSearchResponse {
  data?: OverpaymentSearchResponse[];
}
export interface ReferOverpaymentRequest {
  overpayment_id: string;
}
export interface RetryOverpaymentTransactionRequest {
  mmars_event_id: string;
}
export interface OverpaymentMarkVcmReviewedRequest {
  employee_id: string;
}
export interface HoldOverpaymentTransactionRequest {
  mmars_event_id: string;
  reason?: string;
}
export interface OmniSearchRequest {
  search?: string;
}

export interface POSTAdminOmnisearchUsersResponse {
  data?: UserResponse[];
}
export interface POSTAdminOmnisearchAuthLogsResponse {
  data?: UserAuthLogResponse[];
}
export interface AuditLogResponse {
  admin_audit_log_id?: string;
  admin_user_id?: string;
  ticket_num?: string;
  record_type?: string;
  record_id?: string;
  state_before?: string;
  state_after?: string;
  created_at?: string;
}
export interface GETAdminAuditLogsResponse {
  data?: AuditLogResponse[];
}
export interface UserLeaveAdminRequest {
  user_id?: string | null;
  employer_id?: string | null;
}

export type UserAuthLogResponse = {
  user_id?: string;
  started_at?: string;
  completed_at?: string;
  auth_conversion_status_id?: number;
  oauth_operation?: string;
  meta_data?: {
    outcomes?: string[];
  };
} | null;
export interface GETAdminOmnisearchUsersByUserIdAuthLogResponse {
  data?: UserAuthLogResponse[];
}

export interface UserAuthConversionResponse {
  user_id?: string;
  auth_conversion_status_id?: string;
  auth_conversion_status?: string;
  auth_conversion_date?: string;
}
export interface UserAuthConversionRequest {
  user_auth_conversion_status_id?: string;
}
export interface AdminUpdateBaseRequest {
  ticket_num?: string;
}
export interface AdminUserUpdateRequest {
  auth_id?: string;
  email_address?: string;
  ticket_num?: string;
}
export interface POSTLeaveAdminsSearchResponse {
  data?: UserLeaveAdminResponse[];
}
export interface UserLeaveAdminAddRequest {
  email_address?: string;
  employer_id?: string;
}
export interface POSTLeaveAdminsByUserLeaveAdministratorIdDeactivateResponse {
  data?: UserLeaveAdminResponse;
}
export interface DeviceResponse {
  device_key?: string;
  created_at?: string;
  last_login?: string;
  device_name?: string;
}
export interface GETDevicesResponse {
  data?: DeviceResponse[];
}
export interface DELETEDevicesByDeviceKeyResponse {
  data?: DeviceResponse[];
}
/**
 * Get the API status
 */
export async function getStatus(
  options?: RequestOptions,
): Promise<ApiResponse<SuccessfulResponse>> {
  return await http.fetchJson("/status", {
    ...options,
  });
}
/**
 * Get feature flags
 */
export async function getFlags(
  options?: RequestOptions,
): Promise<ApiResponse<GETFlagsResponse>> {
  return await http.fetchJson("/flags", {
    ...options,
  });
}
/**
 * Get a feature flag
 */
export async function getFlagsByName(
  {
    name,
  }: {
    name: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<GETFlagsByNameResponse>> {
  return await http.fetchJson(`/flags/${name}`, {
    ...options,
  });
}
/**
 * Create a User account
 */
export async function postUsers(
  userCreateRequest: UserCreateRequest,
  options?: RequestOptions,
): Promise<ApiResponse<UserResponse>> {
  return await http.fetchJson(
    "/users",
    http.json({
      ...options,
      method: "POST",
      body: userCreateRequest,
    }),
  );
}
/**
 * Remove a role from a user
 */
export async function deleteRoles(
  roleUserDeleteRequest: RoleUserDeleteRequest,
  options?: RequestOptions,
): Promise<ApiResponse<SuccessfulResponse>> {
  return await http.fetchJson(
    "/roles",
    http.json({
      ...options,
      method: "DELETE",
      body: roleUserDeleteRequest,
    }),
  );
}
/**
 * Convert a User account to an employer role
 */
export async function postUsersByUser_idConvertEmployer(
  {
    user_id,
  }: {
    user_id: string;
  },
  employerAddFeinRequestBody: EmployerAddFeinRequestBody,
  options?: RequestOptions,
): Promise<ApiResponse<UserResponse>> {
  return await http.fetchJson(
    `/users/${user_id}/convert-employer`,
    http.json({
      ...options,
      method: "POST",
      body: employerAddFeinRequestBody,
    }),
  );
}
/**
 * Retrieve a User account
 */
export async function getUsersByUser_id(
  {
    user_id,
  }: {
    user_id: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<UserResponse>> {
  return await http.fetchJson(`/users/${user_id}`, {
    ...options,
  });
}
/**
 * Update a User account
 */
export async function patchUsersByUser_id(
  {
    user_id,
  }: {
    user_id: string;
  },
  userUpdateRequest: UserUpdateRequest,
  options?: RequestOptions,
): Promise<ApiResponse<UserResponse>> {
  return await http.fetchJson(
    `/users/${user_id}`,
    http.json({
      ...options,
      method: "PATCH",
      body: userUpdateRequest,
      headers: {
        ...options?.headers,
      },
    }),
  );
}
/**
 * Retrieve the User account corresponding to the currently authenticated user
 *
 */
export async function getUsersCurrent(
  options?: RequestOptions,
): Promise<ApiResponse<UserResponse>> {
  return await http.fetchJson("/users/current", {
    ...options,
  });
}
/**
 * List of customer documents retrieved from FINEOS for user.
 */
export async function getUsersByUser_idDocuments(
  {
    user_id,
    document_type,
  }: {
    user_id: string;
    document_type?: DocumentType;
  },
  options?: RequestOptions,
): Promise<ApiResponse<GETUsersByUserIdDocumentsResponse>> {
  return await http.fetchJson(
    `/users/${user_id}/documents${QS.query(
      QS.form({
        document_type,
      }),
    )}`,
    {
      ...options,
    },
  );
}
/**
 * Download a document by fineos_document_id.
 */
export async function getUsersByUser_idDocumentsAndFineos_document_id(
  {
    user_id,
    fineos_document_id,
  }: {
    user_id: string;
    fineos_document_id: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<string | undefined>> {
  return await http.fetch(`/users/${user_id}/documents/${fineos_document_id}`, {
    ...options,
  });
}
/**
 * Request to change User email address
 *
 */
export async function postChangeEmailRequests(
  userChangeEmailRequest: UserChangeEmailRequest,
  options?: RequestOptions,
): Promise<ApiResponse<UserResponse>> {
  return await http.fetchJson(
    "/change-email/requests",
    http.json({
      ...options,
      method: "POST",
      body: userChangeEmailRequest,
    }),
  );
}
/**
 * Verify User email change request
 *
 */
export async function postChangeEmailVerifications(
  userChangeEmailVerificationRequest: UserChangeEmailVerificationRequest,
  options?: RequestOptions,
): Promise<ApiResponse<UserResponse>> {
  return await http.fetchJson(
    "/change-email/verifications",
    http.json({
      ...options,
      method: "POST",
      body: userChangeEmailVerificationRequest,
    }),
  );
}
/**
 * Retrieve an Employee record
 */
export async function getEmployeesByEmployee_id(
  {
    employee_id,
  }: {
    employee_id: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<GETEmployeesByEmployeeIdResponse>> {
  return await http.fetchJson(`/employees/${employee_id}`, {
    ...options,
  });
}
/**
 * Lookup Employees
 */
export async function postEmployeesSearch(
  employeeSearchRequest: EmployeeSearchRequest,
  options?: RequestOptions,
): Promise<ApiResponse<POSTEmployeesSearchResponse>> {
  return await http.fetchJson(
    "/employees/search",
    http.json({
      ...options,
      method: "POST",
      body: employeeSearchRequest,
    }),
  );
}
/**
 * Add an FEIN to the logged in Leave Administrator
 */
export async function postEmployersAdd(
  employerAddFeinRequestBody: EmployerAddFeinRequestBody,
  options?: RequestOptions,
): Promise<ApiResponse<POSTEmployersAddResponse>> {
  return await http.fetchJson(
    "/employers/add",
    http.json({
      ...options,
      method: "POST",
      body: employerAddFeinRequestBody,
    }),
  );
}
/**
 * Retrieves the last withholding date for the FEIN specified
 */
export async function getEmployersWithholdingByEmployer_id(
  {
    employer_id,
  }: {
    employer_id: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<GETEmployersWithholdingByEmployerIdResponse>> {
  return await http.fetchJson(`/employers/withholding/${employer_id}`, {
    ...options,
  });
}
/**
 * Retrieve a claim for a specified absence ID
 */
export async function getClaimsByFineos_absence_id(
  {
    fineos_absence_id,
  }: {
    fineos_absence_id: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<GETClaimsByFineosAbsenceIdResponse>> {
  return await http.fetchJson(`/claims/${fineos_absence_id}`, {
    ...options,
  });
}
/**
 * Retrieve claims
 */
export async function getClaims(
  {
    page_size,
    page_offset,
    order_by,
    order_direction,
    employer_id,
    employee_id,
    search,
    is_reviewable,
    request_decision,
  }: {
    page_size?: number;
    page_offset?: number;
    order_by?: "created_at" | "employee" | "latest_follow_up_date";
    order_direction?: "ascending" | "descending";
    employer_id?: string[];
    employee_id?: string[];
    search?: string;
    is_reviewable?: "yes" | "no";
    request_decision?:
      | "approved"
      | "denied"
      | "withdrawn"
      | "pending"
      | "cancelled";
  } = {},
  options?: RequestOptions,
): Promise<ApiResponse<GETClaimsResponse>> {
  return await http.fetchJson(
    `/claims${QS.query(
      QS.form({
        page_size,
        page_offset,
        order_by,
        order_direction,
        employer_id,
        employee_id,
        search,
        is_reviewable,
        request_decision,
      }),
    )}`,
    {
      ...options,
    },
  );
}
/**
 * Retrieve claims
 */
export async function postClaimsSearch(
  claimSearchRequest: ClaimSearchRequest,
  options?: RequestOptions,
): Promise<ApiResponse<POSTClaimsSearchResponse>> {
  return await http.fetchJson(
    "/claims/search",
    http.json({
      ...options,
      method: "POST",
      body: claimSearchRequest,
    }),
  );
}
/**
 * Create a change request record
 */
export async function postChangeRequest(
  {
    fineos_absence_id,
  }: {
    fineos_absence_id: string;
  },
  changeRequest: ChangeRequest,
  options?: RequestOptions,
): Promise<ApiResponse<POSTChangeRequestResponse>> {
  return await http.fetchJson(
    `/change-request${QS.query(
      QS.form({
        fineos_absence_id,
      }),
    )}`,
    http.json({
      ...options,
      method: "POST",
      body: changeRequest,
    }),
  );
}
/**
 * Get list of change requests for a claim
 */
export async function getChangeRequest(
  {
    fineos_absence_id,
  }: {
    fineos_absence_id?: string;
  } = {},
  options?: RequestOptions,
): Promise<ApiResponse<GETChangeRequestResponse>> {
  return await http.fetchJson(
    `/change-request${QS.query(
      QS.form({
        fineos_absence_id,
      }),
    )}`,
    {
      ...options,
    },
  );
}
/**
 * Delete an in-progress ChangeRequest
 */
export async function deleteChangeRequestByChange_request_id(
  {
    change_request_id,
  }: {
    change_request_id: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<SuccessfulResponse>> {
  return await http.fetchJson(`/change-request/${change_request_id}`, {
    ...options,
    method: "DELETE",
  });
}
/**
 * Update an in-progress ChangeRequest
 */
export async function patchChangeRequestByChange_request_id(
  {
    change_request_id,
  }: {
    change_request_id: string;
  },
  changeRequest: ChangeRequest,
  options?: RequestOptions,
): Promise<ApiResponse<PATCHChangeRequestByChangeRequestIdResponse>> {
  return await http.fetchJson(
    `/change-request/${change_request_id}`,
    http.json({
      ...options,
      method: "PATCH",
      body: changeRequest,
    }),
  );
}
/**
 * Submit a completed change request to FINEOS
 */
export async function postChangeRequestByChange_request_idSubmit(
  {
    change_request_id,
  }: {
    change_request_id: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<POSTChangeRequestByChangeRequestIdSubmitResponse>> {
  return await http.fetchJson(`/change-request/${change_request_id}/submit`, {
    ...options,
    method: "POST",
  });
}
/**
 * Upload Document
 */
export async function postChangeRequestByChange_request_idDocuments(
  {
    change_request_id,
  }: {
    change_request_id: string;
  },
  documentUploadRequest: DocumentUploadRequest,
  options?: RequestOptions,
): Promise<ApiResponse<POSTChangeRequestByChangeRequestIdDocumentsResponse>> {
  return await http.fetchJson(
    `/change-request/${change_request_id}/documents`,
    http.multipart({
      ...options,
      method: "POST",
      body: documentUploadRequest,
    }),
  );
}
/**
 * Retrieve payments with status for a specified absence ID
 */
export async function getPayments(
  {
    absence_case_id,
  }: {
    absence_case_id: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<GETPaymentsResponse>> {
  return await http.fetchJson(
    `/payments${QS.query(
      QS.form({
        absence_case_id,
      }),
    )}`,
    {
      ...options,
    },
  );
}
/**
 * Retrieve a FINEOS documents for a specified absence ID and document ID
 */
export async function getEmployersClaimsByFineos_absence_idDocumentsAndFineos_document_id(
  {
    fineos_absence_id,
    fineos_document_id,
  }: {
    fineos_absence_id: string;
    fineos_document_id: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<string | undefined>> {
  return await http.fetch(
    `/employers/claims/${fineos_absence_id}/documents/${fineos_document_id}`,
    {
      ...options,
    },
  );
}
/**
 * Retrieve a list of FINEOS documents for a specified absence ID
 */
export async function getEmployersClaimsByFineos_absence_idDocuments(
  {
    fineos_absence_id,
  }: {
    fineos_absence_id: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<GETEmployersClaimsByFineosAbsenceIdDocumentsResponse>> {
  return await http.fetchJson(
    `/employers/claims/${fineos_absence_id}/documents`,
    {
      ...options,
    },
  );
}
/**
 * Retrieve FINEOS claim review data for a specified absence ID
 */
export async function getEmployersClaimsByFineos_absence_idReview(
  {
    fineos_absence_id,
  }: {
    fineos_absence_id: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<GETEmployersClaimsByFineosAbsenceIdReviewResponse>> {
  return await http.fetchJson(`/employers/claims/${fineos_absence_id}/review`, {
    ...options,
  });
}
/**
 * Save review claim from leave admin
 */
export async function patchEmployersClaimsByFineos_absence_idReview(
  {
    fineos_absence_id,
  }: {
    fineos_absence_id: string;
  },
  employerClaimRequestBody: EmployerClaimRequestBody,
  options?: RequestOptions,
): Promise<ApiResponse<PATCHEmployersClaimsByFineosAbsenceIdReviewResponse>> {
  return await http.fetchJson(
    `/employers/claims/${fineos_absence_id}/review`,
    http.json({
      ...options,
      method: "PATCH",
      body: employerClaimRequestBody,
    }),
  );
}
/**
 * Creates a new application in the PFML database from a FINEOS application that was created through the contact center.
 *
 */
export async function postApplicationImports(
  applicationImportRequestBody: ApplicationImportRequestBody,
  options?: RequestOptions,
): Promise<ApiResponse<POSTApplicationImportsResponse>> {
  return await http.fetchJson(
    "/application-imports",
    http.json({
      ...options,
      method: "POST",
      body: applicationImportRequestBody,
    }),
  );
}
/**
 * Create an Application
 */
export async function postApplications(
  options?: RequestOptions,
): Promise<ApiResponse<POSTApplicationsResponse>> {
  return await http.fetchJson("/applications", {
    ...options,
    method: "POST",
  });
}
/**
 * Retrieve all Applications for the specified user
 */
export async function getApplications(
  options?: RequestOptions,
): Promise<ApiResponse<GETApplicationsResponse>> {
  return await http.fetchJson("/applications", {
    ...options,
  });
}
/**
 * Retrieve an Application identified by the application id
 */
export async function getApplicationsByApplication_id(
  {
    application_id,
  }: {
    application_id: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<ApplicationResponse>> {
  return await http.fetchJson(`/applications/${application_id}`, {
    ...options,
  });
}
/**
 * Update an Application
 */
export async function patchApplicationsByApplication_id(
  {
    application_id,
  }: {
    application_id: string;
  },
  applicationRequestBody: ApplicationRequestBody,
  options?: RequestOptions,
): Promise<ApiResponse<PATCHApplicationsByApplicationIdResponse>> {
  return await http.fetchJson(
    `/applications/${application_id}`,
    http.json({
      ...options,
      method: "PATCH",
      body: applicationRequestBody,
    }),
  );
}
/**
 * Submit the first part of the application to the Claims Processing System.
 *
 */
export async function postApplicationsByApplication_idSubmitApplication(
  {
    application_id,
  }: {
    application_id: string;
  },
  options?: RequestOptions,
): Promise<
  ApiResponse<POSTApplicationsByApplicationIdSubmitApplicationResponse>
> {
  return await http.fetchJson(
    `/applications/${application_id}/submit-application`,
    {
      ...options,
      method: "POST",
    },
  );
}
/**
 * Complete intake of an application in the Claims Processing System.
 *
 */
export async function postApplicationsByApplication_idCompleteApplication(
  {
    application_id,
  }: {
    application_id: string;
  },
  options?: RequestOptions,
): Promise<
  ApiResponse<POSTApplicationsByApplicationIdCompleteApplicationResponse>
> {
  return await http.fetchJson(
    `/applications/${application_id}/complete-application`,
    {
      ...options,
      method: "POST",
    },
  );
}
/**
 * Download an application (case) document by id.
 */
export async function getApplicationsByApplication_idDocumentsAndDocument_id(
  {
    application_id,
    document_id,
  }: {
    application_id: string;
    document_id: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<string | undefined>> {
  return await http.fetch(
    `/applications/${application_id}/documents/${document_id}`,
    {
      ...options,
    },
  );
}
/**
 * Get list of documents for a case
 */
export async function getApplicationsByApplication_idDocuments(
  {
    application_id,
  }: {
    application_id: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<GETApplicationsByApplicationIdDocumentsResponse>> {
  return await http.fetchJson(`/applications/${application_id}/documents`, {
    ...options,
  });
}
/**
 * Upload Document
 */
export async function postApplicationsByApplication_idDocuments(
  {
    application_id,
  }: {
    application_id: string;
  },
  documentUploadRequest: DocumentUploadRequest,
  options?: RequestOptions,
): Promise<ApiResponse<POSTApplicationsByApplicationIdDocumentsResponse>> {
  return await http.fetchJson(
    `/applications/${application_id}/documents`,
    http.multipart({
      ...options,
      method: "POST",
      body: documentUploadRequest,
    }),
  );
}
/**
 * Submit Payment Preference
 */
export async function postApplicationsByApplication_idSubmitPaymentPreference(
  {
    application_id,
  }: {
    application_id: string;
  },
  paymentPreferenceRequestBody: PaymentPreferenceRequestBody,
  options?: RequestOptions,
): Promise<
  ApiResponse<POSTApplicationsByApplicationIdSubmitPaymentPreferenceResponse>
> {
  return await http.fetchJson(
    `/applications/${application_id}/submit-payment-preference`,
    http.json({
      ...options,
      method: "POST",
      body: paymentPreferenceRequestBody,
    }),
  );
}
/**
 * Submit Tax Withholding Preference
 */
export async function postApplicationsByApplication_idSubmitTaxWithholdingPreference(
  {
    application_id,
  }: {
    application_id: string;
  },
  taxWithholdingPreferenceRequestBody: TaxWithholdingPreferenceRequestBody,
  options?: RequestOptions,
): Promise<
  ApiResponse<POSTApplicationsByApplicationIdSubmitTaxWithholdingPreferenceResponse>
> {
  return await http.fetchJson(
    `/applications/${application_id}/submit-tax-withholding-preference`,
    http.json({
      ...options,
      method: "POST",
      body: taxWithholdingPreferenceRequestBody,
    }),
  );
}
/**
 * Create an appeal
 */
export async function postAppeals(
  appealCreateRequest: AppealCreateRequest,
  options?: RequestOptions,
): Promise<ApiResponse<POSTAppealsResponse>> {
  return await http.fetchJson(
    "/appeals",
    http.json({
      ...options,
      method: "POST",
      body: appealCreateRequest,
    }),
  );
}
/**
 * Retrieve an appeal identified by the appeal id
 */
export async function getAppealsByAppeal_id(
  {
    appeal_id,
  }: {
    appeal_id: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<GETAppealsByAppealIdResponse>> {
  return await http.fetchJson(`/appeals/${appeal_id}`, {
    ...options,
  });
}
/**
 * Update an appeal
 */
export async function patchAppealsByAppeal_id(
  {
    appeal_id,
  }: {
    appeal_id: string;
  },
  appealUpdateRequest: AppealUpdateRequest,
  options?: RequestOptions,
): Promise<ApiResponse<PATCHAppealsByAppealIdResponse>> {
  return await http.fetchJson(
    `/appeals/${appeal_id}`,
    http.json({
      ...options,
      method: "PATCH",
      body: appealUpdateRequest,
    }),
  );
}
/**
 * Complete creation of an appeal in FINEOS
 */
export async function postAppealsByAppeal_idComplete(
  {
    appeal_id,
  }: {
    appeal_id: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<POSTAppealsByAppealIdCompleteResponse>> {
  return await http.fetchJson(`/appeals/${appeal_id}/complete`, {
    ...options,
    method: "POST",
  });
}
/**
 * Retrieve appeals
 */
export async function postAppealsSearch(
  appealsSearchRequest: AppealsSearchRequest,
  options?: RequestOptions,
): Promise<ApiResponse<POSTAppealsSearchResponse>> {
  return await http.fetchJson(
    "/appeals/search",
    http.json({
      ...options,
      method: "POST",
      body: appealsSearchRequest,
    }),
  );
}
/**
 * Get a list of documents for an appeal case.
 */
export async function getAppealsByAppeal_idDocuments(
  {
    appeal_id,
  }: {
    appeal_id: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<GETAppealsByAppealIdDocumentsResponse>> {
  return await http.fetchJson(`/appeals/${appeal_id}/documents`, {
    ...options,
  });
}
/**
 * Upload an Appeal Document
 */
export async function postAppealsByAppeal_idDocuments(
  {
    appeal_id,
  }: {
    appeal_id: string;
  },
  documentUploadRequest: DocumentUploadRequest,
  options?: RequestOptions,
): Promise<ApiResponse<POSTAppealsByAppealIdDocumentsResponse>> {
  return await http.fetchJson(
    `/appeals/${appeal_id}/documents`,
    http.multipart({
      ...options,
      method: "POST",
      body: documentUploadRequest,
    }),
  );
}
/**
 * Confirms the uploaded appeal documents for review
 */
export async function postAppealsByAppeal_idConfirmDocuments(
  {
    appeal_id,
  }: {
    appeal_id: string;
  },
  confirmDocumentsRequest: ConfirmDocumentsRequest,
  options?: RequestOptions,
): Promise<ApiResponse<POSTAppealsByAppealIdConfirmDocumentsResponse>> {
  return await http.fetchJson(
    `/appeals/${appeal_id}/confirm-documents`,
    http.json({
      ...options,
      method: "POST",
      body: confirmDocumentsRequest,
    }),
  );
}
/**
 * Retrieve financial eligibility by SSN/ITIN, FEIN, leave start date, application submitted date and employment status.
 */
export async function postFinancialEligibility(
  eligibilityRequest: EligibilityRequest,
  options?: RequestOptions,
): Promise<ApiResponse<POSTFinancialEligibilityResponse>> {
  return await http.fetchJson(
    "/financial-eligibility",
    http.json({
      ...options,
      method: "POST",
      body: eligibilityRequest,
    }),
  );
}
/**
 * Perform lookup and data matching for information on RMV-issued IDs
 */
export async function postRmvCheck(
  rmvCheckRequest: RMVCheckRequest,
  options?: RequestOptions,
): Promise<ApiResponse<POSTRmvCheckResponse>> {
  return await http.fetchJson(
    "/rmv-check",
    http.json({
      ...options,
      method: "POST",
      body: rmvCheckRequest,
    }),
  );
}
/**
 * Send a notification that a document is available for a claimant to either the claimant or leave administrator.
 *
 */
export async function postNotifications(
  notificationRequest: NotificationRequest,
  options?: RequestOptions,
): Promise<ApiResponse<POSTNotificationsResponse>> {
  return await http.fetchJson(
    "/notifications",
    http.json({
      ...options,
      method: "POST",
      body: notificationRequest,
    }),
  );
}
/**
 * Check to see if user should be verified and create verification record
 *
 */
export async function postEmployersVerifications(
  verificationRequest: VerificationRequest,
  options?: RequestOptions,
): Promise<ApiResponse<UserResponse>> {
  return await http.fetchJson(
    "/employers/verifications",
    http.json({
      ...options,
      method: "POST",
      body: verificationRequest,
    }),
  );
}
/**
 * Returns all benefit years for the employee_id in the request; if no employee_id is present returns all benefit years for the current user.
 *
 */
export async function postBenefitYearsSearch(
  benefitYearsSearchRequest: BenefitYearsSearchRequest,
  options?: RequestOptions,
): Promise<ApiResponse<POSTBenefitYearsSearchResponse>> {
  return await http.fetchJson(
    "/benefit-years/search",
    http.json({
      ...options,
      method: "POST",
      body: benefitYearsSearchRequest,
    }),
  );
}
/**
 * Returns any holidays in a date range
 */
export async function postHolidaysSearch(
  body: {
    terms: {
      start_date?: Date;
      end_date?: Date;
    };
  },
  options?: RequestOptions,
): Promise<ApiResponse<POSTHolidaysSearchResponse>> {
  return await http.fetchJson(
    "/holidays/search",
    http.json({
      ...options,
      method: "POST",
      body,
    }),
  );
}
/**
 * Returns azure ad authentication url to initiate user auth code flow
 */
export async function getAdminAuthorize(
  options?: RequestOptions,
): Promise<ApiResponse<AuthURIResponse>> {
  return await http.fetchJson("/admin/authorize", {
    ...options,
  });
}
/**
 * Trade an authentication code for an access token
 */
export async function postAdminToken(
  adminTokenRequest: AdminTokenRequest,
  options?: RequestOptions,
): Promise<ApiResponse<AdminTokenResponse>> {
  return await http.fetchJson(
    "/admin/token",
    http.json({
      ...options,
      method: "POST",
      body: adminTokenRequest,
    }),
  );
}
/**
 * Login as admin user
 */
export async function getAdminLogin(
  options?: RequestOptions,
): Promise<ApiResponse<AdminUserResponse>> {
  return await http.fetchJson("/admin/login", {
    ...options,
  });
}
/**
 * Logout admin user
 */
export async function getAdminLogout(
  options?: RequestOptions,
): Promise<ApiResponse<AdminLogoutResponse>> {
  return await http.fetchJson("/admin/logout", {
    ...options,
  });
}
/**
 * Retrieve all user accounts
 */
export async function getAdminUsers(
  {
    page_size,
    page_offset,
    email_address,
  }: {
    page_size?: number;
    page_offset?: number;
    email_address?: string;
  } = {},
  options?: RequestOptions,
): Promise<ApiResponse<GETAdminUsersResponse>> {
  return await http.fetchJson(
    `/admin/users${QS.query(
      QS.form({
        page_size,
        page_offset,
        email_address,
      }),
    )}`,
    {
      ...options,
    },
  );
}
/**
 * Update a feature flag
 */
export async function patchAdminFlagsByName(
  {
    name,
  }: {
    name: string;
  },
  flag: Flag,
  options?: RequestOptions,
): Promise<ApiResponse<SuccessfulResponse>> {
  return await http.fetchJson(
    `/admin/flags/${name}`,
    http.json({
      ...options,
      method: "PATCH",
      body: flag,
    }),
  );
}
/**
 * Get logs for a feature flag
 */
export async function getAdminFlagLogsByName(
  {
    name,
  }: {
    name: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<GETAdminFlagLogsByNameResponse>> {
  return await http.fetchJson(`/admin/flag-logs/${name}`, {
    ...options,
  });
}

export async function getCheckAddressValidationOverride(
  {
    absence_case_id,
  }: {
    absence_case_id: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<GETCheckAddressValidationOverrideResponse>> {
  return await http.fetchJson(
    `/admin/check-address-validation-override/${absence_case_id}`,
    {
      ...options,
    },
  );
}

export async function patchConfirmAddressValidationOverride(
  {
    absence_case_id,
  }: {
    absence_case_id: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<SuccessfulResponse>> {
  return await http.fetchJson(
    `/admin/confirm-address-validation-override/${absence_case_id}`,
    http.json({
      ...options,
      method: "PATCH",
      body: {},
    }),
  );
}

export async function getSmokeTestFineosCustomerApi(
  options?: RequestOptions,
): Promise<ApiResponse<SuccessfulResponse>> {
  return await http.fetchJson(`/admin/smoke-test-fineos-customer-api`, {
    ...options,
  });
}

export async function getSmokeTestFineosIntegrationServicesApi(
  options?: RequestOptions,
): Promise<ApiResponse<SuccessfulResponse>> {
  return await http.fetchJson(
    `/admin/smoke-test-fineos-integration-services-api`,
    {
      ...options,
    },
  );
}

export async function getSmokeTestFineosGroupClientApi(
  options?: RequestOptions,
): Promise<ApiResponse<SuccessfulResponse>> {
  return await http.fetchJson(`/admin/smoke-test-fineos-group-client-api`, {
    ...options,
  });
}

/**
 * Validate/Suggest Address
 */
export async function getAdminValidateAddress(
  {
    address,
  }: {
    address: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<GETAdminValidateAddressResponse>> {
  return await http.fetchJson(
    `/admin/validate-address${QS.query(
      QS.form({
        address,
      }),
    )}`,
    {
      ...options,
    },
  );
}

/**
 * Used to display the overpayment VCM report
 */
export async function getAdminOverpaymentVcmReport(
  options?: RequestOptions,
): Promise<ApiResponse<GETAdminOverpaymentVcmReportResponse>> {
  return await http.fetchJson("/admin/overpayment/vcm-report", {
    ...options,
  });
}

/**
 * search for overpayments
 */
export async function postAdminOverpaymentSearch(
  overpaymentSearchRequest: OverpaymentSearchRequest,
  options?: RequestOptions,
): Promise<ApiResponse<POSTAdminOverpaymentSearchResponse>> {
  return await http.fetchJson(
    "/admin/overpaymentsearch",
    http.json({
      ...options,
      method: "POST",
      body: overpaymentSearchRequest,
    }),
  );
}
/**
 * Refer an overpayment
 */
export async function postAdminOverpaymentRefer(
  referOverpaymentRequest: ReferOverpaymentRequest,
  options?: RequestOptions,
): Promise<ApiResponse<OverpaymentCaseResponse>> {
  return await http.fetchJson(
    "/admin/overpayment/refer",
    http.json({
      ...options,
      method: "POST",
      body: referOverpaymentRequest,
    }),
  );
}
/**
 * Marks VCM Required mmars event as reviewed (VCC Pending)
 */
export async function postAdminOverpaymentMarkVcmReviewed(
  overpaymentMarkVcmReviewedRequest: OverpaymentMarkVcmReviewedRequest,
  options?: RequestOptions,
): Promise<ApiResponse<SuccessfulResponse>> {
  return await http.fetchJson(
    "/admin/overpayment/mark-vcm-reviewed",
    http.json({
      ...options,
      method: "POST",
      body: overpaymentMarkVcmReviewedRequest,
    }),
  );
}
/**
 * Retry activating an overpayment transaction from an error stage
 */
export async function postAdminOverpaymentRetryTransaction(
  retryOverpaymentTransactionRequest: RetryOverpaymentTransactionRequest,
  options?: RequestOptions,
): Promise<ApiResponse<MmarsEventResponse>> {
  return await http.fetchJson(
    "/admin/overpayment/retry-transaction",
    http.json({
      ...options,
      method: "POST",
      body: retryOverpaymentTransactionRequest,
    }),
  );
}
/**
 * Hold an overpayment transaction
 */
export async function postAdminOverpaymentHoldTransaction(
  holdOverpaymentTransactionRequest: HoldOverpaymentTransactionRequest,
  options?: RequestOptions,
): Promise<ApiResponse<MmarsEventResponse>> {
  return await http.fetchJson(
    "/admin/overpayment/hold-transaction",
    http.json({
      ...options,
      method: "POST",
      body: holdOverpaymentTransactionRequest,
    }),
  );
}
/**
 * Releases the hold from an overpayment transaction
 */
export async function postAdminOverpaymentReleaseTransaction(
  holdOverpaymentTransactionRequest: HoldOverpaymentTransactionRequest,
  options?: RequestOptions,
): Promise<ApiResponse<MmarsEventResponse>> {
  return await http.fetchJson(
    "/admin/overpayment/release-transaction",
    http.json({
      ...options,
      method: "POST",
      body: holdOverpaymentTransactionRequest,
    }),
  );
}
/**
 * search for users
 */
export async function postAdminOmnisearchUsers(
  omniSearchRequest: OmniSearchRequest,
  options?: RequestOptions,
): Promise<ApiResponse<POSTAdminOmnisearchUsersResponse>> {
  return await http.fetchJson(
    "/admin/omnisearch/users",
    http.json({
      ...options,
      method: "POST",
      body: omniSearchRequest,
    }),
  );
}
/**
 * search for users
 */
export async function getAdminOmnisearchUsersByUser_id(
  {
    user_id,
  }: {
    user_id: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<UserResponse>> {
  return await http.fetchJson(`/admin/omnisearch/users/${user_id}`, {
    ...options,
  });
}
/**
 * User Oauth Logs Detail
 */
export async function getAdminOmnisearchUsersByUser_idAuthLog(
  {
    user_id,
  }: {
    user_id: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<GETAdminOmnisearchUsersByUserIdAuthLogResponse>> {
  return await http.fetchJson(`/admin/omnisearch/users/${user_id}/auth-log`, {
    ...options,
  });
}
/**
 * User conversion Detail
 */
export async function getAdminOmnisearchUsersByUser_idAuthConversion(
  {
    user_id,
  }: {
    user_id: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<UserAuthConversionResponse>> {
  return await http.fetchJson(
    `/admin/omnisearch/users/${user_id}/auth-conversion`,
    {
      ...options,
    },
  );
}
/**
 * User Oauth Detail
 */
export async function postAdminOmnisearchUsersByUser_idAuthConversion(
  {
    user_id,
  }: {
    user_id: string;
  },
  userAuthConversionRequest: UserAuthConversionRequest,
  options?: RequestOptions,
): Promise<ApiResponse<SuccessfulResponse>> {
  return await http.fetchJson(
    `/admin/omnisearch/users/${user_id}/auth-conversion`,
    http.json({
      ...options,
      method: "POST",
      body: userAuthConversionRequest,
    }),
  );
}

/**
 * Search user auth logs
 */
export async function postAdminOmnisearchAuthLogs(
  omniSearchRequest: OmniSearchRequest,
  options?: RequestOptions,
): Promise<ApiResponse<POSTAdminOmnisearchAuthLogsResponse>> {
  return await http.fetchJson(
    "/admin/omnisearch/auth-logs",
    http.json({
      ...options,
      method: "POST",
      body: omniSearchRequest,
    }),
  );
}

/**
/**
 * Update a User account
 */
export async function patchAdminUsersByUser_id(
  {
    user_id,
  }: {
    user_id: string;
  },
  adminUserUpdateRequest: AdminUserUpdateRequest,
  options?: RequestOptions,
): Promise<ApiResponse<UserResponse>> {
  return await http.fetchJson(
    `/admin/users/${user_id}`,
    http.json({
      ...options,
      method: "PATCH",
      body: adminUserUpdateRequest,
    }),
  );
}
/**
 * Add a role for a user
 */
export async function postAdminUsersByUser_idRolesAndRole_id(
  {
    user_id,
    role_id,
  }: {
    user_id: string;
    role_id: string;
  },
  adminUpdateBaseRequest: AdminUpdateBaseRequest,
  options?: RequestOptions,
): Promise<ApiResponse<SuccessfulResponse>> {
  return await http.fetchJson(
    `/admin/users/${user_id}/roles/${role_id}`,
    http.json({
      ...options,
      method: "POST",
      body: adminUpdateBaseRequest,
    }),
  );
}
/**
 * Remove a role from a user
 */
export async function deleteAdminUsersByUser_idRolesAndRole_id(
  {
    user_id,
    role_id,
  }: {
    user_id: string;
    role_id: string;
  },
  adminUpdateBaseRequest: AdminUpdateBaseRequest,
  options?: RequestOptions,
): Promise<ApiResponse<SuccessfulResponse>> {
  return await http.fetchJson(
    `/admin/users/${user_id}/roles/${role_id}`,
    http.json({
      ...options,
      method: "DELETE",
      body: adminUpdateBaseRequest,
    }),
  );
}
/**
 * Get audit logs.
 */
export async function getAdminAuditLogs(
  {
    filter_id,
  }: {
    filter_id?: string;
  } = {},
  options?: RequestOptions,
): Promise<ApiResponse<GETAdminAuditLogsResponse>> {
  return await http.fetchJson(
    `/admin/audit-logs${QS.query(
      QS.form({
        filter_id,
      }),
    )}`,
    {
      ...options,
    },
  );
}
/**
 * Returns leave administrators filtered based on key passed into request body
 *
 */
export async function postLeaveAdminsSearch(
  userLeaveAdminRequest: UserLeaveAdminRequest,
  options?: RequestOptions,
): Promise<ApiResponse<POSTLeaveAdminsSearchResponse>> {
  return await http.fetchJson(
    "/leave-admins/search",
    http.json({
      ...options,
      method: "POST",
      body: userLeaveAdminRequest,
    }),
  );
}
/**
 * Add Leave Administrator to employer
 *
 */
export async function postLeaveAdminsAdd(
  userLeaveAdminAddRequest: UserLeaveAdminAddRequest,
  options?: RequestOptions,
): Promise<ApiResponse<SuccessfulResponse>> {
  return await http.fetchJson(
    "/leave-admins/add",
    http.json({
      ...options,
      method: "POST",
      body: userLeaveAdminAddRequest,
    }),
  );
}
/**
 * Deactivates a partciular leave administrator
 *
 */
export async function postLeaveAdminsByUser_leave_administrator_idDeactivate(
  {
    user_leave_administrator_id,
  }: {
    user_leave_administrator_id: string;
  },
  options?: RequestOptions,
): Promise<
  ApiResponse<POSTLeaveAdminsByUserLeaveAdministratorIdDeactivateResponse>
> {
  return await http.fetchJson(
    `/leave-admins/${user_leave_administrator_id}/deactivate`,
    {
      ...options,
      method: "POST",
    },
  );
}
/**
 * Returns currently remembered devices for the particular user's account
 *
 */
export async function getDevices(
  options?: RequestOptions,
): Promise<ApiResponse<GETDevicesResponse>> {
  return await http.fetchJson("/devices", {
    ...options,
  });
}
/**
 * Forgets a specific remembered device
 *
 */
export async function deleteDevicesByDevice_key(
  {
    device_key,
  }: {
    device_key: string;
  },
  options?: RequestOptions,
): Promise<ApiResponse<DELETEDevicesByDeviceKeyResponse>> {
  return await http.fetchJson(`/devices/${device_key}`, {
    ...options,
    method: "DELETE",
  });
}
