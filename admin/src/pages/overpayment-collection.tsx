import React, { useState } from "react";
import OverpaymentCollectionSearch from "./overpayment_collection/OverpaymentCollectionSearch";
import { StaticPropsPermissions } from "../menus";
import { OverpaymentSearchResponse } from "../api";
import VCMReport from "./overpayment_collection/VCMReport";

export default function OverpaymentSearch() {
  const [search, setSearchString] = useState("");
  const [overpaymentData, setOverpaymentData] =
    useState<OverpaymentSearchResponse | null>(null);

  const [activeTab, setActiveTab] = useState("Overpayment Search"); // State to track the active tab

  return (
    <div>
      {/* Tab Navigation */}
      <div className="overpayment__tabs">
        <button
          className={
            activeTab === "Overpayment Search"
              ? "overpayment__tab active"
              : "overpayment__tab"
          }
          onClick={() => setActiveTab("Overpayment Search")}
        >
          Overpayment Search
        </button>
        <button
          className={
            activeTab === "VCM Report"
              ? "overpayment__tab active"
              : "overpayment__tab"
          }
          onClick={() => setActiveTab("VCM Report")}
        >
          VCM Report
        </button>
      </div>

      {/* Tab Content */}
      <div className="overpayment__tab-content">
        {activeTab === "Overpayment Search" && (
          <OverpaymentCollectionSearch
            setSearchString={setSearchString}
            search={search}
            overpaymentData={overpaymentData}
            setOverpaymentData={setOverpaymentData}
          />
        )}
        {activeTab === "VCM Report" && <VCMReport />}
      </div>
    </div>
  );
}

export async function getStaticProps(): Promise<StaticPropsPermissions> {
  return {
    props: {
      permissions: ["OVERPAYMENT_COLLECTION_READ"],
    },
  };
}
