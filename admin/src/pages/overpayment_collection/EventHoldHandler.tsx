import React, { useState } from "react";
import { PauseIcon, PlayIcon } from "@heroicons/react/24/solid";
import ConfirmationDialog from "../../components/ConfirmationDialog";
import {
  HoldOverpaymentTransactionRequest,
  MmarsEventResponse,
  postAdminOverpaymentHoldTransaction,
  postAdminOverpaymentReleaseTransaction,
} from "../../api";

interface EventHoldHandlerProps {
  event: MmarsEventResponse;
  fetchAndRenderOverpaymentData: (search: string) => void;
  fineosCustomerNumber: string | null;
  setErrorMessage: (message: string | null) => void;
  setShowErrorAlert: (show: boolean) => void;
}

const EventHoldHandler: React.FC<EventHoldHandlerProps> = ({
  event,
  fetchAndRenderOverpaymentData,
  fineosCustomerNumber,
  setErrorMessage,
  setShowErrorAlert,
}) => {
  const [showPauseDialog, setShowPauseDialog] = useState(false);
  const [showReleaseDialog, setShowReleaseDialog] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<MmarsEventResponse | null>(
    null,
  );
  const [inputValue, setInputValue] = useState("");
  const [error, setError] = useState("");

  const handlePauseClick = (event: MmarsEventResponse) => {
    setSelectedEvent(event);
    setShowPauseDialog(true);
  };

  const handleReleaseClick = (event: MmarsEventResponse) => {
    setSelectedEvent(event);
    setShowReleaseDialog(true);
  };

  const handleHoldCancel = () => {
    setShowPauseDialog(false);
    setSelectedEvent(null);
    setInputValue("");
    setError("");
  };

  const handleReleaseCancel = () => {
    setShowReleaseDialog(false);
    setSelectedEvent(null);
    setInputValue("");
    setError("");
  };

  const handleHoldConfirm = async () => {
    if (!selectedEvent) return;

    if (!inputValue.trim()) {
      setError("Reason is required.");
      return;
    }

    const holdRequest: HoldOverpaymentTransactionRequest = {
      mmars_event_id: selectedEvent.mmars_event_id,
      reason: inputValue,
    };

    try {
      const response = await postAdminOverpaymentHoldTransaction(holdRequest);
      setShowPauseDialog(false);
      setInputValue("");
      fetchAndRenderOverpaymentData(fineosCustomerNumber || "");
    } catch (error) {
      if (error instanceof Error) {
        setErrorMessage(`Hold failed: ${error.message}`);
      } else {
        setErrorMessage("Hold failed: An unknown error occurred.");
      }
      setShowErrorAlert(true);
    }
  };

  const handleReleaseConfirm = async () => {
    if (!selectedEvent) return;

    const releaseRequest: HoldOverpaymentTransactionRequest = {
      mmars_event_id: selectedEvent.mmars_event_id,
      reason: inputValue || undefined,
    };

    try {
      const response =
        await postAdminOverpaymentReleaseTransaction(releaseRequest);
      setShowReleaseDialog(false);
      setInputValue("");
      fetchAndRenderOverpaymentData(fineosCustomerNumber || "");
    } catch (error) {
      if (error instanceof Error) {
        setErrorMessage(`Release failed: ${error.message}`);
      } else {
        setErrorMessage("Release failed: An unknown error occurred.");
      }
      setShowErrorAlert(true);
    }
  };

  return (
    <>
      {event.is_holdable && (
        <td>
          <button
            title="Hold this transaction"
            className="btn btn--primary"
            onClick={() => handlePauseClick(event)}
            style={{ display: "flex", alignItems: "center" }}
          >
            <PauseIcon
              className="icon icon--pause"
              style={{ width: "20px", height: "20px" }}
            />
          </button>
        </td>
      )}
      {event.is_releasable && (
        <td>
          <button
            title="Release this transaction"
            className="btn btn--primary"
            onClick={() => handleReleaseClick(event)}
            style={{ display: "flex", alignItems: "center" }}
          >
            <PlayIcon
              className="icon icon--play"
              style={{ width: "20px", height: "20px" }}
            />
          </button>
        </td>
      )}
      {showPauseDialog && selectedEvent && (
        <ConfirmationDialog
          title="Hold Event"
          body={
            <>
              <div>
                Holding an event will put a hold on the event and prevent it
                from continuing processing and creating a Receivable event in
                MMARS. You can release the hold when the condition no longer
                exists.
              </div>
              <div className="extended-confirmation-dialog__input-container">
                <label htmlFor="reason" className="pause-dialog__label">
                  Provide the reason for this hold:
                </label>
                <input
                  type="text"
                  id="reason"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  className="extended-confirmation-dialog__input"
                  placeholder="Enter reason"
                />
                {error && <p className="error-message">{error}</p>}
              </div>
            </>
          }
          handleCancelCallback={handleHoldCancel}
          handleContinueCallback={handleHoldConfirm}
        />
      )}
      {showReleaseDialog && selectedEvent && (
        <ConfirmationDialog
          title="Release Hold"
          body={
            <>
              <div>
                Releasing the hold will allow the event to continue processing
                and create a Receivable event in MMARS.
              </div>
              <div className="extended-confirmation-dialog__input-container">
                <label htmlFor="reason" className="pause-dialog__label">
                  Provide the reason for releasing the hold (optional):
                </label>
                <input
                  type="text"
                  id="reason"
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  className="extended-confirmation-dialog__input"
                  placeholder="Enter reason (Optional)"
                />
              </div>
            </>
          }
          handleCancelCallback={handleReleaseCancel}
          handleContinueCallback={handleReleaseConfirm}
        />
      )}
    </>
  );
};

export default EventHoldHandler;
