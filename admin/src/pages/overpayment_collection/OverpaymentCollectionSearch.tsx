import {
  ApiResponse,
  MmarsEventResponse,
  OverpaymentCaseResponse,
  OverpaymentSearchResponse,
  POSTAdminOverpaymentSearchResponse,
  RetryOverpaymentTransactionRequest,
  postAdminOverpaymentRefer,
  postAdminOverpaymentRetryTransaction,
  postAdminOverpaymentSearch,
} from "../../api";

import Alert from "../../components/Alert";
import ConfirmationDialog from "../../components/ConfirmationDialog";
import { Helmet } from "react-helmet-async";
import React from "react";
import classnames from "classnames";
import dayjs from "dayjs";
import { useState } from "react";
import EventHoldHandler from "./EventHoldHandler";

export interface InputTextProps {
  inputMode?: "decimal" | "numeric" | "text";
  inputClassName?: string;
  inputId?: string;
  name: string;
  placeholder?: string;
  onChange?: React.ChangeEventHandler<HTMLInputElement>;
  type?: "email" | "password" | "tel" | "text";
  width?: "small" | "medium";
  maxLength?: number;
  value?: string | number;
  valueType?: "integer" | "float" | "string";
  children?: React.ReactNode;
}

export interface OverpaymentSearchProps {
  search: string;
  setSearchString: any;
  overpaymentData: OverpaymentSearchResponse | null;
  setOverpaymentData: any;
}

// Define constants for each status code
const VCC_PENDING = 1;
const VCC_ERROR = 2;
const VCC_SUBMITTED = 3;
const VCC_FAILED = 4;
const RE_PENDING = 5;
const RE_ERROR = 6;
const RE_SUBMITTED = 7;
const RE_FAILED = 8;
const RE_SUCCESS = 9;
const REM_PENDING = 10;
const REM_ERROR = 11;
const REM_SUBMITTED = 12;
const REM_FAILED = 13;
const REM_SUCCESS = 14;
const VCM_REQUIRE = 15;
const RE_SUSPENDED = 16;
const ON_HOLD = 17;

const mmarsStatusTypeMap: { [key: number]: string } = {
  [VCC_PENDING]: "VCC Pending",
  [VCC_ERROR]: "VCC Error",
  [VCC_SUBMITTED]: "VCC Submitted",
  [VCC_FAILED]: "VCC Failed",
  [RE_PENDING]: "RE Pending",
  [RE_ERROR]: "RE Error",
  [RE_SUBMITTED]: "RE Submitted",
  [RE_FAILED]: "RE Failed",
  [RE_SUCCESS]: "RE Success",
  [REM_PENDING]: "REM Pending",
  [REM_ERROR]: "REM Error",
  [REM_SUBMITTED]: "REM Submitted",
  [REM_FAILED]: "REM Failed",
  [REM_SUCCESS]: "REM Success",
  [VCM_REQUIRE]: "VCM Require",
  [RE_SUSPENDED]: "RE Suspended",
  [ON_HOLD]: "On Hold",
};

// Define an array of error status codes, which will be used to determine if the Retry button should be displayed
// Also, the values are used to populate the alert message in the Confirmation Dialog
const ERROR_TO_PENDING_STATUS_MAP: { [key: number]: number } = {
  [VCC_FAILED]: VCC_PENDING,
  [VCC_ERROR]: VCC_PENDING,
  [RE_FAILED]: RE_PENDING,
  [RE_ERROR]: RE_PENDING,
  [REM_FAILED]: REM_PENDING,
  [REM_ERROR]: REM_PENDING,
};

const mmarsEventTypeMap: { [key: number]: string } = {
  1: "Receivable Event",
  2: "Receivable Modify Event",
};

const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) return "";
  return dayjs(dateString).format("MM/DD/YYYY");
};

const InputText = ({ type = "text", ...props }: InputTextProps) => {
  const fieldClasses = classnames("usa-input", props.inputClassName, {
    "maxw-mobile-lg": !props.width,
    [`usa-input--${props.width}`]: !!props.width,
  });
  let inputId = props.inputId;

  return (
    <input
      className={fieldClasses}
      data-value-type={props.valueType}
      id={inputId}
      inputMode={props.inputMode}
      maxLength={props.maxLength}
      name={props.name}
      onChange={props.onChange}
      type={type}
      placeholder={props.placeholder}
      value={props.value}
    />
  );
};

export default function OverpaymentCollectionSearch(
  props: OverpaymentSearchProps,
) {
  const { search, setSearchString: setSearchString } = props;
  const {
    overpaymentData: overpaymentData,
    setOverpaymentData: setOverpaymentData,
  } = props;
  const [showResult, setShowResult] = useState(false);
  const [selectedCase, setSelectedCase] =
    useState<OverpaymentCaseResponse | null>(null);
  const [
    showReferOverpaymentConfirmationDialog,
    setShowReferOverpaymentConfirmationDialog,
  ] = useState(false);
  const [showErrorAlert, setShowErrorAlert] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [fineosCustomerNumber, setFineosCustomerNumber] = useState<
    string | null
  >(null);

  const [
    showRetryTransactionConfirmationDialog,
    setShowRetryEventConfirmationDialog,
  ] = useState(false);

  const [selectedEvent, setSelectedEvent] = useState<MmarsEventResponse | null>(
    null,
  );

  const initState = () => {
    setOverpaymentData(null);
    setShowResult(false);
  };

  const handleRetryClick = (mmars_event: MmarsEventResponse) => {
    setSelectedEvent(mmars_event);
    setShowRetryEventConfirmationDialog(true);
  };

  const handleRetryCancel = () => {
    setShowRetryEventConfirmationDialog(false);
    setSelectedEvent(null);
  };

  const fetchAndRenderOverpaymentData = async (search: string) => {
    postAdminOverpaymentSearch({ fineos_customer_number: search })
      .then((response: ApiResponse<POSTAdminOverpaymentSearchResponse>) => {
        if (response.data) {
          setOverpaymentData(response.data);
          setShowResult(true);
          setFineosCustomerNumber(search);
        } else {
          setOverpaymentData(null);
          setShowResult(true);
        }
      })
      .catch((error) => {
        console.error("Error fetching data:", error);
        setOverpaymentData(null);
        setShowResult(true);
      });
  };

  const onSubmitHandler = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    fetchAndRenderOverpaymentData(search);
  };

  const onClickClear = async (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    initState();
  };

  const handleRetryConfirm = async () => {
    if (!selectedEvent) return;

    const retryRequest: RetryOverpaymentTransactionRequest = {
      mmars_event_id: selectedEvent.mmars_event_id,
    };

    try {
      const response = await postAdminOverpaymentRetryTransaction(retryRequest);
      setShowRetryEventConfirmationDialog(false);
      fetchAndRenderOverpaymentData(fineosCustomerNumber || "");
    } catch (error) {
      if (error instanceof Error) {
        setErrorMessage(`Retry failed: ${error.message}`);
      } else {
        setErrorMessage("Retry failed: An unknown error occurred.");
      }
      setShowErrorAlert(true);
    }
  };

  const handleReferCase = async (overpayment_id: string | undefined) => {
    if (!overpayment_id) return;

    try {
      const response = await postAdminOverpaymentRefer({ overpayment_id });
      setShowReferOverpaymentConfirmationDialog(false);
      fetchAndRenderOverpaymentData(fineosCustomerNumber || "");
    } catch (error) {
      console.error("Error referring overpayment:", error);
      if (error instanceof Error) {
        setErrorMessage(
          `An error occurred while referring the overpayment. ${
            error.message ? error.message + " " : ""
          }`,
        );
      } else {
        setErrorMessage(
          "An unknown error occurred while referring the overpayment.",
        );
      }
      setShowErrorAlert(true);
    }
  };

  const openPopup = (overpaymentCase: OverpaymentCaseResponse) => {
    setSelectedCase(overpaymentCase);
    setShowReferOverpaymentConfirmationDialog(true);
  };

  const closePopup = () => {
    setShowReferOverpaymentConfirmationDialog(false);
    setSelectedCase(null);
  };

  const closeErrorAlert = () => {
    setShowErrorAlert(false);
    setErrorMessage(null);
  };

  const renderOverpaymentCases = (data: OverpaymentSearchResponse) => {
    const cases = data.overpayment_cases || [];
    return cases.map((overpaymentCase, index) => (
      <tr
        key={overpaymentCase.overpayment_id}
        data-overpayment-id={overpaymentCase.overpayment_id}
      >
        <td className="table__col">{overpaymentCase.overpayment_casenumber}</td>
        <td className="table__col">{overpaymentCase.amount}</td>
        <td className="table__col">
          {overpaymentCase.recovered_to_date_amount}
        </td>
        <td className="table__col">
          {overpaymentCase.overpayment_adjustment_amount}
        </td>
        <td className="table__col">{overpaymentCase.agreed_recovery_amount}</td>
        <td className="table__col">{overpaymentCase.outstanding_amount}</td>
        <td className="table__col">
          {formatDate(overpaymentCase.period_start_date)}
        </td>
        <td className="table__col">
          {formatDate(overpaymentCase.period_end_date)}
        </td>
        <td className="table__col">
          {formatDate(overpaymentCase.overpayment_case_creation_date)}
        </td>
        <td className="table__col">
          {overpaymentCase.adjustment_description || ""}
        </td>
        <td className="table__col">
          {formatDate(overpaymentCase.referral_date)}
        </td>
        <td className="table__col">{overpaymentCase.referral_status}</td>
        <td className="table__col">
          {
            // If no mmars event already exist
            (!overpaymentCase.mmars_events ||
              overpaymentCase.mmars_events.length === 0) &&
              // If there is an outstanding_amount (not zero)
              overpaymentCase.outstanding_amount != 0 && (
                <button
                  className="btn btn--primary"
                  disabled={data.prevent_referral}
                  title={
                    data.prevent_referral ? data.referral_prevention_reason : ""
                  }
                  style={{
                    cursor: data.prevent_referral ? "default" : "pointer",
                  }}
                  onClick={() => openPopup(overpaymentCase)}
                >
                  Refer Case
                </button>
              )
          }
        </td>
      </tr>
    ));
  };

  const renderMmarsEvents = (
    overpayment_casenumber: string,
    events: MmarsEventResponse[],
  ) => {
    return events.map((event) => (
      <tr key={event.mmars_event_id}>
        {/* <th className="table__header"></th> */}
        <td className="table__col">
          <EventHoldHandler
            event={event}
            fetchAndRenderOverpaymentData={fetchAndRenderOverpaymentData}
            fineosCustomerNumber={fineosCustomerNumber}
            setErrorMessage={setErrorMessage} // Pass setErrorMessage as a prop
            setShowErrorAlert={setShowErrorAlert} // Pass setShowErrorAlert as a prop
          />
        </td>
        <td className="table__col">{overpayment_casenumber}</td>
        <td className="table__col">
          {mmarsEventTypeMap[event.mmars_event_type_id]}
        </td>
        <td className="table__col">
          {ERROR_TO_PENDING_STATUS_MAP.hasOwnProperty(
            event.mmars_status_type_id,
          ) ? (
            <span className="maintenance-configure__error">
              {mmarsStatusTypeMap[event.mmars_status_type_id]}
            </span>
          ) : (
            mmarsStatusTypeMap[event.mmars_status_type_id]
          )}
        </td>
        <td className="table__col">{formatDate(event.created_at)}</td>
        <td className="table__col">{formatDate(event.updated_at)}</td>
        {ERROR_TO_PENDING_STATUS_MAP.hasOwnProperty(
          event.mmars_status_type_id,
        ) && (
          <td>
            <button
              className="btn btn--primary"
              onClick={() => handleRetryClick(event)}
            >
              Retry
            </button>
          </td>
        )}
      </tr>
    ));
  };

  const renderOverpaymentData = (data: OverpaymentSearchResponse) => {
    return (
      <div style={{ marginLeft: "20px" }}>
        <h3>Claimant Details</h3>
        <table className="table" cellPadding="0" cellSpacing="0">
          <thead>
            <tr className="table__head">
              <th className="table__header">Customer Number</th>
              <th className="table__header">Name</th>
              <th className="table__header">Address Line 1</th>
              <th className="table__header">Address Line 2</th>
              <th className="table__header">City</th>
              <th className="table__header">State</th>
              <th className="table__header">Zip</th>
            </tr>
          </thead>
          <tbody className="table__body">
            <tr>
              <td className="table__col">{data.fineos_customer_number}</td>
              <td className="table__col">
                {data.employee_firstname} {data.employee_lastname}
              </td>
              <td className="table__col">{data.employee_address?.line_1}</td>
              <td className="table__col">{data.employee_address?.line_2}</td>
              <td className="table__col">{data.employee_address?.city}</td>
              <td className="table__col">{data.employee_address?.state}</td>
              <td className="table__col">{data.employee_address?.zip}</td>
            </tr>
          </tbody>
        </table>
        <div style={{ marginTop: "20px" }}></div> <h3>Overpayment Case(s)</h3>
        <table className="table" cellPadding="0" cellSpacing="0">
          <thead>
            <tr className="table__head">
              <th className="table__header">Case Number</th>
              <th className="table__header">Amount</th>
              <th className="table__header">Recovered to Date Amount</th>
              <th className="table__header">Adjustment Amount</th>
              <th className="table__header">Agreed Recovery Amount</th>
              <th className="table__header">Outstanding Amount</th>
              <th className="table__header">Period Start Date</th>
              <th className="table__header">Period End Date</th>
              <th className="table__header">OP Case creation date</th>
              <th className="table__header">Adjustment Description</th>
              <th className="table__header">Referral Date</th>
              <th className="table__header">Referral Status</th>
              <th className="table__header"></th>
            </tr>
          </thead>
          <tbody className="table__body">{renderOverpaymentCases(data)}</tbody>
        </table>
        <div style={{ marginTop: "20px", width: "40%" }}>
          {showResult && (
            <div className="overpayment-results">
              {overpaymentData && overpaymentData.overpayment_cases ? (
                <div>
                  <h3>Summary of transactions:</h3>
                  <table className="table" cellPadding="0" cellSpacing="0">
                    <thead>
                      <tr className="table__head">
                        <th className="table__header"></th>
                        <th className="table__header">OP Case</th>
                        <th className="table__header">Transaction Type</th>
                        <th className="table__header">Status</th>
                        <th className="table__header">Creation Date</th>
                        <th className="table__header">Updated Date</th>
                      </tr>
                    </thead>
                    <tbody className="table__body">
                      {overpaymentData.overpayment_cases.flatMap(
                        (overpaymentCase) =>
                          renderMmarsEvents(
                            overpaymentCase.overpayment_casenumber,
                            overpaymentCase.mmars_events || [],
                          ),
                      )}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div></div>
              )}
            </div>
          )}
        </div>
        {showRetryTransactionConfirmationDialog && selectedEvent && (
          <ConfirmationDialog
            title="Are you sure you want to retry"
            body={`Retrying will change this transaction's code from ${
              mmarsStatusTypeMap[selectedEvent.mmars_status_type_id]
            } to ${
              mmarsStatusTypeMap[
                ERROR_TO_PENDING_STATUS_MAP[selectedEvent.mmars_status_type_id]
              ]
            }.`}
            handleCancelCallback={handleRetryCancel}
            handleContinueCallback={handleRetryConfirm}
          />
        )}
        {errorMessage && (
          <Alert type="error" closeable onClose={closeErrorAlert}>
            {errorMessage}
          </Alert>
        )}
        {showReferOverpaymentConfirmationDialog &&
          selectedCase &&
          selectedCase.outstanding_amount && (
            <ConfirmationDialog
              title="Are you sure you want to refer this case"
              body={<OverpaymentCaseDetails selectedCase={selectedCase} />}
              handleCancelCallback={closePopup}
              handleContinueCallback={() =>
                handleReferCase(selectedCase.overpayment_id)
              }
            />
          )}
        {showErrorAlert && errorMessage && (
          <Alert type="error" closeable onClose={closeErrorAlert}>
            {errorMessage}
          </Alert>
        )}
      </div>
    );
  };

  return (
    <>
      <Helmet>
        <title>Overpayment Collection</title>
      </Helmet>
      <h1>Overpayment Collection</h1>
      <div className="maintenance-configure">
        <div className="maintenance-configure__description">
          Search for Overpayment Collection Data
        </div>
        <div className="maintenance-configure__form">
          <form onSubmit={onSubmitHandler}>
            <InputText
              inputClassName="search"
              inputId="search"
              name="search"
              width="small"
              placeholder="search by customer number"
              onChange={(event) => setSearchString(event.target.value)}
              value={search}
            ></InputText>
            <fieldset className="maintenance-configure__fieldset maintenance-configure__buttons">
              <button
                className="maintenance-configure__btn maintenance-configure__btn--submit btn"
                type="submit"
              >
                Submit
              </button>
              <button
                className="maintenance-configure__btn maintenance-configure__btn--cancel btn btn--cancel"
                type="button"
                onClick={onClickClear}
              >
                Clear Results
              </button>
            </fieldset>
          </form>
        </div>
        <div className="maintenance">
          <div>
            <p></p>
          </div>
        </div>
        <div>
          {showResult && (
            <div className="overpayment-results">
              {overpaymentData ? (
                renderOverpaymentData(overpaymentData)
              ) : (
                <p>No results found</p>
              )}
            </div>
          )}
        </div>
      </div>
    </>
  );
}

interface OverpaymentCaseDetailsProps {
  selectedCase: OverpaymentCaseResponse;
}

export function OverpaymentCaseDetails({
  selectedCase,
}: OverpaymentCaseDetailsProps) {
  return (
    <div style={{ textAlign: "left" }}>
      <p>
        <strong>Case Number:</strong> {selectedCase.overpayment_casenumber}
      </p>
      <p>
        <strong>Amount:</strong> {selectedCase.amount}
      </p>
      <p>
        <strong>Recovered to Date Amount:</strong>
        {selectedCase.recovered_to_date_amount}
      </p>
      <p>
        <strong>Adjustment Amount:</strong>
        {selectedCase.overpayment_adjustment_amount}
      </p>
      <p>
        <strong>Outstanding Amount:</strong> {selectedCase.outstanding_amount}
      </p>
      <p>
        <strong>Period Start Date:</strong> {selectedCase.period_start_date}
      </p>
      <p>
        <strong>Period End Date:</strong> {selectedCase.period_end_date}
      </p>
      <p>
        <strong>OP Case Creation Date:</strong>
        {selectedCase.overpayment_case_creation_date}
      </p>
    </div>
  );
}
