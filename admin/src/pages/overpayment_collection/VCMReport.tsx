import React, { useEffect, useState } from "react";
import {
  getAdminOverpaymentVcmReport,
  postAdminOverpaymentMarkVcmReviewed,
} from "../../_api";
import { GETAdminOverpaymentVcmReportResponse } from "../../_api";
import { OverpaymentMarkVcmReviewedRequest } from "../../_api";
import ConfirmationDialog from "../../components/ConfirmationDialog";
import Alert from "../../components/Alert";

export default function VCMReport() {
  const [data, setData] = useState<GETAdminOverpaymentVcmReportResponse | null>(
    null,
  );
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [showErrorAlert, setShowErrorAlert] = useState<boolean>(false);
  const [shrinkedRows, setShrinkedRows] = useState<number[]>([]);
  const [markedEmployeeId, setMarkedEmployeeId] = useState<string | null>(null);
  const [showMarkAsReviewedConfirmation, setShowMarkAsReviewedConfirmation] =
    useState<boolean>(false);

  // Fetch data from the backend
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await getAdminOverpaymentVcmReport();
        setData(response.data || null);
      } catch (err) {
        setError("Failed to fetch data from the backend.");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const openMarkAsReviewedConfirmation = (employee_id: string) => {
    setMarkedEmployeeId(employee_id);
    setShowMarkAsReviewedConfirmation(true);
  };

  const closePopup = () => {
    setShowMarkAsReviewedConfirmation(false);
    setMarkedEmployeeId(null);
  };

  const closeErrorAlert = () => {
    setShowErrorAlert(false);
  };

  const handleMarkAsReviewed = async () => {
    if (!markedEmployeeId) return;

    const markAsReviewedRequest: OverpaymentMarkVcmReviewedRequest = {
      employee_id: markedEmployeeId,
    };

    try {
      await postAdminOverpaymentMarkVcmReviewed(markAsReviewedRequest);
      setMarkedEmployeeId(null);
      setShowMarkAsReviewedConfirmation(false);
      const response = await getAdminOverpaymentVcmReport();
      setData(response.data || null);
    } catch (error) {
      if (error instanceof Error) {
        setErrorMessage(`Retry failed: ${error.message}`);
      } else {
        setErrorMessage("Retry failed: An unknown error occurred.");
      }
      setShowErrorAlert(true);
    }
  };

  const toggleRow = (index: number) => {
    setShrinkedRows((prev) =>
      prev.includes(index) ? prev.filter((i) => i !== index) : [...prev, index],
    );
  };

  // clicking on a cell selects the text
  const handleCellClick = (event: React.MouseEvent<HTMLTableCellElement>) => {
    const range = document.createRange();
    range.selectNodeContents(event.currentTarget);
    const selection = window.getSelection();
    if (selection) {
      selection.removeAllRanges();
      selection.addRange(range);
    }
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  if (!data || !Array.isArray(data) || data.length === 0) {
    return <div>No VCM available.</div>;
  }

  return (
    <div>
      <h1>VCM Report</h1>
      {data.map((data, index) => (
        <div key={index} className="overpayment__row">
          {/* Header with Fineos Customer No and Toggle Button */}
          <div className="overpayment__header">
            <span>
              <strong>Fineos Customer No:</strong> {data.fineos_customer_number}
            </span>
            <button
              className="overpayment__toggle-button"
              onClick={() => toggleRow(index)}
            >
              {shrinkedRows.includes(index) ? "▼" : "▲"}
            </button>
          </div>
          {/* Expanded Content */}
          {!shrinkedRows.includes(index) && (
            <div className="overpayment__content">
              <div className="overpayment__status">
                {data.edm && (
                  <div>
                    <strong>EDM Customer active status: </strong>
                    {data.customer_active_status_name}
                  </div>
                )}
                {data.edm && (
                  <div>
                    <strong>EDM Customer approval status: </strong>
                    {data.customer_approval_status_name}
                  </div>
                )}
                {
                  <div>
                    <button
                      className="btn btn--primary"
                      onClick={() =>
                        openMarkAsReviewedConfirmation(data.employee_id)
                      }
                    >
                      Mark As Reviewed
                    </button>
                  </div>
                }
              </div>
              {/* Combined Address Table */}
              <div className="addresses">
                <strong>Addresses:</strong>
                <table className="overpayment__table">
                  <thead>
                    <tr>
                      <th className="overpayment__table-header">SOURCE</th>
                      <th className="overpayment__table-header">
                        VENDOR_CUSTOMER_CODE
                      </th>
                      <th className="overpayment__table-header">FIRST_NAME</th>
                      <th className="overpayment__table-header">LAST_NAME</th>
                      <th className="overpayment__table-header">LEGAL_NAME</th>
                      <th className="overpayment__table-header">ADDRESS_ID</th>
                      <th className="overpayment__table-header">STREET_1</th>
                      <th className="overpayment__table-header">STREET_2</th>
                      <th className="overpayment__table-header">CITY</th>
                      <th className="overpayment__table-header">STATE</th>
                      <th className="overpayment__table-header">ZIPCODE</th>
                    </tr>
                  </thead>
                  <tbody>
                    {data.pfml && (
                      <tr key="pfml">
                        <td className="overpayment__table-cell">PFML</td>
                        <td
                          className="overpayment__table-cell"
                          onClick={handleCellClick}
                        >
                          {data.pfml?.vendor_customer_code}
                        </td>
                        <td
                          className="overpayment__table-cell"
                          onClick={handleCellClick}
                        >
                          {data.pfml?.first_name}
                        </td>
                        <td
                          className="overpayment__table-cell"
                          onClick={handleCellClick}
                        >
                          {data.pfml?.last_name}
                        </td>
                        <td
                          className="overpayment__table-cell"
                          onClick={handleCellClick}
                        >
                          {data.pfml?.legal_name}
                        </td>
                        <td
                          className="overpayment__table-cell"
                          onClick={handleCellClick}
                        >
                          {data.pfml?.address?.ADDRESS_ID}
                        </td>
                        <td
                          className="overpayment__table-cell"
                          onClick={handleCellClick}
                        >
                          {data.pfml?.address?.line_1}
                        </td>
                        <td
                          className="overpayment__table-cell"
                          onClick={handleCellClick}
                        >
                          {data.pfml?.address?.line_2}
                        </td>
                        <td
                          className="overpayment__table-cell"
                          onClick={handleCellClick}
                        >
                          {data.pfml?.address?.city}
                        </td>
                        <td
                          className="overpayment__table-cell"
                          onClick={handleCellClick}
                        >
                          {data.pfml?.address?.state}
                        </td>
                        <td
                          className="overpayment__table-cell"
                          onClick={handleCellClick}
                        >
                          {data.pfml?.address?.zip}
                        </td>
                      </tr>
                    )}
                    {data.edm && (
                      <tr key="edm">
                        <td className="overpayment__table-cell">EDM</td>
                        <td
                          className="overpayment__table-cell"
                          onClick={handleCellClick}
                        >
                          {data.edm?.vendor_customer_code}
                        </td>
                        <td
                          className="overpayment__table-cell"
                          onClick={handleCellClick}
                        >
                          {data.edm?.first_name}
                        </td>
                        <td
                          className="overpayment__table-cell"
                          onClick={handleCellClick}
                        >
                          {data.edm?.last_name}
                        </td>
                        <td
                          className="overpayment__table-cell"
                          onClick={handleCellClick}
                        >
                          {data.edm?.legal_name}
                        </td>
                        <td
                          className="overpayment__table-cell"
                          onClick={handleCellClick}
                        >
                          {data.edm?.address?.ADDRESS_ID}
                        </td>
                        <td
                          className="overpayment__table-cell"
                          onClick={handleCellClick}
                        >
                          {data.edm?.address?.line_1}
                        </td>
                        <td
                          className="overpayment__table-cell"
                          onClick={handleCellClick}
                        >
                          {data.edm?.address?.line_2}
                        </td>
                        <td
                          className="overpayment__table-cell"
                          onClick={handleCellClick}
                        >
                          {data.edm?.address?.city}
                        </td>
                        <td
                          className="overpayment__table-cell"
                          onClick={handleCellClick}
                        >
                          {data.edm?.address?.state}
                        </td>
                        <td
                          className="overpayment__table-cell"
                          onClick={handleCellClick}
                        >
                          {data.edm?.address?.zip}
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </div>
          )}

          {showMarkAsReviewedConfirmation && (
            <ConfirmationDialog
              title="Confirm Mark as Reviewed"
              body="Marking this VCM as reviewed will change its status to VCC Pending and remove it from the report. Do you want to continue?"
              handleCancelCallback={closePopup}
              handleContinueCallback={handleMarkAsReviewed}
            ></ConfirmationDialog>
          )}

          {showErrorAlert && errorMessage && (
            <Alert type="error" closeable onClose={closeErrorAlert}>
              {errorMessage}
            </Alert>
          )}
        </div>
      ))}
    </div>
  );
}
