import {
  AdminUserUpdateRequest,
  ApiResponse,
  AuditLogResponse,
  GETAdminAuditLogsResponse,
  GETAdminOmnisearchUsersByUserIdAuthLogResponse,
  POSTAdminOmnisearchUsersResponse,
  SuccessfulResponse,
  UserAuthLogResponse,
  UserResponse,
  deleteAdminUsersByUser_idRolesAndRole_id,
  getAdminAuditLogs,
  getAdminOmnisearchUsersByUser_id,
  getAdminOmnisearchUsersByUser_idAuthLog,
  patchAdminUsersByUser_id,
  postAdminOmnisearchUsers,
  postAdminUsersByUser_idRolesAndRole_id,
} from "../../api";
import React, { ReactElement, useEffect } from "react";
import {
  formatDateTime,
  formatElapsedTime,
  listAuthOutcomes,
  listRoleDescriptions,
} from "../../utils/content_helpers";

import ActionMenu from "../../components/ActionMenu";
import Alert from "../../components/Alert";
import ConfirmationDialog from "../../components/ConfirmationDialog";
import { HelmetProvider } from "react-helmet-async";
import Link from "next/link";
import Table from "../../components/Table";
import isValidUuid from "../../utils/isValidUuid";
import { useRouter } from "next/router";
import { useState } from "react";

export default function UserAuthDetail(props: any) {
  const router = useRouter();
  const [user_data, set_user_data] = useState<UserResponse>();
  const [userEmail, setUserEmail] = useState<string>("");
  const [userAuthId, setUserAuthId] = useState<string>("");

  const [user_auth_log_data, set_user_auth_log_data] = useState<
    UserAuthLogResponse[] | undefined
  >([]);
  const [auditLogData, setAuditLogData] = useState<
    AuditLogResponse[] | undefined
  >([]);

  const [ticketNumber, setTicketNumber] = useState("");

  const [user_id, set_user_id] = useState<string>("");
  const [successMessage, set_successMessage] = useState<string>("");
  const [errorMessage, set_errorMessage] = useState<string>("");

  const ConfirmDialog = {
    None: 0,
    UserUpdate: 1,
    SetAsClaimant: 2,
    SetAsEmployer: 3,
  } as const;

  const [showConfirm, setShowConfirm] = useState(0);

  const handleConfirmCancel = async () => {
    setShowConfirm(ConfirmDialog.None);
  };

  useEffect(() => {
    if (!router.isReady) {
      return;
    }
    if (router.query.user_id) {
      const userId = router.query.user_id.toString();
      if (!isValidUuid(userId)) {
        throw new Error("Invalid user_id");
      }
      set_user_id(userId);
    }
  }, [router.isReady, router.query.user_id]);

  useEffect(() => {
    if (user_id) {
      set_user_id(user_id);
      loadUserData(user_id);
    }
  }, [user_id]);

  function loadUserData(user_id: string) {
    getAdminOmnisearchUsersByUser_id({ user_id: user_id }).then(
      (response: ApiResponse<UserResponse>) => {
        const data = response.data;
        if (response.data) {
          set_user_data(response.data);
          setUserEmail(data.email_address!);
          setUserAuthId(data.auth_id ?? "");
        }
      },
    );
    getAdminOmnisearchUsersByUser_idAuthLog({ user_id: user_id }).then(
      (
        response: ApiResponse<GETAdminOmnisearchUsersByUserIdAuthLogResponse>,
      ) => {
        const data = Array.isArray(response.data) ? response.data : [];
        if (response.data) {
          set_user_auth_log_data(data);
        }
      },
    );
    getAdminAuditLogs({ filter_id: user_id }).then(
      (response: ApiResponse<GETAdminAuditLogsResponse>) => {
        const data = Array.isArray(response.data) ? response.data : [];
        if (response.data) {
          setAuditLogData(data.reverse());
        }
      },
    );
  }

  function clearAlerts() {
    set_successMessage("");
    set_errorMessage("");
  }

  const leaveAdminRoleId = "3";

  function checkTicketNumber() {
    clearAlerts();
    if (ticketNumber.trim() === "") {
      set_errorMessage("Jira ticket required for changes.");
      return false;
    }
    return true;
  }

  const confirmSetUserAsLeaveAdmin = async () => {
    if (!checkTicketNumber()) {
      return;
    }
    setShowConfirm(ConfirmDialog.SetAsEmployer);
  };

  const doSetUserAsLeaveAdmin = async () => {
    await postAdminUsersByUser_idRolesAndRole_id(
      {
        user_id: user_id,
        role_id: leaveAdminRoleId,
      },
      { ticket_num: ticketNumber },
      {},
    ).then((response: ApiResponse<SuccessfulResponse>) => {
      const data = response.data;
      set_successMessage(
        "User converted to Leave Administrator. (Do you need to clear the OAuth ID also?)",
      );
      loadUserData(user_id);
    });
    setShowConfirm(ConfirmDialog.None);
    return Promise.resolve();
  };

  const confirmSetUserAsClaimant = async () => {
    if (!checkTicketNumber()) {
      return;
    }
    setShowConfirm(ConfirmDialog.SetAsClaimant);
  };

  const doSetUserAsClaimant = async () => {
    await deleteAdminUsersByUser_idRolesAndRole_id(
      {
        user_id: user_id,
        role_id: leaveAdminRoleId,
      },
      { ticket_num: ticketNumber },
      {},
    )
      .then((response: ApiResponse<SuccessfulResponse>) => {
        const data = response.data;
        set_successMessage(
          "User converted to Claimant. (Do you need to clear the OAuth ID also?)",
        );
        loadUserData(user_id);
      })
      .catch((error) => {
        const errors =
          (error.data?.errors as { field: string; message: string }[]) ?? [];
        const errorMessages = errors.map((error) => error.message).join(",");
        set_errorMessage(errorMessages);
      });
    setShowConfirm(ConfirmDialog.None);
    return Promise.resolve();
  };

  const confirmUpdateUser = async () => {
    clearAlerts();
    if (
      userEmail.trim().toLowerCase() ===
        user_data?.email_address?.toLowerCase() &&
      userAuthId.trim().toLowerCase() ===
        (user_data?.auth_id ?? "").toLowerCase()
    ) {
      set_successMessage("Nothing to update.");
      return;
    }
    if (userEmail.trim() === "") {
      set_errorMessage("Email address required.");
      return;
    }
    if (!checkTicketNumber()) {
      return;
    }

    let errors: string[] = [];

    await postAdminOmnisearchUsers({ search: userEmail }).then(
      (response: ApiResponse<POSTAdminOmnisearchUsersResponse>) => {
        let data = Array.isArray(response.data) ? response.data : [];
        // look for exact email matches that are not the current user
        const matchesExisting = data.some(
          (user) =>
            user.email_address.toLowerCase() === userEmail.toLowerCase() &&
            user.user_id !== user_id,
        );
        if (matchesExisting) {
          errors.push("Email address already in use.");
        }
      },
    );
    if (userAuthId.trim() !== "") {
      //  && userAuthId.trim().length !== 36
      await postAdminOmnisearchUsers({ search: userAuthId }).then(
        (response: ApiResponse<POSTAdminOmnisearchUsersResponse>) => {
          const data = Array.isArray(response.data) ? response.data : [];
          // look for exact OAuth ID matches that are not the current user
          const matchesExisting = data.some(
            (user) =>
              user.auth_id?.toLowerCase() === userAuthId.toLowerCase() &&
              user.user_id !== user_id,
          );
          if (matchesExisting) {
            // since this is an internal tool, it could be useful to see this data when this occurs
            errors.push("OAuth ID already in use.");
          }
        },
      );
    }
    if (errors.length > 0) {
      set_errorMessage(errors.join(" "));
      return;
    }

    setShowConfirm(ConfirmDialog.UserUpdate);
  };

  const doUpdateUser = async () => {
    const request: AdminUserUpdateRequest = {
      email_address: userEmail,
      auth_id: userAuthId,
      ticket_num: ticketNumber,
    };
    await patchAdminUsersByUser_id(
      {
        user_id: user_id,
      },
      request,
      {},
    ).then((response: ApiResponse<UserResponse>) => {
      const data = response.data;
      set_successMessage("User updated.");
      loadUserData(user_id);
    });
    setShowConfirm(ConfirmDialog.None);
    return Promise.resolve();
  };

  const RowJson = (props: { readonly state: string | undefined }) => {
    return <pre className="row-json__container">{props.state}</pre>;
  };

  const ToggleUserTypeBlock = () => {
    if (user_data) {
      if (user_data.roles!.length == 0) {
        if (user_data.application_names!.length > 0) {
          // can't convert with applications
          return (
            <Alert type="warn">
              Unable to convert claimant with applications
            </Alert>
          );
        } else {
          return (
            <button
              className="maintenance-configure__btn maintenance-configure__btn--submit btn"
              type="button"
              onClick={confirmSetUserAsLeaveAdmin}
            >
              Convert user to Leave Admin
            </button>
          );
        }
      } else {
        // user is an employer
        if (
          user_data.roles &&
          user_data.roles.length > 0 &&
          user_data.user_leave_administrators &&
          user_data.user_leave_administrators.length > 0
        ) {
          // can't convert verified LAs
          return (
            <Alert type="warn">
              Unable to convert leave administrator with verified employers
            </Alert>
          );
        } else {
          return (
            <button
              className="maintenance-configure__btn maintenance-configure__btn--submit btn"
              type="button"
              onClick={confirmSetUserAsClaimant}
            >
              Convert user to Claimant
            </button>
          );
        }
      }
    } else {
      return <></>;
    }
  };

  const ticketBaseUrl = "https://lwd.atlassian.net/browse/";

  function renderTicket(ticket_num: string | undefined) {
    if (!ticket_num) {
      return "";
    }
    const ticketUrl = ticket_num.startsWith("http")
      ? ticket_num
      : ticketBaseUrl + ticket_num;
    return (
      <Link href={ticketUrl} target="_blank">
        {ticket_num.replace(ticketBaseUrl, "")}
      </Link>
    );
  }

  function renderMetaData(meta_data: any) {
    if (!meta_data) {
      return "";
    }
    return <RowJson state={JSON.stringify(meta_data, null, 2)} />;
  }

  const JiraTicketBlock = () => {
    return (
      <div>
        Jira ticket:
        <br />
        <strong>{ticketNumber}</strong>
      </div>
    );
  };

  const UserDiffBlock = () => {
    let emailDiff = <></>;
    if (userEmail !== user_data?.email_address) {
      emailDiff = (
        <>
          <p>
            New email:
            <br />
            &quot;<strong>{userEmail}</strong>&quot;
          </p>
        </>
      );
    }
    let authIdDiff = <></>;
    if (userAuthId !== user_data?.auth_id) {
      authIdDiff = (
        <p>
          New OAuth ID:
          <br />
          &quot;<strong>{userAuthId}&quot;</strong>
        </p>
      );
    }
    return (
      <>
        {emailDiff}
        {authIdDiff}
      </>
    );
  };

  return (
    <>
      <div>
        <HelmetProvider>
          <title>Portal User Details</title>
        </HelmetProvider>
        <h1>Portal User Details</h1>
        <ActionMenu
          options={[
            {
              enabled: true,
              href: {
                pathname: "",
              },
              text: "Go Back to Search",
              type: "link",
            },
          ]}
        />
        {showConfirm == ConfirmDialog.SetAsClaimant && (
          <ConfirmationDialog
            title="Confirm: convert user to Claimant"
            body={
              <div>
                <JiraTicketBlock />
              </div>
            }
            handleCancelCallback={handleConfirmCancel}
            handleContinueCallback={doSetUserAsClaimant}
          />
        )}
        {showConfirm == ConfirmDialog.SetAsEmployer && (
          <ConfirmationDialog
            title="Confirm: convert user to Leave Admin"
            body={
              <div>
                <JiraTicketBlock />
              </div>
            }
            handleCancelCallback={handleConfirmCancel}
            handleContinueCallback={doSetUserAsLeaveAdmin}
          />
        )}
        {showConfirm == ConfirmDialog.UserUpdate && (
          <ConfirmationDialog
            title="Confirm: update user"
            body={
              <>
                <UserDiffBlock />
                <JiraTicketBlock />
              </>
            }
            handleCancelCallback={handleConfirmCancel}
            handleContinueCallback={doUpdateUser}
          />
        )}
        <div className="user-detail__container">
          <div className="user-detail__panel">
            {user_data && (
              <>
                <h3>User Data</h3>
                <div className="user-detail__container">
                  <div className="user-detail__header">User ID:</div>
                  <div>{user_data.user_id}</div>
                  <div className="user-detail__header">Auth Id:</div>
                  <div>{user_data.auth_id}</div>
                  <div className="user-detail__header">Sub Id:</div>
                  <div>{user_data.auth_id}</div>
                  <div className="user-detail__header">Email:</div>
                  <div>{user_data.email_address}</div>
                  <div className="user-detail__header">First Name:</div>
                  <div>{user_data.first_name}</div>
                  <div className="user-detail__header">Last Name:</div>
                  <div>{user_data.last_name}</div>
                  <div className="user-detail__header">Roles:</div>
                  <div>{listRoleDescriptions(user_data.roles)}</div>
                  <div className="user-detail__header">Applications:</div>
                  <div>{user_data.application_names?.length}</div>
                  <div className="user-detail__header">Employers:</div>
                  <div>{user_data.user_leave_administrators?.length}</div>
                  <div className="user-detail__header">Multiple Tax IDs:</div>
                  <div>{user_data.has_multiple_tax_identifiers}</div>
                  <div className="user-detail__header">
                    Consented data sharing:
                  </div>
                  <div>{user_data.consented_to_data_sharing && "Yes"}</div>
                  <div className="user-detail__header">Consented tax docs:</div>
                  <div>
                    {user_data.consented_to_view_tax_documents && "Yes"}
                  </div>
                </div>
              </>
            )}
            {(errorMessage || successMessage) && (
              <div className="user-detail__content">
                {errorMessage && <Alert type="error">{errorMessage}</Alert>}
                {successMessage && (
                  <Alert type="success">{successMessage}</Alert>
                )}
              </div>
            )}
          </div>
          {user_data && (
            <div className="user-detail__panel">
              <h3>Actions</h3>
              <div>
                Any update action requires a Jira Ticket number and will be
                audited (visible in audit log below).
              </div>
              <div className="user-detail__content">
                Jira Ticket (ID or link):
                <input
                  type="text"
                  value={ticketNumber}
                  placeholder="[https://lwd.atlassian.net/browse/]PSD-12690"
                  onChange={(event) => {
                    setTicketNumber(event.target.value);
                  }}
                />
              </div>
              <h4>User Update</h4>
              <div>
                <div className="user-detail__content">
                  Email Address:
                  <input
                    type="text"
                    value={userEmail}
                    onChange={(event) => {
                      setUserEmail(event.target.value);
                    }}
                  />
                </div>
                <div className="user-detail__content">
                  OAuth ID (MMG) <em>Clear this to un-link from MMG</em>:
                  <input
                    type="text"
                    value={userAuthId}
                    placeholder="00000000-0000-0000-0000-000000000000"
                    onChange={(event) => {
                      setUserAuthId(event.target.value);
                    }}
                  />
                </div>
                <button
                  className="maintenance-configure__btn maintenance-configure__btn--submit btn"
                  type="button"
                  onClick={confirmUpdateUser}
                >
                  Update User
                </button>
              </div>
              <br />
              <h4>User Type Conversion:</h4>
              <div className="user-detail__content">
                <ToggleUserTypeBlock />
              </div>
            </div>
          )}
        </div>
        <br></br>
        <div className="user-detail__panel audit-state__container">
          {auditLogData && (
            <div>
              <h3>Change Audit Log</h3>
              <Table
                noResults={<p></p>}
                rows={auditLogData}
                cols={[
                  {
                    title: "Date",
                    align: "left",
                    content: (row) => formatDateTime(row?.created_at),
                  },
                  {
                    title: "Ticket",
                    align: "left",
                    content: (row) => renderTicket(row?.ticket_num),
                  },
                  {
                    title: "Data",
                    align: "left",
                    content: (row) => row?.record_type,
                  },
                  {
                    title: "Before",
                    align: "left",
                    content: (row) => <RowJson state={row?.state_before} />,
                  },
                  {
                    title: "After",
                    align: "left",
                    content: (row) => <RowJson state={row?.state_after} />,
                  },
                  {
                    title: "Changed By",
                    align: "left",
                    content: (row) => row?.admin_user_id,
                  },
                  {
                    title: "",
                    align: "left",
                    content: (row) => "",
                  },
                ]}
              />
            </div>
          )}
        </div>
        <br></br>
        <div className="user-detail__panel">
          {user_auth_log_data && (
            <div>
              <h3 id="authLog">Authentication Log</h3>
              <Table
                noResults={<p></p>}
                rows={user_auth_log_data}
                cols={[
                  {
                    title: "oauth operation",
                    align: "left",
                    content: (row) => row?.oauth_operation,
                  },
                  {
                    title: "started_at",
                    align: "left",
                    content: (row) => formatDateTime(row?.started_at),
                  },
                  {
                    title: "completed_at",
                    align: "left",
                    content: (row) => formatDateTime(row?.completed_at),
                  },
                  {
                    title: "completed in",
                    align: "left",
                    content: (row) =>
                      formatElapsedTime(row?.started_at, row?.completed_at),
                  },
                  {
                    title: "Meta Data",
                    align: "left",
                    content: (row) => renderMetaData(row?.meta_data),
                  },
                  {
                    title: "",
                    align: "left",
                    content: (row) => "",
                  },
                ]}
              />
            </div>
          )}
        </div>
      </div>
    </>
  );
}
