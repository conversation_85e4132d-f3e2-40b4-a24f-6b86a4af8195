import { UserAuthLogResponse, UserResponse } from "../../api";
import {
  formatDateTime,
  formatElapsedTime,
  listRoleDescriptions,
} from "../../utils/content_helpers";

import Link from "next/link";
import React from "react";
import Table from "../../components/Table";

export interface OmniSearchResultsProps {
  userData: UserResponse[] | null;
  authLogs: UserAuthLogResponse[] | null;
}

const RowJson = (props: { readonly state: string | undefined }) => {
  return <pre className="row-json__container">{props.state}</pre>;
};

export default function OmniSearchResults(props: OmniSearchResultsProps) {
  const { userData, authLogs } = props;

  const renderName = (row: UserResponse) => {
    return `${row.first_name ?? ""} ${row.last_name ?? ""}`;
  };

  const renderMetaData = (meta_data: any) => {
    if (!meta_data) {
      return "";
    }
    return <RowJson state={JSON.stringify(meta_data, null, 2)} />;
  };

  const showUserDetail = (userData: UserResponse) => (
    <Link href={{ pathname: "", query: { user_id: userData.user_id } }}>
      {userData.email_address ?? userData.user_id}
    </Link>
  );

  return (
    <div>
      {userData && (
        <div className="user-detail__panel audit-state__container">
          <h3 style={{ paddingLeft: "2em", paddingTop: "1em" }}>User Data</h3>
          <Table
            noResults={
              <p style={{ paddingLeft: "4em", paddingTop: "1em" }}>
                No user data found
              </p>
            }
            rows={userData}
            cols={[
              {
                title: "Email",
                align: "left",
                content: showUserDetail,
              },
              {
                title: "Name",
                align: "left",
                content: (row) => renderName(row),
              },
              {
                title: "Roles",
                align: "left",
                content: (row) => listRoleDescriptions(row.roles),
              },
              {
                title: "App Count",
                align: "left",
                content: (row) => row.application_names?.length,
              },
              {
                title: "Emp Count",
                align: "left",
                content: (row) => row.user_leave_administrators?.length,
              },
              {
                title: "Auth Id",
                align: "left",
                content: (row) => row.auth_id,
              },
              {
                title: "Sub Id",
                align: "left",
                content: (row) => row.auth_id,
              },
              {
                // This is a dummy column to make the table align better
                title: "",
                content: () => null,
              },
            ]}
          />
        </div>
      )}
      {authLogs && (
        <div className="user-detail__panel audit-state__container">
          <h3 style={{ paddingLeft: "2em", paddingTop: "2em" }}>
            Authentication Logs
          </h3>
          <Table
            noResults={
              <p style={{ paddingLeft: "4em", paddingTop: "1em" }}>
                No auth log data found
              </p>
            }
            rows={authLogs}
            cols={[
              {
                title: "User ID",
                align: "left",
                content: (authLog: UserAuthLogResponse) => (
                  <Link
                    href={{
                      pathname: "",
                      query: { user_id: authLog?.user_id },
                    }}
                  >
                    {authLog?.user_id}
                  </Link>
                ),
              },
              {
                title: "OAuth Operation",
                align: "left",
                content: (row) => row?.oauth_operation,
              },
              {
                title: "Started At",
                align: "left",
                content: (row) => formatDateTime(row?.started_at),
              },
              {
                title: "Completed At",
                align: "left",
                content: (row) => formatDateTime(row?.completed_at),
              },
              {
                title: "Completed In",
                align: "left",
                content: (row) =>
                  formatElapsedTime(row?.started_at, row?.completed_at),
              },
              {
                title: "Meta Data",
                align: "left",
                content: (row) => renderMetaData(row?.meta_data),
              },
              {
                title: "",
                align: "left",
                content: (row) => "",
              },
            ]}
          />
        </div>
      )}
    </div>
  );
}
